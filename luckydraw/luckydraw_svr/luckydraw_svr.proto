syntax = "proto3";

package luckydraw;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/luckydraw";

import "pb/luckydraw/common/luckydraw_comm.proto";

//
//    抽奖中台 svr
//

service Svr {
  // 抽奖
  rpc DoLottery(DoLotteryReq) returns (DoLotteryRsp);
  // 查看抽奖活动详情
  rpc GetActivityDetail(GetActivityDetailReq) returns (GetActivityDetailRsp);
  // 查看中奖记录
  rpc GetLotteryRecord(GetLotteryRecordReq) returns (GetLotteryRecordRsp);
  // 查看中奖记录(跑马灯)
  rpc GetLotteryRecordLamp(GetLotteryRecordLampReq) returns (GetLotteryRecordLampRsp);
}

// 奖项信息
message AwardInfo {
  string id = 1; // 奖项id
  string name = 2; // 奖项名称
  string award_id = 3; // 奖励id
  uint32 award_type = 4; // 奖项类型, see EnumAwardType
  uint32 award_sub_type = 5; // 奖项子类型, see EnumAwardSubType
  string pic = 6; // 奖项图片
  string remarks = 7; // 万能备注
  string doc = 8; // 中奖文案
}

//
//返回错误码说明：
//ERR_CODE_NEED_SAFE_VERIFY   = 10030; // 错误提示语: 验证码url
//ERR_CODE_TICKET_NOT_ENOUGH  = 22403; // 抽奖券不足
//ERR_CODE_ASYNC_LOTTERY      = 22405; // 抽奖进行中，稍后留意抽奖结果
//RetOverLimit                = 5018;   // 请求被限流
//ERR_CODE_NOT_FOUND          = 22408; // 活动不存在
//
message DoLotteryReq {
  string appid = 1; // appid
  string openid = 2; // 用户openid
  string activity_id = 3; // 抽奖活动id
  map<string, string> params = 4; // 透传参数
  string customize_key = 5; // 业务自定义key
  int64 customize_mask = 6; // 业务自定义key的mask, see EnumCustomizeKeyMask
  string biz_id = 7; // 业务幂等id
}

message DoLotteryRsp {
  common.CommRet ret = 1;
  AwardInfo award = 2;
  common.SafetyParams safety_params = 3; // 安全打击的参数
  string seqid = 4; // 抽奖唯一id
}

message GetActivityDetailReq {
  string appid = 1; // appid
  string openid = 2; // 用户openid(可选)
  string activity_id = 3; // 抽奖活动id
  map<string, string> params = 4; // 透传参数
}

message GetActivityDetailRsp {
  common.CommRet ret = 1;
  string activity_id = 2; // 抽奖活动id
  string name = 3; // 活动名称
  int64 begin_ts = 4; // 活动开始时间戳
  int64 end_ts = 5; // 活动结束时间戳
  int64 server_ts = 6; // 服务器当前时间戳
  uint64 ticket_cnt = 7; // 用户拥有的抽奖券数量(openid有传才有,否则为0)
  uint64 ticket_cost = 8; // 一次抽奖消耗抽奖券数量
  repeated AwardInfo award_list = 9; // 奖项信息
}

message LotteryRecordItem {
  string id = 1; // 奖项id
  string name = 2; // 奖项名称
  string award_id = 3; // 奖励id
  uint32 award_type = 4; // 奖项类型, see EnumAwardType
  uint32 award_sub_type = 5; // 奖项子类型, see EnumAwardSubType
  string pic = 6; // 奖项图片
  int64 ts = 7; // 中奖时间戳
}

message GetLotteryRecordReq {
  string appid = 1; // appid
  string openid = 2; // 用户openid(可选)
  string activity_id = 3; // 抽奖活动id
  string passback = 4; // 分页参数, 第一次请求不用传
  uint32 order = 5; // 排序类型, see EnumRecordOrderType
}

message GetLotteryRecordRsp {
  common.CommRet ret = 1;
  repeated LotteryRecordItem record_list = 2; // 中奖记录
  string passback = 3; // 分页参数
  int32 has_more = 4; // 分页参数, 0:没有更多, 1:还有更多
}

message LampLotteryRecordItem {
  string appid = 1; // appid
  string openid = 2; // 用户openid
  LotteryRecordItem award_item = 3;
}

message GetLotteryRecordLampReq {
  string activity_id = 1; // 抽奖活动id
  string passback = 2; // 分页参数, 第一次请求不用传
  uint32 lamp_type = 3; // 跑马灯类型, see EnumLampRecordType
}

message GetLotteryRecordLampRsp {
  common.CommRet ret = 1;
  repeated LampLotteryRecordItem record_list = 2; // 中奖记录
  string passback = 3; // 分页参数
  int32 has_more = 4; // 分页参数, 0:没有更多, 1:还有更多
}
