syntax = "proto3";

package luckydraw;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/luckydraw/luckydraw_callback";

import "pb/luckydraw/common/luckydraw_comm.proto";

//
//    抽奖中台 回调
//

//
//    返回错误码说明：
//    ERR_CODE_DEFAULT_SVR_ERR     = 5011  // 接口系统错误
//    ERR_CODE_BAD_PARAM           = 10003 // 参数错误(不会重试)
//    ERR_CODE_ANTI_REPLAY_DEL     = 22402 // del防重复请求错误(不会重试,strSeqID重复,抽奖流程继续)
//    ERR_CODE_TICKET_NOT_ENOUGH   = 22403 // 抽奖券数量不足错误(不会重试)
//    ERR_CODE_ACTIVITY_NOT_BEGIN  = 22404 // 活动未开启错误(不会重试)
//    ERR_CODE_ACTIVITY_END        = 22406 // 活动未开启错误(不会重试)
//    ERR_CODE_ANTI_REPLAY_DEL_BUS = 22407 // del防重复请求错误(不会重试,strBizID重复,抽奖流程终止)
//    ERR_CODE_NOT_FOUND           = 22408 // 活动不存在(不会重试)
//

// 扣除抽奖券 (接口超时和5011会状态机重试)
message SubTicketReq {
  string appid = 1; // appid
  string openid = 2; // 用户openid
  string activity_id = 3; // 抽奖活动id
  uint64 cnt = 4; // 数量
  string seqid = 5; // 抽奖唯一id
  string bizid = 6; // 业务抽奖唯一id
  map<string, string> params = 7; // 透传参数
}

message SubTicketRsp {
  common.CommRet ret = 1;
  uint64 cnt = 2; // 剩余总数量
}

//
//    ERR_CODE_DEFAULT_SVR_ERR    = 5011  // 接口系统错误
//    ERR_CODE_BAD_PARAM          = 10003 // 参数错误
//

// 发奖回调 (接口超时和5011会状态机重试)
message LotteryAwardReq {
  string id = 1; // 奖项id
  string name = 2; // 奖项名称
  string award_id = 3; // 奖励id
  uint32 award_type = 4; // 奖项类型, see EnumAwardType
  uint32 award_sub_type = 5; // 奖项子类型, see EnumAwardSubType
  string pic = 6; // 奖项图片
  string remarks = 7; // 万能备注
  string doc = 8; // 中奖文案
  string appid = 9; // appid
  string openid = 10; // 用户openid
  string seqid = 11; // 抽奖唯一id
  string biz_id = 12; // 业务幂等id
  map<string, string> params = 13; // 透传参数
}

message LotteryAwardRsp {
  common.CommRet ret = 1;
}
