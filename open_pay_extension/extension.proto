syntax = "proto3";

package open_pay_extension;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/open_pay_extension";

import "pb/device/device.proto";

service Extension {
  // 校验订单
  rpc CheckOrder(CheckOrderReq) returns (CheckOrderRsp);
  // 发货
  rpc Delivery(DeliveryReq) returns (DeliveryRsp);
  // lock
  rpc LockProduct(LockProductReq) returns (LockProductRsp);
  // unlock
  rpc UnlockProduct(UnlockProductReq) returns (UnlockProductRsp);
}

enum OperatingSystem {
  OperatingSystemUnknown = 0;
  OperatingSystemAndroid = 1;
  OperatingSystemIOS = 2;
}

message LockProductReq {
  string appId = 1;
  string openId = 2;
  string productId = 3; // 商品 id
  string transactionId = 4; // 唯一订单 id
  int64 expireTime = 5; // 过期时间 0:不过期
}

message LockProductRsp {}

message UnlockProductReq {
  string appId = 1;
  string openId = 2;
  string productId = 3; // 商品 id
  string transactionId = 4; // 唯一订单 id
}

message UnlockProductRsp {}

message CheckOrderReq {
  string appId = 1;
  string openId = 2;
  string productId = 3; // 商品 id
  int64 price = 4; // 价格
  int64 greenId = 5; // 绿钻 id
  OperatingSystem os = 6; // 系统
  map<string, string> mapExt = 7;
}

message CheckOrderRsp {}

message DeliveryReq {
  string appId = 1;
  string openId = 2;
  string productId = 3; // 商品 id
  string transactionId = 4; // 唯一订单 id
  int64 timestamp = 5; // 发货时间戳
  OperatingSystem os = 6; // 系统
  map<string, string> mapExt = 7;
  device.Device device = 8;
}

message DeliveryRsp {
  message Product {
    string name = 1;
    int64 num = 2;
  }
  repeated Product products = 1;
}
