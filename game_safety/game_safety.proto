syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_safety";

// 安全上报接口
service Safety {
  // 图片安全上报
  rpc SafeCheckPic(SafeReportPicReq) returns (SafeReportPicRsp);
}

message CommRet {
  int32 code = 1; // 错误码
  string msg = 2; // 错误提示语
}

message SafeReportPicReq {
  // 房间 ID
  string room_id = 1;
  // qua 设备qua
  string qua = 2;
  // 图片url
  string pic_url = 3;
  // 扩展参数
  map<string, string> params = 4;
}

message SafeReportPicRsp {
  CommRet ret = 1;
  int32 safe_type = 2; //非0为被安全打击了
}
