syntax = "proto3";

package adapter_unified_assets;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_unified_assets";

import "pb/adapter_common/adapter_common.proto";

//支付状态
enum PayOrderStatus{
    PAY_ORDER_STATUS = 0;         //new 未支付
    PAY_ORDER_SUCCESS = 1;        //支付成功
    PAY_ORDER_FAIL = 2;           //支付失败
}

// 支付场景
enum PaySceneType {
  PAY_SCENE_TYPE_UNKNOWN = 0;
  PAY_SCENE_TYPE_LIVE = 1;        //直播 必填参数:anchor_id、room_id、show_id
  PAY_SCENE_TYPE_KTV = 2;         //歌房 必填参数:anchor_id、room_id、show_id
  PAY_SCENE_TYPE_ASYNC_UGC = 3;   //异步作品 必填参数:ugc_id
  Pay_SCENE_TYPE_ASYNC_MAIL = 4;  //异步私信 必填参数:无
  PAY_SCENE_TYPE_ASYNC_HOMEPAGE = 5;  //异步个人 必填参数:无
}

// 支付场景信息
message PaySceneInfo{
  PaySceneType pay_scene_type = 1; // 见枚举说明PaySceneType
  string anchor_id = 2; // 主播id
  string room_id = 3; // 房间id
  string show_id = 4; // 直播id
  string ugc_id = 5; // 作品id
}

// 支付结果
message PayResult{
  string order_id = 1;
  int64 balance = 2;
  string err_msg = 3;
}

// 支付app
message PayApp{
  string act_id = 1; // 活动id,平台分配给具体活动的标识
  int64 business_id = 2; // 支付businessid，支付平台分配
  int64 currency_type = 4; // 货币类型
}

// 商品信息
message GoodsItem
{
  string goods_id = 1; //商品Id
  int64 num = 2;
}

// 支付信息
message PayInfo
{
  repeated GoodsItem goods_items = 1; //商品列表
  /*
    ==========mlive begin
    position q音歌房 麦序
    ==========mlive end
  */
  map<string,string> map_ext = 2; // 额外信息
}

// 收礼信息
message GiveGiftsInfo{
  adapter_common.GameMiddleInfo recv_game_middle_info = 1; // 被打赏者
  PayInfo pay_info = 2;     //支付信息
}

// 礼包内的奖项
message RewardItem{
  string reward_id = 1; // 奖励ID
  int64 reward_num = 2; // 数量
  int64 reward_type = 3; // 奖励类型
  int64 unit_price = 4; // 单价
  string reward_name = 5; // 资产名称
  string reward_logo = 6; // 资产icon
  string universal = 7; // 万能字段 透传配置系统上配置的信息
}

// 礼包信息
message GiftPackageInfo {
  int64 gift_package_id = 1; // 礼包ID
  int64 series_id = 2; // 礼包序列ID，标识礼包ID属于某个系列活动
  repeated RewardItem items = 3; // 礼包内包含资产列表
  int64 gift_package_value = 4; // 福利价值单位:分
  int64 expire_ts = 5; // 过期时间
  string encry_gift_package_id = 6; // 加密礼包ID
  string gift_package_reason = 7; // 礼包发放原因
}

// 礼物信息
message GiftInfo {
  int64 gift_id = 1; //礼物ID
  int64 gift_price = 2; //礼物单价
  int64 gift_type = 3; //礼物类型
  string gift_name = 4; //礼物名称
  string gift_icon = 5; //礼物图标 默认为160*160尺寸
  string gift_animation_url = 6; //礼物动画资源url
  string gift_animation_md5 = 7; //礼物动画资源md5
  // 字段说明
  // gift_mark_type 礼物标记
  // gift_play_scenes 礼物玩法场景
  // gift_attribute 礼物属性
  // gift_icon_large 礼物图片 360*360尺寸
  map<string, string> gift_ext = 8; //扩展字段
  int64 gift_animation_id = 9; //礼物动画资源id
}

enum SendExtensionId {
  SendExtensionIdUnknow = 0; // 未知
  SendExtensionIdBlessingGodPlay = 1; // 福神玩法
  SendExtensionIdChefGodPlay = 2; // 全民厨神
  SendExtensionIdPetPKPlay = 3; // 宠物PK
  SendExtensionIdGodPlacePlay = 4; // 仙域
  SendExtensionIdHonorWarPlay = 5; // 荣耀空战
  SendExtensionIdKingWar = 6; // 国王战争
  SendExtensionIdGoldEgg = 7; // 砸金蛋
  SendExtensionIdFortuneExplore = 8; // 福禄探秘
  SendExtensionIdPagodaRankSvr = 9; // 九层宝塔通层玩法
}
