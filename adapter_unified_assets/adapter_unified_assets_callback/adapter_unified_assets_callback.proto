syntax = "proto3";

package adapter_unified_assets;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_unified_assets";

import "pb/adapter_unified_assets/callback/callback.proto";

service Callback {
  rpc GiftPackageBusinessCheckSend(callback.GiftPackageBusinessCheckSendReq) returns (GiftPackageBusinessCheckSendRsp);
  
  rpc OrderShipment(callback.OrderShipmentReq) returns (OrderShipmentRsp);
}

message GiftPackageBusinessCheckSendRsp {
  callback.GiftPackageBusinessCheckSendRsp data = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

message OrderShipmentRsp {
  callback.OrderShipmentRsp data = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}
