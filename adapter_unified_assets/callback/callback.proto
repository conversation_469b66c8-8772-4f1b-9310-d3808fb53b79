syntax = "proto3";

package callback;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_unified_assets/callback";

import "pb/adapter_common/adapter_common.proto";

// 礼包发货check==================begin
// 这里针对订单号需要校验：礼包id、发放人、发放数量是否匹配
// 如果正确返回 pass = true
// 如果错误返回 pass = false
message CheckInfo {
  string order_id = 1; // 订单id
  string gift_package_id = 2; // 礼包id
  uint32 num = 3; // 数量
  uint32 send_ts = 4; // 发放时间 秒
  string reason = 5; // 发放原因
  string ext_id = 6; // 业务方带过来的teaceid 用于链路跟踪，填抽奖ID、任务ID、活动ID
  map<string,string> map_ext = 7; // 透传字段 对应发礼包传入的map_ext
}

message CallInfo {
  string call_back_cmd = 1;
}

message GiftPackageBusinessCheckSendReq {
  adapter_common.GameMiddleInfo game_middle_info = 1;
  CheckInfo check_info = 2;
  CallInfo call_info = 3;
}

message GiftPackageBusinessCheckSendRsp {
  bool pass = 1; // 校验是否通过
}
// 礼包发货check==================end

// 支付代理delivery==================begin
message CommodityItem {
  uint32 commodity_id = 1; // 消费的道具id
  uint32 num = 2; // 消费的道具数量
}

message ConsumeInfo {
  repeated CommodityItem items = 1;
  uint32 amount = 2; // 总价
  map<string,string> map_ext = 3; // 透传字段
  uint32 currency_type = 4; // 货币类型 0/1=k币
}

message OrderShipmentReq {
  adapter_common.GameMiddleInfo game_middle_info = 1; // 中台数据
  ConsumeInfo consume_info = 2; // 消费信息-下单时传入的参数
  string consume_id = 3; // 订单id
  bytes vec_data = 4; // 业务透传数据-下单时传入的参数
  uint32 pay_scene = 5; // 付费场景 1=直播 2=歌房 3=异步作品
  bytes pay_scene_data = 6; // 付费场景数据-云上应该暂时用不到，先只透传吧
  int64 business_id = 7; // 支付businessid，支付平台分配
}

message OrderShipmentRsp {
}
// 支付delivery==================end
