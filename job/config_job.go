package job

import (
	"git.kugou.net/fxgo/core/crontab"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/controller/rpc"
)

func Init() {
	// 游戏配置平台
	//rpc.MakeSimpleGiftInfoCache()
	entryID, err := crontab.AddFunc("0 */10 * * * *", rpc.MakeSimpleGiftInfoCache)
	if err != nil {
		logger.Panicf("启动 rpc.MakeSimpleGiftInfoCache 定时任务失败,%s", err.Error())
	}
	logger.Infof("启动 rpc.MakeSimpleGiftInfoCache 定时任务成功,ID:%d", entryID)

}
