syntax = "proto3";

package xian_king_war;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/kblb/pkg/gen/pb/xian_king_war/notify";

import "pb/adapter_unified_assets/callback/callback.proto";
import "pb/event/platform_event/platform_event.proto";
import "pb/timer/timer.proto";

//import "pb/xian_king_war/comm/comm.proto";

service Notify {
  // 定时维护城堡状态
  rpc CastleState(timer.TimerCallbackRequest) returns (timer.TimerCallbackResponse);
  // 发货接口
  rpc Deliver(event.TmePlatformEventReq) returns (event.TmePlatformEventRsp);
  // 中台发福利礼包回调检查
  rpc CallbackCheckSend(callback.GiftPackageBusinessCheckSendReq) returns (callback.GiftPackageBusinessCheckSendRsp);
  // 挂件调用接口,有外网域名,WNS鉴权信任x-auth-openid
  rpc Status(StatusReq) returns (StatusRsp);
  // 大喇叭通知接口
  rpc BigHorns(timer.TimerCallbackRequest) returns (timer.TimerCallbackResponse);
  // 结算排行榜的定时器
  rpc RankSettle(timer.TimerCallbackRequest) returns (timer.TimerCallbackResponse);
  // 分享事件
  rpc ShareEvent(event.TmePlatformEventReq) returns (event.TmePlatformEventRsp);
}

message StatusReq {}

message StatusRsp {
  int64 tn = 1; //服务器当前时间
  int64 wid = 2; //大轮次
  int64 rid = 3; //小轮次
  int64 bt_start = 4; //战斗阶段-开始时间
  int64 bt_end = 5; //战斗阶段-结束时间
}
