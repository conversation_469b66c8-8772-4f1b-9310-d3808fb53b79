syntax = "proto3";

package petpk;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/petpk/pkg/gen/pb/petpk/battle";

import "pb/adapter_unified_assets/callback/callback.proto";
import "pb/petpk/common/common.proto";
import "pb/petpk/pet/pet_info.proto";

service Battle {
  // 场次安排时间配置
  rpc Config(ConfigReq) returns (ConfigRsp);
  // 输出一些简单的信息, 返回自己房间的一些基础信息 和 自己当前的战斗id
  rpc BaseInfo(BaseInfoReq) returns (BaseInfoRsp);
  // 宠物信息<非准备&战斗阶段主界面数据>
  rpc Info(InfoReq) returns (InfoRsp);
  // 战斗信息
  rpc BInfo(BInfoReq) returns (BInfoRsp);
  // 查询余额
  rpc Balance(BalanceReq) returns (BalanceRsp);
  // 状态+战斗积分贡献topN【轮询】
  rpc State(StateReq) returns (StateRsp);
  // 攻击
  rpc Attack(AttackReq) returns (AttackRsp);
  // 礼物攻击
  rpc GiftAttack(GiftAttackReq) returns (GiftAttackRsp);
  // 礼物回血
  rpc GiftHeal(GiftHealReq) returns (GiftHealRsp);
  // 礼物购买道具
  rpc GiftBuy(GiftBuyReq) returns (GiftBuyRsp);
  // 平台支付回调
  rpc CBPay(callback.OrderShipmentReq) returns (callback.OrderShipmentRsp);
  // 标记神兽
  rpc Mark(MarkReq) returns (MarkRsp);
  // 实时排行【战斗内伤害top5】
  rpc Rank(RankReq) returns (RankRsp);
  // 结算界面数据
  rpc BResult(BResultReq) returns (BResultRsp);
  // 上场战况
  rpc PrevStats(PrevStatsReq) returns (PrevStatsRsp);
  // 宠物/房间段位榜
  //  rpc PetRank(PetRankReq) returns (PetRankRsp);
  // 房间-个人伤害榜
  rpc DamageRank(DamageRankReq) returns (DamageRankRsp);
  // 召集玩家
  rpc Convene(ConveneReq) returns (ConveneRsp);
  // 全服实时伤害榜 (开战前返回上场战斗的全服伤害榜; 开战后返回本场战斗的实时全服伤害榜)
  rpc DamageGlobalRank(DamageGlobalRankReq) returns (DamageGlobalRankRsp);
  // 伤害跑马灯
  rpc MarqueeDamage(MarqueeDamageReq) returns (MarqueeDamageRsp);
  // 背包
  rpc ListItem(ListItemReq) returns (ListItemRsp);
  // 结算接口
  rpc Settle(SettleReq) returns (SettleRsp);
  // 血量贡献榜
  rpc HpContributeRank(HpContributeRankReq) returns (HpContributeRankRsp);
  // 房间榜
  rpc RoomRank(RoomRankReq) returns (RoomRankRsp);
  // 【中台】房间排名, 第一名奖励
  rpc BState(BStateReq) returns (BStateRsp);
  // 【中台】PK期宠物排名及状态
  rpc BState2(BStateReq) returns (BState2Rsp);
  // 【中台】房间之星
  rpc RoomStar(RoomStarReq) returns (RoomStarRsp);
  // 房间总伤害的 排行榜
  rpc RoomDamageRank(RoomDamageRankReq) returns (RoomDamageRankRsp);
  // 排行榜第一名的数据
  rpc RankFirst(RankFirstReq) returns (RankFirstRsp);
  // 盲盒抽奖功能
  rpc GiftAddHp(GiftAddHpReq) returns (GiftAddHpRsp);
}

message GiftAddHpReq {
  string tid = 1; // 订单id
  RoomMeta meta = 2; // 透传数据   必传: anchor_id; room_id; show_id; target; pay_scene;
}

message GiftAddHpRsp {
  int64 gift_id = 1; // 抽中的gift_id
  int64 hp_ext = 2; // 任务加成血量
  int64 hp_total = 3; // 总血量 (基础hp + 任务增量 + 送礼增量)
}

message RoomStarReq {
  string appId = 1; // 游戏ID
  string room = 2; // 房间ID
}

message RoomStarRsp {
  uint64 uid = 1;
  string avatar = 2; // 头像
}

message RankFirstReq {}

message RankFirstItem {
  DamageRankItem room = 1;
  DamageRankItem user = 2;
  string rid = 3; // 轮次id
  int64 ts = 4; // 时间戳
}

message RankFirstRsp {
  repeated RankFirstItem list = 1;
}

message RoomDamageRankReq {
  string room = 1; // 当前所在的房间id
}

message RoomDamageRankRsp {
  repeated DamageRankItem tops = 1;
  repeated RankReward rank_rewards = 2; // 名次及对应的奖励
  DamageRankItem self = 3; // 自己的数据
}

message HpContributeRankReq {
  string rid = 1; // 轮次id
  string room = 2; // 房间id
  string passback = 3; // 第一页无须传, 第N页透传第N-1页回包中的
}

message HpContributeRankRsp {
  repeated HpContributeRankItem items = 1; // 榜单项
  HpContributeRankItem self = 2; // 自己的贡献信息
  bool hasMore = 3; // 是否还有下一页
  string passback = 4;
}

message RoomRankReq {
  string room = 1;
  string passback = 2; // 第一页无须传, 第N页透传第N-1页回包中的
}

message RoomRankRsp {
  repeated RoomRankItem items = 1; // 榜单项
  RoomRankItem self = 2;
  bool hasMore = 3; // 是否还有下一页
  string passback = 4;
}

message SettleReq {
  string room = 1;
}

message SettleRoom {
  string room_id = 1; // 房间id
  string room_name = 2; // 房间名称
  string room_cover = 3; // 房间封面
  int32 room_rank = 4; // 自己在这个房间内的排名
}

message SettleRsp {
  repeated Reward rewards = 1; // 战斗奖励
  repeated Reward gd_rank_rewards = 2; // 全服伤害排行榜奖励
  repeated Reward rdr_rewards = 3; // 房间伤害榜奖励
  repeated SettleRoom rooms = 4; // 相关房间列表
}

message RoomMeta {
  string anchor_id = 1; // 主播ID
  string room_id = 2; // 房间ID
  string show_id = 3;
  string ugc_id = 4;
  int32 pay_scene = 5; // 支付场景: 0-未知,1-直播,2-歌房
  string target = 6; // 送礼目标
  int32 position = 7; // 麦位(Q音)
}

message ConfigReq {
  int32 item_ver = 1;
}

message NormalDef {
  int64 atk_val_min = 1; // 发奖最小伤害值
  int64 mdr = 2; // 怪物死亡复活时间(秒)
  int64 atk_factor = 3; // 战神攻击加成 1000=100%
  string title = 4; // 战斗排期文案
  int64 godt = 5; // 复活后的无敌时间
}

message ConfigRsp {
  int64 ts = 1; // 服务器时间
  int64 item_ver = 2; // 道具配置版本
  common.XPlan curr = 3; // 当前轮时间配置
  common.XPlan next = 4; // 下轮时间配置
  repeated ZItem items = 5; // 道具配置
  NormalDef normal = 6; // 常规配置
  repeated ZShop shop_items = 7; // 可购买道具列表
  int64 hp_gift_price = 8; // 加血礼物价格
  repeated HpGiftInfo hp_gifts = 9; // 加血量的礼物列表
  int64 hp_gift_blind = 10; // 加血盲盒礼物id
}

message HpGiftInfo {
  int64 id = 1; // 礼物id
  int64 price = 2; // 礼物价格, KB
  string name = 3; // 礼物名称
  string icon = 4; // 礼物图标, 小
  int64 hp = 5; // 能加多少hp
  int64 animation_id = 6; // 动画资源id
}

// RItem - 玩家排行榜元素
message CRItem {
  uint64 uid = 1; // 用户ID
  string avatar = 2; // 用户头像
  string name = 3; // 玩家昵称
  string room_name = 4; // 归属房间名
  int32 rank = 5; // 排名,-1:未上榜, 其他实际排名(从1开始)
  int64 value = 6; // 伤害值
  int32 value2 = 7; // 扩展数值(攻击力)
}

message InfoReq {
  string room = 1; // 归属房间ID
}

message InfoRsp {
  int64 score = 1; // 段位积分
  int64 hp_dan = 2; // 段位血量
  int64 hp_ext = 3; // 任务加成血量
  int64 hp_max = 4; // 任务血量加成上限
  int64 next_score = 5; // 距下一段位积分
  int32 grank = 6; // 房间上期排名, 0-未上榜
  RankFirstItem rank_first = 7; // 最近一期的榜首信息
  string room_star_avatar = 8; // 房间之星的头像
  uint64 room_star_uid = 9; // 房间之星的uid
  int64 hp_total = 10; // 总血量 (基础hp + 任务增量 + 送礼增量)
}

message RoomFull {
  string id = 1; // 房间ID
  string name = 2; // 房间名
  string avatar = 3; // 房间头像
  int64 dan = 4; // 段位
  int64 score = 5; // 战斗积分
  int64 max_hp = 6; // 血量上限
  int64 hp = 7; // 血量
  int64 revive_time = 8; // 宠物复活时间, HP <= 0时有效
  string petId = 9; // 宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)
  string petPhase = 10; // 宠物形态所在的阶段
  map<string, int64> damaged = 11; // 当前宠物受到的其他3个房间伤害
  map<string, int64> tdamaged = 12; // 当前战斗受到的其他3个房间伤害
}

message RoomSimple {
  string id = 1; // 房间ID
  int64 hp = 2; // 当前血量
  int64 score = 3; // 战斗积分
  int64 revive_time = 4; // 宠物复活时间, HP <= 0时有效
  int32 focus = 5; // 0-未被集火; 1-房主标记; 2-超管标记
  string focus_avatar = 6; // 集火标记头像
  map<string, int64> damaged = 7; // 当前宠物受到的其他3个房间伤害
  map<string, int64> tdamaged = 8; // 当前战斗受到的其他3个房间总伤害
  map<string, int64> split_score = 9; // 瓜分积分
}

message BInfoReq {
  string room = 1; // 归属房间ID
}

message Reward {
  string id = 1; // 奖励ID
  int64 qua = 2; // 奖励数量
  string img = 3; // 图标
  string name = 4; // 名称
}

message RankReward {
  int32 rank = 1; // 排名
  repeated Reward rewards = 2; // 奖励
}

// 读取战斗所需的所有数据
message BInfoRsp {
  string bid = 1; // 战斗ID
  int32 state = 2; // 状态: -1-无战斗; 0-未开始;1-准备阶段;2-战斗;3-结算
  repeated RoomFull list = 3; // 房间列表
  int32 grank = 4; // 全服房间排名
  string buff = 5; // 战神buff 房间ID
  int64 atk_amount = 6; // 个人当前战斗总伤害
  int64 balance = 7 [deprecated = true]; // 平台币余额
}

message BalanceReq {}

message BalanceRsp {
  int64 balance = 1;
}

message StateReq {
  string room = 1; // 归属房间ID
  string bid = 2; // 战斗ID
  int64 last_ts = 3; // 上次请求的时间戳, 第一次的话传0
  int64 last_htbe_ts = 4; // 上一次高门槛通报时间的时间戳
}

message StateRsp {
  int64 ts = 1; // 服务器时间
  string buff = 2; // 战神buff: 房间ID
  repeated RoomSimple list = 3; // 房间列表
  repeated ScoreItem scores = 4; // 最近一段时间攻击过的玩家
  string room_star_avatar = 5; // 房间之星的头像
  repeated DamageRankItem ud_top = 6; // 玩家全服伤害榜的榜首
  repeated DamageRankItem rd_top = 7; // 房间全服伤害榜的榜首
  HtbeItem htbe = 8; // 高门槛事件, 产品说只要一个就可以了
  uint64 room_star_uid = 9; // 房间之星的uid
}

enum HtbeType {
  HTNone = 0; // 无意义
  HTHighPropHighRate = 1; // 使用高攻击力的道具且抽中了最高倍率
  HTUserDamageSum = 2; // 玩家累积伤害超过设定值
  HTUserPropCnt = 3; // 玩家使用了某特定道具多少个
  HTRoomPropCnt = 4; // 房间总计使用了某特定道具多少个
  HTRoomScoreOver = 5; // 房间总战斗积分超过特定值
}

message HtbeItem {
  int64 ts = 1; // 事件发生时间戳
  int32 event_type = 2; // 事件类型
  uint64 uid = 3; // 玩家id
  string name = 4; // 昵称
  string avatar = 5; // 头像
  int64 damage = 6; // 伤害值
  int64 prop_id = 7; // 道具id
  int64 prop_cnt = 8; // 使用了多少个道具
  int64 prop_rate = 9; // 道具倍率
  string room = 10; // 房间id
  int64 score = 11; // 积分
}

message ScoreItem {
  uint64 uid = 1;
  string name = 2;
  string avatar = 3;
  int64 score = 5; // 攻击获得战斗积分
  int64 ts = 6; // 攻击时间
}

message AttackReq {
  string room = 1; // 归属房间id
  string bid = 2; // 战斗ID
  string target = 3; // 攻击目标(房间ID)
  int64 item_id = 4; // 使用的道具ID
}

message AttackRsp {
  int64 rate = 1; // 攻击倍率
  int64 atk_val = 2; // 攻击力
  int64 damage = 3; // 造成的伤害
  int64 self_score = 4; // 本房间-战斗积分
  int64 self_hp = 5; // 本房间-当前血量
  int64 dst_score = 6; // 目标-战斗积分
  int64 dst_hp = 7; // 目标-当前血量
  int64 score_delta = 8; // 本次攻击增加战斗积分
}

message GiftAttackReq {
  string room = 1; // 归属房间id
  string bid = 2; // 战斗ID
  string target = 3; // 攻击目标(房间ID)
  string tid = 4; // 订单ID
  int64 gift_id = 5; // 礼物ID
  RoomMeta meta = 6; // 透传数据
}

message GiftAttackRsp {
  int64 atk_val = 1; // 攻击力
  int64 damage = 2; // 造成的伤害
  int64 self_score = 4; // 本房间-战斗积分
  int64 self_hp = 5; // 本房间-当前血量
  int64 dst_score = 6; // 目标-战斗积分
  int64 dst_hp = 7; // 目标-当前血量
  int64 score_delta = 8; // 本次攻击增加战斗积分
}

message GiftHealReq {
  string room = 1; // 归属房间id
  string bid = 2; // 战斗ID
  string tid = 3; // 订单ID
  int64 gift_id = 4; // 礼物ID
  RoomMeta meta = 5; // 透传数据
}

message GiftHealRsp {
  int64 blood = 1; // 恢复的血量
  int64 hp = 2; // 剩余血量
  int64 status = 3; // 恢复状态: -1: 已死亡, 1: 血量满, 0: 正常加血了
}

message GiftBuyReq {
  string room = 1; // 归属房间id
  string bid = 2; // 战斗ID
  string tid = 3; // 订单ID
  int64 gift_id = 4; // 礼物ID
  int32 gift_num = 5; // 礼物份数
  int32 item_target = 6; // 待购买道具
  RoomMeta meta = 7; // 透传数据
}

message GiftBuyRsp {
  int32 balance = 1; // 购买的道具余额
}

message RankReq {
  string bid = 1; // 战斗ID
  string room = 2; // 归属房间id
}

message RankRsp {
  repeated CRItem us = 1; // 最佳战神TOP5
}

message BResultReq {
  string room = 1; // 归属房间ID
}

message Pet {
  string id = 1; // 房间ID
  string avatar = 2; // 房间头像
  string name = 3; // 房间名称
  int32 dan_score = 4; // 段位分数
  int32 dan_delta = 5; // 段位分增量
  int32 dan = 6; // 段位标识
  int64 tdamage = 7; // 总伤害
  int64 score = 8; // 本场获得战斗积分
}

message BResultRsp {
  repeated Pet pet_list = 1; // 4个房间列表
  int32 self_atk = 2; // 自己在该房间的输出伤害
  string bid = 3; // 战斗ID
  bool settle_over = 4; // 结算结束
  bool settle = 5; // 是否需要结算
  bool has_settle = 6; // 是否已经结算了
}

message PrevStatsReq {
  string room = 1; // 归属房间ID
}

message SSM {
  int32 rank = 1; // 排名
  string id = 2; // 房间ID
  string name = 3; // 房间名
  string avatar = 4; // 房间头像
  int64 score = 5; // 本场获得战斗积分
  int64 total_damage = 6; // 本场输出伤害
  int64 dan_score = 7; // 段位分
  repeated Reward rewards = 8; // 战斗内奖励
}

message PrevStatsRsp {
  int64 settle_time = 1; // 结算日期
  string bid = 2; // 战斗ID
  int32 self_atk = 3; // 自己的总伤害
  repeated CRItem us = 4; // 本厂最佳战神 x5
  repeated SSM ssm = 5; // 战况数据 x4
}

message DamageRankReq {
  string room = 1; // 归属房间ID
  int32 rank = 2; // 从这个排名(包含)开始读取
}

message DamageRankRsp {
  repeated CRItem list = 1;
  CRItem self = 2;
  repeated Reward rewards = 3; // 房间奖励
}

message ConveneReq {
  string room = 1; // 归属房间ID
  string bid = 2; // 战斗id
}

message ConveneRsp {}

message BaseInfoReq {
  string room = 1; // 房间id
}

message BaseInfoRsp {
  string room = 1; // 房间id
  string name = 2; // 房间名
  string avatar = 3; // 房间头像
  string uname = 4; // 玩家昵称
  string uavatar = 5; // 玩家头像
  string bid = 6; // 战斗id
  int32 dan = 7; // 房间段位
  int32 role = 8; // 角色类型, 参考 petpk.PlayerType
  string petId = 9; // 宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)
  string petPhase = 10; // 宠物形态所在的阶段
  repeated RankReward rrs = 11; // 1~4名的奖励, 匹配前后可能不一样, 奖励只会变高, 不会变少
  bool settle = 12; // 是否可以结算
  string debug = 13;
  uint64 uid = 14; // 自己的uid
  int64 balance = 15; // 操作后的余额
}

message DamageGlobalRankReq {}

message DamageRankItem {
  int32 rank = 1; // 排名
  uint64 uid = 2; // 玩家id
  string name = 3; // 玩家昵称
  string avatar = 4; // 头像
  int64 damage = 5; // 造成的伤害
  string room = 6; // 房间id
  string avatar2 = 7; // 头像2
}

message DamageGlobalRankRsp {
  repeated DamageRankItem tops = 1; // 榜上玩家数据
  repeated RankReward rank_rewards = 2; // 名次及对应的奖励
  DamageRankItem self = 3; // 自己的数据
}

message MarqueeDamageReq {}

message MarqueeDamageRsp {
  repeated DamageRankItem tops = 1; // 榜上玩家数据
  repeated DamageRankItem room_tops = 2; // 房间的跑马灯
}

message MarkReq {
  string bid = 1; // 战斗ID
  string room = 2; // 归属房间id
  string target = 4; // 标记目标(房间ID)
}

message MarkRsp {
  bool mark = 1; // 标记成功还是失败
}

message ListItemReq {}

message ZItem {
  int64 id = 1; // 道具id
  int32 type = 2; // 类型 1-普通道具, 2-攻击礼物, 3-回血礼物, 4-购买
  string name = 3; // 名称
  int32 val1 = 4; // 攻击力 | 回血比例1000=100%
  int32 val2 = 5; // 无用 | 价格
  int32 score = 6; // 战斗积分
  int32 max = 7; // 持有上限
  map<string, string> ext = 8; // 扩展
}

message ZShop {
  int32 num = 1; // 礼物份数
  int32 price = 2; // 价格
  int32 x1_num = 3; // 道具1数量
  int32 x2_num = 4; // 道具2数量
  int32 x3_num = 5; // 道具3数量
}

message XItem {
  int64 id = 1; // 道具id
  int64 num = 2; // 道具数量
}

message ListItemRsp {
  int64 round_end_time = 1; // 轮次结束时间
  repeated XItem items = 2; // 道具列表
  string roundId = 3; // 轮次ID
}

message BStateReq {
  string appId = 1; // 游戏ID
  string room = 2; // 房间ID
  string roundId = 3; // 轮次ID
}

message BStateRsp {
  int32 rank = 1; // 当前房间排名
  int32 score = 2; // 当前房间战斗积分
  RankReward reward_top1 = 3; // 第一名奖励乘过系数
}

message BState2Rsp {
  int32 rank = 1; // 当前房间排名
  int64 revive_time = 2; // 复活时间, 为0说明宠物当前活着
}
