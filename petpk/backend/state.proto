syntax = "proto3";

package backend;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/petpk/backend";

message RoomState {
  int64    hp             = 1; // 基础血量 + 从任务获得的血量
  repeated string uniqIds = 2;
  int64    hpFromGift     = 3; // 从礼物获得的血量
}

message RoomInfo {
  int64    score          = 1;  //段位分
  repeated string uniqIds = 2;
  bool isInitialScoreIncreased =3; //是否增加过初始分数(1000分)
}

message BattleInfo {
  repeated string roomIds = 1;
}

// 段位配置项
message RankConfigItem {
  int32 id = 1; // 主段位id, 具体见配置平台
  string name = 2; // 主段位名, 如: 青铜, 白银, ...
  int32 subId = 3; // 子段位id, 具体见配置平台
  string subName = 4; // 子段位名, 如: 青铜-1, 青铜-2, ...
  int64 score = 5; // 对应的段位分, >= 该分数即满足段位要求
  int64 baseHp = 6; // 基础hp
  int64 maxHp = 7; // hp上限(段位+任务)
  int64 subHp = 8; // 扣减的血量
  int64 maxHpFromGift = 9; // hp上限(送礼)
}

message RankConfig {
  repeated RankConfigItem conf = 1;
}
