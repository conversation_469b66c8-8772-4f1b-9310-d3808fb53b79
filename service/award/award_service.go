package award

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/client"
	"kugou_adapter_service/controller/rpc"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/unified_assets"
	"kugou_adapter_service/third-party/gen-go/commonaward"
	"kugou_adapter_service/third-party/gen-go/goodsgiftservice"
	"kugou_adapter_service/third-party/gen-go/platform_order_servicev2"
	"kugou_adapter_service/utils"
)

func CreateOrderRequestAndRuleInfo(sendWelfareReq *unified_assets.SendWelfareReq) (*platform_order_servicev2.OrderRequest, *platform_order_servicev2.RuleInfo, error) {
	appinfo, err := genRuleAppInfo(sendWelfareReq.WelfareId)

	// todo: 保存orderid映射
	orderIdStr := client.GenerateID()
	orderId, err := strconv.ParseInt(orderIdStr, 10, 64)
	if err != nil {
		logger.Warnf("转换 OrderId 失败。OrderId: %v, err: %v", sendWelfareReq.OrderId, err)
		return nil, nil, err
	}

	userId, err := strconv.ParseInt(sendWelfareReq.UserId, 10, 64)
	if err != nil {
		logger.Warnf("转换 OrderId 失败。OrderId: %v, err: %v", sendWelfareReq.OrderId, err)
		return nil, nil, err
	}
	signRequest := &platform_order_servicev2.SignOrderRequest{
		OrderId:         orderId,
		AccessId:        appinfo.AppId,
		KugouId:         userId,
		OrderExpireTime: 0,
		Time:            sendWelfareReq.SendTs * 1000,
		TriggerTime:     0,
		Ext:             "",
	}
	orderRequest := &platform_order_servicev2.OrderRequest{
		OrderId:         signRequest.OrderId,
		AccessId:        signRequest.AccessId,
		KugouId:         signRequest.KugouId,
		OrderExpireTime: signRequest.OrderExpireTime,
		Time:            signRequest.Time,
		TriggerTime:     &signRequest.TriggerTime,
		Ext:             &signRequest.Ext,
		Sign:            utils.SignWithComma(signRequest, appinfo.AppKey),
	}
	orderRequest.CommonParameter = &platform_order_servicev2.CommonParameter{}
	ruleInfo := &platform_order_servicev2.RuleInfo{
		Factor: appinfo.factor,
		Num:    int32(sendWelfareReq.Num),
	}
	return orderRequest, ruleInfo, nil
}

type appInfo struct {
	AppId  int32
	AppKey string
	factor string
}

// appid * 100 + factor == welfareId
// rule == "packege=1" or "packege=2" etc,返回 1 or 2
func genRuleAppInfo(welfareId int64) (*appInfo, error) {

	factor := welfareId % 100
	appId := welfareId / 100

	appKey, err := client.GetAccessKey(int32(appId))
	if err != nil || appKey == nil {
		logger.Warnf("获取appKey失败。welfareId: %v, err: %v", welfareId, err)
		return nil, err
	}

	return &appInfo{
		AppId:  int32(appId),
		AppKey: *appKey,
		factor: fmt.Sprintf("packege=%d", factor),
	}, nil
}

// appid * 100 + factor == welfareId
func genGetFullAwardListAppInfo(welfareId int64) (*appInfo, error) {

	factor := welfareId % 100
	appId := welfareId / 100

	appKey, err := client.GetAccessKey(int32(appId))
	if err != nil || appKey == nil {
		logger.Warnf("获取appKey失败。welfareId: %v, err: %v", welfareId, err)
		return nil, err
	}

	return &appInfo{
		AppId:  int32(appId),
		AppKey: *appKey,
		factor: fmt.Sprintf("packege=='%d'", factor),
	}, nil
}

func genAppInfos(welfareIds []int64) (map[int64]bool, map[int32]*appInfo) {
	//var res []*appInfo
	welfareIdMap := make(map[int64]bool)
	appIdMap := make(map[int32]*appInfo)
	var failed []int64
	for _, v := range welfareIds {
		info, err := genGetFullAwardListAppInfo(v)
		if err != nil {
			failed = append(failed, v)
			continue
		}
		appIdMap[info.AppId] = info
		//res = append(res, info)
		welfareIdMap[v] = true
	}
	logger.Warnf("genAppInfos成功。failed: %v, welfareIdMap: %v", failed, welfareIdMap)
	return welfareIdMap, appIdMap
}

func BizBatchGetGiftPackage(req *unified_assets.BatchGetWelfareInfoReq) (*unified_assets.BatchGetWelfareInfoRsp, error) {
	welfareIdsMap, appIdMap := genAppInfos(req.WelfareIds)
	if len(appIdMap) == 0 {
		return nil, errors.New("没有可用的appinfo")
	}

	mapSucc := make(map[int64]*unified_assets.WelfareInfo)
	mapFailed := make(map[int64]int64)

	for appId, appinfo := range appIdMap {
		if appinfo == nil {
			continue
		}
		list, err := rpc.GetFullAwardList(appId, appinfo.AppKey)
		if err != nil {
			return nil, err
		}

		for _, award := range list {
			if award == nil {
				continue
			}
			retWelfareId := int(appId)*100 + genFactor(award.Rule)
			if _, ok := welfareIdsMap[int64(retWelfareId)]; ok {
				mapSucc[int64(retWelfareId)] = genWelfareInfo(award.GoodsInfoStrList)
			}
		}
	}

	for welfareId, _ := range welfareIdsMap {
		if _, in := mapSucc[welfareId]; !in {
			mapFailed[welfareId] = welfareId
		}
	}

	return &unified_assets.BatchGetWelfareInfoRsp{
		MapSucc: mapSucc,
		MapFail: mapFailed,
	}, nil
}

func genWelfareInfo(goodsInfos []*commonaward.GoodsInfoStr) *unified_assets.WelfareInfo {
	if goodsInfos == nil || len(goodsInfos) == 0 {
		return nil
	}

	welfareInfo := &unified_assets.WelfareInfo{
		Items: make([]*unified_assets.RewardItem, 0),
	}

	for _, goods := range goodsInfos {
		if goods == nil {
			continue
		}
		rewardNum, _ := strconv.ParseInt(goods.GetGoodsNum(), 10, 64)
		rewardType, _ := strconv.ParseInt(goods.GetGoodsType(), 10, 64)
		goodsId, _ := strconv.Atoi(goods.GetGoodsId())
		welfareInfo.Items = append(welfareInfo.Items, &unified_assets.RewardItem{
			RewardId:   goods.GetGoodsId(),
			RewardName: goods.GetGoodsDesc(),
			RewardNum:  rewardNum,
			RewardType: rewardType,
			RewardLogo: goods.GetGoodsPic(),
			UnitPrice:  getGoodsPrice(int32(goodsId)),
			Universal:  goods.GetGoodsExt(),
		})
	}

	return welfareInfo
}

func getGoodsPrice(goodsId int32) int64 {
	res := rpc.GetSimpleGiftInfo(goodsId)
	if res == nil {
		return 0
	}
	return int64(res.GetPrice())
}

// rule == "packege=='1'" or "packege=='2'" etc,返回 1 or 2
func genFactor(rule string) int {
	parts := strings.Split(rule, "==")
	if len(parts) == 2 {
		value := strings.Trim(parts[1], "'")
		factor, err := strconv.Atoi(value)
		if err == nil {
			return factor
		}
	}
	logger.Warnf("解析规则失败: %v", rule)
	return 0
}

func BizBatchGetGiftInfo(req *unified_assets.BatchGetGiftInfoReq) (*unified_assets.BatchGetGiftInfoRsp, error) {
	//welfareIdsMap, appIdMap := genAppInfos(req.GiftIds)
	//if len(appIdMap) == 0 {
	//	return nil, errors.New("没有可用的appinfo")
	//}

	mapSucc := make(map[int64]*unified_assets.GiftInfo)
	mapFailed := make(map[int64]int64)

	for _, giftId := range req.GiftIds {
		giftInfo := rpc.GetSimpleGiftInfo(int32(giftId))
		if giftInfo == nil {
			mapFailed[giftId] = giftId
			continue
		}
		mapSucc[giftId] = genAssetsGiftInfo(giftInfo)
	}

	return &unified_assets.BatchGetGiftInfoRsp{
		MapSucc: mapSucc,
		MapFail: mapFailed,
	}, nil
}

func genAssetsGiftInfo(giftInfo *goodsgiftservice.SimpleGiftInfo) *unified_assets.GiftInfo {
	return &unified_assets.GiftInfo{
		GiftId:           int64(giftInfo.GetID()),
		GiftName:         giftInfo.GetName(),
		GiftPrice:        int64(giftInfo.GetPrice()),
		GiftType:         int64(giftInfo.GetSpecialType()),
		GiftAnimationUrl: giftInfo.GetURL(),
		GiftIcon:         giftInfo.GetPic(),
		//GiftExt:   giftInfo.GetExtAttr(),
	}
}
