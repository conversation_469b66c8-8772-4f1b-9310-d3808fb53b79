package delivery

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"git.kugou.net/fxgo/core/client/xhttp"
	"git.kugou.net/fxgo/core/logger"
	"io"
	"kugou_adapter_service/dao"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/model"
	"kugou_adapter_service/utils/dateutils"
	"net/http"
	"strconv"
	"strings"
	"time"
)

// GameService 游戏服务接口，用于获取游戏配置
type GameService interface {
	GetTmeGamesConfig(appId string) (*model.TmeGame, error)
}

// GameDeliveryProvider 游戏发放提供者
type GameDeliveryProvider struct {
	gameService GameService
}

// NewGameDeliveryProvider 创建游戏发放提供者
func NewGameDeliveryProvider(gameService GameService) *GameDeliveryProvider {
	return &GameDeliveryProvider{
		gameService: gameService,
	}
}

// Delivery 执行游戏发放操作
func (p *GameDeliveryProvider) Delivery(order *dao.TransactionOrder) bool {
	gameConfig, err := p.gameService.GetTmeGamesConfig(order.Appid)
	if err != nil {
		logger.Warnf("调用游戏发货，加载配置失败。err: %v", err)
		return false
	}
	
	connectionTimeout := helper.GetApolloClient().GetIntValue("game.connectionTimeout", 500)
	proxyUrl := helper.GetApolloClient().GetStringValue("game.proxyUrl", "http://forward.proxy.kgidc.cn:3128")
	httpClient, err := xhttp.NewHttpClient(
		xhttp.WithConnectionTimeout(time.Duration(connectionTimeout)*time.Millisecond),
		xhttp.WithProxyURL(proxyUrl),
	)
	if err != nil {
		logger.Error("调用游戏发货，创建客户端失败。err: %v", err)
		return false
	}
	
	guandanNotify := model.GuandanNotify{
		AppId:       order.Appid,
		Platorder:   strconv.Itoa(int(order.Orderno)),
		Gameorderid: order.Outorderno,
		Productid:   order.Skuid,
		Coin:        int(order.Totalcoin.IntPart()),
		CoinType:    order.Cointype,
	}
	
	jsonBody, err := json.Marshal(guandanNotify)
	if err != nil {
		logger.Error("调用游戏发货，反序列化失败。err: %v", err)
		return false
	}
	
	encoded := base64.StdEncoding.EncodeToString(jsonBody)
	buf := bytes.NewBuffer([]byte(encoded))
	gameId := gameConfig.GameId
	sign := p.makeSign(gameConfig.GameKey, jsonBody)
	url := fmt.Sprintf("%s?gameid=%s&platid=%d&sign=%s", gameConfig.GameNotifyUrl, gameId, 0, sign)
	
	response, err := httpClient.Post(url, "application/json", buf)
	if err != nil {
		logger.Error("调用游戏发货，通知游戏发货失败。url: %v, guandanNotify: %+v, err: %v", url, guandanNotify, err)
		return false
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(response.Body)
	
	logger.Warnf("调用游戏发货，请求参数。url：%v, jsonBody: %v", url, string(jsonBody))
	
	// 检查响应状态码
	if response.StatusCode != http.StatusOK {
		logger.Warnf("调用游戏发货，Error: Status code: %v", response.StatusCode)
		return false
	}
	
	// 读取响应体
	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.Warnf("调用游戏发货。Error reading body: %v", err)
		return false
	}
	
	// 打印响应体
	logger.Warnf("调用游戏发货。Response Body: %v", string(body))
	resp := &model.GuandanNotifyResp{}
	err = json.Unmarshal(body, resp)
	if err != nil || resp.Status != 0 {
		logger.Warnf("调用游戏发货。Error reading body: %v", err)
		return false
	}
	
	if resp.Status == 0 {
		month := dateutils.FormatYearMonth(order.Ordertime)
		affected, err := dao.NewTransactionOrderDao().UpdateDeliveryStatus(month, order.Orderno, 1)
		if err != nil || affected < 1 {
			logger.Warn("更新订单发货状态失败。orderNo: %v, err: %v", order.Orderno, err)
		}
	}
	return true
}

// makeSign 生成签名
func (p *GameDeliveryProvider) makeSign(key string, jsonBody []byte) string {
	raw := string(jsonBody) + key
	logger.Warnf("raw: %v", raw)
	hash := md5.Sum([]byte(raw))
	hashHex := hex.EncodeToString(hash[:])
	return strings.ToLower(hashHex)
}
