package delivery

import (
	"fmt"
	"kugou_adapter_service/dao"
)

// DeliveryProvider 发放提供者接口
type DeliveryProvider interface {
	// Delivery 执行发放操作
	// order: 交易订单信息
	// 返回: 发放是否成功
	Delivery(order *dao.TransactionOrder) bool
}

// DeliveryProviderType 发放提供者类型
type DeliveryProviderType string

const (
	// GuandanDeliveryType 掼蛋游戏发放类型
	GuandanDeliveryType DeliveryProviderType = "guandan"
	// OtherDeliveryType 其他发放类型（示例，可根据需要扩展）
	OtherDeliveryType DeliveryProviderType = "other"
)

// DeliveryProviderFactory 发放提供者工厂
type DeliveryProviderFactory struct {
	gameService GameService
}

// NewDeliveryProviderFactory 创建发放提供者工厂
func NewDeliveryProviderFactory(gameService GameService) *DeliveryProviderFactory {
	return &DeliveryProviderFactory{
		gameService: gameService,
	}
}

// CreateProvider 根据类型创建发放提供者
func (f *DeliveryProviderFactory) CreateProvider(providerType DeliveryProviderType) (DeliveryProvider, error) {
	switch providerType {
	case GuandanDeliveryType:
		return NewGuandanDeliveryProvider(f.gameService), nil
	case OtherDeliveryType:
		return NewOtherDeliveryProvider(), nil
	default:
		return nil, fmt.Errorf("unknown delivery provider type: %s", providerType)
	}
}

// DeliveryManager 发放管理器，支持根据订单动态选择发放实现
type DeliveryManager struct {
	factory *DeliveryProviderFactory
}

// NewDeliveryManager 创建发放管理器
func NewDeliveryManager(gameService GameService) *DeliveryManager {
	return &DeliveryManager{
		factory: NewDeliveryProviderFactory(gameService),
	}
}
