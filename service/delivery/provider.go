package delivery

import "kugou_adapter_service/dao"

// DeliveryProvider 发放提供者接口
type DeliveryProvider interface {
	// Delivery 执行发放操作
	// order: 交易订单信息
	// 返回: 发放是否成功
	Delivery(order *dao.TransactionOrder) bool
}

// DeliveryProviderType 发放提供者类型
type DeliveryProviderType string

const (
	// GameDeliveryType 游戏发放类型
	GameDeliveryType DeliveryProviderType = "game"
	// OtherDeliveryType 其他发放类型（示例，可根据需要扩展）
	OtherDeliveryType DeliveryProviderType = "other"
)
