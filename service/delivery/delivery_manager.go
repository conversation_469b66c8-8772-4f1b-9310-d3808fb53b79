package delivery

import (
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/dao"
)

// DeliveryConfig 发放配置
type DeliveryConfig struct {
	AppIdToProviderType map[string]DeliveryProviderType
	DefaultProviderType DeliveryProviderType
}

// AdvancedDeliveryManager 高级发放管理器，支持配置化的发放策略
type AdvancedDeliveryManager struct {
	factory *DeliveryProviderFactory
	config  *DeliveryConfig
}

// NewAdvancedDeliveryManager 创建高级发放管理器
func NewAdvancedDeliveryManager(gameService GameService, config *DeliveryConfig) *AdvancedDeliveryManager {
	if config == nil {
		config = &DeliveryConfig{
			AppIdToProviderType: make(map[string]DeliveryProviderType),
			DefaultProviderType: GuandanDeliveryType,
		}
	}

	return &AdvancedDeliveryManager{
		factory: NewDeliveryProviderFactory(gameService),
		config:  config,
	}
}

// DeliveryWithAutoSelect 根据订单信息自动选择发放提供者并执行发放
func (adm *AdvancedDeliveryManager) DeliveryWithAutoSelect(order *dao.TransactionOrder) bool {
	providerType := OtherDeliveryType
	if order.Appid == "40000010" {
		providerType = GuandanDeliveryType
	}
	logger.Infof("订单 %d (AppId: %s) 选择发放类型: %s", order.Orderno, order.Appid, providerType)
	provider, err := adm.factory.CreateProvider(providerType)
	if err != nil {
		logger.Errorf("创建发放提供者失败。providerType: %s, err: %v", providerType, err)
		return false
	}
	return provider.Delivery(order)
}
