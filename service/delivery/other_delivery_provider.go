package delivery

import (
	"git.kugou.net/fxgo/core/logger"
	"github.com/spf13/cast"
	"kugou_adapter_service/client"
	"kugou_adapter_service/dao"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_common"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/unified_assets"
	"kugou_adapter_service/utils"
	"kugou_adapter_service/utils/dateutils"
)

// OtherDeliveryProvider 其他发放提供者示例
// 这是一个示例实现，展示如何添加新的发放方式
type OtherDeliveryProvider struct {
	// 可以添加其他发放方式需要的依赖
}

// NewOtherDeliveryProvider 创建其他发放提供者
func NewOtherDeliveryProvider() *OtherDeliveryProvider {
	return &OtherDeliveryProvider{}
}

// Delivery 执行其他发放操作
func (p *OtherDeliveryProvider) Delivery(order *dao.TransactionOrder) bool {
	logger.Warnf("执行其他发放操作，订单号: %d", order.Orderno)
	pay4BuyGoodsReq := &unified_assets.Pay4BuyGoodsReq{}
	err := utils.JsonToProto(order.Extrainfo, pay4BuyGoodsReq)
	if err != nil {
		logger.Warnf("解析订单额外信息失败。err: %v, extrainfo: %s", err, order.Extrainfo)
		return false
	}
	req := &callback.OrderShipmentReq{
		GameMiddleInfo: &adapter_common.GameMiddleInfo{
			GameAppId: order.Appid,
			Uid:       cast.ToString(order.Kugouid),
		},
		ConsumeId:  order.Outorderno,
		BusinessId: pay4BuyGoodsReq.PayApp.BusinessId,
	}
	rsp, err := client.OrderShipment("kugou", req)
	if err != nil {
		logger.Warnf("调用订单发货失败。err: %v", err)
		return false
	}
	logger.Warnf("发放结果: %+v, 请求参数: %+v", rsp, req)
	month := dateutils.FormatYearMonth(order.Ordertime)
	affected, err := dao.NewTransactionOrderDao().UpdateDeliveryStatus(month, order.Orderno, 1)
	if err != nil || affected < 1 {
		logger.Warn("更新订单发货状态失败。orderNo: %v, err: %v", order.Orderno, err)
		return false
	}
	return true
}
