package delivery

import (
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/client"
	"kugou_adapter_service/dao"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_common"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	"kugou_adapter_service/utils/dateutils"
	"strconv"
)

// OtherDeliveryProvider 其他发放提供者示例
// 这是一个示例实现，展示如何添加新的发放方式
type OtherDeliveryProvider struct {
	// 可以添加其他发放方式需要的依赖
}

// NewOtherDeliveryProvider 创建其他发放提供者
func NewOtherDeliveryProvider() *OtherDeliveryProvider {
	return &OtherDeliveryProvider{}
}

// Delivery 执行其他发放操作
func (p *OtherDeliveryProvider) Delivery(order *dao.TransactionOrder) bool {
	logger.Warnf("执行其他发放操作，订单号: %d", order.Orderno)
	req := &callback.OrderShipmentReq{
		GameMiddleInfo: &adapter_common.GameMiddleInfo{},
		ConsumeInfo:    &callback.ConsumeInfo{},
		ConsumeId:      strconv.FormatInt(order.Orderno, 10),
		VecData:        []byte(""),
		PayScene:       1,
		PaySceneData:   []byte(""),
		BusinessId:     1,
	}
	rsp, err := client.OrderShipment("kugou", req)
	if err != nil {
		logger.Warnf("调用订单发货失败。err: %v", err)
		return false
	}
	logger.Warnf("发放结果: %+v, 请求参数: %+v", rsp, req)
	month := dateutils.FormatYearMonth(order.Ordertime)
	affected, err := dao.NewTransactionOrderDao().UpdateDeliveryStatus(month, order.Orderno, 1)
	if err != nil || affected < 1 {
		logger.Warn("更新订单发货状态失败。orderNo: %v, err: %v", order.Orderno, err)
		return false
	}
	return true
}
