package service

import (
	"encoding/base64"
	"encoding/json"
	"kugou_adapter_service/dao"
	"kugou_adapter_service/model"
	"kugou_adapter_service/service/delivery"
	"kugou_adapter_service/third-party/gen-go/platform_order_servicev2"
)

type Service struct {
	deliveryFactory         *delivery.DeliveryProviderFactory
	deliveryManager         *delivery.DeliveryManager
	advancedDeliveryManager *delivery.AdvancedDeliveryManager
}

func New() (*Service, error) {
	service := &Service{}
	service.deliveryFactory = delivery.NewDeliveryProviderFactory(service)
	service.deliveryManager = delivery.NewDeliveryManager(service)
	return service, nil
}

func BuildCommonParameter(order *dao.TransactionOrder, platAppId int32, platToken string) *platform_order_servicev2.CommonParameter {
	return &platform_order_servicev2.CommonParameter{
		Pid:        &order.Stdplat,
		ChannelId:  nil,
		Version:    nil,
		ClientIp:   &order.Clientip,
		DeviceId:   nil,
		SysVersion: nil,
		AppId:      &platAppId,
		Token:      &platToken,
		Source:     nil,
		StdImei:    nil,
		RoomId:     nil,
	}
}

func ParseExtra(extra string) (map[string]interface{}, error) {
	var extraMap map[string]interface{}
	decoded, err := base64.StdEncoding.DecodeString(extra)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(decoded, &extraMap)
	if err != nil {
		return nil, err
	}
	return extraMap, nil
}

func ParsePlatExtra(platExtra string) (*model.PlatExtraRaw, error) {
	var extra *model.PlatExtraRaw
	decoded, err := base64.StdEncoding.DecodeString(platExtra)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(decoded, &extra)
	if err != nil {
		return nil, err
	}
	return extra, nil
}
