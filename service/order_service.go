package service

import (
	"git.kugou.net/fxgo/core/logger"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"github.com/uniplaces/carbon"
	"kugou_adapter_service/client"
	"kugou_adapter_service/dao"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/model"
	"kugou_adapter_service/utils/dateutils"
	"time"
)

// CreatePureConsumeOrder 创建纯扣费订单
func (s *Service) CreatePureConsumeOrder(gameAppId string, openId string, kugouId int64, outOrderNo string, coinType int32,
	totalCoin decimal.Decimal, clientIp string) (*dao.TransactionOrder, error) {
	// 8002206 3v9w5756q6 萌宠觅食-秀场
	gameConfig, err := s.GetTmeGamesConfig(gameAppId)
	if err != nil {
		logger.Warnf("创建纯扣费订单，加载游戏配置失败。err: %v", err)
		return nil, err
	}
	// 保存幂等信息
	idempotentDao := dao.NewCwsxIdempotentDao()
	orderId, err := client.GetGlobalId()
	if err != nil {
		logger.Error("创建纯扣费订单，获取GID失败。err: %v", err)
		return nil, err
	}
	idempotentInfo, err := idempotentDao.SaveIdempotentInfo(orderId, outOrderNo)
	if err != nil {
		logger.Warnf("创建纯扣费订单，保存幂等信息失败。err: %v", err)
		return nil, err
	}
	// 创建扣费订单
	month := dateutils.FormatYearMonth(idempotentInfo.Ordertime)
	order := &dao.TransactionOrder{
		Appid:          gameAppId,
		Orderno:        idempotentInfo.Orderid,
		Ordertime:      idempotentInfo.Ordertime,
		Outorderno:     outOrderNo,
		Kugouid:        kugouId,
		Openid:         openId,
		Accessid:       gameConfig.AccessId,
		Status:         0,
		Deliverystatus: 1,
		Skuid:          "",
		Quantity:       1,
		Totalcoin:      totalCoin,
		Clientip:       clientIp,
		Stdplat:        1,
		Extrainfo:      "{}",
		Cointype:       coinType,
		Createtime:     time.Now(),
		Updatetime:     time.Now(),
	}
	logger.Warnf(cast.ToString(order))
	transactionOrderDao := dao.NewTransactionOrderDao()
	affected, err := transactionOrderDao.InsertIgnore(month, order)
	if err != nil {
		logger.Error("创建纯扣费订单，保存请求订单异常。order: %v, err: %v", order, err)
		return nil, err
	}
	if affected > 0 {
		logger.Error("创建纯扣费订单，保存请求订单失败。order: %v, err: %v", order, err)
		return order, nil
	}
	order, err = transactionOrderDao.Find(month, order.Outorderno)
	if err != nil {
		logger.Error("创建纯扣费订单，查询重复订单异常。order: %v, err: %v", order, err)
		return nil, err
	}
	return order, nil
}

// CreateConsumeOrder 创建扣费发奖订单
func (s *Service) CreateConsumeOrder(appId string, openId string, kugouId int64, totalCoin decimal.Decimal, clientIp string, req *model.BuyProductReq) (*dao.TransactionOrder, error) {
	gameConfig, err := s.GetTmeGamesConfig(appId)
	if err != nil {
		logger.Warnf("加载游戏配置失败。err: %v", err)
		return nil, err
	}
	skuInfo, err := s.GetTmeGameSkuConfig(appId, req.SkuId)
	if err != nil {
		logger.Warnf("加载商品配置失败。err: %v", err)
		return nil, err
	}
	// 保存幂等信息
	idempotentDao := dao.NewCwsxIdempotentDao()
	orderId, err := client.GetGlobalId()
	if err != nil {
		logger.Error("获取GID失败。err: %v", err)
		return nil, err
	}
	idempotentInfo, err := idempotentDao.SaveIdempotentInfo(orderId, req.OutOrderNo)
	if err != nil {
		logger.Warnf("保存幂等信息失败。err: %v", err)
		return nil, err
	}
	// 解析游戏扩展参数
	platExtra, err := ParseExtra(req.PlatExtra)
	if err != nil {
		logger.Warnf("解析PlatExtra参数错误。err: %v", err)
		return nil, err
	}
	// 创建扣费订单
	month := dateutils.FormatYearMonth(idempotentInfo.Ordertime)
	order := &dao.TransactionOrder{
		Appid:          appId,
		Orderno:        idempotentInfo.Orderid,
		Ordertime:      idempotentInfo.Ordertime,
		Outorderno:     req.OutOrderNo,
		Kugouid:        kugouId,
		Openid:         openId,
		Accessid:       gameConfig.AccessId,
		Status:         0,
		Deliverystatus: 0,
		Skuid:          skuInfo.Sku,
		Quantity:       req.Quantity,
		Totalcoin:      totalCoin,
		Clientip:       clientIp,
		Stdplat:        cast.ToInt32(platExtra["stdPlat"]),
		Extrainfo:      req.GameExtra,
		Cointype:       req.CoinType,
		Createtime:     time.Now(),
		Updatetime:     time.Now(),
	}
	logger.Warnf(cast.ToString(order))
	transactionOrderDao := dao.NewTransactionOrderDao()
	affected, err := transactionOrderDao.InsertIgnore(month, order)
	if err != nil {
		logger.Error("保存请求订单异常。order: %v, err: %v", order, err)
		return nil, err
	}
	if affected > 0 {
		return order, nil
	}
	order, err = transactionOrderDao.Find(month, order.Outorderno)
	if err != nil {
		logger.Error("查询重复订单异常。order: %v, err: %v", order, err)
		return nil, err
	}
	return order, nil
}

func (s *Service) RetryUnfinishedOrders() {
	scanMinutes := helper.GetApolloClient().GetIntValue("fixOrder.scanMinutes", 60)
	endTime := carbon.Now().AddMinutes(-5).Time
	startTime := carbon.NewCarbon(endTime).AddMinutes(-scanMinutes).Time
	limit := helper.GetApolloClient().GetIntValue("fixOrder.limit", 10)
	logger.Warnf("重试异常订单，扫描范围。startTime: %v, endTime: %v, limit: %v",
		carbon.NewCarbon(startTime).String(), carbon.NewCarbon(endTime).String(), limit)
	// 处理扣费异常订单
	orders1, err := dao.NewTransactionOrderDao().Query(startTime, endTime, 0, 0, limit)
	if err != nil {
		logger.Warnf("处理扣费异常订单失败。err: %v", err)
		return
	}
	lo.ForEach(orders1, func(order *dao.TransactionOrder, index int) {
		logger.Warnf("开始处理扣费异常订单。order: %v", cast.ToString(order))
		s.ConfirmConsumeOrder(order)
		return
	})
	// 处理发货异常订单
	orders2, err := dao.NewTransactionOrderDao().Query(startTime, endTime, 1, 0, limit)
	if err != nil {
		logger.Warnf("处理发货异常订单失败。err: %v", err)
		return
	}
	lo.ForEach(orders2, func(order *dao.TransactionOrder, index int) {
		logger.Warnf("开始处理发货异常订单。order: %v", cast.ToString(order))
		s.Delivery(order)
		return
	})
}

func (s *Service) ConfirmConsumeOrder(order *dao.TransactionOrder) {
	consumeStatus := client.QueryOrderInfo(order)
	if consumeStatus == 1 || consumeStatus == 2 {
		month := dateutils.FormatYearMonth(order.Ordertime)
		rowsAffected, err := dao.NewTransactionOrderDao().UpdateStatus(month, order.Orderno, consumeStatus)
		if err != nil || rowsAffected < 1 {
			logger.Warnf("更新订单扣费状态失败。order: %v, err: %v", cast.ToString(order), err)
			return
		}
		logger.Warnf("更新订单扣费状态成功。order: %v, err: %v", cast.ToString(order), err)
	}
}

func (s *Service) CalculateTotalCoin(skuInfo model.SkuInfo, quantity int32, coinType int32) decimal.Decimal {
	totalCoin := skuInfo.Coin.Mul(decimal.NewFromInt32(quantity))
	if coinType == 1 {
		totalCoin = totalCoin.Mul(decimal.NewFromInt32(100))
	}
	logger.Warnf("根据SKU件数计算商品总价。skuInfo: %+v, quantity: %v, coinType: %v, totalCoin: %v",
		skuInfo, quantity, coinType, totalCoin)
	return totalCoin
}
