package tme

// 游戏中台信息，openId和uid二选一
type GameMiddleInfo struct {
	GameAppId  string `json:"gameAppId"`  // 游戏中台分配的appId
	GameOpenId string `json:"gameOpenId"` // 游戏中台分配的openId
	Uid        string `json:"uid"`        // 游戏内用户的id
}

// 支付应用信息
type PayApp struct {
	ActId      string `json:"actId"`      // 活动id
	BusinessId int64  `json:"businessId"` // 支付businessid
}

// 商品信息
type GoodsItem struct {
	GoodsId string `json:"goodsId"` // 商品id
	Num     int64  `json:"num"`     // 数量
}

// 支付信息
type PayInfo struct {
	GoodsItems []GoodsItem       `json:"goodsItems"`       // 商品信息
	MapExt     map[string]string `json:"mapExt,omitempty"` // 额外信息
}

// 打赏信息
type GiveGiftsInfo struct {
	RecvGameMiddleInfo GameMiddleInfo `json:"recvGameMiddleInfo"` // 被打赏者
	PayInfo            PayInfo        `json:"payInfo"`            // 支付信息
}

// 扣费场景信息
type PaySceneInfo struct {
	PaySceneType int32  `json:"paySceneType"` // 支付场景类型
	AnchorId     string `json:"anchorId"`     // 主播id
	RoomId       string `json:"roomId"`       // 房间id
	ShowId       string `json:"showId"`       // 直播id
	UgcId        string `json:"ugcId"`        // 作品id
}

// 扣费并打赏请求
type PayAndGiveGiftsReq struct {
	GameMiddleInfo GameMiddleInfo  `json:"gameMiddleInfo"`         // 游戏中台信息
	PayApp         PayApp          `json:"payApp"`                 // 支付应用信息
	PayInfo        PayInfo         `json:"payInfo"`                // 扣费信息
	PayAmount      int64           `json:"payAmount"`              // 总价
	GiveGiftsInfos []GiveGiftsInfo `json:"giveGiftsInfos"`         // 打赏信息
	PaySceneInfo   *PaySceneInfo   `json:"paySceneInfo,omitempty"` // 扣费场景信息
	OrderId        string          `json:"orderId"`                // 订单id
	AppName        string          `json:"appName"`
}

// 发礼包请求
type SendGiftPackageReq struct {
	GameMiddleInfo GameMiddleInfo    `json:"gameMiddleInfo"`        // 游戏中台信息
	GiftPackageId  int64             `json:"giftPackageId"`         // 礼包id
	Num            int64             `json:"num"`                   // 礼包数量
	OrderId        string            `json:"orderId"`               // 订单id
	Program        string            `json:"program"`               // 主调业务
	Reason         string            `json:"reason,omitempty"`      // 发放原因
	Identifiers    string            `json:"identifiers,omitempty"` // 标识某个具体业务的发放
	SendTs         int64             `json:"sendTs"`                // 发放时间
	MapExt         map[string]string `json:"mapExt,omitempty"`      // 限制个数4个以内
	ExtensionId    int32             `json:"extensionId,omitempty"` // 业务id
	AppName        string            `json:"appName"`
}

// 扣费请求
type PayReq struct {
	GameMiddleInfo GameMiddleInfo `json:"gameMiddleInfo"`         // 游戏中台信息
	PayApp         PayApp         `json:"payApp"`                 // 支付应用信息
	PayInfo        PayInfo        `json:"payInfo"`                // 扣费信息
	PayAmount      int64          `json:"payAmount"`              // 总价
	PaySceneInfo   *PaySceneInfo  `json:"paySceneInfo,omitempty"` // 扣费场景信息
	OrderId        string         `json:"orderId"`                // 订单id
	AppName        string         `json:"appName"`
}

// 查询订单状态请求
type GetOrderStatusReq struct {
	OrderId string `json:"orderId"` // 订单ID
	AppName string `json:"appName"`
}
