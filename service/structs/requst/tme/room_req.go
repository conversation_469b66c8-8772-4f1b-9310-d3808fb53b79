package tme

// 查询房间信息请求
type GetRoomInfoReq struct {
	RoomId  string `json:"roomId"` // 房间ID
	AppName string `json:"appName"`
}

// 批量查询房间信息请求
type BatchGetRoomInfoReq struct {
	RoomIdList []string `json:"roomIdList"` // 房间ID列表
	AppName    string   `json:"appName"`
}

// 推荐列表请求
type GetRecommendRoomReq struct {
	UserId  string `json:"userId"`            // 用户ID
	IdType  int32  `json:"idType"`            // UserIDType 用户ID类型; 默认0为openid; 1为端内登录态UID; 2为加密UID
	FromTag string `json:"fromTag,omitempty"` // 推荐标识
	AppName string `json:"appName"`
}

type BatchGetRoomInfoWithUserIDReq struct {
	UserIdList []string `json:"userIdList"` // 用户ID列表
	AppName    string   `json:"appName"`
}
