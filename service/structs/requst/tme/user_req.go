package tme

// 获取用户资料请求
type GetProfileReq struct {
	AppId        string `json:"appId"`          // 应用ID
	OpenId       string `json:"openId"`         // 用户OpenID
	AvatarLength int32  `json:"avatarLength"`   // 头像长度
	AvatarWidth  int32  `json:"avatarWidth"`    // 头像宽度
	AppName      string `json:"appName"`        //平台名称
	Mask         *int64 `json:"mask,omitempty"` // 0-默认 1-特权信息 (可选)
	IdType       int32  `json:"idType"`
}
