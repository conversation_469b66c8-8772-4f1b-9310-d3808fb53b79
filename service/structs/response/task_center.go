package response

type ErrorResp struct {
	ErrorCode int32  `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
}

type PlatUserAsset struct {
	AssetId int64 `json:"asset_id"`
	AssetNm int64 `json:"asset_num"`
}

type MapPlatUserAsset map[int64]*PlatUserAsset

type QueryAssetResp struct {
	ErrorCode int32            `json:"error_code"`
	ErrorMsg  string           `json:"error_msg"`
	MapResult MapPlatUserAsset `json:"map_result"`
}

type ChangeAssetResp struct {
	ErrorCode int32            `json:"error_code"`
	ErrorMsg  string           `json:"error_msg"`
	BillNo    string           `json:"bill_no"`
	MapResult MapPlatUserAsset `json:"map_result"`
}

type AdvertCheckResp struct {
	ErrorCode int32  `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
	TraceId   string `json:"traceId"`
	Result    int32  `json:"result"`
	RewardNum uint64 `json:"rewardNum"`
}

type SafeData struct {
	SafeType int32 `json:"safe_type"`
}

type SafeCheckResp struct {
	ErrorCode int32    `json:"error_code"`
	ErrorMsg  string   `json:"error_msg"`
	Data      SafeData `json:"data"`
}

type AssetBillCheckResp struct {
	ErrorCode int32  `json:"errorCode"`
	ErrorMsg  string `json:"errorMsg"`
}
