package service

import (
	"encoding/json"
	"fmt"
	"git.kugou.net/fxgo/core/logger"
	"github.com/AlekSi/pointer"
	"kugou_adapter_service/client"
	"kugou_adapter_service/constant"
	"kugou_adapter_service/dao"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/model"
	"kugou_adapter_service/third-party/gen-go/platform_order_servicev2"
	"kugou_adapter_service/utils/dateutils"
	"kugou_adapter_service/utils/thrift"
)

func (s *Service) InvokeConsumeCoin(order *dao.TransactionOrder, skuInfo model.SkuInfo, platAppId int32, platToken string) (bool, error) {
	if order.Cointype == 0 {
		return s.ConsumeFxCoin(order, skuInfo, platAppId, platToken), nil
	}
	if order.Cointype == 1 {
		return s.ConsumeDogCoin(order, skuInfo, platAppId, platToken), nil
	}
	return false, fmt.Errorf("不支持的扣费类型")
}

func (s *Service) ConsumeFxCoin(order *dao.TransactionOrder, skuInfo model.SkuInfo, platAppId int32, platToken string) bool {
	signRequest := s.createSignOrderRequest(order)
	orderRequest, err := s.createOrderRequest(order, platAppId, platToken, signRequest)
	if err != nil {
		logger.Warnf("创建订单失败。err: %v", err)
		return false
	}
	consumeRequests := []*platform_order_servicev2.ConsumeRequest{
		{
			ConsumeId:   order.Orderno,
			ConsumeType: 1,
			AssetType:   0,
			AssetId:     0,
			AssetName:   "",
			Ext:         pointer.ToString(""),
			Num:         order.Totalcoin.String(),
			ConsumeOption: &platform_order_servicev2.ConsumeOption{
				ActionId:      pointer.ToInt32(0),
				Category:      pointer.ToInt32(11),
				FxcChangeDesc: pointer.ToString("购买" + skuInfo.ConsumeGiftName),
				GiftId:        &skuInfo.ConsumeGiftId,
				GiftName:      &skuInfo.ConsumeGiftName,
				GiftNum:       &order.Quantity,
				ToKugouId:     pointer.ToInt64(0),
				UserAgent:     pointer.ToString("-"),
				Ext:           pointer.ToString(""),
			},
		},
	}
	consumeStatus := client.CreateConsumeOrder(orderRequest, consumeRequests)
	updateOrderStatus(order, consumeStatus)
	return consumeStatus == 1
}

func (s *Service) createSignOrderRequest(order *dao.TransactionOrder) *platform_order_servicev2.SignOrderRequest {
	return &platform_order_servicev2.SignOrderRequest{
		OrderId:         order.Orderno,
		AccessId:        order.Accessid,
		KugouId:         order.Kugouid,
		OrderExpireTime: 0,
		Time:            order.Ordertime,
		TriggerTime:     0,
		Ext:             "",
	}
}

func (s *Service) ConsumeDogCoin(order *dao.TransactionOrder, skuInfo model.SkuInfo, platAppId int32, platToken string) bool {
	serverId := helper.GetApolloClient().GetIntValue("serverId", 3428)
	serverKey := helper.GetApolloClient().GetStringValue("serverKey", "HvVhkiqgFw7U0g8Km6VfdnqlD4rbLsdW")
	fxcChangeDesc := "购买" + skuInfo.ConsumeGiftName
	dogCoinDto := model.DogCoinDto{
		BzName:       "livegame",
		Title:        fxcChangeDesc,
		Remark:       fxcChangeDesc,
		AppId:        1010,
		BizOrderType: "1",
		Clientver:    0,
		Serverid:     serverId,
		MainKey:      serverKey,
	}
	extData := map[string]model.DogCoinDto{}
	extData["dogCoinRequestExt"] = dogCoinDto
	marshal, err := json.Marshal(extData)
	if err != nil {
		logger.Warnf("组装ext失败。extData: %+v, err: %v", extData, err)
		return false
	}
	ext := string(marshal)
	signRequest := s.createSignOrderRequest(order)
	signRequest.Ext = ext
	orderRequest, err := s.createOrderRequest(order, platAppId, platToken, signRequest)
	if err != nil {
		logger.Warnf("创建订单失败。err: %v", err)
		return false
	}
	actionId := int32(0)
	toKugouId := int64(0)
	category := int32(11)
	userAgent := "-"
	consumeRequests := []*platform_order_servicev2.ConsumeRequest{
		{
			ConsumeId:   order.Orderno,
			ConsumeType: 136,
			AssetType:   145,
			AssetId:     2000001025,
			AssetName:   "狗狗币",
			Ext:         &ext,
			Num:         order.Totalcoin.String(),
			ConsumeOption: &platform_order_servicev2.ConsumeOption{
				ActionId:      &actionId,
				Category:      &category,
				FxcChangeDesc: &fxcChangeDesc,
				GiftId:        &skuInfo.ConsumeGiftId,
				GiftName:      &skuInfo.ConsumeGiftName,
				GiftNum:       &order.Quantity,
				ToKugouId:     &toKugouId,
				UserAgent:     &userAgent,
				Ext:           &ext,
			},
		},
	}
	consumeStatus := client.CreateConsumeOrder(orderRequest, consumeRequests)
	updateOrderStatus(order, consumeStatus)
	return consumeStatus == 1
}

func updateOrderStatus(order *dao.TransactionOrder, consumeStatus constant.ConsumeStatus) {
	if consumeStatus > 0 {
		month := dateutils.FormatYearMonth(order.Ordertime)
		affected, err := dao.NewTransactionOrderDao().UpdateStatus(month, order.Orderno, consumeStatus)
		if err != nil || affected < 1 {
			logger.Warn("更新订单状态失败。orderNo: %v, consumeStatus: %v, err: %v", order.Orderno, consumeStatus, err)
		}
	}
}

func (s *Service) createOrderRequest(order *dao.TransactionOrder, platAppId int32, platToken string, signRequest *platform_order_servicev2.SignOrderRequest) (*platform_order_servicev2.OrderRequest, error) {
	accessKey, err := client.GetAccessKey(signRequest.AccessId)
	if err != nil {
		logger.Warnf("获取AccessKey失败。err: %v", err)
		return nil, err
	}
	orderRequest := &platform_order_servicev2.OrderRequest{
		OrderId:         signRequest.OrderId,
		AccessId:        signRequest.AccessId,
		KugouId:         signRequest.KugouId,
		OrderExpireTime: signRequest.OrderExpireTime,
		Time:            signRequest.Time,
		TriggerTime:     &signRequest.TriggerTime,
		Ext:             &signRequest.Ext,
		CommonParameter: BuildCommonParameter(order, platAppId, platToken),
		Sign:            thrift.SignWithComma(signRequest, *accessKey),
	}
	return orderRequest, nil
}
