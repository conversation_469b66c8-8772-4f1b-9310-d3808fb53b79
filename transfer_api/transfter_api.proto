syntax = "proto3";

package kg.transfer_api;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/transfer_api";

import "google/api/annotations.proto";

// 微服务中转接口
service TransferApi {
  rpc GlobalMicro(GlobalMicroReq) returns (GlobalMicroRsp) {
    option (google.api.http) = {
      post: "/transfer/global_micro"
      body: "*"
    };
  }
}

message GlobalMicroReq {
  string body = 1;
  // v2 指定 请求方法
  // 例子1 http调用: cloud://*********:65862/xian.xymc/game/app/corner
  // 例子2 微服务调用: cloud://xian.xymc.Game/AppModule
  // 例子3 微服务通过L5地址调用: cloud://xian.xymc.Game/AppModule?l5=*********:65862
  string addrInfo = 2;
  string appIdKey = 3;
  map<string, string> mpInUserKey = 4;
  // map<string, string> mpOutUserKey = 5; 暂不处理

}

message GlobalMicroRsp {
  string rtnBody = 1;
}
