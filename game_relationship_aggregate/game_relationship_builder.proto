syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_relationship_aggregate";

import "google/api/annotations.proto";
import "pb/game_relationship/comm.proto";

service RelationshipAggregate {
  // 心跳上报
  rpc ReportHeartbeat(HeartbeatReportReq) returns (HeartbeatReportRsp) {
    option (google.api.http) = {
      post: "/heartbeat/report"
      body: "*"
    };
  }
  // 小游戏登录上报
  rpc ReportLogin(LoginReportReq) returns (LoginReportRsp) {
    option (google.api.http) = {
      post: "/login/report"
      body: "*"
    };
  }
}

message LoginReportReq {
  int64 uid = 1;
  string appId = 2;
  int64 ts = 3;
}

message LoginReportRsp {
  string msg = 1;
}

enum HeartType {
  ReportTypeUnknown = 0;
  ReportTypeEnter = 1; // 进入
  ReportTypeHeartbeat = 2; // 心跳
  ReportTypeLeave = 3; // 离开
}

message HeartbeatReportReq {
  int64 uid = 1;
  HeartType heartbeatType = 2;
  RoomType roomType = 3;
  string roomId = 4;
  string showId = 5;
  int64 beginTime = 6;
  int64 reportTime = 7;
}

message HeartbeatReportRsp {
  string msg = 1;
}
