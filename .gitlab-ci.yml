stages:
  - build

build:
  stage: build
  script:
    - cd ..
    - rm -rf tme_game_pb
    - cd tme_game_pb2go
    - git checkout .
    - git pull
    - rm -rf pb
    - git clone -b master http://tcode.tmeoa.com/tme_game/tme_game_pb.git pb
    - go install tcode.tmeoa.com/tme_game/tme_game_plib/protoc-gen-grpc-wrapper@latest
    - go get -u tcode.tmeoa.com/tme_game/tme_game_plib
    - make proto
    - cd pb
    - git checkout .
    - cd ..
    - git add .
    - git status
    - git commit -am 'update pb2go by gitlab-runner' || true
    - git push
  only:
    - master

protosync:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  tags:
    - cloverwu
  script:
    - rm -rf third_party/googleapis
    - sed -i -e 's/^import "pb\/\(.*\)";$/import "\1";/g' **/*.proto
    - protoc --include_imports --descriptor_set_out=fds --proto_path=${CI_PROJECT_DIR} --proto_path=${GITLIB_RUNNER_WORKSPACE_ENV}/protobuf ${CI_PROJECT_DIR}/**/*.proto


  
