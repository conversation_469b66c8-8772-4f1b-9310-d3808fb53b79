syntax = "proto3";

package adapter_geo;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_geo";

service AdapterGeo {
  rpc Query(QueryReq) returns (QueryRsp);
}

message QueryReq {
  string ip = 1;
}

enum Source {
  SourceDefault = 0; // 集团IP地址查询
  SourceIP2Region = 1; // IP2Region本地查询
  SourceFail = 2; // 绑定失败走了兜底
}

message QueryRsp {
  string countryCode = 1;
  string countryName = 2;
  string provinceCode = 3;
  string provinceName = 4;
  string cityCode = 5;
  string cityName = 6;
  int32 source = 7; // Source
}
