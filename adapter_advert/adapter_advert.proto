syntax = "proto3";

package adapter_advert;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_advert";

// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
service AdapterAdvert {
  // 广告曝光校验
  rpc AdvertCheck(AdvertCheckReq) returns (AdvertCheckRsp);
  // 获取广告信息
  rpc AdvertInfo(AdvertInfoReq) returns (AdvertInfoRsp);
  // 领取广告奖励
  rpc AdvertReceiveReward(AdvertReceiveRewardReq) returns (AdvertReceiveRewardRsp);
  // 回调广告通知领奖结果
  rpc AdvertRewardCallback(AdvertRewardCallbackReq) returns (AdvertRewardCallbackRsp);
}

enum AdCheckMask {
  AdCheckMaskNone = 0; //只需校验主广告曝光
  AdCheckMaskDownloadActive = 1; //需要校验广告点击激活是否通过
}

message AdvertCheckReq {
  string appId = 1;
  string openId = 2;
  string adToken = 3; // 广告曝光token
  string adPosId = 4; // 广告位id
  string qimei36 = 5;
  string sceneId = 6; // 场景id(可选)
  AdCheckMask adCheckMask = 7;
  map<string, string> mapExt = 8; // 自定义透传
}

message AdvertCheckRsp {
  string traceId = 1; // 广告曝光唯一id
  int32 result = 2; // 曝光校验结果，0是校验通过
  uint64 rewardNum = 3; // 广告ecpm数值奖励数量
  string EncodedEcpm = 4; // ecpm加密值
  uint32 ecpmCoin = 5; // ecpm换算成金币数
}

enum RewardResultType {
  Reward_Default = 0; //默认值
  Reward_Success = 1; //领奖达成
  Reward_Fail = 2; //领奖失败
  Reward_Timeout = 3; //领奖超时
}

message RewardContent {
  string reward_type = 1; // 协商奖励类型标识 1:免费时长(单位秒)，2:k歌金币，3:免广告时长(单位秒) 4: 短剧金币 5：使用次数
  int64 reward_amount = 2; // 奖励数值
  int64 cost = 3; // (非必填) 奖励成本,换算为人民币千分之一分，即人民币分/1000,做风控估算参考
  string task_id = 4; // (非必填) 任务id，如果通过任务平台则填入
  string reward_id = 5; // (非必填) 奖励id，如果奖励有对应唯一id则可以填入，方便定位追踪
}

message AdvertRewardCallbackReq {
  string appId = 1;
  string openId = 2;
  string adToken = 3; // 广告曝光token
  string adPosId = 4; // 广告位id
  string qimei36 = 5;
  string sceneId = 6; // 场景id(可选)
  RewardResultType result = 7;
  RewardContent reward_content = 8;
}

message AdvertRewardCallbackRsp {
  int32 ret_code = 1; // 错误码
  string ret_msg = 2; // 失败返回错误信息
}

enum IncentiveType {
  IncentiveTypeNone = 0; //非法类型
  IncentiveTypeBI = 1; //使用后台下发的激励广告奖励数值
  IncentiveTypeECPM = 2; //使用商广返回的激励广告奖励数值（基于ecpm）
}

message AdvertScene {
  // 广告位id
  string adPosId = 1;
  // 场景id(可选)
  string sceneId = 2;
}

message AdvertInfoReq {
  string appId = 1;
  string openId = 2;
  // 广告场景
  repeated AdvertScene advertSceneList = 3;
}

message AdvertInfo {
  // 广告奖励数值类型
  IncentiveType incentiveType = 1;
  // 后台计算出的金币值
  uint64 rewardNum = 2;
  // 是否展示广告
  bool showAdvert = 3;
  // 剩余广告次数(仅用于前端展示次数，是否能看广告用showAdvert字段判断)
  uint32 leftAdvert = 4;
}

message AdvertInfoRsp {
  // 广告信息
  repeated AdvertInfo advertInfoList = 1;
}

message AdvertReceiveRewardReq {
  string appId = 1;
  string openId = 2;
  string adToken = 3; // 广告曝光token
  string adPosId = 4; // 广告位id
  string qimei36 = 5;
  string sceneId = 6; // 场景id(可选)
}

message AdvertReceiveRewardRsp {
  string traceId = 1; // 广告曝光唯一id
  int32 result = 2; // 领取结果，0是成功
  uint64 rewardNum = 3; // 广告ecpm数值奖励数量
}
