syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_fw";

enum StyleType {
  STYLE_TYPE_DEFAULT = 0; // 默认样式(图片轮播)
}

enum SceneType {
  SCENE_TYPE_NONE = 0;
  SCENE_TYPE_PENDING = 1;       // 组局页
  SCENE_TYPE_GAMEING = 2;       // 游戏页
  SCENE_TYPE_GAMEOVER = 3;      //  结算页
  SCENE_TYPE_PUBLIC_SQUARE = 4; // 大厅页
  SCENE_TYPE_CREATE_GAME = 5;   // 创建页
}

message Button {
  string button_doc = 1;       // 按钮文案
  string button_url = 2;       // 按钮图片
  string button_color = 3;     // 按钮颜色
  string button_doc_color = 4; // 按钮文案字体颜色
}

message Content {
  string content_url = 1; // 内容图片
  string content_doc = 2; // 内容文案
}

// 默认样式浮窗
message DefaultWindows {
  string top_url = 1;            // 顶图url
  string border_url = 2;         // 边框url
  string title_doc = 3;          // 弹窗大标题文案
  string theme_doc = 4;          // 主题文案
  string bg_url = 5;             // 弹窗背景图
  string bg_color = 6;           // 弹窗背景色
  string content_doc = 7;        // 内容文案
  string content_bg_color = 8;   // 内容背景色
  string content_bg_url = 9;     // 内容背景图
  repeated Content content = 10; // 内容图片(多张)
  int32 content_url_ms = 11;   // 内容图片轮播时间间隔, 单位:毫秒
  Button yes_button = 12;      // 确认按钮
  Button cancel_button = 13;   // 取消按钮
  int32 show_ms = 14;          // 展示时间, 0表示不消失, 单位:毫秒
  string end_button_url = 15;  // 关闭按钮url
  string title_doc_color = 16; // 弹窗大标题文案 字体颜色
  string theme_doc_color = 17; // 主题文案 字体颜色
  string content_doc_color = 18;          // 内容文案 字体颜色
  string content_bottom_point_color = 19; // 内容图片底部轮播按钮颜色
  string jump_url = 20;                   // 跳转链接
}

// ckv+ 配置
message FreqPeriod {
  int32 begin = 1; // 开始时间(每日08:00:00)
  int32 end = 2;   // 结束时间(每日23:00:00)
  int32 cnt = 3;   // 频控次数(0不限制)
}

message CkvPlusCommon {
  int32 id = 1;
  int64 sts = 2;                        // 开始时间
  int64 ets = 3;                        // 结束时间
  string appid = 4;                     // 游戏appid
  int64 scene_mask = 5;                 // 可见行场景mask
  int64 condition_id = 6;               // 定向id
  int64 freq_limit = 7;                 // 可见次数(0不限制)
  repeated FreqPeriod freq_periods = 8; // 每日可见时间段
  int32 show_ms = 9;   // 展示时间, 0表示不消失, 单位:毫秒
  int32 priority = 10; // 优先级(值越小优先级高)
}

message CkvPlusDefaultWindows {
  CkvPlusCommon comm = 1;        // comm
  string top_url = 2;            // 顶图url
  string border_url = 3;         // 边框url
  string title_doc = 4;          // 弹窗大标题文案
  string theme_doc = 5;          // 主题文案
  string bg_url = 6;             // 弹窗背景图
  string bg_color = 7;           // 弹窗背景色
  string content_doc = 8;        // 内容文案
  string content_bg_color = 9;   // 内容背景色
  string content_bg_url = 10;    // 内容背景图
  repeated Content content = 11; // 内容图片(多张)
  int32 content_url_ms = 12;   // 内容图片轮播时间间隔, 单位:毫秒
  Button yes_button = 13;      // 确认按钮
  Button cancel_button = 14;   // 取消按钮
  string end_button_url = 15;  // 关闭按钮url
  string title_doc_color = 16; // 弹窗大标题文案 字体颜色
  string theme_doc_color = 17; // 主题文案 字体颜色
  string content_doc_color = 18;          // 内容文案 字体颜色
  string content_bottom_point_color = 19; // 内容图片底部轮播按钮颜色
  string jump_url = 20;                   // 跳转链接
}

message CkvPlusWindows {
  StyleType style = 1;               // 样式
  CkvPlusDefaultWindows default = 2; // 默认样式
}

message CkvPlusWindowsConf {
  message List { repeated CkvPlusWindows windows = 1; }
  map<string, List> mpWindows = 1; // key: 游戏appid
  int64 ts = 2;
}

message FreqCnt {
  int32 cnt = 1;
  int32 ts = 2;
}
