syntax = "proto3";

package interactive_game_quick_gift;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game_quick_gift/qucik_gift_api";

import "pb/interactive_game_quick_gift/common/common.proto";

//与 web 交互
service InteractiveGameQuickGiftApi {
  //查询收礼列表
  rpc QueryRecvRecord(QueryRecvRecordReq) returns (QueryRecvRecordRsp);
  //更新收礼详情是否回礼
  rpc UpdateRecvRecord(UpdateRecvRecordReq) returns (UpdateRecvRecordRsp);
}

message QueryRecvRecordReq {
  string openid = 1; //用户openid
  string game_appid = 2; //游戏appid
  string current_matchid = 3; //当前游戏的场次ID
  string pass_back = 4; //passback 透传后台,第一次传空
  //int64 query_type = 5; //查询类型 QueryType
  uint32 query_type = 6; //查询类型 QueryType
}

message QueryRecvRecordRsp {
  // int64 hase_more = 1; //是否还有 0:没有  1:还有
  string pass_back = 2; //passback
  string game_name = 3; //游戏名称
  // common.GiftInfo back_gift = 4; //回礼的礼物信息
  repeated common.RecordItem items = 5; //记录列表
  uint32 hase_more = 6; //是否还有 0:没有  1:还有
}

message UpdateRecvRecordReq {
  string openid = 1; //用户openid
  string game_appid = 2; //游戏appid
  common.RecordItem items = 3; //要更改的item
}

message UpdateRecvRecordRsp {
  //int64 result_code = 1;
  uint32 result_code = 2;
}
