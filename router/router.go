package router

import (
	"github.com/gin-gonic/gin"
	"kugou_adapter_service/controller"
	"kugou_adapter_service/controller/tme"
)

func Register(engine *gin.Engine) {

	// k8s health check
	engine.GET("/info", func(c *gin.Context) {
		c.String(200, "ok")
	})

	adapter := engine.Group("kugou_adapter")

	// 公网接口
	outer := adapter.Group("public")
	{
		securityRouter := outer.Group("security")
		{
			securityController := &controller.SecurityController{}
			securityRouter.POST("/safeCheck", securityController.SafeCheck)
		}

		authRouter := outer.Group("/auth")
		{
			authController := &controller.AuthController{}
			authRouter.POST("/checkRealName", authController.CheckRealName)
		}

		consumeRouter := outer.Group("/consume")
		{
			consumeController := &controller.ConsumeController{}
			consumeRouter.POST("/buyProduct", consumeController.BuyProduct)
			consumeRouter.GET("/getUserBalance", consumeController.GetUserBalance)
			consumeRouter.Any("/debug", consumeController.Debug)
		}

	}

	// 内网接口
	inner := adapter.Group("private")
	{
		// 鉴权
		authRouter := inner.Group("/auth")
		{
			authController := &controller.AuthController{}
			authRouter.POST("/getAccessToken", authController.GetAccessToken)
			authRouter.POST("/checkToken", authController.CheckToken)
			authRouter.POST("/kugouAuth", authController.KugouAuth)
		}

		// 共建游戏所需酷狗平台侧功能
		openRouter := inner.Group("/open")
		{
			userController := &controller.UserController{}

			// 用户
			openRouter.POST("/getProfile", userController.GetProfile)
			openRouter.POST("/batchGetProfile", userController.BatchGetProfile)
			openRouter.GET("/queryFriend", userController.QueryFriend)

			// 私信
			mailController := &controller.MailController{}
			openRouter.POST("/sendMail", mailController.SendMail)
		}

		// 资产
		asset := inner.Group("/asset")
		{
			assetController := &controller.AssetController{}

			// 金币查询
			asset.POST("/revenue/queryAsset", assetController.QueryAsset)

			// 金币扣减
			asset.POST("/revenue/subAsset", assetController.SubAsset)

			// 金币增加
			asset.POST("/revenue/addAsset", assetController.AddAsset)
		}

		// adapter_advert
		ad := inner.Group("/adapter_advert")
		{
			adController := &controller.AdController{}
			// 广告校验
			ad.POST("/advertCheck", adController.AdValidation)
		}

		// adapter_safety
		safety := inner.Group("/adapter_safety")
		{
			safetyController := &controller.SafetyController{}
			safety.POST("/safeCheck", safetyController.SafeCheck)
			safety.POST("/syncSafeCheckV2", safetyController.SyncSafeCheckV2)
		}

		// adapter_revenue
		revenue := inner.Group("/adapter_revenue")
		{
			revenueController := &controller.RevenueController{}
			revenue.POST("/sendSingleReward", revenueController.SendSingleReward)
			revenue.POST("/rewardDetailv2", revenueController.RewardDetail)
			revenue.POST("/sendSingleRewardCallback", revenueController.SendSingleRewardCallback)
		}

		// adapter_unified_assets
		unifiedAsset := inner.Group("/adapter_unified_assets")
		{
			unifiedAssetController := &controller.UnifiedAssetsController{}
			unifiedAsset.POST("/batch_get_gift_info", unifiedAssetController.BatchGetGiftInfo)
			unifiedAsset.POST("/batch_get_welfare_info", unifiedAssetController.BatchGetGiftPackage)
			unifiedAsset.POST("/send_welfare", unifiedAssetController.SendGiftPackage)
			unifiedAsset.POST("/buy_goods", unifiedAssetController.Pay)
		}

		// adapter_room
		roomMsg := inner.Group("/adapter_room")
		{
			roomMsgController := &controller.RoomMsgController{}
			roomMsg.POST("/room_msg", roomMsgController.RoomMsg)
			roomMsg.POST("/big_horn_msg", roomMsgController.BigHornMsg)
		}

		// 定时
		cron := inner.Group("cron")
		{
			cronController := &controller.CronController{}
			cron.Any("/fixConsumeOrder", cronController.FixConsumeOrder)
			cron.Any("/debug", cronController.Debug)
			cron.Any("/cacheSimpleGiftInfos", cronController.CacheSimpleGiftInfos)
		}

		tmeGroup := inner.Group("tme")
		{
			assetController := &tme.TmeAssetController{}
			tmeGroup.Any("/asset/payAndGiveGifts", assetController.PayAndGiveGifts)
			tmeGroup.Any("/asset/sendGiftPackage", assetController.SendGiftPackage)
			tmeGroup.Any("/asset/getOrderStatus", assetController.GetOrderStatus)

			roomController := &tme.TmeRoomController{}
			tmeGroup.Any("/room/getRoomInfo", roomController.GetRoomInfo)
			tmeGroup.Any("/room/batchGetRoomInfo", roomController.BatchGetRoomInfo)
			tmeGroup.Any("/room/getRecommendRoom", roomController.GetRecommendRoom)
			tmeGroup.Any("/room/batchGetRoomInfoWithUserId", roomController.BatchGetRoomInfoWithUserID)

			userController := &tme.TmeUserController{}
			tmeGroup.Any("/user/getProfile", userController.GetProfile)

			safeController := &tme.TmeSafeController{}
			tmeGroup.Any("/safe/safeCheck", safeController.SafeCheck)
		}
	}
}
