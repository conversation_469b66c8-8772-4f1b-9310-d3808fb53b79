syntax = "proto3";

package component.gopenpf;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/gopenpf;gopenpf";

// GOpenPfService 游戏开放平台-资料托管服务
service GOpenPfService {
  // 资料获取
  rpc GetProfile(GetProfileReq) returns (GetProfileRsp);
  // 资料更新
  rpc UpdateProfile(UpdateProfileReq) returns (UpdateProfileRsp);
}

enum EMask {
  EMask_None = 0;
  EMask_Nick = 0x1;   // 用户昵称
  EMask_Avatar = 0x2; // 用户头像
  EMask_Uid = 0x4;    // 用户uid
}

// EField 用于资料存储字段
enum EField {
  EField_None = 0;
  EField_Nick = 1;   // 用户昵称
  EField_Avatar = 2; // 用户头像
  EField_Uid = 3;    // 用户uid
}

message UserInfo {
  string strNick = 1;   // 用户昵称
  string strAvatar = 2; // 用户头像
  string strUid = 3;    // 用户uid
}

// GetProfileReq 获取资料请求
message GetProfileReq {
  string strAppID = 1;  // 应用ID
  uint64 lPlatID = 2;   // 平台id
  uint64 lMask = 3;     // 资料掩码
  repeated string vecOpenids = 4; // 用户openid  最大批量100个
}
message GetProfileRsp {
  message Item {
    UserInfo stUserInfo = 1; // 用户资料
  }
  map<string, int32> mapFail = 1; // 转换失败, key: openid  value: error code
  map<string, Item> mapSucc = 2;  // 转换成功, key: openid  value: Item
}

// UpdateProfileReq 更新资料请求
message UpdateProfileReq {
  string strAppID = 1;  // 应用ID
  uint64 lPlatID = 2;   // 平台id
  uint64 lMask = 3;     // 资料掩码
  string strOpenid = 4; // 用户openid
  UserInfo stUserInfo = 5; // 用户资料
}
message UpdateProfileRsp {}