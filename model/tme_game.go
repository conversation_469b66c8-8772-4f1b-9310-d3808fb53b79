package model

import (
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type TmeGameHeader struct {
	XGOpenAppId      string `header:"x-gopen-app-id" binding:"required"`
	XGOpenId         string `header:"x-gopen-id"`
	XGOpenSessionKey string `header:"x-gopen-session-key"`
	XGOpenTs         string `header:"x-gopen-ts" binding:"required"`
	XGOpenSign       string `header:"x-gopen-sign"`
}

type TmeGame struct {
	AppId         string    `json:"appId"`
	AppName       string    `json:"appName"`
	AppKey        string    `json:"appKey"`
	AccessId      int32     `json:"accessId"`
	AccessKey     string    `json:"accessKey"`
	GameId        string    `json:"gameId"`
	GameKey       string    `json:"gameKey"`
	GameNotifyUrl string    `json:"gameNotifyUrl"`
	SkuList       []SkuInfo `json:"skuList"`
}

type SkuInfo struct {
	Sku             string          `json:"sku"`
	Coin            decimal.Decimal `json:"coin"`
	ConsumeGiftId   int32           `json:"consumeGiftId"`
	ConsumeGiftName string          `json:"consumeGiftName"`
	ConsumeGiftNum  int             `json:"consumeGiftNum"`
}

func (t TmeGame) GetSkuInfo(skuId string) (SkuInfo, bool) {
	return lo.Find(t.SkuList, func(skuInfo SkuInfo) bool {
		return skuInfo.Sku == skuId
	})
}

type SubAssetExt struct {
	SkuId     string `json:"skuId"`
	Quantity  int32  `json:"quantity"`
	GameExtra string `json:"gameExtra"`
	PlatExtra string `json:"platExtra"`
}
