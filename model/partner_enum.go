package model

type PartnerEnum int

const (
	K_GE   PartnerEnum = 54
	KUGOU  PartnerEnum = 55
	WESING PartnerEnum = 60
)

var partnerEnumNames = map[PartnerEnum]string{
	K_GE:   "k歌",
	KUGOU:  "酷狗",
	WESING: "k歌国际版",
}

func (p PartnerEnum) String() string {
	return partnerEnumNames[p]
}

func GetEnumById(id int) PartnerEnum {
	switch id {
	case int(K_GE):
		return K_GE
	case int(KUGOU):
		return KUGOU
	case int(WESING):
		return WESING
	default:
		return 0
	}
}

//func OfApp(app int) PartnerEnum {
//	switch app {
//	case AppEnum_KG_LIVE:
//		return KUGOU
//	case AppEnum_KG_MUSIC:
//		return KUGOU
//	case AppEnum_K_SONG:
//		return K_GE
//	case AppEnum_WE_SING:
//		return WESING
//	default:
//		return 0
//	}
//}
