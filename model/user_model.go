package model

type GameMiddleInfo struct {
	Uid        int64  `json:"uid,omitempty"`
	GameAppid  string `json:"game_appid,omitempty"`
	EncryptUid string `json:"encrypt_uid,omitempty"`
	Ipv4       uint32 `json:"ipv4,omitempty"`
	Ipv6       string `json:"ipv6,omitempty"`
	StrUid     string `json:"strUid,omitempty"`
	DeviceInfo string `json:"device_info,omitempty"`
}

type GetProfileReq struct {
	GameMiddleInfo GameMiddleInfo `json:"game_middle_info"`
	AvatarLength   int            `json:"avatar_length"`
	AvatarWidth    int            `json:"avatar_width"`
}

type BatchGetProfileReq struct {
	GameMiddleInfo GameMiddleInfo `json:"game_middle_info"`
	AvatarLength   int            `json:"avatar_length"`
	AvatarWidth    int            `json:"avatar_width"`
	UserList       []interface{}  `json:"uid_list"`
}

type QueryFriendReq struct {
	Uid   int64  `json:"uid"`
	AppId string `json:"app_id"`
	Mask  int    `json:"mask"`
}

type UserInfoVo struct {
	NickName      string `json:"nickname"`
	Avatar        string `json:"avatar"`
	EncryptUID    string `json:"encrypt_uid"`
	TreasureLevel int    `json:"treasure_level"`
	VIPLevel      int    `json:"vip_level"`
	Age           int    `json:"age"`
	City          string `json:"city"`
	Gender        int    `json:"gender"`
}
