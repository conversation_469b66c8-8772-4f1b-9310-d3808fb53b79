package model

type PlatformUserInfo struct {
	Partner   PartnerEnum `json:"partner"`
	OpenID    string      `json:"openId"`
	Nick      string      `json:"nick"`
	Avatar    string      `json:"avatar"`
	RichLevel int         `json:"richLevel"`
	UID       int64       `json:"uid"`
	Age       int         `json:"age"`
	VIPLevel  int         `json:"vipLevel"`
	City      string      `json:"city"`
	Gender    int         `json:"gender"`
}
