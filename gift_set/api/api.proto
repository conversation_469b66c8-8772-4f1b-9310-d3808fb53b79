syntax = "proto3";

package gift_set;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/gift_set";

import "pb/gift_set/common/common.proto";

service Api {
  // 游戏首页
  rpc MainPage(MainPageReq) returns (MainPageRsp);
  // 送礼
  rpc Pay(PayReq) returns (PayRsp);
  // 公开/私密 私密则仅自己可见，公开则所有用户可查看
  rpc UpdatePrivateApi(UpdatePrivateApiReq) returns (UpdatePrivateApiRsp);
  // 查询高光
  rpc QueryListApi(QueryListApiReq) returns (QueryListApiRsp);
}

message MainPageReq {
  int32 setId = 1;
  string hostUid = 2;  //送礼人uid
  string recvUid = 3;  //收礼人uid
}

message MainPageRsp {
  GiftSetInfo gift = 1; //套系礼物信息
  CombineInfo combine = 2; //组合信息 --废弃
  repeated HighlightRecordItem highlightItems = 3;  //高光信息
}

//商品信息
message GoodsItem{
  int64 giftId = 1; //礼物ID
  int64 num = 2; //数量
}

//支付信息
message PayInfo{
  repeated GoodsItem goodsItems = 1;//商品列表
  int64 amount = 2;        // 总价
}

//支付场景
enum PaySceneType{
  PAY_SCENE_TYPE_UNKNOWN = 0;
  PAY_SCENE_TYPE_LIVE = 1; //直播
  PAY_SCENE_TYPE_KTV = 2;   //歌房
  PAY_SCENE_TYPE_ASYNC_UGC = 3;   //异步作品
  Pay_SCENE_TYPE_ASYNC_MAIL = 4;  //异步私信 必填参数:无
  PAY_SCENE_TYPE_ASYNC_HOMEPAGE = 5;  //异步个人 必填参数:无
}

//支付场景信息
message PaySceneInfo{
  PaySceneType paySceneType = 1;
  string anchorId = 2;
  string roomId = 3;
  string showId = 4;
  string ugcId = 5;
}

//支付结果
message PayResult{
  string orderId = 1; 
  int64 balance = 2;
  string errMsg = 3;
}

message PayReq {
  string payUserId = 1;       //付费用户Uid  
  string recvUserId = 2;      //收礼用户Uid
  PayInfo payInfo = 3;        //付费信息
  string orderId = 4;         //订单id，幂等使用 
  PaySceneInfo paySceneInfo = 5;
  int32 setId = 6;            //套系ID
}

message PayRsp {
  PayResult payResult = 1;
}

message UpdatePrivateApiReq {
  string uid = 1; // uid
  HighlightPrivateStatus status = 2; // 隐私状态
}

message UpdatePrivateApiRsp {
  HighlightPrivateStatus status = 1; // 隐私状态
}

message QueryListApiReq {
  string uid = 1; // 用户id
  string receiverUid = 2; // 收礼人uid
  string passback = 3; // 回传参数
}

message QueryListApiRsp {
  repeated HighlightRecordItem items = 1;
  string passback = 2;
  bool hasMore = 3;
  uint32 receiverStatus = 4; // 收礼人隐私状态
}
