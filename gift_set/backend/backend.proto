syntax = "proto3";

package gift_set;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/gift_set";

import "pb/gift_set/common/common.proto";

service Backend {
  // 配置
  rpc GiftSetConfig(GiftSetConfigReq) returns (GiftSetConfigRsp);
  // 组合
  rpc Combine(CombineReq) returns (CombineRsp);
  // 升级彩蛋
  rpc UpdateCartoon(UpdateCartoonReq) returns (UpdateCartoonRsp);
}

message GiftSetConfigReq {
  int32 setId = 1;
  int64 giftId = 2;
  string uid = 3;
}

message GiftSetConfigRsp {
  GiftSetInfo info = 1; //套系礼物信息
}

message CombineReq {
  int64 giftId = 1;     //礼物ID
  int32 num = 2;        //礼物数量
  int64 sendMs = 3;     //送礼时间
  string consumeId = 4; //订单ID
  string recvUid = 5;   //收礼人
  string sendUid = 6;   //送礼人
  string roomId = 7;    //房间ID
  string showId = 8;    //场次ID
  string ugcId = 9;     //作品ID
  uint32 payScene = 10; //支付场景
  
  //全套送额外字段
  int32 setId = 11; //套系ID
  bool bFullset = 12; //是否全套送
  string sign = 13;    // 「giftId,num,sendMs,consumeId,recvUid,sendUid,salt」 -> md5

  uint32 location = 14; //送礼位置 参考GiftSetCombineLocation
}

message CombineRsp {
  int32 res = 1;
}

message UpdateCartoonReq {
  string uid = 1;  
  int32 setId = 2; 
  int32 stage = 3;
  ResourceInfo resourceInfo = 4; // 动画
}

message UpdateCartoonRsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}
