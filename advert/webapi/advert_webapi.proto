syntax = "proto3";

package advert_webapi;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/advert/webapi";

service AdvertWebapi {
  // 获取广告信息
  rpc AdvertInfo(AdvertInfoReq) returns (AdvertInfoRsp);
  // 领取广告奖励
  rpc AdvertReceiveReward(AdvertReceiveRewardReq) returns (AdvertReceiveRewardRsp);
}

enum IncentiveType {
  IncentiveTypeNone = 0; //非法类型
  IncentiveTypeBI = 1; //使用后台下发的激励广告奖励数值
  IncentiveTypeECPM = 2; //使用商广返回的激励广告奖励数值（基于ecpm）
}

message AdvertScene {
  // 广告位id
  string adPosId = 1;
  // 场景id(可选)
  string sceneId = 2;
}

message AdvertInfoReq {
  // 广告场景
  repeated AdvertScene advertSceneList = 1;
}

message AdvertInfo {
  // 广告奖励数值类型
  IncentiveType incentiveType = 1;
  // 后台计算出的金币值
  uint64 rewardNum = 2;
  // 是否展示广告
  bool showAdvert = 3;
  // 剩余广告次数(仅用于前端展示次数，是否能看广告用showAdvert字段判断)
  uint32 leftAdvert = 4;
}

message AdvertInfoRsp {
  // 广告信息
  repeated AdvertInfo advertInfoList = 1;
}

message AdvertReceiveRewardReq {
  string adToken = 1; // 广告曝光token
  string adPosId = 2; // 广告位id
  string qimei36 = 3;
  string sceneId = 4; // 场景id(可选)
}

message AdvertReceiveRewardRsp {
  string traceId = 1; // 广告曝光唯一id
  int32 result = 2; // 领取结果，0是成功
  uint64 rewardNum = 3; // 广告ecpm数值奖励数量
}
