package initial

import (
	"context"
	"fmt"
	"git.kugou.net/fxgo/core-opentelemetry/v2/cat"
	"git.kugou.net/fxgo/core/config"
	"github.com/sirupsen/logrus"
	"log"
	"strings"
)

func InitCat(ctx context.Context) {
	port := 2280
	httpPort := 8080
	addrs := []string{}
	endpoint := config.Get().App.Endpoint
	if endpoint != "" {
		v := strings.Split(endpoint, ",")
		if len(v) > 0 {
			addrs = v
		} else {
			log.Panic(fmt.Errorf("endpoint is required"))
		}
	} else {
		log.Panic(fmt.Errorf("endpoint is required"))
	}

	c := cat.Config{
		AppId:    config.Get().App.AppName,
		Port:     port,
		HttpPort: httpPort,
		Addrs:    addrs,
		Debug:    false,
	}

	initializer := cat.New(ctx, c)
	_, initErr := initializer.Initialize()
	if initErr != nil {
		logrus.Infof("cat is init fail. %v %v", c, initErr)
	} else {
		logrus.Infof("cat is initialized successfully %#v", c)
	}
}
