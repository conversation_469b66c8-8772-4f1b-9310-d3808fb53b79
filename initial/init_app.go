package initial

import (
	"context"
	"errors"
	"fmt"
	"git.kugou.net/fxgo/core/apollo"
	"git.kugou.net/fxgo/core/cache/xredis"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/config"
	"git.kugou.net/fxgo/core/crontab"
	"git.kugou.net/fxgo/core/gin/middleware"
	"git.kugou.net/fxgo/core/logger"
	"git.kugou.net/fxgo/core/maxcpu"
	"git.kugou.net/fxgo/core/storage/xgorm"
	"git.kugou.net/fxgo/core/trace"
	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/spf13/pflag"
	"kugou_adapter_service/client"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/job"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"time"
)

var (
	engine *gin.Engine
	cfgDir = pflag.StringP("config dir", "c", "conf", "config path.")
)

func Init() {
	config.New(*cfgDir, config.FileTypeToml)
	config.Init()
	logger.Init()
	maxcpu.Init()
	apollo.Init()
	InitCat(context.Background())
	//trace.Init()
	xredis.Init()
	xgorm.Init()
	xthrift.Init()
	crontab.Init()

	cfg := config.Get()

	gin.SetMode(cfg.App.HttpMode)
	engine = gin.New()
	engine.Use(middleware.AccessLog())
	engine.Use(Cors())
	engine.Use(middleware.GinRecovery(true))
	engine.Use(middleware.ErrorHandler())
	if cfg.App.EnableTrace {
		engine.Use(middleware.Tracing())
	}
	if cfg.App.EnablePprof {
		pprof.Register(engine)
	}
}

func Router(f func(e *gin.Engine)) {
	f(engine)
}

func Run() {
	gc, err := client.GetGrpcClient()
	if err != nil {
		log.Fatalf("failed to connect to grpc server: %v", err)
	}
	defer func() {
		crontab.Stop()
		trace.Shutdown()
		if err := gc.Close(); err != nil {
			log.Printf("failed to close grpc connection: %v", err)
		}
	}()

	helper.Init()
	job.Init()
	crontab.Start()

	ipPort := fmt.Sprintf("%s:%d", config.Get().App.HttpAddr, config.Get().App.HttpPort)
	httpServer := &http.Server{Addr: ipPort, Handler: engine}
	logger.Warnf("http server start at %s", ipPort)
	go func() {
		// 服务连接
		if err := httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logger.Fatalf("listen: %s", err)
		}
	}()
	quit := make(chan os.Signal)
	signal.Notify(quit, os.Interrupt)
	<-quit
	logger.Warn("http server shutting down ...")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := httpServer.Shutdown(ctx); err != nil {
		logger.Error("http server shutdown ", err)
	}
}

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		var headerKeys []string
		for k, _ := range c.Request.Header {
			headerKeys = append(headerKeys, k)
		}
		headerStr := strings.Join(headerKeys, ", ")
		if headerStr != "" {
			headerStr = fmt.Sprintf("access-control-allow-origin, access-control-allow-headers, %s", headerStr)
		} else {
			headerStr = "access-control-allow-origin, access-control-allow-headers"
		}
		if origin != "" {
			accessControlAllowHeaders := helper.GetApolloClient().GetStringValue("accessControlAllowHeaders", "")
			origin := c.Request.Header.Get("Origin")
			c.Header("Access-Control-Allow-Origin", origin)
			c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE,UPDATE")
			c.Header("Access-Control-Allow-Headers", accessControlAllowHeaders)
			c.Header("Access-Control-Allow-Credentials", "true")
			c.Set("content-type", "application/json")
		}
		if method == "OPTIONS" {
			c.JSON(http.StatusOK, "Options Request!")
		}
		c.Next()
	}
}
