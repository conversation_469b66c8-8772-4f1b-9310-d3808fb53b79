syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_user_preference";

service UserPreference {
  rpc Query(QueryReq) returns (QueryRsp); // 查询
  rpc BatchQuery(BatchQueryReq) returns (BatchQueryRsp); // 批量查
  rpc Modify(ModifyReq) returns (ModifyRsp); // 更新
}

message Preference {
  map<string, string> kvs = 1;
}

message BatchQueryReq {
  string appId = 1;
  repeated string openIds = 2;
  repeated string filters = 3; // 只查特定设置
}

message BatchQueryRsp {
  map<string, Preference> data = 1;
}

message QueryReq {
  string appId = 1;
  string openId = 2;
  repeated string filters = 3; // 只查特定设置
}

message QueryRsp {
  Preference data = 1;
}

message ModifyReq {
  string appId = 1;
  string openId = 2;
  map<string, string> kvs = 3; // key不能为空,长度不能超过20
}

message ModifyRsp {
  Preference data = 1;
}
