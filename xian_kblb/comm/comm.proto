syntax = "proto3";

package xian_kblb;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_game/kblb/pkg/gen/pb/xian_kblb/comm";

// 平台礼物信息
message Gift {
  int64 gift_id = 1; //礼物ID
  int64 gift_price = 2; //礼物单价
  string gift_name = 3; //礼物名称
  string gift_icon = 4; //礼物图标 160x160
  string gift_icon_360 = 5; //礼物图标 360x360
  int32 lv = 6; // 档位, 1,2,3
  int32 category = 7 [deprecated = true]; // 分类: 1-珍宝,2-祥瑞,3-神仙,4-秘宝
  bool is_super = 8; // 是否超特礼物
  int64 gift_animation_id = 9; // 动画字段id
  int64 anim_price = 10; // 动画价值
  string gift_discount = 11; // 礼物折扣
}

// 礼包资产
message GGItem {
  string reward_id = 1; // 奖励ID
  int64 reward_num = 2; // 数量
  int64 reward_type = 3; // 奖励类型
  int64 unit_price = 4; // 单价
  string reward_name = 5; // 资产名称
  string reward_logo = 6; // 资产icon
  string universal = 7; // 万能字段 透传配置系统上配置的信息
}

// 平台礼物包信息
message GiftGroup {
  int64 id = 1; // 礼包ID
  // int64 series_id = 2; // 礼包序列ID，标识礼包ID属于某个系列活动
  repeated GGItem items = 3; // 礼包内包含资产列表
  // int64 gift_package_value = 4; // 福利价值单位:分
  // int64 expire_ts = 5; // 过期时间
  // string encry_gift_package_id = 6; // 加密礼包ID
  // string gift_package_reason = 7; // 礼包发放原因

}
