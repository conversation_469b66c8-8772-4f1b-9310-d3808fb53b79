syntax = "proto3";

package interactive_game_pk_proxy;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game_pk/proxy";

service Game {
  // 创建游戏
  rpc CreateGame(CreateGameReq) returns (CreateGameRsp);
  // 退出游戏
  rpc QuitGame(QuitGameReq) returns (QuitGameRsp);
  // 结束游戏 (平台实现)
  // rpc GameOver(GameOverReq) returns (GameOverRsp);
}

// 根据 roundId 需要保证接口可重入
message CreateGameReq {
  message Player {
    uint32 index = 1; // 位置 从 1 开始
    uint64 uid = 2; // 房主 openId
    string roomId = 3; // 房间 id
    string gameUrl = 4;
  }
  string appId = 1; // 游戏 id
  string roundId = 2; // 场次 id (小于 128 字节)
  repeated Player players = 3; // 玩家列表
  map<string, string> configs = 4; // 自定义配置
}

message CreateGameRsp {}

message QuitGameReq {
  string appId = 1; // appId
  string roundId = 2; // 场次 id
  uint64 uid = 3; // uid
  string roomId = 4; // 房间 id
}

message QuitGameRsp {}

message GameOverReq {
  message Result {
    uint64 uid = 1;
    string roomId = 2;
    uint32 rank = 3; // 排名 从 1 开始
  }
  string appId = 1; // appId
  string roundId = 2; // 场次 id
  repeated Result results = 3; // 各玩家游戏结果
}

message GameOverRsp {}
