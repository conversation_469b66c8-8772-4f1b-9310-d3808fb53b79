package controller

import (
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kugou_adapter_service/ecode"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/kugourpc"
	"kugou_adapter_service/logic"
	"kugou_adapter_service/model"
	"kugou_adapter_service/service/structs/requst"
	"kugou_adapter_service/service/structs/response"
	"kugou_adapter_service/utils/kit"
	"net/http"
	"strings"
)

// 风控（安全打击）
type SafetyController struct {
	controller.Controller
}

// 风控
func (m *SafetyController) SafeCheck(ctx *gin.Context) {
	body := &requst.SafeCheckBody{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}

	safeLogic := new(logic.SafeLogic)

	data, err := safeLogic.SafeCheck(ctx, body)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}

	logger.Error("###", zap.Any("data", *data))

	ctx.JSON(http.StatusOK, data)
	return
}

func (m *SafetyController) SyncSafeCheckV2(ctx *gin.Context) {
	body := &model.SafeCheckV2Req{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("SyncSafeCheckV2, 参数检查失败。err: %+v", err)
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	// 检查游戏是否支持方法
	gameAppid := body.GameMiddleInfo.GameAppid
	if !strings.EqualFold(gameAppid, "40000013") {
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: int32(ecode.NotSupport.Code()),
			ErrorMsg:  ecode.NotSupport.Msg(),
		})
		return
	}
	// 透传header头
	header := kugourpc.GetHeader(ctx)
	rsp := response.SafeCheckResp{}
	rpcHost := helper.GetApolloClient().GetStringValue("kgrpcHost.syncSafeCheckV2."+gameAppid, "mstc.kgidc.cn")
	rpcUri := helper.GetApolloClient().GetStringValue("rpcUri.syncSafeCheckV2."+gameAppid, "/musicsymbol/v1/revenue/safeCheckV2")
	err := kugourpc.PostWithJson(rpcHost, rpcUri, body, &rsp, header)
	if err != nil {
		logger.Error("SyncSafeCheckV2, 调用接口失败。err: %+v", err)
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	rps := &model.SafeCheckV2Rsp{
		ErrorCode:  0,
		ErrorMsg:   "",
		Suggestion: "",
		Basic:      &body.Basic,
		Detail: &model.SafeCheckV2ResultDetail{
			HitType: 0,
		},
	}
	ctx.JSON(http.StatusOK, rps)
}
