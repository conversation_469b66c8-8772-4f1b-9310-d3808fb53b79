package controller

import (
	"git.kugou.net/fxgo/core/errcode"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"kugou_adapter_service/client"
	"kugou_adapter_service/controller/rpc"
	"kugou_adapter_service/ecode"
	"kugou_adapter_service/model"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	"kugou_adapter_service/service"
)

type CronController struct {
	BaseController
}

// FixConsumeOrder 补偿扣费订单
func (c *CronController) FixConsumeOrder(ctx *gin.Context) {
	logger.Warnf("FixConsumeOrder...")
	s, err := service.New()
	if err != nil {
		logger.Error("初始化服务失败。err: %v", err)
		c.Failure(ctx, *ecode.FAILURE)
		return
	}
	s.RetryUnfinishedOrders()
	c.Success(ctx, gin.H{})
	return
}

func (c *CronController) Debug(ctx *gin.Context) {
	token := ctx.Query("token")
	kugouId := ctx.Query("kugouId")
	openId := ctx.Query("openId")
	skuId := ctx.Query("skuId")
	gameAppId := ctx.Query("gameAppId")
	coinType := ctx.Query("coinType")
	logger.Warnf("Debug...")
	orderNo := client.GenerateID()
	platExtraRaw := &model.PlatExtraRaw{
		Appid:   1010,
		KugouId: cast.ToInt64(kugouId),
		Token:   token,
		StdPlat: 1,
	}
	platExtra, err := platExtraRaw.ToPlatExtra()
	if err != nil {
		c.Failure(ctx, errcode.Error{})
		return
	}
	gameExtraRaw := &model.GameExtraRaw{}
	gameExtra, err := gameExtraRaw.ToGameExtra()
	if err != nil {
		c.Failure(ctx, errcode.Error{})
		return
	}
	subAssetExt := model.SubAssetExt{
		SkuId:     skuId,
		Quantity:  1,
		PlatExtra: platExtra,
		GameExtra: gameExtra,
	}
	s, err := service.New()
	if err != nil {
		c.Failure(ctx, errcode.Error{})
		return
	}
	skuInfo, err := s.GetTmeGameSkuConfig(gameAppId, subAssetExt.SkuId)
	if err != nil {
		c.Failure(ctx, errcode.Error{})
		return
	}
	totalCoin := s.CalculateTotalCoin(*skuInfo, subAssetExt.Quantity, cast.ToInt32(coinType))
	assetType := adapter_revenue.AssetType_AssetTypeKugouFxCoin
	if cast.ToInt(coinType) == 1 {
		assetType = adapter_revenue.AssetType_AssetTypeCarrierDogecoin
	}
	rsp, err := client.SubAsset(gameAppId, openId, orderNo, assetType, totalCoin.IntPart(), subAssetExt)
	if err != nil {
		logger.Warnf("err: %v", err)
		return
	}
	logger.Warnf("rsp: %v", rsp)
	c.Success(ctx, gin.H{})
	return
}

// CacheSimpleGiftInfos 缓存全量礼物
func (c *CronController) CacheSimpleGiftInfos(ctx *gin.Context) {
	logger.Warnf("CacheSimpleGiftInfos...")
	err := rpc.MakeSimpleGiftInfoCache()
	if err != nil {
		logger.Error("CacheSimpleGiftInfos 初始化服务失败。err: %v", err)
		c.Failure(ctx, *ecode.FAILURE)
		return
	}
	c.Success(ctx, gin.H{})
	return
}
