package rpc

import (
	"context"
	"git.kugou.net/fxgo/core/logger"
	"github.com/spf13/cast"
	"google.golang.org/grpc/status"
	"kugou_adapter_service/client"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_common"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/adapter_unified_assets"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/unified_assets"
	"kugou_adapter_service/third-party/gen-go/kugouadapterasset"
	"strconv"
)

type KugouAdapterAssetServiceImpl struct{}

func (k KugouAdapterAssetServiceImpl) Pay(ctx context.Context, appName string, req *kugouadapterasset.PayReq) (_r *kugouadapterasset.PayRsp, _err error) {

	logger.Infof(" KugouAdapterAssetService pay req:%v", req)
	gameMiddleInfo := &adapter_common.GameMiddleInfo{
		GameAppId:  req.GameMiddleInfo.GameAppId,
		GameOpenId: req.GameMiddleInfo.GameOpenId,
		Uid:        req.GameMiddleInfo.UID,
	}
	payApp := &adapter_unified_assets.PayApp{
		ActId:      req.PayApp.ActId,
		BusinessId: req.PayApp.BusinessId,
	}

	goodsItems := make([]*adapter_unified_assets.GoodsItem, 0)
	for _, item := range req.PayInfo.GoodsItems {
		goodsItem := &adapter_unified_assets.GoodsItem{
			GoodsId: item.GoodsId,
			Num:     item.Num,
		}
		goodsItems = append(goodsItems, goodsItem)
	}
	payInfo := &adapter_unified_assets.PayInfo{
		GoodsItems: goodsItems,
		MapExt:     req.PayInfo.MapExt,
	}

	paySceneInfo := &adapter_unified_assets.PaySceneInfo{
		PaySceneType: adapter_unified_assets.PaySceneType(req.PaySceneInfo.PaySceneType),
		AnchorId:     req.PaySceneInfo.AnchorId,
		RoomId:       req.PaySceneInfo.RoomId,
		ShowId:       req.PaySceneInfo.ShowId,
		UgcId:        req.PaySceneInfo.UgcId,
	}
	payReq := &adapter_unified_assets.PayReq{
		GameMiddleInfo: gameMiddleInfo,
		PayApp:         payApp,
		PayInfo:        payInfo,
		PayAmount:      req.PayAmount,
		PaySceneInfo:   paySceneInfo,
		OrderId:        req.OrderId,
	}

	rsp, err := client.Pay(appName, payReq)
	if err != nil {
		logger.Warnf("Pay Error。err: %v", err)
		st, ok := status.FromError(err)
		if ok {
			var msg = st.Message()
			return &kugouadapterasset.PayRsp{
				Code: int32(st.Code()),
				Msg:  &msg,
			}, nil
		}
		var msg = "Pay Error"
		return &kugouadapterasset.PayRsp{
			Code: ServiceError,
			Msg:  &msg,
		}, nil
	}
	return &kugouadapterasset.PayRsp{
		Code: 0,
		Msg:  nil,
		Data: &kugouadapterasset.PayResult_{
			OrderId: rsp.PayResult.OrderId,
			Balance: rsp.PayResult.Balance,
			ErrMsg:  &rsp.PayResult.ErrMsg,
		},
	}, nil
}

func (k KugouAdapterAssetServiceImpl) PayAndGiveGifts(ctx context.Context, appName string, req *kugouadapterasset.PayAndGiveGiftsReq) (_r *kugouadapterasset.PayAndGiveGiftsRsp, _err error) {
	logger.Infof(" KugouAdapterAssetService PayAndGiveGifts req:%v", req)
	gameMiddleInfo := &adapter_common.GameMiddleInfo{
		GameAppId:  req.GameMiddleInfo.GameAppId,
		GameOpenId: req.GameMiddleInfo.GameOpenId,
		Uid:        req.GameMiddleInfo.UID,
	}
	payApp := &adapter_unified_assets.PayApp{
		ActId:      req.PayApp.ActId,
		BusinessId: req.PayApp.BusinessId,
	}

	goodsItems := make([]*adapter_unified_assets.GoodsItem, 0)
	for _, item := range req.PayInfo.GoodsItems {
		goodsItem := &adapter_unified_assets.GoodsItem{
			GoodsId: item.GoodsId,
			Num:     item.Num,
		}
		goodsItems = append(goodsItems, goodsItem)
	}
	payInfo := &adapter_unified_assets.PayInfo{
		GoodsItems: goodsItems,
		MapExt:     req.PayInfo.MapExt,
	}

	//奖励信息
	giveGiftInfos := make([]*adapter_unified_assets.GiveGiftsInfo, 0)
	for _, info := range req.GiveGiftsInfos {
		recvGameMiddleInfo := &adapter_common.GameMiddleInfo{
			GameAppId:  info.RecvGameMiddleInfo.GameAppId,
			GameOpenId: info.RecvGameMiddleInfo.GameOpenId,
			Uid:        info.RecvGameMiddleInfo.UID,
		}

		recvGoodsItems := make([]*adapter_unified_assets.GoodsItem, 0)
		for _, item := range info.PayInfo.GoodsItems {
			goodsItem := &adapter_unified_assets.GoodsItem{
				GoodsId: item.GoodsId,
				Num:     item.Num,
			}
			recvGoodsItems = append(recvGoodsItems, goodsItem)
		}
		recvPayInfo := &adapter_unified_assets.PayInfo{
			GoodsItems: recvGoodsItems,
			MapExt:     info.PayInfo.GetMapExt(),
		}

		giveGiftInfo := &adapter_unified_assets.GiveGiftsInfo{
			RecvGameMiddleInfo: recvGameMiddleInfo,
			PayInfo:            recvPayInfo,
		}
		giveGiftInfos = append(giveGiftInfos, giveGiftInfo)
	}

	paySceneInfo := &adapter_unified_assets.PaySceneInfo{
		PaySceneType: adapter_unified_assets.PaySceneType(req.PaySceneInfo.PaySceneType),
		AnchorId:     req.PaySceneInfo.AnchorId,
		RoomId:       req.PaySceneInfo.RoomId,
		ShowId:       req.PaySceneInfo.ShowId,
		UgcId:        req.PaySceneInfo.UgcId,
	}
	payAndGiveGiftsReq := &adapter_unified_assets.PayAndGiveGiftsReq{
		GameMiddleInfo: gameMiddleInfo,
		PayApp:         payApp,
		PayInfo:        payInfo,
		PayAmount:      req.PayAmount,
		GiveGifts:      giveGiftInfos,
		PaySceneInfo:   paySceneInfo,
		OrderId:        req.OrderId,
	}

	rsp, err := client.PayAndGiveGifts(appName, payAndGiveGiftsReq)
	if err != nil {
		logger.Warnf("PayAndGiveGifts Error。err: %v", err)
		var msg = "PayAndGiveGifts Error"
		return &kugouadapterasset.PayAndGiveGiftsRsp{
			Code: ServiceError,
			Msg:  &msg,
		}, nil
	}
	return &kugouadapterasset.PayAndGiveGiftsRsp{
		Code: 0,
		Msg:  nil,
		Data: &kugouadapterasset.PayResult_{
			OrderId: rsp.PayResult.OrderId,
			Balance: rsp.PayResult.Balance,
			ErrMsg:  &rsp.PayResult.ErrMsg,
		},
	}, nil
}

func (k KugouAdapterAssetServiceImpl) SendGiftPackage(ctx context.Context, appName string, req *kugouadapterasset.SendGiftPackageReq) (_r *kugouadapterasset.SendGiftPackageRsp, _err error) {
	logger.Infof(" KugouAdapterAssetService SendGiftPackage req:%v", req)
	gameMiddleInfo := &adapter_common.GameMiddleInfo{
		GameAppId:  req.GameMiddleInfo.GameAppId,
		GameOpenId: req.GameMiddleInfo.GameOpenId,
		Uid:        req.GameMiddleInfo.UID,
	}
	extensionId := adapter_unified_assets.SendExtensionId_SendExtensionIdUnknow
	if req.ExtensionId != nil {
		extensionId = adapter_unified_assets.SendExtensionId(*req.ExtensionId)
	}
	sendReq := &adapter_unified_assets.SendGiftPackageReq{
		GameMiddleInfo: gameMiddleInfo,
		GiftPackageId:  req.GiftPackageId,
		Num:            req.Num,
		OrderId:        req.OrderId,
		Program:        req.Program,
		Reason:         *req.Reason,
		Indentifiers:   *req.Identifiers,
		SendTs:         req.SendTs,
		MapExt:         req.MapExt,
		ExtensionId:    extensionId,
	}

	resp, err := client.SendGiftPackage(appName, sendReq)
	if err != nil {
		logger.Warnf("SendGiftPackage Error。err: %v", err)
		var msg = "SendGiftPackage Error"
		return &kugouadapterasset.SendGiftPackageRsp{
			Code: ServiceError,
			Msg:  &msg,
		}, nil
	}
	return &kugouadapterasset.SendGiftPackageRsp{
		Code: 0,
		Msg:  nil,
		Data: &kugouadapterasset.SendGiftPackageResult_{
			OrderId: resp.OrderId,
		},
	}, nil
}

func (k KugouAdapterAssetServiceImpl) GetOrderStatus(ctx context.Context, appName string, req *kugouadapterasset.GetOrderStatusReq) (_r *kugouadapterasset.GetOrderStatusRsp, _err error) {
	logger.Infof(" KugouAdapterAssetService GetOrderStatus req:%v", req)
	sendReq := &adapter_unified_assets.GetOrderStatusReq{
		OrderId: req.OrderId,
	}

	resp, err := client.GetOrderStatus(appName, sendReq)
	if err != nil {
		logger.Warnf("SendGiftPackage Error。err: %v", err)
		var msg = "GetOrderStatus Error"
		return &kugouadapterasset.GetOrderStatusRsp{
			Code: ServiceError,
			Msg:  &msg,
		}, nil
	}
	return &kugouadapterasset.GetOrderStatusRsp{
		Code: 0,
		Msg:  nil,
		Data: &kugouadapterasset.GetOrderStatusResult_{
			OrderId:     resp.OrderId,
			OrderStatus: cast.ToString(resp.OrderStatus),
			UserId:      resp.UesrId,
		},
	}, nil
}

func GiftPackageBusinessCheckSend(ctx context.Context, sReq *unified_assets.SendWelfareReq, appName string) (err error) {
	sendReq := &callback.GiftPackageBusinessCheckSendReq{
		GameMiddleInfo: &adapter_common.GameMiddleInfo{
			GameAppId: string(sReq.AppId),
			//GameOpenId: sReq.GameOpenId,
			Uid: sReq.UserId,
		},
		CheckInfo: &callback.CheckInfo{
			OrderId:       sReq.OrderId,
			GiftPackageId: strconv.FormatInt(sReq.WelfareId, 10),
			Num:           uint32(sReq.Num),
			SendTs:        uint32(sReq.SendTs),
			Reason:        sReq.Reason,
			MapExt:        sReq.MapExt,
		},
		CallInfo: &callback.CallInfo{
			//CallBackCmd: sReq.,
		},
	}

	logger.Infof("GiftPackageBusinessCheckSend req:%v", sendReq)
	_, err = client.GiftPackageBusinessCheckSend(appName, sendReq)
	if err != nil {
		logger.Warnf("SendGiftPackage Error。err: %v", err)
		return err
		//var msg = "GetOrderStatus Error"
		//return &kugouadapterasset.GetOrderStatusRsp{
		//	Code: ServiceError,
		//	Msg:  &msg,
		//}, nil
	}
	return nil
	//return &kugouadapterasset.GetOrderStatusRsp{
	//	Code: 0,
	//	Msg:  nil,
	//	Data: &kugouadapterasset.GetOrderStatusResult_{
	//		OrderId:     resp.OrderId,
	//		OrderStatus: cast.ToString(resp.OrderStatus),
	//		UserId:      resp.UesrId,
	//	},
	//}, nil
}
