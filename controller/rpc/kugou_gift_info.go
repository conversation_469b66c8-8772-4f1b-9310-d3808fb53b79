package rpc

import (
	"context"
	"fmt"
	"git.kugou.net/fxgo/core/cache/xredis"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/pojo/constant"
	"kugou_adapter_service/third-party/gen-go/goodsgiftservice"
	"kugou_adapter_service/utils"
	"kugou_adapter_service/utils/convert"
)

type AllGiftInfoRequestDto struct {
	AppId    string  `json:"appId"`
	GiftMd5  string  `json:"giftMd5"`
	BizTypes []int32 `json:"bizTypes"`
	Sign     string  `json:"sign"`
}

type LevelResGiftReqDto struct {
	AppId string `json:"appId"`
	BizId int32  `json:"bizId"`
}

func MakeSimpleGiftInfoCache() error {
	var giftInfoList []*goodsgiftservice.SimpleGiftInfo
	client, err := xthrift.GetTClient(goodsgiftservice.NewGoodsGiftServiceClient, "goodsgiftservice")
	if err != nil {
		logger.Errorf("MakeSimpleGiftInfoCache error.创建client失败. error=%v", err)
		return err
	}

	requestDto := AllGiftInfoRequestDto{
		AppId:    "kugou_adapter_service",
		BizTypes: []int32{0, 1},
	}
	request := &goodsgiftservice.GetAllGiftInfoRequestV2{
		AppId:    &requestDto.AppId,
		GiftMd5:  &requestDto.GiftMd5,
		BizTypes: requestDto.BizTypes,
		//Sign:     utils.SignWithComma(requestDto, helper.GetApolloClient().GetStringValue("GiftInfoSalt", "relation.user.kgidc.cn")),
	}
	response, err := client.GetAllSimpleGiftInfoV2(context.Background(), request)
	if err != nil {
		logger.Errorf("MakeSimpleGiftInfoCache error.调用失败. request=%v, error=%v", request, err)
		return err
	}

	if response == nil || response.Ret != 0 {
		logger.Errorf("MakeSimpleGiftInfoCache error.返回值错误. request=%v,  response=%v", request, response)
		//return giftInfoList
	}
	giftInfoList = response.GetData().Data
	if giftInfoList == nil || len(giftInfoList) == 0 {
		logger.Errorf("MakeSimpleGiftInfoCache error.返回值为空. request=%v,  response=%v", request, response)
		return err
	}

	//enableLog := config.Application.EnableLog("simpleGiftInfoDebug")
	for _, giftInfo := range giftInfoList {
		cacheSimpleGiftInfo(giftInfo)
		//if enableLog {
		//	logger.Warnf("simpleGiftInfoDebug , giftInfo = %+v", utils.JsonString(giftInfo))
		//}
	}
	logger.Infof("MakeSimpleGiftInfoCache success.")
	return nil
}

func cacheSimpleGiftInfo(giftInfo *goodsgiftservice.SimpleGiftInfo) {
	key := fmt.Sprintf(constant.GiftInfo.Key, giftInfo.GetID())
	client, _ := xredis.GetClusterClient("default")

	client.Set(context.Background(), key, utils.JsonInfo(giftInfo), constant.GiftInfo.Ttl)
}

func GetSimpleGiftInfo(giftId int32) *goodsgiftservice.SimpleGiftInfo {
	key := fmt.Sprintf(constant.GiftInfo.Key, giftId)
	client, _ := xredis.GetClusterClient("default")
	cmd := client.Get(context.Background(), key)
	vo, _ := cmd.Result()
	if vo == "" {
		logger.Warnf("getGiftInfoByGiftId error.礼物信息不存在. giftId=%d", giftId)
		return nil
	}
	var giftInfo goodsgiftservice.SimpleGiftInfo
	if err := convert.JsonStringToObject(vo, &giftInfo); err != nil {
		logger.Errorf("getGiftInfoByGiftId error.反序列化失败. giftId=%d, error=%v", giftId, err)
		return nil
	}
	return &giftInfo
}
