package rpc

import (
	"context"
	"git.kugou.net/fxgo/core/client/xthrift"
	"git.kugou.net/fxgo/core/logger"
	"kugou_adapter_service/third-party/gen-go/commonaward"
	"kugou_adapter_service/third-party/gen-go/platform_order_servicev2"
	"kugou_adapter_service/utils"
	"kugou_adapter_service/utils/convert"
	"time"
)

func CreateDeliverOrderByRule(orderRequest *platform_order_servicev2.OrderRequest, ruleInfo *platform_order_servicev2.RuleInfo) (bool, []*platform_order_servicev2.GoodsData) {
	syncOrderService, err := xthrift.GetTClient(platform_order_servicev2.NewSyncOrderServiceClient, "sync_order_service")

	if err != nil {
		logger.Errorf("syncOrderService.CreateDeliverOrderByRule createClientErr, err= %+v", err)
		return false, nil
	}
	val, err := syncOrderService.CreateDeliverOrderByRule(context.Background(), orderRequest, ruleInfo)
	if err != nil {
		logger.Errorf("syncOrderService.CreateDeliverOrderByRule error reqrsp:%+v,err:%+v", utils.JsonInfo(orderRequest), err)
		return false, nil
	}
	if val.Code != 0 || len(val.OrderData.GoodsDatas) <= 0 {
		logger.Errorf("syncOrderService.CreateDeliverOrderByRule fail reqrsp:%+v,resp:%+v", utils.JsonInfo(orderRequest), utils.JsonInfo(val))
		return false, nil
	}
	logger.Warnf("syncOrderService.CreateDeliverOrderByRule success reqrsp:%+v,resp:%+v", utils.JsonInfo(orderRequest), utils.JsonInfo(val))
	return true, val.OrderData.GoodsDatas
}

func GetFullAwardList(appId int32, appKey string) ([]*commonaward.AwardRuleDetailV2, error) {
	commonAwardService, err := xthrift.GetTClient(commonaward.NewCommonAwardServiceClient, "commonaward")
	if err != nil {
		logger.Errorf("GetFullAwardList_clientErr, appId=%d, appKey=%s, err=%+v", appId, appKey, err)
		return nil, err
	}
	signReq := &commonaward.SignFullAwardListReq{
		AppId: appId,
		Time:  convert.ToInt32(time.Now().Unix()),
		Ext:   "",
	}
	awardReq := &commonaward.FullAwardListReq{
		AppId: signReq.AppId,
		Time:  convert.ToInt32(time.Now().Unix()),
		Ext:   &signReq.Ext,
		Sign:  utils.SignWithComma(signReq, appKey),
	}
	val, err := commonAwardService.GetFullAwardListV2(context.Background(), awardReq)
	if err != nil {
		logger.Errorf("GetFullAwardList_rpcErr req:%+v err:%+v", utils.JsonInfo(awardReq), err)
		return nil, err
	}
	if val.Ret != 0 {
		logger.Errorf("GetFullAwardList_retErr req:%+v resp:%+v", utils.JsonInfo(awardReq), utils.JsonInfo(val))
		return nil, nil
	}
	return val.GetData().GetRuleDetail(), nil
}
