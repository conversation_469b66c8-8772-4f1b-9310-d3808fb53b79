package controller

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"kugou_adapter_service/client"
	"kugou_adapter_service/constant"
	"kugou_adapter_service/ecode"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/logic"
	"kugou_adapter_service/model"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	"kugou_adapter_service/service"
	"kugou_adapter_service/service/structs/requst"
	"kugou_adapter_service/service/structs/response"
	stringutil "kugou_adapter_service/utils"
	"kugou_adapter_service/utils/kit"
	"net/http"
)

// 资产
type AssetController struct {
	controller.Controller
}

// 金币查询
func (m *AssetController) QueryAsset(ctx *gin.Context) {

	body := &requst.QueryAssetBody{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}

	assetLogic := new(logic.AssetLogic)

	data, err := assetLogic.QueryAsset(ctx, body)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}

	ctx.JSON(http.StatusOK, data)
	return
}

// SubAsset 资产扣减
func (m *AssetController) SubAsset(ctx *gin.Context) {
	body := &requst.SubAssetBody{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("SubAsset参数错误。err: %v", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	assetType := body.AssetType
	gameAppid := body.GameMiddleInfo.GameAppid
	// 幸运抽抽乐
	if gameAppid == "40000007" && assetType == int(adapter_revenue.AssetType_AssetTypeCarrierDogecoin) {
		assetLogic := new(logic.AssetLogic)
		data, err := assetLogic.SubAsset(ctx, body)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, nil)
			return
		}
		ctx.JSON(http.StatusOK, data)
		return
	}
	// 扣减狗狗币
	if gameAppid == "40000010" {
		if len(body.Assets) != 1 || (body.Assets[0].AssetId != 2000000020 && body.Assets[0].AssetId != 2000001025) {
			logger.Error("扣减平台资产，不支持的Assets。body.Assets: %v", body.Assets)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.NotSupportAssets.Code()),
				ErrorMsg:  ecode.NotSupportAssets.Msg(),
			})
			return
		}
		assetId, assetNum := body.Assets[0].AssetId, body.Assets[0].AssetNum
		// 获取扣费币种
		coinType, err := getCoinType(assetType)
		if err != nil {
			logger.Error("扣减平台资产，不支持的assetType。assetType: %v, err: %v", assetType, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.NotSupportAssetType.Code()),
				ErrorMsg:  ecode.NotSupportAssetType.Msg(),
			})
			return
		}
		s, err := service.New()
		if err != nil {
			logger.Error("扣减平台资产，初始化服务失败。err: %v", err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.FAILURE.Code()),
				ErrorMsg:  ecode.FAILURE.Msg(),
			})
			return
		}
		// 平台ID转游戏中台ID
		kugouId := int64(body.GameMiddleInfo.Uid)
		openId, err := client.PlatUid2Openid(gameAppid, kugouId, constant.AppNameKugou)
		if err != nil || stringutil.IsBlank(openId) {
			logger.Error("扣减平台资产，kugouId转openid失败。kugouId: %v, err: %v", kugouId, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.ErrUserID.Code()),
				ErrorMsg:  ecode.ErrUserID.Msg(),
			})
			return
		}
		// 加载SKU信息
		mapExt := body.MapExt
		if mapExt == nil || len(mapExt) == 0 {
			logger.Error("扣减平台资产，mapExt参数缺失。kugouId: %v, err: %v", kugouId, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.InvalidParam.Code()),
				ErrorMsg:  ecode.InvalidParam.Msg(),
			})
			return
		}
		// 解析扩展参数
		skuId := lo.ValueOr(mapExt, "skuId", "")
		quantity := cast.ToInt32(lo.ValueOr(mapExt, "quantity", ""))
		platExtra := lo.ValueOr(mapExt, "platExtra", fallbackExtra())
		gameExtra := lo.ValueOr(mapExt, "gameExtra", fallbackExtra())
		extra, err := service.ParsePlatExtra(platExtra)
		if err != nil || stringutil.IsAnyBlank(skuId, extra.Token) || quantity < 1 {
			logger.Error("扣减平台资产，mapExt参数错误。kugouId: %v, mapExt: %+v, err: %v", kugouId, mapExt, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.InvalidParam.Code()),
				ErrorMsg:  ecode.InvalidParam.Msg(),
			})
			return
		}
		// 检查货品标识
		skuInfo, err := s.GetTmeGameSkuConfig(gameAppid, skuId)
		if err != nil {
			logger.Warnf("扣减平台资产，SKU信息错误。err: %v", err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.NotSupportSkuID.Code()),
				ErrorMsg:  ecode.NotSupportSkuID.Msg(),
			})
			return
		}
		// 检查SKU价格
		totalCoin := decimal.NewFromInt(assetNum)
		checkSkuCoin := helper.GetApolloClient().GetBoolValue("tmeGame.checkSkuCoin", true)
		skuTotalCoin := s.CalculateTotalCoin(*skuInfo, quantity, coinType)
		if checkSkuCoin && !skuTotalCoin.Equal(totalCoin) {
			logger.Warnf("扣减平台资产，扣费金额与SKU总价不一致。checkSkuCoin: %v assetNum: %v, skuTotalCoin: %v, err: %v",
				checkSkuCoin, assetNum, skuTotalCoin, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.NotSupportSkuID.Code()),
				ErrorMsg:  ecode.NotSupportSkuID.Msg(),
			})
			return
		}
		// 检查账户余额
		userMoney := client.GetUserCoin(kugouId, coinType)
		if userMoney.LessThan(totalCoin) {
			logger.Warnf("扣减平台资产，用户账户不足。userMoney: %v, totalCoin: %v", userMoney, totalCoin)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.CoinNotEnough.Code()),
				ErrorMsg:  ecode.CoinNotEnough.Msg(),
			})
			return
		}
		// 创建扣费订单
		order, err := s.CreateConsumeOrder(gameAppid, openId, kugouId, totalCoin, ctx.ClientIP(), &model.BuyProductReq{
			CoinType:   coinType,
			Coin:       totalCoin,
			OutOrderNo: body.BillNo,
			SkuId:      skuId,
			Quantity:   quantity,
			GameExtra:  gameExtra,
			PlatExtra:  platExtra,
		})
		if err != nil {
			logger.Error("扣减平台资产，创建扣费订单失败。kugouId: %v, err: %v", kugouId, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.FAILURE.Code()),
				ErrorMsg:  ecode.FAILURE.Msg(),
			})
			return
		}
		// 调用扣费服务
		flag, err := s.InvokeConsumeCoin(order, *skuInfo, extra.Appid, extra.Token)
		if err != nil || !flag {
			logger.Error("扣减平台资产，调用资产扣费失败。kugouId: %v, err: %v", kugouId, err)
			ctx.JSON(http.StatusOK, response.ErrorResp{
				ErrorCode: int32(ecode.FAILURE.Code()),
				ErrorMsg:  ecode.FAILURE.Msg(),
			})
			return
		}
		ctx.JSON(http.StatusOK, response.ChangeAssetResp{
			ErrorCode: 0,
			ErrorMsg:  "操作成功",
			BillNo:    body.BillNo,
			MapResult: response.MapPlatUserAsset{
				assetId: {
					AssetId: assetId,
					AssetNm: assetNum,
				},
			},
		})
		return
	}
	ctx.JSON(http.StatusOK, response.ErrorResp{
		ErrorCode: 62002,
		ErrorMsg:  "不支持的应用ID",
	})
	return
}

func getCoinType(assetType int) (int32, error) {
	coinType := int32(-1)
	// 酷狗直播星币
	if assetType == int(adapter_revenue.AssetType_AssetTypeKugouFxCoin) {
		coinType = 0
		return coinType, nil
	}
	// 酷狗音乐狗狗币
	if assetType == int(adapter_revenue.AssetType_AssetTypeCarrierDogecoin) {
		coinType = 1
		return coinType, nil
	}
	return coinType, fmt.Errorf("不支持的资产类型[assetType:%v]", assetType)
}

func (m *AssetController) AddAsset(ctx *gin.Context) {
	body := &requst.AddAssetBody{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}

	assetLogic := new(logic.AssetLogic)

	data, err := assetLogic.AddAsset(ctx, body)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}

	ctx.JSON(http.StatusOK, data)
	return
}

func fallbackExtra() string {
	jsonBody, err := json.Marshal(map[string]string{})
	if err != nil {
		logger.Error("序列化失败，降级返回。err: %v", err)
		return "e30="
	}
	return base64.StdEncoding.EncodeToString(jsonBody)
}
