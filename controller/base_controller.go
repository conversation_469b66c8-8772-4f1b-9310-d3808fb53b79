package controller

import (
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/errcode"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

import "time"

type AdapterResponse struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	Data      any    `json:"data,omitempty"`
	Timestamp int64  `json:"timestamp"`
	ErrorCode int    `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
}

func NewAdapterResponse(code int, msg string, data any) *AdapterResponse {
	return &AdapterResponse{
		Code:      code,
		Msg:       msg,
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
		ErrorCode: code,
		ErrorMsg:  msg,
	}
}

type TmeGameResponse struct {
	ErrorCode int    `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
}

func NewTmeGameResponse(code int, msg string) *TmeGameResponse {
	return &TmeGameResponse{
		ErrorCode: code,
		ErrorMsg:  msg,
	}
}

type BaseController struct {
	controller.Controller
}

func (c *BaseController) Success(ctx *gin.Context, data interface{}) {
	resp := NewAdapterResponse(errcode.Success.Code(), errcode.Success.Msg(), data)
	ctx.JSON(http.StatusOK, resp)
}

func (c *BaseController) Failure(ctx *gin.Context, err errcode.Error) {
	resp := NewAdapterResponse(err.Code(), err.Msg(), nil)
	ctx.JSON(http.StatusOK, resp)
}

func (c *BaseController) gopenSuccess(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, data)
}

func (c *BaseController) gopenFailure(ctx *gin.Context, err errcode.Error) {
	resp := NewTmeGameResponse(err.Code(), err.Msg())
	ctx.JSON(http.StatusOK, resp)
}

func (c *BaseController) tmeFailure(ctx *gin.Context, err errcode.Error) {
	ctx.Header("x-error-code", strconv.Itoa(err.Code()))
	ctx.Header("x-error-message", err.Msg())
	ctx.JSON(http.StatusOK, nil)
}
