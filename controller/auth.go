package controller

import (
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kugou_adapter_service/client"
	"kugou_adapter_service/constant"
	"kugou_adapter_service/ecode"
	"kugou_adapter_service/model"
	"kugou_adapter_service/service"
	"kugou_adapter_service/service/structs/response"
	"kugou_adapter_service/utils/kit"
	"net/http"
)

// AuthController main controller test
// @Description:
type AuthController struct {
	controller.Controller
}

type GetAccessTokenReq struct {
	app     int    // 来源平台
	cid     int    // 客户端版本
	uid     int64  // 用户uid
	appId   string // 应用id
	token   string // token
	reqTime int64  // 请求时间
	ip      string // 请求ip
}

type GetAccessTokenResp struct {
	accessToken string // 来源平台
	authToken   string // 客户端版本
	uid         int64  // 用户uid
	appId       string // 应用id
	pToken      string // 请求时间
	openId      string // 请求ip
}

type CheckTokenReq struct {
	app     int    // 来源平台
	cid     int    // 客户端版本
	uid     int64  // 用户uid
	appId   string // 应用id
	token   string // token
	reqTime int64  // 请求时间
	ip      string // 请求ip
}

type KugouAuthReq struct {
	Uid   int64  `form:"uid"`          //对应酷狗kugouId
	Token string `form:"access_token"` //对应酷狗token
	AppId int32  `form:"token_type"`   //对应酷狗appId
}

type CheckRealNameReq struct {
	PlatExtra string `json:"platExtra" binding:"required"`
}

func (m *AuthController) GetAccessToken(c *gin.Context) {
	req := &GetAccessTokenReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, c); err != nil {
		return
	}
	m.Failure(c, *ecode.NotSupport)
}

func (m *AuthController) CheckToken(c *gin.Context) {
	req := &CheckTokenReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, c); err != nil {
		return
	}
	m.Failure(c, *ecode.NotSupport)
}

func (m *AuthController) KugouAuth(c *gin.Context) {
	req := &KugouAuthReq{}
	if err := c.ShouldBind(req); err != nil {
		logger.Error("kugouAuth param error", zap.Error(err))
		wnsResponse(c, constant.AuthApiParamError, "参数无效")
		return
	}
	if req.Uid <= 0 || req.AppId <= 0 || req.Token == "" {
		wnsResponse(c, constant.AuthApiParamError, "参数无效")
		return
	}
	result, err := client.CheckToken(&client.CheckTokenPara{
		Kugouid: req.Uid,
		Token:   req.Token,
		IP:      c.ClientIP(),
		Appid:   req.AppId,
	})
	if err != nil {
		wnsResponse(c, constant.AuthApiServerError, "服务繁忙")
		return
	}
	if result {
		wnsResponse(c, 0, "成功")
		return
	}

	wnsResponse(c, constant.AuthApiInvalidToken, "票据无效")
}

func (m *AuthController) CheckRealName(c *gin.Context) {
	req := &CheckRealNameReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, c); err != nil {
		logger.Warnf("检查是否实名与未成年，业务参数错误。req: %+v, err: %v", req, err)
		m.Failure(c, *ecode.InvalidParam)
		return
	}
	platExtraRaw, err := service.ParsePlatExtra(req.PlatExtra)
	if err != nil || platExtraRaw.KugouId <= 0 {
		logger.Warnf("检查是否实名与未成年，平台参数错误。req: %+v, err: %v", req, err)
		m.Failure(c, *ecode.InvalidParam)
		return
	}
	kugouId := platExtraRaw.KugouId
	realNameAuth, err := client.KugouRealName(kugouId, c.ClientIP())
	if err != nil {
		logger.Warnf("检查是否实名与未成年，实名认证失败。kugouId: %+v, req: %v, err: %v", kugouId, req, err)
		m.Failure(c, *ecode.FAILURE)
		return
	}
	rsp := model.CheckRealNameRsp{
		RealNameAuth: false,
		AdultAuth:    false,
	}
	if !realNameAuth {
		logger.Warnf("检查是否实名与未成年，未实名认证。kugouId: %+v, req: %v", kugouId, req)
		m.Success(c, rsp)
		return
	}
	realAuth, adultAuth, err := client.KugouAdultAuth(kugouId, 0)
	if err != nil {
		logger.Warnf("检查是否实名与未成年，未成年检查失败。kugouId: %+v, req: %v, realAuth: %v, err: %v", kugouId, req, realAuth, err)
		m.Failure(c, *ecode.FAILURE)
		return
	}
	rsp.RealNameAuth = realNameAuth
	rsp.AdultAuth = adultAuth
	m.Success(c, rsp)
}

func wnsResponse(c *gin.Context, errorCode int32, errorMsg string) {
	c.JSON(http.StatusOK, response.ErrorResp{
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	})
}
