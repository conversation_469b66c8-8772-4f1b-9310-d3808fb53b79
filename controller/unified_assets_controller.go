package controller

import (
	"encoding/json"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"kugou_adapter_service/client"
	"kugou_adapter_service/controller/rpc"
	"kugou_adapter_service/ecode"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/unified_assets"
	"kugou_adapter_service/service"
	"kugou_adapter_service/service/award"
	"kugou_adapter_service/utils"
	"kugou_adapter_service/utils/kit"
)

type UnifiedAssetsController struct {
	BaseController
}

func (c *UnifiedAssetsController) Pay(ctx *gin.Context) {
	req := &unified_assets.Pay4BuyGoodsReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, ctx); err != nil {
		logger.Error("请求Pay接口, 绑定参数失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	s, err := service.New()
	if err != nil {
		logger.Error("请求Pay接口, 初始化服务失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	mapExt := req.PayInfo.MapExt
	gameAppId := "40000016"
	platAppId := int32(1010)
	platToken := "159f3d79d02e020a0182f1fa944afd1de6fbf7fc5769bb97f0ab6dd5f33e9673"
	clientIp := "127.0.0.1"
	platUid, err := cast.ToInt64E(req.PayUserId)
	if err != nil {
		logger.Error("请求Pay接口, 解析platUid失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	gameConfig, err := s.GetTmeGamesConfig(gameAppId)
	if err != nil {
		logger.Error("请求Pay接口, 获取游戏配置失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	// 检查货品标识
	skuInfo, exists := gameConfig.GetSkuInfo("consume_item")
	if !exists {
		logger.Error("请求Pay接口, 获取扣费物品失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	// 序列化请求内容
	extraInfo, err := utils.ProtoToJSON(req)
	if err != nil {
		logger.Error("请求Pay接口, 序列化请求参数失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	outOrderNo := req.OrderId
	totalCoin := decimal.NewFromUint64(uint64(req.PayAmount))
	order, err := s.CreatePureConsumeOrder(gameAppId, "", platUid, outOrderNo, 0, totalCoin, clientIp, extraInfo)
	if err != nil {
		logger.Error("请求Pay接口, 创建扣费订单失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.FAILURE)
		return
	}
	result, err := s.InvokeConsumeCoin(order, skuInfo, platAppId, platToken)
	if err != nil || !result {
		logger.Error("请求Pay接口, 调用订单扣费失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.FAILURE)
		return
	}
	if s.Delivery(order) {
		logger.Infof("请求Pay接口，回调业务扣费完成。order: %v", order)
	}
	balance := client.GetUserCoinByAssetType(platUid, adapter_revenue.AssetType_AssetTypeKugouFxCoin)
	rsp := &unified_assets.Pay4BuyGoodsRsp{
		PayResult: &unified_assets.PayResult{
			OrderId: order.Outorderno,
			Balance: balance.IntPart(),
			ErrMsg:  "",
		},
	}
	c.gopenSuccess(ctx, rsp)
}

func (c *UnifiedAssetsController) SendGiftPackage(ctx *gin.Context) {
	body := &unified_assets.SendWelfareReq{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("请求SendGiftPackage接口, 参数检查失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}

	// 回调校验发放方订单
	err := rpc.GiftPackageBusinessCheckSend(body, "kugou")
	if err != nil {
		logger.Error("SendGiftPackage GiftPackageBusinessCheckSend，调用接口失败。body:%v, err:%v", body, err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}

	// 调用内部发放礼包服务
	strBody, err := json.Marshal(body)
	if err != nil {
		logger.Error("请求SendGiftPackage接口, 序列化失败。err: %+v", err)
		c.tmeFailure(ctx, *ecode.FAILURE)
		return
	}
	logger.Warnf("SendGiftPackage，请求参数。body: %s", strBody)
	orderRequest, ruleInfo, err := award.CreateOrderRequestAndRuleInfo(body)
	if err != nil {
		logger.Error("SendGiftPackage，调用接口失败。orderRequest:%v, ruleInfo:%v, err:%v", orderRequest, ruleInfo, err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}

	isSuccess, _ := rpc.CreateDeliverOrderByRule(orderRequest, ruleInfo)
	if !isSuccess {
		logger.Error("SendGiftPackage CreateDeliverOrderByRule，调用接口失败。isSuccess:%v, err:%v", isSuccess, err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	// 转调内部服务
	rsp := unified_assets.SendWelfareRsp{
		OrderId: body.OrderId,
	}

	c.gopenSuccess(ctx, rsp)
}

func (c *UnifiedAssetsController) BatchGetGiftPackage(ctx *gin.Context) {
	body := &unified_assets.BatchGetWelfareInfoReq{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("SendSingleRewardCallback，解析参数失败。err: %v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	logger.Warnf("BatchGetGiftPackage，请求参数。body: %+v", body)
	rsp, err := award.BizBatchGetGiftPackage(body)
	if err != nil {
		logger.Error("service.BizBatchGetGiftPackage，调用接口失败。rsp:%v, err:%v", rsp, err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	//rsp := unified_assets.BatchGetWelfareInfoRsp{}
	c.gopenSuccess(ctx, rsp)
}

func (c *UnifiedAssetsController) BatchGetGiftInfo(ctx *gin.Context) {
	body := &unified_assets.BatchGetGiftInfoReq{}
	if err := kit.GinShouldBindJsonOrQuery(body, ctx); err != nil {
		logger.Error("BatchGetGiftInfo，解析参数失败。err: %v", err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	logger.Warnf("BatchGetGiftInfo，请求参数。body: %+v", body)
	rsp, err := award.BizBatchGetGiftInfo(body)
	if err != nil {
		logger.Error("BatchGetGiftInfo，调用接口失败。rsp:%v, err:%v", rsp, err)
		c.tmeFailure(ctx, *ecode.InvalidParam)
		return
	}
	//rsp := unified_assets.BatchGetWelfareInfoRsp{}
	c.gopenSuccess(ctx, rsp)
}
