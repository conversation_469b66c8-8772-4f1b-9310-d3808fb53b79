package controller

import (
	"context"
	"fmt"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"kugou_adapter_service/client"
	"kugou_adapter_service/ecode"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter"
	"kugou_adapter_service/pojo/vo"
	commonmsg "kugou_adapter_service/third-party/gen-go/common_msg"
	"kugou_adapter_service/utils"
	"kugou_adapter_service/utils/kit"
	"strings"
)

type RoomMsgController struct {
	BaseController
}

// RoomMsg 房间公屏消息，点击可跳转至URL
func (c *RoomMsgController) RoomMsg(ctx *gin.Context) {
	roomMsgReq := &adapter.RoomMsgReq{}
	if err := kit.GinShouldBindJsonOrQuery(roomMsgReq, ctx); err != nil {
		logger.Error("RoomMsg，解析参数失败。err: %v", err)
		c.gopenFailure(ctx, *ecode.InvalidParam)
		return
	}
	logger.Warnf("RoomMsg，请求参数。body: %+v", roomMsgReq)

	// 获取推送的平台
	platType := roomMsgReq.ExtData["platType"]
	if platType == "" {
		c.gopenFailure(ctx, *ecode.InvalidParam)
		return
	}

	// 按照平台推送
	switch platType {
	case "fx":
		sendFxRoomMsg(roomMsgReq)
	case "ys":
		sendYsRoomMsg(roomMsgReq)
	case "cc":
		sendCcRoomMsg(roomMsgReq)
	}

	rsp := adapter.RoomMsgRsp{}
	c.gopenSuccess(ctx, rsp)
}

// 繁星推送房间消息
// 参数：
//
//	msg - 房间消息
//
// 返回值：
//
//	无
func sendFxRoomMsg(msg *adapter.RoomMsgReq) {
	// 神秘嘉宾处理
	userInfo, _ := client.GetUserInfoClient().FxGetUserInfo(utils.ToInt64(msg.OpenId, 0))
	if userInfo != nil {
		userNickName := client.GetUserStarVipClient().GetUserStarVipNickName(utils.ToInt64(msg.OpenId, 0))
		strings.Replace(msg.MsgText, userInfo.Nickname, userNickName, 1)
	}

	//神秘嘉宾处理
	commonPublicChat := client.CommonPublicChat{
		Key:             client.KeyTower,
		Template:        msg.MsgText,
		Images:          make([]string, 0),
		ClickType:       client.ClickTypeHalfH5,
		Url:             msg.ExtData["jumpUrl"],
		UseCommonParams: false,
		JumpRoomId:      0,
		StarsName:       "",
		ColorStyle:      client.ColorStyleDefault,
		GiftId:          0,
	}
	client.SendCommonPublicChatToRoom(utils.ToInt32(msg.RoomId, 0), commonPublicChat)
}

// 鱼声推送房间消息
// 参数：
//
//	msg - 房间消息
//
// 返回值：
//
//	无
func sendYsRoomMsg(msg *adapter.RoomMsgReq) {

	template := msg.MsgText
	msgType := client.CommonMsgTypeText
	function := client.CommonMsgFunctionHalfH5
	bizId := int32(10)
	msgConfigMap := make(map[string]string)
	msgConfigMap["url"] = msg.ExtData["jumpUrl"]
	buttonText := msg.ExtData["jumpText"]
	roomMsg := &commonmsg.CommonMsgDto{
		Type:            &msgType,
		Text:            &template,
		TextColor:       "#FFFFFFFF",
		TriggerFunction: &function,
		BizId:           &bizId,
		Config:          msgConfigMap,
		Button:          &buttonText,
		ButtonTextColor: "#FFFF8C00",
	}
	client.CommonMsgThriftClient.YsSendRoomCommonRoomMsg(context.Background(), utils.ToInt32(msg.RoomId, 0), roomMsg)
}

// 唱唱推送房间消息
// 参数：
//
//	msg - 房间消息
//
// 返回值：
//
//	无
func sendCcRoomMsg(msg *adapter.RoomMsgReq) {

	template := msg.MsgText
	msgType := utils.ToInt(client.CommonMsgTypeText, 0)
	function := utils.ToInt(client.CommonMsgFunctionHalfH5, 0)
	bizId := 10
	buttonText := msg.ExtData["jumpText"]

	roomMsg := &client.SingMsgVo{
		MsgType: 3210,
		MsgData: client.SingMsgData{
			Type:      msgType,
			Text:      template,
			Images:    make([]string, 0),
			LeftImage: "",
			Button:    buttonText,
			Function:  function,
			BizId:     bizId,
			Config: map[string]interface{}{
				"url": msg.ExtData["jumpUrl"],
			},
		},
	}
	client.CommonMsgThriftClient.CcSendRoomCommonRoomMsg(utils.ToInt32(msg.RoomId, 0), utils.JsonString(roomMsg))
}

// BigHornMsg 大喇叭消息，繁星鱼声唱唱为非金钱头条，点击跳转指定直播间
func (c *RoomMsgController) BigHornMsg(ctx *gin.Context) {
	bigHornMsgReq := &adapter.BigHornMsgReq{}
	if err := kit.GinShouldBindJsonOrQuery(bigHornMsgReq, ctx); err != nil {
		logger.Error("BigHornMsg，解析参数失败。err: %v", err)
		c.gopenFailure(ctx, *ecode.InvalidParam)
		return
	}
	logger.Warnf("BigHornMsg，请求参数。body: %+v", bigHornMsgReq)

	// 获取演示配置ID
	configId := bigHornMsgReq.Attach["configID"]
	if configId == "" {
		c.gopenFailure(ctx, *ecode.InvalidParam)
		return
	}

	// 获取推送的平台
	platType := bigHornMsgReq.Attach["platType"]
	if platType == "" {
		c.gopenFailure(ctx, *ecode.InvalidParam)
		return
	}

	// 获取Apolloq全局推送玩法配置
	bigHornMsgConfig := getBigHornMsgConfig(bigHornMsgReq.AppId, platType, configId)
	if bigHornMsgConfig == nil {
		c.gopenFailure(ctx, *ecode.InvalidParam)
		return
	}

	// 按照平台推送
	switch platType {
	case "fx":
		sendFxBigHornMsg(bigHornMsgConfig, bigHornMsgReq)
	case "ys":
		sendYsBigHornMsg(bigHornMsgConfig, bigHornMsgReq)
	case "cc":
		sendCcBigHornMsg(bigHornMsgConfig, bigHornMsgReq)
	}

	rsp := adapter.BigHornMsgRsp{}
	c.gopenSuccess(ctx, rsp)
}

// 繁星推送大喇叭消息（全局公聊点击打开游戏）
// 参数：
//
//	bigHornMsgConfig - 头条配置
//	msg - 头条消息
//
// 返回值：
//
//	无
func sendFxBigHornMsg(bigHornMsgConfig *vo.BigHornMsgConfig, msg *adapter.BigHornMsgReq) {
	// 神秘嘉宾处理
	userInfo, _ := client.GetUserInfoClient().FxGetUserInfo(utils.ToInt64(msg.OpenId, 0))
	if userInfo != nil {
		userNickName := client.GetUserStarVipClient().GetUserStarVipNickName(utils.ToInt64(msg.OpenId, 0))
		strings.Replace(msg.Content, userInfo.Nickname, userNickName, 1)
	}

	// 跳转类型配置
	var clickType int32
	var jumpRoomId int32
	switch bigHornMsgConfig.JumpType {
	case 1:
		clickType = client.ClickTypeHalfH5
		jumpRoomId = 0
	case 2:
		clickType = client.ClickTypeRoom
		jumpRoomId = utils.ToInt32(msg.RoomId, 0)
	default:
		if msg.Open_H5_URL != "" {
			clickType = client.ClickTypeHalfH5
			jumpRoomId = 0
		} else {
			clickType = client.ClickTypeRoom
			jumpRoomId = utils.ToInt32(msg.RoomId, 0)
		}
	}

	// 获取配置的跳转文案
	if bigHornMsgConfig.Extra["JumpText"] != "" {
		msg.Content = msg.Content + bigHornMsgConfig.Extra["JumpText"]
	} else {
		msg.Content = msg.Content + "，我也要玩>>"
	}

	// 跳转链接
	var url string
	if bigHornMsgConfig.JumpUrl != "" {
		url = bigHornMsgConfig.JumpUrl
	} else {
		url = msg.Open_H5_URL
	}

	commonPublicChat := client.CommonPublicChat{
		Key:             client.KeyTower,
		Template:        msg.Content,
		Images:          make([]string, 0),
		ClickType:       clickType,
		Url:             url,
		UseCommonParams: false,
		JumpRoomId:      jumpRoomId,
		StarsName:       "",
		ColorStyle:      client.ColorStyleDefault,
		GiftId:          0,
	}
	client.SendCommonPublicChatToAll(commonPublicChat)
}

// 繁星推送大喇叭消息（全局公聊点击打开游戏）
// 参数：
//
//	bigHornMsgConfig - 头条配置
//	msg - 头条消息
//
// 返回值：
//
//	无
func sendYsBigHornMsg(bigHornMsgConfig *vo.BigHornMsgConfig, msg *adapter.BigHornMsgReq) {

	template := msg.Content
	bizId := int32(10)
	msgConfigMap := make(map[string]string)
	msgType := client.CommonMsgTypeText

	var function int32
	switch bigHornMsgConfig.JumpType {
	case 1:
		function = client.CommonMsgFunctionHalfH5
		msgConfigMap["url"] = bigHornMsgConfig.JumpUrl
	case 2:
		function = client.CommonMsgFunctionRoom
		msgConfigMap["groupId"] = msg.Attach["groupId"]
	default:
		if msg.Open_H5_URL != "" {
			function = client.CommonMsgFunctionHalfH5
			msgConfigMap["url"] = bigHornMsgConfig.JumpUrl
		} else {
			function = client.CommonMsgFunctionRoom
			msgConfigMap["groupId"] = msg.Attach["groupId"]
		}
	}

	// 跳转文案
	buttonText := "我也要玩"
	if bigHornMsgConfig.Extra["JumpText"] != "" {
		buttonText = msg.Content + bigHornMsgConfig.Extra["JumpText"]
	}
	roomMsg := &commonmsg.CommonMsgDto{
		Type:            &msgType,
		Text:            &template,
		TextColor:       "#FFFFFFFF",
		TriggerFunction: &function,
		BizId:           &bizId,
		Config:          msgConfigMap,
		Button:          &buttonText,
		ButtonTextColor: "#FFFF8C00",
	}
	client.CommonMsgThriftClient.YsSendAllCommonRoomMsg(context.Background(), roomMsg)
}

// 唱唱推送大喇叭消息（房间公聊）
// 参数：
//
//	bigHornMsgConfig - 头条配置
//	msg - 头条消息
//
// 返回值：
//
//	无
func sendCcBigHornMsg(bigHornMsgConfig *vo.BigHornMsgConfig, msg *adapter.BigHornMsgReq) {
	template := msg.Content
	msgType := utils.ToInt(client.CommonMsgTypeText, 0)
	bizId := 10
	msgConfigMap := make(map[string]interface{})

	var function int
	switch bigHornMsgConfig.JumpType {
	case 1:
		function = utils.ToInt(client.CommonMsgFunctionHalfH5, 0)
		msgConfigMap["url"] = bigHornMsgConfig.JumpUrl
	case 2:
		function = utils.ToInt(client.CommonMsgFunctionRoom, 0)
		msgConfigMap["groupId"] = msg.Attach["groupId"]
	default:
		if msg.Open_H5_URL != "" {
			function = utils.ToInt(client.CommonMsgFunctionHalfH5, 0)
			msgConfigMap["url"] = bigHornMsgConfig.JumpUrl
		} else {
			function = utils.ToInt(client.CommonMsgFunctionRoom, 0)
			msgConfigMap["groupId"] = msg.Attach["groupId"]
		}
	}

	// 跳转文案
	buttonText := "我也要玩"
	if bigHornMsgConfig.Extra["JumpText"] != "" {
		buttonText = msg.Content + bigHornMsgConfig.Extra["JumpText"]
	}

	roomMsg := &client.SingMsgVo{
		MsgType: 3210,
		MsgData: client.SingMsgData{
			Type:      msgType,
			Text:      template,
			Images:    make([]string, 0),
			LeftImage: "",
			Button:    buttonText,
			Function:  function,
			BizId:     bizId,
			Config: map[string]interface{}{
				"url": bigHornMsgConfig.JumpUrl,
			},
		},
	}
	client.CommonMsgThriftClient.CcSendRoomCommonRoomMsg(utils.ToInt32(msg.RoomId, 0), utils.JsonString(roomMsg))
}

// 从Apollo获取大喇叭配置
// 参数：
//
//		appId - 应用ID
//		platType - 平台类型
//	 	configId - 配置ID
//
// 返回值：
//
//	*vo.HeadlineConfig - 头条配置，未找到配置返回nil
func getBigHornMsgConfig(appId string, platType string, configId string) *vo.BigHornMsgConfig {
	value := helper.GetApolloClient().GetStringValue(fmt.Sprintf("bigHornMsg.%s", appId), "[]")
	list := make([]*vo.BigHornMsgConfig, 0)
	err := utils.ToStructSlice(value, &list)
	if err != nil {
		return nil
	}

	if len(list) <= 0 {
		return nil
	}
	for _, config := range list {
		if config.ConfigId == utils.ToInt32(configId, 0) && config.PlatType == platType {
			return config
		}
	}
	return nil
}
