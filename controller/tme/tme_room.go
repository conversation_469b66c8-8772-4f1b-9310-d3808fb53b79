package tme

import (
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kugou_adapter_service/client"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_room"
	tme_req "kugou_adapter_service/service/structs/requst/tme"
	"kugou_adapter_service/service/structs/response"
	"kugou_adapter_service/utils/kit"
	"net/http"
)

// 房间
type TmeRoomController struct {
	controller.Controller
}

func (m *TmeRoomController) GetRoomInfo(ctx *gin.Context) {
	req := &tme_req.GetRoomInfoReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	logger.Infof(" TmeRoomController GetRoomInfo req:%v", req)

	roomReq := &adapter_room.GetRoomInfoReq{
		RoomId: req.RoomId,
	}
	resp, err := client.GetRoomInfo(req.AppName, roomReq)
	logger.Infof(" TmeRoomController GetRoomInfo resp:%v", resp)
	if err != nil {
		logger.Warnf("TmeRoomController GetRoomInfo Error。err: %v", err)
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}
	ctx.JSON(http.StatusOK, resp)
	return
}

func (m *TmeRoomController) BatchGetRoomInfo(ctx *gin.Context) {
	req := &tme_req.BatchGetRoomInfoReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	logger.Infof(" TmeRoomController BatchGetRoomInfo req:%v", req)

	roomReq := &adapter_room.BatchGetRoomInfoReq{
		RoomIdList: req.RoomIdList,
	}
	resp, err := client.BatchGetRoomInfo(req.AppName, roomReq)

	logger.Infof(" TmeRoomController BatchGetRoomInfo resp:%v", resp)
	if err != nil {
		logger.Warnf("TmeRoomController BatchGetRoomInfo Error。err: %v", err)
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}
	ctx.JSON(http.StatusOK, resp)
	return
}

func (m *TmeRoomController) GetRecommendRoom(ctx *gin.Context) {
	req := &tme_req.GetRecommendRoomReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	logger.Infof(" TmeRoomController GetRecommendRoom req:%v", req)

	roomReq := &adapter_room.GetRecommendRoomReq{
		UserId:  req.UserId,
		IdType:  req.IdType,
		FromTag: req.FromTag,
	}
	resp, err := client.GetRecommendRoom(req.AppName, roomReq)

	logger.Infof(" TmeRoomController GetRecommendRoom resp:%v", resp)
	if err != nil {
		logger.Warnf("TmeRoomController GetRecommendRoom Error。err: %v", err)
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}
	ctx.JSON(http.StatusOK, resp)
	return
}

func (m *TmeRoomController) BatchGetRoomInfoWithUserID(ctx *gin.Context) {
	req := &tme_req.BatchGetRoomInfoWithUserIDReq{}
	if err := kit.GinShouldBindJsonOrQuery(req, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	logger.Infof(" TmeRoomController BatchGetRoomInfoWithUserID req:%v", req)

	roomReq := &adapter_room.BatchGetRoomInfoWithUserIDReq{
		UserIdList: req.UserIdList,
	}
	resp, err := client.BatchGetRoomInfoWithUserID(req.AppName, roomReq)

	logger.Infof(" TmeRoomController BatchGetRoomInfoWithUserID resp:%v", resp)
	if err != nil {
		logger.Warnf("TmeRoomController BatchGetRoomInfoWithUserID Error。err: %v", err)
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}
	ctx.JSON(http.StatusOK, resp)
	return
}
