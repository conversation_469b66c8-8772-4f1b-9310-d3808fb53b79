package tme

import (
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kugou_adapter_service/client"
	tme_req "kugou_adapter_service/service/structs/requst/tme"
	"kugou_adapter_service/service/structs/response"
	"kugou_adapter_service/utils/kit"
	"net/http"
)

// 用户
type TmeUserController struct {
	controller.Controller
}

func (m *TmeUserController) GetProfile(ctx *gin.Context) {
	request := &tme_req.GetProfileReq{}
	if err := kit.GinShouldBindJsonOrQuery(request, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	logger.Infof(" TmeUserController GetProfile req:%v", request)

	resp, err := client.GetProfile(request.AppId, request.OpenId, request.AppName, request.AvatarLength, request.AvatarWidth, request.IdType)

	logger.Infof(" TmeUserController GetProfile resp:%v", resp)
	if err != nil {
		logger.Warnf("TmeUserController GetProfile Error。err: %v", err)
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}
	ctx.JSON(http.StatusOK, resp)
	return
}
