package tme

import (
	controller "git.kugou.net/fxgo/core/conroller"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kugou_adapter_service/client"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter"
	tme_req "kugou_adapter_service/service/structs/requst/tme"
	"kugou_adapter_service/service/structs/response"
	"kugou_adapter_service/utils/kit"
	"net/http"
)

type TmeSafeController struct {
	controller.Controller
}

func (m *TmeSafeController) SafeCheck(ctx *gin.Context) {
	request := &tme_req.SafeCheckReq{}
	if err := kit.GinShouldBindJsonOrQuery(request, ctx); err != nil {
		logger.Warn("", zap.Error(err))
		ctx.JSON(http.StatusOK, response.ErrorResp{
			ErrorCode: 62001,
			ErrorMsg:  "参数错误",
		})
		return
	}
	logger.Infof(" TmeSafeController SafeCheck req:%v", request)

	req := &adapter.SafeCheckReq{
		AppId:     request.AppId,
		SafeAppid: request.SafeAppId,
		Content:   request.Content,
		OpenId:    request.OpenId,
		ToOpenId:  request.ToOpenId,
		Ipv4:      request.Ipv4,
		IdType:    request.IdType,
	}
	resp, err := client.SafeCheck(request.AppName, req)

	logger.Infof(" TmeSafeController SafeCheck resp:%v", resp)
	if err != nil {
		logger.Warnf("TmeSafeController SafeCheck Error。err: %v", err)
		ctx.JSON(http.StatusInternalServerError, nil)
		return
	}
	ctx.JSON(http.StatusOK, resp)
	return
}
