syntax = "proto3";

package interface.open_game_open_api_v2;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/open_game_open_api_v2";

import "google/api/annotations.proto";

service OpenGameOpenApiV2 {
  // 支付V2
  rpc PayV2(PayV2Req) returns (PayV2Rsp) {
    option (google.api.http) = {
      post: "/V2/minigame/openapi/pay"
      body: "*"
    };
  }
  // 发送平台礼物V2
  rpc SendSingleRewardV2(SendSingleRewardV2Req) returns (SendSingleRewardV2Rsp) {
    option (google.api.http) = {
      post: "/V2/minigame/openapi/sendSingleReward"
      body: "*"
    };
  }
  // 礼物信息V2
  rpc RewardDetailV2(RewardDetailV2Req) returns (RewardDetailV2Rsp) {
    option (google.api.http) = {
      post: "/V2/minigame/openapi/rewardDetail"
      body: "*"
    };
  }
}

message Midas {
  string pf = 1;
  string pfKey = 2;
  string session_id = 3;
  string session_type = 4;
  string pay_token = 5;
}

message Device {
  string qua = 1;
  string device_info = 2;
  string end_type = 3;
}

message Scene {
  // 参考 SceneType
  uint32 scene_type = 1;
  string show_id = 2;
  string ugc_id = 3;
}

message OrderConf {
  // 收入类型 1/计收入 2/不计收入
  uint32 revenue_type = 1;
  // 购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得
  //
  //      例如，类型为1：赠送礼物获得，则需送出pay_gift_num个id为pay_gift_id的礼物，赠送目标为Scene中的场景和tarUid（可以将礼物价值和amount进行校验）
  //           类型为3：赠送道具获得，则需送出pay_prop_num个id为pay_prop_id的道具，赠送目标为Scene中的场景和tarUid
  //           类型为2：不送出道具和礼物，直接扣费
  //
  uint32 pay_type = 2;
  // 平台业务ID
  uint32 plat_business_id = 3;
}

message PayData {
  // 订单号
  string bill_no = 1 [json_name = "bill_no"];
  // 扣除后余额
  uint32 balance = 2;
}

message KSongMiddlePay {
  OrderConf orderConf = 1;
  Scene scene = 2;
  Midas midas = 3;
  string sig = 4;
  string app_remark = 5;
  string transaction_id = 6;
}

message KuGouMiddlePay {
  string rc_app_id = 1;
  string std_plat = 2;
}

message PayV2Req {
  string uid = 1;
  string game_appid = 2;
  // 订单号, 全局唯一
  string bill_no = 3;
  // 扣除数量, 必须大于0
  uint32 amount = 4;
  // 送出礼物id
  uint64 pay_gift_id = 5;
  // 送出礼物个数（建议这里的礼物id+礼物个数算出来的饭票数，和amount校验下是否相等）
  uint32 pay_gift_num = 6;
  string room_id = 7;
  // target_uid
  string tar_uid = 8;
  // device
  Device device = 9;
  KSongMiddlePay ksong_middle_param = 10;
  KuGouMiddlePay kugou_middle_param = 11;
}

message PayV2Rsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
  PayData data = 3;
}

message KSongMiddleReward {
  uint64 gift_type = 1;
  string gift_reason = 2;
}

message KuGouMiddleReward {
  string from_uid = 1;
  uint64 expire_time = 2;
  string app = 3;
}

message SendSingleRewardV2Req {
  string uid = 1;
  string game_appid = 2;
  // 数量 num
  uint32 num = 3;
  // 订单号 bill_no
  string bill_no = 4;
  // gift_id
  string gift_id = 5;
  KSongMiddleReward ksong_middle_reward = 6;
  KuGouMiddleReward kugou_middle_reward = 7;
}

message SendSingleRewardV2Rsp {
  int32 error_code = 1 [json_name = "error_code"];
  string error_msg = 2 [json_name = "error_msg"];
}

message KSongMiddleGoods {
  //礼物type 参考reward_sender_comm.proto GiftType
  uint64 gift_type = 1;
}

message RewardDetailV2Req {
  string uid = 1;
  string game_appid = 2;
  // 礼物ID 非奖励ID，为奖励ID下面挂载的礼物
  string gift_id = 3;
  KSongMiddleGoods ksong_middle_goods = 4;
}

//子礼物信息
message SubGiftDetail {
  // 礼物ID
  string sub_gift_id = 1;
  // 礼物type 参考reward_sender_comm.proto GiftType
  string sub_gift_type = 2;
  // 礼物数量
  uint32 sub_gift_num = 3;
  // 礼物名称
  string sub_gift_name = 4;
  // 礼物logo
  string sub_gift_logo = 5;
  // 礼物单价
  uint32 sub_gift_unit_price = 6;
}

message RewardDetailV2Rsp {
  //SubGiftDetail 奖品ID 所指向的礼物列表
  repeated SubGiftDetail gift_array = 1;
  int32 error_code = 2 [json_name = "error_code"];
  string error_msg = 3 [json_name = "error_msg"];
}

enum ENUM_APPID {
  ENUM_PLAT_KNOWN = 0;
  ENUM_APPID_KG = 1;
  ENUM_APPID_MUSIC = 2;
  ENUM_APPID_WESING = 3;
}

enum ENUM_PREPROCESSING_MODEL {
  ENUM_MODEL_UNKNOWN = 0;
  ENUM_MODEL_PROBABILITY = 100;
  ENUM_MODEL_EXCHANGE = 200;
  ENUM_MODEL_ACTIVITY = 300;
}

enum ENUM_PROBABILITY_MODEL_TYPE {
  ENUM_MODEL_PROBABILITY_KNOWN = 0;
  ENUM_MODEL_PROBABILITY_FOR_LOTTERY = 1000001;
  ENUM_MODEL_PROBABILITY_FOR_BLIND_BOX = 1000002;
}

enum ENUM_EXCHANGE_MODEL_TYPE {
  ENUM_MODEL_EXCHANGE_KNOWN = 0;
  ENUM_MODEL_EXCHANGE_FOR_FARM = 2000002;
  ENUM_MODEL_EXCHANGE_FOR_KTV_GIFT_COLLECT = 2000003;
  ENUM_MODEL_EXCHANGE_FOR_GIFT_COLLECT = 2000004;
  ENUM_MODEL_EXCHANGE_FOR_PREMIUMVIP_SCORE = 2000005;
}

enum ENUM_ACTIVITY_MODEL_TYPE {
  ENUM_MODEL_ACTIVITY_KNOWN = 0;
  ENUM_MODEL_ACTIVITY_FOR_TASK = 3000001;
}

message RewardItem {
  uint32 uRewardId = 1;
  string strRewardId = 2;
  uint32 uRewardType = 3;
  int64 lRewardValue = 4;
  uint32 uRewardNum = 5;
  map<string, string> mapExt = 6;
}

message ActivityModelInput {
  RewardItem stReward = 1;
  uint32 uModelSubType = 2;
  repeated RewardItem vecRewards = 3;
}

message ExchangeModelInput {
  RewardItem stReward = 1;
  uint32 uModelSubType = 2;
  repeated RewardItem vecRewards = 3;
}

message ProbabilityItem {
  int64 lProbability = 1;
  RewardItem stReward = 2;
}

message ProbabilityModelInput {
  uint32 uModelSubType = 1;
  repeated ProbabilityItem vecProbability = 2;
}

message CheckIn {
  string strCheckId = 1;
  map<string, string> mapExt = 2;
  int64 lCostValue = 3;
  ProbabilityModelInput stProbability = 4;
  ExchangeModelInput stExchange = 5;
  ActivityModelInput stActivity = 6;
  string strRiskId = 7;
}

message CheckOut {
  bool bPass = 1;
  string strMsg = 2;
  map<string, string> mapExt = 3;
  int64 lExpectedValue = 4;
  int64 lRealValue = 5;
  float fRoi = 6;
}

message RiskPreProcessingCheckReq {
  uint32 uAppId = 1;
  uint32 uModelType = 2;
  CheckIn stCheckIn = 3;
}

message RiskPreProcessingCheckRsp {
  CheckOut stCheckOut = 1;
}
