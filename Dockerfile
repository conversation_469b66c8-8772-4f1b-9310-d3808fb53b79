FROM hb.kgidc.cn:8080/library/centos:7

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
    && cat /etc/timezone \
    && ls -l /etc/localtime \
    && date -R

ENV APP_DIR /data1/goapp/kugou_adapter_service
RUN mkdir -p ${APP_DIR}
COPY ./code ${APP_DIR}
WORKDIR ${APP_DIR}/bin/
RUN chmod +x ${APP_DIR}/bin/kugou_adapter_service
RUN cd ${APP_DIR}/bin/ && ls -al
CMD ["/data1/goapp/kugou_adapter_service/bin/kugou_adapter_service"]