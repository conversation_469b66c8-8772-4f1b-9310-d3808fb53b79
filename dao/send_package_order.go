package dao

import (
	"git.kugou.net/fxgo/core/logger"
	"git.kugou.net/fxgo/core/storage/xgorm"
	"gorm.io/gorm"
	"kugou_adapter_service/constant"
	"kugou_adapter_service/utils/dateutils"
	"time"
)

type SendGiftPackageOrder struct {
	Orderno    int64     `gorm:"column:orderNo;type:bigint(20);comment:订单编号;primaryKey;not null;default:0;" json:"orderNo"`                // 订单编号
	Ordertime  int64     `gorm:"column:orderTime;type:bigint(20);comment:订单时间（毫秒）;not null;default:0;" json:"orderTime"`                 // 订单时间（毫秒）
	Outorderno string    `gorm:"column:outOrderNo;type:varchar(40);comment:前端幂等ID;not null;" json:"outOrderNo"`                            // 前端幂等ID
	Appid      string    `gorm:"column:appId;type:varchar(30);comment:游戏APPID;not null;" json:"appId"`                                       // 游戏APPID
	Kugouid    int64     `gorm:"column:kugouId;type:bigint(20);comment:酷狗ID;not null;default:0;" json:"kugouId"`                             // 酷狗ID
	Openid     string    `gorm:"column:openid;type:varchar(100);comment:游戏用户标识;not null;" json:"openid"`                                 // 游戏用户标识
	Accessid   int32     `gorm:"column:accessId;type:int(11);comment:订单业务ID;not null;default:0;" json:"accessId"`                          // 订单业务ID
	Status     int       `gorm:"column:status;type:tinyint(4);comment:订单扣费状态（0:待处理；1:成功；2:失败）;not null;default:0;" json:"status"` // 订单扣费状态（0:待处理；1:成功；2:失败）
	Extrainfo  string    `gorm:"column:extraInfo;type:varchar(500);comment:游戏附加信息;not null;" json:"extraInfo"`                           // 游戏附加信息
	Createtime time.Time `gorm:"column:createTime;type:datetime;comment:创建时间;not null;default:2023-01-01 00:00:00;" json:"createTime"`     // 创建时间
	Updatetime time.Time `gorm:"column:updateTime;type:datetime;comment:更新时间;not null;default:2023-01-01 00:00:00;" json:"updateTime"`     // 更新时间
}

type SendGiftPackageOrderDao struct {
	db *gorm.DB
}

func NewSendGiftPackageOrderDao() *SendGiftPackageOrderDao {
	db, err := xgorm.GetClient()
	if err != nil {
		logger.Panicf("获取gorm客户端错误,%v", err)
	}
	return &SendGiftPackageOrderDao{db: db}
}

func (d *SendGiftPackageOrderDao) InsertIgnore(month string, model *SendGiftPackageOrder) (int64, error) {
	sql := `
		insert ignore into t_SendGiftPackage_order_` + month + `(orderNo, orderTime, outOrderNo, kugouId, openid, accessId, status,
                                        appId,
                                        createTime, updateTime) 
		values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
		`
	result := d.db.Exec(sql, model.Orderno, model.Ordertime, model.Outorderno, model.Kugouid,
		model.Openid, model.Accessid, model.Status, model.Appid,
		model.Createtime, model.Updatetime)
	if err := result.Error; err != nil {
		logger.Errorf("插入订单失败: %v", err)
		return 0, err
	}
	return result.RowsAffected, nil
}

func (d *SendGiftPackageOrderDao) Find(month string, outOrderNo string) (*SendGiftPackageOrder, error) {
	order := &SendGiftPackageOrder{}
	sql := `select * from t_SendGiftPackage_order_` + month + ` where outOrderNo=?`
	err := d.db.Raw(sql, outOrderNo).First(&order).Error
	if err != nil {
		return nil, err
	}
	return order, nil
}

func (d *SendGiftPackageOrderDao) UpdateStatus(month string, orderNo int64, status constant.ConsumeStatus) (int64, error) {
	sql := `update t_SendGiftPackage_order_` + month + ` set status=?, updateTime=? where orderNo=? and status = 0`
	result := d.db.Exec(sql, status, time.Now(), orderNo)
	if result.Error != nil {
		return 0, result.Error
	}
	rowsAffected := result.RowsAffected
	return rowsAffected, nil
}

func (d *SendGiftPackageOrderDao) UpdateDeliveryStatus(month string, orderNo int64, deliveryStatus int) (int64, error) {
	sql := `update t_SendGiftPackage_order_` + month + ` set deliveryStatus=?, updateTime=? where orderNo=? and status = 1 and deliveryStatus = 0`
	result := d.db.Exec(sql, deliveryStatus, time.Now(), orderNo)
	if result.Error != nil {
		return 0, result.Error
	}
	rowsAffected := result.RowsAffected
	return rowsAffected, nil
}

func (d *SendGiftPackageOrderDao) Query(startTime time.Time, endTime time.Time, status int, deliveryStatus int, limit int) ([]*SendGiftPackageOrder, error) {
	month := dateutils.FormatYearMonth(startTime.UnixMilli())
	sql := `select * from t_SendGiftPackage_order_` + month + ` where orderTime >= ? and orderTime < ? and status = ? and deliveryStatus = ? limit ?`
	orders := []*SendGiftPackageOrder{}
	result := d.db.Raw(sql, startTime.UnixMilli(), endTime.UnixMilli(), status, deliveryStatus, limit).Scan(&orders)
	if result.Error != nil {
		return []*SendGiftPackageOrder{}, result.Error
	}
	return orders, nil
}
