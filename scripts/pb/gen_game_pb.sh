#!/bin/bash

# 设置 protoc 和生成插件的路径
PROTOC=./protoc
PROTOC_GEN_GO=./protoc-gen-go
PROTOC_GEN_GRPC=./protoc-gen-go-grpc

find ./../pb/adapter_room -name "adapter_room.proto" | while read -r PROTO_FILE; do
  DIR=$(dirname "${PROTO_FILE}")
  ${PROTOC} --proto_path=./game/protocol \
             --go_out=${DIR} --plugin=protoc-gen-go=${PROTOC_GEN_GO} \
             --go-grpc_out=${DIR} --plugin=protoc-gen-go-grpc=${PROTOC_GEN_GRPC} \
             "${PROTO_FILE}"
  if [ $? -eq 0 ]; then
    echo "Generated ${PROTO_FILE}"
  else
    echo "Failed to generate ${PROTO_FILE}"
  fi
done

#find ./game/cmd -name "*.proto" | while read -r PROTO_FILE; do
#  DIR=$(dirname "${PROTO_FILE}")
#  ${PROTOC} --proto_path=./game/cmd \
#             --go_out=${DIR} --plugin=protoc-gen-go=${PROTOC_GEN_GO} \
#             --go-grpc_out=${DIR} --plugin=protoc-gen-go-grpc=${PROTOC_GEN_GRPC} \
#             "${PROTO_FILE}"
#  if [ $? -eq 0 ]; then
#    echo "Generated ${PROTO_FILE}"
#  else
#    echo "Failed to generate ${PROTO_FILE}"
#  fi
#done