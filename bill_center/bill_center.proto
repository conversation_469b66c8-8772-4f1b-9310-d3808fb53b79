syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/bill_center";

import "google/api/annotations.proto";
import "pb/game_relationship_aggregate/game_relationship_builder.proto";

message PulsarLoginFlow {
  int64 uid = 1;
  string appId = 2;
  int64 ts = 3;
}

message PulsarLiveHearbeatFlow {
  enum HeartType {
    ReportTypeUnknown = 0;
    ReportTypeEnter = 1; // 进入
    ReportTypeHeartbeat = 2; // 心跳
    ReportTypeLeave = 3; // 离开
  }
  enum RoomType {
    RoomTypeUnknown = 0;
    RoomTypeLive = 1;
    RoomTypeKTV = 2;
  }
  int64 uid = 1;
  HeartType heartbeatType = 2;
  RoomType roomType = 3;
  string roomId = 4;
  string showId = 5;
  int64 beginTime = 6;
  int64 reportTime = 7;
}

message ReportHeartbeatRsp {}

message PulsarLiveHeartbeatFlows {
  repeated PulsarLiveHearbeatFlow bills = 1;
}

message BatchReportHeartbeatRsp {}

message PulsarGift<PERSON>low {
  string openId = 1;       //打赏open_id
  string appId = 2;        //appid
  uint64 giftID = 3;       //礼物ID
  uint64 giftNum = 4;      //礼物数量
  string consumeID = 5;    //消费ID
  uint64 pay = 6;          //支付的价格
  string roomID = 7;       //房间ID
  string showID = 8;       //场次ID
  uint64 ts = 9;           //时间
  string qua = 10;
  string roundID = 11;     //游戏场次ID
  string receiveOpenId = 12;  //被打赏open_id
  uint64 pubTimeMs = 13;   //送礼流水产生时间
  uint64 sendType = 14;    // GiftSendType
}

enum GiftSendType {
  GIFT_SEND_UNKNOW = 0; //未知
  GIFT_SEND_SEND = 1; //送礼
  GIFT_SEND_BACK = 2; //回礼
}

message ReportGiftBillReq {
  uint64 uid = 1;          //打赏uid
  string appId = 2;        //appid
  uint64 giftID = 3;       //礼物ID
  uint64 giftNum = 4;      //礼物数量
  string consumeID = 5;    //消费ID
  uint64 pay = 6;          //支付的价格
  string roomID = 7;       //房间ID
  string showID = 8;       //场次ID
  uint64 ts = 9;           //打赏时间，秒级
  string qua = 10;         //版本号
  string roundID = 11;     //游戏场次ID
  uint64 receiveUID = 12;  //被打赏uid
  uint64 pubTimeMs = 13;   //送礼流水产生时间
  string scene = 14;       //送礼场景
  uint64 sendType = 15;    // GiftSendType
}

message ReportGiftBillRsp {
}


service PulsarBillCenter {
  // 心跳上报
  rpc ReportHeartbeat(PulsarLiveHearbeatFlow) returns (ReportHeartbeatRsp) {
    option (google.api.http) = {
      post: "/heartbeat/report"
      body: "*"
    };
  }
  rpc BatchReportHeartbeat(PulsarLiveHeartbeatFlows) returns (BatchReportHeartbeatRsp);
  rpc ReportGiftBill(ReportGiftBillReq) returns (ReportGiftBillRsp);
  rpc ReportLogin(component.game.LoginReportReq) returns (component.game.LoginReportRsp);
}
