// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package kroompushmsgservice

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// 请求返回结果
//
//
// Attributes:
//  - Ret
//  - ErrMsg
//  - Data
type PlayerResult_ struct {
	Ret    int32  `thrift:"ret,1" db:"ret" json:"ret"`
	ErrMsg string `thrift:"errMsg,2" db:"errMsg" json:"errMsg"`
	Data   string `thrift:"data,3" db:"data" json:"data"`
}

func NewPlayerResult_() *PlayerResult_ {
	return &PlayerResult_{}
}

func (p *PlayerResult_) GetRet() int32 {
	return p.Ret
}

func (p *PlayerResult_) GetErrMsg() string {
	return p.ErrMsg
}

func (p *PlayerResult_) GetData() string {
	return p.Data
}
func (p *PlayerResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlayerResult_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Ret = v
	}
	return nil
}

func (p *PlayerResult_) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ErrMsg = v
	}
	return nil
}

func (p *PlayerResult_) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Data = v
	}
	return nil
}

func (p *PlayerResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "PlayerResult"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlayerResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err)
	}
	return err
}

func (p *PlayerResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "errMsg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:errMsg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.ErrMsg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.errMsg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:errMsg: ", p), err)
	}
	return err
}

func (p *PlayerResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Data)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
	}
	return err
}

func (p *PlayerResult_) Equals(other *PlayerResult_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Ret != other.Ret {
		return false
	}
	if p.ErrMsg != other.ErrMsg {
		return false
	}
	if p.Data != other.Data {
		return false
	}
	return true
}

func (p *PlayerResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlayerResult_(%+v)", *p)
}

type KroomMsgThriftService interface {
	// 推送K房socket消息
	//
	// Parameters:
	//  - RoomId
	//  - Content
	PushKroomSocketMsg(ctx context.Context, roomId int32, content string) (_r *PlayerResult_, _err error)
	// 推送K房私聊的socket消息
	//
	// Parameters:
	//  - RoomId
	//  - KugouId
	//  - Content
	PushSocketMsgToUser(ctx context.Context, roomId int32, kugouId int64, content string) (_r *PlayerResult_, _err error)
}

type KroomMsgThriftServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewKroomMsgThriftServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *KroomMsgThriftServiceClient {
	return &KroomMsgThriftServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewKroomMsgThriftServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *KroomMsgThriftServiceClient {
	return &KroomMsgThriftServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewKroomMsgThriftServiceClient(c thrift.TClient) *KroomMsgThriftServiceClient {
	return &KroomMsgThriftServiceClient{
		c: c,
	}
}

func (p *KroomMsgThriftServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *KroomMsgThriftServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *KroomMsgThriftServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// 推送K房socket消息
//
// Parameters:
//  - RoomId
//  - Content
func (p *KroomMsgThriftServiceClient) PushKroomSocketMsg(ctx context.Context, roomId int32, content string) (_r *PlayerResult_, _err error) {
	var _args0 KroomMsgThriftServicePushKroomSocketMsgArgs
	_args0.RoomId = roomId
	_args0.Content = content
	var _result2 KroomMsgThriftServicePushKroomSocketMsgResult
	var _meta1 thrift.ResponseMeta
	_meta1, _err = p.Client_().Call(ctx, "pushKroomSocketMsg", &_args0, &_result2)
	p.SetLastResponseMeta_(_meta1)
	if _err != nil {
		return
	}
	if _ret3 := _result2.GetSuccess(); _ret3 != nil {
		return _ret3, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "pushKroomSocketMsg failed: unknown result")
}

// 推送K房私聊的socket消息
//
// Parameters:
//  - RoomId
//  - KugouId
//  - Content
func (p *KroomMsgThriftServiceClient) PushSocketMsgToUser(ctx context.Context, roomId int32, kugouId int64, content string) (_r *PlayerResult_, _err error) {
	var _args4 KroomMsgThriftServicePushSocketMsgToUserArgs
	_args4.RoomId = roomId
	_args4.KugouId = kugouId
	_args4.Content = content
	var _result6 KroomMsgThriftServicePushSocketMsgToUserResult
	var _meta5 thrift.ResponseMeta
	_meta5, _err = p.Client_().Call(ctx, "pushSocketMsgToUser", &_args4, &_result6)
	p.SetLastResponseMeta_(_meta5)
	if _err != nil {
		return
	}
	if _ret7 := _result6.GetSuccess(); _ret7 != nil {
		return _ret7, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "pushSocketMsgToUser failed: unknown result")
}

type KroomMsgThriftServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      KroomMsgThriftService
}

func (p *KroomMsgThriftServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *KroomMsgThriftServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *KroomMsgThriftServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewKroomMsgThriftServiceProcessor(handler KroomMsgThriftService) *KroomMsgThriftServiceProcessor {

	self8 := &KroomMsgThriftServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self8.processorMap["pushKroomSocketMsg"] = &kroomMsgThriftServiceProcessorPushKroomSocketMsg{handler: handler}
	self8.processorMap["pushSocketMsgToUser"] = &kroomMsgThriftServiceProcessorPushSocketMsgToUser{handler: handler}
	return self8
}

func (p *KroomMsgThriftServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x9 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x9.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x9

}

type kroomMsgThriftServiceProcessorPushKroomSocketMsg struct {
	handler KroomMsgThriftService
}

func (p *kroomMsgThriftServiceProcessorPushKroomSocketMsg) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err10 error
	args := KroomMsgThriftServicePushKroomSocketMsgArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "pushKroomSocketMsg", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KroomMsgThriftServicePushKroomSocketMsgResult{}
	if retval, err2 := p.handler.PushKroomSocketMsg(ctx, args.RoomId, args.Content); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc11 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing pushKroomSocketMsg: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "pushKroomSocketMsg", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if err2 := _exc11.Write(ctx, oprot); _write_err10 == nil && err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err10 == nil && err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err10 == nil && err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if _write_err10 != nil {
			return false, thrift.WrapTException(_write_err10)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "pushKroomSocketMsg", thrift.REPLY, seqId); err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err10 == nil && err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err10 == nil && err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err10 == nil && err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if _write_err10 != nil {
		return false, thrift.WrapTException(_write_err10)
	}
	return true, err
}

type kroomMsgThriftServiceProcessorPushSocketMsgToUser struct {
	handler KroomMsgThriftService
}

func (p *kroomMsgThriftServiceProcessorPushSocketMsgToUser) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err12 error
	args := KroomMsgThriftServicePushSocketMsgToUserArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "pushSocketMsgToUser", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KroomMsgThriftServicePushSocketMsgToUserResult{}
	if retval, err2 := p.handler.PushSocketMsgToUser(ctx, args.RoomId, args.KugouId, args.Content); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc13 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing pushSocketMsgToUser: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "pushSocketMsgToUser", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if err2 := _exc13.Write(ctx, oprot); _write_err12 == nil && err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err12 == nil && err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err12 == nil && err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if _write_err12 != nil {
			return false, thrift.WrapTException(_write_err12)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "pushSocketMsgToUser", thrift.REPLY, seqId); err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err12 == nil && err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err12 == nil && err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err12 == nil && err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if _write_err12 != nil {
		return false, thrift.WrapTException(_write_err12)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - RoomId
//  - Content
type KroomMsgThriftServicePushKroomSocketMsgArgs struct {
	RoomId  int32  `thrift:"roomId,1" db:"roomId" json:"roomId"`
	Content string `thrift:"content,2" db:"content" json:"content"`
}

func NewKroomMsgThriftServicePushKroomSocketMsgArgs() *KroomMsgThriftServicePushKroomSocketMsgArgs {
	return &KroomMsgThriftServicePushKroomSocketMsgArgs{}
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) GetContent() string {
	return p.Content
}
func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "pushKroomSocketMsg_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:content: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Content)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:content: ", p), err)
	}
	return err
}

func (p *KroomMsgThriftServicePushKroomSocketMsgArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KroomMsgThriftServicePushKroomSocketMsgArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KroomMsgThriftServicePushKroomSocketMsgResult struct {
	Success *PlayerResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKroomMsgThriftServicePushKroomSocketMsgResult() *KroomMsgThriftServicePushKroomSocketMsgResult {
	return &KroomMsgThriftServicePushKroomSocketMsgResult{}
}

var KroomMsgThriftServicePushKroomSocketMsgResult_Success_DEFAULT *PlayerResult_

func (p *KroomMsgThriftServicePushKroomSocketMsgResult) GetSuccess() *PlayerResult_ {
	if !p.IsSetSuccess() {
		return KroomMsgThriftServicePushKroomSocketMsgResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KroomMsgThriftServicePushKroomSocketMsgResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &PlayerResult_{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "pushKroomSocketMsg_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushKroomSocketMsgResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KroomMsgThriftServicePushKroomSocketMsgResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KroomMsgThriftServicePushKroomSocketMsgResult(%+v)", *p)
}

// Attributes:
//  - RoomId
//  - KugouId
//  - Content
type KroomMsgThriftServicePushSocketMsgToUserArgs struct {
	RoomId  int32  `thrift:"roomId,1" db:"roomId" json:"roomId"`
	KugouId int64  `thrift:"kugouId,2" db:"kugouId" json:"kugouId"`
	Content string `thrift:"content,3" db:"content" json:"content"`
}

func NewKroomMsgThriftServicePushSocketMsgToUserArgs() *KroomMsgThriftServicePushSocketMsgToUserArgs {
	return &KroomMsgThriftServicePushSocketMsgToUserArgs{}
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) GetKugouId() int64 {
	return p.KugouId
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) GetContent() string {
	return p.Content
}
func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "pushSocketMsgToUser_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:kugouId: ", p), err)
	}
	return err
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Content)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *KroomMsgThriftServicePushSocketMsgToUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KroomMsgThriftServicePushSocketMsgToUserArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KroomMsgThriftServicePushSocketMsgToUserResult struct {
	Success *PlayerResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKroomMsgThriftServicePushSocketMsgToUserResult() *KroomMsgThriftServicePushSocketMsgToUserResult {
	return &KroomMsgThriftServicePushSocketMsgToUserResult{}
}

var KroomMsgThriftServicePushSocketMsgToUserResult_Success_DEFAULT *PlayerResult_

func (p *KroomMsgThriftServicePushSocketMsgToUserResult) GetSuccess() *PlayerResult_ {
	if !p.IsSetSuccess() {
		return KroomMsgThriftServicePushSocketMsgToUserResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KroomMsgThriftServicePushSocketMsgToUserResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &PlayerResult_{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "pushSocketMsgToUser_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KroomMsgThriftServicePushSocketMsgToUserResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KroomMsgThriftServicePushSocketMsgToUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KroomMsgThriftServicePushSocketMsgToUserResult(%+v)", *p)
}
