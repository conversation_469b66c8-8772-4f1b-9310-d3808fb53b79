// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package kugouadaptersafe

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - AppId
//  - SafeAppId
//  - Content
//  - OpenId
//  - IdType
//  - ToOpenId
//  - Ipv4
type SafeCheckReq struct {
	AppId     string  `thrift:"appId,1,required" db:"appId" json:"appId"`
	SafeAppId int32   `thrift:"safeAppId,2,required" db:"safeAppId" json:"safeAppId"`
	Content   string  `thrift:"content,3,required" db:"content" json:"content"`
	OpenId    string  `thrift:"openId,4,required" db:"openId" json:"openId"`
	IdType    int32   `thrift:"idType,5,required" db:"idType" json:"idType"`
	ToOpenId  *string `thrift:"toOpenId,6" db:"toOpenId" json:"toOpenId,omitempty"`
	Ipv4      *string `thrift:"ipv4,7" db:"ipv4" json:"ipv4,omitempty"`
}

func NewSafeCheckReq() *SafeCheckReq {
	return &SafeCheckReq{}
}

func (p *SafeCheckReq) GetAppId() string {
	return p.AppId
}

func (p *SafeCheckReq) GetSafeAppId() int32 {
	return p.SafeAppId
}

func (p *SafeCheckReq) GetContent() string {
	return p.Content
}

func (p *SafeCheckReq) GetOpenId() string {
	return p.OpenId
}

func (p *SafeCheckReq) GetIdType() int32 {
	return p.IdType
}

var SafeCheckReq_ToOpenId_DEFAULT string

func (p *SafeCheckReq) GetToOpenId() string {
	if !p.IsSetToOpenId() {
		return SafeCheckReq_ToOpenId_DEFAULT
	}
	return *p.ToOpenId
}

var SafeCheckReq_Ipv4_DEFAULT string

func (p *SafeCheckReq) GetIpv4() string {
	if !p.IsSetIpv4() {
		return SafeCheckReq_Ipv4_DEFAULT
	}
	return *p.Ipv4
}
func (p *SafeCheckReq) IsSetToOpenId() bool {
	return p.ToOpenId != nil
}

func (p *SafeCheckReq) IsSetIpv4() bool {
	return p.Ipv4 != nil
}

func (p *SafeCheckReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetAppId bool = false
	var issetSafeAppId bool = false
	var issetContent bool = false
	var issetOpenId bool = false
	var issetIdType bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetSafeAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetOpenId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetIdType = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	if !issetSafeAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field SafeAppId is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	if !issetOpenId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OpenId is not set"))
	}
	if !issetIdType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IdType is not set"))
	}
	return nil
}

func (p *SafeCheckReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *SafeCheckReq) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.SafeAppId = v
	}
	return nil
}

func (p *SafeCheckReq) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *SafeCheckReq) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.OpenId = v
	}
	return nil
}

func (p *SafeCheckReq) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.IdType = v
	}
	return nil
}

func (p *SafeCheckReq) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.ToOpenId = &v
	}
	return nil
}

func (p *SafeCheckReq) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.Ipv4 = &v
	}
	return nil
}

func (p *SafeCheckReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "SafeCheckReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *SafeCheckReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err)
	}
	return err
}

func (p *SafeCheckReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "safeAppId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:safeAppId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.SafeAppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.safeAppId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:safeAppId: ", p), err)
	}
	return err
}

func (p *SafeCheckReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Content)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *SafeCheckReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "openId", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:openId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.OpenId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.openId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:openId: ", p), err)
	}
	return err
}

func (p *SafeCheckReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "idType", thrift.I32, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:idType: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.IdType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.idType (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:idType: ", p), err)
	}
	return err
}

func (p *SafeCheckReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetToOpenId() {
		if err := oprot.WriteFieldBegin(ctx, "toOpenId", thrift.STRING, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:toOpenId: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.ToOpenId)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.toOpenId (6) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:toOpenId: ", p), err)
		}
	}
	return err
}

func (p *SafeCheckReq) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetIpv4() {
		if err := oprot.WriteFieldBegin(ctx, "ipv4", thrift.STRING, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:ipv4: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Ipv4)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.ipv4 (7) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:ipv4: ", p), err)
		}
	}
	return err
}

func (p *SafeCheckReq) Equals(other *SafeCheckReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.AppId != other.AppId {
		return false
	}
	if p.SafeAppId != other.SafeAppId {
		return false
	}
	if p.Content != other.Content {
		return false
	}
	if p.OpenId != other.OpenId {
		return false
	}
	if p.IdType != other.IdType {
		return false
	}
	if p.ToOpenId != other.ToOpenId {
		if p.ToOpenId == nil || other.ToOpenId == nil {
			return false
		}
		if (*p.ToOpenId) != (*other.ToOpenId) {
			return false
		}
	}
	if p.Ipv4 != other.Ipv4 {
		if p.Ipv4 == nil || other.Ipv4 == nil {
			return false
		}
		if (*p.Ipv4) != (*other.Ipv4) {
			return false
		}
	}
	return true
}

func (p *SafeCheckReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SafeCheckReq(%+v)", *p)
}

// Attributes:
//  - Code
//  - Msg
//  - Data
type SafeCheckRsp struct {
	Code int32             `thrift:"code,1,required" db:"code" json:"code"`
	Msg  string            `thrift:"msg,2,required" db:"msg" json:"msg"`
	Data *SafeCheckResult_ `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewSafeCheckRsp() *SafeCheckRsp {
	return &SafeCheckRsp{}
}

func (p *SafeCheckRsp) GetCode() int32 {
	return p.Code
}

func (p *SafeCheckRsp) GetMsg() string {
	return p.Msg
}

var SafeCheckRsp_Data_DEFAULT *SafeCheckResult_

func (p *SafeCheckRsp) GetData() *SafeCheckResult_ {
	if !p.IsSetData() {
		return SafeCheckRsp_Data_DEFAULT
	}
	return p.Data
}
func (p *SafeCheckRsp) IsSetData() bool {
	return p.Data != nil
}

func (p *SafeCheckRsp) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCode bool = false
	var issetMsg bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCode = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCode {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	return nil
}

func (p *SafeCheckRsp) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *SafeCheckRsp) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *SafeCheckRsp) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	p.Data = &SafeCheckResult_{}
	if err := p.Data.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
	}
	return nil
}

func (p *SafeCheckRsp) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "SafeCheckRsp"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *SafeCheckRsp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err)
	}
	return err
}

func (p *SafeCheckRsp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *SafeCheckRsp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
		}
		if err := p.Data.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
		}
	}
	return err
}

func (p *SafeCheckRsp) Equals(other *SafeCheckRsp) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Code != other.Code {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if !p.Data.Equals(other.Data) {
		return false
	}
	return true
}

func (p *SafeCheckRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SafeCheckRsp(%+v)", *p)
}

// Attributes:
//  - SafeType
type SafeCheckResult_ struct {
	SafeType int32 `thrift:"safeType,1,required" db:"safeType" json:"safeType"`
}

func NewSafeCheckResult_() *SafeCheckResult_ {
	return &SafeCheckResult_{}
}

func (p *SafeCheckResult_) GetSafeType() int32 {
	return p.SafeType
}
func (p *SafeCheckResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetSafeType bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetSafeType = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetSafeType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field SafeType is not set"))
	}
	return nil
}

func (p *SafeCheckResult_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.SafeType = v
	}
	return nil
}

func (p *SafeCheckResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "SafeCheckResult"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *SafeCheckResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "safeType", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:safeType: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.SafeType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.safeType (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:safeType: ", p), err)
	}
	return err
}

func (p *SafeCheckResult_) Equals(other *SafeCheckResult_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.SafeType != other.SafeType {
		return false
	}
	return true
}

func (p *SafeCheckResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SafeCheckResult_(%+v)", *p)
}

type KugouAdapterSafeCheckService interface {
	// Parameters:
	//  - AppName
	//  - Req
	SafeCheck(ctx context.Context, appName string, req *SafeCheckReq) (_r *SafeCheckRsp, _err error)
}

type KugouAdapterSafeCheckServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewKugouAdapterSafeCheckServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *KugouAdapterSafeCheckServiceClient {
	return &KugouAdapterSafeCheckServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewKugouAdapterSafeCheckServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *KugouAdapterSafeCheckServiceClient {
	return &KugouAdapterSafeCheckServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewKugouAdapterSafeCheckServiceClient(c thrift.TClient) *KugouAdapterSafeCheckServiceClient {
	return &KugouAdapterSafeCheckServiceClient{
		c: c,
	}
}

func (p *KugouAdapterSafeCheckServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *KugouAdapterSafeCheckServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *KugouAdapterSafeCheckServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// Parameters:
//  - AppName
//  - Req
func (p *KugouAdapterSafeCheckServiceClient) SafeCheck(ctx context.Context, appName string, req *SafeCheckReq) (_r *SafeCheckRsp, _err error) {
	var _args0 KugouAdapterSafeCheckServiceSafeCheckArgs
	_args0.AppName = appName
	_args0.Req = req
	var _result2 KugouAdapterSafeCheckServiceSafeCheckResult
	var _meta1 thrift.ResponseMeta
	_meta1, _err = p.Client_().Call(ctx, "safeCheck", &_args0, &_result2)
	p.SetLastResponseMeta_(_meta1)
	if _err != nil {
		return
	}
	if _ret3 := _result2.GetSuccess(); _ret3 != nil {
		return _ret3, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "safeCheck failed: unknown result")
}

type KugouAdapterSafeCheckServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      KugouAdapterSafeCheckService
}

func (p *KugouAdapterSafeCheckServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *KugouAdapterSafeCheckServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *KugouAdapterSafeCheckServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewKugouAdapterSafeCheckServiceProcessor(handler KugouAdapterSafeCheckService) *KugouAdapterSafeCheckServiceProcessor {

	self4 := &KugouAdapterSafeCheckServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self4.processorMap["safeCheck"] = &kugouAdapterSafeCheckServiceProcessorSafeCheck{handler: handler}
	return self4
}

func (p *KugouAdapterSafeCheckServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x5.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x5

}

type kugouAdapterSafeCheckServiceProcessorSafeCheck struct {
	handler KugouAdapterSafeCheckService
}

func (p *kugouAdapterSafeCheckServiceProcessorSafeCheck) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err6 error
	args := KugouAdapterSafeCheckServiceSafeCheckArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "safeCheck", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KugouAdapterSafeCheckServiceSafeCheckResult{}
	if retval, err2 := p.handler.SafeCheck(ctx, args.AppName, args.Req); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc7 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing safeCheck: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "safeCheck", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err6 = thrift.WrapTException(err2)
		}
		if err2 := _exc7.Write(ctx, oprot); _write_err6 == nil && err2 != nil {
			_write_err6 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err6 == nil && err2 != nil {
			_write_err6 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err6 == nil && err2 != nil {
			_write_err6 = thrift.WrapTException(err2)
		}
		if _write_err6 != nil {
			return false, thrift.WrapTException(_write_err6)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "safeCheck", thrift.REPLY, seqId); err2 != nil {
		_write_err6 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err6 == nil && err2 != nil {
		_write_err6 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err6 == nil && err2 != nil {
		_write_err6 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err6 == nil && err2 != nil {
		_write_err6 = thrift.WrapTException(err2)
	}
	if _write_err6 != nil {
		return false, thrift.WrapTException(_write_err6)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - AppName
//  - Req
type KugouAdapterSafeCheckServiceSafeCheckArgs struct {
	AppName string        `thrift:"appName,1" db:"appName" json:"appName"`
	Req     *SafeCheckReq `thrift:"req,2" db:"req" json:"req"`
}

func NewKugouAdapterSafeCheckServiceSafeCheckArgs() *KugouAdapterSafeCheckServiceSafeCheckArgs {
	return &KugouAdapterSafeCheckServiceSafeCheckArgs{}
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) GetAppName() string {
	return p.AppName
}

var KugouAdapterSafeCheckServiceSafeCheckArgs_Req_DEFAULT *SafeCheckReq

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) GetReq() *SafeCheckReq {
	if !p.IsSetReq() {
		return KugouAdapterSafeCheckServiceSafeCheckArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.Req = &SafeCheckReq{}
	if err := p.Req.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "safeCheck_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AppName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appName: ", p), err)
	}
	return err
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:req: ", p), err)
	}
	if err := p.Req.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:req: ", p), err)
	}
	return err
}

func (p *KugouAdapterSafeCheckServiceSafeCheckArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterSafeCheckServiceSafeCheckArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KugouAdapterSafeCheckServiceSafeCheckResult struct {
	Success *SafeCheckRsp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKugouAdapterSafeCheckServiceSafeCheckResult() *KugouAdapterSafeCheckServiceSafeCheckResult {
	return &KugouAdapterSafeCheckServiceSafeCheckResult{}
}

var KugouAdapterSafeCheckServiceSafeCheckResult_Success_DEFAULT *SafeCheckRsp

func (p *KugouAdapterSafeCheckServiceSafeCheckResult) GetSuccess() *SafeCheckRsp {
	if !p.IsSetSuccess() {
		return KugouAdapterSafeCheckServiceSafeCheckResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KugouAdapterSafeCheckServiceSafeCheckResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &SafeCheckRsp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "safeCheck_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterSafeCheckServiceSafeCheckResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KugouAdapterSafeCheckServiceSafeCheckResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterSafeCheckServiceSafeCheckResult(%+v)", *p)
}
