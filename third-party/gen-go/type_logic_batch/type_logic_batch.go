// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package type_logic_batch

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"kugou_adapter_service/third-party/gen-go/kgthrift/types"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//   - Gid: 消息全局id
//   - IsIdempotent: 是否幂等
type LogicBatchMsgOption struct {
	Gid          int64 `thrift:"gid,1" db:"gid" json:"gid"`
	IsIdempotent bool  `thrift:"isIdempotent,2" db:"isIdempotent" json:"isIdempotent"`
}

func NewLogicBatchMsgOption() *LogicBatchMsgOption {
	return &LogicBatchMsgOption{}
}

var LogicBatchMsgOption_Gid_DEFAULT int64 = 0

func (p *LogicBatchMsgOption) GetGid() int64 {
	return p.Gid
}

var LogicBatchMsgOption_IsIdempotent_DEFAULT bool = false

func (p *LogicBatchMsgOption) GetIsIdempotent() bool {
	return p.IsIdempotent
}
func (p *LogicBatchMsgOption) IsSetGid() bool {
	return p.Gid != LogicBatchMsgOption_Gid_DEFAULT
}

func (p *LogicBatchMsgOption) IsSetIsIdempotent() bool {
	return p.IsIdempotent != LogicBatchMsgOption_IsIdempotent_DEFAULT
}

func (p *LogicBatchMsgOption) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *LogicBatchMsgOption) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Gid = v
	}
	return nil
}

func (p *LogicBatchMsgOption) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.IsIdempotent = v
	}
	return nil
}

func (p *LogicBatchMsgOption) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicBatchMsgOption"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicBatchMsgOption) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetGid() {
		if err := oprot.WriteFieldBegin(ctx, "gid", thrift.I64, 1); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:gid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(p.Gid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.gid (1) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 1:gid: ", p), err)
		}
	}
	return err
}

func (p *LogicBatchMsgOption) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetIsIdempotent() {
		if err := oprot.WriteFieldBegin(ctx, "isIdempotent", thrift.BOOL, 2); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:isIdempotent: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.IsIdempotent)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.isIdempotent (2) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 2:isIdempotent: ", p), err)
		}
	}
	return err
}

func (p *LogicBatchMsgOption) Equals(other *LogicBatchMsgOption) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Gid != other.Gid {
		return false
	}
	if p.IsIdempotent != other.IsIdempotent {
		return false
	}
	return true
}

func (p *LogicBatchMsgOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicBatchMsgOption(%+v)", *p)
}

// 房间广播消息对象
// 支持不同命令号、不同消息体，不同房间ID的批量消息
//
// Attributes:
//   - MessageList
//   - BatchMsgOption
type LogicBatchRoomMessageRequest struct {
	MessageList    []*LogicRoomMessage  `thrift:"messageList,1,required" db:"messageList" json:"messageList"`
	BatchMsgOption *LogicBatchMsgOption `thrift:"batchMsgOption,2,required" db:"batchMsgOption" json:"batchMsgOption"`
}

func NewLogicBatchRoomMessageRequest() *LogicBatchRoomMessageRequest {
	return &LogicBatchRoomMessageRequest{}
}

func (p *LogicBatchRoomMessageRequest) GetMessageList() []*LogicRoomMessage {
	return p.MessageList
}

var LogicBatchRoomMessageRequest_BatchMsgOption_DEFAULT *LogicBatchMsgOption

func (p *LogicBatchRoomMessageRequest) GetBatchMsgOption() *LogicBatchMsgOption {
	if !p.IsSetBatchMsgOption() {
		return LogicBatchRoomMessageRequest_BatchMsgOption_DEFAULT
	}
	return p.BatchMsgOption
}
func (p *LogicBatchRoomMessageRequest) IsSetBatchMsgOption() bool {
	return p.BatchMsgOption != nil
}

func (p *LogicBatchRoomMessageRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetMessageList bool = false
	var issetBatchMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetMessageList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetBatchMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetMessageList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MessageList is not set"))
	}
	if !issetBatchMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field BatchMsgOption is not set"))
	}
	return nil
}

func (p *LogicBatchRoomMessageRequest) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*LogicRoomMessage, 0, size)
	p.MessageList = tSlice
	for i := 0; i < size; i++ {
		_elem0 := &LogicRoomMessage{}
		if err := _elem0.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
		}
		p.MessageList = append(p.MessageList, _elem0)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *LogicBatchRoomMessageRequest) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.BatchMsgOption = &LogicBatchMsgOption{}
	if err := p.BatchMsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BatchMsgOption), err)
	}
	return nil
}

func (p *LogicBatchRoomMessageRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicBatchRoomMessageRequest"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicBatchRoomMessageRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "messageList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:messageList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.MessageList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.MessageList {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:messageList: ", p), err)
	}
	return err
}

func (p *LogicBatchRoomMessageRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "batchMsgOption", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:batchMsgOption: ", p), err)
	}
	if err := p.BatchMsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BatchMsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:batchMsgOption: ", p), err)
	}
	return err
}

func (p *LogicBatchRoomMessageRequest) Equals(other *LogicBatchRoomMessageRequest) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if len(p.MessageList) != len(other.MessageList) {
		return false
	}
	for i, _tgt := range p.MessageList {
		_src1 := other.MessageList[i]
		if !_tgt.Equals(_src1) {
			return false
		}
	}
	if !p.BatchMsgOption.Equals(other.BatchMsgOption) {
		return false
	}
	return true
}

func (p *LogicBatchRoomMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicBatchRoomMessageRequest(%+v)", *p)
}

// 房间单播消息对象
// 支持不同命令号、不同消息体，不同房间ID、酷狗ID的批量消息
//
// Attributes:
//   - MessageList
//   - BatchMsgOption
type LogicBatchUserMessageRequest struct {
	MessageList    []*LogicUserMessage  `thrift:"messageList,1,required" db:"messageList" json:"messageList"`
	BatchMsgOption *LogicBatchMsgOption `thrift:"batchMsgOption,2,required" db:"batchMsgOption" json:"batchMsgOption"`
}

func NewLogicBatchUserMessageRequest() *LogicBatchUserMessageRequest {
	return &LogicBatchUserMessageRequest{}
}

func (p *LogicBatchUserMessageRequest) GetMessageList() []*LogicUserMessage {
	return p.MessageList
}

var LogicBatchUserMessageRequest_BatchMsgOption_DEFAULT *LogicBatchMsgOption

func (p *LogicBatchUserMessageRequest) GetBatchMsgOption() *LogicBatchMsgOption {
	if !p.IsSetBatchMsgOption() {
		return LogicBatchUserMessageRequest_BatchMsgOption_DEFAULT
	}
	return p.BatchMsgOption
}
func (p *LogicBatchUserMessageRequest) IsSetBatchMsgOption() bool {
	return p.BatchMsgOption != nil
}

func (p *LogicBatchUserMessageRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetMessageList bool = false
	var issetBatchMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetMessageList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetBatchMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetMessageList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MessageList is not set"))
	}
	if !issetBatchMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field BatchMsgOption is not set"))
	}
	return nil
}

func (p *LogicBatchUserMessageRequest) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*LogicUserMessage, 0, size)
	p.MessageList = tSlice
	for i := 0; i < size; i++ {
		_elem2 := &LogicUserMessage{}
		if err := _elem2.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem2), err)
		}
		p.MessageList = append(p.MessageList, _elem2)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *LogicBatchUserMessageRequest) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.BatchMsgOption = &LogicBatchMsgOption{}
	if err := p.BatchMsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BatchMsgOption), err)
	}
	return nil
}

func (p *LogicBatchUserMessageRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicBatchUserMessageRequest"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicBatchUserMessageRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "messageList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:messageList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.MessageList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.MessageList {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:messageList: ", p), err)
	}
	return err
}

func (p *LogicBatchUserMessageRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "batchMsgOption", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:batchMsgOption: ", p), err)
	}
	if err := p.BatchMsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BatchMsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:batchMsgOption: ", p), err)
	}
	return err
}

func (p *LogicBatchUserMessageRequest) Equals(other *LogicBatchUserMessageRequest) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if len(p.MessageList) != len(other.MessageList) {
		return false
	}
	for i, _tgt := range p.MessageList {
		_src3 := other.MessageList[i]
		if !_tgt.Equals(_src3) {
			return false
		}
	}
	if !p.BatchMsgOption.Equals(other.BatchMsgOption) {
		return false
	}
	return true
}

func (p *LogicBatchUserMessageRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicBatchUserMessageRequest(%+v)", *p)
}

// Attributes:
//   - MessageList
//   - BatchMsgOption
type LogicBatchUserMessageWithoutRoomIdRequest struct {
	MessageList    []*LogicUserMessageWithoutRoomId `thrift:"messageList,1,required" db:"messageList" json:"messageList"`
	BatchMsgOption *LogicBatchMsgOption             `thrift:"batchMsgOption,2,required" db:"batchMsgOption" json:"batchMsgOption"`
}

func NewLogicBatchUserMessageWithoutRoomIdRequest() *LogicBatchUserMessageWithoutRoomIdRequest {
	return &LogicBatchUserMessageWithoutRoomIdRequest{}
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) GetMessageList() []*LogicUserMessageWithoutRoomId {
	return p.MessageList
}

var LogicBatchUserMessageWithoutRoomIdRequest_BatchMsgOption_DEFAULT *LogicBatchMsgOption

func (p *LogicBatchUserMessageWithoutRoomIdRequest) GetBatchMsgOption() *LogicBatchMsgOption {
	if !p.IsSetBatchMsgOption() {
		return LogicBatchUserMessageWithoutRoomIdRequest_BatchMsgOption_DEFAULT
	}
	return p.BatchMsgOption
}
func (p *LogicBatchUserMessageWithoutRoomIdRequest) IsSetBatchMsgOption() bool {
	return p.BatchMsgOption != nil
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetMessageList bool = false
	var issetBatchMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetMessageList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetBatchMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetMessageList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MessageList is not set"))
	}
	if !issetBatchMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field BatchMsgOption is not set"))
	}
	return nil
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*LogicUserMessageWithoutRoomId, 0, size)
	p.MessageList = tSlice
	for i := 0; i < size; i++ {
		_elem4 := &LogicUserMessageWithoutRoomId{}
		if err := _elem4.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem4), err)
		}
		p.MessageList = append(p.MessageList, _elem4)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.BatchMsgOption = &LogicBatchMsgOption{}
	if err := p.BatchMsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BatchMsgOption), err)
	}
	return nil
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicBatchUserMessageWithoutRoomIdRequest"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "messageList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:messageList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.MessageList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.MessageList {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:messageList: ", p), err)
	}
	return err
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "batchMsgOption", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:batchMsgOption: ", p), err)
	}
	if err := p.BatchMsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BatchMsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:batchMsgOption: ", p), err)
	}
	return err
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) Equals(other *LogicBatchUserMessageWithoutRoomIdRequest) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if len(p.MessageList) != len(other.MessageList) {
		return false
	}
	for i, _tgt := range p.MessageList {
		_src5 := other.MessageList[i]
		if !_tgt.Equals(_src5) {
			return false
		}
	}
	if !p.BatchMsgOption.Equals(other.BatchMsgOption) {
		return false
	}
	return true
}

func (p *LogicBatchUserMessageWithoutRoomIdRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicBatchUserMessageWithoutRoomIdRequest(%+v)", *p)
}

// 房间消息对象
//
// Attributes:
//   - Cmd
//   - RoomId
//   - Content
//   - MsgOption
type LogicRoomMessage struct {
	Cmd    int32 `thrift:"cmd,1,required" db:"cmd" json:"cmd"`
	RoomId int32 `thrift:"roomId,2,required" db:"roomId" json:"roomId"`
	// unused field # 3
	Content   []byte           `thrift:"content,4,required" db:"content" json:"content"`
	MsgOption *types.MsgOption `thrift:"msgOption,5,required" db:"msgOption" json:"msgOption"`
}

func NewLogicRoomMessage() *LogicRoomMessage {
	return &LogicRoomMessage{}
}

func (p *LogicRoomMessage) GetCmd() int32 {
	return p.Cmd
}

func (p *LogicRoomMessage) GetRoomId() int32 {
	return p.RoomId
}

func (p *LogicRoomMessage) GetContent() []byte {
	return p.Content
}

var LogicRoomMessage_MsgOption_DEFAULT *types.MsgOption

func (p *LogicRoomMessage) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return LogicRoomMessage_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *LogicRoomMessage) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *LogicRoomMessage) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCmd bool = false
	var issetRoomId bool = false
	var issetContent bool = false
	var issetMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCmd = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCmd {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Cmd is not set"))
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	if !issetMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MsgOption is not set"))
	}
	return nil
}

func (p *LogicRoomMessage) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Cmd = v
	}
	return nil
}

func (p *LogicRoomMessage) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *LogicRoomMessage) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *LogicRoomMessage) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *LogicRoomMessage) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicRoomMessage"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicRoomMessage) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmd", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmd: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Cmd)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmd (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmd: ", p), err)
	}
	return err
}

func (p *LogicRoomMessage) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *LogicRoomMessage) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *LogicRoomMessage) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *LogicRoomMessage) Equals(other *LogicRoomMessage) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Cmd != other.Cmd {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	if bytes.Compare(p.Content, other.Content) != 0 {
		return false
	}
	if !p.MsgOption.Equals(other.MsgOption) {
		return false
	}
	return true
}

func (p *LogicRoomMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicRoomMessage(%+v)", *p)
}

// 用户消息对象
//
// Attributes:
//   - Cmd
//   - RoomId
//   - KugouId
//   - UserId
//   - Content
//   - MsgOption
type LogicUserMessage struct {
	Cmd       int32            `thrift:"cmd,1,required" db:"cmd" json:"cmd"`
	RoomId    int32            `thrift:"roomId,2,required" db:"roomId" json:"roomId"`
	KugouId   int64            `thrift:"kugouId,3,required" db:"kugouId" json:"kugouId"`
	UserId    int64            `thrift:"userId,4,required" db:"userId" json:"userId"`
	Content   []byte           `thrift:"content,5,required" db:"content" json:"content"`
	MsgOption *types.MsgOption `thrift:"msgOption,6,required" db:"msgOption" json:"msgOption"`
}

func NewLogicUserMessage() *LogicUserMessage {
	return &LogicUserMessage{}
}

func (p *LogicUserMessage) GetCmd() int32 {
	return p.Cmd
}

func (p *LogicUserMessage) GetRoomId() int32 {
	return p.RoomId
}

func (p *LogicUserMessage) GetKugouId() int64 {
	return p.KugouId
}

func (p *LogicUserMessage) GetUserId() int64 {
	return p.UserId
}

func (p *LogicUserMessage) GetContent() []byte {
	return p.Content
}

var LogicUserMessage_MsgOption_DEFAULT *types.MsgOption

func (p *LogicUserMessage) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return LogicUserMessage_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *LogicUserMessage) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *LogicUserMessage) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCmd bool = false
	var issetRoomId bool = false
	var issetKugouId bool = false
	var issetUserId bool = false
	var issetContent bool = false
	var issetMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCmd = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetUserId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
				issetMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCmd {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Cmd is not set"))
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetUserId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserId is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	if !issetMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MsgOption is not set"))
	}
	return nil
}

func (p *LogicUserMessage) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Cmd = v
	}
	return nil
}

func (p *LogicUserMessage) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *LogicUserMessage) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *LogicUserMessage) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *LogicUserMessage) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *LogicUserMessage) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *LogicUserMessage) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicUserMessage"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicUserMessage) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmd", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmd: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Cmd)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmd (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmd: ", p), err)
	}
	return err
}

func (p *LogicUserMessage) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *LogicUserMessage) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:kugouId: ", p), err)
	}
	return err
}

func (p *LogicUserMessage) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:userId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.UserId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:userId: ", p), err)
	}
	return err
}

func (p *LogicUserMessage) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:content: ", p), err)
	}
	return err
}

func (p *LogicUserMessage) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:msgOption: ", p), err)
	}
	return err
}

func (p *LogicUserMessage) Equals(other *LogicUserMessage) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Cmd != other.Cmd {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	if p.KugouId != other.KugouId {
		return false
	}
	if p.UserId != other.UserId {
		return false
	}
	if bytes.Compare(p.Content, other.Content) != 0 {
		return false
	}
	if !p.MsgOption.Equals(other.MsgOption) {
		return false
	}
	return true
}

func (p *LogicUserMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicUserMessage(%+v)", *p)
}

// 用户消息对象
//
// Attributes:
//   - Cmd
//   - KugouId
//   - UserId
//   - Content
//   - MsgOption
type LogicUserMessageWithoutRoomId struct {
	Cmd       int32            `thrift:"cmd,1,required" db:"cmd" json:"cmd"`
	KugouId   int64            `thrift:"kugouId,2,required" db:"kugouId" json:"kugouId"`
	UserId    int64            `thrift:"userId,3,required" db:"userId" json:"userId"`
	Content   []byte           `thrift:"content,4,required" db:"content" json:"content"`
	MsgOption *types.MsgOption `thrift:"msgOption,5,required" db:"msgOption" json:"msgOption"`
}

func NewLogicUserMessageWithoutRoomId() *LogicUserMessageWithoutRoomId {
	return &LogicUserMessageWithoutRoomId{}
}

func (p *LogicUserMessageWithoutRoomId) GetCmd() int32 {
	return p.Cmd
}

func (p *LogicUserMessageWithoutRoomId) GetKugouId() int64 {
	return p.KugouId
}

func (p *LogicUserMessageWithoutRoomId) GetUserId() int64 {
	return p.UserId
}

func (p *LogicUserMessageWithoutRoomId) GetContent() []byte {
	return p.Content
}

var LogicUserMessageWithoutRoomId_MsgOption_DEFAULT *types.MsgOption

func (p *LogicUserMessageWithoutRoomId) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return LogicUserMessageWithoutRoomId_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *LogicUserMessageWithoutRoomId) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *LogicUserMessageWithoutRoomId) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCmd bool = false
	var issetKugouId bool = false
	var issetUserId bool = false
	var issetContent bool = false
	var issetMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCmd = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetUserId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCmd {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Cmd is not set"))
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetUserId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserId is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	if !issetMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MsgOption is not set"))
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Cmd = v
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "LogicUserMessageWithoutRoomId"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *LogicUserMessageWithoutRoomId) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmd", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmd: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Cmd)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmd (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmd: ", p), err)
	}
	return err
}

func (p *LogicUserMessageWithoutRoomId) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:kugouId: ", p), err)
	}
	return err
}

func (p *LogicUserMessageWithoutRoomId) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:userId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.UserId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:userId: ", p), err)
	}
	return err
}

func (p *LogicUserMessageWithoutRoomId) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *LogicUserMessageWithoutRoomId) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *LogicUserMessageWithoutRoomId) Equals(other *LogicUserMessageWithoutRoomId) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Cmd != other.Cmd {
		return false
	}
	if p.KugouId != other.KugouId {
		return false
	}
	if p.UserId != other.UserId {
		return false
	}
	if bytes.Compare(p.Content, other.Content) != 0 {
		return false
	}
	if !p.MsgOption.Equals(other.MsgOption) {
		return false
	}
	return true
}

func (p *LogicUserMessageWithoutRoomId) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogicUserMessageWithoutRoomId(%+v)", *p)
}
