// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package headlineservice

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//   - Ret
//   - Msg
type HeadlineSendResult_ struct {
	Ret int32  `thrift:"ret,1,required" db:"ret" json:"ret"`
	Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
}

func NewHeadlineSendResult_() *HeadlineSendResult_ {
	return &HeadlineSendResult_{}
}

func (p *HeadlineSendResult_) GetRet() int32 {
	return p.Ret
}

func (p *HeadlineSendResult_) GetMsg() string {
	return p.Msg
}
func (p *HeadlineSendResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRet bool = false
	var issetMsg bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRet = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRet {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	return nil
}

func (p *HeadlineSendResult_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Ret = v
	}
	return nil
}

func (p *HeadlineSendResult_) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *HeadlineSendResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "HeadlineSendResult"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *HeadlineSendResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err)
	}
	return err
}

func (p *HeadlineSendResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *HeadlineSendResult_) Equals(other *HeadlineSendResult_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Ret != other.Ret {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	return true
}

func (p *HeadlineSendResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HeadlineSendResult_(%+v)", *p)
}

// Attributes:
//   - Bold
//   - Color
type TextStyle struct {
	Bold  bool   `thrift:"bold,1,required" db:"bold" json:"bold"`
	Color string `thrift:"color,2,required" db:"color" json:"color"`
}

func NewTextStyle() *TextStyle {
	return &TextStyle{}
}

func (p *TextStyle) GetBold() bool {
	return p.Bold
}

func (p *TextStyle) GetColor() string {
	return p.Color
}
func (p *TextStyle) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetBold bool = false
	var issetColor bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetBold = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetColor = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetBold {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Bold is not set"))
	}
	if !issetColor {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Color is not set"))
	}
	return nil
}

func (p *TextStyle) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Bold = v
	}
	return nil
}

func (p *TextStyle) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Color = v
	}
	return nil
}

func (p *TextStyle) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "TextStyle"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *TextStyle) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "bold", thrift.BOOL, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:bold: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.Bold)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.bold (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:bold: ", p), err)
	}
	return err
}

func (p *TextStyle) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "color", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:color: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Color)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.color (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:color: ", p), err)
	}
	return err
}

func (p *TextStyle) Equals(other *TextStyle) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Bold != other.Bold {
		return false
	}
	if p.Color != other.Color {
		return false
	}
	return true
}

func (p *TextStyle) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TextStyle(%+v)", *p)
}

// Attributes:
//   - Biz: 业务
//   - GlobalId: 唯一ID
//   - ImageList: 图标
//   - Template: 模板
//   - Contents: 文案
//   - TextList: 文案样式
//   - BgColor: 背景渐变色
//   - BorderColor: 边框渐变色
//   - Link: 链接
//   - CurrentTime: 当前时间
//   - ShowTime: 展示时间
//   - ExpireTime: 过期时间
//   - LiveMap: 开播类型
//   - Ext: 额外参数
//   - Effect: 特效
//   - RoomId: 房间号 不为0时，可指定房间推送，无挂件，仅用于线上验收
type HeadlineSendRequest struct {
	Biz         int32             `thrift:"biz,1,required" db:"biz" json:"biz"`
	GlobalId    int64             `thrift:"globalId,2,required" db:"globalId" json:"globalId"`
	ImageList   []string          `thrift:"imageList,3" db:"imageList" json:"imageList,omitempty"`
	Template    *string           `thrift:"template,4" db:"template" json:"template,omitempty"`
	Contents    []string          `thrift:"contents,5,required" db:"contents" json:"contents"`
	TextList    []*TextStyle      `thrift:"textList,6" db:"textList" json:"textList,omitempty"`
	BgColor     []string          `thrift:"bgColor,7" db:"bgColor" json:"bgColor,omitempty"`
	BorderColor []string          `thrift:"borderColor,8" db:"borderColor" json:"borderColor,omitempty"`
	Link        string            `thrift:"link,9,required" db:"link" json:"link"`
	CurrentTime int64             `thrift:"currentTime,10,required" db:"currentTime" json:"currentTime"`
	ShowTime    *int32            `thrift:"showTime,11" db:"showTime" json:"showTime,omitempty"`
	ExpireTime  *int32            `thrift:"expireTime,12" db:"expireTime" json:"expireTime,omitempty"`
	LiveMap     map[int32][]int32 `thrift:"liveMap,13" db:"liveMap" json:"liveMap,omitempty"`
	Ext         *string           `thrift:"ext,14" db:"ext" json:"ext,omitempty"`
	Effect      *string           `thrift:"effect,15" db:"effect" json:"effect,omitempty"`
	RoomId      *int32            `thrift:"roomId,16" db:"roomId" json:"roomId,omitempty"`
}

func NewHeadlineSendRequest() *HeadlineSendRequest {
	return &HeadlineSendRequest{}
}

func (p *HeadlineSendRequest) GetBiz() int32 {
	return p.Biz
}

func (p *HeadlineSendRequest) GetGlobalId() int64 {
	return p.GlobalId
}

var HeadlineSendRequest_ImageList_DEFAULT []string

func (p *HeadlineSendRequest) GetImageList() []string {
	return p.ImageList
}

var HeadlineSendRequest_Template_DEFAULT string

func (p *HeadlineSendRequest) GetTemplate() string {
	if !p.IsSetTemplate() {
		return HeadlineSendRequest_Template_DEFAULT
	}
	return *p.Template
}

func (p *HeadlineSendRequest) GetContents() []string {
	return p.Contents
}

var HeadlineSendRequest_TextList_DEFAULT []*TextStyle

func (p *HeadlineSendRequest) GetTextList() []*TextStyle {
	return p.TextList
}

var HeadlineSendRequest_BgColor_DEFAULT []string

func (p *HeadlineSendRequest) GetBgColor() []string {
	return p.BgColor
}

var HeadlineSendRequest_BorderColor_DEFAULT []string

func (p *HeadlineSendRequest) GetBorderColor() []string {
	return p.BorderColor
}

func (p *HeadlineSendRequest) GetLink() string {
	return p.Link
}

func (p *HeadlineSendRequest) GetCurrentTime() int64 {
	return p.CurrentTime
}

var HeadlineSendRequest_ShowTime_DEFAULT int32

func (p *HeadlineSendRequest) GetShowTime() int32 {
	if !p.IsSetShowTime() {
		return HeadlineSendRequest_ShowTime_DEFAULT
	}
	return *p.ShowTime
}

var HeadlineSendRequest_ExpireTime_DEFAULT int32

func (p *HeadlineSendRequest) GetExpireTime() int32 {
	if !p.IsSetExpireTime() {
		return HeadlineSendRequest_ExpireTime_DEFAULT
	}
	return *p.ExpireTime
}

var HeadlineSendRequest_LiveMap_DEFAULT map[int32][]int32

func (p *HeadlineSendRequest) GetLiveMap() map[int32][]int32 {
	return p.LiveMap
}

var HeadlineSendRequest_Ext_DEFAULT string

func (p *HeadlineSendRequest) GetExt() string {
	if !p.IsSetExt() {
		return HeadlineSendRequest_Ext_DEFAULT
	}
	return *p.Ext
}

var HeadlineSendRequest_Effect_DEFAULT string

func (p *HeadlineSendRequest) GetEffect() string {
	if !p.IsSetEffect() {
		return HeadlineSendRequest_Effect_DEFAULT
	}
	return *p.Effect
}

var HeadlineSendRequest_RoomId_DEFAULT int32

func (p *HeadlineSendRequest) GetRoomId() int32 {
	if !p.IsSetRoomId() {
		return HeadlineSendRequest_RoomId_DEFAULT
	}
	return *p.RoomId
}
func (p *HeadlineSendRequest) IsSetImageList() bool {
	return p.ImageList != nil
}

func (p *HeadlineSendRequest) IsSetTemplate() bool {
	return p.Template != nil
}

func (p *HeadlineSendRequest) IsSetTextList() bool {
	return p.TextList != nil
}

func (p *HeadlineSendRequest) IsSetBgColor() bool {
	return p.BgColor != nil
}

func (p *HeadlineSendRequest) IsSetBorderColor() bool {
	return p.BorderColor != nil
}

func (p *HeadlineSendRequest) IsSetShowTime() bool {
	return p.ShowTime != nil
}

func (p *HeadlineSendRequest) IsSetExpireTime() bool {
	return p.ExpireTime != nil
}

func (p *HeadlineSendRequest) IsSetLiveMap() bool {
	return p.LiveMap != nil
}

func (p *HeadlineSendRequest) IsSetExt() bool {
	return p.Ext != nil
}

func (p *HeadlineSendRequest) IsSetEffect() bool {
	return p.Effect != nil
}

func (p *HeadlineSendRequest) IsSetRoomId() bool {
	return p.RoomId != nil
}

func (p *HeadlineSendRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetBiz bool = false
	var issetGlobalId bool = false
	var issetContents bool = false
	var issetLink bool = false
	var issetCurrentTime bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetBiz = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetGlobalId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetContents = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField8(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField9(ctx, iprot); err != nil {
					return err
				}
				issetLink = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField10(ctx, iprot); err != nil {
					return err
				}
				issetCurrentTime = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField11(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField12(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err := p.ReadField13(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField14(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField15(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField16(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetBiz {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Biz is not set"))
	}
	if !issetGlobalId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GlobalId is not set"))
	}
	if !issetContents {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Contents is not set"))
	}
	if !issetLink {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Link is not set"))
	}
	if !issetCurrentTime {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field CurrentTime is not set"))
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Biz = v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.GlobalId = v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.ImageList = tSlice
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem0 = v
		}
		p.ImageList = append(p.ImageList, _elem0)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Template = &v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.Contents = tSlice
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem1 = v
		}
		p.Contents = append(p.Contents, _elem1)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*TextStyle, 0, size)
	p.TextList = tSlice
	for i := 0; i < size; i++ {
		_elem2 := &TextStyle{}
		if err := _elem2.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem2), err)
		}
		p.TextList = append(p.TextList, _elem2)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.BgColor = tSlice
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem3 = v
		}
		p.BgColor = append(p.BgColor, _elem3)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.BorderColor = tSlice
	for i := 0; i < size; i++ {
		var _elem4 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem4 = v
		}
		p.BorderColor = append(p.BorderColor, _elem4)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 9: ", err)
	} else {
		p.Link = v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 10: ", err)
	} else {
		p.CurrentTime = v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 11: ", err)
	} else {
		p.ShowTime = &v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 12: ", err)
	} else {
		p.ExpireTime = &v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading map begin: ", err)
	}
	tMap := make(map[int32][]int32, size)
	p.LiveMap = tMap
	for i := 0; i < size; i++ {
		var _key5 int32
		if v, err := iprot.ReadI32(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_key5 = v
		}
		_, size, err := iprot.ReadListBegin(ctx)
		if err != nil {
			return thrift.PrependError("error reading list begin: ", err)
		}
		tSlice := make([]int32, 0, size)
		_val6 := tSlice
		for i := 0; i < size; i++ {
			var _elem7 int32
			if v, err := iprot.ReadI32(ctx); err != nil {
				return thrift.PrependError("error reading field 0: ", err)
			} else {
				_elem7 = v
			}
			_val6 = append(_val6, _elem7)
		}
		if err := iprot.ReadListEnd(ctx); err != nil {
			return thrift.PrependError("error reading list end: ", err)
		}
		p.LiveMap[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(ctx); err != nil {
		return thrift.PrependError("error reading map end: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 14: ", err)
	} else {
		p.Ext = &v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 15: ", err)
	} else {
		p.Effect = &v
	}
	return nil
}

func (p *HeadlineSendRequest) ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 16: ", err)
	} else {
		p.RoomId = &v
	}
	return nil
}

func (p *HeadlineSendRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "HeadlineSendRequest"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField8(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField9(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField10(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField11(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField12(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField13(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField14(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField15(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField16(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *HeadlineSendRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "biz", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:biz: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Biz)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.biz (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:biz: ", p), err)
	}
	return err
}

func (p *HeadlineSendRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "globalId", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:globalId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.GlobalId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.globalId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:globalId: ", p), err)
	}
	return err
}

func (p *HeadlineSendRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetImageList() {
		if err := oprot.WriteFieldBegin(ctx, "imageList", thrift.LIST, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:imageList: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.ImageList)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.ImageList {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:imageList: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplate() {
		if err := oprot.WriteFieldBegin(ctx, "template", thrift.STRING, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:template: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Template)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.template (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:template: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "contents", thrift.LIST, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:contents: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.Contents)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.Contents {
		if err := oprot.WriteString(ctx, string(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:contents: ", p), err)
	}
	return err
}

func (p *HeadlineSendRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTextList() {
		if err := oprot.WriteFieldBegin(ctx, "textList", thrift.LIST, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:textList: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.TextList)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.TextList {
			if err := v.Write(ctx, oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:textList: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetBgColor() {
		if err := oprot.WriteFieldBegin(ctx, "bgColor", thrift.LIST, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:bgColor: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.BgColor)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.BgColor {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:bgColor: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetBorderColor() {
		if err := oprot.WriteFieldBegin(ctx, "borderColor", thrift.LIST, 8); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:borderColor: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.BorderColor)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.BorderColor {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 8:borderColor: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "link", thrift.STRING, 9); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:link: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Link)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.link (9) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 9:link: ", p), err)
	}
	return err
}

func (p *HeadlineSendRequest) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "currentTime", thrift.I64, 10); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:currentTime: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.CurrentTime)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.currentTime (10) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 10:currentTime: ", p), err)
	}
	return err
}

func (p *HeadlineSendRequest) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetShowTime() {
		if err := oprot.WriteFieldBegin(ctx, "showTime", thrift.I32, 11); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:showTime: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.ShowTime)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.showTime (11) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 11:showTime: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetExpireTime() {
		if err := oprot.WriteFieldBegin(ctx, "expireTime", thrift.I32, 12); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:expireTime: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.ExpireTime)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.expireTime (12) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 12:expireTime: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetLiveMap() {
		if err := oprot.WriteFieldBegin(ctx, "liveMap", thrift.MAP, 13); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:liveMap: ", p), err)
		}
		if err := oprot.WriteMapBegin(ctx, thrift.I32, thrift.LIST, len(p.LiveMap)); err != nil {
			return thrift.PrependError("error writing map begin: ", err)
		}
		for k, v := range p.LiveMap {
			if err := oprot.WriteI32(ctx, int32(k)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
			if err := oprot.WriteListBegin(ctx, thrift.I32, len(v)); err != nil {
				return thrift.PrependError("error writing list begin: ", err)
			}
			for _, v := range v {
				if err := oprot.WriteI32(ctx, int32(v)); err != nil {
					return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
				}
			}
			if err := oprot.WriteListEnd(ctx); err != nil {
				return thrift.PrependError("error writing list end: ", err)
			}
		}
		if err := oprot.WriteMapEnd(ctx); err != nil {
			return thrift.PrependError("error writing map end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 13:liveMap: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetExt() {
		if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 14); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:ext: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.ext (14) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 14:ext: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetEffect() {
		if err := oprot.WriteFieldBegin(ctx, "effect", thrift.STRING, 15); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:effect: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Effect)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.effect (15) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 15:effect: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetRoomId() {
		if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 16); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:roomId: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.RoomId)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.roomId (16) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 16:roomId: ", p), err)
		}
	}
	return err
}

func (p *HeadlineSendRequest) Equals(other *HeadlineSendRequest) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Biz != other.Biz {
		return false
	}
	if p.GlobalId != other.GlobalId {
		return false
	}
	if len(p.ImageList) != len(other.ImageList) {
		return false
	}
	for i, _tgt := range p.ImageList {
		_src8 := other.ImageList[i]
		if _tgt != _src8 {
			return false
		}
	}
	if p.Template != other.Template {
		if p.Template == nil || other.Template == nil {
			return false
		}
		if (*p.Template) != (*other.Template) {
			return false
		}
	}
	if len(p.Contents) != len(other.Contents) {
		return false
	}
	for i, _tgt := range p.Contents {
		_src9 := other.Contents[i]
		if _tgt != _src9 {
			return false
		}
	}
	if len(p.TextList) != len(other.TextList) {
		return false
	}
	for i, _tgt := range p.TextList {
		_src10 := other.TextList[i]
		if !_tgt.Equals(_src10) {
			return false
		}
	}
	if len(p.BgColor) != len(other.BgColor) {
		return false
	}
	for i, _tgt := range p.BgColor {
		_src11 := other.BgColor[i]
		if _tgt != _src11 {
			return false
		}
	}
	if len(p.BorderColor) != len(other.BorderColor) {
		return false
	}
	for i, _tgt := range p.BorderColor {
		_src12 := other.BorderColor[i]
		if _tgt != _src12 {
			return false
		}
	}
	if p.Link != other.Link {
		return false
	}
	if p.CurrentTime != other.CurrentTime {
		return false
	}
	if p.ShowTime != other.ShowTime {
		if p.ShowTime == nil || other.ShowTime == nil {
			return false
		}
		if (*p.ShowTime) != (*other.ShowTime) {
			return false
		}
	}
	if p.ExpireTime != other.ExpireTime {
		if p.ExpireTime == nil || other.ExpireTime == nil {
			return false
		}
		if (*p.ExpireTime) != (*other.ExpireTime) {
			return false
		}
	}
	if len(p.LiveMap) != len(other.LiveMap) {
		return false
	}
	for k, _tgt := range p.LiveMap {
		_src13 := other.LiveMap[k]
		if len(_tgt) != len(_src13) {
			return false
		}
		for i, _tgt := range _tgt {
			_src14 := _src13[i]
			if _tgt != _src14 {
				return false
			}
		}
	}
	if p.Ext != other.Ext {
		if p.Ext == nil || other.Ext == nil {
			return false
		}
		if (*p.Ext) != (*other.Ext) {
			return false
		}
	}
	if p.Effect != other.Effect {
		if p.Effect == nil || other.Effect == nil {
			return false
		}
		if (*p.Effect) != (*other.Effect) {
			return false
		}
	}
	if p.RoomId != other.RoomId {
		if p.RoomId == nil || other.RoomId == nil {
			return false
		}
		if (*p.RoomId) != (*other.RoomId) {
			return false
		}
	}
	return true
}

func (p *HeadlineSendRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HeadlineSendRequest(%+v)", *p)
}

type HeadlineThriftService interface { //外部业务提供的统一规则协议，协议风险由业务方承担，请注意规则的逻辑准确性

	// 统一处理协议,返回统一格式 *
	//
	// Parameters:
	//  - Request
	Send(ctx context.Context, request *HeadlineSendRequest) (_r *HeadlineSendResult_, _err error)
}

// 外部业务提供的统一规则协议，协议风险由业务方承担，请注意规则的逻辑准确性
type HeadlineThriftServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewHeadlineThriftServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *HeadlineThriftServiceClient {
	return &HeadlineThriftServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewHeadlineThriftServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *HeadlineThriftServiceClient {
	return &HeadlineThriftServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewHeadlineThriftServiceClient(c thrift.TClient) *HeadlineThriftServiceClient {
	return &HeadlineThriftServiceClient{
		c: c,
	}
}

func (p *HeadlineThriftServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *HeadlineThriftServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *HeadlineThriftServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// 统一处理协议,返回统一格式 *
//
// Parameters:
//   - Request
func (p *HeadlineThriftServiceClient) Send(ctx context.Context, request *HeadlineSendRequest) (_r *HeadlineSendResult_, _err error) {
	var _args15 HeadlineThriftServiceSendArgs
	_args15.Request = request
	var _result17 HeadlineThriftServiceSendResult
	var _meta16 thrift.ResponseMeta
	_meta16, _err = p.Client_().Call(ctx, "send", &_args15, &_result17)
	p.SetLastResponseMeta_(_meta16)
	if _err != nil {
		return
	}
	return _result17.GetSuccess(), nil
}

type HeadlineThriftServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      HeadlineThriftService
}

func (p *HeadlineThriftServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *HeadlineThriftServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *HeadlineThriftServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewHeadlineThriftServiceProcessor(handler HeadlineThriftService) *HeadlineThriftServiceProcessor {

	self18 := &HeadlineThriftServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self18.processorMap["send"] = &headlineThriftServiceProcessorSend{handler: handler}
	return self18
}

func (p *HeadlineThriftServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x19 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x19.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x19

}

type headlineThriftServiceProcessorSend struct {
	handler HeadlineThriftService
}

func (p *headlineThriftServiceProcessorSend) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err20 error
	args := HeadlineThriftServiceSendArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "send", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := HeadlineThriftServiceSendResult{}
	if retval, err2 := p.handler.Send(ctx, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc21 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing send: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "send", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err20 = thrift.WrapTException(err2)
		}
		if err2 := _exc21.Write(ctx, oprot); _write_err20 == nil && err2 != nil {
			_write_err20 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err20 == nil && err2 != nil {
			_write_err20 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err20 == nil && err2 != nil {
			_write_err20 = thrift.WrapTException(err2)
		}
		if _write_err20 != nil {
			return false, thrift.WrapTException(_write_err20)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "send", thrift.REPLY, seqId); err2 != nil {
		_write_err20 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err20 == nil && err2 != nil {
		_write_err20 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err20 == nil && err2 != nil {
		_write_err20 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err20 == nil && err2 != nil {
		_write_err20 = thrift.WrapTException(err2)
	}
	if _write_err20 != nil {
		return false, thrift.WrapTException(_write_err20)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//   - Request
type HeadlineThriftServiceSendArgs struct {
	Request *HeadlineSendRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewHeadlineThriftServiceSendArgs() *HeadlineThriftServiceSendArgs {
	return &HeadlineThriftServiceSendArgs{}
}

var HeadlineThriftServiceSendArgs_Request_DEFAULT *HeadlineSendRequest

func (p *HeadlineThriftServiceSendArgs) GetRequest() *HeadlineSendRequest {
	if !p.IsSetRequest() {
		return HeadlineThriftServiceSendArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *HeadlineThriftServiceSendArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *HeadlineThriftServiceSendArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRequest bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRequest = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRequest {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"))
	}
	return nil
}

func (p *HeadlineThriftServiceSendArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &HeadlineSendRequest{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *HeadlineThriftServiceSendArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "send_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *HeadlineThriftServiceSendArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err)
	}
	return err
}

func (p *HeadlineThriftServiceSendArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HeadlineThriftServiceSendArgs(%+v)", *p)
}

// Attributes:
//   - Success
type HeadlineThriftServiceSendResult struct {
	Success *HeadlineSendResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewHeadlineThriftServiceSendResult() *HeadlineThriftServiceSendResult {
	return &HeadlineThriftServiceSendResult{}
}

var HeadlineThriftServiceSendResult_Success_DEFAULT *HeadlineSendResult_

func (p *HeadlineThriftServiceSendResult) GetSuccess() *HeadlineSendResult_ {
	if !p.IsSetSuccess() {
		return HeadlineThriftServiceSendResult_Success_DEFAULT
	}
	return p.Success
}
func (p *HeadlineThriftServiceSendResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *HeadlineThriftServiceSendResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *HeadlineThriftServiceSendResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &HeadlineSendResult_{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *HeadlineThriftServiceSendResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "send_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *HeadlineThriftServiceSendResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *HeadlineThriftServiceSendResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("HeadlineThriftServiceSendResult(%+v)", *p)
}
