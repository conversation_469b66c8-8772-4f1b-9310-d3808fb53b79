// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package userv2vo

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - Ret
//  - Msg
//  - Detail: 等级配置*
//  - Feature: 等级特性 *
type RichLevelDetailResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Detail *RichLevelVO `thrift:"detail,3,required" db:"detail" json:"detail"`
  Feature *RichLevelFeature `thrift:"feature,4,required" db:"feature" json:"feature"`
}

func NewRichLevelDetailResponse() *RichLevelDetailResponse {
  return &RichLevelDetailResponse{}
}


func (p *RichLevelDetailResponse) GetRet() int32 {
  return p.Ret
}

func (p *RichLevelDetailResponse) GetMsg() string {
  return p.Msg
}
var RichLevelDetailResponse_Detail_DEFAULT *RichLevelVO
func (p *RichLevelDetailResponse) GetDetail() *RichLevelVO {
  if !p.IsSetDetail() {
    return RichLevelDetailResponse_Detail_DEFAULT
  }
return p.Detail
}
var RichLevelDetailResponse_Feature_DEFAULT *RichLevelFeature
func (p *RichLevelDetailResponse) GetFeature() *RichLevelFeature {
  if !p.IsSetFeature() {
    return RichLevelDetailResponse_Feature_DEFAULT
  }
return p.Feature
}
func (p *RichLevelDetailResponse) IsSetDetail() bool {
  return p.Detail != nil
}

func (p *RichLevelDetailResponse) IsSetFeature() bool {
  return p.Feature != nil
}

func (p *RichLevelDetailResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;
  var issetDetail bool = false;
  var issetFeature bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetDetail = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetFeature = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetDetail{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Detail is not set"));
  }
  if !issetFeature{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Feature is not set"));
  }
  return nil
}

func (p *RichLevelDetailResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *RichLevelDetailResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *RichLevelDetailResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Detail = &RichLevelVO{}
  if err := p.Detail.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Detail), err)
  }
  return nil
}

func (p *RichLevelDetailResponse)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  p.Feature = &RichLevelFeature{}
  if err := p.Feature.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Feature), err)
  }
  return nil
}

func (p *RichLevelDetailResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RichLevelDetailResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *RichLevelDetailResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *RichLevelDetailResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *RichLevelDetailResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "detail", thrift.STRUCT, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:detail: ", p), err) }
  if err := p.Detail.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Detail), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:detail: ", p), err) }
  return err
}

func (p *RichLevelDetailResponse) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "feature", thrift.STRUCT, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:feature: ", p), err) }
  if err := p.Feature.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Feature), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:feature: ", p), err) }
  return err
}

func (p *RichLevelDetailResponse) Equals(other *RichLevelDetailResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Detail.Equals(other.Detail) { return false }
  if !p.Feature.Equals(other.Feature) { return false }
  return true
}

func (p *RichLevelDetailResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("RichLevelDetailResponse(%+v)", *p)
}

// Attributes:
//  - IsGod: 是否神用户 *
//  - IsCustomGod: 是否自定义神用户 *
type RichLevelFeature struct {
  IsGod bool `thrift:"isGod,1,required" db:"isGod" json:"isGod"`
  IsCustomGod bool `thrift:"isCustomGod,2,required" db:"isCustomGod" json:"isCustomGod"`
}

func NewRichLevelFeature() *RichLevelFeature {
  return &RichLevelFeature{}
}


func (p *RichLevelFeature) GetIsGod() bool {
  return p.IsGod
}

func (p *RichLevelFeature) GetIsCustomGod() bool {
  return p.IsCustomGod
}
func (p *RichLevelFeature) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetIsGod bool = false;
  var issetIsCustomGod bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetIsGod = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetIsCustomGod = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetIsGod{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IsGod is not set"));
  }
  if !issetIsCustomGod{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IsCustomGod is not set"));
  }
  return nil
}

func (p *RichLevelFeature)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.IsGod = v
}
  return nil
}

func (p *RichLevelFeature)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.IsCustomGod = v
}
  return nil
}

func (p *RichLevelFeature) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RichLevelFeature"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *RichLevelFeature) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "isGod", thrift.BOOL, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:isGod: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsGod)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.isGod (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:isGod: ", p), err) }
  return err
}

func (p *RichLevelFeature) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "isCustomGod", thrift.BOOL, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:isCustomGod: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsCustomGod)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.isCustomGod (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:isCustomGod: ", p), err) }
  return err
}

func (p *RichLevelFeature) Equals(other *RichLevelFeature) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.IsGod != other.IsGod { return false }
  if p.IsCustomGod != other.IsCustomGod { return false }
  return true
}

func (p *RichLevelFeature) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("RichLevelFeature(%+v)", *p)
}

// 财富等级图标
//  
// 
// Attributes:
//  - Richlevel
//  - Name: 等级名称*
//  - Value: 达到此值即为当前等级*
//  - RichIcon: 财富等级图标，非全路径 例：/fxstatic/images/richLevel/pc/1.gif*
//  - RichIconPng: 财富等级图标-web旧业务使用-慎用*
//  - Subscribe: 订阅开播短信通知可订X位艺人*
//  - MountId: 不知到干吗用，罗栋没找到*
//  - Right
//  - MonthOrTotalConsume: 该等级是按月消费星币计算或者总星币消费计算，1：按总 2：按月*
//  - MonthValue: 有个定时器统计用的  之前的设计是神等级以上的升级是根据月消费来的*
type RichLevelVO struct {
  Richlevel int32 `thrift:"richlevel,1,required" db:"richlevel" json:"richlevel"`
  Name string `thrift:"name,2,required" db:"name" json:"name"`
  Value int64 `thrift:"value,3,required" db:"value" json:"value"`
  RichIcon string `thrift:"richIcon,4,required" db:"richIcon" json:"richIcon"`
  RichIconPng *string `thrift:"richIconPng,5" db:"richIconPng" json:"richIconPng,omitempty"`
  Subscribe int32 `thrift:"subscribe,6,required" db:"subscribe" json:"subscribe"`
  MountId int32 `thrift:"mountId,7,required" db:"mountId" json:"mountId"`
  Right *string `thrift:"right,8" db:"right" json:"right,omitempty"`
  MonthOrTotalConsume int32 `thrift:"monthOrTotalConsume,9,required" db:"monthOrTotalConsume" json:"monthOrTotalConsume"`
  MonthValue int64 `thrift:"monthValue,10,required" db:"monthValue" json:"monthValue"`
}

func NewRichLevelVO() *RichLevelVO {
  return &RichLevelVO{}
}


func (p *RichLevelVO) GetRichlevel() int32 {
  return p.Richlevel
}

func (p *RichLevelVO) GetName() string {
  return p.Name
}

func (p *RichLevelVO) GetValue() int64 {
  return p.Value
}

func (p *RichLevelVO) GetRichIcon() string {
  return p.RichIcon
}
var RichLevelVO_RichIconPng_DEFAULT string
func (p *RichLevelVO) GetRichIconPng() string {
  if !p.IsSetRichIconPng() {
    return RichLevelVO_RichIconPng_DEFAULT
  }
return *p.RichIconPng
}

func (p *RichLevelVO) GetSubscribe() int32 {
  return p.Subscribe
}

func (p *RichLevelVO) GetMountId() int32 {
  return p.MountId
}
var RichLevelVO_Right_DEFAULT string
func (p *RichLevelVO) GetRight() string {
  if !p.IsSetRight() {
    return RichLevelVO_Right_DEFAULT
  }
return *p.Right
}

func (p *RichLevelVO) GetMonthOrTotalConsume() int32 {
  return p.MonthOrTotalConsume
}

func (p *RichLevelVO) GetMonthValue() int64 {
  return p.MonthValue
}
func (p *RichLevelVO) IsSetRichIconPng() bool {
  return p.RichIconPng != nil
}

func (p *RichLevelVO) IsSetRight() bool {
  return p.Right != nil
}

func (p *RichLevelVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRichlevel bool = false;
  var issetName bool = false;
  var issetValue bool = false;
  var issetRichIcon bool = false;
  var issetSubscribe bool = false;
  var issetMountId bool = false;
  var issetMonthOrTotalConsume bool = false;
  var issetMonthValue bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRichlevel = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetName = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetValue = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetRichIcon = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetSubscribe = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
        issetMountId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
        issetMonthOrTotalConsume = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
        issetMonthValue = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRichlevel{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Richlevel is not set"));
  }
  if !issetName{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Name is not set"));
  }
  if !issetValue{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Value is not set"));
  }
  if !issetRichIcon{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RichIcon is not set"));
  }
  if !issetSubscribe{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Subscribe is not set"));
  }
  if !issetMountId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MountId is not set"));
  }
  if !issetMonthOrTotalConsume{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MonthOrTotalConsume is not set"));
  }
  if !issetMonthValue{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MonthValue is not set"));
  }
  return nil
}

func (p *RichLevelVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Richlevel = v
}
  return nil
}

func (p *RichLevelVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Name = v
}
  return nil
}

func (p *RichLevelVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Value = v
}
  return nil
}

func (p *RichLevelVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.RichIcon = v
}
  return nil
}

func (p *RichLevelVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.RichIconPng = &v
}
  return nil
}

func (p *RichLevelVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Subscribe = v
}
  return nil
}

func (p *RichLevelVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.MountId = v
}
  return nil
}

func (p *RichLevelVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Right = &v
}
  return nil
}

func (p *RichLevelVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.MonthOrTotalConsume = v
}
  return nil
}

func (p *RichLevelVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.MonthValue = v
}
  return nil
}

func (p *RichLevelVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RichLevelVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *RichLevelVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "richlevel", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:richlevel: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Richlevel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.richlevel (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:richlevel: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "name", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:name: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Name)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.name (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:name: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "value", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:value: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Value)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.value (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:value: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "richIcon", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:richIcon: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.RichIcon)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.richIcon (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:richIcon: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichIconPng() {
    if err := oprot.WriteFieldBegin(ctx, "richIconPng", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:richIconPng: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.RichIconPng)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richIconPng (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:richIconPng: ", p), err) }
  }
  return err
}

func (p *RichLevelVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "subscribe", thrift.I32, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:subscribe: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Subscribe)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.subscribe (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:subscribe: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "mountId", thrift.I32, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:mountId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.MountId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.mountId (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:mountId: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRight() {
    if err := oprot.WriteFieldBegin(ctx, "right", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:right: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Right)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.right (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:right: ", p), err) }
  }
  return err
}

func (p *RichLevelVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "monthOrTotalConsume", thrift.I32, 9); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:monthOrTotalConsume: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.MonthOrTotalConsume)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.monthOrTotalConsume (9) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 9:monthOrTotalConsume: ", p), err) }
  return err
}

func (p *RichLevelVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "monthValue", thrift.I64, 10); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:monthValue: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.MonthValue)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.monthValue (10) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 10:monthValue: ", p), err) }
  return err
}

func (p *RichLevelVO) Equals(other *RichLevelVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Richlevel != other.Richlevel { return false }
  if p.Name != other.Name { return false }
  if p.Value != other.Value { return false }
  if p.RichIcon != other.RichIcon { return false }
  if p.RichIconPng != other.RichIconPng {
    if p.RichIconPng == nil || other.RichIconPng == nil {
      return false
    }
    if (*p.RichIconPng) != (*other.RichIconPng) { return false }
  }
  if p.Subscribe != other.Subscribe { return false }
  if p.MountId != other.MountId { return false }
  if p.Right != other.Right {
    if p.Right == nil || other.Right == nil {
      return false
    }
    if (*p.Right) != (*other.Right) { return false }
  }
  if p.MonthOrTotalConsume != other.MonthOrTotalConsume { return false }
  if p.MonthValue != other.MonthValue { return false }
  return true
}

func (p *RichLevelVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("RichLevelVO(%+v)", *p)
}

// 明星等级图标
//  
// 
// Attributes:
//  - Starlevel
//  - Name: 等级名称*
//  - Value: 达到此值即为当前等级*
//  - StarIcon: 明星等级图标，非全路径 例:/fxstatic/images/starLevel/pc/1.png*
//  - StarIconPng: 明星等级图标-web旧业务使用-慎用*
type StarLevelVO struct {
  Starlevel int32 `thrift:"starlevel,1,required" db:"starlevel" json:"starlevel"`
  Name string `thrift:"name,2,required" db:"name" json:"name"`
  Value int64 `thrift:"value,3,required" db:"value" json:"value"`
  StarIcon string `thrift:"starIcon,4,required" db:"starIcon" json:"starIcon"`
  StarIconPng *string `thrift:"starIconPng,5" db:"starIconPng" json:"starIconPng,omitempty"`
}

func NewStarLevelVO() *StarLevelVO {
  return &StarLevelVO{}
}


func (p *StarLevelVO) GetStarlevel() int32 {
  return p.Starlevel
}

func (p *StarLevelVO) GetName() string {
  return p.Name
}

func (p *StarLevelVO) GetValue() int64 {
  return p.Value
}

func (p *StarLevelVO) GetStarIcon() string {
  return p.StarIcon
}
var StarLevelVO_StarIconPng_DEFAULT string
func (p *StarLevelVO) GetStarIconPng() string {
  if !p.IsSetStarIconPng() {
    return StarLevelVO_StarIconPng_DEFAULT
  }
return *p.StarIconPng
}
func (p *StarLevelVO) IsSetStarIconPng() bool {
  return p.StarIconPng != nil
}

func (p *StarLevelVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetStarlevel bool = false;
  var issetName bool = false;
  var issetValue bool = false;
  var issetStarIcon bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetStarlevel = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetName = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetValue = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetStarIcon = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetStarlevel{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Starlevel is not set"));
  }
  if !issetName{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Name is not set"));
  }
  if !issetValue{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Value is not set"));
  }
  if !issetStarIcon{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarIcon is not set"));
  }
  return nil
}

func (p *StarLevelVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Starlevel = v
}
  return nil
}

func (p *StarLevelVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Name = v
}
  return nil
}

func (p *StarLevelVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Value = v
}
  return nil
}

func (p *StarLevelVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StarIcon = v
}
  return nil
}

func (p *StarLevelVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.StarIconPng = &v
}
  return nil
}

func (p *StarLevelVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarLevelVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarLevelVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starlevel", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:starlevel: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Starlevel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starlevel (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:starlevel: ", p), err) }
  return err
}

func (p *StarLevelVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "name", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:name: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Name)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.name (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:name: ", p), err) }
  return err
}

func (p *StarLevelVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "value", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:value: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Value)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.value (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:value: ", p), err) }
  return err
}

func (p *StarLevelVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starIcon", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starIcon: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.StarIcon)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starIcon (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starIcon: ", p), err) }
  return err
}

func (p *StarLevelVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarIconPng() {
    if err := oprot.WriteFieldBegin(ctx, "starIconPng", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:starIconPng: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.StarIconPng)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starIconPng (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:starIconPng: ", p), err) }
  }
  return err
}

func (p *StarLevelVO) Equals(other *StarLevelVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Starlevel != other.Starlevel { return false }
  if p.Name != other.Name { return false }
  if p.Value != other.Value { return false }
  if p.StarIcon != other.StarIcon { return false }
  if p.StarIconPng != other.StarIconPng {
    if p.StarIconPng == nil || other.StarIconPng == nil {
      return false
    }
    if (*p.StarIconPng) != (*other.StarIconPng) { return false }
  }
  return true
}

func (p *StarLevelVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarLevelVO(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - RichLevelData: key=财富等级*
//  - StarLevelData: key=明星等级*
type LevelMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  RichLevelData map[int32]*RichLevelVO `thrift:"richLevelData,3,required" db:"richLevelData" json:"richLevelData"`
  StarLevelData map[int32]*StarLevelVO `thrift:"starLevelData,4,required" db:"starLevelData" json:"starLevelData"`
}

func NewLevelMapResponse() *LevelMapResponse {
  return &LevelMapResponse{}
}


func (p *LevelMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *LevelMapResponse) GetMsg() string {
  return p.Msg
}

func (p *LevelMapResponse) GetRichLevelData() map[int32]*RichLevelVO {
  return p.RichLevelData
}

func (p *LevelMapResponse) GetStarLevelData() map[int32]*StarLevelVO {
  return p.StarLevelData
}
func (p *LevelMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;
  var issetRichLevelData bool = false;
  var issetStarLevelData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetRichLevelData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetStarLevelData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetRichLevelData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RichLevelData is not set"));
  }
  if !issetStarLevelData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarLevelData is not set"));
  }
  return nil
}

func (p *LevelMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *LevelMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *LevelMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int32]*RichLevelVO, size)
  p.RichLevelData =  tMap
  for i := 0; i < size; i ++ {
var _key0 int32
    if v, err := iprot.ReadI32(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key0 = v
}
    _val1 := &RichLevelVO{}
    if err := _val1.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val1), err)
    }
    p.RichLevelData[_key0] = _val1
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *LevelMapResponse)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int32]*StarLevelVO, size)
  p.StarLevelData =  tMap
  for i := 0; i < size; i ++ {
var _key2 int32
    if v, err := iprot.ReadI32(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key2 = v
}
    _val3 := &StarLevelVO{}
    if err := _val3.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val3), err)
    }
    p.StarLevelData[_key2] = _val3
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *LevelMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "LevelMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *LevelMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *LevelMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *LevelMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "richLevelData", thrift.MAP, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:richLevelData: ", p), err) }
  if err := oprot.WriteMapBegin(ctx, thrift.I32, thrift.STRUCT, len(p.RichLevelData)); err != nil {
    return thrift.PrependError("error writing map begin: ", err)
  }
  for k, v := range p.RichLevelData {
    if err := oprot.WriteI32(ctx, int32(k)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteMapEnd(ctx); err != nil {
    return thrift.PrependError("error writing map end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:richLevelData: ", p), err) }
  return err
}

func (p *LevelMapResponse) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starLevelData", thrift.MAP, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starLevelData: ", p), err) }
  if err := oprot.WriteMapBegin(ctx, thrift.I32, thrift.STRUCT, len(p.StarLevelData)); err != nil {
    return thrift.PrependError("error writing map begin: ", err)
  }
  for k, v := range p.StarLevelData {
    if err := oprot.WriteI32(ctx, int32(k)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteMapEnd(ctx); err != nil {
    return thrift.PrependError("error writing map end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starLevelData: ", p), err) }
  return err
}

func (p *LevelMapResponse) Equals(other *LevelMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.RichLevelData) != len(other.RichLevelData) { return false }
  for k, _tgt := range p.RichLevelData {
    _src4 := other.RichLevelData[k]
    if !_tgt.Equals(_src4) { return false }
  }
  if len(p.StarLevelData) != len(other.StarLevelData) { return false }
  for k, _tgt := range p.StarLevelData {
    _src5 := other.StarLevelData[k]
    if !_tgt.Equals(_src5) { return false }
  }
  return true
}

func (p *LevelMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("LevelMapResponse(%+v)", *p)
}

// t_user单表信息VO
// 
// 
// Attributes:
//  - UserName
//  - Email
//  - KugouId
//  - NickName
//  - UserLogo
//  - AuditStatus
//  - CoinSpend
//  - CoinTotal
//  - StarLevel
//  - RichLevel
//  - RoomId
//  - Status
//  - StatusTime
//  - Sex
//  - UserId
type UserVO struct {
  UserName *string `thrift:"userName,1" db:"userName" json:"userName,omitempty"`
  Email *string `thrift:"email,2" db:"email" json:"email,omitempty"`
  KugouId *int64 `thrift:"kugouId,3" db:"kugouId" json:"kugouId,omitempty"`
  NickName *string `thrift:"nickName,4" db:"nickName" json:"nickName,omitempty"`
  UserLogo *string `thrift:"userLogo,5" db:"userLogo" json:"userLogo,omitempty"`
  AuditStatus *int32 `thrift:"auditStatus,6" db:"auditStatus" json:"auditStatus,omitempty"`
  CoinSpend *float64 `thrift:"coinSpend,7" db:"coinSpend" json:"coinSpend,omitempty"`
  CoinTotal *float64 `thrift:"coinTotal,8" db:"coinTotal" json:"coinTotal,omitempty"`
  StarLevel *int32 `thrift:"starLevel,9" db:"starLevel" json:"starLevel,omitempty"`
  RichLevel *int32 `thrift:"richLevel,10" db:"richLevel" json:"richLevel,omitempty"`
  RoomId *int32 `thrift:"roomId,11" db:"roomId" json:"roomId,omitempty"`
  Status *int32 `thrift:"status,12" db:"status" json:"status,omitempty"`
  StatusTime *int32 `thrift:"statusTime,13" db:"statusTime" json:"statusTime,omitempty"`
  Sex *int32 `thrift:"sex,14" db:"sex" json:"sex,omitempty"`
  UserId *int64 `thrift:"userId,15" db:"userId" json:"userId,omitempty"`
}

func NewUserVO() *UserVO {
  return &UserVO{}
}

var UserVO_UserName_DEFAULT string
func (p *UserVO) GetUserName() string {
  if !p.IsSetUserName() {
    return UserVO_UserName_DEFAULT
  }
return *p.UserName
}
var UserVO_Email_DEFAULT string
func (p *UserVO) GetEmail() string {
  if !p.IsSetEmail() {
    return UserVO_Email_DEFAULT
  }
return *p.Email
}
var UserVO_KugouId_DEFAULT int64
func (p *UserVO) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserVO_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserVO_NickName_DEFAULT string
func (p *UserVO) GetNickName() string {
  if !p.IsSetNickName() {
    return UserVO_NickName_DEFAULT
  }
return *p.NickName
}
var UserVO_UserLogo_DEFAULT string
func (p *UserVO) GetUserLogo() string {
  if !p.IsSetUserLogo() {
    return UserVO_UserLogo_DEFAULT
  }
return *p.UserLogo
}
var UserVO_AuditStatus_DEFAULT int32
func (p *UserVO) GetAuditStatus() int32 {
  if !p.IsSetAuditStatus() {
    return UserVO_AuditStatus_DEFAULT
  }
return *p.AuditStatus
}
var UserVO_CoinSpend_DEFAULT float64
func (p *UserVO) GetCoinSpend() float64 {
  if !p.IsSetCoinSpend() {
    return UserVO_CoinSpend_DEFAULT
  }
return *p.CoinSpend
}
var UserVO_CoinTotal_DEFAULT float64
func (p *UserVO) GetCoinTotal() float64 {
  if !p.IsSetCoinTotal() {
    return UserVO_CoinTotal_DEFAULT
  }
return *p.CoinTotal
}
var UserVO_StarLevel_DEFAULT int32
func (p *UserVO) GetStarLevel() int32 {
  if !p.IsSetStarLevel() {
    return UserVO_StarLevel_DEFAULT
  }
return *p.StarLevel
}
var UserVO_RichLevel_DEFAULT int32
func (p *UserVO) GetRichLevel() int32 {
  if !p.IsSetRichLevel() {
    return UserVO_RichLevel_DEFAULT
  }
return *p.RichLevel
}
var UserVO_RoomId_DEFAULT int32
func (p *UserVO) GetRoomId() int32 {
  if !p.IsSetRoomId() {
    return UserVO_RoomId_DEFAULT
  }
return *p.RoomId
}
var UserVO_Status_DEFAULT int32
func (p *UserVO) GetStatus() int32 {
  if !p.IsSetStatus() {
    return UserVO_Status_DEFAULT
  }
return *p.Status
}
var UserVO_StatusTime_DEFAULT int32
func (p *UserVO) GetStatusTime() int32 {
  if !p.IsSetStatusTime() {
    return UserVO_StatusTime_DEFAULT
  }
return *p.StatusTime
}
var UserVO_Sex_DEFAULT int32
func (p *UserVO) GetSex() int32 {
  if !p.IsSetSex() {
    return UserVO_Sex_DEFAULT
  }
return *p.Sex
}
var UserVO_UserId_DEFAULT int64
func (p *UserVO) GetUserId() int64 {
  if !p.IsSetUserId() {
    return UserVO_UserId_DEFAULT
  }
return *p.UserId
}
func (p *UserVO) IsSetUserName() bool {
  return p.UserName != nil
}

func (p *UserVO) IsSetEmail() bool {
  return p.Email != nil
}

func (p *UserVO) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserVO) IsSetNickName() bool {
  return p.NickName != nil
}

func (p *UserVO) IsSetUserLogo() bool {
  return p.UserLogo != nil
}

func (p *UserVO) IsSetAuditStatus() bool {
  return p.AuditStatus != nil
}

func (p *UserVO) IsSetCoinSpend() bool {
  return p.CoinSpend != nil
}

func (p *UserVO) IsSetCoinTotal() bool {
  return p.CoinTotal != nil
}

func (p *UserVO) IsSetStarLevel() bool {
  return p.StarLevel != nil
}

func (p *UserVO) IsSetRichLevel() bool {
  return p.RichLevel != nil
}

func (p *UserVO) IsSetRoomId() bool {
  return p.RoomId != nil
}

func (p *UserVO) IsSetStatus() bool {
  return p.Status != nil
}

func (p *UserVO) IsSetStatusTime() bool {
  return p.StatusTime != nil
}

func (p *UserVO) IsSetSex() bool {
  return p.Sex != nil
}

func (p *UserVO) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *UserVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 15:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField15(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.UserName = &v
}
  return nil
}

func (p *UserVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Email = &v
}
  return nil
}

func (p *UserVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.NickName = &v
}
  return nil
}

func (p *UserVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.UserLogo = &v
}
  return nil
}

func (p *UserVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.AuditStatus = &v
}
  return nil
}

func (p *UserVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.CoinSpend = &v
}
  return nil
}

func (p *UserVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.CoinTotal = &v
}
  return nil
}

func (p *UserVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.StarLevel = &v
}
  return nil
}

func (p *UserVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.RichLevel = &v
}
  return nil
}

func (p *UserVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.RoomId = &v
}
  return nil
}

func (p *UserVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.Status = &v
}
  return nil
}

func (p *UserVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.StatusTime = &v
}
  return nil
}

func (p *UserVO)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *UserVO)  ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 15: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *UserVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
    if err := p.writeField15(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserName() {
    if err := oprot.WriteFieldBegin(ctx, "userName", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userName (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userName: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetEmail() {
    if err := oprot.WriteFieldBegin(ctx, "email", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:email: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Email)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.email (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:email: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:kugouId: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNickName() {
    if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:nickName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NickName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.nickName (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:nickName: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserLogo() {
    if err := oprot.WriteFieldBegin(ctx, "userLogo", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:userLogo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserLogo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userLogo (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:userLogo: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAuditStatus() {
    if err := oprot.WriteFieldBegin(ctx, "auditStatus", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:auditStatus: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AuditStatus)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.auditStatus (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:auditStatus: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinSpend() {
    if err := oprot.WriteFieldBegin(ctx, "coinSpend", thrift.DOUBLE, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:coinSpend: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinSpend)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinSpend (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:coinSpend: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinTotal() {
    if err := oprot.WriteFieldBegin(ctx, "coinTotal", thrift.DOUBLE, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:coinTotal: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinTotal)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinTotal (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:coinTotal: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarLevel() {
    if err := oprot.WriteFieldBegin(ctx, "starLevel", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:starLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StarLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starLevel (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:starLevel: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichLevel() {
    if err := oprot.WriteFieldBegin(ctx, "richLevel", thrift.I32, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:richLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RichLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richLevel (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:richLevel: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRoomId() {
    if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:roomId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RoomId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.roomId (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:roomId: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStatus() {
    if err := oprot.WriteFieldBegin(ctx, "status", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:status: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Status)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.status (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:status: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStatusTime() {
    if err := oprot.WriteFieldBegin(ctx, "statusTime", thrift.I32, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:statusTime: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StatusTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.statusTime (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:statusTime: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:sex: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 15); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (15) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 15:userId: ", p), err) }
  }
  return err
}

func (p *UserVO) Equals(other *UserVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.UserName != other.UserName {
    if p.UserName == nil || other.UserName == nil {
      return false
    }
    if (*p.UserName) != (*other.UserName) { return false }
  }
  if p.Email != other.Email {
    if p.Email == nil || other.Email == nil {
      return false
    }
    if (*p.Email) != (*other.Email) { return false }
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.NickName != other.NickName {
    if p.NickName == nil || other.NickName == nil {
      return false
    }
    if (*p.NickName) != (*other.NickName) { return false }
  }
  if p.UserLogo != other.UserLogo {
    if p.UserLogo == nil || other.UserLogo == nil {
      return false
    }
    if (*p.UserLogo) != (*other.UserLogo) { return false }
  }
  if p.AuditStatus != other.AuditStatus {
    if p.AuditStatus == nil || other.AuditStatus == nil {
      return false
    }
    if (*p.AuditStatus) != (*other.AuditStatus) { return false }
  }
  if p.CoinSpend != other.CoinSpend {
    if p.CoinSpend == nil || other.CoinSpend == nil {
      return false
    }
    if (*p.CoinSpend) != (*other.CoinSpend) { return false }
  }
  if p.CoinTotal != other.CoinTotal {
    if p.CoinTotal == nil || other.CoinTotal == nil {
      return false
    }
    if (*p.CoinTotal) != (*other.CoinTotal) { return false }
  }
  if p.StarLevel != other.StarLevel {
    if p.StarLevel == nil || other.StarLevel == nil {
      return false
    }
    if (*p.StarLevel) != (*other.StarLevel) { return false }
  }
  if p.RichLevel != other.RichLevel {
    if p.RichLevel == nil || other.RichLevel == nil {
      return false
    }
    if (*p.RichLevel) != (*other.RichLevel) { return false }
  }
  if p.RoomId != other.RoomId {
    if p.RoomId == nil || other.RoomId == nil {
      return false
    }
    if (*p.RoomId) != (*other.RoomId) { return false }
  }
  if p.Status != other.Status {
    if p.Status == nil || other.Status == nil {
      return false
    }
    if (*p.Status) != (*other.Status) { return false }
  }
  if p.StatusTime != other.StatusTime {
    if p.StatusTime == nil || other.StatusTime == nil {
      return false
    }
    if (*p.StatusTime) != (*other.StatusTime) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *UserVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserVO(%+v)", *p)
}

// 用户核心信息VO
// 
// 
// Attributes:
//  - KugouId
//  - NickName
//  - UserLogo
//  - StarLevel
//  - RichLevel
//  - Sex
//  - CoinSpend
//  - CoinTotal
//  - UserId
type UserCoreInfoVO struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  NickName *string `thrift:"nickName,2" db:"nickName" json:"nickName,omitempty"`
  UserLogo *string `thrift:"userLogo,3" db:"userLogo" json:"userLogo,omitempty"`
  StarLevel *int32 `thrift:"starLevel,4" db:"starLevel" json:"starLevel,omitempty"`
  RichLevel *int32 `thrift:"richLevel,5" db:"richLevel" json:"richLevel,omitempty"`
  Sex *int32 `thrift:"sex,6" db:"sex" json:"sex,omitempty"`
  CoinSpend *float64 `thrift:"coinSpend,7" db:"coinSpend" json:"coinSpend,omitempty"`
  CoinTotal *float64 `thrift:"coinTotal,8" db:"coinTotal" json:"coinTotal,omitempty"`
  UserId *int64 `thrift:"userId,9" db:"userId" json:"userId,omitempty"`
}

func NewUserCoreInfoVO() *UserCoreInfoVO {
  return &UserCoreInfoVO{}
}

var UserCoreInfoVO_KugouId_DEFAULT int64
func (p *UserCoreInfoVO) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserCoreInfoVO_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserCoreInfoVO_NickName_DEFAULT string
func (p *UserCoreInfoVO) GetNickName() string {
  if !p.IsSetNickName() {
    return UserCoreInfoVO_NickName_DEFAULT
  }
return *p.NickName
}
var UserCoreInfoVO_UserLogo_DEFAULT string
func (p *UserCoreInfoVO) GetUserLogo() string {
  if !p.IsSetUserLogo() {
    return UserCoreInfoVO_UserLogo_DEFAULT
  }
return *p.UserLogo
}
var UserCoreInfoVO_StarLevel_DEFAULT int32
func (p *UserCoreInfoVO) GetStarLevel() int32 {
  if !p.IsSetStarLevel() {
    return UserCoreInfoVO_StarLevel_DEFAULT
  }
return *p.StarLevel
}
var UserCoreInfoVO_RichLevel_DEFAULT int32
func (p *UserCoreInfoVO) GetRichLevel() int32 {
  if !p.IsSetRichLevel() {
    return UserCoreInfoVO_RichLevel_DEFAULT
  }
return *p.RichLevel
}
var UserCoreInfoVO_Sex_DEFAULT int32
func (p *UserCoreInfoVO) GetSex() int32 {
  if !p.IsSetSex() {
    return UserCoreInfoVO_Sex_DEFAULT
  }
return *p.Sex
}
var UserCoreInfoVO_CoinSpend_DEFAULT float64
func (p *UserCoreInfoVO) GetCoinSpend() float64 {
  if !p.IsSetCoinSpend() {
    return UserCoreInfoVO_CoinSpend_DEFAULT
  }
return *p.CoinSpend
}
var UserCoreInfoVO_CoinTotal_DEFAULT float64
func (p *UserCoreInfoVO) GetCoinTotal() float64 {
  if !p.IsSetCoinTotal() {
    return UserCoreInfoVO_CoinTotal_DEFAULT
  }
return *p.CoinTotal
}
var UserCoreInfoVO_UserId_DEFAULT int64
func (p *UserCoreInfoVO) GetUserId() int64 {
  if !p.IsSetUserId() {
    return UserCoreInfoVO_UserId_DEFAULT
  }
return *p.UserId
}
func (p *UserCoreInfoVO) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserCoreInfoVO) IsSetNickName() bool {
  return p.NickName != nil
}

func (p *UserCoreInfoVO) IsSetUserLogo() bool {
  return p.UserLogo != nil
}

func (p *UserCoreInfoVO) IsSetStarLevel() bool {
  return p.StarLevel != nil
}

func (p *UserCoreInfoVO) IsSetRichLevel() bool {
  return p.RichLevel != nil
}

func (p *UserCoreInfoVO) IsSetSex() bool {
  return p.Sex != nil
}

func (p *UserCoreInfoVO) IsSetCoinSpend() bool {
  return p.CoinSpend != nil
}

func (p *UserCoreInfoVO) IsSetCoinTotal() bool {
  return p.CoinTotal != nil
}

func (p *UserCoreInfoVO) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *UserCoreInfoVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserCoreInfoVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.NickName = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.UserLogo = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StarLevel = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.RichLevel = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.CoinSpend = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.CoinTotal = &v
}
  return nil
}

func (p *UserCoreInfoVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *UserCoreInfoVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCoreInfoVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCoreInfoVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNickName() {
    if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:nickName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NickName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.nickName (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:nickName: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserLogo() {
    if err := oprot.WriteFieldBegin(ctx, "userLogo", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:userLogo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserLogo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userLogo (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:userLogo: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarLevel() {
    if err := oprot.WriteFieldBegin(ctx, "starLevel", thrift.I32, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StarLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starLevel (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starLevel: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichLevel() {
    if err := oprot.WriteFieldBegin(ctx, "richLevel", thrift.I32, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:richLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RichLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richLevel (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:richLevel: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sex: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinSpend() {
    if err := oprot.WriteFieldBegin(ctx, "coinSpend", thrift.DOUBLE, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:coinSpend: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinSpend)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinSpend (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:coinSpend: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinTotal() {
    if err := oprot.WriteFieldBegin(ctx, "coinTotal", thrift.DOUBLE, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:coinTotal: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinTotal)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinTotal (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:coinTotal: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:userId: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoVO) Equals(other *UserCoreInfoVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.NickName != other.NickName {
    if p.NickName == nil || other.NickName == nil {
      return false
    }
    if (*p.NickName) != (*other.NickName) { return false }
  }
  if p.UserLogo != other.UserLogo {
    if p.UserLogo == nil || other.UserLogo == nil {
      return false
    }
    if (*p.UserLogo) != (*other.UserLogo) { return false }
  }
  if p.StarLevel != other.StarLevel {
    if p.StarLevel == nil || other.StarLevel == nil {
      return false
    }
    if (*p.StarLevel) != (*other.StarLevel) { return false }
  }
  if p.RichLevel != other.RichLevel {
    if p.RichLevel == nil || other.RichLevel == nil {
      return false
    }
    if (*p.RichLevel) != (*other.RichLevel) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.CoinSpend != other.CoinSpend {
    if p.CoinSpend == nil || other.CoinSpend == nil {
      return false
    }
    if (*p.CoinSpend) != (*other.CoinSpend) { return false }
  }
  if p.CoinTotal != other.CoinTotal {
    if p.CoinTotal == nil || other.CoinTotal == nil {
      return false
    }
    if (*p.CoinTotal) != (*other.CoinTotal) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *UserCoreInfoVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCoreInfoVO(%+v)", *p)
}

// 用户常用组合信息VO
// 
// 
// Attributes:
//  - KugouId
//  - NickName
//  - UserLogo
//  - StarLevel
//  - RichLevel
//  - Sex
//  - CoinSpend
//  - CoinTotal
//  - AddTime
//  - RegIp
//  - Constellation
//  - Height
//  - Weight
//  - Weibo
//  - Location
//  - Bwh
//  - LiveTimes
//  - UserId
type UserCombineInfoVO struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  NickName *string `thrift:"nickName,2" db:"nickName" json:"nickName,omitempty"`
  UserLogo *string `thrift:"userLogo,3" db:"userLogo" json:"userLogo,omitempty"`
  StarLevel *int32 `thrift:"starLevel,4" db:"starLevel" json:"starLevel,omitempty"`
  RichLevel *int32 `thrift:"richLevel,5" db:"richLevel" json:"richLevel,omitempty"`
  Sex *int32 `thrift:"sex,6" db:"sex" json:"sex,omitempty"`
  CoinSpend *float64 `thrift:"coinSpend,7" db:"coinSpend" json:"coinSpend,omitempty"`
  CoinTotal *float64 `thrift:"coinTotal,8" db:"coinTotal" json:"coinTotal,omitempty"`
  AddTime *int32 `thrift:"addTime,9" db:"addTime" json:"addTime,omitempty"`
  RegIp *string `thrift:"regIp,10" db:"regIp" json:"regIp,omitempty"`
  Constellation *int32 `thrift:"constellation,11" db:"constellation" json:"constellation,omitempty"`
  Height *int32 `thrift:"height,12" db:"height" json:"height,omitempty"`
  Weight *int32 `thrift:"weight,13" db:"weight" json:"weight,omitempty"`
  Weibo *string `thrift:"weibo,14" db:"weibo" json:"weibo,omitempty"`
  Location *string `thrift:"location,15" db:"location" json:"location,omitempty"`
  Bwh *string `thrift:"bwh,16" db:"bwh" json:"bwh,omitempty"`
  LiveTimes *string `thrift:"liveTimes,17" db:"liveTimes" json:"liveTimes,omitempty"`
  UserId *int64 `thrift:"userId,18" db:"userId" json:"userId,omitempty"`
}

func NewUserCombineInfoVO() *UserCombineInfoVO {
  return &UserCombineInfoVO{}
}

var UserCombineInfoVO_KugouId_DEFAULT int64
func (p *UserCombineInfoVO) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserCombineInfoVO_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserCombineInfoVO_NickName_DEFAULT string
func (p *UserCombineInfoVO) GetNickName() string {
  if !p.IsSetNickName() {
    return UserCombineInfoVO_NickName_DEFAULT
  }
return *p.NickName
}
var UserCombineInfoVO_UserLogo_DEFAULT string
func (p *UserCombineInfoVO) GetUserLogo() string {
  if !p.IsSetUserLogo() {
    return UserCombineInfoVO_UserLogo_DEFAULT
  }
return *p.UserLogo
}
var UserCombineInfoVO_StarLevel_DEFAULT int32
func (p *UserCombineInfoVO) GetStarLevel() int32 {
  if !p.IsSetStarLevel() {
    return UserCombineInfoVO_StarLevel_DEFAULT
  }
return *p.StarLevel
}
var UserCombineInfoVO_RichLevel_DEFAULT int32
func (p *UserCombineInfoVO) GetRichLevel() int32 {
  if !p.IsSetRichLevel() {
    return UserCombineInfoVO_RichLevel_DEFAULT
  }
return *p.RichLevel
}
var UserCombineInfoVO_Sex_DEFAULT int32
func (p *UserCombineInfoVO) GetSex() int32 {
  if !p.IsSetSex() {
    return UserCombineInfoVO_Sex_DEFAULT
  }
return *p.Sex
}
var UserCombineInfoVO_CoinSpend_DEFAULT float64
func (p *UserCombineInfoVO) GetCoinSpend() float64 {
  if !p.IsSetCoinSpend() {
    return UserCombineInfoVO_CoinSpend_DEFAULT
  }
return *p.CoinSpend
}
var UserCombineInfoVO_CoinTotal_DEFAULT float64
func (p *UserCombineInfoVO) GetCoinTotal() float64 {
  if !p.IsSetCoinTotal() {
    return UserCombineInfoVO_CoinTotal_DEFAULT
  }
return *p.CoinTotal
}
var UserCombineInfoVO_AddTime_DEFAULT int32
func (p *UserCombineInfoVO) GetAddTime() int32 {
  if !p.IsSetAddTime() {
    return UserCombineInfoVO_AddTime_DEFAULT
  }
return *p.AddTime
}
var UserCombineInfoVO_RegIp_DEFAULT string
func (p *UserCombineInfoVO) GetRegIp() string {
  if !p.IsSetRegIp() {
    return UserCombineInfoVO_RegIp_DEFAULT
  }
return *p.RegIp
}
var UserCombineInfoVO_Constellation_DEFAULT int32
func (p *UserCombineInfoVO) GetConstellation() int32 {
  if !p.IsSetConstellation() {
    return UserCombineInfoVO_Constellation_DEFAULT
  }
return *p.Constellation
}
var UserCombineInfoVO_Height_DEFAULT int32
func (p *UserCombineInfoVO) GetHeight() int32 {
  if !p.IsSetHeight() {
    return UserCombineInfoVO_Height_DEFAULT
  }
return *p.Height
}
var UserCombineInfoVO_Weight_DEFAULT int32
func (p *UserCombineInfoVO) GetWeight() int32 {
  if !p.IsSetWeight() {
    return UserCombineInfoVO_Weight_DEFAULT
  }
return *p.Weight
}
var UserCombineInfoVO_Weibo_DEFAULT string
func (p *UserCombineInfoVO) GetWeibo() string {
  if !p.IsSetWeibo() {
    return UserCombineInfoVO_Weibo_DEFAULT
  }
return *p.Weibo
}
var UserCombineInfoVO_Location_DEFAULT string
func (p *UserCombineInfoVO) GetLocation() string {
  if !p.IsSetLocation() {
    return UserCombineInfoVO_Location_DEFAULT
  }
return *p.Location
}
var UserCombineInfoVO_Bwh_DEFAULT string
func (p *UserCombineInfoVO) GetBwh() string {
  if !p.IsSetBwh() {
    return UserCombineInfoVO_Bwh_DEFAULT
  }
return *p.Bwh
}
var UserCombineInfoVO_LiveTimes_DEFAULT string
func (p *UserCombineInfoVO) GetLiveTimes() string {
  if !p.IsSetLiveTimes() {
    return UserCombineInfoVO_LiveTimes_DEFAULT
  }
return *p.LiveTimes
}
var UserCombineInfoVO_UserId_DEFAULT int64
func (p *UserCombineInfoVO) GetUserId() int64 {
  if !p.IsSetUserId() {
    return UserCombineInfoVO_UserId_DEFAULT
  }
return *p.UserId
}
func (p *UserCombineInfoVO) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserCombineInfoVO) IsSetNickName() bool {
  return p.NickName != nil
}

func (p *UserCombineInfoVO) IsSetUserLogo() bool {
  return p.UserLogo != nil
}

func (p *UserCombineInfoVO) IsSetStarLevel() bool {
  return p.StarLevel != nil
}

func (p *UserCombineInfoVO) IsSetRichLevel() bool {
  return p.RichLevel != nil
}

func (p *UserCombineInfoVO) IsSetSex() bool {
  return p.Sex != nil
}

func (p *UserCombineInfoVO) IsSetCoinSpend() bool {
  return p.CoinSpend != nil
}

func (p *UserCombineInfoVO) IsSetCoinTotal() bool {
  return p.CoinTotal != nil
}

func (p *UserCombineInfoVO) IsSetAddTime() bool {
  return p.AddTime != nil
}

func (p *UserCombineInfoVO) IsSetRegIp() bool {
  return p.RegIp != nil
}

func (p *UserCombineInfoVO) IsSetConstellation() bool {
  return p.Constellation != nil
}

func (p *UserCombineInfoVO) IsSetHeight() bool {
  return p.Height != nil
}

func (p *UserCombineInfoVO) IsSetWeight() bool {
  return p.Weight != nil
}

func (p *UserCombineInfoVO) IsSetWeibo() bool {
  return p.Weibo != nil
}

func (p *UserCombineInfoVO) IsSetLocation() bool {
  return p.Location != nil
}

func (p *UserCombineInfoVO) IsSetBwh() bool {
  return p.Bwh != nil
}

func (p *UserCombineInfoVO) IsSetLiveTimes() bool {
  return p.LiveTimes != nil
}

func (p *UserCombineInfoVO) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *UserCombineInfoVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 15:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField15(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 16:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField16(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 17:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField17(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 18:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField18(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserCombineInfoVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.NickName = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.UserLogo = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StarLevel = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.RichLevel = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.CoinSpend = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.CoinTotal = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.AddTime = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.RegIp = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.Constellation = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.Height = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.Weight = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.Weibo = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 15: ", err)
} else {
  p.Location = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 16: ", err)
} else {
  p.Bwh = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField17(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 17: ", err)
} else {
  p.LiveTimes = &v
}
  return nil
}

func (p *UserCombineInfoVO)  ReadField18(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 18: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *UserCombineInfoVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCombineInfoVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
    if err := p.writeField15(ctx, oprot); err != nil { return err }
    if err := p.writeField16(ctx, oprot); err != nil { return err }
    if err := p.writeField17(ctx, oprot); err != nil { return err }
    if err := p.writeField18(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCombineInfoVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNickName() {
    if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:nickName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NickName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.nickName (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:nickName: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserLogo() {
    if err := oprot.WriteFieldBegin(ctx, "userLogo", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:userLogo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserLogo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userLogo (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:userLogo: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarLevel() {
    if err := oprot.WriteFieldBegin(ctx, "starLevel", thrift.I32, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StarLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starLevel (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starLevel: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichLevel() {
    if err := oprot.WriteFieldBegin(ctx, "richLevel", thrift.I32, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:richLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RichLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richLevel (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:richLevel: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sex: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinSpend() {
    if err := oprot.WriteFieldBegin(ctx, "coinSpend", thrift.DOUBLE, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:coinSpend: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinSpend)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinSpend (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:coinSpend: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinTotal() {
    if err := oprot.WriteFieldBegin(ctx, "coinTotal", thrift.DOUBLE, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:coinTotal: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinTotal)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinTotal (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:coinTotal: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAddTime() {
    if err := oprot.WriteFieldBegin(ctx, "addTime", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:addTime: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AddTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.addTime (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:addTime: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRegIp() {
    if err := oprot.WriteFieldBegin(ctx, "regIp", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:regIp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.RegIp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.regIp (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:regIp: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetConstellation() {
    if err := oprot.WriteFieldBegin(ctx, "constellation", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:constellation: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Constellation)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.constellation (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:constellation: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeight() {
    if err := oprot.WriteFieldBegin(ctx, "height", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:height: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Height)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.height (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:height: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeight() {
    if err := oprot.WriteFieldBegin(ctx, "weight", thrift.I32, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:weight: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Weight)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weight (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:weight: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeibo() {
    if err := oprot.WriteFieldBegin(ctx, "weibo", thrift.STRING, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:weibo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Weibo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weibo (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:weibo: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLocation() {
    if err := oprot.WriteFieldBegin(ctx, "location", thrift.STRING, 15); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:location: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Location)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.location (15) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 15:location: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBwh() {
    if err := oprot.WriteFieldBegin(ctx, "bwh", thrift.STRING, 16); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:bwh: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Bwh)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.bwh (16) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 16:bwh: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField17(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLiveTimes() {
    if err := oprot.WriteFieldBegin(ctx, "liveTimes", thrift.STRING, 17); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 17:liveTimes: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.LiveTimes)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.liveTimes (17) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 17:liveTimes: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) writeField18(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 18); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 18:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (18) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 18:userId: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoVO) Equals(other *UserCombineInfoVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.NickName != other.NickName {
    if p.NickName == nil || other.NickName == nil {
      return false
    }
    if (*p.NickName) != (*other.NickName) { return false }
  }
  if p.UserLogo != other.UserLogo {
    if p.UserLogo == nil || other.UserLogo == nil {
      return false
    }
    if (*p.UserLogo) != (*other.UserLogo) { return false }
  }
  if p.StarLevel != other.StarLevel {
    if p.StarLevel == nil || other.StarLevel == nil {
      return false
    }
    if (*p.StarLevel) != (*other.StarLevel) { return false }
  }
  if p.RichLevel != other.RichLevel {
    if p.RichLevel == nil || other.RichLevel == nil {
      return false
    }
    if (*p.RichLevel) != (*other.RichLevel) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.CoinSpend != other.CoinSpend {
    if p.CoinSpend == nil || other.CoinSpend == nil {
      return false
    }
    if (*p.CoinSpend) != (*other.CoinSpend) { return false }
  }
  if p.CoinTotal != other.CoinTotal {
    if p.CoinTotal == nil || other.CoinTotal == nil {
      return false
    }
    if (*p.CoinTotal) != (*other.CoinTotal) { return false }
  }
  if p.AddTime != other.AddTime {
    if p.AddTime == nil || other.AddTime == nil {
      return false
    }
    if (*p.AddTime) != (*other.AddTime) { return false }
  }
  if p.RegIp != other.RegIp {
    if p.RegIp == nil || other.RegIp == nil {
      return false
    }
    if (*p.RegIp) != (*other.RegIp) { return false }
  }
  if p.Constellation != other.Constellation {
    if p.Constellation == nil || other.Constellation == nil {
      return false
    }
    if (*p.Constellation) != (*other.Constellation) { return false }
  }
  if p.Height != other.Height {
    if p.Height == nil || other.Height == nil {
      return false
    }
    if (*p.Height) != (*other.Height) { return false }
  }
  if p.Weight != other.Weight {
    if p.Weight == nil || other.Weight == nil {
      return false
    }
    if (*p.Weight) != (*other.Weight) { return false }
  }
  if p.Weibo != other.Weibo {
    if p.Weibo == nil || other.Weibo == nil {
      return false
    }
    if (*p.Weibo) != (*other.Weibo) { return false }
  }
  if p.Location != other.Location {
    if p.Location == nil || other.Location == nil {
      return false
    }
    if (*p.Location) != (*other.Location) { return false }
  }
  if p.Bwh != other.Bwh {
    if p.Bwh == nil || other.Bwh == nil {
      return false
    }
    if (*p.Bwh) != (*other.Bwh) { return false }
  }
  if p.LiveTimes != other.LiveTimes {
    if p.LiveTimes == nil || other.LiveTimes == nil {
      return false
    }
    if (*p.LiveTimes) != (*other.LiveTimes) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *UserCombineInfoVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCombineInfoVO(%+v)", *p)
}

// t_user_attr单表信息VO
// 
// 
// Attributes:
//  - KugouId
//  - DefaultRoomId
//  - FromType
//  - OpenId
//  - AddTime
//  - RegIp
//  - Constellation
//  - Height
//  - Weight
//  - BV
//  - WV
//  - HV
//  - Weibo
//  - Location
//  - Bwh
//  - LiveTimes
//  - UserId
type UserAttrVO struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  DefaultRoomId *int32 `thrift:"defaultRoomId,2" db:"defaultRoomId" json:"defaultRoomId,omitempty"`
  FromType *int32 `thrift:"fromType,3" db:"fromType" json:"fromType,omitempty"`
  OpenId *string `thrift:"openId,4" db:"openId" json:"openId,omitempty"`
  AddTime *int32 `thrift:"addTime,5" db:"addTime" json:"addTime,omitempty"`
  RegIp *string `thrift:"regIp,6" db:"regIp" json:"regIp,omitempty"`
  Constellation *int32 `thrift:"constellation,7" db:"constellation" json:"constellation,omitempty"`
  Height *int32 `thrift:"height,8" db:"height" json:"height,omitempty"`
  Weight *int32 `thrift:"weight,9" db:"weight" json:"weight,omitempty"`
  BV *int32 `thrift:"bV,10" db:"bV" json:"bV,omitempty"`
  WV *int32 `thrift:"wV,11" db:"wV" json:"wV,omitempty"`
  HV *int32 `thrift:"hV,12" db:"hV" json:"hV,omitempty"`
  Weibo *string `thrift:"weibo,13" db:"weibo" json:"weibo,omitempty"`
  Location *string `thrift:"location,14" db:"location" json:"location,omitempty"`
  Bwh *string `thrift:"bwh,15" db:"bwh" json:"bwh,omitempty"`
  LiveTimes *string `thrift:"liveTimes,16" db:"liveTimes" json:"liveTimes,omitempty"`
  UserId *int64 `thrift:"userId,17" db:"userId" json:"userId,omitempty"`
}

func NewUserAttrVO() *UserAttrVO {
  return &UserAttrVO{}
}

var UserAttrVO_KugouId_DEFAULT int64
func (p *UserAttrVO) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserAttrVO_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserAttrVO_DefaultRoomId_DEFAULT int32
func (p *UserAttrVO) GetDefaultRoomId() int32 {
  if !p.IsSetDefaultRoomId() {
    return UserAttrVO_DefaultRoomId_DEFAULT
  }
return *p.DefaultRoomId
}
var UserAttrVO_FromType_DEFAULT int32
func (p *UserAttrVO) GetFromType() int32 {
  if !p.IsSetFromType() {
    return UserAttrVO_FromType_DEFAULT
  }
return *p.FromType
}
var UserAttrVO_OpenId_DEFAULT string
func (p *UserAttrVO) GetOpenId() string {
  if !p.IsSetOpenId() {
    return UserAttrVO_OpenId_DEFAULT
  }
return *p.OpenId
}
var UserAttrVO_AddTime_DEFAULT int32
func (p *UserAttrVO) GetAddTime() int32 {
  if !p.IsSetAddTime() {
    return UserAttrVO_AddTime_DEFAULT
  }
return *p.AddTime
}
var UserAttrVO_RegIp_DEFAULT string
func (p *UserAttrVO) GetRegIp() string {
  if !p.IsSetRegIp() {
    return UserAttrVO_RegIp_DEFAULT
  }
return *p.RegIp
}
var UserAttrVO_Constellation_DEFAULT int32
func (p *UserAttrVO) GetConstellation() int32 {
  if !p.IsSetConstellation() {
    return UserAttrVO_Constellation_DEFAULT
  }
return *p.Constellation
}
var UserAttrVO_Height_DEFAULT int32
func (p *UserAttrVO) GetHeight() int32 {
  if !p.IsSetHeight() {
    return UserAttrVO_Height_DEFAULT
  }
return *p.Height
}
var UserAttrVO_Weight_DEFAULT int32
func (p *UserAttrVO) GetWeight() int32 {
  if !p.IsSetWeight() {
    return UserAttrVO_Weight_DEFAULT
  }
return *p.Weight
}
var UserAttrVO_BV_DEFAULT int32
func (p *UserAttrVO) GetBV() int32 {
  if !p.IsSetBV() {
    return UserAttrVO_BV_DEFAULT
  }
return *p.BV
}
var UserAttrVO_WV_DEFAULT int32
func (p *UserAttrVO) GetWV() int32 {
  if !p.IsSetWV() {
    return UserAttrVO_WV_DEFAULT
  }
return *p.WV
}
var UserAttrVO_HV_DEFAULT int32
func (p *UserAttrVO) GetHV() int32 {
  if !p.IsSetHV() {
    return UserAttrVO_HV_DEFAULT
  }
return *p.HV
}
var UserAttrVO_Weibo_DEFAULT string
func (p *UserAttrVO) GetWeibo() string {
  if !p.IsSetWeibo() {
    return UserAttrVO_Weibo_DEFAULT
  }
return *p.Weibo
}
var UserAttrVO_Location_DEFAULT string
func (p *UserAttrVO) GetLocation() string {
  if !p.IsSetLocation() {
    return UserAttrVO_Location_DEFAULT
  }
return *p.Location
}
var UserAttrVO_Bwh_DEFAULT string
func (p *UserAttrVO) GetBwh() string {
  if !p.IsSetBwh() {
    return UserAttrVO_Bwh_DEFAULT
  }
return *p.Bwh
}
var UserAttrVO_LiveTimes_DEFAULT string
func (p *UserAttrVO) GetLiveTimes() string {
  if !p.IsSetLiveTimes() {
    return UserAttrVO_LiveTimes_DEFAULT
  }
return *p.LiveTimes
}
var UserAttrVO_UserId_DEFAULT int64
func (p *UserAttrVO) GetUserId() int64 {
  if !p.IsSetUserId() {
    return UserAttrVO_UserId_DEFAULT
  }
return *p.UserId
}
func (p *UserAttrVO) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserAttrVO) IsSetDefaultRoomId() bool {
  return p.DefaultRoomId != nil
}

func (p *UserAttrVO) IsSetFromType() bool {
  return p.FromType != nil
}

func (p *UserAttrVO) IsSetOpenId() bool {
  return p.OpenId != nil
}

func (p *UserAttrVO) IsSetAddTime() bool {
  return p.AddTime != nil
}

func (p *UserAttrVO) IsSetRegIp() bool {
  return p.RegIp != nil
}

func (p *UserAttrVO) IsSetConstellation() bool {
  return p.Constellation != nil
}

func (p *UserAttrVO) IsSetHeight() bool {
  return p.Height != nil
}

func (p *UserAttrVO) IsSetWeight() bool {
  return p.Weight != nil
}

func (p *UserAttrVO) IsSetBV() bool {
  return p.BV != nil
}

func (p *UserAttrVO) IsSetWV() bool {
  return p.WV != nil
}

func (p *UserAttrVO) IsSetHV() bool {
  return p.HV != nil
}

func (p *UserAttrVO) IsSetWeibo() bool {
  return p.Weibo != nil
}

func (p *UserAttrVO) IsSetLocation() bool {
  return p.Location != nil
}

func (p *UserAttrVO) IsSetBwh() bool {
  return p.Bwh != nil
}

func (p *UserAttrVO) IsSetLiveTimes() bool {
  return p.LiveTimes != nil
}

func (p *UserAttrVO) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *UserAttrVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 15:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField15(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 16:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField16(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 17:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField17(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserAttrVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.DefaultRoomId = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.FromType = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.OpenId = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.AddTime = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.RegIp = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Constellation = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Height = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Weight = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.BV = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.WV = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.HV = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.Weibo = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.Location = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 15: ", err)
} else {
  p.Bwh = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 16: ", err)
} else {
  p.LiveTimes = &v
}
  return nil
}

func (p *UserAttrVO)  ReadField17(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 17: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *UserAttrVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserAttrVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
    if err := p.writeField15(ctx, oprot); err != nil { return err }
    if err := p.writeField16(ctx, oprot); err != nil { return err }
    if err := p.writeField17(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserAttrVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDefaultRoomId() {
    if err := oprot.WriteFieldBegin(ctx, "defaultRoomId", thrift.I32, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:defaultRoomId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.DefaultRoomId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.defaultRoomId (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:defaultRoomId: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFromType() {
    if err := oprot.WriteFieldBegin(ctx, "fromType", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:fromType: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.FromType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.fromType (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:fromType: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetOpenId() {
    if err := oprot.WriteFieldBegin(ctx, "openId", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:openId: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.OpenId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.openId (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:openId: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAddTime() {
    if err := oprot.WriteFieldBegin(ctx, "addTime", thrift.I32, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:addTime: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AddTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.addTime (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:addTime: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRegIp() {
    if err := oprot.WriteFieldBegin(ctx, "regIp", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:regIp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.RegIp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.regIp (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:regIp: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetConstellation() {
    if err := oprot.WriteFieldBegin(ctx, "constellation", thrift.I32, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:constellation: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Constellation)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.constellation (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:constellation: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeight() {
    if err := oprot.WriteFieldBegin(ctx, "height", thrift.I32, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:height: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Height)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.height (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:height: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeight() {
    if err := oprot.WriteFieldBegin(ctx, "weight", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:weight: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Weight)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weight (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:weight: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBV() {
    if err := oprot.WriteFieldBegin(ctx, "bV", thrift.I32, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:bV: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.BV)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.bV (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:bV: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWV() {
    if err := oprot.WriteFieldBegin(ctx, "wV", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:wV: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.WV)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.wV (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:wV: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHV() {
    if err := oprot.WriteFieldBegin(ctx, "hV", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:hV: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.HV)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.hV (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:hV: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeibo() {
    if err := oprot.WriteFieldBegin(ctx, "weibo", thrift.STRING, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:weibo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Weibo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weibo (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:weibo: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLocation() {
    if err := oprot.WriteFieldBegin(ctx, "location", thrift.STRING, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:location: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Location)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.location (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:location: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBwh() {
    if err := oprot.WriteFieldBegin(ctx, "bwh", thrift.STRING, 15); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:bwh: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Bwh)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.bwh (15) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 15:bwh: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLiveTimes() {
    if err := oprot.WriteFieldBegin(ctx, "liveTimes", thrift.STRING, 16); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:liveTimes: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.LiveTimes)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.liveTimes (16) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 16:liveTimes: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) writeField17(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 17); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 17:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (17) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 17:userId: ", p), err) }
  }
  return err
}

func (p *UserAttrVO) Equals(other *UserAttrVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.DefaultRoomId != other.DefaultRoomId {
    if p.DefaultRoomId == nil || other.DefaultRoomId == nil {
      return false
    }
    if (*p.DefaultRoomId) != (*other.DefaultRoomId) { return false }
  }
  if p.FromType != other.FromType {
    if p.FromType == nil || other.FromType == nil {
      return false
    }
    if (*p.FromType) != (*other.FromType) { return false }
  }
  if p.OpenId != other.OpenId {
    if p.OpenId == nil || other.OpenId == nil {
      return false
    }
    if (*p.OpenId) != (*other.OpenId) { return false }
  }
  if p.AddTime != other.AddTime {
    if p.AddTime == nil || other.AddTime == nil {
      return false
    }
    if (*p.AddTime) != (*other.AddTime) { return false }
  }
  if p.RegIp != other.RegIp {
    if p.RegIp == nil || other.RegIp == nil {
      return false
    }
    if (*p.RegIp) != (*other.RegIp) { return false }
  }
  if p.Constellation != other.Constellation {
    if p.Constellation == nil || other.Constellation == nil {
      return false
    }
    if (*p.Constellation) != (*other.Constellation) { return false }
  }
  if p.Height != other.Height {
    if p.Height == nil || other.Height == nil {
      return false
    }
    if (*p.Height) != (*other.Height) { return false }
  }
  if p.Weight != other.Weight {
    if p.Weight == nil || other.Weight == nil {
      return false
    }
    if (*p.Weight) != (*other.Weight) { return false }
  }
  if p.BV != other.BV {
    if p.BV == nil || other.BV == nil {
      return false
    }
    if (*p.BV) != (*other.BV) { return false }
  }
  if p.WV != other.WV {
    if p.WV == nil || other.WV == nil {
      return false
    }
    if (*p.WV) != (*other.WV) { return false }
  }
  if p.HV != other.HV {
    if p.HV == nil || other.HV == nil {
      return false
    }
    if (*p.HV) != (*other.HV) { return false }
  }
  if p.Weibo != other.Weibo {
    if p.Weibo == nil || other.Weibo == nil {
      return false
    }
    if (*p.Weibo) != (*other.Weibo) { return false }
  }
  if p.Location != other.Location {
    if p.Location == nil || other.Location == nil {
      return false
    }
    if (*p.Location) != (*other.Location) { return false }
  }
  if p.Bwh != other.Bwh {
    if p.Bwh == nil || other.Bwh == nil {
      return false
    }
    if (*p.Bwh) != (*other.Bwh) { return false }
  }
  if p.LiveTimes != other.LiveTimes {
    if p.LiveTimes == nil || other.LiveTimes == nil {
      return false
    }
    if (*p.LiveTimes) != (*other.LiveTimes) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *UserAttrVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserAttrVO(%+v)", *p)
}

// t_user_data单表信息VO
// 
// 
// Attributes:
//  - KugouId
//  - PayCount
//  - LiveCount
//  - AudioCount
//  - FansCount
//  - FollowCount
//  - XxCount
//  - FansValueCount
//  - GiftCount
//  - PhotoCount
//  - VideoCount
//  - MessageCount
//  - UserId
type UserDataVO struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  PayCount *int32 `thrift:"payCount,2" db:"payCount" json:"payCount,omitempty"`
  LiveCount *int32 `thrift:"liveCount,3" db:"liveCount" json:"liveCount,omitempty"`
  AudioCount *int32 `thrift:"audioCount,4" db:"audioCount" json:"audioCount,omitempty"`
  FansCount *int32 `thrift:"fansCount,5" db:"fansCount" json:"fansCount,omitempty"`
  FollowCount *int32 `thrift:"followCount,6" db:"followCount" json:"followCount,omitempty"`
  XxCount *int64 `thrift:"xxCount,7" db:"xxCount" json:"xxCount,omitempty"`
  FansValueCount *int64 `thrift:"fansValueCount,8" db:"fansValueCount" json:"fansValueCount,omitempty"`
  GiftCount *int32 `thrift:"giftCount,9" db:"giftCount" json:"giftCount,omitempty"`
  PhotoCount *int32 `thrift:"photoCount,10" db:"photoCount" json:"photoCount,omitempty"`
  VideoCount *int32 `thrift:"videoCount,11" db:"videoCount" json:"videoCount,omitempty"`
  MessageCount *int32 `thrift:"messageCount,12" db:"messageCount" json:"messageCount,omitempty"`
  UserId *int64 `thrift:"userId,13" db:"userId" json:"userId,omitempty"`
}

func NewUserDataVO() *UserDataVO {
  return &UserDataVO{}
}

var UserDataVO_KugouId_DEFAULT int64
func (p *UserDataVO) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserDataVO_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserDataVO_PayCount_DEFAULT int32
func (p *UserDataVO) GetPayCount() int32 {
  if !p.IsSetPayCount() {
    return UserDataVO_PayCount_DEFAULT
  }
return *p.PayCount
}
var UserDataVO_LiveCount_DEFAULT int32
func (p *UserDataVO) GetLiveCount() int32 {
  if !p.IsSetLiveCount() {
    return UserDataVO_LiveCount_DEFAULT
  }
return *p.LiveCount
}
var UserDataVO_AudioCount_DEFAULT int32
func (p *UserDataVO) GetAudioCount() int32 {
  if !p.IsSetAudioCount() {
    return UserDataVO_AudioCount_DEFAULT
  }
return *p.AudioCount
}
var UserDataVO_FansCount_DEFAULT int32
func (p *UserDataVO) GetFansCount() int32 {
  if !p.IsSetFansCount() {
    return UserDataVO_FansCount_DEFAULT
  }
return *p.FansCount
}
var UserDataVO_FollowCount_DEFAULT int32
func (p *UserDataVO) GetFollowCount() int32 {
  if !p.IsSetFollowCount() {
    return UserDataVO_FollowCount_DEFAULT
  }
return *p.FollowCount
}
var UserDataVO_XxCount_DEFAULT int64
func (p *UserDataVO) GetXxCount() int64 {
  if !p.IsSetXxCount() {
    return UserDataVO_XxCount_DEFAULT
  }
return *p.XxCount
}
var UserDataVO_FansValueCount_DEFAULT int64
func (p *UserDataVO) GetFansValueCount() int64 {
  if !p.IsSetFansValueCount() {
    return UserDataVO_FansValueCount_DEFAULT
  }
return *p.FansValueCount
}
var UserDataVO_GiftCount_DEFAULT int32
func (p *UserDataVO) GetGiftCount() int32 {
  if !p.IsSetGiftCount() {
    return UserDataVO_GiftCount_DEFAULT
  }
return *p.GiftCount
}
var UserDataVO_PhotoCount_DEFAULT int32
func (p *UserDataVO) GetPhotoCount() int32 {
  if !p.IsSetPhotoCount() {
    return UserDataVO_PhotoCount_DEFAULT
  }
return *p.PhotoCount
}
var UserDataVO_VideoCount_DEFAULT int32
func (p *UserDataVO) GetVideoCount() int32 {
  if !p.IsSetVideoCount() {
    return UserDataVO_VideoCount_DEFAULT
  }
return *p.VideoCount
}
var UserDataVO_MessageCount_DEFAULT int32
func (p *UserDataVO) GetMessageCount() int32 {
  if !p.IsSetMessageCount() {
    return UserDataVO_MessageCount_DEFAULT
  }
return *p.MessageCount
}
var UserDataVO_UserId_DEFAULT int64
func (p *UserDataVO) GetUserId() int64 {
  if !p.IsSetUserId() {
    return UserDataVO_UserId_DEFAULT
  }
return *p.UserId
}
func (p *UserDataVO) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserDataVO) IsSetPayCount() bool {
  return p.PayCount != nil
}

func (p *UserDataVO) IsSetLiveCount() bool {
  return p.LiveCount != nil
}

func (p *UserDataVO) IsSetAudioCount() bool {
  return p.AudioCount != nil
}

func (p *UserDataVO) IsSetFansCount() bool {
  return p.FansCount != nil
}

func (p *UserDataVO) IsSetFollowCount() bool {
  return p.FollowCount != nil
}

func (p *UserDataVO) IsSetXxCount() bool {
  return p.XxCount != nil
}

func (p *UserDataVO) IsSetFansValueCount() bool {
  return p.FansValueCount != nil
}

func (p *UserDataVO) IsSetGiftCount() bool {
  return p.GiftCount != nil
}

func (p *UserDataVO) IsSetPhotoCount() bool {
  return p.PhotoCount != nil
}

func (p *UserDataVO) IsSetVideoCount() bool {
  return p.VideoCount != nil
}

func (p *UserDataVO) IsSetMessageCount() bool {
  return p.MessageCount != nil
}

func (p *UserDataVO) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *UserDataVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserDataVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserDataVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.PayCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.LiveCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.AudioCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.FansCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.FollowCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.XxCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.FansValueCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.GiftCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.PhotoCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.VideoCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.MessageCount = &v
}
  return nil
}

func (p *UserDataVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *UserDataVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserDataVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserDataVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPayCount() {
    if err := oprot.WriteFieldBegin(ctx, "payCount", thrift.I32, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:payCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.PayCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.payCount (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:payCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLiveCount() {
    if err := oprot.WriteFieldBegin(ctx, "liveCount", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:liveCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.LiveCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.liveCount (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:liveCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAudioCount() {
    if err := oprot.WriteFieldBegin(ctx, "audioCount", thrift.I32, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:audioCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AudioCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.audioCount (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:audioCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFansCount() {
    if err := oprot.WriteFieldBegin(ctx, "fansCount", thrift.I32, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:fansCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.FansCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.fansCount (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:fansCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFollowCount() {
    if err := oprot.WriteFieldBegin(ctx, "followCount", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:followCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.FollowCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.followCount (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:followCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetXxCount() {
    if err := oprot.WriteFieldBegin(ctx, "xxCount", thrift.I64, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:xxCount: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.XxCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.xxCount (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:xxCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFansValueCount() {
    if err := oprot.WriteFieldBegin(ctx, "fansValueCount", thrift.I64, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:fansValueCount: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.FansValueCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.fansValueCount (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:fansValueCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGiftCount() {
    if err := oprot.WriteFieldBegin(ctx, "giftCount", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:giftCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.GiftCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.giftCount (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:giftCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPhotoCount() {
    if err := oprot.WriteFieldBegin(ctx, "photoCount", thrift.I32, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:photoCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.PhotoCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.photoCount (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:photoCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetVideoCount() {
    if err := oprot.WriteFieldBegin(ctx, "videoCount", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:videoCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.VideoCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.videoCount (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:videoCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMessageCount() {
    if err := oprot.WriteFieldBegin(ctx, "messageCount", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:messageCount: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.MessageCount)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.messageCount (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:messageCount: ", p), err) }
  }
  return err
}

func (p *UserDataVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:userId: ", p), err) }
  }
  return err
}

func (p *UserDataVO) Equals(other *UserDataVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.PayCount != other.PayCount {
    if p.PayCount == nil || other.PayCount == nil {
      return false
    }
    if (*p.PayCount) != (*other.PayCount) { return false }
  }
  if p.LiveCount != other.LiveCount {
    if p.LiveCount == nil || other.LiveCount == nil {
      return false
    }
    if (*p.LiveCount) != (*other.LiveCount) { return false }
  }
  if p.AudioCount != other.AudioCount {
    if p.AudioCount == nil || other.AudioCount == nil {
      return false
    }
    if (*p.AudioCount) != (*other.AudioCount) { return false }
  }
  if p.FansCount != other.FansCount {
    if p.FansCount == nil || other.FansCount == nil {
      return false
    }
    if (*p.FansCount) != (*other.FansCount) { return false }
  }
  if p.FollowCount != other.FollowCount {
    if p.FollowCount == nil || other.FollowCount == nil {
      return false
    }
    if (*p.FollowCount) != (*other.FollowCount) { return false }
  }
  if p.XxCount != other.XxCount {
    if p.XxCount == nil || other.XxCount == nil {
      return false
    }
    if (*p.XxCount) != (*other.XxCount) { return false }
  }
  if p.FansValueCount != other.FansValueCount {
    if p.FansValueCount == nil || other.FansValueCount == nil {
      return false
    }
    if (*p.FansValueCount) != (*other.FansValueCount) { return false }
  }
  if p.GiftCount != other.GiftCount {
    if p.GiftCount == nil || other.GiftCount == nil {
      return false
    }
    if (*p.GiftCount) != (*other.GiftCount) { return false }
  }
  if p.PhotoCount != other.PhotoCount {
    if p.PhotoCount == nil || other.PhotoCount == nil {
      return false
    }
    if (*p.PhotoCount) != (*other.PhotoCount) { return false }
  }
  if p.VideoCount != other.VideoCount {
    if p.VideoCount == nil || other.VideoCount == nil {
      return false
    }
    if (*p.VideoCount) != (*other.VideoCount) { return false }
  }
  if p.MessageCount != other.MessageCount {
    if p.MessageCount == nil || other.MessageCount == nil {
      return false
    }
    if (*p.MessageCount) != (*other.MessageCount) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *UserDataVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserDataVO(%+v)", *p)
}

// t_user_ext_attr单表信息VO
// 
// 
// Attributes:
//  - KugouId
//  - UnionId
//  - ClanId
//  - BadgeBuyTypeId
//  - SUnionId
//  - UserId
type UserExtAttrVO struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  UnionId *int64 `thrift:"unionId,2" db:"unionId" json:"unionId,omitempty"`
  ClanId *int64 `thrift:"clanId,3" db:"clanId" json:"clanId,omitempty"`
  BadgeBuyTypeId *int32 `thrift:"badgeBuyTypeId,4" db:"badgeBuyTypeId" json:"badgeBuyTypeId,omitempty"`
  SUnionId *string `thrift:"sUnionId,5" db:"sUnionId" json:"sUnionId,omitempty"`
  UserId *int64 `thrift:"userId,6" db:"userId" json:"userId,omitempty"`
}

func NewUserExtAttrVO() *UserExtAttrVO {
  return &UserExtAttrVO{}
}

var UserExtAttrVO_KugouId_DEFAULT int64
func (p *UserExtAttrVO) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserExtAttrVO_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserExtAttrVO_UnionId_DEFAULT int64
func (p *UserExtAttrVO) GetUnionId() int64 {
  if !p.IsSetUnionId() {
    return UserExtAttrVO_UnionId_DEFAULT
  }
return *p.UnionId
}
var UserExtAttrVO_ClanId_DEFAULT int64
func (p *UserExtAttrVO) GetClanId() int64 {
  if !p.IsSetClanId() {
    return UserExtAttrVO_ClanId_DEFAULT
  }
return *p.ClanId
}
var UserExtAttrVO_BadgeBuyTypeId_DEFAULT int32
func (p *UserExtAttrVO) GetBadgeBuyTypeId() int32 {
  if !p.IsSetBadgeBuyTypeId() {
    return UserExtAttrVO_BadgeBuyTypeId_DEFAULT
  }
return *p.BadgeBuyTypeId
}
var UserExtAttrVO_SUnionId_DEFAULT string
func (p *UserExtAttrVO) GetSUnionId() string {
  if !p.IsSetSUnionId() {
    return UserExtAttrVO_SUnionId_DEFAULT
  }
return *p.SUnionId
}
var UserExtAttrVO_UserId_DEFAULT int64
func (p *UserExtAttrVO) GetUserId() int64 {
  if !p.IsSetUserId() {
    return UserExtAttrVO_UserId_DEFAULT
  }
return *p.UserId
}
func (p *UserExtAttrVO) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserExtAttrVO) IsSetUnionId() bool {
  return p.UnionId != nil
}

func (p *UserExtAttrVO) IsSetClanId() bool {
  return p.ClanId != nil
}

func (p *UserExtAttrVO) IsSetBadgeBuyTypeId() bool {
  return p.BadgeBuyTypeId != nil
}

func (p *UserExtAttrVO) IsSetSUnionId() bool {
  return p.SUnionId != nil
}

func (p *UserExtAttrVO) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *UserExtAttrVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserExtAttrVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserExtAttrVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.UnionId = &v
}
  return nil
}

func (p *UserExtAttrVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.ClanId = &v
}
  return nil
}

func (p *UserExtAttrVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.BadgeBuyTypeId = &v
}
  return nil
}

func (p *UserExtAttrVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.SUnionId = &v
}
  return nil
}

func (p *UserExtAttrVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *UserExtAttrVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserExtAttrVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserExtAttrVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *UserExtAttrVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUnionId() {
    if err := oprot.WriteFieldBegin(ctx, "unionId", thrift.I64, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:unionId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UnionId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.unionId (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:unionId: ", p), err) }
  }
  return err
}

func (p *UserExtAttrVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClanId() {
    if err := oprot.WriteFieldBegin(ctx, "clanId", thrift.I64, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:clanId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.ClanId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clanId (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:clanId: ", p), err) }
  }
  return err
}

func (p *UserExtAttrVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBadgeBuyTypeId() {
    if err := oprot.WriteFieldBegin(ctx, "badgeBuyTypeId", thrift.I32, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:badgeBuyTypeId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.BadgeBuyTypeId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.badgeBuyTypeId (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:badgeBuyTypeId: ", p), err) }
  }
  return err
}

func (p *UserExtAttrVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSUnionId() {
    if err := oprot.WriteFieldBegin(ctx, "sUnionId", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:sUnionId: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.SUnionId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sUnionId (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:sUnionId: ", p), err) }
  }
  return err
}

func (p *UserExtAttrVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:userId: ", p), err) }
  }
  return err
}

func (p *UserExtAttrVO) Equals(other *UserExtAttrVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.UnionId != other.UnionId {
    if p.UnionId == nil || other.UnionId == nil {
      return false
    }
    if (*p.UnionId) != (*other.UnionId) { return false }
  }
  if p.ClanId != other.ClanId {
    if p.ClanId == nil || other.ClanId == nil {
      return false
    }
    if (*p.ClanId) != (*other.ClanId) { return false }
  }
  if p.BadgeBuyTypeId != other.BadgeBuyTypeId {
    if p.BadgeBuyTypeId == nil || other.BadgeBuyTypeId == nil {
      return false
    }
    if (*p.BadgeBuyTypeId) != (*other.BadgeBuyTypeId) { return false }
  }
  if p.SUnionId != other.SUnionId {
    if p.SUnionId == nil || other.SUnionId == nil {
      return false
    }
    if (*p.SUnionId) != (*other.SUnionId) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *UserExtAttrVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserExtAttrVO(%+v)", *p)
}

// t_user/ t_user_attr /t_user_data /t_user_ext_attr 多表信息VO
// 
// 
// Attributes:
//  - UserVo
//  - UserAttrVO
//  - UserDataVO
//  - UserExtAttrVO
type DynamicUserVO struct {
  UserVo *UserVO `thrift:"userVo,1" db:"userVo" json:"userVo,omitempty"`
  UserAttrVO *UserAttrVO `thrift:"userAttrVO,2" db:"userAttrVO" json:"userAttrVO,omitempty"`
  UserDataVO *UserDataVO `thrift:"userDataVO,3" db:"userDataVO" json:"userDataVO,omitempty"`
  UserExtAttrVO *UserExtAttrVO `thrift:"userExtAttrVO,4" db:"userExtAttrVO" json:"userExtAttrVO,omitempty"`
}

func NewDynamicUserVO() *DynamicUserVO {
  return &DynamicUserVO{}
}

var DynamicUserVO_UserVo_DEFAULT *UserVO
func (p *DynamicUserVO) GetUserVo() *UserVO {
  if !p.IsSetUserVo() {
    return DynamicUserVO_UserVo_DEFAULT
  }
return p.UserVo
}
var DynamicUserVO_UserAttrVO_DEFAULT *UserAttrVO
func (p *DynamicUserVO) GetUserAttrVO() *UserAttrVO {
  if !p.IsSetUserAttrVO() {
    return DynamicUserVO_UserAttrVO_DEFAULT
  }
return p.UserAttrVO
}
var DynamicUserVO_UserDataVO_DEFAULT *UserDataVO
func (p *DynamicUserVO) GetUserDataVO() *UserDataVO {
  if !p.IsSetUserDataVO() {
    return DynamicUserVO_UserDataVO_DEFAULT
  }
return p.UserDataVO
}
var DynamicUserVO_UserExtAttrVO_DEFAULT *UserExtAttrVO
func (p *DynamicUserVO) GetUserExtAttrVO() *UserExtAttrVO {
  if !p.IsSetUserExtAttrVO() {
    return DynamicUserVO_UserExtAttrVO_DEFAULT
  }
return p.UserExtAttrVO
}
func (p *DynamicUserVO) IsSetUserVo() bool {
  return p.UserVo != nil
}

func (p *DynamicUserVO) IsSetUserAttrVO() bool {
  return p.UserAttrVO != nil
}

func (p *DynamicUserVO) IsSetUserDataVO() bool {
  return p.UserDataVO != nil
}

func (p *DynamicUserVO) IsSetUserExtAttrVO() bool {
  return p.UserExtAttrVO != nil
}

func (p *DynamicUserVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DynamicUserVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.UserVo = &UserVO{}
  if err := p.UserVo.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.UserVo), err)
  }
  return nil
}

func (p *DynamicUserVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  p.UserAttrVO = &UserAttrVO{}
  if err := p.UserAttrVO.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.UserAttrVO), err)
  }
  return nil
}

func (p *DynamicUserVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.UserDataVO = &UserDataVO{}
  if err := p.UserDataVO.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.UserDataVO), err)
  }
  return nil
}

func (p *DynamicUserVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  p.UserExtAttrVO = &UserExtAttrVO{}
  if err := p.UserExtAttrVO.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.UserExtAttrVO), err)
  }
  return nil
}

func (p *DynamicUserVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DynamicUserVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DynamicUserVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserVo() {
    if err := oprot.WriteFieldBegin(ctx, "userVo", thrift.STRUCT, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userVo: ", p), err) }
    if err := p.UserVo.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.UserVo), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userVo: ", p), err) }
  }
  return err
}

func (p *DynamicUserVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserAttrVO() {
    if err := oprot.WriteFieldBegin(ctx, "userAttrVO", thrift.STRUCT, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:userAttrVO: ", p), err) }
    if err := p.UserAttrVO.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.UserAttrVO), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:userAttrVO: ", p), err) }
  }
  return err
}

func (p *DynamicUserVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserDataVO() {
    if err := oprot.WriteFieldBegin(ctx, "userDataVO", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:userDataVO: ", p), err) }
    if err := p.UserDataVO.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.UserDataVO), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:userDataVO: ", p), err) }
  }
  return err
}

func (p *DynamicUserVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserExtAttrVO() {
    if err := oprot.WriteFieldBegin(ctx, "userExtAttrVO", thrift.STRUCT, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:userExtAttrVO: ", p), err) }
    if err := p.UserExtAttrVO.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.UserExtAttrVO), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:userExtAttrVO: ", p), err) }
  }
  return err
}

func (p *DynamicUserVO) Equals(other *DynamicUserVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.UserVo.Equals(other.UserVo) { return false }
  if !p.UserAttrVO.Equals(other.UserAttrVO) { return false }
  if !p.UserDataVO.Equals(other.UserDataVO) { return false }
  if !p.UserExtAttrVO.Equals(other.UserExtAttrVO) { return false }
  return true
}

func (p *DynamicUserVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DynamicUserVO(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserDynamicResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*DynamicUserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserDynamicResponse() *UserDynamicResponse {
  return &UserDynamicResponse{}
}


func (p *UserDynamicResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserDynamicResponse) GetMsg() string {
  return p.Msg
}
var UserDynamicResponse_Data_DEFAULT map[int64]*DynamicUserVO

func (p *UserDynamicResponse) GetData() map[int64]*DynamicUserVO {
  return p.Data
}
func (p *UserDynamicResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserDynamicResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserDynamicResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserDynamicResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserDynamicResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*DynamicUserVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key6 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key6 = v
}
    _val7 := &DynamicUserVO{}
    if err := _val7.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val7), err)
    }
    p.Data[_key6] = _val7
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserDynamicResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserDynamicResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserDynamicResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserDynamicResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserDynamicResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserDynamicResponse) Equals(other *UserDynamicResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src8 := other.Data[k]
    if !_tgt.Equals(_src8) { return false }
  }
  return true
}

func (p *UserDynamicResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserDynamicResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserResponse() *UserResponse {
  return &UserResponse{}
}


func (p *UserResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserResponse) GetMsg() string {
  return p.Msg
}
var UserResponse_Data_DEFAULT *UserVO
func (p *UserResponse) GetData() *UserVO {
  if !p.IsSetData() {
    return UserResponse_Data_DEFAULT
  }
return p.Data
}
func (p *UserResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserResponse) Equals(other *UserResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*UserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserMapResponse() *UserMapResponse {
  return &UserMapResponse{}
}


func (p *UserMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserMapResponse) GetMsg() string {
  return p.Msg
}
var UserMapResponse_Data_DEFAULT map[int64]*UserVO

func (p *UserMapResponse) GetData() map[int64]*UserVO {
  return p.Data
}
func (p *UserMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*UserVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key9 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key9 = v
}
    _val10 := &UserVO{}
    if err := _val10.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val10), err)
    }
    p.Data[_key9] = _val10
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserMapResponse) Equals(other *UserMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src11 := other.Data[k]
    if !_tgt.Equals(_src11) { return false }
  }
  return true
}

func (p *UserMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*UserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserListResponse() *UserListResponse {
  return &UserListResponse{}
}


func (p *UserListResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserListResponse) GetMsg() string {
  return p.Msg
}
var UserListResponse_Data_DEFAULT []*UserVO

func (p *UserListResponse) GetData() []*UserVO {
  return p.Data
}
func (p *UserListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem12 := &UserVO{}
    if err := _elem12.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem12), err)
    }
    p.Data = append(p.Data, _elem12)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *UserListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserListResponse) Equals(other *UserListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src13 := other.Data[i]
    if !_tgt.Equals(_src13) { return false }
  }
  return true
}

func (p *UserListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserListResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserCoreInfoResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserCoreInfoVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserCoreInfoResponse() *UserCoreInfoResponse {
  return &UserCoreInfoResponse{}
}


func (p *UserCoreInfoResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserCoreInfoResponse) GetMsg() string {
  return p.Msg
}
var UserCoreInfoResponse_Data_DEFAULT *UserCoreInfoVO
func (p *UserCoreInfoResponse) GetData() *UserCoreInfoVO {
  if !p.IsSetData() {
    return UserCoreInfoResponse_Data_DEFAULT
  }
return p.Data
}
func (p *UserCoreInfoResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserCoreInfoResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserCoreInfoResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserCoreInfoResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserCoreInfoResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserCoreInfoVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserCoreInfoResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCoreInfoResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCoreInfoResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserCoreInfoResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserCoreInfoResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoResponse) Equals(other *UserCoreInfoResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserCoreInfoResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCoreInfoResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserCoreInfoMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*UserCoreInfoVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserCoreInfoMapResponse() *UserCoreInfoMapResponse {
  return &UserCoreInfoMapResponse{}
}


func (p *UserCoreInfoMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserCoreInfoMapResponse) GetMsg() string {
  return p.Msg
}
var UserCoreInfoMapResponse_Data_DEFAULT map[int64]*UserCoreInfoVO

func (p *UserCoreInfoMapResponse) GetData() map[int64]*UserCoreInfoVO {
  return p.Data
}
func (p *UserCoreInfoMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserCoreInfoMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserCoreInfoMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserCoreInfoMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserCoreInfoMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*UserCoreInfoVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key14 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key14 = v
}
    _val15 := &UserCoreInfoVO{}
    if err := _val15.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val15), err)
    }
    p.Data[_key14] = _val15
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserCoreInfoMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCoreInfoMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCoreInfoMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserCoreInfoMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserCoreInfoMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoMapResponse) Equals(other *UserCoreInfoMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src16 := other.Data[k]
    if !_tgt.Equals(_src16) { return false }
  }
  return true
}

func (p *UserCoreInfoMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCoreInfoMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserCoreInfoListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*UserCoreInfoVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserCoreInfoListResponse() *UserCoreInfoListResponse {
  return &UserCoreInfoListResponse{}
}


func (p *UserCoreInfoListResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserCoreInfoListResponse) GetMsg() string {
  return p.Msg
}
var UserCoreInfoListResponse_Data_DEFAULT []*UserCoreInfoVO

func (p *UserCoreInfoListResponse) GetData() []*UserCoreInfoVO {
  return p.Data
}
func (p *UserCoreInfoListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserCoreInfoListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserCoreInfoListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserCoreInfoListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserCoreInfoListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserCoreInfoVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem17 := &UserCoreInfoVO{}
    if err := _elem17.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem17), err)
    }
    p.Data = append(p.Data, _elem17)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *UserCoreInfoListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCoreInfoListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCoreInfoListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserCoreInfoListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserCoreInfoListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserCoreInfoListResponse) Equals(other *UserCoreInfoListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src18 := other.Data[i]
    if !_tgt.Equals(_src18) { return false }
  }
  return true
}

func (p *UserCoreInfoListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCoreInfoListResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserCombineInfoResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserCombineInfoVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserCombineInfoResponse() *UserCombineInfoResponse {
  return &UserCombineInfoResponse{}
}


func (p *UserCombineInfoResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserCombineInfoResponse) GetMsg() string {
  return p.Msg
}
var UserCombineInfoResponse_Data_DEFAULT *UserCombineInfoVO
func (p *UserCombineInfoResponse) GetData() *UserCombineInfoVO {
  if !p.IsSetData() {
    return UserCombineInfoResponse_Data_DEFAULT
  }
return p.Data
}
func (p *UserCombineInfoResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserCombineInfoResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserCombineInfoResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserCombineInfoResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserCombineInfoResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserCombineInfoVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserCombineInfoResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCombineInfoResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCombineInfoResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserCombineInfoResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserCombineInfoResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoResponse) Equals(other *UserCombineInfoResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserCombineInfoResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCombineInfoResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserCombineInfoMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*UserCombineInfoVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserCombineInfoMapResponse() *UserCombineInfoMapResponse {
  return &UserCombineInfoMapResponse{}
}


func (p *UserCombineInfoMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserCombineInfoMapResponse) GetMsg() string {
  return p.Msg
}
var UserCombineInfoMapResponse_Data_DEFAULT map[int64]*UserCombineInfoVO

func (p *UserCombineInfoMapResponse) GetData() map[int64]*UserCombineInfoVO {
  return p.Data
}
func (p *UserCombineInfoMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserCombineInfoMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserCombineInfoMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserCombineInfoMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserCombineInfoMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*UserCombineInfoVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key19 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key19 = v
}
    _val20 := &UserCombineInfoVO{}
    if err := _val20.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val20), err)
    }
    p.Data[_key19] = _val20
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserCombineInfoMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCombineInfoMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCombineInfoMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserCombineInfoMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserCombineInfoMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoMapResponse) Equals(other *UserCombineInfoMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src21 := other.Data[k]
    if !_tgt.Equals(_src21) { return false }
  }
  return true
}

func (p *UserCombineInfoMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCombineInfoMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserCombineInfoListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*UserCombineInfoVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserCombineInfoListResponse() *UserCombineInfoListResponse {
  return &UserCombineInfoListResponse{}
}


func (p *UserCombineInfoListResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserCombineInfoListResponse) GetMsg() string {
  return p.Msg
}
var UserCombineInfoListResponse_Data_DEFAULT []*UserCombineInfoVO

func (p *UserCombineInfoListResponse) GetData() []*UserCombineInfoVO {
  return p.Data
}
func (p *UserCombineInfoListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserCombineInfoListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserCombineInfoListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserCombineInfoListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserCombineInfoListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserCombineInfoVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem22 := &UserCombineInfoVO{}
    if err := _elem22.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem22), err)
    }
    p.Data = append(p.Data, _elem22)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *UserCombineInfoListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCombineInfoListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCombineInfoListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserCombineInfoListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserCombineInfoListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserCombineInfoListResponse) Equals(other *UserCombineInfoListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src23 := other.Data[i]
    if !_tgt.Equals(_src23) { return false }
  }
  return true
}

func (p *UserCombineInfoListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCombineInfoListResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserStrMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[string]*UserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserStrMapResponse() *UserStrMapResponse {
  return &UserStrMapResponse{}
}


func (p *UserStrMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserStrMapResponse) GetMsg() string {
  return p.Msg
}
var UserStrMapResponse_Data_DEFAULT map[string]*UserVO

func (p *UserStrMapResponse) GetData() map[string]*UserVO {
  return p.Data
}
func (p *UserStrMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserStrMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserStrMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserStrMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserStrMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[string]*UserVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key24 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key24 = v
}
    _val25 := &UserVO{}
    if err := _val25.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val25), err)
    }
    p.Data[_key24] = _val25
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserStrMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserStrMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserStrMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserStrMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserStrMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteString(ctx, string(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserStrMapResponse) Equals(other *UserStrMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src26 := other.Data[k]
    if !_tgt.Equals(_src26) { return false }
  }
  return true
}

func (p *UserStrMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserStrMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserAttrMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*UserAttrVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserAttrMapResponse() *UserAttrMapResponse {
  return &UserAttrMapResponse{}
}


func (p *UserAttrMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserAttrMapResponse) GetMsg() string {
  return p.Msg
}
var UserAttrMapResponse_Data_DEFAULT map[int64]*UserAttrVO

func (p *UserAttrMapResponse) GetData() map[int64]*UserAttrVO {
  return p.Data
}
func (p *UserAttrMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserAttrMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserAttrMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserAttrMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserAttrMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*UserAttrVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key27 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key27 = v
}
    _val28 := &UserAttrVO{}
    if err := _val28.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val28), err)
    }
    p.Data[_key27] = _val28
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserAttrMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserAttrMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserAttrMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserAttrMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserAttrMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserAttrMapResponse) Equals(other *UserAttrMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src29 := other.Data[k]
    if !_tgt.Equals(_src29) { return false }
  }
  return true
}

func (p *UserAttrMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserAttrMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserAttrListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*UserAttrVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserAttrListResponse() *UserAttrListResponse {
  return &UserAttrListResponse{}
}


func (p *UserAttrListResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserAttrListResponse) GetMsg() string {
  return p.Msg
}
var UserAttrListResponse_Data_DEFAULT []*UserAttrVO

func (p *UserAttrListResponse) GetData() []*UserAttrVO {
  return p.Data
}
func (p *UserAttrListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserAttrListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserAttrListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserAttrListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserAttrListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserAttrVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem30 := &UserAttrVO{}
    if err := _elem30.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem30), err)
    }
    p.Data = append(p.Data, _elem30)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *UserAttrListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserAttrListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserAttrListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserAttrListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserAttrListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserAttrListResponse) Equals(other *UserAttrListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src31 := other.Data[i]
    if !_tgt.Equals(_src31) { return false }
  }
  return true
}

func (p *UserAttrListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserAttrListResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserAttrResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserAttrVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserAttrResponse() *UserAttrResponse {
  return &UserAttrResponse{}
}


func (p *UserAttrResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserAttrResponse) GetMsg() string {
  return p.Msg
}
var UserAttrResponse_Data_DEFAULT *UserAttrVO
func (p *UserAttrResponse) GetData() *UserAttrVO {
  if !p.IsSetData() {
    return UserAttrResponse_Data_DEFAULT
  }
return p.Data
}
func (p *UserAttrResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserAttrResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserAttrResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserAttrResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserAttrResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserAttrVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserAttrResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserAttrResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserAttrResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserAttrResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserAttrResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserAttrResponse) Equals(other *UserAttrResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserAttrResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserAttrResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserDataMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*UserDataVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserDataMapResponse() *UserDataMapResponse {
  return &UserDataMapResponse{}
}


func (p *UserDataMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserDataMapResponse) GetMsg() string {
  return p.Msg
}
var UserDataMapResponse_Data_DEFAULT map[int64]*UserDataVO

func (p *UserDataMapResponse) GetData() map[int64]*UserDataVO {
  return p.Data
}
func (p *UserDataMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserDataMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserDataMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserDataMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserDataMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*UserDataVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key32 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key32 = v
}
    _val33 := &UserDataVO{}
    if err := _val33.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val33), err)
    }
    p.Data[_key32] = _val33
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserDataMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserDataMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserDataMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserDataMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserDataMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserDataMapResponse) Equals(other *UserDataMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src34 := other.Data[k]
    if !_tgt.Equals(_src34) { return false }
  }
  return true
}

func (p *UserDataMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserDataMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserDataListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*UserDataVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserDataListResponse() *UserDataListResponse {
  return &UserDataListResponse{}
}


func (p *UserDataListResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserDataListResponse) GetMsg() string {
  return p.Msg
}
var UserDataListResponse_Data_DEFAULT []*UserDataVO

func (p *UserDataListResponse) GetData() []*UserDataVO {
  return p.Data
}
func (p *UserDataListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserDataListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserDataListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserDataListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserDataListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserDataVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem35 := &UserDataVO{}
    if err := _elem35.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem35), err)
    }
    p.Data = append(p.Data, _elem35)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *UserDataListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserDataListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserDataListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserDataListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserDataListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserDataListResponse) Equals(other *UserDataListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src36 := other.Data[i]
    if !_tgt.Equals(_src36) { return false }
  }
  return true
}

func (p *UserDataListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserDataListResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserDataResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserDataVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserDataResponse() *UserDataResponse {
  return &UserDataResponse{}
}


func (p *UserDataResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserDataResponse) GetMsg() string {
  return p.Msg
}
var UserDataResponse_Data_DEFAULT *UserDataVO
func (p *UserDataResponse) GetData() *UserDataVO {
  if !p.IsSetData() {
    return UserDataResponse_Data_DEFAULT
  }
return p.Data
}
func (p *UserDataResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserDataResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserDataResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserDataResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserDataResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserDataVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserDataResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserDataResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserDataResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserDataResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserDataResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserDataResponse) Equals(other *UserDataResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserDataResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserDataResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserExtAttrListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*UserExtAttrVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserExtAttrListResponse() *UserExtAttrListResponse {
  return &UserExtAttrListResponse{}
}


func (p *UserExtAttrListResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserExtAttrListResponse) GetMsg() string {
  return p.Msg
}
var UserExtAttrListResponse_Data_DEFAULT []*UserExtAttrVO

func (p *UserExtAttrListResponse) GetData() []*UserExtAttrVO {
  return p.Data
}
func (p *UserExtAttrListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserExtAttrListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserExtAttrListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserExtAttrListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserExtAttrListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserExtAttrVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem37 := &UserExtAttrVO{}
    if err := _elem37.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem37), err)
    }
    p.Data = append(p.Data, _elem37)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *UserExtAttrListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserExtAttrListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserExtAttrListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserExtAttrListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserExtAttrListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserExtAttrListResponse) Equals(other *UserExtAttrListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src38 := other.Data[i]
    if !_tgt.Equals(_src38) { return false }
  }
  return true
}

func (p *UserExtAttrListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserExtAttrListResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserExtAttrMapResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]*UserExtAttrVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserExtAttrMapResponse() *UserExtAttrMapResponse {
  return &UserExtAttrMapResponse{}
}


func (p *UserExtAttrMapResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserExtAttrMapResponse) GetMsg() string {
  return p.Msg
}
var UserExtAttrMapResponse_Data_DEFAULT map[int64]*UserExtAttrVO

func (p *UserExtAttrMapResponse) GetData() map[int64]*UserExtAttrVO {
  return p.Data
}
func (p *UserExtAttrMapResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserExtAttrMapResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserExtAttrMapResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserExtAttrMapResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserExtAttrMapResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]*UserExtAttrVO, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key39 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key39 = v
}
    _val40 := &UserExtAttrVO{}
    if err := _val40.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val40), err)
    }
    p.Data[_key39] = _val40
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *UserExtAttrMapResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserExtAttrMapResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserExtAttrMapResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserExtAttrMapResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserExtAttrMapResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserExtAttrMapResponse) Equals(other *UserExtAttrMapResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src41 := other.Data[k]
    if !_tgt.Equals(_src41) { return false }
  }
  return true
}

func (p *UserExtAttrMapResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserExtAttrMapResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type UserExtAttrResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserExtAttrVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewUserExtAttrResponse() *UserExtAttrResponse {
  return &UserExtAttrResponse{}
}


func (p *UserExtAttrResponse) GetRet() int32 {
  return p.Ret
}

func (p *UserExtAttrResponse) GetMsg() string {
  return p.Msg
}
var UserExtAttrResponse_Data_DEFAULT *UserExtAttrVO
func (p *UserExtAttrResponse) GetData() *UserExtAttrVO {
  if !p.IsSetData() {
    return UserExtAttrResponse_Data_DEFAULT
  }
return p.Data
}
func (p *UserExtAttrResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *UserExtAttrResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *UserExtAttrResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *UserExtAttrResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserExtAttrResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserExtAttrVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserExtAttrResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserExtAttrResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserExtAttrResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *UserExtAttrResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserExtAttrResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *UserExtAttrResponse) Equals(other *UserExtAttrResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserExtAttrResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserExtAttrResponse(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
type ResponseMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
}

func NewResponseMsg() *ResponseMsg {
  return &ResponseMsg{}
}

var ResponseMsg_Ret_DEFAULT int32
func (p *ResponseMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResponseMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResponseMsg_Msg_DEFAULT string
func (p *ResponseMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResponseMsg_Msg_DEFAULT
  }
return *p.Msg
}
func (p *ResponseMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResponseMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResponseMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResponseMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResponseMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResponseMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResponseMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResponseMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResponseMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResponseMsg) Equals(other *ResponseMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  return true
}

func (p *ResponseMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResponseMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResLongMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data *int64 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResLongMsg() *ResLongMsg {
  return &ResLongMsg{}
}

var ResLongMsg_Ret_DEFAULT int32
func (p *ResLongMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResLongMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResLongMsg_Msg_DEFAULT string
func (p *ResLongMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResLongMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResLongMsg_Data_DEFAULT int64
func (p *ResLongMsg) GetData() int64 {
  if !p.IsSetData() {
    return ResLongMsg_Data_DEFAULT
  }
return *p.Data
}
func (p *ResLongMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResLongMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResLongMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResLongMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResLongMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResLongMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResLongMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResLongMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResLongMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResLongMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResLongMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResLongMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.I64, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResLongMsg) Equals(other *ResLongMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResLongMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResLongMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResLongListMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data []int64 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResLongListMsg() *ResLongListMsg {
  return &ResLongListMsg{}
}

var ResLongListMsg_Ret_DEFAULT int32
func (p *ResLongListMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResLongListMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResLongListMsg_Msg_DEFAULT string
func (p *ResLongListMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResLongListMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResLongListMsg_Data_DEFAULT []int64

func (p *ResLongListMsg) GetData() []int64 {
  return p.Data
}
func (p *ResLongListMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResLongListMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResLongListMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResLongListMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResLongListMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResLongListMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResLongListMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
var _elem42 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem42 = v
}
    p.Data = append(p.Data, _elem42)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ResLongListMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResLongListMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResLongListMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResLongListMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResLongListMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResLongListMsg) Equals(other *ResLongListMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src43 := other.Data[i]
    if _tgt != _src43 { return false }
  }
  return true
}

func (p *ResLongListMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResLongListMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResIntegerMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data *int32 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResIntegerMsg() *ResIntegerMsg {
  return &ResIntegerMsg{}
}

var ResIntegerMsg_Ret_DEFAULT int32
func (p *ResIntegerMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResIntegerMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResIntegerMsg_Msg_DEFAULT string
func (p *ResIntegerMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResIntegerMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResIntegerMsg_Data_DEFAULT int32
func (p *ResIntegerMsg) GetData() int32 {
  if !p.IsSetData() {
    return ResIntegerMsg_Data_DEFAULT
  }
return *p.Data
}
func (p *ResIntegerMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResIntegerMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResIntegerMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResIntegerMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResIntegerMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResIntegerMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResIntegerMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResIntegerMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResIntegerMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResIntegerMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResIntegerMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResIntegerMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResIntegerMsg) Equals(other *ResIntegerMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResIntegerMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResIntegerMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResLongIntegerMapMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data map[int64]int32 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResLongIntegerMapMsg() *ResLongIntegerMapMsg {
  return &ResLongIntegerMapMsg{}
}

var ResLongIntegerMapMsg_Ret_DEFAULT int32
func (p *ResLongIntegerMapMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResLongIntegerMapMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResLongIntegerMapMsg_Msg_DEFAULT string
func (p *ResLongIntegerMapMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResLongIntegerMapMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResLongIntegerMapMsg_Data_DEFAULT map[int64]int32

func (p *ResLongIntegerMapMsg) GetData() map[int64]int32 {
  return p.Data
}
func (p *ResLongIntegerMapMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResLongIntegerMapMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResLongIntegerMapMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResLongIntegerMapMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResLongIntegerMapMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResLongIntegerMapMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResLongIntegerMapMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]int32, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key44 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key44 = v
}
var _val45 int32
    if v, err := iprot.ReadI32(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val45 = v
}
    p.Data[_key44] = _val45
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *ResLongIntegerMapMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResLongIntegerMapMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResLongIntegerMapMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResLongIntegerMapMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResLongIntegerMapMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.I32, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := oprot.WriteI32(ctx, int32(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResLongIntegerMapMsg) Equals(other *ResLongIntegerMapMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src46 := other.Data[k]
    if _tgt != _src46 { return false }
  }
  return true
}

func (p *ResLongIntegerMapMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResLongIntegerMapMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResStrBoolMapMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data map[string]bool `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResStrBoolMapMsg() *ResStrBoolMapMsg {
  return &ResStrBoolMapMsg{}
}

var ResStrBoolMapMsg_Ret_DEFAULT int32
func (p *ResStrBoolMapMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResStrBoolMapMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResStrBoolMapMsg_Msg_DEFAULT string
func (p *ResStrBoolMapMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResStrBoolMapMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResStrBoolMapMsg_Data_DEFAULT map[string]bool

func (p *ResStrBoolMapMsg) GetData() map[string]bool {
  return p.Data
}
func (p *ResStrBoolMapMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResStrBoolMapMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResStrBoolMapMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResStrBoolMapMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResStrBoolMapMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResStrBoolMapMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResStrBoolMapMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[string]bool, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key47 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key47 = v
}
var _val48 bool
    if v, err := iprot.ReadBool(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val48 = v
}
    p.Data[_key47] = _val48
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *ResStrBoolMapMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResStrBoolMapMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResStrBoolMapMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResStrBoolMapMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResStrBoolMapMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.BOOL, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteString(ctx, string(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := oprot.WriteBool(ctx, bool(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResStrBoolMapMsg) Equals(other *ResStrBoolMapMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src49 := other.Data[k]
    if _tgt != _src49 { return false }
  }
  return true
}

func (p *ResStrBoolMapMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResStrBoolMapMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResIntegerListMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data []int32 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResIntegerListMsg() *ResIntegerListMsg {
  return &ResIntegerListMsg{}
}

var ResIntegerListMsg_Ret_DEFAULT int32
func (p *ResIntegerListMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResIntegerListMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResIntegerListMsg_Msg_DEFAULT string
func (p *ResIntegerListMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResIntegerListMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResIntegerListMsg_Data_DEFAULT []int32

func (p *ResIntegerListMsg) GetData() []int32 {
  return p.Data
}
func (p *ResIntegerListMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResIntegerListMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResIntegerListMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResIntegerListMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResIntegerListMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResIntegerListMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResIntegerListMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int32, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
var _elem50 int32
    if v, err := iprot.ReadI32(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem50 = v
}
    p.Data = append(p.Data, _elem50)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ResIntegerListMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResIntegerListMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResIntegerListMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResIntegerListMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResIntegerListMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.I32, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := oprot.WriteI32(ctx, int32(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResIntegerListMsg) Equals(other *ResIntegerListMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src51 := other.Data[i]
    if _tgt != _src51 { return false }
  }
  return true
}

func (p *ResIntegerListMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResIntegerListMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResStringMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data *string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResStringMsg() *ResStringMsg {
  return &ResStringMsg{}
}

var ResStringMsg_Ret_DEFAULT int32
func (p *ResStringMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResStringMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResStringMsg_Msg_DEFAULT string
func (p *ResStringMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResStringMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResStringMsg_Data_DEFAULT string
func (p *ResStringMsg) GetData() string {
  if !p.IsSetData() {
    return ResStringMsg_Data_DEFAULT
  }
return *p.Data
}
func (p *ResStringMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResStringMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResStringMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResStringMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResStringMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResStringMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResStringMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResStringMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResStringMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResStringMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResStringMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResStringMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResStringMsg) Equals(other *ResStringMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResStringMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResStringMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResStringListMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data []string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResStringListMsg() *ResStringListMsg {
  return &ResStringListMsg{}
}

var ResStringListMsg_Ret_DEFAULT int32
func (p *ResStringListMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResStringListMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResStringListMsg_Msg_DEFAULT string
func (p *ResStringListMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResStringListMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResStringListMsg_Data_DEFAULT []string

func (p *ResStringListMsg) GetData() []string {
  return p.Data
}
func (p *ResStringListMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResStringListMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResStringListMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResStringListMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResStringListMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResStringListMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResStringListMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]string, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
var _elem52 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem52 = v
}
    p.Data = append(p.Data, _elem52)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ResStringListMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResStringListMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResStringListMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResStringListMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResStringListMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResStringListMsg) Equals(other *ResStringListMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src53 := other.Data[i]
    if _tgt != _src53 { return false }
  }
  return true
}

func (p *ResStringListMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResStringListMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResBooleanMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data *bool `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResBooleanMsg() *ResBooleanMsg {
  return &ResBooleanMsg{}
}

var ResBooleanMsg_Ret_DEFAULT int32
func (p *ResBooleanMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResBooleanMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResBooleanMsg_Msg_DEFAULT string
func (p *ResBooleanMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResBooleanMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResBooleanMsg_Data_DEFAULT bool
func (p *ResBooleanMsg) GetData() bool {
  if !p.IsSetData() {
    return ResBooleanMsg_Data_DEFAULT
  }
return *p.Data
}
func (p *ResBooleanMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResBooleanMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResBooleanMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResBooleanMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResBooleanMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResBooleanMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResBooleanMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResBooleanMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResBooleanMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResBooleanMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResBooleanMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResBooleanMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteBool(ctx, bool(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResBooleanMsg) Equals(other *ResBooleanMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResBooleanMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResBooleanMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type ResIDMapMsg struct {
  Ret *int32 `thrift:"ret,1" db:"ret" json:"ret,omitempty"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data map[int64]int64 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResIDMapMsg() *ResIDMapMsg {
  return &ResIDMapMsg{}
}

var ResIDMapMsg_Ret_DEFAULT int32
func (p *ResIDMapMsg) GetRet() int32 {
  if !p.IsSetRet() {
    return ResIDMapMsg_Ret_DEFAULT
  }
return *p.Ret
}
var ResIDMapMsg_Msg_DEFAULT string
func (p *ResIDMapMsg) GetMsg() string {
  if !p.IsSetMsg() {
    return ResIDMapMsg_Msg_DEFAULT
  }
return *p.Msg
}
var ResIDMapMsg_Data_DEFAULT map[int64]int64

func (p *ResIDMapMsg) GetData() map[int64]int64 {
  return p.Data
}
func (p *ResIDMapMsg) IsSetRet() bool {
  return p.Ret != nil
}

func (p *ResIDMapMsg) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResIDMapMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResIDMapMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResIDMapMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = &v
}
  return nil
}

func (p *ResIDMapMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResIDMapMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]int64, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key54 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key54 = v
}
var _val55 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val55 = v
}
    p.Data[_key54] = _val55
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *ResIDMapMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResIDMapMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResIDMapMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRet() {
    if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Ret)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  }
  return err
}

func (p *ResIDMapMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResIDMapMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.I64, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := oprot.WriteI64(ctx, int64(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResIDMapMsg) Equals(other *ResIDMapMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret {
    if p.Ret == nil || other.Ret == nil {
      return false
    }
    if (*p.Ret) != (*other.Ret) { return false }
  }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src56 := other.Data[k]
    if _tgt != _src56 { return false }
  }
  return true
}

func (p *ResIDMapMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResIDMapMsg(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type BaseUserInfoListResponse struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*BaseUserInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewBaseUserInfoListResponse() *BaseUserInfoListResponse {
  return &BaseUserInfoListResponse{}
}


func (p *BaseUserInfoListResponse) GetRet() int32 {
  return p.Ret
}

func (p *BaseUserInfoListResponse) GetMsg() string {
  return p.Msg
}
var BaseUserInfoListResponse_Data_DEFAULT []*BaseUserInfo

func (p *BaseUserInfoListResponse) GetData() []*BaseUserInfo {
  return p.Data
}
func (p *BaseUserInfoListResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *BaseUserInfoListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *BaseUserInfoListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *BaseUserInfoListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *BaseUserInfoListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*BaseUserInfo, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem57 := &BaseUserInfo{}
    if err := _elem57.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem57), err)
    }
    p.Data = append(p.Data, _elem57)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *BaseUserInfoListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BaseUserInfoListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BaseUserInfoListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *BaseUserInfoListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *BaseUserInfoListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *BaseUserInfoListResponse) Equals(other *BaseUserInfoListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src58 := other.Data[i]
    if !_tgt.Equals(_src58) { return false }
  }
  return true
}

func (p *BaseUserInfoListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BaseUserInfoListResponse(%+v)", *p)
}

// Attributes:
//  - UserName: 用户名
//  - Email: 邮件
//  - KugouId: kugouId
//  - NickName: 昵称
//  - UserLogo: 用户头像
//  - CoinSpend: 消耗的星币，财富等级累计数，richLevel的经验值
//  - CoinTotal: 收到的星币[累计]，明星等级累计数，starLevel经验值
//  - StarLevel: 主播等级
//  - RichLevel: 财富等级
//  - Sex: 性别(0:保密,1:男,2:女)
//  - UserId: 用户id
type BaseUserInfo struct {
  UserName *string `thrift:"userName,1" db:"userName" json:"userName,omitempty"`
  Email *string `thrift:"email,2" db:"email" json:"email,omitempty"`
  KugouId *int64 `thrift:"kugouId,3" db:"kugouId" json:"kugouId,omitempty"`
  NickName *string `thrift:"nickName,4" db:"nickName" json:"nickName,omitempty"`
  UserLogo *string `thrift:"userLogo,5" db:"userLogo" json:"userLogo,omitempty"`
  CoinSpend *float64 `thrift:"coinSpend,6" db:"coinSpend" json:"coinSpend,omitempty"`
  CoinTotal *float64 `thrift:"coinTotal,7" db:"coinTotal" json:"coinTotal,omitempty"`
  StarLevel *int32 `thrift:"starLevel,8" db:"starLevel" json:"starLevel,omitempty"`
  RichLevel *int32 `thrift:"richLevel,9" db:"richLevel" json:"richLevel,omitempty"`
  Sex *int32 `thrift:"sex,10" db:"sex" json:"sex,omitempty"`
  UserId *int64 `thrift:"userId,11" db:"userId" json:"userId,omitempty"`
}

func NewBaseUserInfo() *BaseUserInfo {
  return &BaseUserInfo{}
}

var BaseUserInfo_UserName_DEFAULT string
func (p *BaseUserInfo) GetUserName() string {
  if !p.IsSetUserName() {
    return BaseUserInfo_UserName_DEFAULT
  }
return *p.UserName
}
var BaseUserInfo_Email_DEFAULT string
func (p *BaseUserInfo) GetEmail() string {
  if !p.IsSetEmail() {
    return BaseUserInfo_Email_DEFAULT
  }
return *p.Email
}
var BaseUserInfo_KugouId_DEFAULT int64
func (p *BaseUserInfo) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return BaseUserInfo_KugouId_DEFAULT
  }
return *p.KugouId
}
var BaseUserInfo_NickName_DEFAULT string
func (p *BaseUserInfo) GetNickName() string {
  if !p.IsSetNickName() {
    return BaseUserInfo_NickName_DEFAULT
  }
return *p.NickName
}
var BaseUserInfo_UserLogo_DEFAULT string
func (p *BaseUserInfo) GetUserLogo() string {
  if !p.IsSetUserLogo() {
    return BaseUserInfo_UserLogo_DEFAULT
  }
return *p.UserLogo
}
var BaseUserInfo_CoinSpend_DEFAULT float64
func (p *BaseUserInfo) GetCoinSpend() float64 {
  if !p.IsSetCoinSpend() {
    return BaseUserInfo_CoinSpend_DEFAULT
  }
return *p.CoinSpend
}
var BaseUserInfo_CoinTotal_DEFAULT float64
func (p *BaseUserInfo) GetCoinTotal() float64 {
  if !p.IsSetCoinTotal() {
    return BaseUserInfo_CoinTotal_DEFAULT
  }
return *p.CoinTotal
}
var BaseUserInfo_StarLevel_DEFAULT int32
func (p *BaseUserInfo) GetStarLevel() int32 {
  if !p.IsSetStarLevel() {
    return BaseUserInfo_StarLevel_DEFAULT
  }
return *p.StarLevel
}
var BaseUserInfo_RichLevel_DEFAULT int32
func (p *BaseUserInfo) GetRichLevel() int32 {
  if !p.IsSetRichLevel() {
    return BaseUserInfo_RichLevel_DEFAULT
  }
return *p.RichLevel
}
var BaseUserInfo_Sex_DEFAULT int32
func (p *BaseUserInfo) GetSex() int32 {
  if !p.IsSetSex() {
    return BaseUserInfo_Sex_DEFAULT
  }
return *p.Sex
}
var BaseUserInfo_UserId_DEFAULT int64
func (p *BaseUserInfo) GetUserId() int64 {
  if !p.IsSetUserId() {
    return BaseUserInfo_UserId_DEFAULT
  }
return *p.UserId
}
func (p *BaseUserInfo) IsSetUserName() bool {
  return p.UserName != nil
}

func (p *BaseUserInfo) IsSetEmail() bool {
  return p.Email != nil
}

func (p *BaseUserInfo) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *BaseUserInfo) IsSetNickName() bool {
  return p.NickName != nil
}

func (p *BaseUserInfo) IsSetUserLogo() bool {
  return p.UserLogo != nil
}

func (p *BaseUserInfo) IsSetCoinSpend() bool {
  return p.CoinSpend != nil
}

func (p *BaseUserInfo) IsSetCoinTotal() bool {
  return p.CoinTotal != nil
}

func (p *BaseUserInfo) IsSetStarLevel() bool {
  return p.StarLevel != nil
}

func (p *BaseUserInfo) IsSetRichLevel() bool {
  return p.RichLevel != nil
}

func (p *BaseUserInfo) IsSetSex() bool {
  return p.Sex != nil
}

func (p *BaseUserInfo) IsSetUserId() bool {
  return p.UserId != nil
}

func (p *BaseUserInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *BaseUserInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.UserName = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Email = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.NickName = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.UserLogo = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.CoinSpend = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.CoinTotal = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.StarLevel = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.RichLevel = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *BaseUserInfo)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.UserId = &v
}
  return nil
}

func (p *BaseUserInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BaseUserInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BaseUserInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserName() {
    if err := oprot.WriteFieldBegin(ctx, "userName", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userName (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userName: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetEmail() {
    if err := oprot.WriteFieldBegin(ctx, "email", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:email: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Email)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.email (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:email: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:kugouId: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNickName() {
    if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:nickName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NickName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.nickName (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:nickName: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserLogo() {
    if err := oprot.WriteFieldBegin(ctx, "userLogo", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:userLogo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserLogo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userLogo (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:userLogo: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinSpend() {
    if err := oprot.WriteFieldBegin(ctx, "coinSpend", thrift.DOUBLE, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:coinSpend: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinSpend)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinSpend (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:coinSpend: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCoinTotal() {
    if err := oprot.WriteFieldBegin(ctx, "coinTotal", thrift.DOUBLE, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:coinTotal: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.CoinTotal)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.coinTotal (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:coinTotal: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarLevel() {
    if err := oprot.WriteFieldBegin(ctx, "starLevel", thrift.I32, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:starLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StarLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starLevel (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:starLevel: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichLevel() {
    if err := oprot.WriteFieldBegin(ctx, "richLevel", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:richLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RichLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richLevel (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:richLevel: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:sex: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserId() {
    if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:userId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.UserId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userId (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:userId: ", p), err) }
  }
  return err
}

func (p *BaseUserInfo) Equals(other *BaseUserInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.UserName != other.UserName {
    if p.UserName == nil || other.UserName == nil {
      return false
    }
    if (*p.UserName) != (*other.UserName) { return false }
  }
  if p.Email != other.Email {
    if p.Email == nil || other.Email == nil {
      return false
    }
    if (*p.Email) != (*other.Email) { return false }
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.NickName != other.NickName {
    if p.NickName == nil || other.NickName == nil {
      return false
    }
    if (*p.NickName) != (*other.NickName) { return false }
  }
  if p.UserLogo != other.UserLogo {
    if p.UserLogo == nil || other.UserLogo == nil {
      return false
    }
    if (*p.UserLogo) != (*other.UserLogo) { return false }
  }
  if p.CoinSpend != other.CoinSpend {
    if p.CoinSpend == nil || other.CoinSpend == nil {
      return false
    }
    if (*p.CoinSpend) != (*other.CoinSpend) { return false }
  }
  if p.CoinTotal != other.CoinTotal {
    if p.CoinTotal == nil || other.CoinTotal == nil {
      return false
    }
    if (*p.CoinTotal) != (*other.CoinTotal) { return false }
  }
  if p.StarLevel != other.StarLevel {
    if p.StarLevel == nil || other.StarLevel == nil {
      return false
    }
    if (*p.StarLevel) != (*other.StarLevel) { return false }
  }
  if p.RichLevel != other.RichLevel {
    if p.RichLevel == nil || other.RichLevel == nil {
      return false
    }
    if (*p.RichLevel) != (*other.RichLevel) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.UserId != other.UserId {
    if p.UserId == nil || other.UserId == nil {
      return false
    }
    if (*p.UserId) != (*other.UserId) { return false }
  }
  return true
}

func (p *BaseUserInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BaseUserInfo(%+v)", *p)
}

