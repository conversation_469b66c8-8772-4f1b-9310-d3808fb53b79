// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package userv2service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	userv2vo "kugou_adapter_service/third-party/gen-go/userv2service/userv2vo"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal


type UserModuleV2StarAndRichLevelService interface { //#########################
	//fansCount	int(11)	粉丝数量
	// followCount	int(11)	关注用户数量
	// xxCount	bigint(20)	星星总数量
	//giftCount	int(11)	礼物排名
	//@以上字段用户模块数据不准，建议走各子系统@
	//######################################
	// 查询消息服务,所有批量接口参数最大100
	//-------------------------------------
	//所有接口错误码
	// SUC("suc", 0),
	// PARAMS_ERROR("para error", 3010001),
	// QUERY_FAILED("query DB failed", 3010002),
	// INVOKE_OTHER_SERVICE_FAILED("invoke other service failed or cannot foud data!", 3010003),
	// QUERY_NULL_DATA("cannot found data", 3010004);
	//############################################

	GetStarAndRichLevelConfig(ctx context.Context) (_r *userv2vo.LevelMapResponse, _err error)
	// Parameters:
	//  - Level
	GetRichLevelDetail(ctx context.Context, level int32) (_r *userv2vo.RichLevelDetailResponse, _err error)
}

// #########################
// fansCount	int(11)	粉丝数量
// followCount	int(11)	关注用户数量
// xxCount	bigint(20)	星星总数量
// giftCount	int(11)	礼物排名
// @以上字段用户模块数据不准，建议走各子系统@
// ######################################
// 查询消息服务,所有批量接口参数最大100
// -------------------------------------
// 所有接口错误码
// SUC("suc", 0),
// PARAMS_ERROR("para error", 3010001),
// QUERY_FAILED("query DB failed", 3010002),
// INVOKE_OTHER_SERVICE_FAILED("invoke other service failed or cannot foud data!", 3010003),
// QUERY_NULL_DATA("cannot found data", 3010004);
// ############################################
type UserModuleV2StarAndRichLevelServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewUserModuleV2StarAndRichLevelServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UserModuleV2StarAndRichLevelServiceClient {
	return &UserModuleV2StarAndRichLevelServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewUserModuleV2StarAndRichLevelServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UserModuleV2StarAndRichLevelServiceClient {
	return &UserModuleV2StarAndRichLevelServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewUserModuleV2StarAndRichLevelServiceClient(c thrift.TClient) *UserModuleV2StarAndRichLevelServiceClient {
	return &UserModuleV2StarAndRichLevelServiceClient{
		c: c,
	}
}

func (p *UserModuleV2StarAndRichLevelServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *UserModuleV2StarAndRichLevelServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *UserModuleV2StarAndRichLevelServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

func (p *UserModuleV2StarAndRichLevelServiceClient) GetStarAndRichLevelConfig(ctx context.Context) (_r *userv2vo.LevelMapResponse, _err error) {
	var _args0 UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs
	var _result2 UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult
	var _meta1 thrift.ResponseMeta
	_meta1, _err = p.Client_().Call(ctx, "getStarAndRichLevelConfig", &_args0, &_result2)
	p.SetLastResponseMeta_(_meta1)
	if _err != nil {
		return
	}
	if _ret3 := _result2.GetSuccess(); _ret3 != nil {
		return _ret3, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarAndRichLevelConfig failed: unknown result")
}

// Parameters:
//   - Level
func (p *UserModuleV2StarAndRichLevelServiceClient) GetRichLevelDetail(ctx context.Context, level int32) (_r *userv2vo.RichLevelDetailResponse, _err error) {
	var _args4 UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs
	_args4.Level = level
	var _result6 UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult
	var _meta5 thrift.ResponseMeta
	_meta5, _err = p.Client_().Call(ctx, "getRichLevelDetail", &_args4, &_result6)
	p.SetLastResponseMeta_(_meta5)
	if _err != nil {
		return
	}
	if _ret7 := _result6.GetSuccess(); _ret7 != nil {
		return _ret7, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getRichLevelDetail failed: unknown result")
}

type UserModuleV2StarAndRichLevelServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UserModuleV2StarAndRichLevelService
}

func (p *UserModuleV2StarAndRichLevelServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UserModuleV2StarAndRichLevelServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UserModuleV2StarAndRichLevelServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUserModuleV2StarAndRichLevelServiceProcessor(handler UserModuleV2StarAndRichLevelService) *UserModuleV2StarAndRichLevelServiceProcessor {

	self8 := &UserModuleV2StarAndRichLevelServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self8.processorMap["getStarAndRichLevelConfig"] = &userModuleV2StarAndRichLevelServiceProcessorGetStarAndRichLevelConfig{handler: handler}
	self8.processorMap["getRichLevelDetail"] = &userModuleV2StarAndRichLevelServiceProcessorGetRichLevelDetail{handler: handler}
	return self8
}

func (p *UserModuleV2StarAndRichLevelServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x9 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x9.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x9

}

type userModuleV2StarAndRichLevelServiceProcessorGetStarAndRichLevelConfig struct {
	handler UserModuleV2StarAndRichLevelService
}

func (p *userModuleV2StarAndRichLevelServiceProcessorGetStarAndRichLevelConfig) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err10 error
	args := UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getStarAndRichLevelConfig", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult{}
	if retval, err2 := p.handler.GetStarAndRichLevelConfig(ctx); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc11 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarAndRichLevelConfig: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getStarAndRichLevelConfig", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if err2 := _exc11.Write(ctx, oprot); _write_err10 == nil && err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err10 == nil && err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err10 == nil && err2 != nil {
			_write_err10 = thrift.WrapTException(err2)
		}
		if _write_err10 != nil {
			return false, thrift.WrapTException(_write_err10)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getStarAndRichLevelConfig", thrift.REPLY, seqId); err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err10 == nil && err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err10 == nil && err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err10 == nil && err2 != nil {
		_write_err10 = thrift.WrapTException(err2)
	}
	if _write_err10 != nil {
		return false, thrift.WrapTException(_write_err10)
	}
	return true, err
}

type userModuleV2StarAndRichLevelServiceProcessorGetRichLevelDetail struct {
	handler UserModuleV2StarAndRichLevelService
}

func (p *userModuleV2StarAndRichLevelServiceProcessorGetRichLevelDetail) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err12 error
	args := UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getRichLevelDetail", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult{}
	if retval, err2 := p.handler.GetRichLevelDetail(ctx, args.Level); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc13 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRichLevelDetail: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getRichLevelDetail", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if err2 := _exc13.Write(ctx, oprot); _write_err12 == nil && err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err12 == nil && err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err12 == nil && err2 != nil {
			_write_err12 = thrift.WrapTException(err2)
		}
		if _write_err12 != nil {
			return false, thrift.WrapTException(_write_err12)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getRichLevelDetail", thrift.REPLY, seqId); err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err12 == nil && err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err12 == nil && err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err12 == nil && err2 != nil {
		_write_err12 = thrift.WrapTException(err2)
	}
	if _write_err12 != nil {
		return false, thrift.WrapTException(_write_err12)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs struct {
}

func NewUserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs() *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs {
	return &UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs{}
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.Skip(ctx, fieldTypeId); err != nil {
			return err
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getStarAndRichLevelConfig_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult struct {
	Success *userv2vo.LevelMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult() *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult {
	return &UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult{}
}

var UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult_Success_DEFAULT *userv2vo.LevelMapResponse

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) GetSuccess() *userv2vo.LevelMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.LevelMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getStarAndRichLevelConfig_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2StarAndRichLevelServiceGetStarAndRichLevelConfigResult(%+v)", *p)
}

// Attributes:
//   - Level
type UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs struct {
	Level int32 `thrift:"level,1,required" db:"level" json:"level"`
}

func NewUserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs() *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs {
	return &UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs{}
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs) GetLevel() int32 {
	return p.Level
}
func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetLevel bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetLevel = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetLevel {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Level is not set"))
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Level = v
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getRichLevelDetail_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "level", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:level: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Level)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.level (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:level: ", p), err)
	}
	return err
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2StarAndRichLevelServiceGetRichLevelDetailArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult struct {
	Success *userv2vo.RichLevelDetailResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult() *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult {
	return &UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult{}
}

var UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult_Success_DEFAULT *userv2vo.RichLevelDetailResponse

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) GetSuccess() *userv2vo.RichLevelDetailResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.RichLevelDetailResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getRichLevelDetail_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2StarAndRichLevelServiceGetRichLevelDetailResult(%+v)", *p)
}

type UserModuleV2BizService interface {
	// Parameters:
	//  - KugouId
	GetUserByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserListResponse, _err error)
	// 根据酷狗ID列表获取用户信息List列表-事件通知更新，默认5分钟缓存 空10s，kugouIds最大长度30
	//
	// Parameters:
	//  - KugouIds: 最大长度30
	GetBaseUserInfoList(ctx context.Context, kugouIds []int64) (_r *userv2vo.BaseUserInfoListResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserMapResponse, _err error)
	// Parameters:
	//  - KugouId
	GetUserAttrByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserAttrResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserAttrListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserAttrListResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserAttrMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserAttrMapResponse, _err error)
	// Parameters:
	//  - KugouId
	GetUserDataByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserDataResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserDataListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserDataListResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserDataMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserDataMapResponse, _err error)
	// Parameters:
	//  - KugouId
	GetUserExtAttrByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserExtAttrResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserExtAttrListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserExtAttrListResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserExtAttrMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserExtAttrMapResponse, _err error)
	// Parameters:
	//  - UserName
	GetUserByUserName(ctx context.Context, userName string) (_r *userv2vo.UserResponse, _err error)
	// Parameters:
	//  - UserNameList
	GetUserMapByUserNameList(ctx context.Context, userNameList []string) (_r *userv2vo.UserStrMapResponse, _err error)
	// Parameters:
	//  - NickName
	GetUserByNickName(ctx context.Context, nickName string) (_r *userv2vo.UserResponse, _err error)
	// Parameters:
	//  - NickNameList
	GetUserMapByNickNameList(ctx context.Context, nickNameList []string) (_r *userv2vo.UserStrMapResponse, _err error)
	// Parameters:
	//  - NickName
	IsExistNickName(ctx context.Context, nickName string) (_r *userv2vo.ResBooleanMsg, _err error)
	// Parameters:
	//  - NickNames
	IsExistNickNameList(ctx context.Context, nickNames []string) (_r *userv2vo.ResStringListMsg, _err error)
	// Parameters:
	//  - KugouIds
	GetUserConstellationByKugouIds(ctx context.Context, kugouIds []int64) (_r *userv2vo.ResLongIntegerMapMsg, _err error)
	// Parameters:
	//  - KugouId
	GetUserCoreInfoByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserCoreInfoResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserCoreInfoListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCoreInfoListResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetCoreInfoMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCoreInfoMapResponse, _err error)
	// Parameters:
	//  - KugouId
	GetUserCombineInfoByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserCombineInfoResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetUserCombineInfoListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCombineInfoListResponse, _err error)
	// Parameters:
	//  - KugouIdList
	GetCombineInfoMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCombineInfoMapResponse, _err error)
	// Parameters:
	//  - KugouIdList
	//  - DynamicUserVO
	GetDynamicUserInfoByKugouIdList(ctx context.Context, kugouIdList []int64, dynamicUserVO *userv2vo.DynamicUserVO) (_r *userv2vo.UserDynamicResponse, _err error)
	// Parameters:
	//  - KugouIds
	GetUserIdMappingByKugouIds(ctx context.Context, kugouIds []int64) (_r *userv2vo.ResIDMapMsg, _err error)
	// Parameters:
	//  - UserIds
	GetKugouIdMappingByUserIds(ctx context.Context, userIds []int64) (_r *userv2vo.ResIDMapMsg, _err error)
	// Parameters:
	//  - KugouId
	GetUserIdByKugouId(ctx context.Context, kugouId int64) (_r int64, _err error)
	// Parameters:
	//  - UserId
	GetKugouIdByUserId(ctx context.Context, userId int64) (_r int64, _err error)
}

type UserModuleV2BizServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewUserModuleV2BizServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UserModuleV2BizServiceClient {
	return &UserModuleV2BizServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewUserModuleV2BizServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UserModuleV2BizServiceClient {
	return &UserModuleV2BizServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewUserModuleV2BizServiceClient(c thrift.TClient) *UserModuleV2BizServiceClient {
	return &UserModuleV2BizServiceClient{
		c: c,
	}
}

func (p *UserModuleV2BizServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *UserModuleV2BizServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *UserModuleV2BizServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserResponse, _err error) {
	var _args15 UserModuleV2BizServiceGetUserByKugouIdArgs
	_args15.KugouId = kugouId
	var _result17 UserModuleV2BizServiceGetUserByKugouIdResult
	var _meta16 thrift.ResponseMeta
	_meta16, _err = p.Client_().Call(ctx, "getUserByKugouId", &_args15, &_result17)
	p.SetLastResponseMeta_(_meta16)
	if _err != nil {
		return
	}
	if _ret18 := _result17.GetSuccess(); _ret18 != nil {
		return _ret18, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserByKugouId failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserListResponse, _err error) {
	var _args19 UserModuleV2BizServiceGetUserListByKugouIdListArgs
	_args19.KugouIdList = kugouIdList
	var _result21 UserModuleV2BizServiceGetUserListByKugouIdListResult
	var _meta20 thrift.ResponseMeta
	_meta20, _err = p.Client_().Call(ctx, "getUserListByKugouIdList", &_args19, &_result21)
	p.SetLastResponseMeta_(_meta20)
	if _err != nil {
		return
	}
	if _ret22 := _result21.GetSuccess(); _ret22 != nil {
		return _ret22, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserListByKugouIdList failed: unknown result")
}

// 根据酷狗ID列表获取用户信息List列表-事件通知更新，默认5分钟缓存 空10s，kugouIds最大长度30
//
// Parameters:
//   - KugouIds: 最大长度30
func (p *UserModuleV2BizServiceClient) GetBaseUserInfoList(ctx context.Context, kugouIds []int64) (_r *userv2vo.BaseUserInfoListResponse, _err error) {
	var _args23 UserModuleV2BizServiceGetBaseUserInfoListArgs
	_args23.KugouIds = kugouIds
	var _result25 UserModuleV2BizServiceGetBaseUserInfoListResult
	var _meta24 thrift.ResponseMeta
	_meta24, _err = p.Client_().Call(ctx, "getBaseUserInfoList", &_args23, &_result25)
	p.SetLastResponseMeta_(_meta24)
	if _err != nil {
		return
	}
	if _ret26 := _result25.GetSuccess(); _ret26 != nil {
		return _ret26, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getBaseUserInfoList failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserMapResponse, _err error) {
	var _args27 UserModuleV2BizServiceGetUserMapByKugouIdListArgs
	_args27.KugouIdList = kugouIdList
	var _result29 UserModuleV2BizServiceGetUserMapByKugouIdListResult
	var _meta28 thrift.ResponseMeta
	_meta28, _err = p.Client_().Call(ctx, "getUserMapByKugouIdList", &_args27, &_result29)
	p.SetLastResponseMeta_(_meta28)
	if _err != nil {
		return
	}
	if _ret30 := _result29.GetSuccess(); _ret30 != nil {
		return _ret30, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserMapByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserAttrByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserAttrResponse, _err error) {
	var _args31 UserModuleV2BizServiceGetUserAttrByKugouIdArgs
	_args31.KugouId = kugouId
	var _result33 UserModuleV2BizServiceGetUserAttrByKugouIdResult
	var _meta32 thrift.ResponseMeta
	_meta32, _err = p.Client_().Call(ctx, "getUserAttrByKugouId", &_args31, &_result33)
	p.SetLastResponseMeta_(_meta32)
	if _err != nil {
		return
	}
	if _ret34 := _result33.GetSuccess(); _ret34 != nil {
		return _ret34, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserAttrByKugouId failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserAttrListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserAttrListResponse, _err error) {
	var _args35 UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs
	_args35.KugouIdList = kugouIdList
	var _result37 UserModuleV2BizServiceGetUserAttrListByKugouIdListResult
	var _meta36 thrift.ResponseMeta
	_meta36, _err = p.Client_().Call(ctx, "getUserAttrListByKugouIdList", &_args35, &_result37)
	p.SetLastResponseMeta_(_meta36)
	if _err != nil {
		return
	}
	if _ret38 := _result37.GetSuccess(); _ret38 != nil {
		return _ret38, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserAttrListByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserAttrMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserAttrMapResponse, _err error) {
	var _args39 UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs
	_args39.KugouIdList = kugouIdList
	var _result41 UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult
	var _meta40 thrift.ResponseMeta
	_meta40, _err = p.Client_().Call(ctx, "getUserAttrMapByKugouIdList", &_args39, &_result41)
	p.SetLastResponseMeta_(_meta40)
	if _err != nil {
		return
	}
	if _ret42 := _result41.GetSuccess(); _ret42 != nil {
		return _ret42, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserAttrMapByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserDataByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserDataResponse, _err error) {
	var _args43 UserModuleV2BizServiceGetUserDataByKugouIdArgs
	_args43.KugouId = kugouId
	var _result45 UserModuleV2BizServiceGetUserDataByKugouIdResult
	var _meta44 thrift.ResponseMeta
	_meta44, _err = p.Client_().Call(ctx, "getUserDataByKugouId", &_args43, &_result45)
	p.SetLastResponseMeta_(_meta44)
	if _err != nil {
		return
	}
	if _ret46 := _result45.GetSuccess(); _ret46 != nil {
		return _ret46, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserDataByKugouId failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserDataListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserDataListResponse, _err error) {
	var _args47 UserModuleV2BizServiceGetUserDataListByKugouIdListArgs
	_args47.KugouIdList = kugouIdList
	var _result49 UserModuleV2BizServiceGetUserDataListByKugouIdListResult
	var _meta48 thrift.ResponseMeta
	_meta48, _err = p.Client_().Call(ctx, "getUserDataListByKugouIdList", &_args47, &_result49)
	p.SetLastResponseMeta_(_meta48)
	if _err != nil {
		return
	}
	if _ret50 := _result49.GetSuccess(); _ret50 != nil {
		return _ret50, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserDataListByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserDataMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserDataMapResponse, _err error) {
	var _args51 UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs
	_args51.KugouIdList = kugouIdList
	var _result53 UserModuleV2BizServiceGetUserDataMapByKugouIdListResult
	var _meta52 thrift.ResponseMeta
	_meta52, _err = p.Client_().Call(ctx, "getUserDataMapByKugouIdList", &_args51, &_result53)
	p.SetLastResponseMeta_(_meta52)
	if _err != nil {
		return
	}
	if _ret54 := _result53.GetSuccess(); _ret54 != nil {
		return _ret54, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserDataMapByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserExtAttrByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserExtAttrResponse, _err error) {
	var _args55 UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs
	_args55.KugouId = kugouId
	var _result57 UserModuleV2BizServiceGetUserExtAttrByKugouIdResult
	var _meta56 thrift.ResponseMeta
	_meta56, _err = p.Client_().Call(ctx, "getUserExtAttrByKugouId", &_args55, &_result57)
	p.SetLastResponseMeta_(_meta56)
	if _err != nil {
		return
	}
	if _ret58 := _result57.GetSuccess(); _ret58 != nil {
		return _ret58, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserExtAttrByKugouId failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserExtAttrListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserExtAttrListResponse, _err error) {
	var _args59 UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs
	_args59.KugouIdList = kugouIdList
	var _result61 UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult
	var _meta60 thrift.ResponseMeta
	_meta60, _err = p.Client_().Call(ctx, "getUserExtAttrListByKugouIdList", &_args59, &_result61)
	p.SetLastResponseMeta_(_meta60)
	if _err != nil {
		return
	}
	if _ret62 := _result61.GetSuccess(); _ret62 != nil {
		return _ret62, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserExtAttrListByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserExtAttrMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserExtAttrMapResponse, _err error) {
	var _args63 UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs
	_args63.KugouIdList = kugouIdList
	var _result65 UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult
	var _meta64 thrift.ResponseMeta
	_meta64, _err = p.Client_().Call(ctx, "getUserExtAttrMapByKugouIdList", &_args63, &_result65)
	p.SetLastResponseMeta_(_meta64)
	if _err != nil {
		return
	}
	if _ret66 := _result65.GetSuccess(); _ret66 != nil {
		return _ret66, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserExtAttrMapByKugouIdList failed: unknown result")
}

// Parameters:
//   - UserName
func (p *UserModuleV2BizServiceClient) GetUserByUserName(ctx context.Context, userName string) (_r *userv2vo.UserResponse, _err error) {
	var _args67 UserModuleV2BizServiceGetUserByUserNameArgs
	_args67.UserName = userName
	var _result69 UserModuleV2BizServiceGetUserByUserNameResult
	var _meta68 thrift.ResponseMeta
	_meta68, _err = p.Client_().Call(ctx, "getUserByUserName", &_args67, &_result69)
	p.SetLastResponseMeta_(_meta68)
	if _err != nil {
		return
	}
	if _ret70 := _result69.GetSuccess(); _ret70 != nil {
		return _ret70, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserByUserName failed: unknown result")
}

// Parameters:
//   - UserNameList
func (p *UserModuleV2BizServiceClient) GetUserMapByUserNameList(ctx context.Context, userNameList []string) (_r *userv2vo.UserStrMapResponse, _err error) {
	var _args71 UserModuleV2BizServiceGetUserMapByUserNameListArgs
	_args71.UserNameList = userNameList
	var _result73 UserModuleV2BizServiceGetUserMapByUserNameListResult
	var _meta72 thrift.ResponseMeta
	_meta72, _err = p.Client_().Call(ctx, "getUserMapByUserNameList", &_args71, &_result73)
	p.SetLastResponseMeta_(_meta72)
	if _err != nil {
		return
	}
	if _ret74 := _result73.GetSuccess(); _ret74 != nil {
		return _ret74, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserMapByUserNameList failed: unknown result")
}

// Parameters:
//   - NickName
func (p *UserModuleV2BizServiceClient) GetUserByNickName(ctx context.Context, nickName string) (_r *userv2vo.UserResponse, _err error) {
	var _args75 UserModuleV2BizServiceGetUserByNickNameArgs
	_args75.NickName = nickName
	var _result77 UserModuleV2BizServiceGetUserByNickNameResult
	var _meta76 thrift.ResponseMeta
	_meta76, _err = p.Client_().Call(ctx, "getUserByNickName", &_args75, &_result77)
	p.SetLastResponseMeta_(_meta76)
	if _err != nil {
		return
	}
	if _ret78 := _result77.GetSuccess(); _ret78 != nil {
		return _ret78, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserByNickName failed: unknown result")
}

// Parameters:
//   - NickNameList
func (p *UserModuleV2BizServiceClient) GetUserMapByNickNameList(ctx context.Context, nickNameList []string) (_r *userv2vo.UserStrMapResponse, _err error) {
	var _args79 UserModuleV2BizServiceGetUserMapByNickNameListArgs
	_args79.NickNameList = nickNameList
	var _result81 UserModuleV2BizServiceGetUserMapByNickNameListResult
	var _meta80 thrift.ResponseMeta
	_meta80, _err = p.Client_().Call(ctx, "getUserMapByNickNameList", &_args79, &_result81)
	p.SetLastResponseMeta_(_meta80)
	if _err != nil {
		return
	}
	if _ret82 := _result81.GetSuccess(); _ret82 != nil {
		return _ret82, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserMapByNickNameList failed: unknown result")
}

// Parameters:
//   - NickName
func (p *UserModuleV2BizServiceClient) IsExistNickName(ctx context.Context, nickName string) (_r *userv2vo.ResBooleanMsg, _err error) {
	var _args83 UserModuleV2BizServiceIsExistNickNameArgs
	_args83.NickName = nickName
	var _result85 UserModuleV2BizServiceIsExistNickNameResult
	var _meta84 thrift.ResponseMeta
	_meta84, _err = p.Client_().Call(ctx, "isExistNickName", &_args83, &_result85)
	p.SetLastResponseMeta_(_meta84)
	if _err != nil {
		return
	}
	if _ret86 := _result85.GetSuccess(); _ret86 != nil {
		return _ret86, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "isExistNickName failed: unknown result")
}

// Parameters:
//   - NickNames
func (p *UserModuleV2BizServiceClient) IsExistNickNameList(ctx context.Context, nickNames []string) (_r *userv2vo.ResStringListMsg, _err error) {
	var _args87 UserModuleV2BizServiceIsExistNickNameListArgs
	_args87.NickNames = nickNames
	var _result89 UserModuleV2BizServiceIsExistNickNameListResult
	var _meta88 thrift.ResponseMeta
	_meta88, _err = p.Client_().Call(ctx, "isExistNickNameList", &_args87, &_result89)
	p.SetLastResponseMeta_(_meta88)
	if _err != nil {
		return
	}
	if _ret90 := _result89.GetSuccess(); _ret90 != nil {
		return _ret90, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "isExistNickNameList failed: unknown result")
}

// Parameters:
//   - KugouIds
func (p *UserModuleV2BizServiceClient) GetUserConstellationByKugouIds(ctx context.Context, kugouIds []int64) (_r *userv2vo.ResLongIntegerMapMsg, _err error) {
	var _args91 UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs
	_args91.KugouIds = kugouIds
	var _result93 UserModuleV2BizServiceGetUserConstellationByKugouIdsResult
	var _meta92 thrift.ResponseMeta
	_meta92, _err = p.Client_().Call(ctx, "getUserConstellationByKugouIds", &_args91, &_result93)
	p.SetLastResponseMeta_(_meta92)
	if _err != nil {
		return
	}
	if _ret94 := _result93.GetSuccess(); _ret94 != nil {
		return _ret94, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserConstellationByKugouIds failed: unknown result")
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserCoreInfoByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserCoreInfoResponse, _err error) {
	var _args95 UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs
	_args95.KugouId = kugouId
	var _result97 UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult
	var _meta96 thrift.ResponseMeta
	_meta96, _err = p.Client_().Call(ctx, "getUserCoreInfoByKugouId", &_args95, &_result97)
	p.SetLastResponseMeta_(_meta96)
	if _err != nil {
		return
	}
	if _ret98 := _result97.GetSuccess(); _ret98 != nil {
		return _ret98, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserCoreInfoByKugouId failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserCoreInfoListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCoreInfoListResponse, _err error) {
	var _args99 UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs
	_args99.KugouIdList = kugouIdList
	var _result101 UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult
	var _meta100 thrift.ResponseMeta
	_meta100, _err = p.Client_().Call(ctx, "getUserCoreInfoListByKugouIdList", &_args99, &_result101)
	p.SetLastResponseMeta_(_meta100)
	if _err != nil {
		return
	}
	if _ret102 := _result101.GetSuccess(); _ret102 != nil {
		return _ret102, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserCoreInfoListByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetCoreInfoMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCoreInfoMapResponse, _err error) {
	var _args103 UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs
	_args103.KugouIdList = kugouIdList
	var _result105 UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult
	var _meta104 thrift.ResponseMeta
	_meta104, _err = p.Client_().Call(ctx, "getCoreInfoMapByKugouIdList", &_args103, &_result105)
	p.SetLastResponseMeta_(_meta104)
	if _err != nil {
		return
	}
	if _ret106 := _result105.GetSuccess(); _ret106 != nil {
		return _ret106, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getCoreInfoMapByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserCombineInfoByKugouId(ctx context.Context, kugouId int64) (_r *userv2vo.UserCombineInfoResponse, _err error) {
	var _args107 UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs
	_args107.KugouId = kugouId
	var _result109 UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult
	var _meta108 thrift.ResponseMeta
	_meta108, _err = p.Client_().Call(ctx, "getUserCombineInfoByKugouId", &_args107, &_result109)
	p.SetLastResponseMeta_(_meta108)
	if _err != nil {
		return
	}
	if _ret110 := _result109.GetSuccess(); _ret110 != nil {
		return _ret110, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserCombineInfoByKugouId failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetUserCombineInfoListByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCombineInfoListResponse, _err error) {
	var _args111 UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs
	_args111.KugouIdList = kugouIdList
	var _result113 UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult
	var _meta112 thrift.ResponseMeta
	_meta112, _err = p.Client_().Call(ctx, "getUserCombineInfoListByKugouIdList", &_args111, &_result113)
	p.SetLastResponseMeta_(_meta112)
	if _err != nil {
		return
	}
	if _ret114 := _result113.GetSuccess(); _ret114 != nil {
		return _ret114, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserCombineInfoListByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIdList
func (p *UserModuleV2BizServiceClient) GetCombineInfoMapByKugouIdList(ctx context.Context, kugouIdList []int64) (_r *userv2vo.UserCombineInfoMapResponse, _err error) {
	var _args115 UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs
	_args115.KugouIdList = kugouIdList
	var _result117 UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult
	var _meta116 thrift.ResponseMeta
	_meta116, _err = p.Client_().Call(ctx, "getCombineInfoMapByKugouIdList", &_args115, &_result117)
	p.SetLastResponseMeta_(_meta116)
	if _err != nil {
		return
	}
	if _ret118 := _result117.GetSuccess(); _ret118 != nil {
		return _ret118, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getCombineInfoMapByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIdList
//   - DynamicUserVO
func (p *UserModuleV2BizServiceClient) GetDynamicUserInfoByKugouIdList(ctx context.Context, kugouIdList []int64, dynamicUserVO *userv2vo.DynamicUserVO) (_r *userv2vo.UserDynamicResponse, _err error) {
	var _args119 UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs
	_args119.KugouIdList = kugouIdList
	_args119.DynamicUserVO = dynamicUserVO
	var _result121 UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult
	var _meta120 thrift.ResponseMeta
	_meta120, _err = p.Client_().Call(ctx, "getDynamicUserInfoByKugouIdList", &_args119, &_result121)
	p.SetLastResponseMeta_(_meta120)
	if _err != nil {
		return
	}
	if _ret122 := _result121.GetSuccess(); _ret122 != nil {
		return _ret122, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getDynamicUserInfoByKugouIdList failed: unknown result")
}

// Parameters:
//   - KugouIds
func (p *UserModuleV2BizServiceClient) GetUserIdMappingByKugouIds(ctx context.Context, kugouIds []int64) (_r *userv2vo.ResIDMapMsg, _err error) {
	var _args123 UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs
	_args123.KugouIds = kugouIds
	var _result125 UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult
	var _meta124 thrift.ResponseMeta
	_meta124, _err = p.Client_().Call(ctx, "getUserIdMappingByKugouIds", &_args123, &_result125)
	p.SetLastResponseMeta_(_meta124)
	if _err != nil {
		return
	}
	if _ret126 := _result125.GetSuccess(); _ret126 != nil {
		return _ret126, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserIdMappingByKugouIds failed: unknown result")
}

// Parameters:
//   - UserIds
func (p *UserModuleV2BizServiceClient) GetKugouIdMappingByUserIds(ctx context.Context, userIds []int64) (_r *userv2vo.ResIDMapMsg, _err error) {
	var _args127 UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs
	_args127.UserIds = userIds
	var _result129 UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult
	var _meta128 thrift.ResponseMeta
	_meta128, _err = p.Client_().Call(ctx, "getKugouIdMappingByUserIds", &_args127, &_result129)
	p.SetLastResponseMeta_(_meta128)
	if _err != nil {
		return
	}
	if _ret130 := _result129.GetSuccess(); _ret130 != nil {
		return _ret130, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getKugouIdMappingByUserIds failed: unknown result")
}

// Parameters:
//   - KugouId
func (p *UserModuleV2BizServiceClient) GetUserIdByKugouId(ctx context.Context, kugouId int64) (_r int64, _err error) {
	var _args131 UserModuleV2BizServiceGetUserIdByKugouIdArgs
	_args131.KugouId = kugouId
	var _result133 UserModuleV2BizServiceGetUserIdByKugouIdResult
	var _meta132 thrift.ResponseMeta
	_meta132, _err = p.Client_().Call(ctx, "getUserIdByKugouId", &_args131, &_result133)
	p.SetLastResponseMeta_(_meta132)
	if _err != nil {
		return
	}
	return _result133.GetSuccess(), nil
}

// Parameters:
//   - UserId
func (p *UserModuleV2BizServiceClient) GetKugouIdByUserId(ctx context.Context, userId int64) (_r int64, _err error) {
	var _args134 UserModuleV2BizServiceGetKugouIdByUserIdArgs
	_args134.UserId = userId
	var _result136 UserModuleV2BizServiceGetKugouIdByUserIdResult
	var _meta135 thrift.ResponseMeta
	_meta135, _err = p.Client_().Call(ctx, "getKugouIdByUserId", &_args134, &_result136)
	p.SetLastResponseMeta_(_meta135)
	if _err != nil {
		return
	}
	return _result136.GetSuccess(), nil
}

type UserModuleV2BizServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      UserModuleV2BizService
}

func (p *UserModuleV2BizServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *UserModuleV2BizServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *UserModuleV2BizServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewUserModuleV2BizServiceProcessor(handler UserModuleV2BizService) *UserModuleV2BizServiceProcessor {

	self137 := &UserModuleV2BizServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self137.processorMap["getUserByKugouId"] = &userModuleV2BizServiceProcessorGetUserByKugouId{handler: handler}
	self137.processorMap["getUserListByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserListByKugouIdList{handler: handler}
	self137.processorMap["getBaseUserInfoList"] = &userModuleV2BizServiceProcessorGetBaseUserInfoList{handler: handler}
	self137.processorMap["getUserMapByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserMapByKugouIdList{handler: handler}
	self137.processorMap["getUserAttrByKugouId"] = &userModuleV2BizServiceProcessorGetUserAttrByKugouId{handler: handler}
	self137.processorMap["getUserAttrListByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserAttrListByKugouIdList{handler: handler}
	self137.processorMap["getUserAttrMapByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserAttrMapByKugouIdList{handler: handler}
	self137.processorMap["getUserDataByKugouId"] = &userModuleV2BizServiceProcessorGetUserDataByKugouId{handler: handler}
	self137.processorMap["getUserDataListByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserDataListByKugouIdList{handler: handler}
	self137.processorMap["getUserDataMapByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserDataMapByKugouIdList{handler: handler}
	self137.processorMap["getUserExtAttrByKugouId"] = &userModuleV2BizServiceProcessorGetUserExtAttrByKugouId{handler: handler}
	self137.processorMap["getUserExtAttrListByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserExtAttrListByKugouIdList{handler: handler}
	self137.processorMap["getUserExtAttrMapByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserExtAttrMapByKugouIdList{handler: handler}
	self137.processorMap["getUserByUserName"] = &userModuleV2BizServiceProcessorGetUserByUserName{handler: handler}
	self137.processorMap["getUserMapByUserNameList"] = &userModuleV2BizServiceProcessorGetUserMapByUserNameList{handler: handler}
	self137.processorMap["getUserByNickName"] = &userModuleV2BizServiceProcessorGetUserByNickName{handler: handler}
	self137.processorMap["getUserMapByNickNameList"] = &userModuleV2BizServiceProcessorGetUserMapByNickNameList{handler: handler}
	self137.processorMap["isExistNickName"] = &userModuleV2BizServiceProcessorIsExistNickName{handler: handler}
	self137.processorMap["isExistNickNameList"] = &userModuleV2BizServiceProcessorIsExistNickNameList{handler: handler}
	self137.processorMap["getUserConstellationByKugouIds"] = &userModuleV2BizServiceProcessorGetUserConstellationByKugouIds{handler: handler}
	self137.processorMap["getUserCoreInfoByKugouId"] = &userModuleV2BizServiceProcessorGetUserCoreInfoByKugouId{handler: handler}
	self137.processorMap["getUserCoreInfoListByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserCoreInfoListByKugouIdList{handler: handler}
	self137.processorMap["getCoreInfoMapByKugouIdList"] = &userModuleV2BizServiceProcessorGetCoreInfoMapByKugouIdList{handler: handler}
	self137.processorMap["getUserCombineInfoByKugouId"] = &userModuleV2BizServiceProcessorGetUserCombineInfoByKugouId{handler: handler}
	self137.processorMap["getUserCombineInfoListByKugouIdList"] = &userModuleV2BizServiceProcessorGetUserCombineInfoListByKugouIdList{handler: handler}
	self137.processorMap["getCombineInfoMapByKugouIdList"] = &userModuleV2BizServiceProcessorGetCombineInfoMapByKugouIdList{handler: handler}
	self137.processorMap["getDynamicUserInfoByKugouIdList"] = &userModuleV2BizServiceProcessorGetDynamicUserInfoByKugouIdList{handler: handler}
	self137.processorMap["getUserIdMappingByKugouIds"] = &userModuleV2BizServiceProcessorGetUserIdMappingByKugouIds{handler: handler}
	self137.processorMap["getKugouIdMappingByUserIds"] = &userModuleV2BizServiceProcessorGetKugouIdMappingByUserIds{handler: handler}
	self137.processorMap["getUserIdByKugouId"] = &userModuleV2BizServiceProcessorGetUserIdByKugouId{handler: handler}
	self137.processorMap["getKugouIdByUserId"] = &userModuleV2BizServiceProcessorGetKugouIdByUserId{handler: handler}
	return self137
}

func (p *UserModuleV2BizServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x138 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x138.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x138

}

type userModuleV2BizServiceProcessorGetUserByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err139 error
	args := UserModuleV2BizServiceGetUserByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserByKugouIdResult{}
	if retval, err2 := p.handler.GetUserByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc140 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err139 = thrift.WrapTException(err2)
		}
		if err2 := _exc140.Write(ctx, oprot); _write_err139 == nil && err2 != nil {
			_write_err139 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err139 == nil && err2 != nil {
			_write_err139 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err139 == nil && err2 != nil {
			_write_err139 = thrift.WrapTException(err2)
		}
		if _write_err139 != nil {
			return false, thrift.WrapTException(_write_err139)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err139 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err139 == nil && err2 != nil {
		_write_err139 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err139 == nil && err2 != nil {
		_write_err139 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err139 == nil && err2 != nil {
		_write_err139 = thrift.WrapTException(err2)
	}
	if _write_err139 != nil {
		return false, thrift.WrapTException(_write_err139)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserListByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserListByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err141 error
	args := UserModuleV2BizServiceGetUserListByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserListByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserListByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserListByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc142 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserListByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserListByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err141 = thrift.WrapTException(err2)
		}
		if err2 := _exc142.Write(ctx, oprot); _write_err141 == nil && err2 != nil {
			_write_err141 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err141 == nil && err2 != nil {
			_write_err141 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err141 == nil && err2 != nil {
			_write_err141 = thrift.WrapTException(err2)
		}
		if _write_err141 != nil {
			return false, thrift.WrapTException(_write_err141)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserListByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err141 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err141 == nil && err2 != nil {
		_write_err141 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err141 == nil && err2 != nil {
		_write_err141 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err141 == nil && err2 != nil {
		_write_err141 = thrift.WrapTException(err2)
	}
	if _write_err141 != nil {
		return false, thrift.WrapTException(_write_err141)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetBaseUserInfoList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetBaseUserInfoList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err143 error
	args := UserModuleV2BizServiceGetBaseUserInfoListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getBaseUserInfoList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetBaseUserInfoListResult{}
	if retval, err2 := p.handler.GetBaseUserInfoList(ctx, args.KugouIds); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc144 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getBaseUserInfoList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getBaseUserInfoList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err143 = thrift.WrapTException(err2)
		}
		if err2 := _exc144.Write(ctx, oprot); _write_err143 == nil && err2 != nil {
			_write_err143 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err143 == nil && err2 != nil {
			_write_err143 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err143 == nil && err2 != nil {
			_write_err143 = thrift.WrapTException(err2)
		}
		if _write_err143 != nil {
			return false, thrift.WrapTException(_write_err143)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getBaseUserInfoList", thrift.REPLY, seqId); err2 != nil {
		_write_err143 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err143 == nil && err2 != nil {
		_write_err143 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err143 == nil && err2 != nil {
		_write_err143 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err143 == nil && err2 != nil {
		_write_err143 = thrift.WrapTException(err2)
	}
	if _write_err143 != nil {
		return false, thrift.WrapTException(_write_err143)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserMapByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserMapByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err145 error
	args := UserModuleV2BizServiceGetUserMapByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserMapByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserMapByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserMapByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc146 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMapByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserMapByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err145 = thrift.WrapTException(err2)
		}
		if err2 := _exc146.Write(ctx, oprot); _write_err145 == nil && err2 != nil {
			_write_err145 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err145 == nil && err2 != nil {
			_write_err145 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err145 == nil && err2 != nil {
			_write_err145 = thrift.WrapTException(err2)
		}
		if _write_err145 != nil {
			return false, thrift.WrapTException(_write_err145)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserMapByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err145 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err145 == nil && err2 != nil {
		_write_err145 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err145 == nil && err2 != nil {
		_write_err145 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err145 == nil && err2 != nil {
		_write_err145 = thrift.WrapTException(err2)
	}
	if _write_err145 != nil {
		return false, thrift.WrapTException(_write_err145)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserAttrByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserAttrByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err147 error
	args := UserModuleV2BizServiceGetUserAttrByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserAttrByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserAttrByKugouIdResult{}
	if retval, err2 := p.handler.GetUserAttrByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc148 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserAttrByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserAttrByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err147 = thrift.WrapTException(err2)
		}
		if err2 := _exc148.Write(ctx, oprot); _write_err147 == nil && err2 != nil {
			_write_err147 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err147 == nil && err2 != nil {
			_write_err147 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err147 == nil && err2 != nil {
			_write_err147 = thrift.WrapTException(err2)
		}
		if _write_err147 != nil {
			return false, thrift.WrapTException(_write_err147)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserAttrByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err147 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err147 == nil && err2 != nil {
		_write_err147 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err147 == nil && err2 != nil {
		_write_err147 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err147 == nil && err2 != nil {
		_write_err147 = thrift.WrapTException(err2)
	}
	if _write_err147 != nil {
		return false, thrift.WrapTException(_write_err147)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserAttrListByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserAttrListByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err149 error
	args := UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserAttrListByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserAttrListByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserAttrListByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc150 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserAttrListByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserAttrListByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err149 = thrift.WrapTException(err2)
		}
		if err2 := _exc150.Write(ctx, oprot); _write_err149 == nil && err2 != nil {
			_write_err149 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err149 == nil && err2 != nil {
			_write_err149 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err149 == nil && err2 != nil {
			_write_err149 = thrift.WrapTException(err2)
		}
		if _write_err149 != nil {
			return false, thrift.WrapTException(_write_err149)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserAttrListByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err149 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err149 == nil && err2 != nil {
		_write_err149 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err149 == nil && err2 != nil {
		_write_err149 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err149 == nil && err2 != nil {
		_write_err149 = thrift.WrapTException(err2)
	}
	if _write_err149 != nil {
		return false, thrift.WrapTException(_write_err149)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserAttrMapByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserAttrMapByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err151 error
	args := UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserAttrMapByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserAttrMapByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc152 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserAttrMapByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserAttrMapByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err151 = thrift.WrapTException(err2)
		}
		if err2 := _exc152.Write(ctx, oprot); _write_err151 == nil && err2 != nil {
			_write_err151 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err151 == nil && err2 != nil {
			_write_err151 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err151 == nil && err2 != nil {
			_write_err151 = thrift.WrapTException(err2)
		}
		if _write_err151 != nil {
			return false, thrift.WrapTException(_write_err151)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserAttrMapByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err151 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err151 == nil && err2 != nil {
		_write_err151 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err151 == nil && err2 != nil {
		_write_err151 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err151 == nil && err2 != nil {
		_write_err151 = thrift.WrapTException(err2)
	}
	if _write_err151 != nil {
		return false, thrift.WrapTException(_write_err151)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserDataByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserDataByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err153 error
	args := UserModuleV2BizServiceGetUserDataByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserDataByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserDataByKugouIdResult{}
	if retval, err2 := p.handler.GetUserDataByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc154 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserDataByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserDataByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err153 = thrift.WrapTException(err2)
		}
		if err2 := _exc154.Write(ctx, oprot); _write_err153 == nil && err2 != nil {
			_write_err153 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err153 == nil && err2 != nil {
			_write_err153 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err153 == nil && err2 != nil {
			_write_err153 = thrift.WrapTException(err2)
		}
		if _write_err153 != nil {
			return false, thrift.WrapTException(_write_err153)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserDataByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err153 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err153 == nil && err2 != nil {
		_write_err153 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err153 == nil && err2 != nil {
		_write_err153 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err153 == nil && err2 != nil {
		_write_err153 = thrift.WrapTException(err2)
	}
	if _write_err153 != nil {
		return false, thrift.WrapTException(_write_err153)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserDataListByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserDataListByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err155 error
	args := UserModuleV2BizServiceGetUserDataListByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserDataListByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserDataListByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserDataListByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc156 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserDataListByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserDataListByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err155 = thrift.WrapTException(err2)
		}
		if err2 := _exc156.Write(ctx, oprot); _write_err155 == nil && err2 != nil {
			_write_err155 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err155 == nil && err2 != nil {
			_write_err155 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err155 == nil && err2 != nil {
			_write_err155 = thrift.WrapTException(err2)
		}
		if _write_err155 != nil {
			return false, thrift.WrapTException(_write_err155)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserDataListByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err155 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err155 == nil && err2 != nil {
		_write_err155 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err155 == nil && err2 != nil {
		_write_err155 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err155 == nil && err2 != nil {
		_write_err155 = thrift.WrapTException(err2)
	}
	if _write_err155 != nil {
		return false, thrift.WrapTException(_write_err155)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserDataMapByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserDataMapByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err157 error
	args := UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserDataMapByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserDataMapByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserDataMapByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc158 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserDataMapByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserDataMapByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err157 = thrift.WrapTException(err2)
		}
		if err2 := _exc158.Write(ctx, oprot); _write_err157 == nil && err2 != nil {
			_write_err157 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err157 == nil && err2 != nil {
			_write_err157 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err157 == nil && err2 != nil {
			_write_err157 = thrift.WrapTException(err2)
		}
		if _write_err157 != nil {
			return false, thrift.WrapTException(_write_err157)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserDataMapByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err157 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err157 == nil && err2 != nil {
		_write_err157 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err157 == nil && err2 != nil {
		_write_err157 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err157 == nil && err2 != nil {
		_write_err157 = thrift.WrapTException(err2)
	}
	if _write_err157 != nil {
		return false, thrift.WrapTException(_write_err157)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserExtAttrByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserExtAttrByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err159 error
	args := UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserExtAttrByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserExtAttrByKugouIdResult{}
	if retval, err2 := p.handler.GetUserExtAttrByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc160 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserExtAttrByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserExtAttrByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err159 = thrift.WrapTException(err2)
		}
		if err2 := _exc160.Write(ctx, oprot); _write_err159 == nil && err2 != nil {
			_write_err159 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err159 == nil && err2 != nil {
			_write_err159 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err159 == nil && err2 != nil {
			_write_err159 = thrift.WrapTException(err2)
		}
		if _write_err159 != nil {
			return false, thrift.WrapTException(_write_err159)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserExtAttrByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err159 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err159 == nil && err2 != nil {
		_write_err159 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err159 == nil && err2 != nil {
		_write_err159 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err159 == nil && err2 != nil {
		_write_err159 = thrift.WrapTException(err2)
	}
	if _write_err159 != nil {
		return false, thrift.WrapTException(_write_err159)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserExtAttrListByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserExtAttrListByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err161 error
	args := UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserExtAttrListByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserExtAttrListByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc162 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserExtAttrListByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserExtAttrListByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err161 = thrift.WrapTException(err2)
		}
		if err2 := _exc162.Write(ctx, oprot); _write_err161 == nil && err2 != nil {
			_write_err161 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err161 == nil && err2 != nil {
			_write_err161 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err161 == nil && err2 != nil {
			_write_err161 = thrift.WrapTException(err2)
		}
		if _write_err161 != nil {
			return false, thrift.WrapTException(_write_err161)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserExtAttrListByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err161 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err161 == nil && err2 != nil {
		_write_err161 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err161 == nil && err2 != nil {
		_write_err161 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err161 == nil && err2 != nil {
		_write_err161 = thrift.WrapTException(err2)
	}
	if _write_err161 != nil {
		return false, thrift.WrapTException(_write_err161)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserExtAttrMapByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserExtAttrMapByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err163 error
	args := UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserExtAttrMapByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserExtAttrMapByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc164 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserExtAttrMapByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserExtAttrMapByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err163 = thrift.WrapTException(err2)
		}
		if err2 := _exc164.Write(ctx, oprot); _write_err163 == nil && err2 != nil {
			_write_err163 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err163 == nil && err2 != nil {
			_write_err163 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err163 == nil && err2 != nil {
			_write_err163 = thrift.WrapTException(err2)
		}
		if _write_err163 != nil {
			return false, thrift.WrapTException(_write_err163)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserExtAttrMapByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err163 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err163 == nil && err2 != nil {
		_write_err163 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err163 == nil && err2 != nil {
		_write_err163 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err163 == nil && err2 != nil {
		_write_err163 = thrift.WrapTException(err2)
	}
	if _write_err163 != nil {
		return false, thrift.WrapTException(_write_err163)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserByUserName struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserByUserName) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err165 error
	args := UserModuleV2BizServiceGetUserByUserNameArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserByUserName", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserByUserNameResult{}
	if retval, err2 := p.handler.GetUserByUserName(ctx, args.UserName); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc166 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserByUserName: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserByUserName", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err165 = thrift.WrapTException(err2)
		}
		if err2 := _exc166.Write(ctx, oprot); _write_err165 == nil && err2 != nil {
			_write_err165 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err165 == nil && err2 != nil {
			_write_err165 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err165 == nil && err2 != nil {
			_write_err165 = thrift.WrapTException(err2)
		}
		if _write_err165 != nil {
			return false, thrift.WrapTException(_write_err165)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserByUserName", thrift.REPLY, seqId); err2 != nil {
		_write_err165 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err165 == nil && err2 != nil {
		_write_err165 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err165 == nil && err2 != nil {
		_write_err165 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err165 == nil && err2 != nil {
		_write_err165 = thrift.WrapTException(err2)
	}
	if _write_err165 != nil {
		return false, thrift.WrapTException(_write_err165)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserMapByUserNameList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserMapByUserNameList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err167 error
	args := UserModuleV2BizServiceGetUserMapByUserNameListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserMapByUserNameList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserMapByUserNameListResult{}
	if retval, err2 := p.handler.GetUserMapByUserNameList(ctx, args.UserNameList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc168 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMapByUserNameList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserMapByUserNameList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err167 = thrift.WrapTException(err2)
		}
		if err2 := _exc168.Write(ctx, oprot); _write_err167 == nil && err2 != nil {
			_write_err167 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err167 == nil && err2 != nil {
			_write_err167 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err167 == nil && err2 != nil {
			_write_err167 = thrift.WrapTException(err2)
		}
		if _write_err167 != nil {
			return false, thrift.WrapTException(_write_err167)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserMapByUserNameList", thrift.REPLY, seqId); err2 != nil {
		_write_err167 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err167 == nil && err2 != nil {
		_write_err167 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err167 == nil && err2 != nil {
		_write_err167 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err167 == nil && err2 != nil {
		_write_err167 = thrift.WrapTException(err2)
	}
	if _write_err167 != nil {
		return false, thrift.WrapTException(_write_err167)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserByNickName struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserByNickName) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err169 error
	args := UserModuleV2BizServiceGetUserByNickNameArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserByNickName", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserByNickNameResult{}
	if retval, err2 := p.handler.GetUserByNickName(ctx, args.NickName); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc170 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserByNickName: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserByNickName", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err169 = thrift.WrapTException(err2)
		}
		if err2 := _exc170.Write(ctx, oprot); _write_err169 == nil && err2 != nil {
			_write_err169 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err169 == nil && err2 != nil {
			_write_err169 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err169 == nil && err2 != nil {
			_write_err169 = thrift.WrapTException(err2)
		}
		if _write_err169 != nil {
			return false, thrift.WrapTException(_write_err169)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserByNickName", thrift.REPLY, seqId); err2 != nil {
		_write_err169 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err169 == nil && err2 != nil {
		_write_err169 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err169 == nil && err2 != nil {
		_write_err169 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err169 == nil && err2 != nil {
		_write_err169 = thrift.WrapTException(err2)
	}
	if _write_err169 != nil {
		return false, thrift.WrapTException(_write_err169)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserMapByNickNameList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserMapByNickNameList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err171 error
	args := UserModuleV2BizServiceGetUserMapByNickNameListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserMapByNickNameList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserMapByNickNameListResult{}
	if retval, err2 := p.handler.GetUserMapByNickNameList(ctx, args.NickNameList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc172 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMapByNickNameList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserMapByNickNameList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err171 = thrift.WrapTException(err2)
		}
		if err2 := _exc172.Write(ctx, oprot); _write_err171 == nil && err2 != nil {
			_write_err171 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err171 == nil && err2 != nil {
			_write_err171 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err171 == nil && err2 != nil {
			_write_err171 = thrift.WrapTException(err2)
		}
		if _write_err171 != nil {
			return false, thrift.WrapTException(_write_err171)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserMapByNickNameList", thrift.REPLY, seqId); err2 != nil {
		_write_err171 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err171 == nil && err2 != nil {
		_write_err171 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err171 == nil && err2 != nil {
		_write_err171 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err171 == nil && err2 != nil {
		_write_err171 = thrift.WrapTException(err2)
	}
	if _write_err171 != nil {
		return false, thrift.WrapTException(_write_err171)
	}
	return true, err
}

type userModuleV2BizServiceProcessorIsExistNickName struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorIsExistNickName) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err173 error
	args := UserModuleV2BizServiceIsExistNickNameArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "isExistNickName", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceIsExistNickNameResult{}
	if retval, err2 := p.handler.IsExistNickName(ctx, args.NickName); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc174 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isExistNickName: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "isExistNickName", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err173 = thrift.WrapTException(err2)
		}
		if err2 := _exc174.Write(ctx, oprot); _write_err173 == nil && err2 != nil {
			_write_err173 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err173 == nil && err2 != nil {
			_write_err173 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err173 == nil && err2 != nil {
			_write_err173 = thrift.WrapTException(err2)
		}
		if _write_err173 != nil {
			return false, thrift.WrapTException(_write_err173)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "isExistNickName", thrift.REPLY, seqId); err2 != nil {
		_write_err173 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err173 == nil && err2 != nil {
		_write_err173 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err173 == nil && err2 != nil {
		_write_err173 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err173 == nil && err2 != nil {
		_write_err173 = thrift.WrapTException(err2)
	}
	if _write_err173 != nil {
		return false, thrift.WrapTException(_write_err173)
	}
	return true, err
}

type userModuleV2BizServiceProcessorIsExistNickNameList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorIsExistNickNameList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err175 error
	args := UserModuleV2BizServiceIsExistNickNameListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "isExistNickNameList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceIsExistNickNameListResult{}
	if retval, err2 := p.handler.IsExistNickNameList(ctx, args.NickNames); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc176 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isExistNickNameList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "isExistNickNameList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err175 = thrift.WrapTException(err2)
		}
		if err2 := _exc176.Write(ctx, oprot); _write_err175 == nil && err2 != nil {
			_write_err175 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err175 == nil && err2 != nil {
			_write_err175 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err175 == nil && err2 != nil {
			_write_err175 = thrift.WrapTException(err2)
		}
		if _write_err175 != nil {
			return false, thrift.WrapTException(_write_err175)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "isExistNickNameList", thrift.REPLY, seqId); err2 != nil {
		_write_err175 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err175 == nil && err2 != nil {
		_write_err175 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err175 == nil && err2 != nil {
		_write_err175 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err175 == nil && err2 != nil {
		_write_err175 = thrift.WrapTException(err2)
	}
	if _write_err175 != nil {
		return false, thrift.WrapTException(_write_err175)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserConstellationByKugouIds struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserConstellationByKugouIds) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err177 error
	args := UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserConstellationByKugouIds", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserConstellationByKugouIdsResult{}
	if retval, err2 := p.handler.GetUserConstellationByKugouIds(ctx, args.KugouIds); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc178 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserConstellationByKugouIds: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserConstellationByKugouIds", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err177 = thrift.WrapTException(err2)
		}
		if err2 := _exc178.Write(ctx, oprot); _write_err177 == nil && err2 != nil {
			_write_err177 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err177 == nil && err2 != nil {
			_write_err177 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err177 == nil && err2 != nil {
			_write_err177 = thrift.WrapTException(err2)
		}
		if _write_err177 != nil {
			return false, thrift.WrapTException(_write_err177)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserConstellationByKugouIds", thrift.REPLY, seqId); err2 != nil {
		_write_err177 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err177 == nil && err2 != nil {
		_write_err177 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err177 == nil && err2 != nil {
		_write_err177 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err177 == nil && err2 != nil {
		_write_err177 = thrift.WrapTException(err2)
	}
	if _write_err177 != nil {
		return false, thrift.WrapTException(_write_err177)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserCoreInfoByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserCoreInfoByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err179 error
	args := UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserCoreInfoByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult{}
	if retval, err2 := p.handler.GetUserCoreInfoByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc180 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserCoreInfoByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserCoreInfoByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err179 = thrift.WrapTException(err2)
		}
		if err2 := _exc180.Write(ctx, oprot); _write_err179 == nil && err2 != nil {
			_write_err179 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err179 == nil && err2 != nil {
			_write_err179 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err179 == nil && err2 != nil {
			_write_err179 = thrift.WrapTException(err2)
		}
		if _write_err179 != nil {
			return false, thrift.WrapTException(_write_err179)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserCoreInfoByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err179 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err179 == nil && err2 != nil {
		_write_err179 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err179 == nil && err2 != nil {
		_write_err179 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err179 == nil && err2 != nil {
		_write_err179 = thrift.WrapTException(err2)
	}
	if _write_err179 != nil {
		return false, thrift.WrapTException(_write_err179)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserCoreInfoListByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserCoreInfoListByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err181 error
	args := UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserCoreInfoListByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserCoreInfoListByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc182 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserCoreInfoListByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserCoreInfoListByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err181 = thrift.WrapTException(err2)
		}
		if err2 := _exc182.Write(ctx, oprot); _write_err181 == nil && err2 != nil {
			_write_err181 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err181 == nil && err2 != nil {
			_write_err181 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err181 == nil && err2 != nil {
			_write_err181 = thrift.WrapTException(err2)
		}
		if _write_err181 != nil {
			return false, thrift.WrapTException(_write_err181)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserCoreInfoListByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err181 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err181 == nil && err2 != nil {
		_write_err181 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err181 == nil && err2 != nil {
		_write_err181 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err181 == nil && err2 != nil {
		_write_err181 = thrift.WrapTException(err2)
	}
	if _write_err181 != nil {
		return false, thrift.WrapTException(_write_err181)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetCoreInfoMapByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetCoreInfoMapByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err183 error
	args := UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getCoreInfoMapByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult{}
	if retval, err2 := p.handler.GetCoreInfoMapByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc184 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCoreInfoMapByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getCoreInfoMapByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err183 = thrift.WrapTException(err2)
		}
		if err2 := _exc184.Write(ctx, oprot); _write_err183 == nil && err2 != nil {
			_write_err183 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err183 == nil && err2 != nil {
			_write_err183 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err183 == nil && err2 != nil {
			_write_err183 = thrift.WrapTException(err2)
		}
		if _write_err183 != nil {
			return false, thrift.WrapTException(_write_err183)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getCoreInfoMapByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err183 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err183 == nil && err2 != nil {
		_write_err183 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err183 == nil && err2 != nil {
		_write_err183 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err183 == nil && err2 != nil {
		_write_err183 = thrift.WrapTException(err2)
	}
	if _write_err183 != nil {
		return false, thrift.WrapTException(_write_err183)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserCombineInfoByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserCombineInfoByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err185 error
	args := UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserCombineInfoByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult{}
	if retval, err2 := p.handler.GetUserCombineInfoByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc186 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserCombineInfoByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserCombineInfoByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err185 = thrift.WrapTException(err2)
		}
		if err2 := _exc186.Write(ctx, oprot); _write_err185 == nil && err2 != nil {
			_write_err185 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err185 == nil && err2 != nil {
			_write_err185 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err185 == nil && err2 != nil {
			_write_err185 = thrift.WrapTException(err2)
		}
		if _write_err185 != nil {
			return false, thrift.WrapTException(_write_err185)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserCombineInfoByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err185 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err185 == nil && err2 != nil {
		_write_err185 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err185 == nil && err2 != nil {
		_write_err185 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err185 == nil && err2 != nil {
		_write_err185 = thrift.WrapTException(err2)
	}
	if _write_err185 != nil {
		return false, thrift.WrapTException(_write_err185)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserCombineInfoListByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserCombineInfoListByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err187 error
	args := UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserCombineInfoListByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult{}
	if retval, err2 := p.handler.GetUserCombineInfoListByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc188 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserCombineInfoListByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserCombineInfoListByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err187 = thrift.WrapTException(err2)
		}
		if err2 := _exc188.Write(ctx, oprot); _write_err187 == nil && err2 != nil {
			_write_err187 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err187 == nil && err2 != nil {
			_write_err187 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err187 == nil && err2 != nil {
			_write_err187 = thrift.WrapTException(err2)
		}
		if _write_err187 != nil {
			return false, thrift.WrapTException(_write_err187)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserCombineInfoListByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err187 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err187 == nil && err2 != nil {
		_write_err187 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err187 == nil && err2 != nil {
		_write_err187 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err187 == nil && err2 != nil {
		_write_err187 = thrift.WrapTException(err2)
	}
	if _write_err187 != nil {
		return false, thrift.WrapTException(_write_err187)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetCombineInfoMapByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetCombineInfoMapByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err189 error
	args := UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getCombineInfoMapByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult{}
	if retval, err2 := p.handler.GetCombineInfoMapByKugouIdList(ctx, args.KugouIdList); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc190 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCombineInfoMapByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getCombineInfoMapByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err189 = thrift.WrapTException(err2)
		}
		if err2 := _exc190.Write(ctx, oprot); _write_err189 == nil && err2 != nil {
			_write_err189 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err189 == nil && err2 != nil {
			_write_err189 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err189 == nil && err2 != nil {
			_write_err189 = thrift.WrapTException(err2)
		}
		if _write_err189 != nil {
			return false, thrift.WrapTException(_write_err189)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getCombineInfoMapByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err189 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err189 == nil && err2 != nil {
		_write_err189 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err189 == nil && err2 != nil {
		_write_err189 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err189 == nil && err2 != nil {
		_write_err189 = thrift.WrapTException(err2)
	}
	if _write_err189 != nil {
		return false, thrift.WrapTException(_write_err189)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetDynamicUserInfoByKugouIdList struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetDynamicUserInfoByKugouIdList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err191 error
	args := UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getDynamicUserInfoByKugouIdList", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult{}
	if retval, err2 := p.handler.GetDynamicUserInfoByKugouIdList(ctx, args.KugouIdList, args.DynamicUserVO); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc192 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDynamicUserInfoByKugouIdList: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getDynamicUserInfoByKugouIdList", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err191 = thrift.WrapTException(err2)
		}
		if err2 := _exc192.Write(ctx, oprot); _write_err191 == nil && err2 != nil {
			_write_err191 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err191 == nil && err2 != nil {
			_write_err191 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err191 == nil && err2 != nil {
			_write_err191 = thrift.WrapTException(err2)
		}
		if _write_err191 != nil {
			return false, thrift.WrapTException(_write_err191)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getDynamicUserInfoByKugouIdList", thrift.REPLY, seqId); err2 != nil {
		_write_err191 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err191 == nil && err2 != nil {
		_write_err191 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err191 == nil && err2 != nil {
		_write_err191 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err191 == nil && err2 != nil {
		_write_err191 = thrift.WrapTException(err2)
	}
	if _write_err191 != nil {
		return false, thrift.WrapTException(_write_err191)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserIdMappingByKugouIds struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserIdMappingByKugouIds) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err193 error
	args := UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserIdMappingByKugouIds", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult{}
	if retval, err2 := p.handler.GetUserIdMappingByKugouIds(ctx, args.KugouIds); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc194 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserIdMappingByKugouIds: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserIdMappingByKugouIds", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err193 = thrift.WrapTException(err2)
		}
		if err2 := _exc194.Write(ctx, oprot); _write_err193 == nil && err2 != nil {
			_write_err193 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err193 == nil && err2 != nil {
			_write_err193 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err193 == nil && err2 != nil {
			_write_err193 = thrift.WrapTException(err2)
		}
		if _write_err193 != nil {
			return false, thrift.WrapTException(_write_err193)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserIdMappingByKugouIds", thrift.REPLY, seqId); err2 != nil {
		_write_err193 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err193 == nil && err2 != nil {
		_write_err193 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err193 == nil && err2 != nil {
		_write_err193 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err193 == nil && err2 != nil {
		_write_err193 = thrift.WrapTException(err2)
	}
	if _write_err193 != nil {
		return false, thrift.WrapTException(_write_err193)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetKugouIdMappingByUserIds struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetKugouIdMappingByUserIds) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err195 error
	args := UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getKugouIdMappingByUserIds", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult{}
	if retval, err2 := p.handler.GetKugouIdMappingByUserIds(ctx, args.UserIds); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc196 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getKugouIdMappingByUserIds: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getKugouIdMappingByUserIds", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err195 = thrift.WrapTException(err2)
		}
		if err2 := _exc196.Write(ctx, oprot); _write_err195 == nil && err2 != nil {
			_write_err195 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err195 == nil && err2 != nil {
			_write_err195 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err195 == nil && err2 != nil {
			_write_err195 = thrift.WrapTException(err2)
		}
		if _write_err195 != nil {
			return false, thrift.WrapTException(_write_err195)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getKugouIdMappingByUserIds", thrift.REPLY, seqId); err2 != nil {
		_write_err195 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err195 == nil && err2 != nil {
		_write_err195 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err195 == nil && err2 != nil {
		_write_err195 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err195 == nil && err2 != nil {
		_write_err195 = thrift.WrapTException(err2)
	}
	if _write_err195 != nil {
		return false, thrift.WrapTException(_write_err195)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetUserIdByKugouId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetUserIdByKugouId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err197 error
	args := UserModuleV2BizServiceGetUserIdByKugouIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserIdByKugouId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetUserIdByKugouIdResult{}
	if retval, err2 := p.handler.GetUserIdByKugouId(ctx, args.KugouId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc198 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserIdByKugouId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserIdByKugouId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err197 = thrift.WrapTException(err2)
		}
		if err2 := _exc198.Write(ctx, oprot); _write_err197 == nil && err2 != nil {
			_write_err197 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err197 == nil && err2 != nil {
			_write_err197 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err197 == nil && err2 != nil {
			_write_err197 = thrift.WrapTException(err2)
		}
		if _write_err197 != nil {
			return false, thrift.WrapTException(_write_err197)
		}
		return true, err
	} else {
		result.Success = &retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserIdByKugouId", thrift.REPLY, seqId); err2 != nil {
		_write_err197 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err197 == nil && err2 != nil {
		_write_err197 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err197 == nil && err2 != nil {
		_write_err197 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err197 == nil && err2 != nil {
		_write_err197 = thrift.WrapTException(err2)
	}
	if _write_err197 != nil {
		return false, thrift.WrapTException(_write_err197)
	}
	return true, err
}

type userModuleV2BizServiceProcessorGetKugouIdByUserId struct {
	handler UserModuleV2BizService
}

func (p *userModuleV2BizServiceProcessorGetKugouIdByUserId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err199 error
	args := UserModuleV2BizServiceGetKugouIdByUserIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getKugouIdByUserId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := UserModuleV2BizServiceGetKugouIdByUserIdResult{}
	if retval, err2 := p.handler.GetKugouIdByUserId(ctx, args.UserId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc200 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getKugouIdByUserId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getKugouIdByUserId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err199 = thrift.WrapTException(err2)
		}
		if err2 := _exc200.Write(ctx, oprot); _write_err199 == nil && err2 != nil {
			_write_err199 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err199 == nil && err2 != nil {
			_write_err199 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err199 == nil && err2 != nil {
			_write_err199 = thrift.WrapTException(err2)
		}
		if _write_err199 != nil {
			return false, thrift.WrapTException(_write_err199)
		}
		return true, err
	} else {
		result.Success = &retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getKugouIdByUserId", thrift.REPLY, seqId); err2 != nil {
		_write_err199 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err199 == nil && err2 != nil {
		_write_err199 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err199 == nil && err2 != nil {
		_write_err199 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err199 == nil && err2 != nil {
		_write_err199 = thrift.WrapTException(err2)
	}
	if _write_err199 != nil {
		return false, thrift.WrapTException(_write_err199)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserByKugouIdArgs() *UserModuleV2BizServiceGetUserByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserByKugouIdResult struct {
	Success *userv2vo.UserResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserByKugouIdResult() *UserModuleV2BizServiceGetUserByKugouIdResult {
	return &UserModuleV2BizServiceGetUserByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserByKugouIdResult_Success_DEFAULT *userv2vo.UserResponse

func (p *UserModuleV2BizServiceGetUserByKugouIdResult) GetSuccess() *userv2vo.UserResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserByKugouIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserListByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserListByKugouIdListArgs() *UserModuleV2BizServiceGetUserListByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserListByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserListByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem201 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem201 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem201)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserListByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserListByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserListByKugouIdListResult struct {
	Success *userv2vo.UserListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserListByKugouIdListResult() *UserModuleV2BizServiceGetUserListByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserListByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserListByKugouIdListResult_Success_DEFAULT *userv2vo.UserListResponse

func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) GetSuccess() *userv2vo.UserListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserListByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserListByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserListByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserListByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIds: 最大长度30
type UserModuleV2BizServiceGetBaseUserInfoListArgs struct {
	KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
}

func NewUserModuleV2BizServiceGetBaseUserInfoListArgs() *UserModuleV2BizServiceGetBaseUserInfoListArgs {
	return &UserModuleV2BizServiceGetBaseUserInfoListArgs{}
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListArgs) GetKugouIds() []int64 {
	return p.KugouIds
}
func (p *UserModuleV2BizServiceGetBaseUserInfoListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIds bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIds = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIds {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIds = tSlice
	for i := 0; i < size; i++ {
		var _elem202 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem202 = v
		}
		p.KugouIds = append(p.KugouIds, _elem202)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getBaseUserInfoList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIds {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetBaseUserInfoListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetBaseUserInfoListResult struct {
	Success *userv2vo.BaseUserInfoListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetBaseUserInfoListResult() *UserModuleV2BizServiceGetBaseUserInfoListResult {
	return &UserModuleV2BizServiceGetBaseUserInfoListResult{}
}

var UserModuleV2BizServiceGetBaseUserInfoListResult_Success_DEFAULT *userv2vo.BaseUserInfoListResponse

func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) GetSuccess() *userv2vo.BaseUserInfoListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetBaseUserInfoListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.BaseUserInfoListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getBaseUserInfoList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetBaseUserInfoListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetBaseUserInfoListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserMapByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserMapByKugouIdListArgs() *UserModuleV2BizServiceGetUserMapByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserMapByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserMapByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem203 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem203 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem203)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMapByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserMapByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserMapByKugouIdListResult struct {
	Success *userv2vo.UserMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserMapByKugouIdListResult() *UserModuleV2BizServiceGetUserMapByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserMapByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserMapByKugouIdListResult_Success_DEFAULT *userv2vo.UserMapResponse

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) GetSuccess() *userv2vo.UserMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserMapByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMapByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserMapByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserMapByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserAttrByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserAttrByKugouIdArgs() *UserModuleV2BizServiceGetUserAttrByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserAttrByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserAttrByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAttrByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserAttrByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserAttrByKugouIdResult struct {
	Success *userv2vo.UserAttrResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserAttrByKugouIdResult() *UserModuleV2BizServiceGetUserAttrByKugouIdResult {
	return &UserModuleV2BizServiceGetUserAttrByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserAttrByKugouIdResult_Success_DEFAULT *userv2vo.UserAttrResponse

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) GetSuccess() *userv2vo.UserAttrResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserAttrByKugouIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserAttrResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAttrByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserAttrByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserAttrByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserAttrListByKugouIdListArgs() *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem204 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem204 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem204)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAttrListByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserAttrListByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserAttrListByKugouIdListResult struct {
	Success *userv2vo.UserAttrListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserAttrListByKugouIdListResult() *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserAttrListByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserAttrListByKugouIdListResult_Success_DEFAULT *userv2vo.UserAttrListResponse

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) GetSuccess() *userv2vo.UserAttrListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserAttrListByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserAttrListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAttrListByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserAttrListByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserAttrListByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs() *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem205 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem205 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem205)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAttrMapByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserAttrMapByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult struct {
	Success *userv2vo.UserAttrMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserAttrMapByKugouIdListResult() *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult_Success_DEFAULT *userv2vo.UserAttrMapResponse

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) GetSuccess() *userv2vo.UserAttrMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserAttrMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAttrMapByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserAttrMapByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserDataByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserDataByKugouIdArgs() *UserModuleV2BizServiceGetUserDataByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserDataByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserDataByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserDataByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserDataByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserDataByKugouIdResult struct {
	Success *userv2vo.UserDataResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserDataByKugouIdResult() *UserModuleV2BizServiceGetUserDataByKugouIdResult {
	return &UserModuleV2BizServiceGetUserDataByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserDataByKugouIdResult_Success_DEFAULT *userv2vo.UserDataResponse

func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) GetSuccess() *userv2vo.UserDataResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserDataByKugouIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserDataResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserDataByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserDataByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserDataByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserDataListByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserDataListByKugouIdListArgs() *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserDataListByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem206 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem206 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem206)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserDataListByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserDataListByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserDataListByKugouIdListResult struct {
	Success *userv2vo.UserDataListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserDataListByKugouIdListResult() *UserModuleV2BizServiceGetUserDataListByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserDataListByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserDataListByKugouIdListResult_Success_DEFAULT *userv2vo.UserDataListResponse

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) GetSuccess() *userv2vo.UserDataListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserDataListByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserDataListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserDataListByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserDataListByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserDataListByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserDataMapByKugouIdListArgs() *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem207 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem207 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem207)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserDataMapByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserDataMapByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserDataMapByKugouIdListResult struct {
	Success *userv2vo.UserDataMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserDataMapByKugouIdListResult() *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserDataMapByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserDataMapByKugouIdListResult_Success_DEFAULT *userv2vo.UserDataMapResponse

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) GetSuccess() *userv2vo.UserDataMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserDataMapByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserDataMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserDataMapByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserDataMapByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserDataMapByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserExtAttrByKugouIdArgs() *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserExtAttrByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserExtAttrByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserExtAttrByKugouIdResult struct {
	Success *userv2vo.UserExtAttrResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserExtAttrByKugouIdResult() *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult {
	return &UserModuleV2BizServiceGetUserExtAttrByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserExtAttrByKugouIdResult_Success_DEFAULT *userv2vo.UserExtAttrResponse

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) GetSuccess() *userv2vo.UserExtAttrResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserExtAttrByKugouIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserExtAttrResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserExtAttrByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserExtAttrByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserExtAttrByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs() *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem208 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem208 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem208)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserExtAttrListByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserExtAttrListByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult struct {
	Success *userv2vo.UserExtAttrListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult() *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult_Success_DEFAULT *userv2vo.UserExtAttrListResponse

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) GetSuccess() *userv2vo.UserExtAttrListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserExtAttrListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserExtAttrListByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserExtAttrListByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs() *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem209 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem209 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem209)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserExtAttrMapByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult struct {
	Success *userv2vo.UserExtAttrMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult() *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult_Success_DEFAULT *userv2vo.UserExtAttrMapResponse

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) GetSuccess() *userv2vo.UserExtAttrMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserExtAttrMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserExtAttrMapByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserExtAttrMapByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - UserName
type UserModuleV2BizServiceGetUserByUserNameArgs struct {
	UserName string `thrift:"userName,1,required" db:"userName" json:"userName"`
}

func NewUserModuleV2BizServiceGetUserByUserNameArgs() *UserModuleV2BizServiceGetUserByUserNameArgs {
	return &UserModuleV2BizServiceGetUserByUserNameArgs{}
}

func (p *UserModuleV2BizServiceGetUserByUserNameArgs) GetUserName() string {
	return p.UserName
}
func (p *UserModuleV2BizServiceGetUserByUserNameArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetUserName bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetUserName = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetUserName {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserName is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.UserName = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserByUserName_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.UserName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userName: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserByUserNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserByUserNameArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserByUserNameResult struct {
	Success *userv2vo.UserResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserByUserNameResult() *UserModuleV2BizServiceGetUserByUserNameResult {
	return &UserModuleV2BizServiceGetUserByUserNameResult{}
}

var UserModuleV2BizServiceGetUserByUserNameResult_Success_DEFAULT *userv2vo.UserResponse

func (p *UserModuleV2BizServiceGetUserByUserNameResult) GetSuccess() *userv2vo.UserResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserByUserNameResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserByUserNameResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserByUserName_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByUserNameResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserByUserNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserByUserNameResult(%+v)", *p)
}

// Attributes:
//   - UserNameList
type UserModuleV2BizServiceGetUserMapByUserNameListArgs struct {
	UserNameList []string `thrift:"userNameList,1,required" db:"userNameList" json:"userNameList"`
}

func NewUserModuleV2BizServiceGetUserMapByUserNameListArgs() *UserModuleV2BizServiceGetUserMapByUserNameListArgs {
	return &UserModuleV2BizServiceGetUserMapByUserNameListArgs{}
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListArgs) GetUserNameList() []string {
	return p.UserNameList
}
func (p *UserModuleV2BizServiceGetUserMapByUserNameListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetUserNameList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetUserNameList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetUserNameList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserNameList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.UserNameList = tSlice
	for i := 0; i < size; i++ {
		var _elem210 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem210 = v
		}
		p.UserNameList = append(p.UserNameList, _elem210)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMapByUserNameList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userNameList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userNameList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.UserNameList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.UserNameList {
		if err := oprot.WriteString(ctx, string(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userNameList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserMapByUserNameListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserMapByUserNameListResult struct {
	Success *userv2vo.UserStrMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserMapByUserNameListResult() *UserModuleV2BizServiceGetUserMapByUserNameListResult {
	return &UserModuleV2BizServiceGetUserMapByUserNameListResult{}
}

var UserModuleV2BizServiceGetUserMapByUserNameListResult_Success_DEFAULT *userv2vo.UserStrMapResponse

func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) GetSuccess() *userv2vo.UserStrMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserMapByUserNameListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserStrMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMapByUserNameList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserMapByUserNameListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserMapByUserNameListResult(%+v)", *p)
}

// Attributes:
//   - NickName
type UserModuleV2BizServiceGetUserByNickNameArgs struct {
	NickName string `thrift:"nickName,1,required" db:"nickName" json:"nickName"`
}

func NewUserModuleV2BizServiceGetUserByNickNameArgs() *UserModuleV2BizServiceGetUserByNickNameArgs {
	return &UserModuleV2BizServiceGetUserByNickNameArgs{}
}

func (p *UserModuleV2BizServiceGetUserByNickNameArgs) GetNickName() string {
	return p.NickName
}
func (p *UserModuleV2BizServiceGetUserByNickNameArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetNickName bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetNickName = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetNickName {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field NickName is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.NickName = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserByNickName_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:nickName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.NickName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.nickName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:nickName: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserByNickNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserByNickNameArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserByNickNameResult struct {
	Success *userv2vo.UserResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserByNickNameResult() *UserModuleV2BizServiceGetUserByNickNameResult {
	return &UserModuleV2BizServiceGetUserByNickNameResult{}
}

var UserModuleV2BizServiceGetUserByNickNameResult_Success_DEFAULT *userv2vo.UserResponse

func (p *UserModuleV2BizServiceGetUserByNickNameResult) GetSuccess() *userv2vo.UserResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserByNickNameResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserByNickNameResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserByNickName_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserByNickNameResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserByNickNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserByNickNameResult(%+v)", *p)
}

// Attributes:
//   - NickNameList
type UserModuleV2BizServiceGetUserMapByNickNameListArgs struct {
	NickNameList []string `thrift:"nickNameList,1,required" db:"nickNameList" json:"nickNameList"`
}

func NewUserModuleV2BizServiceGetUserMapByNickNameListArgs() *UserModuleV2BizServiceGetUserMapByNickNameListArgs {
	return &UserModuleV2BizServiceGetUserMapByNickNameListArgs{}
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListArgs) GetNickNameList() []string {
	return p.NickNameList
}
func (p *UserModuleV2BizServiceGetUserMapByNickNameListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetNickNameList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetNickNameList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetNickNameList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field NickNameList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.NickNameList = tSlice
	for i := 0; i < size; i++ {
		var _elem211 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem211 = v
		}
		p.NickNameList = append(p.NickNameList, _elem211)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMapByNickNameList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "nickNameList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:nickNameList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.NickNameList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.NickNameList {
		if err := oprot.WriteString(ctx, string(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:nickNameList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserMapByNickNameListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserMapByNickNameListResult struct {
	Success *userv2vo.UserStrMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserMapByNickNameListResult() *UserModuleV2BizServiceGetUserMapByNickNameListResult {
	return &UserModuleV2BizServiceGetUserMapByNickNameListResult{}
}

var UserModuleV2BizServiceGetUserMapByNickNameListResult_Success_DEFAULT *userv2vo.UserStrMapResponse

func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) GetSuccess() *userv2vo.UserStrMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserMapByNickNameListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserStrMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMapByNickNameList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserMapByNickNameListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserMapByNickNameListResult(%+v)", *p)
}

// Attributes:
//   - NickName
type UserModuleV2BizServiceIsExistNickNameArgs struct {
	NickName string `thrift:"nickName,1,required" db:"nickName" json:"nickName"`
}

func NewUserModuleV2BizServiceIsExistNickNameArgs() *UserModuleV2BizServiceIsExistNickNameArgs {
	return &UserModuleV2BizServiceIsExistNickNameArgs{}
}

func (p *UserModuleV2BizServiceIsExistNickNameArgs) GetNickName() string {
	return p.NickName
}
func (p *UserModuleV2BizServiceIsExistNickNameArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetNickName bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetNickName = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetNickName {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field NickName is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.NickName = v
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "isExistNickName_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:nickName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.NickName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.nickName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:nickName: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceIsExistNickNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceIsExistNickNameArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceIsExistNickNameResult struct {
	Success *userv2vo.ResBooleanMsg `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceIsExistNickNameResult() *UserModuleV2BizServiceIsExistNickNameResult {
	return &UserModuleV2BizServiceIsExistNickNameResult{}
}

var UserModuleV2BizServiceIsExistNickNameResult_Success_DEFAULT *userv2vo.ResBooleanMsg

func (p *UserModuleV2BizServiceIsExistNickNameResult) GetSuccess() *userv2vo.ResBooleanMsg {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceIsExistNickNameResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceIsExistNickNameResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceIsExistNickNameResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.ResBooleanMsg{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "isExistNickName_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceIsExistNickNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceIsExistNickNameResult(%+v)", *p)
}

// Attributes:
//   - NickNames
type UserModuleV2BizServiceIsExistNickNameListArgs struct {
	NickNames []string `thrift:"nickNames,1,required" db:"nickNames" json:"nickNames"`
}

func NewUserModuleV2BizServiceIsExistNickNameListArgs() *UserModuleV2BizServiceIsExistNickNameListArgs {
	return &UserModuleV2BizServiceIsExistNickNameListArgs{}
}

func (p *UserModuleV2BizServiceIsExistNickNameListArgs) GetNickNames() []string {
	return p.NickNames
}
func (p *UserModuleV2BizServiceIsExistNickNameListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetNickNames bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetNickNames = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetNickNames {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field NickNames is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.NickNames = tSlice
	for i := 0; i < size; i++ {
		var _elem212 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem212 = v
		}
		p.NickNames = append(p.NickNames, _elem212)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "isExistNickNameList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "nickNames", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:nickNames: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.NickNames)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.NickNames {
		if err := oprot.WriteString(ctx, string(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:nickNames: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceIsExistNickNameListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceIsExistNickNameListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceIsExistNickNameListResult struct {
	Success *userv2vo.ResStringListMsg `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceIsExistNickNameListResult() *UserModuleV2BizServiceIsExistNickNameListResult {
	return &UserModuleV2BizServiceIsExistNickNameListResult{}
}

var UserModuleV2BizServiceIsExistNickNameListResult_Success_DEFAULT *userv2vo.ResStringListMsg

func (p *UserModuleV2BizServiceIsExistNickNameListResult) GetSuccess() *userv2vo.ResStringListMsg {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceIsExistNickNameListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceIsExistNickNameListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.ResStringListMsg{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "isExistNickNameList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceIsExistNickNameListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceIsExistNickNameListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceIsExistNickNameListResult(%+v)", *p)
}

// Attributes:
//   - KugouIds
type UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs struct {
	KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
}

func NewUserModuleV2BizServiceGetUserConstellationByKugouIdsArgs() *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs {
	return &UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs{}
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs) GetKugouIds() []int64 {
	return p.KugouIds
}
func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIds bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIds = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIds {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIds = tSlice
	for i := 0; i < size; i++ {
		var _elem213 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem213 = v
		}
		p.KugouIds = append(p.KugouIds, _elem213)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserConstellationByKugouIds_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIds {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserConstellationByKugouIdsArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserConstellationByKugouIdsResult struct {
	Success *userv2vo.ResLongIntegerMapMsg `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserConstellationByKugouIdsResult() *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult {
	return &UserModuleV2BizServiceGetUserConstellationByKugouIdsResult{}
}

var UserModuleV2BizServiceGetUserConstellationByKugouIdsResult_Success_DEFAULT *userv2vo.ResLongIntegerMapMsg

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) GetSuccess() *userv2vo.ResLongIntegerMapMsg {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserConstellationByKugouIdsResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.ResLongIntegerMapMsg{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserConstellationByKugouIds_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserConstellationByKugouIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserConstellationByKugouIdsResult(%+v)", *p)
}

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs() *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCoreInfoByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCoreInfoByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult struct {
	Success *userv2vo.UserCoreInfoResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserCoreInfoByKugouIdResult() *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult {
	return &UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult_Success_DEFAULT *userv2vo.UserCoreInfoResponse

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) GetSuccess() *userv2vo.UserCoreInfoResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserCoreInfoResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCoreInfoByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCoreInfoByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs() *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem214 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem214 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem214)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCoreInfoListByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult struct {
	Success *userv2vo.UserCoreInfoListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult() *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult_Success_DEFAULT *userv2vo.UserCoreInfoListResponse

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) GetSuccess() *userv2vo.UserCoreInfoListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserCoreInfoListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCoreInfoListByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCoreInfoListByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs() *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs {
	return &UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem215 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem215 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem215)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getCoreInfoMapByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetCoreInfoMapByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult struct {
	Success *userv2vo.UserCoreInfoMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult() *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult {
	return &UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult{}
}

var UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult_Success_DEFAULT *userv2vo.UserCoreInfoMapResponse

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) GetSuccess() *userv2vo.UserCoreInfoMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserCoreInfoMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getCoreInfoMapByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetCoreInfoMapByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs() *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCombineInfoByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCombineInfoByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult struct {
	Success *userv2vo.UserCombineInfoResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserCombineInfoByKugouIdResult() *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult {
	return &UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult_Success_DEFAULT *userv2vo.UserCombineInfoResponse

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) GetSuccess() *userv2vo.UserCombineInfoResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserCombineInfoResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCombineInfoByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCombineInfoByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs() *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs {
	return &UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem216 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem216 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem216)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCombineInfoListByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult struct {
	Success *userv2vo.UserCombineInfoListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult() *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult {
	return &UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult{}
}

var UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult_Success_DEFAULT *userv2vo.UserCombineInfoListResponse

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) GetSuccess() *userv2vo.UserCombineInfoListResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserCombineInfoListResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserCombineInfoListByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserCombineInfoListByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
type UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs struct {
	KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewUserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs() *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs {
	return &UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}
func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem217 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem217 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem217)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getCombineInfoMapByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetCombineInfoMapByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult struct {
	Success *userv2vo.UserCombineInfoMapResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult() *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult {
	return &UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult{}
}

var UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult_Success_DEFAULT *userv2vo.UserCombineInfoMapResponse

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) GetSuccess() *userv2vo.UserCombineInfoMapResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserCombineInfoMapResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getCombineInfoMapByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetCombineInfoMapByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIdList
//   - DynamicUserVO
type UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs struct {
	KugouIdList   []int64                 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
	DynamicUserVO *userv2vo.DynamicUserVO `thrift:"dynamicUserVO,2,required" db:"dynamicUserVO" json:"dynamicUserVO"`
}

func NewUserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs() *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs {
	return &UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs{}
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) GetKugouIdList() []int64 {
	return p.KugouIdList
}

var UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs_DynamicUserVO_DEFAULT *userv2vo.DynamicUserVO

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) GetDynamicUserVO() *userv2vo.DynamicUserVO {
	if !p.IsSetDynamicUserVO() {
		return UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs_DynamicUserVO_DEFAULT
	}
	return p.DynamicUserVO
}
func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) IsSetDynamicUserVO() bool {
	return p.DynamicUserVO != nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIdList bool = false
	var issetDynamicUserVO bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetDynamicUserVO = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"))
	}
	if !issetDynamicUserVO {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field DynamicUserVO is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem218 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem218 = v
		}
		p.KugouIdList = append(p.KugouIdList, _elem218)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.DynamicUserVO = &userv2vo.DynamicUserVO{}
	if err := p.DynamicUserVO.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.DynamicUserVO), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getDynamicUserInfoByKugouIdList_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIdList {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "dynamicUserVO", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:dynamicUserVO: ", p), err)
	}
	if err := p.DynamicUserVO.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.DynamicUserVO), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:dynamicUserVO: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult struct {
	Success *userv2vo.UserDynamicResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult() *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult {
	return &UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult{}
}

var UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult_Success_DEFAULT *userv2vo.UserDynamicResponse

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) GetSuccess() *userv2vo.UserDynamicResponse {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.UserDynamicResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getDynamicUserInfoByKugouIdList_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetDynamicUserInfoByKugouIdListResult(%+v)", *p)
}

// Attributes:
//   - KugouIds
type UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs struct {
	KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
}

func NewUserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs() *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs {
	return &UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs{}
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs) GetKugouIds() []int64 {
	return p.KugouIds
}
func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIds bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIds = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIds {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIds = tSlice
	for i := 0; i < size; i++ {
		var _elem219 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem219 = v
		}
		p.KugouIds = append(p.KugouIds, _elem219)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserIdMappingByKugouIds_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIds {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserIdMappingByKugouIdsArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult struct {
	Success *userv2vo.ResIDMapMsg `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserIdMappingByKugouIdsResult() *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult {
	return &UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult{}
}

var UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult_Success_DEFAULT *userv2vo.ResIDMapMsg

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) GetSuccess() *userv2vo.ResIDMapMsg {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.ResIDMapMsg{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserIdMappingByKugouIds_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserIdMappingByKugouIdsResult(%+v)", *p)
}

// Attributes:
//   - UserIds
type UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs struct {
	UserIds []int64 `thrift:"userIds,1,required" db:"userIds" json:"userIds"`
}

func NewUserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs() *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs {
	return &UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs{}
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs) GetUserIds() []int64 {
	return p.UserIds
}
func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetUserIds bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetUserIds = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetUserIds {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserIds is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.UserIds = tSlice
	for i := 0; i < size; i++ {
		var _elem220 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem220 = v
		}
		p.UserIds = append(p.UserIds, _elem220)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getKugouIdMappingByUserIds_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userIds", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userIds: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.UserIds)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.UserIds {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userIds: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetKugouIdMappingByUserIdsArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult struct {
	Success *userv2vo.ResIDMapMsg `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetKugouIdMappingByUserIdsResult() *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult {
	return &UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult{}
}

var UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult_Success_DEFAULT *userv2vo.ResIDMapMsg

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) GetSuccess() *userv2vo.ResIDMapMsg {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult_Success_DEFAULT
	}
	return p.Success
}
func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &userv2vo.ResIDMapMsg{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getKugouIdMappingByUserIds_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetKugouIdMappingByUserIdsResult(%+v)", *p)
}

// Attributes:
//   - KugouId
type UserModuleV2BizServiceGetUserIdByKugouIdArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewUserModuleV2BizServiceGetUserIdByKugouIdArgs() *UserModuleV2BizServiceGetUserIdByKugouIdArgs {
	return &UserModuleV2BizServiceGetUserIdByKugouIdArgs{}
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdArgs) GetKugouId() int64 {
	return p.KugouId
}
func (p *UserModuleV2BizServiceGetUserIdByKugouIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserIdByKugouId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserIdByKugouIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetUserIdByKugouIdResult struct {
	Success *int64 `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetUserIdByKugouIdResult() *UserModuleV2BizServiceGetUserIdByKugouIdResult {
	return &UserModuleV2BizServiceGetUserIdByKugouIdResult{}
}

var UserModuleV2BizServiceGetUserIdByKugouIdResult_Success_DEFAULT int64

func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) GetSuccess() int64 {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetUserIdByKugouIdResult_Success_DEFAULT
	}
	return *p.Success
}
func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 0: ", err)
	} else {
		p.Success = &v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserIdByKugouId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.I64, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.Success)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.success (0) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetUserIdByKugouIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetUserIdByKugouIdResult(%+v)", *p)
}

// Attributes:
//   - UserId
type UserModuleV2BizServiceGetKugouIdByUserIdArgs struct {
	UserId int64 `thrift:"userId,1,required" db:"userId" json:"userId"`
}

func NewUserModuleV2BizServiceGetKugouIdByUserIdArgs() *UserModuleV2BizServiceGetKugouIdByUserIdArgs {
	return &UserModuleV2BizServiceGetKugouIdByUserIdArgs{}
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdArgs) GetUserId() int64 {
	return p.UserId
}
func (p *UserModuleV2BizServiceGetKugouIdByUserIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetUserId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetUserId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetUserId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserId is not set"))
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getKugouIdByUserId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.UserId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userId: ", p), err)
	}
	return err
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetKugouIdByUserIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type UserModuleV2BizServiceGetKugouIdByUserIdResult struct {
	Success *int64 `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewUserModuleV2BizServiceGetKugouIdByUserIdResult() *UserModuleV2BizServiceGetKugouIdByUserIdResult {
	return &UserModuleV2BizServiceGetKugouIdByUserIdResult{}
}

var UserModuleV2BizServiceGetKugouIdByUserIdResult_Success_DEFAULT int64

func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) GetSuccess() int64 {
	if !p.IsSetSuccess() {
		return UserModuleV2BizServiceGetKugouIdByUserIdResult_Success_DEFAULT
	}
	return *p.Success
}
func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 0: ", err)
	} else {
		p.Success = &v
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getKugouIdByUserId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.I64, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.Success)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.success (0) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *UserModuleV2BizServiceGetKugouIdByUserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserModuleV2BizServiceGetKugouIdByUserIdResult(%+v)", *p)
}
