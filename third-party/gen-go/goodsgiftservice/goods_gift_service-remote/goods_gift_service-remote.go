// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
   "kugou_adapter_service/third-party/gen-go/goodsgiftservice"
  "math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
)

var _ = goodsgiftservice.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  AllSimpleGiftInfoResult getAllSimpleGiftInfo(GetAllGiftInfoRequest request)")
  fmt.Fprintln(os.<PERSON>r, "  AllSimpleGiftInfoResult getAllSimpleGiftInfoV2(GetAllGiftInfoRequestV2 request)")
  fmt.Fprintln(os.Stderr, "  AllSimpleGiftInfoResult getAllSimpleGiftInfoV3(GetAllGiftInfoRequestV3 request)")
  fmt.Fprintln(os.Stderr, "  SimpleGiftInfoResult getSimpleGiftInfo(GetGiftInfoRequest request)")
  fmt.Fprintln(os.Stderr, "  AllGiftInfoResult getAllGiftInfo(GetAllGiftInfoRequest request)")
  fmt.Fprintln(os.Stderr, "  AllGiftInfoResult getAllGiftInfoV2(GetAllGiftInfoRequestV2 request)")
  fmt.Fprintln(os.Stderr, "  AllGiftInfoResult getAllGiftInfoV3(GetAllGiftInfoRequestV3 request)")
  fmt.Fprintln(os.Stderr, "  ExtGiftInfoResult getAllExtGiftInfo(GetAllGiftInfoRequest request)")
  fmt.Fprintln(os.Stderr, "  ExtGiftInfoResult getAllExtGiftInfoV2(GetAllGiftInfoRequestV2 request)")
  fmt.Fprintln(os.Stderr, "  GiftInfoResult getCustomizedGiftInfo(GetCustomizedGiftRequest request)")
  fmt.Fprintln(os.Stderr, "  AllGiftListResult getCustomizedPCRoomGiftList(PCRoomGiftListReq pcRoomGiftListReq)")
  fmt.Fprintln(os.Stderr, "  DefaultGiftListResult getDefaultGiftList(GetDefaultGiftListRequest request)")
  fmt.Fprintln(os.Stderr, "  HappyGiftInfoResult getHappyGiftInfoList(GetHappyGiftInfoRequest request)")
  fmt.Fprintln(os.Stderr, "  HotGiftInfoResult getHotGiftInfoList(GetHotGiftInfoRequest request)")
  fmt.Fprintln(os.Stderr, "  AllGiftListResult getCustomizedRoomGiftList(RoomGiftListReq roomGiftListReq)")
  fmt.Fprintln(os.Stderr, "  AllGiftListResult getCustomizedRoomGiftListV2(RoomGiftListReqV2 roomGiftListReq)")
  fmt.Fprintln(os.Stderr, "  GetGiftConditionResult getGiftConditionList(GetGiftConditionRequest request)")
  fmt.Fprintln(os.Stderr, "  GiftListCustomizationResult getGiftListCustomizationList(GiftListCustomizationRequest request)")
  fmt.Fprintln(os.Stderr, "  GetGiftCategoryResult getGiftCategoryList(GetGiftCategoryRequest request)")
  fmt.Fprintln(os.Stderr, "  OperationGiftResult getOperationGiftInfo(GetOperationGiftRequest request)")
  fmt.Fprintln(os.Stderr, "  SpecialOperationGiftResult getSpecialOperationGiftInfo(GetOperationGiftRequest request)")
  fmt.Fprintln(os.Stderr, "  LevelResGiftResp getGiftLevelResList(LevelResGiftReq levelResGiftReq)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := GoodsGiftService.NewGoodsGiftServiceClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "getAllSimpleGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllSimpleGiftInfo requires 1 args")
      flag.Usage()
    }
    arg183 := flag.Arg(1)
    mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
    defer mbTrans184.Close()
    _, err185 := mbTrans184.WriteString(arg183)
    if err185 != nil {
      Usage()
      return
    }
    factory186 := thrift.NewTJSONProtocolFactory()
    jsProt187 := factory186.GetProtocol(mbTrans184)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequest()
    err188 := argvalue0.Read(context.Background(), jsProt187)
    if err188 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllSimpleGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllSimpleGiftInfoV2":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllSimpleGiftInfoV2 requires 1 args")
      flag.Usage()
    }
    arg189 := flag.Arg(1)
    mbTrans190 := thrift.NewTMemoryBufferLen(len(arg189))
    defer mbTrans190.Close()
    _, err191 := mbTrans190.WriteString(arg189)
    if err191 != nil {
      Usage()
      return
    }
    factory192 := thrift.NewTJSONProtocolFactory()
    jsProt193 := factory192.GetProtocol(mbTrans190)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequestV2()
    err194 := argvalue0.Read(context.Background(), jsProt193)
    if err194 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllSimpleGiftInfoV2(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllSimpleGiftInfoV3":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllSimpleGiftInfoV3 requires 1 args")
      flag.Usage()
    }
    arg195 := flag.Arg(1)
    mbTrans196 := thrift.NewTMemoryBufferLen(len(arg195))
    defer mbTrans196.Close()
    _, err197 := mbTrans196.WriteString(arg195)
    if err197 != nil {
      Usage()
      return
    }
    factory198 := thrift.NewTJSONProtocolFactory()
    jsProt199 := factory198.GetProtocol(mbTrans196)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequestV3()
    err200 := argvalue0.Read(context.Background(), jsProt199)
    if err200 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllSimpleGiftInfoV3(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getSimpleGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetSimpleGiftInfo requires 1 args")
      flag.Usage()
    }
    arg201 := flag.Arg(1)
    mbTrans202 := thrift.NewTMemoryBufferLen(len(arg201))
    defer mbTrans202.Close()
    _, err203 := mbTrans202.WriteString(arg201)
    if err203 != nil {
      Usage()
      return
    }
    factory204 := thrift.NewTJSONProtocolFactory()
    jsProt205 := factory204.GetProtocol(mbTrans202)
    argvalue0 := GoodsGiftService.NewGetGiftInfoRequest()
    err206 := argvalue0.Read(context.Background(), jsProt205)
    if err206 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetSimpleGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllGiftInfo requires 1 args")
      flag.Usage()
    }
    arg207 := flag.Arg(1)
    mbTrans208 := thrift.NewTMemoryBufferLen(len(arg207))
    defer mbTrans208.Close()
    _, err209 := mbTrans208.WriteString(arg207)
    if err209 != nil {
      Usage()
      return
    }
    factory210 := thrift.NewTJSONProtocolFactory()
    jsProt211 := factory210.GetProtocol(mbTrans208)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequest()
    err212 := argvalue0.Read(context.Background(), jsProt211)
    if err212 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllGiftInfoV2":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllGiftInfoV2 requires 1 args")
      flag.Usage()
    }
    arg213 := flag.Arg(1)
    mbTrans214 := thrift.NewTMemoryBufferLen(len(arg213))
    defer mbTrans214.Close()
    _, err215 := mbTrans214.WriteString(arg213)
    if err215 != nil {
      Usage()
      return
    }
    factory216 := thrift.NewTJSONProtocolFactory()
    jsProt217 := factory216.GetProtocol(mbTrans214)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequestV2()
    err218 := argvalue0.Read(context.Background(), jsProt217)
    if err218 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllGiftInfoV2(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllGiftInfoV3":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllGiftInfoV3 requires 1 args")
      flag.Usage()
    }
    arg219 := flag.Arg(1)
    mbTrans220 := thrift.NewTMemoryBufferLen(len(arg219))
    defer mbTrans220.Close()
    _, err221 := mbTrans220.WriteString(arg219)
    if err221 != nil {
      Usage()
      return
    }
    factory222 := thrift.NewTJSONProtocolFactory()
    jsProt223 := factory222.GetProtocol(mbTrans220)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequestV3()
    err224 := argvalue0.Read(context.Background(), jsProt223)
    if err224 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllGiftInfoV3(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllExtGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllExtGiftInfo requires 1 args")
      flag.Usage()
    }
    arg225 := flag.Arg(1)
    mbTrans226 := thrift.NewTMemoryBufferLen(len(arg225))
    defer mbTrans226.Close()
    _, err227 := mbTrans226.WriteString(arg225)
    if err227 != nil {
      Usage()
      return
    }
    factory228 := thrift.NewTJSONProtocolFactory()
    jsProt229 := factory228.GetProtocol(mbTrans226)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequest()
    err230 := argvalue0.Read(context.Background(), jsProt229)
    if err230 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllExtGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getAllExtGiftInfoV2":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetAllExtGiftInfoV2 requires 1 args")
      flag.Usage()
    }
    arg231 := flag.Arg(1)
    mbTrans232 := thrift.NewTMemoryBufferLen(len(arg231))
    defer mbTrans232.Close()
    _, err233 := mbTrans232.WriteString(arg231)
    if err233 != nil {
      Usage()
      return
    }
    factory234 := thrift.NewTJSONProtocolFactory()
    jsProt235 := factory234.GetProtocol(mbTrans232)
    argvalue0 := GoodsGiftService.NewGetAllGiftInfoRequestV2()
    err236 := argvalue0.Read(context.Background(), jsProt235)
    if err236 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetAllExtGiftInfoV2(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getCustomizedGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetCustomizedGiftInfo requires 1 args")
      flag.Usage()
    }
    arg237 := flag.Arg(1)
    mbTrans238 := thrift.NewTMemoryBufferLen(len(arg237))
    defer mbTrans238.Close()
    _, err239 := mbTrans238.WriteString(arg237)
    if err239 != nil {
      Usage()
      return
    }
    factory240 := thrift.NewTJSONProtocolFactory()
    jsProt241 := factory240.GetProtocol(mbTrans238)
    argvalue0 := GoodsGiftService.NewGetCustomizedGiftRequest()
    err242 := argvalue0.Read(context.Background(), jsProt241)
    if err242 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetCustomizedGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getCustomizedPCRoomGiftList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetCustomizedPCRoomGiftList requires 1 args")
      flag.Usage()
    }
    arg243 := flag.Arg(1)
    mbTrans244 := thrift.NewTMemoryBufferLen(len(arg243))
    defer mbTrans244.Close()
    _, err245 := mbTrans244.WriteString(arg243)
    if err245 != nil {
      Usage()
      return
    }
    factory246 := thrift.NewTJSONProtocolFactory()
    jsProt247 := factory246.GetProtocol(mbTrans244)
    argvalue0 := GoodsGiftService.NewPCRoomGiftListReq()
    err248 := argvalue0.Read(context.Background(), jsProt247)
    if err248 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetCustomizedPCRoomGiftList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getDefaultGiftList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetDefaultGiftList requires 1 args")
      flag.Usage()
    }
    arg249 := flag.Arg(1)
    mbTrans250 := thrift.NewTMemoryBufferLen(len(arg249))
    defer mbTrans250.Close()
    _, err251 := mbTrans250.WriteString(arg249)
    if err251 != nil {
      Usage()
      return
    }
    factory252 := thrift.NewTJSONProtocolFactory()
    jsProt253 := factory252.GetProtocol(mbTrans250)
    argvalue0 := GoodsGiftService.NewGetDefaultGiftListRequest()
    err254 := argvalue0.Read(context.Background(), jsProt253)
    if err254 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetDefaultGiftList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getHappyGiftInfoList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetHappyGiftInfoList requires 1 args")
      flag.Usage()
    }
    arg255 := flag.Arg(1)
    mbTrans256 := thrift.NewTMemoryBufferLen(len(arg255))
    defer mbTrans256.Close()
    _, err257 := mbTrans256.WriteString(arg255)
    if err257 != nil {
      Usage()
      return
    }
    factory258 := thrift.NewTJSONProtocolFactory()
    jsProt259 := factory258.GetProtocol(mbTrans256)
    argvalue0 := GoodsGiftService.NewGetHappyGiftInfoRequest()
    err260 := argvalue0.Read(context.Background(), jsProt259)
    if err260 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetHappyGiftInfoList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getHotGiftInfoList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetHotGiftInfoList requires 1 args")
      flag.Usage()
    }
    arg261 := flag.Arg(1)
    mbTrans262 := thrift.NewTMemoryBufferLen(len(arg261))
    defer mbTrans262.Close()
    _, err263 := mbTrans262.WriteString(arg261)
    if err263 != nil {
      Usage()
      return
    }
    factory264 := thrift.NewTJSONProtocolFactory()
    jsProt265 := factory264.GetProtocol(mbTrans262)
    argvalue0 := GoodsGiftService.NewGetHotGiftInfoRequest()
    err266 := argvalue0.Read(context.Background(), jsProt265)
    if err266 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetHotGiftInfoList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getCustomizedRoomGiftList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetCustomizedRoomGiftList requires 1 args")
      flag.Usage()
    }
    arg267 := flag.Arg(1)
    mbTrans268 := thrift.NewTMemoryBufferLen(len(arg267))
    defer mbTrans268.Close()
    _, err269 := mbTrans268.WriteString(arg267)
    if err269 != nil {
      Usage()
      return
    }
    factory270 := thrift.NewTJSONProtocolFactory()
    jsProt271 := factory270.GetProtocol(mbTrans268)
    argvalue0 := GoodsGiftService.NewRoomGiftListReq()
    err272 := argvalue0.Read(context.Background(), jsProt271)
    if err272 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetCustomizedRoomGiftList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getCustomizedRoomGiftListV2":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetCustomizedRoomGiftListV2 requires 1 args")
      flag.Usage()
    }
    arg273 := flag.Arg(1)
    mbTrans274 := thrift.NewTMemoryBufferLen(len(arg273))
    defer mbTrans274.Close()
    _, err275 := mbTrans274.WriteString(arg273)
    if err275 != nil {
      Usage()
      return
    }
    factory276 := thrift.NewTJSONProtocolFactory()
    jsProt277 := factory276.GetProtocol(mbTrans274)
    argvalue0 := GoodsGiftService.NewRoomGiftListReqV2()
    err278 := argvalue0.Read(context.Background(), jsProt277)
    if err278 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetCustomizedRoomGiftListV2(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getGiftConditionList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetGiftConditionList requires 1 args")
      flag.Usage()
    }
    arg279 := flag.Arg(1)
    mbTrans280 := thrift.NewTMemoryBufferLen(len(arg279))
    defer mbTrans280.Close()
    _, err281 := mbTrans280.WriteString(arg279)
    if err281 != nil {
      Usage()
      return
    }
    factory282 := thrift.NewTJSONProtocolFactory()
    jsProt283 := factory282.GetProtocol(mbTrans280)
    argvalue0 := GoodsGiftService.NewGetGiftConditionRequest()
    err284 := argvalue0.Read(context.Background(), jsProt283)
    if err284 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetGiftConditionList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getGiftListCustomizationList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetGiftListCustomizationList requires 1 args")
      flag.Usage()
    }
    arg285 := flag.Arg(1)
    mbTrans286 := thrift.NewTMemoryBufferLen(len(arg285))
    defer mbTrans286.Close()
    _, err287 := mbTrans286.WriteString(arg285)
    if err287 != nil {
      Usage()
      return
    }
    factory288 := thrift.NewTJSONProtocolFactory()
    jsProt289 := factory288.GetProtocol(mbTrans286)
    argvalue0 := GoodsGiftService.NewGiftListCustomizationRequest()
    err290 := argvalue0.Read(context.Background(), jsProt289)
    if err290 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetGiftListCustomizationList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getGiftCategoryList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetGiftCategoryList requires 1 args")
      flag.Usage()
    }
    arg291 := flag.Arg(1)
    mbTrans292 := thrift.NewTMemoryBufferLen(len(arg291))
    defer mbTrans292.Close()
    _, err293 := mbTrans292.WriteString(arg291)
    if err293 != nil {
      Usage()
      return
    }
    factory294 := thrift.NewTJSONProtocolFactory()
    jsProt295 := factory294.GetProtocol(mbTrans292)
    argvalue0 := GoodsGiftService.NewGetGiftCategoryRequest()
    err296 := argvalue0.Read(context.Background(), jsProt295)
    if err296 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetGiftCategoryList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getOperationGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetOperationGiftInfo requires 1 args")
      flag.Usage()
    }
    arg297 := flag.Arg(1)
    mbTrans298 := thrift.NewTMemoryBufferLen(len(arg297))
    defer mbTrans298.Close()
    _, err299 := mbTrans298.WriteString(arg297)
    if err299 != nil {
      Usage()
      return
    }
    factory300 := thrift.NewTJSONProtocolFactory()
    jsProt301 := factory300.GetProtocol(mbTrans298)
    argvalue0 := GoodsGiftService.NewGetOperationGiftRequest()
    err302 := argvalue0.Read(context.Background(), jsProt301)
    if err302 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetOperationGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getSpecialOperationGiftInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetSpecialOperationGiftInfo requires 1 args")
      flag.Usage()
    }
    arg303 := flag.Arg(1)
    mbTrans304 := thrift.NewTMemoryBufferLen(len(arg303))
    defer mbTrans304.Close()
    _, err305 := mbTrans304.WriteString(arg303)
    if err305 != nil {
      Usage()
      return
    }
    factory306 := thrift.NewTJSONProtocolFactory()
    jsProt307 := factory306.GetProtocol(mbTrans304)
    argvalue0 := GoodsGiftService.NewGetOperationGiftRequest()
    err308 := argvalue0.Read(context.Background(), jsProt307)
    if err308 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetSpecialOperationGiftInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getGiftLevelResList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetGiftLevelResList requires 1 args")
      flag.Usage()
    }
    arg309 := flag.Arg(1)
    mbTrans310 := thrift.NewTMemoryBufferLen(len(arg309))
    defer mbTrans310.Close()
    _, err311 := mbTrans310.WriteString(arg309)
    if err311 != nil {
      Usage()
      return
    }
    factory312 := thrift.NewTJSONProtocolFactory()
    jsProt313 := factory312.GetProtocol(mbTrans310)
    argvalue0 := GoodsGiftService.NewLevelResGiftReq()
    err314 := argvalue0.Read(context.Background(), jsProt313)
    if err314 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetGiftLevelResList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
