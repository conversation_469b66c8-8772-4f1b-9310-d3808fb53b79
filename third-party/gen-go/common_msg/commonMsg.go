// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package commonmsg

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - Code
//  - Msg
//  - Result_
type SendResult_ struct {
	Code    int32  `thrift:"code,1,required" db:"code" json:"code"`
	Msg     string `thrift:"msg,2,required" db:"msg" json:"msg"`
	Result_ bool   `thrift:"result,3,required" db:"result" json:"result"`
}

func NewSendResult_() *SendResult_ {
	return &SendResult_{}
}

func (p *SendResult_) GetCode() int32 {
	return p.Code
}

func (p *SendResult_) GetMsg() string {
	return p.Msg
}

func (p *SendResult_) GetResult_() bool {
	return p.Result_
}
func (p *SendResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCode bool = false
	var issetMsg bool = false
	var issetResult_ bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCode = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetResult_ = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCode {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	if !issetResult_ {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Result_ is not set"))
	}
	return nil
}

func (p *SendResult_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *SendResult_) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *SendResult_) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Result_ = v
	}
	return nil
}

func (p *SendResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "SendResult"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *SendResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err)
	}
	return err
}

func (p *SendResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *SendResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "result", thrift.BOOL, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:result: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.Result_)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.result (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:result: ", p), err)
	}
	return err
}

func (p *SendResult_) Equals(other *SendResult_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Code != other.Code {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if p.Result_ != other.Result_ {
		return false
	}
	return true
}

func (p *SendResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendResult_(%+v)", *p)
}

// 通用消息数据结构
//
// Attributes:
//  - Type
//  - Text
//  - Title
//  - TextColor
//  - TextList
//  - Images
//  - LeftImage
//  - Button
//  - ButtonTextColor
//  - ButtonColor
//  - TriggerFunction
//  - Config
//  - BizId
//  - MsgVersionMatch
//  - BackgroundColor
//  - Priority
//  - ShowSecond
type CommonMsgDto struct {
	Type            *int32            `thrift:"type,1" db:"type" json:"type,omitempty"`
	Text            *string           `thrift:"text,2" db:"text" json:"text,omitempty"`
	Title           *string           `thrift:"title,3" db:"title" json:"title,omitempty"`
	TextColor       string            `thrift:"textColor,4" db:"textColor" json:"textColor"`
	TextList        []string          `thrift:"textList,5" db:"textList" json:"textList,omitempty"`
	Images          []string          `thrift:"images,6" db:"images" json:"images,omitempty"`
	LeftImage       *string           `thrift:"leftImage,7" db:"leftImage" json:"leftImage,omitempty"`
	Button          *string           `thrift:"button,8" db:"button" json:"button,omitempty"`
	ButtonTextColor string            `thrift:"buttonTextColor,9" db:"buttonTextColor" json:"buttonTextColor"`
	ButtonColor     []string          `thrift:"buttonColor,10" db:"buttonColor" json:"buttonColor,omitempty"`
	TriggerFunction *int32            `thrift:"triggerFunction,11" db:"triggerFunction" json:"triggerFunction,omitempty"`
	Config          map[string]string `thrift:"config,12" db:"config" json:"config,omitempty"`
	BizId           *int32            `thrift:"bizId,13" db:"bizId" json:"bizId,omitempty"`
	MsgVersionMatch []*VersionMatch   `thrift:"msgVersionMatch,14" db:"msgVersionMatch" json:"msgVersionMatch,omitempty"`
	BackgroundColor []string          `thrift:"backgroundColor,15" db:"backgroundColor" json:"backgroundColor,omitempty"`
	Priority        *int32            `thrift:"priority,16" db:"priority" json:"priority,omitempty"`
	ShowSecond      *int32            `thrift:"showSecond,17" db:"showSecond" json:"showSecond,omitempty"`
}

func NewCommonMsgDto() *CommonMsgDto {
	return &CommonMsgDto{
		TextColor: "#FFFFFFFF",

		ButtonTextColor: "#FFFFFFFF",
	}
}

var CommonMsgDto_Type_DEFAULT int32

func (p *CommonMsgDto) GetType() int32 {
	if !p.IsSetType() {
		return CommonMsgDto_Type_DEFAULT
	}
	return *p.Type
}

var CommonMsgDto_Text_DEFAULT string

func (p *CommonMsgDto) GetText() string {
	if !p.IsSetText() {
		return CommonMsgDto_Text_DEFAULT
	}
	return *p.Text
}

var CommonMsgDto_Title_DEFAULT string

func (p *CommonMsgDto) GetTitle() string {
	if !p.IsSetTitle() {
		return CommonMsgDto_Title_DEFAULT
	}
	return *p.Title
}

var CommonMsgDto_TextColor_DEFAULT string = "#FFFFFFFF"

func (p *CommonMsgDto) GetTextColor() string {
	return p.TextColor
}

var CommonMsgDto_TextList_DEFAULT []string

func (p *CommonMsgDto) GetTextList() []string {
	return p.TextList
}

var CommonMsgDto_Images_DEFAULT []string

func (p *CommonMsgDto) GetImages() []string {
	return p.Images
}

var CommonMsgDto_LeftImage_DEFAULT string

func (p *CommonMsgDto) GetLeftImage() string {
	if !p.IsSetLeftImage() {
		return CommonMsgDto_LeftImage_DEFAULT
	}
	return *p.LeftImage
}

var CommonMsgDto_Button_DEFAULT string

func (p *CommonMsgDto) GetButton() string {
	if !p.IsSetButton() {
		return CommonMsgDto_Button_DEFAULT
	}
	return *p.Button
}

var CommonMsgDto_ButtonTextColor_DEFAULT string = "#FFFFFFFF"

func (p *CommonMsgDto) GetButtonTextColor() string {
	return p.ButtonTextColor
}

var CommonMsgDto_ButtonColor_DEFAULT []string

func (p *CommonMsgDto) GetButtonColor() []string {
	return p.ButtonColor
}

var CommonMsgDto_TriggerFunction_DEFAULT int32

func (p *CommonMsgDto) GetTriggerFunction() int32 {
	if !p.IsSetTriggerFunction() {
		return CommonMsgDto_TriggerFunction_DEFAULT
	}
	return *p.TriggerFunction
}

var CommonMsgDto_Config_DEFAULT map[string]string

func (p *CommonMsgDto) GetConfig() map[string]string {
	return p.Config
}

var CommonMsgDto_BizId_DEFAULT int32

func (p *CommonMsgDto) GetBizId() int32 {
	if !p.IsSetBizId() {
		return CommonMsgDto_BizId_DEFAULT
	}
	return *p.BizId
}

var CommonMsgDto_MsgVersionMatch_DEFAULT []*VersionMatch

func (p *CommonMsgDto) GetMsgVersionMatch() []*VersionMatch {
	return p.MsgVersionMatch
}

var CommonMsgDto_BackgroundColor_DEFAULT []string

func (p *CommonMsgDto) GetBackgroundColor() []string {
	return p.BackgroundColor
}

var CommonMsgDto_Priority_DEFAULT int32

func (p *CommonMsgDto) GetPriority() int32 {
	if !p.IsSetPriority() {
		return CommonMsgDto_Priority_DEFAULT
	}
	return *p.Priority
}

var CommonMsgDto_ShowSecond_DEFAULT int32

func (p *CommonMsgDto) GetShowSecond() int32 {
	if !p.IsSetShowSecond() {
		return CommonMsgDto_ShowSecond_DEFAULT
	}
	return *p.ShowSecond
}
func (p *CommonMsgDto) IsSetType() bool {
	return p.Type != nil
}

func (p *CommonMsgDto) IsSetText() bool {
	return p.Text != nil
}

func (p *CommonMsgDto) IsSetTitle() bool {
	return p.Title != nil
}

func (p *CommonMsgDto) IsSetTextColor() bool {
	return p.TextColor != CommonMsgDto_TextColor_DEFAULT
}

func (p *CommonMsgDto) IsSetTextList() bool {
	return p.TextList != nil
}

func (p *CommonMsgDto) IsSetImages() bool {
	return p.Images != nil
}

func (p *CommonMsgDto) IsSetLeftImage() bool {
	return p.LeftImage != nil
}

func (p *CommonMsgDto) IsSetButton() bool {
	return p.Button != nil
}

func (p *CommonMsgDto) IsSetButtonTextColor() bool {
	return p.ButtonTextColor != CommonMsgDto_ButtonTextColor_DEFAULT
}

func (p *CommonMsgDto) IsSetButtonColor() bool {
	return p.ButtonColor != nil
}

func (p *CommonMsgDto) IsSetTriggerFunction() bool {
	return p.TriggerFunction != nil
}

func (p *CommonMsgDto) IsSetConfig() bool {
	return p.Config != nil
}

func (p *CommonMsgDto) IsSetBizId() bool {
	return p.BizId != nil
}

func (p *CommonMsgDto) IsSetMsgVersionMatch() bool {
	return p.MsgVersionMatch != nil
}

func (p *CommonMsgDto) IsSetBackgroundColor() bool {
	return p.BackgroundColor != nil
}

func (p *CommonMsgDto) IsSetPriority() bool {
	return p.Priority != nil
}

func (p *CommonMsgDto) IsSetShowSecond() bool {
	return p.ShowSecond != nil
}

func (p *CommonMsgDto) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField8(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField9(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField10(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField11(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.MAP {
				if err := p.ReadField12(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField13(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField14(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField15(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField16(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField17(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Type = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Text = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Title = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.TextColor = v
	}
	return nil
}

func (p *CommonMsgDto) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.TextList = tSlice
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem0 = v
		}
		p.TextList = append(p.TextList, _elem0)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.Images = tSlice
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem1 = v
		}
		p.Images = append(p.Images, _elem1)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.LeftImage = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 8: ", err)
	} else {
		p.Button = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 9: ", err)
	} else {
		p.ButtonTextColor = v
	}
	return nil
}

func (p *CommonMsgDto) ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.ButtonColor = tSlice
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem2 = v
		}
		p.ButtonColor = append(p.ButtonColor, _elem2)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 11: ", err)
	} else {
		p.TriggerFunction = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading map begin: ", err)
	}
	tMap := make(map[string]string, size)
	p.Config = tMap
	for i := 0; i < size; i++ {
		var _key3 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_key3 = v
		}
		var _val4 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_val4 = v
		}
		p.Config[_key3] = _val4
	}
	if err := iprot.ReadMapEnd(ctx); err != nil {
		return thrift.PrependError("error reading map end: ", err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 13: ", err)
	} else {
		p.BizId = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*VersionMatch, 0, size)
	p.MsgVersionMatch = tSlice
	for i := 0; i < size; i++ {
		_elem5 := &VersionMatch{}
		if err := _elem5.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem5), err)
		}
		p.MsgVersionMatch = append(p.MsgVersionMatch, _elem5)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.BackgroundColor = tSlice
	for i := 0; i < size; i++ {
		var _elem6 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem6 = v
		}
		p.BackgroundColor = append(p.BackgroundColor, _elem6)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *CommonMsgDto) ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 16: ", err)
	} else {
		p.Priority = &v
	}
	return nil
}

func (p *CommonMsgDto) ReadField17(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 17: ", err)
	} else {
		p.ShowSecond = &v
	}
	return nil
}

func (p *CommonMsgDto) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "CommonMsgDto"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField8(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField9(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField10(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField11(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField12(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField13(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField14(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField15(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField16(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField17(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgDto) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetType() {
		if err := oprot.WriteFieldBegin(ctx, "type", thrift.I32, 1); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:type: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.Type)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.type (1) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 1:type: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetText() {
		if err := oprot.WriteFieldBegin(ctx, "text", thrift.STRING, 2); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:text: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Text)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.text (2) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 2:text: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTitle() {
		if err := oprot.WriteFieldBegin(ctx, "title", thrift.STRING, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:title: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Title)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.title (3) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:title: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTextColor() {
		if err := oprot.WriteFieldBegin(ctx, "textColor", thrift.STRING, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:textColor: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(p.TextColor)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.textColor (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:textColor: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTextList() {
		if err := oprot.WriteFieldBegin(ctx, "textList", thrift.LIST, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:textList: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.TextList)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.TextList {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:textList: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetImages() {
		if err := oprot.WriteFieldBegin(ctx, "images", thrift.LIST, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:images: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.Images)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.Images {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:images: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetLeftImage() {
		if err := oprot.WriteFieldBegin(ctx, "leftImage", thrift.STRING, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:leftImage: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.LeftImage)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.leftImage (7) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:leftImage: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetButton() {
		if err := oprot.WriteFieldBegin(ctx, "button", thrift.STRING, 8); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:button: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.Button)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.button (8) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 8:button: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetButtonTextColor() {
		if err := oprot.WriteFieldBegin(ctx, "buttonTextColor", thrift.STRING, 9); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:buttonTextColor: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(p.ButtonTextColor)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.buttonTextColor (9) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 9:buttonTextColor: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetButtonColor() {
		if err := oprot.WriteFieldBegin(ctx, "buttonColor", thrift.LIST, 10); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:buttonColor: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.ButtonColor)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.ButtonColor {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 10:buttonColor: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTriggerFunction() {
		if err := oprot.WriteFieldBegin(ctx, "triggerFunction", thrift.I32, 11); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:triggerFunction: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.TriggerFunction)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.triggerFunction (11) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 11:triggerFunction: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetConfig() {
		if err := oprot.WriteFieldBegin(ctx, "config", thrift.MAP, 12); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:config: ", p), err)
		}
		if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRING, len(p.Config)); err != nil {
			return thrift.PrependError("error writing map begin: ", err)
		}
		for k, v := range p.Config {
			if err := oprot.WriteString(ctx, string(k)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteMapEnd(ctx); err != nil {
			return thrift.PrependError("error writing map end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 12:config: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetBizId() {
		if err := oprot.WriteFieldBegin(ctx, "bizId", thrift.I32, 13); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:bizId: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.BizId)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.bizId (13) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 13:bizId: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetMsgVersionMatch() {
		if err := oprot.WriteFieldBegin(ctx, "msgVersionMatch", thrift.LIST, 14); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:msgVersionMatch: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.MsgVersionMatch)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.MsgVersionMatch {
			if err := v.Write(ctx, oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 14:msgVersionMatch: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetBackgroundColor() {
		if err := oprot.WriteFieldBegin(ctx, "backgroundColor", thrift.LIST, 15); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:backgroundColor: ", p), err)
		}
		if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.BackgroundColor)); err != nil {
			return thrift.PrependError("error writing list begin: ", err)
		}
		for _, v := range p.BackgroundColor {
			if err := oprot.WriteString(ctx, string(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteListEnd(ctx); err != nil {
			return thrift.PrependError("error writing list end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 15:backgroundColor: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetPriority() {
		if err := oprot.WriteFieldBegin(ctx, "priority", thrift.I32, 16); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:priority: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.Priority)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.priority (16) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 16:priority: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) writeField17(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetShowSecond() {
		if err := oprot.WriteFieldBegin(ctx, "showSecond", thrift.I32, 17); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 17:showSecond: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.ShowSecond)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.showSecond (17) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 17:showSecond: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgDto) Equals(other *CommonMsgDto) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Type != other.Type {
		if p.Type == nil || other.Type == nil {
			return false
		}
		if (*p.Type) != (*other.Type) {
			return false
		}
	}
	if p.Text != other.Text {
		if p.Text == nil || other.Text == nil {
			return false
		}
		if (*p.Text) != (*other.Text) {
			return false
		}
	}
	if p.Title != other.Title {
		if p.Title == nil || other.Title == nil {
			return false
		}
		if (*p.Title) != (*other.Title) {
			return false
		}
	}
	if p.TextColor != other.TextColor {
		return false
	}
	if len(p.TextList) != len(other.TextList) {
		return false
	}
	for i, _tgt := range p.TextList {
		_src7 := other.TextList[i]
		if _tgt != _src7 {
			return false
		}
	}
	if len(p.Images) != len(other.Images) {
		return false
	}
	for i, _tgt := range p.Images {
		_src8 := other.Images[i]
		if _tgt != _src8 {
			return false
		}
	}
	if p.LeftImage != other.LeftImage {
		if p.LeftImage == nil || other.LeftImage == nil {
			return false
		}
		if (*p.LeftImage) != (*other.LeftImage) {
			return false
		}
	}
	if p.Button != other.Button {
		if p.Button == nil || other.Button == nil {
			return false
		}
		if (*p.Button) != (*other.Button) {
			return false
		}
	}
	if p.ButtonTextColor != other.ButtonTextColor {
		return false
	}
	if len(p.ButtonColor) != len(other.ButtonColor) {
		return false
	}
	for i, _tgt := range p.ButtonColor {
		_src9 := other.ButtonColor[i]
		if _tgt != _src9 {
			return false
		}
	}
	if p.TriggerFunction != other.TriggerFunction {
		if p.TriggerFunction == nil || other.TriggerFunction == nil {
			return false
		}
		if (*p.TriggerFunction) != (*other.TriggerFunction) {
			return false
		}
	}
	if len(p.Config) != len(other.Config) {
		return false
	}
	for k, _tgt := range p.Config {
		_src10 := other.Config[k]
		if _tgt != _src10 {
			return false
		}
	}
	if p.BizId != other.BizId {
		if p.BizId == nil || other.BizId == nil {
			return false
		}
		if (*p.BizId) != (*other.BizId) {
			return false
		}
	}
	if len(p.MsgVersionMatch) != len(other.MsgVersionMatch) {
		return false
	}
	for i, _tgt := range p.MsgVersionMatch {
		_src11 := other.MsgVersionMatch[i]
		if !_tgt.Equals(_src11) {
			return false
		}
	}
	if len(p.BackgroundColor) != len(other.BackgroundColor) {
		return false
	}
	for i, _tgt := range p.BackgroundColor {
		_src12 := other.BackgroundColor[i]
		if _tgt != _src12 {
			return false
		}
	}
	if p.Priority != other.Priority {
		if p.Priority == nil || other.Priority == nil {
			return false
		}
		if (*p.Priority) != (*other.Priority) {
			return false
		}
	}
	if p.ShowSecond != other.ShowSecond {
		if p.ShowSecond == nil || other.ShowSecond == nil {
			return false
		}
		if (*p.ShowSecond) != (*other.ShowSecond) {
			return false
		}
	}
	return true
}

func (p *CommonMsgDto) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgDto(%+v)", *p)
}

// 版本控制结构体
//
// Attributes:
//  - StdPlat
//  - Min
//  - Max
type VersionMatch struct {
	StdPlat int32 `thrift:"stdPlat,1,required" db:"stdPlat" json:"stdPlat"`
	Min     int32 `thrift:"min,2,required" db:"min" json:"min"`
	Max     int32 `thrift:"max,3,required" db:"max" json:"max"`
}

func NewVersionMatch() *VersionMatch {
	return &VersionMatch{}
}

func (p *VersionMatch) GetStdPlat() int32 {
	return p.StdPlat
}

func (p *VersionMatch) GetMin() int32 {
	return p.Min
}

func (p *VersionMatch) GetMax() int32 {
	return p.Max
}
func (p *VersionMatch) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetStdPlat bool = false
	var issetMin bool = false
	var issetMax bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetStdPlat = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMin = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetMax = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetStdPlat {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StdPlat is not set"))
	}
	if !issetMin {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Min is not set"))
	}
	if !issetMax {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Max is not set"))
	}
	return nil
}

func (p *VersionMatch) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.StdPlat = v
	}
	return nil
}

func (p *VersionMatch) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Min = v
	}
	return nil
}

func (p *VersionMatch) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Max = v
	}
	return nil
}

func (p *VersionMatch) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "VersionMatch"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *VersionMatch) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "stdPlat", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:stdPlat: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.StdPlat)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.stdPlat (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:stdPlat: ", p), err)
	}
	return err
}

func (p *VersionMatch) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "min", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:min: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Min)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.min (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:min: ", p), err)
	}
	return err
}

func (p *VersionMatch) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "max", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:max: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Max)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.max (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:max: ", p), err)
	}
	return err
}

func (p *VersionMatch) Equals(other *VersionMatch) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.StdPlat != other.StdPlat {
		return false
	}
	if p.Min != other.Min {
		return false
	}
	if p.Max != other.Max {
		return false
	}
	return true
}

func (p *VersionMatch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VersionMatch(%+v)", *p)
}

type CommonMsgService interface { //鱼声通用消息通知
	//

	// 单个房间消息通知
	//
	// Parameters:
	//  - RoomId
	//  - Msg
	SendRoomCommonRoomMsg(ctx context.Context, roomId int32, msg *CommonMsgDto) (_r *SendResult_, _err error)
	// 所有房间消息通知
	//
	// Parameters:
	//  - Msg
	SendAllCommonRoomMsg(ctx context.Context, msg *CommonMsgDto) (_r *SendResult_, _err error)
	// 单个房间消息通知(指定用户可见）
	//
	// Parameters:
	//  - RoomId
	//  - ToKgId
	//  - Msg
	SendCommonRoomMsgToUser(ctx context.Context, roomId int32, toKgId int64, msg *CommonMsgDto) (_r *SendResult_, _err error)
}

//鱼声通用消息通知
//
type CommonMsgServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewCommonMsgServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *CommonMsgServiceClient {
	return &CommonMsgServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewCommonMsgServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *CommonMsgServiceClient {
	return &CommonMsgServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewCommonMsgServiceClient(c thrift.TClient) *CommonMsgServiceClient {
	return &CommonMsgServiceClient{
		c: c,
	}
}

func (p *CommonMsgServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *CommonMsgServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *CommonMsgServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// 单个房间消息通知
//
// Parameters:
//  - RoomId
//  - Msg
func (p *CommonMsgServiceClient) SendRoomCommonRoomMsg(ctx context.Context, roomId int32, msg *CommonMsgDto) (_r *SendResult_, _err error) {
	var _args13 CommonMsgServiceSendRoomCommonRoomMsgArgs
	_args13.RoomId = roomId
	_args13.Msg = msg
	var _result15 CommonMsgServiceSendRoomCommonRoomMsgResult
	var _meta14 thrift.ResponseMeta
	_meta14, _err = p.Client_().Call(ctx, "sendRoomCommonRoomMsg", &_args13, &_result15)
	p.SetLastResponseMeta_(_meta14)
	if _err != nil {
		return
	}
	return _result15.GetSuccess(), nil
}

// 所有房间消息通知
//
// Parameters:
//  - Msg
func (p *CommonMsgServiceClient) SendAllCommonRoomMsg(ctx context.Context, msg *CommonMsgDto) (_r *SendResult_, _err error) {
	var _args16 CommonMsgServiceSendAllCommonRoomMsgArgs
	_args16.Msg = msg
	var _result18 CommonMsgServiceSendAllCommonRoomMsgResult
	var _meta17 thrift.ResponseMeta
	_meta17, _err = p.Client_().Call(ctx, "sendAllCommonRoomMsg", &_args16, &_result18)
	p.SetLastResponseMeta_(_meta17)
	if _err != nil {
		return
	}
	return _result18.GetSuccess(), nil
}

// 单个房间消息通知(指定用户可见）
//
// Parameters:
//  - RoomId
//  - ToKgId
//  - Msg
func (p *CommonMsgServiceClient) SendCommonRoomMsgToUser(ctx context.Context, roomId int32, toKgId int64, msg *CommonMsgDto) (_r *SendResult_, _err error) {
	var _args19 CommonMsgServiceSendCommonRoomMsgToUserArgs
	_args19.RoomId = roomId
	_args19.ToKgId = toKgId
	_args19.Msg = msg
	var _result21 CommonMsgServiceSendCommonRoomMsgToUserResult
	var _meta20 thrift.ResponseMeta
	_meta20, _err = p.Client_().Call(ctx, "sendCommonRoomMsgToUser", &_args19, &_result21)
	p.SetLastResponseMeta_(_meta20)
	if _err != nil {
		return
	}
	return _result21.GetSuccess(), nil
}

type CommonMsgServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      CommonMsgService
}

func (p *CommonMsgServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *CommonMsgServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *CommonMsgServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewCommonMsgServiceProcessor(handler CommonMsgService) *CommonMsgServiceProcessor {

	self22 := &CommonMsgServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self22.processorMap["sendRoomCommonRoomMsg"] = &commonMsgServiceProcessorSendRoomCommonRoomMsg{handler: handler}
	self22.processorMap["sendAllCommonRoomMsg"] = &commonMsgServiceProcessorSendAllCommonRoomMsg{handler: handler}
	self22.processorMap["sendCommonRoomMsgToUser"] = &commonMsgServiceProcessorSendCommonRoomMsgToUser{handler: handler}
	return self22
}

func (p *CommonMsgServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x23 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x23.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x23

}

type commonMsgServiceProcessorSendRoomCommonRoomMsg struct {
	handler CommonMsgService
}

func (p *commonMsgServiceProcessorSendRoomCommonRoomMsg) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err24 error
	args := CommonMsgServiceSendRoomCommonRoomMsgArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendRoomCommonRoomMsg", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := CommonMsgServiceSendRoomCommonRoomMsgResult{}
	if retval, err2 := p.handler.SendRoomCommonRoomMsg(ctx, args.RoomId, args.Msg); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc25 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendRoomCommonRoomMsg: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendRoomCommonRoomMsg", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err24 = thrift.WrapTException(err2)
		}
		if err2 := _exc25.Write(ctx, oprot); _write_err24 == nil && err2 != nil {
			_write_err24 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err24 == nil && err2 != nil {
			_write_err24 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err24 == nil && err2 != nil {
			_write_err24 = thrift.WrapTException(err2)
		}
		if _write_err24 != nil {
			return false, thrift.WrapTException(_write_err24)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendRoomCommonRoomMsg", thrift.REPLY, seqId); err2 != nil {
		_write_err24 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err24 == nil && err2 != nil {
		_write_err24 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err24 == nil && err2 != nil {
		_write_err24 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err24 == nil && err2 != nil {
		_write_err24 = thrift.WrapTException(err2)
	}
	if _write_err24 != nil {
		return false, thrift.WrapTException(_write_err24)
	}
	return true, err
}

type commonMsgServiceProcessorSendAllCommonRoomMsg struct {
	handler CommonMsgService
}

func (p *commonMsgServiceProcessorSendAllCommonRoomMsg) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err26 error
	args := CommonMsgServiceSendAllCommonRoomMsgArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendAllCommonRoomMsg", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := CommonMsgServiceSendAllCommonRoomMsgResult{}
	if retval, err2 := p.handler.SendAllCommonRoomMsg(ctx, args.Msg); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc27 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendAllCommonRoomMsg: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendAllCommonRoomMsg", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if err2 := _exc27.Write(ctx, oprot); _write_err26 == nil && err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err26 == nil && err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err26 == nil && err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if _write_err26 != nil {
			return false, thrift.WrapTException(_write_err26)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendAllCommonRoomMsg", thrift.REPLY, seqId); err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err26 == nil && err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err26 == nil && err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err26 == nil && err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if _write_err26 != nil {
		return false, thrift.WrapTException(_write_err26)
	}
	return true, err
}

type commonMsgServiceProcessorSendCommonRoomMsgToUser struct {
	handler CommonMsgService
}

func (p *commonMsgServiceProcessorSendCommonRoomMsgToUser) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err28 error
	args := CommonMsgServiceSendCommonRoomMsgToUserArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendCommonRoomMsgToUser", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := CommonMsgServiceSendCommonRoomMsgToUserResult{}
	if retval, err2 := p.handler.SendCommonRoomMsgToUser(ctx, args.RoomId, args.ToKgId, args.Msg); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc29 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendCommonRoomMsgToUser: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendCommonRoomMsgToUser", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if err2 := _exc29.Write(ctx, oprot); _write_err28 == nil && err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err28 == nil && err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err28 == nil && err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if _write_err28 != nil {
			return false, thrift.WrapTException(_write_err28)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendCommonRoomMsgToUser", thrift.REPLY, seqId); err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err28 == nil && err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err28 == nil && err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err28 == nil && err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if _write_err28 != nil {
		return false, thrift.WrapTException(_write_err28)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - RoomId
//  - Msg
type CommonMsgServiceSendRoomCommonRoomMsgArgs struct {
	RoomId int32         `thrift:"roomId,1" db:"roomId" json:"roomId"`
	Msg    *CommonMsgDto `thrift:"msg,2" db:"msg" json:"msg"`
}

func NewCommonMsgServiceSendRoomCommonRoomMsgArgs() *CommonMsgServiceSendRoomCommonRoomMsgArgs {
	return &CommonMsgServiceSendRoomCommonRoomMsgArgs{}
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) GetRoomId() int32 {
	return p.RoomId
}

var CommonMsgServiceSendRoomCommonRoomMsgArgs_Msg_DEFAULT *CommonMsgDto

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) GetMsg() *CommonMsgDto {
	if !p.IsSetMsg() {
		return CommonMsgServiceSendRoomCommonRoomMsgArgs_Msg_DEFAULT
	}
	return p.Msg
}
func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.Msg = &CommonMsgDto{
		TextColor: "#FFFFFFFF",

		ButtonTextColor: "#FFFFFFFF",
	}
	if err := p.Msg.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Msg), err)
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendRoomCommonRoomMsg_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := p.Msg.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Msg), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgServiceSendRoomCommonRoomMsgArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonMsgServiceSendRoomCommonRoomMsgResult struct {
	Success *SendResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonMsgServiceSendRoomCommonRoomMsgResult() *CommonMsgServiceSendRoomCommonRoomMsgResult {
	return &CommonMsgServiceSendRoomCommonRoomMsgResult{}
}

var CommonMsgServiceSendRoomCommonRoomMsgResult_Success_DEFAULT *SendResult_

func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) GetSuccess() *SendResult_ {
	if !p.IsSetSuccess() {
		return CommonMsgServiceSendRoomCommonRoomMsgResult_Success_DEFAULT
	}
	return p.Success
}
func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &SendResult_{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendRoomCommonRoomMsg_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgServiceSendRoomCommonRoomMsgResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgServiceSendRoomCommonRoomMsgResult(%+v)", *p)
}

// Attributes:
//  - Msg
type CommonMsgServiceSendAllCommonRoomMsgArgs struct {
	Msg *CommonMsgDto `thrift:"msg,1" db:"msg" json:"msg"`
}

func NewCommonMsgServiceSendAllCommonRoomMsgArgs() *CommonMsgServiceSendAllCommonRoomMsgArgs {
	return &CommonMsgServiceSendAllCommonRoomMsgArgs{}
}

var CommonMsgServiceSendAllCommonRoomMsgArgs_Msg_DEFAULT *CommonMsgDto

func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) GetMsg() *CommonMsgDto {
	if !p.IsSetMsg() {
		return CommonMsgServiceSendAllCommonRoomMsgArgs_Msg_DEFAULT
	}
	return p.Msg
}
func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Msg = &CommonMsgDto{
		TextColor: "#FFFFFFFF",

		ButtonTextColor: "#FFFFFFFF",
	}
	if err := p.Msg.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Msg), err)
	}
	return nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendAllCommonRoomMsg_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:msg: ", p), err)
	}
	if err := p.Msg.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Msg), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:msg: ", p), err)
	}
	return err
}

func (p *CommonMsgServiceSendAllCommonRoomMsgArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgServiceSendAllCommonRoomMsgArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonMsgServiceSendAllCommonRoomMsgResult struct {
	Success *SendResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonMsgServiceSendAllCommonRoomMsgResult() *CommonMsgServiceSendAllCommonRoomMsgResult {
	return &CommonMsgServiceSendAllCommonRoomMsgResult{}
}

var CommonMsgServiceSendAllCommonRoomMsgResult_Success_DEFAULT *SendResult_

func (p *CommonMsgServiceSendAllCommonRoomMsgResult) GetSuccess() *SendResult_ {
	if !p.IsSetSuccess() {
		return CommonMsgServiceSendAllCommonRoomMsgResult_Success_DEFAULT
	}
	return p.Success
}
func (p *CommonMsgServiceSendAllCommonRoomMsgResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &SendResult_{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendAllCommonRoomMsg_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgServiceSendAllCommonRoomMsgResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgServiceSendAllCommonRoomMsgResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgServiceSendAllCommonRoomMsgResult(%+v)", *p)
}

// Attributes:
//  - RoomId
//  - ToKgId
//  - Msg
type CommonMsgServiceSendCommonRoomMsgToUserArgs struct {
	RoomId int32         `thrift:"roomId,1" db:"roomId" json:"roomId"`
	ToKgId int64         `thrift:"toKgId,2" db:"toKgId" json:"toKgId"`
	Msg    *CommonMsgDto `thrift:"msg,3" db:"msg" json:"msg"`
}

func NewCommonMsgServiceSendCommonRoomMsgToUserArgs() *CommonMsgServiceSendCommonRoomMsgToUserArgs {
	return &CommonMsgServiceSendCommonRoomMsgToUserArgs{}
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) GetToKgId() int64 {
	return p.ToKgId
}

var CommonMsgServiceSendCommonRoomMsgToUserArgs_Msg_DEFAULT *CommonMsgDto

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) GetMsg() *CommonMsgDto {
	if !p.IsSetMsg() {
		return CommonMsgServiceSendCommonRoomMsgToUserArgs_Msg_DEFAULT
	}
	return p.Msg
}
func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ToKgId = v
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	p.Msg = &CommonMsgDto{
		TextColor: "#FFFFFFFF",

		ButtonTextColor: "#FFFFFFFF",
	}
	if err := p.Msg.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Msg), err)
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendCommonRoomMsgToUser_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKgId", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:toKgId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKgId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKgId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:toKgId: ", p), err)
	}
	return err
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRUCT, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:msg: ", p), err)
	}
	if err := p.Msg.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Msg), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:msg: ", p), err)
	}
	return err
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgServiceSendCommonRoomMsgToUserArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonMsgServiceSendCommonRoomMsgToUserResult struct {
	Success *SendResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonMsgServiceSendCommonRoomMsgToUserResult() *CommonMsgServiceSendCommonRoomMsgToUserResult {
	return &CommonMsgServiceSendCommonRoomMsgToUserResult{}
}

var CommonMsgServiceSendCommonRoomMsgToUserResult_Success_DEFAULT *SendResult_

func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) GetSuccess() *SendResult_ {
	if !p.IsSetSuccess() {
		return CommonMsgServiceSendCommonRoomMsgToUserResult_Success_DEFAULT
	}
	return p.Success
}
func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &SendResult_{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendCommonRoomMsgToUser_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *CommonMsgServiceSendCommonRoomMsgToUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CommonMsgServiceSendCommonRoomMsgToUserResult(%+v)", *p)
}
