// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package userstarvipservice

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - AppId: APPID
//  - KugouId: 酷狗ID
//  - Timestamp: 时间戳 (秒)
//  - Sign: md5(所有参数名字升序排列对应的数值拼接（逗号拼接） + 固定密钥[加盐][appId对应的描述])
type StarvipRequest struct {
  AppId int32 `thrift:"appId,1,required" db:"appId" json:"appId"`
  KugouId int64 `thrift:"kugouId,2,required" db:"kugouId" json:"kugouId"`
  Timestamp int64 `thrift:"timestamp,3,required" db:"timestamp" json:"timestamp"`
  Sign string `thrift:"sign,4,required" db:"sign" json:"sign"`
}

func NewStarvipRequest() *StarvipRequest {
  return &StarvipRequest{}
}


func (p *StarvipRequest) GetAppId() int32 {
  return p.AppId
}

func (p *StarvipRequest) GetKugouId() int64 {
  return p.KugouId
}

func (p *StarvipRequest) GetTimestamp() int64 {
  return p.Timestamp
}

func (p *StarvipRequest) GetSign() string {
  return p.Sign
}
func (p *StarvipRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetKugouId bool = false;
  var issetTimestamp bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetTimestamp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetTimestamp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Timestamp is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *StarvipRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *StarvipRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StarvipRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Timestamp = v
}
  return nil
}

func (p *StarvipRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *StarvipRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *StarvipRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:kugouId: ", p), err) }
  return err
}

func (p *StarvipRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:timestamp: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Timestamp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.timestamp (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:timestamp: ", p), err) }
  return err
}

func (p *StarvipRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:sign: ", p), err) }
  return err
}

func (p *StarvipRequest) Equals(other *StarvipRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.KugouId != other.KugouId { return false }
  if p.Timestamp != other.Timestamp { return false }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *StarvipRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipRequest(%+v)", *p)
}

// Attributes:
//  - KugouId: kugouID
//  - MysticStatus: 神秘嘉宾身份1-开启 0-关闭
//  - StarvipType: 是否星钻会员 0 非会员 >0会员 (1-月会员 2-年会员)
//  - StarvipLevel: 星钻等级
//  - URL
//  - MysticName
//  - KingName
//  - BorthType
type StarvipInfoVo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  MysticStatus int32 `thrift:"mysticStatus,2,required" db:"mysticStatus" json:"mysticStatus"`
  StarvipType int32 `thrift:"starvipType,3,required" db:"starvipType" json:"starvipType"`
  StarvipLevel int32 `thrift:"starvipLevel,4,required" db:"starvipLevel" json:"starvipLevel"`
  URL string `thrift:"url,5,required" db:"url" json:"url"`
  MysticName *string `thrift:"mysticName,6" db:"mysticName" json:"mysticName,omitempty"`
  KingName *string `thrift:"kingName,7" db:"kingName" json:"kingName,omitempty"`
  BorthType *int32 `thrift:"borthType,8" db:"borthType" json:"borthType,omitempty"`
}

func NewStarvipInfoVo() *StarvipInfoVo {
  return &StarvipInfoVo{}
}


func (p *StarvipInfoVo) GetKugouId() int64 {
  return p.KugouId
}

func (p *StarvipInfoVo) GetMysticStatus() int32 {
  return p.MysticStatus
}

func (p *StarvipInfoVo) GetStarvipType() int32 {
  return p.StarvipType
}

func (p *StarvipInfoVo) GetStarvipLevel() int32 {
  return p.StarvipLevel
}

func (p *StarvipInfoVo) GetURL() string {
  return p.URL
}
var StarvipInfoVo_MysticName_DEFAULT string
func (p *StarvipInfoVo) GetMysticName() string {
  if !p.IsSetMysticName() {
    return StarvipInfoVo_MysticName_DEFAULT
  }
return *p.MysticName
}
var StarvipInfoVo_KingName_DEFAULT string
func (p *StarvipInfoVo) GetKingName() string {
  if !p.IsSetKingName() {
    return StarvipInfoVo_KingName_DEFAULT
  }
return *p.KingName
}
var StarvipInfoVo_BorthType_DEFAULT int32
func (p *StarvipInfoVo) GetBorthType() int32 {
  if !p.IsSetBorthType() {
    return StarvipInfoVo_BorthType_DEFAULT
  }
return *p.BorthType
}
func (p *StarvipInfoVo) IsSetMysticName() bool {
  return p.MysticName != nil
}

func (p *StarvipInfoVo) IsSetKingName() bool {
  return p.KingName != nil
}

func (p *StarvipInfoVo) IsSetBorthType() bool {
  return p.BorthType != nil
}

func (p *StarvipInfoVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetMysticStatus bool = false;
  var issetStarvipType bool = false;
  var issetStarvipLevel bool = false;
  var issetURL bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMysticStatus = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetStarvipType = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetStarvipLevel = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetURL = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetMysticStatus{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MysticStatus is not set"));
  }
  if !issetStarvipType{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarvipType is not set"));
  }
  if !issetStarvipLevel{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarvipLevel is not set"));
  }
  if !issetURL{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field URL is not set"));
  }
  return nil
}

func (p *StarvipInfoVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.MysticStatus = v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.StarvipType = v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StarvipLevel = v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.URL = v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.MysticName = &v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.KingName = &v
}
  return nil
}

func (p *StarvipInfoVo)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.BorthType = &v
}
  return nil
}

func (p *StarvipInfoVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipInfoVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipInfoVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *StarvipInfoVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "mysticStatus", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:mysticStatus: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.MysticStatus)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.mysticStatus (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:mysticStatus: ", p), err) }
  return err
}

func (p *StarvipInfoVo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starvipType", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:starvipType: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.StarvipType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starvipType (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:starvipType: ", p), err) }
  return err
}

func (p *StarvipInfoVo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starvipLevel", thrift.I32, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starvipLevel: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.StarvipLevel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starvipLevel (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starvipLevel: ", p), err) }
  return err
}

func (p *StarvipInfoVo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "url", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:url: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.URL)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.url (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:url: ", p), err) }
  return err
}

func (p *StarvipInfoVo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMysticName() {
    if err := oprot.WriteFieldBegin(ctx, "mysticName", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:mysticName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MysticName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mysticName (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:mysticName: ", p), err) }
  }
  return err
}

func (p *StarvipInfoVo) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKingName() {
    if err := oprot.WriteFieldBegin(ctx, "kingName", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:kingName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.KingName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kingName (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:kingName: ", p), err) }
  }
  return err
}

func (p *StarvipInfoVo) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBorthType() {
    if err := oprot.WriteFieldBegin(ctx, "borthType", thrift.I32, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:borthType: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.BorthType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.borthType (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:borthType: ", p), err) }
  }
  return err
}

func (p *StarvipInfoVo) Equals(other *StarvipInfoVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.MysticStatus != other.MysticStatus { return false }
  if p.StarvipType != other.StarvipType { return false }
  if p.StarvipLevel != other.StarvipLevel { return false }
  if p.URL != other.URL { return false }
  if p.MysticName != other.MysticName {
    if p.MysticName == nil || other.MysticName == nil {
      return false
    }
    if (*p.MysticName) != (*other.MysticName) { return false }
  }
  if p.KingName != other.KingName {
    if p.KingName == nil || other.KingName == nil {
      return false
    }
    if (*p.KingName) != (*other.KingName) { return false }
  }
  if p.BorthType != other.BorthType {
    if p.BorthType == nil || other.BorthType == nil {
      return false
    }
    if (*p.BorthType) != (*other.BorthType) { return false }
  }
  return true
}

func (p *StarvipInfoVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipInfoVo(%+v)", *p)
}

// Attributes:
//  - KugouId: kugouID
//  - MysticStatus: 神秘嘉宾身份1-开启 0-关闭
//  - StarvipType: 是否星钻会员 0 非会员 >0会员 (1-月会员 2-年会员)
//  - StarvipLevel: 星钻等级
//  - URL
//  - MysticName
//  - KingName
//  - ButlerName
type StarvipInfoCustomerVo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  MysticStatus int32 `thrift:"mysticStatus,2,required" db:"mysticStatus" json:"mysticStatus"`
  StarvipType int32 `thrift:"starvipType,3,required" db:"starvipType" json:"starvipType"`
  StarvipLevel int32 `thrift:"starvipLevel,4,required" db:"starvipLevel" json:"starvipLevel"`
  URL string `thrift:"url,5,required" db:"url" json:"url"`
  MysticName *string `thrift:"mysticName,6" db:"mysticName" json:"mysticName,omitempty"`
  KingName *string `thrift:"kingName,7" db:"kingName" json:"kingName,omitempty"`
  ButlerName *string `thrift:"butlerName,8" db:"butlerName" json:"butlerName,omitempty"`
}

func NewStarvipInfoCustomerVo() *StarvipInfoCustomerVo {
  return &StarvipInfoCustomerVo{}
}


func (p *StarvipInfoCustomerVo) GetKugouId() int64 {
  return p.KugouId
}

func (p *StarvipInfoCustomerVo) GetMysticStatus() int32 {
  return p.MysticStatus
}

func (p *StarvipInfoCustomerVo) GetStarvipType() int32 {
  return p.StarvipType
}

func (p *StarvipInfoCustomerVo) GetStarvipLevel() int32 {
  return p.StarvipLevel
}

func (p *StarvipInfoCustomerVo) GetURL() string {
  return p.URL
}
var StarvipInfoCustomerVo_MysticName_DEFAULT string
func (p *StarvipInfoCustomerVo) GetMysticName() string {
  if !p.IsSetMysticName() {
    return StarvipInfoCustomerVo_MysticName_DEFAULT
  }
return *p.MysticName
}
var StarvipInfoCustomerVo_KingName_DEFAULT string
func (p *StarvipInfoCustomerVo) GetKingName() string {
  if !p.IsSetKingName() {
    return StarvipInfoCustomerVo_KingName_DEFAULT
  }
return *p.KingName
}
var StarvipInfoCustomerVo_ButlerName_DEFAULT string
func (p *StarvipInfoCustomerVo) GetButlerName() string {
  if !p.IsSetButlerName() {
    return StarvipInfoCustomerVo_ButlerName_DEFAULT
  }
return *p.ButlerName
}
func (p *StarvipInfoCustomerVo) IsSetMysticName() bool {
  return p.MysticName != nil
}

func (p *StarvipInfoCustomerVo) IsSetKingName() bool {
  return p.KingName != nil
}

func (p *StarvipInfoCustomerVo) IsSetButlerName() bool {
  return p.ButlerName != nil
}

func (p *StarvipInfoCustomerVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetMysticStatus bool = false;
  var issetStarvipType bool = false;
  var issetStarvipLevel bool = false;
  var issetURL bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMysticStatus = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetStarvipType = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetStarvipLevel = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetURL = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetMysticStatus{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MysticStatus is not set"));
  }
  if !issetStarvipType{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarvipType is not set"));
  }
  if !issetStarvipLevel{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarvipLevel is not set"));
  }
  if !issetURL{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field URL is not set"));
  }
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.MysticStatus = v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.StarvipType = v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StarvipLevel = v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.URL = v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.MysticName = &v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.KingName = &v
}
  return nil
}

func (p *StarvipInfoCustomerVo)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.ButlerName = &v
}
  return nil
}

func (p *StarvipInfoCustomerVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipInfoCustomerVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipInfoCustomerVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "mysticStatus", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:mysticStatus: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.MysticStatus)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.mysticStatus (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:mysticStatus: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerVo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starvipType", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:starvipType: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.StarvipType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starvipType (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:starvipType: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerVo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starvipLevel", thrift.I32, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starvipLevel: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.StarvipLevel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starvipLevel (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starvipLevel: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerVo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "url", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:url: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.URL)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.url (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:url: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerVo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMysticName() {
    if err := oprot.WriteFieldBegin(ctx, "mysticName", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:mysticName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MysticName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mysticName (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:mysticName: ", p), err) }
  }
  return err
}

func (p *StarvipInfoCustomerVo) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKingName() {
    if err := oprot.WriteFieldBegin(ctx, "kingName", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:kingName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.KingName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kingName (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:kingName: ", p), err) }
  }
  return err
}

func (p *StarvipInfoCustomerVo) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetButlerName() {
    if err := oprot.WriteFieldBegin(ctx, "butlerName", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:butlerName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ButlerName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.butlerName (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:butlerName: ", p), err) }
  }
  return err
}

func (p *StarvipInfoCustomerVo) Equals(other *StarvipInfoCustomerVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.MysticStatus != other.MysticStatus { return false }
  if p.StarvipType != other.StarvipType { return false }
  if p.StarvipLevel != other.StarvipLevel { return false }
  if p.URL != other.URL { return false }
  if p.MysticName != other.MysticName {
    if p.MysticName == nil || other.MysticName == nil {
      return false
    }
    if (*p.MysticName) != (*other.MysticName) { return false }
  }
  if p.KingName != other.KingName {
    if p.KingName == nil || other.KingName == nil {
      return false
    }
    if (*p.KingName) != (*other.KingName) { return false }
  }
  if p.ButlerName != other.ButlerName {
    if p.ButlerName == nil || other.ButlerName == nil {
      return false
    }
    if (*p.ButlerName) != (*other.ButlerName) { return false }
  }
  return true
}

func (p *StarvipInfoCustomerVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipInfoCustomerVo(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type StarvipInfoResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string          `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *StarvipInfoVo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewStarvipInfoResult_() *StarvipInfoResult_ {
  return &StarvipInfoResult_{}
}


func (p *StarvipInfoResult_) GetRet() int32 {
  return p.Ret
}

func (p *StarvipInfoResult_) GetMsg() string {
  return p.Msg
}
var StarvipInfoResult__Data_DEFAULT *StarvipInfoVo
func (p *StarvipInfoResult_) GetData() *StarvipInfoVo {
  if !p.IsSetData() {
    return StarvipInfoResult__Data_DEFAULT
  }
return p.Data
}
func (p *StarvipInfoResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *StarvipInfoResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *StarvipInfoResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *StarvipInfoResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *StarvipInfoResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &StarvipInfoVo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *StarvipInfoResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipInfoResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipInfoResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *StarvipInfoResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *StarvipInfoResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *StarvipInfoResult_) Equals(other *StarvipInfoResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *StarvipInfoResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipInfoResult_(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type StarvipInfoCustomerResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string                  `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *StarvipInfoCustomerVo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewStarvipInfoCustomerResult_() *StarvipInfoCustomerResult_ {
  return &StarvipInfoCustomerResult_{}
}


func (p *StarvipInfoCustomerResult_) GetRet() int32 {
  return p.Ret
}

func (p *StarvipInfoCustomerResult_) GetMsg() string {
  return p.Msg
}
var StarvipInfoCustomerResult__Data_DEFAULT *StarvipInfoCustomerVo
func (p *StarvipInfoCustomerResult_) GetData() *StarvipInfoCustomerVo {
  if !p.IsSetData() {
    return StarvipInfoCustomerResult__Data_DEFAULT
  }
return p.Data
}
func (p *StarvipInfoCustomerResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *StarvipInfoCustomerResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *StarvipInfoCustomerResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *StarvipInfoCustomerResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *StarvipInfoCustomerResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &StarvipInfoCustomerVo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *StarvipInfoCustomerResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipInfoCustomerResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipInfoCustomerResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *StarvipInfoCustomerResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *StarvipInfoCustomerResult_) Equals(other *StarvipInfoCustomerResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *StarvipInfoCustomerResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipInfoCustomerResult_(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type StarvipInfoBatchResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string            `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data []*StarvipInfoVo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewStarvipInfoBatchResult_() *StarvipInfoBatchResult_ {
  return &StarvipInfoBatchResult_{}
}


func (p *StarvipInfoBatchResult_) GetRet() int32 {
  return p.Ret
}

func (p *StarvipInfoBatchResult_) GetMsg() string {
  return p.Msg
}
var StarvipInfoBatchResult__Data_DEFAULT []*StarvipInfoVo

func (p *StarvipInfoBatchResult_) GetData() []*StarvipInfoVo {
  return p.Data
}
func (p *StarvipInfoBatchResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *StarvipInfoBatchResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *StarvipInfoBatchResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *StarvipInfoBatchResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *StarvipInfoBatchResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*StarvipInfoVo, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem0 := &StarvipInfoVo{}
    if err := _elem0.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
    }
    p.Data = append(p.Data, _elem0)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *StarvipInfoBatchResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipInfoBatchResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipInfoBatchResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *StarvipInfoBatchResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *StarvipInfoBatchResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *StarvipInfoBatchResult_) Equals(other *StarvipInfoBatchResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src1 := other.Data[i]
    if !_tgt.Equals(_src1) { return false }
  }
  return true
}

func (p *StarvipInfoBatchResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipInfoBatchResult_(%+v)", *p)
}

// Attributes:
//  - KugouId: kugouID
//  - MysticStatus: 神秘嘉宾身份1-开启 0-关闭
//  - StarvipType: 是否星钻会员 0 非会员 >0会员 (1-月会员 2-年会员)
//  - StarvipLevel: 星钻等级
//  - MysticSwitch
//  - URL
//  - MysticName
//  - KingName
//  - KingModify
//  - IsBirthdayMode
//  - BirthType
type StarvipMysticVo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  MysticStatus int32 `thrift:"mysticStatus,2,required" db:"mysticStatus" json:"mysticStatus"`
  StarvipType int32 `thrift:"starvipType,3,required" db:"starvipType" json:"starvipType"`
  StarvipLevel int32 `thrift:"starvipLevel,4,required" db:"starvipLevel" json:"starvipLevel"`
  MysticSwitch int32 `thrift:"mysticSwitch,5,required" db:"mysticSwitch" json:"mysticSwitch"`
  URL *string `thrift:"url,6" db:"url" json:"url,omitempty"`
  MysticName *string `thrift:"mysticName,7" db:"mysticName" json:"mysticName,omitempty"`
  KingName *string `thrift:"kingName,8" db:"kingName" json:"kingName,omitempty"`
  KingModify *int32 `thrift:"kingModify,9" db:"kingModify" json:"kingModify,omitempty"`
  IsBirthdayMode *int32 `thrift:"isBirthdayMode,10" db:"isBirthdayMode" json:"isBirthdayMode,omitempty"`
  BirthType *int32 `thrift:"birthType,11" db:"birthType" json:"birthType,omitempty"`
}

func NewStarvipMysticVo() *StarvipMysticVo {
  return &StarvipMysticVo{}
}


func (p *StarvipMysticVo) GetKugouId() int64 {
  return p.KugouId
}

func (p *StarvipMysticVo) GetMysticStatus() int32 {
  return p.MysticStatus
}

func (p *StarvipMysticVo) GetStarvipType() int32 {
  return p.StarvipType
}

func (p *StarvipMysticVo) GetStarvipLevel() int32 {
  return p.StarvipLevel
}

func (p *StarvipMysticVo) GetMysticSwitch() int32 {
  return p.MysticSwitch
}
var StarvipMysticVo_URL_DEFAULT string
func (p *StarvipMysticVo) GetURL() string {
  if !p.IsSetURL() {
    return StarvipMysticVo_URL_DEFAULT
  }
return *p.URL
}
var StarvipMysticVo_MysticName_DEFAULT string
func (p *StarvipMysticVo) GetMysticName() string {
  if !p.IsSetMysticName() {
    return StarvipMysticVo_MysticName_DEFAULT
  }
return *p.MysticName
}
var StarvipMysticVo_KingName_DEFAULT string
func (p *StarvipMysticVo) GetKingName() string {
  if !p.IsSetKingName() {
    return StarvipMysticVo_KingName_DEFAULT
  }
return *p.KingName
}
var StarvipMysticVo_KingModify_DEFAULT int32
func (p *StarvipMysticVo) GetKingModify() int32 {
  if !p.IsSetKingModify() {
    return StarvipMysticVo_KingModify_DEFAULT
  }
return *p.KingModify
}
var StarvipMysticVo_IsBirthdayMode_DEFAULT int32
func (p *StarvipMysticVo) GetIsBirthdayMode() int32 {
  if !p.IsSetIsBirthdayMode() {
    return StarvipMysticVo_IsBirthdayMode_DEFAULT
  }
return *p.IsBirthdayMode
}
var StarvipMysticVo_BirthType_DEFAULT int32
func (p *StarvipMysticVo) GetBirthType() int32 {
  if !p.IsSetBirthType() {
    return StarvipMysticVo_BirthType_DEFAULT
  }
return *p.BirthType
}
func (p *StarvipMysticVo) IsSetURL() bool {
  return p.URL != nil
}

func (p *StarvipMysticVo) IsSetMysticName() bool {
  return p.MysticName != nil
}

func (p *StarvipMysticVo) IsSetKingName() bool {
  return p.KingName != nil
}

func (p *StarvipMysticVo) IsSetKingModify() bool {
  return p.KingModify != nil
}

func (p *StarvipMysticVo) IsSetIsBirthdayMode() bool {
  return p.IsBirthdayMode != nil
}

func (p *StarvipMysticVo) IsSetBirthType() bool {
  return p.BirthType != nil
}

func (p *StarvipMysticVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetMysticStatus bool = false;
  var issetStarvipType bool = false;
  var issetStarvipLevel bool = false;
  var issetMysticSwitch bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMysticStatus = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetStarvipType = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetStarvipLevel = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetMysticSwitch = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetMysticStatus{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MysticStatus is not set"));
  }
  if !issetStarvipType{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarvipType is not set"));
  }
  if !issetStarvipLevel{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StarvipLevel is not set"));
  }
  if !issetMysticSwitch{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MysticSwitch is not set"));
  }
  return nil
}

func (p *StarvipMysticVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.MysticStatus = v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.StarvipType = v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StarvipLevel = v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.MysticSwitch = v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.URL = &v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.MysticName = &v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.KingName = &v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.KingModify = &v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.IsBirthdayMode = &v
}
  return nil
}

func (p *StarvipMysticVo)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.BirthType = &v
}
  return nil
}

func (p *StarvipMysticVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipMysticVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipMysticVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *StarvipMysticVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "mysticStatus", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:mysticStatus: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.MysticStatus)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.mysticStatus (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:mysticStatus: ", p), err) }
  return err
}

func (p *StarvipMysticVo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starvipType", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:starvipType: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.StarvipType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starvipType (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:starvipType: ", p), err) }
  return err
}

func (p *StarvipMysticVo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "starvipLevel", thrift.I32, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:starvipLevel: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.StarvipLevel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.starvipLevel (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:starvipLevel: ", p), err) }
  return err
}

func (p *StarvipMysticVo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "mysticSwitch", thrift.I32, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:mysticSwitch: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.MysticSwitch)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.mysticSwitch (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:mysticSwitch: ", p), err) }
  return err
}

func (p *StarvipMysticVo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetURL() {
    if err := oprot.WriteFieldBegin(ctx, "url", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:url: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.URL)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.url (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:url: ", p), err) }
  }
  return err
}

func (p *StarvipMysticVo) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMysticName() {
    if err := oprot.WriteFieldBegin(ctx, "mysticName", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:mysticName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MysticName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mysticName (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:mysticName: ", p), err) }
  }
  return err
}

func (p *StarvipMysticVo) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKingName() {
    if err := oprot.WriteFieldBegin(ctx, "kingName", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:kingName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.KingName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kingName (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:kingName: ", p), err) }
  }
  return err
}

func (p *StarvipMysticVo) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKingModify() {
    if err := oprot.WriteFieldBegin(ctx, "kingModify", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:kingModify: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.KingModify)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kingModify (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:kingModify: ", p), err) }
  }
  return err
}

func (p *StarvipMysticVo) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetIsBirthdayMode() {
    if err := oprot.WriteFieldBegin(ctx, "isBirthdayMode", thrift.I32, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:isBirthdayMode: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.IsBirthdayMode)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.isBirthdayMode (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:isBirthdayMode: ", p), err) }
  }
  return err
}

func (p *StarvipMysticVo) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBirthType() {
    if err := oprot.WriteFieldBegin(ctx, "birthType", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:birthType: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.BirthType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.birthType (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:birthType: ", p), err) }
  }
  return err
}

func (p *StarvipMysticVo) Equals(other *StarvipMysticVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.MysticStatus != other.MysticStatus { return false }
  if p.StarvipType != other.StarvipType { return false }
  if p.StarvipLevel != other.StarvipLevel { return false }
  if p.MysticSwitch != other.MysticSwitch { return false }
  if p.URL != other.URL {
    if p.URL == nil || other.URL == nil {
      return false
    }
    if (*p.URL) != (*other.URL) { return false }
  }
  if p.MysticName != other.MysticName {
    if p.MysticName == nil || other.MysticName == nil {
      return false
    }
    if (*p.MysticName) != (*other.MysticName) { return false }
  }
  if p.KingName != other.KingName {
    if p.KingName == nil || other.KingName == nil {
      return false
    }
    if (*p.KingName) != (*other.KingName) { return false }
  }
  if p.KingModify != other.KingModify {
    if p.KingModify == nil || other.KingModify == nil {
      return false
    }
    if (*p.KingModify) != (*other.KingModify) { return false }
  }
  if p.IsBirthdayMode != other.IsBirthdayMode {
    if p.IsBirthdayMode == nil || other.IsBirthdayMode == nil {
      return false
    }
    if (*p.IsBirthdayMode) != (*other.IsBirthdayMode) { return false }
  }
  if p.BirthType != other.BirthType {
    if p.BirthType == nil || other.BirthType == nil {
      return false
    }
    if (*p.BirthType) != (*other.BirthType) { return false }
  }
  return true
}

func (p *StarvipMysticVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipMysticVo(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type StarvipMysticResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string            `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *StarvipMysticVo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewStarvipMysticResult_() *StarvipMysticResult_ {
  return &StarvipMysticResult_{}
}


func (p *StarvipMysticResult_) GetRet() int32 {
  return p.Ret
}

func (p *StarvipMysticResult_) GetMsg() string {
  return p.Msg
}
var StarvipMysticResult__Data_DEFAULT *StarvipMysticVo
func (p *StarvipMysticResult_) GetData() *StarvipMysticVo {
  if !p.IsSetData() {
    return StarvipMysticResult__Data_DEFAULT
  }
return p.Data
}
func (p *StarvipMysticResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *StarvipMysticResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *StarvipMysticResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *StarvipMysticResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *StarvipMysticResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &StarvipMysticVo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *StarvipMysticResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipMysticResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipMysticResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *StarvipMysticResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *StarvipMysticResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *StarvipMysticResult_) Equals(other *StarvipMysticResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *StarvipMysticResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipMysticResult_(%+v)", *p)
}

// Attributes:
//  - AppId: APPID
//  - KugouId: 酷狗ID
//  - Score: 本次加分
//  - OrderId: 订单ID
//  - Timestamp: 时间戳 (秒)
//  - Sign: md5(所有参数名字升序排列对应的数值拼接（逗号拼接） + 固定密钥[加盐][appId对应的描述])
type StarvipAddScoreRequest struct {
  AppId int32 `thrift:"appId,1,required" db:"appId" json:"appId"`
  KugouId int64 `thrift:"kugouId,2,required" db:"kugouId" json:"kugouId"`
  Score int64 `thrift:"score,3,required" db:"score" json:"score"`
  OrderId int64 `thrift:"orderId,4,required" db:"orderId" json:"orderId"`
  Timestamp int64 `thrift:"timestamp,5,required" db:"timestamp" json:"timestamp"`
  Sign string `thrift:"sign,6,required" db:"sign" json:"sign"`
}

func NewStarvipAddScoreRequest() *StarvipAddScoreRequest {
  return &StarvipAddScoreRequest{}
}


func (p *StarvipAddScoreRequest) GetAppId() int32 {
  return p.AppId
}

func (p *StarvipAddScoreRequest) GetKugouId() int64 {
  return p.KugouId
}

func (p *StarvipAddScoreRequest) GetScore() int64 {
  return p.Score
}

func (p *StarvipAddScoreRequest) GetOrderId() int64 {
  return p.OrderId
}

func (p *StarvipAddScoreRequest) GetTimestamp() int64 {
  return p.Timestamp
}

func (p *StarvipAddScoreRequest) GetSign() string {
  return p.Sign
}
func (p *StarvipAddScoreRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetKugouId bool = false;
  var issetScore bool = false;
  var issetOrderId bool = false;
  var issetTimestamp bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetScore = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetOrderId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetTimestamp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetScore{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Score is not set"));
  }
  if !issetOrderId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OrderId is not set"));
  }
  if !issetTimestamp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Timestamp is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *StarvipAddScoreRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *StarvipAddScoreRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StarvipAddScoreRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Score = v
}
  return nil
}

func (p *StarvipAddScoreRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.OrderId = v
}
  return nil
}

func (p *StarvipAddScoreRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Timestamp = v
}
  return nil
}

func (p *StarvipAddScoreRequest)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *StarvipAddScoreRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipAddScoreRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipAddScoreRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *StarvipAddScoreRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:kugouId: ", p), err) }
  return err
}

func (p *StarvipAddScoreRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "score", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:score: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Score)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.score (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:score: ", p), err) }
  return err
}

func (p *StarvipAddScoreRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "orderId", thrift.I64, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:orderId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.OrderId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.orderId (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:orderId: ", p), err) }
  return err
}

func (p *StarvipAddScoreRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:timestamp: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Timestamp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.timestamp (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:timestamp: ", p), err) }
  return err
}

func (p *StarvipAddScoreRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sign: ", p), err) }
  return err
}

func (p *StarvipAddScoreRequest) Equals(other *StarvipAddScoreRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.KugouId != other.KugouId { return false }
  if p.Score != other.Score { return false }
  if p.OrderId != other.OrderId { return false }
  if p.Timestamp != other.Timestamp { return false }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *StarvipAddScoreRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipAddScoreRequest(%+v)", *p)
}

// Attributes:
//  - AppId: APPID
//  - KugouId: 酷狗ID
//  - Integral: 本次调整积分
//  - Descr: 调整描述
//  - UniqId: 订单ID
//  - Timestamp: 时间戳 (秒)
//  - Sign: md5(所有参数名字升序排列对应的数值拼接（逗号拼接） + 固定密钥[加盐][appId对应的描述])
type StarvipIntegralAdjustRequest struct {
  AppId int32 `thrift:"appId,1,required" db:"appId" json:"appId"`
  KugouId int64 `thrift:"kugouId,2,required" db:"kugouId" json:"kugouId"`
  Integral int32 `thrift:"integral,3,required" db:"integral" json:"integral"`
  Descr string `thrift:"descr,4,required" db:"descr" json:"descr"`
  UniqId int64 `thrift:"uniqId,5,required" db:"uniqId" json:"uniqId"`
  Timestamp int64 `thrift:"timestamp,6,required" db:"timestamp" json:"timestamp"`
  Sign string `thrift:"sign,7,required" db:"sign" json:"sign"`
}

func NewStarvipIntegralAdjustRequest() *StarvipIntegralAdjustRequest {
  return &StarvipIntegralAdjustRequest{}
}


func (p *StarvipIntegralAdjustRequest) GetAppId() int32 {
  return p.AppId
}

func (p *StarvipIntegralAdjustRequest) GetKugouId() int64 {
  return p.KugouId
}

func (p *StarvipIntegralAdjustRequest) GetIntegral() int32 {
  return p.Integral
}

func (p *StarvipIntegralAdjustRequest) GetDescr() string {
  return p.Descr
}

func (p *StarvipIntegralAdjustRequest) GetUniqId() int64 {
  return p.UniqId
}

func (p *StarvipIntegralAdjustRequest) GetTimestamp() int64 {
  return p.Timestamp
}

func (p *StarvipIntegralAdjustRequest) GetSign() string {
  return p.Sign
}
func (p *StarvipIntegralAdjustRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetKugouId bool = false;
  var issetIntegral bool = false;
  var issetDescr bool = false;
  var issetUniqId bool = false;
  var issetTimestamp bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetIntegral = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetDescr = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetUniqId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetTimestamp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetIntegral{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Integral is not set"));
  }
  if !issetDescr{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Descr is not set"));
  }
  if !issetUniqId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UniqId is not set"));
  }
  if !issetTimestamp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Timestamp is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Integral = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Descr = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.UniqId = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Timestamp = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *StarvipIntegralAdjustRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipIntegralAdjustRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipIntegralAdjustRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:kugouId: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "integral", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:integral: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Integral)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.integral (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:integral: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "descr", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:descr: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Descr)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.descr (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:descr: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "uniqId", thrift.I64, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:uniqId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.UniqId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.uniqId (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:uniqId: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:timestamp: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Timestamp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.timestamp (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:timestamp: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:sign: ", p), err) }
  return err
}

func (p *StarvipIntegralAdjustRequest) Equals(other *StarvipIntegralAdjustRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.KugouId != other.KugouId { return false }
  if p.Integral != other.Integral { return false }
  if p.Descr != other.Descr { return false }
  if p.UniqId != other.UniqId { return false }
  if p.Timestamp != other.Timestamp { return false }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *StarvipIntegralAdjustRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipIntegralAdjustRequest(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type StarvipResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewStarvipResult_() *StarvipResult_ {
  return &StarvipResult_{}
}


func (p *StarvipResult_) GetRet() int32 {
  return p.Ret
}

func (p *StarvipResult_) GetMsg() string {
  return p.Msg
}
var StarvipResult__Data_DEFAULT string
func (p *StarvipResult_) GetData() string {
  if !p.IsSetData() {
    return StarvipResult__Data_DEFAULT
  }
return *p.Data
}
func (p *StarvipResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *StarvipResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *StarvipResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *StarvipResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *StarvipResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *StarvipResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *StarvipResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *StarvipResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *StarvipResult_) Equals(other *StarvipResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *StarvipResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipResult_(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type StarvipMysticStatusBatchResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]bool `thrift:"data,3,required" db:"data" json:"data"`
}

func NewStarvipMysticStatusBatchResult_() *StarvipMysticStatusBatchResult_ {
  return &StarvipMysticStatusBatchResult_{}
}


func (p *StarvipMysticStatusBatchResult_) GetRet() int32 {
  return p.Ret
}

func (p *StarvipMysticStatusBatchResult_) GetMsg() string {
  return p.Msg
}

func (p *StarvipMysticStatusBatchResult_) GetData() map[int64]bool {
  return p.Data
}
func (p *StarvipMysticStatusBatchResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;
  var issetMsg bool = false;
  var issetData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  return nil
}

func (p *StarvipMysticStatusBatchResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *StarvipMysticStatusBatchResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *StarvipMysticStatusBatchResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]bool, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key2 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key2 = v
}
var _val3 bool
    if v, err := iprot.ReadBool(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val3 = v
}
    p.Data[_key2] = _val3
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *StarvipMysticStatusBatchResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StarvipMysticStatusBatchResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipMysticStatusBatchResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *StarvipMysticStatusBatchResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *StarvipMysticStatusBatchResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
  if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.BOOL, len(p.Data)); err != nil {
    return thrift.PrependError("error writing map begin: ", err)
  }
  for k, v := range p.Data {
    if err := oprot.WriteI64(ctx, int64(k)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    if err := oprot.WriteBool(ctx, bool(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteMapEnd(ctx); err != nil {
    return thrift.PrependError("error writing map end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  return err
}

func (p *StarvipMysticStatusBatchResult_) Equals(other *StarvipMysticStatusBatchResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src4 := other.Data[k]
    if _tgt != _src4 { return false }
  }
  return true
}

func (p *StarvipMysticStatusBatchResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipMysticStatusBatchResult_(%+v)", *p)
}

type StarvipService interface {
  // 根据酷狗ID查询星钻用户信息
  // 
  // Parameters:
  //  - Request
  GetStarvipInfo(ctx context.Context, request *StarvipRequest) (_r *StarvipInfoResult_, _err error)
  // Parameters:
  //  - KugouIds
  GetStarvipInfoBatch(ctx context.Context, kugouIds []int64) (_r *StarvipInfoBatchResult_, _err error)
  // Parameters:
  //  - KugouIds
  //  - AppId
  GetStarvipInfoBatchV2(ctx context.Context, kugouIds []int64, appId int32) (_r *StarvipInfoBatchResult_, _err error)
  // Parameters:
  //  - Request
  GetStarvipMystic(ctx context.Context, request *StarvipRequest) (_r *StarvipMysticResult_, _err error)
  // Parameters:
  //  - Request
  AddStarvipScore(ctx context.Context, request *StarvipAddScoreRequest) (_r *StarvipResult_, _err error)
  // 星钻积分调整
  // 
  // Parameters:
  //  - Request
  AdjustStarvipIntegral(ctx context.Context, request *StarvipIntegralAdjustRequest) (_r *StarvipResult_, _err error)
  // Parameters:
  //  - Request
  GetStarvipInfoForCustomer(ctx context.Context, request *StarvipRequest) (_r *StarvipInfoCustomerResult_, _err error)
  // 批量查询星钻神秘嘉宾状态
  // 
  // Parameters:
  //  - KugouIds
  GetStarvipMysticStatusBatch(ctx context.Context, kugouIds []int64) (_r *StarvipMysticStatusBatchResult_, _err error)
}

type StarvipServiceClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewStarvipServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *StarvipServiceClient {
  return &StarvipServiceClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewStarvipServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *StarvipServiceClient {
  return &StarvipServiceClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewStarvipServiceClient(c thrift.TClient) *StarvipServiceClient {
  return &StarvipServiceClient{
    c: c,
  }
}

func (p *StarvipServiceClient) Client_() thrift.TClient {
  return p.c
}

func (p *StarvipServiceClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *StarvipServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// 根据酷狗ID查询星钻用户信息
// 
// Parameters:
//  - Request
func (p *StarvipServiceClient) GetStarvipInfo(ctx context.Context, request *StarvipRequest) (_r *StarvipInfoResult_, _err error) {
  var _args5 StarvipServiceGetStarvipInfoArgs
  _args5.Request = request
  var _result7 StarvipServiceGetStarvipInfoResult
  var _meta6 thrift.ResponseMeta
  _meta6, _err = p.Client_().Call(ctx, "getStarvipInfo", &_args5, &_result7)
  p.SetLastResponseMeta_(_meta6)
  if _err != nil {
    return
  }
  if _ret8 := _result7.GetSuccess(); _ret8 != nil {
    return _ret8, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarvipInfo failed: unknown result")
}

// Parameters:
//  - KugouIds
func (p *StarvipServiceClient) GetStarvipInfoBatch(ctx context.Context, kugouIds []int64) (_r *StarvipInfoBatchResult_, _err error) {
  var _args9 StarvipServiceGetStarvipInfoBatchArgs
  _args9.KugouIds = kugouIds
  var _result11 StarvipServiceGetStarvipInfoBatchResult
  var _meta10 thrift.ResponseMeta
  _meta10, _err = p.Client_().Call(ctx, "getStarvipInfoBatch", &_args9, &_result11)
  p.SetLastResponseMeta_(_meta10)
  if _err != nil {
    return
  }
  if _ret12 := _result11.GetSuccess(); _ret12 != nil {
    return _ret12, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarvipInfoBatch failed: unknown result")
}

// Parameters:
//  - KugouIds
//  - AppId
func (p *StarvipServiceClient) GetStarvipInfoBatchV2(ctx context.Context, kugouIds []int64, appId int32) (_r *StarvipInfoBatchResult_, _err error) {
  var _args13 StarvipServiceGetStarvipInfoBatchV2Args
  _args13.KugouIds = kugouIds
  _args13.AppId = appId
  var _result15 StarvipServiceGetStarvipInfoBatchV2Result
  var _meta14 thrift.ResponseMeta
  _meta14, _err = p.Client_().Call(ctx, "getStarvipInfoBatchV2", &_args13, &_result15)
  p.SetLastResponseMeta_(_meta14)
  if _err != nil {
    return
  }
  if _ret16 := _result15.GetSuccess(); _ret16 != nil {
    return _ret16, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarvipInfoBatchV2 failed: unknown result")
}

// Parameters:
//  - Request
func (p *StarvipServiceClient) GetStarvipMystic(ctx context.Context, request *StarvipRequest) (_r *StarvipMysticResult_, _err error) {
  var _args17 StarvipServiceGetStarvipMysticArgs
  _args17.Request = request
  var _result19 StarvipServiceGetStarvipMysticResult
  var _meta18 thrift.ResponseMeta
  _meta18, _err = p.Client_().Call(ctx, "getStarvipMystic", &_args17, &_result19)
  p.SetLastResponseMeta_(_meta18)
  if _err != nil {
    return
  }
  if _ret20 := _result19.GetSuccess(); _ret20 != nil {
    return _ret20, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarvipMystic failed: unknown result")
}

// Parameters:
//  - Request
func (p *StarvipServiceClient) AddStarvipScore(ctx context.Context, request *StarvipAddScoreRequest) (_r *StarvipResult_, _err error) {
  var _args21 StarvipServiceAddStarvipScoreArgs
  _args21.Request = request
  var _result23 StarvipServiceAddStarvipScoreResult
  var _meta22 thrift.ResponseMeta
  _meta22, _err = p.Client_().Call(ctx, "addStarvipScore", &_args21, &_result23)
  p.SetLastResponseMeta_(_meta22)
  if _err != nil {
    return
  }
  if _ret24 := _result23.GetSuccess(); _ret24 != nil {
    return _ret24, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "addStarvipScore failed: unknown result")
}

// 星钻积分调整
// 
// Parameters:
//  - Request
func (p *StarvipServiceClient) AdjustStarvipIntegral(ctx context.Context, request *StarvipIntegralAdjustRequest) (_r *StarvipResult_, _err error) {
  var _args25 StarvipServiceAdjustStarvipIntegralArgs
  _args25.Request = request
  var _result27 StarvipServiceAdjustStarvipIntegralResult
  var _meta26 thrift.ResponseMeta
  _meta26, _err = p.Client_().Call(ctx, "adjustStarvipIntegral", &_args25, &_result27)
  p.SetLastResponseMeta_(_meta26)
  if _err != nil {
    return
  }
  if _ret28 := _result27.GetSuccess(); _ret28 != nil {
    return _ret28, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "adjustStarvipIntegral failed: unknown result")
}

// Parameters:
//  - Request
func (p *StarvipServiceClient) GetStarvipInfoForCustomer(ctx context.Context, request *StarvipRequest) (_r *StarvipInfoCustomerResult_, _err error) {
  var _args29 StarvipServiceGetStarvipInfoForCustomerArgs
  _args29.Request = request
  var _result31 StarvipServiceGetStarvipInfoForCustomerResult
  var _meta30 thrift.ResponseMeta
  _meta30, _err = p.Client_().Call(ctx, "getStarvipInfoForCustomer", &_args29, &_result31)
  p.SetLastResponseMeta_(_meta30)
  if _err != nil {
    return
  }
  if _ret32 := _result31.GetSuccess(); _ret32 != nil {
    return _ret32, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarvipInfoForCustomer failed: unknown result")
}

// 批量查询星钻神秘嘉宾状态
// 
// Parameters:
//  - KugouIds
func (p *StarvipServiceClient) GetStarvipMysticStatusBatch(ctx context.Context, kugouIds []int64) (_r *StarvipMysticStatusBatchResult_, _err error) {
  var _args33 StarvipServiceGetStarvipMysticStatusBatchArgs
  _args33.KugouIds = kugouIds
  var _result35 StarvipServiceGetStarvipMysticStatusBatchResult
  var _meta34 thrift.ResponseMeta
  _meta34, _err = p.Client_().Call(ctx, "getStarvipMysticStatusBatch", &_args33, &_result35)
  p.SetLastResponseMeta_(_meta34)
  if _err != nil {
    return
  }
  if _ret36 := _result35.GetSuccess(); _ret36 != nil {
    return _ret36, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getStarvipMysticStatusBatch failed: unknown result")
}

type StarvipServiceProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler      StarvipService
}

func (p *StarvipServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *StarvipServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *StarvipServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewStarvipServiceProcessor(handler StarvipService) *StarvipServiceProcessor {

  self37 := &StarvipServiceProcessor{handler: handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self37.processorMap["getStarvipInfo"] = &starvipServiceProcessorGetStarvipInfo{handler: handler}
  self37.processorMap["getStarvipInfoBatch"] = &starvipServiceProcessorGetStarvipInfoBatch{handler: handler}
  self37.processorMap["getStarvipInfoBatchV2"] = &starvipServiceProcessorGetStarvipInfoBatchV2{handler: handler}
  self37.processorMap["getStarvipMystic"] = &starvipServiceProcessorGetStarvipMystic{handler: handler}
  self37.processorMap["addStarvipScore"] = &starvipServiceProcessorAddStarvipScore{handler: handler}
  self37.processorMap["adjustStarvipIntegral"] = &starvipServiceProcessorAdjustStarvipIntegral{handler: handler}
  self37.processorMap["getStarvipInfoForCustomer"] = &starvipServiceProcessorGetStarvipInfoForCustomer{handler: handler}
  self37.processorMap["getStarvipMysticStatusBatch"] = &starvipServiceProcessorGetStarvipMysticStatusBatch{handler: handler}
return self37
}

func (p *StarvipServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x38 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x38.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x38

}

type starvipServiceProcessorGetStarvipInfo struct {
  handler StarvipService
}

func (p *starvipServiceProcessorGetStarvipInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err39 error
  args := StarvipServiceGetStarvipInfoArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getStarvipInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceGetStarvipInfoResult{}
  if retval, err2 := p.handler.GetStarvipInfo(ctx, args.Request); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc40 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarvipInfo: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfo", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err39 = thrift.WrapTException(err2)
    }
    if err2 := _exc40.Write(ctx, oprot); _write_err39 == nil && err2 != nil {
      _write_err39 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err39 == nil && err2 != nil {
      _write_err39 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err39 == nil && err2 != nil {
      _write_err39 = thrift.WrapTException(err2)
    }
    if _write_err39 != nil {
      return false, thrift.WrapTException(_write_err39)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfo", thrift.REPLY, seqId); err2 != nil {
    _write_err39 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err39 == nil && err2 != nil {
    _write_err39 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err39 == nil && err2 != nil {
    _write_err39 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err39 == nil && err2 != nil {
    _write_err39 = thrift.WrapTException(err2)
  }
  if _write_err39 != nil {
    return false, thrift.WrapTException(_write_err39)
  }
  return true, err
}

type starvipServiceProcessorGetStarvipInfoBatch struct {
  handler StarvipService
}

func (p *starvipServiceProcessorGetStarvipInfoBatch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err41 error
  args := StarvipServiceGetStarvipInfoBatchArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getStarvipInfoBatch", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceGetStarvipInfoBatchResult{}
  if retval, err2 := p.handler.GetStarvipInfoBatch(ctx, args.KugouIds); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc42 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarvipInfoBatch: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfoBatch", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err41 = thrift.WrapTException(err2)
    }
    if err2 := _exc42.Write(ctx, oprot); _write_err41 == nil && err2 != nil {
      _write_err41 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err41 == nil && err2 != nil {
      _write_err41 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err41 == nil && err2 != nil {
      _write_err41 = thrift.WrapTException(err2)
    }
    if _write_err41 != nil {
      return false, thrift.WrapTException(_write_err41)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfoBatch", thrift.REPLY, seqId); err2 != nil {
    _write_err41 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err41 == nil && err2 != nil {
    _write_err41 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err41 == nil && err2 != nil {
    _write_err41 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err41 == nil && err2 != nil {
    _write_err41 = thrift.WrapTException(err2)
  }
  if _write_err41 != nil {
    return false, thrift.WrapTException(_write_err41)
  }
  return true, err
}

type starvipServiceProcessorGetStarvipInfoBatchV2 struct {
  handler StarvipService
}

func (p *starvipServiceProcessorGetStarvipInfoBatchV2) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err43 error
  args := StarvipServiceGetStarvipInfoBatchV2Args{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getStarvipInfoBatchV2", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceGetStarvipInfoBatchV2Result{}
  if retval, err2 := p.handler.GetStarvipInfoBatchV2(ctx, args.KugouIds, args.AppId); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc44 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarvipInfoBatchV2: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfoBatchV2", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err43 = thrift.WrapTException(err2)
    }
    if err2 := _exc44.Write(ctx, oprot); _write_err43 == nil && err2 != nil {
      _write_err43 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err43 == nil && err2 != nil {
      _write_err43 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err43 == nil && err2 != nil {
      _write_err43 = thrift.WrapTException(err2)
    }
    if _write_err43 != nil {
      return false, thrift.WrapTException(_write_err43)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfoBatchV2", thrift.REPLY, seqId); err2 != nil {
    _write_err43 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err43 == nil && err2 != nil {
    _write_err43 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err43 == nil && err2 != nil {
    _write_err43 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err43 == nil && err2 != nil {
    _write_err43 = thrift.WrapTException(err2)
  }
  if _write_err43 != nil {
    return false, thrift.WrapTException(_write_err43)
  }
  return true, err
}

type starvipServiceProcessorGetStarvipMystic struct {
  handler StarvipService
}

func (p *starvipServiceProcessorGetStarvipMystic) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err45 error
  args := StarvipServiceGetStarvipMysticArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getStarvipMystic", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceGetStarvipMysticResult{}
  if retval, err2 := p.handler.GetStarvipMystic(ctx, args.Request); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc46 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarvipMystic: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getStarvipMystic", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err45 = thrift.WrapTException(err2)
    }
    if err2 := _exc46.Write(ctx, oprot); _write_err45 == nil && err2 != nil {
      _write_err45 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err45 == nil && err2 != nil {
      _write_err45 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err45 == nil && err2 != nil {
      _write_err45 = thrift.WrapTException(err2)
    }
    if _write_err45 != nil {
      return false, thrift.WrapTException(_write_err45)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getStarvipMystic", thrift.REPLY, seqId); err2 != nil {
    _write_err45 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err45 == nil && err2 != nil {
    _write_err45 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err45 == nil && err2 != nil {
    _write_err45 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err45 == nil && err2 != nil {
    _write_err45 = thrift.WrapTException(err2)
  }
  if _write_err45 != nil {
    return false, thrift.WrapTException(_write_err45)
  }
  return true, err
}

type starvipServiceProcessorAddStarvipScore struct {
  handler StarvipService
}

func (p *starvipServiceProcessorAddStarvipScore) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err47 error
  args := StarvipServiceAddStarvipScoreArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "addStarvipScore", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceAddStarvipScoreResult{}
  if retval, err2 := p.handler.AddStarvipScore(ctx, args.Request); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc48 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addStarvipScore: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "addStarvipScore", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err47 = thrift.WrapTException(err2)
    }
    if err2 := _exc48.Write(ctx, oprot); _write_err47 == nil && err2 != nil {
      _write_err47 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err47 == nil && err2 != nil {
      _write_err47 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err47 == nil && err2 != nil {
      _write_err47 = thrift.WrapTException(err2)
    }
    if _write_err47 != nil {
      return false, thrift.WrapTException(_write_err47)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "addStarvipScore", thrift.REPLY, seqId); err2 != nil {
    _write_err47 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err47 == nil && err2 != nil {
    _write_err47 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err47 == nil && err2 != nil {
    _write_err47 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err47 == nil && err2 != nil {
    _write_err47 = thrift.WrapTException(err2)
  }
  if _write_err47 != nil {
    return false, thrift.WrapTException(_write_err47)
  }
  return true, err
}

type starvipServiceProcessorAdjustStarvipIntegral struct {
  handler StarvipService
}

func (p *starvipServiceProcessorAdjustStarvipIntegral) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err49 error
  args := StarvipServiceAdjustStarvipIntegralArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "adjustStarvipIntegral", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceAdjustStarvipIntegralResult{}
  if retval, err2 := p.handler.AdjustStarvipIntegral(ctx, args.Request); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc50 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing adjustStarvipIntegral: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "adjustStarvipIntegral", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err49 = thrift.WrapTException(err2)
    }
    if err2 := _exc50.Write(ctx, oprot); _write_err49 == nil && err2 != nil {
      _write_err49 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err49 == nil && err2 != nil {
      _write_err49 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err49 == nil && err2 != nil {
      _write_err49 = thrift.WrapTException(err2)
    }
    if _write_err49 != nil {
      return false, thrift.WrapTException(_write_err49)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "adjustStarvipIntegral", thrift.REPLY, seqId); err2 != nil {
    _write_err49 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err49 == nil && err2 != nil {
    _write_err49 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err49 == nil && err2 != nil {
    _write_err49 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err49 == nil && err2 != nil {
    _write_err49 = thrift.WrapTException(err2)
  }
  if _write_err49 != nil {
    return false, thrift.WrapTException(_write_err49)
  }
  return true, err
}

type starvipServiceProcessorGetStarvipInfoForCustomer struct {
  handler StarvipService
}

func (p *starvipServiceProcessorGetStarvipInfoForCustomer) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err51 error
  args := StarvipServiceGetStarvipInfoForCustomerArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getStarvipInfoForCustomer", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceGetStarvipInfoForCustomerResult{}
  if retval, err2 := p.handler.GetStarvipInfoForCustomer(ctx, args.Request); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc52 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarvipInfoForCustomer: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfoForCustomer", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err51 = thrift.WrapTException(err2)
    }
    if err2 := _exc52.Write(ctx, oprot); _write_err51 == nil && err2 != nil {
      _write_err51 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err51 == nil && err2 != nil {
      _write_err51 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err51 == nil && err2 != nil {
      _write_err51 = thrift.WrapTException(err2)
    }
    if _write_err51 != nil {
      return false, thrift.WrapTException(_write_err51)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getStarvipInfoForCustomer", thrift.REPLY, seqId); err2 != nil {
    _write_err51 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err51 == nil && err2 != nil {
    _write_err51 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err51 == nil && err2 != nil {
    _write_err51 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err51 == nil && err2 != nil {
    _write_err51 = thrift.WrapTException(err2)
  }
  if _write_err51 != nil {
    return false, thrift.WrapTException(_write_err51)
  }
  return true, err
}

type starvipServiceProcessorGetStarvipMysticStatusBatch struct {
  handler StarvipService
}

func (p *starvipServiceProcessorGetStarvipMysticStatusBatch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err53 error
  args := StarvipServiceGetStarvipMysticStatusBatchArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getStarvipMysticStatusBatch", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := StarvipServiceGetStarvipMysticStatusBatchResult{}
  if retval, err2 := p.handler.GetStarvipMysticStatusBatch(ctx, args.KugouIds); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc54 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStarvipMysticStatusBatch: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getStarvipMysticStatusBatch", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err53 = thrift.WrapTException(err2)
    }
    if err2 := _exc54.Write(ctx, oprot); _write_err53 == nil && err2 != nil {
      _write_err53 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err53 == nil && err2 != nil {
      _write_err53 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err53 == nil && err2 != nil {
      _write_err53 = thrift.WrapTException(err2)
    }
    if _write_err53 != nil {
      return false, thrift.WrapTException(_write_err53)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getStarvipMysticStatusBatch", thrift.REPLY, seqId); err2 != nil {
    _write_err53 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err53 == nil && err2 != nil {
    _write_err53 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err53 == nil && err2 != nil {
    _write_err53 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err53 == nil && err2 != nil {
    _write_err53 = thrift.WrapTException(err2)
  }
  if _write_err53 != nil {
    return false, thrift.WrapTException(_write_err53)
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Request
type StarvipServiceGetStarvipInfoArgs struct {
  Request *StarvipRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewStarvipServiceGetStarvipInfoArgs() *StarvipServiceGetStarvipInfoArgs {
  return &StarvipServiceGetStarvipInfoArgs{}
}

var StarvipServiceGetStarvipInfoArgs_Request_DEFAULT *StarvipRequest
func (p *StarvipServiceGetStarvipInfoArgs) GetRequest() *StarvipRequest {
  if !p.IsSetRequest() {
    return StarvipServiceGetStarvipInfoArgs_Request_DEFAULT
  }
return p.Request
}
func (p *StarvipServiceGetStarvipInfoArgs) IsSetRequest() bool {
  return p.Request != nil
}

func (p *StarvipServiceGetStarvipInfoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRequest bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRequest = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRequest{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"));
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Request = &StarvipRequest{}
  if err := p.Request.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfo_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err) }
  if err := p.Request.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipInfoArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceGetStarvipInfoResult struct {
  Success *StarvipInfoResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceGetStarvipInfoResult() *StarvipServiceGetStarvipInfoResult {
  return &StarvipServiceGetStarvipInfoResult{}
}

var StarvipServiceGetStarvipInfoResult_Success_DEFAULT *StarvipInfoResult_
func (p *StarvipServiceGetStarvipInfoResult) GetSuccess() *StarvipInfoResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceGetStarvipInfoResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceGetStarvipInfoResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceGetStarvipInfoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipInfoResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfo_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceGetStarvipInfoResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoResult(%+v)", *p)
}

// Attributes:
//  - KugouIds
type StarvipServiceGetStarvipInfoBatchArgs struct {
  KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
}

func NewStarvipServiceGetStarvipInfoBatchArgs() *StarvipServiceGetStarvipInfoBatchArgs {
  return &StarvipServiceGetStarvipInfoBatchArgs{}
}


func (p *StarvipServiceGetStarvipInfoBatchArgs) GetKugouIds() []int64 {
  return p.KugouIds
}
func (p *StarvipServiceGetStarvipInfoBatchArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouIds bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouIds = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouIds{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"));
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.KugouIds =  tSlice
  for i := 0; i < size; i ++ {
var _elem55 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem55 = v
}
    p.KugouIds = append(p.KugouIds, _elem55)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfoBatch_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.KugouIds {
    if err := oprot.WriteI64(ctx, int64(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipInfoBatchArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoBatchArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceGetStarvipInfoBatchResult struct {
  Success *StarvipInfoBatchResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceGetStarvipInfoBatchResult() *StarvipServiceGetStarvipInfoBatchResult {
  return &StarvipServiceGetStarvipInfoBatchResult{}
}

var StarvipServiceGetStarvipInfoBatchResult_Success_DEFAULT *StarvipInfoBatchResult_
func (p *StarvipServiceGetStarvipInfoBatchResult) GetSuccess() *StarvipInfoBatchResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceGetStarvipInfoBatchResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceGetStarvipInfoBatchResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceGetStarvipInfoBatchResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipInfoBatchResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfoBatch_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceGetStarvipInfoBatchResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoBatchResult(%+v)", *p)
}

// Attributes:
//  - KugouIds
//  - AppId
type StarvipServiceGetStarvipInfoBatchV2Args struct {
  KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
}

func NewStarvipServiceGetStarvipInfoBatchV2Args() *StarvipServiceGetStarvipInfoBatchV2Args {
  return &StarvipServiceGetStarvipInfoBatchV2Args{}
}


func (p *StarvipServiceGetStarvipInfoBatchV2Args) GetKugouIds() []int64 {
  return p.KugouIds
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args) GetAppId() int32 {
  return p.AppId
}
func (p *StarvipServiceGetStarvipInfoBatchV2Args) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouIds bool = false;
  var issetAppId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouIds = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouIds{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.KugouIds =  tSlice
  for i := 0; i < size; i ++ {
var _elem56 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem56 = v
}
    p.KugouIds = append(p.KugouIds, _elem56)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfoBatchV2_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.KugouIds {
    if err := oprot.WriteI64(ctx, int64(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipInfoBatchV2Args) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoBatchV2Args(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceGetStarvipInfoBatchV2Result struct {
  Success *StarvipInfoBatchResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceGetStarvipInfoBatchV2Result() *StarvipServiceGetStarvipInfoBatchV2Result {
  return &StarvipServiceGetStarvipInfoBatchV2Result{}
}

var StarvipServiceGetStarvipInfoBatchV2Result_Success_DEFAULT *StarvipInfoBatchResult_
func (p *StarvipServiceGetStarvipInfoBatchV2Result) GetSuccess() *StarvipInfoBatchResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceGetStarvipInfoBatchV2Result_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceGetStarvipInfoBatchV2Result) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Result) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Result)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipInfoBatchResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Result) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfoBatchV2_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoBatchV2Result) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceGetStarvipInfoBatchV2Result) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoBatchV2Result(%+v)", *p)
}

// Attributes:
//  - Request
type StarvipServiceGetStarvipMysticArgs struct {
  Request *StarvipRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewStarvipServiceGetStarvipMysticArgs() *StarvipServiceGetStarvipMysticArgs {
  return &StarvipServiceGetStarvipMysticArgs{}
}

var StarvipServiceGetStarvipMysticArgs_Request_DEFAULT *StarvipRequest
func (p *StarvipServiceGetStarvipMysticArgs) GetRequest() *StarvipRequest {
  if !p.IsSetRequest() {
    return StarvipServiceGetStarvipMysticArgs_Request_DEFAULT
  }
return p.Request
}
func (p *StarvipServiceGetStarvipMysticArgs) IsSetRequest() bool {
  return p.Request != nil
}

func (p *StarvipServiceGetStarvipMysticArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRequest bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRequest = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRequest{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"));
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Request = &StarvipRequest{}
  if err := p.Request.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipMystic_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipMysticArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err) }
  if err := p.Request.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipMysticArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipMysticArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceGetStarvipMysticResult struct {
  Success *StarvipMysticResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceGetStarvipMysticResult() *StarvipServiceGetStarvipMysticResult {
  return &StarvipServiceGetStarvipMysticResult{}
}

var StarvipServiceGetStarvipMysticResult_Success_DEFAULT *StarvipMysticResult_
func (p *StarvipServiceGetStarvipMysticResult) GetSuccess() *StarvipMysticResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceGetStarvipMysticResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceGetStarvipMysticResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceGetStarvipMysticResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipMysticResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipMystic_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipMysticResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceGetStarvipMysticResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipMysticResult(%+v)", *p)
}

// Attributes:
//  - Request
type StarvipServiceAddStarvipScoreArgs struct {
  Request *StarvipAddScoreRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewStarvipServiceAddStarvipScoreArgs() *StarvipServiceAddStarvipScoreArgs {
  return &StarvipServiceAddStarvipScoreArgs{}
}

var StarvipServiceAddStarvipScoreArgs_Request_DEFAULT *StarvipAddScoreRequest
func (p *StarvipServiceAddStarvipScoreArgs) GetRequest() *StarvipAddScoreRequest {
  if !p.IsSetRequest() {
    return StarvipServiceAddStarvipScoreArgs_Request_DEFAULT
  }
return p.Request
}
func (p *StarvipServiceAddStarvipScoreArgs) IsSetRequest() bool {
  return p.Request != nil
}

func (p *StarvipServiceAddStarvipScoreArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRequest bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRequest = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRequest{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"));
  }
  return nil
}

func (p *StarvipServiceAddStarvipScoreArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Request = &StarvipAddScoreRequest{}
  if err := p.Request.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
  }
  return nil
}

func (p *StarvipServiceAddStarvipScoreArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "addStarvipScore_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceAddStarvipScoreArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err) }
  if err := p.Request.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err) }
  return err
}

func (p *StarvipServiceAddStarvipScoreArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceAddStarvipScoreArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceAddStarvipScoreResult struct {
  Success *StarvipResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceAddStarvipScoreResult() *StarvipServiceAddStarvipScoreResult {
  return &StarvipServiceAddStarvipScoreResult{}
}

var StarvipServiceAddStarvipScoreResult_Success_DEFAULT *StarvipResult_
func (p *StarvipServiceAddStarvipScoreResult) GetSuccess() *StarvipResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceAddStarvipScoreResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceAddStarvipScoreResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceAddStarvipScoreResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceAddStarvipScoreResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceAddStarvipScoreResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "addStarvipScore_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceAddStarvipScoreResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceAddStarvipScoreResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceAddStarvipScoreResult(%+v)", *p)
}

// Attributes:
//  - Request
type StarvipServiceAdjustStarvipIntegralArgs struct {
  Request *StarvipIntegralAdjustRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewStarvipServiceAdjustStarvipIntegralArgs() *StarvipServiceAdjustStarvipIntegralArgs {
  return &StarvipServiceAdjustStarvipIntegralArgs{}
}

var StarvipServiceAdjustStarvipIntegralArgs_Request_DEFAULT *StarvipIntegralAdjustRequest
func (p *StarvipServiceAdjustStarvipIntegralArgs) GetRequest() *StarvipIntegralAdjustRequest {
  if !p.IsSetRequest() {
    return StarvipServiceAdjustStarvipIntegralArgs_Request_DEFAULT
  }
return p.Request
}
func (p *StarvipServiceAdjustStarvipIntegralArgs) IsSetRequest() bool {
  return p.Request != nil
}

func (p *StarvipServiceAdjustStarvipIntegralArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRequest bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRequest = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRequest{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"));
  }
  return nil
}

func (p *StarvipServiceAdjustStarvipIntegralArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Request = &StarvipIntegralAdjustRequest{}
  if err := p.Request.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
  }
  return nil
}

func (p *StarvipServiceAdjustStarvipIntegralArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "adjustStarvipIntegral_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceAdjustStarvipIntegralArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err) }
  if err := p.Request.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err) }
  return err
}

func (p *StarvipServiceAdjustStarvipIntegralArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceAdjustStarvipIntegralArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceAdjustStarvipIntegralResult struct {
  Success *StarvipResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceAdjustStarvipIntegralResult() *StarvipServiceAdjustStarvipIntegralResult {
  return &StarvipServiceAdjustStarvipIntegralResult{}
}

var StarvipServiceAdjustStarvipIntegralResult_Success_DEFAULT *StarvipResult_
func (p *StarvipServiceAdjustStarvipIntegralResult) GetSuccess() *StarvipResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceAdjustStarvipIntegralResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceAdjustStarvipIntegralResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceAdjustStarvipIntegralResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceAdjustStarvipIntegralResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceAdjustStarvipIntegralResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "adjustStarvipIntegral_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceAdjustStarvipIntegralResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceAdjustStarvipIntegralResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceAdjustStarvipIntegralResult(%+v)", *p)
}

// Attributes:
//  - Request
type StarvipServiceGetStarvipInfoForCustomerArgs struct {
  Request *StarvipRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewStarvipServiceGetStarvipInfoForCustomerArgs() *StarvipServiceGetStarvipInfoForCustomerArgs {
  return &StarvipServiceGetStarvipInfoForCustomerArgs{}
}

var StarvipServiceGetStarvipInfoForCustomerArgs_Request_DEFAULT *StarvipRequest
func (p *StarvipServiceGetStarvipInfoForCustomerArgs) GetRequest() *StarvipRequest {
  if !p.IsSetRequest() {
    return StarvipServiceGetStarvipInfoForCustomerArgs_Request_DEFAULT
  }
return p.Request
}
func (p *StarvipServiceGetStarvipInfoForCustomerArgs) IsSetRequest() bool {
  return p.Request != nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRequest bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRequest = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRequest{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"));
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Request = &StarvipRequest{}
  if err := p.Request.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfoForCustomer_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err) }
  if err := p.Request.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipInfoForCustomerArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoForCustomerArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceGetStarvipInfoForCustomerResult struct {
  Success *StarvipInfoCustomerResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceGetStarvipInfoForCustomerResult() *StarvipServiceGetStarvipInfoForCustomerResult {
  return &StarvipServiceGetStarvipInfoForCustomerResult{}
}

var StarvipServiceGetStarvipInfoForCustomerResult_Success_DEFAULT *StarvipInfoCustomerResult_
func (p *StarvipServiceGetStarvipInfoForCustomerResult) GetSuccess() *StarvipInfoCustomerResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceGetStarvipInfoForCustomerResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceGetStarvipInfoForCustomerResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipInfoCustomerResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipInfoForCustomer_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipInfoForCustomerResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceGetStarvipInfoForCustomerResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipInfoForCustomerResult(%+v)", *p)
}

// Attributes:
//  - KugouIds
type StarvipServiceGetStarvipMysticStatusBatchArgs struct {
  KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
}

func NewStarvipServiceGetStarvipMysticStatusBatchArgs() *StarvipServiceGetStarvipMysticStatusBatchArgs {
  return &StarvipServiceGetStarvipMysticStatusBatchArgs{}
}


func (p *StarvipServiceGetStarvipMysticStatusBatchArgs) GetKugouIds() []int64 {
  return p.KugouIds
}
func (p *StarvipServiceGetStarvipMysticStatusBatchArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouIds bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouIds = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouIds{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"));
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.KugouIds =  tSlice
  for i := 0; i < size; i ++ {
var _elem57 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem57 = v
}
    p.KugouIds = append(p.KugouIds, _elem57)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipMysticStatusBatch_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.KugouIds {
    if err := oprot.WriteI64(ctx, int64(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err) }
  return err
}

func (p *StarvipServiceGetStarvipMysticStatusBatchArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipMysticStatusBatchArgs(%+v)", *p)
}

// Attributes:
//  - Success
type StarvipServiceGetStarvipMysticStatusBatchResult struct {
  Success *StarvipMysticStatusBatchResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewStarvipServiceGetStarvipMysticStatusBatchResult() *StarvipServiceGetStarvipMysticStatusBatchResult {
  return &StarvipServiceGetStarvipMysticStatusBatchResult{}
}

var StarvipServiceGetStarvipMysticStatusBatchResult_Success_DEFAULT *StarvipMysticStatusBatchResult_
func (p *StarvipServiceGetStarvipMysticStatusBatchResult) GetSuccess() *StarvipMysticStatusBatchResult_ {
  if !p.IsSetSuccess() {
    return StarvipServiceGetStarvipMysticStatusBatchResult_Success_DEFAULT
  }
return p.Success
}
func (p *StarvipServiceGetStarvipMysticStatusBatchResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StarvipMysticStatusBatchResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getStarvipMysticStatusBatch_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StarvipServiceGetStarvipMysticStatusBatchResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *StarvipServiceGetStarvipMysticStatusBatchResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StarvipServiceGetStarvipMysticStatusBatchResult(%+v)", *p)
}


