// Code generated by Thrift Compiler (0.19.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"kugou_adapter_service/third-party/gen-go/thrift/platform/user/vo"
	"kugou_adapter_service/third-party/gen-go/thrift/platform/user"
)

var _ = vo.GoUnusedProtection__
var _ = user.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  ResSave saveUser(UserVO userVO)")
  fmt.Fprintln(os.Stderr, "  ResSave update(UserVO userVO, string token)")
  fmt.Fprintln(os.Stderr, "  ResMutiUser getUsersByKugouIds( kugouIds)")
  fmt.Fprintln(os.Stderr, "  ResUser getUserByKugouId(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResUser getSingleUserByKugouId(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResIntegerMsg initUserByKugouId(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResUser initUserByKugouIdV2(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResUser getUserByUserName(string userName)")
  fmt.Fprintln(os.Stderr, "  ResMutiUser getUsersByUserNames( userNames)")
  fmt.Fprintln(os.Stderr, "  ResUser getUserByNickName(string nickName)")
  fmt.Fprintln(os.Stderr, "  ResMutiUser getUsersByNickNames( nickNames)")
  fmt.Fprintln(os.Stderr, "  ResIntegerMsg isExistNickName(string nickName)")
  fmt.Fprintln(os.Stderr, "  ResMutiUser getUserVOsByKugouIdsDynamic( kugouIds, DynamicResponseUserVO dynamicResponseUserVO)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := user.NewUserPlatServiceClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "saveUser":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "SaveUser requires 1 args")
      flag.Usage()
    }
    arg84 := flag.Arg(1)
    mbTrans85 := thrift.NewTMemoryBufferLen(len(arg84))
    defer mbTrans85.Close()
    _, err86 := mbTrans85.WriteString(arg84)
    if err86 != nil {
      Usage()
      return
    }
    factory87 := thrift.NewTJSONProtocolFactory()
    jsProt88 := factory87.GetProtocol(mbTrans85)
    argvalue0 := vo.NewUserVO()
    err89 := argvalue0.Read(context.Background(), jsProt88)
    if err89 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.SaveUser(context.Background(), value0))
    fmt.Print("\n")
    break
  case "update":
    if flag.NArg() - 1 != 2 {
      fmt.Fprintln(os.Stderr, "Update requires 2 args")
      flag.Usage()
    }
    arg90 := flag.Arg(1)
    mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
    defer mbTrans91.Close()
    _, err92 := mbTrans91.WriteString(arg90)
    if err92 != nil {
      Usage()
      return
    }
    factory93 := thrift.NewTJSONProtocolFactory()
    jsProt94 := factory93.GetProtocol(mbTrans91)
    argvalue0 := vo.NewUserVO()
    err95 := argvalue0.Read(context.Background(), jsProt94)
    if err95 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    argvalue1 := flag.Arg(2)
    value1 := argvalue1
    fmt.Print(client.Update(context.Background(), value0, value1))
    fmt.Print("\n")
    break
  case "getUsersByKugouIds":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUsersByKugouIds requires 1 args")
      flag.Usage()
    }
    arg97 := flag.Arg(1)
    mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
    defer mbTrans98.Close()
    _, err99 := mbTrans98.WriteString(arg97)
    if err99 != nil { 
      Usage()
      return
    }
    factory100 := thrift.NewTJSONProtocolFactory()
    jsProt101 := factory100.GetProtocol(mbTrans98)
    containerStruct0 := user.NewUserPlatServiceGetUsersByKugouIdsArgs()
    err102 := containerStruct0.ReadField1(context.Background(), jsProt101)
    if err102 != nil {
      Usage()
      return
    }
    argvalue0 := containerStruct0.KugouIds
    value0 := argvalue0
    fmt.Print(client.GetUsersByKugouIds(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserByKugouId":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserByKugouId requires 1 args")
      flag.Usage()
    }
    argvalue0, err103 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err103 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserByKugouId(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getSingleUserByKugouId":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetSingleUserByKugouId requires 1 args")
      flag.Usage()
    }
    argvalue0, err104 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err104 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetSingleUserByKugouId(context.Background(), value0))
    fmt.Print("\n")
    break
  case "initUserByKugouId":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "InitUserByKugouId requires 1 args")
      flag.Usage()
    }
    argvalue0, err105 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err105 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.InitUserByKugouId(context.Background(), value0))
    fmt.Print("\n")
    break
  case "initUserByKugouIdV2":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "InitUserByKugouIdV2 requires 1 args")
      flag.Usage()
    }
    argvalue0, err106 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err106 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.InitUserByKugouIdV2(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserByUserName":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserByUserName requires 1 args")
      flag.Usage()
    }
    argvalue0 := flag.Arg(1)
    value0 := argvalue0
    fmt.Print(client.GetUserByUserName(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUsersByUserNames":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUsersByUserNames requires 1 args")
      flag.Usage()
    }
    arg108 := flag.Arg(1)
    mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
    defer mbTrans109.Close()
    _, err110 := mbTrans109.WriteString(arg108)
    if err110 != nil { 
      Usage()
      return
    }
    factory111 := thrift.NewTJSONProtocolFactory()
    jsProt112 := factory111.GetProtocol(mbTrans109)
    containerStruct0 := user.NewUserPlatServiceGetUsersByUserNamesArgs()
    err113 := containerStruct0.ReadField1(context.Background(), jsProt112)
    if err113 != nil {
      Usage()
      return
    }
    argvalue0 := containerStruct0.UserNames
    value0 := argvalue0
    fmt.Print(client.GetUsersByUserNames(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserByNickName":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserByNickName requires 1 args")
      flag.Usage()
    }
    argvalue0 := flag.Arg(1)
    value0 := argvalue0
    fmt.Print(client.GetUserByNickName(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUsersByNickNames":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUsersByNickNames requires 1 args")
      flag.Usage()
    }
    arg115 := flag.Arg(1)
    mbTrans116 := thrift.NewTMemoryBufferLen(len(arg115))
    defer mbTrans116.Close()
    _, err117 := mbTrans116.WriteString(arg115)
    if err117 != nil { 
      Usage()
      return
    }
    factory118 := thrift.NewTJSONProtocolFactory()
    jsProt119 := factory118.GetProtocol(mbTrans116)
    containerStruct0 := user.NewUserPlatServiceGetUsersByNickNamesArgs()
    err120 := containerStruct0.ReadField1(context.Background(), jsProt119)
    if err120 != nil {
      Usage()
      return
    }
    argvalue0 := containerStruct0.NickNames
    value0 := argvalue0
    fmt.Print(client.GetUsersByNickNames(context.Background(), value0))
    fmt.Print("\n")
    break
  case "isExistNickName":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "IsExistNickName requires 1 args")
      flag.Usage()
    }
    argvalue0 := flag.Arg(1)
    value0 := argvalue0
    fmt.Print(client.IsExistNickName(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserVOsByKugouIdsDynamic":
    if flag.NArg() - 1 != 2 {
      fmt.Fprintln(os.Stderr, "GetUserVOsByKugouIdsDynamic requires 2 args")
      flag.Usage()
    }
    arg122 := flag.Arg(1)
    mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
    defer mbTrans123.Close()
    _, err124 := mbTrans123.WriteString(arg122)
    if err124 != nil { 
      Usage()
      return
    }
    factory125 := thrift.NewTJSONProtocolFactory()
    jsProt126 := factory125.GetProtocol(mbTrans123)
    containerStruct0 := user.NewUserPlatServiceGetUserVOsByKugouIdsDynamicArgs()
    err127 := containerStruct0.ReadField1(context.Background(), jsProt126)
    if err127 != nil {
      Usage()
      return
    }
    argvalue0 := containerStruct0.KugouIds
    value0 := argvalue0
    arg128 := flag.Arg(2)
    mbTrans129 := thrift.NewTMemoryBufferLen(len(arg128))
    defer mbTrans129.Close()
    _, err130 := mbTrans129.WriteString(arg128)
    if err130 != nil {
      Usage()
      return
    }
    factory131 := thrift.NewTJSONProtocolFactory()
    jsProt132 := factory131.GetProtocol(mbTrans129)
    argvalue1 := vo.NewDynamicResponseUserVO()
    err133 := argvalue1.Read(context.Background(), jsProt132)
    if err133 != nil {
      Usage()
      return
    }
    value1 := argvalue1
    fmt.Print(client.GetUserVOsByKugouIdsDynamic(context.Background(), value0, value1))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
