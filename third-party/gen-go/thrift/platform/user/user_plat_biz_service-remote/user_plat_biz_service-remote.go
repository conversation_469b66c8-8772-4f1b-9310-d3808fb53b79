// Code generated by Thrift Compiler (0.19.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"kugou_adapter_service/third-party/gen-go/thrift/platform/user/vo"
	"kugou_adapter_service/third-party/gen-go/thrift/platform/user"
)

var _ = vo.GoUnusedProtection__
var _ = user.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  ResMsg isBindMobileNum(i64 kugouId, string clientIp)")
  fmt.Fprintln(os.<PERSON>r, "  MResMsg risksQueryAndIsBindMobileNum(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResMsg getMobileNum(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResMobileResponse getMobileNumV1(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResMobileResponse getMobileNumInRealTime(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResMsg getUserInfoFromKugou(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResMsg getUserInfoFromKugouV1(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResUserCaptchaResponse sendCaptcha(UserCaptchaVo request)")
  fmt.Fprintln(os.Stderr, "  ResCheckUserCaptchaResponse checkCaptcha(CheckUserCaptchaVo request)")
  fmt.Fprintln(os.Stderr, "  ResBindMobilePhoneResponse bindPhone(BindMobilePhoneVo request)")
  fmt.Fprintln(os.Stderr, "  ResCheckBindMobilePhoneResponse checkBindPhoneInfo(CheckBindMobilePhoneVo request)")
  fmt.Fprintln(os.Stderr, "  ResUserCancelStatusResponse getUserCancelStatus(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ResUserBirthDayResponse batchGetUserBirthDay( kugouIds)")
  fmt.Fprintln(os.Stderr, "  SendSmsByKugouIdResp sendSmsByKugouId(SendSmsByKugouIdReq request)")
  fmt.Fprintln(os.Stderr, "  KugouUserLoginResp loginKugouSystem(KugouUserLoginReq userVO)")
  fmt.Fprintln(os.Stderr, "  UserLoginInfoResp getUserLoginInfo(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  GetLazyUserResp getLazyUserInfo(GetLazyUserReq req)")
  fmt.Fprintln(os.Stderr, "  VisitorResp getUserVisitorInfo(VisitorReq req)")
  fmt.Fprintln(os.Stderr, "  CanVisibleResp canVisible(CanVisibleReq req)")
  fmt.Fprintln(os.Stderr, "  GetUserExtResp getUserExtInfo(GetUserExtReq req)")
  fmt.Fprintln(os.Stderr, "  MobileRelatorResp getMobileRelator(i64 kugouId)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := user.NewUserPlatBizServiceClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "isBindMobileNum":
    if flag.NArg() - 1 != 2 {
      fmt.Fprintln(os.Stderr, "IsBindMobileNum requires 2 args")
      flag.Usage()
    }
    argvalue0, err367 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err367 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    argvalue1 := flag.Arg(2)
    value1 := argvalue1
    fmt.Print(client.IsBindMobileNum(context.Background(), value0, value1))
    fmt.Print("\n")
    break
  case "risksQueryAndIsBindMobileNum":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "RisksQueryAndIsBindMobileNum requires 1 args")
      flag.Usage()
    }
    argvalue0, err369 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err369 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.RisksQueryAndIsBindMobileNum(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getMobileNum":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetMobileNum requires 1 args")
      flag.Usage()
    }
    argvalue0, err370 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err370 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetMobileNum(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getMobileNumV1":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetMobileNumV1 requires 1 args")
      flag.Usage()
    }
    argvalue0, err371 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err371 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetMobileNumV1(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getMobileNumInRealTime":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetMobileNumInRealTime requires 1 args")
      flag.Usage()
    }
    argvalue0, err372 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err372 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetMobileNumInRealTime(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserInfoFromKugou":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserInfoFromKugou requires 1 args")
      flag.Usage()
    }
    argvalue0, err373 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err373 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserInfoFromKugou(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserInfoFromKugouV1":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserInfoFromKugouV1 requires 1 args")
      flag.Usage()
    }
    argvalue0, err374 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err374 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserInfoFromKugouV1(context.Background(), value0))
    fmt.Print("\n")
    break
  case "sendCaptcha":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "SendCaptcha requires 1 args")
      flag.Usage()
    }
    arg375 := flag.Arg(1)
    mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
    defer mbTrans376.Close()
    _, err377 := mbTrans376.WriteString(arg375)
    if err377 != nil {
      Usage()
      return
    }
    factory378 := thrift.NewTJSONProtocolFactory()
    jsProt379 := factory378.GetProtocol(mbTrans376)
    argvalue0 := vo.NewUserCaptchaVo()
    err380 := argvalue0.Read(context.Background(), jsProt379)
    if err380 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.SendCaptcha(context.Background(), value0))
    fmt.Print("\n")
    break
  case "checkCaptcha":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CheckCaptcha requires 1 args")
      flag.Usage()
    }
    arg381 := flag.Arg(1)
    mbTrans382 := thrift.NewTMemoryBufferLen(len(arg381))
    defer mbTrans382.Close()
    _, err383 := mbTrans382.WriteString(arg381)
    if err383 != nil {
      Usage()
      return
    }
    factory384 := thrift.NewTJSONProtocolFactory()
    jsProt385 := factory384.GetProtocol(mbTrans382)
    argvalue0 := vo.NewCheckUserCaptchaVo()
    err386 := argvalue0.Read(context.Background(), jsProt385)
    if err386 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CheckCaptcha(context.Background(), value0))
    fmt.Print("\n")
    break
  case "bindPhone":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "BindPhone requires 1 args")
      flag.Usage()
    }
    arg387 := flag.Arg(1)
    mbTrans388 := thrift.NewTMemoryBufferLen(len(arg387))
    defer mbTrans388.Close()
    _, err389 := mbTrans388.WriteString(arg387)
    if err389 != nil {
      Usage()
      return
    }
    factory390 := thrift.NewTJSONProtocolFactory()
    jsProt391 := factory390.GetProtocol(mbTrans388)
    argvalue0 := vo.NewBindMobilePhoneVo()
    err392 := argvalue0.Read(context.Background(), jsProt391)
    if err392 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.BindPhone(context.Background(), value0))
    fmt.Print("\n")
    break
  case "checkBindPhoneInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CheckBindPhoneInfo requires 1 args")
      flag.Usage()
    }
    arg393 := flag.Arg(1)
    mbTrans394 := thrift.NewTMemoryBufferLen(len(arg393))
    defer mbTrans394.Close()
    _, err395 := mbTrans394.WriteString(arg393)
    if err395 != nil {
      Usage()
      return
    }
    factory396 := thrift.NewTJSONProtocolFactory()
    jsProt397 := factory396.GetProtocol(mbTrans394)
    argvalue0 := vo.NewCheckBindMobilePhoneVo()
    err398 := argvalue0.Read(context.Background(), jsProt397)
    if err398 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CheckBindPhoneInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserCancelStatus":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserCancelStatus requires 1 args")
      flag.Usage()
    }
    argvalue0, err399 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err399 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserCancelStatus(context.Background(), value0))
    fmt.Print("\n")
    break
  case "batchGetUserBirthDay":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "BatchGetUserBirthDay requires 1 args")
      flag.Usage()
    }
    arg400 := flag.Arg(1)
    mbTrans401 := thrift.NewTMemoryBufferLen(len(arg400))
    defer mbTrans401.Close()
    _, err402 := mbTrans401.WriteString(arg400)
    if err402 != nil { 
      Usage()
      return
    }
    factory403 := thrift.NewTJSONProtocolFactory()
    jsProt404 := factory403.GetProtocol(mbTrans401)
    containerStruct0 := user.NewUserPlatBizServiceBatchGetUserBirthDayArgs()
    err405 := containerStruct0.ReadField1(context.Background(), jsProt404)
    if err405 != nil {
      Usage()
      return
    }
    argvalue0 := containerStruct0.KugouIds
    value0 := argvalue0
    fmt.Print(client.BatchGetUserBirthDay(context.Background(), value0))
    fmt.Print("\n")
    break
  case "sendSmsByKugouId":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "SendSmsByKugouId requires 1 args")
      flag.Usage()
    }
    arg406 := flag.Arg(1)
    mbTrans407 := thrift.NewTMemoryBufferLen(len(arg406))
    defer mbTrans407.Close()
    _, err408 := mbTrans407.WriteString(arg406)
    if err408 != nil {
      Usage()
      return
    }
    factory409 := thrift.NewTJSONProtocolFactory()
    jsProt410 := factory409.GetProtocol(mbTrans407)
    argvalue0 := vo.NewSendSmsByKugouIdReq()
    err411 := argvalue0.Read(context.Background(), jsProt410)
    if err411 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.SendSmsByKugouId(context.Background(), value0))
    fmt.Print("\n")
    break
  case "loginKugouSystem":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "LoginKugouSystem requires 1 args")
      flag.Usage()
    }
    arg412 := flag.Arg(1)
    mbTrans413 := thrift.NewTMemoryBufferLen(len(arg412))
    defer mbTrans413.Close()
    _, err414 := mbTrans413.WriteString(arg412)
    if err414 != nil {
      Usage()
      return
    }
    factory415 := thrift.NewTJSONProtocolFactory()
    jsProt416 := factory415.GetProtocol(mbTrans413)
    argvalue0 := vo.NewKugouUserLoginReq()
    err417 := argvalue0.Read(context.Background(), jsProt416)
    if err417 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.LoginKugouSystem(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserLoginInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserLoginInfo requires 1 args")
      flag.Usage()
    }
    argvalue0, err418 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err418 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserLoginInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getLazyUserInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetLazyUserInfo requires 1 args")
      flag.Usage()
    }
    arg419 := flag.Arg(1)
    mbTrans420 := thrift.NewTMemoryBufferLen(len(arg419))
    defer mbTrans420.Close()
    _, err421 := mbTrans420.WriteString(arg419)
    if err421 != nil {
      Usage()
      return
    }
    factory422 := thrift.NewTJSONProtocolFactory()
    jsProt423 := factory422.GetProtocol(mbTrans420)
    argvalue0 := vo.NewGetLazyUserReq()
    err424 := argvalue0.Read(context.Background(), jsProt423)
    if err424 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetLazyUserInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserVisitorInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserVisitorInfo requires 1 args")
      flag.Usage()
    }
    arg425 := flag.Arg(1)
    mbTrans426 := thrift.NewTMemoryBufferLen(len(arg425))
    defer mbTrans426.Close()
    _, err427 := mbTrans426.WriteString(arg425)
    if err427 != nil {
      Usage()
      return
    }
    factory428 := thrift.NewTJSONProtocolFactory()
    jsProt429 := factory428.GetProtocol(mbTrans426)
    argvalue0 := vo.NewVisitorReq()
    err430 := argvalue0.Read(context.Background(), jsProt429)
    if err430 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserVisitorInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "canVisible":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CanVisible requires 1 args")
      flag.Usage()
    }
    arg431 := flag.Arg(1)
    mbTrans432 := thrift.NewTMemoryBufferLen(len(arg431))
    defer mbTrans432.Close()
    _, err433 := mbTrans432.WriteString(arg431)
    if err433 != nil {
      Usage()
      return
    }
    factory434 := thrift.NewTJSONProtocolFactory()
    jsProt435 := factory434.GetProtocol(mbTrans432)
    argvalue0 := vo.NewCanVisibleReq()
    err436 := argvalue0.Read(context.Background(), jsProt435)
    if err436 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CanVisible(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserExtInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserExtInfo requires 1 args")
      flag.Usage()
    }
    arg437 := flag.Arg(1)
    mbTrans438 := thrift.NewTMemoryBufferLen(len(arg437))
    defer mbTrans438.Close()
    _, err439 := mbTrans438.WriteString(arg437)
    if err439 != nil {
      Usage()
      return
    }
    factory440 := thrift.NewTJSONProtocolFactory()
    jsProt441 := factory440.GetProtocol(mbTrans438)
    argvalue0 := vo.NewGetUserExtReq()
    err442 := argvalue0.Read(context.Background(), jsProt441)
    if err442 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserExtInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getMobileRelator":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetMobileRelator requires 1 args")
      flag.Usage()
    }
    argvalue0, err443 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err443 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetMobileRelator(context.Background(), value0))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
