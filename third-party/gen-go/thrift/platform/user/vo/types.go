// Code generated by Thrift Compiler (0.19.0). DO NOT EDIT.

package vo

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"strings"
	"regexp"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal
// (needed by validator.)
var _ = strings.Contains
var _ = regexp.MatchString

// 用户信息实体
// 
// 
// Attributes:
//  - KugouId:     * 酷狗id
// *
//  - Email:     * email
// *
//  - UserName:     * 用户名
// *
//  - NickName:     * 昵称
// *
//  - UserLogo:     * 用户logo
// *
//  - Status:     * 状态(0:正常，1:封号)
// *
//  - Sex:     * 性别(0:保密,1:男,2:女)
// *
//  - FromType: 来源(0:官网,1:酷狗,2:qq,3:新浪,4:人人,25:天翼)
// 
//  - AddTime:     * 注册时间
// *
//  - RegIp:     * 注册ip
// *
//  - Constellation:     * 星座(0:保密,1:白羊座,2:金牛座,3:双子座,4:巨蟹座,5:狮子座,6:处女座,7:天秤座,8:天蝎座,9:射手座,10:摩羯座,11:水瓶座,12:双鱼座)
// *
//  - Height:     * 身高[厘米]
// *
//  - Weight:     * 体重[公斤]
// *
//  - Weibo:     * 微博
// *
//  - Location: 所在地
// 
//  - Bwh: 三围
// 
//  - RichLevel: 财富等级
// 
//  - StarLevel: 明星等级
// 
//  - ProductLineId: 产品线 0:繁星（系统上线前数据）,1:繁星, 2:KuGou Live, 3:移动直播
// 
type UserVO struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Email *string `thrift:"email,2" db:"email" json:"email,omitempty"`
  UserName *string `thrift:"userName,3" db:"userName" json:"userName,omitempty"`
  NickName *string `thrift:"nickName,4" db:"nickName" json:"nickName,omitempty"`
  UserLogo *string `thrift:"userLogo,5" db:"userLogo" json:"userLogo,omitempty"`
  Status *int32 `thrift:"status,6" db:"status" json:"status,omitempty"`
  Sex *int32 `thrift:"sex,7" db:"sex" json:"sex,omitempty"`
  FromType *int32 `thrift:"fromType,8" db:"fromType" json:"fromType,omitempty"`
  AddTime *int32 `thrift:"addTime,9" db:"addTime" json:"addTime,omitempty"`
  RegIp *string `thrift:"regIp,10" db:"regIp" json:"regIp,omitempty"`
  Constellation *int32 `thrift:"constellation,11" db:"constellation" json:"constellation,omitempty"`
  Height *int32 `thrift:"height,12" db:"height" json:"height,omitempty"`
  Weight *int32 `thrift:"weight,13" db:"weight" json:"weight,omitempty"`
  Weibo *string `thrift:"weibo,14" db:"weibo" json:"weibo,omitempty"`
  Location *string `thrift:"location,15" db:"location" json:"location,omitempty"`
  Bwh *string `thrift:"bwh,16" db:"bwh" json:"bwh,omitempty"`
  RichLevel *int32 `thrift:"richLevel,17" db:"richLevel" json:"richLevel,omitempty"`
  StarLevel *int32 `thrift:"starLevel,18" db:"starLevel" json:"starLevel,omitempty"`
  ProductLineId *int32 `thrift:"productLineId,19" db:"productLineId" json:"productLineId,omitempty"`
}

func NewUserVO() *UserVO {
  return &UserVO{}
}


func (p *UserVO) GetKugouId() int64 {
  return p.KugouId
}
var UserVO_Email_DEFAULT string
func (p *UserVO) GetEmail() string {
  if !p.IsSetEmail() {
    return UserVO_Email_DEFAULT
  }
return *p.Email
}
var UserVO_UserName_DEFAULT string
func (p *UserVO) GetUserName() string {
  if !p.IsSetUserName() {
    return UserVO_UserName_DEFAULT
  }
return *p.UserName
}
var UserVO_NickName_DEFAULT string
func (p *UserVO) GetNickName() string {
  if !p.IsSetNickName() {
    return UserVO_NickName_DEFAULT
  }
return *p.NickName
}
var UserVO_UserLogo_DEFAULT string
func (p *UserVO) GetUserLogo() string {
  if !p.IsSetUserLogo() {
    return UserVO_UserLogo_DEFAULT
  }
return *p.UserLogo
}
var UserVO_Status_DEFAULT int32
func (p *UserVO) GetStatus() int32 {
  if !p.IsSetStatus() {
    return UserVO_Status_DEFAULT
  }
return *p.Status
}
var UserVO_Sex_DEFAULT int32
func (p *UserVO) GetSex() int32 {
  if !p.IsSetSex() {
    return UserVO_Sex_DEFAULT
  }
return *p.Sex
}
var UserVO_FromType_DEFAULT int32
func (p *UserVO) GetFromType() int32 {
  if !p.IsSetFromType() {
    return UserVO_FromType_DEFAULT
  }
return *p.FromType
}
var UserVO_AddTime_DEFAULT int32
func (p *UserVO) GetAddTime() int32 {
  if !p.IsSetAddTime() {
    return UserVO_AddTime_DEFAULT
  }
return *p.AddTime
}
var UserVO_RegIp_DEFAULT string
func (p *UserVO) GetRegIp() string {
  if !p.IsSetRegIp() {
    return UserVO_RegIp_DEFAULT
  }
return *p.RegIp
}
var UserVO_Constellation_DEFAULT int32
func (p *UserVO) GetConstellation() int32 {
  if !p.IsSetConstellation() {
    return UserVO_Constellation_DEFAULT
  }
return *p.Constellation
}
var UserVO_Height_DEFAULT int32
func (p *UserVO) GetHeight() int32 {
  if !p.IsSetHeight() {
    return UserVO_Height_DEFAULT
  }
return *p.Height
}
var UserVO_Weight_DEFAULT int32
func (p *UserVO) GetWeight() int32 {
  if !p.IsSetWeight() {
    return UserVO_Weight_DEFAULT
  }
return *p.Weight
}
var UserVO_Weibo_DEFAULT string
func (p *UserVO) GetWeibo() string {
  if !p.IsSetWeibo() {
    return UserVO_Weibo_DEFAULT
  }
return *p.Weibo
}
var UserVO_Location_DEFAULT string
func (p *UserVO) GetLocation() string {
  if !p.IsSetLocation() {
    return UserVO_Location_DEFAULT
  }
return *p.Location
}
var UserVO_Bwh_DEFAULT string
func (p *UserVO) GetBwh() string {
  if !p.IsSetBwh() {
    return UserVO_Bwh_DEFAULT
  }
return *p.Bwh
}
var UserVO_RichLevel_DEFAULT int32
func (p *UserVO) GetRichLevel() int32 {
  if !p.IsSetRichLevel() {
    return UserVO_RichLevel_DEFAULT
  }
return *p.RichLevel
}
var UserVO_StarLevel_DEFAULT int32
func (p *UserVO) GetStarLevel() int32 {
  if !p.IsSetStarLevel() {
    return UserVO_StarLevel_DEFAULT
  }
return *p.StarLevel
}
var UserVO_ProductLineId_DEFAULT int32
func (p *UserVO) GetProductLineId() int32 {
  if !p.IsSetProductLineId() {
    return UserVO_ProductLineId_DEFAULT
  }
return *p.ProductLineId
}
func (p *UserVO) IsSetEmail() bool {
  return p.Email != nil
}

func (p *UserVO) IsSetUserName() bool {
  return p.UserName != nil
}

func (p *UserVO) IsSetNickName() bool {
  return p.NickName != nil
}

func (p *UserVO) IsSetUserLogo() bool {
  return p.UserLogo != nil
}

func (p *UserVO) IsSetStatus() bool {
  return p.Status != nil
}

func (p *UserVO) IsSetSex() bool {
  return p.Sex != nil
}

func (p *UserVO) IsSetFromType() bool {
  return p.FromType != nil
}

func (p *UserVO) IsSetAddTime() bool {
  return p.AddTime != nil
}

func (p *UserVO) IsSetRegIp() bool {
  return p.RegIp != nil
}

func (p *UserVO) IsSetConstellation() bool {
  return p.Constellation != nil
}

func (p *UserVO) IsSetHeight() bool {
  return p.Height != nil
}

func (p *UserVO) IsSetWeight() bool {
  return p.Weight != nil
}

func (p *UserVO) IsSetWeibo() bool {
  return p.Weibo != nil
}

func (p *UserVO) IsSetLocation() bool {
  return p.Location != nil
}

func (p *UserVO) IsSetBwh() bool {
  return p.Bwh != nil
}

func (p *UserVO) IsSetRichLevel() bool {
  return p.RichLevel != nil
}

func (p *UserVO) IsSetStarLevel() bool {
  return p.StarLevel != nil
}

func (p *UserVO) IsSetProductLineId() bool {
  return p.ProductLineId != nil
}

func (p *UserVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 15:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField15(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 16:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField16(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 17:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField17(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 18:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField18(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 19:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField19(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  return nil
}

func (p *UserVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *UserVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Email = &v
}
  return nil
}

func (p *UserVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.UserName = &v
}
  return nil
}

func (p *UserVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.NickName = &v
}
  return nil
}

func (p *UserVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.UserLogo = &v
}
  return nil
}

func (p *UserVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Status = &v
}
  return nil
}

func (p *UserVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *UserVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.FromType = &v
}
  return nil
}

func (p *UserVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.AddTime = &v
}
  return nil
}

func (p *UserVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.RegIp = &v
}
  return nil
}

func (p *UserVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.Constellation = &v
}
  return nil
}

func (p *UserVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.Height = &v
}
  return nil
}

func (p *UserVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.Weight = &v
}
  return nil
}

func (p *UserVO)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.Weibo = &v
}
  return nil
}

func (p *UserVO)  ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 15: ", err)
} else {
  p.Location = &v
}
  return nil
}

func (p *UserVO)  ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 16: ", err)
} else {
  p.Bwh = &v
}
  return nil
}

func (p *UserVO)  ReadField17(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 17: ", err)
} else {
  p.RichLevel = &v
}
  return nil
}

func (p *UserVO)  ReadField18(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 18: ", err)
} else {
  p.StarLevel = &v
}
  return nil
}

func (p *UserVO)  ReadField19(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 19: ", err)
} else {
  p.ProductLineId = &v
}
  return nil
}

func (p *UserVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
    if err := p.writeField15(ctx, oprot); err != nil { return err }
    if err := p.writeField16(ctx, oprot); err != nil { return err }
    if err := p.writeField17(ctx, oprot); err != nil { return err }
    if err := p.writeField18(ctx, oprot); err != nil { return err }
    if err := p.writeField19(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *UserVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetEmail() {
    if err := oprot.WriteFieldBegin(ctx, "email", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:email: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Email)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.email (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:email: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserName() {
    if err := oprot.WriteFieldBegin(ctx, "userName", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:userName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userName (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:userName: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNickName() {
    if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:nickName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NickName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.nickName (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:nickName: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserLogo() {
    if err := oprot.WriteFieldBegin(ctx, "userLogo", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:userLogo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserLogo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userLogo (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:userLogo: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStatus() {
    if err := oprot.WriteFieldBegin(ctx, "status", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:status: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Status)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.status (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:status: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:sex: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFromType() {
    if err := oprot.WriteFieldBegin(ctx, "fromType", thrift.I32, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:fromType: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.FromType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.fromType (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:fromType: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAddTime() {
    if err := oprot.WriteFieldBegin(ctx, "addTime", thrift.I32, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:addTime: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AddTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.addTime (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:addTime: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRegIp() {
    if err := oprot.WriteFieldBegin(ctx, "regIp", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:regIp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.RegIp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.regIp (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:regIp: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetConstellation() {
    if err := oprot.WriteFieldBegin(ctx, "constellation", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:constellation: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Constellation)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.constellation (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:constellation: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeight() {
    if err := oprot.WriteFieldBegin(ctx, "height", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:height: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Height)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.height (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:height: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeight() {
    if err := oprot.WriteFieldBegin(ctx, "weight", thrift.I32, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:weight: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Weight)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weight (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:weight: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeibo() {
    if err := oprot.WriteFieldBegin(ctx, "weibo", thrift.STRING, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:weibo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Weibo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weibo (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:weibo: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLocation() {
    if err := oprot.WriteFieldBegin(ctx, "location", thrift.STRING, 15); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:location: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Location)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.location (15) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 15:location: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBwh() {
    if err := oprot.WriteFieldBegin(ctx, "bwh", thrift.STRING, 16); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:bwh: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Bwh)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.bwh (16) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 16:bwh: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField17(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichLevel() {
    if err := oprot.WriteFieldBegin(ctx, "richLevel", thrift.I32, 17); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 17:richLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RichLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richLevel (17) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 17:richLevel: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField18(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarLevel() {
    if err := oprot.WriteFieldBegin(ctx, "starLevel", thrift.I32, 18); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 18:starLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StarLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starLevel (18) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 18:starLevel: ", p), err) }
  }
  return err
}

func (p *UserVO) writeField19(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetProductLineId() {
    if err := oprot.WriteFieldBegin(ctx, "productLineId", thrift.I32, 19); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 19:productLineId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.ProductLineId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.productLineId (19) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 19:productLineId: ", p), err) }
  }
  return err
}

func (p *UserVO) Equals(other *UserVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Email != other.Email {
    if p.Email == nil || other.Email == nil {
      return false
    }
    if (*p.Email) != (*other.Email) { return false }
  }
  if p.UserName != other.UserName {
    if p.UserName == nil || other.UserName == nil {
      return false
    }
    if (*p.UserName) != (*other.UserName) { return false }
  }
  if p.NickName != other.NickName {
    if p.NickName == nil || other.NickName == nil {
      return false
    }
    if (*p.NickName) != (*other.NickName) { return false }
  }
  if p.UserLogo != other.UserLogo {
    if p.UserLogo == nil || other.UserLogo == nil {
      return false
    }
    if (*p.UserLogo) != (*other.UserLogo) { return false }
  }
  if p.Status != other.Status {
    if p.Status == nil || other.Status == nil {
      return false
    }
    if (*p.Status) != (*other.Status) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.FromType != other.FromType {
    if p.FromType == nil || other.FromType == nil {
      return false
    }
    if (*p.FromType) != (*other.FromType) { return false }
  }
  if p.AddTime != other.AddTime {
    if p.AddTime == nil || other.AddTime == nil {
      return false
    }
    if (*p.AddTime) != (*other.AddTime) { return false }
  }
  if p.RegIp != other.RegIp {
    if p.RegIp == nil || other.RegIp == nil {
      return false
    }
    if (*p.RegIp) != (*other.RegIp) { return false }
  }
  if p.Constellation != other.Constellation {
    if p.Constellation == nil || other.Constellation == nil {
      return false
    }
    if (*p.Constellation) != (*other.Constellation) { return false }
  }
  if p.Height != other.Height {
    if p.Height == nil || other.Height == nil {
      return false
    }
    if (*p.Height) != (*other.Height) { return false }
  }
  if p.Weight != other.Weight {
    if p.Weight == nil || other.Weight == nil {
      return false
    }
    if (*p.Weight) != (*other.Weight) { return false }
  }
  if p.Weibo != other.Weibo {
    if p.Weibo == nil || other.Weibo == nil {
      return false
    }
    if (*p.Weibo) != (*other.Weibo) { return false }
  }
  if p.Location != other.Location {
    if p.Location == nil || other.Location == nil {
      return false
    }
    if (*p.Location) != (*other.Location) { return false }
  }
  if p.Bwh != other.Bwh {
    if p.Bwh == nil || other.Bwh == nil {
      return false
    }
    if (*p.Bwh) != (*other.Bwh) { return false }
  }
  if p.RichLevel != other.RichLevel {
    if p.RichLevel == nil || other.RichLevel == nil {
      return false
    }
    if (*p.RichLevel) != (*other.RichLevel) { return false }
  }
  if p.StarLevel != other.StarLevel {
    if p.StarLevel == nil || other.StarLevel == nil {
      return false
    }
    if (*p.StarLevel) != (*other.StarLevel) { return false }
  }
  if p.ProductLineId != other.ProductLineId {
    if p.ProductLineId == nil || other.ProductLineId == nil {
      return false
    }
    if (*p.ProductLineId) != (*other.ProductLineId) { return false }
  }
  return true
}

func (p *UserVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserVO(%+v)", *p)
}

func (p *UserVO) Validate() error {
  return nil
}
// Attributes:
//  - Username
//  - Password
//  - AppId
//  - ClientIp
//  - Plat
type KugouUserLoginReq struct {
  Username string `thrift:"username,1,required" db:"username" json:"username"`
  Password string `thrift:"password,2,required" db:"password" json:"password"`
  AppId int32 `thrift:"appId,3,required" db:"appId" json:"appId"`
  ClientIp string `thrift:"clientIp,4,required" db:"clientIp" json:"clientIp"`
  Plat int32 `thrift:"plat,5,required" db:"plat" json:"plat"`
}

func NewKugouUserLoginReq() *KugouUserLoginReq {
  return &KugouUserLoginReq{}
}


func (p *KugouUserLoginReq) GetUsername() string {
  return p.Username
}

func (p *KugouUserLoginReq) GetPassword() string {
  return p.Password
}

func (p *KugouUserLoginReq) GetAppId() int32 {
  return p.AppId
}

func (p *KugouUserLoginReq) GetClientIp() string {
  return p.ClientIp
}

func (p *KugouUserLoginReq) GetPlat() int32 {
  return p.Plat
}
func (p *KugouUserLoginReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetUsername bool = false;
  var issetPassword bool = false;
  var issetAppId bool = false;
  var issetClientIp bool = false;
  var issetPlat bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetUsername = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetPassword = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetClientIp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetPlat = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetUsername{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Username is not set"));
  }
  if !issetPassword{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Password is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetClientIp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ClientIp is not set"));
  }
  if !issetPlat{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Plat is not set"));
  }
  return nil
}

func (p *KugouUserLoginReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Username = v
}
  return nil
}

func (p *KugouUserLoginReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Password = v
}
  return nil
}

func (p *KugouUserLoginReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *KugouUserLoginReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.ClientIp = v
}
  return nil
}

func (p *KugouUserLoginReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Plat = v
}
  return nil
}

func (p *KugouUserLoginReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "KugouUserLoginReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *KugouUserLoginReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "username", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:username: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Username)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.username (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:username: ", p), err) }
  return err
}

func (p *KugouUserLoginReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "password", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:password: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Password)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.password (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:password: ", p), err) }
  return err
}

func (p *KugouUserLoginReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err) }
  return err
}

func (p *KugouUserLoginReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "clientIp", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:clientIp: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ClientIp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.clientIp (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:clientIp: ", p), err) }
  return err
}

func (p *KugouUserLoginReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "plat", thrift.I32, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:plat: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Plat)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.plat (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:plat: ", p), err) }
  return err
}

func (p *KugouUserLoginReq) Equals(other *KugouUserLoginReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Username != other.Username { return false }
  if p.Password != other.Password { return false }
  if p.AppId != other.AppId { return false }
  if p.ClientIp != other.ClientIp { return false }
  if p.Plat != other.Plat { return false }
  return true
}

func (p *KugouUserLoginReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("KugouUserLoginReq(%+v)", *p)
}

func (p *KugouUserLoginReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type KugouUserLoginResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *KugouUserLogin `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewKugouUserLoginResp() *KugouUserLoginResp {
  return &KugouUserLoginResp{}
}


func (p *KugouUserLoginResp) GetCode() int32 {
  return p.Code
}

func (p *KugouUserLoginResp) GetMsg() string {
  return p.Msg
}
var KugouUserLoginResp_Data_DEFAULT *KugouUserLogin
func (p *KugouUserLoginResp) GetData() *KugouUserLogin {
  if !p.IsSetData() {
    return KugouUserLoginResp_Data_DEFAULT
  }
return p.Data
}
func (p *KugouUserLoginResp) IsSetData() bool {
  return p.Data != nil
}

func (p *KugouUserLoginResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *KugouUserLoginResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *KugouUserLoginResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *KugouUserLoginResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &KugouUserLogin{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *KugouUserLoginResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "KugouUserLoginResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *KugouUserLoginResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *KugouUserLoginResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *KugouUserLoginResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *KugouUserLoginResp) Equals(other *KugouUserLoginResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *KugouUserLoginResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("KugouUserLoginResp(%+v)", *p)
}

func (p *KugouUserLoginResp) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Token
//  - AppId
type KugouUserLogin struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Token string `thrift:"token,2,required" db:"token" json:"token"`
  AppId int32 `thrift:"appId,3,required" db:"appId" json:"appId"`
}

func NewKugouUserLogin() *KugouUserLogin {
  return &KugouUserLogin{}
}


func (p *KugouUserLogin) GetKugouId() int64 {
  return p.KugouId
}

func (p *KugouUserLogin) GetToken() string {
  return p.Token
}

func (p *KugouUserLogin) GetAppId() int32 {
  return p.AppId
}
func (p *KugouUserLogin) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetToken bool = false;
  var issetAppId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetToken = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetToken{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Token is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  return nil
}

func (p *KugouUserLogin)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *KugouUserLogin)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Token = v
}
  return nil
}

func (p *KugouUserLogin)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *KugouUserLogin) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "KugouUserLogin"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *KugouUserLogin) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *KugouUserLogin) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:token: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Token)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.token (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:token: ", p), err) }
  return err
}

func (p *KugouUserLogin) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err) }
  return err
}

func (p *KugouUserLogin) Equals(other *KugouUserLogin) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Token != other.Token { return false }
  if p.AppId != other.AppId { return false }
  return true
}

func (p *KugouUserLogin) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("KugouUserLogin(%+v)", *p)
}

func (p *KugouUserLogin) Validate() error {
  return nil
}
// 保存接口返回
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后1位为0为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
type ResSave struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
}

func NewResSave() *ResSave {
  return &ResSave{}
}


func (p *ResSave) GetResponseCode() string {
  return p.ResponseCode
}
var ResSave_ResponseDesp_DEFAULT string
func (p *ResSave) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResSave_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
func (p *ResSave) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResSave) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  return nil
}

func (p *ResSave)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *ResSave)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResSave) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResSave"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResSave) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *ResSave) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResSave) Equals(other *ResSave) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  return true
}

func (p *ResSave) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResSave(%+v)", *p)
}

func (p *ResSave) Validate() error {
  return nil
}
// 统计数返回
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后3位为000为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
//  - Data
type ResCount struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data int64 `thrift:"data,3,required" db:"data" json:"data"`
}

func NewResCount() *ResCount {
  return &ResCount{}
}


func (p *ResCount) GetResponseCode() string {
  return p.ResponseCode
}
var ResCount_ResponseDesp_DEFAULT string
func (p *ResCount) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResCount_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}

func (p *ResCount) GetData() int64 {
  return p.Data
}
func (p *ResCount) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResCount) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;
  var issetData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  return nil
}

func (p *ResCount)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *ResCount)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResCount)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = v
}
  return nil
}

func (p *ResCount) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResCount"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResCount) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *ResCount) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResCount) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Data)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  return err
}

func (p *ResCount) Equals(other *ResCount) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if p.Data != other.Data { return false }
  return true
}

func (p *ResCount) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResCount(%+v)", *p)
}

func (p *ResCount) Validate() error {
  return nil
}
// 返回结构体
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后3位为0为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
//  - Data
type ResMsg struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data *string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResMsg() *ResMsg {
  return &ResMsg{}
}


func (p *ResMsg) GetResponseCode() string {
  return p.ResponseCode
}
var ResMsg_ResponseDesp_DEFAULT string
func (p *ResMsg) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResMsg_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var ResMsg_Data_DEFAULT string
func (p *ResMsg) GetData() string {
  if !p.IsSetData() {
    return ResMsg_Data_DEFAULT
  }
return *p.Data
}
func (p *ResMsg) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  return nil
}

func (p *ResMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *ResMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *ResMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResMsg) Equals(other *ResMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResMsg(%+v)", *p)
}

func (p *ResMsg) Validate() error {
  return nil
}
// Attributes:
//  - RiskLevel
//  - IsBindMobileNum
type UserRiskVo struct {
  RiskLevel *int32 `thrift:"riskLevel,1" db:"riskLevel" json:"riskLevel,omitempty"`
  IsBindMobileNum *int32 `thrift:"isBindMobileNum,2" db:"isBindMobileNum" json:"isBindMobileNum,omitempty"`
}

func NewUserRiskVo() *UserRiskVo {
  return &UserRiskVo{}
}

var UserRiskVo_RiskLevel_DEFAULT int32
func (p *UserRiskVo) GetRiskLevel() int32 {
  if !p.IsSetRiskLevel() {
    return UserRiskVo_RiskLevel_DEFAULT
  }
return *p.RiskLevel
}
var UserRiskVo_IsBindMobileNum_DEFAULT int32
func (p *UserRiskVo) GetIsBindMobileNum() int32 {
  if !p.IsSetIsBindMobileNum() {
    return UserRiskVo_IsBindMobileNum_DEFAULT
  }
return *p.IsBindMobileNum
}
func (p *UserRiskVo) IsSetRiskLevel() bool {
  return p.RiskLevel != nil
}

func (p *UserRiskVo) IsSetIsBindMobileNum() bool {
  return p.IsBindMobileNum != nil
}

func (p *UserRiskVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserRiskVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.RiskLevel = &v
}
  return nil
}

func (p *UserRiskVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.IsBindMobileNum = &v
}
  return nil
}

func (p *UserRiskVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserRiskVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserRiskVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRiskLevel() {
    if err := oprot.WriteFieldBegin(ctx, "riskLevel", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:riskLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RiskLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.riskLevel (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:riskLevel: ", p), err) }
  }
  return err
}

func (p *UserRiskVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetIsBindMobileNum() {
    if err := oprot.WriteFieldBegin(ctx, "isBindMobileNum", thrift.I32, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:isBindMobileNum: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.IsBindMobileNum)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.isBindMobileNum (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:isBindMobileNum: ", p), err) }
  }
  return err
}

func (p *UserRiskVo) Equals(other *UserRiskVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.RiskLevel != other.RiskLevel {
    if p.RiskLevel == nil || other.RiskLevel == nil {
      return false
    }
    if (*p.RiskLevel) != (*other.RiskLevel) { return false }
  }
  if p.IsBindMobileNum != other.IsBindMobileNum {
    if p.IsBindMobileNum == nil || other.IsBindMobileNum == nil {
      return false
    }
    if (*p.IsBindMobileNum) != (*other.IsBindMobileNum) { return false }
  }
  return true
}

func (p *UserRiskVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserRiskVo(%+v)", *p)
}

func (p *UserRiskVo) Validate() error {
  return nil
}
// Attributes:
//  - OpenId
type GetLazyUserReq struct {
  OpenId string `thrift:"openId,1,required" db:"openId" json:"openId"`
}

func NewGetLazyUserReq() *GetLazyUserReq {
  return &GetLazyUserReq{}
}


func (p *GetLazyUserReq) GetOpenId() string {
  return p.OpenId
}
func (p *GetLazyUserReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetOpenId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetOpenId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetOpenId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OpenId is not set"));
  }
  return nil
}

func (p *GetLazyUserReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.OpenId = v
}
  return nil
}

func (p *GetLazyUserReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetLazyUserReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetLazyUserReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "openId", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:openId: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OpenId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.openId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:openId: ", p), err) }
  return err
}

func (p *GetLazyUserReq) Equals(other *GetLazyUserReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.OpenId != other.OpenId { return false }
  return true
}

func (p *GetLazyUserReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetLazyUserReq(%+v)", *p)
}

func (p *GetLazyUserReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type GetLazyUserResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *LazyUser `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewGetLazyUserResp() *GetLazyUserResp {
  return &GetLazyUserResp{}
}


func (p *GetLazyUserResp) GetCode() int32 {
  return p.Code
}

func (p *GetLazyUserResp) GetMsg() string {
  return p.Msg
}
var GetLazyUserResp_Data_DEFAULT *LazyUser
func (p *GetLazyUserResp) GetData() *LazyUser {
  if !p.IsSetData() {
    return GetLazyUserResp_Data_DEFAULT
  }
return p.Data
}
func (p *GetLazyUserResp) IsSetData() bool {
  return p.Data != nil
}

func (p *GetLazyUserResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *GetLazyUserResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *GetLazyUserResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *GetLazyUserResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &LazyUser{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *GetLazyUserResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetLazyUserResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetLazyUserResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *GetLazyUserResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *GetLazyUserResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *GetLazyUserResp) Equals(other *GetLazyUserResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *GetLazyUserResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetLazyUserResp(%+v)", *p)
}

func (p *GetLazyUserResp) Validate() error {
  return nil
}
// Attributes:
//  - OpenId
//  - Nickname
//  - HeadCover
//  - Sex
//  - Birthday
//  - CreateTime
type LazyUser struct {
  OpenId string `thrift:"openId,1,required" db:"openId" json:"openId"`
  Nickname string `thrift:"nickname,2,required" db:"nickname" json:"nickname"`
  HeadCover *string `thrift:"headCover,3" db:"headCover" json:"headCover,omitempty"`
  Sex *int32 `thrift:"sex,4" db:"sex" json:"sex,omitempty"`
  Birthday *int64 `thrift:"birthday,5" db:"birthday" json:"birthday,omitempty"`
  CreateTime *int64 `thrift:"createTime,6" db:"createTime" json:"createTime,omitempty"`
}

func NewLazyUser() *LazyUser {
  return &LazyUser{}
}


func (p *LazyUser) GetOpenId() string {
  return p.OpenId
}

func (p *LazyUser) GetNickname() string {
  return p.Nickname
}
var LazyUser_HeadCover_DEFAULT string
func (p *LazyUser) GetHeadCover() string {
  if !p.IsSetHeadCover() {
    return LazyUser_HeadCover_DEFAULT
  }
return *p.HeadCover
}
var LazyUser_Sex_DEFAULT int32
func (p *LazyUser) GetSex() int32 {
  if !p.IsSetSex() {
    return LazyUser_Sex_DEFAULT
  }
return *p.Sex
}
var LazyUser_Birthday_DEFAULT int64
func (p *LazyUser) GetBirthday() int64 {
  if !p.IsSetBirthday() {
    return LazyUser_Birthday_DEFAULT
  }
return *p.Birthday
}
var LazyUser_CreateTime_DEFAULT int64
func (p *LazyUser) GetCreateTime() int64 {
  if !p.IsSetCreateTime() {
    return LazyUser_CreateTime_DEFAULT
  }
return *p.CreateTime
}
func (p *LazyUser) IsSetHeadCover() bool {
  return p.HeadCover != nil
}

func (p *LazyUser) IsSetSex() bool {
  return p.Sex != nil
}

func (p *LazyUser) IsSetBirthday() bool {
  return p.Birthday != nil
}

func (p *LazyUser) IsSetCreateTime() bool {
  return p.CreateTime != nil
}

func (p *LazyUser) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetOpenId bool = false;
  var issetNickname bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetOpenId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetNickname = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetOpenId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OpenId is not set"));
  }
  if !issetNickname{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Nickname is not set"));
  }
  return nil
}

func (p *LazyUser)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.OpenId = v
}
  return nil
}

func (p *LazyUser)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Nickname = v
}
  return nil
}

func (p *LazyUser)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.HeadCover = &v
}
  return nil
}

func (p *LazyUser)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *LazyUser)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Birthday = &v
}
  return nil
}

func (p *LazyUser)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.CreateTime = &v
}
  return nil
}

func (p *LazyUser) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "LazyUser"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *LazyUser) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "openId", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:openId: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OpenId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.openId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:openId: ", p), err) }
  return err
}

func (p *LazyUser) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "nickname", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:nickname: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Nickname)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.nickname (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:nickname: ", p), err) }
  return err
}

func (p *LazyUser) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeadCover() {
    if err := oprot.WriteFieldBegin(ctx, "headCover", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:headCover: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.HeadCover)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.headCover (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:headCover: ", p), err) }
  }
  return err
}

func (p *LazyUser) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:sex: ", p), err) }
  }
  return err
}

func (p *LazyUser) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBirthday() {
    if err := oprot.WriteFieldBegin(ctx, "birthday", thrift.I64, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:birthday: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.Birthday)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.birthday (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:birthday: ", p), err) }
  }
  return err
}

func (p *LazyUser) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCreateTime() {
    if err := oprot.WriteFieldBegin(ctx, "createTime", thrift.I64, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:createTime: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.CreateTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.createTime (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:createTime: ", p), err) }
  }
  return err
}

func (p *LazyUser) Equals(other *LazyUser) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.OpenId != other.OpenId { return false }
  if p.Nickname != other.Nickname { return false }
  if p.HeadCover != other.HeadCover {
    if p.HeadCover == nil || other.HeadCover == nil {
      return false
    }
    if (*p.HeadCover) != (*other.HeadCover) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.Birthday != other.Birthday {
    if p.Birthday == nil || other.Birthday == nil {
      return false
    }
    if (*p.Birthday) != (*other.Birthday) { return false }
  }
  if p.CreateTime != other.CreateTime {
    if p.CreateTime == nil || other.CreateTime == nil {
      return false
    }
    if (*p.CreateTime) != (*other.CreateTime) { return false }
  }
  return true
}

func (p *LazyUser) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("LazyUser(%+v)", *p)
}

func (p *LazyUser) Validate() error {
  return nil
}
// 返回结构体
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后1位为0为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
//  - Data
type MResMsg struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data *UserRiskVo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewMResMsg() *MResMsg {
  return &MResMsg{}
}


func (p *MResMsg) GetResponseCode() string {
  return p.ResponseCode
}
var MResMsg_ResponseDesp_DEFAULT string
func (p *MResMsg) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return MResMsg_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var MResMsg_Data_DEFAULT *UserRiskVo
func (p *MResMsg) GetData() *UserRiskVo {
  if !p.IsSetData() {
    return MResMsg_Data_DEFAULT
  }
return p.Data
}
func (p *MResMsg) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *MResMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *MResMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  return nil
}

func (p *MResMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *MResMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *MResMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserRiskVo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *MResMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MResMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *MResMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *MResMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *MResMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *MResMsg) Equals(other *MResMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *MResMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("MResMsg(%+v)", *p)
}

func (p *MResMsg) Validate() error {
  return nil
}
// 获取单个User
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后3位为000为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
//  - Data:     * userVO实体
// *
type ResUser struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data *UserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResUser() *ResUser {
  return &ResUser{}
}


func (p *ResUser) GetResponseCode() string {
  return p.ResponseCode
}
var ResUser_ResponseDesp_DEFAULT string
func (p *ResUser) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResUser_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var ResUser_Data_DEFAULT *UserVO
func (p *ResUser) GetData() *UserVO {
  if !p.IsSetData() {
    return ResUser_Data_DEFAULT
  }
return p.Data
}
func (p *ResUser) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResUser) IsSetData() bool {
  return p.Data != nil
}

func (p *ResUser) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  return nil
}

func (p *ResUser)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *ResUser)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResUser)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserVO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *ResUser) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResUser"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResUser) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *ResUser) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResUser) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResUser) Equals(other *ResUser) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *ResUser) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResUser(%+v)", *p)
}

func (p *ResUser) Validate() error {
  return nil
}
// 获取多个User
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后3位为000为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
//  - Data:     * userVO实体
// *
//  - Total:     * 总数量
// *
type ResMutiUser struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data []*UserVO `thrift:"data,3" db:"data" json:"data,omitempty"`
  Total *int64 `thrift:"total,4" db:"total" json:"total,omitempty"`
}

func NewResMutiUser() *ResMutiUser {
  return &ResMutiUser{}
}


func (p *ResMutiUser) GetResponseCode() string {
  return p.ResponseCode
}
var ResMutiUser_ResponseDesp_DEFAULT string
func (p *ResMutiUser) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResMutiUser_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var ResMutiUser_Data_DEFAULT []*UserVO

func (p *ResMutiUser) GetData() []*UserVO {
  return p.Data
}
var ResMutiUser_Total_DEFAULT int64
func (p *ResMutiUser) GetTotal() int64 {
  if !p.IsSetTotal() {
    return ResMutiUser_Total_DEFAULT
  }
return *p.Total
}
func (p *ResMutiUser) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResMutiUser) IsSetData() bool {
  return p.Data != nil
}

func (p *ResMutiUser) IsSetTotal() bool {
  return p.Total != nil
}

func (p *ResMutiUser) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  return nil
}

func (p *ResMutiUser)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *ResMutiUser)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResMutiUser)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*UserVO, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem0 := &UserVO{}
    if err := _elem0.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
    }
    p.Data = append(p.Data, _elem0)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ResMutiUser)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Total = &v
}
  return nil
}

func (p *ResMutiUser) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResMutiUser"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResMutiUser) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *ResMutiUser) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResMutiUser) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResMutiUser) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTotal() {
    if err := oprot.WriteFieldBegin(ctx, "total", thrift.I64, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:total: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.Total)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.total (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:total: ", p), err) }
  }
  return err
}

func (p *ResMutiUser) Equals(other *ResMutiUser) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src1 := other.Data[i]
    if !_tgt.Equals(_src1) { return false }
  }
  if p.Total != other.Total {
    if p.Total == nil || other.Total == nil {
      return false
    }
    if (*p.Total) != (*other.Total) { return false }
  }
  return true
}

func (p *ResMutiUser) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResMutiUser(%+v)", *p)
}

func (p *ResMutiUser) Validate() error {
  return nil
}
// 入参
// 
// 
// Attributes:
//  - KugouIds:     * kugouIds
// *
type ParmKugouIds struct {
  KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
}

func NewParmKugouIds() *ParmKugouIds {
  return &ParmKugouIds{}
}


func (p *ParmKugouIds) GetKugouIds() []int64 {
  return p.KugouIds
}
func (p *ParmKugouIds) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouIds bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouIds = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouIds{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"));
  }
  return nil
}

func (p *ParmKugouIds)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.KugouIds =  tSlice
  for i := 0; i < size; i ++ {
var _elem2 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem2 = v
}
    p.KugouIds = append(p.KugouIds, _elem2)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ParmKugouIds) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ParmKugouIds"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ParmKugouIds) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.KugouIds {
    if err := oprot.WriteI64(ctx, int64(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err) }
  return err
}

func (p *ParmKugouIds) Equals(other *ParmKugouIds) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.KugouIds) != len(other.KugouIds) { return false }
  for i, _tgt := range p.KugouIds {
    _src3 := other.KugouIds[i]
    if _tgt != _src3 { return false }
  }
  return true
}

func (p *ParmKugouIds) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ParmKugouIds(%+v)", *p)
}

func (p *ParmKugouIds) Validate() error {
  return nil
}
// 入参
// 
// 
// Attributes:
//  - KugouId:     * kugouId
// *
type ParmKugouId struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
}

func NewParmKugouId() *ParmKugouId {
  return &ParmKugouId{}
}


func (p *ParmKugouId) GetKugouId() int64 {
  return p.KugouId
}
func (p *ParmKugouId) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  return nil
}

func (p *ParmKugouId)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *ParmKugouId) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ParmKugouId"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ParmKugouId) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *ParmKugouId) Equals(other *ParmKugouId) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  return true
}

func (p *ParmKugouId) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ParmKugouId(%+v)", *p)
}

func (p *ParmKugouId) Validate() error {
  return nil
}
// 用于返回Integer业务
//  
// 
// Attributes:
//  - ResponseCode
//  - ResponseDesp
//  - Data
type ResIntegerMsg struct {
  ResponseCode *string `thrift:"responseCode,1" db:"responseCode" json:"responseCode,omitempty"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data *int32 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResIntegerMsg() *ResIntegerMsg {
  return &ResIntegerMsg{}
}

var ResIntegerMsg_ResponseCode_DEFAULT string
func (p *ResIntegerMsg) GetResponseCode() string {
  if !p.IsSetResponseCode() {
    return ResIntegerMsg_ResponseCode_DEFAULT
  }
return *p.ResponseCode
}
var ResIntegerMsg_ResponseDesp_DEFAULT string
func (p *ResIntegerMsg) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResIntegerMsg_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var ResIntegerMsg_Data_DEFAULT int32
func (p *ResIntegerMsg) GetData() int32 {
  if !p.IsSetData() {
    return ResIntegerMsg_Data_DEFAULT
  }
return *p.Data
}
func (p *ResIntegerMsg) IsSetResponseCode() bool {
  return p.ResponseCode != nil
}

func (p *ResIntegerMsg) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResIntegerMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResIntegerMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResIntegerMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = &v
}
  return nil
}

func (p *ResIntegerMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResIntegerMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResIntegerMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResIntegerMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResIntegerMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseCode() {
    if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseCode)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  }
  return err
}

func (p *ResIntegerMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResIntegerMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResIntegerMsg) Equals(other *ResIntegerMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode {
    if p.ResponseCode == nil || other.ResponseCode == nil {
      return false
    }
    if (*p.ResponseCode) != (*other.ResponseCode) { return false }
  }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResIntegerMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResIntegerMsg(%+v)", *p)
}

func (p *ResIntegerMsg) Validate() error {
  return nil
}
// Attributes:
//  - ResponseCode
//  - ResponseDesp
//  - Data
type ResLongListMsg struct {
  ResponseCode *string `thrift:"responseCode,1" db:"responseCode" json:"responseCode,omitempty"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data []int64 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResLongListMsg() *ResLongListMsg {
  return &ResLongListMsg{}
}

var ResLongListMsg_ResponseCode_DEFAULT string
func (p *ResLongListMsg) GetResponseCode() string {
  if !p.IsSetResponseCode() {
    return ResLongListMsg_ResponseCode_DEFAULT
  }
return *p.ResponseCode
}
var ResLongListMsg_ResponseDesp_DEFAULT string
func (p *ResLongListMsg) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResLongListMsg_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var ResLongListMsg_Data_DEFAULT []int64

func (p *ResLongListMsg) GetData() []int64 {
  return p.Data
}
func (p *ResLongListMsg) IsSetResponseCode() bool {
  return p.ResponseCode != nil
}

func (p *ResLongListMsg) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResLongListMsg) IsSetData() bool {
  return p.Data != nil
}

func (p *ResLongListMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ResLongListMsg)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = &v
}
  return nil
}

func (p *ResLongListMsg)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResLongListMsg)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
var _elem4 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem4 = v
}
    p.Data = append(p.Data, _elem4)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ResLongListMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResLongListMsg"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResLongListMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseCode() {
    if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseCode)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  }
  return err
}

func (p *ResLongListMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResLongListMsg) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResLongListMsg) Equals(other *ResLongListMsg) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode {
    if p.ResponseCode == nil || other.ResponseCode == nil {
      return false
    }
    if (*p.ResponseCode) != (*other.ResponseCode) { return false }
  }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src5 := other.Data[i]
    if _tgt != _src5 { return false }
  }
  return true
}

func (p *ResLongListMsg) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResLongListMsg(%+v)", *p)
}

func (p *ResLongListMsg) Validate() error {
  return nil
}
// 动态查询用户信息实体
// 
// 
// Attributes:
//  - Email:     * email
// *
//  - UserName:     * 用户名
// *
//  - NickName:     * 昵称
// *
//  - UserLogo:     * 用户logo
// *
//  - Status:     * 状态(0:正常，1:封号)
// *
//  - Sex:     * 性别(0:保密,1:男,2:女)
// *
//  - FromType: 来源(0:官网,1:酷狗,2:qq,3:新浪,4:人人,25:天翼)
// 
//  - AddTime:     * 注册时间
// *
//  - RegIp:     * 注册ip
// *
//  - Constellation:     * 星座(0:保密,1:白羊座,2:金牛座,3:双子座,4:巨蟹座,5:狮子座,6:处女座,7:天秤座,8:天蝎座,9:射手座,10:摩羯座,11:水瓶座,12:双鱼座)
// *
//  - Height:     * 身高[厘米]
// *
//  - Weight:     * 体重[公斤]
// *
//  - Weibo:     * 微博
// *
//  - Location: 所在地
// 
//  - Bwh: 三围
// 
//  - RichLevel: 财富等级
// 
//  - StarLevel: 明星等级
// 
//  - ProductLineId: 产品线 0:繁星（系统上线前数据）,1:繁星, 2:KuGou Live, 3:移动直播
// 
type DynamicResponseUserVO struct {
  Email *string `thrift:"email,1" db:"email" json:"email,omitempty"`
  UserName *string `thrift:"userName,2" db:"userName" json:"userName,omitempty"`
  NickName *string `thrift:"nickName,3" db:"nickName" json:"nickName,omitempty"`
  UserLogo *string `thrift:"userLogo,4" db:"userLogo" json:"userLogo,omitempty"`
  Status *int32 `thrift:"status,5" db:"status" json:"status,omitempty"`
  Sex *int32 `thrift:"sex,6" db:"sex" json:"sex,omitempty"`
  FromType *int32 `thrift:"fromType,7" db:"fromType" json:"fromType,omitempty"`
  AddTime *int32 `thrift:"addTime,8" db:"addTime" json:"addTime,omitempty"`
  RegIp *string `thrift:"regIp,9" db:"regIp" json:"regIp,omitempty"`
  Constellation *int32 `thrift:"constellation,10" db:"constellation" json:"constellation,omitempty"`
  Height *int32 `thrift:"height,11" db:"height" json:"height,omitempty"`
  Weight *int32 `thrift:"weight,12" db:"weight" json:"weight,omitempty"`
  Weibo *string `thrift:"weibo,13" db:"weibo" json:"weibo,omitempty"`
  Location *string `thrift:"location,14" db:"location" json:"location,omitempty"`
  Bwh *string `thrift:"bwh,15" db:"bwh" json:"bwh,omitempty"`
  RichLevel *int32 `thrift:"richLevel,16" db:"richLevel" json:"richLevel,omitempty"`
  StarLevel *int32 `thrift:"starLevel,17" db:"starLevel" json:"starLevel,omitempty"`
  ProductLineId *int32 `thrift:"productLineId,18" db:"productLineId" json:"productLineId,omitempty"`
}

func NewDynamicResponseUserVO() *DynamicResponseUserVO {
  return &DynamicResponseUserVO{}
}

var DynamicResponseUserVO_Email_DEFAULT string
func (p *DynamicResponseUserVO) GetEmail() string {
  if !p.IsSetEmail() {
    return DynamicResponseUserVO_Email_DEFAULT
  }
return *p.Email
}
var DynamicResponseUserVO_UserName_DEFAULT string
func (p *DynamicResponseUserVO) GetUserName() string {
  if !p.IsSetUserName() {
    return DynamicResponseUserVO_UserName_DEFAULT
  }
return *p.UserName
}
var DynamicResponseUserVO_NickName_DEFAULT string
func (p *DynamicResponseUserVO) GetNickName() string {
  if !p.IsSetNickName() {
    return DynamicResponseUserVO_NickName_DEFAULT
  }
return *p.NickName
}
var DynamicResponseUserVO_UserLogo_DEFAULT string
func (p *DynamicResponseUserVO) GetUserLogo() string {
  if !p.IsSetUserLogo() {
    return DynamicResponseUserVO_UserLogo_DEFAULT
  }
return *p.UserLogo
}
var DynamicResponseUserVO_Status_DEFAULT int32
func (p *DynamicResponseUserVO) GetStatus() int32 {
  if !p.IsSetStatus() {
    return DynamicResponseUserVO_Status_DEFAULT
  }
return *p.Status
}
var DynamicResponseUserVO_Sex_DEFAULT int32
func (p *DynamicResponseUserVO) GetSex() int32 {
  if !p.IsSetSex() {
    return DynamicResponseUserVO_Sex_DEFAULT
  }
return *p.Sex
}
var DynamicResponseUserVO_FromType_DEFAULT int32
func (p *DynamicResponseUserVO) GetFromType() int32 {
  if !p.IsSetFromType() {
    return DynamicResponseUserVO_FromType_DEFAULT
  }
return *p.FromType
}
var DynamicResponseUserVO_AddTime_DEFAULT int32
func (p *DynamicResponseUserVO) GetAddTime() int32 {
  if !p.IsSetAddTime() {
    return DynamicResponseUserVO_AddTime_DEFAULT
  }
return *p.AddTime
}
var DynamicResponseUserVO_RegIp_DEFAULT string
func (p *DynamicResponseUserVO) GetRegIp() string {
  if !p.IsSetRegIp() {
    return DynamicResponseUserVO_RegIp_DEFAULT
  }
return *p.RegIp
}
var DynamicResponseUserVO_Constellation_DEFAULT int32
func (p *DynamicResponseUserVO) GetConstellation() int32 {
  if !p.IsSetConstellation() {
    return DynamicResponseUserVO_Constellation_DEFAULT
  }
return *p.Constellation
}
var DynamicResponseUserVO_Height_DEFAULT int32
func (p *DynamicResponseUserVO) GetHeight() int32 {
  if !p.IsSetHeight() {
    return DynamicResponseUserVO_Height_DEFAULT
  }
return *p.Height
}
var DynamicResponseUserVO_Weight_DEFAULT int32
func (p *DynamicResponseUserVO) GetWeight() int32 {
  if !p.IsSetWeight() {
    return DynamicResponseUserVO_Weight_DEFAULT
  }
return *p.Weight
}
var DynamicResponseUserVO_Weibo_DEFAULT string
func (p *DynamicResponseUserVO) GetWeibo() string {
  if !p.IsSetWeibo() {
    return DynamicResponseUserVO_Weibo_DEFAULT
  }
return *p.Weibo
}
var DynamicResponseUserVO_Location_DEFAULT string
func (p *DynamicResponseUserVO) GetLocation() string {
  if !p.IsSetLocation() {
    return DynamicResponseUserVO_Location_DEFAULT
  }
return *p.Location
}
var DynamicResponseUserVO_Bwh_DEFAULT string
func (p *DynamicResponseUserVO) GetBwh() string {
  if !p.IsSetBwh() {
    return DynamicResponseUserVO_Bwh_DEFAULT
  }
return *p.Bwh
}
var DynamicResponseUserVO_RichLevel_DEFAULT int32
func (p *DynamicResponseUserVO) GetRichLevel() int32 {
  if !p.IsSetRichLevel() {
    return DynamicResponseUserVO_RichLevel_DEFAULT
  }
return *p.RichLevel
}
var DynamicResponseUserVO_StarLevel_DEFAULT int32
func (p *DynamicResponseUserVO) GetStarLevel() int32 {
  if !p.IsSetStarLevel() {
    return DynamicResponseUserVO_StarLevel_DEFAULT
  }
return *p.StarLevel
}
var DynamicResponseUserVO_ProductLineId_DEFAULT int32
func (p *DynamicResponseUserVO) GetProductLineId() int32 {
  if !p.IsSetProductLineId() {
    return DynamicResponseUserVO_ProductLineId_DEFAULT
  }
return *p.ProductLineId
}
func (p *DynamicResponseUserVO) IsSetEmail() bool {
  return p.Email != nil
}

func (p *DynamicResponseUserVO) IsSetUserName() bool {
  return p.UserName != nil
}

func (p *DynamicResponseUserVO) IsSetNickName() bool {
  return p.NickName != nil
}

func (p *DynamicResponseUserVO) IsSetUserLogo() bool {
  return p.UserLogo != nil
}

func (p *DynamicResponseUserVO) IsSetStatus() bool {
  return p.Status != nil
}

func (p *DynamicResponseUserVO) IsSetSex() bool {
  return p.Sex != nil
}

func (p *DynamicResponseUserVO) IsSetFromType() bool {
  return p.FromType != nil
}

func (p *DynamicResponseUserVO) IsSetAddTime() bool {
  return p.AddTime != nil
}

func (p *DynamicResponseUserVO) IsSetRegIp() bool {
  return p.RegIp != nil
}

func (p *DynamicResponseUserVO) IsSetConstellation() bool {
  return p.Constellation != nil
}

func (p *DynamicResponseUserVO) IsSetHeight() bool {
  return p.Height != nil
}

func (p *DynamicResponseUserVO) IsSetWeight() bool {
  return p.Weight != nil
}

func (p *DynamicResponseUserVO) IsSetWeibo() bool {
  return p.Weibo != nil
}

func (p *DynamicResponseUserVO) IsSetLocation() bool {
  return p.Location != nil
}

func (p *DynamicResponseUserVO) IsSetBwh() bool {
  return p.Bwh != nil
}

func (p *DynamicResponseUserVO) IsSetRichLevel() bool {
  return p.RichLevel != nil
}

func (p *DynamicResponseUserVO) IsSetStarLevel() bool {
  return p.StarLevel != nil
}

func (p *DynamicResponseUserVO) IsSetProductLineId() bool {
  return p.ProductLineId != nil
}

func (p *DynamicResponseUserVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 15:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField15(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 16:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField16(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 17:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField17(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 18:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField18(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DynamicResponseUserVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Email = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.UserName = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.NickName = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.UserLogo = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Status = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Sex = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.FromType = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.AddTime = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.RegIp = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.Constellation = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.Height = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.Weight = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.Weibo = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.Location = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 15: ", err)
} else {
  p.Bwh = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 16: ", err)
} else {
  p.RichLevel = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField17(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 17: ", err)
} else {
  p.StarLevel = &v
}
  return nil
}

func (p *DynamicResponseUserVO)  ReadField18(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 18: ", err)
} else {
  p.ProductLineId = &v
}
  return nil
}

func (p *DynamicResponseUserVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DynamicResponseUserVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
    if err := p.writeField15(ctx, oprot); err != nil { return err }
    if err := p.writeField16(ctx, oprot); err != nil { return err }
    if err := p.writeField17(ctx, oprot); err != nil { return err }
    if err := p.writeField18(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DynamicResponseUserVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetEmail() {
    if err := oprot.WriteFieldBegin(ctx, "email", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:email: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Email)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.email (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:email: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserName() {
    if err := oprot.WriteFieldBegin(ctx, "userName", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:userName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userName (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:userName: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNickName() {
    if err := oprot.WriteFieldBegin(ctx, "nickName", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:nickName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NickName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.nickName (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:nickName: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetUserLogo() {
    if err := oprot.WriteFieldBegin(ctx, "userLogo", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:userLogo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.UserLogo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.userLogo (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:userLogo: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStatus() {
    if err := oprot.WriteFieldBegin(ctx, "status", thrift.I32, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:status: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Status)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.status (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:status: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSex() {
    if err := oprot.WriteFieldBegin(ctx, "sex", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sex: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Sex)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sex (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sex: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFromType() {
    if err := oprot.WriteFieldBegin(ctx, "fromType", thrift.I32, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:fromType: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.FromType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.fromType (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:fromType: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAddTime() {
    if err := oprot.WriteFieldBegin(ctx, "addTime", thrift.I32, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:addTime: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AddTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.addTime (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:addTime: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRegIp() {
    if err := oprot.WriteFieldBegin(ctx, "regIp", thrift.STRING, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:regIp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.RegIp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.regIp (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:regIp: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetConstellation() {
    if err := oprot.WriteFieldBegin(ctx, "constellation", thrift.I32, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:constellation: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Constellation)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.constellation (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:constellation: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeight() {
    if err := oprot.WriteFieldBegin(ctx, "height", thrift.I32, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:height: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Height)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.height (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:height: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeight() {
    if err := oprot.WriteFieldBegin(ctx, "weight", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:weight: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Weight)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weight (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:weight: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWeibo() {
    if err := oprot.WriteFieldBegin(ctx, "weibo", thrift.STRING, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:weibo: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Weibo)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.weibo (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:weibo: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLocation() {
    if err := oprot.WriteFieldBegin(ctx, "location", thrift.STRING, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:location: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Location)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.location (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:location: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBwh() {
    if err := oprot.WriteFieldBegin(ctx, "bwh", thrift.STRING, 15); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:bwh: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Bwh)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.bwh (15) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 15:bwh: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRichLevel() {
    if err := oprot.WriteFieldBegin(ctx, "richLevel", thrift.I32, 16); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:richLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.RichLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.richLevel (16) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 16:richLevel: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField17(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStarLevel() {
    if err := oprot.WriteFieldBegin(ctx, "starLevel", thrift.I32, 17); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 17:starLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StarLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.starLevel (17) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 17:starLevel: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) writeField18(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetProductLineId() {
    if err := oprot.WriteFieldBegin(ctx, "productLineId", thrift.I32, 18); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 18:productLineId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.ProductLineId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.productLineId (18) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 18:productLineId: ", p), err) }
  }
  return err
}

func (p *DynamicResponseUserVO) Equals(other *DynamicResponseUserVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Email != other.Email {
    if p.Email == nil || other.Email == nil {
      return false
    }
    if (*p.Email) != (*other.Email) { return false }
  }
  if p.UserName != other.UserName {
    if p.UserName == nil || other.UserName == nil {
      return false
    }
    if (*p.UserName) != (*other.UserName) { return false }
  }
  if p.NickName != other.NickName {
    if p.NickName == nil || other.NickName == nil {
      return false
    }
    if (*p.NickName) != (*other.NickName) { return false }
  }
  if p.UserLogo != other.UserLogo {
    if p.UserLogo == nil || other.UserLogo == nil {
      return false
    }
    if (*p.UserLogo) != (*other.UserLogo) { return false }
  }
  if p.Status != other.Status {
    if p.Status == nil || other.Status == nil {
      return false
    }
    if (*p.Status) != (*other.Status) { return false }
  }
  if p.Sex != other.Sex {
    if p.Sex == nil || other.Sex == nil {
      return false
    }
    if (*p.Sex) != (*other.Sex) { return false }
  }
  if p.FromType != other.FromType {
    if p.FromType == nil || other.FromType == nil {
      return false
    }
    if (*p.FromType) != (*other.FromType) { return false }
  }
  if p.AddTime != other.AddTime {
    if p.AddTime == nil || other.AddTime == nil {
      return false
    }
    if (*p.AddTime) != (*other.AddTime) { return false }
  }
  if p.RegIp != other.RegIp {
    if p.RegIp == nil || other.RegIp == nil {
      return false
    }
    if (*p.RegIp) != (*other.RegIp) { return false }
  }
  if p.Constellation != other.Constellation {
    if p.Constellation == nil || other.Constellation == nil {
      return false
    }
    if (*p.Constellation) != (*other.Constellation) { return false }
  }
  if p.Height != other.Height {
    if p.Height == nil || other.Height == nil {
      return false
    }
    if (*p.Height) != (*other.Height) { return false }
  }
  if p.Weight != other.Weight {
    if p.Weight == nil || other.Weight == nil {
      return false
    }
    if (*p.Weight) != (*other.Weight) { return false }
  }
  if p.Weibo != other.Weibo {
    if p.Weibo == nil || other.Weibo == nil {
      return false
    }
    if (*p.Weibo) != (*other.Weibo) { return false }
  }
  if p.Location != other.Location {
    if p.Location == nil || other.Location == nil {
      return false
    }
    if (*p.Location) != (*other.Location) { return false }
  }
  if p.Bwh != other.Bwh {
    if p.Bwh == nil || other.Bwh == nil {
      return false
    }
    if (*p.Bwh) != (*other.Bwh) { return false }
  }
  if p.RichLevel != other.RichLevel {
    if p.RichLevel == nil || other.RichLevel == nil {
      return false
    }
    if (*p.RichLevel) != (*other.RichLevel) { return false }
  }
  if p.StarLevel != other.StarLevel {
    if p.StarLevel == nil || other.StarLevel == nil {
      return false
    }
    if (*p.StarLevel) != (*other.StarLevel) { return false }
  }
  if p.ProductLineId != other.ProductLineId {
    if p.ProductLineId == nil || other.ProductLineId == nil {
      return false
    }
    if (*p.ProductLineId) != (*other.ProductLineId) { return false }
  }
  return true
}

func (p *DynamicResponseUserVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DynamicResponseUserVO(%+v)", *p)
}

func (p *DynamicResponseUserVO) Validate() error {
  return nil
}
// 返回结构体
// 
// 
// Attributes:
//  - ResponseCode:     * 接口返回码 业务方只需要判断最后3位为0为成功即可
// *
//  - ResponseDesp:     * 接口返回描述
// *
//  - Data
type ResMobileResponse struct {
  ResponseCode string `thrift:"responseCode,1,required" db:"responseCode" json:"responseCode"`
  ResponseDesp *string `thrift:"responseDesp,2" db:"responseDesp" json:"responseDesp,omitempty"`
  Data *MobileResponseDTO `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResMobileResponse() *ResMobileResponse {
  return &ResMobileResponse{}
}


func (p *ResMobileResponse) GetResponseCode() string {
  return p.ResponseCode
}
var ResMobileResponse_ResponseDesp_DEFAULT string
func (p *ResMobileResponse) GetResponseDesp() string {
  if !p.IsSetResponseDesp() {
    return ResMobileResponse_ResponseDesp_DEFAULT
  }
return *p.ResponseDesp
}
var ResMobileResponse_Data_DEFAULT *MobileResponseDTO
func (p *ResMobileResponse) GetData() *MobileResponseDTO {
  if !p.IsSetData() {
    return ResMobileResponse_Data_DEFAULT
  }
return p.Data
}
func (p *ResMobileResponse) IsSetResponseDesp() bool {
  return p.ResponseDesp != nil
}

func (p *ResMobileResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResMobileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetResponseCode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetResponseCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetResponseCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ResponseCode is not set"));
  }
  return nil
}

func (p *ResMobileResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ResponseCode = v
}
  return nil
}

func (p *ResMobileResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ResponseDesp = &v
}
  return nil
}

func (p *ResMobileResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &MobileResponseDTO{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *ResMobileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResMobileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResMobileResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "responseCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:responseCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ResponseCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.responseCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:responseCode: ", p), err) }
  return err
}

func (p *ResMobileResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetResponseDesp() {
    if err := oprot.WriteFieldBegin(ctx, "responseDesp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:responseDesp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ResponseDesp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.responseDesp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:responseDesp: ", p), err) }
  }
  return err
}

func (p *ResMobileResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResMobileResponse) Equals(other *ResMobileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ResponseCode != other.ResponseCode { return false }
  if p.ResponseDesp != other.ResponseDesp {
    if p.ResponseDesp == nil || other.ResponseDesp == nil {
      return false
    }
    if (*p.ResponseDesp) != (*other.ResponseDesp) { return false }
  }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *ResMobileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResMobileResponse(%+v)", *p)
}

func (p *ResMobileResponse) Validate() error {
  return nil
}
// Attributes:
//  - MobileFingerprint
//  - MobileEncrypt
type MobileResponseDTO struct {
  MobileFingerprint *string `thrift:"mobileFingerprint,1" db:"mobileFingerprint" json:"mobileFingerprint,omitempty"`
  MobileEncrypt *string `thrift:"mobileEncrypt,2" db:"mobileEncrypt" json:"mobileEncrypt,omitempty"`
}

func NewMobileResponseDTO() *MobileResponseDTO {
  return &MobileResponseDTO{}
}

var MobileResponseDTO_MobileFingerprint_DEFAULT string
func (p *MobileResponseDTO) GetMobileFingerprint() string {
  if !p.IsSetMobileFingerprint() {
    return MobileResponseDTO_MobileFingerprint_DEFAULT
  }
return *p.MobileFingerprint
}
var MobileResponseDTO_MobileEncrypt_DEFAULT string
func (p *MobileResponseDTO) GetMobileEncrypt() string {
  if !p.IsSetMobileEncrypt() {
    return MobileResponseDTO_MobileEncrypt_DEFAULT
  }
return *p.MobileEncrypt
}
func (p *MobileResponseDTO) IsSetMobileFingerprint() bool {
  return p.MobileFingerprint != nil
}

func (p *MobileResponseDTO) IsSetMobileEncrypt() bool {
  return p.MobileEncrypt != nil
}

func (p *MobileResponseDTO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *MobileResponseDTO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.MobileFingerprint = &v
}
  return nil
}

func (p *MobileResponseDTO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.MobileEncrypt = &v
}
  return nil
}

func (p *MobileResponseDTO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MobileResponseDTO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *MobileResponseDTO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMobileFingerprint() {
    if err := oprot.WriteFieldBegin(ctx, "mobileFingerprint", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:mobileFingerprint: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MobileFingerprint)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mobileFingerprint (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:mobileFingerprint: ", p), err) }
  }
  return err
}

func (p *MobileResponseDTO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMobileEncrypt() {
    if err := oprot.WriteFieldBegin(ctx, "mobileEncrypt", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:mobileEncrypt: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MobileEncrypt)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mobileEncrypt (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:mobileEncrypt: ", p), err) }
  }
  return err
}

func (p *MobileResponseDTO) Equals(other *MobileResponseDTO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.MobileFingerprint != other.MobileFingerprint {
    if p.MobileFingerprint == nil || other.MobileFingerprint == nil {
      return false
    }
    if (*p.MobileFingerprint) != (*other.MobileFingerprint) { return false }
  }
  if p.MobileEncrypt != other.MobileEncrypt {
    if p.MobileEncrypt == nil || other.MobileEncrypt == nil {
      return false
    }
    if (*p.MobileEncrypt) != (*other.MobileEncrypt) { return false }
  }
  return true
}

func (p *MobileResponseDTO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("MobileResponseDTO(%+v)", *p)
}

func (p *MobileResponseDTO) Validate() error {
  return nil
}
// 用户验证码VO
// 
// 
// Attributes:
//  - KugouId
//  - ClientIp
type UserCaptchaVo struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  ClientIp *string `thrift:"clientIp,2" db:"clientIp" json:"clientIp,omitempty"`
}

func NewUserCaptchaVo() *UserCaptchaVo {
  return &UserCaptchaVo{}
}

var UserCaptchaVo_KugouId_DEFAULT int64
func (p *UserCaptchaVo) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return UserCaptchaVo_KugouId_DEFAULT
  }
return *p.KugouId
}
var UserCaptchaVo_ClientIp_DEFAULT string
func (p *UserCaptchaVo) GetClientIp() string {
  if !p.IsSetClientIp() {
    return UserCaptchaVo_ClientIp_DEFAULT
  }
return *p.ClientIp
}
func (p *UserCaptchaVo) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *UserCaptchaVo) IsSetClientIp() bool {
  return p.ClientIp != nil
}

func (p *UserCaptchaVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UserCaptchaVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *UserCaptchaVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ClientIp = &v
}
  return nil
}

func (p *UserCaptchaVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserCaptchaVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserCaptchaVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *UserCaptchaVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientIp() {
    if err := oprot.WriteFieldBegin(ctx, "clientIp", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:clientIp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ClientIp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientIp (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:clientIp: ", p), err) }
  }
  return err
}

func (p *UserCaptchaVo) Equals(other *UserCaptchaVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.ClientIp != other.ClientIp {
    if p.ClientIp == nil || other.ClientIp == nil {
      return false
    }
    if (*p.ClientIp) != (*other.ClientIp) { return false }
  }
  return true
}

func (p *UserCaptchaVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserCaptchaVo(%+v)", *p)
}

func (p *UserCaptchaVo) Validate() error {
  return nil
}
// 用户验证码返回结果
// 
// 
// Attributes:
//  - Code
//  - Msg
//  - Data
type ResUserCaptchaResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResUserCaptchaResponse() *ResUserCaptchaResponse {
  return &ResUserCaptchaResponse{}
}


func (p *ResUserCaptchaResponse) GetCode() int32 {
  return p.Code
}

func (p *ResUserCaptchaResponse) GetMsg() string {
  return p.Msg
}
var ResUserCaptchaResponse_Data_DEFAULT string
func (p *ResUserCaptchaResponse) GetData() string {
  if !p.IsSetData() {
    return ResUserCaptchaResponse_Data_DEFAULT
  }
return *p.Data
}
func (p *ResUserCaptchaResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResUserCaptchaResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *ResUserCaptchaResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ResUserCaptchaResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *ResUserCaptchaResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResUserCaptchaResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResUserCaptchaResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResUserCaptchaResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *ResUserCaptchaResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *ResUserCaptchaResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResUserCaptchaResponse) Equals(other *ResUserCaptchaResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResUserCaptchaResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResUserCaptchaResponse(%+v)", *p)
}

func (p *ResUserCaptchaResponse) Validate() error {
  return nil
}
// 用户验证码VO
// 
// 
// Attributes:
//  - KugouId
//  - CaptchaId
//  - CaptchaValue
type CheckUserCaptchaVo struct {
  KugouId *int64 `thrift:"kugouId,1" db:"kugouId" json:"kugouId,omitempty"`
  CaptchaId *string `thrift:"captchaId,2" db:"captchaId" json:"captchaId,omitempty"`
  CaptchaValue *string `thrift:"captchaValue,3" db:"captchaValue" json:"captchaValue,omitempty"`
}

func NewCheckUserCaptchaVo() *CheckUserCaptchaVo {
  return &CheckUserCaptchaVo{}
}

var CheckUserCaptchaVo_KugouId_DEFAULT int64
func (p *CheckUserCaptchaVo) GetKugouId() int64 {
  if !p.IsSetKugouId() {
    return CheckUserCaptchaVo_KugouId_DEFAULT
  }
return *p.KugouId
}
var CheckUserCaptchaVo_CaptchaId_DEFAULT string
func (p *CheckUserCaptchaVo) GetCaptchaId() string {
  if !p.IsSetCaptchaId() {
    return CheckUserCaptchaVo_CaptchaId_DEFAULT
  }
return *p.CaptchaId
}
var CheckUserCaptchaVo_CaptchaValue_DEFAULT string
func (p *CheckUserCaptchaVo) GetCaptchaValue() string {
  if !p.IsSetCaptchaValue() {
    return CheckUserCaptchaVo_CaptchaValue_DEFAULT
  }
return *p.CaptchaValue
}
func (p *CheckUserCaptchaVo) IsSetKugouId() bool {
  return p.KugouId != nil
}

func (p *CheckUserCaptchaVo) IsSetCaptchaId() bool {
  return p.CaptchaId != nil
}

func (p *CheckUserCaptchaVo) IsSetCaptchaValue() bool {
  return p.CaptchaValue != nil
}

func (p *CheckUserCaptchaVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CheckUserCaptchaVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = &v
}
  return nil
}

func (p *CheckUserCaptchaVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.CaptchaId = &v
}
  return nil
}

func (p *CheckUserCaptchaVo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.CaptchaValue = &v
}
  return nil
}

func (p *CheckUserCaptchaVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CheckUserCaptchaVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CheckUserCaptchaVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.KugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  }
  return err
}

func (p *CheckUserCaptchaVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCaptchaId() {
    if err := oprot.WriteFieldBegin(ctx, "captchaId", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:captchaId: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.CaptchaId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.captchaId (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:captchaId: ", p), err) }
  }
  return err
}

func (p *CheckUserCaptchaVo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCaptchaValue() {
    if err := oprot.WriteFieldBegin(ctx, "captchaValue", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:captchaValue: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.CaptchaValue)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.captchaValue (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:captchaValue: ", p), err) }
  }
  return err
}

func (p *CheckUserCaptchaVo) Equals(other *CheckUserCaptchaVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId {
    if p.KugouId == nil || other.KugouId == nil {
      return false
    }
    if (*p.KugouId) != (*other.KugouId) { return false }
  }
  if p.CaptchaId != other.CaptchaId {
    if p.CaptchaId == nil || other.CaptchaId == nil {
      return false
    }
    if (*p.CaptchaId) != (*other.CaptchaId) { return false }
  }
  if p.CaptchaValue != other.CaptchaValue {
    if p.CaptchaValue == nil || other.CaptchaValue == nil {
      return false
    }
    if (*p.CaptchaValue) != (*other.CaptchaValue) { return false }
  }
  return true
}

func (p *CheckUserCaptchaVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CheckUserCaptchaVo(%+v)", *p)
}

func (p *CheckUserCaptchaVo) Validate() error {
  return nil
}
// 用户验证码返回结果
// 
// 
// Attributes:
//  - Code
//  - Msg
//  - Data
type ResCheckUserCaptchaResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *bool `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResCheckUserCaptchaResponse() *ResCheckUserCaptchaResponse {
  return &ResCheckUserCaptchaResponse{}
}


func (p *ResCheckUserCaptchaResponse) GetCode() int32 {
  return p.Code
}

func (p *ResCheckUserCaptchaResponse) GetMsg() string {
  return p.Msg
}
var ResCheckUserCaptchaResponse_Data_DEFAULT bool
func (p *ResCheckUserCaptchaResponse) GetData() bool {
  if !p.IsSetData() {
    return ResCheckUserCaptchaResponse_Data_DEFAULT
  }
return *p.Data
}
func (p *ResCheckUserCaptchaResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResCheckUserCaptchaResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *ResCheckUserCaptchaResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ResCheckUserCaptchaResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *ResCheckUserCaptchaResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResCheckUserCaptchaResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResCheckUserCaptchaResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResCheckUserCaptchaResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *ResCheckUserCaptchaResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *ResCheckUserCaptchaResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteBool(ctx, bool(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResCheckUserCaptchaResponse) Equals(other *ResCheckUserCaptchaResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResCheckUserCaptchaResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResCheckUserCaptchaResponse(%+v)", *p)
}

func (p *ResCheckUserCaptchaResponse) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - AppId
//  - Code
//  - Phone
//  - ClientIp
//  - Token
type BindMobilePhoneVo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
  Code string `thrift:"code,3,required" db:"code" json:"code"`
  Phone string `thrift:"phone,4,required" db:"phone" json:"phone"`
  ClientIp string `thrift:"clientIp,5,required" db:"clientIp" json:"clientIp"`
  Token string `thrift:"token,6,required" db:"token" json:"token"`
}

func NewBindMobilePhoneVo() *BindMobilePhoneVo {
  return &BindMobilePhoneVo{}
}


func (p *BindMobilePhoneVo) GetKugouId() int64 {
  return p.KugouId
}

func (p *BindMobilePhoneVo) GetAppId() int32 {
  return p.AppId
}

func (p *BindMobilePhoneVo) GetCode() string {
  return p.Code
}

func (p *BindMobilePhoneVo) GetPhone() string {
  return p.Phone
}

func (p *BindMobilePhoneVo) GetClientIp() string {
  return p.ClientIp
}

func (p *BindMobilePhoneVo) GetToken() string {
  return p.Token
}
func (p *BindMobilePhoneVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetAppId bool = false;
  var issetCode bool = false;
  var issetPhone bool = false;
  var issetClientIp bool = false;
  var issetToken bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetPhone = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetClientIp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetToken = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetPhone{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Phone is not set"));
  }
  if !issetClientIp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ClientIp is not set"));
  }
  if !issetToken{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Token is not set"));
  }
  return nil
}

func (p *BindMobilePhoneVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *BindMobilePhoneVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *BindMobilePhoneVo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *BindMobilePhoneVo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Phone = v
}
  return nil
}

func (p *BindMobilePhoneVo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.ClientIp = v
}
  return nil
}

func (p *BindMobilePhoneVo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Token = v
}
  return nil
}

func (p *BindMobilePhoneVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BindMobilePhoneVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BindMobilePhoneVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *BindMobilePhoneVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *BindMobilePhoneVo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:code: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:code: ", p), err) }
  return err
}

func (p *BindMobilePhoneVo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "phone", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:phone: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Phone)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.phone (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:phone: ", p), err) }
  return err
}

func (p *BindMobilePhoneVo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "clientIp", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:clientIp: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ClientIp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.clientIp (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:clientIp: ", p), err) }
  return err
}

func (p *BindMobilePhoneVo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:token: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Token)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.token (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:token: ", p), err) }
  return err
}

func (p *BindMobilePhoneVo) Equals(other *BindMobilePhoneVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.AppId != other.AppId { return false }
  if p.Code != other.Code { return false }
  if p.Phone != other.Phone { return false }
  if p.ClientIp != other.ClientIp { return false }
  if p.Token != other.Token { return false }
  return true
}

func (p *BindMobilePhoneVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BindMobilePhoneVo(%+v)", *p)
}

func (p *BindMobilePhoneVo) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type ResBindMobilePhoneResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *bool `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResBindMobilePhoneResponse() *ResBindMobilePhoneResponse {
  return &ResBindMobilePhoneResponse{}
}


func (p *ResBindMobilePhoneResponse) GetCode() int32 {
  return p.Code
}

func (p *ResBindMobilePhoneResponse) GetMsg() string {
  return p.Msg
}
var ResBindMobilePhoneResponse_Data_DEFAULT bool
func (p *ResBindMobilePhoneResponse) GetData() bool {
  if !p.IsSetData() {
    return ResBindMobilePhoneResponse_Data_DEFAULT
  }
return *p.Data
}
func (p *ResBindMobilePhoneResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResBindMobilePhoneResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *ResBindMobilePhoneResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ResBindMobilePhoneResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *ResBindMobilePhoneResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResBindMobilePhoneResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResBindMobilePhoneResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResBindMobilePhoneResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *ResBindMobilePhoneResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *ResBindMobilePhoneResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteBool(ctx, bool(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResBindMobilePhoneResponse) Equals(other *ResBindMobilePhoneResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResBindMobilePhoneResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResBindMobilePhoneResponse(%+v)", *p)
}

func (p *ResBindMobilePhoneResponse) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - AppId
//  - Code
//  - Phone
//  - ClientIp
//  - Token
type CheckBindMobilePhoneVo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
  Code string `thrift:"code,3,required" db:"code" json:"code"`
  Phone string `thrift:"phone,4,required" db:"phone" json:"phone"`
  ClientIp string `thrift:"clientIp,5,required" db:"clientIp" json:"clientIp"`
  Token string `thrift:"token,6,required" db:"token" json:"token"`
}

func NewCheckBindMobilePhoneVo() *CheckBindMobilePhoneVo {
  return &CheckBindMobilePhoneVo{}
}


func (p *CheckBindMobilePhoneVo) GetKugouId() int64 {
  return p.KugouId
}

func (p *CheckBindMobilePhoneVo) GetAppId() int32 {
  return p.AppId
}

func (p *CheckBindMobilePhoneVo) GetCode() string {
  return p.Code
}

func (p *CheckBindMobilePhoneVo) GetPhone() string {
  return p.Phone
}

func (p *CheckBindMobilePhoneVo) GetClientIp() string {
  return p.ClientIp
}

func (p *CheckBindMobilePhoneVo) GetToken() string {
  return p.Token
}
func (p *CheckBindMobilePhoneVo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetAppId bool = false;
  var issetCode bool = false;
  var issetPhone bool = false;
  var issetClientIp bool = false;
  var issetToken bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetPhone = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetClientIp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetToken = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetPhone{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Phone is not set"));
  }
  if !issetClientIp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ClientIp is not set"));
  }
  if !issetToken{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Token is not set"));
  }
  return nil
}

func (p *CheckBindMobilePhoneVo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *CheckBindMobilePhoneVo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *CheckBindMobilePhoneVo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *CheckBindMobilePhoneVo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Phone = v
}
  return nil
}

func (p *CheckBindMobilePhoneVo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.ClientIp = v
}
  return nil
}

func (p *CheckBindMobilePhoneVo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Token = v
}
  return nil
}

func (p *CheckBindMobilePhoneVo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CheckBindMobilePhoneVo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CheckBindMobilePhoneVo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *CheckBindMobilePhoneVo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *CheckBindMobilePhoneVo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:code: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:code: ", p), err) }
  return err
}

func (p *CheckBindMobilePhoneVo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "phone", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:phone: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Phone)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.phone (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:phone: ", p), err) }
  return err
}

func (p *CheckBindMobilePhoneVo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "clientIp", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:clientIp: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ClientIp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.clientIp (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:clientIp: ", p), err) }
  return err
}

func (p *CheckBindMobilePhoneVo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:token: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Token)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.token (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:token: ", p), err) }
  return err
}

func (p *CheckBindMobilePhoneVo) Equals(other *CheckBindMobilePhoneVo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.AppId != other.AppId { return false }
  if p.Code != other.Code { return false }
  if p.Phone != other.Phone { return false }
  if p.ClientIp != other.ClientIp { return false }
  if p.Token != other.Token { return false }
  return true
}

func (p *CheckBindMobilePhoneVo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CheckBindMobilePhoneVo(%+v)", *p)
}

func (p *CheckBindMobilePhoneVo) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type ResCheckBindMobilePhoneResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *bool `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResCheckBindMobilePhoneResponse() *ResCheckBindMobilePhoneResponse {
  return &ResCheckBindMobilePhoneResponse{}
}


func (p *ResCheckBindMobilePhoneResponse) GetCode() int32 {
  return p.Code
}

func (p *ResCheckBindMobilePhoneResponse) GetMsg() string {
  return p.Msg
}
var ResCheckBindMobilePhoneResponse_Data_DEFAULT bool
func (p *ResCheckBindMobilePhoneResponse) GetData() bool {
  if !p.IsSetData() {
    return ResCheckBindMobilePhoneResponse_Data_DEFAULT
  }
return *p.Data
}
func (p *ResCheckBindMobilePhoneResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResCheckBindMobilePhoneResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *ResCheckBindMobilePhoneResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ResCheckBindMobilePhoneResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *ResCheckBindMobilePhoneResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResCheckBindMobilePhoneResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResCheckBindMobilePhoneResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResCheckBindMobilePhoneResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *ResCheckBindMobilePhoneResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *ResCheckBindMobilePhoneResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteBool(ctx, bool(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResCheckBindMobilePhoneResponse) Equals(other *ResCheckBindMobilePhoneResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResCheckBindMobilePhoneResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResCheckBindMobilePhoneResponse(%+v)", *p)
}

func (p *ResCheckBindMobilePhoneResponse) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type ResUserCancelStatusResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *bool `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResUserCancelStatusResponse() *ResUserCancelStatusResponse {
  return &ResUserCancelStatusResponse{}
}


func (p *ResUserCancelStatusResponse) GetCode() int32 {
  return p.Code
}

func (p *ResUserCancelStatusResponse) GetMsg() string {
  return p.Msg
}
var ResUserCancelStatusResponse_Data_DEFAULT bool
func (p *ResUserCancelStatusResponse) GetData() bool {
  if !p.IsSetData() {
    return ResUserCancelStatusResponse_Data_DEFAULT
  }
return *p.Data
}
func (p *ResUserCancelStatusResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResUserCancelStatusResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *ResUserCancelStatusResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ResUserCancelStatusResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *ResUserCancelStatusResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *ResUserCancelStatusResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResUserCancelStatusResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResUserCancelStatusResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *ResUserCancelStatusResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *ResUserCancelStatusResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteBool(ctx, bool(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResUserCancelStatusResponse) Equals(other *ResUserCancelStatusResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *ResUserCancelStatusResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResUserCancelStatusResponse(%+v)", *p)
}

func (p *ResUserCancelStatusResponse) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type ResUserBirthDayResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data map[int64]string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewResUserBirthDayResponse() *ResUserBirthDayResponse {
  return &ResUserBirthDayResponse{}
}


func (p *ResUserBirthDayResponse) GetCode() int32 {
  return p.Code
}

func (p *ResUserBirthDayResponse) GetMsg() string {
  return p.Msg
}
var ResUserBirthDayResponse_Data_DEFAULT map[int64]string

func (p *ResUserBirthDayResponse) GetData() map[int64]string {
  return p.Data
}
func (p *ResUserBirthDayResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *ResUserBirthDayResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *ResUserBirthDayResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ResUserBirthDayResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *ResUserBirthDayResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[int64]string, size)
  p.Data =  tMap
  for i := 0; i < size; i ++ {
var _key6 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key6 = v
}
var _val7 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val7 = v
}
    p.Data[_key6] = _val7
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *ResUserBirthDayResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResUserBirthDayResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResUserBirthDayResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *ResUserBirthDayResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *ResUserBirthDayResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteMapBegin(ctx, thrift.I64, thrift.STRING, len(p.Data)); err != nil {
      return thrift.PrependError("error writing map begin: ", err)
    }
    for k, v := range p.Data {
      if err := oprot.WriteI64(ctx, int64(k)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteMapEnd(ctx); err != nil {
      return thrift.PrependError("error writing map end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResUserBirthDayResponse) Equals(other *ResUserBirthDayResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if len(p.Data) != len(other.Data) { return false }
  for k, _tgt := range p.Data {
    _src8 := other.Data[k]
    if _tgt != _src8 { return false }
  }
  return true
}

func (p *ResUserBirthDayResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResUserBirthDayResponse(%+v)", *p)
}

func (p *ResUserBirthDayResponse) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - AppId
//  - Channel
//  - ClientIp
//  - Msg
//  - Sign
type SendSmsByKugouIdReq struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
  Channel string `thrift:"channel,3,required" db:"channel" json:"channel"`
  ClientIp string `thrift:"clientIp,4,required" db:"clientIp" json:"clientIp"`
  Msg string `thrift:"msg,5,required" db:"msg" json:"msg"`
  Sign string `thrift:"sign,6,required" db:"sign" json:"sign"`
}

func NewSendSmsByKugouIdReq() *SendSmsByKugouIdReq {
  return &SendSmsByKugouIdReq{}
}


func (p *SendSmsByKugouIdReq) GetKugouId() int64 {
  return p.KugouId
}

func (p *SendSmsByKugouIdReq) GetAppId() int32 {
  return p.AppId
}

func (p *SendSmsByKugouIdReq) GetChannel() string {
  return p.Channel
}

func (p *SendSmsByKugouIdReq) GetClientIp() string {
  return p.ClientIp
}

func (p *SendSmsByKugouIdReq) GetMsg() string {
  return p.Msg
}

func (p *SendSmsByKugouIdReq) GetSign() string {
  return p.Sign
}
func (p *SendSmsByKugouIdReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetAppId bool = false;
  var issetChannel bool = false;
  var issetClientIp bool = false;
  var issetMsg bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetChannel = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetClientIp = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetChannel{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Channel is not set"));
  }
  if !issetClientIp{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ClientIp is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *SendSmsByKugouIdReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *SendSmsByKugouIdReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *SendSmsByKugouIdReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Channel = v
}
  return nil
}

func (p *SendSmsByKugouIdReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.ClientIp = v
}
  return nil
}

func (p *SendSmsByKugouIdReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *SendSmsByKugouIdReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *SendSmsByKugouIdReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendSmsByKugouIdReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendSmsByKugouIdReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "channel", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:channel: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Channel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.channel (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:channel: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "clientIp", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:clientIp: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ClientIp)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.clientIp (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:clientIp: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msg: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sign: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdReq) Equals(other *SendSmsByKugouIdReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.AppId != other.AppId { return false }
  if p.Channel != other.Channel { return false }
  if p.ClientIp != other.ClientIp { return false }
  if p.Msg != other.Msg { return false }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *SendSmsByKugouIdReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendSmsByKugouIdReq(%+v)", *p)
}

func (p *SendSmsByKugouIdReq) Validate() error {
  return nil
}
// Attributes:
//  - Phone
//  - Type
type SendJxCaptchaSmsByPhoneReq struct {
  Phone string `thrift:"phone,1,required" db:"phone" json:"phone"`
  Type int32 `thrift:"type,2,required" db:"type" json:"type"`
}

func NewSendJxCaptchaSmsByPhoneReq() *SendJxCaptchaSmsByPhoneReq {
  return &SendJxCaptchaSmsByPhoneReq{}
}


func (p *SendJxCaptchaSmsByPhoneReq) GetPhone() string {
  return p.Phone
}

func (p *SendJxCaptchaSmsByPhoneReq) GetType() int32 {
  return p.Type
}
func (p *SendJxCaptchaSmsByPhoneReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetPhone bool = false;
  var issetType bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetPhone = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetType = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetPhone{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Phone is not set"));
  }
  if !issetType{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Type is not set"));
  }
  return nil
}

func (p *SendJxCaptchaSmsByPhoneReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Phone = v
}
  return nil
}

func (p *SendJxCaptchaSmsByPhoneReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Type = v
}
  return nil
}

func (p *SendJxCaptchaSmsByPhoneReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendJxCaptchaSmsByPhoneReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendJxCaptchaSmsByPhoneReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "phone", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:phone: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Phone)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.phone (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:phone: ", p), err) }
  return err
}

func (p *SendJxCaptchaSmsByPhoneReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "type", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:type: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Type)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.type (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:type: ", p), err) }
  return err
}

func (p *SendJxCaptchaSmsByPhoneReq) Equals(other *SendJxCaptchaSmsByPhoneReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Phone != other.Phone { return false }
  if p.Type != other.Type { return false }
  return true
}

func (p *SendJxCaptchaSmsByPhoneReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendJxCaptchaSmsByPhoneReq(%+v)", *p)
}

func (p *SendJxCaptchaSmsByPhoneReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type SendJxCaptchaSmsByPhoneResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewSendJxCaptchaSmsByPhoneResp() *SendJxCaptchaSmsByPhoneResp {
  return &SendJxCaptchaSmsByPhoneResp{}
}


func (p *SendJxCaptchaSmsByPhoneResp) GetCode() int32 {
  return p.Code
}

func (p *SendJxCaptchaSmsByPhoneResp) GetMsg() string {
  return p.Msg
}
var SendJxCaptchaSmsByPhoneResp_Data_DEFAULT string
func (p *SendJxCaptchaSmsByPhoneResp) GetData() string {
  if !p.IsSetData() {
    return SendJxCaptchaSmsByPhoneResp_Data_DEFAULT
  }
return *p.Data
}
func (p *SendJxCaptchaSmsByPhoneResp) IsSetData() bool {
  return p.Data != nil
}

func (p *SendJxCaptchaSmsByPhoneResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *SendJxCaptchaSmsByPhoneResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *SendJxCaptchaSmsByPhoneResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *SendJxCaptchaSmsByPhoneResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *SendJxCaptchaSmsByPhoneResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendJxCaptchaSmsByPhoneResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendJxCaptchaSmsByPhoneResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *SendJxCaptchaSmsByPhoneResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *SendJxCaptchaSmsByPhoneResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *SendJxCaptchaSmsByPhoneResp) Equals(other *SendJxCaptchaSmsByPhoneResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  return true
}

func (p *SendJxCaptchaSmsByPhoneResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendJxCaptchaSmsByPhoneResp(%+v)", *p)
}

func (p *SendJxCaptchaSmsByPhoneResp) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Code: 短信验证码值*
//  - BindCode: 发送短信验证码code*
//  - Sid: 酷我的token *
//  - Phone: 需要绑定的手机号*
//  - Type: 类型2绑定 3解绑 *
type BindJxPhoneByKugouIdReq struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Code string `thrift:"code,2,required" db:"code" json:"code"`
  BindCode string `thrift:"bindCode,3,required" db:"bindCode" json:"bindCode"`
  Sid string `thrift:"sid,4,required" db:"sid" json:"sid"`
  Phone string `thrift:"phone,5,required" db:"phone" json:"phone"`
  Type int32 `thrift:"type,6,required" db:"type" json:"type"`
}

func NewBindJxPhoneByKugouIdReq() *BindJxPhoneByKugouIdReq {
  return &BindJxPhoneByKugouIdReq{}
}


func (p *BindJxPhoneByKugouIdReq) GetKugouId() int64 {
  return p.KugouId
}

func (p *BindJxPhoneByKugouIdReq) GetCode() string {
  return p.Code
}

func (p *BindJxPhoneByKugouIdReq) GetBindCode() string {
  return p.BindCode
}

func (p *BindJxPhoneByKugouIdReq) GetSid() string {
  return p.Sid
}

func (p *BindJxPhoneByKugouIdReq) GetPhone() string {
  return p.Phone
}

func (p *BindJxPhoneByKugouIdReq) GetType() int32 {
  return p.Type
}
func (p *BindJxPhoneByKugouIdReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetCode bool = false;
  var issetBindCode bool = false;
  var issetSid bool = false;
  var issetPhone bool = false;
  var issetType bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetBindCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetSid = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetPhone = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetType = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetBindCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field BindCode is not set"));
  }
  if !issetSid{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sid is not set"));
  }
  if !issetPhone{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Phone is not set"));
  }
  if !issetType{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Type is not set"));
  }
  return nil
}

func (p *BindJxPhoneByKugouIdReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.BindCode = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Sid = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Phone = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Type = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BindJxPhoneByKugouIdReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BindJxPhoneByKugouIdReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:code: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:code: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "bindCode", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:bindCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.BindCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.bindCode (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:bindCode: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sid", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:sid: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sid)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sid (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:sid: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "phone", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:phone: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Phone)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.phone (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:phone: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "type", thrift.I32, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:type: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Type)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.type (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:type: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdReq) Equals(other *BindJxPhoneByKugouIdReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Code != other.Code { return false }
  if p.BindCode != other.BindCode { return false }
  if p.Sid != other.Sid { return false }
  if p.Phone != other.Phone { return false }
  if p.Type != other.Type { return false }
  return true
}

func (p *BindJxPhoneByKugouIdReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BindJxPhoneByKugouIdReq(%+v)", *p)
}

func (p *BindJxPhoneByKugouIdReq) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Code: 短信验证码值*
//  - BindCode: 发送短信验证码code*
//  - OldCode: 旧手机的验证码*
//  - OldBindCode: 旧手机的验证码Code*
//  - Sid: 酷我的token *
//  - Phone: 需要换绑定的手机号*
type ChangeJxMobileReq struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Code string `thrift:"code,2,required" db:"code" json:"code"`
  BindCode string `thrift:"bindCode,3,required" db:"bindCode" json:"bindCode"`
  OldCode string `thrift:"oldCode,4,required" db:"oldCode" json:"oldCode"`
  OldBindCode string `thrift:"oldBindCode,5,required" db:"oldBindCode" json:"oldBindCode"`
  Sid string `thrift:"sid,6,required" db:"sid" json:"sid"`
  Phone string `thrift:"phone,7,required" db:"phone" json:"phone"`
}

func NewChangeJxMobileReq() *ChangeJxMobileReq {
  return &ChangeJxMobileReq{}
}


func (p *ChangeJxMobileReq) GetKugouId() int64 {
  return p.KugouId
}

func (p *ChangeJxMobileReq) GetCode() string {
  return p.Code
}

func (p *ChangeJxMobileReq) GetBindCode() string {
  return p.BindCode
}

func (p *ChangeJxMobileReq) GetOldCode() string {
  return p.OldCode
}

func (p *ChangeJxMobileReq) GetOldBindCode() string {
  return p.OldBindCode
}

func (p *ChangeJxMobileReq) GetSid() string {
  return p.Sid
}

func (p *ChangeJxMobileReq) GetPhone() string {
  return p.Phone
}
func (p *ChangeJxMobileReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetCode bool = false;
  var issetBindCode bool = false;
  var issetOldCode bool = false;
  var issetOldBindCode bool = false;
  var issetSid bool = false;
  var issetPhone bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetBindCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetOldCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetOldBindCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetSid = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
        issetPhone = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetBindCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field BindCode is not set"));
  }
  if !issetOldCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OldCode is not set"));
  }
  if !issetOldBindCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OldBindCode is not set"));
  }
  if !issetSid{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sid is not set"));
  }
  if !issetPhone{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Phone is not set"));
  }
  return nil
}

func (p *ChangeJxMobileReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *ChangeJxMobileReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *ChangeJxMobileReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.BindCode = v
}
  return nil
}

func (p *ChangeJxMobileReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.OldCode = v
}
  return nil
}

func (p *ChangeJxMobileReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.OldBindCode = v
}
  return nil
}

func (p *ChangeJxMobileReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Sid = v
}
  return nil
}

func (p *ChangeJxMobileReq)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Phone = v
}
  return nil
}

func (p *ChangeJxMobileReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ChangeJxMobileReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ChangeJxMobileReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:code: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:code: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "bindCode", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:bindCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.BindCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.bindCode (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:bindCode: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "oldCode", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:oldCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OldCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.oldCode (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:oldCode: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "oldBindCode", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:oldBindCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OldBindCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.oldBindCode (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:oldBindCode: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sid", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sid: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sid)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sid (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sid: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "phone", thrift.STRING, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:phone: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Phone)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.phone (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:phone: ", p), err) }
  return err
}

func (p *ChangeJxMobileReq) Equals(other *ChangeJxMobileReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Code != other.Code { return false }
  if p.BindCode != other.BindCode { return false }
  if p.OldCode != other.OldCode { return false }
  if p.OldBindCode != other.OldBindCode { return false }
  if p.Sid != other.Sid { return false }
  if p.Phone != other.Phone { return false }
  return true
}

func (p *ChangeJxMobileReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ChangeJxMobileReq(%+v)", *p)
}

func (p *ChangeJxMobileReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type BindJxPhoneByKugouIdResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data bool `thrift:"data,3,required" db:"data" json:"data"`
}

func NewBindJxPhoneByKugouIdResp() *BindJxPhoneByKugouIdResp {
  return &BindJxPhoneByKugouIdResp{}
}


func (p *BindJxPhoneByKugouIdResp) GetCode() int32 {
  return p.Code
}

func (p *BindJxPhoneByKugouIdResp) GetMsg() string {
  return p.Msg
}

func (p *BindJxPhoneByKugouIdResp) GetData() bool {
  return p.Data
}
func (p *BindJxPhoneByKugouIdResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;
  var issetData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  return nil
}

func (p *BindJxPhoneByKugouIdResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = v
}
  return nil
}

func (p *BindJxPhoneByKugouIdResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BindJxPhoneByKugouIdResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BindJxPhoneByKugouIdResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.Data)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  return err
}

func (p *BindJxPhoneByKugouIdResp) Equals(other *BindJxPhoneByKugouIdResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data { return false }
  return true
}

func (p *BindJxPhoneByKugouIdResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BindJxPhoneByKugouIdResp(%+v)", *p)
}

func (p *BindJxPhoneByKugouIdResp) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type BindInfoResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *BindInfo `thrift:"data,3,required" db:"data" json:"data"`
}

func NewBindInfoResp() *BindInfoResp {
  return &BindInfoResp{}
}


func (p *BindInfoResp) GetCode() int32 {
  return p.Code
}

func (p *BindInfoResp) GetMsg() string {
  return p.Msg
}
var BindInfoResp_Data_DEFAULT *BindInfo
func (p *BindInfoResp) GetData() *BindInfo {
  if !p.IsSetData() {
    return BindInfoResp_Data_DEFAULT
  }
return p.Data
}
func (p *BindInfoResp) IsSetData() bool {
  return p.Data != nil
}

func (p *BindInfoResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;
  var issetData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  return nil
}

func (p *BindInfoResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *BindInfoResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *BindInfoResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &BindInfo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *BindInfoResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BindInfoResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BindInfoResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *BindInfoResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *BindInfoResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
  if err := p.Data.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  return err
}

func (p *BindInfoResp) Equals(other *BindInfoResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *BindInfoResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BindInfoResp(%+v)", *p)
}

func (p *BindInfoResp) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Phone
//  - Qq
//  - Wechat
//  - Mail
type BindInfo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Phone *string `thrift:"phone,2" db:"phone" json:"phone,omitempty"`
  Qq *string `thrift:"qq,3" db:"qq" json:"qq,omitempty"`
  Wechat *string `thrift:"wechat,4" db:"wechat" json:"wechat,omitempty"`
  Mail *string `thrift:"mail,5" db:"mail" json:"mail,omitempty"`
}

func NewBindInfo() *BindInfo {
  return &BindInfo{}
}


func (p *BindInfo) GetKugouId() int64 {
  return p.KugouId
}
var BindInfo_Phone_DEFAULT string
func (p *BindInfo) GetPhone() string {
  if !p.IsSetPhone() {
    return BindInfo_Phone_DEFAULT
  }
return *p.Phone
}
var BindInfo_Qq_DEFAULT string
func (p *BindInfo) GetQq() string {
  if !p.IsSetQq() {
    return BindInfo_Qq_DEFAULT
  }
return *p.Qq
}
var BindInfo_Wechat_DEFAULT string
func (p *BindInfo) GetWechat() string {
  if !p.IsSetWechat() {
    return BindInfo_Wechat_DEFAULT
  }
return *p.Wechat
}
var BindInfo_Mail_DEFAULT string
func (p *BindInfo) GetMail() string {
  if !p.IsSetMail() {
    return BindInfo_Mail_DEFAULT
  }
return *p.Mail
}
func (p *BindInfo) IsSetPhone() bool {
  return p.Phone != nil
}

func (p *BindInfo) IsSetQq() bool {
  return p.Qq != nil
}

func (p *BindInfo) IsSetWechat() bool {
  return p.Wechat != nil
}

func (p *BindInfo) IsSetMail() bool {
  return p.Mail != nil
}

func (p *BindInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  return nil
}

func (p *BindInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *BindInfo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Phone = &v
}
  return nil
}

func (p *BindInfo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Qq = &v
}
  return nil
}

func (p *BindInfo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Wechat = &v
}
  return nil
}

func (p *BindInfo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Mail = &v
}
  return nil
}

func (p *BindInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "BindInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BindInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *BindInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPhone() {
    if err := oprot.WriteFieldBegin(ctx, "phone", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:phone: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Phone)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.phone (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:phone: ", p), err) }
  }
  return err
}

func (p *BindInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetQq() {
    if err := oprot.WriteFieldBegin(ctx, "qq", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:qq: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Qq)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.qq (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:qq: ", p), err) }
  }
  return err
}

func (p *BindInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWechat() {
    if err := oprot.WriteFieldBegin(ctx, "wechat", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:wechat: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Wechat)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.wechat (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:wechat: ", p), err) }
  }
  return err
}

func (p *BindInfo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMail() {
    if err := oprot.WriteFieldBegin(ctx, "mail", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:mail: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Mail)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mail (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:mail: ", p), err) }
  }
  return err
}

func (p *BindInfo) Equals(other *BindInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Phone != other.Phone {
    if p.Phone == nil || other.Phone == nil {
      return false
    }
    if (*p.Phone) != (*other.Phone) { return false }
  }
  if p.Qq != other.Qq {
    if p.Qq == nil || other.Qq == nil {
      return false
    }
    if (*p.Qq) != (*other.Qq) { return false }
  }
  if p.Wechat != other.Wechat {
    if p.Wechat == nil || other.Wechat == nil {
      return false
    }
    if (*p.Wechat) != (*other.Wechat) { return false }
  }
  if p.Mail != other.Mail {
    if p.Mail == nil || other.Mail == nil {
      return false
    }
    if (*p.Mail) != (*other.Mail) { return false }
  }
  return true
}

func (p *BindInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BindInfo(%+v)", *p)
}

func (p *BindInfo) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type SendSmsByKugouIdResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data bool `thrift:"data,3,required" db:"data" json:"data"`
}

func NewSendSmsByKugouIdResp() *SendSmsByKugouIdResp {
  return &SendSmsByKugouIdResp{}
}


func (p *SendSmsByKugouIdResp) GetCode() int32 {
  return p.Code
}

func (p *SendSmsByKugouIdResp) GetMsg() string {
  return p.Msg
}

func (p *SendSmsByKugouIdResp) GetData() bool {
  return p.Data
}
func (p *SendSmsByKugouIdResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;
  var issetData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  return nil
}

func (p *SendSmsByKugouIdResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *SendSmsByKugouIdResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *SendSmsByKugouIdResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Data = v
}
  return nil
}

func (p *SendSmsByKugouIdResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendSmsByKugouIdResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendSmsByKugouIdResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.Data)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  return err
}

func (p *SendSmsByKugouIdResp) Equals(other *SendSmsByKugouIdResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if p.Data != other.Data { return false }
  return true
}

func (p *SendSmsByKugouIdResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendSmsByKugouIdResp(%+v)", *p)
}

func (p *SendSmsByKugouIdResp) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type UserLoginInfoResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserLoginInfo `thrift:"data,3,required" db:"data" json:"data"`
}

func NewUserLoginInfoResp() *UserLoginInfoResp {
  return &UserLoginInfoResp{}
}


func (p *UserLoginInfoResp) GetCode() int32 {
  return p.Code
}

func (p *UserLoginInfoResp) GetMsg() string {
  return p.Msg
}
var UserLoginInfoResp_Data_DEFAULT *UserLoginInfo
func (p *UserLoginInfoResp) GetData() *UserLoginInfo {
  if !p.IsSetData() {
    return UserLoginInfoResp_Data_DEFAULT
  }
return p.Data
}
func (p *UserLoginInfoResp) IsSetData() bool {
  return p.Data != nil
}

func (p *UserLoginInfoResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;
  var issetData bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  return nil
}

func (p *UserLoginInfoResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *UserLoginInfoResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *UserLoginInfoResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserLoginInfo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *UserLoginInfoResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserLoginInfoResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserLoginInfoResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *UserLoginInfoResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *UserLoginInfoResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
  if err := p.Data.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  return err
}

func (p *UserLoginInfoResp) Equals(other *UserLoginInfoResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *UserLoginInfoResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserLoginInfoResp(%+v)", *p)
}

func (p *UserLoginInfoResp) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Country
//  - Province
//  - City
//  - Isp
//  - ClientIP
type UserLoginInfo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Country *string `thrift:"country,2" db:"country" json:"country,omitempty"`
  Province *string `thrift:"province,3" db:"province" json:"province,omitempty"`
  City *string `thrift:"city,4" db:"city" json:"city,omitempty"`
  Isp *string `thrift:"isp,5" db:"isp" json:"isp,omitempty"`
  ClientIP *string `thrift:"clientIP,6" db:"clientIP" json:"clientIP,omitempty"`
}

func NewUserLoginInfo() *UserLoginInfo {
  return &UserLoginInfo{}
}


func (p *UserLoginInfo) GetKugouId() int64 {
  return p.KugouId
}
var UserLoginInfo_Country_DEFAULT string
func (p *UserLoginInfo) GetCountry() string {
  if !p.IsSetCountry() {
    return UserLoginInfo_Country_DEFAULT
  }
return *p.Country
}
var UserLoginInfo_Province_DEFAULT string
func (p *UserLoginInfo) GetProvince() string {
  if !p.IsSetProvince() {
    return UserLoginInfo_Province_DEFAULT
  }
return *p.Province
}
var UserLoginInfo_City_DEFAULT string
func (p *UserLoginInfo) GetCity() string {
  if !p.IsSetCity() {
    return UserLoginInfo_City_DEFAULT
  }
return *p.City
}
var UserLoginInfo_Isp_DEFAULT string
func (p *UserLoginInfo) GetIsp() string {
  if !p.IsSetIsp() {
    return UserLoginInfo_Isp_DEFAULT
  }
return *p.Isp
}
var UserLoginInfo_ClientIP_DEFAULT string
func (p *UserLoginInfo) GetClientIP() string {
  if !p.IsSetClientIP() {
    return UserLoginInfo_ClientIP_DEFAULT
  }
return *p.ClientIP
}
func (p *UserLoginInfo) IsSetCountry() bool {
  return p.Country != nil
}

func (p *UserLoginInfo) IsSetProvince() bool {
  return p.Province != nil
}

func (p *UserLoginInfo) IsSetCity() bool {
  return p.City != nil
}

func (p *UserLoginInfo) IsSetIsp() bool {
  return p.Isp != nil
}

func (p *UserLoginInfo) IsSetClientIP() bool {
  return p.ClientIP != nil
}

func (p *UserLoginInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  return nil
}

func (p *UserLoginInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *UserLoginInfo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Country = &v
}
  return nil
}

func (p *UserLoginInfo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Province = &v
}
  return nil
}

func (p *UserLoginInfo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.City = &v
}
  return nil
}

func (p *UserLoginInfo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Isp = &v
}
  return nil
}

func (p *UserLoginInfo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.ClientIP = &v
}
  return nil
}

func (p *UserLoginInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserLoginInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserLoginInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *UserLoginInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCountry() {
    if err := oprot.WriteFieldBegin(ctx, "country", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:country: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Country)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.country (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:country: ", p), err) }
  }
  return err
}

func (p *UserLoginInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetProvince() {
    if err := oprot.WriteFieldBegin(ctx, "province", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:province: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Province)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.province (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:province: ", p), err) }
  }
  return err
}

func (p *UserLoginInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCity() {
    if err := oprot.WriteFieldBegin(ctx, "city", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:city: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.City)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.city (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:city: ", p), err) }
  }
  return err
}

func (p *UserLoginInfo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetIsp() {
    if err := oprot.WriteFieldBegin(ctx, "isp", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:isp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Isp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.isp (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:isp: ", p), err) }
  }
  return err
}

func (p *UserLoginInfo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientIP() {
    if err := oprot.WriteFieldBegin(ctx, "clientIP", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:clientIP: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ClientIP)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientIP (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:clientIP: ", p), err) }
  }
  return err
}

func (p *UserLoginInfo) Equals(other *UserLoginInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Country != other.Country {
    if p.Country == nil || other.Country == nil {
      return false
    }
    if (*p.Country) != (*other.Country) { return false }
  }
  if p.Province != other.Province {
    if p.Province == nil || other.Province == nil {
      return false
    }
    if (*p.Province) != (*other.Province) { return false }
  }
  if p.City != other.City {
    if p.City == nil || other.City == nil {
      return false
    }
    if (*p.City) != (*other.City) { return false }
  }
  if p.Isp != other.Isp {
    if p.Isp == nil || other.Isp == nil {
      return false
    }
    if (*p.Isp) != (*other.Isp) { return false }
  }
  if p.ClientIP != other.ClientIP {
    if p.ClientIP == nil || other.ClientIP == nil {
      return false
    }
    if (*p.ClientIP) != (*other.ClientIP) { return false }
  }
  return true
}

func (p *UserLoginInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserLoginInfo(%+v)", *p)
}

func (p *UserLoginInfo) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Token
//  - AppId
//  - Dfid
//  - Mid
//  - ClientIP
//  - MKugouId
type VisitorReq struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Token *string `thrift:"token,2" db:"token" json:"token,omitempty"`
  AppId *int32 `thrift:"appId,3" db:"appId" json:"appId,omitempty"`
  Dfid *string `thrift:"dfid,4" db:"dfid" json:"dfid,omitempty"`
  Mid *string `thrift:"mid,5" db:"mid" json:"mid,omitempty"`
  ClientIP *string `thrift:"clientIP,6" db:"clientIP" json:"clientIP,omitempty"`
  MKugouId *int64 `thrift:"mKugouId,7" db:"mKugouId" json:"mKugouId,omitempty"`
}

func NewVisitorReq() *VisitorReq {
  return &VisitorReq{}
}


func (p *VisitorReq) GetKugouId() int64 {
  return p.KugouId
}
var VisitorReq_Token_DEFAULT string
func (p *VisitorReq) GetToken() string {
  if !p.IsSetToken() {
    return VisitorReq_Token_DEFAULT
  }
return *p.Token
}
var VisitorReq_AppId_DEFAULT int32
func (p *VisitorReq) GetAppId() int32 {
  if !p.IsSetAppId() {
    return VisitorReq_AppId_DEFAULT
  }
return *p.AppId
}
var VisitorReq_Dfid_DEFAULT string
func (p *VisitorReq) GetDfid() string {
  if !p.IsSetDfid() {
    return VisitorReq_Dfid_DEFAULT
  }
return *p.Dfid
}
var VisitorReq_Mid_DEFAULT string
func (p *VisitorReq) GetMid() string {
  if !p.IsSetMid() {
    return VisitorReq_Mid_DEFAULT
  }
return *p.Mid
}
var VisitorReq_ClientIP_DEFAULT string
func (p *VisitorReq) GetClientIP() string {
  if !p.IsSetClientIP() {
    return VisitorReq_ClientIP_DEFAULT
  }
return *p.ClientIP
}
var VisitorReq_MKugouId_DEFAULT int64
func (p *VisitorReq) GetMKugouId() int64 {
  if !p.IsSetMKugouId() {
    return VisitorReq_MKugouId_DEFAULT
  }
return *p.MKugouId
}
func (p *VisitorReq) IsSetToken() bool {
  return p.Token != nil
}

func (p *VisitorReq) IsSetAppId() bool {
  return p.AppId != nil
}

func (p *VisitorReq) IsSetDfid() bool {
  return p.Dfid != nil
}

func (p *VisitorReq) IsSetMid() bool {
  return p.Mid != nil
}

func (p *VisitorReq) IsSetClientIP() bool {
  return p.ClientIP != nil
}

func (p *VisitorReq) IsSetMKugouId() bool {
  return p.MKugouId != nil
}

func (p *VisitorReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  return nil
}

func (p *VisitorReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *VisitorReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Token = &v
}
  return nil
}

func (p *VisitorReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.AppId = &v
}
  return nil
}

func (p *VisitorReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Dfid = &v
}
  return nil
}

func (p *VisitorReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Mid = &v
}
  return nil
}

func (p *VisitorReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.ClientIP = &v
}
  return nil
}

func (p *VisitorReq)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.MKugouId = &v
}
  return nil
}

func (p *VisitorReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "VisitorReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *VisitorReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *VisitorReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetToken() {
    if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:token: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Token)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.token (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:token: ", p), err) }
  }
  return err
}

func (p *VisitorReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAppId() {
    if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AppId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err) }
  }
  return err
}

func (p *VisitorReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDfid() {
    if err := oprot.WriteFieldBegin(ctx, "dfid", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:dfid: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Dfid)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.dfid (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:dfid: ", p), err) }
  }
  return err
}

func (p *VisitorReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMid() {
    if err := oprot.WriteFieldBegin(ctx, "mid", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:mid: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Mid)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mid (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:mid: ", p), err) }
  }
  return err
}

func (p *VisitorReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientIP() {
    if err := oprot.WriteFieldBegin(ctx, "clientIP", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:clientIP: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ClientIP)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientIP (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:clientIP: ", p), err) }
  }
  return err
}

func (p *VisitorReq) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "mKugouId", thrift.I64, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:mKugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.MKugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mKugouId (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:mKugouId: ", p), err) }
  }
  return err
}

func (p *VisitorReq) Equals(other *VisitorReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Token != other.Token {
    if p.Token == nil || other.Token == nil {
      return false
    }
    if (*p.Token) != (*other.Token) { return false }
  }
  if p.AppId != other.AppId {
    if p.AppId == nil || other.AppId == nil {
      return false
    }
    if (*p.AppId) != (*other.AppId) { return false }
  }
  if p.Dfid != other.Dfid {
    if p.Dfid == nil || other.Dfid == nil {
      return false
    }
    if (*p.Dfid) != (*other.Dfid) { return false }
  }
  if p.Mid != other.Mid {
    if p.Mid == nil || other.Mid == nil {
      return false
    }
    if (*p.Mid) != (*other.Mid) { return false }
  }
  if p.ClientIP != other.ClientIP {
    if p.ClientIP == nil || other.ClientIP == nil {
      return false
    }
    if (*p.ClientIP) != (*other.ClientIP) { return false }
  }
  if p.MKugouId != other.MKugouId {
    if p.MKugouId == nil || other.MKugouId == nil {
      return false
    }
    if (*p.MKugouId) != (*other.MKugouId) { return false }
  }
  return true
}

func (p *VisitorReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("VisitorReq(%+v)", *p)
}

func (p *VisitorReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type VisitorResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *VisitorInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewVisitorResp() *VisitorResp {
  return &VisitorResp{}
}


func (p *VisitorResp) GetCode() int32 {
  return p.Code
}

func (p *VisitorResp) GetMsg() string {
  return p.Msg
}
var VisitorResp_Data_DEFAULT *VisitorInfo
func (p *VisitorResp) GetData() *VisitorInfo {
  if !p.IsSetData() {
    return VisitorResp_Data_DEFAULT
  }
return p.Data
}
func (p *VisitorResp) IsSetData() bool {
  return p.Data != nil
}

func (p *VisitorResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *VisitorResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *VisitorResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *VisitorResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &VisitorInfo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *VisitorResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "VisitorResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *VisitorResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *VisitorResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *VisitorResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *VisitorResp) Equals(other *VisitorResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *VisitorResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("VisitorResp(%+v)", *p)
}

func (p *VisitorResp) Validate() error {
  return nil
}
// Attributes:
//  - Count
type VisitorInfo struct {
  Count int32 `thrift:"count,1,required" db:"count" json:"count"`
}

func NewVisitorInfo() *VisitorInfo {
  return &VisitorInfo{}
}


func (p *VisitorInfo) GetCount() int32 {
  return p.Count
}
func (p *VisitorInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCount bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCount = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCount{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Count is not set"));
  }
  return nil
}

func (p *VisitorInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Count = v
}
  return nil
}

func (p *VisitorInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "VisitorInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *VisitorInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "count", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:count: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Count)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.count (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:count: ", p), err) }
  return err
}

func (p *VisitorInfo) Equals(other *VisitorInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Count != other.Count { return false }
  return true
}

func (p *VisitorInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("VisitorInfo(%+v)", *p)
}

func (p *VisitorInfo) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - Token
//  - AppId
//  - Dfid
//  - Mid
//  - ClientIP
//  - MKugouId
//  - ClientVersion
type CanVisibleReq struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  Token *string `thrift:"token,2" db:"token" json:"token,omitempty"`
  AppId *int32 `thrift:"appId,3" db:"appId" json:"appId,omitempty"`
  Dfid *string `thrift:"dfid,4" db:"dfid" json:"dfid,omitempty"`
  Mid *string `thrift:"mid,5" db:"mid" json:"mid,omitempty"`
  ClientIP *string `thrift:"clientIP,6" db:"clientIP" json:"clientIP,omitempty"`
  MKugouId *int64 `thrift:"mKugouId,7" db:"mKugouId" json:"mKugouId,omitempty"`
  ClientVersion *int32 `thrift:"clientVersion,8" db:"clientVersion" json:"clientVersion,omitempty"`
}

func NewCanVisibleReq() *CanVisibleReq {
  return &CanVisibleReq{}
}


func (p *CanVisibleReq) GetKugouId() int64 {
  return p.KugouId
}
var CanVisibleReq_Token_DEFAULT string
func (p *CanVisibleReq) GetToken() string {
  if !p.IsSetToken() {
    return CanVisibleReq_Token_DEFAULT
  }
return *p.Token
}
var CanVisibleReq_AppId_DEFAULT int32
func (p *CanVisibleReq) GetAppId() int32 {
  if !p.IsSetAppId() {
    return CanVisibleReq_AppId_DEFAULT
  }
return *p.AppId
}
var CanVisibleReq_Dfid_DEFAULT string
func (p *CanVisibleReq) GetDfid() string {
  if !p.IsSetDfid() {
    return CanVisibleReq_Dfid_DEFAULT
  }
return *p.Dfid
}
var CanVisibleReq_Mid_DEFAULT string
func (p *CanVisibleReq) GetMid() string {
  if !p.IsSetMid() {
    return CanVisibleReq_Mid_DEFAULT
  }
return *p.Mid
}
var CanVisibleReq_ClientIP_DEFAULT string
func (p *CanVisibleReq) GetClientIP() string {
  if !p.IsSetClientIP() {
    return CanVisibleReq_ClientIP_DEFAULT
  }
return *p.ClientIP
}
var CanVisibleReq_MKugouId_DEFAULT int64
func (p *CanVisibleReq) GetMKugouId() int64 {
  if !p.IsSetMKugouId() {
    return CanVisibleReq_MKugouId_DEFAULT
  }
return *p.MKugouId
}
var CanVisibleReq_ClientVersion_DEFAULT int32
func (p *CanVisibleReq) GetClientVersion() int32 {
  if !p.IsSetClientVersion() {
    return CanVisibleReq_ClientVersion_DEFAULT
  }
return *p.ClientVersion
}
func (p *CanVisibleReq) IsSetToken() bool {
  return p.Token != nil
}

func (p *CanVisibleReq) IsSetAppId() bool {
  return p.AppId != nil
}

func (p *CanVisibleReq) IsSetDfid() bool {
  return p.Dfid != nil
}

func (p *CanVisibleReq) IsSetMid() bool {
  return p.Mid != nil
}

func (p *CanVisibleReq) IsSetClientIP() bool {
  return p.ClientIP != nil
}

func (p *CanVisibleReq) IsSetMKugouId() bool {
  return p.MKugouId != nil
}

func (p *CanVisibleReq) IsSetClientVersion() bool {
  return p.ClientVersion != nil
}

func (p *CanVisibleReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  return nil
}

func (p *CanVisibleReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *CanVisibleReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Token = &v
}
  return nil
}

func (p *CanVisibleReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.AppId = &v
}
  return nil
}

func (p *CanVisibleReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Dfid = &v
}
  return nil
}

func (p *CanVisibleReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Mid = &v
}
  return nil
}

func (p *CanVisibleReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.ClientIP = &v
}
  return nil
}

func (p *CanVisibleReq)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.MKugouId = &v
}
  return nil
}

func (p *CanVisibleReq)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.ClientVersion = &v
}
  return nil
}

func (p *CanVisibleReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CanVisibleReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CanVisibleReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *CanVisibleReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetToken() {
    if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:token: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Token)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.token (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:token: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAppId() {
    if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.AppId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDfid() {
    if err := oprot.WriteFieldBegin(ctx, "dfid", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:dfid: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Dfid)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.dfid (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:dfid: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMid() {
    if err := oprot.WriteFieldBegin(ctx, "mid", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:mid: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Mid)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mid (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:mid: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientIP() {
    if err := oprot.WriteFieldBegin(ctx, "clientIP", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:clientIP: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ClientIP)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientIP (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:clientIP: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMKugouId() {
    if err := oprot.WriteFieldBegin(ctx, "mKugouId", thrift.I64, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:mKugouId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.MKugouId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mKugouId (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:mKugouId: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientVersion() {
    if err := oprot.WriteFieldBegin(ctx, "clientVersion", thrift.I32, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:clientVersion: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.ClientVersion)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientVersion (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:clientVersion: ", p), err) }
  }
  return err
}

func (p *CanVisibleReq) Equals(other *CanVisibleReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.Token != other.Token {
    if p.Token == nil || other.Token == nil {
      return false
    }
    if (*p.Token) != (*other.Token) { return false }
  }
  if p.AppId != other.AppId {
    if p.AppId == nil || other.AppId == nil {
      return false
    }
    if (*p.AppId) != (*other.AppId) { return false }
  }
  if p.Dfid != other.Dfid {
    if p.Dfid == nil || other.Dfid == nil {
      return false
    }
    if (*p.Dfid) != (*other.Dfid) { return false }
  }
  if p.Mid != other.Mid {
    if p.Mid == nil || other.Mid == nil {
      return false
    }
    if (*p.Mid) != (*other.Mid) { return false }
  }
  if p.ClientIP != other.ClientIP {
    if p.ClientIP == nil || other.ClientIP == nil {
      return false
    }
    if (*p.ClientIP) != (*other.ClientIP) { return false }
  }
  if p.MKugouId != other.MKugouId {
    if p.MKugouId == nil || other.MKugouId == nil {
      return false
    }
    if (*p.MKugouId) != (*other.MKugouId) { return false }
  }
  if p.ClientVersion != other.ClientVersion {
    if p.ClientVersion == nil || other.ClientVersion == nil {
      return false
    }
    if (*p.ClientVersion) != (*other.ClientVersion) { return false }
  }
  return true
}

func (p *CanVisibleReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CanVisibleReq(%+v)", *p)
}

func (p *CanVisibleReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type CanVisibleResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *Visible `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewCanVisibleResp() *CanVisibleResp {
  return &CanVisibleResp{}
}


func (p *CanVisibleResp) GetCode() int32 {
  return p.Code
}

func (p *CanVisibleResp) GetMsg() string {
  return p.Msg
}
var CanVisibleResp_Data_DEFAULT *Visible
func (p *CanVisibleResp) GetData() *Visible {
  if !p.IsSetData() {
    return CanVisibleResp_Data_DEFAULT
  }
return p.Data
}
func (p *CanVisibleResp) IsSetData() bool {
  return p.Data != nil
}

func (p *CanVisibleResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *CanVisibleResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *CanVisibleResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *CanVisibleResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &Visible{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *CanVisibleResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CanVisibleResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CanVisibleResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *CanVisibleResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *CanVisibleResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *CanVisibleResp) Equals(other *CanVisibleResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *CanVisibleResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CanVisibleResp(%+v)", *p)
}

func (p *CanVisibleResp) Validate() error {
  return nil
}
// Attributes:
//  - CanVisible
type Visible struct {
  CanVisible bool `thrift:"canVisible,1,required" db:"canVisible" json:"canVisible"`
}

func NewVisible() *Visible {
  return &Visible{}
}


func (p *Visible) GetCanVisible() bool {
  return p.CanVisible
}
func (p *Visible) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCanVisible bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCanVisible = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCanVisible{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field CanVisible is not set"));
  }
  return nil
}

func (p *Visible)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.CanVisible = v
}
  return nil
}

func (p *Visible) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Visible"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *Visible) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "canVisible", thrift.BOOL, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:canVisible: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.CanVisible)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.canVisible (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:canVisible: ", p), err) }
  return err
}

func (p *Visible) Equals(other *Visible) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.CanVisible != other.CanVisible { return false }
  return true
}

func (p *Visible) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("Visible(%+v)", *p)
}

func (p *Visible) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - AppId
type GetUserExtReq struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
}

func NewGetUserExtReq() *GetUserExtReq {
  return &GetUserExtReq{}
}


func (p *GetUserExtReq) GetKugouId() int64 {
  return p.KugouId
}

func (p *GetUserExtReq) GetAppId() int32 {
  return p.AppId
}
func (p *GetUserExtReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetAppId bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  return nil
}

func (p *GetUserExtReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *GetUserExtReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *GetUserExtReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetUserExtReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetUserExtReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *GetUserExtReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *GetUserExtReq) Equals(other *GetUserExtReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.AppId != other.AppId { return false }
  return true
}

func (p *GetUserExtReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetUserExtReq(%+v)", *p)
}

func (p *GetUserExtReq) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type GetUserExtResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *UserExtInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewGetUserExtResp() *GetUserExtResp {
  return &GetUserExtResp{}
}


func (p *GetUserExtResp) GetCode() int32 {
  return p.Code
}

func (p *GetUserExtResp) GetMsg() string {
  return p.Msg
}
var GetUserExtResp_Data_DEFAULT *UserExtInfo
func (p *GetUserExtResp) GetData() *UserExtInfo {
  if !p.IsSetData() {
    return GetUserExtResp_Data_DEFAULT
  }
return p.Data
}
func (p *GetUserExtResp) IsSetData() bool {
  return p.Data != nil
}

func (p *GetUserExtResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *GetUserExtResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *GetUserExtResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *GetUserExtResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &UserExtInfo{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *GetUserExtResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetUserExtResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetUserExtResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *GetUserExtResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *GetUserExtResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *GetUserExtResp) Equals(other *GetUserExtResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *GetUserExtResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetUserExtResp(%+v)", *p)
}

func (p *GetUserExtResp) Validate() error {
  return nil
}
// Attributes:
//  - KugouId
//  - IsHuawei
//  - IsBindWx
//  - IsBindQq
//  - IsBindPhone
type UserExtInfo struct {
  KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
  IsHuawei bool `thrift:"isHuawei,2,required" db:"isHuawei" json:"isHuawei"`
  IsBindWx bool `thrift:"isBindWx,3,required" db:"isBindWx" json:"isBindWx"`
  IsBindQq bool `thrift:"isBindQq,4,required" db:"isBindQq" json:"isBindQq"`
  IsBindPhone bool `thrift:"isBindPhone,5,required" db:"isBindPhone" json:"isBindPhone"`
}

func NewUserExtInfo() *UserExtInfo {
  return &UserExtInfo{}
}


func (p *UserExtInfo) GetKugouId() int64 {
  return p.KugouId
}

func (p *UserExtInfo) GetIsHuawei() bool {
  return p.IsHuawei
}

func (p *UserExtInfo) GetIsBindWx() bool {
  return p.IsBindWx
}

func (p *UserExtInfo) GetIsBindQq() bool {
  return p.IsBindQq
}

func (p *UserExtInfo) GetIsBindPhone() bool {
  return p.IsBindPhone
}
func (p *UserExtInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouId bool = false;
  var issetIsHuawei bool = false;
  var issetIsBindWx bool = false;
  var issetIsBindQq bool = false;
  var issetIsBindPhone bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetIsHuawei = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetIsBindWx = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetIsBindQq = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetIsBindPhone = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetIsHuawei{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IsHuawei is not set"));
  }
  if !issetIsBindWx{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IsBindWx is not set"));
  }
  if !issetIsBindQq{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IsBindQq is not set"));
  }
  if !issetIsBindPhone{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IsBindPhone is not set"));
  }
  return nil
}

func (p *UserExtInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *UserExtInfo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.IsHuawei = v
}
  return nil
}

func (p *UserExtInfo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.IsBindWx = v
}
  return nil
}

func (p *UserExtInfo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.IsBindQq = v
}
  return nil
}

func (p *UserExtInfo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.IsBindPhone = v
}
  return nil
}

func (p *UserExtInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UserExtInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UserExtInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err) }
  return err
}

func (p *UserExtInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "isHuawei", thrift.BOOL, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:isHuawei: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsHuawei)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.isHuawei (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:isHuawei: ", p), err) }
  return err
}

func (p *UserExtInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "isBindWx", thrift.BOOL, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:isBindWx: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsBindWx)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.isBindWx (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:isBindWx: ", p), err) }
  return err
}

func (p *UserExtInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "isBindQq", thrift.BOOL, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:isBindQq: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsBindQq)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.isBindQq (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:isBindQq: ", p), err) }
  return err
}

func (p *UserExtInfo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "isBindPhone", thrift.BOOL, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:isBindPhone: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsBindPhone)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.isBindPhone (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:isBindPhone: ", p), err) }
  return err
}

func (p *UserExtInfo) Equals(other *UserExtInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.KugouId != other.KugouId { return false }
  if p.IsHuawei != other.IsHuawei { return false }
  if p.IsBindWx != other.IsBindWx { return false }
  if p.IsBindQq != other.IsBindQq { return false }
  if p.IsBindPhone != other.IsBindPhone { return false }
  return true
}

func (p *UserExtInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UserExtInfo(%+v)", *p)
}

func (p *UserExtInfo) Validate() error {
  return nil
}
// Attributes:
//  - Code
//  - Msg
//  - Data
type MobileRelatorResp struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *MobileRelator `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewMobileRelatorResp() *MobileRelatorResp {
  return &MobileRelatorResp{}
}


func (p *MobileRelatorResp) GetCode() int32 {
  return p.Code
}

func (p *MobileRelatorResp) GetMsg() string {
  return p.Msg
}
var MobileRelatorResp_Data_DEFAULT *MobileRelator
func (p *MobileRelatorResp) GetData() *MobileRelator {
  if !p.IsSetData() {
    return MobileRelatorResp_Data_DEFAULT
  }
return p.Data
}
func (p *MobileRelatorResp) IsSetData() bool {
  return p.Data != nil
}

func (p *MobileRelatorResp) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *MobileRelatorResp)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *MobileRelatorResp)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *MobileRelatorResp)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &MobileRelator{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *MobileRelatorResp) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MobileRelatorResp"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *MobileRelatorResp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *MobileRelatorResp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *MobileRelatorResp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *MobileRelatorResp) Equals(other *MobileRelatorResp) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *MobileRelatorResp) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("MobileRelatorResp(%+v)", *p)
}

func (p *MobileRelatorResp) Validate() error {
  return nil
}
// Attributes:
//  - KugouIdList
type MobileRelator struct {
  KugouIdList []int64 `thrift:"kugouIdList,1,required" db:"kugouIdList" json:"kugouIdList"`
}

func NewMobileRelator() *MobileRelator {
  return &MobileRelator{}
}


func (p *MobileRelator) GetKugouIdList() []int64 {
  return p.KugouIdList
}
func (p *MobileRelator) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetKugouIdList bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetKugouIdList = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetKugouIdList{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIdList is not set"));
  }
  return nil
}

func (p *MobileRelator)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.KugouIdList =  tSlice
  for i := 0; i < size; i ++ {
var _elem9 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem9 = v
}
    p.KugouIdList = append(p.KugouIdList, _elem9)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *MobileRelator) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MobileRelator"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *MobileRelator) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouIdList", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIdList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIdList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.KugouIdList {
    if err := oprot.WriteI64(ctx, int64(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIdList: ", p), err) }
  return err
}

func (p *MobileRelator) Equals(other *MobileRelator) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.KugouIdList) != len(other.KugouIdList) { return false }
  for i, _tgt := range p.KugouIdList {
    _src10 := other.KugouIdList[i]
    if _tgt != _src10 { return false }
  }
  return true
}

func (p *MobileRelator) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("MobileRelator(%+v)", *p)
}

func (p *MobileRelator) Validate() error {
  return nil
}
