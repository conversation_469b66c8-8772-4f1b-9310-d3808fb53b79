// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"platform_consume_service"
)

var _ = platform_consume_service.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  ConsumeResp repairConsume(RepairConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp multiConsume(MultiConsumeVO vo)")
  fmt.Fprintln(os.<PERSON>, "  ConsumeResp batchConsume(BatchConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp asyncConsume(ConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp consume(ConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp consumeStrictMoney(ConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp mvConsume(ConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp bean(BeanVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp rechargeFee(RechargeFeeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp addStorageConsumeLog(StorageConsumeVO vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp getUserMoney(i64 kugouId)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp albumConsume(AlbumConsume vo)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp fixBean(i32 appId, i64 consumeId, i64 timeStamp)")
  fmt.Fprintln(os.Stderr, "  ConsumeResp storageConsume(StorageVO vo)")
  fmt.Fprintln(os.Stderr, "  AppConfigResult queryAppConfig(ConfigRequest params)")
  fmt.Fprintln(os.Stderr, "  BeanDivideResult queryBeanDivideInfo(BeanDivideQueryRequest param)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := platform_consume_service.NewPlatformConsumeServiceClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "repairConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "RepairConsume requires 1 args")
      flag.Usage()
    }
    arg108 := flag.Arg(1)
    mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
    defer mbTrans109.Close()
    _, err110 := mbTrans109.WriteString(arg108)
    if err110 != nil {
      Usage()
      return
    }
    factory111 := thrift.NewTJSONProtocolFactory()
    jsProt112 := factory111.GetProtocol(mbTrans109)
    argvalue0 := platform_consume_service.NewRepairConsumeVO()
    err113 := argvalue0.Read(context.Background(), jsProt112)
    if err113 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.RepairConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "multiConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "MultiConsume requires 1 args")
      flag.Usage()
    }
    arg114 := flag.Arg(1)
    mbTrans115 := thrift.NewTMemoryBufferLen(len(arg114))
    defer mbTrans115.Close()
    _, err116 := mbTrans115.WriteString(arg114)
    if err116 != nil {
      Usage()
      return
    }
    factory117 := thrift.NewTJSONProtocolFactory()
    jsProt118 := factory117.GetProtocol(mbTrans115)
    argvalue0 := platform_consume_service.NewMultiConsumeVO()
    err119 := argvalue0.Read(context.Background(), jsProt118)
    if err119 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.MultiConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "batchConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "BatchConsume requires 1 args")
      flag.Usage()
    }
    arg120 := flag.Arg(1)
    mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
    defer mbTrans121.Close()
    _, err122 := mbTrans121.WriteString(arg120)
    if err122 != nil {
      Usage()
      return
    }
    factory123 := thrift.NewTJSONProtocolFactory()
    jsProt124 := factory123.GetProtocol(mbTrans121)
    argvalue0 := platform_consume_service.NewBatchConsumeVO()
    err125 := argvalue0.Read(context.Background(), jsProt124)
    if err125 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.BatchConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "asyncConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "AsyncConsume requires 1 args")
      flag.Usage()
    }
    arg126 := flag.Arg(1)
    mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
    defer mbTrans127.Close()
    _, err128 := mbTrans127.WriteString(arg126)
    if err128 != nil {
      Usage()
      return
    }
    factory129 := thrift.NewTJSONProtocolFactory()
    jsProt130 := factory129.GetProtocol(mbTrans127)
    argvalue0 := platform_consume_service.NewConsumeVO()
    err131 := argvalue0.Read(context.Background(), jsProt130)
    if err131 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.AsyncConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "consume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "Consume requires 1 args")
      flag.Usage()
    }
    arg132 := flag.Arg(1)
    mbTrans133 := thrift.NewTMemoryBufferLen(len(arg132))
    defer mbTrans133.Close()
    _, err134 := mbTrans133.WriteString(arg132)
    if err134 != nil {
      Usage()
      return
    }
    factory135 := thrift.NewTJSONProtocolFactory()
    jsProt136 := factory135.GetProtocol(mbTrans133)
    argvalue0 := platform_consume_service.NewConsumeVO()
    err137 := argvalue0.Read(context.Background(), jsProt136)
    if err137 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.Consume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "consumeStrictMoney":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "ConsumeStrictMoney requires 1 args")
      flag.Usage()
    }
    arg138 := flag.Arg(1)
    mbTrans139 := thrift.NewTMemoryBufferLen(len(arg138))
    defer mbTrans139.Close()
    _, err140 := mbTrans139.WriteString(arg138)
    if err140 != nil {
      Usage()
      return
    }
    factory141 := thrift.NewTJSONProtocolFactory()
    jsProt142 := factory141.GetProtocol(mbTrans139)
    argvalue0 := platform_consume_service.NewConsumeVO()
    err143 := argvalue0.Read(context.Background(), jsProt142)
    if err143 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.ConsumeStrictMoney(context.Background(), value0))
    fmt.Print("\n")
    break
  case "mvConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "MvConsume requires 1 args")
      flag.Usage()
    }
    arg144 := flag.Arg(1)
    mbTrans145 := thrift.NewTMemoryBufferLen(len(arg144))
    defer mbTrans145.Close()
    _, err146 := mbTrans145.WriteString(arg144)
    if err146 != nil {
      Usage()
      return
    }
    factory147 := thrift.NewTJSONProtocolFactory()
    jsProt148 := factory147.GetProtocol(mbTrans145)
    argvalue0 := platform_consume_service.NewConsumeVO()
    err149 := argvalue0.Read(context.Background(), jsProt148)
    if err149 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.MvConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "bean":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "Bean requires 1 args")
      flag.Usage()
    }
    arg150 := flag.Arg(1)
    mbTrans151 := thrift.NewTMemoryBufferLen(len(arg150))
    defer mbTrans151.Close()
    _, err152 := mbTrans151.WriteString(arg150)
    if err152 != nil {
      Usage()
      return
    }
    factory153 := thrift.NewTJSONProtocolFactory()
    jsProt154 := factory153.GetProtocol(mbTrans151)
    argvalue0 := platform_consume_service.NewBeanVO()
    err155 := argvalue0.Read(context.Background(), jsProt154)
    if err155 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.Bean(context.Background(), value0))
    fmt.Print("\n")
    break
  case "rechargeFee":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "RechargeFee requires 1 args")
      flag.Usage()
    }
    arg156 := flag.Arg(1)
    mbTrans157 := thrift.NewTMemoryBufferLen(len(arg156))
    defer mbTrans157.Close()
    _, err158 := mbTrans157.WriteString(arg156)
    if err158 != nil {
      Usage()
      return
    }
    factory159 := thrift.NewTJSONProtocolFactory()
    jsProt160 := factory159.GetProtocol(mbTrans157)
    argvalue0 := platform_consume_service.NewRechargeFeeVO()
    err161 := argvalue0.Read(context.Background(), jsProt160)
    if err161 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.RechargeFee(context.Background(), value0))
    fmt.Print("\n")
    break
  case "addStorageConsumeLog":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "AddStorageConsumeLog requires 1 args")
      flag.Usage()
    }
    arg162 := flag.Arg(1)
    mbTrans163 := thrift.NewTMemoryBufferLen(len(arg162))
    defer mbTrans163.Close()
    _, err164 := mbTrans163.WriteString(arg162)
    if err164 != nil {
      Usage()
      return
    }
    factory165 := thrift.NewTJSONProtocolFactory()
    jsProt166 := factory165.GetProtocol(mbTrans163)
    argvalue0 := platform_consume_service.NewStorageConsumeVO()
    err167 := argvalue0.Read(context.Background(), jsProt166)
    if err167 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.AddStorageConsumeLog(context.Background(), value0))
    fmt.Print("\n")
    break
  case "getUserMoney":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetUserMoney requires 1 args")
      flag.Usage()
    }
    argvalue0, err168 := (strconv.ParseInt(flag.Arg(1), 10, 64))
    if err168 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetUserMoney(context.Background(), value0))
    fmt.Print("\n")
    break
  case "albumConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "AlbumConsume requires 1 args")
      flag.Usage()
    }
    arg169 := flag.Arg(1)
    mbTrans170 := thrift.NewTMemoryBufferLen(len(arg169))
    defer mbTrans170.Close()
    _, err171 := mbTrans170.WriteString(arg169)
    if err171 != nil {
      Usage()
      return
    }
    factory172 := thrift.NewTJSONProtocolFactory()
    jsProt173 := factory172.GetProtocol(mbTrans170)
    argvalue0 := platform_consume_service.NewAlbumConsume()
    err174 := argvalue0.Read(context.Background(), jsProt173)
    if err174 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.AlbumConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "fixBean":
    if flag.NArg() - 1 != 3 {
      fmt.Fprintln(os.Stderr, "FixBean requires 3 args")
      flag.Usage()
    }
    tmp0, err175 := (strconv.Atoi(flag.Arg(1)))
    if err175 != nil {
      Usage()
      return
    }
    argvalue0 := int32(tmp0)
    value0 := argvalue0
    argvalue1, err176 := (strconv.ParseInt(flag.Arg(2), 10, 64))
    if err176 != nil {
      Usage()
      return
    }
    value1 := argvalue1
    argvalue2, err177 := (strconv.ParseInt(flag.Arg(3), 10, 64))
    if err177 != nil {
      Usage()
      return
    }
    value2 := argvalue2
    fmt.Print(client.FixBean(context.Background(), value0, value1, value2))
    fmt.Print("\n")
    break
  case "storageConsume":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "StorageConsume requires 1 args")
      flag.Usage()
    }
    arg178 := flag.Arg(1)
    mbTrans179 := thrift.NewTMemoryBufferLen(len(arg178))
    defer mbTrans179.Close()
    _, err180 := mbTrans179.WriteString(arg178)
    if err180 != nil {
      Usage()
      return
    }
    factory181 := thrift.NewTJSONProtocolFactory()
    jsProt182 := factory181.GetProtocol(mbTrans179)
    argvalue0 := platform_consume_service.NewStorageVO()
    err183 := argvalue0.Read(context.Background(), jsProt182)
    if err183 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.StorageConsume(context.Background(), value0))
    fmt.Print("\n")
    break
  case "queryAppConfig":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "QueryAppConfig requires 1 args")
      flag.Usage()
    }
    arg184 := flag.Arg(1)
    mbTrans185 := thrift.NewTMemoryBufferLen(len(arg184))
    defer mbTrans185.Close()
    _, err186 := mbTrans185.WriteString(arg184)
    if err186 != nil {
      Usage()
      return
    }
    factory187 := thrift.NewTJSONProtocolFactory()
    jsProt188 := factory187.GetProtocol(mbTrans185)
    argvalue0 := platform_consume_service.NewConfigRequest()
    err189 := argvalue0.Read(context.Background(), jsProt188)
    if err189 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.QueryAppConfig(context.Background(), value0))
    fmt.Print("\n")
    break
  case "queryBeanDivideInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "QueryBeanDivideInfo requires 1 args")
      flag.Usage()
    }
    arg190 := flag.Arg(1)
    mbTrans191 := thrift.NewTMemoryBufferLen(len(arg190))
    defer mbTrans191.Close()
    _, err192 := mbTrans191.WriteString(arg190)
    if err192 != nil {
      Usage()
      return
    }
    factory193 := thrift.NewTJSONProtocolFactory()
    jsProt194 := factory193.GetProtocol(mbTrans191)
    argvalue0 := platform_consume_service.NewBeanDivideQueryRequest()
    err195 := argvalue0.Read(context.Background(), jsProt194)
    if err195 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.QueryBeanDivideInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
