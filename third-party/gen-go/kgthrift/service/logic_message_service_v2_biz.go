// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"kugou_adapter_service/third-party/gen-go/kgthrift/types"
	"kugou_adapter_service/third-party/gen-go/type_logic_batch"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

type AppDispatchServiceV2 interface { //JSON协议推送服务
	//
	//服务发现配置：
	//appDispatchServiceV2.applicationName = platform_socket_message
	//appDispatchServiceV2.protocol = thrift
	//appDispatchServiceV2.className = com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2
	//appDispatchServiceV2.url = /socket/v2/message.thrift
	//

	// 特殊通道
	// 推送消息到指定房间ID
	//
	//
	// Parameters:
	//  - CmdId
	//  - RoomId
	//  - Content
	//  - MsgOpt
	SendToRoomWithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error)
	// 特殊通道
	// 推送消息到指定房间ID、指定酷狗ID
	//
	//
	// Parameters:
	//  - CmdId
	//  - RoomId
	//  - ToKid
	//  - Content
	//  - MsgOpt
	SendToUserWithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, toKid int64, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error)
	// 特殊通道
	// 推送消息到批量房间ID
	//
	//
	// Parameters:
	//  - CmdId
	//  - RoomIds
	//  - Content
	//  - MsgOpt
	SendToMultiRoomWithSpecialChannel(ctx context.Context, cmdId int32, roomIds []int32, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error)
	// 特殊通道
	// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
	// 推送消息到指定房间ID，附加用户EXT信息
	//
	//
	// Parameters:
	//  - CmdId
	//  - RoomId
	//  - FromUser
	//  - Content
	//  - MsgOpt
	SendToRoomFromUserV2WithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, fromUser *types.FromUserV2, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error)
	// 特殊通道
	// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
	// 推送消息到指定房间ID、指定酷狗ID，附加用户EXT信息
	//
	//
	// Parameters:
	//  - CmdId
	//  - RoomId
	//  - ToKid
	//  - FromUser
	//  - Content
	//  - MsgOpt
	SendToUserFromUserV2WithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, toKid int64, fromUser *types.FromUserV2, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error)
	// 推送消息到全部直播间
	//
	//
	// Parameters:
	//  - BcId
	//  - BcKey
	//  - CmdId
	//  - Content
	//  - MsgOption
	SendToAll(ctx context.Context, bcId int32, bcKey string, cmdId int32, content []byte, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error)
	// * 推送消息到指定房间ID
	//    *
	//
	// Parameters:
	//  - RoomId
	//  - CmdId
	//  - Content
	//  - MsgOption
	SendToRoom(ctx context.Context, roomId int32, cmdId int32, content []byte, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error)
	// * 推送消息到指定房间ID、指定酷狗ID
	//    *
	//
	// Parameters:
	//  - RoomId
	//  - ToKid
	//  - CmdId
	//  - Content
	//  - MsgOption
	SendToUser(ctx context.Context, roomId int32, toKid int64, cmdId int32, content []byte, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error)
	// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
	// 推送消息到全部直播间，附加用户EXT信息
	//
	//
	// Parameters:
	//  - BcId
	//  - BcKey
	//  - CmdId
	//  - Content
	//  - FromUser
	//  - MsgOption
	SendToAllFromUserV2(ctx context.Context, bcId int32, bcKey string, cmdId int32, content []byte, fromUser *types.FromUserV2, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error)
	// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
	// 推送消息到指定房间ID，附加用户EXT信息
	//
	//
	// Parameters:
	//  - RoomId
	//  - CmdId
	//  - Content
	//  - FromUser
	//  - MsgOption
	SendToRoomFromUserV2(ctx context.Context, roomId int32, cmdId int32, content []byte, fromUser *types.FromUserV2, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error)
	// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
	// 推送消息到指定房间ID、指定酷狗ID，附加用户EXT信息
	//
	//
	// Parameters:
	//  - RoomId
	//  - ToKid
	//  - CmdId
	//  - Content
	//  - FromUser
	//  - MsgOption
	SendToUserFromUserV2(ctx context.Context, roomId int32, toKid int64, cmdId int32, content []byte, fromUser *types.FromUserV2, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error)
	// 特殊通道
	//  批量 推送消息给指定kugouId（该用户在连接的任意房间均可收到）
	//
	//
	// Parameters:
	//  - BatchMessages
	SendToUserByKidBatchWithSpecialChannel(ctx context.Context, batchMessages []*types.BatchMessageSpecial) (_r *types.BoolResponse, _err error)
	// Parameters:
	//  - Request
	BatchSendToRoomMessage(ctx context.Context, request *type_logic_batch.LogicBatchRoomMessageRequest) (_r *types.BoolResponse, _err error)
	// Parameters:
	//  - Request
	BatchSendToUserMessage(ctx context.Context, request *type_logic_batch.LogicBatchUserMessageRequest) (_r *types.BoolResponse, _err error)
	// Parameters:
	//  - Request
	BatchSendToUserMessageWithoutRoomId(ctx context.Context, request *type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest) (_r *types.BoolResponse, _err error)
}

// JSON协议推送服务
//
// 服务发现配置：
// appDispatchServiceV2.applicationName = platform_socket_message
// appDispatchServiceV2.protocol = thrift
// appDispatchServiceV2.className = com.kugou.fanxing.thrift.acksocket.gather.service.AppDispatchServiceV2
// appDispatchServiceV2.url = /socket/v2/message.thrift
type AppDispatchServiceV2Client struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewAppDispatchServiceV2ClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AppDispatchServiceV2Client {
	return &AppDispatchServiceV2Client{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewAppDispatchServiceV2ClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AppDispatchServiceV2Client {
	return &AppDispatchServiceV2Client{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewAppDispatchServiceV2Client(c thrift.TClient) *AppDispatchServiceV2Client {
	return &AppDispatchServiceV2Client{
		c: c,
	}
}

func (p *AppDispatchServiceV2Client) Client_() thrift.TClient {
	return p.c
}

func (p *AppDispatchServiceV2Client) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *AppDispatchServiceV2Client) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// 特殊通道
// 推送消息到指定房间ID
//
// Parameters:
//   - CmdId
//   - RoomId
//   - Content
//   - MsgOpt
func (p *AppDispatchServiceV2Client) SendToRoomWithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error) {
	var _args0 AppDispatchServiceV2SendToRoomWithSpecialChannelArgs
	_args0.CmdId = cmdId
	_args0.RoomId = roomId
	_args0.Content = content
	_args0.MsgOpt = msgOpt
	var _result2 AppDispatchServiceV2SendToRoomWithSpecialChannelResult
	var _meta1 thrift.ResponseMeta
	_meta1, _err = p.Client_().Call(ctx, "sendToRoomWithSpecialChannel", &_args0, &_result2)
	p.SetLastResponseMeta_(_meta1)
	if _err != nil {
		return
	}
	if _ret3 := _result2.GetSuccess(); _ret3 != nil {
		return _ret3, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToRoomWithSpecialChannel failed: unknown result")
}

// 特殊通道
// 推送消息到指定房间ID、指定酷狗ID
//
// Parameters:
//   - CmdId
//   - RoomId
//   - ToKid
//   - Content
//   - MsgOpt
func (p *AppDispatchServiceV2Client) SendToUserWithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, toKid int64, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error) {
	var _args4 AppDispatchServiceV2SendToUserWithSpecialChannelArgs
	_args4.CmdId = cmdId
	_args4.RoomId = roomId
	_args4.ToKid = toKid
	_args4.Content = content
	_args4.MsgOpt = msgOpt
	var _result6 AppDispatchServiceV2SendToUserWithSpecialChannelResult
	var _meta5 thrift.ResponseMeta
	_meta5, _err = p.Client_().Call(ctx, "sendToUserWithSpecialChannel", &_args4, &_result6)
	p.SetLastResponseMeta_(_meta5)
	if _err != nil {
		return
	}
	if _ret7 := _result6.GetSuccess(); _ret7 != nil {
		return _ret7, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToUserWithSpecialChannel failed: unknown result")
}

// 特殊通道
// 推送消息到批量房间ID
//
// Parameters:
//   - CmdId
//   - RoomIds
//   - Content
//   - MsgOpt
func (p *AppDispatchServiceV2Client) SendToMultiRoomWithSpecialChannel(ctx context.Context, cmdId int32, roomIds []int32, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error) {
	var _args8 AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs
	_args8.CmdId = cmdId
	_args8.RoomIds = roomIds
	_args8.Content = content
	_args8.MsgOpt = msgOpt
	var _result10 AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult
	var _meta9 thrift.ResponseMeta
	_meta9, _err = p.Client_().Call(ctx, "sendToMultiRoomWithSpecialChannel", &_args8, &_result10)
	p.SetLastResponseMeta_(_meta9)
	if _err != nil {
		return
	}
	if _ret11 := _result10.GetSuccess(); _ret11 != nil {
		return _ret11, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToMultiRoomWithSpecialChannel failed: unknown result")
}

// 特殊通道
// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
// 推送消息到指定房间ID，附加用户EXT信息
//
// Parameters:
//   - CmdId
//   - RoomId
//   - FromUser
//   - Content
//   - MsgOpt
func (p *AppDispatchServiceV2Client) SendToRoomFromUserV2WithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, fromUser *types.FromUserV2, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error) {
	var _args12 AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs
	_args12.CmdId = cmdId
	_args12.RoomId = roomId
	_args12.FromUser = fromUser
	_args12.Content = content
	_args12.MsgOpt = msgOpt
	var _result14 AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult
	var _meta13 thrift.ResponseMeta
	_meta13, _err = p.Client_().Call(ctx, "sendToRoomFromUserV2WithSpecialChannel", &_args12, &_result14)
	p.SetLastResponseMeta_(_meta13)
	if _err != nil {
		return
	}
	if _ret15 := _result14.GetSuccess(); _ret15 != nil {
		return _ret15, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToRoomFromUserV2WithSpecialChannel failed: unknown result")
}

// 特殊通道
// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
// 推送消息到指定房间ID、指定酷狗ID，附加用户EXT信息
//
// Parameters:
//   - CmdId
//   - RoomId
//   - ToKid
//   - FromUser
//   - Content
//   - MsgOpt
func (p *AppDispatchServiceV2Client) SendToUserFromUserV2WithSpecialChannel(ctx context.Context, cmdId int32, roomId int32, toKid int64, fromUser *types.FromUserV2, content []byte, msgOpt *types.MsgOpt) (_r *types.BoolResponse, _err error) {
	var _args16 AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs
	_args16.CmdId = cmdId
	_args16.RoomId = roomId
	_args16.ToKid = toKid
	_args16.FromUser = fromUser
	_args16.Content = content
	_args16.MsgOpt = msgOpt
	var _result18 AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult
	var _meta17 thrift.ResponseMeta
	_meta17, _err = p.Client_().Call(ctx, "sendToUserFromUserV2WithSpecialChannel", &_args16, &_result18)
	p.SetLastResponseMeta_(_meta17)
	if _err != nil {
		return
	}
	if _ret19 := _result18.GetSuccess(); _ret19 != nil {
		return _ret19, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToUserFromUserV2WithSpecialChannel failed: unknown result")
}

// 推送消息到全部直播间
//
// Parameters:
//   - BcId
//   - BcKey
//   - CmdId
//   - Content
//   - MsgOption
func (p *AppDispatchServiceV2Client) SendToAll(ctx context.Context, bcId int32, bcKey string, cmdId int32, content []byte, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error) {
	var _args20 AppDispatchServiceV2SendToAllArgs
	_args20.BcId = bcId
	_args20.BcKey = bcKey
	_args20.CmdId = cmdId
	_args20.Content = content
	_args20.MsgOption = msgOption
	var _result22 AppDispatchServiceV2SendToAllResult
	var _meta21 thrift.ResponseMeta
	_meta21, _err = p.Client_().Call(ctx, "sendToAll", &_args20, &_result22)
	p.SetLastResponseMeta_(_meta21)
	if _err != nil {
		return
	}
	if _ret23 := _result22.GetSuccess(); _ret23 != nil {
		return _ret23, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToAll failed: unknown result")
}

//   - 推送消息到指定房间ID
//     *
//
// Parameters:
//   - RoomId
//   - CmdId
//   - Content
//   - MsgOption
func (p *AppDispatchServiceV2Client) SendToRoom(ctx context.Context, roomId int32, cmdId int32, content []byte, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error) {
	var _args24 AppDispatchServiceV2SendToRoomArgs
	_args24.RoomId = roomId
	_args24.CmdId = cmdId
	_args24.Content = content
	_args24.MsgOption = msgOption
	var _result26 AppDispatchServiceV2SendToRoomResult
	var _meta25 thrift.ResponseMeta
	_meta25, _err = p.Client_().Call(ctx, "sendToRoom", &_args24, &_result26)
	p.SetLastResponseMeta_(_meta25)
	if _err != nil {
		return
	}
	if _ret27 := _result26.GetSuccess(); _ret27 != nil {
		return _ret27, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToRoom failed: unknown result")
}

//   - 推送消息到指定房间ID、指定酷狗ID
//     *
//
// Parameters:
//   - RoomId
//   - ToKid
//   - CmdId
//   - Content
//   - MsgOption
func (p *AppDispatchServiceV2Client) SendToUser(ctx context.Context, roomId int32, toKid int64, cmdId int32, content []byte, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error) {
	var _args28 AppDispatchServiceV2SendToUserArgs
	_args28.RoomId = roomId
	_args28.ToKid = toKid
	_args28.CmdId = cmdId
	_args28.Content = content
	_args28.MsgOption = msgOption
	var _result30 AppDispatchServiceV2SendToUserResult
	var _meta29 thrift.ResponseMeta
	_meta29, _err = p.Client_().Call(ctx, "sendToUser", &_args28, &_result30)
	p.SetLastResponseMeta_(_meta29)
	if _err != nil {
		return
	}
	if _ret31 := _result30.GetSuccess(); _ret31 != nil {
		return _ret31, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToUser failed: unknown result")
}

// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
// 推送消息到全部直播间，附加用户EXT信息
//
// Parameters:
//   - BcId
//   - BcKey
//   - CmdId
//   - Content
//   - FromUser
//   - MsgOption
func (p *AppDispatchServiceV2Client) SendToAllFromUserV2(ctx context.Context, bcId int32, bcKey string, cmdId int32, content []byte, fromUser *types.FromUserV2, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error) {
	var _args32 AppDispatchServiceV2SendToAllFromUserV2Args
	_args32.BcId = bcId
	_args32.BcKey = bcKey
	_args32.CmdId = cmdId
	_args32.Content = content
	_args32.FromUser = fromUser
	_args32.MsgOption = msgOption
	var _result34 AppDispatchServiceV2SendToAllFromUserV2Result
	var _meta33 thrift.ResponseMeta
	_meta33, _err = p.Client_().Call(ctx, "sendToAllFromUserV2", &_args32, &_result34)
	p.SetLastResponseMeta_(_meta33)
	if _err != nil {
		return
	}
	if _ret35 := _result34.GetSuccess(); _ret35 != nil {
		return _ret35, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToAllFromUserV2 failed: unknown result")
}

// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
// 推送消息到指定房间ID，附加用户EXT信息
//
// Parameters:
//   - RoomId
//   - CmdId
//   - Content
//   - FromUser
//   - MsgOption
func (p *AppDispatchServiceV2Client) SendToRoomFromUserV2(ctx context.Context, roomId int32, cmdId int32, content []byte, fromUser *types.FromUserV2, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error) {
	var _args36 AppDispatchServiceV2SendToRoomFromUserV2Args
	_args36.RoomId = roomId
	_args36.CmdId = cmdId
	_args36.Content = content
	_args36.FromUser = fromUser
	_args36.MsgOption = msgOption
	var _result38 AppDispatchServiceV2SendToRoomFromUserV2Result
	var _meta37 thrift.ResponseMeta
	_meta37, _err = p.Client_().Call(ctx, "sendToRoomFromUserV2", &_args36, &_result38)
	p.SetLastResponseMeta_(_meta37)
	if _err != nil {
		return
	}
	if _ret39 := _result38.GetSuccess(); _ret39 != nil {
		return _ret39, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToRoomFromUserV2 failed: unknown result")
}

// 大部分情况下，调用方不需要调用该接口，如有需要请联系服务提供方
// 推送消息到指定房间ID、指定酷狗ID，附加用户EXT信息
//
// Parameters:
//   - RoomId
//   - ToKid
//   - CmdId
//   - Content
//   - FromUser
//   - MsgOption
func (p *AppDispatchServiceV2Client) SendToUserFromUserV2(ctx context.Context, roomId int32, toKid int64, cmdId int32, content []byte, fromUser *types.FromUserV2, msgOption *types.MsgOption) (_r *types.BoolResponse, _err error) {
	var _args40 AppDispatchServiceV2SendToUserFromUserV2Args
	_args40.RoomId = roomId
	_args40.ToKid = toKid
	_args40.CmdId = cmdId
	_args40.Content = content
	_args40.FromUser = fromUser
	_args40.MsgOption = msgOption
	var _result42 AppDispatchServiceV2SendToUserFromUserV2Result
	var _meta41 thrift.ResponseMeta
	_meta41, _err = p.Client_().Call(ctx, "sendToUserFromUserV2", &_args40, &_result42)
	p.SetLastResponseMeta_(_meta41)
	if _err != nil {
		return
	}
	if _ret43 := _result42.GetSuccess(); _ret43 != nil {
		return _ret43, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToUserFromUserV2 failed: unknown result")
}

// 特殊通道
//
//	批量 推送消息给指定kugouId（该用户在连接的任意房间均可收到）
//
// Parameters:
//   - BatchMessages
func (p *AppDispatchServiceV2Client) SendToUserByKidBatchWithSpecialChannel(ctx context.Context, batchMessages []*types.BatchMessageSpecial) (_r *types.BoolResponse, _err error) {
	var _args44 AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs
	_args44.BatchMessages = batchMessages
	var _result46 AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult
	var _meta45 thrift.ResponseMeta
	_meta45, _err = p.Client_().Call(ctx, "sendToUserByKidBatchWithSpecialChannel", &_args44, &_result46)
	p.SetLastResponseMeta_(_meta45)
	if _err != nil {
		return
	}
	if _ret47 := _result46.GetSuccess(); _ret47 != nil {
		return _ret47, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendToUserByKidBatchWithSpecialChannel failed: unknown result")
}

// Parameters:
//   - Request
func (p *AppDispatchServiceV2Client) BatchSendToRoomMessage(ctx context.Context, request *type_logic_batch.LogicBatchRoomMessageRequest) (_r *types.BoolResponse, _err error) {
	var _args48 AppDispatchServiceV2BatchSendToRoomMessageArgs
	_args48.Request = request
	var _result50 AppDispatchServiceV2BatchSendToRoomMessageResult
	var _meta49 thrift.ResponseMeta
	_meta49, _err = p.Client_().Call(ctx, "batchSendToRoomMessage", &_args48, &_result50)
	p.SetLastResponseMeta_(_meta49)
	if _err != nil {
		return
	}
	if _ret51 := _result50.GetSuccess(); _ret51 != nil {
		return _ret51, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "batchSendToRoomMessage failed: unknown result")
}

// Parameters:
//   - Request
func (p *AppDispatchServiceV2Client) BatchSendToUserMessage(ctx context.Context, request *type_logic_batch.LogicBatchUserMessageRequest) (_r *types.BoolResponse, _err error) {
	var _args52 AppDispatchServiceV2BatchSendToUserMessageArgs
	_args52.Request = request
	var _result54 AppDispatchServiceV2BatchSendToUserMessageResult
	var _meta53 thrift.ResponseMeta
	_meta53, _err = p.Client_().Call(ctx, "batchSendToUserMessage", &_args52, &_result54)
	p.SetLastResponseMeta_(_meta53)
	if _err != nil {
		return
	}
	if _ret55 := _result54.GetSuccess(); _ret55 != nil {
		return _ret55, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "batchSendToUserMessage failed: unknown result")
}

// Parameters:
//   - Request
func (p *AppDispatchServiceV2Client) BatchSendToUserMessageWithoutRoomId(ctx context.Context, request *type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest) (_r *types.BoolResponse, _err error) {
	var _args56 AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs
	_args56.Request = request
	var _result58 AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult
	var _meta57 thrift.ResponseMeta
	_meta57, _err = p.Client_().Call(ctx, "batchSendToUserMessageWithoutRoomId", &_args56, &_result58)
	p.SetLastResponseMeta_(_meta57)
	if _err != nil {
		return
	}
	if _ret59 := _result58.GetSuccess(); _ret59 != nil {
		return _ret59, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "batchSendToUserMessageWithoutRoomId failed: unknown result")
}

type AppDispatchServiceV2Processor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AppDispatchServiceV2
}

func (p *AppDispatchServiceV2Processor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AppDispatchServiceV2Processor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AppDispatchServiceV2Processor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAppDispatchServiceV2Processor(handler AppDispatchServiceV2) *AppDispatchServiceV2Processor {

	self60 := &AppDispatchServiceV2Processor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self60.processorMap["sendToRoomWithSpecialChannel"] = &appDispatchServiceV2ProcessorSendToRoomWithSpecialChannel{handler: handler}
	self60.processorMap["sendToUserWithSpecialChannel"] = &appDispatchServiceV2ProcessorSendToUserWithSpecialChannel{handler: handler}
	self60.processorMap["sendToMultiRoomWithSpecialChannel"] = &appDispatchServiceV2ProcessorSendToMultiRoomWithSpecialChannel{handler: handler}
	self60.processorMap["sendToRoomFromUserV2WithSpecialChannel"] = &appDispatchServiceV2ProcessorSendToRoomFromUserV2WithSpecialChannel{handler: handler}
	self60.processorMap["sendToUserFromUserV2WithSpecialChannel"] = &appDispatchServiceV2ProcessorSendToUserFromUserV2WithSpecialChannel{handler: handler}
	self60.processorMap["sendToAll"] = &appDispatchServiceV2ProcessorSendToAll{handler: handler}
	self60.processorMap["sendToRoom"] = &appDispatchServiceV2ProcessorSendToRoom{handler: handler}
	self60.processorMap["sendToUser"] = &appDispatchServiceV2ProcessorSendToUser{handler: handler}
	self60.processorMap["sendToAllFromUserV2"] = &appDispatchServiceV2ProcessorSendToAllFromUserV2{handler: handler}
	self60.processorMap["sendToRoomFromUserV2"] = &appDispatchServiceV2ProcessorSendToRoomFromUserV2{handler: handler}
	self60.processorMap["sendToUserFromUserV2"] = &appDispatchServiceV2ProcessorSendToUserFromUserV2{handler: handler}
	self60.processorMap["sendToUserByKidBatchWithSpecialChannel"] = &appDispatchServiceV2ProcessorSendToUserByKidBatchWithSpecialChannel{handler: handler}
	self60.processorMap["batchSendToRoomMessage"] = &appDispatchServiceV2ProcessorBatchSendToRoomMessage{handler: handler}
	self60.processorMap["batchSendToUserMessage"] = &appDispatchServiceV2ProcessorBatchSendToUserMessage{handler: handler}
	self60.processorMap["batchSendToUserMessageWithoutRoomId"] = &appDispatchServiceV2ProcessorBatchSendToUserMessageWithoutRoomId{handler: handler}
	return self60
}

func (p *AppDispatchServiceV2Processor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x61 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x61.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x61

}

type appDispatchServiceV2ProcessorSendToRoomWithSpecialChannel struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToRoomWithSpecialChannel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err62 error
	args := AppDispatchServiceV2SendToRoomWithSpecialChannelArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToRoomWithSpecialChannel", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToRoomWithSpecialChannelResult{}
	if retval, err2 := p.handler.SendToRoomWithSpecialChannel(ctx, args.CmdId, args.RoomId, args.Content, args.MsgOpt); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc63 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToRoomWithSpecialChannel: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToRoomWithSpecialChannel", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err62 = thrift.WrapTException(err2)
		}
		if err2 := _exc63.Write(ctx, oprot); _write_err62 == nil && err2 != nil {
			_write_err62 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err62 == nil && err2 != nil {
			_write_err62 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err62 == nil && err2 != nil {
			_write_err62 = thrift.WrapTException(err2)
		}
		if _write_err62 != nil {
			return false, thrift.WrapTException(_write_err62)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToRoomWithSpecialChannel", thrift.REPLY, seqId); err2 != nil {
		_write_err62 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err62 == nil && err2 != nil {
		_write_err62 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err62 == nil && err2 != nil {
		_write_err62 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err62 == nil && err2 != nil {
		_write_err62 = thrift.WrapTException(err2)
	}
	if _write_err62 != nil {
		return false, thrift.WrapTException(_write_err62)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToUserWithSpecialChannel struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToUserWithSpecialChannel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err64 error
	args := AppDispatchServiceV2SendToUserWithSpecialChannelArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToUserWithSpecialChannel", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToUserWithSpecialChannelResult{}
	if retval, err2 := p.handler.SendToUserWithSpecialChannel(ctx, args.CmdId, args.RoomId, args.ToKid, args.Content, args.MsgOpt); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc65 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToUserWithSpecialChannel: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToUserWithSpecialChannel", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err64 = thrift.WrapTException(err2)
		}
		if err2 := _exc65.Write(ctx, oprot); _write_err64 == nil && err2 != nil {
			_write_err64 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err64 == nil && err2 != nil {
			_write_err64 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err64 == nil && err2 != nil {
			_write_err64 = thrift.WrapTException(err2)
		}
		if _write_err64 != nil {
			return false, thrift.WrapTException(_write_err64)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToUserWithSpecialChannel", thrift.REPLY, seqId); err2 != nil {
		_write_err64 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err64 == nil && err2 != nil {
		_write_err64 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err64 == nil && err2 != nil {
		_write_err64 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err64 == nil && err2 != nil {
		_write_err64 = thrift.WrapTException(err2)
	}
	if _write_err64 != nil {
		return false, thrift.WrapTException(_write_err64)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToMultiRoomWithSpecialChannel struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToMultiRoomWithSpecialChannel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err66 error
	args := AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToMultiRoomWithSpecialChannel", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult{}
	if retval, err2 := p.handler.SendToMultiRoomWithSpecialChannel(ctx, args.CmdId, args.RoomIds, args.Content, args.MsgOpt); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc67 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToMultiRoomWithSpecialChannel: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToMultiRoomWithSpecialChannel", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err66 = thrift.WrapTException(err2)
		}
		if err2 := _exc67.Write(ctx, oprot); _write_err66 == nil && err2 != nil {
			_write_err66 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err66 == nil && err2 != nil {
			_write_err66 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err66 == nil && err2 != nil {
			_write_err66 = thrift.WrapTException(err2)
		}
		if _write_err66 != nil {
			return false, thrift.WrapTException(_write_err66)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToMultiRoomWithSpecialChannel", thrift.REPLY, seqId); err2 != nil {
		_write_err66 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err66 == nil && err2 != nil {
		_write_err66 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err66 == nil && err2 != nil {
		_write_err66 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err66 == nil && err2 != nil {
		_write_err66 = thrift.WrapTException(err2)
	}
	if _write_err66 != nil {
		return false, thrift.WrapTException(_write_err66)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToRoomFromUserV2WithSpecialChannel struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToRoomFromUserV2WithSpecialChannel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err68 error
	args := AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToRoomFromUserV2WithSpecialChannel", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult{}
	if retval, err2 := p.handler.SendToRoomFromUserV2WithSpecialChannel(ctx, args.CmdId, args.RoomId, args.FromUser, args.Content, args.MsgOpt); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc69 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToRoomFromUserV2WithSpecialChannel: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToRoomFromUserV2WithSpecialChannel", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err68 = thrift.WrapTException(err2)
		}
		if err2 := _exc69.Write(ctx, oprot); _write_err68 == nil && err2 != nil {
			_write_err68 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err68 == nil && err2 != nil {
			_write_err68 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err68 == nil && err2 != nil {
			_write_err68 = thrift.WrapTException(err2)
		}
		if _write_err68 != nil {
			return false, thrift.WrapTException(_write_err68)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToRoomFromUserV2WithSpecialChannel", thrift.REPLY, seqId); err2 != nil {
		_write_err68 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err68 == nil && err2 != nil {
		_write_err68 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err68 == nil && err2 != nil {
		_write_err68 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err68 == nil && err2 != nil {
		_write_err68 = thrift.WrapTException(err2)
	}
	if _write_err68 != nil {
		return false, thrift.WrapTException(_write_err68)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToUserFromUserV2WithSpecialChannel struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToUserFromUserV2WithSpecialChannel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err70 error
	args := AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToUserFromUserV2WithSpecialChannel", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult{}
	if retval, err2 := p.handler.SendToUserFromUserV2WithSpecialChannel(ctx, args.CmdId, args.RoomId, args.ToKid, args.FromUser, args.Content, args.MsgOpt); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc71 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToUserFromUserV2WithSpecialChannel: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToUserFromUserV2WithSpecialChannel", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err70 = thrift.WrapTException(err2)
		}
		if err2 := _exc71.Write(ctx, oprot); _write_err70 == nil && err2 != nil {
			_write_err70 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err70 == nil && err2 != nil {
			_write_err70 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err70 == nil && err2 != nil {
			_write_err70 = thrift.WrapTException(err2)
		}
		if _write_err70 != nil {
			return false, thrift.WrapTException(_write_err70)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToUserFromUserV2WithSpecialChannel", thrift.REPLY, seqId); err2 != nil {
		_write_err70 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err70 == nil && err2 != nil {
		_write_err70 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err70 == nil && err2 != nil {
		_write_err70 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err70 == nil && err2 != nil {
		_write_err70 = thrift.WrapTException(err2)
	}
	if _write_err70 != nil {
		return false, thrift.WrapTException(_write_err70)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToAll struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToAll) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err72 error
	args := AppDispatchServiceV2SendToAllArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToAll", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToAllResult{}
	if retval, err2 := p.handler.SendToAll(ctx, args.BcId, args.BcKey, args.CmdId, args.Content, args.MsgOption); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc73 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToAll: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToAll", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err72 = thrift.WrapTException(err2)
		}
		if err2 := _exc73.Write(ctx, oprot); _write_err72 == nil && err2 != nil {
			_write_err72 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err72 == nil && err2 != nil {
			_write_err72 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err72 == nil && err2 != nil {
			_write_err72 = thrift.WrapTException(err2)
		}
		if _write_err72 != nil {
			return false, thrift.WrapTException(_write_err72)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToAll", thrift.REPLY, seqId); err2 != nil {
		_write_err72 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err72 == nil && err2 != nil {
		_write_err72 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err72 == nil && err2 != nil {
		_write_err72 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err72 == nil && err2 != nil {
		_write_err72 = thrift.WrapTException(err2)
	}
	if _write_err72 != nil {
		return false, thrift.WrapTException(_write_err72)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToRoom struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToRoom) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err74 error
	args := AppDispatchServiceV2SendToRoomArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToRoom", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToRoomResult{}
	if retval, err2 := p.handler.SendToRoom(ctx, args.RoomId, args.CmdId, args.Content, args.MsgOption); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc75 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToRoom: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToRoom", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err74 = thrift.WrapTException(err2)
		}
		if err2 := _exc75.Write(ctx, oprot); _write_err74 == nil && err2 != nil {
			_write_err74 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err74 == nil && err2 != nil {
			_write_err74 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err74 == nil && err2 != nil {
			_write_err74 = thrift.WrapTException(err2)
		}
		if _write_err74 != nil {
			return false, thrift.WrapTException(_write_err74)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToRoom", thrift.REPLY, seqId); err2 != nil {
		_write_err74 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err74 == nil && err2 != nil {
		_write_err74 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err74 == nil && err2 != nil {
		_write_err74 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err74 == nil && err2 != nil {
		_write_err74 = thrift.WrapTException(err2)
	}
	if _write_err74 != nil {
		return false, thrift.WrapTException(_write_err74)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToUser struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToUser) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err76 error
	args := AppDispatchServiceV2SendToUserArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToUser", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToUserResult{}
	if retval, err2 := p.handler.SendToUser(ctx, args.RoomId, args.ToKid, args.CmdId, args.Content, args.MsgOption); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc77 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToUser: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToUser", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err76 = thrift.WrapTException(err2)
		}
		if err2 := _exc77.Write(ctx, oprot); _write_err76 == nil && err2 != nil {
			_write_err76 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err76 == nil && err2 != nil {
			_write_err76 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err76 == nil && err2 != nil {
			_write_err76 = thrift.WrapTException(err2)
		}
		if _write_err76 != nil {
			return false, thrift.WrapTException(_write_err76)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToUser", thrift.REPLY, seqId); err2 != nil {
		_write_err76 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err76 == nil && err2 != nil {
		_write_err76 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err76 == nil && err2 != nil {
		_write_err76 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err76 == nil && err2 != nil {
		_write_err76 = thrift.WrapTException(err2)
	}
	if _write_err76 != nil {
		return false, thrift.WrapTException(_write_err76)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToAllFromUserV2 struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToAllFromUserV2) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err78 error
	args := AppDispatchServiceV2SendToAllFromUserV2Args{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToAllFromUserV2", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToAllFromUserV2Result{}
	if retval, err2 := p.handler.SendToAllFromUserV2(ctx, args.BcId, args.BcKey, args.CmdId, args.Content, args.FromUser, args.MsgOption); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc79 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToAllFromUserV2: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToAllFromUserV2", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err78 = thrift.WrapTException(err2)
		}
		if err2 := _exc79.Write(ctx, oprot); _write_err78 == nil && err2 != nil {
			_write_err78 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err78 == nil && err2 != nil {
			_write_err78 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err78 == nil && err2 != nil {
			_write_err78 = thrift.WrapTException(err2)
		}
		if _write_err78 != nil {
			return false, thrift.WrapTException(_write_err78)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToAllFromUserV2", thrift.REPLY, seqId); err2 != nil {
		_write_err78 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err78 == nil && err2 != nil {
		_write_err78 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err78 == nil && err2 != nil {
		_write_err78 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err78 == nil && err2 != nil {
		_write_err78 = thrift.WrapTException(err2)
	}
	if _write_err78 != nil {
		return false, thrift.WrapTException(_write_err78)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToRoomFromUserV2 struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToRoomFromUserV2) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err80 error
	args := AppDispatchServiceV2SendToRoomFromUserV2Args{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToRoomFromUserV2", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToRoomFromUserV2Result{}
	if retval, err2 := p.handler.SendToRoomFromUserV2(ctx, args.RoomId, args.CmdId, args.Content, args.FromUser, args.MsgOption); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc81 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToRoomFromUserV2: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToRoomFromUserV2", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err80 = thrift.WrapTException(err2)
		}
		if err2 := _exc81.Write(ctx, oprot); _write_err80 == nil && err2 != nil {
			_write_err80 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err80 == nil && err2 != nil {
			_write_err80 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err80 == nil && err2 != nil {
			_write_err80 = thrift.WrapTException(err2)
		}
		if _write_err80 != nil {
			return false, thrift.WrapTException(_write_err80)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToRoomFromUserV2", thrift.REPLY, seqId); err2 != nil {
		_write_err80 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err80 == nil && err2 != nil {
		_write_err80 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err80 == nil && err2 != nil {
		_write_err80 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err80 == nil && err2 != nil {
		_write_err80 = thrift.WrapTException(err2)
	}
	if _write_err80 != nil {
		return false, thrift.WrapTException(_write_err80)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToUserFromUserV2 struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToUserFromUserV2) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err82 error
	args := AppDispatchServiceV2SendToUserFromUserV2Args{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToUserFromUserV2", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToUserFromUserV2Result{}
	if retval, err2 := p.handler.SendToUserFromUserV2(ctx, args.RoomId, args.ToKid, args.CmdId, args.Content, args.FromUser, args.MsgOption); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc83 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToUserFromUserV2: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToUserFromUserV2", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err82 = thrift.WrapTException(err2)
		}
		if err2 := _exc83.Write(ctx, oprot); _write_err82 == nil && err2 != nil {
			_write_err82 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err82 == nil && err2 != nil {
			_write_err82 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err82 == nil && err2 != nil {
			_write_err82 = thrift.WrapTException(err2)
		}
		if _write_err82 != nil {
			return false, thrift.WrapTException(_write_err82)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToUserFromUserV2", thrift.REPLY, seqId); err2 != nil {
		_write_err82 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err82 == nil && err2 != nil {
		_write_err82 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err82 == nil && err2 != nil {
		_write_err82 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err82 == nil && err2 != nil {
		_write_err82 = thrift.WrapTException(err2)
	}
	if _write_err82 != nil {
		return false, thrift.WrapTException(_write_err82)
	}
	return true, err
}

type appDispatchServiceV2ProcessorSendToUserByKidBatchWithSpecialChannel struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorSendToUserByKidBatchWithSpecialChannel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err84 error
	args := AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "sendToUserByKidBatchWithSpecialChannel", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult{}
	if retval, err2 := p.handler.SendToUserByKidBatchWithSpecialChannel(ctx, args.BatchMessages); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc85 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendToUserByKidBatchWithSpecialChannel: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "sendToUserByKidBatchWithSpecialChannel", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err84 = thrift.WrapTException(err2)
		}
		if err2 := _exc85.Write(ctx, oprot); _write_err84 == nil && err2 != nil {
			_write_err84 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err84 == nil && err2 != nil {
			_write_err84 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err84 == nil && err2 != nil {
			_write_err84 = thrift.WrapTException(err2)
		}
		if _write_err84 != nil {
			return false, thrift.WrapTException(_write_err84)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "sendToUserByKidBatchWithSpecialChannel", thrift.REPLY, seqId); err2 != nil {
		_write_err84 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err84 == nil && err2 != nil {
		_write_err84 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err84 == nil && err2 != nil {
		_write_err84 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err84 == nil && err2 != nil {
		_write_err84 = thrift.WrapTException(err2)
	}
	if _write_err84 != nil {
		return false, thrift.WrapTException(_write_err84)
	}
	return true, err
}

type appDispatchServiceV2ProcessorBatchSendToRoomMessage struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorBatchSendToRoomMessage) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err86 error
	args := AppDispatchServiceV2BatchSendToRoomMessageArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "batchSendToRoomMessage", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2BatchSendToRoomMessageResult{}
	if retval, err2 := p.handler.BatchSendToRoomMessage(ctx, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc87 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchSendToRoomMessage: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "batchSendToRoomMessage", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err86 = thrift.WrapTException(err2)
		}
		if err2 := _exc87.Write(ctx, oprot); _write_err86 == nil && err2 != nil {
			_write_err86 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err86 == nil && err2 != nil {
			_write_err86 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err86 == nil && err2 != nil {
			_write_err86 = thrift.WrapTException(err2)
		}
		if _write_err86 != nil {
			return false, thrift.WrapTException(_write_err86)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "batchSendToRoomMessage", thrift.REPLY, seqId); err2 != nil {
		_write_err86 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err86 == nil && err2 != nil {
		_write_err86 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err86 == nil && err2 != nil {
		_write_err86 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err86 == nil && err2 != nil {
		_write_err86 = thrift.WrapTException(err2)
	}
	if _write_err86 != nil {
		return false, thrift.WrapTException(_write_err86)
	}
	return true, err
}

type appDispatchServiceV2ProcessorBatchSendToUserMessage struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorBatchSendToUserMessage) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err88 error
	args := AppDispatchServiceV2BatchSendToUserMessageArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "batchSendToUserMessage", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2BatchSendToUserMessageResult{}
	if retval, err2 := p.handler.BatchSendToUserMessage(ctx, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc89 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchSendToUserMessage: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "batchSendToUserMessage", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err88 = thrift.WrapTException(err2)
		}
		if err2 := _exc89.Write(ctx, oprot); _write_err88 == nil && err2 != nil {
			_write_err88 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err88 == nil && err2 != nil {
			_write_err88 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err88 == nil && err2 != nil {
			_write_err88 = thrift.WrapTException(err2)
		}
		if _write_err88 != nil {
			return false, thrift.WrapTException(_write_err88)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "batchSendToUserMessage", thrift.REPLY, seqId); err2 != nil {
		_write_err88 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err88 == nil && err2 != nil {
		_write_err88 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err88 == nil && err2 != nil {
		_write_err88 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err88 == nil && err2 != nil {
		_write_err88 = thrift.WrapTException(err2)
	}
	if _write_err88 != nil {
		return false, thrift.WrapTException(_write_err88)
	}
	return true, err
}

type appDispatchServiceV2ProcessorBatchSendToUserMessageWithoutRoomId struct {
	handler AppDispatchServiceV2
}

func (p *appDispatchServiceV2ProcessorBatchSendToUserMessageWithoutRoomId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err90 error
	args := AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "batchSendToUserMessageWithoutRoomId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult{}
	if retval, err2 := p.handler.BatchSendToUserMessageWithoutRoomId(ctx, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc91 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchSendToUserMessageWithoutRoomId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "batchSendToUserMessageWithoutRoomId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err90 = thrift.WrapTException(err2)
		}
		if err2 := _exc91.Write(ctx, oprot); _write_err90 == nil && err2 != nil {
			_write_err90 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err90 == nil && err2 != nil {
			_write_err90 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err90 == nil && err2 != nil {
			_write_err90 = thrift.WrapTException(err2)
		}
		if _write_err90 != nil {
			return false, thrift.WrapTException(_write_err90)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "batchSendToUserMessageWithoutRoomId", thrift.REPLY, seqId); err2 != nil {
		_write_err90 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err90 == nil && err2 != nil {
		_write_err90 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err90 == nil && err2 != nil {
		_write_err90 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err90 == nil && err2 != nil {
		_write_err90 = thrift.WrapTException(err2)
	}
	if _write_err90 != nil {
		return false, thrift.WrapTException(_write_err90)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//   - CmdId
//   - RoomId
//   - Content
//   - MsgOpt
type AppDispatchServiceV2SendToRoomWithSpecialChannelArgs struct {
	CmdId   int32         `thrift:"cmdId,1" db:"cmdId" json:"cmdId"`
	RoomId  int32         `thrift:"roomId,2" db:"roomId" json:"roomId"`
	Content []byte        `thrift:"content,3" db:"content" json:"content"`
	MsgOpt  *types.MsgOpt `thrift:"msgOpt,4" db:"msgOpt" json:"msgOpt"`
}

func NewAppDispatchServiceV2SendToRoomWithSpecialChannelArgs() *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs {
	return &AppDispatchServiceV2SendToRoomWithSpecialChannelArgs{}
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToRoomWithSpecialChannelArgs_MsgOpt_DEFAULT *types.MsgOpt

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) GetMsgOpt() *types.MsgOpt {
	if !p.IsSetMsgOpt() {
		return AppDispatchServiceV2SendToRoomWithSpecialChannelArgs_MsgOpt_DEFAULT
	}
	return p.MsgOpt
}
func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) IsSetMsgOpt() bool {
	return p.MsgOpt != nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOpt = &types.MsgOpt{}
	if err := p.MsgOpt.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOpt), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoomWithSpecialChannel_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOpt", thrift.STRUCT, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:msgOpt: ", p), err)
	}
	if err := p.MsgOpt.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOpt), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:msgOpt: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomWithSpecialChannelArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToRoomWithSpecialChannelResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToRoomWithSpecialChannelResult() *AppDispatchServiceV2SendToRoomWithSpecialChannelResult {
	return &AppDispatchServiceV2SendToRoomWithSpecialChannelResult{}
}

var AppDispatchServiceV2SendToRoomWithSpecialChannelResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToRoomWithSpecialChannelResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoomWithSpecialChannel_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomWithSpecialChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomWithSpecialChannelResult(%+v)", *p)
}

// Attributes:
//   - CmdId
//   - RoomId
//   - ToKid
//   - Content
//   - MsgOpt
type AppDispatchServiceV2SendToUserWithSpecialChannelArgs struct {
	CmdId   int32         `thrift:"cmdId,1" db:"cmdId" json:"cmdId"`
	RoomId  int32         `thrift:"roomId,2" db:"roomId" json:"roomId"`
	ToKid   int64         `thrift:"toKid,3" db:"toKid" json:"toKid"`
	Content []byte        `thrift:"content,4" db:"content" json:"content"`
	MsgOpt  *types.MsgOpt `thrift:"msgOpt,5" db:"msgOpt" json:"msgOpt"`
}

func NewAppDispatchServiceV2SendToUserWithSpecialChannelArgs() *AppDispatchServiceV2SendToUserWithSpecialChannelArgs {
	return &AppDispatchServiceV2SendToUserWithSpecialChannelArgs{}
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) GetToKid() int64 {
	return p.ToKid
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToUserWithSpecialChannelArgs_MsgOpt_DEFAULT *types.MsgOpt

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) GetMsgOpt() *types.MsgOpt {
	if !p.IsSetMsgOpt() {
		return AppDispatchServiceV2SendToUserWithSpecialChannelArgs_MsgOpt_DEFAULT
	}
	return p.MsgOpt
}
func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) IsSetMsgOpt() bool {
	return p.MsgOpt != nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOpt = &types.MsgOpt{}
	if err := p.MsgOpt.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOpt), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserWithSpecialChannel_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:toKid: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOpt", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOpt: ", p), err)
	}
	if err := p.MsgOpt.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOpt), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOpt: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserWithSpecialChannelArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToUserWithSpecialChannelResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToUserWithSpecialChannelResult() *AppDispatchServiceV2SendToUserWithSpecialChannelResult {
	return &AppDispatchServiceV2SendToUserWithSpecialChannelResult{}
}

var AppDispatchServiceV2SendToUserWithSpecialChannelResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToUserWithSpecialChannelResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserWithSpecialChannel_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserWithSpecialChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserWithSpecialChannelResult(%+v)", *p)
}

// Attributes:
//   - CmdId
//   - RoomIds
//   - Content
//   - MsgOpt
type AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs struct {
	CmdId   int32         `thrift:"cmdId,1" db:"cmdId" json:"cmdId"`
	RoomIds []int32       `thrift:"roomIds,2" db:"roomIds" json:"roomIds"`
	Content []byte        `thrift:"content,3" db:"content" json:"content"`
	MsgOpt  *types.MsgOpt `thrift:"msgOpt,4" db:"msgOpt" json:"msgOpt"`
}

func NewAppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs() *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs {
	return &AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs{}
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) GetRoomIds() []int32 {
	return p.RoomIds
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs_MsgOpt_DEFAULT *types.MsgOpt

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) GetMsgOpt() *types.MsgOpt {
	if !p.IsSetMsgOpt() {
		return AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs_MsgOpt_DEFAULT
	}
	return p.MsgOpt
}
func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) IsSetMsgOpt() bool {
	return p.MsgOpt != nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int32, 0, size)
	p.RoomIds = tSlice
	for i := 0; i < size; i++ {
		var _elem92 int32
		if v, err := iprot.ReadI32(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem92 = v
		}
		p.RoomIds = append(p.RoomIds, _elem92)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOpt = &types.MsgOpt{}
	if err := p.MsgOpt.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOpt), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToMultiRoomWithSpecialChannel_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomIds", thrift.LIST, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomIds: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I32, len(p.RoomIds)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.RoomIds {
		if err := oprot.WriteI32(ctx, int32(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomIds: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOpt", thrift.STRUCT, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:msgOpt: ", p), err)
	}
	if err := p.MsgOpt.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOpt), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:msgOpt: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToMultiRoomWithSpecialChannelArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult() *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult {
	return &AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult{}
}

var AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToMultiRoomWithSpecialChannel_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToMultiRoomWithSpecialChannelResult(%+v)", *p)
}

// Attributes:
//   - CmdId
//   - RoomId
//   - FromUser
//   - Content
//   - MsgOpt
type AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs struct {
	CmdId    int32             `thrift:"cmdId,1" db:"cmdId" json:"cmdId"`
	RoomId   int32             `thrift:"roomId,2" db:"roomId" json:"roomId"`
	FromUser *types.FromUserV2 `thrift:"fromUser,3" db:"fromUser" json:"fromUser"`
	Content  []byte            `thrift:"content,4" db:"content" json:"content"`
	MsgOpt   *types.MsgOpt     `thrift:"msgOpt,5" db:"msgOpt" json:"msgOpt"`
}

func NewAppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs() *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs {
	return &AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs{}
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) GetRoomId() int32 {
	return p.RoomId
}

var AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs_FromUser_DEFAULT *types.FromUserV2

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) GetFromUser() *types.FromUserV2 {
	if !p.IsSetFromUser() {
		return AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs_FromUser_DEFAULT
	}
	return p.FromUser
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs_MsgOpt_DEFAULT *types.MsgOpt

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) GetMsgOpt() *types.MsgOpt {
	if !p.IsSetMsgOpt() {
		return AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs_MsgOpt_DEFAULT
	}
	return p.MsgOpt
}
func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) IsSetFromUser() bool {
	return p.FromUser != nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) IsSetMsgOpt() bool {
	return p.MsgOpt != nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	p.FromUser = &types.FromUserV2{}
	if err := p.FromUser.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.FromUser), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOpt = &types.MsgOpt{}
	if err := p.MsgOpt.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOpt), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoomFromUserV2WithSpecialChannel_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "fromUser", thrift.STRUCT, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:fromUser: ", p), err)
	}
	if err := p.FromUser.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.FromUser), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:fromUser: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOpt", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOpt: ", p), err)
	}
	if err := p.MsgOpt.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOpt), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOpt: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult() *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult {
	return &AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult{}
}

var AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoomFromUserV2WithSpecialChannel_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomFromUserV2WithSpecialChannelResult(%+v)", *p)
}

// Attributes:
//   - CmdId
//   - RoomId
//   - ToKid
//   - FromUser
//   - Content
//   - MsgOpt
type AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs struct {
	CmdId    int32             `thrift:"cmdId,1" db:"cmdId" json:"cmdId"`
	RoomId   int32             `thrift:"roomId,2" db:"roomId" json:"roomId"`
	ToKid    int64             `thrift:"toKid,3" db:"toKid" json:"toKid"`
	FromUser *types.FromUserV2 `thrift:"fromUser,4" db:"fromUser" json:"fromUser"`
	Content  []byte            `thrift:"content,5" db:"content" json:"content"`
	MsgOpt   *types.MsgOpt     `thrift:"msgOpt,6" db:"msgOpt" json:"msgOpt"`
}

func NewAppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs() *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs {
	return &AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs{}
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) GetToKid() int64 {
	return p.ToKid
}

var AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs_FromUser_DEFAULT *types.FromUserV2

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) GetFromUser() *types.FromUserV2 {
	if !p.IsSetFromUser() {
		return AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs_FromUser_DEFAULT
	}
	return p.FromUser
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs_MsgOpt_DEFAULT *types.MsgOpt

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) GetMsgOpt() *types.MsgOpt {
	if !p.IsSetMsgOpt() {
		return AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs_MsgOpt_DEFAULT
	}
	return p.MsgOpt
}
func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) IsSetFromUser() bool {
	return p.FromUser != nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) IsSetMsgOpt() bool {
	return p.MsgOpt != nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	p.FromUser = &types.FromUserV2{}
	if err := p.FromUser.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.FromUser), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOpt = &types.MsgOpt{}
	if err := p.MsgOpt.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOpt), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserFromUserV2WithSpecialChannel_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:toKid: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "fromUser", thrift.STRUCT, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:fromUser: ", p), err)
	}
	if err := p.FromUser.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.FromUser), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:fromUser: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOpt", thrift.STRUCT, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:msgOpt: ", p), err)
	}
	if err := p.MsgOpt.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOpt), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:msgOpt: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult() *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult {
	return &AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult{}
}

var AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserFromUserV2WithSpecialChannel_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserFromUserV2WithSpecialChannelResult(%+v)", *p)
}

// Attributes:
//   - BcId
//   - BcKey
//   - CmdId
//   - Content
//   - MsgOption
type AppDispatchServiceV2SendToAllArgs struct {
	BcId      int32            `thrift:"bcId,1" db:"bcId" json:"bcId"`
	BcKey     string           `thrift:"bcKey,2" db:"bcKey" json:"bcKey"`
	CmdId     int32            `thrift:"cmdId,3" db:"cmdId" json:"cmdId"`
	Content   []byte           `thrift:"content,4" db:"content" json:"content"`
	MsgOption *types.MsgOption `thrift:"msgOption,5" db:"msgOption" json:"msgOption"`
}

func NewAppDispatchServiceV2SendToAllArgs() *AppDispatchServiceV2SendToAllArgs {
	return &AppDispatchServiceV2SendToAllArgs{}
}

func (p *AppDispatchServiceV2SendToAllArgs) GetBcId() int32 {
	return p.BcId
}

func (p *AppDispatchServiceV2SendToAllArgs) GetBcKey() string {
	return p.BcKey
}

func (p *AppDispatchServiceV2SendToAllArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToAllArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToAllArgs_MsgOption_DEFAULT *types.MsgOption

func (p *AppDispatchServiceV2SendToAllArgs) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return AppDispatchServiceV2SendToAllArgs_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *AppDispatchServiceV2SendToAllArgs) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *AppDispatchServiceV2SendToAllArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.BcId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.BcKey = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToAll_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "bcId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:bcId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.BcId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.bcId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:bcId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "bcKey", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:bcKey: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.BcKey)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.bcKey (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:bcKey: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllArgs) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToAllArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToAllResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToAllResult() *AppDispatchServiceV2SendToAllResult {
	return &AppDispatchServiceV2SendToAllResult{}
}

var AppDispatchServiceV2SendToAllResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToAllResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToAllResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToAllResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToAllResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToAll_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToAllResult(%+v)", *p)
}

// Attributes:
//   - RoomId
//   - CmdId
//   - Content
//   - MsgOption
type AppDispatchServiceV2SendToRoomArgs struct {
	RoomId    int32            `thrift:"roomId,1" db:"roomId" json:"roomId"`
	CmdId     int32            `thrift:"cmdId,2" db:"cmdId" json:"cmdId"`
	Content   []byte           `thrift:"content,3" db:"content" json:"content"`
	MsgOption *types.MsgOption `thrift:"msgOption,4" db:"msgOption" json:"msgOption"`
}

func NewAppDispatchServiceV2SendToRoomArgs() *AppDispatchServiceV2SendToRoomArgs {
	return &AppDispatchServiceV2SendToRoomArgs{}
}

func (p *AppDispatchServiceV2SendToRoomArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToRoomArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToRoomArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToRoomArgs_MsgOption_DEFAULT *types.MsgOption

func (p *AppDispatchServiceV2SendToRoomArgs) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return AppDispatchServiceV2SendToRoomArgs_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *AppDispatchServiceV2SendToRoomArgs) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoom_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:msgOption: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToRoomResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToRoomResult() *AppDispatchServiceV2SendToRoomResult {
	return &AppDispatchServiceV2SendToRoomResult{}
}

var AppDispatchServiceV2SendToRoomResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToRoomResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToRoomResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToRoomResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToRoomResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoom_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomResult(%+v)", *p)
}

// Attributes:
//   - RoomId
//   - ToKid
//   - CmdId
//   - Content
//   - MsgOption
type AppDispatchServiceV2SendToUserArgs struct {
	RoomId    int32            `thrift:"roomId,1" db:"roomId" json:"roomId"`
	ToKid     int64            `thrift:"toKid,2" db:"toKid" json:"toKid"`
	CmdId     int32            `thrift:"cmdId,3" db:"cmdId" json:"cmdId"`
	Content   []byte           `thrift:"content,4" db:"content" json:"content"`
	MsgOption *types.MsgOption `thrift:"msgOption,5" db:"msgOption" json:"msgOption"`
}

func NewAppDispatchServiceV2SendToUserArgs() *AppDispatchServiceV2SendToUserArgs {
	return &AppDispatchServiceV2SendToUserArgs{}
}

func (p *AppDispatchServiceV2SendToUserArgs) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToUserArgs) GetToKid() int64 {
	return p.ToKid
}

func (p *AppDispatchServiceV2SendToUserArgs) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToUserArgs) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToUserArgs_MsgOption_DEFAULT *types.MsgOption

func (p *AppDispatchServiceV2SendToUserArgs) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return AppDispatchServiceV2SendToUserArgs_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *AppDispatchServiceV2SendToUserArgs) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *AppDispatchServiceV2SendToUserArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUser_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:toKid: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserArgs) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserArgs) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToUserResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToUserResult() *AppDispatchServiceV2SendToUserResult {
	return &AppDispatchServiceV2SendToUserResult{}
}

var AppDispatchServiceV2SendToUserResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToUserResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToUserResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToUserResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToUserResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUser_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserResult(%+v)", *p)
}

// Attributes:
//   - BcId
//   - BcKey
//   - CmdId
//   - Content
//   - FromUser
//   - MsgOption
type AppDispatchServiceV2SendToAllFromUserV2Args struct {
	BcId      int32             `thrift:"bcId,1" db:"bcId" json:"bcId"`
	BcKey     string            `thrift:"bcKey,2" db:"bcKey" json:"bcKey"`
	CmdId     int32             `thrift:"cmdId,3" db:"cmdId" json:"cmdId"`
	Content   []byte            `thrift:"content,4" db:"content" json:"content"`
	FromUser  *types.FromUserV2 `thrift:"fromUser,5" db:"fromUser" json:"fromUser"`
	MsgOption *types.MsgOption  `thrift:"msgOption,6" db:"msgOption" json:"msgOption"`
}

func NewAppDispatchServiceV2SendToAllFromUserV2Args() *AppDispatchServiceV2SendToAllFromUserV2Args {
	return &AppDispatchServiceV2SendToAllFromUserV2Args{}
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) GetBcId() int32 {
	return p.BcId
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) GetBcKey() string {
	return p.BcKey
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToAllFromUserV2Args_FromUser_DEFAULT *types.FromUserV2

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) GetFromUser() *types.FromUserV2 {
	if !p.IsSetFromUser() {
		return AppDispatchServiceV2SendToAllFromUserV2Args_FromUser_DEFAULT
	}
	return p.FromUser
}

var AppDispatchServiceV2SendToAllFromUserV2Args_MsgOption_DEFAULT *types.MsgOption

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return AppDispatchServiceV2SendToAllFromUserV2Args_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *AppDispatchServiceV2SendToAllFromUserV2Args) IsSetFromUser() bool {
	return p.FromUser != nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.BcId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.BcKey = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.FromUser = &types.FromUserV2{}
	if err := p.FromUser.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.FromUser), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToAllFromUserV2_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "bcId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:bcId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.BcId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.bcId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:bcId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "bcKey", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:bcKey: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.BcKey)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.bcKey (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:bcKey: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "fromUser", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:fromUser: ", p), err)
	}
	if err := p.FromUser.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.FromUser), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:fromUser: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:msgOption: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Args) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToAllFromUserV2Args(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToAllFromUserV2Result struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToAllFromUserV2Result() *AppDispatchServiceV2SendToAllFromUserV2Result {
	return &AppDispatchServiceV2SendToAllFromUserV2Result{}
}

var AppDispatchServiceV2SendToAllFromUserV2Result_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToAllFromUserV2Result) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToAllFromUserV2Result_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToAllFromUserV2Result) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Result) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Result) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Result) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToAllFromUserV2_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Result) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToAllFromUserV2Result) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToAllFromUserV2Result(%+v)", *p)
}

// Attributes:
//   - RoomId
//   - CmdId
//   - Content
//   - FromUser
//   - MsgOption
type AppDispatchServiceV2SendToRoomFromUserV2Args struct {
	RoomId    int32             `thrift:"roomId,1" db:"roomId" json:"roomId"`
	CmdId     int32             `thrift:"cmdId,2" db:"cmdId" json:"cmdId"`
	Content   []byte            `thrift:"content,3" db:"content" json:"content"`
	FromUser  *types.FromUserV2 `thrift:"fromUser,4" db:"fromUser" json:"fromUser"`
	MsgOption *types.MsgOption  `thrift:"msgOption,5" db:"msgOption" json:"msgOption"`
}

func NewAppDispatchServiceV2SendToRoomFromUserV2Args() *AppDispatchServiceV2SendToRoomFromUserV2Args {
	return &AppDispatchServiceV2SendToRoomFromUserV2Args{}
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToRoomFromUserV2Args_FromUser_DEFAULT *types.FromUserV2

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) GetFromUser() *types.FromUserV2 {
	if !p.IsSetFromUser() {
		return AppDispatchServiceV2SendToRoomFromUserV2Args_FromUser_DEFAULT
	}
	return p.FromUser
}

var AppDispatchServiceV2SendToRoomFromUserV2Args_MsgOption_DEFAULT *types.MsgOption

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return AppDispatchServiceV2SendToRoomFromUserV2Args_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) IsSetFromUser() bool {
	return p.FromUser != nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	p.FromUser = &types.FromUserV2{}
	if err := p.FromUser.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.FromUser), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoomFromUserV2_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "fromUser", thrift.STRUCT, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:fromUser: ", p), err)
	}
	if err := p.FromUser.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.FromUser), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:fromUser: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Args) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomFromUserV2Args(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToRoomFromUserV2Result struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToRoomFromUserV2Result() *AppDispatchServiceV2SendToRoomFromUserV2Result {
	return &AppDispatchServiceV2SendToRoomFromUserV2Result{}
}

var AppDispatchServiceV2SendToRoomFromUserV2Result_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToRoomFromUserV2Result_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToRoomFromUserV2_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToRoomFromUserV2Result) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToRoomFromUserV2Result(%+v)", *p)
}

// Attributes:
//   - RoomId
//   - ToKid
//   - CmdId
//   - Content
//   - FromUser
//   - MsgOption
type AppDispatchServiceV2SendToUserFromUserV2Args struct {
	RoomId    int32             `thrift:"roomId,1" db:"roomId" json:"roomId"`
	ToKid     int64             `thrift:"toKid,2" db:"toKid" json:"toKid"`
	CmdId     int32             `thrift:"cmdId,3" db:"cmdId" json:"cmdId"`
	Content   []byte            `thrift:"content,4" db:"content" json:"content"`
	FromUser  *types.FromUserV2 `thrift:"fromUser,5" db:"fromUser" json:"fromUser"`
	MsgOption *types.MsgOption  `thrift:"msgOption,6" db:"msgOption" json:"msgOption"`
}

func NewAppDispatchServiceV2SendToUserFromUserV2Args() *AppDispatchServiceV2SendToUserFromUserV2Args {
	return &AppDispatchServiceV2SendToUserFromUserV2Args{}
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) GetRoomId() int32 {
	return p.RoomId
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) GetToKid() int64 {
	return p.ToKid
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) GetCmdId() int32 {
	return p.CmdId
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) GetContent() []byte {
	return p.Content
}

var AppDispatchServiceV2SendToUserFromUserV2Args_FromUser_DEFAULT *types.FromUserV2

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) GetFromUser() *types.FromUserV2 {
	if !p.IsSetFromUser() {
		return AppDispatchServiceV2SendToUserFromUserV2Args_FromUser_DEFAULT
	}
	return p.FromUser
}

var AppDispatchServiceV2SendToUserFromUserV2Args_MsgOption_DEFAULT *types.MsgOption

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) GetMsgOption() *types.MsgOption {
	if !p.IsSetMsgOption() {
		return AppDispatchServiceV2SendToUserFromUserV2Args_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *AppDispatchServiceV2SendToUserFromUserV2Args) IsSetFromUser() bool {
	return p.FromUser != nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.FromUser = &types.FromUserV2{}
	if err := p.FromUser.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.FromUser), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &types.MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserFromUserV2_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:toKid: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:cmdId: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:content: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "fromUser", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:fromUser: ", p), err)
	}
	if err := p.FromUser.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.FromUser), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:fromUser: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:msgOption: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Args) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserFromUserV2Args(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToUserFromUserV2Result struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToUserFromUserV2Result() *AppDispatchServiceV2SendToUserFromUserV2Result {
	return &AppDispatchServiceV2SendToUserFromUserV2Result{}
}

var AppDispatchServiceV2SendToUserFromUserV2Result_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToUserFromUserV2Result) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToUserFromUserV2Result_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToUserFromUserV2Result) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Result) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Result) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Result) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserFromUserV2_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Result) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserFromUserV2Result) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserFromUserV2Result(%+v)", *p)
}

// Attributes:
//   - BatchMessages
type AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs struct {
	BatchMessages []*types.BatchMessageSpecial `thrift:"batchMessages,1" db:"batchMessages" json:"batchMessages"`
}

func NewAppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs() *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs {
	return &AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs{}
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs) GetBatchMessages() []*types.BatchMessageSpecial {
	return p.BatchMessages
}
func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]*types.BatchMessageSpecial, 0, size)
	p.BatchMessages = tSlice
	for i := 0; i < size; i++ {
		_elem93 := &types.BatchMessageSpecial{}
		if err := _elem93.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem93), err)
		}
		p.BatchMessages = append(p.BatchMessages, _elem93)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserByKidBatchWithSpecialChannel_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "batchMessages", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:batchMessages: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.BatchMessages)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.BatchMessages {
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:batchMessages: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult() *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult {
	return &AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult{}
}

var AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "sendToUserByKidBatchWithSpecialChannel_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2SendToUserByKidBatchWithSpecialChannelResult(%+v)", *p)
}

// Attributes:
//   - Request
type AppDispatchServiceV2BatchSendToRoomMessageArgs struct {
	Request *type_logic_batch.LogicBatchRoomMessageRequest `thrift:"request,1" db:"request" json:"request"`
}

func NewAppDispatchServiceV2BatchSendToRoomMessageArgs() *AppDispatchServiceV2BatchSendToRoomMessageArgs {
	return &AppDispatchServiceV2BatchSendToRoomMessageArgs{}
}

var AppDispatchServiceV2BatchSendToRoomMessageArgs_Request_DEFAULT *type_logic_batch.LogicBatchRoomMessageRequest

func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) GetRequest() *type_logic_batch.LogicBatchRoomMessageRequest {
	if !p.IsSetRequest() {
		return AppDispatchServiceV2BatchSendToRoomMessageArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &type_logic_batch.LogicBatchRoomMessageRequest{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchSendToRoomMessage_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2BatchSendToRoomMessageArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2BatchSendToRoomMessageResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2BatchSendToRoomMessageResult() *AppDispatchServiceV2BatchSendToRoomMessageResult {
	return &AppDispatchServiceV2BatchSendToRoomMessageResult{}
}

var AppDispatchServiceV2BatchSendToRoomMessageResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2BatchSendToRoomMessageResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchSendToRoomMessage_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2BatchSendToRoomMessageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2BatchSendToRoomMessageResult(%+v)", *p)
}

// Attributes:
//   - Request
type AppDispatchServiceV2BatchSendToUserMessageArgs struct {
	Request *type_logic_batch.LogicBatchUserMessageRequest `thrift:"request,1" db:"request" json:"request"`
}

func NewAppDispatchServiceV2BatchSendToUserMessageArgs() *AppDispatchServiceV2BatchSendToUserMessageArgs {
	return &AppDispatchServiceV2BatchSendToUserMessageArgs{}
}

var AppDispatchServiceV2BatchSendToUserMessageArgs_Request_DEFAULT *type_logic_batch.LogicBatchUserMessageRequest

func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) GetRequest() *type_logic_batch.LogicBatchUserMessageRequest {
	if !p.IsSetRequest() {
		return AppDispatchServiceV2BatchSendToUserMessageArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &type_logic_batch.LogicBatchUserMessageRequest{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchSendToUserMessage_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2BatchSendToUserMessageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2BatchSendToUserMessageArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2BatchSendToUserMessageResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2BatchSendToUserMessageResult() *AppDispatchServiceV2BatchSendToUserMessageResult {
	return &AppDispatchServiceV2BatchSendToUserMessageResult{}
}

var AppDispatchServiceV2BatchSendToUserMessageResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2BatchSendToUserMessageResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2BatchSendToUserMessageResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2BatchSendToUserMessageResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchSendToUserMessage_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2BatchSendToUserMessageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2BatchSendToUserMessageResult(%+v)", *p)
}

// Attributes:
//   - Request
type AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs struct {
	Request *type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest `thrift:"request,1" db:"request" json:"request"`
}

func NewAppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs() *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs {
	return &AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs{}
}

var AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs_Request_DEFAULT *type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) GetRequest() *type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest {
	if !p.IsSetRequest() {
		return AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &type_logic_batch.LogicBatchUserMessageWithoutRoomIdRequest{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchSendToUserMessageWithoutRoomId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err)
	}
	return err
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult struct {
	Success *types.BoolResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewAppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult() *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult {
	return &AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult{}
}

var AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult_Success_DEFAULT *types.BoolResponse

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) GetSuccess() *types.BoolResponse {
	if !p.IsSetSuccess() {
		return AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &types.BoolResponse{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchSendToUserMessageWithoutRoomId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppDispatchServiceV2BatchSendToUserMessageWithoutRoomIdResult(%+v)", *p)
}
