// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package types

import (
	"bytes"
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

type DispatchErrCode int64

const (
	DispatchErrCode_NONE               DispatchErrCode = -1
	DispatchErrCode_Suc                DispatchErrCode = 0
	DispatchErrCode_ServerErr          DispatchErrCode = 1
	DispatchErrCode_InvalidKid         DispatchErrCode = 2
	DispatchErrCode_InvalidUid         DispatchErrCode = 3
	DispatchErrCode_InvalidBcId        DispatchErrCode = 4
	DispatchErrCode_InvalidRoomId      DispatchErrCode = 5
	DispatchErrCode_InvalidAppId       DispatchErrCode = 6
	DispatchErrCode_UserNotFound       DispatchErrCode = 7
	DispatchErrCode_InvalidEndTime     DispatchErrCode = 8
	DispatchErrCode_InvalidClientIds   DispatchErrCode = 9
	DispatchErrCode_InvalidSpecialCmd  DispatchErrCode = 10
	DispatchErrCode_InvalidCmdId       DispatchErrCode = 11
	DispatchErrCode_InvalidContent     DispatchErrCode = 12
	DispatchErrCode_InvalidGid         DispatchErrCode = 13
	DispatchErrCode_InvalidParameter   DispatchErrCode = 14
	DispatchErrCode_InvalidBatchSize   DispatchErrCode = 15
	DispatchErrCode_InvalidRoomIdsSize DispatchErrCode = 16
	DispatchErrCode_InvalidCmdConfig   DispatchErrCode = 17
)

func (p DispatchErrCode) String() string {
	switch p {
	case DispatchErrCode_NONE:
		return "NONE"
	case DispatchErrCode_Suc:
		return "Suc"
	case DispatchErrCode_ServerErr:
		return "ServerErr"
	case DispatchErrCode_InvalidKid:
		return "InvalidKid"
	case DispatchErrCode_InvalidUid:
		return "InvalidUid"
	case DispatchErrCode_InvalidBcId:
		return "InvalidBcId"
	case DispatchErrCode_InvalidRoomId:
		return "InvalidRoomId"
	case DispatchErrCode_InvalidAppId:
		return "InvalidAppId"
	case DispatchErrCode_UserNotFound:
		return "UserNotFound"
	case DispatchErrCode_InvalidEndTime:
		return "InvalidEndTime"
	case DispatchErrCode_InvalidClientIds:
		return "InvalidClientIds"
	case DispatchErrCode_InvalidSpecialCmd:
		return "InvalidSpecialCmd"
	case DispatchErrCode_InvalidCmdId:
		return "InvalidCmdId"
	case DispatchErrCode_InvalidContent:
		return "InvalidContent"
	case DispatchErrCode_InvalidGid:
		return "InvalidGid"
	case DispatchErrCode_InvalidParameter:
		return "InvalidParameter"
	case DispatchErrCode_InvalidBatchSize:
		return "InvalidBatchSize"
	case DispatchErrCode_InvalidRoomIdsSize:
		return "InvalidRoomIdsSize"
	case DispatchErrCode_InvalidCmdConfig:
		return "InvalidCmdConfig"
	}
	return "<UNSET>"
}

func DispatchErrCodeFromString(s string) (DispatchErrCode, error) {
	switch s {
	case "NONE":
		return DispatchErrCode_NONE, nil
	case "Suc":
		return DispatchErrCode_Suc, nil
	case "ServerErr":
		return DispatchErrCode_ServerErr, nil
	case "InvalidKid":
		return DispatchErrCode_InvalidKid, nil
	case "InvalidUid":
		return DispatchErrCode_InvalidUid, nil
	case "InvalidBcId":
		return DispatchErrCode_InvalidBcId, nil
	case "InvalidRoomId":
		return DispatchErrCode_InvalidRoomId, nil
	case "InvalidAppId":
		return DispatchErrCode_InvalidAppId, nil
	case "UserNotFound":
		return DispatchErrCode_UserNotFound, nil
	case "InvalidEndTime":
		return DispatchErrCode_InvalidEndTime, nil
	case "InvalidClientIds":
		return DispatchErrCode_InvalidClientIds, nil
	case "InvalidSpecialCmd":
		return DispatchErrCode_InvalidSpecialCmd, nil
	case "InvalidCmdId":
		return DispatchErrCode_InvalidCmdId, nil
	case "InvalidContent":
		return DispatchErrCode_InvalidContent, nil
	case "InvalidGid":
		return DispatchErrCode_InvalidGid, nil
	case "InvalidParameter":
		return DispatchErrCode_InvalidParameter, nil
	case "InvalidBatchSize":
		return DispatchErrCode_InvalidBatchSize, nil
	case "InvalidRoomIdsSize":
		return DispatchErrCode_InvalidRoomIdsSize, nil
	case "InvalidCmdConfig":
		return DispatchErrCode_InvalidCmdConfig, nil
	}
	return DispatchErrCode(0), fmt.Errorf("not a valid DispatchErrCode string")
}

func DispatchErrCodePtr(v DispatchErrCode) *DispatchErrCode { return &v }

func (p DispatchErrCode) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *DispatchErrCode) UnmarshalText(text []byte) error {
	q, err := DispatchErrCodeFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

func (p *DispatchErrCode) Scan(value interface{}) error {
	v, ok := value.(int64)
	if !ok {
		return errors.New("Scan value is not int64")
	}
	*p = DispatchErrCode(v)
	return nil
}

func (p *DispatchErrCode) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type MsgFrom int64

const (
	MsgFrom_Default   MsgFrom = 0
	MsgFrom_NewSocket MsgFrom = 1
	MsgFrom_AckSocket MsgFrom = 2
)

func (p MsgFrom) String() string {
	switch p {
	case MsgFrom_Default:
		return "Default"
	case MsgFrom_NewSocket:
		return "NewSocket"
	case MsgFrom_AckSocket:
		return "AckSocket"
	}
	return "<UNSET>"
}

func MsgFromFromString(s string) (MsgFrom, error) {
	switch s {
	case "Default":
		return MsgFrom_Default, nil
	case "NewSocket":
		return MsgFrom_NewSocket, nil
	case "AckSocket":
		return MsgFrom_AckSocket, nil
	}
	return MsgFrom(0), fmt.Errorf("not a valid MsgFrom string")
}

func MsgFromPtr(v MsgFrom) *MsgFrom { return &v }

func (p MsgFrom) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *MsgFrom) UnmarshalText(text []byte) error {
	q, err := MsgFromFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

func (p *MsgFrom) Scan(value interface{}) error {
	v, ok := value.(int64)
	if !ok {
		return errors.New("Scan value is not int64")
	}
	*p = MsgFrom(v)
	return nil
}

func (p *MsgFrom) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type MsgSource int64

const (
	MsgSource_System MsgSource = 0
	MsgSource_Nsq    MsgSource = 1
	MsgSource_Thrift MsgSource = 2
)

func (p MsgSource) String() string {
	switch p {
	case MsgSource_System:
		return "System"
	case MsgSource_Nsq:
		return "Nsq"
	case MsgSource_Thrift:
		return "Thrift"
	}
	return "<UNSET>"
}

func MsgSourceFromString(s string) (MsgSource, error) {
	switch s {
	case "System":
		return MsgSource_System, nil
	case "Nsq":
		return MsgSource_Nsq, nil
	case "Thrift":
		return MsgSource_Thrift, nil
	}
	return MsgSource(0), fmt.Errorf("not a valid MsgSource string")
}

func MsgSourcePtr(v MsgSource) *MsgSource { return &v }

func (p MsgSource) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *MsgSource) UnmarshalText(text []byte) error {
	q, err := MsgSourceFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

func (p *MsgSource) Scan(value interface{}) error {
	v, ok := value.(int64)
	if !ok {
		return errors.New("Scan value is not int64")
	}
	*p = MsgSource(v)
	return nil
}

func (p *MsgSource) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

// Attributes:
//   - KugouId
//   - RoomId
//   - AppId
//   - UserId
//   - Ext
type FromUserV2 struct {
	KugouId int64  `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
	RoomId  int32  `thrift:"roomId,2,required" db:"roomId" json:"roomId"`
	AppId   int32  `thrift:"appId,3,required" db:"appId" json:"appId"`
	UserId  int64  `thrift:"userId,4" db:"userId" json:"userId"`
	Ext     string `thrift:"ext,5" db:"ext" json:"ext"`
}

func NewFromUserV2() *FromUserV2 {
	return &FromUserV2{}
}

func (p *FromUserV2) GetKugouId() int64 {
	return p.KugouId
}

func (p *FromUserV2) GetRoomId() int32 {
	return p.RoomId
}

func (p *FromUserV2) GetAppId() int32 {
	return p.AppId
}

var FromUserV2_UserId_DEFAULT int64 = 0

func (p *FromUserV2) GetUserId() int64 {
	return p.UserId
}

var FromUserV2_Ext_DEFAULT string = ""

func (p *FromUserV2) GetExt() string {
	return p.Ext
}
func (p *FromUserV2) IsSetUserId() bool {
	return p.UserId != FromUserV2_UserId_DEFAULT
}

func (p *FromUserV2) IsSetExt() bool {
	return p.Ext != FromUserV2_Ext_DEFAULT
}

func (p *FromUserV2) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false
	var issetRoomId bool = false
	var issetAppId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	return nil
}

func (p *FromUserV2) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *FromUserV2) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *FromUserV2) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *FromUserV2) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *FromUserV2) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Ext = v
	}
	return nil
}

func (p *FromUserV2) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "FromUserV2"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *FromUserV2) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *FromUserV2) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *FromUserV2) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err)
	}
	return err
}

func (p *FromUserV2) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetUserId() {
		if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:userId: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(p.UserId)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.userId (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:userId: ", p), err)
		}
	}
	return err
}

func (p *FromUserV2) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetExt() {
		if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:ext: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(p.Ext)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.ext (5) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:ext: ", p), err)
		}
	}
	return err
}

func (p *FromUserV2) Equals(other *FromUserV2) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.KugouId != other.KugouId {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	if p.AppId != other.AppId {
		return false
	}
	if p.UserId != other.UserId {
		return false
	}
	if p.Ext != other.Ext {
		return false
	}
	return true
}

func (p *FromUserV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FromUserV2(%+v)", *p)
}

// Attributes:
//   - KugouId
//   - RoomId
//   - AppId
//   - UserId
//   - NeedExt: 是否需要补充发送者ext
type FromUserProto struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
	RoomId  int32 `thrift:"roomId,2,required" db:"roomId" json:"roomId"`
	AppId   int32 `thrift:"appId,3,required" db:"appId" json:"appId"`
	UserId  int64 `thrift:"userId,4,required" db:"userId" json:"userId"`
	NeedExt bool  `thrift:"needExt,5" db:"needExt" json:"needExt"`
}

func NewFromUserProto() *FromUserProto {
	return &FromUserProto{}
}

func (p *FromUserProto) GetKugouId() int64 {
	return p.KugouId
}

func (p *FromUserProto) GetRoomId() int32 {
	return p.RoomId
}

func (p *FromUserProto) GetAppId() int32 {
	return p.AppId
}

func (p *FromUserProto) GetUserId() int64 {
	return p.UserId
}

var FromUserProto_NeedExt_DEFAULT bool = false

func (p *FromUserProto) GetNeedExt() bool {
	return p.NeedExt
}
func (p *FromUserProto) IsSetNeedExt() bool {
	return p.NeedExt != FromUserProto_NeedExt_DEFAULT
}

func (p *FromUserProto) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false
	var issetRoomId bool = false
	var issetAppId bool = false
	var issetUserId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetUserId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	if !issetUserId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserId is not set"))
	}
	return nil
}

func (p *FromUserProto) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *FromUserProto) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *FromUserProto) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *FromUserProto) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *FromUserProto) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.NeedExt = v
	}
	return nil
}

func (p *FromUserProto) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "FromUserProto"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *FromUserProto) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *FromUserProto) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomId: ", p), err)
	}
	return err
}

func (p *FromUserProto) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err)
	}
	return err
}

func (p *FromUserProto) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userId", thrift.I64, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:userId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.UserId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:userId: ", p), err)
	}
	return err
}

func (p *FromUserProto) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetNeedExt() {
		if err := oprot.WriteFieldBegin(ctx, "needExt", thrift.BOOL, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:needExt: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.NeedExt)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.needExt (5) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:needExt: ", p), err)
		}
	}
	return err
}

func (p *FromUserProto) Equals(other *FromUserProto) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.KugouId != other.KugouId {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	if p.AppId != other.AppId {
		return false
	}
	if p.UserId != other.UserId {
		return false
	}
	if p.NeedExt != other.NeedExt {
		return false
	}
	return true
}

func (p *FromUserProto) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FromUserProto(%+v)", *p)
}

// Attributes:
//   - Ret
//   - Msg
//   - Data
type BoolResponse struct {
	Ret  int32  `thrift:"ret,1,required" db:"ret" json:"ret"`
	Msg  string `thrift:"msg,2,required" db:"msg" json:"msg"`
	Data bool   `thrift:"data,3,required" db:"data" json:"data"`
}

func NewBoolResponse() *BoolResponse {
	return &BoolResponse{}
}

func (p *BoolResponse) GetRet() int32 {
	return p.Ret
}

func (p *BoolResponse) GetMsg() string {
	return p.Msg
}

func (p *BoolResponse) GetData() bool {
	return p.Data
}
func (p *BoolResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRet bool = false
	var issetMsg bool = false
	var issetData bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRet = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetData = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRet {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	if !issetData {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"))
	}
	return nil
}

func (p *BoolResponse) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Ret = v
	}
	return nil
}

func (p *BoolResponse) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *BoolResponse) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Data = v
	}
	return nil
}

func (p *BoolResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BoolResponse"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BoolResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err)
	}
	return err
}

func (p *BoolResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *BoolResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "data", thrift.BOOL, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.Data)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.data (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
	}
	return err
}

func (p *BoolResponse) Equals(other *BoolResponse) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Ret != other.Ret {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if p.Data != other.Data {
		return false
	}
	return true
}

func (p *BoolResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BoolResponse(%+v)", *p)
}

// Attributes:
//   - Timestamp: 发送到前端的时间截，如果你指定了时间截，将不会使用socket系统时间，单位秒
//   - Ack: 是否启用ack机制
//   - Gid: 消息全局id
//   - Rpt: 是否上报本条消息到bi(1:是,0:否)
//   - IsIdempotent: 是否幂等
//   - SenderKid: 发送者kugouId
//   - SenderUid: 发送者繁星id
type MsgOpt struct {
	Timestamp    int64  `thrift:"timestamp,1" db:"timestamp" json:"timestamp"`
	Ack          bool   `thrift:"ack,2" db:"ack" json:"ack"`
	Gid          int64  `thrift:"gid,3" db:"gid" json:"gid"`
	Rpt          int32  `thrift:"rpt,4" db:"rpt" json:"rpt"`
	IsIdempotent bool   `thrift:"isIdempotent,5" db:"isIdempotent" json:"isIdempotent"`
	SenderKid    *int64 `thrift:"senderKid,6" db:"senderKid" json:"senderKid,omitempty"`
	SenderUid    *int64 `thrift:"senderUid,7" db:"senderUid" json:"senderUid,omitempty"`
}

func NewMsgOpt() *MsgOpt {
	return &MsgOpt{}
}

var MsgOpt_Timestamp_DEFAULT int64 = 0

func (p *MsgOpt) GetTimestamp() int64 {
	return p.Timestamp
}

var MsgOpt_Ack_DEFAULT bool = false

func (p *MsgOpt) GetAck() bool {
	return p.Ack
}

var MsgOpt_Gid_DEFAULT int64 = 0

func (p *MsgOpt) GetGid() int64 {
	return p.Gid
}

var MsgOpt_Rpt_DEFAULT int32 = 0

func (p *MsgOpt) GetRpt() int32 {
	return p.Rpt
}

var MsgOpt_IsIdempotent_DEFAULT bool = false

func (p *MsgOpt) GetIsIdempotent() bool {
	return p.IsIdempotent
}

var MsgOpt_SenderKid_DEFAULT int64

func (p *MsgOpt) GetSenderKid() int64 {
	if !p.IsSetSenderKid() {
		return MsgOpt_SenderKid_DEFAULT
	}
	return *p.SenderKid
}

var MsgOpt_SenderUid_DEFAULT int64

func (p *MsgOpt) GetSenderUid() int64 {
	if !p.IsSetSenderUid() {
		return MsgOpt_SenderUid_DEFAULT
	}
	return *p.SenderUid
}
func (p *MsgOpt) IsSetTimestamp() bool {
	return p.Timestamp != MsgOpt_Timestamp_DEFAULT
}

func (p *MsgOpt) IsSetAck() bool {
	return p.Ack != MsgOpt_Ack_DEFAULT
}

func (p *MsgOpt) IsSetGid() bool {
	return p.Gid != MsgOpt_Gid_DEFAULT
}

func (p *MsgOpt) IsSetRpt() bool {
	return p.Rpt != MsgOpt_Rpt_DEFAULT
}

func (p *MsgOpt) IsSetIsIdempotent() bool {
	return p.IsIdempotent != MsgOpt_IsIdempotent_DEFAULT
}

func (p *MsgOpt) IsSetSenderKid() bool {
	return p.SenderKid != nil
}

func (p *MsgOpt) IsSetSenderUid() bool {
	return p.SenderUid != nil
}

func (p *MsgOpt) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *MsgOpt) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *MsgOpt) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Ack = v
	}
	return nil
}

func (p *MsgOpt) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Gid = v
	}
	return nil
}

func (p *MsgOpt) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Rpt = v
	}
	return nil
}

func (p *MsgOpt) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.IsIdempotent = v
	}
	return nil
}

func (p *MsgOpt) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.SenderKid = &v
	}
	return nil
}

func (p *MsgOpt) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.SenderUid = &v
	}
	return nil
}

func (p *MsgOpt) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "MsgOpt"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *MsgOpt) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTimestamp() {
		if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 1); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:timestamp: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(p.Timestamp)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.timestamp (1) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 1:timestamp: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetAck() {
		if err := oprot.WriteFieldBegin(ctx, "ack", thrift.BOOL, 2); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:ack: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.Ack)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.ack (2) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 2:ack: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetGid() {
		if err := oprot.WriteFieldBegin(ctx, "gid", thrift.I64, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:gid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(p.Gid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.gid (3) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:gid: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetRpt() {
		if err := oprot.WriteFieldBegin(ctx, "rpt", thrift.I32, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:rpt: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(p.Rpt)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.rpt (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:rpt: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetIsIdempotent() {
		if err := oprot.WriteFieldBegin(ctx, "isIdempotent", thrift.BOOL, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:isIdempotent: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.IsIdempotent)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.isIdempotent (5) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:isIdempotent: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSenderKid() {
		if err := oprot.WriteFieldBegin(ctx, "senderKid", thrift.I64, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:senderKid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.SenderKid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.senderKid (6) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:senderKid: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSenderUid() {
		if err := oprot.WriteFieldBegin(ctx, "senderUid", thrift.I64, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:senderUid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.SenderUid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.senderUid (7) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:senderUid: ", p), err)
		}
	}
	return err
}

func (p *MsgOpt) Equals(other *MsgOpt) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Timestamp != other.Timestamp {
		return false
	}
	if p.Ack != other.Ack {
		return false
	}
	if p.Gid != other.Gid {
		return false
	}
	if p.Rpt != other.Rpt {
		return false
	}
	if p.IsIdempotent != other.IsIdempotent {
		return false
	}
	if p.SenderKid != other.SenderKid {
		if p.SenderKid == nil || other.SenderKid == nil {
			return false
		}
		if (*p.SenderKid) != (*other.SenderKid) {
			return false
		}
	}
	if p.SenderUid != other.SenderUid {
		if p.SenderUid == nil || other.SenderUid == nil {
			return false
		}
		if (*p.SenderUid) != (*other.SenderUid) {
			return false
		}
	}
	return true
}

func (p *MsgOpt) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MsgOpt(%+v)", *p)
}

// Attributes:
//   - SendToAll: 是否发送给所有端，如是为true，将忽略clientIds
//   - ClientIds: 发送到指定终端，当sendToAll=false时生效，此时参数不能为空
//   - ProtocolVersion: 发送到某个登录协议版本大于等于protocolVersion
//   - Gid: 消息全局id
//   - Rpt: 是否上报本条消息到bi(1:是,0:否)
//   - MaxProtocolVersion: 发送到某个登录协议版本小于等于maxProtocolVersion，0表示无上限
//   - Timestamp: 发送到前端的时间截，如果你指定了时间截，将不会使用socket系统时间，单位秒
//   - MsgFrom: 消息来源
//   - Ack: 是否启用ack机制
//   - ExceptKugouIds: 不发送给此kugouId列表
//   - IsSystem: 如果isSystem=true，优先级比Result.deadLink高，直接发送给用户
//   - Source: 消息来源
//   - JsonAttach: 内容见,http://c.fxwork.kugou.net/pages/viewpage.action?pageId=18089077
//   - IsIdempotent: 是否幂等
//   - Plev: 限流时的优先级
//   - Pvalue: 限流时优先级的权重
//   - SenderKid: 发送者kugouId
//   - SenderUid: 发送者繁星id
//   - ExcludeClientIds: 不发送到指定中断，当sendToAll=false时生效
type MsgOption struct {
	SendToAll          bool      `thrift:"sendToAll,1,required" db:"sendToAll" json:"sendToAll"`
	ClientIds          []int32   `thrift:"clientIds,2,required" db:"clientIds" json:"clientIds"`
	ProtocolVersion    int32     `thrift:"protocolVersion,3" db:"protocolVersion" json:"protocolVersion"`
	Gid                int64     `thrift:"gid,4" db:"gid" json:"gid"`
	Rpt                int32     `thrift:"rpt,5" db:"rpt" json:"rpt"`
	MaxProtocolVersion int32     `thrift:"maxProtocolVersion,6" db:"maxProtocolVersion" json:"maxProtocolVersion"`
	Timestamp          int64     `thrift:"timestamp,7" db:"timestamp" json:"timestamp"`
	MsgFrom            MsgFrom   `thrift:"msgFrom,8" db:"msgFrom" json:"msgFrom"`
	Ack                bool      `thrift:"ack,9" db:"ack" json:"ack"`
	ExceptKugouIds     []int64   `thrift:"exceptKugouIds,10" db:"exceptKugouIds" json:"exceptKugouIds,omitempty"`
	IsSystem           bool      `thrift:"isSystem,11" db:"isSystem" json:"isSystem"`
	Source             MsgSource `thrift:"Source,12" db:"Source" json:"Source"`
	JsonAttach         string    `thrift:"jsonAttach,13" db:"jsonAttach" json:"jsonAttach"`
	IsIdempotent       bool      `thrift:"isIdempotent,14" db:"isIdempotent" json:"isIdempotent"`
	Plev               *int32    `thrift:"plev,15" db:"plev" json:"plev,omitempty"`
	Pvalue             *int32    `thrift:"pvalue,16" db:"pvalue" json:"pvalue,omitempty"`
	SenderKid          *int64    `thrift:"senderKid,17" db:"senderKid" json:"senderKid,omitempty"`
	SenderUid          *int64    `thrift:"senderUid,18" db:"senderUid" json:"senderUid,omitempty"`
	ExcludeClientIds   []int32   `thrift:"excludeClientIds,19" db:"excludeClientIds" json:"excludeClientIds,omitempty"`
}

func NewMsgOption() *MsgOption {
	return &MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
}

func (p *MsgOption) GetSendToAll() bool {
	return p.SendToAll
}

func (p *MsgOption) GetClientIds() []int32 {
	return p.ClientIds
}

var MsgOption_ProtocolVersion_DEFAULT int32 = 0

func (p *MsgOption) GetProtocolVersion() int32 {
	return p.ProtocolVersion
}

var MsgOption_Gid_DEFAULT int64 = 0

func (p *MsgOption) GetGid() int64 {
	return p.Gid
}

var MsgOption_Rpt_DEFAULT int32 = 0

func (p *MsgOption) GetRpt() int32 {
	return p.Rpt
}

var MsgOption_MaxProtocolVersion_DEFAULT int32 = 0

func (p *MsgOption) GetMaxProtocolVersion() int32 {
	return p.MaxProtocolVersion
}

var MsgOption_Timestamp_DEFAULT int64 = 0

func (p *MsgOption) GetTimestamp() int64 {
	return p.Timestamp
}

var MsgOption_MsgFrom_DEFAULT MsgFrom = 0

func (p *MsgOption) GetMsgFrom() MsgFrom {
	return p.MsgFrom
}

var MsgOption_Ack_DEFAULT bool = false

func (p *MsgOption) GetAck() bool {
	return p.Ack
}

var MsgOption_ExceptKugouIds_DEFAULT []int64

func (p *MsgOption) GetExceptKugouIds() []int64 {
	return p.ExceptKugouIds
}

var MsgOption_IsSystem_DEFAULT bool = false

func (p *MsgOption) GetIsSystem() bool {
	return p.IsSystem
}

var MsgOption_Source_DEFAULT MsgSource = 0

func (p *MsgOption) GetSource() MsgSource {
	return p.Source
}

var MsgOption_JsonAttach_DEFAULT string = ""

func (p *MsgOption) GetJsonAttach() string {
	return p.JsonAttach
}

var MsgOption_IsIdempotent_DEFAULT bool = false

func (p *MsgOption) GetIsIdempotent() bool {
	return p.IsIdempotent
}

var MsgOption_Plev_DEFAULT int32

func (p *MsgOption) GetPlev() int32 {
	if !p.IsSetPlev() {
		return MsgOption_Plev_DEFAULT
	}
	return *p.Plev
}

var MsgOption_Pvalue_DEFAULT int32

func (p *MsgOption) GetPvalue() int32 {
	if !p.IsSetPvalue() {
		return MsgOption_Pvalue_DEFAULT
	}
	return *p.Pvalue
}

var MsgOption_SenderKid_DEFAULT int64

func (p *MsgOption) GetSenderKid() int64 {
	if !p.IsSetSenderKid() {
		return MsgOption_SenderKid_DEFAULT
	}
	return *p.SenderKid
}

var MsgOption_SenderUid_DEFAULT int64

func (p *MsgOption) GetSenderUid() int64 {
	if !p.IsSetSenderUid() {
		return MsgOption_SenderUid_DEFAULT
	}
	return *p.SenderUid
}

var MsgOption_ExcludeClientIds_DEFAULT []int32

func (p *MsgOption) GetExcludeClientIds() []int32 {
	return p.ExcludeClientIds
}
func (p *MsgOption) IsSetProtocolVersion() bool {
	return p.ProtocolVersion != MsgOption_ProtocolVersion_DEFAULT
}

func (p *MsgOption) IsSetGid() bool {
	return p.Gid != MsgOption_Gid_DEFAULT
}

func (p *MsgOption) IsSetRpt() bool {
	return p.Rpt != MsgOption_Rpt_DEFAULT
}

func (p *MsgOption) IsSetMaxProtocolVersion() bool {
	return p.MaxProtocolVersion != MsgOption_MaxProtocolVersion_DEFAULT
}

func (p *MsgOption) IsSetTimestamp() bool {
	return p.Timestamp != MsgOption_Timestamp_DEFAULT
}

func (p *MsgOption) IsSetMsgFrom() bool {
	return p.MsgFrom != MsgOption_MsgFrom_DEFAULT
}

func (p *MsgOption) IsSetAck() bool {
	return p.Ack != MsgOption_Ack_DEFAULT
}

func (p *MsgOption) IsSetExceptKugouIds() bool {
	return p.ExceptKugouIds != nil
}

func (p *MsgOption) IsSetIsSystem() bool {
	return p.IsSystem != MsgOption_IsSystem_DEFAULT
}

func (p *MsgOption) IsSetSource() bool {
	return p.Source != MsgOption_Source_DEFAULT
}

func (p *MsgOption) IsSetJsonAttach() bool {
	return p.JsonAttach != MsgOption_JsonAttach_DEFAULT
}

func (p *MsgOption) IsSetIsIdempotent() bool {
	return p.IsIdempotent != MsgOption_IsIdempotent_DEFAULT
}

func (p *MsgOption) IsSetPlev() bool {
	return p.Plev != nil
}

func (p *MsgOption) IsSetPvalue() bool {
	return p.Pvalue != nil
}

func (p *MsgOption) IsSetSenderKid() bool {
	return p.SenderKid != nil
}

func (p *MsgOption) IsSetSenderUid() bool {
	return p.SenderUid != nil
}

func (p *MsgOption) IsSetExcludeClientIds() bool {
	return p.ExcludeClientIds != nil
}

func (p *MsgOption) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetSendToAll bool = false
	var issetClientIds bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetSendToAll = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.SET {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetClientIds = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField8(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField9(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.SET {
				if err := p.ReadField10(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField11(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField12(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField13(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField14(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField15(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField16(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField17(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField18(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.SET {
				if err := p.ReadField19(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetSendToAll {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field SendToAll is not set"))
	}
	if !issetClientIds {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ClientIds is not set"))
	}
	return nil
}

func (p *MsgOption) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.SendToAll = v
	}
	return nil
}

func (p *MsgOption) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadSetBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading set begin: ", err)
	}
	tSet := make([]int32, 0, size)
	p.ClientIds = tSet
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem0 = v
		}
		p.ClientIds = append(p.ClientIds, _elem0)
	}
	if err := iprot.ReadSetEnd(ctx); err != nil {
		return thrift.PrependError("error reading set end: ", err)
	}
	return nil
}

func (p *MsgOption) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.ProtocolVersion = v
	}
	return nil
}

func (p *MsgOption) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Gid = v
	}
	return nil
}

func (p *MsgOption) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Rpt = v
	}
	return nil
}

func (p *MsgOption) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.MaxProtocolVersion = v
	}
	return nil
}

func (p *MsgOption) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *MsgOption) ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 8: ", err)
	} else {
		temp := MsgFrom(v)
		p.MsgFrom = temp
	}
	return nil
}

func (p *MsgOption) ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 9: ", err)
	} else {
		p.Ack = v
	}
	return nil
}

func (p *MsgOption) ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadSetBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading set begin: ", err)
	}
	tSet := make([]int64, 0, size)
	p.ExceptKugouIds = tSet
	for i := 0; i < size; i++ {
		var _elem1 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem1 = v
		}
		p.ExceptKugouIds = append(p.ExceptKugouIds, _elem1)
	}
	if err := iprot.ReadSetEnd(ctx); err != nil {
		return thrift.PrependError("error reading set end: ", err)
	}
	return nil
}

func (p *MsgOption) ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 11: ", err)
	} else {
		p.IsSystem = v
	}
	return nil
}

func (p *MsgOption) ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 12: ", err)
	} else {
		temp := MsgSource(v)
		p.Source = temp
	}
	return nil
}

func (p *MsgOption) ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 13: ", err)
	} else {
		p.JsonAttach = v
	}
	return nil
}

func (p *MsgOption) ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 14: ", err)
	} else {
		p.IsIdempotent = v
	}
	return nil
}

func (p *MsgOption) ReadField15(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 15: ", err)
	} else {
		p.Plev = &v
	}
	return nil
}

func (p *MsgOption) ReadField16(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 16: ", err)
	} else {
		p.Pvalue = &v
	}
	return nil
}

func (p *MsgOption) ReadField17(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 17: ", err)
	} else {
		p.SenderKid = &v
	}
	return nil
}

func (p *MsgOption) ReadField18(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 18: ", err)
	} else {
		p.SenderUid = &v
	}
	return nil
}

func (p *MsgOption) ReadField19(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadSetBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading set begin: ", err)
	}
	tSet := make([]int32, 0, size)
	p.ExcludeClientIds = tSet
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem2 = v
		}
		p.ExcludeClientIds = append(p.ExcludeClientIds, _elem2)
	}
	if err := iprot.ReadSetEnd(ctx); err != nil {
		return thrift.PrependError("error reading set end: ", err)
	}
	return nil
}

func (p *MsgOption) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "MsgOption"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField8(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField9(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField10(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField11(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField12(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField13(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField14(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField15(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField16(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField17(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField18(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField19(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *MsgOption) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "sendToAll", thrift.BOOL, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:sendToAll: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.SendToAll)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.sendToAll (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:sendToAll: ", p), err)
	}
	return err
}

func (p *MsgOption) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "clientIds", thrift.SET, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:clientIds: ", p), err)
	}
	if err := oprot.WriteSetBegin(ctx, thrift.I32, len(p.ClientIds)); err != nil {
		return thrift.PrependError("error writing set begin: ", err)
	}
	for i := 0; i < len(p.ClientIds); i++ {
		for j := i + 1; j < len(p.ClientIds); j++ {
			if func(tgt, src int32) bool {
				if tgt != src {
					return false
				}
				return true
			}(p.ClientIds[i], p.ClientIds[j]) {
				return thrift.PrependError("", fmt.Errorf("%T error writing set field: slice is not unique", p.ClientIds))
			}
		}
	}
	for _, v := range p.ClientIds {
		if err := oprot.WriteI32(ctx, int32(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteSetEnd(ctx); err != nil {
		return thrift.PrependError("error writing set end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:clientIds: ", p), err)
	}
	return err
}

func (p *MsgOption) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetProtocolVersion() {
		if err := oprot.WriteFieldBegin(ctx, "protocolVersion", thrift.I32, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:protocolVersion: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(p.ProtocolVersion)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.protocolVersion (3) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:protocolVersion: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetGid() {
		if err := oprot.WriteFieldBegin(ctx, "gid", thrift.I64, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:gid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(p.Gid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.gid (4) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:gid: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetRpt() {
		if err := oprot.WriteFieldBegin(ctx, "rpt", thrift.I32, 5); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:rpt: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(p.Rpt)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.rpt (5) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 5:rpt: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetMaxProtocolVersion() {
		if err := oprot.WriteFieldBegin(ctx, "maxProtocolVersion", thrift.I32, 6); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:maxProtocolVersion: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(p.MaxProtocolVersion)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.maxProtocolVersion (6) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 6:maxProtocolVersion: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetTimestamp() {
		if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I64, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:timestamp: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(p.Timestamp)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.timestamp (7) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:timestamp: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetMsgFrom() {
		if err := oprot.WriteFieldBegin(ctx, "msgFrom", thrift.I32, 8); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:msgFrom: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(p.MsgFrom)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.msgFrom (8) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 8:msgFrom: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetAck() {
		if err := oprot.WriteFieldBegin(ctx, "ack", thrift.BOOL, 9); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:ack: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.Ack)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.ack (9) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 9:ack: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetExceptKugouIds() {
		if err := oprot.WriteFieldBegin(ctx, "exceptKugouIds", thrift.SET, 10); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:exceptKugouIds: ", p), err)
		}
		if err := oprot.WriteSetBegin(ctx, thrift.I64, len(p.ExceptKugouIds)); err != nil {
			return thrift.PrependError("error writing set begin: ", err)
		}
		for i := 0; i < len(p.ExceptKugouIds); i++ {
			for j := i + 1; j < len(p.ExceptKugouIds); j++ {
				if func(tgt, src int64) bool {
					if tgt != src {
						return false
					}
					return true
				}(p.ExceptKugouIds[i], p.ExceptKugouIds[j]) {
					return thrift.PrependError("", fmt.Errorf("%T error writing set field: slice is not unique", p.ExceptKugouIds))
				}
			}
		}
		for _, v := range p.ExceptKugouIds {
			if err := oprot.WriteI64(ctx, int64(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteSetEnd(ctx); err != nil {
			return thrift.PrependError("error writing set end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 10:exceptKugouIds: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetIsSystem() {
		if err := oprot.WriteFieldBegin(ctx, "isSystem", thrift.BOOL, 11); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:isSystem: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.IsSystem)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.isSystem (11) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 11:isSystem: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err := oprot.WriteFieldBegin(ctx, "Source", thrift.I32, 12); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:Source: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(p.Source)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.Source (12) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 12:Source: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetJsonAttach() {
		if err := oprot.WriteFieldBegin(ctx, "jsonAttach", thrift.STRING, 13); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:jsonAttach: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(p.JsonAttach)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.jsonAttach (13) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 13:jsonAttach: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetIsIdempotent() {
		if err := oprot.WriteFieldBegin(ctx, "isIdempotent", thrift.BOOL, 14); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:isIdempotent: ", p), err)
		}
		if err := oprot.WriteBool(ctx, bool(p.IsIdempotent)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.isIdempotent (14) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 14:isIdempotent: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField15(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetPlev() {
		if err := oprot.WriteFieldBegin(ctx, "plev", thrift.I32, 15); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 15:plev: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.Plev)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.plev (15) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 15:plev: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField16(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetPvalue() {
		if err := oprot.WriteFieldBegin(ctx, "pvalue", thrift.I32, 16); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 16:pvalue: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.Pvalue)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.pvalue (16) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 16:pvalue: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField17(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSenderKid() {
		if err := oprot.WriteFieldBegin(ctx, "senderKid", thrift.I64, 17); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 17:senderKid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.SenderKid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.senderKid (17) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 17:senderKid: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField18(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSenderUid() {
		if err := oprot.WriteFieldBegin(ctx, "senderUid", thrift.I64, 18); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 18:senderUid: ", p), err)
		}
		if err := oprot.WriteI64(ctx, int64(*p.SenderUid)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.senderUid (18) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 18:senderUid: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) writeField19(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetExcludeClientIds() {
		if err := oprot.WriteFieldBegin(ctx, "excludeClientIds", thrift.SET, 19); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 19:excludeClientIds: ", p), err)
		}
		if err := oprot.WriteSetBegin(ctx, thrift.I32, len(p.ExcludeClientIds)); err != nil {
			return thrift.PrependError("error writing set begin: ", err)
		}
		for i := 0; i < len(p.ExcludeClientIds); i++ {
			for j := i + 1; j < len(p.ExcludeClientIds); j++ {
				if func(tgt, src int32) bool {
					if tgt != src {
						return false
					}
					return true
				}(p.ExcludeClientIds[i], p.ExcludeClientIds[j]) {
					return thrift.PrependError("", fmt.Errorf("%T error writing set field: slice is not unique", p.ExcludeClientIds))
				}
			}
		}
		for _, v := range p.ExcludeClientIds {
			if err := oprot.WriteI32(ctx, int32(v)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
		}
		if err := oprot.WriteSetEnd(ctx); err != nil {
			return thrift.PrependError("error writing set end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 19:excludeClientIds: ", p), err)
		}
	}
	return err
}

func (p *MsgOption) Equals(other *MsgOption) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.SendToAll != other.SendToAll {
		return false
	}
	if len(p.ClientIds) != len(other.ClientIds) {
		return false
	}
	for i, _tgt := range p.ClientIds {
		_src3 := other.ClientIds[i]
		if _tgt != _src3 {
			return false
		}
	}
	if p.ProtocolVersion != other.ProtocolVersion {
		return false
	}
	if p.Gid != other.Gid {
		return false
	}
	if p.Rpt != other.Rpt {
		return false
	}
	if p.MaxProtocolVersion != other.MaxProtocolVersion {
		return false
	}
	if p.Timestamp != other.Timestamp {
		return false
	}
	if p.MsgFrom != other.MsgFrom {
		return false
	}
	if p.Ack != other.Ack {
		return false
	}
	if len(p.ExceptKugouIds) != len(other.ExceptKugouIds) {
		return false
	}
	for i, _tgt := range p.ExceptKugouIds {
		_src4 := other.ExceptKugouIds[i]
		if _tgt != _src4 {
			return false
		}
	}
	if p.IsSystem != other.IsSystem {
		return false
	}
	if p.Source != other.Source {
		return false
	}
	if p.JsonAttach != other.JsonAttach {
		return false
	}
	if p.IsIdempotent != other.IsIdempotent {
		return false
	}
	if p.Plev != other.Plev {
		if p.Plev == nil || other.Plev == nil {
			return false
		}
		if (*p.Plev) != (*other.Plev) {
			return false
		}
	}
	if p.Pvalue != other.Pvalue {
		if p.Pvalue == nil || other.Pvalue == nil {
			return false
		}
		if (*p.Pvalue) != (*other.Pvalue) {
			return false
		}
	}
	if p.SenderKid != other.SenderKid {
		if p.SenderKid == nil || other.SenderKid == nil {
			return false
		}
		if (*p.SenderKid) != (*other.SenderKid) {
			return false
		}
	}
	if p.SenderUid != other.SenderUid {
		if p.SenderUid == nil || other.SenderUid == nil {
			return false
		}
		if (*p.SenderUid) != (*other.SenderUid) {
			return false
		}
	}
	if len(p.ExcludeClientIds) != len(other.ExcludeClientIds) {
		return false
	}
	for i, _tgt := range p.ExcludeClientIds {
		_src5 := other.ExcludeClientIds[i]
		if _tgt != _src5 {
			return false
		}
	}
	return true
}

func (p *MsgOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MsgOption(%+v)", *p)
}

// 批量发送的消息参数
//
// Attributes:
//   - BatchOption
//   - Content
type BatchMsg struct {
	BatchOption *BatchOption `thrift:"batchOption,1,required" db:"batchOption" json:"batchOption"`
	Content     []byte       `thrift:"content,2,required" db:"content" json:"content"`
}

func NewBatchMsg() *BatchMsg {
	return &BatchMsg{}
}

var BatchMsg_BatchOption_DEFAULT *BatchOption

func (p *BatchMsg) GetBatchOption() *BatchOption {
	if !p.IsSetBatchOption() {
		return BatchMsg_BatchOption_DEFAULT
	}
	return p.BatchOption
}

func (p *BatchMsg) GetContent() []byte {
	return p.Content
}
func (p *BatchMsg) IsSetBatchOption() bool {
	return p.BatchOption != nil
}

func (p *BatchMsg) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetBatchOption bool = false
	var issetContent bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetBatchOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetBatchOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field BatchOption is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	return nil
}

func (p *BatchMsg) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.BatchOption = &BatchOption{}
	if err := p.BatchOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BatchOption), err)
	}
	return nil
}

func (p *BatchMsg) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *BatchMsg) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchMsg"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchMsg) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "batchOption", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:batchOption: ", p), err)
	}
	if err := p.BatchOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BatchOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:batchOption: ", p), err)
	}
	return err
}

func (p *BatchMsg) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:content: ", p), err)
	}
	return err
}

func (p *BatchMsg) Equals(other *BatchMsg) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if !p.BatchOption.Equals(other.BatchOption) {
		return false
	}
	if bytes.Compare(p.Content, other.Content) != 0 {
		return false
	}
	return true
}

func (p *BatchMsg) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchMsg(%+v)", *p)
}

// 批量发送的消息参数
//
// Attributes:
//   - RoomId
//   - ToKid
//   - ToUid
//   - CmdId
//   - MsgOption
type BatchOption struct {
	RoomId    int32      `thrift:"roomId,1,required" db:"roomId" json:"roomId"`
	ToKid     int64      `thrift:"toKid,2,required" db:"toKid" json:"toKid"`
	ToUid     int64      `thrift:"toUid,3,required" db:"toUid" json:"toUid"`
	CmdId     int32      `thrift:"cmdId,4,required" db:"cmdId" json:"cmdId"`
	MsgOption *MsgOption `thrift:"msgOption,5,required" db:"msgOption" json:"msgOption"`
}

func NewBatchOption() *BatchOption {
	return &BatchOption{}
}

func (p *BatchOption) GetRoomId() int32 {
	return p.RoomId
}

func (p *BatchOption) GetToKid() int64 {
	return p.ToKid
}

func (p *BatchOption) GetToUid() int64 {
	return p.ToUid
}

func (p *BatchOption) GetCmdId() int32 {
	return p.CmdId
}

var BatchOption_MsgOption_DEFAULT *MsgOption

func (p *BatchOption) GetMsgOption() *MsgOption {
	if !p.IsSetMsgOption() {
		return BatchOption_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *BatchOption) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *BatchOption) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRoomId bool = false
	var issetToKid bool = false
	var issetToUid bool = false
	var issetCmdId bool = false
	var issetMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetToKid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetToUid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetCmdId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	if !issetToKid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ToKid is not set"))
	}
	if !issetToUid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ToUid is not set"))
	}
	if !issetCmdId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field CmdId is not set"))
	}
	if !issetMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MsgOption is not set"))
	}
	return nil
}

func (p *BatchOption) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *BatchOption) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *BatchOption) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.ToUid = v
	}
	return nil
}

func (p *BatchOption) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *BatchOption) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *BatchOption) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchOption"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchOption) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *BatchOption) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:toKid: ", p), err)
	}
	return err
}

func (p *BatchOption) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toUid", thrift.I64, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:toUid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToUid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toUid (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:toUid: ", p), err)
	}
	return err
}

func (p *BatchOption) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:cmdId: ", p), err)
	}
	return err
}

func (p *BatchOption) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *BatchOption) Equals(other *BatchOption) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	if p.ToKid != other.ToKid {
		return false
	}
	if p.ToUid != other.ToUid {
		return false
	}
	if p.CmdId != other.CmdId {
		return false
	}
	if !p.MsgOption.Equals(other.MsgOption) {
		return false
	}
	return true
}

func (p *BatchOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchOption(%+v)", *p)
}

// 批量根据kugouId发送消息入参
//
// Attributes:
//   - ToKid
//   - ToUid
//   - Content
//   - CmdId
//   - MsgOption
type BatchMessage struct {
	ToKid     int64      `thrift:"toKid,1,required" db:"toKid" json:"toKid"`
	ToUid     int64      `thrift:"toUid,2,required" db:"toUid" json:"toUid"`
	Content   []byte     `thrift:"content,3,required" db:"content" json:"content"`
	CmdId     int32      `thrift:"cmdId,4,required" db:"cmdId" json:"cmdId"`
	MsgOption *MsgOption `thrift:"msgOption,5,required" db:"msgOption" json:"msgOption"`
}

func NewBatchMessage() *BatchMessage {
	return &BatchMessage{}
}

func (p *BatchMessage) GetToKid() int64 {
	return p.ToKid
}

func (p *BatchMessage) GetToUid() int64 {
	return p.ToUid
}

func (p *BatchMessage) GetContent() []byte {
	return p.Content
}

func (p *BatchMessage) GetCmdId() int32 {
	return p.CmdId
}

var BatchMessage_MsgOption_DEFAULT *MsgOption

func (p *BatchMessage) GetMsgOption() *MsgOption {
	if !p.IsSetMsgOption() {
		return BatchMessage_MsgOption_DEFAULT
	}
	return p.MsgOption
}
func (p *BatchMessage) IsSetMsgOption() bool {
	return p.MsgOption != nil
}

func (p *BatchMessage) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetToKid bool = false
	var issetToUid bool = false
	var issetContent bool = false
	var issetCmdId bool = false
	var issetMsgOption bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetToKid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetToUid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetCmdId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetMsgOption = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetToKid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ToKid is not set"))
	}
	if !issetToUid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ToUid is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	if !issetCmdId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field CmdId is not set"))
	}
	if !issetMsgOption {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MsgOption is not set"))
	}
	return nil
}

func (p *BatchMessage) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *BatchMessage) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ToUid = v
	}
	return nil
}

func (p *BatchMessage) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *BatchMessage) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *BatchMessage) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOption = &MsgOption{
		MsgFrom: 0,

		Source: 0,
	}
	if err := p.MsgOption.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOption), err)
	}
	return nil
}

func (p *BatchMessage) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchMessage"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchMessage) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:toKid: ", p), err)
	}
	return err
}

func (p *BatchMessage) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toUid", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:toUid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToUid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toUid (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:toUid: ", p), err)
	}
	return err
}

func (p *BatchMessage) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *BatchMessage) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:cmdId: ", p), err)
	}
	return err
}

func (p *BatchMessage) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOption", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOption: ", p), err)
	}
	if err := p.MsgOption.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOption), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOption: ", p), err)
	}
	return err
}

func (p *BatchMessage) Equals(other *BatchMessage) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.ToKid != other.ToKid {
		return false
	}
	if p.ToUid != other.ToUid {
		return false
	}
	if bytes.Compare(p.Content, other.Content) != 0 {
		return false
	}
	if p.CmdId != other.CmdId {
		return false
	}
	if !p.MsgOption.Equals(other.MsgOption) {
		return false
	}
	return true
}

func (p *BatchMessage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchMessage(%+v)", *p)
}

// 特殊通道：批量根据kugouId发送消息入参
//
// Attributes:
//   - ToKid
//   - ToUid
//   - Content
//   - CmdId
//   - MsgOpt
type BatchMessageSpecial struct {
	ToKid   int64   `thrift:"toKid,1,required" db:"toKid" json:"toKid"`
	ToUid   int64   `thrift:"toUid,2,required" db:"toUid" json:"toUid"`
	Content []byte  `thrift:"content,3,required" db:"content" json:"content"`
	CmdId   int32   `thrift:"cmdId,4,required" db:"cmdId" json:"cmdId"`
	MsgOpt  *MsgOpt `thrift:"msgOpt,5,required" db:"msgOpt" json:"msgOpt"`
}

func NewBatchMessageSpecial() *BatchMessageSpecial {
	return &BatchMessageSpecial{}
}

func (p *BatchMessageSpecial) GetToKid() int64 {
	return p.ToKid
}

func (p *BatchMessageSpecial) GetToUid() int64 {
	return p.ToUid
}

func (p *BatchMessageSpecial) GetContent() []byte {
	return p.Content
}

func (p *BatchMessageSpecial) GetCmdId() int32 {
	return p.CmdId
}

var BatchMessageSpecial_MsgOpt_DEFAULT *MsgOpt

func (p *BatchMessageSpecial) GetMsgOpt() *MsgOpt {
	if !p.IsSetMsgOpt() {
		return BatchMessageSpecial_MsgOpt_DEFAULT
	}
	return p.MsgOpt
}
func (p *BatchMessageSpecial) IsSetMsgOpt() bool {
	return p.MsgOpt != nil
}

func (p *BatchMessageSpecial) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetToKid bool = false
	var issetToUid bool = false
	var issetContent bool = false
	var issetCmdId bool = false
	var issetMsgOpt bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetToKid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetToUid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetContent = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetCmdId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetMsgOpt = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetToKid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ToKid is not set"))
	}
	if !issetToUid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ToUid is not set"))
	}
	if !issetContent {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Content is not set"))
	}
	if !issetCmdId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field CmdId is not set"))
	}
	if !issetMsgOpt {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field MsgOpt is not set"))
	}
	return nil
}

func (p *BatchMessageSpecial) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.ToKid = v
	}
	return nil
}

func (p *BatchMessageSpecial) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ToUid = v
	}
	return nil
}

func (p *BatchMessageSpecial) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *BatchMessageSpecial) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.CmdId = v
	}
	return nil
}

func (p *BatchMessageSpecial) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	p.MsgOpt = &MsgOpt{}
	if err := p.MsgOpt.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.MsgOpt), err)
	}
	return nil
}

func (p *BatchMessageSpecial) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchMessageSpecial"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchMessageSpecial) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toKid", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:toKid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToKid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toKid (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:toKid: ", p), err)
	}
	return err
}

func (p *BatchMessageSpecial) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "toUid", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:toUid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ToUid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.toUid (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:toUid: ", p), err)
	}
	return err
}

func (p *BatchMessageSpecial) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "content", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:content: ", p), err)
	}
	if err := oprot.WriteBinary(ctx, p.Content); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.content (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:content: ", p), err)
	}
	return err
}

func (p *BatchMessageSpecial) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "cmdId", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:cmdId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.CmdId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.cmdId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:cmdId: ", p), err)
	}
	return err
}

func (p *BatchMessageSpecial) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msgOpt", thrift.STRUCT, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:msgOpt: ", p), err)
	}
	if err := p.MsgOpt.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.MsgOpt), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:msgOpt: ", p), err)
	}
	return err
}

func (p *BatchMessageSpecial) Equals(other *BatchMessageSpecial) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.ToKid != other.ToKid {
		return false
	}
	if p.ToUid != other.ToUid {
		return false
	}
	if bytes.Compare(p.Content, other.Content) != 0 {
		return false
	}
	if p.CmdId != other.CmdId {
		return false
	}
	if !p.MsgOpt.Equals(other.MsgOpt) {
		return false
	}
	return true
}

func (p *BatchMessageSpecial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchMessageSpecial(%+v)", *p)
}
