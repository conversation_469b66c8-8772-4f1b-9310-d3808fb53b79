// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package platform_consume_read_service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"kugou_adapter_service/third-party/gen-go/platform_consume_service"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//   - AccountChangeType: 用户财务变更类型
//   - ConsumeId: 消费id
//   - ReqTimestamp: 订单请求时间，用户找出消费记录的月表月份
//   - Pid: pid
//   - AppId: appId
//   - Timestamp: 时间戳
//   - Sign: 签名
type QueryConsumeLogReq struct {
	AccountChangeType int64  `thrift:"accountChangeType,1,required" db:"accountChangeType" json:"accountChangeType"`
	ConsumeId         int64  `thrift:"consumeId,2,required" db:"consumeId" json:"consumeId"`
	ReqTimestamp      int32  `thrift:"reqTimestamp,3,required" db:"reqTimestamp" json:"reqTimestamp"`
	Pid               int32  `thrift:"pid,4,required" db:"pid" json:"pid"`
	AppId             int32  `thrift:"appId,5,required" db:"appId" json:"appId"`
	Timestamp         int32  `thrift:"timestamp,6,required" db:"timestamp" json:"timestamp"`
	Sign              string `thrift:"sign,7,required" db:"sign" json:"sign"`
}

func NewQueryConsumeLogReq() *QueryConsumeLogReq {
	return &QueryConsumeLogReq{}
}

func (p *QueryConsumeLogReq) GetAccountChangeType() int64 {
	return p.AccountChangeType
}

func (p *QueryConsumeLogReq) GetConsumeId() int64 {
	return p.ConsumeId
}

func (p *QueryConsumeLogReq) GetReqTimestamp() int32 {
	return p.ReqTimestamp
}

func (p *QueryConsumeLogReq) GetPid() int32 {
	return p.Pid
}

func (p *QueryConsumeLogReq) GetAppId() int32 {
	return p.AppId
}

func (p *QueryConsumeLogReq) GetTimestamp() int32 {
	return p.Timestamp
}

func (p *QueryConsumeLogReq) GetSign() string {
	return p.Sign
}
func (p *QueryConsumeLogReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetAccountChangeType bool = false
	var issetConsumeId bool = false
	var issetReqTimestamp bool = false
	var issetPid bool = false
	var issetAppId bool = false
	var issetTimestamp bool = false
	var issetSign bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetAccountChangeType = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetConsumeId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetReqTimestamp = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetPid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
				issetTimestamp = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
				issetSign = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetAccountChangeType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AccountChangeType is not set"))
	}
	if !issetConsumeId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ConsumeId is not set"))
	}
	if !issetReqTimestamp {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ReqTimestamp is not set"))
	}
	if !issetPid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Pid is not set"))
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	if !issetTimestamp {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Timestamp is not set"))
	}
	if !issetSign {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"))
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AccountChangeType = v
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.ConsumeId = v
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.ReqTimestamp = v
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *QueryConsumeLogReq) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.Sign = v
	}
	return nil
}

func (p *QueryConsumeLogReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "QueryConsumeLogReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *QueryConsumeLogReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "accountChangeType", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:accountChangeType: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.AccountChangeType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.accountChangeType (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:accountChangeType: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "consumeId", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:consumeId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ConsumeId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.consumeId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:consumeId: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "reqTimestamp", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:reqTimestamp: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.ReqTimestamp)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.reqTimestamp (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:reqTimestamp: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "pid", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:pid: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Pid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.pid (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:pid: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:appId: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I32, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:timestamp: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Timestamp)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.timestamp (6) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:timestamp: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 7); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:sign: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.sign (7) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 7:sign: ", p), err)
	}
	return err
}

func (p *QueryConsumeLogReq) Equals(other *QueryConsumeLogReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.AccountChangeType != other.AccountChangeType {
		return false
	}
	if p.ConsumeId != other.ConsumeId {
		return false
	}
	if p.ReqTimestamp != other.ReqTimestamp {
		return false
	}
	if p.Pid != other.Pid {
		return false
	}
	if p.AppId != other.AppId {
		return false
	}
	if p.Timestamp != other.Timestamp {
		return false
	}
	if p.Sign != other.Sign {
		return false
	}
	return true
}

func (p *QueryConsumeLogReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryConsumeLogReq(%+v)", *p)
}

// Attributes:
//   - AccountChangeType: 用户财务变更类型
//   - StartTime: 查询开始时间 单位秒
//   - EndTime: 查询开始时间 单位秒
//   - KugouId: kugouId
//   - Timestamp: 时间戳
//   - Sign: 签名
type QueryAddBeanReq struct {
	AccountChangeType int64  `thrift:"accountChangeType,1,required" db:"accountChangeType" json:"accountChangeType"`
	StartTime         int32  `thrift:"startTime,2,required" db:"startTime" json:"startTime"`
	EndTime           int32  `thrift:"endTime,3,required" db:"endTime" json:"endTime"`
	KugouId           int64  `thrift:"kugouId,4,required" db:"kugouId" json:"kugouId"`
	Timestamp         int32  `thrift:"timestamp,5,required" db:"timestamp" json:"timestamp"`
	Sign              string `thrift:"sign,6,required" db:"sign" json:"sign"`
}

func NewQueryAddBeanReq() *QueryAddBeanReq {
	return &QueryAddBeanReq{}
}

func (p *QueryAddBeanReq) GetAccountChangeType() int64 {
	return p.AccountChangeType
}

func (p *QueryAddBeanReq) GetStartTime() int32 {
	return p.StartTime
}

func (p *QueryAddBeanReq) GetEndTime() int32 {
	return p.EndTime
}

func (p *QueryAddBeanReq) GetKugouId() int64 {
	return p.KugouId
}

func (p *QueryAddBeanReq) GetTimestamp() int32 {
	return p.Timestamp
}

func (p *QueryAddBeanReq) GetSign() string {
	return p.Sign
}
func (p *QueryAddBeanReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetAccountChangeType bool = false
	var issetStartTime bool = false
	var issetEndTime bool = false
	var issetKugouId bool = false
	var issetTimestamp bool = false
	var issetSign bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetAccountChangeType = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetStartTime = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetEndTime = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetTimestamp = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
				issetSign = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetAccountChangeType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AccountChangeType is not set"))
	}
	if !issetStartTime {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field StartTime is not set"))
	}
	if !issetEndTime {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field EndTime is not set"))
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetTimestamp {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Timestamp is not set"))
	}
	if !issetSign {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"))
	}
	return nil
}

func (p *QueryAddBeanReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AccountChangeType = v
	}
	return nil
}

func (p *QueryAddBeanReq) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *QueryAddBeanReq) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *QueryAddBeanReq) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *QueryAddBeanReq) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *QueryAddBeanReq) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.Sign = v
	}
	return nil
}

func (p *QueryAddBeanReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "QueryAddBeanReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *QueryAddBeanReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "accountChangeType", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:accountChangeType: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.AccountChangeType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.accountChangeType (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:accountChangeType: ", p), err)
	}
	return err
}

func (p *QueryAddBeanReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "startTime", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:startTime: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.StartTime)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.startTime (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:startTime: ", p), err)
	}
	return err
}

func (p *QueryAddBeanReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "endTime", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:endTime: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.EndTime)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.endTime (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:endTime: ", p), err)
	}
	return err
}

func (p *QueryAddBeanReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:kugouId: ", p), err)
	}
	return err
}

func (p *QueryAddBeanReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "timestamp", thrift.I32, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:timestamp: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Timestamp)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.timestamp (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:timestamp: ", p), err)
	}
	return err
}

func (p *QueryAddBeanReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:sign: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.sign (6) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:sign: ", p), err)
	}
	return err
}

func (p *QueryAddBeanReq) Equals(other *QueryAddBeanReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.AccountChangeType != other.AccountChangeType {
		return false
	}
	if p.StartTime != other.StartTime {
		return false
	}
	if p.EndTime != other.EndTime {
		return false
	}
	if p.KugouId != other.KugouId {
		return false
	}
	if p.Timestamp != other.Timestamp {
		return false
	}
	if p.Sign != other.Sign {
		return false
	}
	return true
}

func (p *QueryAddBeanReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAddBeanReq(%+v)", *p)
}

type PlatformConsumeReadService interface {
	// 获得用户金钱接口，非实时，查从库
	//
	// Parameters:
	//  - KugouId
	//  - AppId
	GetUserMoney(ctx context.Context, kugouId int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error)
	// 批量获取用户金钱接口，非实时，查从库
	//
	// Parameters:
	//  - KugouIds
	//  - AppId
	GetUserMoneyBatch(ctx context.Context, kugouIds []int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error)
	// 金钱变更日志回查接口
	//
	// Parameters:
	//  - Month
	//  - UniqId
	//  - AppId
	QueryLogByUniqId(ctx context.Context, month string, uniqId string, appId int32) (_r *platform_consume_service.ConsumeResp, _err error)
	// 根据consumeId查询消费日志
	//
	// Parameters:
	//  - Log
	QueryConsumeLogById(ctx context.Context, log *QueryConsumeLogReq) (_r *platform_consume_service.ConsumeResp, _err error)
	// 获得用户金钱接口，非实时，查从库
	//
	// Parameters:
	//  - KugouId
	//  - AppId
	GetUserAccount(ctx context.Context, kugouId int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error)
	// 查询用户某段区间内增加的星豆数，非实时，查从库
	//
	// Parameters:
	//  - Req
	GetUserAddBean(ctx context.Context, req *QueryAddBeanReq) (_r *platform_consume_service.ConsumeResp, _err error)
}

type PlatformConsumeReadServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewPlatformConsumeReadServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PlatformConsumeReadServiceClient {
	return &PlatformConsumeReadServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewPlatformConsumeReadServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PlatformConsumeReadServiceClient {
	return &PlatformConsumeReadServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewPlatformConsumeReadServiceClient(c thrift.TClient) *PlatformConsumeReadServiceClient {
	return &PlatformConsumeReadServiceClient{
		c: c,
	}
}

func (p *PlatformConsumeReadServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *PlatformConsumeReadServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *PlatformConsumeReadServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// 获得用户金钱接口，非实时，查从库
//
// Parameters:
//   - KugouId
//   - AppId
func (p *PlatformConsumeReadServiceClient) GetUserMoney(ctx context.Context, kugouId int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args0 PlatformConsumeReadServiceGetUserMoneyArgs
	_args0.KugouId = kugouId
	_args0.AppId = appId
	var _result2 PlatformConsumeReadServiceGetUserMoneyResult
	var _meta1 thrift.ResponseMeta
	_meta1, _err = p.Client_().Call(ctx, "getUserMoney", &_args0, &_result2)
	p.SetLastResponseMeta_(_meta1)
	if _err != nil {
		return
	}
	if _ret3 := _result2.GetSuccess(); _ret3 != nil {
		return _ret3, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserMoney failed: unknown result")
}

// 批量获取用户金钱接口，非实时，查从库
//
// Parameters:
//   - KugouIds
//   - AppId
func (p *PlatformConsumeReadServiceClient) GetUserMoneyBatch(ctx context.Context, kugouIds []int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args4 PlatformConsumeReadServiceGetUserMoneyBatchArgs
	_args4.KugouIds = kugouIds
	_args4.AppId = appId
	var _result6 PlatformConsumeReadServiceGetUserMoneyBatchResult
	var _meta5 thrift.ResponseMeta
	_meta5, _err = p.Client_().Call(ctx, "getUserMoneyBatch", &_args4, &_result6)
	p.SetLastResponseMeta_(_meta5)
	if _err != nil {
		return
	}
	if _ret7 := _result6.GetSuccess(); _ret7 != nil {
		return _ret7, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserMoneyBatch failed: unknown result")
}

// 金钱变更日志回查接口
//
// Parameters:
//   - Month
//   - UniqId
//   - AppId
func (p *PlatformConsumeReadServiceClient) QueryLogByUniqId(ctx context.Context, month string, uniqId string, appId int32) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args8 PlatformConsumeReadServiceQueryLogByUniqIdArgs
	_args8.Month = month
	_args8.UniqId = uniqId
	_args8.AppId = appId
	var _result10 PlatformConsumeReadServiceQueryLogByUniqIdResult
	var _meta9 thrift.ResponseMeta
	_meta9, _err = p.Client_().Call(ctx, "queryLogByUniqId", &_args8, &_result10)
	p.SetLastResponseMeta_(_meta9)
	if _err != nil {
		return
	}
	if _ret11 := _result10.GetSuccess(); _ret11 != nil {
		return _ret11, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "queryLogByUniqId failed: unknown result")
}

// 根据consumeId查询消费日志
//
// Parameters:
//   - Log
func (p *PlatformConsumeReadServiceClient) QueryConsumeLogById(ctx context.Context, log *QueryConsumeLogReq) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args12 PlatformConsumeReadServiceQueryConsumeLogByIdArgs
	_args12.Log = log
	var _result14 PlatformConsumeReadServiceQueryConsumeLogByIdResult
	var _meta13 thrift.ResponseMeta
	_meta13, _err = p.Client_().Call(ctx, "queryConsumeLogById", &_args12, &_result14)
	p.SetLastResponseMeta_(_meta13)
	if _err != nil {
		return
	}
	if _ret15 := _result14.GetSuccess(); _ret15 != nil {
		return _ret15, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "queryConsumeLogById failed: unknown result")
}

// 获得用户金钱接口，非实时，查从库
//
// Parameters:
//   - KugouId
//   - AppId
func (p *PlatformConsumeReadServiceClient) GetUserAccount(ctx context.Context, kugouId int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args16 PlatformConsumeReadServiceGetUserAccountArgs
	_args16.KugouId = kugouId
	_args16.AppId = appId
	var _result18 PlatformConsumeReadServiceGetUserAccountResult
	var _meta17 thrift.ResponseMeta
	_meta17, _err = p.Client_().Call(ctx, "getUserAccount", &_args16, &_result18)
	p.SetLastResponseMeta_(_meta17)
	if _err != nil {
		return
	}
	if _ret19 := _result18.GetSuccess(); _ret19 != nil {
		return _ret19, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserAccount failed: unknown result")
}

// 查询用户某段区间内增加的星豆数，非实时，查从库
//
// Parameters:
//   - Req
func (p *PlatformConsumeReadServiceClient) GetUserAddBean(ctx context.Context, req *QueryAddBeanReq) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args20 PlatformConsumeReadServiceGetUserAddBeanArgs
	_args20.Req = req
	var _result22 PlatformConsumeReadServiceGetUserAddBeanResult
	var _meta21 thrift.ResponseMeta
	_meta21, _err = p.Client_().Call(ctx, "getUserAddBean", &_args20, &_result22)
	p.SetLastResponseMeta_(_meta21)
	if _err != nil {
		return
	}
	if _ret23 := _result22.GetSuccess(); _ret23 != nil {
		return _ret23, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserAddBean failed: unknown result")
}

type PlatformConsumeReadServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      PlatformConsumeReadService
}

func (p *PlatformConsumeReadServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *PlatformConsumeReadServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *PlatformConsumeReadServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewPlatformConsumeReadServiceProcessor(handler PlatformConsumeReadService) *PlatformConsumeReadServiceProcessor {

	self24 := &PlatformConsumeReadServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self24.processorMap["getUserMoney"] = &platformConsumeReadServiceProcessorGetUserMoney{handler: handler}
	self24.processorMap["getUserMoneyBatch"] = &platformConsumeReadServiceProcessorGetUserMoneyBatch{handler: handler}
	self24.processorMap["queryLogByUniqId"] = &platformConsumeReadServiceProcessorQueryLogByUniqId{handler: handler}
	self24.processorMap["queryConsumeLogById"] = &platformConsumeReadServiceProcessorQueryConsumeLogById{handler: handler}
	self24.processorMap["getUserAccount"] = &platformConsumeReadServiceProcessorGetUserAccount{handler: handler}
	self24.processorMap["getUserAddBean"] = &platformConsumeReadServiceProcessorGetUserAddBean{handler: handler}
	return self24
}

func (p *PlatformConsumeReadServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x25 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x25.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x25

}

type platformConsumeReadServiceProcessorGetUserMoney struct {
	handler PlatformConsumeReadService
}

func (p *platformConsumeReadServiceProcessorGetUserMoney) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err26 error
	args := PlatformConsumeReadServiceGetUserMoneyArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserMoney", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := PlatformConsumeReadServiceGetUserMoneyResult{}
	if retval, err2 := p.handler.GetUserMoney(ctx, args.KugouId, args.AppId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc27 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMoney: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserMoney", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if err2 := _exc27.Write(ctx, oprot); _write_err26 == nil && err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err26 == nil && err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err26 == nil && err2 != nil {
			_write_err26 = thrift.WrapTException(err2)
		}
		if _write_err26 != nil {
			return false, thrift.WrapTException(_write_err26)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserMoney", thrift.REPLY, seqId); err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err26 == nil && err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err26 == nil && err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err26 == nil && err2 != nil {
		_write_err26 = thrift.WrapTException(err2)
	}
	if _write_err26 != nil {
		return false, thrift.WrapTException(_write_err26)
	}
	return true, err
}

type platformConsumeReadServiceProcessorGetUserMoneyBatch struct {
	handler PlatformConsumeReadService
}

func (p *platformConsumeReadServiceProcessorGetUserMoneyBatch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err28 error
	args := PlatformConsumeReadServiceGetUserMoneyBatchArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserMoneyBatch", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := PlatformConsumeReadServiceGetUserMoneyBatchResult{}
	if retval, err2 := p.handler.GetUserMoneyBatch(ctx, args.KugouIds, args.AppId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc29 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMoneyBatch: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserMoneyBatch", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if err2 := _exc29.Write(ctx, oprot); _write_err28 == nil && err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err28 == nil && err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err28 == nil && err2 != nil {
			_write_err28 = thrift.WrapTException(err2)
		}
		if _write_err28 != nil {
			return false, thrift.WrapTException(_write_err28)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserMoneyBatch", thrift.REPLY, seqId); err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err28 == nil && err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err28 == nil && err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err28 == nil && err2 != nil {
		_write_err28 = thrift.WrapTException(err2)
	}
	if _write_err28 != nil {
		return false, thrift.WrapTException(_write_err28)
	}
	return true, err
}

type platformConsumeReadServiceProcessorQueryLogByUniqId struct {
	handler PlatformConsumeReadService
}

func (p *platformConsumeReadServiceProcessorQueryLogByUniqId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err30 error
	args := PlatformConsumeReadServiceQueryLogByUniqIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "queryLogByUniqId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := PlatformConsumeReadServiceQueryLogByUniqIdResult{}
	if retval, err2 := p.handler.QueryLogByUniqId(ctx, args.Month, args.UniqId, args.AppId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc31 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryLogByUniqId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "queryLogByUniqId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err30 = thrift.WrapTException(err2)
		}
		if err2 := _exc31.Write(ctx, oprot); _write_err30 == nil && err2 != nil {
			_write_err30 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err30 == nil && err2 != nil {
			_write_err30 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err30 == nil && err2 != nil {
			_write_err30 = thrift.WrapTException(err2)
		}
		if _write_err30 != nil {
			return false, thrift.WrapTException(_write_err30)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "queryLogByUniqId", thrift.REPLY, seqId); err2 != nil {
		_write_err30 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err30 == nil && err2 != nil {
		_write_err30 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err30 == nil && err2 != nil {
		_write_err30 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err30 == nil && err2 != nil {
		_write_err30 = thrift.WrapTException(err2)
	}
	if _write_err30 != nil {
		return false, thrift.WrapTException(_write_err30)
	}
	return true, err
}

type platformConsumeReadServiceProcessorQueryConsumeLogById struct {
	handler PlatformConsumeReadService
}

func (p *platformConsumeReadServiceProcessorQueryConsumeLogById) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err32 error
	args := PlatformConsumeReadServiceQueryConsumeLogByIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "queryConsumeLogById", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := PlatformConsumeReadServiceQueryConsumeLogByIdResult{}
	if retval, err2 := p.handler.QueryConsumeLogById(ctx, args.Log); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc33 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryConsumeLogById: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "queryConsumeLogById", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err32 = thrift.WrapTException(err2)
		}
		if err2 := _exc33.Write(ctx, oprot); _write_err32 == nil && err2 != nil {
			_write_err32 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err32 == nil && err2 != nil {
			_write_err32 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err32 == nil && err2 != nil {
			_write_err32 = thrift.WrapTException(err2)
		}
		if _write_err32 != nil {
			return false, thrift.WrapTException(_write_err32)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "queryConsumeLogById", thrift.REPLY, seqId); err2 != nil {
		_write_err32 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err32 == nil && err2 != nil {
		_write_err32 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err32 == nil && err2 != nil {
		_write_err32 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err32 == nil && err2 != nil {
		_write_err32 = thrift.WrapTException(err2)
	}
	if _write_err32 != nil {
		return false, thrift.WrapTException(_write_err32)
	}
	return true, err
}

type platformConsumeReadServiceProcessorGetUserAccount struct {
	handler PlatformConsumeReadService
}

func (p *platformConsumeReadServiceProcessorGetUserAccount) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err34 error
	args := PlatformConsumeReadServiceGetUserAccountArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserAccount", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := PlatformConsumeReadServiceGetUserAccountResult{}
	if retval, err2 := p.handler.GetUserAccount(ctx, args.KugouId, args.AppId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc35 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserAccount: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserAccount", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if err2 := _exc35.Write(ctx, oprot); _write_err34 == nil && err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err34 == nil && err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err34 == nil && err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if _write_err34 != nil {
			return false, thrift.WrapTException(_write_err34)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserAccount", thrift.REPLY, seqId); err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err34 == nil && err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err34 == nil && err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err34 == nil && err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if _write_err34 != nil {
		return false, thrift.WrapTException(_write_err34)
	}
	return true, err
}

type platformConsumeReadServiceProcessorGetUserAddBean struct {
	handler PlatformConsumeReadService
}

func (p *platformConsumeReadServiceProcessorGetUserAddBean) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err36 error
	args := PlatformConsumeReadServiceGetUserAddBeanArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserAddBean", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := PlatformConsumeReadServiceGetUserAddBeanResult{}
	if retval, err2 := p.handler.GetUserAddBean(ctx, args.Req); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc37 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserAddBean: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserAddBean", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if err2 := _exc37.Write(ctx, oprot); _write_err36 == nil && err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err36 == nil && err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err36 == nil && err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if _write_err36 != nil {
			return false, thrift.WrapTException(_write_err36)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserAddBean", thrift.REPLY, seqId); err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err36 == nil && err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err36 == nil && err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err36 == nil && err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if _write_err36 != nil {
		return false, thrift.WrapTException(_write_err36)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//   - KugouId
//   - AppId
type PlatformConsumeReadServiceGetUserMoneyArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
	AppId   int32 `thrift:"appId,2" db:"appId" json:"appId"`
}

func NewPlatformConsumeReadServiceGetUserMoneyArgs() *PlatformConsumeReadServiceGetUserMoneyArgs {
	return &PlatformConsumeReadServiceGetUserMoneyArgs{}
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) GetKugouId() int64 {
	return p.KugouId
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) GetAppId() int32 {
	return p.AppId
}
func (p *PlatformConsumeReadServiceGetUserMoneyArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMoney_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserMoneyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserMoneyArgs(%+v)", *p)
}

// Attributes:
//   - Success
type PlatformConsumeReadServiceGetUserMoneyResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformConsumeReadServiceGetUserMoneyResult() *PlatformConsumeReadServiceGetUserMoneyResult {
	return &PlatformConsumeReadServiceGetUserMoneyResult{}
}

var PlatformConsumeReadServiceGetUserMoneyResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *PlatformConsumeReadServiceGetUserMoneyResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return PlatformConsumeReadServiceGetUserMoneyResult_Success_DEFAULT
	}
	return p.Success
}
func (p *PlatformConsumeReadServiceGetUserMoneyResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMoney_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserMoneyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserMoneyResult(%+v)", *p)
}

// Attributes:
//   - KugouIds
//   - AppId
type PlatformConsumeReadServiceGetUserMoneyBatchArgs struct {
	KugouIds []int64 `thrift:"kugouIds,1,required" db:"kugouIds" json:"kugouIds"`
	AppId    int32   `thrift:"appId,2" db:"appId" json:"appId"`
}

func NewPlatformConsumeReadServiceGetUserMoneyBatchArgs() *PlatformConsumeReadServiceGetUserMoneyBatchArgs {
	return &PlatformConsumeReadServiceGetUserMoneyBatchArgs{}
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) GetKugouIds() []int64 {
	return p.KugouIds
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) GetAppId() int32 {
	return p.AppId
}
func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouIds bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouIds = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouIds {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouIds is not set"))
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]int64, 0, size)
	p.KugouIds = tSlice
	for i := 0; i < size; i++ {
		var _elem38 int64
		if v, err := iprot.ReadI64(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem38 = v
		}
		p.KugouIds = append(p.KugouIds, _elem38)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMoneyBatch_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouIds", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouIds: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.KugouIds)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.KugouIds {
		if err := oprot.WriteI64(ctx, int64(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouIds: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserMoneyBatchArgs(%+v)", *p)
}

// Attributes:
//   - Success
type PlatformConsumeReadServiceGetUserMoneyBatchResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformConsumeReadServiceGetUserMoneyBatchResult() *PlatformConsumeReadServiceGetUserMoneyBatchResult {
	return &PlatformConsumeReadServiceGetUserMoneyBatchResult{}
}

var PlatformConsumeReadServiceGetUserMoneyBatchResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return PlatformConsumeReadServiceGetUserMoneyBatchResult_Success_DEFAULT
	}
	return p.Success
}
func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMoneyBatch_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserMoneyBatchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserMoneyBatchResult(%+v)", *p)
}

// Attributes:
//   - Month
//   - UniqId
//   - AppId
type PlatformConsumeReadServiceQueryLogByUniqIdArgs struct {
	Month  string `thrift:"month,1,required" db:"month" json:"month"`
	UniqId string `thrift:"uniqId,2,required" db:"uniqId" json:"uniqId"`
	AppId  int32  `thrift:"appId,3,required" db:"appId" json:"appId"`
}

func NewPlatformConsumeReadServiceQueryLogByUniqIdArgs() *PlatformConsumeReadServiceQueryLogByUniqIdArgs {
	return &PlatformConsumeReadServiceQueryLogByUniqIdArgs{}
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) GetMonth() string {
	return p.Month
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) GetUniqId() string {
	return p.UniqId
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) GetAppId() int32 {
	return p.AppId
}
func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetMonth bool = false
	var issetUniqId bool = false
	var issetAppId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetMonth = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetUniqId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetMonth {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Month is not set"))
	}
	if !issetUniqId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UniqId is not set"))
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.UniqId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "queryLogByUniqId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "month", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:month: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Month)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.month (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:month: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "uniqId", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:uniqId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.UniqId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.uniqId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:uniqId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:appId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceQueryLogByUniqIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type PlatformConsumeReadServiceQueryLogByUniqIdResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformConsumeReadServiceQueryLogByUniqIdResult() *PlatformConsumeReadServiceQueryLogByUniqIdResult {
	return &PlatformConsumeReadServiceQueryLogByUniqIdResult{}
}

var PlatformConsumeReadServiceQueryLogByUniqIdResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return PlatformConsumeReadServiceQueryLogByUniqIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "queryLogByUniqId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *PlatformConsumeReadServiceQueryLogByUniqIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceQueryLogByUniqIdResult(%+v)", *p)
}

// Attributes:
//   - Log
type PlatformConsumeReadServiceQueryConsumeLogByIdArgs struct {
	Log *QueryConsumeLogReq `thrift:"log,1,required" db:"log" json:"log"`
}

func NewPlatformConsumeReadServiceQueryConsumeLogByIdArgs() *PlatformConsumeReadServiceQueryConsumeLogByIdArgs {
	return &PlatformConsumeReadServiceQueryConsumeLogByIdArgs{}
}

var PlatformConsumeReadServiceQueryConsumeLogByIdArgs_Log_DEFAULT *QueryConsumeLogReq

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) GetLog() *QueryConsumeLogReq {
	if !p.IsSetLog() {
		return PlatformConsumeReadServiceQueryConsumeLogByIdArgs_Log_DEFAULT
	}
	return p.Log
}
func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) IsSetLog() bool {
	return p.Log != nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetLog bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetLog = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetLog {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Log is not set"))
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Log = &QueryConsumeLogReq{}
	if err := p.Log.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Log), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "queryConsumeLogById_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "log", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:log: ", p), err)
	}
	if err := p.Log.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Log), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:log: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceQueryConsumeLogByIdArgs(%+v)", *p)
}

// Attributes:
//   - Success
type PlatformConsumeReadServiceQueryConsumeLogByIdResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformConsumeReadServiceQueryConsumeLogByIdResult() *PlatformConsumeReadServiceQueryConsumeLogByIdResult {
	return &PlatformConsumeReadServiceQueryConsumeLogByIdResult{}
}

var PlatformConsumeReadServiceQueryConsumeLogByIdResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return PlatformConsumeReadServiceQueryConsumeLogByIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "queryConsumeLogById_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *PlatformConsumeReadServiceQueryConsumeLogByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceQueryConsumeLogByIdResult(%+v)", *p)
}

// Attributes:
//   - KugouId
//   - AppId
type PlatformConsumeReadServiceGetUserAccountArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
	AppId   int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
}

func NewPlatformConsumeReadServiceGetUserAccountArgs() *PlatformConsumeReadServiceGetUserAccountArgs {
	return &PlatformConsumeReadServiceGetUserAccountArgs{}
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) GetKugouId() int64 {
	return p.KugouId
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) GetAppId() int32 {
	return p.AppId
}
func (p *PlatformConsumeReadServiceGetUserAccountArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false
	var issetAppId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAccount_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserAccountArgs(%+v)", *p)
}

// Attributes:
//   - Success
type PlatformConsumeReadServiceGetUserAccountResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformConsumeReadServiceGetUserAccountResult() *PlatformConsumeReadServiceGetUserAccountResult {
	return &PlatformConsumeReadServiceGetUserAccountResult{}
}

var PlatformConsumeReadServiceGetUserAccountResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *PlatformConsumeReadServiceGetUserAccountResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return PlatformConsumeReadServiceGetUserAccountResult_Success_DEFAULT
	}
	return p.Success
}
func (p *PlatformConsumeReadServiceGetUserAccountResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PlatformConsumeReadServiceGetUserAccountResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAccount_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAccountResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserAccountResult(%+v)", *p)
}

// Attributes:
//   - Req
type PlatformConsumeReadServiceGetUserAddBeanArgs struct {
	Req *QueryAddBeanReq `thrift:"req,1,required" db:"req" json:"req"`
}

func NewPlatformConsumeReadServiceGetUserAddBeanArgs() *PlatformConsumeReadServiceGetUserAddBeanArgs {
	return &PlatformConsumeReadServiceGetUserAddBeanArgs{}
}

var PlatformConsumeReadServiceGetUserAddBeanArgs_Req_DEFAULT *QueryAddBeanReq

func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) GetReq() *QueryAddBeanReq {
	if !p.IsSetReq() {
		return PlatformConsumeReadServiceGetUserAddBeanArgs_Req_DEFAULT
	}
	return p.Req
}
func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetReq bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetReq = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetReq {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Req is not set"))
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	p.Req = &QueryAddBeanReq{}
	if err := p.Req.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAddBean_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err)
	}
	if err := p.Req.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err)
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserAddBeanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserAddBeanArgs(%+v)", *p)
}

// Attributes:
//   - Success
type PlatformConsumeReadServiceGetUserAddBeanResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformConsumeReadServiceGetUserAddBeanResult() *PlatformConsumeReadServiceGetUserAddBeanResult {
	return &PlatformConsumeReadServiceGetUserAddBeanResult{}
}

var PlatformConsumeReadServiceGetUserAddBeanResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *PlatformConsumeReadServiceGetUserAddBeanResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return PlatformConsumeReadServiceGetUserAddBeanResult_Success_DEFAULT
	}
	return p.Success
}
func (p *PlatformConsumeReadServiceGetUserAddBeanResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserAddBean_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *PlatformConsumeReadServiceGetUserAddBeanResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *PlatformConsumeReadServiceGetUserAddBeanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PlatformConsumeReadServiceGetUserAddBeanResult(%+v)", *p)
}

type ConsumeReadMasterService interface { //拆分不同service，以便nginx灵活控制流量
	//

	// 获得用户金钱接口，实时，查主库(建议核心业务接入，接入前需预估流量)
	//
	// Parameters:
	//  - KugouId
	//  - AppId
	GetUserMoney(ctx context.Context, kugouId int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error)
}

// 拆分不同service，以便nginx灵活控制流量
type ConsumeReadMasterServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewConsumeReadMasterServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ConsumeReadMasterServiceClient {
	return &ConsumeReadMasterServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewConsumeReadMasterServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ConsumeReadMasterServiceClient {
	return &ConsumeReadMasterServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewConsumeReadMasterServiceClient(c thrift.TClient) *ConsumeReadMasterServiceClient {
	return &ConsumeReadMasterServiceClient{
		c: c,
	}
}

func (p *ConsumeReadMasterServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *ConsumeReadMasterServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *ConsumeReadMasterServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// 获得用户金钱接口，实时，查主库(建议核心业务接入，接入前需预估流量)
//
// Parameters:
//   - KugouId
//   - AppId
func (p *ConsumeReadMasterServiceClient) GetUserMoney(ctx context.Context, kugouId int64, appId int32) (_r *platform_consume_service.ConsumeResp, _err error) {
	var _args65 ConsumeReadMasterServiceGetUserMoneyArgs
	_args65.KugouId = kugouId
	_args65.AppId = appId
	var _result67 ConsumeReadMasterServiceGetUserMoneyResult
	var _meta66 thrift.ResponseMeta
	_meta66, _err = p.Client_().Call(ctx, "getUserMoney", &_args65, &_result67)
	p.SetLastResponseMeta_(_meta66)
	if _err != nil {
		return
	}
	if _ret68 := _result67.GetSuccess(); _ret68 != nil {
		return _ret68, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getUserMoney failed: unknown result")
}

type ConsumeReadMasterServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ConsumeReadMasterService
}

func (p *ConsumeReadMasterServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ConsumeReadMasterServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ConsumeReadMasterServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewConsumeReadMasterServiceProcessor(handler ConsumeReadMasterService) *ConsumeReadMasterServiceProcessor {

	self69 := &ConsumeReadMasterServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self69.processorMap["getUserMoney"] = &consumeReadMasterServiceProcessorGetUserMoney{handler: handler}
	return self69
}

func (p *ConsumeReadMasterServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x70 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x70.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x70

}

type consumeReadMasterServiceProcessorGetUserMoney struct {
	handler ConsumeReadMasterService
}

func (p *consumeReadMasterServiceProcessorGetUserMoney) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err71 error
	args := ConsumeReadMasterServiceGetUserMoneyArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getUserMoney", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := ConsumeReadMasterServiceGetUserMoneyResult{}
	if retval, err2 := p.handler.GetUserMoney(ctx, args.KugouId, args.AppId); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc72 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getUserMoney: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getUserMoney", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err71 = thrift.WrapTException(err2)
		}
		if err2 := _exc72.Write(ctx, oprot); _write_err71 == nil && err2 != nil {
			_write_err71 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err71 == nil && err2 != nil {
			_write_err71 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err71 == nil && err2 != nil {
			_write_err71 = thrift.WrapTException(err2)
		}
		if _write_err71 != nil {
			return false, thrift.WrapTException(_write_err71)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getUserMoney", thrift.REPLY, seqId); err2 != nil {
		_write_err71 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err71 == nil && err2 != nil {
		_write_err71 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err71 == nil && err2 != nil {
		_write_err71 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err71 == nil && err2 != nil {
		_write_err71 = thrift.WrapTException(err2)
	}
	if _write_err71 != nil {
		return false, thrift.WrapTException(_write_err71)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//   - KugouId
//   - AppId
type ConsumeReadMasterServiceGetUserMoneyArgs struct {
	KugouId int64 `thrift:"kugouId,1,required" db:"kugouId" json:"kugouId"`
	AppId   int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
}

func NewConsumeReadMasterServiceGetUserMoneyArgs() *ConsumeReadMasterServiceGetUserMoneyArgs {
	return &ConsumeReadMasterServiceGetUserMoneyArgs{}
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) GetKugouId() int64 {
	return p.KugouId
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) GetAppId() int32 {
	return p.AppId
}
func (p *ConsumeReadMasterServiceGetUserMoneyArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouId bool = false
	var issetAppId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetAppId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"))
	}
	if !issetAppId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"))
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.KugouId = v
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMoney_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouId: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouId: ", p), err)
	}
	return err
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err)
	}
	return err
}

func (p *ConsumeReadMasterServiceGetUserMoneyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeReadMasterServiceGetUserMoneyArgs(%+v)", *p)
}

// Attributes:
//   - Success
type ConsumeReadMasterServiceGetUserMoneyResult struct {
	Success *platform_consume_service.ConsumeResp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewConsumeReadMasterServiceGetUserMoneyResult() *ConsumeReadMasterServiceGetUserMoneyResult {
	return &ConsumeReadMasterServiceGetUserMoneyResult{}
}

var ConsumeReadMasterServiceGetUserMoneyResult_Success_DEFAULT *platform_consume_service.ConsumeResp

func (p *ConsumeReadMasterServiceGetUserMoneyResult) GetSuccess() *platform_consume_service.ConsumeResp {
	if !p.IsSetSuccess() {
		return ConsumeReadMasterServiceGetUserMoneyResult_Success_DEFAULT
	}
	return p.Success
}
func (p *ConsumeReadMasterServiceGetUserMoneyResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &platform_consume_service.ConsumeResp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getUserMoney_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *ConsumeReadMasterServiceGetUserMoneyResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *ConsumeReadMasterServiceGetUserMoneyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeReadMasterServiceGetUserMoneyResult(%+v)", *p)
}
