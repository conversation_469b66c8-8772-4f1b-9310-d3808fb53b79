// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package kugouadaptercallback

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - AppId
//  - OpenId
//  - RewardId
//  - Num
//  - OriginBillNo
type SendSingleRewardCallbackRequest struct {
  AppId string `thrift:"appId,1,required" db:"appId" json:"appId"`
  OpenId string `thrift:"openId,2,required" db:"openId" json:"openId"`
  RewardId int64 `thrift:"rewardId,3,required" db:"rewardId" json:"rewardId"`
  Num int32 `thrift:"num,4,required" db:"num" json:"num"`
  OriginBillNo string `thrift:"originBillNo,5,required" db:"originBillNo" json:"originBillNo"`
}

func NewSendSingleRewardCallbackRequest() *SendSingleRewardCallbackRequest {
  return &SendSingleRewardCallbackRequest{}
}


func (p *SendSingleRewardCallbackRequest) GetAppId() string {
  return p.AppId
}

func (p *SendSingleRewardCallbackRequest) GetOpenId() string {
  return p.OpenId
}

func (p *SendSingleRewardCallbackRequest) GetRewardId() int64 {
  return p.RewardId
}

func (p *SendSingleRewardCallbackRequest) GetNum() int32 {
  return p.Num
}

func (p *SendSingleRewardCallbackRequest) GetOriginBillNo() string {
  return p.OriginBillNo
}
func (p *SendSingleRewardCallbackRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetOpenId bool = false;
  var issetRewardId bool = false;
  var issetNum bool = false;
  var issetOriginBillNo bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetOpenId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetRewardId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetNum = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetOriginBillNo = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetOpenId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OpenId is not set"));
  }
  if !issetRewardId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RewardId is not set"));
  }
  if !issetNum{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Num is not set"));
  }
  if !issetOriginBillNo{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OriginBillNo is not set"));
  }
  return nil
}

func (p *SendSingleRewardCallbackRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *SendSingleRewardCallbackRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.OpenId = v
}
  return nil
}

func (p *SendSingleRewardCallbackRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.RewardId = v
}
  return nil
}

func (p *SendSingleRewardCallbackRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Num = v
}
  return nil
}

func (p *SendSingleRewardCallbackRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.OriginBillNo = v
}
  return nil
}

func (p *SendSingleRewardCallbackRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendSingleRewardCallbackRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendSingleRewardCallbackRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "openId", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:openId: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OpenId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.openId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:openId: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "rewardId", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:rewardId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.RewardId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.rewardId (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:rewardId: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "num", thrift.I32, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:num: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Num)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.num (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:num: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "originBillNo", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:originBillNo: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OriginBillNo)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.originBillNo (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:originBillNo: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackRequest) Equals(other *SendSingleRewardCallbackRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.OpenId != other.OpenId { return false }
  if p.RewardId != other.RewardId { return false }
  if p.Num != other.Num { return false }
  if p.OriginBillNo != other.OriginBillNo { return false }
  return true
}

func (p *SendSingleRewardCallbackRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendSingleRewardCallbackRequest(%+v)", *p)
}

// Attributes:
//  - Code
//  - Msg
//  - Data
type SendSingleRewardCallbackResponse struct {
  Code int32 `thrift:"code,1,required" db:"code" json:"code"`
  Msg string `thrift:"msg,2,required" db:"msg" json:"msg"`
  Data *SendSingleRewardCallbackData `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewSendSingleRewardCallbackResponse() *SendSingleRewardCallbackResponse {
  return &SendSingleRewardCallbackResponse{}
}


func (p *SendSingleRewardCallbackResponse) GetCode() int32 {
  return p.Code
}

func (p *SendSingleRewardCallbackResponse) GetMsg() string {
  return p.Msg
}
var SendSingleRewardCallbackResponse_Data_DEFAULT *SendSingleRewardCallbackData
func (p *SendSingleRewardCallbackResponse) GetData() *SendSingleRewardCallbackData {
  if !p.IsSetData() {
    return SendSingleRewardCallbackResponse_Data_DEFAULT
  }
return p.Data
}
func (p *SendSingleRewardCallbackResponse) IsSetData() bool {
  return p.Data != nil
}

func (p *SendSingleRewardCallbackResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetCode bool = false;
  var issetMsg bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetCode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetMsg = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetCode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"));
  }
  if !issetMsg{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"));
  }
  return nil
}

func (p *SendSingleRewardCallbackResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = v
}
  return nil
}

func (p *SendSingleRewardCallbackResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = v
}
  return nil
}

func (p *SendSingleRewardCallbackResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &SendSingleRewardCallbackData{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *SendSingleRewardCallbackResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendSingleRewardCallbackResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendSingleRewardCallbackResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  return err
}

func (p *SendSingleRewardCallbackResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *SendSingleRewardCallbackResponse) Equals(other *SendSingleRewardCallbackResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code { return false }
  if p.Msg != other.Msg { return false }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *SendSingleRewardCallbackResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendSingleRewardCallbackResponse(%+v)", *p)
}

type SendSingleRewardCallbackData struct {
}

func NewSendSingleRewardCallbackData() *SendSingleRewardCallbackData {
  return &SendSingleRewardCallbackData{}
}

func (p *SendSingleRewardCallbackData) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    if err := iprot.Skip(ctx, fieldTypeId); err != nil {
      return err
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *SendSingleRewardCallbackData) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SendSingleRewardCallbackData"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SendSingleRewardCallbackData) Equals(other *SendSingleRewardCallbackData) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  return true
}

func (p *SendSingleRewardCallbackData) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SendSingleRewardCallbackData(%+v)", *p)
}

type KugouAdapterCallbackService interface {
  // Parameters:
  //  - Request
  SendSingleRewardCallback(ctx context.Context, request *SendSingleRewardCallbackRequest) (_r *SendSingleRewardCallbackResponse, _err error)
}

type KugouAdapterCallbackServiceClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewKugouAdapterCallbackServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *KugouAdapterCallbackServiceClient {
  return &KugouAdapterCallbackServiceClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewKugouAdapterCallbackServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *KugouAdapterCallbackServiceClient {
  return &KugouAdapterCallbackServiceClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewKugouAdapterCallbackServiceClient(c thrift.TClient) *KugouAdapterCallbackServiceClient {
  return &KugouAdapterCallbackServiceClient{
    c: c,
  }
}

func (p *KugouAdapterCallbackServiceClient) Client_() thrift.TClient {
  return p.c
}

func (p *KugouAdapterCallbackServiceClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *KugouAdapterCallbackServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Request
func (p *KugouAdapterCallbackServiceClient) SendSingleRewardCallback(ctx context.Context, request *SendSingleRewardCallbackRequest) (_r *SendSingleRewardCallbackResponse, _err error) {
  var _args0 KugouAdapterCallbackServiceSendSingleRewardCallbackArgs
  _args0.Request = request
  var _result2 KugouAdapterCallbackServiceSendSingleRewardCallbackResult
  var _meta1 thrift.ResponseMeta
  _meta1, _err = p.Client_().Call(ctx, "sendSingleRewardCallback", &_args0, &_result2)
  p.SetLastResponseMeta_(_meta1)
  if _err != nil {
    return
  }
  if _ret3 := _result2.GetSuccess(); _ret3 != nil {
    return _ret3, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendSingleRewardCallback failed: unknown result")
}

type KugouAdapterCallbackServiceProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler KugouAdapterCallbackService
}

func (p *KugouAdapterCallbackServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *KugouAdapterCallbackServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *KugouAdapterCallbackServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewKugouAdapterCallbackServiceProcessor(handler KugouAdapterCallbackService) *KugouAdapterCallbackServiceProcessor {

  self4 := &KugouAdapterCallbackServiceProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self4.processorMap["sendSingleRewardCallback"] = &kugouAdapterCallbackServiceProcessorSendSingleRewardCallback{handler:handler}
return self4
}

func (p *KugouAdapterCallbackServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x5.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x5

}

type kugouAdapterCallbackServiceProcessorSendSingleRewardCallback struct {
  handler KugouAdapterCallbackService
}

func (p *kugouAdapterCallbackServiceProcessorSendSingleRewardCallback) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err6 error
  args := KugouAdapterCallbackServiceSendSingleRewardCallbackArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "sendSingleRewardCallback", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := KugouAdapterCallbackServiceSendSingleRewardCallbackResult{}
  if retval, err2 := p.handler.SendSingleRewardCallback(ctx, args.Request); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc7 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendSingleRewardCallback: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "sendSingleRewardCallback", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if err2 := _exc7.Write(ctx, oprot); _write_err6 == nil && err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err6 == nil && err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err6 == nil && err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if _write_err6 != nil {
      return false, thrift.WrapTException(_write_err6)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "sendSingleRewardCallback", thrift.REPLY, seqId); err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err6 == nil && err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err6 == nil && err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err6 == nil && err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if _write_err6 != nil {
    return false, thrift.WrapTException(_write_err6)
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Request
type KugouAdapterCallbackServiceSendSingleRewardCallbackArgs struct {
  Request *SendSingleRewardCallbackRequest `thrift:"request,1,required" db:"request" json:"request"`
}

func NewKugouAdapterCallbackServiceSendSingleRewardCallbackArgs() *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs {
  return &KugouAdapterCallbackServiceSendSingleRewardCallbackArgs{}
}

var KugouAdapterCallbackServiceSendSingleRewardCallbackArgs_Request_DEFAULT *SendSingleRewardCallbackRequest
func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs) GetRequest() *SendSingleRewardCallbackRequest {
  if !p.IsSetRequest() {
    return KugouAdapterCallbackServiceSendSingleRewardCallbackArgs_Request_DEFAULT
  }
return p.Request
}
func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs) IsSetRequest() bool {
  return p.Request != nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRequest bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRequest = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRequest{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Request is not set"));
  }
  return nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Request = &SendSingleRewardCallbackRequest{}
  if err := p.Request.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
  }
  return nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "sendSingleRewardCallback_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:request: ", p), err) }
  if err := p.Request.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:request: ", p), err) }
  return err
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("KugouAdapterCallbackServiceSendSingleRewardCallbackArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KugouAdapterCallbackServiceSendSingleRewardCallbackResult struct {
  Success *SendSingleRewardCallbackResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKugouAdapterCallbackServiceSendSingleRewardCallbackResult() *KugouAdapterCallbackServiceSendSingleRewardCallbackResult {
  return &KugouAdapterCallbackServiceSendSingleRewardCallbackResult{}
}

var KugouAdapterCallbackServiceSendSingleRewardCallbackResult_Success_DEFAULT *SendSingleRewardCallbackResponse
func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult) GetSuccess() *SendSingleRewardCallbackResponse {
  if !p.IsSetSuccess() {
    return KugouAdapterCallbackServiceSendSingleRewardCallbackResult_Success_DEFAULT
  }
return p.Success
}
func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &SendSingleRewardCallbackResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "sendSingleRewardCallback_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *KugouAdapterCallbackServiceSendSingleRewardCallbackResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("KugouAdapterCallbackServiceSendSingleRewardCallbackResult(%+v)", *p)
}


