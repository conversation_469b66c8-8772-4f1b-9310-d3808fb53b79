// Code generated by <PERSON>hrift Compiler (0.17.0). DO NOT EDIT.

package strategyservice

import (
	"bytes"
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

type EvilLevel int64
const (
  EvilLevel_SAFT EvilLevel = 0
  EvilLevel_WARN EvilLevel = 1000
  EvilLevel_DANGEROUS EvilLevel = 10000
)

func (p EvilLevel) String() string {
  switch p {
  case EvilLevel_SAFT: return "SAFT"
  case EvilLevel_WARN: return "WARN"
  case EvilLevel_DANGEROUS: return "DANGEROUS"
  }
  return "<UNSET>"
}

func EvilLevelFromString(s string) (EvilLevel, error) {
  switch s {
  case "SAFT": return EvilLevel_SAFT, nil 
  case "WARN": return EvilLevel_WARN, nil 
  case "DANGEROUS": return EvilLevel_DANGEROUS, nil 
  }
  return EvilLevel(0), fmt.<PERSON><PERSON><PERSON>("not a valid EvilLevel string")
}


func EvilLevelPtr(v EvilLevel) *EvilLevel { return &v }

func (p EvilLevel) MarshalText() ([]byte, error) {
return []byte(p.String()), nil
}

func (p *EvilLevel) UnmarshalText(text []byte) error {
q, err := EvilLevelFromString(string(text))
if (err != nil) {
return err
}
*p = q
return nil
}

func (p *EvilLevel) Scan(value interface{}) error {
v, ok := value.(int64)
if !ok {
return errors.New("Scan value is not int64")
}
*p = EvilLevel(v)
return nil
}

func (p * EvilLevel) Value() (driver.Value, error) {
  if p == nil {
    return nil, nil
  }
return int64(*p), nil
}
type Measure int64
const (
  Measure_BLANK Measure = 0
  Measure_PICCODE Measure = 1
  Measure_PASSWORD Measure = 2
  Measure_SMSCODE Measure = 4
  Measure_BINDMOBILE Measure = 8
  Measure_BINDWEIXIN Measure = 16
  Measure_BINDMW Measure = 32
  Measure_FACESCAN Measure = 64
  Measure_SLIDE Measure = 128
  Measure_KGVERIFY Measure = 256
  Measure_FREEZE Measure = 100
  Measure_FXVERIFY Measure = 512
)

func (p Measure) String() string {
  switch p {
  case Measure_BLANK: return "BLANK"
  case Measure_PICCODE: return "PICCODE"
  case Measure_PASSWORD: return "PASSWORD"
  case Measure_SMSCODE: return "SMSCODE"
  case Measure_BINDMOBILE: return "BINDMOBILE"
  case Measure_BINDWEIXIN: return "BINDWEIXIN"
  case Measure_BINDMW: return "BINDMW"
  case Measure_FACESCAN: return "FACESCAN"
  case Measure_SLIDE: return "SLIDE"
  case Measure_KGVERIFY: return "KGVERIFY"
  case Measure_FREEZE: return "FREEZE"
  case Measure_FXVERIFY: return "FXVERIFY"
  }
  return "<UNSET>"
}

func MeasureFromString(s string) (Measure, error) {
  switch s {
  case "BLANK": return Measure_BLANK, nil 
  case "PICCODE": return Measure_PICCODE, nil 
  case "PASSWORD": return Measure_PASSWORD, nil 
  case "SMSCODE": return Measure_SMSCODE, nil 
  case "BINDMOBILE": return Measure_BINDMOBILE, nil 
  case "BINDWEIXIN": return Measure_BINDWEIXIN, nil 
  case "BINDMW": return Measure_BINDMW, nil 
  case "FACESCAN": return Measure_FACESCAN, nil 
  case "SLIDE": return Measure_SLIDE, nil 
  case "KGVERIFY": return Measure_KGVERIFY, nil 
  case "FREEZE": return Measure_FREEZE, nil 
  case "FXVERIFY": return Measure_FXVERIFY, nil 
  }
  return Measure(0), fmt.Errorf("not a valid Measure string")
}


func MeasurePtr(v Measure) *Measure { return &v }

func (p Measure) MarshalText() ([]byte, error) {
return []byte(p.String()), nil
}

func (p *Measure) UnmarshalText(text []byte) error {
q, err := MeasureFromString(string(text))
if (err != nil) {
return err
}
*p = q
return nil
}

func (p *Measure) Scan(value interface{}) error {
v, ok := value.(int64)
if !ok {
return errors.New("Scan value is not int64")
}
*p = Measure(v)
return nil
}

func (p * Measure) Value() (driver.Value, error) {
  if p == nil {
    return nil, nil
  }
return int64(*p), nil
}
// Attributes:
//  - Appid: 应用ID
//  - Biz: 业务编码
//  - Endtype: 终端类型：pc或mobile
//  - Sid: 业务流水ID,如消费ID
//  - KugouId: 酷狗ID
//  - IP: IP
//  - DeviceId: 设备ID
//  - Ts: 操作时间戳，单位毫秒
//  - Data: 业务数据，json格式字符串（按业务制定协议）
//  - RoomId: 房间号
//  - Clientver: 客户端版本
//  - Platform: 平台号
//  - ClentType: 客户端类型 - 0：H5，1：ios，2:android
type StrategyVO struct {
  Appid string `thrift:"appid,1,required" db:"appid" json:"appid"`
  Biz string `thrift:"biz,2,required" db:"biz" json:"biz"`
  Endtype string `thrift:"endtype,3,required" db:"endtype" json:"endtype"`
  Sid string `thrift:"sid,4,required" db:"sid" json:"sid"`
  KugouId int64 `thrift:"kugouId,5,required" db:"kugouId" json:"kugouId"`
  IP string `thrift:"ip,6,required" db:"ip" json:"ip"`
  DeviceId string `thrift:"deviceId,7,required" db:"deviceId" json:"deviceId"`
  Ts int64 `thrift:"ts,8,required" db:"ts" json:"ts"`
  Data *string `thrift:"data,9" db:"data" json:"data,omitempty"`
  RoomId *int64 `thrift:"roomId,10" db:"roomId" json:"roomId,omitempty"`
  Clientver *string `thrift:"clientver,11" db:"clientver" json:"clientver,omitempty"`
  Platform *int32 `thrift:"platform,12" db:"platform" json:"platform,omitempty"`
  ClentType *int32 `thrift:"clentType,13" db:"clentType" json:"clentType,omitempty"`
}

func NewStrategyVO() *StrategyVO {
  return &StrategyVO{}
}


func (p *StrategyVO) GetAppid() string {
  return p.Appid
}

func (p *StrategyVO) GetBiz() string {
  return p.Biz
}

func (p *StrategyVO) GetEndtype() string {
  return p.Endtype
}

func (p *StrategyVO) GetSid() string {
  return p.Sid
}

func (p *StrategyVO) GetKugouId() int64 {
  return p.KugouId
}

func (p *StrategyVO) GetIP() string {
  return p.IP
}

func (p *StrategyVO) GetDeviceId() string {
  return p.DeviceId
}

func (p *StrategyVO) GetTs() int64 {
  return p.Ts
}
var StrategyVO_Data_DEFAULT string
func (p *StrategyVO) GetData() string {
  if !p.IsSetData() {
    return StrategyVO_Data_DEFAULT
  }
return *p.Data
}
var StrategyVO_RoomId_DEFAULT int64
func (p *StrategyVO) GetRoomId() int64 {
  if !p.IsSetRoomId() {
    return StrategyVO_RoomId_DEFAULT
  }
return *p.RoomId
}
var StrategyVO_Clientver_DEFAULT string
func (p *StrategyVO) GetClientver() string {
  if !p.IsSetClientver() {
    return StrategyVO_Clientver_DEFAULT
  }
return *p.Clientver
}
var StrategyVO_Platform_DEFAULT int32
func (p *StrategyVO) GetPlatform() int32 {
  if !p.IsSetPlatform() {
    return StrategyVO_Platform_DEFAULT
  }
return *p.Platform
}
var StrategyVO_ClentType_DEFAULT int32
func (p *StrategyVO) GetClentType() int32 {
  if !p.IsSetClentType() {
    return StrategyVO_ClentType_DEFAULT
  }
return *p.ClentType
}
func (p *StrategyVO) IsSetData() bool {
  return p.Data != nil
}

func (p *StrategyVO) IsSetRoomId() bool {
  return p.RoomId != nil
}

func (p *StrategyVO) IsSetClientver() bool {
  return p.Clientver != nil
}

func (p *StrategyVO) IsSetPlatform() bool {
  return p.Platform != nil
}

func (p *StrategyVO) IsSetClentType() bool {
  return p.ClentType != nil
}

func (p *StrategyVO) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppid bool = false;
  var issetBiz bool = false;
  var issetEndtype bool = false;
  var issetSid bool = false;
  var issetKugouId bool = false;
  var issetIP bool = false;
  var issetDeviceId bool = false;
  var issetTs bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppid = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetBiz = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetEndtype = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetSid = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetIP = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
        issetDeviceId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
        issetTs = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppid{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Appid is not set"));
  }
  if !issetBiz{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Biz is not set"));
  }
  if !issetEndtype{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Endtype is not set"));
  }
  if !issetSid{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sid is not set"));
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetIP{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IP is not set"));
  }
  if !issetDeviceId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field DeviceId is not set"));
  }
  if !issetTs{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ts is not set"));
  }
  return nil
}

func (p *StrategyVO)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Appid = v
}
  return nil
}

func (p *StrategyVO)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Biz = v
}
  return nil
}

func (p *StrategyVO)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Endtype = v
}
  return nil
}

func (p *StrategyVO)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Sid = v
}
  return nil
}

func (p *StrategyVO)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *StrategyVO)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.IP = v
}
  return nil
}

func (p *StrategyVO)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.DeviceId = v
}
  return nil
}

func (p *StrategyVO)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Ts = v
}
  return nil
}

func (p *StrategyVO)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Data = &v
}
  return nil
}

func (p *StrategyVO)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.RoomId = &v
}
  return nil
}

func (p *StrategyVO)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.Clientver = &v
}
  return nil
}

func (p *StrategyVO)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.Platform = &v
}
  return nil
}

func (p *StrategyVO)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.ClentType = &v
}
  return nil
}

func (p *StrategyVO) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StrategyVO"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StrategyVO) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appid", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appid: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Appid)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appid (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appid: ", p), err) }
  return err
}

func (p *StrategyVO) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "biz", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:biz: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Biz)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.biz (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:biz: ", p), err) }
  return err
}

func (p *StrategyVO) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "endtype", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:endtype: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Endtype)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.endtype (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:endtype: ", p), err) }
  return err
}

func (p *StrategyVO) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sid", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:sid: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sid)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sid (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:sid: ", p), err) }
  return err
}

func (p *StrategyVO) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:kugouId: ", p), err) }
  return err
}

func (p *StrategyVO) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ip", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:ip: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.IP)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ip (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:ip: ", p), err) }
  return err
}

func (p *StrategyVO) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "deviceId", thrift.STRING, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:deviceId: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.DeviceId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.deviceId (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:deviceId: ", p), err) }
  return err
}

func (p *StrategyVO) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ts", thrift.I64, 8); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:ts: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Ts)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ts (8) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 8:ts: ", p), err) }
  return err
}

func (p *StrategyVO) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:data: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Data)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:data: ", p), err) }
  }
  return err
}

func (p *StrategyVO) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRoomId() {
    if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.I64, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:roomId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.RoomId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.roomId (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:roomId: ", p), err) }
  }
  return err
}

func (p *StrategyVO) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientver() {
    if err := oprot.WriteFieldBegin(ctx, "clientver", thrift.STRING, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:clientver: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Clientver)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientver (11) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:clientver: ", p), err) }
  }
  return err
}

func (p *StrategyVO) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPlatform() {
    if err := oprot.WriteFieldBegin(ctx, "platform", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:platform: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Platform)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.platform (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:platform: ", p), err) }
  }
  return err
}

func (p *StrategyVO) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClentType() {
    if err := oprot.WriteFieldBegin(ctx, "clentType", thrift.I32, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:clentType: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.ClentType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clentType (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:clentType: ", p), err) }
  }
  return err
}

func (p *StrategyVO) Equals(other *StrategyVO) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Appid != other.Appid { return false }
  if p.Biz != other.Biz { return false }
  if p.Endtype != other.Endtype { return false }
  if p.Sid != other.Sid { return false }
  if p.KugouId != other.KugouId { return false }
  if p.IP != other.IP { return false }
  if p.DeviceId != other.DeviceId { return false }
  if p.Ts != other.Ts { return false }
  if p.Data != other.Data {
    if p.Data == nil || other.Data == nil {
      return false
    }
    if (*p.Data) != (*other.Data) { return false }
  }
  if p.RoomId != other.RoomId {
    if p.RoomId == nil || other.RoomId == nil {
      return false
    }
    if (*p.RoomId) != (*other.RoomId) { return false }
  }
  if p.Clientver != other.Clientver {
    if p.Clientver == nil || other.Clientver == nil {
      return false
    }
    if (*p.Clientver) != (*other.Clientver) { return false }
  }
  if p.Platform != other.Platform {
    if p.Platform == nil || other.Platform == nil {
      return false
    }
    if (*p.Platform) != (*other.Platform) { return false }
  }
  if p.ClentType != other.ClentType {
    if p.ClentType == nil || other.ClentType == nil {
      return false
    }
    if (*p.ClentType) != (*other.ClentType) { return false }
  }
  return true
}

func (p *StrategyVO) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StrategyVO(%+v)", *p)
}

// Attributes:
//  - Recode: 返回码
//  - Message: 返回说明
//  - Data: 策略结果
type StrategyResult_ struct {
  Recode int32 `thrift:"recode,1,required" db:"recode" json:"recode"`
  Message *string `thrift:"message,2" db:"message" json:"message,omitempty"`
  Data *Result_ `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewStrategyResult_() *StrategyResult_ {
  return &StrategyResult_{}
}


func (p *StrategyResult_) GetRecode() int32 {
  return p.Recode
}
var StrategyResult__Message_DEFAULT string
func (p *StrategyResult_) GetMessage() string {
  if !p.IsSetMessage() {
    return StrategyResult__Message_DEFAULT
  }
return *p.Message
}
var StrategyResult__Data_DEFAULT *Result_
func (p *StrategyResult_) GetData() *Result_ {
  if !p.IsSetData() {
    return StrategyResult__Data_DEFAULT
  }
return p.Data
}
func (p *StrategyResult_) IsSetMessage() bool {
  return p.Message != nil
}

func (p *StrategyResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *StrategyResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRecode bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRecode = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRecode{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Recode is not set"));
  }
  return nil
}

func (p *StrategyResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Recode = v
}
  return nil
}

func (p *StrategyResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Message = &v
}
  return nil
}

func (p *StrategyResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &Result_{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *StrategyResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "StrategyResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *StrategyResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "recode", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:recode: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Recode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.recode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:recode: ", p), err) }
  return err
}

func (p *StrategyResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMessage() {
    if err := oprot.WriteFieldBegin(ctx, "message", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:message: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Message)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.message (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:message: ", p), err) }
  }
  return err
}

func (p *StrategyResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *StrategyResult_) Equals(other *StrategyResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Recode != other.Recode { return false }
  if p.Message != other.Message {
    if p.Message == nil || other.Message == nil {
      return false
    }
    if (*p.Message) != (*other.Message) { return false }
  }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *StrategyResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("StrategyResult_(%+v)", *p)
}

// Attributes:
//  - EvilLevel: 恶意等级
//  - BizRecode: 业务返回码
//  - Measure: 风控处理手段
//  - Mtoken: 调起验证时的token：
// 1、调用的是大酷狗的通用验证时，这里返回的是eventId。前端利用eventId调起验证；
// 2、调用扫脸时，这里返回的是transactionId；
//  - Message: 风险提示信息
type Result_ struct {
  EvilLevel *int32 `thrift:"evilLevel,1" db:"evilLevel" json:"evilLevel,omitempty"`
  BizRecode *string `thrift:"bizRecode,2" db:"bizRecode" json:"bizRecode,omitempty"`
  Measure *Measure `thrift:"measure,3" db:"measure" json:"measure,omitempty"`
  Mtoken *string `thrift:"mtoken,4" db:"mtoken" json:"mtoken,omitempty"`
  Message *string `thrift:"message,5" db:"message" json:"message,omitempty"`
}

func NewResult_() *Result_ {
  return &Result_{}
}

var Result__EvilLevel_DEFAULT int32
func (p *Result_) GetEvilLevel() int32 {
  if !p.IsSetEvilLevel() {
    return Result__EvilLevel_DEFAULT
  }
return *p.EvilLevel
}
var Result__BizRecode_DEFAULT string
func (p *Result_) GetBizRecode() string {
  if !p.IsSetBizRecode() {
    return Result__BizRecode_DEFAULT
  }
return *p.BizRecode
}
var Result__Measure_DEFAULT Measure
func (p *Result_) GetMeasure() Measure {
  if !p.IsSetMeasure() {
    return Result__Measure_DEFAULT
  }
return *p.Measure
}
var Result__Mtoken_DEFAULT string
func (p *Result_) GetMtoken() string {
  if !p.IsSetMtoken() {
    return Result__Mtoken_DEFAULT
  }
return *p.Mtoken
}
var Result__Message_DEFAULT string
func (p *Result_) GetMessage() string {
  if !p.IsSetMessage() {
    return Result__Message_DEFAULT
  }
return *p.Message
}
func (p *Result_) IsSetEvilLevel() bool {
  return p.EvilLevel != nil
}

func (p *Result_) IsSetBizRecode() bool {
  return p.BizRecode != nil
}

func (p *Result_) IsSetMeasure() bool {
  return p.Measure != nil
}

func (p *Result_) IsSetMtoken() bool {
  return p.Mtoken != nil
}

func (p *Result_) IsSetMessage() bool {
  return p.Message != nil
}

func (p *Result_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *Result_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.EvilLevel = &v
}
  return nil
}

func (p *Result_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.BizRecode = &v
}
  return nil
}

func (p *Result_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  temp := Measure(v)
  p.Measure = &temp
}
  return nil
}

func (p *Result_)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Mtoken = &v
}
  return nil
}

func (p *Result_)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Message = &v
}
  return nil
}

func (p *Result_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *Result_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetEvilLevel() {
    if err := oprot.WriteFieldBegin(ctx, "evilLevel", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:evilLevel: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.EvilLevel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.evilLevel (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:evilLevel: ", p), err) }
  }
  return err
}

func (p *Result_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetBizRecode() {
    if err := oprot.WriteFieldBegin(ctx, "bizRecode", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:bizRecode: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.BizRecode)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.bizRecode (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:bizRecode: ", p), err) }
  }
  return err
}

func (p *Result_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMeasure() {
    if err := oprot.WriteFieldBegin(ctx, "measure", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:measure: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Measure)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.measure (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:measure: ", p), err) }
  }
  return err
}

func (p *Result_) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMtoken() {
    if err := oprot.WriteFieldBegin(ctx, "mtoken", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:mtoken: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Mtoken)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.mtoken (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:mtoken: ", p), err) }
  }
  return err
}

func (p *Result_) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMessage() {
    if err := oprot.WriteFieldBegin(ctx, "message", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:message: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Message)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.message (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:message: ", p), err) }
  }
  return err
}

func (p *Result_) Equals(other *Result_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.EvilLevel != other.EvilLevel {
    if p.EvilLevel == nil || other.EvilLevel == nil {
      return false
    }
    if (*p.EvilLevel) != (*other.EvilLevel) { return false }
  }
  if p.BizRecode != other.BizRecode {
    if p.BizRecode == nil || other.BizRecode == nil {
      return false
    }
    if (*p.BizRecode) != (*other.BizRecode) { return false }
  }
  if p.Measure != other.Measure {
    if p.Measure == nil || other.Measure == nil {
      return false
    }
    if (*p.Measure) != (*other.Measure) { return false }
  }
  if p.Mtoken != other.Mtoken {
    if p.Mtoken == nil || other.Mtoken == nil {
      return false
    }
    if (*p.Mtoken) != (*other.Mtoken) { return false }
  }
  if p.Message != other.Message {
    if p.Message == nil || other.Message == nil {
      return false
    }
    if (*p.Message) != (*other.Message) { return false }
  }
  return true
}

func (p *Result_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("Result_(%+v)", *p)
}

type PlatformStrategyService interface {
  // Parameters:
  //  - Vo
  Conclude(ctx context.Context, vo *StrategyVO) (_r *StrategyResult_, _err error)
}

type PlatformStrategyServiceClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewPlatformStrategyServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PlatformStrategyServiceClient {
  return &PlatformStrategyServiceClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewPlatformStrategyServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PlatformStrategyServiceClient {
  return &PlatformStrategyServiceClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewPlatformStrategyServiceClient(c thrift.TClient) *PlatformStrategyServiceClient {
  return &PlatformStrategyServiceClient{
    c: c,
  }
}

func (p *PlatformStrategyServiceClient) Client_() thrift.TClient {
  return p.c
}

func (p *PlatformStrategyServiceClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *PlatformStrategyServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Vo
func (p *PlatformStrategyServiceClient) Conclude(ctx context.Context, vo *StrategyVO) (_r *StrategyResult_, _err error) {
  var _args0 PlatformStrategyServiceConcludeArgs
  _args0.Vo = vo
  var _result2 PlatformStrategyServiceConcludeResult
  var _meta1 thrift.ResponseMeta
  _meta1, _err = p.Client_().Call(ctx, "conclude", &_args0, &_result2)
  p.SetLastResponseMeta_(_meta1)
  if _err != nil {
    return
  }
  if _ret3 := _result2.GetSuccess(); _ret3 != nil {
    return _ret3, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "conclude failed: unknown result")
}

type PlatformStrategyServiceProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler PlatformStrategyService
}

func (p *PlatformStrategyServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *PlatformStrategyServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *PlatformStrategyServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewPlatformStrategyServiceProcessor(handler PlatformStrategyService) *PlatformStrategyServiceProcessor {

  self4 := &PlatformStrategyServiceProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self4.processorMap["conclude"] = &platformStrategyServiceProcessorConclude{handler:handler}
return self4
}

func (p *PlatformStrategyServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x5.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x5

}

type platformStrategyServiceProcessorConclude struct {
  handler PlatformStrategyService
}

func (p *platformStrategyServiceProcessorConclude) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err6 error
  args := PlatformStrategyServiceConcludeArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "conclude", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := PlatformStrategyServiceConcludeResult{}
  if retval, err2 := p.handler.Conclude(ctx, args.Vo); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc7 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing conclude: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "conclude", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if err2 := _exc7.Write(ctx, oprot); _write_err6 == nil && err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err6 == nil && err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err6 == nil && err2 != nil {
      _write_err6 = thrift.WrapTException(err2)
    }
    if _write_err6 != nil {
      return false, thrift.WrapTException(_write_err6)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "conclude", thrift.REPLY, seqId); err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err6 == nil && err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err6 == nil && err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err6 == nil && err2 != nil {
    _write_err6 = thrift.WrapTException(err2)
  }
  if _write_err6 != nil {
    return false, thrift.WrapTException(_write_err6)
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Vo
type PlatformStrategyServiceConcludeArgs struct {
  Vo *StrategyVO `thrift:"vo,1,required" db:"vo" json:"vo"`
}

func NewPlatformStrategyServiceConcludeArgs() *PlatformStrategyServiceConcludeArgs {
  return &PlatformStrategyServiceConcludeArgs{}
}

var PlatformStrategyServiceConcludeArgs_Vo_DEFAULT *StrategyVO
func (p *PlatformStrategyServiceConcludeArgs) GetVo() *StrategyVO {
  if !p.IsSetVo() {
    return PlatformStrategyServiceConcludeArgs_Vo_DEFAULT
  }
return p.Vo
}
func (p *PlatformStrategyServiceConcludeArgs) IsSetVo() bool {
  return p.Vo != nil
}

func (p *PlatformStrategyServiceConcludeArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetVo bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetVo = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetVo{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Vo is not set"));
  }
  return nil
}

func (p *PlatformStrategyServiceConcludeArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Vo = &StrategyVO{}
  if err := p.Vo.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Vo), err)
  }
  return nil
}

func (p *PlatformStrategyServiceConcludeArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "conclude_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PlatformStrategyServiceConcludeArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "vo", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:vo: ", p), err) }
  if err := p.Vo.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Vo), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:vo: ", p), err) }
  return err
}

func (p *PlatformStrategyServiceConcludeArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PlatformStrategyServiceConcludeArgs(%+v)", *p)
}

// Attributes:
//  - Success
type PlatformStrategyServiceConcludeResult struct {
  Success *StrategyResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewPlatformStrategyServiceConcludeResult() *PlatformStrategyServiceConcludeResult {
  return &PlatformStrategyServiceConcludeResult{}
}

var PlatformStrategyServiceConcludeResult_Success_DEFAULT *StrategyResult_
func (p *PlatformStrategyServiceConcludeResult) GetSuccess() *StrategyResult_ {
  if !p.IsSetSuccess() {
    return PlatformStrategyServiceConcludeResult_Success_DEFAULT
  }
return p.Success
}
func (p *PlatformStrategyServiceConcludeResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *PlatformStrategyServiceConcludeResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PlatformStrategyServiceConcludeResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &StrategyResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *PlatformStrategyServiceConcludeResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "conclude_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PlatformStrategyServiceConcludeResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *PlatformStrategyServiceConcludeResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PlatformStrategyServiceConcludeResult(%+v)", *p)
}


