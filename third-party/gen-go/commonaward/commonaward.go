// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package commonaward

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - GoodsType
//  - GoodsId
//  - GoodsNum
//  - GoodsDesc
//  - GoodsPic
//  - GoodsSort
//  - GoodsExt
//  - GoodsOrderId
//  - Rate
//  - GoodsMagnification
//  - SubGoodsList
type GoodsInfo struct {
  GoodsType int32 `thrift:"goodsType,1,required" db:"goodsType" json:"goodsType"`
  GoodsId int64 `thrift:"goodsId,2,required" db:"goodsId" json:"goodsId"`
  GoodsNum int32 `thrift:"goodsNum,3,required" db:"goodsNum" json:"goodsNum"`
  GoodsDesc string `thrift:"goodsDesc,4,required" db:"goodsDesc" json:"goodsDesc"`
  GoodsPic *string `thrift:"goodsPic,5" db:"goodsPic" json:"goodsPic,omitempty"`
  GoodsSort *int32 `thrift:"goodsSort,6" db:"goodsSort" json:"goodsSort,omitempty"`
  GoodsExt *string `thrift:"goodsExt,7" db:"goodsExt" json:"goodsExt,omitempty"`
  GoodsOrderId *int64 `thrift:"goodsOrderId,8" db:"goodsOrderId" json:"goodsOrderId,omitempty"`
  Rate *float64 `thrift:"rate,9" db:"rate" json:"rate,omitempty"`
  GoodsMagnification *string `thrift:"goodsMagnification,10" db:"goodsMagnification" json:"goodsMagnification,omitempty"`
  SubGoodsList []*GoodsInfo `thrift:"subGoodsList,11" db:"subGoodsList" json:"subGoodsList,omitempty"`
}

func NewGoodsInfo() *GoodsInfo {
  return &GoodsInfo{}
}


func (p *GoodsInfo) GetGoodsType() int32 {
  return p.GoodsType
}

func (p *GoodsInfo) GetGoodsId() int64 {
  return p.GoodsId
}

func (p *GoodsInfo) GetGoodsNum() int32 {
  return p.GoodsNum
}

func (p *GoodsInfo) GetGoodsDesc() string {
  return p.GoodsDesc
}
var GoodsInfo_GoodsPic_DEFAULT string
func (p *GoodsInfo) GetGoodsPic() string {
  if !p.IsSetGoodsPic() {
    return GoodsInfo_GoodsPic_DEFAULT
  }
return *p.GoodsPic
}
var GoodsInfo_GoodsSort_DEFAULT int32
func (p *GoodsInfo) GetGoodsSort() int32 {
  if !p.IsSetGoodsSort() {
    return GoodsInfo_GoodsSort_DEFAULT
  }
return *p.GoodsSort
}
var GoodsInfo_GoodsExt_DEFAULT string
func (p *GoodsInfo) GetGoodsExt() string {
  if !p.IsSetGoodsExt() {
    return GoodsInfo_GoodsExt_DEFAULT
  }
return *p.GoodsExt
}
var GoodsInfo_GoodsOrderId_DEFAULT int64
func (p *GoodsInfo) GetGoodsOrderId() int64 {
  if !p.IsSetGoodsOrderId() {
    return GoodsInfo_GoodsOrderId_DEFAULT
  }
return *p.GoodsOrderId
}
var GoodsInfo_Rate_DEFAULT float64
func (p *GoodsInfo) GetRate() float64 {
  if !p.IsSetRate() {
    return GoodsInfo_Rate_DEFAULT
  }
return *p.Rate
}
var GoodsInfo_GoodsMagnification_DEFAULT string
func (p *GoodsInfo) GetGoodsMagnification() string {
  if !p.IsSetGoodsMagnification() {
    return GoodsInfo_GoodsMagnification_DEFAULT
  }
return *p.GoodsMagnification
}
var GoodsInfo_SubGoodsList_DEFAULT []*GoodsInfo

func (p *GoodsInfo) GetSubGoodsList() []*GoodsInfo {
  return p.SubGoodsList
}
func (p *GoodsInfo) IsSetGoodsPic() bool {
  return p.GoodsPic != nil
}

func (p *GoodsInfo) IsSetGoodsSort() bool {
  return p.GoodsSort != nil
}

func (p *GoodsInfo) IsSetGoodsExt() bool {
  return p.GoodsExt != nil
}

func (p *GoodsInfo) IsSetGoodsOrderId() bool {
  return p.GoodsOrderId != nil
}

func (p *GoodsInfo) IsSetRate() bool {
  return p.Rate != nil
}

func (p *GoodsInfo) IsSetGoodsMagnification() bool {
  return p.GoodsMagnification != nil
}

func (p *GoodsInfo) IsSetSubGoodsList() bool {
  return p.SubGoodsList != nil
}

func (p *GoodsInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetGoodsType bool = false;
  var issetGoodsId bool = false;
  var issetGoodsNum bool = false;
  var issetGoodsDesc bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetGoodsType = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetGoodsId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetGoodsNum = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetGoodsDesc = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetGoodsType{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GoodsType is not set"));
  }
  if !issetGoodsId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GoodsId is not set"));
  }
  if !issetGoodsNum{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GoodsNum is not set"));
  }
  if !issetGoodsDesc{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GoodsDesc is not set"));
  }
  return nil
}

func (p *GoodsInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.GoodsType = v
}
  return nil
}

func (p *GoodsInfo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.GoodsId = v
}
  return nil
}

func (p *GoodsInfo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.GoodsNum = v
}
  return nil
}

func (p *GoodsInfo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.GoodsDesc = v
}
  return nil
}

func (p *GoodsInfo)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.GoodsPic = &v
}
  return nil
}

func (p *GoodsInfo)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.GoodsSort = &v
}
  return nil
}

func (p *GoodsInfo)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.GoodsExt = &v
}
  return nil
}

func (p *GoodsInfo)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.GoodsOrderId = &v
}
  return nil
}

func (p *GoodsInfo)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Rate = &v
}
  return nil
}

func (p *GoodsInfo)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.GoodsMagnification = &v
}
  return nil
}

func (p *GoodsInfo)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*GoodsInfo, 0, size)
  p.SubGoodsList =  tSlice
  for i := 0; i < size; i ++ {
    _elem0 := &GoodsInfo{}
    if err := _elem0.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
    }
    p.SubGoodsList = append(p.SubGoodsList, _elem0)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GoodsInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GoodsInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GoodsInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "goodsType", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:goodsType: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.GoodsType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.goodsType (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:goodsType: ", p), err) }
  return err
}

func (p *GoodsInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "goodsId", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:goodsId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.GoodsId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.goodsId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:goodsId: ", p), err) }
  return err
}

func (p *GoodsInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "goodsNum", thrift.I32, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:goodsNum: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.GoodsNum)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.goodsNum (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:goodsNum: ", p), err) }
  return err
}

func (p *GoodsInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "goodsDesc", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:goodsDesc: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.GoodsDesc)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.goodsDesc (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:goodsDesc: ", p), err) }
  return err
}

func (p *GoodsInfo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsPic() {
    if err := oprot.WriteFieldBegin(ctx, "goodsPic", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:goodsPic: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsPic)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsPic (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:goodsPic: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsSort() {
    if err := oprot.WriteFieldBegin(ctx, "goodsSort", thrift.I32, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:goodsSort: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.GoodsSort)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsSort (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:goodsSort: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsExt() {
    if err := oprot.WriteFieldBegin(ctx, "goodsExt", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:goodsExt: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsExt)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsExt (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:goodsExt: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsOrderId() {
    if err := oprot.WriteFieldBegin(ctx, "goodsOrderId", thrift.I64, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:goodsOrderId: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.GoodsOrderId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsOrderId (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:goodsOrderId: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRate() {
    if err := oprot.WriteFieldBegin(ctx, "rate", thrift.DOUBLE, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:rate: ", p), err) }
    if err := oprot.WriteDouble(ctx, float64(*p.Rate)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.rate (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:rate: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsMagnification() {
    if err := oprot.WriteFieldBegin(ctx, "goodsMagnification", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:goodsMagnification: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsMagnification)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsMagnification (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:goodsMagnification: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSubGoodsList() {
    if err := oprot.WriteFieldBegin(ctx, "subGoodsList", thrift.LIST, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:subGoodsList: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.SubGoodsList)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.SubGoodsList {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:subGoodsList: ", p), err) }
  }
  return err
}

func (p *GoodsInfo) Equals(other *GoodsInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.GoodsType != other.GoodsType { return false }
  if p.GoodsId != other.GoodsId { return false }
  if p.GoodsNum != other.GoodsNum { return false }
  if p.GoodsDesc != other.GoodsDesc { return false }
  if p.GoodsPic != other.GoodsPic {
    if p.GoodsPic == nil || other.GoodsPic == nil {
      return false
    }
    if (*p.GoodsPic) != (*other.GoodsPic) { return false }
  }
  if p.GoodsSort != other.GoodsSort {
    if p.GoodsSort == nil || other.GoodsSort == nil {
      return false
    }
    if (*p.GoodsSort) != (*other.GoodsSort) { return false }
  }
  if p.GoodsExt != other.GoodsExt {
    if p.GoodsExt == nil || other.GoodsExt == nil {
      return false
    }
    if (*p.GoodsExt) != (*other.GoodsExt) { return false }
  }
  if p.GoodsOrderId != other.GoodsOrderId {
    if p.GoodsOrderId == nil || other.GoodsOrderId == nil {
      return false
    }
    if (*p.GoodsOrderId) != (*other.GoodsOrderId) { return false }
  }
  if p.Rate != other.Rate {
    if p.Rate == nil || other.Rate == nil {
      return false
    }
    if (*p.Rate) != (*other.Rate) { return false }
  }
  if p.GoodsMagnification != other.GoodsMagnification {
    if p.GoodsMagnification == nil || other.GoodsMagnification == nil {
      return false
    }
    if (*p.GoodsMagnification) != (*other.GoodsMagnification) { return false }
  }
  if len(p.SubGoodsList) != len(other.SubGoodsList) { return false }
  for i, _tgt := range p.SubGoodsList {
    _src1 := other.SubGoodsList[i]
    if !_tgt.Equals(_src1) { return false }
  }
  return true
}

func (p *GoodsInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GoodsInfo(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
//  - Num
type ResultInfo struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data []*GoodsInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
  Num *int64 `thrift:"num,4" db:"num" json:"num,omitempty"`
}

func NewResultInfo() *ResultInfo {
  return &ResultInfo{}
}


func (p *ResultInfo) GetRet() int32 {
  return p.Ret
}
var ResultInfo_Msg_DEFAULT string
func (p *ResultInfo) GetMsg() string {
  if !p.IsSetMsg() {
    return ResultInfo_Msg_DEFAULT
  }
return *p.Msg
}
var ResultInfo_Data_DEFAULT []*GoodsInfo

func (p *ResultInfo) GetData() []*GoodsInfo {
  return p.Data
}
var ResultInfo_Num_DEFAULT int64
func (p *ResultInfo) GetNum() int64 {
  if !p.IsSetNum() {
    return ResultInfo_Num_DEFAULT
  }
return *p.Num
}
func (p *ResultInfo) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *ResultInfo) IsSetData() bool {
  return p.Data != nil
}

func (p *ResultInfo) IsSetNum() bool {
  return p.Num != nil
}

func (p *ResultInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  return nil
}

func (p *ResultInfo)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *ResultInfo)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *ResultInfo)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*GoodsInfo, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem2 := &GoodsInfo{}
    if err := _elem2.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem2), err)
    }
    p.Data = append(p.Data, _elem2)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ResultInfo)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Num = &v
}
  return nil
}

func (p *ResultInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ResultInfo"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ResultInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *ResultInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *ResultInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *ResultInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNum() {
    if err := oprot.WriteFieldBegin(ctx, "num", thrift.I64, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:num: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.Num)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.num (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:num: ", p), err) }
  }
  return err
}

func (p *ResultInfo) Equals(other *ResultInfo) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src3 := other.Data[i]
    if !_tgt.Equals(_src3) { return false }
  }
  if p.Num != other.Num {
    if p.Num == nil || other.Num == nil {
      return false
    }
    if (*p.Num) != (*other.Num) { return false }
  }
  return true
}

func (p *ResultInfo) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ResultInfo(%+v)", *p)
}

// 通用参数
// 
// Attributes:
//  - Pid
//  - ChannelId
//  - Version
//  - ClientIp
//  - DeviceId
//  - Ext
type CommonParameterReq struct {
  Pid *int32 `thrift:"pid,1" db:"pid" json:"pid,omitempty"`
  ChannelId *int32 `thrift:"channelId,2" db:"channelId" json:"channelId,omitempty"`
  Version *string `thrift:"version,3" db:"version" json:"version,omitempty"`
  ClientIp *string `thrift:"clientIp,4" db:"clientIp" json:"clientIp,omitempty"`
  DeviceId *string `thrift:"deviceId,5" db:"deviceId" json:"deviceId,omitempty"`
  Ext *string `thrift:"ext,6" db:"ext" json:"ext,omitempty"`
}

func NewCommonParameterReq() *CommonParameterReq {
  return &CommonParameterReq{}
}

var CommonParameterReq_Pid_DEFAULT int32
func (p *CommonParameterReq) GetPid() int32 {
  if !p.IsSetPid() {
    return CommonParameterReq_Pid_DEFAULT
  }
return *p.Pid
}
var CommonParameterReq_ChannelId_DEFAULT int32
func (p *CommonParameterReq) GetChannelId() int32 {
  if !p.IsSetChannelId() {
    return CommonParameterReq_ChannelId_DEFAULT
  }
return *p.ChannelId
}
var CommonParameterReq_Version_DEFAULT string
func (p *CommonParameterReq) GetVersion() string {
  if !p.IsSetVersion() {
    return CommonParameterReq_Version_DEFAULT
  }
return *p.Version
}
var CommonParameterReq_ClientIp_DEFAULT string
func (p *CommonParameterReq) GetClientIp() string {
  if !p.IsSetClientIp() {
    return CommonParameterReq_ClientIp_DEFAULT
  }
return *p.ClientIp
}
var CommonParameterReq_DeviceId_DEFAULT string
func (p *CommonParameterReq) GetDeviceId() string {
  if !p.IsSetDeviceId() {
    return CommonParameterReq_DeviceId_DEFAULT
  }
return *p.DeviceId
}
var CommonParameterReq_Ext_DEFAULT string
func (p *CommonParameterReq) GetExt() string {
  if !p.IsSetExt() {
    return CommonParameterReq_Ext_DEFAULT
  }
return *p.Ext
}
func (p *CommonParameterReq) IsSetPid() bool {
  return p.Pid != nil
}

func (p *CommonParameterReq) IsSetChannelId() bool {
  return p.ChannelId != nil
}

func (p *CommonParameterReq) IsSetVersion() bool {
  return p.Version != nil
}

func (p *CommonParameterReq) IsSetClientIp() bool {
  return p.ClientIp != nil
}

func (p *CommonParameterReq) IsSetDeviceId() bool {
  return p.DeviceId != nil
}

func (p *CommonParameterReq) IsSetExt() bool {
  return p.Ext != nil
}

func (p *CommonParameterReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonParameterReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Pid = &v
}
  return nil
}

func (p *CommonParameterReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ChannelId = &v
}
  return nil
}

func (p *CommonParameterReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Version = &v
}
  return nil
}

func (p *CommonParameterReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.ClientIp = &v
}
  return nil
}

func (p *CommonParameterReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.DeviceId = &v
}
  return nil
}

func (p *CommonParameterReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Ext = &v
}
  return nil
}

func (p *CommonParameterReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CommonParameterReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonParameterReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPid() {
    if err := oprot.WriteFieldBegin(ctx, "pid", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:pid: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Pid)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.pid (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:pid: ", p), err) }
  }
  return err
}

func (p *CommonParameterReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetChannelId() {
    if err := oprot.WriteFieldBegin(ctx, "channelId", thrift.I32, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:channelId: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.ChannelId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.channelId (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:channelId: ", p), err) }
  }
  return err
}

func (p *CommonParameterReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetVersion() {
    if err := oprot.WriteFieldBegin(ctx, "version", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:version: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Version)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.version (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:version: ", p), err) }
  }
  return err
}

func (p *CommonParameterReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetClientIp() {
    if err := oprot.WriteFieldBegin(ctx, "clientIp", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:clientIp: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ClientIp)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.clientIp (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:clientIp: ", p), err) }
  }
  return err
}

func (p *CommonParameterReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDeviceId() {
    if err := oprot.WriteFieldBegin(ctx, "deviceId", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:deviceId: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DeviceId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.deviceId (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:deviceId: ", p), err) }
  }
  return err
}

func (p *CommonParameterReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExt() {
    if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:ext: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ext (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:ext: ", p), err) }
  }
  return err
}

func (p *CommonParameterReq) Equals(other *CommonParameterReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Pid != other.Pid {
    if p.Pid == nil || other.Pid == nil {
      return false
    }
    if (*p.Pid) != (*other.Pid) { return false }
  }
  if p.ChannelId != other.ChannelId {
    if p.ChannelId == nil || other.ChannelId == nil {
      return false
    }
    if (*p.ChannelId) != (*other.ChannelId) { return false }
  }
  if p.Version != other.Version {
    if p.Version == nil || other.Version == nil {
      return false
    }
    if (*p.Version) != (*other.Version) { return false }
  }
  if p.ClientIp != other.ClientIp {
    if p.ClientIp == nil || other.ClientIp == nil {
      return false
    }
    if (*p.ClientIp) != (*other.ClientIp) { return false }
  }
  if p.DeviceId != other.DeviceId {
    if p.DeviceId == nil || other.DeviceId == nil {
      return false
    }
    if (*p.DeviceId) != (*other.DeviceId) { return false }
  }
  if p.Ext != other.Ext {
    if p.Ext == nil || other.Ext == nil {
      return false
    }
    if (*p.Ext) != (*other.Ext) { return false }
  }
  return true
}

func (p *CommonParameterReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonParameterReq(%+v)", *p)
}

// 规则引擎发放
// 
// Attributes:
//  - OrderId
//  - AppId
//  - KugouId
//  - Num
//  - Time
//  - Fact
//  - SysVersion
//  - Ext
//  - Sign
type AwardRuleEngineReq struct {
  OrderId int64 `thrift:"orderId,1,required" db:"orderId" json:"orderId"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
  KugouId int64 `thrift:"kugouId,3,required" db:"kugouId" json:"kugouId"`
  Num int32 `thrift:"num,4,required" db:"num" json:"num"`
  Time int32 `thrift:"time,5,required" db:"time" json:"time"`
  Fact string `thrift:"fact,6,required" db:"fact" json:"fact"`
  SysVersion *int16 `thrift:"sysVersion,7" db:"sysVersion" json:"sysVersion,omitempty"`
  Ext *string `thrift:"ext,8" db:"ext" json:"ext,omitempty"`
  Sign string `thrift:"sign,9,required" db:"sign" json:"sign"`
}

func NewAwardRuleEngineReq() *AwardRuleEngineReq {
  return &AwardRuleEngineReq{}
}


func (p *AwardRuleEngineReq) GetOrderId() int64 {
  return p.OrderId
}

func (p *AwardRuleEngineReq) GetAppId() int32 {
  return p.AppId
}

func (p *AwardRuleEngineReq) GetKugouId() int64 {
  return p.KugouId
}

func (p *AwardRuleEngineReq) GetNum() int32 {
  return p.Num
}

func (p *AwardRuleEngineReq) GetTime() int32 {
  return p.Time
}

func (p *AwardRuleEngineReq) GetFact() string {
  return p.Fact
}
var AwardRuleEngineReq_SysVersion_DEFAULT int16
func (p *AwardRuleEngineReq) GetSysVersion() int16 {
  if !p.IsSetSysVersion() {
    return AwardRuleEngineReq_SysVersion_DEFAULT
  }
return *p.SysVersion
}
var AwardRuleEngineReq_Ext_DEFAULT string
func (p *AwardRuleEngineReq) GetExt() string {
  if !p.IsSetExt() {
    return AwardRuleEngineReq_Ext_DEFAULT
  }
return *p.Ext
}

func (p *AwardRuleEngineReq) GetSign() string {
  return p.Sign
}
func (p *AwardRuleEngineReq) IsSetSysVersion() bool {
  return p.SysVersion != nil
}

func (p *AwardRuleEngineReq) IsSetExt() bool {
  return p.Ext != nil
}

func (p *AwardRuleEngineReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetOrderId bool = false;
  var issetAppId bool = false;
  var issetKugouId bool = false;
  var issetNum bool = false;
  var issetTime bool = false;
  var issetFact bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetOrderId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetNum = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetTime = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetFact = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I16 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetOrderId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OrderId is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetNum{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Num is not set"));
  }
  if !issetTime{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Time is not set"));
  }
  if !issetFact{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Fact is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *AwardRuleEngineReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.OrderId = v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Num = v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Time = v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Fact = v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI16(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.SysVersion = &v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Ext = &v
}
  return nil
}

func (p *AwardRuleEngineReq)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *AwardRuleEngineReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRuleEngineReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRuleEngineReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "orderId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:orderId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.OrderId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.orderId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:orderId: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:kugouId: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "num", thrift.I32, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:num: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Num)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.num (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:num: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "time", thrift.I32, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:time: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Time)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.time (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:time: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "fact", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:fact: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Fact)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.fact (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:fact: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSysVersion() {
    if err := oprot.WriteFieldBegin(ctx, "sysVersion", thrift.I16, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:sysVersion: ", p), err) }
    if err := oprot.WriteI16(ctx, int16(*p.SysVersion)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sysVersion (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:sysVersion: ", p), err) }
  }
  return err
}

func (p *AwardRuleEngineReq) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExt() {
    if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:ext: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ext (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:ext: ", p), err) }
  }
  return err
}

func (p *AwardRuleEngineReq) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 9); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (9) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 9:sign: ", p), err) }
  return err
}

func (p *AwardRuleEngineReq) Equals(other *AwardRuleEngineReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.OrderId != other.OrderId { return false }
  if p.AppId != other.AppId { return false }
  if p.KugouId != other.KugouId { return false }
  if p.Num != other.Num { return false }
  if p.Time != other.Time { return false }
  if p.Fact != other.Fact { return false }
  if p.SysVersion != other.SysVersion {
    if p.SysVersion == nil || other.SysVersion == nil {
      return false
    }
    if (*p.SysVersion) != (*other.SysVersion) { return false }
  }
  if p.Ext != other.Ext {
    if p.Ext == nil || other.Ext == nil {
      return false
    }
    if (*p.Ext) != (*other.Ext) { return false }
  }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *AwardRuleEngineReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRuleEngineReq(%+v)", *p)
}

// 规则引擎抽奖请求
// 
// Attributes:
//  - OrderId
//  - AppId
//  - KugouId
//  - Num
//  - Time
//  - Fact
//  - SysVersion
//  - Ext
//  - Sign
type AwardRuleEngineV2Req struct {
  OrderId string `thrift:"orderId,1,required" db:"orderId" json:"orderId"`
  AppId int32 `thrift:"appId,2,required" db:"appId" json:"appId"`
  KugouId int64 `thrift:"kugouId,3,required" db:"kugouId" json:"kugouId"`
  Num int32 `thrift:"num,4,required" db:"num" json:"num"`
  Time int32 `thrift:"time,5,required" db:"time" json:"time"`
  Fact string `thrift:"fact,6,required" db:"fact" json:"fact"`
  SysVersion *int16 `thrift:"sysVersion,7" db:"sysVersion" json:"sysVersion,omitempty"`
  Ext *string `thrift:"ext,8" db:"ext" json:"ext,omitempty"`
  Sign string `thrift:"sign,9,required" db:"sign" json:"sign"`
}

func NewAwardRuleEngineV2Req() *AwardRuleEngineV2Req {
  return &AwardRuleEngineV2Req{}
}


func (p *AwardRuleEngineV2Req) GetOrderId() string {
  return p.OrderId
}

func (p *AwardRuleEngineV2Req) GetAppId() int32 {
  return p.AppId
}

func (p *AwardRuleEngineV2Req) GetKugouId() int64 {
  return p.KugouId
}

func (p *AwardRuleEngineV2Req) GetNum() int32 {
  return p.Num
}

func (p *AwardRuleEngineV2Req) GetTime() int32 {
  return p.Time
}

func (p *AwardRuleEngineV2Req) GetFact() string {
  return p.Fact
}
var AwardRuleEngineV2Req_SysVersion_DEFAULT int16
func (p *AwardRuleEngineV2Req) GetSysVersion() int16 {
  if !p.IsSetSysVersion() {
    return AwardRuleEngineV2Req_SysVersion_DEFAULT
  }
return *p.SysVersion
}
var AwardRuleEngineV2Req_Ext_DEFAULT string
func (p *AwardRuleEngineV2Req) GetExt() string {
  if !p.IsSetExt() {
    return AwardRuleEngineV2Req_Ext_DEFAULT
  }
return *p.Ext
}

func (p *AwardRuleEngineV2Req) GetSign() string {
  return p.Sign
}
func (p *AwardRuleEngineV2Req) IsSetSysVersion() bool {
  return p.SysVersion != nil
}

func (p *AwardRuleEngineV2Req) IsSetExt() bool {
  return p.Ext != nil
}

func (p *AwardRuleEngineV2Req) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetOrderId bool = false;
  var issetAppId bool = false;
  var issetKugouId bool = false;
  var issetNum bool = false;
  var issetTime bool = false;
  var issetFact bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetOrderId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetKugouId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetNum = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetTime = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
        issetFact = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I16 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetOrderId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field OrderId is not set"));
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetKugouId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field KugouId is not set"));
  }
  if !issetNum{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Num is not set"));
  }
  if !issetTime{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Time is not set"));
  }
  if !issetFact{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Fact is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.OrderId = v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.KugouId = v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Num = v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Time = v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Fact = v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI16(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.SysVersion = &v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Ext = &v
}
  return nil
}

func (p *AwardRuleEngineV2Req)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *AwardRuleEngineV2Req) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRuleEngineV2Req"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRuleEngineV2Req) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "orderId", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:orderId: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.OrderId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.orderId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:orderId: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:appId: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "kugouId", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:kugouId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.KugouId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.kugouId (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:kugouId: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "num", thrift.I32, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:num: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Num)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.num (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:num: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "time", thrift.I32, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:time: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Time)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.time (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:time: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "fact", thrift.STRING, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:fact: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Fact)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.fact (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:fact: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSysVersion() {
    if err := oprot.WriteFieldBegin(ctx, "sysVersion", thrift.I16, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:sysVersion: ", p), err) }
    if err := oprot.WriteI16(ctx, int16(*p.SysVersion)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.sysVersion (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:sysVersion: ", p), err) }
  }
  return err
}

func (p *AwardRuleEngineV2Req) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExt() {
    if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:ext: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ext (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:ext: ", p), err) }
  }
  return err
}

func (p *AwardRuleEngineV2Req) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 9); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (9) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 9:sign: ", p), err) }
  return err
}

func (p *AwardRuleEngineV2Req) Equals(other *AwardRuleEngineV2Req) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.OrderId != other.OrderId { return false }
  if p.AppId != other.AppId { return false }
  if p.KugouId != other.KugouId { return false }
  if p.Num != other.Num { return false }
  if p.Time != other.Time { return false }
  if p.Fact != other.Fact { return false }
  if p.SysVersion != other.SysVersion {
    if p.SysVersion == nil || other.SysVersion == nil {
      return false
    }
    if (*p.SysVersion) != (*other.SysVersion) { return false }
  }
  if p.Ext != other.Ext {
    if p.Ext == nil || other.Ext == nil {
      return false
    }
    if (*p.Ext) != (*other.Ext) { return false }
  }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *AwardRuleEngineV2Req) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRuleEngineV2Req(%+v)", *p)
}

// 拉取奖品规则列表
// 
// Attributes:
//  - AppId
//  - Time
//  - Fact
//  - Ext
//  - Sign
type AwardListReq struct {
  AppId int32 `thrift:"appId,1,required" db:"appId" json:"appId"`
  Time int32 `thrift:"time,2,required" db:"time" json:"time"`
  Fact string `thrift:"fact,3,required" db:"fact" json:"fact"`
  Ext *string `thrift:"ext,4" db:"ext" json:"ext,omitempty"`
  Sign string `thrift:"sign,5,required" db:"sign" json:"sign"`
}

func NewAwardListReq() *AwardListReq {
  return &AwardListReq{}
}


func (p *AwardListReq) GetAppId() int32 {
  return p.AppId
}

func (p *AwardListReq) GetTime() int32 {
  return p.Time
}

func (p *AwardListReq) GetFact() string {
  return p.Fact
}
var AwardListReq_Ext_DEFAULT string
func (p *AwardListReq) GetExt() string {
  if !p.IsSetExt() {
    return AwardListReq_Ext_DEFAULT
  }
return *p.Ext
}

func (p *AwardListReq) GetSign() string {
  return p.Sign
}
func (p *AwardListReq) IsSetExt() bool {
  return p.Ext != nil
}

func (p *AwardListReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetTime bool = false;
  var issetFact bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetTime = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
        issetFact = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetTime{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Time is not set"));
  }
  if !issetFact{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Fact is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *AwardListReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *AwardListReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Time = v
}
  return nil
}

func (p *AwardListReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Fact = v
}
  return nil
}

func (p *AwardListReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Ext = &v
}
  return nil
}

func (p *AwardListReq)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *AwardListReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardListReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardListReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *AwardListReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "time", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:time: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Time)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.time (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:time: ", p), err) }
  return err
}

func (p *AwardListReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "fact", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:fact: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Fact)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.fact (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:fact: ", p), err) }
  return err
}

func (p *AwardListReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExt() {
    if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:ext: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ext (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:ext: ", p), err) }
  }
  return err
}

func (p *AwardListReq) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:sign: ", p), err) }
  return err
}

func (p *AwardListReq) Equals(other *AwardListReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.Time != other.Time { return false }
  if p.Fact != other.Fact { return false }
  if p.Ext != other.Ext {
    if p.Ext == nil || other.Ext == nil {
      return false
    }
    if (*p.Ext) != (*other.Ext) { return false }
  }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *AwardListReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardListReq(%+v)", *p)
}

// 拉取奖品规则因子列表
// 
// Attributes:
//  - AppId
//  - Time
//  - Ext
//  - Sign
type AwardRuleListReq struct {
  AppId int32 `thrift:"appId,1,required" db:"appId" json:"appId"`
  Time int32 `thrift:"time,2,required" db:"time" json:"time"`
  Ext *string `thrift:"ext,3" db:"ext" json:"ext,omitempty"`
  Sign string `thrift:"sign,4,required" db:"sign" json:"sign"`
}

func NewAwardRuleListReq() *AwardRuleListReq {
  return &AwardRuleListReq{}
}


func (p *AwardRuleListReq) GetAppId() int32 {
  return p.AppId
}

func (p *AwardRuleListReq) GetTime() int32 {
  return p.Time
}
var AwardRuleListReq_Ext_DEFAULT string
func (p *AwardRuleListReq) GetExt() string {
  if !p.IsSetExt() {
    return AwardRuleListReq_Ext_DEFAULT
  }
return *p.Ext
}

func (p *AwardRuleListReq) GetSign() string {
  return p.Sign
}
func (p *AwardRuleListReq) IsSetExt() bool {
  return p.Ext != nil
}

func (p *AwardRuleListReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetTime bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetTime = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetTime{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Time is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *AwardRuleListReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *AwardRuleListReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Time = v
}
  return nil
}

func (p *AwardRuleListReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Ext = &v
}
  return nil
}

func (p *AwardRuleListReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *AwardRuleListReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRuleListReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRuleListReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *AwardRuleListReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "time", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:time: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Time)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.time (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:time: ", p), err) }
  return err
}

func (p *AwardRuleListReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExt() {
    if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ext: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ext (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ext: ", p), err) }
  }
  return err
}

func (p *AwardRuleListReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:sign: ", p), err) }
  return err
}

func (p *AwardRuleListReq) Equals(other *AwardRuleListReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.Time != other.Time { return false }
  if p.Ext != other.Ext {
    if p.Ext == nil || other.Ext == nil {
      return false
    }
    if (*p.Ext) != (*other.Ext) { return false }
  }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *AwardRuleListReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRuleListReq(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type RuleResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data []string `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewRuleResult_() *RuleResult_ {
  return &RuleResult_{}
}


func (p *RuleResult_) GetRet() int32 {
  return p.Ret
}
var RuleResult__Msg_DEFAULT string
func (p *RuleResult_) GetMsg() string {
  if !p.IsSetMsg() {
    return RuleResult__Msg_DEFAULT
  }
return *p.Msg
}
var RuleResult__Data_DEFAULT []string

func (p *RuleResult_) GetData() []string {
  return p.Data
}
func (p *RuleResult_) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *RuleResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *RuleResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  return nil
}

func (p *RuleResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *RuleResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *RuleResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]string, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
var _elem4 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem4 = v
}
    p.Data = append(p.Data, _elem4)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *RuleResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RuleResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *RuleResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *RuleResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *RuleResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *RuleResult_) Equals(other *RuleResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src5 := other.Data[i]
    if _tgt != _src5 { return false }
  }
  return true
}

func (p *RuleResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("RuleResult_(%+v)", *p)
}

// 拉取全量规则列表
// 
// Attributes:
//  - AppId
//  - Time
//  - Ext
//  - Sign
type FullAwardListReq struct {
  AppId int32 `thrift:"appId,1,required" db:"appId" json:"appId"`
  Time int32 `thrift:"time,2,required" db:"time" json:"time"`
  Ext *string `thrift:"ext,3" db:"ext" json:"ext,omitempty"`
  Sign string `thrift:"sign,4,required" db:"sign" json:"sign"`
}

func NewFullAwardListReq() *FullAwardListReq {
  return &FullAwardListReq{}
}


func (p *FullAwardListReq) GetAppId() int32 {
  return p.AppId
}

func (p *FullAwardListReq) GetTime() int32 {
  return p.Time
}
var FullAwardListReq_Ext_DEFAULT string
func (p *FullAwardListReq) GetExt() string {
  if !p.IsSetExt() {
    return FullAwardListReq_Ext_DEFAULT
  }
return *p.Ext
}

func (p *FullAwardListReq) GetSign() string {
  return p.Sign
}
func (p *FullAwardListReq) IsSetExt() bool {
  return p.Ext != nil
}

func (p *FullAwardListReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetAppId bool = false;
  var issetTime bool = false;
  var issetSign bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetAppId = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetTime = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
        issetSign = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetAppId{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AppId is not set"));
  }
  if !issetTime{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Time is not set"));
  }
  if !issetSign{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Sign is not set"));
  }
  return nil
}

func (p *FullAwardListReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.AppId = v
}
  return nil
}

func (p *FullAwardListReq)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Time = v
}
  return nil
}

func (p *FullAwardListReq)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Ext = &v
}
  return nil
}

func (p *FullAwardListReq)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Sign = v
}
  return nil
}

func (p *FullAwardListReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "FullAwardListReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FullAwardListReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "appId", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appId: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.AppId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.appId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appId: ", p), err) }
  return err
}

func (p *FullAwardListReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "time", thrift.I32, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:time: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Time)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.time (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:time: ", p), err) }
  return err
}

func (p *FullAwardListReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExt() {
    if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ext: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Ext)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ext (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ext: ", p), err) }
  }
  return err
}

func (p *FullAwardListReq) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "sign", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:sign: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Sign)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.sign (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:sign: ", p), err) }
  return err
}

func (p *FullAwardListReq) Equals(other *FullAwardListReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.AppId != other.AppId { return false }
  if p.Time != other.Time { return false }
  if p.Ext != other.Ext {
    if p.Ext == nil || other.Ext == nil {
      return false
    }
    if (*p.Ext) != (*other.Ext) { return false }
  }
  if p.Sign != other.Sign { return false }
  return true
}

func (p *FullAwardListReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FullAwardListReq(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type FullAwardListResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data *AwardRule `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewFullAwardListResult_() *FullAwardListResult_ {
  return &FullAwardListResult_{}
}


func (p *FullAwardListResult_) GetRet() int32 {
  return p.Ret
}
var FullAwardListResult__Msg_DEFAULT string
func (p *FullAwardListResult_) GetMsg() string {
  if !p.IsSetMsg() {
    return FullAwardListResult__Msg_DEFAULT
  }
return *p.Msg
}
var FullAwardListResult__Data_DEFAULT *AwardRule
func (p *FullAwardListResult_) GetData() *AwardRule {
  if !p.IsSetData() {
    return FullAwardListResult__Data_DEFAULT
  }
return p.Data
}
func (p *FullAwardListResult_) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *FullAwardListResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *FullAwardListResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  return nil
}

func (p *FullAwardListResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *FullAwardListResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *FullAwardListResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &AwardRule{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *FullAwardListResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "FullAwardListResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FullAwardListResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *FullAwardListResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *FullAwardListResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *FullAwardListResult_) Equals(other *FullAwardListResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *FullAwardListResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FullAwardListResult_(%+v)", *p)
}

// Attributes:
//  - Version
//  - RuleDetail
type AwardRule struct {
  Version int32 `thrift:"version,1,required" db:"version" json:"version"`
  RuleDetail []*AwardRuleDetail `thrift:"ruleDetail,2,required" db:"ruleDetail" json:"ruleDetail"`
}

func NewAwardRule() *AwardRule {
  return &AwardRule{}
}


func (p *AwardRule) GetVersion() int32 {
  return p.Version
}

func (p *AwardRule) GetRuleDetail() []*AwardRuleDetail {
  return p.RuleDetail
}
func (p *AwardRule) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetVersion bool = false;
  var issetRuleDetail bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetVersion = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetRuleDetail = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetVersion{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Version is not set"));
  }
  if !issetRuleDetail{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RuleDetail is not set"));
  }
  return nil
}

func (p *AwardRule)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Version = v
}
  return nil
}

func (p *AwardRule)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*AwardRuleDetail, 0, size)
  p.RuleDetail =  tSlice
  for i := 0; i < size; i ++ {
    _elem6 := &AwardRuleDetail{}
    if err := _elem6.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem6), err)
    }
    p.RuleDetail = append(p.RuleDetail, _elem6)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AwardRule) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRule"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRule) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "version", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:version: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Version)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.version (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:version: ", p), err) }
  return err
}

func (p *AwardRule) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ruleDetail", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:ruleDetail: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.RuleDetail)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.RuleDetail {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:ruleDetail: ", p), err) }
  return err
}

func (p *AwardRule) Equals(other *AwardRule) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Version != other.Version { return false }
  if len(p.RuleDetail) != len(other.RuleDetail) { return false }
  for i, _tgt := range p.RuleDetail {
    _src7 := other.RuleDetail[i]
    if !_tgt.Equals(_src7) { return false }
  }
  return true
}

func (p *AwardRule) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRule(%+v)", *p)
}

// Attributes:
//  - Rule
//  - GoodsInfoList
type AwardRuleDetail struct {
  Rule string `thrift:"rule,1,required" db:"rule" json:"rule"`
  GoodsInfoList []*GoodsInfo `thrift:"goodsInfoList,2,required" db:"goodsInfoList" json:"goodsInfoList"`
}

func NewAwardRuleDetail() *AwardRuleDetail {
  return &AwardRuleDetail{}
}


func (p *AwardRuleDetail) GetRule() string {
  return p.Rule
}

func (p *AwardRuleDetail) GetGoodsInfoList() []*GoodsInfo {
  return p.GoodsInfoList
}
func (p *AwardRuleDetail) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRule bool = false;
  var issetGoodsInfoList bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRule = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetGoodsInfoList = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRule{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Rule is not set"));
  }
  if !issetGoodsInfoList{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GoodsInfoList is not set"));
  }
  return nil
}

func (p *AwardRuleDetail)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Rule = v
}
  return nil
}

func (p *AwardRuleDetail)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*GoodsInfo, 0, size)
  p.GoodsInfoList =  tSlice
  for i := 0; i < size; i ++ {
    _elem8 := &GoodsInfo{}
    if err := _elem8.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem8), err)
    }
    p.GoodsInfoList = append(p.GoodsInfoList, _elem8)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AwardRuleDetail) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRuleDetail"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRuleDetail) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "rule", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:rule: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Rule)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.rule (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:rule: ", p), err) }
  return err
}

func (p *AwardRuleDetail) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "goodsInfoList", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:goodsInfoList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.GoodsInfoList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.GoodsInfoList {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:goodsInfoList: ", p), err) }
  return err
}

func (p *AwardRuleDetail) Equals(other *AwardRuleDetail) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Rule != other.Rule { return false }
  if len(p.GoodsInfoList) != len(other.GoodsInfoList) { return false }
  for i, _tgt := range p.GoodsInfoList {
    _src9 := other.GoodsInfoList[i]
    if !_tgt.Equals(_src9) { return false }
  }
  return true
}

func (p *AwardRuleDetail) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRuleDetail(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
//  - PubStatus
type AwardListResult_ struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data []*GoodsInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
  PubStatus *int32 `thrift:"pubStatus,4" db:"pubStatus" json:"pubStatus,omitempty"`
}

func NewAwardListResult_() *AwardListResult_ {
  return &AwardListResult_{}
}


func (p *AwardListResult_) GetRet() int32 {
  return p.Ret
}
var AwardListResult__Msg_DEFAULT string
func (p *AwardListResult_) GetMsg() string {
  if !p.IsSetMsg() {
    return AwardListResult__Msg_DEFAULT
  }
return *p.Msg
}
var AwardListResult__Data_DEFAULT []*GoodsInfo

func (p *AwardListResult_) GetData() []*GoodsInfo {
  return p.Data
}
var AwardListResult__PubStatus_DEFAULT int32
func (p *AwardListResult_) GetPubStatus() int32 {
  if !p.IsSetPubStatus() {
    return AwardListResult__PubStatus_DEFAULT
  }
return *p.PubStatus
}
func (p *AwardListResult_) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *AwardListResult_) IsSetData() bool {
  return p.Data != nil
}

func (p *AwardListResult_) IsSetPubStatus() bool {
  return p.PubStatus != nil
}

func (p *AwardListResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  return nil
}

func (p *AwardListResult_)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *AwardListResult_)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *AwardListResult_)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*GoodsInfo, 0, size)
  p.Data =  tSlice
  for i := 0; i < size; i ++ {
    _elem10 := &GoodsInfo{}
    if err := _elem10.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem10), err)
    }
    p.Data = append(p.Data, _elem10)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AwardListResult_)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.PubStatus = &v
}
  return nil
}

func (p *AwardListResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardListResult"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardListResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *AwardListResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *AwardListResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.LIST, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Data)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.Data {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *AwardListResult_) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPubStatus() {
    if err := oprot.WriteFieldBegin(ctx, "pubStatus", thrift.I32, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:pubStatus: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.PubStatus)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.pubStatus (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:pubStatus: ", p), err) }
  }
  return err
}

func (p *AwardListResult_) Equals(other *AwardListResult_) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if len(p.Data) != len(other.Data) { return false }
  for i, _tgt := range p.Data {
    _src11 := other.Data[i]
    if !_tgt.Equals(_src11) { return false }
  }
  if p.PubStatus != other.PubStatus {
    if p.PubStatus == nil || other.PubStatus == nil {
      return false
    }
    if (*p.PubStatus) != (*other.PubStatus) { return false }
  }
  return true
}

func (p *AwardListResult_) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardListResult_(%+v)", *p)
}

// Attributes:
//  - GoodsType
//  - GoodsId
//  - GoodsNum
//  - GoodsDesc
//  - GoodsPic
//  - GoodsSort
//  - GoodsExt
//  - GoodsOrderId
//  - Rate
//  - GoodsMagnification
//  - SubGoodsList
//  - StockLimit
//  - StockNum
//  - GoodsExpireDay
type GoodsInfoStr struct {
  GoodsType *string `thrift:"goodsType,1" db:"goodsType" json:"goodsType,omitempty"`
  GoodsId *string `thrift:"goodsId,2" db:"goodsId" json:"goodsId,omitempty"`
  GoodsNum *string `thrift:"goodsNum,3" db:"goodsNum" json:"goodsNum,omitempty"`
  GoodsDesc *string `thrift:"goodsDesc,4" db:"goodsDesc" json:"goodsDesc,omitempty"`
  GoodsPic *string `thrift:"goodsPic,5" db:"goodsPic" json:"goodsPic,omitempty"`
  GoodsSort *string `thrift:"goodsSort,6" db:"goodsSort" json:"goodsSort,omitempty"`
  GoodsExt *string `thrift:"goodsExt,7" db:"goodsExt" json:"goodsExt,omitempty"`
  GoodsOrderId *string `thrift:"goodsOrderId,8" db:"goodsOrderId" json:"goodsOrderId,omitempty"`
  Rate *string `thrift:"rate,9" db:"rate" json:"rate,omitempty"`
  GoodsMagnification *string `thrift:"goodsMagnification,10" db:"goodsMagnification" json:"goodsMagnification,omitempty"`
  SubGoodsList []*GoodsInfoStr `thrift:"subGoodsList,11" db:"subGoodsList" json:"subGoodsList,omitempty"`
  StockLimit *int32 `thrift:"stockLimit,12" db:"stockLimit" json:"stockLimit,omitempty"`
  StockNum *int32 `thrift:"stockNum,13" db:"stockNum" json:"stockNum,omitempty"`
  GoodsExpireDay *int32 `thrift:"goodsExpireDay,14" db:"goodsExpireDay" json:"goodsExpireDay,omitempty"`
}

func NewGoodsInfoStr() *GoodsInfoStr {
  return &GoodsInfoStr{}
}

var GoodsInfoStr_GoodsType_DEFAULT string
func (p *GoodsInfoStr) GetGoodsType() string {
  if !p.IsSetGoodsType() {
    return GoodsInfoStr_GoodsType_DEFAULT
  }
return *p.GoodsType
}
var GoodsInfoStr_GoodsId_DEFAULT string
func (p *GoodsInfoStr) GetGoodsId() string {
  if !p.IsSetGoodsId() {
    return GoodsInfoStr_GoodsId_DEFAULT
  }
return *p.GoodsId
}
var GoodsInfoStr_GoodsNum_DEFAULT string
func (p *GoodsInfoStr) GetGoodsNum() string {
  if !p.IsSetGoodsNum() {
    return GoodsInfoStr_GoodsNum_DEFAULT
  }
return *p.GoodsNum
}
var GoodsInfoStr_GoodsDesc_DEFAULT string
func (p *GoodsInfoStr) GetGoodsDesc() string {
  if !p.IsSetGoodsDesc() {
    return GoodsInfoStr_GoodsDesc_DEFAULT
  }
return *p.GoodsDesc
}
var GoodsInfoStr_GoodsPic_DEFAULT string
func (p *GoodsInfoStr) GetGoodsPic() string {
  if !p.IsSetGoodsPic() {
    return GoodsInfoStr_GoodsPic_DEFAULT
  }
return *p.GoodsPic
}
var GoodsInfoStr_GoodsSort_DEFAULT string
func (p *GoodsInfoStr) GetGoodsSort() string {
  if !p.IsSetGoodsSort() {
    return GoodsInfoStr_GoodsSort_DEFAULT
  }
return *p.GoodsSort
}
var GoodsInfoStr_GoodsExt_DEFAULT string
func (p *GoodsInfoStr) GetGoodsExt() string {
  if !p.IsSetGoodsExt() {
    return GoodsInfoStr_GoodsExt_DEFAULT
  }
return *p.GoodsExt
}
var GoodsInfoStr_GoodsOrderId_DEFAULT string
func (p *GoodsInfoStr) GetGoodsOrderId() string {
  if !p.IsSetGoodsOrderId() {
    return GoodsInfoStr_GoodsOrderId_DEFAULT
  }
return *p.GoodsOrderId
}
var GoodsInfoStr_Rate_DEFAULT string
func (p *GoodsInfoStr) GetRate() string {
  if !p.IsSetRate() {
    return GoodsInfoStr_Rate_DEFAULT
  }
return *p.Rate
}
var GoodsInfoStr_GoodsMagnification_DEFAULT string
func (p *GoodsInfoStr) GetGoodsMagnification() string {
  if !p.IsSetGoodsMagnification() {
    return GoodsInfoStr_GoodsMagnification_DEFAULT
  }
return *p.GoodsMagnification
}
var GoodsInfoStr_SubGoodsList_DEFAULT []*GoodsInfoStr

func (p *GoodsInfoStr) GetSubGoodsList() []*GoodsInfoStr {
  return p.SubGoodsList
}
var GoodsInfoStr_StockLimit_DEFAULT int32
func (p *GoodsInfoStr) GetStockLimit() int32 {
  if !p.IsSetStockLimit() {
    return GoodsInfoStr_StockLimit_DEFAULT
  }
return *p.StockLimit
}
var GoodsInfoStr_StockNum_DEFAULT int32
func (p *GoodsInfoStr) GetStockNum() int32 {
  if !p.IsSetStockNum() {
    return GoodsInfoStr_StockNum_DEFAULT
  }
return *p.StockNum
}
var GoodsInfoStr_GoodsExpireDay_DEFAULT int32
func (p *GoodsInfoStr) GetGoodsExpireDay() int32 {
  if !p.IsSetGoodsExpireDay() {
    return GoodsInfoStr_GoodsExpireDay_DEFAULT
  }
return *p.GoodsExpireDay
}
func (p *GoodsInfoStr) IsSetGoodsType() bool {
  return p.GoodsType != nil
}

func (p *GoodsInfoStr) IsSetGoodsId() bool {
  return p.GoodsId != nil
}

func (p *GoodsInfoStr) IsSetGoodsNum() bool {
  return p.GoodsNum != nil
}

func (p *GoodsInfoStr) IsSetGoodsDesc() bool {
  return p.GoodsDesc != nil
}

func (p *GoodsInfoStr) IsSetGoodsPic() bool {
  return p.GoodsPic != nil
}

func (p *GoodsInfoStr) IsSetGoodsSort() bool {
  return p.GoodsSort != nil
}

func (p *GoodsInfoStr) IsSetGoodsExt() bool {
  return p.GoodsExt != nil
}

func (p *GoodsInfoStr) IsSetGoodsOrderId() bool {
  return p.GoodsOrderId != nil
}

func (p *GoodsInfoStr) IsSetRate() bool {
  return p.Rate != nil
}

func (p *GoodsInfoStr) IsSetGoodsMagnification() bool {
  return p.GoodsMagnification != nil
}

func (p *GoodsInfoStr) IsSetSubGoodsList() bool {
  return p.SubGoodsList != nil
}

func (p *GoodsInfoStr) IsSetStockLimit() bool {
  return p.StockLimit != nil
}

func (p *GoodsInfoStr) IsSetStockNum() bool {
  return p.StockNum != nil
}

func (p *GoodsInfoStr) IsSetGoodsExpireDay() bool {
  return p.GoodsExpireDay != nil
}

func (p *GoodsInfoStr) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GoodsInfoStr)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.GoodsType = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.GoodsId = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.GoodsNum = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.GoodsDesc = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.GoodsPic = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.GoodsSort = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.GoodsExt = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.GoodsOrderId = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Rate = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.GoodsMagnification = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*GoodsInfoStr, 0, size)
  p.SubGoodsList =  tSlice
  for i := 0; i < size; i ++ {
    _elem12 := &GoodsInfoStr{}
    if err := _elem12.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem12), err)
    }
    p.SubGoodsList = append(p.SubGoodsList, _elem12)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GoodsInfoStr)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.StockLimit = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.StockNum = &v
}
  return nil
}

func (p *GoodsInfoStr)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.GoodsExpireDay = &v
}
  return nil
}

func (p *GoodsInfoStr) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GoodsInfoStr"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GoodsInfoStr) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsType() {
    if err := oprot.WriteFieldBegin(ctx, "goodsType", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:goodsType: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsType (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:goodsType: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsId() {
    if err := oprot.WriteFieldBegin(ctx, "goodsId", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:goodsId: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsId (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:goodsId: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsNum() {
    if err := oprot.WriteFieldBegin(ctx, "goodsNum", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:goodsNum: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsNum)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsNum (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:goodsNum: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsDesc() {
    if err := oprot.WriteFieldBegin(ctx, "goodsDesc", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:goodsDesc: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsDesc)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsDesc (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:goodsDesc: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsPic() {
    if err := oprot.WriteFieldBegin(ctx, "goodsPic", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:goodsPic: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsPic)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsPic (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:goodsPic: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsSort() {
    if err := oprot.WriteFieldBegin(ctx, "goodsSort", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:goodsSort: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsSort)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsSort (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:goodsSort: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsExt() {
    if err := oprot.WriteFieldBegin(ctx, "goodsExt", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:goodsExt: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsExt)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsExt (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:goodsExt: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsOrderId() {
    if err := oprot.WriteFieldBegin(ctx, "goodsOrderId", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:goodsOrderId: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsOrderId)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsOrderId (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:goodsOrderId: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRate() {
    if err := oprot.WriteFieldBegin(ctx, "rate", thrift.STRING, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:rate: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Rate)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.rate (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:rate: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsMagnification() {
    if err := oprot.WriteFieldBegin(ctx, "goodsMagnification", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:goodsMagnification: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.GoodsMagnification)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsMagnification (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:goodsMagnification: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSubGoodsList() {
    if err := oprot.WriteFieldBegin(ctx, "subGoodsList", thrift.LIST, 11); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:subGoodsList: ", p), err) }
    if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.SubGoodsList)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range p.SubGoodsList {
      if err := v.Write(ctx, oprot); err != nil {
        return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
      }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 11:subGoodsList: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStockLimit() {
    if err := oprot.WriteFieldBegin(ctx, "stockLimit", thrift.I32, 12); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:stockLimit: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StockLimit)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.stockLimit (12) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 12:stockLimit: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStockNum() {
    if err := oprot.WriteFieldBegin(ctx, "stockNum", thrift.I32, 13); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:stockNum: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.StockNum)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.stockNum (13) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 13:stockNum: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGoodsExpireDay() {
    if err := oprot.WriteFieldBegin(ctx, "goodsExpireDay", thrift.I32, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:goodsExpireDay: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.GoodsExpireDay)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.goodsExpireDay (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:goodsExpireDay: ", p), err) }
  }
  return err
}

func (p *GoodsInfoStr) Equals(other *GoodsInfoStr) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.GoodsType != other.GoodsType {
    if p.GoodsType == nil || other.GoodsType == nil {
      return false
    }
    if (*p.GoodsType) != (*other.GoodsType) { return false }
  }
  if p.GoodsId != other.GoodsId {
    if p.GoodsId == nil || other.GoodsId == nil {
      return false
    }
    if (*p.GoodsId) != (*other.GoodsId) { return false }
  }
  if p.GoodsNum != other.GoodsNum {
    if p.GoodsNum == nil || other.GoodsNum == nil {
      return false
    }
    if (*p.GoodsNum) != (*other.GoodsNum) { return false }
  }
  if p.GoodsDesc != other.GoodsDesc {
    if p.GoodsDesc == nil || other.GoodsDesc == nil {
      return false
    }
    if (*p.GoodsDesc) != (*other.GoodsDesc) { return false }
  }
  if p.GoodsPic != other.GoodsPic {
    if p.GoodsPic == nil || other.GoodsPic == nil {
      return false
    }
    if (*p.GoodsPic) != (*other.GoodsPic) { return false }
  }
  if p.GoodsSort != other.GoodsSort {
    if p.GoodsSort == nil || other.GoodsSort == nil {
      return false
    }
    if (*p.GoodsSort) != (*other.GoodsSort) { return false }
  }
  if p.GoodsExt != other.GoodsExt {
    if p.GoodsExt == nil || other.GoodsExt == nil {
      return false
    }
    if (*p.GoodsExt) != (*other.GoodsExt) { return false }
  }
  if p.GoodsOrderId != other.GoodsOrderId {
    if p.GoodsOrderId == nil || other.GoodsOrderId == nil {
      return false
    }
    if (*p.GoodsOrderId) != (*other.GoodsOrderId) { return false }
  }
  if p.Rate != other.Rate {
    if p.Rate == nil || other.Rate == nil {
      return false
    }
    if (*p.Rate) != (*other.Rate) { return false }
  }
  if p.GoodsMagnification != other.GoodsMagnification {
    if p.GoodsMagnification == nil || other.GoodsMagnification == nil {
      return false
    }
    if (*p.GoodsMagnification) != (*other.GoodsMagnification) { return false }
  }
  if len(p.SubGoodsList) != len(other.SubGoodsList) { return false }
  for i, _tgt := range p.SubGoodsList {
    _src13 := other.SubGoodsList[i]
    if !_tgt.Equals(_src13) { return false }
  }
  if p.StockLimit != other.StockLimit {
    if p.StockLimit == nil || other.StockLimit == nil {
      return false
    }
    if (*p.StockLimit) != (*other.StockLimit) { return false }
  }
  if p.StockNum != other.StockNum {
    if p.StockNum == nil || other.StockNum == nil {
      return false
    }
    if (*p.StockNum) != (*other.StockNum) { return false }
  }
  if p.GoodsExpireDay != other.GoodsExpireDay {
    if p.GoodsExpireDay == nil || other.GoodsExpireDay == nil {
      return false
    }
    if (*p.GoodsExpireDay) != (*other.GoodsExpireDay) { return false }
  }
  return true
}

func (p *GoodsInfoStr) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GoodsInfoStr(%+v)", *p)
}

// Attributes:
//  - Ret
//  - Msg
//  - Data
type FullAwardListResultV2 struct {
  Ret int32 `thrift:"ret,1,required" db:"ret" json:"ret"`
  Msg *string `thrift:"msg,2" db:"msg" json:"msg,omitempty"`
  Data *AwardRuleV2 `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewFullAwardListResultV2() *FullAwardListResultV2 {
  return &FullAwardListResultV2{}
}


func (p *FullAwardListResultV2) GetRet() int32 {
  return p.Ret
}
var FullAwardListResultV2_Msg_DEFAULT string
func (p *FullAwardListResultV2) GetMsg() string {
  if !p.IsSetMsg() {
    return FullAwardListResultV2_Msg_DEFAULT
  }
return *p.Msg
}
var FullAwardListResultV2_Data_DEFAULT *AwardRuleV2
func (p *FullAwardListResultV2) GetData() *AwardRuleV2 {
  if !p.IsSetData() {
    return FullAwardListResultV2_Data_DEFAULT
  }
return p.Data
}
func (p *FullAwardListResultV2) IsSetMsg() bool {
  return p.Msg != nil
}

func (p *FullAwardListResultV2) IsSetData() bool {
  return p.Data != nil
}

func (p *FullAwardListResultV2) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRet bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRet = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRet{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ret is not set"));
  }
  return nil
}

func (p *FullAwardListResultV2)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ret = v
}
  return nil
}

func (p *FullAwardListResultV2)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Msg = &v
}
  return nil
}

func (p *FullAwardListResultV2)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  p.Data = &AwardRuleV2{}
  if err := p.Data.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
  }
  return nil
}

func (p *FullAwardListResultV2) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "FullAwardListResultV2"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FullAwardListResultV2) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ret", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ret: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Ret)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ret (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ret: ", p), err) }
  return err
}

func (p *FullAwardListResultV2) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMsg() {
    if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Msg)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err) }
  }
  return err
}

func (p *FullAwardListResultV2) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err) }
    if err := p.Data.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err) }
  }
  return err
}

func (p *FullAwardListResultV2) Equals(other *FullAwardListResultV2) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ret != other.Ret { return false }
  if p.Msg != other.Msg {
    if p.Msg == nil || other.Msg == nil {
      return false
    }
    if (*p.Msg) != (*other.Msg) { return false }
  }
  if !p.Data.Equals(other.Data) { return false }
  return true
}

func (p *FullAwardListResultV2) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FullAwardListResultV2(%+v)", *p)
}

// Attributes:
//  - Version
//  - RuleDetail
type AwardRuleV2 struct {
  Version int32 `thrift:"version,1,required" db:"version" json:"version"`
  RuleDetail []*AwardRuleDetailV2 `thrift:"ruleDetail,2,required" db:"ruleDetail" json:"ruleDetail"`
}

func NewAwardRuleV2() *AwardRuleV2 {
  return &AwardRuleV2{}
}


func (p *AwardRuleV2) GetVersion() int32 {
  return p.Version
}

func (p *AwardRuleV2) GetRuleDetail() []*AwardRuleDetailV2 {
  return p.RuleDetail
}
func (p *AwardRuleV2) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetVersion bool = false;
  var issetRuleDetail bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetVersion = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetRuleDetail = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetVersion{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Version is not set"));
  }
  if !issetRuleDetail{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RuleDetail is not set"));
  }
  return nil
}

func (p *AwardRuleV2)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Version = v
}
  return nil
}

func (p *AwardRuleV2)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*AwardRuleDetailV2, 0, size)
  p.RuleDetail =  tSlice
  for i := 0; i < size; i ++ {
    _elem14 := &AwardRuleDetailV2{}
    if err := _elem14.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem14), err)
    }
    p.RuleDetail = append(p.RuleDetail, _elem14)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AwardRuleV2) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRuleV2"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRuleV2) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "version", thrift.I32, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:version: ", p), err) }
  if err := oprot.WriteI32(ctx, int32(p.Version)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.version (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:version: ", p), err) }
  return err
}

func (p *AwardRuleV2) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ruleDetail", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:ruleDetail: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.RuleDetail)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.RuleDetail {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:ruleDetail: ", p), err) }
  return err
}

func (p *AwardRuleV2) Equals(other *AwardRuleV2) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Version != other.Version { return false }
  if len(p.RuleDetail) != len(other.RuleDetail) { return false }
  for i, _tgt := range p.RuleDetail {
    _src15 := other.RuleDetail[i]
    if !_tgt.Equals(_src15) { return false }
  }
  return true
}

func (p *AwardRuleV2) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRuleV2(%+v)", *p)
}

// Attributes:
//  - Rule
//  - GoodsInfoStrList
type AwardRuleDetailV2 struct {
  Rule string `thrift:"rule,1,required" db:"rule" json:"rule"`
  GoodsInfoStrList []*GoodsInfoStr `thrift:"goodsInfoStrList,2,required" db:"goodsInfoStrList" json:"goodsInfoStrList"`
}

func NewAwardRuleDetailV2() *AwardRuleDetailV2 {
  return &AwardRuleDetailV2{}
}


func (p *AwardRuleDetailV2) GetRule() string {
  return p.Rule
}

func (p *AwardRuleDetailV2) GetGoodsInfoStrList() []*GoodsInfoStr {
  return p.GoodsInfoStrList
}
func (p *AwardRuleDetailV2) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetRule bool = false;
  var issetGoodsInfoStrList bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetRule = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetGoodsInfoStrList = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetRule{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Rule is not set"));
  }
  if !issetGoodsInfoStrList{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field GoodsInfoStrList is not set"));
  }
  return nil
}

func (p *AwardRuleDetailV2)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Rule = v
}
  return nil
}

func (p *AwardRuleDetailV2)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*GoodsInfoStr, 0, size)
  p.GoodsInfoStrList =  tSlice
  for i := 0; i < size; i ++ {
    _elem16 := &GoodsInfoStr{}
    if err := _elem16.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem16), err)
    }
    p.GoodsInfoStrList = append(p.GoodsInfoStrList, _elem16)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AwardRuleDetailV2) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AwardRuleDetailV2"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AwardRuleDetailV2) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "rule", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:rule: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Rule)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.rule (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:rule: ", p), err) }
  return err
}

func (p *AwardRuleDetailV2) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "goodsInfoStrList", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:goodsInfoStrList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.GoodsInfoStrList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.GoodsInfoStrList {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:goodsInfoStrList: ", p), err) }
  return err
}

func (p *AwardRuleDetailV2) Equals(other *AwardRuleDetailV2) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Rule != other.Rule { return false }
  if len(p.GoodsInfoStrList) != len(other.GoodsInfoStrList) { return false }
  for i, _tgt := range p.GoodsInfoStrList {
    _src17 := other.GoodsInfoStrList[i]
    if !_tgt.Equals(_src17) { return false }
  }
  return true
}

func (p *AwardRuleDetailV2) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AwardRuleDetailV2(%+v)", *p)
}

type CommonAwardService interface {
  // 规则引擎发放
  // 
  // Parameters:
  //  - Params
  //  - CommonParams
  SendAwardRuleEngine(ctx context.Context, params *AwardRuleEngineReq, commonParams *CommonParameterReq) (_r *ResultInfo, _err error)
  // 拉取后台配置的物品列表
  // 
  // Parameters:
  //  - Params
  GetAwardList(ctx context.Context, params *AwardListReq) (_r *ResultInfo, _err error)
  // 拉取后台配置的奖品因子列表
  // 
  // Parameters:
  //  - Params
  GetAwardRuleList(ctx context.Context, params *AwardRuleListReq) (_r *RuleResult_, _err error)
  // 拉取后台配置的全量物品列表,该接口不支持多概率区间模型
  // 
  // 
  // Parameters:
  //  - Params
  GetFullAwardList(ctx context.Context, params *FullAwardListReq) (_r *FullAwardListResult_, _err error)
  // 拉取后台配置的全量物品列表
  // 
  // Parameters:
  //  - Params
  GetFullAwardListV2(ctx context.Context, params *FullAwardListReq) (_r *FullAwardListResultV2, _err error)
  // 规则引擎抽奖
  // 
  // Parameters:
  //  - Params
  //  - CommonParams
  AwardByRuleEngine(ctx context.Context, params *AwardRuleEngineV2Req, commonParams *CommonParameterReq) (_r *ResultInfo, _err error)
  // 拉取后台配置的最新草稿态的全量物品列表--仅限上线前roi测算使用，不能直接用于线上业务
  // 
  // Parameters:
  //  - Params
  GetNoPubFullAwardList(ctx context.Context, params *FullAwardListReq) (_r *FullAwardListResult_, _err error)
  // 拉取后台配置的最新草稿态的物品列表--仅限上线前roi测算使用，不能直接用于线上业务
  // 
  // Parameters:
  //  - Params
  GetNoPubAwardList(ctx context.Context, params *AwardListReq) (_r *AwardListResult_, _err error)
}

type CommonAwardServiceClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewCommonAwardServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *CommonAwardServiceClient {
  return &CommonAwardServiceClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewCommonAwardServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *CommonAwardServiceClient {
  return &CommonAwardServiceClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewCommonAwardServiceClient(c thrift.TClient) *CommonAwardServiceClient {
  return &CommonAwardServiceClient{
    c: c,
  }
}

func (p *CommonAwardServiceClient) Client_() thrift.TClient {
  return p.c
}

func (p *CommonAwardServiceClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *CommonAwardServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// 规则引擎发放
// 
// Parameters:
//  - Params
//  - CommonParams
func (p *CommonAwardServiceClient) SendAwardRuleEngine(ctx context.Context, params *AwardRuleEngineReq, commonParams *CommonParameterReq) (_r *ResultInfo, _err error) {
  var _args18 CommonAwardServiceSendAwardRuleEngineArgs
  _args18.Params = params
  _args18.CommonParams = commonParams
  var _result20 CommonAwardServiceSendAwardRuleEngineResult
  var _meta19 thrift.ResponseMeta
  _meta19, _err = p.Client_().Call(ctx, "sendAwardRuleEngine", &_args18, &_result20)
  p.SetLastResponseMeta_(_meta19)
  if _err != nil {
    return
  }
  if _ret21 := _result20.GetSuccess(); _ret21 != nil {
    return _ret21, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "sendAwardRuleEngine failed: unknown result")
}

// 拉取后台配置的物品列表
// 
// Parameters:
//  - Params
func (p *CommonAwardServiceClient) GetAwardList(ctx context.Context, params *AwardListReq) (_r *ResultInfo, _err error) {
  var _args22 CommonAwardServiceGetAwardListArgs
  _args22.Params = params
  var _result24 CommonAwardServiceGetAwardListResult
  var _meta23 thrift.ResponseMeta
  _meta23, _err = p.Client_().Call(ctx, "getAwardList", &_args22, &_result24)
  p.SetLastResponseMeta_(_meta23)
  if _err != nil {
    return
  }
  if _ret25 := _result24.GetSuccess(); _ret25 != nil {
    return _ret25, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getAwardList failed: unknown result")
}

// 拉取后台配置的奖品因子列表
// 
// Parameters:
//  - Params
func (p *CommonAwardServiceClient) GetAwardRuleList(ctx context.Context, params *AwardRuleListReq) (_r *RuleResult_, _err error) {
  var _args26 CommonAwardServiceGetAwardRuleListArgs
  _args26.Params = params
  var _result28 CommonAwardServiceGetAwardRuleListResult
  var _meta27 thrift.ResponseMeta
  _meta27, _err = p.Client_().Call(ctx, "getAwardRuleList", &_args26, &_result28)
  p.SetLastResponseMeta_(_meta27)
  if _err != nil {
    return
  }
  if _ret29 := _result28.GetSuccess(); _ret29 != nil {
    return _ret29, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getAwardRuleList failed: unknown result")
}

// 拉取后台配置的全量物品列表,该接口不支持多概率区间模型
// 
// 
// Parameters:
//  - Params
func (p *CommonAwardServiceClient) GetFullAwardList(ctx context.Context, params *FullAwardListReq) (_r *FullAwardListResult_, _err error) {
  var _args30 CommonAwardServiceGetFullAwardListArgs
  _args30.Params = params
  var _result32 CommonAwardServiceGetFullAwardListResult
  var _meta31 thrift.ResponseMeta
  _meta31, _err = p.Client_().Call(ctx, "getFullAwardList", &_args30, &_result32)
  p.SetLastResponseMeta_(_meta31)
  if _err != nil {
    return
  }
  if _ret33 := _result32.GetSuccess(); _ret33 != nil {
    return _ret33, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getFullAwardList failed: unknown result")
}

// 拉取后台配置的全量物品列表
// 
// Parameters:
//  - Params
func (p *CommonAwardServiceClient) GetFullAwardListV2(ctx context.Context, params *FullAwardListReq) (_r *FullAwardListResultV2, _err error) {
  var _args34 CommonAwardServiceGetFullAwardListV2Args
  _args34.Params = params
  var _result36 CommonAwardServiceGetFullAwardListV2Result
  var _meta35 thrift.ResponseMeta
  _meta35, _err = p.Client_().Call(ctx, "getFullAwardListV2", &_args34, &_result36)
  p.SetLastResponseMeta_(_meta35)
  if _err != nil {
    return
  }
  if _ret37 := _result36.GetSuccess(); _ret37 != nil {
    return _ret37, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getFullAwardListV2 failed: unknown result")
}

// 规则引擎抽奖
// 
// Parameters:
//  - Params
//  - CommonParams
func (p *CommonAwardServiceClient) AwardByRuleEngine(ctx context.Context, params *AwardRuleEngineV2Req, commonParams *CommonParameterReq) (_r *ResultInfo, _err error) {
  var _args38 CommonAwardServiceAwardByRuleEngineArgs
  _args38.Params = params
  _args38.CommonParams = commonParams
  var _result40 CommonAwardServiceAwardByRuleEngineResult
  var _meta39 thrift.ResponseMeta
  _meta39, _err = p.Client_().Call(ctx, "awardByRuleEngine", &_args38, &_result40)
  p.SetLastResponseMeta_(_meta39)
  if _err != nil {
    return
  }
  if _ret41 := _result40.GetSuccess(); _ret41 != nil {
    return _ret41, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "awardByRuleEngine failed: unknown result")
}

// 拉取后台配置的最新草稿态的全量物品列表--仅限上线前roi测算使用，不能直接用于线上业务
// 
// Parameters:
//  - Params
func (p *CommonAwardServiceClient) GetNoPubFullAwardList(ctx context.Context, params *FullAwardListReq) (_r *FullAwardListResult_, _err error) {
  var _args42 CommonAwardServiceGetNoPubFullAwardListArgs
  _args42.Params = params
  var _result44 CommonAwardServiceGetNoPubFullAwardListResult
  var _meta43 thrift.ResponseMeta
  _meta43, _err = p.Client_().Call(ctx, "getNoPubFullAwardList", &_args42, &_result44)
  p.SetLastResponseMeta_(_meta43)
  if _err != nil {
    return
  }
  if _ret45 := _result44.GetSuccess(); _ret45 != nil {
    return _ret45, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getNoPubFullAwardList failed: unknown result")
}

// 拉取后台配置的最新草稿态的物品列表--仅限上线前roi测算使用，不能直接用于线上业务
// 
// Parameters:
//  - Params
func (p *CommonAwardServiceClient) GetNoPubAwardList(ctx context.Context, params *AwardListReq) (_r *AwardListResult_, _err error) {
  var _args46 CommonAwardServiceGetNoPubAwardListArgs
  _args46.Params = params
  var _result48 CommonAwardServiceGetNoPubAwardListResult
  var _meta47 thrift.ResponseMeta
  _meta47, _err = p.Client_().Call(ctx, "getNoPubAwardList", &_args46, &_result48)
  p.SetLastResponseMeta_(_meta47)
  if _err != nil {
    return
  }
  if _ret49 := _result48.GetSuccess(); _ret49 != nil {
    return _ret49, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getNoPubAwardList failed: unknown result")
}

type CommonAwardServiceProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler CommonAwardService
}

func (p *CommonAwardServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *CommonAwardServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *CommonAwardServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewCommonAwardServiceProcessor(handler CommonAwardService) *CommonAwardServiceProcessor {

  self50 := &CommonAwardServiceProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self50.processorMap["sendAwardRuleEngine"] = &commonAwardServiceProcessorSendAwardRuleEngine{handler:handler}
  self50.processorMap["getAwardList"] = &commonAwardServiceProcessorGetAwardList{handler:handler}
  self50.processorMap["getAwardRuleList"] = &commonAwardServiceProcessorGetAwardRuleList{handler:handler}
  self50.processorMap["getFullAwardList"] = &commonAwardServiceProcessorGetFullAwardList{handler:handler}
  self50.processorMap["getFullAwardListV2"] = &commonAwardServiceProcessorGetFullAwardListV2{handler:handler}
  self50.processorMap["awardByRuleEngine"] = &commonAwardServiceProcessorAwardByRuleEngine{handler:handler}
  self50.processorMap["getNoPubFullAwardList"] = &commonAwardServiceProcessorGetNoPubFullAwardList{handler:handler}
  self50.processorMap["getNoPubAwardList"] = &commonAwardServiceProcessorGetNoPubAwardList{handler:handler}
return self50
}

func (p *CommonAwardServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x51 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x51.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x51

}

type commonAwardServiceProcessorSendAwardRuleEngine struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorSendAwardRuleEngine) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err52 error
  args := CommonAwardServiceSendAwardRuleEngineArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "sendAwardRuleEngine", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceSendAwardRuleEngineResult{}
  if retval, err2 := p.handler.SendAwardRuleEngine(ctx, args.Params, args.CommonParams); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc53 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendAwardRuleEngine: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "sendAwardRuleEngine", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err52 = thrift.WrapTException(err2)
    }
    if err2 := _exc53.Write(ctx, oprot); _write_err52 == nil && err2 != nil {
      _write_err52 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err52 == nil && err2 != nil {
      _write_err52 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err52 == nil && err2 != nil {
      _write_err52 = thrift.WrapTException(err2)
    }
    if _write_err52 != nil {
      return false, thrift.WrapTException(_write_err52)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "sendAwardRuleEngine", thrift.REPLY, seqId); err2 != nil {
    _write_err52 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err52 == nil && err2 != nil {
    _write_err52 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err52 == nil && err2 != nil {
    _write_err52 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err52 == nil && err2 != nil {
    _write_err52 = thrift.WrapTException(err2)
  }
  if _write_err52 != nil {
    return false, thrift.WrapTException(_write_err52)
  }
  return true, err
}

type commonAwardServiceProcessorGetAwardList struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorGetAwardList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err54 error
  args := CommonAwardServiceGetAwardListArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getAwardList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceGetAwardListResult{}
  if retval, err2 := p.handler.GetAwardList(ctx, args.Params); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc55 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAwardList: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getAwardList", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err54 = thrift.WrapTException(err2)
    }
    if err2 := _exc55.Write(ctx, oprot); _write_err54 == nil && err2 != nil {
      _write_err54 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err54 == nil && err2 != nil {
      _write_err54 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err54 == nil && err2 != nil {
      _write_err54 = thrift.WrapTException(err2)
    }
    if _write_err54 != nil {
      return false, thrift.WrapTException(_write_err54)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getAwardList", thrift.REPLY, seqId); err2 != nil {
    _write_err54 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err54 == nil && err2 != nil {
    _write_err54 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err54 == nil && err2 != nil {
    _write_err54 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err54 == nil && err2 != nil {
    _write_err54 = thrift.WrapTException(err2)
  }
  if _write_err54 != nil {
    return false, thrift.WrapTException(_write_err54)
  }
  return true, err
}

type commonAwardServiceProcessorGetAwardRuleList struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorGetAwardRuleList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err56 error
  args := CommonAwardServiceGetAwardRuleListArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getAwardRuleList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceGetAwardRuleListResult{}
  if retval, err2 := p.handler.GetAwardRuleList(ctx, args.Params); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc57 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAwardRuleList: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getAwardRuleList", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err56 = thrift.WrapTException(err2)
    }
    if err2 := _exc57.Write(ctx, oprot); _write_err56 == nil && err2 != nil {
      _write_err56 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err56 == nil && err2 != nil {
      _write_err56 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err56 == nil && err2 != nil {
      _write_err56 = thrift.WrapTException(err2)
    }
    if _write_err56 != nil {
      return false, thrift.WrapTException(_write_err56)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getAwardRuleList", thrift.REPLY, seqId); err2 != nil {
    _write_err56 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err56 == nil && err2 != nil {
    _write_err56 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err56 == nil && err2 != nil {
    _write_err56 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err56 == nil && err2 != nil {
    _write_err56 = thrift.WrapTException(err2)
  }
  if _write_err56 != nil {
    return false, thrift.WrapTException(_write_err56)
  }
  return true, err
}

type commonAwardServiceProcessorGetFullAwardList struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorGetFullAwardList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err58 error
  args := CommonAwardServiceGetFullAwardListArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getFullAwardList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceGetFullAwardListResult{}
  if retval, err2 := p.handler.GetFullAwardList(ctx, args.Params); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc59 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFullAwardList: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getFullAwardList", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err58 = thrift.WrapTException(err2)
    }
    if err2 := _exc59.Write(ctx, oprot); _write_err58 == nil && err2 != nil {
      _write_err58 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err58 == nil && err2 != nil {
      _write_err58 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err58 == nil && err2 != nil {
      _write_err58 = thrift.WrapTException(err2)
    }
    if _write_err58 != nil {
      return false, thrift.WrapTException(_write_err58)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getFullAwardList", thrift.REPLY, seqId); err2 != nil {
    _write_err58 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err58 == nil && err2 != nil {
    _write_err58 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err58 == nil && err2 != nil {
    _write_err58 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err58 == nil && err2 != nil {
    _write_err58 = thrift.WrapTException(err2)
  }
  if _write_err58 != nil {
    return false, thrift.WrapTException(_write_err58)
  }
  return true, err
}

type commonAwardServiceProcessorGetFullAwardListV2 struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorGetFullAwardListV2) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err60 error
  args := CommonAwardServiceGetFullAwardListV2Args{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getFullAwardListV2", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceGetFullAwardListV2Result{}
  if retval, err2 := p.handler.GetFullAwardListV2(ctx, args.Params); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc61 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFullAwardListV2: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getFullAwardListV2", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err60 = thrift.WrapTException(err2)
    }
    if err2 := _exc61.Write(ctx, oprot); _write_err60 == nil && err2 != nil {
      _write_err60 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err60 == nil && err2 != nil {
      _write_err60 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err60 == nil && err2 != nil {
      _write_err60 = thrift.WrapTException(err2)
    }
    if _write_err60 != nil {
      return false, thrift.WrapTException(_write_err60)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getFullAwardListV2", thrift.REPLY, seqId); err2 != nil {
    _write_err60 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err60 == nil && err2 != nil {
    _write_err60 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err60 == nil && err2 != nil {
    _write_err60 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err60 == nil && err2 != nil {
    _write_err60 = thrift.WrapTException(err2)
  }
  if _write_err60 != nil {
    return false, thrift.WrapTException(_write_err60)
  }
  return true, err
}

type commonAwardServiceProcessorAwardByRuleEngine struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorAwardByRuleEngine) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err62 error
  args := CommonAwardServiceAwardByRuleEngineArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "awardByRuleEngine", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceAwardByRuleEngineResult{}
  if retval, err2 := p.handler.AwardByRuleEngine(ctx, args.Params, args.CommonParams); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc63 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing awardByRuleEngine: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "awardByRuleEngine", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err62 = thrift.WrapTException(err2)
    }
    if err2 := _exc63.Write(ctx, oprot); _write_err62 == nil && err2 != nil {
      _write_err62 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err62 == nil && err2 != nil {
      _write_err62 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err62 == nil && err2 != nil {
      _write_err62 = thrift.WrapTException(err2)
    }
    if _write_err62 != nil {
      return false, thrift.WrapTException(_write_err62)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "awardByRuleEngine", thrift.REPLY, seqId); err2 != nil {
    _write_err62 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err62 == nil && err2 != nil {
    _write_err62 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err62 == nil && err2 != nil {
    _write_err62 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err62 == nil && err2 != nil {
    _write_err62 = thrift.WrapTException(err2)
  }
  if _write_err62 != nil {
    return false, thrift.WrapTException(_write_err62)
  }
  return true, err
}

type commonAwardServiceProcessorGetNoPubFullAwardList struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorGetNoPubFullAwardList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err64 error
  args := CommonAwardServiceGetNoPubFullAwardListArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getNoPubFullAwardList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceGetNoPubFullAwardListResult{}
  if retval, err2 := p.handler.GetNoPubFullAwardList(ctx, args.Params); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc65 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNoPubFullAwardList: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getNoPubFullAwardList", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err64 = thrift.WrapTException(err2)
    }
    if err2 := _exc65.Write(ctx, oprot); _write_err64 == nil && err2 != nil {
      _write_err64 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err64 == nil && err2 != nil {
      _write_err64 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err64 == nil && err2 != nil {
      _write_err64 = thrift.WrapTException(err2)
    }
    if _write_err64 != nil {
      return false, thrift.WrapTException(_write_err64)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getNoPubFullAwardList", thrift.REPLY, seqId); err2 != nil {
    _write_err64 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err64 == nil && err2 != nil {
    _write_err64 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err64 == nil && err2 != nil {
    _write_err64 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err64 == nil && err2 != nil {
    _write_err64 = thrift.WrapTException(err2)
  }
  if _write_err64 != nil {
    return false, thrift.WrapTException(_write_err64)
  }
  return true, err
}

type commonAwardServiceProcessorGetNoPubAwardList struct {
  handler CommonAwardService
}

func (p *commonAwardServiceProcessorGetNoPubAwardList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err66 error
  args := CommonAwardServiceGetNoPubAwardListArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "getNoPubAwardList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := CommonAwardServiceGetNoPubAwardListResult{}
  if retval, err2 := p.handler.GetNoPubAwardList(ctx, args.Params); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    _exc67 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNoPubAwardList: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "getNoPubAwardList", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err66 = thrift.WrapTException(err2)
    }
    if err2 := _exc67.Write(ctx, oprot); _write_err66 == nil && err2 != nil {
      _write_err66 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err66 == nil && err2 != nil {
      _write_err66 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err66 == nil && err2 != nil {
      _write_err66 = thrift.WrapTException(err2)
    }
    if _write_err66 != nil {
      return false, thrift.WrapTException(_write_err66)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "getNoPubAwardList", thrift.REPLY, seqId); err2 != nil {
    _write_err66 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err66 == nil && err2 != nil {
    _write_err66 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err66 == nil && err2 != nil {
    _write_err66 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err66 == nil && err2 != nil {
    _write_err66 = thrift.WrapTException(err2)
  }
  if _write_err66 != nil {
    return false, thrift.WrapTException(_write_err66)
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Params
//  - CommonParams
type CommonAwardServiceSendAwardRuleEngineArgs struct {
  Params *AwardRuleEngineReq `thrift:"params,1" db:"params" json:"params"`
  CommonParams *CommonParameterReq `thrift:"commonParams,2" db:"commonParams" json:"commonParams"`
}

func NewCommonAwardServiceSendAwardRuleEngineArgs() *CommonAwardServiceSendAwardRuleEngineArgs {
  return &CommonAwardServiceSendAwardRuleEngineArgs{}
}

var CommonAwardServiceSendAwardRuleEngineArgs_Params_DEFAULT *AwardRuleEngineReq
func (p *CommonAwardServiceSendAwardRuleEngineArgs) GetParams() *AwardRuleEngineReq {
  if !p.IsSetParams() {
    return CommonAwardServiceSendAwardRuleEngineArgs_Params_DEFAULT
  }
return p.Params
}
var CommonAwardServiceSendAwardRuleEngineArgs_CommonParams_DEFAULT *CommonParameterReq
func (p *CommonAwardServiceSendAwardRuleEngineArgs) GetCommonParams() *CommonParameterReq {
  if !p.IsSetCommonParams() {
    return CommonAwardServiceSendAwardRuleEngineArgs_CommonParams_DEFAULT
  }
return p.CommonParams
}
func (p *CommonAwardServiceSendAwardRuleEngineArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs) IsSetCommonParams() bool {
  return p.CommonParams != nil
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &AwardRuleEngineReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  p.CommonParams = &CommonParameterReq{}
  if err := p.CommonParams.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.CommonParams), err)
  }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "sendAwardRuleEngine_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "commonParams", thrift.STRUCT, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:commonParams: ", p), err) }
  if err := p.CommonParams.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.CommonParams), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:commonParams: ", p), err) }
  return err
}

func (p *CommonAwardServiceSendAwardRuleEngineArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceSendAwardRuleEngineArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceSendAwardRuleEngineResult struct {
  Success *ResultInfo `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceSendAwardRuleEngineResult() *CommonAwardServiceSendAwardRuleEngineResult {
  return &CommonAwardServiceSendAwardRuleEngineResult{}
}

var CommonAwardServiceSendAwardRuleEngineResult_Success_DEFAULT *ResultInfo
func (p *CommonAwardServiceSendAwardRuleEngineResult) GetSuccess() *ResultInfo {
  if !p.IsSetSuccess() {
    return CommonAwardServiceSendAwardRuleEngineResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceSendAwardRuleEngineResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceSendAwardRuleEngineResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &ResultInfo{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "sendAwardRuleEngine_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceSendAwardRuleEngineResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceSendAwardRuleEngineResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceSendAwardRuleEngineResult(%+v)", *p)
}

// Attributes:
//  - Params
type CommonAwardServiceGetAwardListArgs struct {
  Params *AwardListReq `thrift:"params,1" db:"params" json:"params"`
}

func NewCommonAwardServiceGetAwardListArgs() *CommonAwardServiceGetAwardListArgs {
  return &CommonAwardServiceGetAwardListArgs{}
}

var CommonAwardServiceGetAwardListArgs_Params_DEFAULT *AwardListReq
func (p *CommonAwardServiceGetAwardListArgs) GetParams() *AwardListReq {
  if !p.IsSetParams() {
    return CommonAwardServiceGetAwardListArgs_Params_DEFAULT
  }
return p.Params
}
func (p *CommonAwardServiceGetAwardListArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceGetAwardListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardListArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &AwardListReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getAwardList_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetAwardListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceGetAwardListArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetAwardListArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceGetAwardListResult struct {
  Success *ResultInfo `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceGetAwardListResult() *CommonAwardServiceGetAwardListResult {
  return &CommonAwardServiceGetAwardListResult{}
}

var CommonAwardServiceGetAwardListResult_Success_DEFAULT *ResultInfo
func (p *CommonAwardServiceGetAwardListResult) GetSuccess() *ResultInfo {
  if !p.IsSetSuccess() {
    return CommonAwardServiceGetAwardListResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceGetAwardListResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceGetAwardListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardListResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &ResultInfo{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getAwardList_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetAwardListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceGetAwardListResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetAwardListResult(%+v)", *p)
}

// Attributes:
//  - Params
type CommonAwardServiceGetAwardRuleListArgs struct {
  Params *AwardRuleListReq `thrift:"params,1" db:"params" json:"params"`
}

func NewCommonAwardServiceGetAwardRuleListArgs() *CommonAwardServiceGetAwardRuleListArgs {
  return &CommonAwardServiceGetAwardRuleListArgs{}
}

var CommonAwardServiceGetAwardRuleListArgs_Params_DEFAULT *AwardRuleListReq
func (p *CommonAwardServiceGetAwardRuleListArgs) GetParams() *AwardRuleListReq {
  if !p.IsSetParams() {
    return CommonAwardServiceGetAwardRuleListArgs_Params_DEFAULT
  }
return p.Params
}
func (p *CommonAwardServiceGetAwardRuleListArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceGetAwardRuleListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardRuleListArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &AwardRuleListReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardRuleListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getAwardRuleList_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetAwardRuleListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceGetAwardRuleListArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetAwardRuleListArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceGetAwardRuleListResult struct {
  Success *RuleResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceGetAwardRuleListResult() *CommonAwardServiceGetAwardRuleListResult {
  return &CommonAwardServiceGetAwardRuleListResult{}
}

var CommonAwardServiceGetAwardRuleListResult_Success_DEFAULT *RuleResult_
func (p *CommonAwardServiceGetAwardRuleListResult) GetSuccess() *RuleResult_ {
  if !p.IsSetSuccess() {
    return CommonAwardServiceGetAwardRuleListResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceGetAwardRuleListResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceGetAwardRuleListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardRuleListResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &RuleResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceGetAwardRuleListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getAwardRuleList_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetAwardRuleListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceGetAwardRuleListResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetAwardRuleListResult(%+v)", *p)
}

// Attributes:
//  - Params
type CommonAwardServiceGetFullAwardListArgs struct {
  Params *FullAwardListReq `thrift:"params,1" db:"params" json:"params"`
}

func NewCommonAwardServiceGetFullAwardListArgs() *CommonAwardServiceGetFullAwardListArgs {
  return &CommonAwardServiceGetFullAwardListArgs{}
}

var CommonAwardServiceGetFullAwardListArgs_Params_DEFAULT *FullAwardListReq
func (p *CommonAwardServiceGetFullAwardListArgs) GetParams() *FullAwardListReq {
  if !p.IsSetParams() {
    return CommonAwardServiceGetFullAwardListArgs_Params_DEFAULT
  }
return p.Params
}
func (p *CommonAwardServiceGetFullAwardListArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceGetFullAwardListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &FullAwardListReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getFullAwardList_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceGetFullAwardListArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetFullAwardListArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceGetFullAwardListResult struct {
  Success *FullAwardListResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceGetFullAwardListResult() *CommonAwardServiceGetFullAwardListResult {
  return &CommonAwardServiceGetFullAwardListResult{}
}

var CommonAwardServiceGetFullAwardListResult_Success_DEFAULT *FullAwardListResult_
func (p *CommonAwardServiceGetFullAwardListResult) GetSuccess() *FullAwardListResult_ {
  if !p.IsSetSuccess() {
    return CommonAwardServiceGetFullAwardListResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceGetFullAwardListResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceGetFullAwardListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &FullAwardListResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getFullAwardList_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceGetFullAwardListResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetFullAwardListResult(%+v)", *p)
}

// Attributes:
//  - Params
type CommonAwardServiceGetFullAwardListV2Args struct {
  Params *FullAwardListReq `thrift:"params,1" db:"params" json:"params"`
}

func NewCommonAwardServiceGetFullAwardListV2Args() *CommonAwardServiceGetFullAwardListV2Args {
  return &CommonAwardServiceGetFullAwardListV2Args{}
}

var CommonAwardServiceGetFullAwardListV2Args_Params_DEFAULT *FullAwardListReq
func (p *CommonAwardServiceGetFullAwardListV2Args) GetParams() *FullAwardListReq {
  if !p.IsSetParams() {
    return CommonAwardServiceGetFullAwardListV2Args_Params_DEFAULT
  }
return p.Params
}
func (p *CommonAwardServiceGetFullAwardListV2Args) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceGetFullAwardListV2Args) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListV2Args)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &FullAwardListReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListV2Args) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getFullAwardListV2_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListV2Args) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceGetFullAwardListV2Args) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetFullAwardListV2Args(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceGetFullAwardListV2Result struct {
  Success *FullAwardListResultV2 `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceGetFullAwardListV2Result() *CommonAwardServiceGetFullAwardListV2Result {
  return &CommonAwardServiceGetFullAwardListV2Result{}
}

var CommonAwardServiceGetFullAwardListV2Result_Success_DEFAULT *FullAwardListResultV2
func (p *CommonAwardServiceGetFullAwardListV2Result) GetSuccess() *FullAwardListResultV2 {
  if !p.IsSetSuccess() {
    return CommonAwardServiceGetFullAwardListV2Result_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceGetFullAwardListV2Result) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceGetFullAwardListV2Result) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListV2Result)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &FullAwardListResultV2{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListV2Result) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getFullAwardListV2_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetFullAwardListV2Result) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceGetFullAwardListV2Result) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetFullAwardListV2Result(%+v)", *p)
}

// Attributes:
//  - Params
//  - CommonParams
type CommonAwardServiceAwardByRuleEngineArgs struct {
  Params *AwardRuleEngineV2Req `thrift:"params,1" db:"params" json:"params"`
  CommonParams *CommonParameterReq `thrift:"commonParams,2" db:"commonParams" json:"commonParams"`
}

func NewCommonAwardServiceAwardByRuleEngineArgs() *CommonAwardServiceAwardByRuleEngineArgs {
  return &CommonAwardServiceAwardByRuleEngineArgs{}
}

var CommonAwardServiceAwardByRuleEngineArgs_Params_DEFAULT *AwardRuleEngineV2Req
func (p *CommonAwardServiceAwardByRuleEngineArgs) GetParams() *AwardRuleEngineV2Req {
  if !p.IsSetParams() {
    return CommonAwardServiceAwardByRuleEngineArgs_Params_DEFAULT
  }
return p.Params
}
var CommonAwardServiceAwardByRuleEngineArgs_CommonParams_DEFAULT *CommonParameterReq
func (p *CommonAwardServiceAwardByRuleEngineArgs) GetCommonParams() *CommonParameterReq {
  if !p.IsSetCommonParams() {
    return CommonAwardServiceAwardByRuleEngineArgs_CommonParams_DEFAULT
  }
return p.CommonParams
}
func (p *CommonAwardServiceAwardByRuleEngineArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceAwardByRuleEngineArgs) IsSetCommonParams() bool {
  return p.CommonParams != nil
}

func (p *CommonAwardServiceAwardByRuleEngineArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &AwardRuleEngineV2Req{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineArgs)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  p.CommonParams = &CommonParameterReq{}
  if err := p.CommonParams.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.CommonParams), err)
  }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "awardByRuleEngine_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceAwardByRuleEngineArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "commonParams", thrift.STRUCT, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:commonParams: ", p), err) }
  if err := p.CommonParams.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.CommonParams), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:commonParams: ", p), err) }
  return err
}

func (p *CommonAwardServiceAwardByRuleEngineArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceAwardByRuleEngineArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceAwardByRuleEngineResult struct {
  Success *ResultInfo `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceAwardByRuleEngineResult() *CommonAwardServiceAwardByRuleEngineResult {
  return &CommonAwardServiceAwardByRuleEngineResult{}
}

var CommonAwardServiceAwardByRuleEngineResult_Success_DEFAULT *ResultInfo
func (p *CommonAwardServiceAwardByRuleEngineResult) GetSuccess() *ResultInfo {
  if !p.IsSetSuccess() {
    return CommonAwardServiceAwardByRuleEngineResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceAwardByRuleEngineResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceAwardByRuleEngineResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &ResultInfo{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "awardByRuleEngine_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceAwardByRuleEngineResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceAwardByRuleEngineResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceAwardByRuleEngineResult(%+v)", *p)
}

// Attributes:
//  - Params
type CommonAwardServiceGetNoPubFullAwardListArgs struct {
  Params *FullAwardListReq `thrift:"params,1" db:"params" json:"params"`
}

func NewCommonAwardServiceGetNoPubFullAwardListArgs() *CommonAwardServiceGetNoPubFullAwardListArgs {
  return &CommonAwardServiceGetNoPubFullAwardListArgs{}
}

var CommonAwardServiceGetNoPubFullAwardListArgs_Params_DEFAULT *FullAwardListReq
func (p *CommonAwardServiceGetNoPubFullAwardListArgs) GetParams() *FullAwardListReq {
  if !p.IsSetParams() {
    return CommonAwardServiceGetNoPubFullAwardListArgs_Params_DEFAULT
  }
return p.Params
}
func (p *CommonAwardServiceGetNoPubFullAwardListArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &FullAwardListReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getNoPubFullAwardList_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceGetNoPubFullAwardListArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetNoPubFullAwardListArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceGetNoPubFullAwardListResult struct {
  Success *FullAwardListResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceGetNoPubFullAwardListResult() *CommonAwardServiceGetNoPubFullAwardListResult {
  return &CommonAwardServiceGetNoPubFullAwardListResult{}
}

var CommonAwardServiceGetNoPubFullAwardListResult_Success_DEFAULT *FullAwardListResult_
func (p *CommonAwardServiceGetNoPubFullAwardListResult) GetSuccess() *FullAwardListResult_ {
  if !p.IsSetSuccess() {
    return CommonAwardServiceGetNoPubFullAwardListResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceGetNoPubFullAwardListResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &FullAwardListResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getNoPubFullAwardList_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetNoPubFullAwardListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceGetNoPubFullAwardListResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetNoPubFullAwardListResult(%+v)", *p)
}

// Attributes:
//  - Params
type CommonAwardServiceGetNoPubAwardListArgs struct {
  Params *AwardListReq `thrift:"params,1" db:"params" json:"params"`
}

func NewCommonAwardServiceGetNoPubAwardListArgs() *CommonAwardServiceGetNoPubAwardListArgs {
  return &CommonAwardServiceGetNoPubAwardListArgs{}
}

var CommonAwardServiceGetNoPubAwardListArgs_Params_DEFAULT *AwardListReq
func (p *CommonAwardServiceGetNoPubAwardListArgs) GetParams() *AwardListReq {
  if !p.IsSetParams() {
    return CommonAwardServiceGetNoPubAwardListArgs_Params_DEFAULT
  }
return p.Params
}
func (p *CommonAwardServiceGetNoPubAwardListArgs) IsSetParams() bool {
  return p.Params != nil
}

func (p *CommonAwardServiceGetNoPubAwardListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubAwardListArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Params = &AwardListReq{}
  if err := p.Params.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Params), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubAwardListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getNoPubAwardList_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetNoPubAwardListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "params", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:params: ", p), err) }
  if err := p.Params.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Params), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:params: ", p), err) }
  return err
}

func (p *CommonAwardServiceGetNoPubAwardListArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetNoPubAwardListArgs(%+v)", *p)
}

// Attributes:
//  - Success
type CommonAwardServiceGetNoPubAwardListResult struct {
  Success *AwardListResult_ `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewCommonAwardServiceGetNoPubAwardListResult() *CommonAwardServiceGetNoPubAwardListResult {
  return &CommonAwardServiceGetNoPubAwardListResult{}
}

var CommonAwardServiceGetNoPubAwardListResult_Success_DEFAULT *AwardListResult_
func (p *CommonAwardServiceGetNoPubAwardListResult) GetSuccess() *AwardListResult_ {
  if !p.IsSetSuccess() {
    return CommonAwardServiceGetNoPubAwardListResult_Success_DEFAULT
  }
return p.Success
}
func (p *CommonAwardServiceGetNoPubAwardListResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *CommonAwardServiceGetNoPubAwardListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubAwardListResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &AwardListResult_{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *CommonAwardServiceGetNoPubAwardListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "getNoPubAwardList_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CommonAwardServiceGetNoPubAwardListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *CommonAwardServiceGetNoPubAwardListResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CommonAwardServiceGetNoPubAwardListResult(%+v)", *p)
}


