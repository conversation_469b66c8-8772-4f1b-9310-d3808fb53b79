// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package tokenservice

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//   - Result_
//   - Errorcode: 参见文档*
//   - Desc
type CheckResult_ struct {
	Result_   bool   `thrift:"result,1,required" db:"result" json:"result"`
	Errorcode int32  `thrift:"errorcode,2" db:"errorcode" json:"errorcode"`
	Desc      string `thrift:"desc,3" db:"desc" json:"desc"`
}

func NewCheckResult_() *CheckResult_ {
	return &CheckResult_{}
}

func (p *CheckResult_) GetResult_() bool {
	return p.Result_
}

func (p *CheckResult_) GetErrorcode() int32 {
	return p.Errorcode
}

func (p *CheckResult_) GetDesc() string {
	return p.Desc
}
func (p *CheckResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetResult_ bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetResult_ = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetResult_ {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Result_ is not set"))
	}
	return nil
}

func (p *CheckResult_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Result_ = v
	}
	return nil
}

func (p *CheckResult_) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Errorcode = v
	}
	return nil
}

func (p *CheckResult_) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *CheckResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "CheckResult"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CheckResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "result", thrift.BOOL, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:result: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.Result_)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.result (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:result: ", p), err)
	}
	return err
}

func (p *CheckResult_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "errorcode", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:errorcode: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Errorcode)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.errorcode (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:errorcode: ", p), err)
	}
	return err
}

func (p *CheckResult_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "desc", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:desc: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Desc)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.desc (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:desc: ", p), err)
	}
	return err
}

func (p *CheckResult_) Equals(other *CheckResult_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Result_ != other.Result_ {
		return false
	}
	if p.Errorcode != other.Errorcode {
		return false
	}
	if p.Desc != other.Desc {
		return false
	}
	return true
}

func (p *CheckResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckResult_(%+v)", *p)
}

// Attributes:
//   - Kugouid
//   - Token
//   - IP
//   - Appid
type CheckPara struct {
	Kugouid int64  `thrift:"kugouid,1,required" db:"kugouid" json:"kugouid"`
	Token   string `thrift:"token,2,required" db:"token" json:"token"`
	IP      string `thrift:"ip,3,required" db:"ip" json:"ip"`
	Appid   int32  `thrift:"appid,4,required" db:"appid" json:"appid"`
}

func NewCheckPara() *CheckPara {
	return &CheckPara{}
}

func (p *CheckPara) GetKugouid() int64 {
	return p.Kugouid
}

func (p *CheckPara) GetToken() string {
	return p.Token
}

func (p *CheckPara) GetIP() string {
	return p.IP
}

func (p *CheckPara) GetAppid() int32 {
	return p.Appid
}
func (p *CheckPara) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouid bool = false
	var issetToken bool = false
	var issetIP bool = false
	var issetAppid bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetToken = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetIP = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetAppid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Kugouid is not set"))
	}
	if !issetToken {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Token is not set"))
	}
	if !issetIP {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IP is not set"))
	}
	if !issetAppid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Appid is not set"))
	}
	return nil
}

func (p *CheckPara) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Kugouid = v
	}
	return nil
}

func (p *CheckPara) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Token = v
	}
	return nil
}

func (p *CheckPara) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.IP = v
	}
	return nil
}

func (p *CheckPara) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *CheckPara) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "CheckPara"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CheckPara) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouid", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.Kugouid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouid (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouid: ", p), err)
	}
	return err
}

func (p *CheckPara) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:token: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Token)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.token (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:token: ", p), err)
	}
	return err
}

func (p *CheckPara) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ip", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ip: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.IP)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ip (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ip: ", p), err)
	}
	return err
}

func (p *CheckPara) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appid", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:appid: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Appid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appid (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:appid: ", p), err)
	}
	return err
}

func (p *CheckPara) Equals(other *CheckPara) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Kugouid != other.Kugouid {
		return false
	}
	if p.Token != other.Token {
		return false
	}
	if p.IP != other.IP {
		return false
	}
	if p.Appid != other.Appid {
		return false
	}
	return true
}

func (p *CheckPara) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckPara(%+v)", *p)
}

// Attributes:
//   - Kugouid
//   - P2
//   - Clientip
//   - Appid
//   - ClienttimeMs: *毫秒的时间戳**
type CheckParaV2 struct {
	Kugouid      int64  `thrift:"kugouid,1,required" db:"kugouid" json:"kugouid"`
	P2           string `thrift:"p2,2,required" db:"p2" json:"p2"`
	Clientip     string `thrift:"clientip,3,required" db:"clientip" json:"clientip"`
	Appid        int32  `thrift:"appid,4,required" db:"appid" json:"appid"`
	ClienttimeMs int64  `thrift:"clienttime_ms,5,required" db:"clienttime_ms" json:"clienttime_ms"`
}

func NewCheckParaV2() *CheckParaV2 {
	return &CheckParaV2{}
}

func (p *CheckParaV2) GetKugouid() int64 {
	return p.Kugouid
}

func (p *CheckParaV2) GetP2() string {
	return p.P2
}

func (p *CheckParaV2) GetClientip() string {
	return p.Clientip
}

func (p *CheckParaV2) GetAppid() int32 {
	return p.Appid
}

func (p *CheckParaV2) GetClienttimeMs() int64 {
	return p.ClienttimeMs
}
func (p *CheckParaV2) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouid bool = false
	var issetP2 bool = false
	var issetClientip bool = false
	var issetAppid bool = false
	var issetClienttimeMs bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetP2 = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetClientip = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetAppid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetClienttimeMs = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Kugouid is not set"))
	}
	if !issetP2 {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field P2 is not set"))
	}
	if !issetClientip {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Clientip is not set"))
	}
	if !issetAppid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Appid is not set"))
	}
	if !issetClienttimeMs {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field ClienttimeMs is not set"))
	}
	return nil
}

func (p *CheckParaV2) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Kugouid = v
	}
	return nil
}

func (p *CheckParaV2) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.P2 = v
	}
	return nil
}

func (p *CheckParaV2) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Clientip = v
	}
	return nil
}

func (p *CheckParaV2) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *CheckParaV2) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.ClienttimeMs = v
	}
	return nil
}

func (p *CheckParaV2) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "CheckPara_v2"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CheckParaV2) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouid", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.Kugouid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouid (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouid: ", p), err)
	}
	return err
}

func (p *CheckParaV2) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "p2", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:p2: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.P2)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.p2 (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:p2: ", p), err)
	}
	return err
}

func (p *CheckParaV2) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "clientip", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:clientip: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Clientip)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.clientip (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:clientip: ", p), err)
	}
	return err
}

func (p *CheckParaV2) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appid", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:appid: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Appid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appid (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:appid: ", p), err)
	}
	return err
}

func (p *CheckParaV2) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "clienttime_ms", thrift.I64, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:clienttime_ms: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.ClienttimeMs)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.clienttime_ms (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:clienttime_ms: ", p), err)
	}
	return err
}

func (p *CheckParaV2) Equals(other *CheckParaV2) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Kugouid != other.Kugouid {
		return false
	}
	if p.P2 != other.P2 {
		return false
	}
	if p.Clientip != other.Clientip {
		return false
	}
	if p.Appid != other.Appid {
		return false
	}
	if p.ClienttimeMs != other.ClienttimeMs {
		return false
	}
	return true
}

func (p *CheckParaV2) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckParaV2(%+v)", *p)
}

// Attributes:
//   - Kugouid
//   - Token
//   - IP
//   - Appid
//   - Roomid
type CheckTokenPara struct {
	Kugouid int64  `thrift:"kugouid,1,required" db:"kugouid" json:"kugouid"`
	Token   string `thrift:"token,2,required" db:"token" json:"token"`
	IP      string `thrift:"ip,3,required" db:"ip" json:"ip"`
	Appid   int32  `thrift:"appid,4,required" db:"appid" json:"appid"`
	Roomid  int32  `thrift:"roomid,5" db:"roomid" json:"roomid"`
}

func NewCheckTokenPara() *CheckTokenPara {
	return &CheckTokenPara{}
}

func (p *CheckTokenPara) GetKugouid() int64 {
	return p.Kugouid
}

func (p *CheckTokenPara) GetToken() string {
	return p.Token
}

func (p *CheckTokenPara) GetIP() string {
	return p.IP
}

func (p *CheckTokenPara) GetAppid() int32 {
	return p.Appid
}

func (p *CheckTokenPara) GetRoomid() int32 {
	return p.Roomid
}
func (p *CheckTokenPara) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetKugouid bool = false
	var issetToken bool = false
	var issetIP bool = false
	var issetAppid bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetKugouid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetToken = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetIP = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetAppid = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetKugouid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Kugouid is not set"))
	}
	if !issetToken {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Token is not set"))
	}
	if !issetIP {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IP is not set"))
	}
	if !issetAppid {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Appid is not set"))
	}
	return nil
}

func (p *CheckTokenPara) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Kugouid = v
	}
	return nil
}

func (p *CheckTokenPara) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Token = v
	}
	return nil
}

func (p *CheckTokenPara) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.IP = v
	}
	return nil
}

func (p *CheckTokenPara) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *CheckTokenPara) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Roomid = v
	}
	return nil
}

func (p *CheckTokenPara) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "CheckTokenPara"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *CheckTokenPara) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "kugouid", thrift.I64, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:kugouid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.Kugouid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.kugouid (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:kugouid: ", p), err)
	}
	return err
}

func (p *CheckTokenPara) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "token", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:token: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Token)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.token (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:token: ", p), err)
	}
	return err
}

func (p *CheckTokenPara) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ip", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ip: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.IP)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ip (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ip: ", p), err)
	}
	return err
}

func (p *CheckTokenPara) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appid", thrift.I32, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:appid: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Appid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appid (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:appid: ", p), err)
	}
	return err
}

func (p *CheckTokenPara) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomid", thrift.I32, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:roomid: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Roomid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomid (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:roomid: ", p), err)
	}
	return err
}

func (p *CheckTokenPara) Equals(other *CheckTokenPara) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Kugouid != other.Kugouid {
		return false
	}
	if p.Token != other.Token {
		return false
	}
	if p.IP != other.IP {
		return false
	}
	if p.Appid != other.Appid {
		return false
	}
	if p.Roomid != other.Roomid {
		return false
	}
	return true
}

func (p *CheckTokenPara) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckTokenPara(%+v)", *p)
}

// Attributes:
//   - Result_
//   - Userid
//   - Ext
//   - Desc
//   - Nickname
//   - Richlevel
//   - Ismaster: 是否主播，1：是当前房间的主播，0：否（普通用户）,2:是其他房间的主播，3：金主
//
// *
//   - Errorcode: 参见文档*
type Result_ struct {
	Result_   bool   `thrift:"result,1,required" db:"result" json:"result"`
	Userid    int64  `thrift:"userid,2" db:"userid" json:"userid"`
	Ext       string `thrift:"ext,3" db:"ext" json:"ext"`
	Desc      string `thrift:"desc,4" db:"desc" json:"desc"`
	Nickname  string `thrift:"nickname,5" db:"nickname" json:"nickname"`
	Richlevel int32  `thrift:"richlevel,6" db:"richlevel" json:"richlevel"`
	Ismaster  int32  `thrift:"ismaster,7" db:"ismaster" json:"ismaster"`
	Errorcode int32  `thrift:"errorcode,8" db:"errorcode" json:"errorcode"`
}

func NewResult_() *Result_ {
	return &Result_{}
}

func (p *Result_) GetResult_() bool {
	return p.Result_
}

func (p *Result_) GetUserid() int64 {
	return p.Userid
}

func (p *Result_) GetExt() string {
	return p.Ext
}

func (p *Result_) GetDesc() string {
	return p.Desc
}

func (p *Result_) GetNickname() string {
	return p.Nickname
}

func (p *Result_) GetRichlevel() int32 {
	return p.Richlevel
}

func (p *Result_) GetIsmaster() int32 {
	return p.Ismaster
}

func (p *Result_) GetErrorcode() int32 {
	return p.Errorcode
}
func (p *Result_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetResult_ bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetResult_ = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField8(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetResult_ {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Result_ is not set"))
	}
	return nil
}

func (p *Result_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Result_ = v
	}
	return nil
}

func (p *Result_) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *Result_) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.Ext = v
	}
	return nil
}

func (p *Result_) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *Result_) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.Nickname = v
	}
	return nil
}

func (p *Result_) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.Richlevel = v
	}
	return nil
}

func (p *Result_) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.Ismaster = v
	}
	return nil
}

func (p *Result_) ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 8: ", err)
	} else {
		p.Errorcode = v
	}
	return nil
}

func (p *Result_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "Result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField8(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *Result_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "result", thrift.BOOL, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:result: ", p), err)
	}
	if err := oprot.WriteBool(ctx, bool(p.Result_)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.result (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:result: ", p), err)
	}
	return err
}

func (p *Result_) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userid", thrift.I64, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:userid: ", p), err)
	}
	if err := oprot.WriteI64(ctx, int64(p.Userid)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userid (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:userid: ", p), err)
	}
	return err
}

func (p *Result_) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ext", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ext: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Ext)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ext (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ext: ", p), err)
	}
	return err
}

func (p *Result_) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "desc", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:desc: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Desc)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.desc (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:desc: ", p), err)
	}
	return err
}

func (p *Result_) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "nickname", thrift.STRING, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:nickname: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Nickname)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.nickname (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:nickname: ", p), err)
	}
	return err
}

func (p *Result_) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "richlevel", thrift.I32, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:richlevel: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Richlevel)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.richlevel (6) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:richlevel: ", p), err)
	}
	return err
}

func (p *Result_) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "ismaster", thrift.I32, 7); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:ismaster: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Ismaster)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.ismaster (7) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 7:ismaster: ", p), err)
	}
	return err
}

func (p *Result_) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "errorcode", thrift.I32, 8); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:errorcode: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Errorcode)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.errorcode (8) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 8:errorcode: ", p), err)
	}
	return err
}

func (p *Result_) Equals(other *Result_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Result_ != other.Result_ {
		return false
	}
	if p.Userid != other.Userid {
		return false
	}
	if p.Ext != other.Ext {
		return false
	}
	if p.Desc != other.Desc {
		return false
	}
	if p.Nickname != other.Nickname {
		return false
	}
	if p.Richlevel != other.Richlevel {
		return false
	}
	if p.Ismaster != other.Ismaster {
		return false
	}
	if p.Errorcode != other.Errorcode {
		return false
	}
	return true
}

func (p *Result_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Result_(%+v)", *p)
}
