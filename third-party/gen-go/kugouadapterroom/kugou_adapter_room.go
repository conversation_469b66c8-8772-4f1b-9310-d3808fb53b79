// Code generated by Thrift Compiler (0.17.0). DO NOT EDIT.

package kugouadapterroom

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"time"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

// Attributes:
//  - RoomId
//  - RoomName
//  - RoomLogo
//  - AnchorId
//  - EncodeAnchorId
//  - RoomStatus
//  - PrivacyRoom
//  - RoomType
type RoomInfo struct {
	RoomId         string `thrift:"roomId,1,required" db:"roomId" json:"roomId"`
	RoomName       string `thrift:"roomName,2,required" db:"roomName" json:"roomName"`
	RoomLogo       string `thrift:"roomLogo,3,required" db:"roomLogo" json:"roomLogo"`
	AnchorId       string `thrift:"anchorId,4,required" db:"anchorId" json:"anchorId"`
	EncodeAnchorId string `thrift:"encodeAnchorId,5,required" db:"encodeAnchorId" json:"encodeAnchorId"`
	RoomStatus     int32  `thrift:"roomStatus,6,required" db:"roomStatus" json:"roomStatus"`
	PrivacyRoom    *int32 `thrift:"privacyRoom,7" db:"privacyRoom" json:"privacyRoom,omitempty"`
	RoomType       *int32 `thrift:"roomType,8" db:"roomType" json:"roomType,omitempty"`
}

func NewRoomInfo() *RoomInfo {
	return &RoomInfo{}
}

func (p *RoomInfo) GetRoomId() string {
	return p.RoomId
}

func (p *RoomInfo) GetRoomName() string {
	return p.RoomName
}

func (p *RoomInfo) GetRoomLogo() string {
	return p.RoomLogo
}

func (p *RoomInfo) GetAnchorId() string {
	return p.AnchorId
}

func (p *RoomInfo) GetEncodeAnchorId() string {
	return p.EncodeAnchorId
}

func (p *RoomInfo) GetRoomStatus() int32 {
	return p.RoomStatus
}

var RoomInfo_PrivacyRoom_DEFAULT int32

func (p *RoomInfo) GetPrivacyRoom() int32 {
	if !p.IsSetPrivacyRoom() {
		return RoomInfo_PrivacyRoom_DEFAULT
	}
	return *p.PrivacyRoom
}

var RoomInfo_RoomType_DEFAULT int32

func (p *RoomInfo) GetRoomType() int32 {
	if !p.IsSetRoomType() {
		return RoomInfo_RoomType_DEFAULT
	}
	return *p.RoomType
}
func (p *RoomInfo) IsSetPrivacyRoom() bool {
	return p.PrivacyRoom != nil
}

func (p *RoomInfo) IsSetRoomType() bool {
	return p.RoomType != nil
}

func (p *RoomInfo) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRoomId bool = false
	var issetRoomName bool = false
	var issetRoomLogo bool = false
	var issetAnchorId bool = false
	var issetEncodeAnchorId bool = false
	var issetRoomStatus bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetRoomName = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
				issetRoomLogo = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
				issetAnchorId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField5(ctx, iprot); err != nil {
					return err
				}
				issetEncodeAnchorId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField6(ctx, iprot); err != nil {
					return err
				}
				issetRoomStatus = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField7(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField8(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	if !issetRoomName {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomName is not set"))
	}
	if !issetRoomLogo {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomLogo is not set"))
	}
	if !issetAnchorId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field AnchorId is not set"))
	}
	if !issetEncodeAnchorId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field EncodeAnchorId is not set"))
	}
	if !issetRoomStatus {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomStatus is not set"))
	}
	return nil
}

func (p *RoomInfo) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *RoomInfo) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.RoomName = v
	}
	return nil
}

func (p *RoomInfo) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.RoomLogo = v
	}
	return nil
}

func (p *RoomInfo) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 4: ", err)
	} else {
		p.AnchorId = v
	}
	return nil
}

func (p *RoomInfo) ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 5: ", err)
	} else {
		p.EncodeAnchorId = v
	}
	return nil
}

func (p *RoomInfo) ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 6: ", err)
	} else {
		p.RoomStatus = v
	}
	return nil
}

func (p *RoomInfo) ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 7: ", err)
	} else {
		p.PrivacyRoom = &v
	}
	return nil
}

func (p *RoomInfo) ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 8: ", err)
	} else {
		p.RoomType = &v
	}
	return nil
}

func (p *RoomInfo) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "RoomInfo"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField5(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField6(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField7(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField8(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *RoomInfo) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *RoomInfo) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomName", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:roomName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.RoomName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomName (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:roomName: ", p), err)
	}
	return err
}

func (p *RoomInfo) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomLogo", thrift.STRING, 3); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:roomLogo: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.RoomLogo)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomLogo (3) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 3:roomLogo: ", p), err)
	}
	return err
}

func (p *RoomInfo) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "anchorId", thrift.STRING, 4); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:anchorId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AnchorId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.anchorId (4) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 4:anchorId: ", p), err)
	}
	return err
}

func (p *RoomInfo) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "encodeAnchorId", thrift.STRING, 5); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:encodeAnchorId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.EncodeAnchorId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.encodeAnchorId (5) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 5:encodeAnchorId: ", p), err)
	}
	return err
}

func (p *RoomInfo) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomStatus", thrift.I32, 6); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:roomStatus: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.RoomStatus)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomStatus (6) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 6:roomStatus: ", p), err)
	}
	return err
}

func (p *RoomInfo) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetPrivacyRoom() {
		if err := oprot.WriteFieldBegin(ctx, "privacyRoom", thrift.I32, 7); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:privacyRoom: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.PrivacyRoom)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.privacyRoom (7) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 7:privacyRoom: ", p), err)
		}
	}
	return err
}

func (p *RoomInfo) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetRoomType() {
		if err := oprot.WriteFieldBegin(ctx, "roomType", thrift.I32, 8); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:roomType: ", p), err)
		}
		if err := oprot.WriteI32(ctx, int32(*p.RoomType)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.roomType (8) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 8:roomType: ", p), err)
		}
	}
	return err
}

func (p *RoomInfo) Equals(other *RoomInfo) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	if p.RoomName != other.RoomName {
		return false
	}
	if p.RoomLogo != other.RoomLogo {
		return false
	}
	if p.AnchorId != other.AnchorId {
		return false
	}
	if p.EncodeAnchorId != other.EncodeAnchorId {
		return false
	}
	if p.RoomStatus != other.RoomStatus {
		return false
	}
	if p.PrivacyRoom != other.PrivacyRoom {
		if p.PrivacyRoom == nil || other.PrivacyRoom == nil {
			return false
		}
		if (*p.PrivacyRoom) != (*other.PrivacyRoom) {
			return false
		}
	}
	if p.RoomType != other.RoomType {
		if p.RoomType == nil || other.RoomType == nil {
			return false
		}
		if (*p.RoomType) != (*other.RoomType) {
			return false
		}
	}
	return true
}

func (p *RoomInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RoomInfo(%+v)", *p)
}

// Attributes:
//  - RoomId
type GetRoomInfoReq struct {
	RoomId string `thrift:"roomId,1,required" db:"roomId" json:"roomId"`
}

func NewGetRoomInfoReq() *GetRoomInfoReq {
	return &GetRoomInfoReq{}
}

func (p *GetRoomInfoReq) GetRoomId() string {
	return p.RoomId
}
func (p *GetRoomInfoReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRoomId bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRoomId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRoomId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomId is not set"))
	}
	return nil
}

func (p *GetRoomInfoReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.RoomId = v
	}
	return nil
}

func (p *GetRoomInfoReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "GetRoomInfoReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *GetRoomInfoReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomId", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.RoomId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.roomId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomId: ", p), err)
	}
	return err
}

func (p *GetRoomInfoReq) Equals(other *GetRoomInfoReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.RoomId != other.RoomId {
		return false
	}
	return true
}

func (p *GetRoomInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRoomInfoReq(%+v)", *p)
}

// Attributes:
//  - Code
//  - Msg
//  - Data
type GetRoomInfoRsp struct {
	Code int32     `thrift:"code,1,required" db:"code" json:"code"`
	Msg  string    `thrift:"msg,2,required" db:"msg" json:"msg"`
	Data *RoomInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewGetRoomInfoRsp() *GetRoomInfoRsp {
	return &GetRoomInfoRsp{}
}

func (p *GetRoomInfoRsp) GetCode() int32 {
	return p.Code
}

func (p *GetRoomInfoRsp) GetMsg() string {
	return p.Msg
}

var GetRoomInfoRsp_Data_DEFAULT *RoomInfo

func (p *GetRoomInfoRsp) GetData() *RoomInfo {
	if !p.IsSetData() {
		return GetRoomInfoRsp_Data_DEFAULT
	}
	return p.Data
}
func (p *GetRoomInfoRsp) IsSetData() bool {
	return p.Data != nil
}

func (p *GetRoomInfoRsp) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCode bool = false
	var issetMsg bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCode = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCode {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	return nil
}

func (p *GetRoomInfoRsp) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *GetRoomInfoRsp) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *GetRoomInfoRsp) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	p.Data = &RoomInfo{}
	if err := p.Data.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
	}
	return nil
}

func (p *GetRoomInfoRsp) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "GetRoomInfoRsp"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *GetRoomInfoRsp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err)
	}
	return err
}

func (p *GetRoomInfoRsp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *GetRoomInfoRsp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
		}
		if err := p.Data.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
		}
	}
	return err
}

func (p *GetRoomInfoRsp) Equals(other *GetRoomInfoRsp) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Code != other.Code {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if !p.Data.Equals(other.Data) {
		return false
	}
	return true
}

func (p *GetRoomInfoRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRoomInfoRsp(%+v)", *p)
}

// Attributes:
//  - RoomIdList
type BatchGetRoomInfoReq struct {
	RoomIdList []string `thrift:"roomIdList,1,required" db:"roomIdList" json:"roomIdList"`
}

func NewBatchGetRoomInfoReq() *BatchGetRoomInfoReq {
	return &BatchGetRoomInfoReq{}
}

func (p *BatchGetRoomInfoReq) GetRoomIdList() []string {
	return p.RoomIdList
}
func (p *BatchGetRoomInfoReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetRoomIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetRoomIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetRoomIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field RoomIdList is not set"))
	}
	return nil
}

func (p *BatchGetRoomInfoReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.RoomIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem0 = v
		}
		p.RoomIdList = append(p.RoomIdList, _elem0)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchGetRoomInfoReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "roomIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:roomIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.RoomIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.RoomIdList {
		if err := oprot.WriteString(ctx, string(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:roomIdList: ", p), err)
	}
	return err
}

func (p *BatchGetRoomInfoReq) Equals(other *BatchGetRoomInfoReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if len(p.RoomIdList) != len(other.RoomIdList) {
		return false
	}
	for i, _tgt := range p.RoomIdList {
		_src1 := other.RoomIdList[i]
		if _tgt != _src1 {
			return false
		}
	}
	return true
}

func (p *BatchGetRoomInfoReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchGetRoomInfoReq(%+v)", *p)
}

// Attributes:
//  - Code
//  - Msg
//  - Data
type BatchGetRoomInfoRsp struct {
	Code int32                `thrift:"code,1,required" db:"code" json:"code"`
	Msg  string               `thrift:"msg,2,required" db:"msg" json:"msg"`
	Data map[string]*RoomInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewBatchGetRoomInfoRsp() *BatchGetRoomInfoRsp {
	return &BatchGetRoomInfoRsp{}
}

func (p *BatchGetRoomInfoRsp) GetCode() int32 {
	return p.Code
}

func (p *BatchGetRoomInfoRsp) GetMsg() string {
	return p.Msg
}

var BatchGetRoomInfoRsp_Data_DEFAULT map[string]*RoomInfo

func (p *BatchGetRoomInfoRsp) GetData() map[string]*RoomInfo {
	return p.Data
}
func (p *BatchGetRoomInfoRsp) IsSetData() bool {
	return p.Data != nil
}

func (p *BatchGetRoomInfoRsp) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCode bool = false
	var issetMsg bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCode = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCode {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	return nil
}

func (p *BatchGetRoomInfoRsp) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *BatchGetRoomInfoRsp) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *BatchGetRoomInfoRsp) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading map begin: ", err)
	}
	tMap := make(map[string]*RoomInfo, size)
	p.Data = tMap
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_key2 = v
		}
		_val3 := &RoomInfo{}
		if err := _val3.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val3), err)
		}
		p.Data[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(ctx); err != nil {
		return thrift.PrependError("error reading map end: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoRsp) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchGetRoomInfoRsp"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoRsp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err)
	}
	return err
}

func (p *BatchGetRoomInfoRsp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *BatchGetRoomInfoRsp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
		}
		if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRUCT, len(p.Data)); err != nil {
			return thrift.PrependError("error writing map begin: ", err)
		}
		for k, v := range p.Data {
			if err := oprot.WriteString(ctx, string(k)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
			if err := v.Write(ctx, oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteMapEnd(ctx); err != nil {
			return thrift.PrependError("error writing map end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
		}
	}
	return err
}

func (p *BatchGetRoomInfoRsp) Equals(other *BatchGetRoomInfoRsp) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Code != other.Code {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if len(p.Data) != len(other.Data) {
		return false
	}
	for k, _tgt := range p.Data {
		_src4 := other.Data[k]
		if !_tgt.Equals(_src4) {
			return false
		}
	}
	return true
}

func (p *BatchGetRoomInfoRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchGetRoomInfoRsp(%+v)", *p)
}

// Attributes:
//  - UserId
//  - IdType
//  - FromTag
type GetRecommendRoomReq struct {
	UserId  string  `thrift:"userId,1,required" db:"userId" json:"userId"`
	IdType  int32   `thrift:"idType,2,required" db:"idType" json:"idType"`
	FromTag *string `thrift:"fromTag,3" db:"fromTag" json:"fromTag,omitempty"`
}

func NewGetRecommendRoomReq() *GetRecommendRoomReq {
	return &GetRecommendRoomReq{}
}

func (p *GetRecommendRoomReq) GetUserId() string {
	return p.UserId
}

func (p *GetRecommendRoomReq) GetIdType() int32 {
	return p.IdType
}

var GetRecommendRoomReq_FromTag_DEFAULT string

func (p *GetRecommendRoomReq) GetFromTag() string {
	if !p.IsSetFromTag() {
		return GetRecommendRoomReq_FromTag_DEFAULT
	}
	return *p.FromTag
}
func (p *GetRecommendRoomReq) IsSetFromTag() bool {
	return p.FromTag != nil
}

func (p *GetRecommendRoomReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetUserId bool = false
	var issetIdType bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetUserId = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetIdType = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetUserId {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserId is not set"))
	}
	if !issetIdType {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field IdType is not set"))
	}
	return nil
}

func (p *GetRecommendRoomReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *GetRecommendRoomReq) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.IdType = v
	}
	return nil
}

func (p *GetRecommendRoomReq) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 3: ", err)
	} else {
		p.FromTag = &v
	}
	return nil
}

func (p *GetRecommendRoomReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "GetRecommendRoomReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *GetRecommendRoomReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userId", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userId: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.UserId)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.userId (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userId: ", p), err)
	}
	return err
}

func (p *GetRecommendRoomReq) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "idType", thrift.I32, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:idType: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.IdType)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.idType (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:idType: ", p), err)
	}
	return err
}

func (p *GetRecommendRoomReq) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetFromTag() {
		if err := oprot.WriteFieldBegin(ctx, "fromTag", thrift.STRING, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:fromTag: ", p), err)
		}
		if err := oprot.WriteString(ctx, string(*p.FromTag)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T.fromTag (3) field write error: ", p), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:fromTag: ", p), err)
		}
	}
	return err
}

func (p *GetRecommendRoomReq) Equals(other *GetRecommendRoomReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.UserId != other.UserId {
		return false
	}
	if p.IdType != other.IdType {
		return false
	}
	if p.FromTag != other.FromTag {
		if p.FromTag == nil || other.FromTag == nil {
			return false
		}
		if (*p.FromTag) != (*other.FromTag) {
			return false
		}
	}
	return true
}

func (p *GetRecommendRoomReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRecommendRoomReq(%+v)", *p)
}

// Attributes:
//  - Code
//  - Msg
//  - Data
type GetRecommendRoomRsp struct {
	Code int32                    `thrift:"code,1,required" db:"code" json:"code"`
	Msg  string                   `thrift:"msg,2,required" db:"msg" json:"msg"`
	Data *GetRecommendRoomResult_ `thrift:"data,3" db:"data" json:"data,omitempty"`
}

func NewGetRecommendRoomRsp() *GetRecommendRoomRsp {
	return &GetRecommendRoomRsp{}
}

func (p *GetRecommendRoomRsp) GetCode() int32 {
	return p.Code
}

func (p *GetRecommendRoomRsp) GetMsg() string {
	return p.Msg
}

var GetRecommendRoomRsp_Data_DEFAULT *GetRecommendRoomResult_

func (p *GetRecommendRoomRsp) GetData() *GetRecommendRoomResult_ {
	if !p.IsSetData() {
		return GetRecommendRoomRsp_Data_DEFAULT
	}
	return p.Data
}
func (p *GetRecommendRoomRsp) IsSetData() bool {
	return p.Data != nil
}

func (p *GetRecommendRoomRsp) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCode bool = false
	var issetMsg bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCode = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCode {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	return nil
}

func (p *GetRecommendRoomRsp) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *GetRecommendRoomRsp) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *GetRecommendRoomRsp) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	p.Data = &GetRecommendRoomResult_{}
	if err := p.Data.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Data), err)
	}
	return nil
}

func (p *GetRecommendRoomRsp) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "GetRecommendRoomRsp"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *GetRecommendRoomRsp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err)
	}
	return err
}

func (p *GetRecommendRoomRsp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *GetRecommendRoomRsp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRUCT, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
		}
		if err := p.Data.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Data), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
		}
	}
	return err
}

func (p *GetRecommendRoomRsp) Equals(other *GetRecommendRoomRsp) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Code != other.Code {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if !p.Data.Equals(other.Data) {
		return false
	}
	return true
}

func (p *GetRecommendRoomRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRecommendRoomRsp(%+v)", *p)
}

// Attributes:
//  - InfoMap
type GetRecommendRoomResult_ struct {
	InfoMap map[string]*RoomInfo `thrift:"infoMap,1,required" db:"infoMap" json:"infoMap"`
}

func NewGetRecommendRoomResult_() *GetRecommendRoomResult_ {
	return &GetRecommendRoomResult_{}
}

func (p *GetRecommendRoomResult_) GetInfoMap() map[string]*RoomInfo {
	return p.InfoMap
}
func (p *GetRecommendRoomResult_) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetInfoMap bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetInfoMap = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetInfoMap {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field InfoMap is not set"))
	}
	return nil
}

func (p *GetRecommendRoomResult_) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading map begin: ", err)
	}
	tMap := make(map[string]*RoomInfo, size)
	p.InfoMap = tMap
	for i := 0; i < size; i++ {
		var _key5 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_key5 = v
		}
		_val6 := &RoomInfo{}
		if err := _val6.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val6), err)
		}
		p.InfoMap[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(ctx); err != nil {
		return thrift.PrependError("error reading map end: ", err)
	}
	return nil
}

func (p *GetRecommendRoomResult_) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "GetRecommendRoomResult"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *GetRecommendRoomResult_) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "infoMap", thrift.MAP, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:infoMap: ", p), err)
	}
	if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRUCT, len(p.InfoMap)); err != nil {
		return thrift.PrependError("error writing map begin: ", err)
	}
	for k, v := range p.InfoMap {
		if err := oprot.WriteString(ctx, string(k)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
		if err := v.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
		}
	}
	if err := oprot.WriteMapEnd(ctx); err != nil {
		return thrift.PrependError("error writing map end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:infoMap: ", p), err)
	}
	return err
}

func (p *GetRecommendRoomResult_) Equals(other *GetRecommendRoomResult_) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if len(p.InfoMap) != len(other.InfoMap) {
		return false
	}
	for k, _tgt := range p.InfoMap {
		_src7 := other.InfoMap[k]
		if !_tgt.Equals(_src7) {
			return false
		}
	}
	return true
}

func (p *GetRecommendRoomResult_) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRecommendRoomResult_(%+v)", *p)
}

// Attributes:
//  - UserIdList
type BatchGetRoomInfoWithUserIdReq struct {
	UserIdList []string `thrift:"userIdList,1,required" db:"userIdList" json:"userIdList"`
}

func NewBatchGetRoomInfoWithUserIdReq() *BatchGetRoomInfoWithUserIdReq {
	return &BatchGetRoomInfoWithUserIdReq{}
}

func (p *BatchGetRoomInfoWithUserIdReq) GetUserIdList() []string {
	return p.UserIdList
}
func (p *BatchGetRoomInfoWithUserIdReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetUserIdList bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetUserIdList = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetUserIdList {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field UserIdList is not set"))
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdReq) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading list begin: ", err)
	}
	tSlice := make([]string, 0, size)
	p.UserIdList = tSlice
	for i := 0; i < size; i++ {
		var _elem8 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_elem8 = v
		}
		p.UserIdList = append(p.UserIdList, _elem8)
	}
	if err := iprot.ReadListEnd(ctx); err != nil {
		return thrift.PrependError("error reading list end: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchGetRoomInfoWithUserIdReq"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "userIdList", thrift.LIST, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:userIdList: ", p), err)
	}
	if err := oprot.WriteListBegin(ctx, thrift.STRING, len(p.UserIdList)); err != nil {
		return thrift.PrependError("error writing list begin: ", err)
	}
	for _, v := range p.UserIdList {
		if err := oprot.WriteString(ctx, string(v)); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
		}
	}
	if err := oprot.WriteListEnd(ctx); err != nil {
		return thrift.PrependError("error writing list end: ", err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:userIdList: ", p), err)
	}
	return err
}

func (p *BatchGetRoomInfoWithUserIdReq) Equals(other *BatchGetRoomInfoWithUserIdReq) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if len(p.UserIdList) != len(other.UserIdList) {
		return false
	}
	for i, _tgt := range p.UserIdList {
		_src9 := other.UserIdList[i]
		if _tgt != _src9 {
			return false
		}
	}
	return true
}

func (p *BatchGetRoomInfoWithUserIdReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchGetRoomInfoWithUserIdReq(%+v)", *p)
}

// Attributes:
//  - Code
//  - Msg
//  - Data
//  - Data1
type BatchGetRoomInfoWithUserIdRsp struct {
	Code  int32                `thrift:"code,1,required" db:"code" json:"code"`
	Msg   string               `thrift:"msg,2,required" db:"msg" json:"msg"`
	Data  map[string]*RoomInfo `thrift:"data,3" db:"data" json:"data,omitempty"`
	Data1 map[string]*RoomInfo `thrift:"data1,4" db:"data1" json:"data1,omitempty"`
}

func NewBatchGetRoomInfoWithUserIdRsp() *BatchGetRoomInfoWithUserIdRsp {
	return &BatchGetRoomInfoWithUserIdRsp{}
}

func (p *BatchGetRoomInfoWithUserIdRsp) GetCode() int32 {
	return p.Code
}

func (p *BatchGetRoomInfoWithUserIdRsp) GetMsg() string {
	return p.Msg
}

var BatchGetRoomInfoWithUserIdRsp_Data_DEFAULT map[string]*RoomInfo

func (p *BatchGetRoomInfoWithUserIdRsp) GetData() map[string]*RoomInfo {
	return p.Data
}

var BatchGetRoomInfoWithUserIdRsp_Data1_DEFAULT map[string]*RoomInfo

func (p *BatchGetRoomInfoWithUserIdRsp) GetData1() map[string]*RoomInfo {
	return p.Data1
}
func (p *BatchGetRoomInfoWithUserIdRsp) IsSetData() bool {
	return p.Data != nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) IsSetData1() bool {
	return p.Data1 != nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	var issetCode bool = false
	var issetMsg bool = false

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
				issetCode = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
				issetMsg = true
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.ReadField3(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.ReadField4(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	if !issetCode {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Code is not set"))
	}
	if !issetMsg {
		return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Msg is not set"))
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 2: ", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading map begin: ", err)
	}
	tMap := make(map[string]*RoomInfo, size)
	p.Data = tMap
	for i := 0; i < size; i++ {
		var _key10 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_key10 = v
		}
		_val11 := &RoomInfo{}
		if err := _val11.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val11), err)
		}
		p.Data[_key10] = _val11
	}
	if err := iprot.ReadMapEnd(ctx); err != nil {
		return thrift.PrependError("error reading map end: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin(ctx)
	if err != nil {
		return thrift.PrependError("error reading map begin: ", err)
	}
	tMap := make(map[string]*RoomInfo, size)
	p.Data1 = tMap
	for i := 0; i < size; i++ {
		var _key12 string
		if v, err := iprot.ReadString(ctx); err != nil {
			return thrift.PrependError("error reading field 0: ", err)
		} else {
			_key12 = v
		}
		_val13 := &RoomInfo{}
		if err := _val13.Read(ctx, iprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _val13), err)
		}
		p.Data1[_key12] = _val13
	}
	if err := iprot.ReadMapEnd(ctx); err != nil {
		return thrift.PrependError("error reading map end: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "BatchGetRoomInfoWithUserIdRsp"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField3(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField4(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *BatchGetRoomInfoWithUserIdRsp) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err)
	}
	if err := oprot.WriteI32(ctx, int32(p.Code)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err)
	}
	return err
}

func (p *BatchGetRoomInfoWithUserIdRsp) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "msg", thrift.STRING, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:msg: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.Msg)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.msg (2) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:msg: ", p), err)
	}
	return err
}

func (p *BatchGetRoomInfoWithUserIdRsp) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetData() {
		if err := oprot.WriteFieldBegin(ctx, "data", thrift.MAP, 3); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:data: ", p), err)
		}
		if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRUCT, len(p.Data)); err != nil {
			return thrift.PrependError("error writing map begin: ", err)
		}
		for k, v := range p.Data {
			if err := oprot.WriteString(ctx, string(k)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
			if err := v.Write(ctx, oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteMapEnd(ctx); err != nil {
			return thrift.PrependError("error writing map end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 3:data: ", p), err)
		}
	}
	return err
}

func (p *BatchGetRoomInfoWithUserIdRsp) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetData1() {
		if err := oprot.WriteFieldBegin(ctx, "data1", thrift.MAP, 4); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:data1: ", p), err)
		}
		if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRUCT, len(p.Data1)); err != nil {
			return thrift.PrependError("error writing map begin: ", err)
		}
		for k, v := range p.Data1 {
			if err := oprot.WriteString(ctx, string(k)); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err)
			}
			if err := v.Write(ctx, oprot); err != nil {
				return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
			}
		}
		if err := oprot.WriteMapEnd(ctx); err != nil {
			return thrift.PrependError("error writing map end: ", err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 4:data1: ", p), err)
		}
	}
	return err
}

func (p *BatchGetRoomInfoWithUserIdRsp) Equals(other *BatchGetRoomInfoWithUserIdRsp) bool {
	if p == other {
		return true
	} else if p == nil || other == nil {
		return false
	}
	if p.Code != other.Code {
		return false
	}
	if p.Msg != other.Msg {
		return false
	}
	if len(p.Data) != len(other.Data) {
		return false
	}
	for k, _tgt := range p.Data {
		_src14 := other.Data[k]
		if !_tgt.Equals(_src14) {
			return false
		}
	}
	if len(p.Data1) != len(other.Data1) {
		return false
	}
	for k, _tgt := range p.Data1 {
		_src15 := other.Data1[k]
		if !_tgt.Equals(_src15) {
			return false
		}
	}
	return true
}

func (p *BatchGetRoomInfoWithUserIdRsp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchGetRoomInfoWithUserIdRsp(%+v)", *p)
}

type KugouAdapterRoomService interface {
	// Parameters:
	//  - AppName
	//  - Request
	GetRoomInfo(ctx context.Context, appName string, request *GetRoomInfoReq) (_r *GetRoomInfoRsp, _err error)
	// Parameters:
	//  - AppName
	//  - Request
	BatchGetRoomInfo(ctx context.Context, appName string, request *BatchGetRoomInfoReq) (_r *BatchGetRoomInfoRsp, _err error)
	// Parameters:
	//  - AppName
	//  - Request
	GetRecommendRoom(ctx context.Context, appName string, request *GetRecommendRoomReq) (_r *GetRecommendRoomRsp, _err error)
	// Parameters:
	//  - AppName
	//  - Request
	BatchGetRoomInfoWithUserId(ctx context.Context, appName string, request *BatchGetRoomInfoWithUserIdReq) (_r *BatchGetRoomInfoWithUserIdRsp, _err error)
}

type KugouAdapterRoomServiceClient struct {
	c    thrift.TClient
	meta thrift.ResponseMeta
}

func NewKugouAdapterRoomServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *KugouAdapterRoomServiceClient {
	return &KugouAdapterRoomServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewKugouAdapterRoomServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *KugouAdapterRoomServiceClient {
	return &KugouAdapterRoomServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewKugouAdapterRoomServiceClient(c thrift.TClient) *KugouAdapterRoomServiceClient {
	return &KugouAdapterRoomServiceClient{
		c: c,
	}
}

func (p *KugouAdapterRoomServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *KugouAdapterRoomServiceClient) LastResponseMeta_() thrift.ResponseMeta {
	return p.meta
}

func (p *KugouAdapterRoomServiceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
	p.meta = meta
}

// Parameters:
//  - AppName
//  - Request
func (p *KugouAdapterRoomServiceClient) GetRoomInfo(ctx context.Context, appName string, request *GetRoomInfoReq) (_r *GetRoomInfoRsp, _err error) {
	var _args16 KugouAdapterRoomServiceGetRoomInfoArgs
	_args16.AppName = appName
	_args16.Request = request
	var _result18 KugouAdapterRoomServiceGetRoomInfoResult
	var _meta17 thrift.ResponseMeta
	_meta17, _err = p.Client_().Call(ctx, "getRoomInfo", &_args16, &_result18)
	p.SetLastResponseMeta_(_meta17)
	if _err != nil {
		return
	}
	if _ret19 := _result18.GetSuccess(); _ret19 != nil {
		return _ret19, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getRoomInfo failed: unknown result")
}

// Parameters:
//  - AppName
//  - Request
func (p *KugouAdapterRoomServiceClient) BatchGetRoomInfo(ctx context.Context, appName string, request *BatchGetRoomInfoReq) (_r *BatchGetRoomInfoRsp, _err error) {
	var _args20 KugouAdapterRoomServiceBatchGetRoomInfoArgs
	_args20.AppName = appName
	_args20.Request = request
	var _result22 KugouAdapterRoomServiceBatchGetRoomInfoResult
	var _meta21 thrift.ResponseMeta
	_meta21, _err = p.Client_().Call(ctx, "batchGetRoomInfo", &_args20, &_result22)
	p.SetLastResponseMeta_(_meta21)
	if _err != nil {
		return
	}
	if _ret23 := _result22.GetSuccess(); _ret23 != nil {
		return _ret23, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "batchGetRoomInfo failed: unknown result")
}

// Parameters:
//  - AppName
//  - Request
func (p *KugouAdapterRoomServiceClient) GetRecommendRoom(ctx context.Context, appName string, request *GetRecommendRoomReq) (_r *GetRecommendRoomRsp, _err error) {
	var _args24 KugouAdapterRoomServiceGetRecommendRoomArgs
	_args24.AppName = appName
	_args24.Request = request
	var _result26 KugouAdapterRoomServiceGetRecommendRoomResult
	var _meta25 thrift.ResponseMeta
	_meta25, _err = p.Client_().Call(ctx, "getRecommendRoom", &_args24, &_result26)
	p.SetLastResponseMeta_(_meta25)
	if _err != nil {
		return
	}
	if _ret27 := _result26.GetSuccess(); _ret27 != nil {
		return _ret27, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "getRecommendRoom failed: unknown result")
}

// Parameters:
//  - AppName
//  - Request
func (p *KugouAdapterRoomServiceClient) BatchGetRoomInfoWithUserId(ctx context.Context, appName string, request *BatchGetRoomInfoWithUserIdReq) (_r *BatchGetRoomInfoWithUserIdRsp, _err error) {
	var _args28 KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs
	_args28.AppName = appName
	_args28.Request = request
	var _result30 KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult
	var _meta29 thrift.ResponseMeta
	_meta29, _err = p.Client_().Call(ctx, "batchGetRoomInfoWithUserId", &_args28, &_result30)
	p.SetLastResponseMeta_(_meta29)
	if _err != nil {
		return
	}
	if _ret31 := _result30.GetSuccess(); _ret31 != nil {
		return _ret31, nil
	}
	return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "batchGetRoomInfoWithUserId failed: unknown result")
}

type KugouAdapterRoomServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      KugouAdapterRoomService
}

func (p *KugouAdapterRoomServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *KugouAdapterRoomServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *KugouAdapterRoomServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewKugouAdapterRoomServiceProcessor(handler KugouAdapterRoomService) *KugouAdapterRoomServiceProcessor {

	self32 := &KugouAdapterRoomServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self32.processorMap["getRoomInfo"] = &kugouAdapterRoomServiceProcessorGetRoomInfo{handler: handler}
	self32.processorMap["batchGetRoomInfo"] = &kugouAdapterRoomServiceProcessorBatchGetRoomInfo{handler: handler}
	self32.processorMap["getRecommendRoom"] = &kugouAdapterRoomServiceProcessorGetRecommendRoom{handler: handler}
	self32.processorMap["batchGetRoomInfoWithUserId"] = &kugouAdapterRoomServiceProcessorBatchGetRoomInfoWithUserId{handler: handler}
	return self32
}

func (p *KugouAdapterRoomServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
	if err2 != nil {
		return false, thrift.WrapTException(err2)
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(ctx, thrift.STRUCT)
	iprot.ReadMessageEnd(ctx)
	x33 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
	x33.Write(ctx, oprot)
	oprot.WriteMessageEnd(ctx)
	oprot.Flush(ctx)
	return false, x33

}

type kugouAdapterRoomServiceProcessorGetRoomInfo struct {
	handler KugouAdapterRoomService
}

func (p *kugouAdapterRoomServiceProcessorGetRoomInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err34 error
	args := KugouAdapterRoomServiceGetRoomInfoArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getRoomInfo", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KugouAdapterRoomServiceGetRoomInfoResult{}
	if retval, err2 := p.handler.GetRoomInfo(ctx, args.AppName, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc35 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRoomInfo: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getRoomInfo", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if err2 := _exc35.Write(ctx, oprot); _write_err34 == nil && err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err34 == nil && err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err34 == nil && err2 != nil {
			_write_err34 = thrift.WrapTException(err2)
		}
		if _write_err34 != nil {
			return false, thrift.WrapTException(_write_err34)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getRoomInfo", thrift.REPLY, seqId); err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err34 == nil && err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err34 == nil && err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err34 == nil && err2 != nil {
		_write_err34 = thrift.WrapTException(err2)
	}
	if _write_err34 != nil {
		return false, thrift.WrapTException(_write_err34)
	}
	return true, err
}

type kugouAdapterRoomServiceProcessorBatchGetRoomInfo struct {
	handler KugouAdapterRoomService
}

func (p *kugouAdapterRoomServiceProcessorBatchGetRoomInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err36 error
	args := KugouAdapterRoomServiceBatchGetRoomInfoArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "batchGetRoomInfo", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KugouAdapterRoomServiceBatchGetRoomInfoResult{}
	if retval, err2 := p.handler.BatchGetRoomInfo(ctx, args.AppName, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc37 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchGetRoomInfo: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "batchGetRoomInfo", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if err2 := _exc37.Write(ctx, oprot); _write_err36 == nil && err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err36 == nil && err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err36 == nil && err2 != nil {
			_write_err36 = thrift.WrapTException(err2)
		}
		if _write_err36 != nil {
			return false, thrift.WrapTException(_write_err36)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "batchGetRoomInfo", thrift.REPLY, seqId); err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err36 == nil && err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err36 == nil && err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err36 == nil && err2 != nil {
		_write_err36 = thrift.WrapTException(err2)
	}
	if _write_err36 != nil {
		return false, thrift.WrapTException(_write_err36)
	}
	return true, err
}

type kugouAdapterRoomServiceProcessorGetRecommendRoom struct {
	handler KugouAdapterRoomService
}

func (p *kugouAdapterRoomServiceProcessorGetRecommendRoom) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err38 error
	args := KugouAdapterRoomServiceGetRecommendRoomArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "getRecommendRoom", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KugouAdapterRoomServiceGetRecommendRoomResult{}
	if retval, err2 := p.handler.GetRecommendRoom(ctx, args.AppName, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc39 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRecommendRoom: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "getRecommendRoom", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err38 = thrift.WrapTException(err2)
		}
		if err2 := _exc39.Write(ctx, oprot); _write_err38 == nil && err2 != nil {
			_write_err38 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err38 == nil && err2 != nil {
			_write_err38 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err38 == nil && err2 != nil {
			_write_err38 = thrift.WrapTException(err2)
		}
		if _write_err38 != nil {
			return false, thrift.WrapTException(_write_err38)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "getRecommendRoom", thrift.REPLY, seqId); err2 != nil {
		_write_err38 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err38 == nil && err2 != nil {
		_write_err38 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err38 == nil && err2 != nil {
		_write_err38 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err38 == nil && err2 != nil {
		_write_err38 = thrift.WrapTException(err2)
	}
	if _write_err38 != nil {
		return false, thrift.WrapTException(_write_err38)
	}
	return true, err
}

type kugouAdapterRoomServiceProcessorBatchGetRoomInfoWithUserId struct {
	handler KugouAdapterRoomService
}

func (p *kugouAdapterRoomServiceProcessorBatchGetRoomInfoWithUserId) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	var _write_err40 error
	args := KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs{}
	if err2 := args.Read(ctx, iprot); err2 != nil {
		iprot.ReadMessageEnd(ctx)
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
		oprot.WriteMessageBegin(ctx, "batchGetRoomInfoWithUserId", thrift.EXCEPTION, seqId)
		x.Write(ctx, oprot)
		oprot.WriteMessageEnd(ctx)
		oprot.Flush(ctx)
		return false, thrift.WrapTException(err2)
	}
	iprot.ReadMessageEnd(ctx)

	tickerCancel := func() {}
	// Start a goroutine to do server side connectivity check.
	if thrift.ServerConnectivityCheckInterval > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
		var tickerCtx context.Context
		tickerCtx, tickerCancel = context.WithCancel(context.Background())
		defer tickerCancel()
		go func(ctx context.Context, cancel context.CancelFunc) {
			ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ctx.Done():
					return
				case <-ticker.C:
					if !iprot.Transport().IsOpen() {
						cancel()
						return
					}
				}
			}
		}(tickerCtx, cancel)
	}

	result := KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult{}
	if retval, err2 := p.handler.BatchGetRoomInfoWithUserId(ctx, args.AppName, args.Request); err2 != nil {
		tickerCancel()
		err = thrift.WrapTException(err2)
		if errors.Is(err2, thrift.ErrAbandonRequest) {
			return false, thrift.WrapTException(err2)
		}
		_exc41 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchGetRoomInfoWithUserId: "+err2.Error())
		if err2 := oprot.WriteMessageBegin(ctx, "batchGetRoomInfoWithUserId", thrift.EXCEPTION, seqId); err2 != nil {
			_write_err40 = thrift.WrapTException(err2)
		}
		if err2 := _exc41.Write(ctx, oprot); _write_err40 == nil && err2 != nil {
			_write_err40 = thrift.WrapTException(err2)
		}
		if err2 := oprot.WriteMessageEnd(ctx); _write_err40 == nil && err2 != nil {
			_write_err40 = thrift.WrapTException(err2)
		}
		if err2 := oprot.Flush(ctx); _write_err40 == nil && err2 != nil {
			_write_err40 = thrift.WrapTException(err2)
		}
		if _write_err40 != nil {
			return false, thrift.WrapTException(_write_err40)
		}
		return true, err
	} else {
		result.Success = retval
	}
	tickerCancel()
	if err2 := oprot.WriteMessageBegin(ctx, "batchGetRoomInfoWithUserId", thrift.REPLY, seqId); err2 != nil {
		_write_err40 = thrift.WrapTException(err2)
	}
	if err2 := result.Write(ctx, oprot); _write_err40 == nil && err2 != nil {
		_write_err40 = thrift.WrapTException(err2)
	}
	if err2 := oprot.WriteMessageEnd(ctx); _write_err40 == nil && err2 != nil {
		_write_err40 = thrift.WrapTException(err2)
	}
	if err2 := oprot.Flush(ctx); _write_err40 == nil && err2 != nil {
		_write_err40 = thrift.WrapTException(err2)
	}
	if _write_err40 != nil {
		return false, thrift.WrapTException(_write_err40)
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - AppName
//  - Request
type KugouAdapterRoomServiceGetRoomInfoArgs struct {
	AppName string          `thrift:"appName,1" db:"appName" json:"appName"`
	Request *GetRoomInfoReq `thrift:"request,2" db:"request" json:"request"`
}

func NewKugouAdapterRoomServiceGetRoomInfoArgs() *KugouAdapterRoomServiceGetRoomInfoArgs {
	return &KugouAdapterRoomServiceGetRoomInfoArgs{}
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) GetAppName() string {
	return p.AppName
}

var KugouAdapterRoomServiceGetRoomInfoArgs_Request_DEFAULT *GetRoomInfoReq

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) GetRequest() *GetRoomInfoReq {
	if !p.IsSetRequest() {
		return KugouAdapterRoomServiceGetRoomInfoArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *KugouAdapterRoomServiceGetRoomInfoArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &GetRoomInfoReq{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getRoomInfo_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AppName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appName: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:request: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceGetRoomInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceGetRoomInfoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KugouAdapterRoomServiceGetRoomInfoResult struct {
	Success *GetRoomInfoRsp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKugouAdapterRoomServiceGetRoomInfoResult() *KugouAdapterRoomServiceGetRoomInfoResult {
	return &KugouAdapterRoomServiceGetRoomInfoResult{}
}

var KugouAdapterRoomServiceGetRoomInfoResult_Success_DEFAULT *GetRoomInfoRsp

func (p *KugouAdapterRoomServiceGetRoomInfoResult) GetSuccess() *GetRoomInfoRsp {
	if !p.IsSetSuccess() {
		return KugouAdapterRoomServiceGetRoomInfoResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KugouAdapterRoomServiceGetRoomInfoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &GetRoomInfoRsp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getRoomInfo_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRoomInfoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KugouAdapterRoomServiceGetRoomInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceGetRoomInfoResult(%+v)", *p)
}

// Attributes:
//  - AppName
//  - Request
type KugouAdapterRoomServiceBatchGetRoomInfoArgs struct {
	AppName string               `thrift:"appName,1" db:"appName" json:"appName"`
	Request *BatchGetRoomInfoReq `thrift:"request,2" db:"request" json:"request"`
}

func NewKugouAdapterRoomServiceBatchGetRoomInfoArgs() *KugouAdapterRoomServiceBatchGetRoomInfoArgs {
	return &KugouAdapterRoomServiceBatchGetRoomInfoArgs{}
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) GetAppName() string {
	return p.AppName
}

var KugouAdapterRoomServiceBatchGetRoomInfoArgs_Request_DEFAULT *BatchGetRoomInfoReq

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) GetRequest() *BatchGetRoomInfoReq {
	if !p.IsSetRequest() {
		return KugouAdapterRoomServiceBatchGetRoomInfoArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &BatchGetRoomInfoReq{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchGetRoomInfo_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AppName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appName: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:request: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceBatchGetRoomInfoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KugouAdapterRoomServiceBatchGetRoomInfoResult struct {
	Success *BatchGetRoomInfoRsp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKugouAdapterRoomServiceBatchGetRoomInfoResult() *KugouAdapterRoomServiceBatchGetRoomInfoResult {
	return &KugouAdapterRoomServiceBatchGetRoomInfoResult{}
}

var KugouAdapterRoomServiceBatchGetRoomInfoResult_Success_DEFAULT *BatchGetRoomInfoRsp

func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) GetSuccess() *BatchGetRoomInfoRsp {
	if !p.IsSetSuccess() {
		return KugouAdapterRoomServiceBatchGetRoomInfoResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &BatchGetRoomInfoRsp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchGetRoomInfo_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceBatchGetRoomInfoResult(%+v)", *p)
}

// Attributes:
//  - AppName
//  - Request
type KugouAdapterRoomServiceGetRecommendRoomArgs struct {
	AppName string               `thrift:"appName,1" db:"appName" json:"appName"`
	Request *GetRecommendRoomReq `thrift:"request,2" db:"request" json:"request"`
}

func NewKugouAdapterRoomServiceGetRecommendRoomArgs() *KugouAdapterRoomServiceGetRecommendRoomArgs {
	return &KugouAdapterRoomServiceGetRecommendRoomArgs{}
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) GetAppName() string {
	return p.AppName
}

var KugouAdapterRoomServiceGetRecommendRoomArgs_Request_DEFAULT *GetRecommendRoomReq

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) GetRequest() *GetRecommendRoomReq {
	if !p.IsSetRequest() {
		return KugouAdapterRoomServiceGetRecommendRoomArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &GetRecommendRoomReq{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getRecommendRoom_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AppName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appName: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:request: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceGetRecommendRoomArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceGetRecommendRoomArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KugouAdapterRoomServiceGetRecommendRoomResult struct {
	Success *GetRecommendRoomRsp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKugouAdapterRoomServiceGetRecommendRoomResult() *KugouAdapterRoomServiceGetRecommendRoomResult {
	return &KugouAdapterRoomServiceGetRecommendRoomResult{}
}

var KugouAdapterRoomServiceGetRecommendRoomResult_Success_DEFAULT *GetRecommendRoomRsp

func (p *KugouAdapterRoomServiceGetRecommendRoomResult) GetSuccess() *GetRecommendRoomRsp {
	if !p.IsSetSuccess() {
		return KugouAdapterRoomServiceGetRecommendRoomResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KugouAdapterRoomServiceGetRecommendRoomResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &GetRecommendRoomRsp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "getRecommendRoom_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceGetRecommendRoomResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KugouAdapterRoomServiceGetRecommendRoomResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceGetRecommendRoomResult(%+v)", *p)
}

// Attributes:
//  - AppName
//  - Request
type KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs struct {
	AppName string                         `thrift:"appName,1" db:"appName" json:"appName"`
	Request *BatchGetRoomInfoWithUserIdReq `thrift:"request,2" db:"request" json:"request"`
}

func NewKugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs() *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs {
	return &KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs{}
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) GetAppName() string {
	return p.AppName
}

var KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs_Request_DEFAULT *BatchGetRoomInfoWithUserIdReq

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) GetRequest() *BatchGetRoomInfoWithUserIdReq {
	if !p.IsSetRequest() {
		return KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs_Request_DEFAULT
	}
	return p.Request
}
func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.ReadField1(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField2(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(ctx); err != nil {
		return thrift.PrependError("error reading field 1: ", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
	p.Request = &BatchGetRoomInfoWithUserIdReq{}
	if err := p.Request.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Request), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchGetRoomInfoWithUserId_args"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField1(ctx, oprot); err != nil {
			return err
		}
		if err := p.writeField2(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "appName", thrift.STRING, 1); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:appName: ", p), err)
	}
	if err := oprot.WriteString(ctx, string(p.AppName)); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T.appName (1) field write error: ", p), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 1:appName: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin(ctx, "request", thrift.STRUCT, 2); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:request: ", p), err)
	}
	if err := p.Request.Write(ctx, oprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Request), err)
	}
	if err := oprot.WriteFieldEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write field end error 2:request: ", p), err)
	}
	return err
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdArgs(%+v)", *p)
}

// Attributes:
//  - Success
type KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult struct {
	Success *BatchGetRoomInfoWithUserIdRsp `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewKugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult() *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult {
	return &KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult{}
}

var KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult_Success_DEFAULT *BatchGetRoomInfoWithUserIdRsp

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) GetSuccess() *BatchGetRoomInfoWithUserIdRsp {
	if !p.IsSetSuccess() {
		return KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult_Success_DEFAULT
	}
	return p.Success
}
func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
	}

	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
		if err != nil {
			return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.ReadField0(ctx, iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(ctx, fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(ctx, fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(ctx); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(ctx); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
	p.Success = &BatchGetRoomInfoWithUserIdRsp{}
	if err := p.Success.Read(ctx, iprot); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin(ctx, "batchGetRoomInfoWithUserId_result"); err != nil {
		return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
	}
	if p != nil {
		if err := p.writeField0(ctx, oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(ctx); err != nil {
		return thrift.PrependError("write field stop error: ", err)
	}
	if err := oprot.WriteStructEnd(ctx); err != nil {
		return thrift.PrependError("write struct stop error: ", err)
	}
	return nil
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err)
		}
		if err := p.Success.Write(ctx, oprot); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
		}
		if err := oprot.WriteFieldEnd(ctx); err != nil {
			return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err)
		}
	}
	return err
}

func (p *KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KugouAdapterRoomServiceBatchGetRoomInfoWithUserIdResult(%+v)", *p)
}
