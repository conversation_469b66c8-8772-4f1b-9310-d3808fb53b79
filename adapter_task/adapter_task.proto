syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_task";

import "pb/adapter_task/comm.proto";

// 登录态数据,直接按照以下field从cookie中获取出来即可
message CookieFileds {
  string strOpenType = 1; // cookie["opentype"]
  string strOpenKey = 2; // cookie["openkey"]
  string strOpenId = 3; // cookie["openid"], 这里非中台openid,如微信openid
}

// QueryReq 查询任务
message QueryReq {
  string appId = 1;
  string openId = 2;
  int32 sceneId = 3; // 场景id
  int32 moduleId = 4; // 模块id
  string qua = 5; // qua参数
  string filter = 6; // 自定义filter过滤
  string ua = 7; // ua参数
  CookieFileds cookies = 8;
}

// QueryRsp 查询任务
message QueryRsp {
  TaskModule module = 1; // 模块
}

// ClaimReq 领取任务
message ClaimReq {
  string appId = 1;
  string openId = 2;
  int32 taskId = 3; // 任务ID
  string qua = 4; // 设备信息
  string ua = 5; // ua参数
  CookieFileds cookies = 6;
}

// ClaimRsp 领取任务
message ClaimRsp {
  string strMsg = 1;
}

// CompleteReq 完成任务,领取奖励
message CompleteReq {
  string appId = 1;
  string openId = 2;
  int32 taskId = 3; // 任务ID
  string qua = 4;
  int32 rewardRate = 5; // 领取倍率
  string rewardToken = 6; // 校验token
  string ua = 7; // userAgent
  CookieFileds cookies = 8;
  bool normalTask = 9;
  map<string,string> mapBusiPassback = 10; // 业务透传参数-仅q音
}

// CompleteRsp 完成任务,领取奖励
message CompleteRsp {
  string strMsg = 1;
  repeated Reward rewards = 2;
}

// GiveupReq 放弃任务
message GiveupReq {
  string appId = 1;
  string openId = 2;
  int32 taskId = 3; // 任务ID
  string qua = 4;
  string ua = 5; // ua参数
  CookieFileds cookies = 6;
}

// GiveupRsp 放弃任务
message GiveupRsp {
  string strMsg = 1;
}

message CompleteCheckReq {
  string appId = 1;
  int64 uid = 2;
  int32 taskId = 3; // 任务id
  string rewardToken = 4; // token
  int32 rewardRate = 5; // 领奖倍数
}

message CompleteCheckRsp {
  int32 code = 1; // 校验成功返回0,否则返回其他
  string msg = 2;
}

message CompleteNotifyReq {
  string appId = 1;
  int64 uid = 2;
  int32 taskId = 3; // 任务id
  string rewardToken = 4; // token
  int32 rewardRate = 5; // 领奖倍数
  map<string, string> ext = 6; // 透传参数
  string consumeId = 7; // 幂等ID
}

message CompleteNotifyRsp {
  int32 code = 1; // 校验成功返回0,否则返回其他
  string msg = 2;
}

message QueryStatusReq {
  string appId = 1;
  string openId = 2;
  int32 taskId = 3;
}

message QueryStatusRsp {
  Task task = 1;
}

service AdapterTask {
  rpc QueryStatus(QueryStatusReq) returns (QueryStatusRsp); // 查询单个任务状态
  rpc Query(QueryReq) returns (QueryRsp); // 查询任务
  rpc Claim(ClaimReq) returns (ClaimRsp); // 领取任务
  rpc Complete(CompleteReq) returns (CompleteRsp); // 完成任务,领取奖励
  rpc Giveup(GiveupReq) returns (GiveupRsp); // 放弃任务
  rpc CompleteVerify(CompleteCheckReq) returns (CompleteCheckRsp); // 发奖校验回调
  rpc CompleteNotify(CompleteNotifyReq) returns (CompleteNotifyRsp); // 自定义发奖回调
  rpc QueryModule(QueryModuleReq) returns (QueryModuleRsp); // 查询模块
  rpc GrantBundle(GrantBundleReq) returns (GrantBundleRsp); // 发奖励
}

// 关于领奖:
// 领奖方式在任务中台配置任务时决定, 选择是否走自定义发奖
// 自定义发奖方式需要游戏侧自己维护映射 taskId -> 奖励信息

// 领奖方式1: (由任务中台实现发奖):
//  1.(游戏后台) 领奖调用 kg.game.AdapterTask/Complete, 透传倍数和token
//  2.(游戏中台) 透传领奖参数
//  3.(任务中台) 回调领奖验证, 游戏后台实现token验证接口, 入参/出参:CompleteCheckCallbackReq/CompleteCheckCallbackRsp
//  4.(任务中台) 任务中台根据配置执行奖励发放
// 配置方式:
//  1. (任务中台) 回调配置:配置回调验证接口, kg.tme_game.tme_game_task_proxy.complete_verify
//  2. (任务中台) 奖励配置:正常配置奖励
//  3. (红石配置) 配置游戏后台回调接口 http://gm.tmeoa.com/?type=game_task_config_test

// 领奖方式2: (由游戏侧实现自定义发奖):
//  1.(游戏后台) 领奖 kg.game.AdapterTask/Complete, 透传倍数和token
//  2.(任务中台) 自定义发奖回调, 回
//  3.(游戏后台) 游戏后台实现领奖通知接口, 入参/出参:CompleteNotifyCallbackReq/CompleteNotifyCallbackReq
//  4.(游戏后台) 游戏后台自定义调用奖品包/游戏资产发放
// 配置方式:
//  1.(任务中台) 配置领奖成功回调kg.tme_game.tme_game_task_proxy.complete_notify
//  2.(任务中台) 自定义奖励发放填否,福利ID填0
//  3.(红石配置) 配置游戏后台回调接口 http://gm.tmeoa.com/?type=game_task_config_test

message CompleteCheckCallbackReq {
  string openId = 1;
  int32 taskId = 2; // 任务id
  string rewardToken = 3; // token
  int32 rewardRate = 4; // 领奖倍数
}

message CompleteCheckCallbackRsp {
  int32 code = 1; // 校验成功返回0,否则返回其他
  string msg = 2;
}

message CompleteNotifyCallbackReq {
  string openId = 1;
  int32 taskId = 2; // 任务id
  string rewardToken = 3; // token
  int32 rewardRate = 4; // 领奖倍数
  map<string, string> ext = 5; // 透传参数
  string consumeId = 6; // 幂等ID
}

message CompleteNotifyCallbackRsp {
  int32 code = 1; // 校验成功返回0,否则返回其他
  string msg = 2;
}

message QueryModuleReq {
  string appId = 1;
  string openId = 2;
  int32 sceneId = 3; // 场景id
  string qua = 4; // qua参数
  string ua = 5; // ua参数
  CookieFileds cookies = 6;
}

message QueryModuleRsp {
  repeated TaskModule modules = 1;
}

message GrantBundleReq {
  string appId = 1;
  int64 uid = 2;
  int32 taskId = 3;
  int64 bundleId = 4;
  int64 bundleNum = 5;
  string transactionId = 6;
  string reportReason = 7;
  bool isPaid = 8;
}

message GrantBundleRsp {
  int32 code = 1; // 校验成功返回0,否则返回其他
  string msg = 2;
}
