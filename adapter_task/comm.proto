syntax = "proto3";

package game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_task";

// TaskType 任务类型
enum TaskType {
  Normal = 0; // 普通任务
  Custom = 1; // 自定义任务
  Diversion = 2; // 导流任务
  Step = 3; // 阶梯任务
  Sign = 4; // 签到任务
}

// TaskState 任务状态
enum TaskState {
  TaskStateDefault = 0; // 未完成, 未领取
  TaskStateAvailable = 1; // 已完成, 可领取奖励, 未领取奖励
  TaskStateCompelete = 2; // 已完成, 可领取奖励, 已领取奖励
  TaskStateHidden = 3; // 隐藏
  TaskStateFinish = 4; // 已完成,不可领取奖励
  TaskStateOver = 5; // 已结束（eg：非循环签到任务已结束，仅可展示）
}

// TaskClaimState 任务领取状态
enum TaskClaimState {
  TaskClaimStateDefault = 0; // 未领取
  TaskClaimStateClaimed = 1; // 已领取
  TaskClaimStateGiveup = 2; // 已放弃
  TaskClaimStateOver = 3; // 不可再接受任务（任务失败）
}

enum TaskProgressNodeStatus {
  TaskProgressNodeStatusTodo = 0;
  TaskProgressNodeStatusCanRecv = 1;
  TaskProgressNodeStatusRecved = 2;
}

message NormalProperty {
  string title = 1; // 标题
  string desc = 2; // 子标题
  string tag = 3; // tag标签
  string tagColor = 4; // tag文案颜色
  string button = 5; // 按钮文案
  string buttonUrl = 6; // 跳转连接
  string icon = 7; // 图标
  RewardProgress rewardProgress = 10;
  uint32 achievedTimes = 11; // 达成次数
  uint32 totalTimes = 12; // 总次数
}

message RewardProgressDetail {
  string icon = 1;
  string desc = 2;
}

message RewardProgressNode {
  uint32 threshold = 1;
  string desc = 2;
  repeated RewardProgressDetail details = 3;
  TaskProgressNodeStatus status = 4;
  uint32 welfareId = 5;
}

message RewardProgress {
  uint32 current = 1;
  uint32 max = 2;
  repeated RewardProgressNode nodes = 3;
}

message Task {
  int32 id = 1; // 任务id
  TaskType typ = 2; // 任务类型
  TaskState state = 3; // 任务状态
  string rewardId = 4; // 奖励id
  NormalProperty normal = 5; // 普通属性
  string abTestStr = 6; // abTestStr "newcenternewtask|1_2665_10689"
  map<string, string> mapExt = 7; // 透传字段
  TaskClaimState claimState = 8; // 领取状态
  int64 claimTime = 9; // 领取时间
}

// 命中的模块
message TaskModule {
  string name = 1; // 模块名称
  string desc = 2; // 模块描述
  string jump = 3; // 模块跳转链接
  repeated Task tasks = 4; // 任务列表
  int32 id = 5; // 模块id
}

message Reward {
  int32 id = 1; // 奖励id
  int32 num = 2; // 数量
  string name = 3; // 名称
  string icon = 4; // 图标
}
