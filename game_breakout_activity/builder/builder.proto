syntax = "proto3";

package game_breakout_activity;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_breakout_activity/builder";

import "pb/device/device.proto";
import "pb/game_api/game_api.proto";

service Builder {
  rpc Read(ReadReq) returns (ReadRsp);
  rpc Join(JoinReq) returns (JoinRsp);
  rpc Claim(ClaimReq) returns (ClaimRsp);
  rpc AddProgress(AddProgressReq) returns (AddProgressRsp);
  rpc Clean(CleanReq) returns (CleanRsp);
  rpc RetentionPopup(game_api.RetentionPopupReq) returns (game_api.RetentionPopupRsp); // 挽留弹窗
}

message CleanReq {
  string appId = 1;
  string openId = 2;
  int32 activityId = 3;
}

message CleanRsp {}

message ReadReq {
  string appId = 1;
  string openId = 2;
  int32 activityId = 3;
}

message ReadRsp {}

message JoinReq {
  string appId = 1;
  string openId = 2;
  int32 activityId = 3;
}

message JoinRsp {}

message ClaimReq {
  string appId = 1;
  string openId = 2;
  int32 activityId = 3;
  int32 ladderId = 4;
  device.Device device = 5;
}

message ClaimRsp {
  repeated int32 ladderIds = 1; // 已领取的id列表
  repeated game_api.TreasureCard cards = 2; // 宝藏卡信息, 如果产生宝藏卡领取, 则放在这里
  repeated game_api.RewardItem rewards = 3; // 奖励信息
}

message AddProgressReq {
  message PropInfo {
    uint32 propId = 1; // 道具id
    uint32 propNum = 2; // 道具数量
  }
  string appId = 1;
  string openId = 2;
  int32 activityId = 3; // 可选
  int32 stageId = 4; // 关卡
  int32 stageType = 5; // 关卡类型
  repeated PropInfo preProps = 6; // 关卡前道具
  repeated PropInfo inProps = 7; // 关卡中道具
}

message AddProgressRsp {}
