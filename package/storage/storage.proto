syntax = "proto3";

package package.storage;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/package/storage";

import "pb/package/common/common.proto";

message Package {
  message Item {
    int64 itemId = 1;
    int64 num = 2;
    common.PackageItemType type = 3;
    int64 externalId = 4;
  }
  message ABTest {
    bool enable = 1;
    repeated string experimentGroup = 2;
    repeated string controlGroup = 3;
    string businessId = 4; // 业务 id
    string channelId = 5; // 渠道 id
    string moduleId = 6; // 模块 id
  }
  message Targeting {
    string ruleExpr = 1;
    int64 cacheTime = 2; // 秒
    ABTest abTest = 3;
    repeated string whitelist = 4;
  }
  message TimeRange {
    int64 beginTime = 1;
    int64 endTime = 2;
    int64 maxPurchaseTimes = 3; // 最大购买次数
  }
  message Frequency {
    int64 purchaseInterval = 1; // 可买间隔 秒
    int64 visibleDuration = 2; // 可见时长 秒
    int64 visibleInterval = 3; // 可见间隔 秒
  }
  message PriceGreenMidasGoods {
    string productId = 1;
    int64 price = 2;
    string skuId = 3;
    string availableTime = 4;
    string activityId = 5;
    string extra = 6; // 额外信息
  }
  message PriceGreen {
    PriceGreenMidasGoods android = 1;
    PriceGreenMidasGoods ios = 2;
    int64 greenId = 3;
  }
  message SceneUI {
    common.Scene scene = 1;
    string uiConfigId = 2;
    string bgProperty = 3;
  }
  message WeekCycle {
    string beginTime = 1; //开始时间，格式为 00:00:00
    string endTime = 2; //结束时间，格式为 00:00:00
    uint32 beginWeekday = 3; //周日:0  周一 ～ 周六：1～6
    uint32 endWeekday = 4; //周日:0  周一 ～ 周六：1～6
    int64 cycleMaxPurchaseTimes = 5; // 周期最大购买次数
  }
  message DayCycle {
    string beginTime = 1; // 开始时间，格式为 00:00:00
    string endTime = 2; // 结束时间，格式为 00:00:00
    int64 cycleMaxPurchaseTimes = 3; // 周期最大购买次数
  }
  int64 packageId = 1;
  string name = 2;
  string description = 3;
  common.PackagePriceType priceType = 4;
  int64 priceKB = 5; // 价格
  PriceGreen priceGreen = 6;
  Targeting targeting = 7;
  common.PackageVisibility visibility = 8;
  int64 sceneLobbyInterval = 9; // 大厅弹框间隔 秒
  repeated Item items = 10;
  TimeRange visibilityTimeRange = 11;
  Frequency visibilityFrequency = 12;
  repeated SceneUI sceneUIs = 13;
  int64 androidDiscount = 14; // 折扣
  bool enable = 15;
  int32 salePlatform = 16;
  int64 iosDiscount = 17; // 折扣
  int64 bundleId = 18;
  int64 targetingId = 19;
  WeekCycle weekCycle = 20;
  map<string, string> mMarketInfo = 21; // 商品营销属性
  DayCycle dayCycle = 22;
  PriceGreen pricePay = 23;
  int64 androidPayDiscount = 24; // 折扣
  int64 iosPayDiscount = 25; // 折扣
  // music svip {"exclusive_music_svip":true}
  string extraData = 26; // 额外信息
}

message ScenePackages {
  message Whitelist {
    string openId = 1; // 白名单使用
    repeated int64 packageIds = 2;
  }
  repeated int64 packageIds = 1;
  Whitelist whitelist = 2;
}

message UserPackageVisibility {
  int64 purchaseTimes = 1;
  int64 lastPurchaseTime = 2;
  int64 lastExposureTime = 3;
  repeated string transactionIds = 4;
  map<int32, int64> sceneLastExposureTimes = 5;
  map<int64, int64> weekCyclePurchaseTimes = 6; // key:每周一零点
  map<int64, int64> dayCyclePurchaseTimes = 7; // key:每天零点
}

message PayTransaction {
  string transactionId = 1;
}

message Bundle {
  message Item {
    int64 itemId = 1;
    int64 num = 2;
    common.PackageItemType type = 3;
    int64 externalId = 4;
    uint32 externalType = 5;
    string img = 6;
    string name = 7;
    string desc = 8; // 描述文案
  }
  int64 bundleId = 1;
  repeated Item items = 2;
  string appId = 3; // appId
}

message BundleLog {
  string appId = 1;
  string openId = 2;
  int64 bundleId = 3;
  int64 bundleNum = 4;
  string transactionId = 5;
  int64 timestamp = 6;
}

message GreenTradeInfoGroup {
  package.common.GreenTradeInfo android = 1;
  package.common.GreenTradeInfo ios = 2;
}

message BundleTransaction {
  string transactionId = 1;
  int64 timestamp = 2;
}
