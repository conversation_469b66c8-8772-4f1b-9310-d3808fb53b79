syntax = "proto3";

package package_monitor;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/package_monitor";

service Monitor {
  // produce
  rpc Produce(ProduceReq) returns (ProduceRsp);
  // consume
  rpc Consume(ConsumeReq) returns (ConsumeRsp);
  // alarm
  rpc Alarm (AlarmReq) returns (AlarmRsp);
  rpc AlarmQuery (AlarmQueryReq) returns (AlarmQueryRsp);
  // sync config
  rpc SyncConfig (SyncConfigReq) returns (SyncConfigRsp);
  rpc QueryConfig (QueryConfigReq) returns (QueryConfigRsp);
};

message AlarmQueryReq {
  string appId = 1;
  string openId = 2;
  int64 assetId = 3; // 资产id
  int64 start = 4;
  int64 end = 5;
}

message AlarmQueryRsp {
  message Item {
    string appId = 1;
    string openId = 2;
    int64 assetId = 3; // 资产id
    string assetName = 4; // 资产名称
    string reason = 5; // 道具发放原因
    string date = 6;
    int64 timestamp = 7; // 告警日期
    int64 assetNum = 8; // 实际发放数量
    int64 assetNumLimit = 9; // 预期发放数量
  }
  repeated Item items = 1;
}

message QueryConfigReq {
  string key = 1;
}

message QueryConfigRsp {
  bytes data = 1;
}

message SyncConfigReq {
  string key = 1;
  bytes data = 2;
}

message SyncConfigRsp {}

message ProduceReq {
  BundleFlow flow = 1;
}

message ProduceRsp {}

message ConsumeReq {
  string messageId = 1;
  BundleFlow flow = 2;
}

message ConsumeRsp {}

message BundleFlow {
  message Item {
    int32 id = 1;
    int32 num = 2;
    string name = 3;
    string transactionId = 4;
    uint32 type = 5;
    int64 externalId = 6;
    uint32 externalType = 7;
  }
  string appId = 1;
  string openId = 2;
  int64 bundleId = 3;
  repeated Item items = 4;
  string transactionId = 5;
  string reason = 6;
  bool isPaid = 7;
  int64 bundleNum = 8;
  int64 timestamp = 9;
}

message AlarmReq {
  string appId = 1;
  string openId = 2;
  int64 assetId = 3; // 资产id
  string assetName = 4; // 资产名称
  string reason = 5; // 道具发放原因
  string date = 6;
  int64 timestamp = 7; // 告警日期
  int64 assetNum = 8; // 实际发放数量
  int64 assetNumLimit = 9; // 预期发放数量
}

message AlarmRsp {}
