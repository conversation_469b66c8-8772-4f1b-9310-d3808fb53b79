syntax = "proto3";

package package_callback;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/package_callback";

import "pb/open_pay_extension/extension.proto";

service Callback {
  // 校验订单
  rpc CheckOrder(open_pay_extension.CheckOrderReq) returns (open_pay_extension.CheckOrderRsp);
  // 发货
  rpc Delivery(open_pay_extension.DeliveryReq) returns (open_pay_extension.DeliveryRsp);
}
