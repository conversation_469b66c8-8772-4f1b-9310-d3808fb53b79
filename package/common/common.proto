syntax = "proto3";

package package.common;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/package/common";

enum Scene {
  SceneUnknown = 0;

  // 全民爱消除
  ScenePreBattle = 15;     // 战前弹窗
  SceneMarket = 16;        // 游戏商城
  SceneStageFailure = 17;  // 闯关失败
  SceneLobby = 18;         // 大厅
  SceneLobbyDiscount = 19; // 大厅折扣入口
  SceneInGame = 20;        // 闯关中
  SceneStrengthExhausted = 34; // 体力用尽

  // 全民旅行
  SceneTravelMall = 22; // 商城列表
  SceneTravelFailure = 23; // 失败弹窗
  SceneTravelStrength = 24; // 体力弹窗
}

enum OperatingSystem {
  OperatingSystemUnknown = 0;
  OperatingSystemAndroid = 1;
  OperatingSystemIOS = 2;
}

enum PackageItemType {
  PackageItemTypeUnknown = 0;
  PackageItemTypeGame = 1;        // 游戏内物资
  PackageItemTypePlatform = 2;    // 平台内物资
  PackageItemTypeGameSpecial = 3; // 游戏内特殊物资
}

enum PackageVisibility {
  PackageVisibilityUnknown = 0;
  PackageVisibilityTimeRange = 1; // 固化活动期
  PackageVisibilityFrequency = 2; // 非固化活动期
  PackageVisibilityWeekCycle = 3; // 周重复礼包
  PackageVisibilityDayCycle = 4; // 日重复礼包
}

enum PackagePriceType {
  PackagePriceTypeUnknown = 0;
  PackagePriceTypeKB = 1;    // kb
  PackagePriceTypeGreen = 2; // 绿钻
}

message GreenTradeInfo {
  string productId = 1;
  int64 price = 2;
  string skuId = 3;
  string availableTime = 4;
  string activityId = 5;
  string payProductId = 6;
  string extra = 7; // 额外信息
}
