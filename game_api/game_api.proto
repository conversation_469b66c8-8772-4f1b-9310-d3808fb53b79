syntax = "proto3";

package game_api;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_api";

import "pb/event/event.proto";

// 游戏相关：中台——》游戏
service GameApi {
  // 赠送体力
  rpc SendHeart(SendHeartReq) returns (SendHeartRsp);
  // 查询闯关
  rpc QueryStage(QueryStageReq) returns (QueryStageRsp);
  // 查询礼包
  rpc BatchQueryPackage(BatchQueryPackageReq) returns (BatchQueryPackageRsp);
  // 游戏发货
  rpc GameDeliveryPackage(GameDeliveryPackageReq) returns (GameDeliveryPackageRsp);
  // 批量兑换礼包
  rpc BatchGameDeliveryPackage(BatchGameDeliveryPackageReq) returns (BatchGameDeliveryPackageRsp);
  // 批量兑换礼包--单个id奖励区分
  rpc BatchGameDeliveryPackageList(BatchGameDeliveryPackageListReq) returns (BatchGameDeliveryPackageListRsp);
}

message SendHeartReq {
  string friend_id = 1; // 接收方openid
  string owner_id = 2; // 赠送方openid
}

message SendHeartRsp {
  uint32 hearts = 1; // 本次赠送的体力数量
}

message QueryStageReq {
  string app_id = 1;
  string open_id = 2;
}

message QueryStageRsp {
  uint32 cur_stage = 1; // 累计成功闯关数：包含巅峰赛
  uint32 max_normal_floor = 2;// 闯关进度：对应页面上显示的进度
}

enum RewardItemType {
  Reward_From_GameNormal = 0; // 游戏物品-默认
  Reward_From_Platform = 1; // 平台物品
  Reward_From_GameLimitedTime = 2; // 游戏物品-限时
}

message RewardItem {
  uint32 id = 1; // 奖励id
  uint32 num = 2; // 奖励数量 限时道具表示分钟数
  string name = 3; // 奖励名称
  RewardItemType type = 4; // 资产类型
  string img = 5; // 图片
  string desc = 6; // 描述问题呢
}

message Package {
  string package_id = 1; // 礼包id
  string package_name = 2; // 礼包名称
  repeated RewardItem rewards = 3; // 奖励
  int64 price = 4; // 礼包价格
}

message BatchQueryPackageReq {
  string app_id = 1;
  string open_id = 2;
  repeated string package_ids = 3; // 礼包ids
}

message BatchQueryPackageRsp {
  map<string, Package> packages = 1; // 礼包
}

enum GameDeliveryType {
  GameDeliveryTypeRedStone = 0; // 游戏红石 https://gm.tmeoa.com/?type=kg_cwsx_activity_gift_prod
  GameDeliveryTypeMiddleConfig = 1; // 中台配置 https://game-config.tmeoa.com/sanxiao/asset/props
}

message GameDeliveryPackageReq {
  string app_id = 1;
  string open_id = 2;
  string package_id = 3; // 礼包 id
  string transaction_id = 4; // 唯一订单 id
  int64 timestamp = 5; // 发货时间戳
  uint32 num = 6; // 发几个礼包,默认一个
  GameDeliveryType type = 7;
}

message GameDeliveryPackageRsp {
  repeated RewardItem rewards = 1; // 奖励
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励, 这个字段不为空
}

message BatchGameDeliveryPackageReq {
  string app_id = 1;
  string open_id = 2;
  repeated string package_ids = 3; // 礼包 id
  string transaction_id = 4; // 唯一订单 id
  int64 timestamp = 5; // 发货时间戳
}

message BatchGameDeliveryPackageRsp {
  repeated RewardItem rewards = 1; // 奖励
  repeated TreasureCard cards = 2; // 如果发奖产生宝藏奖励, 这个字段不为空
}

// 宝藏卡结构
message TreasureCard {
  uint32 treasure_card_id = 1; // 宝藏卡id
  bool is_decompose = 2; // 是否被分
  uint32 decompose_num = 3; // 分解出多少碎片
}

message BatchGameDeliveryPackageListReq {
  string app_id = 1;
  string open_id = 2;
  repeated string package_ids = 3; // 礼包 id
  string transaction_id = 4; // 唯一订单 id
  int64 timestamp = 5; // 发货时间戳
}

message GameDeliveryPackageRewards {
  string package_id = 1; // 礼包id
  repeated RewardItem rewards = 2; // 奖励
  repeated TreasureCard cards = 3; // 如果发奖产生宝藏奖励这个字段不为空
}

message BatchGameDeliveryPackageListRsp {
  repeated GameDeliveryPackageRewards rewards = 1; // 按请求顺序返回
}

// 挽留弹窗协议
enum GameActivityType {
  Unknow = 0; //  未知
  Schedule = 1; // 进度
  Collect = 2; // 收集
}

enum GameActivityStatus {
  Default = 0; // 默认 未参加
  Ongoing = 1; // 参加进行中
  UnClaimed = 2; // 待领取
}

message RetentionPopupReq {
  string appId = 1;
  string openId = 2;
  event.CWSXStageType stageType = 3; // 关卡类型
  bool doubleBuff = 4; // 双倍buff
}

message RetentionPopupRsp {
  GameActivityType activityType = 1; // GameActivityType 活动类型
  GameActivityStatus activityStatus = 2; // GameActivityStatus 活动状态
  uint32 currentSchedule = 3; // 当前进度值
  uint32 nextSchedule = 4; // 下一阶段值
  uint32 lostGoodNums = 5; // 损失物品数量
  repeated RewardItem NextStageRewards = 6; // 下阶段可获得奖励

  map<string, string> mapExt = 15;
}
// 挽留弹窗协议

message CallbackStepBuyReq {
  string appId = 1;
  string openId = 2;
  uint32 buyRound = 3; // 本次闯关第几次购买
  string billId = 4; // 购买唯一id
  int64 step = 5; // 免费步数
}

message CallbackStepBuyRsp {}