syntax = "proto3";

package timer;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/timer";

import "pb/timer/common.proto";

service Timer {
  // 查询计时器
  rpc Query(TimerQueryRequest) returns (TimerQueryResponse);
  // 注册定时器
  rpc Register(TimerRegisterRequest) returns (TimerRegisterResponse);
  // 取消定时器
  rpc Cancel(TimerCancelRequest) returns (TimerCancelResponse);
  // 测试回调
  // rpc Callback(TimerCallbackRequest) returns (TimerCallbackResponse);
}

message TimerCallbackRequest {
  string appId = 1;
  string timerId = 2;
  int64 fireTime = 3; // 触发时间 毫秒
  bytes bizData = 4; // 业务数据
}

message TimerCallbackResponse {
  bool cancel = 1; // 用于周期定时器取消
  int64 nextFireTime = 2; // 用于一次性定时器指定下次触发时间
  bytes bizData = 3; // 业务数据 不为 nil 则覆盖
}

message TimerQueryRequest {
  string appId = 1;
  string timerId = 2;
}

message TimerQueryResponse {
  TimerStatus status = 1;
  bytes bizData = 2; // 业务数据
  TimerConfig config = 3; // 定时器配置
}

message TimerRegisterRequest {
  string appId = 1; // app id
  string timerId = 2; // 唯一 id 不大于 128 字节
  bytes bizData = 3; // 业务数据
  TimerConfig config = 4; // 定时器配置
}

message TimerRegisterResponse {}

message TimerCancelRequest {
  string appId = 1;
  string timerId = 2;
}

message TimerCancelResponse {}
