syntax = "proto3";

package timer;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/timer";

enum TimerTriggerType {
  Unknow = 0; // 未知
  Once = 1; // 一次性
  Period = 2; // 周期
}

enum TimerStatus {
  Invalid = 0; // 无效
  Pending = 1;
  Normal = 2; // 正常
  Completed = 3; // 已完成
  Canceled = 4; // 已取消
  Abandoned = 5;
}

message TimerTriggerOnce {
  int64 fireTime = 1; // 触发时间戳 毫秒
}

message TimerTriggerPeriod {
  int64 beginTime = 1; // 开始时间戳 毫秒
  int64 interval = 2; // 间隔 毫秒
}

message TimerTrigger {
  TimerTriggerType type = 1;
  TimerTriggerOnce once = 2; // 一次性触发
  TimerTriggerPeriod period = 3; // 周期触发
}

message TimerRetry {
  int64 maxTimes = 1; // 最大重试次数 默认 0 不重试
  int64 interval = 2; // 重试间隔 毫秒 默认 1000ms
}

message TimerCallback {
  string serviceName = 1; // 服务名
  int64 timeout = 2; // 超时时间 毫秒 范围 (1, 5000] 默认 1000ms 周期类型的超时时间如果比触发间隔时间长 超时会导致中间的周期不触发
}

message TimerConfig {
  TimerTrigger trigger = 1; // 触发配置
  TimerCallback callback = 2; // 回调配置
  TimerRetry retry = 3; // 重试配置
}
