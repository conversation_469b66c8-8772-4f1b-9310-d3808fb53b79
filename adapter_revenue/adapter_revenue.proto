syntax = "proto3";

package adapter_revenue;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_revenue";

import "pb/asset/asset.proto";
import "pb/openpay/openpay.proto";

// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
service AdapterRevenue {
  // 查询游戏币; joox, wesing
  rpc GetBalance(GetBalanceReq) returns (GetBalanceRsp);
  // 下单
  rpc PlaceOrder(PlaceOrderReq) returns (PlaceOrderRsp);
  // 扣除游戏币;
  rpc Pay(PayReq) returns (PayRsp);
  // 退款
  rpc Refund(RefundReq) returns (RefundRsp);
  // 奖励游戏币; wesing
  rpc Present(PresentReq) returns (PresentRsp);
  // 根据指定金额发奖
  rpc Prize(PrizeReq) returns (PrizeRsp);
  // 消耗免费礼物
  rpc ConsumeFreeGift(ConsumeFreeGiftReq) returns (ConsumeFreeGiftRsp);
  // 发送平台资产
  rpc SendSingleReward(SendSingleRewardReq) returns (SendSingleRewardRsp);
  // 礼物信息
  rpc RewardDetail(RewardDetailReq) returns (RewardDetailRsp);
  // 礼物信息批量
  rpc RewardDetailBatch(RewardDetailBatchReq) returns (RewardDetailBatchRsp);
  // 批量获取道具信息
  rpc BatchPropDetail(BatchPropDetailReq) returns (BatchPropDetailRsp);
  // 批量获取礼物信息
  rpc BatchGiftDetail(BatchGiftDetailReq) returns (BatchGiftDetailRsp);
  // 查询平台货币数量
  rpc GetPlatBalance(GetPlatBalanceReq) returns (GetPlatBalanceRsp);
  //  废弃: 发送平台资产 TODO
  rpc PrizeV2(PrizeV2Req) returns (PrizeV2Rsp);
  // 查平台资产
  rpc QueryAsset(QueryAssetReq) returns (QueryAssetRsp);
  // 加平台资产
  rpc AddAsset(AddAssetReq) returns (AddAssetRsp);
  // 扣平台资产
  rpc SubAsset(SubAssetReq) returns (SubAssetRsp);
  // 事前风控检查
  rpc RiskPreProcessingCheck(RiskPreProcessingCheckReq) returns (RiskPreProcessingCheckRsp);
  // 支付中台 下单
  rpc CreateTrade(CreateTradeReq) returns (CreateTradeRsp);
}

message GetBalanceReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
}

message GetBalanceRsp {
  // 余额
  uint32 balance = 1;
  // 赠币余额，joox有用到
  uint32 free_currency_balance = 2;
}

message PlaceOrderReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 扣除数量, 必须大于0
  uint32 amount = 3;
  // 增加资产列表, 调用delivery时透传
  repeated component.game.UserAssetChange assets = 4;
  // 订单配置，平台业务id，是否算收入等
  component.game.OrderConf order_conf = 5;
  // 场景
  component.game.Scene scene = 6;
  // 设备相关
  component.game.Device device = 7;
  // midas
  component.game.Midas midas = 8;
  // mapExt
  map<string, string> mapExt = 9;
  //时间 用于透传
  uint32 sys_ts = 10;
}

message PlaceOrderRsp {
  string bill_no = 1; // 全局唯一
  // 订单签名，kg有
  string sig = 2;
  //时间 用于透传
  uint32 sys_ts = 3;
}

message PayReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 订单号
  string bill_no = 3;
  // 扣除游戏币数量
  uint32 amount = 4;
  // 备注，会写流水日志
  string app_remark = 5;
  // 增加资产列表, 调用delivery时透传
  repeated component.game.UserAssetChange assets = 6;
  // 订单配置，平台业务id，是否算收入等
  component.game.OrderConf order_conf = 7;
  // 场景
  component.game.Scene scene = 8;
  // 设备相关
  component.game.Device device = 9;
  // midas
  component.game.Midas midas = 10;
  // mapExt
  map<string, string> mapExt = 11;
  // 订单签名，kg有
  string sig = 12;
  // 调用Delivery接口时透传这个id
  string transaction_id = 13;
  // 游戏场次 id
  string round_id = 14;
}

message PayRsp {
  // 订单号
  string bill_no = 1;
  // 余额
  uint32 balance = 2;
}

message RefundReq {
  // app id
  string app_id = 1;
  // 订单号
  string bill_no = 2;
  // open id
  string open_id = 3;
}

message RefundRsp {}

message PresentReq {
  message AssetItems {
    string asset_id = 1;
    uint32 amount = 2;
  }
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 订单号
  string bill_no = 3;
  // 奖励游戏币数量
  uint32 amount = 4;
  // 备注，会写流水日志
  string app_remark = 5;
  // 游戏场次 id
  string round_id = 6;
  // 资产
  repeated AssetItems asset_items = 7;
}

message PresentRsp {
  // 订单号
  string bill_no = 1;
  // 余额
  uint32 balance = 2;
}

message PrizeReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 金额
  uint32 amount = 3;
  // 订单号
  string bill_no = 4;
}

message PrizeGift {
  // 礼物名称
  string name = 1;
  // 礼物图标
  string icon = 2;
  // 礼物数量
  uint32 num = 3;
  // 礼物ID
  string gift_id = 4;
}

message PrizeRsp {
  // 礼物列表
  repeated PrizeGift gifts = 1;
}

message ConsumeFreeGiftReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 消耗数量 num
  uint32 num = 3;
  // 订单号 bill_no
  string bill_no = 4;
  // 来源文案 from_desc
  string from_desc = 5;
  // 上报 id report_id
  int64 report_id = 6;
}

message ConsumeFreeGiftRsp {}

message SingleRewardItem {
  // gift_id 奖品ID
  string gift_id = 1;
  // gift_type 奖品Type
  uint32 gift_type = 2;
  // gift_num 发放数量
  int64 gift_num = 3;
  // gift_reason 发放理由
  string gift_reason = 4;
}

message SendSingleRewardReq {
  message ConsumeAssetItem {
    string asset_id = 1;
    uint32 amount = 2;
  }
  message Device {
    string platform = 1; // 平台信息 kugou、qqmusic、qmkege、kuwo、lanren
    string version = 2; // 客户端版本 1.2.3
    string os = 3; // 系统 android、ios
  }
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 奖励ID 透传用
  int64 reward_id = 3;
  // 数量 num
  uint32 num = 4;
  // 订单号 bill_no
  string bill_no = 5;
  // 奖品信息 reward_item
  SingleRewardItem reward_item = 6;
  // 消耗的资产信息
  ConsumeAssetItem consume_item = 7;
  // 设备信息
  Device device = 8;
  //userid类型
  int32 id_type = 9; //UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin
  map<string, string> MapExt = 10;
  // 礼包发放原始的订单id
  string origin_bill_no = 11;
}

message SendSingleRewardRsp {}

message RewardDetailReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 礼物ID 非奖励ID，为奖励ID下面挂载的礼物
  string gift_id = 3;
  // 礼物type 参考reward_sender_comm.proto GiftType
  uint32 gift_type = 4;
  //userid类型
  int32 id_type = 5; //UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin
}

//子礼物信息
message SubGiftDetail {
  // 礼物ID
  string sub_gift_id = 1;
  // 礼物type 参考reward_sender_comm.proto GiftType
  string sub_gift_type = 2;
  // 礼物数量
  uint32 sub_gift_num = 3;
  // 礼物名称
  string sub_gift_name = 4;
  // 礼物logo
  string sub_gift_logo = 5;
  // 礼物单价
  uint32 sub_gift_unit_price = 6;
  // expire_type 过期类型, 1相对过期, 2绝对过期, 0不过期
  uint32 sub_gift_expire_type = 7;
  // expire_sec 过期时间(s), 相对过期是过期时间(s),绝对过期是过期时间戳
  uint32 sub_gift_expire_time = 8;
}

// 自定义类型
message CustomRewardItem {
  string StrRewardName = 1; // 名称
  string StrData = 2; // 数据
  uint32 URewardType = 3; // 类型
  string StrLogo = 4; // 图片
  uint32 URewardNum = 5; // 数量
  int64 LCustomValue = 6; // 价值
}

message RewardDetailRsp {
  //SubGiftDetail 奖品ID 所指向的礼物列表
  repeated SubGiftDetail gift_array = 1;
  // 自定义类型
  CustomRewardItem customRewardItem = 2;
}

message RewardDetailBatchReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 礼物ID 非奖励ID，为奖励ID下面挂载的礼物
  repeated string gift_ids = 3;
  // 礼物type 参考reward_sender_comm.proto GiftType
  uint32 gift_type = 4;
}

message RewardDetailBatchRsp {
  message RewardDetail {
    //SubGiftDetail 奖品ID 所指向的礼物列表
    repeated SubGiftDetail gift_array = 1;
    // 自定义类型
    CustomRewardItem customRewardItem = 2;
  }
  map<string, RewardDetail> subGiftDetails = 1;
}

message PropDetail {
  // 道具名称
  string prop_name = 1;
  // 道具logo
  string prop_logo = 2;
}

message BatchPropDetailReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 道具ID
  repeated int64 prop_ids = 3;
  // plat 如果有plat，就不从openid和appid里面解析了
  uint32 plat = 4;
}

message BatchPropDetailRsp {
  map<int64, PropDetail> data = 1;
}

message GiftDetail {
  // 礼物名称
  string gift_name = 1;
  // 礼物logo
  string gift_logo = 2;
  // 礼物单价
  uint32 gift_price = 3;
}

message BatchGiftDetailReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 礼物ID
  repeated int64 gift_ids = 3;
  // plat 如果有plat，就不从openid和appid里面解析了
  uint32 plat = 4;
}

message BatchGiftDetailRsp {
  map<int64, GiftDetail> data = 1;
}

enum PlatAssetType {
  PLAT_BALANCE = 0; //默认查平台货币余额
  KG_FLOWER = 1;
}

message GetPlatBalanceReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // plat_asset_id 平台货币ID
  string plat_asset_id = 3;
  // plat_asset_type 平台货币类型 参考PlatAssetType
  uint32 plat_asset_type = 4;
}

message GetPlatBalanceRsp {
  uint64 balance = 1;
}

message PrizeV2Req {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 金额
  uint32 amount = 3;
  // 订单号
  string bill_no = 4;
  // 待发奖礼物 K歌：K币礼物
  repeated PrizeGift gifts = 5;
}

message PrizeV2Rsp {}

//来源渠道
message SourceChannel {
  uint32 source_id = 1; //来源ID, 后台分配
  string reason = 2;
}

message UserAsset {
  int64 asset_id = 1;
  int64 asset_num = 2;
}

message AssetChange {
  int64 asset_id = 1;
  int64 asset_num = 2;
}

enum AssetType {
  AssetTypeDefault = 0; //默认
  AssetTypeDiamond = 1; //K歌钻石
  AssetTypeDiamondGift = 2; //K歌钻石礼物
  AssetTypeSilverCoin = 3; //wesing银币
  AssetTypeCarrierDogecoin = 4; // 酷狗网赚金币
  AssetTypeKgWZCoin = 5; // K歌网赚金币
  AssetTypeLrTaskCoin = 6; // 懒人网赚金币
  AssetTypeKugouFxCoin = 7; // 酷狗直播星币
}

message QueryAssetReq {
  string app_id = 1;
  string open_id = 2;
  repeated int64 asset_ids = 3;
  SourceChannel source = 4;
  AssetType asset_type = 5;
}

message QueryAssetRsp {
  map<int64, UserAsset> map_result = 1;
}

message AddAssetReq {
  string app_id = 1;
  string open_id = 2;
  repeated AssetChange assets = 3;
  string bill_no = 4;
  SourceChannel source = 5;
  int64 timestamp = 6; // 操作时间戳 单位毫秒
  AssetType asset_type = 7;
  int32 scene = 8; //场景，透传上报用
  map<string, string> map_ext = 9; // 扩展参数
}

message AddAssetRsp {
  string bill_no = 1;
  map<int64, UserAsset> map_result = 2; //结果资产列表
}

message SubAssetReq {
  string app_id = 1;
  string open_id = 2;
  repeated AssetChange assets = 3;
  string bill_no = 4;
  SourceChannel source = 5;
  int64 timestamp = 6; // 操作时间戳 单位毫秒
  AssetType asset_type = 7;
  int32 scene = 8; //场景，透传上报用
  map<string, string> map_ext = 9; // 扩展参数
}

message SubAssetRsp {
  string bill_no = 1;
  map<int64, UserAsset> map_result = 2; //结果资产列表
}

enum EPreprocessingModelType {
  MODEL_UNKNOW = 0; // 无效，不要用
  MODEL_PROBABILITY = 100; //概率模型
  MODEL_EXCHANGE = 200; //兑换模型
  MODEL_ACTIVITY = 300; //活动模型
}

//对于主类型是 MODEL_PROBABILITY ，其子类型范围为：[100w1-200w)
enum EProbabilityModelType {
  MODEL_PROBABILITY_FOR_UNKNOW = 0; // 无效
  MODEL_PROBABILITY_FOR_CLOUD_GAME = 1000003; //公有云小游戏公用，没特殊情况都用这个，如果需要新增，请联系@xiaoqlin(林小强)
}

//对于主类型是 MODEL_EXCHANGE ，   其子类型范围为：[200w1-300w)
enum EExchangeModelType {
  MODEL_EXCHANGE_FOR_UNKNOW = 0; // 无效
  MODEL_EXCHANGE_FOR_CLOUD_GAME = 2000006; // 公有云小游戏公用，没特殊情况都用这个，如果需要新增，请联系@xiaoqlin(林小强)
}

//对于主类型是 MODEL_ACTIVITY ，   其子类型范围为：[300w1-400w)
enum EActivityModelType {
  MODEL_ACTIVITY_FOR_UNKNOW = 0; // 无效
  MODEL_ACTIVITY_FOR_CLOUD_GAME = 3000003; // 公有云小游戏公用，没特殊情况都用这个，如果需要新增，请联系@xiaoqlin(林小强)
}

enum EKGRewardType {
  KG_REWARD_UNKNOW = 0; //无效
  KG_REWARD_WELFARE = 1; //福利ID
  KG_REWARD_KB = 2; //kb
  KG_REWARD_KB_GIFT = 3; //kb礼物
  KG_REWARD_LOTTERY_ACT = 4; //抽奖中台
  KG_REWARD_CUSTOMIZE = 10000; //自定义
}

message RewardItem {
  uint32 u_reward_id = 1;
  string str_reward_id = 2;
  uint32 reward_type = 3; // EKGRewardType
  int64 reward_value = 4; //单价 单位：分(RMB)
  uint32 reward_num = 5; //奖励发放个数
  map<string, string> map_ext = 6;
}

message ProbabilityItem {
  int64 probability = 1;
  RewardItem reward = 2;
}

message ProbabilityModelInput {
  EProbabilityModelType sub_type = 1; //模型子类型 EProbabilityModelType
  repeated ProbabilityItem probabilitys = 2;
}

message ExchangeModelInput {
  EExchangeModelType sub_type = 1; //模型子类型 EExchangeModelType
  repeated RewardItem rewards = 2; // 奖励列表
}

message ActivityModelInput {
  EActivityModelType sub_type = 1; // 模型子类型，EProbabilityModelType
  repeated RewardItem rewards = 2; // 奖励列表，对于一次发放多个奖励的，通过这个字段传
}

message CheckIn {
  string check_id = 1; //活动ID、任务ID、游戏ID、抽奖活动ID等等
  map<string, string> map_ext = 2;
  int64 cost_value = 3; //业务消耗价值。单位：分(RMB)
  ProbabilityModelInput probability = 4; //概率模型相关参数
  ExchangeModelInput exchange = 5; //兑换模型相关参数
  ActivityModelInput activity = 6; //活动模型相关参数
  string risk_id = 7; //风控ID
}

message CheckOut {
  bool pass = 1;
  string msg = 2;
  map<string, string> map_ext = 3;
  int64 expected_value = 4; //期望奖励数值
  int64 real_value = 5; //实际消耗数值
  double roi = 6; //返奖率   lExpectedValue/lRealValue
}

message RiskPreProcessingCheckReq {
  string app_id = 1; // 游戏AppID，例如 20000015，adapter中自动转为平台ID
  EPreprocessingModelType model_type = 2; //预处理模型类型 ENUM_PREPROCESSING_MODEL
  CheckIn check_in = 3;
}

message RiskPreProcessingCheckRsp {
  CheckOut check_out = 1;
}

message CreateTradeReq {
  message Goods {
    string goods_id = 1;
    int64 goods_num = 2;
    int64 goods_amount = 3;
  }
  string app_id = 1;
  string open_id = 2;
  string offer_id = 3; // 购买场景 id
  int64 amount = 4; // 订单总金额
  string description = 5; // 订单描述（会展示在微信支付页面）
  string business_data = 6; // 业务透传数据（发货时会回传）
  repeated Goods goods_list = 7; // 物品列表
  int64 expire_time = 8; // 订单过期时间 单位秒 不设置会用默认的
  string user_ip = 9; // 用户 ip
  string device = 10; // 用户设备号
}

message CreateTradeRsp {
  string trade_id = 1; // 订单号
}
