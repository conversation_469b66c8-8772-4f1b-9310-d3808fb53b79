syntax = "proto3";

package open_pay_marketing;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/open_pay_marketing";

enum MarketingType {
  MarketingTypeNone = 0;
  MarketingTypeFixed = 1;
  MarketingTypeThreshold = 2;
}

message MarketingConfig {
  message Gear {
    int64 value = 1;
    string tag = 2;
    int64 rewardId = 3;
  }

  int64 id = 1;
  int64 targetId = 2;
  MarketingType marketingType = 3;
  string copywriting = 4;
  string image = 5;
  string imageUrl = 6;
  repeated Gear gears = 7;
  int64 beginTime = 8;
  int64 endTime = 9;
  int64 priority = 10;
}

message MarketingConfigs {
  message List {
    repeated MarketingConfig configs = 1;
  }
  map<int64, List> lists = 1;
}

// mu:<marketingId>:<uid>
message MarketingUser {
  int64 rewardId = 1;
  int64 timestamp = 2;
  // bool consume = 3;
}
