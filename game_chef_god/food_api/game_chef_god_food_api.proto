syntax = "proto3";

package game_chef_god_food_api;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_chef_god/food_api";

import "pb/game_chef_god/backend/food.proto";


// 厨神食材api
service GameChefGodFoodApi {
  // 查询食材信息
  rpc QueryFoodInfo(QueryFoodInfoReq) returns (QueryFoodInfoRsp);
  // 查询用户食材过期信息
  rpc QueryUserFoodExpireInfo(QueryUserFoodExpireInfoReq) returns (QueryUserFoodExpireInfoRsp);
  // 查询用户食材列表
  rpc QueryUserFoodList(QueryUserFoodListReq) returns (QueryUserFoodListRsp);
  //扣食材(内部使用,菜品服务)
  rpc DeductFood(DeductFoodReq) returns (DeductFoodRsp);
  //加食材(内部使用)
  rpc AddFood(AddFoodReq) returns (AddFoodRsp);
}

message QueryFoodInfoReq {
  //食材id列表
  repeated int64 FoodIDList = 1;
}

message QueryFoodInfoRsp {
  map<int64, game_chef_god_backend.Food> FoodMap = 1;
}

message QueryUserFoodExpireInfoReq {//用登录态
}

message QueryUserFoodExpireInfoRsp {
  //即将过期的食材种数
  int32 FoodNum = 1;
}

message QueryUserFoodListReq {//用登录态
}

// 食材过期信息
message FoodExpireInfo {
  int32 Num = 1; //数量
  int64 ExpireTs = 2; //过期时间
}

// 食材
message Food {
  game_chef_god_backend.Food Food = 1;
  int32 TotalNum = 2; //总数
  repeated FoodExpireInfo ExpireInfoList = 3; //过期列表
  string ExpireTips = 4; //过期提示
}

message QueryUserFoodListRsp {
  //食材列表
  repeated Food FoodList = 1;
}

message DeductFoodItem{
  int64 FoodID = 1; //食材id
  int32 Num = 2;
}

message DeductFoodReq{
  int64 Uid = 1;
  repeated DeductFoodItem FoodList = 2;
  string BillNo = 3; //订单号
}

enum DeductFoodCode{
  Unknown = 0;
  Success = 1; //成功 
  BillNoRepeat = 2; //订单重复
  NotEnough = 3; //食材不够
}

message DeductFoodRsp{
  DeductFoodCode DeductFoodCode = 1;
}

message AddFoodReq{
  int64 Uid = 1;
  repeated DeductFoodItem FoodList = 2;
  int64 Ts = 3; //时间戳,自动转换成23:59:59
  string BillNo = 4; //订单号
}

message AddFoodRsp{

}