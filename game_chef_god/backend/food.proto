syntax = "proto3";

package game_chef_god_backend;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_chef_god/backend";

message GiftInfo{
  int64 GiftID = 1; 
  string GiftName = 2;
}
// 食材(表)
message Food {
  int64 FoodID = 1; //食物id
  string Name = 2; //食物名
  string Logo = 3; //图片
  repeated GiftInfo GiftList = 4; //可以从什么礼物中开出(配置不写入)
}

enum FoodDropType{
  Default = 0; //默认类型
  DayFirstFull = 1; //每天首次送满n个
}

// 食材掉落概率
message FoodDropProbability {
  int32 Probability = 1; //掉落概率
  int64 FoodID = 2;
  int32 MustHitNum = 3; //送n次后必中
  int32 Num = 4; //掉落的数量
}

// 食材掉落配置(表)
message FoodDropConfig {
  int64 GiftID = 1; //可获得食材的礼物id
  repeated FoodDropProbability DropProbabilityList = 2; //掉落概率列表
  FoodDropType FoodDropType = 3; //掉落类型
  int32 SendGiftNum = 4; //送礼数量(每天首次送满使用)
}
