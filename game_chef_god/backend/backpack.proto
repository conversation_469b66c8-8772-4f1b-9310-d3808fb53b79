syntax = "proto3";

package game_chef_god_backend;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_chef_god/backend";

message ExpireInfo {
  int32 Num = 1; //数量
  int64 ExpireTs = 2; //过期时间
}

// 背包食材
message BackpackFood {
  int64 FoodID = 1; //食材id
  repeated ExpireInfo ExpireInfoList = 2;
}

// 背包
message Backpack {
  map<int64,BackpackFood> FoodMap = 1;
  repeated string BillRecord = 2; //记录最新的订单号
}
