syntax = "proto3";

package cproxy_comm;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/cproxy/cproxy_comm";

enum ProductType {
  PRODUCT_TYPE_INT_KG = 0; // const string PRODUCT_TYPE_KG = "kg";
  PRODUCT_TYPE_INT_QY = 1; // const string PRODUCT_TYPE_QY = "qy";
}

enum BusinessType {
  BUSINESS_TYPE_INVALID = 0;
  BUSINESS_TYPE_SPACE_CITY_GAME_KG = 1; // kg疯狂太空城
  BUSINESS_TYPE_SPACE_CITY_GAME_QY = 2; // qy疯狂太空城
}

enum CproxyRetCode {
  CPROXY_RET_CODE_SUCC = 0; // 返回成功码
  CPROXY_RET_CODE_NOT_ONLINE = 1; // 用户不在线
  CPROXY_RET_JOIN_GROUP_FAIL = 2; // 用户加入广播组失败
  CRPOXY_RET_REQUEST_FAIL = 3; // 请求服务错误
  CPROXY_RET_RATE_LIMITED = 4; // 被限流
}

enum MsgType {
  MSGTYPE_INVALID = 0; // 无效类型
  MSGTYPE_UNICAST = 1; // 单播消息
  MSGTYPE_BROADCAST_ROOM = 2; // 组播消息
  MSGTYPE_BROADCAST_ALL = 3; // 全量广播消息
  MSGTYPE_RES = 4; // websocket下游请求回包内容
  MSGTYPE_META = 5; // 元数据(如心跳配置)
}

enum BodyType {
  BODYTYPE_PROTO = 0; // 默认类型，Proto二进制
  BODYTYPE_JSON = 1; // json类型
  BODYTYPE_JSON_OBJECT = 2; // json实体,即body会被转成一个interface{}对象
}

// Message Header;
message Header {
  MsgType uMsgType = 1; // 消息体类型
  string strExt = 2; // 该服务对应的下游操作类型，由业务服务定义
  string strRoomId = 3; // 房间ID
  string strTraceId = 4; // 消息体ID，服务端透传至client，追踪链路消息送达情况
  uint32 uBodyType = 5; // 默认用二进制, BodyType
  int64 lSeqId = 6; // 消息序列号, TODO: 废弃字段信息
  int32 iErrCode = 7; //请求返回错误号
  string strErrMsg = 8; //请求返回错误信息
  string strCmdService = 9; //请求微服务名称
  string strAppId = 10; // 应用ID
  int64 lStartTime = 11; // 消息到达长链接系统的时间
  string strSeqId = 12; // 消息seqId
  bool bIsNotRsp = 13; // 是否不需要回包
  int32 iPriority = 14; // 消息优先级, 值越高优先级越高
  string strCmdServiceKey = 15; // 上行消息一致性哈希Key,可选
}

message Message {
  Header stHeader = 1; // 消息头, 下游通过该结构体中的 strExt和 uMsgType 来确定如何解析 vctBody 字段
  bytes vctBody = 2; // 消息体
}
