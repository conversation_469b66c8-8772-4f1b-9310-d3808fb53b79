syntax = "proto3";

package interactive_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game/room_dev";

service RoomDev {
  // 创建游戏 (空实现)
  rpc DevCreateGame(DevCreateGameReq) returns (DevCreateGameRsp);
  // 结束游戏
  rpc DevGameOver(DevGameOverReq) returns (DevGameOverRsp);
  // 中途淘汰
  rpc DevPlayerOut(DevPlayerOutReq) returns (DevPlayerOutRsp);
}

message DevCreateGameReq {
}

message DevCreateGameRsp {
}

message DevGameOverReq {
  string roomId = 1;
}

message DevGameOverRsp {
}

message DevPlayerOutReq {
  string roomId = 1;
  string openId = 2;
}

message DevPlayerOutRsp {
}
