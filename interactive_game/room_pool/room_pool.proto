syntax = "proto3";

package interactive_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game/room_pool";

import "pb/interactive_game/common/common.proto";

service RoomPool {
  // 匹配人数
  rpc CountMatchPlayer(CountMatchPlayerReq) returns (CountMatchPlayerRsp);
  // 匹配历史
  rpc GetMatchIntention(GetMatchIntentionReq) returns (GetMatchIntentionRsp);
}

message CountMatchPlayerReq {}

message CountMatchPlayerRsp {
  int64 playerNum = 1; // 玩家数量
}

message GetMatchIntentionReq {}

message GetMatchIntentionRsp {
  repeated common.MatchIntention intentions = 1;
}
