syntax = "proto3";

package interactive_game_trading_api;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game/trading_api";

service TradingApi {
  // 查询代替品
  rpc QueryAlternate(QueryAlternateReq) returns (QueryAlternateRsp);
  // 查询偏好
  rpc QueryPreference(QueryPreferenceReq) returns (QueryPreferenceRsp);
  // 设置偏好
  rpc UpdatePreference(UpdatePreferenceReq) returns (UpdatePreferenceRsp);
  // 兑换
  rpc Exchange(ExchangeReq) returns (ExchangeRsp);
}

message QueryAlternateReq {
  int64 assetId = 1; // 功能卡/门票 资产 id
  int64 assetNum = 2; // 需要的资产数量
}

message QueryAlternateRsp {
  message Reward {
    string rewardIcon = 1; // 奖励图标
    string rewardName = 2; // 奖励名称
    int64 rewardNum = 3; // 奖励数量
  }

  string assetIcon = 1; // 资产图标
  string assetName = 2; // 资产名称
  int64 assetNum = 3; // 资产数量
  string alternateName = 4; // 代替品名称
  int64 alternateNum = 5; // 替代品数量
  string ticket = 6; // 兑换回传
  repeated Reward rewards = 7; // 加赠列表
  string exchangeRate = 8; // 兑换比例
  bool enableAlternate = 9; // 是否勾选
}

message QueryPreferenceReq {}

message QueryPreferenceRsp {
  // key: "alternate_222_224" value: "0", "1" // K 豆兑换游乐券
  // key: "alternate_263_224" value: "0", "1" // K 豆兑换功能卡
  map<string, string> preference = 1;
}

message UpdatePreferenceReq {
  string key = 1;
  string value = 2;
}

message UpdatePreferenceRsp {}

message ExchangeReq {
  string ticket = 1;
  bool setPreference = 2;
}

message ExchangeRsp {}
