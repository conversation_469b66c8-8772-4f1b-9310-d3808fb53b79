syntax = "proto3";

package interactive_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game";

import "pb/interactive_game/common/common.proto";

service RoomAdmin {
  // 创建房间
  rpc CreateRoom(CreateRoomReq) returns (CreateRoomRsp);
  // 销毁房间
  rpc DestroyRoom(DestroyRoomReq) returns (DestroyRoomRsp);
  // 查询游戏配置
  rpc QueryGameConfig(QueryGameConfigReq) returns (QueryGameConfigRsp);
  // 查询游戏房间
  rpc QueryRoom(QueryRoomReq) returns (QueryRoomRsp);
  // 退房
  rpc LeaveRoom(LeaveRoomReq) returns (LeaveRoomRsp);
  // 创建房间校验
  rpc CreateRoomCheck(CreateRoomCheckReq) returns (CreateRoomCheckRsp);
  // 重新加载房间
  rpc ReloadRoom(ReloadRoomReq) returns (ReloadRoomRsp);
  // 预加入房间
  rpc PreJoinRoom(PreJoinRoomReq) returns (PreJoinRoomRsp);
  // 校验免费次数
  rpc CheckFree(CheckFreeReq) returns (CheckFreeRsp);
  // 校验余额
  rpc CheckBalance(CheckBalanceReq) returns (CheckBalanceRsp);
  // 增加玩家 (只支持免费模式)
  rpc AddPlayers(AddPlayersReq) returns (AddPlayersRsp);
  // 校验付费模式
  rpc CheckPayConfig(CheckPayConfigReq) returns (CheckPayConfigRsp);
}

message CreateRoomReq {
  message Player {
    uint64 uid = 1;
  }
  string roomId = 1; // 房间 id
  uint64 ownerId = 2; // 房主 uid
  string gameAppId = 3; // 游戏 appId
  common.RoomType roomType = 4; // 房间类型
  common.CreateRoomConfig config = 5;
  // common.GameRoomConfig config = 5;
  map<string, string> reportExtend = 6; // 上报扩展字段
  uint64 creatorId = 7; // 创建者 uid
  map<string, string> gameExtend = 8; // 游戏扩展字段
  common.PlatformInfo platformInfo = 9; // 平台信息
  // string mergeTaskId = 10;
  // bool match = 11;
  // int32 createSource = 12; // 平台来源
  map<string, string> createGameConfigs = 13; // 创建游戏透传配置
  // repeated Player players = 14; // 默认玩家
  bool createOnlyNoExists = 15; // 仅不存在时创建
  string roomTitle = 16;
  bool ignoreInsufficientBalance = 17;
}

message CreateRoomRsp {
  string gameUrl = 1; // 游戏链接
  string gameRoomId = 2;
  string gameAppId = 3;
  int32 code = 4;
  string message = 5;
  string modeName = 6;
  int64 payDifference = 7;
}

message DestroyRoomReq {
  string roomId = 1;
  bool graceful = 2;
}

message DestroyRoomRsp {
  string gameRoomId = 1;
}

message QueryGameConfigReq {
  repeated string gameAppIds = 1; // 游戏 appId
  bool queryAll = 2; // 查询所有
}

message QueryGameConfigRsp {
  message ModeConfig {
    string modeId = 1;
    string name = 2; // 名称
    common.GameRoomMakeUpConfig makeUpConfig = 3; // 组局配置
    int64 assetId = 4;
    repeated uint32 directIds = 5; // 定向id
  }
  message ModeConfigs {
    repeated ModeConfig configs = 1;
  }
  message AppConfig {
    repeated string useFreePayModeIds = 1;
    repeated string priorityPayModeId = 2;
  }
  map<string, ModeConfigs> modes = 1; // 游戏 appId -> 模式配置
  map<string, AppConfig> apps = 2; // 游戏 appId -> 游戏配置
}

message QueryRoomReq {
  repeated string roomIds = 1; // 最多 50 个
}

message QueryRoomRsp {
  message RoomConfig {
    string modeId = 1;
    common.JoinMode joinMode = 2;
    common.GameRoomPayConfig payConfig = 3;
    common.GameRoomMakeUpConfig makeUpConfig = 4;
  }
  message Player {
    uint32 index = 1; // 座位号
    uint64 uid = 2;
  }
  message Room {
    common.RoomStatus status = 1;
    string gameAppId = 2;
    uint64 ownerId = 3;
    repeated Player players = 4;
    string roundId = 5;
    common.PlatformInfo platformInfo = 6;
    RoomConfig config = 7;
    common.RoomType roomType = 8;
    int64 updateTime = 9; // 最后更新时间
    int64 matchTime = 10;
  }
  map<string, Room> rooms = 1; // roomId -> room
}

message LeaveRoomReq {
  string roomId = 1; // 房间 id
  uint64 uid = 2; // uid
}

message LeaveRoomRsp {}

message UpdateRoomStatusReq {
  enum StatusTransition {
    TransitionUnknown = 0; // 未知
    TransitionReadying = 1; // 准备中
    TransitionRunning = 2; // 进行中
    TransitionEnd = 3; // 已结束
  }
  message Player {
    uint32 index = 1; // 座位号
    uint64 uid = 2;
    bool eliminated = 3; // 被淘汰
  }
  string gameAppId = 1; // 游戏 appId
  string roomId = 2; // 房间 id
  StatusTransition status = 3; // 状态变更
  repeated Player players = 4; // 玩家
  common.EventType eventType = 5; // 事件类型
  uint64 uid = 6; // 事件触发 uid
  bool kickOut = 7; // 是否被踢 (eventType == EventLeave)
}

message UpdateRoomStatusRsp {}

message CreateRoomCheckReq {
  string roomId = 1; // 房间 id
  uint64 ownerId = 2; // 房主 uid
  string gameAppId = 3; // 游戏 appId
  common.RoomType roomType = 4; // 房间类型
  common.CreateRoomConfig config = 5;
  // common.GameRoomConfig config = 5;
  uint64 creatorId = 6; // 创建者 uid
}

message CreateRoomCheckRsp {
  int32 code = 1;
  string message = 2;
  uint32 balance = 3;
}

message ReloadRoomReq {
  string roomId = 1;
}

message ReloadRoomRsp {
  string gameUrl = 1; // 游戏链接
  string gameRoomId = 2;
  string gameAppId = 3;
  int32 code = 4;
  string message = 5;
  string modeName = 6;
}

message PreJoinRoomReq {
  string roomId = 1;
  uint64 uid = 2;
}

message PreJoinRoomRsp {
  common.RoomStatus status = 1;
  string modeId = 2;
  string payModeId = 3;
  string gameAppId = 4;
}

message CheckFreeReq {
  uint64 uid = 1; // 房主 uid
  string gameAppId = 2;
  string payModeId = 3;
  common.RoomType roomType = 4; // 房间类型
  common.GameRoomPayConfig payConfig = 5;
}

message CheckFreeRsp {
  int64 balance = 1;
}

message CheckBalanceReq {
  uint64 uid = 1; // 房主 uid
  string gameAppId = 2;
  common.RoomType roomType = 3; // 房间类型
  common.GameRoomPayConfig payConfig = 4; // 付费配置
}

message CheckBalanceRsp {
  int32 code = 1;
  string message = 2;
  int64 payDifference = 3;
}

message AddPlayersReq {
  message Player {
    uint64 uid = 2;
    bool robot = 3;
    int64 index = 4; // 座位号 0 自动选择
  }

  string roomId = 1;
  repeated Player players = 2;
}

message AddPlayersRsp {
  repeated uint64 uids = 2; // 成功加入的 uid
}

message CheckPayConfigReq {
  uint64 uid = 1;
  string gameAppId = 2;
  common.RoomType roomType = 3; // 房间类型
  repeated common.GameRoomPayConfig payConfigs = 4;
}

message CheckPayConfigRsp {
  message Result {
    int64 shortfall = 1;
    bool useFree = 2;
    bool useAlternate = 3;
    bool alternateEnough = 4;
    int64 freeTimes = 5;
  }
  map<string, Result> results = 1; // payModeId -> result
}

message PendingRoomNotiyReq {
  message Player {
    uint32 index = 1; // 座位号
    uint64 uid = 2;
    bool eliminated = 3; // 被淘汰
    bool robot = 4;
    int64 timestamp = 5; // 玩家入座时间
  }

  string gameAppId = 1; // 游戏 appId
  string roomId = 2; // 房间 id
  repeated Player players = 3; // 当前玩家信息
  int64 createTime = 4; // 房间创建时间
}

message PendingRoomNotiyRsp {}
