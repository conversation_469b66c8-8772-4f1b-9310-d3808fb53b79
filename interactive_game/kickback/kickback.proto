syntax = "proto3";

package interactive_game_kickback;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game/kickback";

import "pb/interactive_game/common/common.proto";

service Kickback {
  // 转入
  rpc TransferIn(TransferInReq) returns (TransferInRsp);
}

message TransferInReq {
  string appId = 1;
  interactive_game.common.PayMode payMode = 2;
  int64 value = 3;
  string transactionId = 4;
}

message TransferInRsp {
}
