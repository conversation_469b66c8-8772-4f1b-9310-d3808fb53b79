syntax = "proto3";

package interactive_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game";

service SettlementApi {
  //  查询结算结果
  rpc SettlementResult(SettlementResultReq) returns (SettlementResultRsp);
}

message SettlementResultReq {
  string roundId = 1;  // 场次 id
  string passback = 2; // 分页用
}

message SettlementResultRsp {
  // 段位结算信息
  message SegmentResult {
    int64 score = 1;        // 当前获得积分(有正负数情况)
    int64 tScore = 2;       // 总积分
    string segName = 3;     // 段位名(例如:白银II)
    bool hasEvent = 4;      // 是否有段位事件
    string segIcon = 5;     // 段位Icon
    string avatarFrame = 6; // 段位头像框
  }
  message Result {
    string openId = 1;
    uint32 rank = 2;
    int64 experience = 3;
    int64 score = 4;        // 得分
    int64 win = 5;          // 赢得的货币/鲜花
    string avatar = 6;      // 头像
    string nick = 7;        // 昵称
    uint32 followed = 8;    // 关注1，没关注0
    uint32 seat = 9;        // 座位号
    string extra = 10;      // 额外透传，预留
    uint32 team = 11;       // 组队号
    SegmentResult seg = 12; // 段位信息
  }
  int32 payMode = 1; // 0免费场/1货币场/2鲜花场
  string prizeIcon = 2; // 获奖奖励的图标（如果是鲜花则是鲜花的图标）
  repeated Result results = 3; // 各玩家游戏结果
  Result hostResult = 4;       // 用户自己的获奖结果
  string passback = 5;         // 分页用
  bool hasMore = 6;            // 分页用
  string prizeName = 7;        // 奖励名称
  int32 settlementMode = 8;    // 0单人/1组队
  uint32 playerNum = 9;        // 最大玩家数
}
