syntax = "proto3";

package interactive_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game";

import "pb/interactive_game/common/common.proto";

service PublicSquare {
  // 房间列表
  rpc RoomList(RoomListReq) returns (RoomListRsp);
  // 快速匹配
  rpc QuickMatch(QuickMatchReq) returns (QuickMatchRsp);
}

message RoomListReq {
  string gameAppId = 1;
  string passback = 2;
  string modeId = 3; // 根据游戏模式筛选
  common.GameRoomPayConfig payConf = 4; // 付费参数
  string openId = 5;
}

message RoomListRsp {
  message Player {
    string openId = 1;
  }
  message Room {
    common.RoomStatus status = 1;
    string modeId = 2;
    repeated Player players = 3;
    uint32 maxPlayers = 4;
    common.JoinMode joinMode = 5;
    common.GameRoomPayConfig payConf = 6;
    string gameAppId = 7;
    string roomId = 8;
    common.RoomType roomType = 9;
  }
  repeated Room rooms = 1;
  bool hasMore = 2;
  string passback = 3;
}

message QuickMatchReq {
  string gameAppId = 1;
  string modeId = 2;
  common.GameRoomPayConfig payConf = 3; // 付费参数
  string openId = 4;
}

message QuickMatchRsp {
  string roomId = 1;
}
