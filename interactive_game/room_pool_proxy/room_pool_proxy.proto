syntax = "proto3";

package interactive_game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/interactive_game/room_pool_proxy";

import "pb/interactive_game/common/common.proto";

service RoomPoolProxy {
  // 玩家匹配
  rpc PlayerMatch(PlayerMatchReq) returns (PlayerMatchRsp);
  // 玩家匹配轮询
  rpc PlayerMatchPoll(PlayerMatchPollReq) returns (PlayerMatchPollRsp);
  // 匹配成功
  rpc MatchSuccess(MatchSuccessReq) returns (MatchSuccessRsp);
  // 匹配玩家池
  rpc ListMatchRoom(ListMatchRoomReq) returns (ListMatchRoomRsp);
  // 玩家取消匹配
  rpc PlayerCancelMatch(PlayerCancelMatchReq) returns (PlayerCancelMatchRsp);
  // 房间列表
  rpc ListRoom(ListRoomReq) returns (ListRoomRsp);
  // 匹配历史
  rpc PlayerMatchIntention(PlayerMatchIntentionReq) returns (PlayerMatchIntentionRsp);
  // 玩家暂停匹配
  rpc PlayerPauseMatch(PlayerPauseMatchReq) returns (PlayerPauseMatchRsp);
  // 玩家重新设置匹配
  rpc PlayerResetMatch(PlayerResetMatchReq) returns (PlayerResetMatchRsp);
  // 最大匹配模式
  rpc MaxMatchingMode(MaxMatchingModeReq) returns (MaxMatchingModeRsp);
  // 最优模式
  rpc OptimalIntention(OptimalIntentionReq) returns (OptimalIntentionRsp);
}

message PlayerMatchReq {
  uint64 uid = 1;
  string appId = 2;
  repeated interactive_game.common.ConcreteMatchIntention intentions = 3;
  bool saveIntention = 4;
  bool replaceIntention = 5;
}

message PlayerMatchRsp {
  string passback = 1;
}

message PlayerMatchPollReq {
  uint64 uid = 1;
  string passback = 2;
}

message PlayerMatchPollRsp {
  string passback = 1;
  string roomId = 2;
  bool backup = 3;
  string modeId = 4;
  string payModeId = 5;
  string appId = 6;
  int32 matchSource = 7;
  map<string, string> extend = 8;
}

message MatchSuccessReq {
  message MatchResult {
    string roomId = 1;
    int32 roomType = 2;
    repeated uint64 uids = 3;
    string appId = 4;
    string modeId = 5;
    string payModeId = 6;
    map<string, string> extend = 7;
  }
  repeated MatchResult results = 1;
}

message MatchSuccessRsp {}

message ListMatchRoomReq {
  string appId = 1;
  common.AppMatchType matchType = 2;
}

message ListMatchRoomRsp {
  message Player {
    uint64 uid = 1;
  }
  message RoomConfig {
    string modeId = 1;
    common.GameRoomPayConfig payConfig = 2;
    common.GameRoomMakeUpConfig makeUpConfig = 3;
  }
  message MatchRoom {
    common.RoomStatus status = 1;
    uint64 owner = 2;
    string gameAppId = 3;
    RoomConfig config = 4;
    common.RoomType roomType = 5;
    int64 updateTime = 6;
    int64 matchTime = 7;
    string roomId = 8;
    repeated Player players = 9;
    repeated RoomConfig intentions = 10;
  }
  repeated MatchRoom rooms = 1;
}

message PlayerCancelMatchReq {
  uint64 uid = 1;
  string passback = 2;
}

message PlayerCancelMatchRsp {}

message ListRoomReq {
  string gameAppId = 1;
  string passback = 2;
  string modeId = 3;
}

message ListRoomRsp {
  message Player {
    uint64 uid = 1;
  }
  message Room {
    string roomId = 1;
    repeated Player players = 2;
    common.RoomStatus status = 3;
    common.GameRoomMakeUpConfig makeUpConfig = 4;
    common.GameRoomPayConfig payConfig = 5;
    common.RoomType roomType = 6;
  }
  repeated Room rooms = 1;
  bool hasMore = 2;
  string passback = 3;
}

message PlayerMatchIntentionReq {
  string gameAppId = 1;
  uint64 uid = 2;
}

message PlayerMatchIntentionRsp {
  repeated common.MatchIntention intentions = 1;
}

message PlayerPauseMatchReq {
  uint64 uid = 1;
  string passback = 2;
}

message PlayerPauseMatchRsp {
}

message PlayerResetMatchReq {
  uint64 uid = 1;
  string passback = 2;
  repeated interactive_game.common.ConcreteMatchIntention intentions = 3;
}

message PlayerResetMatchRsp {
}

message MaxMatchingModeReq {
  string gameAppId = 1;
}

message MaxMatchingModeRsp {
  string modeId = 1;
}

message OptimalIntentionReq {
  string gameAppId = 1;
  repeated interactive_game.common.ConcreteMatchIntention intentions = 2;
}

message OptimalIntentionRsp {
  interactive_game.common.ConcreteMatchIntention intention = 1;
}
