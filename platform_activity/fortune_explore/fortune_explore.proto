syntax = "proto3";

package fortune_explore;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/fortune_explore";

import "pb/adapter_unified_assets/callback/callback.proto";
import "pb/adapter_unified_assets/adapter_unified_assets/common.proto";

// 福禄探秘
service FortuneExploreApi {
  // 获取葫芦列表
  rpc GetGourdList(GetGourdListReq) returns (GetGourdListRsp);
  // 购买葫芦
  rpc PayGourd(PayGourdReq) returns (PayGourdRsp);
  // 查询获奖记录
  rpc QueryRewardRecord(QueryRewardRecordReq) returns (QueryRewardRecordRsp);
  // 付费成功回调
  rpc PayGourdCallback(callback.OrderShipmentReq) returns (callback.OrderShipmentRsp);
  // 配置同步
  rpc ConfigSync(ConfigSyncReq) returns (ConfigSyncRsp);
  // 中台发福利礼包回调检查
  rpc CallbackCheckSend(callback.GiftPackageBusinessCheckSendReq) returns (callback.GiftPackageBusinessCheckSendRsp);
}

message GetGourdListReq {
  uint32 activityId = 1;
  int32 round = 2;
  string uid = 3;
}

message GetGourdListRsp {
  UserExploreShowData displayInfo = 1;
  uint32 unitPrice = 2; //单个抽取价格
  uint32 startTime = 3; //开始时间
  uint32 endTime = 4; //结束时间
  bool hasNext = 5; //是否解锁下一轮
  bool isRebate = 8; //是否返利，true时有返利，使用临时背包
  string rulePage = 9; //规则页链接
}

message UserExploreShowData {
  uint32 nowRound = 1;
  ExploreRound exploreInfo = 2;
  JadeRingInfo jadeRingInfo = 3;
  UserStatusInfo userStatus = 4;
}

message UserStatusInfo {
  bool openNextRound = 1;
  bool getJadeRingReward = 2;
  uint32 openJadeRingNum = 3;
  bool roundCut = 4;
  string rewardLocation = 5;
  uint32 jadeRingRewardID = 6;
}

message UserExploreData {
  uint32 nowRound = 1;
  repeated ExploreRound exploreList = 2;
  JadeRingInfo jadeRingInfo = 3;
  repeated RecordItem recordInfo = 4;
  uint32 roundCutPos = 5;
  map<string, string> orderIDMap = 6; // 扣费订单信息
}

message JadeRingInfo {
  uint32 collectNum = 1;
  uint32 targetNum = 2;
  uint32 probabilityUp = 3;
  RewardInfo rewardInfo = 4;
  uint32 userDrawInterval = 5;
  uint32 welfareId = 6;
  uint32 alreadyRewardCount = 7;
}

enum GiftLevel {
  Normal = 0;
  R = 1;
  SR = 2;
  SSR = 3;
}

message RewardInfo {
  string pic = 1;
  string name = 2;
  uint32 price = 3;
  uint32 count = 4;
  int32 giftLevel = 5; // 枚举 GiftLevel
  string showTag = 6; // 无价值道具 price=0 时的显示标签
}

message ExploreRound {
  repeated GourdInfo gourdList = 1;
}

message GourdInfo {
  bool isOpen = 1;
  uint32 welfareId = 2;
  RewardInfo rewardInfo = 3;
  uint32 rewardTime = 4;

}

message PayGourdReq {
  string appId = 1;
  uint32 activityId = 2; // 本活动的标识ID
  adapter_unified_assets.PayApp payApp = 3; // 支付app
  int32 round = 4; // 付费轮次
  repeated uint32 payIndex = 5; // 购买葫芦索引
  string payUserID = 6; //付费用户
  uint32  payAmount = 7; //总付费数
  string recvUserID = 8; //被打赏者
  string orderID = 9; //订单id，幂等使用
  adapter_unified_assets.PaySceneInfo pay_scene_info = 10;  // 扣费场景信息
  uint32 roomType = 11;
}

message PayGourdRsp {
  string errMsg = 1;
  UserExploreShowData displayInfo = 2;
}

message QueryRewardRecordReq {
  uint32 activityId = 1;
  string userID = 2;
  uint32 pageSize = 3; //页大小 默认10
  uint32 passBack = 4;
}

message QueryRewardRecordRsp {
  repeated RecordItem info = 1;
  bool hasMore = 2;
  uint32 passBack = 3;
}

enum RecordRewardType {
  Gourd = 0;
  JadeRing = 1;
}

message RecordItem {
  RewardInfo rewardInfo = 1;
  uint32 welfareId = 2;
  uint32 rewardTime = 3;
  int32 rewardType = 4; // 枚举 RecordRewardType
  string orderID = 5; // 获取奖励的订单号，对账使用
}

message ConfigSyncReq {
  uint32 id = 1;
  uint32 price = 2; //抽取价格
  string gourdList = 3; //葫芦配置
  string jadeRingList = 4; //玉环配置
  string startTime = 5; //开始时间
  string endTime = 6; //结束时间
  string status = 7; //上架状态
  string isRebate = 8; //是否返利 0否 1是
  string rulePage = 9; //规则页链接
}

message FortuneExploreConf {
  uint32 id = 1;
  uint32 price = 2; //抽取价格
  repeated GourdConf gourdList = 3; //葫芦配置
  JadeRingConf JadeRingConf = 4; //玉环配置
  uint32 startTime = 5; //开始时间
  uint32 endTime = 6; //结束时间
  bool status = 7; //上架状态
  bool isRebate = 8; //是否返利
  string rulePage = 9; //规则页链接
}

message GourdConf {
  repeated GourdRewardConf rewardList = 1;
  uint32 gourdNum = 2;
  uint32 beginStage = 3;
  uint32 endStage = 4;
}

message GourdRewardConf {
  uint32 welfareID = 1;
  uint32 probability = 2; //抽取概率
  uint32 giftLevel = 3;
}

message JadeRingConf {
  repeated JadeRingRewardConf rewardList = 1;
  uint32 drawInterval = 2;
  uint32 targetNum = 3;
  bool sendRoomMsg = 4;
  repeated JadeRingWelfareConf welfareList = 5;
}

message JadeRingRewardConf {
  uint32 beginStage = 1;
  uint32 endStage = 2;
  uint32 probability = 3; //抽取概率
}

message JadeRingWelfareConf {
  uint32 welfareID = 1;
  string rewardLocation = 2;
}

message ConfigSyncRsp {
  uint32 retCode = 1; // 错误码
  string retMsg = 2; // 错误提示语
}
