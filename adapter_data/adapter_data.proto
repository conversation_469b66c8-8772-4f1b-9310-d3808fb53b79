syntax = "proto3";

package adapter_data;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/adapter_data";

import "pb/event/event.proto";

// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
service AdapterData {
  // 数据上报
  rpc TDBankReport(TDBankReportReq) returns (TDBankReportRsp);
  // 数据上报
  rpc DataReport(DataReportReq) returns (DataReportRsp);
  // kb 写上报
  rpc QzaReport(QzaReportReq) returns (QzaReportRsp);
  // 上报事件
  rpc ReportEvent(ReportEventReq) returns (ReportEventRsp);
  // 上报 IDC DC表; 上报公有云DC不可用该接口
  rpc DCReport(DCReportReq) returns (DCReportRsp);
  // 配置同步
  rpc ConfigSync(ConfigSyncReq) returns (ConfigSyncRsp);
  // 排行榜
  rpc NoteRank(NoteRankReq) returns (NoteRankRsp);
  // 排行榜 周
  rpc NoteWeekRank(NoteWeekRankReq) returns (NoteWeekRankRsp);
  // 获取cw游戏信息
  rpc GetCwGameRankInfo(GetCwGameRankInfoReq) returns (GetCwGameRankInfoRsp);
  // 风控上报
  rpc RiskReport(RiskReportReq) returns (RiskReportRsp);
  // 三消对账 查询平台订单
  rpc CwsxQueryOrders(CwsxQueryOrdersReq) returns (CwsxQueryOrdersRsp);
  // 三消拉取推荐信息
  rpc CwsxGetSuggest(CwsxGetSuggestReq) returns (CwsxGetSuggestRsp);
  // 智研上报
  rpc ZhiYanReport(ZhiYanReportReq) returns (ZhiYanReportRsp);
  // 上报事件
  rpc ReportEventPlatform(ReportEventReq) returns (ReportEventRsp);
  // cwsx广告点位
  rpc CwsxAdSpotTimes(CwsxAdSpotTimesReq) returns (CwsxAdSpotTimesRsp);
  // 告警
  rpc AlarmProxy(AlarmProxyReq) returns (AlarmProxyRsp);
}

enum PetStatus {
  PetNotAdopt = 0; // 尚未被领取, 所有宠物的初始状态
  PetAdopted = 1; // 已领取孵化中, 用户点击收下宠物蛋后转这个状态
  PetHatched = 2; // 已孵化成功, 成功孵化后转这个状态
  PetIdle = 3; // 在小窝, 用户点击领养宠物/出门回家被查看后转这个状态
  PetOutWaiting = 4; // 点击出门之后, 宠物处于出门等待中状态
  PetOutting = 5; // 出门中, 宠物匹配到后转这个状态
  PetBack = 6; // 宠物刚刚回家
  PetDying = 7; // 濒临死亡状态
  PetRescuing = 8; // 听歌抢救状态
}

enum PetLiveStatus {
  PetLSNormal = 0; // 正常状态
  PetLSHungry = 1; // 饥饿状态
}

enum PetInteractiveStatus {
  PetDefault = 0; // 无
  PetReceivingNote = 1; // 收音符
  PetHunger = 2; // 饿了
  PetDirty = 3; // 脏了
  PetUnHappy = 4; // 不开心
  PetSick = 5; // 生病
}

message CwGameRankInfo {
  int64 friendPetId = 1; // 好友的宠物id
  string friendPetCover = 2; // 宠物头像
  PetStatus friendPetStatus = 3; // 宠物状态
  PetLiveStatus friendPetLiveStatus = 4; // 宠物饥饿状态
  PetInteractiveStatus petInteractiveStatus = 5; // 宠物互动操作状态
  int32 concerned = 6; // 是否关注 1:展示关注icon 2:展示微信 3:展示qq
}

message GetCwGameRankInfoReq {
  string app_id = 1; // app id
  string open_id = 2; // 主人态 open_id
  repeated string open_id_list = 3; // 榜单 open_id 列表,支持查自己
}

message GetCwGameRankInfoRsp {
  map<string, CwGameRankInfo> cwGameRankInfos = 1; // 用户资料 key 为 open id
}

message NoteRankItem {
  string openId = 1; // 用户id
  uint64 notes = 2; // 音符
  string nickname = 3; // 用户昵称
  string avatar = 4; // 用户头像
  CwGameRankInfo cwGameRankInfo = 5; // cw游戏排行榜信息
  int32 extraNote = 6; // 是否有额外音符可收
}

message SelfRankItem {
  int32 rank = 1; // 用户排名
  uint64 notes = 2; // 音符
  string nickname = 3; // 用户昵称
  string avatar = 4; // 用户头像
  string friendPetCover = 5; // 宠物头像
}

message NoteRankReq {
  string appId = 1;
  string openId = 2;
  int32 passback = 3; // 首次不传, 服务器返回什么, 传什么
}

message NoteRankRsp {
  repeated NoteRankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; // 再次翻页的时候需要把这个东西传过来
  int32 friendApiAuth = 4; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bingStatus = 5; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
  SelfRankItem selfRankItem = 6; // 自己的榜单信息
}

message NoteWeekRankReq {
  string appId = 1;
  string openId = 2;
  int32 passback = 3; // 首次不传, 服务器返回什么, 传什么
}

message NoteWeekRankRsp {
  repeated NoteRankItem list = 1; // 排行榜列表
  bool has_next = 2; // 是否还有下一页
  int32 passback = 3; // 再次翻页的时候需要把这个东西传过来
  int32 friendApiAuth = 4; // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
  int32 bingStatus = 5; // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
  SelfRankItem selfRankItem = 6; // 自己的榜单信息
}

message ConfigSyncReq {
  string key = 1;
  bytes data = 2;
  uint64 pid = 3;
  string openId = 4;
  string appId = 5;
}

message ConfigSyncRsp {}

message TDBankReportReq {
  // app id
  string app_id = 1;
  // open id
  string open_id = 2;
  // 表名
  string table = 3;
  // 服务名
  string program = 4;
  // 数据
  string message = 5;
  // 业务
  string buss_id = 6;
}

message TDBankReportRsp {}

message DataReportReq {
  // type 数据类型
  string type = 1;
  // data 数据
  string data = 2;
  // app id
  string app_id = 3;
  // open id
  string open_id = 4;
  // plat id
  uint64 plat_id = 5;
}

message DataReportRsp {}

// 优先级:
// 1. 当new_report_qmusic = true时, 只上报到music_dc01918(Q音表), 否则:
// 2. 当new_report = true时, 只上报到music_dc00109(K歌表), 否则:
// 3. 当new_report = false时, 上报到k币写上报表(k歌表)
message QzaReportReq {
  map<string, string> data = 1;
  bool new_report = 2; // 上报到music_dc00109, K歌表
  bool new_report_qmusic = 3; // 上报到music_dc01918,Q音表
}

message QzaReportRsp {}

message ReportEventReq {
  string app_id = 1; // app id
  string open_id = 2; // 单个用户事件的时候需要填写
  uint32 event_type = 3; // 事件类型 event.TmeEventType
  string event_id = 4; // 事件ID
  event.RoomBase room = 5; // 房间基础信息
  uint32 ts = 6; // 时间时间戳
  string to_open_id = 7; // 动作朝向 open_id
  string event_info = 10; // event_type对接的结构体 json.Marshal 之后的结果
  map<string, string> map_ext = 11; // 过滤信息 && 额外设置信息
  string uid = 12;
  string to_uid = 13;
}

message ReportEventRsp {}

message DCReportReq {
  string table_name = 1;
  repeated string data = 2;
}

message DCReportRsp {}

message RiskReportReq {
  message CommInfo {
    uint32 eAppType = 1;
    string strOrderId = 2;
    int64 lTimestamp = 3;
    string strTraceId = 4;
  }
  message UserInfo {
    string strUserId = 1;
    uint32 eUserType = 2;
  }
  message AssetInfo {
    string strAssetType = 1;
    string strAssetId = 2;
    int64 lAssetAmount = 3;
    int64 lAssetPrice = 4;
  }
  message SceneInfo {
    string strRoiId = 1;
    string strSceneId = 2;
  }
  message DeviceInfo {
    string strOsPlatform = 1;
    string strQimei36 = 2;
    string strUuid = 3;
    string strClientIp = 4;
  }
  message AssetOperationItem {
    CommInfo stCommInfo = 1;
    UserInfo stUserInfo = 2; // 填openid,会根据strAppId自动替换为uid
    AssetInfo stAssetInfo = 3;
    SceneInfo stSceneInfo = 4;
    DeviceInfo stDeviceInfo = 5;
    map<string, string> mapExtInfo = 6;
  }
  repeated AssetOperationItem vctIncomes = 1;
  repeated AssetOperationItem vctPayouts = 2;
  string strAppId = 3;
}

message RiskReportRsp {
  int32 iPlaceholder = 1;
}

message CwsxQueryOrdersReq {
  string appId = 1;
  int64 startTime = 2;
  int64 endTime = 3;
  int64 passback = 4;
}

message CwsxQueryOrdersRsp {
  string data = 1;
  int64 passback = 2;
  bool hasMore = 3;
}

message CwsxGetSuggestReq {
  enum OsType {
    OsTypeAndriod = 0;
    OsTypeIOS = 1;
  }
  message PropInfo {
    uint32 id = 1; // 道具id
    uint32 num = 2; // 道具数量
  }
  message Device {
    OsType os = 1; // 操作系统
  }
  message GameContext {
    int32 curStageId = 1; // 当前闯关id
    int32 curHealth = 2; // 当前体力值
    int64 curCoins = 3; // 当前金币数
    int32 maxNormalFloor = 4; // 界面上展示的第x关
    repeated PropInfo beforeProps = 5; // 战前使用道具
    map<string, string> maxExt = 6; // 扩展字段
  }
  string appId = 1;
  string openId = 2;
  GameContext gameContext = 3; // 闯关信息
  Device device = 4; // 设备信息
}

message CwsxGetSuggestRsp {
  enum DifficultyType {
    DifficultyEasy = 0;
    DifficultyNormal = 1;
    DifficultyHard = 2;
  }
  enum SuggestionType {
    ModifyDifficulty = 0; // 推荐类型-难度修改
    ModifyStep = 1; // 推荐类型-最后一步
    ModifyInlineStep = 2; // 推荐类型-局内推荐
  }
  message SuggestionValue {
    DifficultyType difficultyType = 1; // 难度
    float difficultyValue = 2; // 随机概率
  }
  message Suggestion {
    SuggestionType type = 1; // 推荐类型
    SuggestionValue value = 2; // 推荐内容
  }
  repeated Suggestion suggests = 1;
  string algorithmInfo = 2; // 上报用, 给到前端, 前端上报到 algorithm_id 字段
  string traceId = 3; // 上报用, 给到前端, 前端上报到trace_id字段
}

message ZhiYanReportReq {
  string appMark = 1; // 上报标识
  string metricGroup = 2; // 指标组
  map<string, double> reportIndex = 3; // 指标
  map<string, string> reportTag = 4; // 维度
  int64 reportTs = 5; // 上报的秒级时间
}

message ZhiYanReportRsp {}

message CwsxAdSpotTimesReq {
  string appId = 1;
  string openId = 2;
}

message CwsxAdSpotTimesRsp {
  map<int64, int64> spotTimes = 1;
}

message AlarmProxyReq {
  AlarmType alarmType = 1;
  string msg = 2;
}

message AlarmProxyRsp {}

enum AlarmType {
  AlarmTypeUnknow = 0;
  AlarmTypeMsg   = 1;
  AlarmTypeEmail = 2;
  AlarmTypePhone = 3;
}

message AlertReceiver{
  string strReceiver = 1;
}

message AlarmDataMsg {
  string strReceiver = 1;
  string strSender = 2;
  string title = 3;
  string strMsg = 4;
}

message AlarmDataEmail {
  string strSender = 1;
  repeated AlertReceiver vctReceiver = 2;
  repeated AlertReceiver vctCC = 3;
  string strTitle = 4;
  string strContent = 5;
  uint32 uBodyFormat = 6;
  string strAppendPath = 7;
}

message AlarmDataPhone {
  repeated AlertReceiver vctReceiver = 1;
  string strTaskName = 2;
  string strContent = 3;
  AlertReceiver stTransferReceiver = 4;
  uint32 uInterval = 5;
  uint32 uRecallTimes = 6;
}