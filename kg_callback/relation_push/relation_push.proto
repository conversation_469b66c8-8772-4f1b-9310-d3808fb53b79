syntax = "proto3";

package kg.kg_callback_relation_push;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/kg_callback/relation_push";

message MailInfo {
  string content = 1;
  map<string, string> attachment = 2;
}

message RelationNotifyInfoReq {
  string strAppId = 1;
  string strOpenId = 2; // 发送消息人 openId
  string strOwnerOpenId = 3; // 事件触发人 openId
  string passback = 4; // 事件回传信息
}

message RelationNotifyInfoRsp {
  MailInfo stMail = 1;
}
