syntax = "proto3";

package kg.kg_callback_game_center;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/kg_callback/game_center";

enum SceneID {
  SCENE_ID_NONE = 0;
  SCENE_ID_LIVE = 1001; // 直播
  SCENE_ID_KTV = 1002; // 歌房
  SCENE_ID_ASYNC = 1003; // 异步
  SCENE_ID_FRIENDS_KTV = 1004; // 好友歌房
  SCENE_ID_MULTI_KTV = 1005; // 多麦歌房
  SCENE_ID_PLAT_ASYNC = 1006; // 平台-异步
  SCENE_ID_TASK_CENTER = 1007; // 任务中心
  SCENE_ID_VISUAL_CENTER = 1008; // 可视化运营中台
  SCENE_ID_ASSET_CENTER = 1009; // 资产中心
  SCENE_ID_LIVE_CALL = 1010; // 直播连麦
  SCENE_ID_FEED_ADVERT = 1011; // 游戏大卡片
}

enum emRoomType {
  ROOM_TYPE_LIVE_VIDEO = 0; // 直播间 - 视频
  ROOM_TYPE_LIVE_AUDIO = 1; // 直播间 - 音频
  ROOM_TYPE_KTV_SIG = 2; // 欢唱歌房 - 单麦
  ROOM_TYPE_KTV_MUL = 3; // 欢唱歌房 - 多麦
  ROOM_TYPE_KTV_SOCIAL = 4; // 欢聚歌房 - 社交
  ROOM_TYPE_LIVE_RECORD = 5; // 录屏直播间
}

enum emSpecialStateType {
  SPECIAL_STATE_NONE = 0;
  SPECIAL_STATE_CRANIVAL = 1; // 狂欢时刻
}

enum ModuleStyle {
  SUB_MODULESTYLE_INVALID = 0; // 无效类型
  // 常用 Module不允许添加
  SUB_MODULESTYLE_TXT = 1; // 简易文本设置;
  // 自定义Module 最小值; 新增自定类型，朝后侧加
  SUB_MODULESTYLE_BUSI_MIN = 100000000;
}

message SceneParam {
  string strRoomID = 1; // 房间id
  int32 iRoomType = 2; // 房间类型
  int64 lAnchorID = 3; // 主播id
  map<string, string> mapExt = 4; // 扩展参数
  string strShowID = 5; // showId
  string strDeviceInfo = 6; // 设备信息
}

message PlayCorner {
  string strDoc = 1; // 文案
  string strColor = 2; // 文案底色
}

message PlayModule {
  string strStyle = 1; // 样式
  string strData = 2; // json格式
  string strSpecialState = 3; // 游戏特殊状态，json格式，参考SpecialState。
}

message ProfileEntry {
  string strDesc = 1; // 文案
  string strDescColor = 2; // 文案底色
  int32 iShowRedPoint = 3; // 是否展示红点, 1:展示, 0:不展示
}

// --- float frame start //
enum FloatFrameBubbleType {
  FLOAT_FRAME_BUBBLE_TYPE_NORMAL = 0; // 文本
  FLOAT_FRAME_BUBBLE_TYPE_TIME = 1; // 时间
}

message FloatFrameBubble {
  uint32 uBubbleType = 1;
  string strBubbleTxt = 2;
  uint32 uCur = 3; // 当前时间值 A， 显示(A/1000)/60: (A/1000)%60, 每秒 uCur+1000
  repeated string vecBgColor = 4; // 背景颜色; 如果是渐变色, 数组传两个值
}

message FloatFrameTime {
  uint32 uMax = 1; // 最大值
  uint32 uCur = 2; // 当前值
  uint32 iIsTime = 3; // 是否是时间戳 需要秒级实时更新; 每秒uCur+1000
}

enum FloatFrameDescType {
  FLOAT_FRAME_DESC_TYPE_TXT = 0; // 文本
  FLOAT_FRAME_DESC_TYPE_PIC_TXT = 1; // 图文
  FLOAT_FRAME_DESC_TYPE_TIME = 2; // 进度条
}

message FloatFrameDesc {
  uint32 uDescType = 1; // FloatFrameDescType
  string strTxt = 2;
  string strPic = 3;
  FloatFrameTime stTime = 4;
}

message FloatFrameItem {
  string strIcon = 1; // 展示icon
  string strJumpURL = 2; // 跳转连接
  FloatFrameBubble stBubble = 3; // 气泡
  string strTitle = 4; // 标题
  FloatFrameDesc stDesc = 5; // 描述
  uint32 uRefreshSkip = 6; // 下一次刷新时间间隔
  uint32 uIsHideDesc = 7; // 是否展示描述信息
  string strEventId = 8; // 事件Id
}

// --- float frame end //

// --- GameCenter 回调参数 Start -----//
message CustomizeCornerReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
}

message CustomizeCornerRsp {
  PlayCorner stCorner = 1;
}

message CustomizeModuleReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
}

message CustomizeModuleRsp {
  PlayModule stModule = 1;
}

message CustomizeProfileEntryReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
}

message CustomizeProfileEntryRsp {
  ProfileEntry stProfile = 1;
}

message CustomizeFloatFrameReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
  uint32 uGameID = 3;
  uint32 uTaskID = 4;
  int64 uUid = 5; // 用户uid
}

message CustomizeFloatFrameRsp {
  FloatFrameItem stInfo = 1;
}

// --- GameCenter 回调参数 End -----//

// --- 自定义Module Start --- //

// SUB_MODULESTYLE_TXT
message ModuleStyleTxt {
  uint32 iShowRedPoint = 1; // 1展示红点
  string strDoc = 2; // 文案
  string strDocColor = 3; // 文案颜色
}

message SpecialState {
  uint32 uStateType = 1; // emSpecialStateType
  uint32 uStartTs = 2; // 状态开始时间
  uint32 uEndTs = 3; // 状态结束时间
  string strDoc = 4; // 状态文案，如对与幸运牧场狂欢时刻，这里应该是【参与必得XX鲜花】
}

// --- 自定义Module End --- //
