syntax = "proto3";

package cwsx_game_invitation;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/cwsx_game_invitation/web";

import "pb/cwsx_game_invitation/common/common.proto";
import "pb/device/device.proto";

// cwsx 邀请新用户
service CwsxGameInvitationApi {
  // 活动入口
  rpc QueryStatus(QueryStatusReq) returns (QueryStatusRsp);
  // 活动详情
  rpc QueryDetail(QueryDetailReq) returns (QueryDetailRsp);
  // 邀请列表
  rpc InvitateList(InvitateListReq) returns (InvitateListRsp);
  // 发起邀请
  rpc Invitate(InvitateReq) returns (InvitateRsp);
  // 邀请记录
  rpc InvitateRecord(InvitateRecordReq) returns (InvitateRecordRsp);
  // 开始助力
  rpc StartHelping(StartHelpingReq) returns (StartHelpingRsp);
  // 助力成功
  rpc HelpingSucc(HelpingSuccReq) returns (HelpingSuccRsp);
  // 助力领奖-单条邀请
  rpc HelpingClaim(HelpingClaimReq) returns (HelpingClaimRsp);
  // 邀请领奖-限时活动
  rpc InvitateClaim(InvitateClaimReq) returns (InvitateClaimRsp);
}

// 活动状态
message QueryStatusReq {}

message QueryStatusRsp {
  message Unclaim {
    uint32 activityId = 1; // 限时活动id
    uint32 threshold = 2; // 阶梯值
  }
  uint32 activityId = 1; // 限时活动id
  uint32 status = 2; // 活动状态 见 ActivityStatus
  uint32 leftTime = 3; // 剩余时间
  repeated Unclaim unclaims = 4; // 限时未领取奖励
}

// 活动详情
message QueryDetailReq {}

message QueryDetailRsp {
  message ClaimProgress {
    uint32 status = 1; // 阶段状态 见 ClaimStatus
    uint32 threshold = 2; // 阶梯值
    repeated common.Item items = 3; // 奖励
  }
  uint32 activityId = 1; // 限时活动id
  uint32 status = 2; // 活动状态 见 ActivityStatus
  uint32 leftTime = 3; // 剩余时间
  string ruleImgUrl = 4; // 活动规则图片
  string title = 5; // 主标题
  string detailDesc = 6; // 详细说明
  repeated ClaimProgress progress = 7; // 领取进度
  uint32 succCount = 8; // 成功邀请个数
  uint32 unclaimCount = 9; // 未领取个数
  string invitateId = 10; // 邀请id inviterOpenId
}

// 邀请列表
message InvitateListReq {
  uint32 passback = 1; // 分页参数
}

message InvitateListRsp {
  repeated common.InvitateUser invitateList = 1; // 邀请列表
  uint32 passback = 2; // 分页参数
  bool hasNext = 3; // 是否有下一页
}

// 发起邀请 需要校验关系
message InvitateReq {
  string inviteeOpenId = 1; // 被邀请人openId
  string invitateId = 2; // 邀请id 标识表示助力的对象
}

message InvitateRsp {}

// 邀请记录
message InvitateRecordReq {
  uint32 passback = 1; // 分页参数
  uint32 activityId = 2; // 限时活动id
}

message InvitateRecordRsp {
  repeated common.InvitateRecord recordList = 1; // 记录列表
  uint32 passback = 2; // 分页参数
  bool hasNext = 3; // 是否有下一页
}

// 开始助力
message StartHelpingReq {
  string invitateId = 1; // 邀请id 标识表示助力的对象
}

message StartHelpingRsp {
  string message = 1; // 开始助力文案
}

// 助力成功
message HelpingSuccReq {}

message HelpingSuccRsp {
  string inviterOpenId = 1; // 邀请人openId
  string name = 2; // 姓名
  string avatar = 3; // 头像
  repeated common.Item items = 4; // 奖励
  uint64 uid = 5; // web上报用
}

// 日常助力领奖
message HelpingClaimReq {
  string inviteeOpenId = 1; // 被邀请人openId
  device.Device device = 2;
}

message HelpingClaimRsp {
  repeated common.Item items = 1; // 奖励
}

// 限时活动领奖
message InvitateClaimReq {
  message ClaimInfo {
    uint32 activityId = 1; // 限时活动id
    uint32 threshold = 2; // 阶梯值
  }
  repeated ClaimInfo infos = 1; // 领取奖励
  device.Device device = 2;
}

message InvitateClaimRsp {
  repeated common.Item items = 1; // 奖励
}
