syntax = "proto3";

package cwsx_game_invitation.common;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/cwsx_game_invitation/common";

enum PackageItemType {
    PackageItemTypeUnknown = 0;
    PackageItemTypeGame = 1;        // 游戏内物资
    PackageItemTypePlatform = 2;    // 平台内物资
    PackageItemTypeGameSpecial = 3; // 游戏内特殊物资
  }

message Item {
    uint32 itemId = 1;
    uint32 num = 2;
    uint32 type = 3; // 礼物类型 见 PackageItemType
}

enum ActivityStatus {
    ActivityStatusDefault = 0; // 默认 没有活动
    ActivityStatusOngoingDaily = 1; // 进行中 日常
    ActivityStatusOngoingLimit = 2; // 进行中 限时
    ActivityStatusUnclaimed = 3; // 待领取
}

enum ClaimStatus {
    ClaimStatusDefault = 0; // 默认 不能领取
    ClaimStatusUnclaimed = 1; // 待领取
    ClaimStatusClaimed = 2; // 已领取
}

enum InvitateStatus {
    InvitateStatusToBeInvited = 0; // 待邀请
    InvitateStatusAlreadyInvited = 1; // 已邀请
}

enum RecordStatus {
    RecordStatusUnclaim = 0; // 待领取
    RecordStatusClaimed = 1; // 已领取
}

message InvitateUser {
    string openId = 1; // 待邀请人 openId
    string name = 2; // 姓名
    string avatar = 3; // 头像
    uint32 status = 4; // 邀请状态 见 InvitateStatus
    repeated Item items = 5; // 奖励
    uint32 uid = 6; // web上报用
}

message InvitateRecord {
    string inviteeOpenId = 1; // 被邀请人openId
    string name = 2; // 姓名
    string avatar = 3; // 头像
    uint32 status = 4; // 邀请状态 见 RecordStatus
    repeated Item items = 5; // 奖励
    uint32 uid = 6; // web上报用
}
