package logic

import (
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_revenue"
	"kugou_adapter_service/service/structs/requst"
	"kugou_adapter_service/service/structs/response"
	"kugou_adapter_service/service/task_center"
)

const (
	PlatAssetTypeDefault        = 0 //默认
	PlatAssetTypeDiamond        = 1 //K歌钻石
	PlatAssetTypeDiamondGift    = 2 //K歌钻石礼物
	PlatAssetTypeCarrierDogCoin = 4 // 酷狗航母狗狗币
)

type AssetLogic struct{}

func (logic *AssetLogic) QueryAsset(ctx *gin.Context, body *requst.QueryAssetBody) (data *response.QueryAssetResp, err error) {
	logger.Warnf("查询用户资产【QueryAsset】，请求参数。body: %+v", body)
	// 根据游戏资产和游戏类型转发到不同实现
	if body.AssetType == int(adapter_revenue.AssetType_AssetTypeCarrierDogecoin) && body.GameMiddleInfo.GameAppid == "40000007" {
		tcSvr := new(task_center.TaskCenter)
		data, err = tcSvr.QueryAssetRpc(ctx, body)
		if err != nil {
			logger.Warn("QueryAssetRpc", zap.Error(err))
			return data, err
		}

		return data, err
	}

	// 兜底返回异常
	data = &response.QueryAssetResp{
		ErrorCode: 1,
		ErrorMsg:  "资产类型或游戏异常",
	}

	return data, err

}

func (logic *AssetLogic) SubAsset(ctx *gin.Context, body *requst.SubAssetBody) (data *response.ChangeAssetResp, err error) {
	logger.Warnf("扣减用户资产【SubAsset】，请求参数。body: %+v", body)
	// 先直接全部转发到任务中心，后面有其他实现在转到其他服务 todo
	tcSvr := new(task_center.TaskCenter)
	data, err = tcSvr.SubAssetRpc(ctx, body)
	if err != nil {
		logger.Warn("SubAssetRpc", zap.Error(err))
		return data, err
	}
	return data, err
}

func (logic *AssetLogic) AddAsset(ctx *gin.Context, body *requst.AddAssetBody) (data *response.ChangeAssetResp, err error) {
	logger.Warnf("增加用户资产【SubAsset】，请求参数。body: %+v", body)
	// 先直接全部转发到任务中心，后面有其他实现在转到其他服务 todo
	tcSvr := new(task_center.TaskCenter)
	data, err = tcSvr.AddAssetRpc(ctx, body)
	if err != nil {
		logger.Error("AddAssetRpc", zap.Error(err))
		return data, err
	}
	return data, err
}
