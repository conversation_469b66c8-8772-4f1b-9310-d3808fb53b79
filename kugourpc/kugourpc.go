package kugourpc

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"git.kugou.net/fxgo/core/logger"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"kugou_adapter_service/helper"
	"kugou_adapter_service/httpclient"
	"net"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

func init() {
	settings := httpclient.BasicHTTPSettings{
		UserAgent:        "fx_go",
		ConnectTimeout:   1 * time.Second,
		ReadWriteTimeout: 3 * time.Second,
		Gzip:             true,
		DumpBody:         true,
	}
	h := &http.Transport{
		TLSClientConfig: settings.TLSClientConfig,
		Proxy:           settings.Proxy,
		DialContext: (&net.Dialer{
			Timeout:   1 * time.Second,
			KeepAlive: 15 * time.Second,
		}).DialContext,
		MaxIdleConnsPerHost: 50,
		MaxConnsPerHost:     100,
		MaxIdleConns:        100,
		IdleConnTimeout:     90 * time.Second,
	}
	settings.Transport = h
	httpclient.SetDefaultSetting(settings)
}

func GetWithJson(kgrpcHost, url string, obj interface{}, header map[string]string) error {
	client := helper.GetApolloClient()
	url = client.GetStringValue("kgrpc.prefix", "http://zuultest.fxwork.kugou.com/kgrpc_proxy") + url
	request := httpclient.Get(url)
	request.Header("KgrpcHost", kgrpcHost)
	request.Header("x-rpc-send-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-send-timeout", 500)))
	request.Header("x-rpc-connect-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-connect-timeout", 500)))
	request.Header("x-rpc-read-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-read-timeout", 500)))
	request.Header("x-rpc-next-upstream-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-next-upstream-timeout", 500)))
	if header != nil && len(header) > 0 {
		for k, v := range header {
			request.Header(k, v)
		}
	}
	if client.GetBoolValue("kgrpc.debug", false) {
		err := request.Debug(true).ToJSON(obj)
		if err == nil {
			logger.Warnf("request: %v", string(request.DumpRequest()))
		}
	}
	err := request.ToJSON(obj)
	return err
}

func PostWithJson(kgrpcHost, url string, body interface{}, obj interface{}, header map[string]string) error {
	jsonBody := ""
	if buf, err := json.Marshal(body); err == nil {
		jsonBody = string(buf)
	}
	client := helper.GetApolloClient()
	url = client.GetStringValue("kgrpc.prefix", "http://zuultest.fxwork.kugou.com/kgrpc_proxy") + url
	logger.Warnf("通过kgrpc调用，调用请求。url: %v, kgrpcHost: %v, jsonBody: %v, header: %+v", url, kgrpcHost, jsonBody, header)

	request, err := httpclient.Post(url).JSONBody(body)
	if err != nil {
		logger.Warnf("通过kgrpc调用，构建请求失败。err: %v", err)
		return err
	}
	request.Header("KgrpcHost", kgrpcHost)
	request.Header("x-rpc-send-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-send-timeout", 500)))
	request.Header("x-rpc-connect-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-connect-timeout", 500)))
	request.Header("x-rpc-read-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-read-timeout", 500)))
	request.Header("x-rpc-next-upstream-timeout", strconv.Itoa(client.GetIntValue("kgrpc.x-rpc-next-upstream-timeout", 500)))
	if header != nil && len(header) > 0 {
		for k, v := range header {
			request.Header(k, v)
		}
	}
	if client.GetBoolValue("kgrpc.debug", false) {
		err = request.Debug(true).ToJSON(obj)
		if err == nil {
			logger.Warnf("request: %+v", string(request.DumpRequest()))
		}
	}

	err = request.ToJSON(obj)
	if err != nil {
		logger.Warnf("通过kgrpc调用，发起请求失败。err: %+v", err)
		return err
	}
	logger.Warnf("通过kgrpc调用，调用成功。obj: %+v", obj)
	return err
}

func EncodeArgs(args map[string]string) string {
	var buf bytes.Buffer
	buf.WriteString("?")

	count := 0
	total := len(args)

	for key, value := range args {
		buf.WriteString(key + "=" + value)
		count++

		if count < total {
			buf.WriteString("&")
		}
	}

	return buf.String()
}

func BuildSignature(serverKey string, params map[string]string, body interface{}) string {
	var builder strings.Builder
	builder.WriteString(serverKey)

	// Sorting the keys of the map to ensure consistent order
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Appending sorted parameters to the builder
	for _, k := range keys {
		builder.WriteString(k + "=" + params[k])
	}

	// If body is not nil, convert it to JSON and append
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err == nil { // Only append if marshaling succeeds
			builder.Write(bodyBytes)
		}
	}

	builder.WriteString(serverKey)

	logger.Warnf("raw: %v", builder.String())
	// Calculating MD5 hash
	hash := md5.Sum([]byte(builder.String()))
	return hex.EncodeToString(hash[:])
}

func BuildSignature256(serverKey string, params map[string]string, body interface{}) string {
	var builder strings.Builder
	builder.WriteString(serverKey)

	// Sorting the keys of the map to ensure consistent order
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Appending sorted parameters to the builder
	for _, k := range keys {
		builder.WriteString(k + "=" + params[k])
	}

	// If body is not nil, convert it to JSON and append
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if len(bodyBytes) > 256 {
			bodyBytes = bodyBytes[:256]
		}
		if err == nil { // Only append if marshaling succeeds
			builder.Write(bodyBytes)
		}
	}

	builder.WriteString(serverKey)

	// Calculating MD5 hash
	hash := md5.Sum([]byte(builder.String()))
	return hex.EncodeToString(hash[:])
}

func GetHeader(ginCtx *gin.Context) map[string]string {
	header := make(map[string]string)
	jsonString := helper.GetApolloClient().GetStringValue("kgprc.allowHeaders", "[]")
	var allowHeaders []string
	err := json.Unmarshal([]byte(jsonString), &allowHeaders)
	if err != nil {
		logger.Warnf("获取[kgprc.allowHeaders]配置，解析失败。err: %v", err)
		return header
	}
	for k, _ := range ginCtx.Request.Header {
		if lo.Contains(allowHeaders, strings.ToLower(k)) {
			header[strings.ToLower(k)] = ginCtx.Request.Header.Get(k)
		}
		if strings.HasPrefix(strings.ToLower(k), "x-gopen-") {
			header[strings.ToLower(k)] = ginCtx.Request.Header.Get(k)
		}
	}
	return header
}
