syntax = "proto3";

package stateful_router;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/stateful_router_selector";

// 选择有状态路由
service StatefulRouterSelector {
  rpc Select(SelectReq) returns (SelectRsp);
}

message SelectReq {
  string appId = 1; // appid
  string roomId = 2; // roomId
  string roundId = 3; // roundid
}

message SelectRsp {
  string ip = 1; // ip和label必有其一
  string label = 2; // ip和label必有其一
  int32 port = 3; // 端口
}
