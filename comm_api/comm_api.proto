syntax = "proto3";

package component.game;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/comm_api";

import "google/api/annotations.proto";

//与 web 交互
service CommApi {
  //批量获取用户PlatUid
  //
  //    下面的例子实际上请求不成功，因为header没鉴权字段等，只是为了展示请求结构体
  //    请求：
  //    curl -X POST -d '{"appid":"20000012", "openid_list":["p4Ddbit03fUWpwPL_U-snv8Xi3D73IPsaJq-Nekkq62x4DNrIrtGiqVE-ObZUFfK", "p2Dwb_tZ3gUqprPK_K-enG8rijDg3QPsaCq_N-kGqhyB3heVEuOb0rnxhyZyAgH4"]}'  https://testapigame.kg.qq.com/comm_api/batchOpenid2PlatUid
  //    返回：
  //    {"mapSucc":{"p2Dwb_tZ3gUqprPK_K-enG8rijDg3QPsaCq_N-kGqhyB3heVEuOb0rnxhyZyAgH4":{"platid":"2","uid":"*********"},"p4Ddbit03fUWpwPL_U-snv8Xi3D73IPsaJq-Nekkq62x4DNrIrtGiqVE-ObZUFfK":{"platid":"2","uid":"*********"}}}
  //

  rpc BatchOpenid2PlatUid(BatchOpenid2PlatUidReq) returns (BatchOpenid2PlatUidRsp) {
    option (google.api.http) = {
      post: "/comm_api/batchOpenid2PlatUid"
      body: "*"
    };
  }
}

// MOpenid2PlatUidReq multi openid转平台uid
message BatchOpenid2PlatUidReq {
  repeated string openid_list = 1; // 用户Openid,  批量大小限制100
}

// 参考了GOpenService的结构体
message BatchOpenid2PlatUidRsp {
  message Item {
    uint64 platid = 1; // 平台
    string uid = 2; // 平台对应用户
  }
  map<string, Item> mapSucc = 2; // 转换成功, key: openid  value: Item
}
