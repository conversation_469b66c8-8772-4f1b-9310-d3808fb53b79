syntax = "proto3";

package targeting;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/targeting";

service Targeting {
  // 检测是否命中
  rpc IsMatch(IsMatchReq) returns (IsMatchRsp);
}

message IsMatchReq {
  string openId = 1;
  repeated int64 targetingIds = 2;
  string appId = 3;
  map<string, string> cookies = 4; // cookies
  // extra 扩展字段, qua,device
  // eg:
  // map["qua"] = "xxx"
  // map["device"] = "xxx"
  map<string, string> extra = 5;
  string version = 6; // 客户端版本
  string platform = 7; // 平台 andriod、ios
}

message IsMatchRsp {
  message Result {
    bool match = 1;
  }
  map<int64, Result> results = 1;
}

message TargetingAbConfig {
  string bizId = 1;
  string channelId = 2;
  string moduleId = 3;
  string paramKey = 4;
  string paramValue = 5;
}

message TargetingTagStarConfig {
  int64 tagStarId = 1;
}

message TargetingRule {
  string ruleExpr = 1;
  int64 cacheTiem = 2; // 秒
  repeated string whiteList = 3; //白名单
  TargetingAbConfig abConfig = 4; // ab测试信息
  TargetingTagStarConfig tagStar = 5; // 星画分群
}
