syntax = "proto3";

package game_cwsx;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_cwsx/wechat_reconciliation";

service WechatReconciliation {
  // 对账接口
  rpc Reconciliation(ReconciliationReq) returns (ReconciliationRsp);
  // 同步订单接口
  rpc SyncOrders(SyncOrdersReq) returns (SyncOrdersRsp);
}

enum Platform {
  PlatformWx = 0; // wx小游戏版本
  PlatformApp = 1; // 独立app版本
}

message ReconciliationReq {
  repeated string date = 1; // 开始日期 20060102
}

message AbnormalOrderItem {
  string payeeOrderId = 1; // 收款方订单号
  int64 payeePrice = 2; // 收款方金额
  bool isPaid = 3; // 是否付费
  string shipperOrderId = 4; // 发货方订单号
  int64 shipperPrice = 5; // 发货方金额
  bool isDeliveried = 6; // 是否发货
}

message ReconciliationResult {
  uint32 totalVal = 1; 
  uint32 totalNum = 2;
  repeated AbnormalOrderItem fail = 3; // 失败
}

message ReconciliationRsp {
  map<string, ReconciliationResult> payeeResult = 1; // 收款方
  map<string, ReconciliationResult> shipperResult = 2; // 发货方
}

message SyncOrdersReq {
  repeated string date = 1; // 开始日期 20060102
}

message SyncOrdersRsp {}