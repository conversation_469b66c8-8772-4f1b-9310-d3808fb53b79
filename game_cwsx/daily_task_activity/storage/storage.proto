syntax = "proto3";

package game_cwsx_daily_task;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_cwsx/daily_task_activity/storage";

message ConfigReward {
  int64 pkgId = 1; // 礼包Id
  int64 probability = 2; // 概率
  int64 maxLimit = 3; // 最大值
  int64 minLimit = 4; // 最小值
  int64 newerMaxLimit = 5; // 最大值(新手引导期)
  int64 newerMinLimit = 6; // 最小值(新手引导期)
}

message ConfigNode {
  int64 id = 1; // 阶梯序号
  int64 cap = 2; // 阶梯长度
  repeated ConfigReward rewards = 3;
  string icon = 4; // 图标信息
  int64 capNewer = 5; // 新手阶梯长度(新手引导期)
}

message Config {
  int64 id = 1; // 配置Id
  repeated ConfigNode nodes = 2;
  string introLink = 3; // 规则介绍
}

message Configs {
  repeated Config data = 1;
  int64 updateTime = 2;
}

message UserAchieveLog {
  int64 nodeId = 1; // 节点Id
  int64 achieveTime = 2; // 达成时间
  int64 pkgId = 3; // 发奖的礼包Id
  string tranId = 4; // 发货幂等Id
  int64 num = 5; // 数量
}

message UserPlayLog {
  int64 maxFloorId = 1; // 界面上显示关卡数
  int64 time = 2; // 时间戳
}

// key: ${openId}_${dayTs}
// expire for one month
message User {
  int64 configId = 1; // 配置Id
  int64 initTime = 2; // 激活时间(从任务中心进来时激活)
  repeated UserPlayLog playLogs = 3; // 闯关纪录
  repeated UserAchieveLog achieveLog = 4; // 达成纪录
  int64 initLottery = 5; // 抽奖数值
}

// key: lucky_history
message LuckyHistory {
  message LuckyUser {
    int64 time = 1; // 时间
    string openId = 2; // openId
    int64 num = 3; // 数量
  }
  repeated LuckyUser users = 1; // 已达成且中了1w金币的用户
}
