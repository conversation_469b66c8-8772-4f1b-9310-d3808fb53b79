syntax = "proto3";

package game_cwsx_ranking;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_cwsx/ranking/webapi";

import "pb/game_api/game_api.proto";
import "pb/game_cwsx/inlet/inlet.proto";
import "pb/game_cwsx/ranking/common/common.proto";
import "pb/game_cwsx/xian_cwsx/dan/notify/notify.proto";

service WebApi {
  // 查询榜单
  rpc QueryRanking(QueryRankingReq) returns (QueryRankingRsp);
  // 查询配置
  rpc QueryConfig(QueryConfigReq) returns (QueryConfigRsp);
  // 查询领奖
  rpc QueryClaimStatus(QueryClaimStatusReq) returns (QueryClaimStatusRsp);
  // 领奖
  rpc Claim(ClaimReq) returns (ClaimRsp);
  // 查询用户的城市绑定信息
  rpc QueryBinding(QueryBindingReq) returns (QueryBindingRsp);
  // 弹窗查询
  rpc PopupState(inlet.ActivityStateReq) returns (inlet.ActivityStateRsp);
  // 刷新绑定
  rpc FlushBinding(FlushBindingReq) returns (FlushBindingRsp);
}

message Pagenation {
  string passback = 1; // 分页参数
  bool hasMore = 2; // 是否还有更多
}

message RewardPackage {
  repeated game_api.TreasureCard cards = 1; // 宝藏卡信息, 如果产生宝藏卡领取, 则放在这里
  repeated game_api.RewardItem rewards = 2; // 奖励信息
}

message Region {
  string regionName = 1; // 地区名称
  string regionCode = 2; // 地区代码
}

message RankingItem {
  string openId = 1; // openId
  string nick = 2; // 昵称
  string avatar = 3; // 头像
  string petIcon = 4; // 宠物头像
  string teamName = 5; // 战队名
  uint32 rank = 6; // 排名
  uint32 score = 7; // 分数
  RewardPackage reward = 8; // 奖励信息
  string uid = 9; // 平台uid
  uint32 private = 10; // 是否屏蔽个人主页
  int64 petId = 11; // 宠物id
  string encryptUid = 12;
  map<string, string> privilege = 13; // 特权信息
  xian_cwsx_dan.DanInfo dan_info = 14; // 玩家段位
}

enum ClaimStatus {
  ClaimStatusNone = 0; // 未参与
  ClaimStatusNoClaim = 1; // 达不到名次, 不能获得奖励
  ClaimStatusClaimed = 2; // 达到名次, 领取了奖励, 弹窗
}

message ClaimReq {}

message ClaimRsp {
  string roundId = 1;
  int64 claimTime = 2;
  ClaimStatus status = 3;
  int32 changed = 4; // 如果为1,则表示本次请求发生了变更,后续重复操作则返回changed=0
}

message QueryRankingReq {
  string passback = 1; // 透传回包中的passback参数,第一次不用传
  string reginCode = 2; // 地区码
  int64 timestamp = 3; // 时间戳
}

message QueryRankingRsp {
  Pagenation pagenation = 1; // 分页信息
  repeated RankingItem ranks = 2; // 榜单信息
  uint32 remainSec = 3; // 剩余时间
  string roundId = 4; // 轮次id
  RankingItem curUserRank = 5; // 当前用户的排名
  Region region = 6; // 当前城市
  uint32 bindType = 7; // ip绑定类型,0未绑定,1从profile取, 2从ip取, 3从兜底配置取
  string strRule = 8; // 规则图
}

message QueryConfigReq {
  string cacheVer = 1; // 前端回传本地缓存的版本号
}

// 前端本地缓存回包中的地区列表regions以及regionVersion
// 当地区映射未发生变化时,回包不会返回regions数据
message QueryConfigRsp {
  string regionVer = 1;
  repeated GeoRegion regions = 2;
  string curRegionCode = 3; // 当前用户所在的地区
}

message QueryClaimStatusReq {
  string roundId = 1; // 指定轮次, 不指定就是当前周
  string passback = 2; // 透传回包中的passback参数,第一次不用传
  string timestamp = 3; //优先判断roundId, 没有再通过timestamp, 兜底就是当前时间
}

message QueryClaimStatusRsp {
  Region region = 1; // 上周参与的地区
  uint32 score = 2; // 上周获得的分数
  Pagenation pagenation = 3; // 分页信息
  repeated RankingItem ranks = 4; // 榜单信息
  ClaimStatus status = 5; // 获奖状态, 只有等于ClaimStatusClaimed时才会返回ranks
  string roundId = 6; // 轮次id
  RankingItem curUserRank = 7; // 当前用户的排名
  uint32 weeks = 8; // 第几周
  string weekDate = 9; // 日期
}

message QueryBindingReq {
  string appId = 1;
  string openId = 2;
  int64 timestamp = 3; // 不传默认用当前时间
  string ip = 4; // 如果用户绑定过了,就用现有的,否则用这个ip走地区绑定流程
}

message QueryBindingRsp {
  Region region = 1; // 用户绑定的地区信息
}

message FlushBindingReq {}

message FlushBindingRsp {
  Region region = 1; // 用户绑定的地区信息
}
