syntax = "proto3";

package game_cwsx_ranking;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_cwsx/ranking/config";

// 名次奖励配置
message RankRewardConfig {
  repeated RankRewardConfigItem items = 1;
}

message RankRewardConfigItem {
  uint32 id = 1; // 配置Id
  uint32 from = 2; // 开始名次
  uint32 to = 3; // 结束名次
  uint32 packageId = 4; // 奖励包Id
}

message RegionMergeConfig {
  string regionCode = 1;
  string regionName = 2;
  uint32 merge = 3;
}

// 地区配置
message RegionConfigItem {
  uint32 id = 1; // 配置id
  uint32 beginWeek = 2; // 开始周数
  repeated RegionMergeConfig mergeConfig = 3; // 映射关系
  uint64 version = 4; // 同步版本
}

message RankRuleConfig {
  repeated RankRuleConfigItem items = 1;
}

message RankRuleConfigItem {
  uint32 id = 1; // 配置Id
  string RuleUrl = 2; // 规则
  uint64 begin = 3; // 开始时间 毫秒
  uint64 end = 4; // 结束时间 毫秒
}
