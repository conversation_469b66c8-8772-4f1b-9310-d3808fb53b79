syntax = "proto3";

package inlet;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/inlet";

import "pb/game_cwsx/inlet/inlet.proto";

// ActivityOptReq 活动配置 Req
message ActivityOptReq {
  InletNumber number = 1; // 入口编号
  int64 v = 2; // 版本号
}

// ActivityOptRsp 活动配置 Rsp
message ActivityOptRsp {
  int64 v = 1; // 版本号
  ActivityOpt opt = 2; // 活动配置
}

// ActivityOpt 活动配置 Rsp
message ActivityOpt {
  InletNumber number = 1; // 入口编号
  ManageSwitch switch = 2; // 总开关类型
  uint32 switch_device_type = 3; // 手机类型开关  安卓[1<<1]  |  ios[1<<2]
  int32 threshold = 4; // 关卡展示门槛
  int32 threshold_max_show = 5; // 关卡最大展示门槛 -1 则不使用
  ManageDurationType duration_type = 6; // 时间类型
  ManageWeekObj week_obj = 7; // 时间周期对象
}

// ManageSwitch 开关类型
enum ManageSwitch {
  ManageSwitch_OFF = 0; // 关闭
  ManageSwitch_Yes = 1; // 开启
  ManageSwitch_SwitchGm = 2; // 白名单开启
}

// ManageWeekObj 时间周期对象
message ManageWeekObj {
  // 周刷新
  int32 w_begin_week = 1; // 周几开始
  string w_begin_clock = 2; // 几点开始 00:00:00
  int32 w_duration = 3; // 持续时间(s)
  // 周每天刷新
  int32 wd_week = 4; // 活动执行周期 二进制占位
  string wd_begin_clock = 5; // 开始时间点 [00->23]
  string wd_end_clock = 6; // 结束时间点 [00->23]
  int32 wd_duration = 7; // 活动持续时间(s)
}
