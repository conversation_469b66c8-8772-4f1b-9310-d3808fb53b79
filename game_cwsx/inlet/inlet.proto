syntax = "proto3";

package inlet;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/inlet";

// InletListRsp 入口管理列表
message InletListRsp {
  repeated InletInfo left = 1; //  左边入口活动列表
  repeated InletInfo right = 2; // 右边入口活动列表
}

// InletInfo 信息
message InletInfo {
  int64 number = 1; // 入口活动编号=>InletNumber
  int64 duration_type = 2; // 持续时间类型=>InletDurationType
  int64 start_time = 3; // 开始时间
  int64 end_time = 4; // 结束时间
}

// InletOffReq 关闭活动
message InletOffReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
  string open_id = 2; // 用户ID
}

// InletOffRsp 关闭活动
message InletOffRsp {}

// InletOptSetReq 关闭活动
message InletOptSetReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
  repeated InletOptTime opt_time = 2; // 时间列表
  message InletOptTime {
    int64 start_time = 1; // 开始时间
    int64 end_time = 2; // 结束时间
  }
}

// InletSetRsp 关闭活动
message InletOptSetRsp {}

// InletStatusReq 查询活动状态
message InletStatusReq {
  repeated InletNumber number = 1; // 入口活动编号=>InletNumber
  string open_id = 2; // 用户的open_id
  PlatformType os = 3; // 设备类型
  int32 sceneId = 4; // 场景ID
  string userAgent = 5; // 透传ua
  string appId = 6; // appId
}

// InletSetRsp 查询活动状态
message InletStatusRsp {
  map<int64, bool> inlet_status = 2; // 入口活动编号[InletNumber]=>入口状态[true展示 false关闭]
}

// -------------
// -------------
// -------------
// 新入口方案
// -------------
// -------------
// -------------
// -------------

// 直接从宠物三消的manage服务过来，后期直接覆盖统一
// -------------
// 活动状态协议【入口服务请求活动】 开始
// -------------

// ActivityStateReq 查询活动状态请求
message ActivityStateReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
  string open_id = 2; // 用户的open_id
  PlatformType os = 3; // 设备类型
  int32 sceneId = 4; // 场景ID
  string userAgent = 5; // ua
  string appId = 6; // appid
  NeedDataType need_data = 7; // 需要具体数据
  int64 floor = 8; // 玩家当前关卡
  string cache_bind_key = 9; // 卢学彦 【荣耀奖牌赛】
  string team_id = 10; // 战队ID
}

// ActivityStateRsp 查询活动状态响应 下次请求仅在 在need_data=1的时候不判断next_req_time。入口服务也不记载data
message ActivityStateRsp {
  InletStatusType inlet_status = 1; // 入口状态 【1.0版本弃用】
  int64 next_req_time = 2; // 下次请求时间  -1永久保持历史状态
  int64 start_time = 3; // 活动开始时间
  int64 end_time = 4; // 活动结束时间
  bytes data = 5; // 当need_data=1时，返回具体活动的数据。
  string round = 6; // 轮次 刷新记录使用
  BuyType buy_status = 7; // 购买状态
  // -- 入口展示条件，下面的值拥有后，忽略上面的【 InletStatusType inlet_status = 1; // 入口状态】的状态
  ManageShowType show_type = 8; // 入口展示状态【决定是否入口展示】:在nextReqTime过期后重新加载
  PopupType popup_type = 9; // 弹窗的状态【决定弹窗的类型，在showType开启的时候才会有效】:在nextReqTime过期后重新加载
}

// -------------
// 活动状态协议【入口服务请求活动】： 结束
// -------------

// -------------
// 单独设置入口状态：【活动服务请求入口服务】 开始
// 比如场景：活动返回的`next_req_time`未过期前，需要重新设置入口状态【关闭、开启、或InletStatusType状态变化】。就需要接入该接口
// 比如活动：新手任务、签到等
// -------------

// ActivitySetReq 活动入口单独设置时间+状态 请求 详解看上面备注
message ActivitySetReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
  string open_id = 2; // 用户的open_id
  InletStatusType inlet_status = 3; // 入口状态  【1.0版本弃用】
  int64 next_req_time = 4; // 下次请求时间   -1永久保持历史状态
  int64 start_time = 5; // 活动开始时间
  int64 end_time = 6; // 活动结束时间
  string round = 7; // 轮次 刷新记录使用
  BuyType buy_status = 8; // 购买状态 弹窗使用
  // -- 入口展示条件，下面的值拥有后，忽略上面的【 InletStatusType inlet_status = 1; // 入口状态】的状态
  ManageShowType show_type = 9; // 入口展示状态【决定是否入口展示】
  PopupType popup_type = 10; // 弹窗的状态【决定弹窗的类型，在showType开启的时候才会有效】
}

// ActivitySetRsp  活动入口单独设置时间+状态
message ActivitySetRsp {}

// -------------
// 单独设置入口状态：【活动服务请求入口服务】 结束
// -------------

// -------------
// 活动设置弹窗完成【入口服务请求活动】： 开始
// -------------

// ActivityPopupReportReq 查询活动状态 请求
message ActivityPopupReportReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
}

// ActivityPopupReportRsp 查询活动状态 响应
message ActivityPopupReportRsp {}

// -------------
// 活动设置弹窗状态【入口服务请求活动】： 结束
// -------------

// InletNumber 增加枚举值
enum InletNumber {
  InletNumber_None = 0; // 无
  InletNumber_HonorMedalCompetition = 1; // 荣耀奖牌赛 @卢学彦
  InletNumber_AirplaneRace = 2 [deprecated = true]; // 飞机竞赛 @作废
  InletNumber_SuperColorfulLights = 3; // 超级彩灯 @上官冲
  InletNumber_TeamRudder = 4; // 战队淘金 @裴晓晨
  InletNumber_TeamCompetition = 5; // 战队竞赛  @作废
  InletNumber_BattlePass = 6; // 战令 @上官冲
  InletNumber_Fishing = 7; // 小猫钓鱼 @裴晓晨
  InletNumber_CuteRabbitParadise = 8; // 萌兔乐园 @卢学彦
  InletNumber_EndlessTreasures = 9; // 无尽宝藏 @陈航
  InletNumber_CheckIn = 10; // 签到 @上官冲
  InletNumber_LightingRush = 11; // 彩虹竞速 @裴晓晨
  InletNumber_SpecialDiscountPackage = 12; // 特惠礼包 @陈航
  InletNumber_NoviceChallengeEvent = 13; // 新手闯关活动 @陈航
  InletNumber_DragonsTreasure = 14; // 巨龙宝藏 @陈航
  InletNumber_InviteFriends = 15; // 邀请好友 @白龙斐
  InletNumber_ShareFriends = 16; // 分享好友 @上官冲
  InletNumber_Favorite = 17; // 收藏 @白龙斐
  InletNumber_Recharge = 18; // 宠物三消充值活动 @王国栋
  InletNumber_EveryDayReceive = 19; // 分天领取礼包 @王国栋
  InletNumber_DragonBoat = 20; // 龙舟竞赛 @曾润良
  InletNumber_DailyTask = 21; // 任务中心每日消除金币任务 @卢学彦
  InletNumber_Laba = 22; // 拉霸活动@车照
  InletNumber_Vip = 23; // VIP专有客服 @曾润良
  InletNumber_CwsxShop = 24; // 宠物三消商城入口 @裴晓晨
  InletNumber_GameHub = 25; // 微信游戏圈 @裴晓晨
  InletNumber_Announces = 26; // 公告 @白龙斐
  InletNumber_RescuePlants = 27; // 营救植物 @上官冲
  InletNumber_Dan = 28; // 段位赛 @车照
  InletNumber_Block = 29; // 俄罗斯方块 @裴晓晨
  // -------------

  // ------ 游戏中的单机小游戏 从200开始-------
  InletNumber_DecPop = 200; // 解密消除 @白龙斐
  // -------------

  // -------------
  // 下面是仅有弹窗无的枚举 Begin
  // -------------
  // 非入口的活动 增加枚举值， 1000 开头
  InletNumber_WeekRank = 1001; // 周排行榜 @上官冲
  InletNumber_StarActivity = 1002; // 明星活动 @裴晓晨
  InletNumber_PeakRaceBigRound = 1003; // 巅峰赛大轮次 @上官冲
  InletNumber_CollectTasks = 1004; // 收集任务 @上官冲
  InletNumber_PeakRaceSmallRound = 1005; // 巅峰赛小轮次 @上官冲
  InletNumber_Announce = 1006 [deprecated = true]; // @废弃 公告由 InletNumber_Announces(26号)代替
}

// InletDurationType 时间周期类型 【后期弃用】
enum InletDurationType {
  InletDurationType_DurationNone = 0; // 无
  InletDurationType_DurationAlways = 1; // 永久
  InletDurationType_DurationSingle = 2; // 单次展示
  InletDurationType_DurationDay = 3; // 天刷新
  InletDurationType_DurationWeek = 4; // 周刷新
  InletDurationType_DurationWeekDay = 5; // 周每天刷新
  InletDurationType_DurationCallback = 6; // 回调刷新
  InletDurationType_DurationSetSE = 7; // 特殊设置开始与结束时间
  InletDurationType_DurationMinute = 8; // 分钟刷新
}

// ManageShowType 查询活动状态响应
enum ManageShowType {
  ManageShowType_None = 0; // 无定义，继续保持老状态
  ManageShowType_Open = 1; // 入口展示
  ManageShowType_Close = 2; // 入口关闭
}

// 弹窗的类型 弹窗的类型状态 弹窗顺序由编号升序决策
enum PopupType {
  PopupType_None = 0; // 无
  PopupType_EndNoAward = 1001; // 结算期间：无领奖状态
  PopupType_EndHaveAward = 1002; // 结算期间：有领奖状态
  PopupType_Begin = 1003; // 活动开启弹窗
  PopupType_Buy = 1004; // 购买类型:展示时间
}

// BuyType 需要具体数据
enum BuyType {
  BuyType_None = 0; // 未购买
  BuyType_Yes = 1; // 已购买
}

// SceneType 场景
enum SceneType {
  SceneType_None = 0; // 无定义
  SceneType_OpenGame = 1; // 打开游戏
  SceneType_Settle = 2; // 结算游戏
}

// PlatformType 设备类型
enum PlatformType {
  PlatformUnknown = 0;
  PlatformAndroid = 1;
  PlatformIOS = 2;
}

// ManageDurationType 时间周期类型
enum ManageDurationType {
  ManageDurationType_DurationNone = 0; // 活动单独配置【周期类型不支持的活动，或者不使用此处的时间周期，直接选择活动独有设置即可】
  ManageDurationType_DurationAlways = 1; // 永久
  ManageDurationType_DurationSingle = 2; // 单次展示
  ManageDurationType_DurationDay = 3; // 天刷新
  ManageDurationType_DurationWeek = 4; // 周刷新
  ManageDurationType_DurationWeekDay = 5; // 周每天刷新
}

// InletStatusType 入口状态枚举 （活动入口加载的状态值、弹窗所用的状态值）
enum InletStatusType {
  InletStatusType_None = 0; // 无定义，继续保持老状态
  InletStatusType_Open = 1; // 活动开启中但未参与
  InletStatusType_OpenAndJoin = 2; // 活动开启，玩家参与中
  InletStatusType_EndHaveAward = 3; // 活动结束，但有奖可领：有待领取的奖品
  InletStatusType_EndReceivedAward = 4; // 活动结束，但有奖可领：全部奖品领取完成
  InletStatusType_EndNoAward = 5; // 活动结束，但无奖可领：参与后未达到领奖条件
  InletStatusType_EndNoJoin = 6; // 活动结束，且一直未参与
  InletStatusType_AbClose = 7; // Ab无法参与
  InletStatusType_OpenAndJoin_HasAward = 8; // 活动开启，玩家参与中：有待领取的奖品[特殊活动使用]
  InletStatusType_Notice = 9; // 预告期
}

// NeedDataType 需要具体数据
enum NeedDataType {
  NeedDataType_None = 0; // 不需要活动具体数据，仅返回状态即可
  NeedDataType_Yes = 1; // 需要具体数据
}

// PopupReportType 弹窗展示类型
enum PopupReportType {
  PopupReportType_None = 0; // 无
  PopupReportType_Open = 1; // 已展示
  PopupReportType_BREAK_BY_OTHER = 2; // 被别的打断 [达到上线、被玩家主动操作例如开启闯关]
  PopupReportType_LOAD_PREFAB_FAILD = 3; // 预制加载失败
  PopupReportType_LOAD_Fail = 4; // 前端未知原因未弹出
}
