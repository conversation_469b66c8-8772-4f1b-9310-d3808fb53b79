syntax = "proto3";

package game_cwsx_kingscup;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_cwsx/kings_cup/webapi";

import "pb/device/device.proto";
import "pb/game_api/game_api.proto";
import "pb/game_cwsx/inlet/inlet.proto";
import "pb/game_cwsx/kings_cup/common/common.proto";

service WebApi {
  rpc Query(WebQueryReq) returns (WebQueryRsp); // 查询状态
  rpc Claim(WebClaimReq) returns (WebClaimRsp); // 领取奖励
  rpc Ranks(WebRanksReq) returns (WebRanksRsp); // 榜单查询
  rpc ActivityState(inlet.ActivityStateReq) returns (inlet.ActivityStateRsp); // 入口查询
}

message WebQueryReq {
  string openId = 1; // 可以放在header里,不显式传
  string appId = 2; // 可以放在header里,不显式传
  uint32 stageId = 3; // 当前的闯关id
  string cachedBindKey = 4; // 前端本地缓存的bindKey, 如果后端发现缓存的bindKey和用户当前进行的bindKey不一致, 则返回当前上一轮的数据
}

message WebQueryRsp {
  string bindKey = 1; // 活动key
  uint32 cups = 2; // 杯子数
  uint32 rank = 3; // 实时排名
  uint32 remain = 4; // 剩余时间(s)
  UserActivityState state = 5; // 用户活动参与状态
  UserActivityRankState rankState = 6; // 是否达到榜单领奖标准
  string latestBindKey = 7; // 当前正在进行的bindKey, 前端缓存起来, 并在下次请求时原封不懂回传给服务端
}

message WebClaimReq {
  string openId = 1; // 可以放在header里,不显式传
  string appId = 2; // 可以放在header里,不显式传
  string bindKey = 3; // 活动key
  device.Device device = 4;
}

message WebClaimRsp {
  repeated game_api.TreasureCard cards = 2; // 宝藏卡信息, 如果产生宝藏卡领取, 则放在这里
  repeated game_api.RewardItem rewards = 3; // 奖励信息
}

message WebRanksReq {
  string openId = 1; // 可以放在header里,不显式传
  string appId = 2; // 可以放在header里,不显式传
  string bindKey = 3; // 活动key
}

message WebRankItem {
  string openId = 1;
  uint32 rank = 2; // 排名
  uint32 score = 3; // 分数,杯子数
  string nick = 4; // 昵称
  uint32 userType = 5; // 用户类型(1正常用户 2机器人)
  string avatar = 6; // 头像
  uint32 private = 7; // 私密状态, 为1时不能跳转平台个人主页
  string uid = 8; // 平台uid
}

message WebRanksRsp {
  repeated RankRewardPackage configs = 1; // 奖励配置
  repeated WebRankItem items = 2; // 排行榜信息
  uint32 capacity = 3; // 榜单容量
}
