syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/report";

service Report {
  // 关卡静态数据上报
  rpc FloorReport(FloorReportReq) returns (FloorReportRsp);
  // 关卡列表
  rpc FloorList(FloorListReq) returns (FloorListRsp);
  // 动画列表
  rpc AnimationList(AnimationListReq) returns (AnimationListRsp);
  // 动画编辑
  rpc AnimationUpdateStatus(AnimationUpdateStatusReq) returns (AnimationUpdateStatusRsp);
  // Cos上传临时秘钥
  rpc CosAuth(CosAuthReq) returns (CosAuthRsp);
}

// FloorReportReq 关卡静态数据上报req
message FloorReportReq {
  uint32 floor_id = 1; // 关卡顺序id
  string cos_url = 2; // 操作cos的url
  string animation_url = 3; // 操作cos的url
}

// FloorReportRsp 关卡静态数据上报rsp
message FloorReportRsp {}

// FloorListReq 关卡列表req
message FloorListReq {
  uint64 page = 1; // 页码
  string uid = 2; // 用户ID
  int64 pass_begin = 3; // 闯关耗时开始区间
  int64 pass_end = 4; // 闯关耗时结束区间
  int64 floor_begin = 5; // 关卡开始区间
  int64 floor_end = 6; // 关卡结束区间
  int64 submit_begin = 7; // 通关开始区间
  int64 submit_end = 8; // 通关结束区间
}

// FloorReport 关卡数据对象
message FloorReport {
  string uid = 1; // 用户ID
  uint32 floor_id = 2; // 关卡顺序id
  int64 submit_time = 3; // 结算时间
  int64 entry_time = 4; // 关卡进入时间
  int64 play_time = 5; // 闯关消耗时间
  int64 fail_cnt = 6; // 失败次数
  int64 no_report = 7; // 未收到上报次数
  string cos_url = 8; // 操作cos的url
}

// FloorListRsp 关卡列表rsp
message FloorListRsp {
  repeated FloorReport report_list = 1; // 关卡上报数据列表
}

// CosAuthReq Cos临时秘钥Req
message CosAuthReq {}

// CosAuthRsp Cos临时秘钥Rsp
message CosAuthRsp {
  int64 expired_time = 1;
  string expiration = 2;
  int64 start_time = 3;
  Credentials credentials = 4;
}

message Credentials {
  string tmp_secret_id = 1;
  string tmp_secret_key = 2;
  string session_token = 3;
}

// AnimationListReq 关卡数据对象
message AnimationListReq {
  uint64 page = 1; // 页码
  int64 floor_begin = 2; // 关卡开始区间
  int64 floor_end = 3; // 关卡结束区间
  int64 used_step_begin = 4; // 使用步数开始区间
  int64 used_step_end = 5; // 使用步数结束区间
  int64 submit_begin = 6; // 通关开始区间
  int64 submit_end = 7; // 通关结束区间
  string uid = 8; // 用户ID
  string open_id = 9; // 用户ID
  int64 status = 10; // 验证结果
}

// FloorListRsp 关卡列表rsp
message AnimationListRsp {
  repeated Animation animation_list = 1; // 关卡上报数据列表
}

// Animation 动画对象
message Animation {
  uint64 id = 1; // 业务ID
  string uid = 2; // 用户ID
  string open_id = 3; // 用户ID
  uint32 floor_id = 4; // 关卡顺序id
  int64 submit_time = 5; // 结算时间
  int64 fail_cnt = 6; // 失败次数
  int64 no_report = 7; // 未收到上报次数
  string animation_url = 8; // 操作cos的url
}

// AnimationUpdateStatusReq 动画编辑req
message AnimationUpdateStatusReq {
  repeated uint64 ids = 1; // 编辑ID
  int64 status = 2; // 状态码
  int64 time = 3; // 时间戳
}

// AnimationUpdateStatusRsp 动画编辑rsp
message AnimationUpdateStatusRsp {}
