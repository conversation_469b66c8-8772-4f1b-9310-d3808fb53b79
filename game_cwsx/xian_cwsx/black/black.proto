syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/black";

service Black {
  // 设置/修改一个黑名单
  rpc SetBlack(SetBlackReq) returns (SetBlackRsp);
  // 删除一个黑名单
  rpc DelBlack(DelBlackReq) returns (DelBlackRsp);
  // 查询黑名单列表
  rpc BlackList(BlackListReq) returns (BlackListRsp);
}

// 设置/修改黑名单
message SetBlackReq {
  string open_id = 1; // 玩家openid
  int64 ban_end = 2; // 封禁结束时间戳
}

message SetBlackRsp {}

message DelBlackReq {
  string open_id = 1; // 玩家openid
}

message DelBlackRsp {}

message BlackListReq {
  uint32 page = 1; // 页码从0开始,1页20个
}

message BlackInfo {
  string open_id = 1; // 玩家openid
  int64 ban_end = 2; // 封禁结束时间戳
}

message BlackListRsp {
  repeated BlackInfo infos = 1; // 没数据就会发空数组
}
