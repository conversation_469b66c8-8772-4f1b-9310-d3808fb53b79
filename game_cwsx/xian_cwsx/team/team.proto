syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/team";

import "pb/game_cwsx/xian_cwsx/game/game.proto";

service Team {
  // --------战队相关--------
  // 创建战队-发到中台审核 -- 是否有等级限制
  rpc CreateTeamToAudit(CreateTeamToAuditReq) returns (CreateTeamToAuditRsp);
  // 申请加入战队 --添加到战队审核
  rpc ApplyJoinTeam(ApplyJoinTeamReq) returns (ApplyJoinTeamRsp);
  // 查看战队信息
  rpc ViewTeam(ViewTeamReq) returns (ViewTeamRsp);
  // 查看战队基础信息
  rpc ViewTeamBase(ViewTeamBaseReq) returns (ViewTeamBaseRsp);
  // 战队申请列表
  rpc ApplyList(ApplyListReq) returns (ApplyListRsp);
  // 搜索战队 --模糊查询
  rpc SearchTeam(SearchTeamReq) returns (SearchTeamRsp);
  // 战队一览
  rpc TeamList(TeamListReq) returns (TeamListRsp);
  // 离开战队 --自己主动离开
  rpc TeamLeave(TeamLeaveReq) returns (TeamLeaveRsp);
  // 升降队员权限
  //  rpc TeamChangePermissions(TeamChangePermissionsReq) returns (TeamChangePermissionsRsp);
  // 编辑战队信息
  rpc TeamEditInfo(TeamEditInfoReq) returns (TeamEditInfoRsp);
  // 查询头像审核状态
  rpc TeamAvatarReviewStatus(TeamAvatarReviewStatusReq) returns (TeamAvatarReviewStatusRsp);
  // 审批加入
  rpc ApprovalToJoin(ApprovalToJoinReq) returns (ApprovalToJoinRsp);
  // 战队聊天发送
  // 求体力
  rpc BegHeart(BegHeartReq) returns (BegHeartRsp);
  // 助力体力
  rpc HelpHeart(HelpHeartReq) returns (HelpHeartRsp);
  // 领取战队赠送体力
  //  rpc DrawTeamHeart(DrawTeamHeartReq) returns (DrawTeamHeartRsp);
  // 获取可领取体力战友列表
  //  rpc TeamCanReciveList(TeamCanReciveListReq) returns (TeamCanReciveListRsp);
  // 获取战队求体力次数/可送次数
  rpc TeamCanReciveNum(TeamCanReciveNumReq) returns (TeamCanReciveNumRsp);
  // 踢出工会 --队长或管理员踢人
  rpc TeamOut(TeamOutReq) returns (TeamOutRsp);
  // 购买战队福利
  //  rpc TeamBuyWelfare(TeamBuyWelfareReq) returns (TeamBuyWelfareRsp);
  //  战队排行榜
  rpc TeamRank(TeamRankReq) returns (TeamRankRsp);
  //  战队成员排行榜
  rpc TeamUserRank(TeamUserRankReq) returns (TeamUserRankRsp);
  //    抢n
  //  rpc TeamNRank(TeamNRankReq) returns (TeamNRankRsp);
  //    战队消息
  rpc TeamMessageList(TeamMessageListReq) returns (TeamMessageListRsp);
  //    战队消息初始调用
  rpc TeamMessageListInitial(TeamMessageListReq) returns (TeamMessageListRsp);
  //    战队消息新接口
  rpc TeamMessageListOptimize(TeamMessageListReq) returns (TeamMessageListRsp);
  //    战队配置
  rpc TeamConfig(TeamConfigReq) returns (TeamConfigRsp);
  // 发起邀请
  rpc TeamInvite(TeamInviteReq) returns (TeamInviteRsp);
  // 查询邀请信息
  rpc TeamInviteInfo(TeamInviteInfoReq) returns (TeamInviteInfoRsp);
  // 通过邀请页加入
  //  rpc TeamJoinByInvite(TeamJoinByInviteReq) returns (TeamJoinByInviteRsp);
  // todo 特殊白单自动踢玩家出战队功能 测完在上
  //  rpc AutomaticKick(AutomaticKickReq) returns (AutomaticKickRsp);
  // 自动加入推荐列表第一个(如果是无需审核的战队)
  rpc AutoJoinTeam(AutoJoinTeamReq) returns (AutoJoinTeamRsp);
  // 战队周榜查看(当前周)
  rpc TeamWeekRank(TeamWeekRankReq) returns (TeamWeekRankRsp);
  // 战队成员周贡献榜查看(当前周)
  rpc TeamWeekUserRank(TeamWeekUserRankReq) returns (TeamWeekUserRankRsp);
  // 战队周榜奖励状态检查 --检查是否可以弹奖励了
  rpc TeamWeekAwardCheck(TeamWeekAwardCheckReq) returns (TeamWeekAwardCheckRsp);
}

message DeviceInfo {
  string ip = 1;
  string mac = 2;
  string imei = 3;
  string idfa = 4;
  string idfv = 5;
  uint32 mobileFlag = 6; // 是否来自手机
  string mobleQUA = 7; // qua
  string uuid = 8;
  string udid = 9;
  string qimei36 = 10;
  string deviceInfo = 11;
}

message CreateTeamToAuditReq {
  string set_avatar = 1; // 玩家自己设置图片的url 预制也是url
  string name = 2; // 战队名字
  string describe = 3; // 战队描述
  bool is_audit = 4; // 加入是否需要审核
  uint32 unlock_floor = 5; // 加入关卡必须打过的关卡
  DeviceInfo device = 6; // 设备信息, 有的尽量填
}

message CreateTeamToAuditRsp {
  string team_id = 1; // 唯一id
}

message ApplyJoinTeamReq {
  string team_id = 1; // 战队唯一id
  string member_id = 2; // 申请加入者id
}

// 如果申请的战队有审核开关,这个成功只表示进入审核列表
message ApplyJoinTeamRsp {}

message ViewTeamReq {
  string team_id = 1; // 啥都不传就查自己所在战队
}

enum MemberState {
  MemberStateUnknown = 0; // 未加入
  MemberStateMember = 1; // 成员/队长
  MemberStateApply = 2; // 在审批列表
}

message ViewTeamRsp {
  string set_avatar = 1; // 玩家自己设置图片的url 预制id也是url填入
  string name = 2; // 战队名字
  string describe = 3; // 战队描述
  bool is_audit = 4; // 加入是否需要审核
  int32 unlock_floor = 5; // 加入关卡必须打过的关卡
  repeated InfiniteItem infinite_items = 6; // 无限道具及过期时间
  repeated TeamPlayer players = 7;
  int32 passback = 8;
  bool hasNext = 9;
  int64 score = 10; // 战队总分
  uint32 active_level = 11; //  战队活跃等级
  uint32 member_num = 12; // 战队成员数量
  bool is_leader = 13;
  MemberState state = 14;
  uint32 edit_count = 15; // 改名剩余次数
  uint32 week_rank = 16; // 本战队在当前周的周榜排行
  int64 contribute_score = 17; // 战队贡献分数
}

message ViewTeamBaseReq {}

message ViewTeamBaseRsp {
  string team_id = 1; // 啥都不传就查自己所在战队
  string set_avatar = 2; // 玩家自己设置图片的url 预制id也是url填入
  string name = 3; // 战队名字
  uint32 position = 4; // 职位
  bool is_audit = 5; // 加入是否需要审核
  int64 score = 6; // 战队总分
  uint32 member_num = 7; // 战队成员数量
  uint32 edit_count = 8; // 改名剩余次数
  int64 contribute_score = 9; // 战队贡献分数
}

message ApplyListReq {
  int32 passback = 1;
}

// 申请成员信息
message ApplyMember {
  string open_id = 1;
  string nickname = 2; // 用户昵称
  string avatar = 3; // 用户头像
  uint32 max_floor = 4; // 当前关卡
  uint32 score = 5; // 巅峰赛积分
  int64 apply_time = 6; // 申请的时间
  string encrypt_uid = 7; // 加密uin
  int64 friendPetId = 8; // 此人宠物id
}

message ApplyListRsp {
  repeated ApplyMember list = 1;
  int32 passback = 2;
  bool has_next = 3;
}

message TeamPlayer {
  uint32 position = 1; // 职位 0 队长 1 管理员 2普通人
  uint32 max_floor = 2; // 打过的最大非噩梦关卡
  int64 join_time = 3; // 加入时间
  string encrypt_uid = 4; // 加密uin
  string nickname = 5; // 用户昵称
  string avatar = 6; // 用户头像
  int64 fresh_time = 7; // 玩家数据刷新时间 --打巅峰赛的时候这个值是巅峰赛分数刷新时间
  uint32 score = 8; // 巅峰赛积分
  uint32 help_num = 9; // 助力次数
  string open_id = 10;
  int64 friendPetId = 11; // 此人宠物id
}

message SearchTeamReq {
  string name = 1;
}

message TeamInfo {
  string team_id = 1; // 战队唯一id
  string set_avatar = 2; // 玩家自己设置图片的url 预制id也是url填入
  string name = 3; // 战队名字
  uint32 members_number = 4; // 人员数量
  repeated InfiniteItem infinite_items = 5; // 无限道具及过期时间
  uint32 active_level = 6; // 活跃等级
  uint32 score = 7;
  string describe = 8;
  bool is_apply = 9; // 是否已申请
  bool is_audit = 10; // 加入是否需要审核
  uint32 lowest_level = 11; // 进关条件
}

message SearchTeamRsp {
  repeated TeamInfo team_list = 1;
}

message TeamListReq {}

message TeamListRsp {
  repeated TeamInfo team_list = 1;
}

message ApprovalToJoinReq {
  string open_id = 1; // 审批加入的玩家openid
  bool agree = 2; // true 同意
}

// 加入成功，客户端自动塞入战队普通玩家列表
message ApprovalToJoinRsp {
  TeamPlayer player = 1;
  TeamWeekPlayer week_player = 2;
}

message TeamLeaveReq {}

message TeamLeaveRsp {}

message TeamChangePermissionsReq {
  string uid = 1; // 需要操作的成员id
  uint32 op = 2; // 0 升为管理员 1 降为普通成员
}

message TeamChangePermissionsRsp {}

message TeamEditInfoReq {
  string set_avatar = 1; // 头像
  string describe = 2; // 描述
  bool is_audit = 3; // 加入是否需要审核
  uint32 unlock_floor = 4; // 加入关卡必须打过的关卡
  DeviceInfo device = 5; // 设备信息, 有的尽量填
  string name = 6; // 战队名字
}

message TeamEditInfoRsp {
  uint32 edit_count = 1; // 改名剩余次数
}

message TeamAvatarReviewStatusReq {}

message TeamAvatarReviewStatusRsp {
  bool is_audit = 1; // 是否处于审核中
  string avatar = 2; //需要显示的头像
}

message TeamOutReq {
  string open_id = 1; // 需要踢出的人id
}

message TeamOutRsp {}

message TeamBuyWelfareReq {
  // 需要兑换的商品id
  uint32 shop_id = 1;
  string bill_id = 2; // 使用唯一id
}

message TeamBuyWelfareRsp {
  repeated TreasureCard cards = 1; // 如果发奖产生宝藏奖励这个字段不为空
}

// 战队榜结构
message TeamRankItem {
  uint64 team_score = 1; // 战队总分
  string team_id = 2; // 战队id
  string nickname = 3; // 战队昵称
  string avatar = 4; // 战队头像
  int64 fresh_time = 5; // 战队数据刷新时间
  uint32 member_num = 6; // 战队目前人数
}

message TeamRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
}

message TeamRankRsp {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  bool hasNext = 2;
  repeated TeamRankItem list = 3;
  uint32 rank = 4; // 自己的排行
}

message TeamUserRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  string team_id = 2; // 啥都不传就查自己所在战队
}

// 成员榜
message TeamUserRankRsp {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  bool hasNext = 2;
  repeated TeamPlayer list = 3;
}

message TeamNRankReq {}

message TeamNRankRsp {
  repeated TeamRankItem list = 1;
}

message BegHeartReq {}

message BegHeartRsp {}

message HelpHeartReq {
  string member_id = 1;
}

message HelpHeartRsp {}

message DrawTeamHeartReq {
  string member_id = 1; //  成员openid
}

message DrawTeamHeartRsp {
  uint32 hearts = 1; // 本次领取的爱心数量
}

message TeamCanReciveListReq {}

message MemberBase {
  string uid = 1;
  string name = 2; // 昵称
  string avatar = 3; // 头像
  int32 num = 4; // 还剩几次可领
}

message TeamCanReciveListRsp {
  repeated MemberBase friends = 1;
}

message TeamCanReciveNumReq {}

message TeamCanReciveNumRsp {
  uint32 num = 1; // 战队求的次数
  uint32 can_help_num = 2; // 可助力次数
}

enum MessageType {
  MessageTypeUnknown = 0;
  MessageTypeHeart = 1; // 体力
  MessageTypeEntry = 2; // 加入消息
  MessageTypeLeave = 3; // 退出消息
  MessageTypeKick = 4; // 踢人消息
  MessageTypeRefuse = 5; // 拒绝人消息
  MessageTypeAgree = 6; // 同意人消息
  MessageTypeLeader = 7; // 成为队长
  MessageTypeHelp = 8; // 助力消息
}

message TeamMessage {
  bool is_admin = 1; // 发送者是否系统
  string open_id = 2; // 发送者id
  string avatar = 3; // 发送者头像
  string name = 4; // 发送者名字
  string to_id = 5;
  string to_name = 6;
  MessageType message_type = 7; // 消息类型
  uint32 progress = 8; // 如果是求体力，需要发进度,如果是助力，这里是助力后的的进度
  bool can_help = 9; // 如果是求体力，需要发是否可送
  int64 ts = 10; // 发消息时间戳 毫秒
}

message TeamMessageListReq {
  int64 ts = 1; // 客户端需要的时间
}

message TeamMessageListRsp {
  repeated TeamMessage list = 1; // 消息列表
  bool has_next = 2; // 是否还有下一页
  int64 message_time = 3; // 最后一条消息的时间戳
  int64 beg_time = 4; // 上次求体力时间戳
}

message TeamConfigReq {
  uint32 version = 1;
}

message TeamConfigRsp {
  uint32 version = 1;
  string data = 2;
}

message TeamInviteReq {}

message TeamInviteRsp {
  string unique_id = 1; // 本次邀请唯一id
}

message TeamInviteInfoReq {
  string unique_id = 1; // 本次邀请唯一id
}

message TeamInviteInfoRsp {
  string open_id = 1; // 邀请者id
  uint32 position = 2; // 邀请者当时职位 0 队长 其他非队长
  string avatar = 3; // 邀请者头像
  string name = 4; // 邀请者呢称
  string team_id = 5; // 邀请时的战队id
}

message TeamJoinByInviteReq {
  string unique_id = 1; // 本次邀请唯一id
  string open_id = 2; // 被邀请者open_id
}

message TeamJoinByInviteRsp {}

message AutomaticKickReq {
  string open_id = 1; // 需要处理的玩家id
}

message AutomaticKickRsp {}

message AutoJoinTeamReq {}

message AutoJoinTeamRsp {
  string team_id = 1; // 如果加入成功，这里返回加入的id
  string set_avatar = 2; // 战队设置的头像
  string name = 3; // 战队名字
  string describe = 4; // 战队描述
  bool is_audit = 5; // 加入是否需要审核
  int32 unlock_floor = 6; // 加入关卡必须打过的关卡
  repeated InfiniteItem infinite_items = 7; // 无限道具及过期时间
  repeated TeamPlayer players = 8;
  int32 passback = 9;
  bool hasNext = 10;
  int64 score = 11; // 战队总分
  uint32 active_level = 12; //  战队活跃等级
  uint32 member_num = 13; // 战队成员数量
  bool is_leader = 14;
  MemberState state = 15;
  uint32 edit_count = 16; // 改名剩余次数
  uint32 contribute_score = 17; // 战队贡献分
}

message TeamWeekRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  bool is_pre = 2; // 是否拉上期，false拉本期
}

message TeamWeekRankRsp {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  bool hasNext = 2;
  repeated TeamRankItem list = 3;
  uint32 rank = 4; // 自己的排行
  int64 remain_sec = 5; // 本轮周榜倒计时
  uint32 score = 6; // 自己战队的分数
  uint32 self_contribute_score = 7; // 自己贡献分
}

message TeamWeekPlayer {
  uint32 position = 1; // 职位 0 队长 1 管理员 2普通人
  uint64 level = 2; // 关卡
  uint32 peak_score = 3; // 巅峰赛积分
  int64 contribute_score = 4; // 本周贡献分数
  string encrypt_uid = 5; // 加密uin
  string nickname = 6; // 用户昵称
  string avatar = 7; // 用户头像
  int64 fresh_time = 8; // 玩家本周贡献分数刷新时间
  string open_id = 9;
  int64 friendPetId = 10; // 此人宠物id
}

message TeamWeekUserRankReq {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  string team_id = 2; // 啥都不传就查自己所在战队
}

// 成员榜
message TeamWeekUserRankRsp {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  bool hasNext = 2;
  repeated TeamWeekPlayer list = 3;
  uint32 rank = 4; // 自己的排行
}

message TeamWeekAwardCheckReq {}

// 这里是弹窗如果玩家有奖励需要把上期排名信息展示
message WeekRank {
  int32 passback = 1; // 首次不传, 服务器返回什么, 传什么
  bool hasNext = 2;
  repeated TeamRankItem list = 3;
}

message TeamWeekAwardCheckRsp {
  bool can_week_reward = 1; // 是否可弹战队周榜奖励弹窗(领的都是上期) --满足条件才可以,即使没领奖,但是玩家不满足领奖条件也不可领奖弹窗
  bool can_user_week_reward = 2; // 是否可弹战队成员周贡献榜奖励弹窗(领的都是上期) --满足条件才可以,即使没领奖,但是玩家不满足领奖条件也不可领奖弹窗
  int64 week_rank = 3; // 周榜上周战队排名
  int64 user_week_rank = 4; // 上周个人贡献榜个人排名
  int64 st = 5; // 当前服务器10位时间戳
  int64 end_ts = 6; // 本轮周榜结束的10位时间戳
  int64 score = 7; // 战队分
  repeated Reward rewards = 8; // 如果是可以弹,这个返回奖励列表
  WeekRank rank_list = 9; // 这里只会返回上期的第一页,其他数据请拉TeamWeekRank
  string avatar = 10; // 战队头像
  string name = 11; // 战队名称
}
