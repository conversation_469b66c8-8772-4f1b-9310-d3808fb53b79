syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/game";

enum SceneID {
  SCENE_ID_NONE = 0;
  SCENE_ID_LIVE = 1001; // 直播
  SCENE_ID_KTV = 1002; // 歌房
  SCENE_ID_ASYNC = 1003; // 异步
  SCENE_ID_FRIENDS_KTV = 1004; // 好友歌房
  SCENE_ID_MULTI_KTV = 1005; // 多麦歌房
  SCENE_ID_PLAT_ASYNC = 1006; // 平台-异步
  SCENE_ID_TASK_CENTER = 1007; // 任务中心
  SCENE_ID_VISUAL_CENTER = 1008; // 可视化运营中台
  SCENE_ID_ASSET_CENTER = 1009; // 资产中心
  SCENE_ID_LIVE_CALL = 1010; // 直播连麦
  SCENE_ID_FEED_ADVERT = 1011; // 游戏大卡片
}

enum emRoomType {
  ROOM_TYPE_LIVE_VIDEO = 0; // 直播间 - 视频
  ROOM_TYPE_LIVE_AUDIO = 1; // 直播间 - 音频
  ROOM_TYPE_KTV_SIG = 2; // 欢唱歌房 - 单麦
  ROOM_TYPE_KTV_MUL = 3; // 欢唱歌房 - 多麦
  ROOM_TYPE_KTV_SOCIAL = 4; // 欢聚歌房 - 社交
  ROOM_TYPE_LIVE_RECORD = 5; // 录屏直播间
}

enum emSpecialStateType {
  SPECIAL_STATE_NONE = 0;
  SPECIAL_STATE_CRANIVAL = 1; // 狂欢时刻
}

enum ModuleStyle {
  SUB_MODULESTYLE_INVALID = 0; // 无效类型
  // 常用 Module不允许添加
  SUB_MODULESTYLE_TXT = 1; // 简易文本设置;
  // 自定义Module 最小值; 新增自定类型，朝后侧加
  SUB_MODULESTYLE_BUSI_MIN = 100000000;
}

message SceneParam {
  string strRoomID = 1; // 房间id
  int32 iRoomType = 2; // 房间类型
  int64 lAnchorID = 3; // 主播id
  map<string, string> mapExt = 4; // 扩展参数
  string strShowID = 5; // showId
}

message PlayCorner {
  string strDoc = 1; // 文案
  string strColor = 2; // 文案底色
}

message PlayModule {
  string strStyle = 1; // 样式
  string strData = 2; // json格式
  string strSpecialState = 3; // 游戏特殊状态，json格式，参考SpecialState。
}

message ProfileEntry {
  string strDesc = 1; // 文案
  string strDescColor = 2; // 文案底色
  int32 iShowRedPoint = 3; // 是否展示红点, 1:展示, 0:不展示
}

// --- GameCenter 回调参数 Start -----//
message CustomizeCornerReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
}

message CustomizeCornerRsp {
  PlayCorner stCorner = 1;
}

message CustomizeModuleReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
  string access_token = 3; // 透传accessToken
}

message CustomizeModuleRsp {
  PlayModule stModule = 1;
}

message CustomizeProfileEntryReq {
  uint32 uScene = 1; // SceneID
  SceneParam stSceneParams = 2;
}

message CustomizeProfileEntryRsp {
  ProfileEntry stProfile = 1;
}

// --- GameCenter 回调参数 End -----//

// --- 自定义Module Start --- //

// SUB_MODULESTYLE_TXT
message ModuleStyleTxt {
  uint32 iShowRedPoint = 1; // 1展示红点
  string strDoc = 2; // 文案
  string strDocColor = 3; // 文案颜色
}

message SpecialState {
  uint32 uStateType = 1; // emSpecialStateType
  uint32 uStartTs = 2; // 状态开始时间
  uint32 uEndTs = 3; // 状态结束时间
  string strDoc = 4; // 状态文案，如对与幸运牧场狂欢时刻，这里应该是【参与必得XX鲜花】
}

message SimpleInfo {
  string nickname = 1; // 昵称
  string avatar = 2; // 头像
  int64 fresh_time = 3;
}

message AppModuleLeft {
  string icon = 1; // 显示icon
  uint32 expire = 2; // 无限道具到期时间-时间戳,此值为0再判断数量,其他类型此值为0
  int64 num = 3; //显示数量,其他类型此值为0
  int64 st = 4; // 服务器当前时间戳
  repeated SimpleInfo friends = 5; // 轮播类型直接无视1，2，3参数
}

message AppModuleMiddle {
  string describe = 1; // 描述文案
  string color = 2; // 描述文案颜色,默认FFFFFF
}

message AppModuleRight {
  string button_text = 1; // 按钮文本
  string button_text_color = 2; // 按钮文本颜色
  string button_color = 3; // 按钮颜色
  string button_url = 4; // 按钮跳转链接
}

enum AppModuleShowType {
  SHOW_TYPE_NONE = 0; // 无运营位
  SHOW_TYPE_TEXT = 1; //图片/文案/按钮
  SHOW_TYPE_EXPIRE_NUM = 2; //图片/倒计时或数量/文案/按钮
  SHOW_TYPE_CAROUSEL = 3; //轮播图/文案/按钮
}

message AppModuleState {
  AppModuleShowType show_type = 1; // 展示类型
  string bg_url = 2; // 背景图url
  AppModuleLeft left = 3; // 左边：图片、是否倒计时、图片文案-数量
  AppModuleMiddle middle = 4; // 中间：描述信息+文字颜色
  AppModuleRight right = 5; // 右边：按钮文字、按钮文字颜色、按钮颜色、按钮跳转链接
}

// --- 自定义Module End --- //

message ColorText {
  string strText = 1;
  string strColor = 2; // FFFFFF
}

message PlayEntryReq {
  string strQua = 1;
  string strDeviceInfo = 2;
}

message PlayEntryRsp {
  string strTitle = 1;
  string strIcon = 2;
  ColorText stDescription = 3;
  string strJumpUrl = 4;
  uint32 uDirect = 5;
}

message Preferance {
  map<string, string> kvs = 1;
}

message BatchQueryReq {
  repeated string openIds = 1;
  repeated string filters = 2; // 只查特定设置
}

message BatchQueryRsp {
  map<string, Preferance> data = 1;
}

message QueryReq {
  string openId = 1;
  repeated string filters = 2; // 只查特定设置
}

message QueryRsp {
  Preferance data = 1;
}

message ModifyReq {
  string openId = 1;
  map<string, string> kvs = 2; // key不能为空,长度不能超过20
}

message ModifyRsp {
  Preferance data = 1;
}

message SyncFloorMapReq {
  uint32 sequence_id = 1; // 关卡顺序id
  uint32 ab_group = 2; // ab test组
  uint32 stage = 3; // 关卡id
  uint32 stage_type = 4; // 关卡类型
  uint32 step_num = 5; // 步数限制
  string floor_map = 6; // 此关卡地图
  string msg = 7; // 描述信息
  string tag = 8; // 版本
}

message SyncFloorMapRsp {}

message GetFloorMapReq {
  uint32 sequence_id = 1; // 关卡顺序id
  uint32 ab_group = 2; // ab test组
  string tag = 3; // 版本
}

message GetFloorMapRsp {
  uint32 sequence_id = 1; // 关卡顺序id
  uint32 ab_group = 2; // ab test组
  uint32 stage = 3; // 关卡id
  uint32 stage_type = 4; // 关卡类型
  uint32 step_num = 5; // 步数限制
  string floor_map = 6; // 此关卡地图
  string msg = 7; // 描述信息
  string tag = 8; // 版本
}

message SyncItemReq {
  uint32 id = 1; // 自增id
  uint32 props_id = 2; // 道具id
  string name = 3; // 道具名
  uint32 props_type = 4; // 道具类型
  uint32 props_category = 5; // 道具类别-目前仅q音有（宠物小窝）
  uint32 scene_type = 6; // 道具使用场景
  string describe = 7; // 描述
  uint32 kg_tme_id = 8; // K歌对应中台id
  uint32 qm_tme_id = 9; // Q音对应中台id
  uint32 is_treasure = 10; // 是否宝藏卡
  uint32 expire_time = 11; // 过期时间
  uint32 exchange_coin = 12; // 结算兑换奖励金币
  string price = 13; //道具价值 单位毫厘 分缩小100倍
  string image = 14; // 道具图标
  string image2 = 15; // 道具图标
  uint32 kugou_tme_id = 16; // 酷狗对应中台id
  uint32 kuwo_tme_id = 17; // 酷我对应中台id
  uint32 lanren_tme_id = 18; // 懒人对应中台id
  uint32 weixin_tme_id = 19; // WX小程序对应中台id
}

message SyncItemRsp {}

message ItemInfo {
  uint32 id = 1; // 自增id
  uint32 props_id = 2; // 道具id
  string name = 3; // 道具名
  uint32 props_type = 4; // 道具类型
  uint32 props_category = 5; // 道具类别-目前仅q音有（宠物小窝）
  uint32 scene_type = 6; // 道具使用场景
  string describe = 7; // 描述
  uint32 kg_tme_id = 8; // K歌对应中台id
  uint32 qm_tme_id = 9; // Q音对应中台id
  uint32 is_treasure = 10; // 是否宝藏卡
  uint32 expire_time = 11; // 过期时间
  uint32 exchange_coin = 12; // 结算兑换奖励金币
  string price = 13; //道具价值 单位毫厘 分缩小100倍
  string image = 14; // 道具图标
  string image2 = 15; // 道具图标
  uint32 kugou_tme_id = 16; // 酷狗对应中台id
  uint32 kuwo_tme_id = 17; // 酷我对应中台id
  uint32 lanren_tme_id = 18; // 懒人对应中台id
  uint32 weixin_tme_id = 19; // WX小程序对应中台id
}

message GetItemReq {
  int64 version = 1; // 客户端缓存的版本记录
}

message GetItemRsp {
  map<uint32, ItemInfo> items = 1;
  int64 version = 2; // 最新推送版本记录
}
