syntax = "proto3";

package xian_cwsx;

option go_package = "git.coding.tmeoa.com/codingcorp/xian_cwsx/xian_cwsx/pkg/gen/pb/game_cwsx/xian_cwsx/activity";

// InletListReq 入口管理列表
message InletListReq {
  PlatformType pt = 1; // 设备类型
  int32 sceneId = 2; // 场景ID 前端与中台李剑军对一下
  int64 left_num = 3; // 左边所有ICON [0~5]
  int64 right_num = 4; // 右边所有ICON [0~5]
}

enum PlatformType {
  PlatformUnknown = 0;
  PlatformAndroid = 1;
  PlatformIOS = 2;
}

// InletListRsp 入口管理列表
message InletListRsp {
  repeated InletInfo left = 1; //  左边入口活动列表
  repeated InletInfo right = 2; // 右边入口活动列表
}

// InletInfo 信息
message InletInfo {
  int64 number = 1; // 入口活动编号=>InletNumber
  int64 duration_type = 2; // 持续时间类型=>InletDurationType
  int64 start_time = 3; // 开始时间
  int64 end_time = 4; // 结束时间
}

// InletOffReq 关闭活动
message InletOffReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
  string open_id = 2; // 用户ID
}

// InletOffRsp 关闭活动
message InletOffRsp {}

enum InletNumber {
  InletNumber_None = 0; // 无
  InletNumber_HonorMedalCompetition = 1; // 荣耀奖牌赛
  InletNumber_AirplaneRace = 2; // 飞机竞赛
  InletNumber_SuperColorfulLights = 3; // 超级彩灯
  InletNumber_TeamRudder = 4; // 战队淘金
  InletNumber_TeamCompetition = 5; // 战队竞赛
  InletNumber_BattlePass = 6; // 战令
  InletNumber_Fishing = 7; // 小猫钓鱼
  InletNumber_CuteRabbitParadise = 8; // 萌兔乐园
  InletNumber_EndlessTreasures = 9; // 无尽宝藏
  InletNumber_CheckIn = 10; // 签到
  InletNumber_LightingRush = 11; // 彩虹竞速
  InletNumber_SpecialDiscountPackage = 12; // 特惠礼包
  InletNumber_NoviceChallengeEvent = 13; // 新手闯关活动
  InletNumber_DragonsTreasure = 14; // 巨龙宝藏
  InletNumber_InviteFriends = 15; // 邀请好友
  InletNumber_ShareFriends = 16; // 分享好友
  InletNumber_Favorite = 17; // 收藏
}

enum InletDurationType {
  InletDurationType_DurationNone = 0; // 无
  InletDurationType_DurationAlways = 1; // 永久
  InletDurationType_DurationSingle = 2; // 单次展示
  InletDurationType_DurationDay = 3; // 天刷新
  InletDurationType_DurationWeek = 4; // 周刷新
  InletDurationType_DurationWeekDay = 5; // 周每天刷新
  InletDurationType_DurationCallback = 6; // 回调刷新
  InletDurationType_DurationSetSE = 7; // 特殊设置开始与结束时间
  InletDurationType_DurationMinute = 8; // 分钟刷新
}

// InletOptSetReq 设置活动开始与结束时间
message InletOptSetReq {
  InletNumber number = 1; // 入口活动编号=>InletNumber
  repeated InletOptTime opt_time = 2; // 时间列表
  message InletOptTime {
    int64 start_time = 1; // 开始时间
    int64 end_time = 2; // 结束时间
  }
}

// InletSetRsp 设置活动开始与结束时间
message InletOptSetRsp {}

// InletStatusReq 查询活动状态
message InletStatusReq {
  repeated InletNumber number = 1; // 入口活动编号=>InletNumber
  string open_id = 2; // 用户的open_id
  PlatformType os = 3; // 设备类型
  int32 sceneId = 4; // 场景ID
  string userAgent = 5; // ua
  string appId = 6; // appid
}

// InletSetRsp 查询活动状态
message InletStatusRsp {
  map<int64, bool> inlet_status = 2; // 入口活动编号[InletNumber]=>入口状态[true展示 false关闭]
}
