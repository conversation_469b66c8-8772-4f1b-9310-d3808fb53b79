syntax = "proto3";

package game_robot;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/game_robot/storage";

message RobotRecord {
  uint64 id = 1; // id
  string nick = 2; // 昵称
  string avatar = 3; // 头像
  uint32 gender = 4; // 性别
}

message RobotCursor {
  int64 cursor = 1;
  int64 max = 2;
}

enum RobotGender {
  RobotGenderNone = 0; // 未知
  RobotGenderMale = 1; // 男
  RobotGenderFemale = 2; // 女
}
