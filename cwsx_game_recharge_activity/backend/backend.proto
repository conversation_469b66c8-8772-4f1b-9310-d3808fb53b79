syntax = "proto3";

package cwsx_game_recharge_activity;

option go_package = "tcode.tmeoa.com/tme_game/tme_game_platform/pkg/gen/pb/cwsx_game_recharge_activity";

import "pb/device/device.proto";
import "pb/game_cwsx/inlet/inlet.proto";

service Backend {
  // 领取奖励
  rpc Claim(ClaimReq) returns (ClaimRsp);
  // 上报充值
  rpc ReportRecharge(ReportRechargeReq) returns (ReportRechargeRsp);
  // 查询活动状态
  rpc InletStatus(inlet.InletStatusReq) returns (inlet.InletStatusRsp);
  // 查询活动状态(新入口逻辑)
  rpc ActivityState(inlet.ActivityStateReq) returns (inlet.ActivityStateRsp);
}

message ClaimReq {
  string appId = 1;
  string openId = 2;
  int32 activityId = 3; // 活动id
  int32 taskCardId = 4; // 任务ID
  device.Device device = 5;
}

message ClaimRsp {
  int32 taskCardId = 1;
}

message ReportRechargeReq {
  string appId = 1;
  string openId = 2;
  string billNo = 3;
  int32 amount = 4;
  int64 timestamp = 5;
}

message ReportRechargeRsp {}
