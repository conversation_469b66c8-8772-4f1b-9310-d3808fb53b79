// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: moe_risk_proto/moe_risk_adapter/openapi/moe_risk_openapi.proto

package openapi

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	comm "kugou_adapter_service/pkg/gen/proto/moe_risk_proto/moe_risk_adapter/comm"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 中台基础字段
type GameMiddleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// uid
	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// game app id
	GameAppid string `protobuf:"bytes,2,opt,name=game_appid,json=gameAppid,proto3" json:"game_appid,omitempty"`
	// encrypt_uid
	EncryptUid string `protobuf:"bytes,3,opt,name=encrypt_uid,json=encryptUid,proto3" json:"encrypt_uid,omitempty"`
	// 客户端ipv4
	Ipv4 uint32 `protobuf:"varint,4,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	// 客户端ipv6
	Ipv6 string `protobuf:"bytes,5,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	// strUid 支持str类型的平台uid
	StrUid string `protobuf:"bytes,6,opt,name=strUid,proto3" json:"strUid,omitempty"`
	// 设备信息
	DeviceInfo string `protobuf:"bytes,7,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
}

func (x *GameMiddleInfo) Reset() {
	*x = GameMiddleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameMiddleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameMiddleInfo) ProtoMessage() {}

func (x *GameMiddleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameMiddleInfo.ProtoReflect.Descriptor instead.
func (*GameMiddleInfo) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{0}
}

func (x *GameMiddleInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GameMiddleInfo) GetGameAppid() string {
	if x != nil {
		return x.GameAppid
	}
	return ""
}

func (x *GameMiddleInfo) GetEncryptUid() string {
	if x != nil {
		return x.EncryptUid
	}
	return ""
}

func (x *GameMiddleInfo) GetIpv4() uint32 {
	if x != nil {
		return x.Ipv4
	}
	return 0
}

func (x *GameMiddleInfo) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *GameMiddleInfo) GetStrUid() string {
	if x != nil {
		return x.StrUid
	}
	return ""
}

func (x *GameMiddleInfo) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

// 风控上报
type RiskReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MiddleInfo *GameMiddleInfo `protobuf:"bytes,1,opt,name=middleInfo,proto3" json:"middleInfo,omitempty"`
	Event      *comm.RiskEvent `protobuf:"bytes,2,opt,name=event,proto3" json:"event,omitempty"`
}

func (x *RiskReportReq) Reset() {
	*x = RiskReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskReportReq) ProtoMessage() {}

func (x *RiskReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskReportReq.ProtoReflect.Descriptor instead.
func (*RiskReportReq) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{1}
}

func (x *RiskReportReq) GetMiddleInfo() *GameMiddleInfo {
	if x != nil {
		return x.MiddleInfo
	}
	return nil
}

func (x *RiskReportReq) GetEvent() *comm.RiskEvent {
	if x != nil {
		return x.Event
	}
	return nil
}

type RiskReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
	Msg       string `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"` // 占位
}

func (x *RiskReportRsp) Reset() {
	*x = RiskReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskReportRsp) ProtoMessage() {}

func (x *RiskReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskReportRsp.ProtoReflect.Descriptor instead.
func (*RiskReportRsp) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{2}
}

func (x *RiskReportRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *RiskReportRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *RiskReportRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 统一校验（同步）
type RiskCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MiddleInfo *GameMiddleInfo             `protobuf:"bytes,1,opt,name=middleInfo,proto3" json:"middleInfo,omitempty"`
	CheckType  comm.RiskCheckType          `protobuf:"varint,2,opt,name=checkType,proto3,enum=moe_risk_comm.RiskCheckType" json:"checkType,omitempty"` // 校验类型
	UserInfo   *comm.RiskUserInfo          `protobuf:"bytes,3,opt,name=userInfo,proto3" json:"userInfo,omitempty"`                                     // 用户信息
	SceneInfo  *comm.RiskSceneInfo         `protobuf:"bytes,4,opt,name=sceneInfo,proto3" json:"sceneInfo,omitempty"`                                   // 场景信息
	AssetIn    *comm.AssetCheckInfo        `protobuf:"bytes,5,opt,name=assetIn,proto3" json:"assetIn,omitempty"`                                       // 用户入帐信息
	AssetOut   *comm.AssetCheckInfo        `protobuf:"bytes,6,opt,name=assetOut,proto3" json:"assetOut,omitempty"`                                     // 用户出账信息, 兑换/提现(RiskCheckType_Exchange/RiskCheckType_Cashout)类校验需要填出账信息
	ExtraInfo  *comm.RiskCheckReqExtraInfo `protobuf:"bytes,7,opt,name=extraInfo,proto3" json:"extraInfo,omitempty"`                                   // 额外信息(按需设置)
	SafeInfo   *comm.RiskCheckSafetyInfo   `protobuf:"bytes,8,opt,name=safeInfo,proto3" json:"safeInfo,omitempty"`                                     // 安全串联信息(按需设置)
}

func (x *RiskCheckReq) Reset() {
	*x = RiskCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCheckReq) ProtoMessage() {}

func (x *RiskCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCheckReq.ProtoReflect.Descriptor instead.
func (*RiskCheckReq) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{3}
}

func (x *RiskCheckReq) GetMiddleInfo() *GameMiddleInfo {
	if x != nil {
		return x.MiddleInfo
	}
	return nil
}

func (x *RiskCheckReq) GetCheckType() comm.RiskCheckType {
	if x != nil {
		return x.CheckType
	}
	return comm.RiskCheckType(0)
}

func (x *RiskCheckReq) GetUserInfo() *comm.RiskUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *RiskCheckReq) GetSceneInfo() *comm.RiskSceneInfo {
	if x != nil {
		return x.SceneInfo
	}
	return nil
}

func (x *RiskCheckReq) GetAssetIn() *comm.AssetCheckInfo {
	if x != nil {
		return x.AssetIn
	}
	return nil
}

func (x *RiskCheckReq) GetAssetOut() *comm.AssetCheckInfo {
	if x != nil {
		return x.AssetOut
	}
	return nil
}

func (x *RiskCheckReq) GetExtraInfo() *comm.RiskCheckReqExtraInfo {
	if x != nil {
		return x.ExtraInfo
	}
	return nil
}

func (x *RiskCheckReq) GetSafeInfo() *comm.RiskCheckSafetyInfo {
	if x != nil {
		return x.SafeInfo
	}
	return nil
}

type RiskCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32                 `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string                `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
	Result    *comm.RiskCheckResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *RiskCheckRsp) Reset() {
	*x = RiskCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCheckRsp) ProtoMessage() {}

func (x *RiskCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCheckRsp.ProtoReflect.Descriptor instead.
func (*RiskCheckRsp) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{4}
}

func (x *RiskCheckRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *RiskCheckRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *RiskCheckRsp) GetResult() *comm.RiskCheckResult {
	if x != nil {
		return x.Result
	}
	return nil
}

// 风控查询
type RiskQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MiddleInfo *GameMiddleInfo     `protobuf:"bytes,1,opt,name=middleInfo,proto3" json:"middleInfo,omitempty"`
	UserInfo   *comm.RiskUserInfo  `protobuf:"bytes,2,opt,name=userInfo,proto3" json:"userInfo,omitempty"`   // 用户信息
	SceneInfo  *comm.RiskSceneInfo `protobuf:"bytes,3,opt,name=sceneInfo,proto3" json:"sceneInfo,omitempty"` // 场景信息
}

func (x *RiskQueryReq) Reset() {
	*x = RiskQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskQueryReq) ProtoMessage() {}

func (x *RiskQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskQueryReq.ProtoReflect.Descriptor instead.
func (*RiskQueryReq) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{5}
}

func (x *RiskQueryReq) GetMiddleInfo() *GameMiddleInfo {
	if x != nil {
		return x.MiddleInfo
	}
	return nil
}

func (x *RiskQueryReq) GetUserInfo() *comm.RiskUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *RiskQueryReq) GetSceneInfo() *comm.RiskSceneInfo {
	if x != nil {
		return x.SceneInfo
	}
	return nil
}

type RiskQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32                    `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string                   `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
	TotalStat map[int32]*comm.RiskStat `protobuf:"bytes,3,rep,name=totalStat,proto3" json:"totalStat,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 整体维度风控信息, key: RiskDimension
	UserStat  map[int32]*comm.RiskStat `protobuf:"bytes,4,rep,name=userStat,proto3" json:"userStat,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`   // 个人维度风控信息, key: RiskDimension
}

func (x *RiskQueryRsp) Reset() {
	*x = RiskQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskQueryRsp) ProtoMessage() {}

func (x *RiskQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskQueryRsp.ProtoReflect.Descriptor instead.
func (*RiskQueryRsp) Descriptor() ([]byte, []int) {
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP(), []int{6}
}

func (x *RiskQueryRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *RiskQueryRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *RiskQueryRsp) GetTotalStat() map[int32]*comm.RiskStat {
	if x != nil {
		return x.TotalStat
	}
	return nil
}

func (x *RiskQueryRsp) GetUserStat() map[int32]*comm.RiskStat {
	if x != nil {
		return x.UserStat
	}
	return nil
}

var File_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto protoreflect.FileDescriptor

var file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x10, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x01, 0x0a, 0x0e, 0x47,
	0x61, 0x6d, 0x65, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x55, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x69,
	0x70, 0x76, 0x34, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x36, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x55, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x55, 0x69, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x81, 0x01, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x40, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x22, 0x5f, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xf9, 0x03, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65,
	0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x6d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x09, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x6d, 0x6f,
	0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a,
	0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65,
	0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6e, 0x12, 0x39, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x12, 0x42, 0x0a,
	0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x3e, 0x0a, 0x08, 0x73, 0x61, 0x66, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x61, 0x66, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x84, 0x01, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67,
	0x12, 0x36, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x0c, 0x52, 0x69, 0x73,
	0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x40, 0x0a, 0x0a, 0x6d, 0x69, 0x64,
	0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x65, 0x6e,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x90, 0x03, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73,
	0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x12,
	0x4b, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x73, 0x70, 0x2e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x12, 0x48, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x1a, 0x55, 0x0a, 0x0e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x5f,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x54, 0x0a,
	0x0d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x32, 0xfd, 0x02, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x47, 0x61, 0x6d, 0x65,
	0x4f, 0x70, 0x65, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x7a, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x3a,
	0x01, 0x2a, 0x22, 0x1f, 0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x76, 0x0a, 0x09, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x12, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70,
	0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x6d, 0x69,
	0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x76, 0x0a, 0x09, 0x52,
	0x69, 0x73, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x5f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23,
	0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x42, 0x69, 0x5a, 0x67, 0x63, 0x6e, 0x62, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x67, 0x65, 0x6e, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61,
	0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescOnce sync.Once
	file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescData = file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDesc
)

func file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescGZIP() []byte {
	file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescOnce.Do(func() {
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescData)
	})
	return file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDescData
}

var file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_goTypes = []interface{}{
	(*GameMiddleInfo)(nil),             // 0: moe_risk_openapi.GameMiddleInfo
	(*RiskReportReq)(nil),              // 1: moe_risk_openapi.RiskReportReq
	(*RiskReportRsp)(nil),              // 2: moe_risk_openapi.RiskReportRsp
	(*RiskCheckReq)(nil),               // 3: moe_risk_openapi.RiskCheckReq
	(*RiskCheckRsp)(nil),               // 4: moe_risk_openapi.RiskCheckRsp
	(*RiskQueryReq)(nil),               // 5: moe_risk_openapi.RiskQueryReq
	(*RiskQueryRsp)(nil),               // 6: moe_risk_openapi.RiskQueryRsp
	nil,                                // 7: moe_risk_openapi.RiskQueryRsp.TotalStatEntry
	nil,                                // 8: moe_risk_openapi.RiskQueryRsp.UserStatEntry
	(*comm.RiskEvent)(nil),             // 9: moe_risk_comm.RiskEvent
	(comm.RiskCheckType)(0),            // 10: moe_risk_comm.RiskCheckType
	(*comm.RiskUserInfo)(nil),          // 11: moe_risk_comm.RiskUserInfo
	(*comm.RiskSceneInfo)(nil),         // 12: moe_risk_comm.RiskSceneInfo
	(*comm.AssetCheckInfo)(nil),        // 13: moe_risk_comm.AssetCheckInfo
	(*comm.RiskCheckReqExtraInfo)(nil), // 14: moe_risk_comm.RiskCheckReqExtraInfo
	(*comm.RiskCheckSafetyInfo)(nil),   // 15: moe_risk_comm.RiskCheckSafetyInfo
	(*comm.RiskCheckResult)(nil),       // 16: moe_risk_comm.RiskCheckResult
	(*comm.RiskStat)(nil),              // 17: moe_risk_comm.RiskStat
}
var file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_depIdxs = []int32{
	0,  // 0: moe_risk_openapi.RiskReportReq.middleInfo:type_name -> moe_risk_openapi.GameMiddleInfo
	9,  // 1: moe_risk_openapi.RiskReportReq.event:type_name -> moe_risk_comm.RiskEvent
	0,  // 2: moe_risk_openapi.RiskCheckReq.middleInfo:type_name -> moe_risk_openapi.GameMiddleInfo
	10, // 3: moe_risk_openapi.RiskCheckReq.checkType:type_name -> moe_risk_comm.RiskCheckType
	11, // 4: moe_risk_openapi.RiskCheckReq.userInfo:type_name -> moe_risk_comm.RiskUserInfo
	12, // 5: moe_risk_openapi.RiskCheckReq.sceneInfo:type_name -> moe_risk_comm.RiskSceneInfo
	13, // 6: moe_risk_openapi.RiskCheckReq.assetIn:type_name -> moe_risk_comm.AssetCheckInfo
	13, // 7: moe_risk_openapi.RiskCheckReq.assetOut:type_name -> moe_risk_comm.AssetCheckInfo
	14, // 8: moe_risk_openapi.RiskCheckReq.extraInfo:type_name -> moe_risk_comm.RiskCheckReqExtraInfo
	15, // 9: moe_risk_openapi.RiskCheckReq.safeInfo:type_name -> moe_risk_comm.RiskCheckSafetyInfo
	16, // 10: moe_risk_openapi.RiskCheckRsp.result:type_name -> moe_risk_comm.RiskCheckResult
	0,  // 11: moe_risk_openapi.RiskQueryReq.middleInfo:type_name -> moe_risk_openapi.GameMiddleInfo
	11, // 12: moe_risk_openapi.RiskQueryReq.userInfo:type_name -> moe_risk_comm.RiskUserInfo
	12, // 13: moe_risk_openapi.RiskQueryReq.sceneInfo:type_name -> moe_risk_comm.RiskSceneInfo
	7,  // 14: moe_risk_openapi.RiskQueryRsp.totalStat:type_name -> moe_risk_openapi.RiskQueryRsp.TotalStatEntry
	8,  // 15: moe_risk_openapi.RiskQueryRsp.userStat:type_name -> moe_risk_openapi.RiskQueryRsp.UserStatEntry
	17, // 16: moe_risk_openapi.RiskQueryRsp.TotalStatEntry.value:type_name -> moe_risk_comm.RiskStat
	17, // 17: moe_risk_openapi.RiskQueryRsp.UserStatEntry.value:type_name -> moe_risk_comm.RiskStat
	1,  // 18: moe_risk_openapi.OpenGameOpenApi.RiskReport:input_type -> moe_risk_openapi.RiskReportReq
	3,  // 19: moe_risk_openapi.OpenGameOpenApi.RiskCheck:input_type -> moe_risk_openapi.RiskCheckReq
	5,  // 20: moe_risk_openapi.OpenGameOpenApi.RiskQuery:input_type -> moe_risk_openapi.RiskQueryReq
	2,  // 21: moe_risk_openapi.OpenGameOpenApi.RiskReport:output_type -> moe_risk_openapi.RiskReportRsp
	4,  // 22: moe_risk_openapi.OpenGameOpenApi.RiskCheck:output_type -> moe_risk_openapi.RiskCheckRsp
	6,  // 23: moe_risk_openapi.OpenGameOpenApi.RiskQuery:output_type -> moe_risk_openapi.RiskQueryRsp
	21, // [21:24] is the sub-list for method output_type
	18, // [18:21] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_init() }
func file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_init() {
	if File_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameMiddleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_goTypes,
		DependencyIndexes: file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_depIdxs,
		MessageInfos:      file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_msgTypes,
	}.Build()
	File_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto = out.File
	file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_rawDesc = nil
	file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_goTypes = nil
	file_moe_risk_proto_moe_risk_adapter_openapi_moe_risk_openapi_proto_depIdxs = nil
}
