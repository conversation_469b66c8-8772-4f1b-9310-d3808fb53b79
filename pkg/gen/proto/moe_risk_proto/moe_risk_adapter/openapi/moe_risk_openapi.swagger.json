{"swagger": "2.0", "info": {"title": "moe_risk_proto/moe_risk_adapter/openapi/moe_risk_openapi.proto", "version": "version not set"}, "tags": [{"name": "OpenGameOpenApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/miniprogram/openapi/riskCheck": {"post": {"summary": "统一校验", "operationId": "OpenGameOpenApi_RiskCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/moe_risk_openapiRiskCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/moe_risk_openapiRiskCheckReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/riskQuery": {"post": {"summary": "风控查询", "operationId": "OpenGameOpenApi_RiskQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/moe_risk_openapiRiskQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/moe_risk_openapiRiskQueryReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/riskReport": {"post": {"summary": "风控上报", "operationId": "OpenGameOpenApi_RiskReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/moe_risk_openapiRiskReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/moe_risk_openapiRiskReportReq"}}], "tags": ["OpenGameOpenApi"]}}}, "definitions": {"moe_risk_commAssetCheckInfo": {"type": "object", "properties": {"assetType": {"type": "integer", "format": "int32", "title": "资产类型(货币/福利/商品), eg. 短剧资产类型定义见 proto_risk_scene_dj.jce EmDJAssetType"}, "assetId": {"type": "string", "title": "资产ID(福利ID/商品ID/货币可不填)"}, "assetNum": {"type": "string", "format": "int64", "title": "资产总数量"}, "assetValue": {"type": "string", "format": "int64", "title": "资产总价值(福利/货币, 可以不填。 其他场景必填!!!, 单位是货币最小单位)"}, "assetBalance": {"type": "string", "format": "int64", "title": "资产余额(选填, 出账时余额必填)"}}, "title": "检查资产信息"}, "moe_risk_commCommSafetyInfo": {"type": "object", "properties": {"extMap": {"type": "object", "additionalProperties": {"type": "string"}}}, "title": "通用安全校验信息"}, "moe_risk_commCustomSafetyInfo": {"type": "object", "properties": {"appId": {"type": "string"}, "category": {"type": "string"}, "extMap": {"type": "object", "additionalProperties": {"type": "string"}}}, "title": "自定义安全校验信息"}, "moe_risk_commPunishDetail": {"type": "object", "properties": {"riskType": {"type": "integer", "format": "int32", "title": "命中的风控类型, RiskType"}, "punishReason": {"type": "integer", "format": "int32", "title": "打击原因枚举 不同RiskType各自定义, 安全类: 透传安全错误码, 风控策略类: RiskPunishReason"}, "strPunishReason": {"type": "string", "title": "打击原因"}, "strPrompt": {"type": "string", "title": "打击提示语"}, "strCommVerifyUrl": {"type": "string", "title": "安全验证链接"}}, "title": "风控打击详情"}, "moe_risk_commRiskAssetInfo": {"type": "object", "properties": {"assetType": {"type": "integer", "format": "int32", "title": "资产类型(福利/商品/货币), eg. 短剧资产类型定义见 proto_risk_scene_dj.jce EmDJAssetType"}, "assetId": {"type": "string", "title": "资产ID(福利ID/商品ID/货币可不填)"}, "assetNum": {"type": "string", "format": "int64", "title": "资产总数量"}, "assetValue": {"type": "string", "format": "int64", "title": "资产总价值"}, "assetBalance": {"type": "string", "format": "int64", "title": "资产余额(选填, 出账时余额必填)"}}, "title": "资产信息"}, "moe_risk_commRiskCheckReqExtraInfo": {"type": "object", "properties": {"cnt": {"type": "string", "format": "int64", "title": "指定次数,默认值1(用户多次行为合并为一次风控检查调用的场景，需指定用户行为次数)"}, "eventId": {"type": "string", "title": "幂等ID(有些同步规则, 需要校验时同时更新计数信息, 需要做幂等保护)"}}, "title": "风控校验请求额外信息"}, "moe_risk_commRiskCheckResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int64", "title": "(0-通过 1-不通过)"}, "punishType": {"type": "integer", "format": "int32", "title": "打击类型 emPunishType"}, "punishDetail": {"$ref": "#/definitions/moe_risk_commPunishDetail", "title": "打击具体信息"}}, "title": "风控打击结果"}, "moe_risk_commRiskCheckSafetyInfo": {"type": "object", "properties": {"commSafe": {"$ref": "#/definitions/moe_risk_commCommSafetyInfo", "title": "通用安全校验(全平台统一安全策略校验)"}, "customSafe": {"$ref": "#/definitions/moe_risk_commCustomSafetyInfo", "title": "自定义安全校验(自定义安全策略校验)"}}, "title": "风控校验请求安全信息"}, "moe_risk_commRiskCheckType": {"type": "string", "enum": ["RiskCheckType_Unknown", "RiskCheckType_Receive", "RiskCheckType_Direct", "RiskCheckType_Exchange", "RiskCheckType_Cashout"], "default": "RiskCheckType_Unknown", "description": "- RiskCheckType_Unknown: 无效类型\n - RiskCheckType_Receive: 领取校验(eg. 用户手动点击领奖)\n - RiskCheckType_Direct: 直充校验(eg. 看完广告/活动结束系统自动发奖)\n - RiskCheckType_Exchange: 兑换校验\n - RiskCheckType_Cashout: 提现校验", "title": "风控校验类型"}, "moe_risk_commRiskCountItem": {"type": "object", "properties": {"ts": {"type": "integer", "format": "int64", "title": "周期起始时间戳"}, "cnt": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "操作计数, key: RiskTrigType, 0表示全部"}, "value": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "价值计数, key: RiskTrigType, 0表示全部"}}, "title": "风控计数"}, "moe_risk_commRiskEvent": {"type": "object", "properties": {"eventId": {"type": "string", "title": "幂等ID"}, "sceneInfo": {"$ref": "#/definitions/moe_risk_commRiskSceneInfo", "title": "场景信息"}, "userInfo": {"$ref": "#/definitions/moe_risk_commRiskUserInfo", "title": "主人态信息"}, "opInfo": {"$ref": "#/definitions/moe_risk_commRiskOpInfo", "title": "操作信息"}, "assetInfo": {"$ref": "#/definitions/moe_risk_commRiskAssetInfo", "title": "(福利/商品/货币)变更的资产信息"}, "sysInfo": {"$ref": "#/definitions/moe_risk_commRiskSysInfo", "title": "系统信息(风控系统内部的一些信息), 业务不需要填"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "风控扩展信息, 方便不同接入场景功能扩展"}}, "title": "风控事件"}, "moe_risk_commRiskOpInfo": {"type": "object", "properties": {"opType": {"type": "integer", "format": "int32", "title": "操作类型, 定义见: RiskOpType"}, "trigType": {"type": "integer", "format": "int32", "title": "触发类型, 定义见: RiskTrigType"}, "opTimeMs": {"type": "string", "format": "int64", "title": "操作时间, 单位ms(废弃， 后续都用opTs)"}, "opTs": {"type": "string", "format": "int64", "title": "操作时间, 单位ms"}}, "title": "操作信息"}, "moe_risk_commRiskSceneInfo": {"type": "object", "properties": {"biz": {"type": "string", "title": "不同app自行定义, eg. 短剧bizId定义见: proto_risk_scene_dj.jce RISK_DJ_BIZID_xxxx"}, "scene": {"type": "integer", "format": "int32", "title": "主场景, 定义见: proto_risk_scene_comm.jce:: EmRiskScene"}, "subscene": {"type": "integer", "format": "int32", "title": "子场景, 公共定义见: proto_risk_scene_comm.jce::  EmRiskSubSceneXXX, 业务自定义枚举见: proto_risk_scene_xxx.jce EmRiskSubSceneXXX"}, "actId": {"type": "string", "title": "活动ID(一个活动可能包含多个玩法playId, 由业务自行定义)"}, "playId": {"type": "string", "title": "玩法ID(eg. 任务taskID、广告位ID、活动ID, 由业务自行定义)"}, "extId": {"type": "string", "title": "自定义扩展ID(eg. 某个任务taskID下额外的奖励发放, 某个任务taskID下广告的发放)"}}, "title": "场景信息"}, "moe_risk_commRiskStat": {"type": "object", "properties": {"inStat": {"$ref": "#/definitions/moe_risk_commRiskTimeCount", "title": "入账统计"}, "outStat": {"$ref": "#/definitions/moe_risk_commRiskTimeCount", "title": "出账统计"}}, "title": "风控统计信息"}, "moe_risk_commRiskSysInfo": {"type": "object", "properties": {"sysExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "操作时间, 单位ms"}}, "title": "系统信息(风控系统内部的一些信息)"}, "moe_risk_commRiskTimeCount": {"type": "object", "properties": {"ts": {"type": "integer", "format": "int64", "title": "当前时间戳"}, "minCount": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/moe_risk_commRiskCountItem"}, "title": "最近N分钟统计,"}, "hourCount": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/moe_risk_commRiskCountItem"}, "title": "最近N小时统计"}, "dayCount": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/moe_risk_commRiskCountItem"}, "title": "最近N天统计"}, "weekCount": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/moe_risk_commRiskCountItem"}, "title": "最近N周统计"}, "monthCount": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/moe_risk_commRiskCountItem"}, "title": "最近N月统计"}}, "title": "时间维度的计数统计"}, "moe_risk_commRiskUserInfo": {"type": "object", "properties": {"uid": {"type": "string"}, "qua": {"type": "string"}, "deviceInfo": {"type": "string"}, "clientIp": {"type": "string", "title": "风控校验接口需传，用于安全上报"}, "openId": {"type": "string"}, "qimei36": {"type": "string"}}, "title": "用户信息"}, "moe_risk_openapiGameMiddleInfo": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "uid"}, "gameAppid": {"type": "string", "title": "game app id"}, "encryptUid": {"type": "string", "title": "encrypt_uid"}, "ipv4": {"type": "integer", "format": "int64", "title": "客户端ipv4"}, "ipv6": {"type": "string", "title": "客户端ipv6"}, "strUid": {"type": "string", "title": "strUid 支持str类型的平台uid"}, "deviceInfo": {"type": "string", "title": "设备信息"}}, "title": "中台基础字段"}, "moe_risk_openapiRiskCheckReq": {"type": "object", "properties": {"middleInfo": {"$ref": "#/definitions/moe_risk_openapiGameMiddleInfo"}, "checkType": {"$ref": "#/definitions/moe_risk_commRiskCheckType", "title": "校验类型"}, "userInfo": {"$ref": "#/definitions/moe_risk_commRiskUserInfo", "title": "用户信息"}, "sceneInfo": {"$ref": "#/definitions/moe_risk_commRiskSceneInfo", "title": "场景信息"}, "assetIn": {"$ref": "#/definitions/moe_risk_commAssetCheckInfo", "title": "用户入帐信息"}, "assetOut": {"$ref": "#/definitions/moe_risk_commAssetCheckInfo", "title": "用户出账信息, 兑换/提现(RiskCheckType_Exchange/RiskCheckType_Cashout)类校验需要填出账信息"}, "extraInfo": {"$ref": "#/definitions/moe_risk_commRiskCheckReqExtraInfo", "title": "额外信息(按需设置)"}, "safeInfo": {"$ref": "#/definitions/moe_risk_commRiskCheckSafetyInfo", "title": "安全串联信息(按需设置)"}}, "title": "统一校验（同步）"}, "moe_risk_openapiRiskCheckRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "result": {"$ref": "#/definitions/moe_risk_commRiskCheckResult"}}}, "moe_risk_openapiRiskQueryReq": {"type": "object", "properties": {"middleInfo": {"$ref": "#/definitions/moe_risk_openapiGameMiddleInfo"}, "userInfo": {"$ref": "#/definitions/moe_risk_commRiskUserInfo", "title": "用户信息"}, "sceneInfo": {"$ref": "#/definitions/moe_risk_commRiskSceneInfo", "title": "场景信息"}}, "title": "风控查询"}, "moe_risk_openapiRiskQueryRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "totalStat": {"type": "object", "additionalProperties": {"$ref": "#/definitions/moe_risk_commRiskStat"}, "title": "整体维度风控信息, key: RiskDimension"}, "userStat": {"type": "object", "additionalProperties": {"$ref": "#/definitions/moe_risk_commRiskStat"}, "title": "个人维度风控信息, key: RiskDimension"}}}, "moe_risk_openapiRiskReportReq": {"type": "object", "properties": {"middleInfo": {"$ref": "#/definitions/moe_risk_openapiGameMiddleInfo"}, "event": {"$ref": "#/definitions/moe_risk_commRiskEvent"}}, "title": "风控上报"}, "moe_risk_openapiRiskReportRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "msg": {"type": "string", "title": "占位"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}