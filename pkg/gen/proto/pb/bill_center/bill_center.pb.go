// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/bill_center/bill_center.proto

package bill_center

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GiftSendType int32

const (
	GiftSendType_GIFT_SEND_UNKNOW GiftSendType = 0 //未知
	GiftSendType_GIFT_SEND_SEND   GiftSendType = 1 //送礼
	GiftSendType_GIFT_SEND_BACK   GiftSendType = 2 //回礼
)

// Enum value maps for GiftSendType.
var (
	GiftSendType_name = map[int32]string{
		0: "GIFT_SEND_UNKNOW",
		1: "GIFT_SEND_SEND",
		2: "GIFT_SEND_BACK",
	}
	GiftSendType_value = map[string]int32{
		"GIFT_SEND_UNKNOW": 0,
		"GIFT_SEND_SEND":   1,
		"GIFT_SEND_BACK":   2,
	}
)

func (x GiftSendType) Enum() *GiftSendType {
	p := new(GiftSendType)
	*p = x
	return p
}

func (x GiftSendType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GiftSendType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_bill_center_bill_center_proto_enumTypes[0].Descriptor()
}

func (GiftSendType) Type() protoreflect.EnumType {
	return &file_pb_bill_center_bill_center_proto_enumTypes[0]
}

func (x GiftSendType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GiftSendType.Descriptor instead.
func (GiftSendType) EnumDescriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{0}
}

type PulsarLiveHearbeatFlow_HeartType int32

const (
	PulsarLiveHearbeatFlow_ReportTypeUnknown   PulsarLiveHearbeatFlow_HeartType = 0
	PulsarLiveHearbeatFlow_ReportTypeEnter     PulsarLiveHearbeatFlow_HeartType = 1 // 进入
	PulsarLiveHearbeatFlow_ReportTypeHeartbeat PulsarLiveHearbeatFlow_HeartType = 2 // 心跳
	PulsarLiveHearbeatFlow_ReportTypeLeave     PulsarLiveHearbeatFlow_HeartType = 3 // 离开
)

// Enum value maps for PulsarLiveHearbeatFlow_HeartType.
var (
	PulsarLiveHearbeatFlow_HeartType_name = map[int32]string{
		0: "ReportTypeUnknown",
		1: "ReportTypeEnter",
		2: "ReportTypeHeartbeat",
		3: "ReportTypeLeave",
	}
	PulsarLiveHearbeatFlow_HeartType_value = map[string]int32{
		"ReportTypeUnknown":   0,
		"ReportTypeEnter":     1,
		"ReportTypeHeartbeat": 2,
		"ReportTypeLeave":     3,
	}
)

func (x PulsarLiveHearbeatFlow_HeartType) Enum() *PulsarLiveHearbeatFlow_HeartType {
	p := new(PulsarLiveHearbeatFlow_HeartType)
	*p = x
	return p
}

func (x PulsarLiveHearbeatFlow_HeartType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PulsarLiveHearbeatFlow_HeartType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_bill_center_bill_center_proto_enumTypes[1].Descriptor()
}

func (PulsarLiveHearbeatFlow_HeartType) Type() protoreflect.EnumType {
	return &file_pb_bill_center_bill_center_proto_enumTypes[1]
}

func (x PulsarLiveHearbeatFlow_HeartType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PulsarLiveHearbeatFlow_HeartType.Descriptor instead.
func (PulsarLiveHearbeatFlow_HeartType) EnumDescriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{1, 0}
}

type PulsarLiveHearbeatFlow_RoomType int32

const (
	PulsarLiveHearbeatFlow_RoomTypeUnknown PulsarLiveHearbeatFlow_RoomType = 0
	PulsarLiveHearbeatFlow_RoomTypeLive    PulsarLiveHearbeatFlow_RoomType = 1
	PulsarLiveHearbeatFlow_RoomTypeKTV     PulsarLiveHearbeatFlow_RoomType = 2
)

// Enum value maps for PulsarLiveHearbeatFlow_RoomType.
var (
	PulsarLiveHearbeatFlow_RoomType_name = map[int32]string{
		0: "RoomTypeUnknown",
		1: "RoomTypeLive",
		2: "RoomTypeKTV",
	}
	PulsarLiveHearbeatFlow_RoomType_value = map[string]int32{
		"RoomTypeUnknown": 0,
		"RoomTypeLive":    1,
		"RoomTypeKTV":     2,
	}
)

func (x PulsarLiveHearbeatFlow_RoomType) Enum() *PulsarLiveHearbeatFlow_RoomType {
	p := new(PulsarLiveHearbeatFlow_RoomType)
	*p = x
	return p
}

func (x PulsarLiveHearbeatFlow_RoomType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PulsarLiveHearbeatFlow_RoomType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_bill_center_bill_center_proto_enumTypes[2].Descriptor()
}

func (PulsarLiveHearbeatFlow_RoomType) Type() protoreflect.EnumType {
	return &file_pb_bill_center_bill_center_proto_enumTypes[2]
}

func (x PulsarLiveHearbeatFlow_RoomType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PulsarLiveHearbeatFlow_RoomType.Descriptor instead.
func (PulsarLiveHearbeatFlow_RoomType) EnumDescriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{1, 1}
}

type PulsarLoginFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   int64  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppId string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Ts    int64  `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *PulsarLoginFlow) Reset() {
	*x = PulsarLoginFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PulsarLoginFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PulsarLoginFlow) ProtoMessage() {}

func (x *PulsarLoginFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PulsarLoginFlow.ProtoReflect.Descriptor instead.
func (*PulsarLoginFlow) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{0}
}

func (x *PulsarLoginFlow) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PulsarLoginFlow) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PulsarLoginFlow) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type PulsarLiveHearbeatFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid           int64                            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HeartbeatType PulsarLiveHearbeatFlow_HeartType `protobuf:"varint,2,opt,name=heartbeatType,proto3,enum=component.game.PulsarLiveHearbeatFlow_HeartType" json:"heartbeatType,omitempty"`
	RoomType      PulsarLiveHearbeatFlow_RoomType  `protobuf:"varint,3,opt,name=roomType,proto3,enum=component.game.PulsarLiveHearbeatFlow_RoomType" json:"roomType,omitempty"`
	RoomId        string                           `protobuf:"bytes,4,opt,name=roomId,proto3" json:"roomId,omitempty"`
	ShowId        string                           `protobuf:"bytes,5,opt,name=showId,proto3" json:"showId,omitempty"`
	BeginTime     int64                            `protobuf:"varint,6,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	ReportTime    int64                            `protobuf:"varint,7,opt,name=reportTime,proto3" json:"reportTime,omitempty"`
}

func (x *PulsarLiveHearbeatFlow) Reset() {
	*x = PulsarLiveHearbeatFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PulsarLiveHearbeatFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PulsarLiveHearbeatFlow) ProtoMessage() {}

func (x *PulsarLiveHearbeatFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PulsarLiveHearbeatFlow.ProtoReflect.Descriptor instead.
func (*PulsarLiveHearbeatFlow) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{1}
}

func (x *PulsarLiveHearbeatFlow) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PulsarLiveHearbeatFlow) GetHeartbeatType() PulsarLiveHearbeatFlow_HeartType {
	if x != nil {
		return x.HeartbeatType
	}
	return PulsarLiveHearbeatFlow_ReportTypeUnknown
}

func (x *PulsarLiveHearbeatFlow) GetRoomType() PulsarLiveHearbeatFlow_RoomType {
	if x != nil {
		return x.RoomType
	}
	return PulsarLiveHearbeatFlow_RoomTypeUnknown
}

func (x *PulsarLiveHearbeatFlow) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *PulsarLiveHearbeatFlow) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

func (x *PulsarLiveHearbeatFlow) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *PulsarLiveHearbeatFlow) GetReportTime() int64 {
	if x != nil {
		return x.ReportTime
	}
	return 0
}

type ReportHeartbeatRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportHeartbeatRsp) Reset() {
	*x = ReportHeartbeatRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportHeartbeatRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportHeartbeatRsp) ProtoMessage() {}

func (x *ReportHeartbeatRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportHeartbeatRsp.ProtoReflect.Descriptor instead.
func (*ReportHeartbeatRsp) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{2}
}

type PulsarLiveHeartbeatFlows struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bills []*PulsarLiveHearbeatFlow `protobuf:"bytes,1,rep,name=bills,proto3" json:"bills,omitempty"`
}

func (x *PulsarLiveHeartbeatFlows) Reset() {
	*x = PulsarLiveHeartbeatFlows{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PulsarLiveHeartbeatFlows) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PulsarLiveHeartbeatFlows) ProtoMessage() {}

func (x *PulsarLiveHeartbeatFlows) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PulsarLiveHeartbeatFlows.ProtoReflect.Descriptor instead.
func (*PulsarLiveHeartbeatFlows) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{3}
}

func (x *PulsarLiveHeartbeatFlows) GetBills() []*PulsarLiveHearbeatFlow {
	if x != nil {
		return x.Bills
	}
	return nil
}

type BatchReportHeartbeatRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchReportHeartbeatRsp) Reset() {
	*x = BatchReportHeartbeatRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchReportHeartbeatRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchReportHeartbeatRsp) ProtoMessage() {}

func (x *BatchReportHeartbeatRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchReportHeartbeatRsp.ProtoReflect.Descriptor instead.
func (*BatchReportHeartbeatRsp) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{4}
}

type PulsarGiftFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId        string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`       //打赏open_id
	AppId         string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`         //appid
	GiftID        uint64 `protobuf:"varint,3,opt,name=giftID,proto3" json:"giftID,omitempty"`      //礼物ID
	GiftNum       uint64 `protobuf:"varint,4,opt,name=giftNum,proto3" json:"giftNum,omitempty"`    //礼物数量
	ConsumeID     string `protobuf:"bytes,5,opt,name=consumeID,proto3" json:"consumeID,omitempty"` //消费ID
	Pay           uint64 `protobuf:"varint,6,opt,name=pay,proto3" json:"pay,omitempty"`            //支付的价格
	RoomID        string `protobuf:"bytes,7,opt,name=roomID,proto3" json:"roomID,omitempty"`       //房间ID
	ShowID        string `protobuf:"bytes,8,opt,name=showID,proto3" json:"showID,omitempty"`       //场次ID
	Ts            uint64 `protobuf:"varint,9,opt,name=ts,proto3" json:"ts,omitempty"`              //时间
	Qua           string `protobuf:"bytes,10,opt,name=qua,proto3" json:"qua,omitempty"`
	RoundID       string `protobuf:"bytes,11,opt,name=roundID,proto3" json:"roundID,omitempty"`             //游戏场次ID
	ReceiveOpenId string `protobuf:"bytes,12,opt,name=receiveOpenId,proto3" json:"receiveOpenId,omitempty"` //被打赏open_id
	PubTimeMs     uint64 `protobuf:"varint,13,opt,name=pubTimeMs,proto3" json:"pubTimeMs,omitempty"`        //送礼流水产生时间
	SendType      uint64 `protobuf:"varint,14,opt,name=sendType,proto3" json:"sendType,omitempty"`          // GiftSendType
}

func (x *PulsarGiftFlow) Reset() {
	*x = PulsarGiftFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PulsarGiftFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PulsarGiftFlow) ProtoMessage() {}

func (x *PulsarGiftFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PulsarGiftFlow.ProtoReflect.Descriptor instead.
func (*PulsarGiftFlow) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{5}
}

func (x *PulsarGiftFlow) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PulsarGiftFlow) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PulsarGiftFlow) GetGiftID() uint64 {
	if x != nil {
		return x.GiftID
	}
	return 0
}

func (x *PulsarGiftFlow) GetGiftNum() uint64 {
	if x != nil {
		return x.GiftNum
	}
	return 0
}

func (x *PulsarGiftFlow) GetConsumeID() string {
	if x != nil {
		return x.ConsumeID
	}
	return ""
}

func (x *PulsarGiftFlow) GetPay() uint64 {
	if x != nil {
		return x.Pay
	}
	return 0
}

func (x *PulsarGiftFlow) GetRoomID() string {
	if x != nil {
		return x.RoomID
	}
	return ""
}

func (x *PulsarGiftFlow) GetShowID() string {
	if x != nil {
		return x.ShowID
	}
	return ""
}

func (x *PulsarGiftFlow) GetTs() uint64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *PulsarGiftFlow) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *PulsarGiftFlow) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *PulsarGiftFlow) GetReceiveOpenId() string {
	if x != nil {
		return x.ReceiveOpenId
	}
	return ""
}

func (x *PulsarGiftFlow) GetPubTimeMs() uint64 {
	if x != nil {
		return x.PubTimeMs
	}
	return 0
}

func (x *PulsarGiftFlow) GetSendType() uint64 {
	if x != nil {
		return x.SendType
	}
	return 0
}

type ReportGiftBillReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                //打赏uid
	AppId      string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`             //appid
	GiftID     uint64 `protobuf:"varint,3,opt,name=giftID,proto3" json:"giftID,omitempty"`          //礼物ID
	GiftNum    uint64 `protobuf:"varint,4,opt,name=giftNum,proto3" json:"giftNum,omitempty"`        //礼物数量
	ConsumeID  string `protobuf:"bytes,5,opt,name=consumeID,proto3" json:"consumeID,omitempty"`     //消费ID
	Pay        uint64 `protobuf:"varint,6,opt,name=pay,proto3" json:"pay,omitempty"`                //支付的价格
	RoomID     string `protobuf:"bytes,7,opt,name=roomID,proto3" json:"roomID,omitempty"`           //房间ID
	ShowID     string `protobuf:"bytes,8,opt,name=showID,proto3" json:"showID,omitempty"`           //场次ID
	Ts         uint64 `protobuf:"varint,9,opt,name=ts,proto3" json:"ts,omitempty"`                  //打赏时间，秒级
	Qua        string `protobuf:"bytes,10,opt,name=qua,proto3" json:"qua,omitempty"`                //版本号
	RoundID    string `protobuf:"bytes,11,opt,name=roundID,proto3" json:"roundID,omitempty"`        //游戏场次ID
	ReceiveUID uint64 `protobuf:"varint,12,opt,name=receiveUID,proto3" json:"receiveUID,omitempty"` //被打赏uid
	PubTimeMs  uint64 `protobuf:"varint,13,opt,name=pubTimeMs,proto3" json:"pubTimeMs,omitempty"`   //送礼流水产生时间
	Scene      string `protobuf:"bytes,14,opt,name=scene,proto3" json:"scene,omitempty"`            //送礼场景
	SendType   uint64 `protobuf:"varint,15,opt,name=sendType,proto3" json:"sendType,omitempty"`     // GiftSendType
}

func (x *ReportGiftBillReq) Reset() {
	*x = ReportGiftBillReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportGiftBillReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportGiftBillReq) ProtoMessage() {}

func (x *ReportGiftBillReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportGiftBillReq.ProtoReflect.Descriptor instead.
func (*ReportGiftBillReq) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{6}
}

func (x *ReportGiftBillReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *ReportGiftBillReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportGiftBillReq) GetGiftID() uint64 {
	if x != nil {
		return x.GiftID
	}
	return 0
}

func (x *ReportGiftBillReq) GetGiftNum() uint64 {
	if x != nil {
		return x.GiftNum
	}
	return 0
}

func (x *ReportGiftBillReq) GetConsumeID() string {
	if x != nil {
		return x.ConsumeID
	}
	return ""
}

func (x *ReportGiftBillReq) GetPay() uint64 {
	if x != nil {
		return x.Pay
	}
	return 0
}

func (x *ReportGiftBillReq) GetRoomID() string {
	if x != nil {
		return x.RoomID
	}
	return ""
}

func (x *ReportGiftBillReq) GetShowID() string {
	if x != nil {
		return x.ShowID
	}
	return ""
}

func (x *ReportGiftBillReq) GetTs() uint64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *ReportGiftBillReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *ReportGiftBillReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *ReportGiftBillReq) GetReceiveUID() uint64 {
	if x != nil {
		return x.ReceiveUID
	}
	return 0
}

func (x *ReportGiftBillReq) GetPubTimeMs() uint64 {
	if x != nil {
		return x.PubTimeMs
	}
	return 0
}

func (x *ReportGiftBillReq) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

func (x *ReportGiftBillReq) GetSendType() uint64 {
	if x != nil {
		return x.SendType
	}
	return 0
}

type ReportGiftBillRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportGiftBillRsp) Reset() {
	*x = ReportGiftBillRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_bill_center_bill_center_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportGiftBillRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportGiftBillRsp) ProtoMessage() {}

func (x *ReportGiftBillRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_bill_center_bill_center_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportGiftBillRsp.ProtoReflect.Descriptor instead.
func (*ReportGiftBillRsp) Descriptor() ([]byte, []int) {
	return file_pb_bill_center_bill_center_proto_rawDescGZIP(), []int{7}
}

var File_pb_bill_center_bill_center_proto protoreflect.FileDescriptor

var file_pb_bill_center_bill_center_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x49, 0x0a, 0x0f, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0xe8, 0x03, 0x0a, 0x16,
	0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x48, 0x65, 0x61, 0x72, 0x62, 0x65,
	0x61, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x72,
	0x74, 0x62, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x48, 0x65, 0x61, 0x72, 0x62,
	0x65, 0x61, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x4b, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x48, 0x65,
	0x61, 0x72, 0x62, 0x65, 0x61, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x65, 0x0a, 0x09, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x10, 0x02, 0x12, 0x13, 0x0a,
	0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x10, 0x03, 0x22, 0x42, 0x0a, 0x08, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13,
	0x0a, 0x0f, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x4c,
	0x69, 0x76, 0x65, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x4b, 0x54, 0x56, 0x10, 0x02, 0x22, 0x14, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x73, 0x70, 0x22, 0x58, 0x0a, 0x18,
	0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c, 0x69, 0x76, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x3c, 0x0a, 0x05, 0x62, 0x69, 0x6c, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c,
	0x69, 0x76, 0x65, 0x48, 0x65, 0x61, 0x72, 0x62, 0x65, 0x61, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x05, 0x62, 0x69, 0x6c, 0x6c, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x73,
	0x70, 0x22, 0xec, 0x02, 0x0a, 0x0e, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x47, 0x69, 0x66, 0x74,
	0x46, 0x6c, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x69,
	0x66, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x69, 0x66,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49,
	0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x70, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x6f, 0x77, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x74, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x44, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44,
	0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x75, 0x62, 0x54, 0x69, 0x6d,
	0x65, 0x4d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x75, 0x62, 0x54, 0x69,
	0x6d, 0x65, 0x4d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xf9, 0x02, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x69, 0x66, 0x74, 0x42,
	0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06,
	0x67, 0x69, 0x66, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x75,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x75, 0x6d,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x44, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x44, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x70, 0x61, 0x79,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77,
	0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x44,
	0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x74, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71,
	0x75, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x55, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x55, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x75, 0x62, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x70, 0x75, 0x62, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x13, 0x0a, 0x11,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x69, 0x66, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x73,
	0x70, 0x2a, 0x4c, 0x0a, 0x0c, 0x47, 0x69, 0x66, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x49, 0x46, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x47, 0x49, 0x46, 0x54, 0x5f,
	0x53, 0x45, 0x4e, 0x44, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x47,
	0x49, 0x46, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x02, 0x32,
	0xd2, 0x02, 0x0a, 0x10, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x42, 0x69, 0x6c, 0x6c, 0x43, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x12, 0x7b, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x75, 0x6c, 0x73, 0x61, 0x72, 0x4c,
	0x69, 0x76, 0x65, 0x48, 0x65, 0x61, 0x72, 0x62, 0x65, 0x61, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x1a,
	0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x52, 0x73, 0x70, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11,
	0x2f, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x69, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x75, 0x6c, 0x73, 0x61,
	0x72, 0x4c, 0x69, 0x76, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x46, 0x6c,
	0x6f, 0x77, 0x73, 0x1a, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x0e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x69, 0x66, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x21,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x69, 0x66, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x69, 0x66, 0x74, 0x42, 0x69, 0x6c,
	0x6c, 0x52, 0x73, 0x70, 0x42, 0x43, 0x5a, 0x41, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x62, 0x69,
	0x6c, 0x6c, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_bill_center_bill_center_proto_rawDescOnce sync.Once
	file_pb_bill_center_bill_center_proto_rawDescData = file_pb_bill_center_bill_center_proto_rawDesc
)

func file_pb_bill_center_bill_center_proto_rawDescGZIP() []byte {
	file_pb_bill_center_bill_center_proto_rawDescOnce.Do(func() {
		file_pb_bill_center_bill_center_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_bill_center_bill_center_proto_rawDescData)
	})
	return file_pb_bill_center_bill_center_proto_rawDescData
}

var file_pb_bill_center_bill_center_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pb_bill_center_bill_center_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_bill_center_bill_center_proto_goTypes = []interface{}{
	(GiftSendType)(0),                     // 0: component.game.GiftSendType
	(PulsarLiveHearbeatFlow_HeartType)(0), // 1: component.game.PulsarLiveHearbeatFlow.HeartType
	(PulsarLiveHearbeatFlow_RoomType)(0),  // 2: component.game.PulsarLiveHearbeatFlow.RoomType
	(*PulsarLoginFlow)(nil),               // 3: component.game.PulsarLoginFlow
	(*PulsarLiveHearbeatFlow)(nil),        // 4: component.game.PulsarLiveHearbeatFlow
	(*ReportHeartbeatRsp)(nil),            // 5: component.game.ReportHeartbeatRsp
	(*PulsarLiveHeartbeatFlows)(nil),      // 6: component.game.PulsarLiveHeartbeatFlows
	(*BatchReportHeartbeatRsp)(nil),       // 7: component.game.BatchReportHeartbeatRsp
	(*PulsarGiftFlow)(nil),                // 8: component.game.PulsarGiftFlow
	(*ReportGiftBillReq)(nil),             // 9: component.game.ReportGiftBillReq
	(*ReportGiftBillRsp)(nil),             // 10: component.game.ReportGiftBillRsp
}
var file_pb_bill_center_bill_center_proto_depIdxs = []int32{
	1,  // 0: component.game.PulsarLiveHearbeatFlow.heartbeatType:type_name -> component.game.PulsarLiveHearbeatFlow.HeartType
	2,  // 1: component.game.PulsarLiveHearbeatFlow.roomType:type_name -> component.game.PulsarLiveHearbeatFlow.RoomType
	4,  // 2: component.game.PulsarLiveHeartbeatFlows.bills:type_name -> component.game.PulsarLiveHearbeatFlow
	4,  // 3: component.game.PulsarBillCenter.ReportHeartbeat:input_type -> component.game.PulsarLiveHearbeatFlow
	6,  // 4: component.game.PulsarBillCenter.BatchReportHeartbeat:input_type -> component.game.PulsarLiveHeartbeatFlows
	9,  // 5: component.game.PulsarBillCenter.ReportGiftBill:input_type -> component.game.ReportGiftBillReq
	5,  // 6: component.game.PulsarBillCenter.ReportHeartbeat:output_type -> component.game.ReportHeartbeatRsp
	7,  // 7: component.game.PulsarBillCenter.BatchReportHeartbeat:output_type -> component.game.BatchReportHeartbeatRsp
	10, // 8: component.game.PulsarBillCenter.ReportGiftBill:output_type -> component.game.ReportGiftBillRsp
	6,  // [6:9] is the sub-list for method output_type
	3,  // [3:6] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_pb_bill_center_bill_center_proto_init() }
func file_pb_bill_center_bill_center_proto_init() {
	if File_pb_bill_center_bill_center_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_bill_center_bill_center_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PulsarLoginFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PulsarLiveHearbeatFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportHeartbeatRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PulsarLiveHeartbeatFlows); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchReportHeartbeatRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PulsarGiftFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportGiftBillReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_bill_center_bill_center_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportGiftBillRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_bill_center_bill_center_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_bill_center_bill_center_proto_goTypes,
		DependencyIndexes: file_pb_bill_center_bill_center_proto_depIdxs,
		EnumInfos:         file_pb_bill_center_bill_center_proto_enumTypes,
		MessageInfos:      file_pb_bill_center_bill_center_proto_msgTypes,
	}.Build()
	File_pb_bill_center_bill_center_proto = out.File
	file_pb_bill_center_bill_center_proto_rawDesc = nil
	file_pb_bill_center_bill_center_proto_goTypes = nil
	file_pb_bill_center_bill_center_proto_depIdxs = nil
}
