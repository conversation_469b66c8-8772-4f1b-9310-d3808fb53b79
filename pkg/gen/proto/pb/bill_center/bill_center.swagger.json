{"swagger": "2.0", "info": {"title": "pb/bill_center/bill_center.proto", "version": "version not set"}, "tags": [{"name": "PulsarBillCenter"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.PulsarBillCenter/BatchReportHeartbeat": {"post": {"operationId": "PulsarBillCenter_BatchReportHeartbeat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBatchReportHeartbeatRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gamePulsarLiveHeartbeatFlows"}}], "tags": ["PulsarBillCenter"]}}, "/component.game.PulsarBillCenter/ReportGiftBill": {"post": {"operationId": "PulsarBillCenter_ReportGiftBill", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameReportGiftBillRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameReportGiftBillReq"}}], "tags": ["PulsarBillCenter"]}}, "/heartbeat/report": {"post": {"summary": "心跳上报", "operationId": "PulsarBillCenter_ReportHeartbeat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameReportHeartbeatRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gamePulsarLiveHearbeatFlow"}}], "tags": ["PulsarBillCenter"]}}}, "definitions": {"PulsarLiveHearbeatFlowHeartType": {"type": "string", "enum": ["ReportTypeUnknown", "ReportTypeEnter", "ReportTypeHeartbeat", "ReportTypeLeave"], "default": "ReportTypeUnknown", "title": "- ReportTypeEnter: 进入\n - ReportTypeHeartbeat: 心跳\n - ReportTypeLeave: 离开"}, "PulsarLiveHearbeatFlowRoomType": {"type": "string", "enum": ["RoomTypeUnknown", "RoomTypeLive", "RoomTypeKTV"], "default": "RoomTypeUnknown"}, "gameBatchReportHeartbeatRsp": {"type": "object"}, "gamePulsarLiveHearbeatFlow": {"type": "object", "properties": {"uid": {"type": "string", "format": "int64"}, "heartbeatType": {"$ref": "#/definitions/PulsarLiveHearbeatFlowHeartType"}, "roomType": {"$ref": "#/definitions/PulsarLiveHearbeatFlowRoomType"}, "roomId": {"type": "string"}, "showId": {"type": "string"}, "beginTime": {"type": "string", "format": "int64"}, "reportTime": {"type": "string", "format": "int64"}}}, "gamePulsarLiveHeartbeatFlows": {"type": "object", "properties": {"bills": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gamePulsarLiveHearbeatFlow"}}}}, "gameReportGiftBillReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "打赏uid"}, "appId": {"type": "string", "title": "appid"}, "giftID": {"type": "string", "format": "uint64", "title": "礼物ID"}, "giftNum": {"type": "string", "format": "uint64", "title": "礼物数量"}, "consumeID": {"type": "string", "title": "消费ID"}, "pay": {"type": "string", "format": "uint64", "title": "支付的价格"}, "roomID": {"type": "string", "title": "房间ID"}, "showID": {"type": "string", "title": "场次ID"}, "ts": {"type": "string", "format": "uint64", "title": "打赏时间，秒级"}, "qua": {"type": "string", "title": "版本号"}, "roundID": {"type": "string", "title": "游戏场次ID"}, "receiveUID": {"type": "string", "format": "uint64", "title": "被打赏uid"}, "pubTimeMs": {"type": "string", "format": "uint64", "title": "送礼流水产生时间"}, "scene": {"type": "string", "title": "送礼场景"}, "sendType": {"type": "string", "format": "uint64", "title": "GiftSendType"}}}, "gameReportGiftBillRsp": {"type": "object"}, "gameReportHeartbeatRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}