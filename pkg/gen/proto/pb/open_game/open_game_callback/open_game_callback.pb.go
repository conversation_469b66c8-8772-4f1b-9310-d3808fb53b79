// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/open_game/open_game_callback/open_game_callback.proto

package open_game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ---- module 自定义配置 start ---- //
type ModuleStyle int32

const (
	ModuleStyle_SUB_MODULESTYLE_INVALID ModuleStyle = 0
	// 简易文本配置
	// 1. 阳光农场, 种子熟了
	// 2. 周周开保盒, 有奖励可以领取
	// 3. 金榜题名，绣球奖励即将开启;
	ModuleStyle_SUB_MODULESTYLE_TXT ModuleStyle = 1
	// 阳光农场, 本房的友友很多金币未领！抓住时机
	ModuleStyle_SUB_MODULESTYPE_FARM_STEAL_COINS ModuleStyle = 1000
	// 第三方卡片，例如大头斗地主在游戏页的运营态透传
	ModuleStyle_SUB_MODULESTYPE_THIRDPARTY_CARD ModuleStyle = 1001
)

// Enum value maps for ModuleStyle.
var (
	ModuleStyle_name = map[int32]string{
		0:    "SUB_MODULESTYLE_INVALID",
		1:    "SUB_MODULESTYLE_TXT",
		1000: "SUB_MODULESTYPE_FARM_STEAL_COINS",
		1001: "SUB_MODULESTYPE_THIRDPARTY_CARD",
	}
	ModuleStyle_value = map[string]int32{
		"SUB_MODULESTYLE_INVALID":          0,
		"SUB_MODULESTYLE_TXT":              1,
		"SUB_MODULESTYPE_FARM_STEAL_COINS": 1000,
		"SUB_MODULESTYPE_THIRDPARTY_CARD":  1001,
	}
)

func (x ModuleStyle) Enum() *ModuleStyle {
	p := new(ModuleStyle)
	*p = x
	return p
}

func (x ModuleStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_enumTypes[0].Descriptor()
}

func (ModuleStyle) Type() protoreflect.EnumType {
	return &file_pb_open_game_open_game_callback_open_game_callback_proto_enumTypes[0]
}

func (x ModuleStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleStyle.Descriptor instead.
func (ModuleStyle) EnumDescriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{0}
}

type BusinessConsumeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OrderId string `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *BusinessConsumeInfo) Reset() {
	*x = BusinessConsumeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessConsumeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessConsumeInfo) ProtoMessage() {}

func (x *BusinessConsumeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessConsumeInfo.ProtoReflect.Descriptor instead.
func (*BusinessConsumeInfo) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessConsumeInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BusinessConsumeInfo) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type CheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AppId   string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"` // 用于校验
	Uid     int64  `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`                 // 用于校验
	Amt     uint32 `protobuf:"varint,4,opt,name=amt,proto3" json:"amt,omitempty"`                 // 用于校验 金额
}

func (x *CheckOrderReq) Reset() {
	*x = CheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderReq) ProtoMessage() {}

func (x *CheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderReq.ProtoReflect.Descriptor instead.
func (*CheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{1}
}

func (x *CheckOrderReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckOrderReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CheckOrderReq) GetAmt() uint32 {
	if x != nil {
		return x.Amt
	}
	return 0
}

type CheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pass      uint32 `protobuf:"varint,1,opt,name=pass,proto3" json:"pass,omitempty"`             // 校验是否通过 0=否 1=是
	PayMethod uint32 `protobuf:"varint,2,opt,name=pay_method,proto3" json:"pay_method,omitempty"` // 支付方式 0=midas 1=boss
}

func (x *CheckOrderRsp) Reset() {
	*x = CheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderRsp) ProtoMessage() {}

func (x *CheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderRsp.ProtoReflect.Descriptor instead.
func (*CheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{2}
}

func (x *CheckOrderRsp) GetPass() uint32 {
	if x != nil {
		return x.Pass
	}
	return 0
}

func (x *CheckOrderRsp) GetPayMethod() uint32 {
	if x != nil {
		return x.PayMethod
	}
	return 0
}

type SuccessNotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AppId   string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"` // 用于校验
	Uid     int64  `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`                 // 用于校验
	Amt     uint32 `protobuf:"varint,4,opt,name=amt,proto3" json:"amt,omitempty"`                 // 用于校验 金额
}

func (x *SuccessNotifyReq) Reset() {
	*x = SuccessNotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuccessNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuccessNotifyReq) ProtoMessage() {}

func (x *SuccessNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuccessNotifyReq.ProtoReflect.Descriptor instead.
func (*SuccessNotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{3}
}

func (x *SuccessNotifyReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *SuccessNotifyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SuccessNotifyReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *SuccessNotifyReq) GetAmt() uint32 {
	if x != nil {
		return x.Amt
	}
	return 0
}

type SuccessNotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Remark string `protobuf:"bytes,1,opt,name=remark,proto3" json:"remark,omitempty"`
}

func (x *SuccessNotifyRsp) Reset() {
	*x = SuccessNotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuccessNotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuccessNotifyRsp) ProtoMessage() {}

func (x *SuccessNotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuccessNotifyRsp.ProtoReflect.Descriptor instead.
func (*SuccessNotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{4}
}

func (x *SuccessNotifyRsp) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type ReconcileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=app_id,proto3" json:"app_id,omitempty"`
	StartTs uint32 `protobuf:"varint,2,opt,name=start_ts,proto3" json:"start_ts,omitempty"`
	EndTs   uint32 `protobuf:"varint,3,opt,name=end_ts,proto3" json:"end_ts,omitempty"`
}

func (x *ReconcileReq) Reset() {
	*x = ReconcileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReconcileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconcileReq) ProtoMessage() {}

func (x *ReconcileReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconcileReq.ProtoReflect.Descriptor instead.
func (*ReconcileReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{5}
}

func (x *ReconcileReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReconcileReq) GetStartTs() uint32 {
	if x != nil {
		return x.StartTs
	}
	return 0
}

func (x *ReconcileReq) GetEndTs() uint32 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

type ReconcileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count uint32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Amt   uint32 `protobuf:"varint,2,opt,name=amt,proto3" json:"amt,omitempty"`
}

func (x *ReconcileRsp) Reset() {
	*x = ReconcileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReconcileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconcileRsp) ProtoMessage() {}

func (x *ReconcileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconcileRsp.ProtoReflect.Descriptor instead.
func (*ReconcileRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{6}
}

func (x *ReconcileRsp) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ReconcileRsp) GetAmt() uint32 {
	if x != nil {
		return x.Amt
	}
	return 0
}

type SceneParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrRoomID string            `protobuf:"bytes,1,opt,name=strRoomID,proto3" json:"strRoomID,omitempty"`                                                                                   // 房间id
	IRoomType int32             `protobuf:"varint,2,opt,name=iRoomType,proto3" json:"iRoomType,omitempty"`                                                                                  // 房间类型
	LAnchorID int64             `protobuf:"varint,3,opt,name=lAnchorID,proto3" json:"lAnchorID,omitempty"`                                                                                  // 主播id
	MapExt    map[string]string `protobuf:"bytes,4,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展参数
	StrShowID string            `protobuf:"bytes,5,opt,name=strShowID,proto3" json:"strShowID,omitempty"`                                                                                   // showId
}

func (x *SceneParam) Reset() {
	*x = SceneParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SceneParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SceneParam) ProtoMessage() {}

func (x *SceneParam) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SceneParam.ProtoReflect.Descriptor instead.
func (*SceneParam) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{7}
}

func (x *SceneParam) GetStrRoomID() string {
	if x != nil {
		return x.StrRoomID
	}
	return ""
}

func (x *SceneParam) GetIRoomType() int32 {
	if x != nil {
		return x.IRoomType
	}
	return 0
}

func (x *SceneParam) GetLAnchorID() int64 {
	if x != nil {
		return x.LAnchorID
	}
	return 0
}

func (x *SceneParam) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *SceneParam) GetStrShowID() string {
	if x != nil {
		return x.StrShowID
	}
	return ""
}

type PlayCorner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrDoc   string `protobuf:"bytes,1,opt,name=strDoc,proto3" json:"strDoc,omitempty"`     // 文案
	StrColor string `protobuf:"bytes,2,opt,name=strColor,proto3" json:"strColor,omitempty"` // 文案底色
}

func (x *PlayCorner) Reset() {
	*x = PlayCorner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayCorner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayCorner) ProtoMessage() {}

func (x *PlayCorner) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayCorner.ProtoReflect.Descriptor instead.
func (*PlayCorner) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{8}
}

func (x *PlayCorner) GetStrDoc() string {
	if x != nil {
		return x.StrDoc
	}
	return ""
}

func (x *PlayCorner) GetStrColor() string {
	if x != nil {
		return x.StrColor
	}
	return ""
}

type PlayModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrStyle        string `protobuf:"bytes,1,opt,name=strStyle,proto3" json:"strStyle,omitempty"`               // 样式
	StrData         string `protobuf:"bytes,2,opt,name=strData,proto3" json:"strData,omitempty"`                 // json格式
	StrSpecialState string `protobuf:"bytes,3,opt,name=strSpecialState,proto3" json:"strSpecialState,omitempty"` // 游戏特殊状态，json格式，参考SpecialState。
}

func (x *PlayModule) Reset() {
	*x = PlayModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayModule) ProtoMessage() {}

func (x *PlayModule) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayModule.ProtoReflect.Descriptor instead.
func (*PlayModule) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{9}
}

func (x *PlayModule) GetStrStyle() string {
	if x != nil {
		return x.StrStyle
	}
	return ""
}

func (x *PlayModule) GetStrData() string {
	if x != nil {
		return x.StrData
	}
	return ""
}

func (x *PlayModule) GetStrSpecialState() string {
	if x != nil {
		return x.StrSpecialState
	}
	return ""
}

type CustomizeCornerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UScene        uint32      `protobuf:"varint,1,opt,name=uScene,proto3" json:"uScene,omitempty"` // SceneID
	StSceneParams *SceneParam `protobuf:"bytes,2,opt,name=stSceneParams,proto3" json:"stSceneParams,omitempty"`
	UGameID       uint32      `protobuf:"varint,3,opt,name=uGameID,proto3" json:"uGameID,omitempty"` // uGameID
	UUid          int64       `protobuf:"varint,4,opt,name=uUid,proto3" json:"uUid,omitempty"`
}

func (x *CustomizeCornerReq) Reset() {
	*x = CustomizeCornerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizeCornerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeCornerReq) ProtoMessage() {}

func (x *CustomizeCornerReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeCornerReq.ProtoReflect.Descriptor instead.
func (*CustomizeCornerReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{10}
}

func (x *CustomizeCornerReq) GetUScene() uint32 {
	if x != nil {
		return x.UScene
	}
	return 0
}

func (x *CustomizeCornerReq) GetStSceneParams() *SceneParam {
	if x != nil {
		return x.StSceneParams
	}
	return nil
}

func (x *CustomizeCornerReq) GetUGameID() uint32 {
	if x != nil {
		return x.UGameID
	}
	return 0
}

func (x *CustomizeCornerReq) GetUUid() int64 {
	if x != nil {
		return x.UUid
	}
	return 0
}

type CustomizeCornerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StCorner *PlayCorner `protobuf:"bytes,1,opt,name=stCorner,proto3" json:"stCorner,omitempty"`
}

func (x *CustomizeCornerRsp) Reset() {
	*x = CustomizeCornerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizeCornerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeCornerRsp) ProtoMessage() {}

func (x *CustomizeCornerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeCornerRsp.ProtoReflect.Descriptor instead.
func (*CustomizeCornerRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{11}
}

func (x *CustomizeCornerRsp) GetStCorner() *PlayCorner {
	if x != nil {
		return x.StCorner
	}
	return nil
}

type CustomizeModuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UScene        uint32      `protobuf:"varint,1,opt,name=uScene,proto3" json:"uScene,omitempty"` // SceneID
	StSceneParams *SceneParam `protobuf:"bytes,2,opt,name=stSceneParams,proto3" json:"stSceneParams,omitempty"`
	UGameID       uint32      `protobuf:"varint,3,opt,name=uGameID,proto3" json:"uGameID,omitempty"` // uGameID
	UUid          int64       `protobuf:"varint,4,opt,name=uUid,proto3" json:"uUid,omitempty"`
}

func (x *CustomizeModuleReq) Reset() {
	*x = CustomizeModuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizeModuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeModuleReq) ProtoMessage() {}

func (x *CustomizeModuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeModuleReq.ProtoReflect.Descriptor instead.
func (*CustomizeModuleReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{12}
}

func (x *CustomizeModuleReq) GetUScene() uint32 {
	if x != nil {
		return x.UScene
	}
	return 0
}

func (x *CustomizeModuleReq) GetStSceneParams() *SceneParam {
	if x != nil {
		return x.StSceneParams
	}
	return nil
}

func (x *CustomizeModuleReq) GetUGameID() uint32 {
	if x != nil {
		return x.UGameID
	}
	return 0
}

func (x *CustomizeModuleReq) GetUUid() int64 {
	if x != nil {
		return x.UUid
	}
	return 0
}

type CustomizeModuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StModule *PlayModule `protobuf:"bytes,1,opt,name=stModule,proto3" json:"stModule,omitempty"`
}

func (x *CustomizeModuleRsp) Reset() {
	*x = CustomizeModuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizeModuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizeModuleRsp) ProtoMessage() {}

func (x *CustomizeModuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizeModuleRsp.ProtoReflect.Descriptor instead.
func (*CustomizeModuleRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP(), []int{13}
}

func (x *CustomizeModuleRsp) GetStModule() *PlayModule {
	if x != nil {
		return x.StModule
	}
	return nil
}

var File_pb_open_game_open_game_callback_open_game_callback_proto protoreflect.FileDescriptor

var file_pb_open_game_open_game_callback_open_game_callback_proto_rawDesc = []byte{
	0x0a, 0x38, 0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x47, 0x0a, 0x13, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x65,
	0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6d, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x61, 0x6d, 0x74, 0x22, 0x43, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61,
	0x79, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x70, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x22, 0x68, 0x0a, 0x10, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6d, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x61, 0x6d, 0x74, 0x22, 0x2a, 0x0a, 0x10, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x22, 0x5a, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x73, 0x22, 0x36, 0x0a, 0x0c,
	0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6d, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x61, 0x6d, 0x74, 0x22, 0xfa, 0x01, 0x0a, 0x0a, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x52, 0x6f, 0x6f, 0x6d, 0x49,
	0x44, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x49, 0x44, 0x12, 0x39, 0x0a,
	0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x53,
	0x68, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72,
	0x53, 0x68, 0x6f, 0x77, 0x49, 0x44, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x40, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x44, 0x6f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x72, 0x44, 0x6f, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x22, 0x6c, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x53, 0x70,
	0x65, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x73, 0x74, 0x72, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x22, 0x97, 0x01, 0x0a, 0x12, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43,
	0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x53, 0x63, 0x65,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x12, 0x3b, 0x0a, 0x0d, 0x73, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0d,
	0x73, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x75, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x75, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x55, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x75, 0x55, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x12, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x73,
	0x70, 0x12, 0x31, 0x0a, 0x08, 0x73, 0x74, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x73, 0x74, 0x43, 0x6f,
	0x72, 0x6e, 0x65, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x12, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x12, 0x3b, 0x0a, 0x0d, 0x73, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x52, 0x0d, 0x73, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x75, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x75, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x55,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x75, 0x55, 0x69, 0x64, 0x22, 0x47,
	0x0a, 0x12, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x08, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x73,
	0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x2a, 0x90, 0x01, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x55, 0x42, 0x5f, 0x4d,
	0x4f, 0x44, 0x55, 0x4c, 0x45, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44, 0x55,
	0x4c, 0x45, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x01, 0x12, 0x25, 0x0a,
	0x20, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x53, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x46, 0x41, 0x52, 0x4d, 0x5f, 0x53, 0x54, 0x45, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x49, 0x4e,
	0x53, 0x10, 0xe8, 0x07, 0x12, 0x24, 0x0a, 0x1f, 0x53, 0x55, 0x42, 0x5f, 0x4d, 0x4f, 0x44, 0x55,
	0x4c, 0x45, 0x53, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x48, 0x49, 0x52, 0x44, 0x50, 0x41, 0x52,
	0x54, 0x59, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0xe9, 0x07, 0x32, 0x80, 0x03, 0x0a, 0x10, 0x4f,
	0x70, 0x65, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x49, 0x0a, 0x0d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x12, 0x1b, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09, 0x52, 0x65,
	0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x12, 0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x17, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x0f, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a,
	0x65, 0x43, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x0f, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x69, 0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0a, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x42, 0x41, 0x5a,
	0x3f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescOnce sync.Once
	file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescData = file_pb_open_game_open_game_callback_open_game_callback_proto_rawDesc
)

func file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescGZIP() []byte {
	file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescOnce.Do(func() {
		file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescData)
	})
	return file_pb_open_game_open_game_callback_open_game_callback_proto_rawDescData
}

var file_pb_open_game_open_game_callback_open_game_callback_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pb_open_game_open_game_callback_open_game_callback_proto_goTypes = []interface{}{
	(ModuleStyle)(0),            // 0: open_game.ModuleStyle
	(*BusinessConsumeInfo)(nil), // 1: open_game.BusinessConsumeInfo
	(*CheckOrderReq)(nil),       // 2: open_game.CheckOrderReq
	(*CheckOrderRsp)(nil),       // 3: open_game.CheckOrderRsp
	(*SuccessNotifyReq)(nil),    // 4: open_game.SuccessNotifyReq
	(*SuccessNotifyRsp)(nil),    // 5: open_game.SuccessNotifyRsp
	(*ReconcileReq)(nil),        // 6: open_game.ReconcileReq
	(*ReconcileRsp)(nil),        // 7: open_game.ReconcileRsp
	(*SceneParam)(nil),          // 8: open_game.SceneParam
	(*PlayCorner)(nil),          // 9: open_game.PlayCorner
	(*PlayModule)(nil),          // 10: open_game.PlayModule
	(*CustomizeCornerReq)(nil),  // 11: open_game.CustomizeCornerReq
	(*CustomizeCornerRsp)(nil),  // 12: open_game.CustomizeCornerRsp
	(*CustomizeModuleReq)(nil),  // 13: open_game.CustomizeModuleReq
	(*CustomizeModuleRsp)(nil),  // 14: open_game.CustomizeModuleRsp
	nil,                         // 15: open_game.SceneParam.MapExtEntry
}
var file_pb_open_game_open_game_callback_open_game_callback_proto_depIdxs = []int32{
	15, // 0: open_game.SceneParam.mapExt:type_name -> open_game.SceneParam.MapExtEntry
	8,  // 1: open_game.CustomizeCornerReq.stSceneParams:type_name -> open_game.SceneParam
	9,  // 2: open_game.CustomizeCornerRsp.stCorner:type_name -> open_game.PlayCorner
	8,  // 3: open_game.CustomizeModuleReq.stSceneParams:type_name -> open_game.SceneParam
	10, // 4: open_game.CustomizeModuleRsp.stModule:type_name -> open_game.PlayModule
	4,  // 5: open_game.OpenGameCallback.SuccessNotify:input_type -> open_game.SuccessNotifyReq
	6,  // 6: open_game.OpenGameCallback.Reconcile:input_type -> open_game.ReconcileReq
	11, // 7: open_game.OpenGameCallback.CustomizeCorner:input_type -> open_game.CustomizeCornerReq
	13, // 8: open_game.OpenGameCallback.CustomizeModule:input_type -> open_game.CustomizeModuleReq
	2,  // 9: open_game.OpenGameCallback.CheckOrder:input_type -> open_game.CheckOrderReq
	5,  // 10: open_game.OpenGameCallback.SuccessNotify:output_type -> open_game.SuccessNotifyRsp
	7,  // 11: open_game.OpenGameCallback.Reconcile:output_type -> open_game.ReconcileRsp
	12, // 12: open_game.OpenGameCallback.CustomizeCorner:output_type -> open_game.CustomizeCornerRsp
	14, // 13: open_game.OpenGameCallback.CustomizeModule:output_type -> open_game.CustomizeModuleRsp
	3,  // 14: open_game.OpenGameCallback.CheckOrder:output_type -> open_game.CheckOrderRsp
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pb_open_game_open_game_callback_open_game_callback_proto_init() }
func file_pb_open_game_open_game_callback_open_game_callback_proto_init() {
	if File_pb_open_game_open_game_callback_open_game_callback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessConsumeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuccessNotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuccessNotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReconcileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReconcileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SceneParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayCorner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizeCornerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizeCornerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizeModuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizeModuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_open_game_open_game_callback_open_game_callback_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_open_game_open_game_callback_open_game_callback_proto_goTypes,
		DependencyIndexes: file_pb_open_game_open_game_callback_open_game_callback_proto_depIdxs,
		EnumInfos:         file_pb_open_game_open_game_callback_open_game_callback_proto_enumTypes,
		MessageInfos:      file_pb_open_game_open_game_callback_open_game_callback_proto_msgTypes,
	}.Build()
	File_pb_open_game_open_game_callback_open_game_callback_proto = out.File
	file_pb_open_game_open_game_callback_open_game_callback_proto_rawDesc = nil
	file_pb_open_game_open_game_callback_open_game_callback_proto_goTypes = nil
	file_pb_open_game_open_game_callback_open_game_callback_proto_depIdxs = nil
}
