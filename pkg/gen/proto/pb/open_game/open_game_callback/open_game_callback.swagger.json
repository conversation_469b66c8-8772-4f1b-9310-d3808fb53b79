{"swagger": "2.0", "info": {"title": "pb/open_game/open_game_callback/open_game_callback.proto", "version": "version not set"}, "tags": [{"name": "OpenGameCallback"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/open_game.OpenGameCallback/CheckOrder": {"post": {"summary": "订单校验", "operationId": "OpenGameCallback_CheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_gameCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_gameCheckOrderReq"}}], "tags": ["OpenGameCallback"]}}, "/open_game.OpenGameCallback/CustomizeCorner": {"post": {"summary": "角标自定义", "operationId": "OpenGameCallback_CustomizeCorner", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_gameCustomizeCornerRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_gameCustomizeCornerReq"}}], "tags": ["OpenGameCallback"]}}, "/open_game.OpenGameCallback/CustomizeModule": {"post": {"summary": "卡片自定义", "operationId": "OpenGameCallback_CustomizeModule", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_gameCustomizeModuleRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_gameCustomizeModuleReq"}}], "tags": ["OpenGameCallback"]}}, "/open_game.OpenGameCallback/Reconcile": {"post": {"summary": "对账", "operationId": "OpenGameCallback_Reconcile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_gameReconcileRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_gameReconcileReq"}}], "tags": ["OpenGameCallback"]}}, "/open_game.OpenGameCallback/SuccessNotify": {"post": {"summary": "后台成功回调", "operationId": "OpenGameCallback_SuccessNotify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_gameSuccessNotifyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_gameSuccessNotifyReq"}}], "tags": ["OpenGameCallback"]}}}, "definitions": {"open_gameCheckOrderReq": {"type": "object", "properties": {"orderId": {"type": "string"}, "appId": {"type": "string", "title": "用于校验"}, "uid": {"type": "string", "format": "int64", "title": "用于校验"}, "amt": {"type": "integer", "format": "int64", "title": "用于校验 金额"}}}, "open_gameCheckOrderRsp": {"type": "object", "properties": {"pass": {"type": "integer", "format": "int64", "title": "校验是否通过 0=否 1=是"}, "pay_method": {"type": "integer", "format": "int64", "title": "支付方式 0=midas 1=boss"}}}, "open_gameCustomizeCornerReq": {"type": "object", "properties": {"uScene": {"type": "integer", "format": "int64", "title": "SceneID"}, "stSceneParams": {"$ref": "#/definitions/open_gameSceneParam"}, "uGameID": {"type": "integer", "format": "int64", "title": "uGameID"}, "uUid": {"type": "string", "format": "int64"}}}, "open_gameCustomizeCornerRsp": {"type": "object", "properties": {"stCorner": {"$ref": "#/definitions/open_gamePlayCorner"}}}, "open_gameCustomizeModuleReq": {"type": "object", "properties": {"uScene": {"type": "integer", "format": "int64", "title": "SceneID"}, "stSceneParams": {"$ref": "#/definitions/open_gameSceneParam"}, "uGameID": {"type": "integer", "format": "int64", "title": "uGameID"}, "uUid": {"type": "string", "format": "int64"}}}, "open_gameCustomizeModuleRsp": {"type": "object", "properties": {"stModule": {"$ref": "#/definitions/open_gamePlayModule"}}}, "open_gamePlayCorner": {"type": "object", "properties": {"strDoc": {"type": "string", "title": "文案"}, "strColor": {"type": "string", "title": "文案底色"}}}, "open_gamePlayModule": {"type": "object", "properties": {"strStyle": {"type": "string", "title": "样式"}, "strData": {"type": "string", "title": "json格式"}, "strSpecialState": {"type": "string", "title": "游戏特殊状态，json格式，参考SpecialState。"}}}, "open_gameReconcileReq": {"type": "object", "properties": {"app_id": {"type": "string"}, "start_ts": {"type": "integer", "format": "int64"}, "end_ts": {"type": "integer", "format": "int64"}}}, "open_gameReconcileRsp": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64"}, "amt": {"type": "integer", "format": "int64"}}}, "open_gameSceneParam": {"type": "object", "properties": {"strRoomID": {"type": "string", "title": "房间id"}, "iRoomType": {"type": "integer", "format": "int32", "title": "房间类型"}, "lAnchorID": {"type": "string", "format": "int64", "title": "主播id"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展参数"}, "strShowID": {"type": "string", "title": "showId"}}}, "open_gameSuccessNotifyReq": {"type": "object", "properties": {"orderId": {"type": "string"}, "appId": {"type": "string", "title": "用于校验"}, "uid": {"type": "string", "format": "int64", "title": "用于校验"}, "amt": {"type": "integer", "format": "int64", "title": "用于校验 金额"}}}, "open_gameSuccessNotifyRsp": {"type": "object", "properties": {"remark": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}