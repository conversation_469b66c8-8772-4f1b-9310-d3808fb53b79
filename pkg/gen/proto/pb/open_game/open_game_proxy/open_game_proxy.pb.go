// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/open_game/open_game_proxy/open_game_proxy.proto

package open_game_proxy

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateGameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string                  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`                                              // 游戏 id
	RoomId   string                  `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`                                            // 房间 id (小于 128 字节)
	RoomType common.RoomType         `protobuf:"varint,3,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	RoundId  string                  `protobuf:"bytes,4,opt,name=roundId,proto3" json:"roundId,omitempty"`                                          // 场次 id (小于 64 字节)
	Players  []*CreateGameReq_Player `protobuf:"bytes,5,rep,name=players,proto3" json:"players,omitempty"`                                          // 玩家列表
	// 20000034: difficulty 游戏难度 (normal 普通 challenge 挑战)
	Configs      map[string]string    `protobuf:"bytes,6,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 自定义配置
	OwnerId      string               `protobuf:"bytes,7,opt,name=ownerId,proto3" json:"ownerId,omitempty"`                                                                                         // 房主 openId
	Pay          *CreateGameReq_Pay   `protobuf:"bytes,8,opt,name=pay,proto3" json:"pay,omitempty"`                                                                                                 // 付费信息
	PaidTotal    int64                `protobuf:"varint,9,opt,name=paidTotal,proto3" json:"paidTotal,omitempty"`                                                                                    // 支付总门票数
	GroupNum     uint32               `protobuf:"varint,10,opt,name=groupNum,proto3" json:"groupNum,omitempty"`                                                                                     // 队伍人数
	PlatformInfo *common.PlatformInfo `protobuf:"bytes,11,opt,name=platformInfo,proto3" json:"platformInfo,omitempty"`
	// bool createFromMatch = 12; // 通过匹配创建
	RoomVersion int64 `protobuf:"varint,13,opt,name=roomVersion,proto3" json:"roomVersion,omitempty"` // 房间版本号
}

func (x *CreateGameReq) Reset() {
	*x = CreateGameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameReq) ProtoMessage() {}

func (x *CreateGameReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameReq.ProtoReflect.Descriptor instead.
func (*CreateGameReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{0}
}

func (x *CreateGameReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CreateGameReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CreateGameReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *CreateGameReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *CreateGameReq) GetPlayers() []*CreateGameReq_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *CreateGameReq) GetConfigs() map[string]string {
	if x != nil {
		return x.Configs
	}
	return nil
}

func (x *CreateGameReq) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *CreateGameReq) GetPay() *CreateGameReq_Pay {
	if x != nil {
		return x.Pay
	}
	return nil
}

func (x *CreateGameReq) GetPaidTotal() int64 {
	if x != nil {
		return x.PaidTotal
	}
	return 0
}

func (x *CreateGameReq) GetGroupNum() uint32 {
	if x != nil {
		return x.GroupNum
	}
	return 0
}

func (x *CreateGameReq) GetPlatformInfo() *common.PlatformInfo {
	if x != nil {
		return x.PlatformInfo
	}
	return nil
}

func (x *CreateGameReq) GetRoomVersion() int64 {
	if x != nil {
		return x.RoomVersion
	}
	return 0
}

type CreateGameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateGameRsp) Reset() {
	*x = CreateGameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameRsp) ProtoMessage() {}

func (x *CreateGameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameRsp.ProtoReflect.Descriptor instead.
func (*CreateGameRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{1}
}

type GetGameRoundStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`     // appId
	RoomId  string `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`   // 房间 id
	RoundId string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次 id
}

func (x *GetGameRoundStatusReq) Reset() {
	*x = GetGameRoundStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameRoundStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameRoundStatusReq) ProtoMessage() {}

func (x *GetGameRoundStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameRoundStatusReq.ProtoReflect.Descriptor instead.
func (*GetGameRoundStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{2}
}

func (x *GetGameRoundStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetGameRoundStatusReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GetGameRoundStatusReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type GetGameRoundStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameRoundStatus   common.GameRoundStatus `protobuf:"varint,1,opt,name=gameRoundStatus,proto3,enum=interactive_game.common.GameRoundStatus" json:"gameRoundStatus,omitempty"` // 游戏场次状态
	Results           []*common.PlayerResult `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`                                                               // 各玩家游戏结果
	RoundStatus       common.RoundStatus     `protobuf:"varint,3,opt,name=roundStatus,proto3,enum=interactive_game.common.RoundStatus" json:"roundStatus,omitempty"`             // 场次状态
	RequireSettlement bool                   `protobuf:"varint,4,opt,name=requireSettlement,proto3" json:"requireSettlement,omitempty"`                                          // 需要结算
	SettlementPool    int64                  `protobuf:"varint,5,opt,name=settlementPool,proto3" json:"settlementPool,omitempty"`                                                // 结算奖池 用于游戏自定义抽成 需要 <= CreateGame 的门票总数 不传默认为门票总数
}

func (x *GetGameRoundStatusRsp) Reset() {
	*x = GetGameRoundStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGameRoundStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGameRoundStatusRsp) ProtoMessage() {}

func (x *GetGameRoundStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGameRoundStatusRsp.ProtoReflect.Descriptor instead.
func (*GetGameRoundStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{3}
}

func (x *GetGameRoundStatusRsp) GetGameRoundStatus() common.GameRoundStatus {
	if x != nil {
		return x.GameRoundStatus
	}
	return common.GameRoundStatus(0)
}

func (x *GetGameRoundStatusRsp) GetResults() []*common.PlayerResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *GetGameRoundStatusRsp) GetRoundStatus() common.RoundStatus {
	if x != nil {
		return x.RoundStatus
	}
	return common.RoundStatus(0)
}

func (x *GetGameRoundStatusRsp) GetRequireSettlement() bool {
	if x != nil {
		return x.RequireSettlement
	}
	return false
}

func (x *GetGameRoundStatusRsp) GetSettlementPool() int64 {
	if x != nil {
		return x.SettlementPool
	}
	return 0
}

type ClearRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	RoomId string `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *ClearRoomReq) Reset() {
	*x = ClearRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearRoomReq) ProtoMessage() {}

func (x *ClearRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearRoomReq.ProtoReflect.Descriptor instead.
func (*ClearRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{4}
}

func (x *ClearRoomReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ClearRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type ClearRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearRoomRsp) Reset() {
	*x = ClearRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearRoomRsp) ProtoMessage() {}

func (x *ClearRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearRoomRsp.ProtoReflect.Descriptor instead.
func (*ClearRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{5}
}

// 游戏调用中台结构
type PlayerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId          string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	Win             bool   `protobuf:"varint,2,opt,name=win,proto3" json:"win,omitempty"`
	Rank            uint32 `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`                                             // 排名 从 1 开始
	SettlementExtra string `protobuf:"bytes,4,opt,name=settlement_extra,json=settlementExtra,proto3" json:"settlement_extra,omitempty"` // 结算展示文案
}

func (x *PlayerResult) Reset() {
	*x = PlayerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerResult) ProtoMessage() {}

func (x *PlayerResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerResult.ProtoReflect.Descriptor instead.
func (*PlayerResult) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{6}
}

func (x *PlayerResult) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PlayerResult) GetWin() bool {
	if x != nil {
		return x.Win
	}
	return false
}

func (x *PlayerResult) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *PlayerResult) GetSettlementExtra() string {
	if x != nil {
		return x.SettlementExtra
	}
	return ""
}

type ReportRoundStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId                string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	RoomId               string                 `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	RoundId              string                 `protobuf:"bytes,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	BattleStartAt        uint32                 `protobuf:"varint,4,opt,name=battle_start_at,json=battleStartAt,proto3" json:"battle_start_at,omitempty"`
	BattleEndAt          uint32                 `protobuf:"varint,5,opt,name=battle_end_at,json=battleEndAt,proto3" json:"battle_end_at,omitempty"`
	RoundStatus          common.GameRoundStatus `protobuf:"varint,6,opt,name=round_status,json=roundStatus,proto3,enum=interactive_game.common.GameRoundStatus" json:"round_status,omitempty"`           // 游戏场次状态
	RoundOverStatus      common.RoundStatus     `protobuf:"varint,7,opt,name=round_over_status,json=roundOverStatus,proto3,enum=interactive_game.common.RoundStatus" json:"round_over_status,omitempty"` // 场次状态
	ReportGameInfoExtras string                 `protobuf:"bytes,8,opt,name=report_game_info_extras,json=reportGameInfoExtras,proto3" json:"report_game_info_extras,omitempty"`
	Results              []*PlayerResult        `protobuf:"bytes,9,rep,name=results,proto3" json:"results,omitempty"` // 各玩家游戏结果
}

func (x *ReportRoundStatusReq) Reset() {
	*x = ReportRoundStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportRoundStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportRoundStatusReq) ProtoMessage() {}

func (x *ReportRoundStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportRoundStatusReq.ProtoReflect.Descriptor instead.
func (*ReportRoundStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{7}
}

func (x *ReportRoundStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportRoundStatusReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *ReportRoundStatusReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *ReportRoundStatusReq) GetBattleStartAt() uint32 {
	if x != nil {
		return x.BattleStartAt
	}
	return 0
}

func (x *ReportRoundStatusReq) GetBattleEndAt() uint32 {
	if x != nil {
		return x.BattleEndAt
	}
	return 0
}

func (x *ReportRoundStatusReq) GetRoundStatus() common.GameRoundStatus {
	if x != nil {
		return x.RoundStatus
	}
	return common.GameRoundStatus(0)
}

func (x *ReportRoundStatusReq) GetRoundOverStatus() common.RoundStatus {
	if x != nil {
		return x.RoundOverStatus
	}
	return common.RoundStatus(0)
}

func (x *ReportRoundStatusReq) GetReportGameInfoExtras() string {
	if x != nil {
		return x.ReportGameInfoExtras
	}
	return ""
}

func (x *ReportRoundStatusReq) GetResults() []*PlayerResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type ReportRoundStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportRoundStatusRsp) Reset() {
	*x = ReportRoundStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportRoundStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportRoundStatusRsp) ProtoMessage() {}

func (x *ReportRoundStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportRoundStatusRsp.ProtoReflect.Descriptor instead.
func (*ReportRoundStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{8}
}

type CreateGameReq_Pay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode   common.PayMode `protobuf:"varint,1,opt,name=mode,proto3,enum=interactive_game.common.PayMode" json:"mode,omitempty"` // 付费模式
	Num    int64          `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`                                        // 门票价格
	ModeId string         `protobuf:"bytes,3,opt,name=modeId,proto3" json:"modeId,omitempty"`                                   // 模式 id
}

func (x *CreateGameReq_Pay) Reset() {
	*x = CreateGameReq_Pay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameReq_Pay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameReq_Pay) ProtoMessage() {}

func (x *CreateGameReq_Pay) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameReq_Pay.ProtoReflect.Descriptor instead.
func (*CreateGameReq_Pay) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateGameReq_Pay) GetMode() common.PayMode {
	if x != nil {
		return x.Mode
	}
	return common.PayMode(0)
}

func (x *CreateGameReq_Pay) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *CreateGameReq_Pay) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

type CreateGameReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index  uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` // 座位号
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *CreateGameReq_Player) Reset() {
	*x = CreateGameReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameReq_Player) ProtoMessage() {}

func (x *CreateGameReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameReq_Player.ProtoReflect.Descriptor instead.
func (*CreateGameReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP(), []int{0, 1}
}

func (x *CreateGameReq_Player) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *CreateGameReq_Player) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

var File_pb_open_game_open_game_proxy_open_game_proxy_proto protoreflect.FileDescriptor

var file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDesc = []byte{
	0x0a, 0x32, 0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf0,
	0x05, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x3d,
	0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x45, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x03, 0x70, 0x61, 0x79,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x61, 0x79, 0x52, 0x03, 0x70, 0x61, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x61, 0x69, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x69, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x75, 0x6d, 0x12, 0x49, 0x0a, 0x0c, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x6f, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x6f, 0x6f, 0x6d, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x65, 0x0a, 0x03, 0x50, 0x61, 0x79, 0x12, 0x34, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x1a, 0x36, 0x0a,
	0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x5f, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x22, 0xca, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a,
	0x0f, 0x67, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0f, 0x67, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3f, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x73, 0x12, 0x46, 0x0a, 0x0b, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6f, 0x6c,
	0x22, 0x3c, 0x0a, 0x0c, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x0e,
	0x0a, 0x0c, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x22, 0x78,
	0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x29, 0x0a,
	0x10, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x22, 0xbc, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x65,
	0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x62, 0x61, 0x74,
	0x74, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x4b, 0x0a, 0x0c, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x11, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x75, 0x6e, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x4f, 0x76, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x37,
	0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x16, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x32,
	0xf1, 0x02, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x12, 0x4c, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1e, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x64, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e,
	0x47, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x61, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x78, 0x79, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x09, 0x43, 0x6c, 0x65, 0x61,
	0x72, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x52, 0x6f, 0x6f,
	0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x52, 0x6f, 0x6f, 0x6d,
	0x52, 0x73, 0x70, 0x42, 0x51, 0x5a, 0x4f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65,
	0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f,
	0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescOnce sync.Once
	file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescData = file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDesc
)

func file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescGZIP() []byte {
	file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescOnce.Do(func() {
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescData)
	})
	return file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDescData
}

var file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_pb_open_game_open_game_proxy_open_game_proxy_proto_goTypes = []interface{}{
	(*CreateGameReq)(nil),         // 0: open_game_proxy.CreateGameReq
	(*CreateGameRsp)(nil),         // 1: open_game_proxy.CreateGameRsp
	(*GetGameRoundStatusReq)(nil), // 2: open_game_proxy.GetGameRoundStatusReq
	(*GetGameRoundStatusRsp)(nil), // 3: open_game_proxy.GetGameRoundStatusRsp
	(*ClearRoomReq)(nil),          // 4: open_game_proxy.ClearRoomReq
	(*ClearRoomRsp)(nil),          // 5: open_game_proxy.ClearRoomRsp
	(*PlayerResult)(nil),          // 6: open_game_proxy.PlayerResult
	(*ReportRoundStatusReq)(nil),  // 7: open_game_proxy.ReportRoundStatusReq
	(*ReportRoundStatusRsp)(nil),  // 8: open_game_proxy.ReportRoundStatusRsp
	(*CreateGameReq_Pay)(nil),     // 9: open_game_proxy.CreateGameReq.Pay
	(*CreateGameReq_Player)(nil),  // 10: open_game_proxy.CreateGameReq.Player
	nil,                           // 11: open_game_proxy.CreateGameReq.ConfigsEntry
	(common.RoomType)(0),          // 12: interactive_game.common.RoomType
	(*common.PlatformInfo)(nil),   // 13: interactive_game.common.PlatformInfo
	(common.GameRoundStatus)(0),   // 14: interactive_game.common.GameRoundStatus
	(*common.PlayerResult)(nil),   // 15: interactive_game.common.PlayerResult
	(common.RoundStatus)(0),       // 16: interactive_game.common.RoundStatus
	(common.PayMode)(0),           // 17: interactive_game.common.PayMode
}
var file_pb_open_game_open_game_proxy_open_game_proxy_proto_depIdxs = []int32{
	12, // 0: open_game_proxy.CreateGameReq.roomType:type_name -> interactive_game.common.RoomType
	10, // 1: open_game_proxy.CreateGameReq.players:type_name -> open_game_proxy.CreateGameReq.Player
	11, // 2: open_game_proxy.CreateGameReq.configs:type_name -> open_game_proxy.CreateGameReq.ConfigsEntry
	9,  // 3: open_game_proxy.CreateGameReq.pay:type_name -> open_game_proxy.CreateGameReq.Pay
	13, // 4: open_game_proxy.CreateGameReq.platformInfo:type_name -> interactive_game.common.PlatformInfo
	14, // 5: open_game_proxy.GetGameRoundStatusRsp.gameRoundStatus:type_name -> interactive_game.common.GameRoundStatus
	15, // 6: open_game_proxy.GetGameRoundStatusRsp.results:type_name -> interactive_game.common.PlayerResult
	16, // 7: open_game_proxy.GetGameRoundStatusRsp.roundStatus:type_name -> interactive_game.common.RoundStatus
	14, // 8: open_game_proxy.ReportRoundStatusReq.round_status:type_name -> interactive_game.common.GameRoundStatus
	16, // 9: open_game_proxy.ReportRoundStatusReq.round_over_status:type_name -> interactive_game.common.RoundStatus
	6,  // 10: open_game_proxy.ReportRoundStatusReq.results:type_name -> open_game_proxy.PlayerResult
	17, // 11: open_game_proxy.CreateGameReq.Pay.mode:type_name -> interactive_game.common.PayMode
	0,  // 12: open_game_proxy.OpenGameProxy.CreateGame:input_type -> open_game_proxy.CreateGameReq
	2,  // 13: open_game_proxy.OpenGameProxy.GetGameRoundStatus:input_type -> open_game_proxy.GetGameRoundStatusReq
	7,  // 14: open_game_proxy.OpenGameProxy.ReportRoundStatus:input_type -> open_game_proxy.ReportRoundStatusReq
	4,  // 15: open_game_proxy.OpenGameProxy.ClearRoom:input_type -> open_game_proxy.ClearRoomReq
	1,  // 16: open_game_proxy.OpenGameProxy.CreateGame:output_type -> open_game_proxy.CreateGameRsp
	3,  // 17: open_game_proxy.OpenGameProxy.GetGameRoundStatus:output_type -> open_game_proxy.GetGameRoundStatusRsp
	8,  // 18: open_game_proxy.OpenGameProxy.ReportRoundStatus:output_type -> open_game_proxy.ReportRoundStatusRsp
	5,  // 19: open_game_proxy.OpenGameProxy.ClearRoom:output_type -> open_game_proxy.ClearRoomRsp
	16, // [16:20] is the sub-list for method output_type
	12, // [12:16] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_pb_open_game_open_game_proxy_open_game_proxy_proto_init() }
func file_pb_open_game_open_game_proxy_open_game_proxy_proto_init() {
	if File_pb_open_game_open_game_proxy_open_game_proxy_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameRoundStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGameRoundStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportRoundStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportRoundStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameReq_Pay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_open_game_open_game_proxy_open_game_proxy_proto_goTypes,
		DependencyIndexes: file_pb_open_game_open_game_proxy_open_game_proxy_proto_depIdxs,
		MessageInfos:      file_pb_open_game_open_game_proxy_open_game_proxy_proto_msgTypes,
	}.Build()
	File_pb_open_game_open_game_proxy_open_game_proxy_proto = out.File
	file_pb_open_game_open_game_proxy_open_game_proxy_proto_rawDesc = nil
	file_pb_open_game_open_game_proxy_open_game_proxy_proto_goTypes = nil
	file_pb_open_game_open_game_proxy_open_game_proxy_proto_depIdxs = nil
}
