// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_unified_assets/unified_assets/unified_app.proto

package unified_assets

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AppID int32

const (
	AppID_APP_UNKNOW AppID = 0 //未知
	AppID_APP_KG     AppID = 1 //kg
	AppID_APP_MLIVE  AppID = 2 //q音直播
	AppID_APP_WESING AppID = 3 //wesing
	AppID_APP_KUGOU  AppID = 4 //酷狗
)

// Enum value maps for AppID.
var (
	AppID_name = map[int32]string{
		0: "APP_UNKNOW",
		1: "APP_KG",
		2: "APP_MLIVE",
		3: "APP_WESING",
		4: "APP_KUGOU",
	}
	AppID_value = map[string]int32{
		"APP_UNKNOW": 0,
		"APP_KG":     1,
		"APP_MLIVE":  2,
		"APP_WESING": 3,
		"APP_KUGOU":  4,
	}
)

func (x AppID) Enum() *AppID {
	p := new(AppID)
	*p = x
	return p
}

func (x AppID) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppID) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_unified_assets_unified_assets_unified_app_proto_enumTypes[0].Descriptor()
}

func (AppID) Type() protoreflect.EnumType {
	return &file_pb_adapter_unified_assets_unified_assets_unified_app_proto_enumTypes[0]
}

func (x AppID) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppID.Descriptor instead.
func (AppID) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescGZIP(), []int{0}
}

var File_pb_adapter_unified_assets_unified_assets_unified_app_proto protoreflect.FileDescriptor

var file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2a, 0x51, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x50, 0x50, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x50, 0x50, 0x5f, 0x4b, 0x47, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x50, 0x50, 0x5f, 0x4d, 0x4c, 0x49, 0x56, 0x45, 0x10, 0x02,
	0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x50, 0x50, 0x5f, 0x57, 0x45, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x03,
	0x12, 0x0d, 0x0a, 0x09, 0x41, 0x50, 0x50, 0x5f, 0x4b, 0x55, 0x47, 0x4f, 0x55, 0x10, 0x04, 0x42,
	0x5d, 0x5a, 0x5b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f,
	0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescOnce sync.Once
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescData = file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDesc
)

func file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescGZIP() []byte {
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescOnce.Do(func() {
		file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescData)
	})
	return file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDescData
}

var file_pb_adapter_unified_assets_unified_assets_unified_app_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_adapter_unified_assets_unified_assets_unified_app_proto_goTypes = []interface{}{
	(AppID)(0), // 0: unified_assets.AppID
}
var file_pb_adapter_unified_assets_unified_assets_unified_app_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_adapter_unified_assets_unified_assets_unified_app_proto_init() }
func file_pb_adapter_unified_assets_unified_assets_unified_app_proto_init() {
	if File_pb_adapter_unified_assets_unified_assets_unified_app_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_adapter_unified_assets_unified_assets_unified_app_proto_goTypes,
		DependencyIndexes: file_pb_adapter_unified_assets_unified_assets_unified_app_proto_depIdxs,
		EnumInfos:         file_pb_adapter_unified_assets_unified_assets_unified_app_proto_enumTypes,
	}.Build()
	File_pb_adapter_unified_assets_unified_assets_unified_app_proto = out.File
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_rawDesc = nil
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_goTypes = nil
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_depIdxs = nil
}
