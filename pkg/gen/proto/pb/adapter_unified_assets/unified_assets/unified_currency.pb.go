// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_unified_assets/unified_assets/unified_currency.proto

package unified_assets

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 查询余额
type GetBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   AppID  `protobuf:"varint,1,opt,name=app_id,json=appId,proto3,enum=unified_assets.AppID" json:"app_id,omitempty"` //不同的app 参考 unified_app.AppID
	UserId  string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Program string `protobuf:"bytes,3,opt,name=program,proto3" json:"program,omitempty"` //主调业务(不能包含中文)
	Qua     string `protobuf:"bytes,4,opt,name=qua,proto3" json:"qua,omitempty"`
}

func (x *GetBalanceReq) Reset() {
	*x = GetBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceReq) ProtoMessage() {}

func (x *GetBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceReq.ProtoReflect.Descriptor instead.
func (*GetBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescGZIP(), []int{0}
}

func (x *GetBalanceReq) GetAppId() AppID {
	if x != nil {
		return x.AppId
	}
	return AppID_APP_UNKNOW
}

func (x *GetBalanceReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetBalanceReq) GetProgram() string {
	if x != nil {
		return x.Program
	}
	return ""
}

func (x *GetBalanceReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

type GetBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance int64 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *GetBalanceRsp) Reset() {
	*x = GetBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceRsp) ProtoMessage() {}

func (x *GetBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceRsp.ProtoReflect.Descriptor instead.
func (*GetBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescGZIP(), []int{1}
}

func (x *GetBalanceRsp) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

var File_pb_adapter_unified_assets_unified_assets_unified_currency_proto protoreflect.FileDescriptor

var file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0e, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x1a, 0x3a, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x66,
	0x69, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x82, 0x01,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x2c, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2e, 0x41, 0x70, 0x70, 0x49, 0x44, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71,
	0x75, 0x61, 0x22, 0x29, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x5d, 0x5a,
	0x5b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescOnce sync.Once
	file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescData = file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDesc
)

func file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescGZIP() []byte {
	file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescOnce.Do(func() {
		file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescData)
	})
	return file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDescData
}

var file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_goTypes = []interface{}{
	(*GetBalanceReq)(nil), // 0: unified_assets.GetBalanceReq
	(*GetBalanceRsp)(nil), // 1: unified_assets.GetBalanceRsp
	(AppID)(0),            // 2: unified_assets.AppID
}
var file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_depIdxs = []int32{
	2, // 0: unified_assets.GetBalanceReq.app_id:type_name -> unified_assets.AppID
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_init() }
func file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_init() {
	if File_pb_adapter_unified_assets_unified_assets_unified_currency_proto != nil {
		return
	}
	file_pb_adapter_unified_assets_unified_assets_unified_app_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_goTypes,
		DependencyIndexes: file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_depIdxs,
		MessageInfos:      file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_msgTypes,
	}.Build()
	File_pb_adapter_unified_assets_unified_assets_unified_currency_proto = out.File
	file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_rawDesc = nil
	file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_goTypes = nil
	file_pb_adapter_unified_assets_unified_assets_unified_currency_proto_depIdxs = nil
}
