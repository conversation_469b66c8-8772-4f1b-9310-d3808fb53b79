// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/adapter_unified_assets/adapter_unified_assets_callback/adapter_unified_assets_callback.proto

/*
Package adapter_unified_assets is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package adapter_unified_assets

import (
	"context"
	"io"
	"kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_Callback_GiftPackageBusinessCheckSend_0(ctx context.Context, marshaler runtime.Marshaler, client CallbackClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq callback.GiftPackageBusinessCheckSendReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.GiftPackageBusinessCheckSend(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_Callback_GiftPackageBusinessCheckSend_0(ctx context.Context, marshaler runtime.Marshaler, server CallbackServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq callback.GiftPackageBusinessCheckSendReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.GiftPackageBusinessCheckSend(ctx, &protoReq)
	return msg, metadata, err

}

func request_Callback_OrderShipment_0(ctx context.Context, marshaler runtime.Marshaler, client CallbackClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq callback.OrderShipmentReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.OrderShipment(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_Callback_OrderShipment_0(ctx context.Context, marshaler runtime.Marshaler, server CallbackServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq callback.OrderShipmentReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.OrderShipment(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterCallbackHandlerServer registers the http handlers for service Callback to "mux".
// UnaryRPC     :call CallbackServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterCallbackHandlerFromEndpoint instead.
func RegisterCallbackHandlerServer(ctx context.Context, mux *runtime.ServeMux, server CallbackServer) error {

	mux.Handle("POST", pattern_Callback_GiftPackageBusinessCheckSend_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/adapter_unified_assets.Callback/GiftPackageBusinessCheckSend", runtime.WithHTTPPathPattern("/adapter_unified_assets.Callback/GiftPackageBusinessCheckSend"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_Callback_GiftPackageBusinessCheckSend_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_Callback_GiftPackageBusinessCheckSend_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_Callback_OrderShipment_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/adapter_unified_assets.Callback/OrderShipment", runtime.WithHTTPPathPattern("/adapter_unified_assets.Callback/OrderShipment"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_Callback_OrderShipment_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_Callback_OrderShipment_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterCallbackHandlerFromEndpoint is same as RegisterCallbackHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterCallbackHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterCallbackHandler(ctx, mux, conn)
}

// RegisterCallbackHandler registers the http handlers for service Callback to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterCallbackHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterCallbackHandlerClient(ctx, mux, NewCallbackClient(conn))
}

// RegisterCallbackHandlerClient registers the http handlers for service Callback
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "CallbackClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "CallbackClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "CallbackClient" to call the correct interceptors.
func RegisterCallbackHandlerClient(ctx context.Context, mux *runtime.ServeMux, client CallbackClient) error {

	mux.Handle("POST", pattern_Callback_GiftPackageBusinessCheckSend_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/adapter_unified_assets.Callback/GiftPackageBusinessCheckSend", runtime.WithHTTPPathPattern("/adapter_unified_assets.Callback/GiftPackageBusinessCheckSend"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_Callback_GiftPackageBusinessCheckSend_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_Callback_GiftPackageBusinessCheckSend_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_Callback_OrderShipment_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/adapter_unified_assets.Callback/OrderShipment", runtime.WithHTTPPathPattern("/adapter_unified_assets.Callback/OrderShipment"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_Callback_OrderShipment_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_Callback_OrderShipment_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_Callback_GiftPackageBusinessCheckSend_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"adapter_unified_assets.Callback", "GiftPackageBusinessCheckSend"}, ""))

	pattern_Callback_OrderShipment_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"adapter_unified_assets.Callback", "OrderShipment"}, ""))
)

var (
	forward_Callback_GiftPackageBusinessCheckSend_0 = runtime.ForwardResponseMessage

	forward_Callback_OrderShipment_0 = runtime.ForwardResponseMessage
)
