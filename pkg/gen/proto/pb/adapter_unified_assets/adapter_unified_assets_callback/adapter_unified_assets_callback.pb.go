// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_unified_assets/adapter_unified_assets_callback/adapter_unified_assets_callback.proto

package adapter_unified_assets

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GiftPackageBusinessCheckSendRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data      *callback.GiftPackageBusinessCheckSendRsp `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	ErrorCode int32                                     `protobuf:"varint,2,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string                                    `protobuf:"bytes,3,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *GiftPackageBusinessCheckSendRsp) Reset() {
	*x = GiftPackageBusinessCheckSendRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftPackageBusinessCheckSendRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftPackageBusinessCheckSendRsp) ProtoMessage() {}

func (x *GiftPackageBusinessCheckSendRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftPackageBusinessCheckSendRsp.ProtoReflect.Descriptor instead.
func (*GiftPackageBusinessCheckSendRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescGZIP(), []int{0}
}

func (x *GiftPackageBusinessCheckSendRsp) GetData() *callback.GiftPackageBusinessCheckSendRsp {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GiftPackageBusinessCheckSendRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *GiftPackageBusinessCheckSendRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type OrderShipmentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data      *callback.OrderShipmentRsp `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	ErrorCode int32                      `protobuf:"varint,2,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string                     `protobuf:"bytes,3,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *OrderShipmentRsp) Reset() {
	*x = OrderShipmentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderShipmentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderShipmentRsp) ProtoMessage() {}

func (x *OrderShipmentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderShipmentRsp.ProtoReflect.Descriptor instead.
func (*OrderShipmentRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescGZIP(), []int{1}
}

func (x *OrderShipmentRsp) GetData() *callback.OrderShipmentRsp {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *OrderShipmentRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *OrderShipmentRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

var File_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto protoreflect.FileDescriptor

var file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDesc = []byte{
	0x0a, 0x5f, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x61, 0x64, 0x61, 0x70,
	0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x61, 0x64, 0x61, 0x70,
	0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x16, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x1a, 0x31, 0x70, 0x62, 0x2f, 0x61, 0x64,
	0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x01, 0x0a,
	0x1f, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x3d, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x73, 0x70, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x22, 0x80, 0x01,
	0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67,
	0x32, 0xe6, 0x01, 0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x82, 0x01,
	0x0a, 0x1c, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x12, 0x29,
	0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x61, 0x64, 0x61, 0x70,
	0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x55, 0x0a, 0x0d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x28, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x4e, 0x5a, 0x4c, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescOnce sync.Once
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescData = file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDesc
)

func file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescGZIP() []byte {
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescOnce.Do(func() {
		file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescData)
	})
	return file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDescData
}

var file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_goTypes = []interface{}{
	(*GiftPackageBusinessCheckSendRsp)(nil),          // 0: adapter_unified_assets.GiftPackageBusinessCheckSendRsp
	(*OrderShipmentRsp)(nil),                         // 1: adapter_unified_assets.OrderShipmentRsp
	(*callback.GiftPackageBusinessCheckSendRsp)(nil), // 2: callback.GiftPackageBusinessCheckSendRsp
	(*callback.OrderShipmentRsp)(nil),                // 3: callback.OrderShipmentRsp
	(*callback.GiftPackageBusinessCheckSendReq)(nil), // 4: callback.GiftPackageBusinessCheckSendReq
	(*callback.OrderShipmentReq)(nil),                // 5: callback.OrderShipmentReq
}
var file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_depIdxs = []int32{
	2, // 0: adapter_unified_assets.GiftPackageBusinessCheckSendRsp.data:type_name -> callback.GiftPackageBusinessCheckSendRsp
	3, // 1: adapter_unified_assets.OrderShipmentRsp.data:type_name -> callback.OrderShipmentRsp
	4, // 2: adapter_unified_assets.Callback.GiftPackageBusinessCheckSend:input_type -> callback.GiftPackageBusinessCheckSendReq
	5, // 3: adapter_unified_assets.Callback.OrderShipment:input_type -> callback.OrderShipmentReq
	0, // 4: adapter_unified_assets.Callback.GiftPackageBusinessCheckSend:output_type -> adapter_unified_assets.GiftPackageBusinessCheckSendRsp
	1, // 5: adapter_unified_assets.Callback.OrderShipment:output_type -> adapter_unified_assets.OrderShipmentRsp
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() {
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_init()
}
func file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_init() {
	if File_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftPackageBusinessCheckSendRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderShipmentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_goTypes,
		DependencyIndexes: file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_depIdxs,
		MessageInfos:      file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_msgTypes,
	}.Build()
	File_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto = out.File
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_rawDesc = nil
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_goTypes = nil
	file_pb_adapter_unified_assets_adapter_unified_assets_callback_adapter_unified_assets_callback_proto_depIdxs = nil
}
