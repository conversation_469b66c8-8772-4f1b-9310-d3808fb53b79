// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/adapter_unified_assets/adapter_unified_assets/adapter_unified_assets.proto

package adapter_unified_assets

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AdapterUnifiedAsset_Pay_FullMethodName                 = "/adapter_unified_assets.AdapterUnifiedAsset/Pay"
	AdapterUnifiedAsset_PayAndGiveGifts_FullMethodName     = "/adapter_unified_assets.AdapterUnifiedAsset/PayAndGiveGifts"
	AdapterUnifiedAsset_GiveGifts_FullMethodName           = "/adapter_unified_assets.AdapterUnifiedAsset/GiveGifts"
	AdapterUnifiedAsset_GetGiftPackage_FullMethodName      = "/adapter_unified_assets.AdapterUnifiedAsset/GetGiftPackage"
	AdapterUnifiedAsset_BatchGetGiftPackage_FullMethodName = "/adapter_unified_assets.AdapterUnifiedAsset/BatchGetGiftPackage"
	AdapterUnifiedAsset_SendGiftPackage_FullMethodName     = "/adapter_unified_assets.AdapterUnifiedAsset/SendGiftPackage"
	AdapterUnifiedAsset_GetGiftInfo_FullMethodName         = "/adapter_unified_assets.AdapterUnifiedAsset/GetGiftInfo"
	AdapterUnifiedAsset_BatchGetGiftInfo_FullMethodName    = "/adapter_unified_assets.AdapterUnifiedAsset/BatchGetGiftInfo"
	AdapterUnifiedAsset_GetBalance_FullMethodName          = "/adapter_unified_assets.AdapterUnifiedAsset/GetBalance"
	AdapterUnifiedAsset_GetOrderStatus_FullMethodName      = "/adapter_unified_assets.AdapterUnifiedAsset/GetOrderStatus"
)

// AdapterUnifiedAssetClient is the client API for AdapterUnifiedAsset service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdapterUnifiedAssetClient interface {
	// 扣费
	Pay(ctx context.Context, in *PayReq, opts ...grpc.CallOption) (*PayRsp, error)
	// 扣费+送礼(打赏)
	PayAndGiveGifts(ctx context.Context, in *PayAndGiveGiftsReq, opts ...grpc.CallOption) (*PayAndGiveGiftsRsp, error)
	// 送礼(打赏)
	GiveGifts(ctx context.Context, in *GiveGiftsReq, opts ...grpc.CallOption) (*GiveGiftsRsp, error)
	// 礼包 k歌对应福利平台
	GetGiftPackage(ctx context.Context, in *GetGiftPackageReq, opts ...grpc.CallOption) (*GetGiftPackageRsp, error)
	BatchGetGiftPackage(ctx context.Context, in *BatchGetGiftPackageReq, opts ...grpc.CallOption) (*BatchGetGiftPackageRsp, error)
	SendGiftPackage(ctx context.Context, in *SendGiftPackageReq, opts ...grpc.CallOption) (*SendGiftPackageRsp, error)
	// 礼物
	GetGiftInfo(ctx context.Context, in *GetGiftInfoReq, opts ...grpc.CallOption) (*GetGiftInfoRsp, error)
	BatchGetGiftInfo(ctx context.Context, in *BatchGetGiftInfoReq, opts ...grpc.CallOption) (*BatchGetGiftInfoRsp, error)
	// 查询余额
	GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceRsp, error)
	// 查询订单状态
	GetOrderStatus(ctx context.Context, in *GetOrderStatusReq, opts ...grpc.CallOption) (*GetOrderStatusRsp, error)
}

type adapterUnifiedAssetClient struct {
	cc grpc.ClientConnInterface
}

func NewAdapterUnifiedAssetClient(cc grpc.ClientConnInterface) AdapterUnifiedAssetClient {
	return &adapterUnifiedAssetClient{cc}
}

func (c *adapterUnifiedAssetClient) Pay(ctx context.Context, in *PayReq, opts ...grpc.CallOption) (*PayRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_Pay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) PayAndGiveGifts(ctx context.Context, in *PayAndGiveGiftsReq, opts ...grpc.CallOption) (*PayAndGiveGiftsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayAndGiveGiftsRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_PayAndGiveGifts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) GiveGifts(ctx context.Context, in *GiveGiftsReq, opts ...grpc.CallOption) (*GiveGiftsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GiveGiftsRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_GiveGifts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) GetGiftPackage(ctx context.Context, in *GetGiftPackageReq, opts ...grpc.CallOption) (*GetGiftPackageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGiftPackageRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_GetGiftPackage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) BatchGetGiftPackage(ctx context.Context, in *BatchGetGiftPackageReq, opts ...grpc.CallOption) (*BatchGetGiftPackageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetGiftPackageRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_BatchGetGiftPackage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) SendGiftPackage(ctx context.Context, in *SendGiftPackageReq, opts ...grpc.CallOption) (*SendGiftPackageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendGiftPackageRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_SendGiftPackage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) GetGiftInfo(ctx context.Context, in *GetGiftInfoReq, opts ...grpc.CallOption) (*GetGiftInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGiftInfoRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_GetGiftInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) BatchGetGiftInfo(ctx context.Context, in *BatchGetGiftInfoReq, opts ...grpc.CallOption) (*BatchGetGiftInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetGiftInfoRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_BatchGetGiftInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_GetBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterUnifiedAssetClient) GetOrderStatus(ctx context.Context, in *GetOrderStatusReq, opts ...grpc.CallOption) (*GetOrderStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrderStatusRsp)
	err := c.cc.Invoke(ctx, AdapterUnifiedAsset_GetOrderStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterUnifiedAssetServer is the server API for AdapterUnifiedAsset service.
// All implementations should embed UnimplementedAdapterUnifiedAssetServer
// for forward compatibility
type AdapterUnifiedAssetServer interface {
	// 扣费
	Pay(context.Context, *PayReq) (*PayRsp, error)
	// 扣费+送礼(打赏)
	PayAndGiveGifts(context.Context, *PayAndGiveGiftsReq) (*PayAndGiveGiftsRsp, error)
	// 送礼(打赏)
	GiveGifts(context.Context, *GiveGiftsReq) (*GiveGiftsRsp, error)
	// 礼包 k歌对应福利平台
	GetGiftPackage(context.Context, *GetGiftPackageReq) (*GetGiftPackageRsp, error)
	BatchGetGiftPackage(context.Context, *BatchGetGiftPackageReq) (*BatchGetGiftPackageRsp, error)
	SendGiftPackage(context.Context, *SendGiftPackageReq) (*SendGiftPackageRsp, error)
	// 礼物
	GetGiftInfo(context.Context, *GetGiftInfoReq) (*GetGiftInfoRsp, error)
	BatchGetGiftInfo(context.Context, *BatchGetGiftInfoReq) (*BatchGetGiftInfoRsp, error)
	// 查询余额
	GetBalance(context.Context, *GetBalanceReq) (*GetBalanceRsp, error)
	// 查询订单状态
	GetOrderStatus(context.Context, *GetOrderStatusReq) (*GetOrderStatusRsp, error)
}

// UnimplementedAdapterUnifiedAssetServer should be embedded to have forward compatible implementations.
type UnimplementedAdapterUnifiedAssetServer struct {
}

func (UnimplementedAdapterUnifiedAssetServer) Pay(context.Context, *PayReq) (*PayRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Pay not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) PayAndGiveGifts(context.Context, *PayAndGiveGiftsReq) (*PayAndGiveGiftsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayAndGiveGifts not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) GiveGifts(context.Context, *GiveGiftsReq) (*GiveGiftsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiveGifts not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) GetGiftPackage(context.Context, *GetGiftPackageReq) (*GetGiftPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGiftPackage not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) BatchGetGiftPackage(context.Context, *BatchGetGiftPackageReq) (*BatchGetGiftPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetGiftPackage not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) SendGiftPackage(context.Context, *SendGiftPackageReq) (*SendGiftPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendGiftPackage not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) GetGiftInfo(context.Context, *GetGiftInfoReq) (*GetGiftInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGiftInfo not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) BatchGetGiftInfo(context.Context, *BatchGetGiftInfoReq) (*BatchGetGiftInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetGiftInfo not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) GetBalance(context.Context, *GetBalanceReq) (*GetBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalance not implemented")
}
func (UnimplementedAdapterUnifiedAssetServer) GetOrderStatus(context.Context, *GetOrderStatusReq) (*GetOrderStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderStatus not implemented")
}

// UnsafeAdapterUnifiedAssetServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdapterUnifiedAssetServer will
// result in compilation errors.
type UnsafeAdapterUnifiedAssetServer interface {
	mustEmbedUnimplementedAdapterUnifiedAssetServer()
}

func RegisterAdapterUnifiedAssetServer(s grpc.ServiceRegistrar, srv AdapterUnifiedAssetServer) {
	s.RegisterService(&AdapterUnifiedAsset_ServiceDesc, srv)
}

func _AdapterUnifiedAsset_Pay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).Pay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_Pay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).Pay(ctx, req.(*PayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_PayAndGiveGifts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayAndGiveGiftsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).PayAndGiveGifts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_PayAndGiveGifts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).PayAndGiveGifts(ctx, req.(*PayAndGiveGiftsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_GiveGifts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiveGiftsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).GiveGifts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_GiveGifts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).GiveGifts(ctx, req.(*GiveGiftsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_GetGiftPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGiftPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).GetGiftPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_GetGiftPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).GetGiftPackage(ctx, req.(*GetGiftPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_BatchGetGiftPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGiftPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).BatchGetGiftPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_BatchGetGiftPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).BatchGetGiftPackage(ctx, req.(*BatchGetGiftPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_SendGiftPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendGiftPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).SendGiftPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_SendGiftPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).SendGiftPackage(ctx, req.(*SendGiftPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_GetGiftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGiftInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).GetGiftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_GetGiftInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).GetGiftInfo(ctx, req.(*GetGiftInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_BatchGetGiftInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGiftInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).BatchGetGiftInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_BatchGetGiftInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).BatchGetGiftInfo(ctx, req.(*BatchGetGiftInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_GetBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).GetBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_GetBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).GetBalance(ctx, req.(*GetBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterUnifiedAsset_GetOrderStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterUnifiedAssetServer).GetOrderStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterUnifiedAsset_GetOrderStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterUnifiedAssetServer).GetOrderStatus(ctx, req.(*GetOrderStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdapterUnifiedAsset_ServiceDesc is the grpc.ServiceDesc for AdapterUnifiedAsset service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdapterUnifiedAsset_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "adapter_unified_assets.AdapterUnifiedAsset",
	HandlerType: (*AdapterUnifiedAssetServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Pay",
			Handler:    _AdapterUnifiedAsset_Pay_Handler,
		},
		{
			MethodName: "PayAndGiveGifts",
			Handler:    _AdapterUnifiedAsset_PayAndGiveGifts_Handler,
		},
		{
			MethodName: "GiveGifts",
			Handler:    _AdapterUnifiedAsset_GiveGifts_Handler,
		},
		{
			MethodName: "GetGiftPackage",
			Handler:    _AdapterUnifiedAsset_GetGiftPackage_Handler,
		},
		{
			MethodName: "BatchGetGiftPackage",
			Handler:    _AdapterUnifiedAsset_BatchGetGiftPackage_Handler,
		},
		{
			MethodName: "SendGiftPackage",
			Handler:    _AdapterUnifiedAsset_SendGiftPackage_Handler,
		},
		{
			MethodName: "GetGiftInfo",
			Handler:    _AdapterUnifiedAsset_GetGiftInfo_Handler,
		},
		{
			MethodName: "BatchGetGiftInfo",
			Handler:    _AdapterUnifiedAsset_BatchGetGiftInfo_Handler,
		},
		{
			MethodName: "GetBalance",
			Handler:    _AdapterUnifiedAsset_GetBalance_Handler,
		},
		{
			MethodName: "GetOrderStatus",
			Handler:    _AdapterUnifiedAsset_GetOrderStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/adapter_unified_assets/adapter_unified_assets/adapter_unified_assets.proto",
}
