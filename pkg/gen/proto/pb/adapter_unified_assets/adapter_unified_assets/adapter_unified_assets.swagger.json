{"swagger": "2.0", "info": {"title": "pb/adapter_unified_assets/adapter_unified_assets/adapter_unified_assets.proto", "version": "version not set"}, "tags": [{"name": "AdapterUnifiedAsset"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/adapter_unified_assets.AdapterUnifiedAsset/BatchGetGiftInfo": {"post": {"operationId": "AdapterUnifiedAsset_BatchGetGiftInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsBatchGetGiftInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsBatchGetGiftInfoReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/BatchGetGiftPackage": {"post": {"operationId": "AdapterUnifiedAsset_BatchGetGiftPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsBatchGetGiftPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsBatchGetGiftPackageReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/GetBalance": {"post": {"summary": "查询余额", "operationId": "AdapterUnifiedAsset_GetBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsGetBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsGetBalanceReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/GetGiftInfo": {"post": {"summary": "礼物", "operationId": "AdapterUnifiedAsset_GetGiftInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsGetGiftInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsGetGiftInfoReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/GetGiftPackage": {"post": {"summary": "礼包 k歌对应福利平台", "operationId": "AdapterUnifiedAsset_GetGiftPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsGetGiftPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsGetGiftPackageReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/GetOrderStatus": {"post": {"summary": "查询订单状态", "operationId": "AdapterUnifiedAsset_GetOrderStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsGetOrderStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsGetOrderStatusReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/GiveGifts": {"post": {"summary": "送礼(打赏)", "operationId": "AdapterUnifiedAsset_GiveGifts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsGiveGiftsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsGiveGiftsReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/Pay": {"post": {"summary": "扣费", "operationId": "AdapterUnifiedAsset_Pay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsPayRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsPayReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/PayAndGiveGifts": {"post": {"summary": "扣费+送礼(打赏)", "operationId": "AdapterUnifiedAsset_PayAndGiveGifts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsPayAndGiveGiftsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsPayAndGiveGiftsReq"}}], "tags": ["AdapterUnifiedAsset"]}}, "/adapter_unified_assets.AdapterUnifiedAsset/SendGiftPackage": {"post": {"operationId": "AdapterUnifiedAsset_SendGiftPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_unified_assetsSendGiftPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_unified_assetsSendGiftPackageReq"}}], "tags": ["AdapterUnifiedAsset"]}}}, "definitions": {"adapter_commonGameMiddleInfo": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "gameOpenId": {"type": "string"}, "uid": {"type": "string"}}, "title": "方式二 宿主平台游戏账号体系\n   必填参数：uid"}, "adapter_unified_assetsBatchGetGiftInfoReq": {"type": "object", "properties": {"giftIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "限制100个"}}, "title": "BatchGetGiftInfoReq 批量查询礼物信息"}, "adapter_unified_assetsBatchGetGiftInfoRsp": {"type": "object", "properties": {"mapSucc": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_unified_assetsGiftInfo"}, "title": "succ"}, "mapFail": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "fail"}}}, "adapter_unified_assetsBatchGetGiftPackageReq": {"type": "object", "properties": {"giftPackageIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "限制100个"}}, "title": "BatchGetGiftPackage 礼包信息 批量"}, "adapter_unified_assetsBatchGetGiftPackageRsp": {"type": "object", "properties": {"mapSucc": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_unified_assetsGiftPackageInfo"}, "title": "succ"}, "mapFail": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "fail"}}}, "adapter_unified_assetsGetBalanceReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}}, "title": "GetBalance"}, "adapter_unified_assetsGetBalanceRsp": {"type": "object", "properties": {"balance": {"type": "string", "format": "int64", "title": "余额"}}}, "adapter_unified_assetsGetGiftInfoReq": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物id"}}, "title": "GetGiftInfo 查礼物信息"}, "adapter_unified_assetsGetGiftInfoRsp": {"type": "object", "properties": {"giftInfo": {"$ref": "#/definitions/adapter_unified_assetsGiftInfo", "title": "礼物信息"}}}, "adapter_unified_assetsGetGiftPackageReq": {"type": "object", "properties": {"giftPackageId": {"type": "string", "format": "int64", "title": "礼包id"}}, "title": "GetGiftPackage 礼包信息 单个"}, "adapter_unified_assetsGetGiftPackageRsp": {"type": "object", "properties": {"giftPackageInfo": {"$ref": "#/definitions/adapter_unified_assetsGiftPackageInfo", "title": "礼包信息"}}}, "adapter_unified_assetsGetOrderStatusReq": {"type": "object", "properties": {"orderId": {"type": "string"}}}, "adapter_unified_assetsGetOrderStatusRsp": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id"}, "orderStatus": {"type": "integer", "format": "int32", "title": "状态PayOrderStatus  0未支付 1已完成支付 2支付失败"}, "orderTs": {"type": "string", "format": "int64", "title": "时间搓"}, "uesrId": {"type": "string", "title": "用户id"}, "payAmount": {"type": "string", "format": "int64", "title": "总价"}, "appId": {"type": "string", "title": "游戏id"}, "payApp": {"$ref": "#/definitions/adapter_unified_assetsPayApp", "title": "支付app"}, "payInfo": {"$ref": "#/definitions/adapter_unified_assetsPayInfo", "title": "扣费信息"}}}, "adapter_unified_assetsGiftInfo": {"type": "object", "properties": {"giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "giftPrice": {"type": "string", "format": "int64", "title": "礼物单价"}, "giftType": {"type": "string", "format": "int64", "title": "礼物类型"}, "giftName": {"type": "string", "title": "礼物名称"}, "giftIcon": {"type": "string", "title": "礼物图标 默认为160*160尺寸"}, "giftAnimationUrl": {"type": "string", "title": "礼物动画资源url"}, "giftAnimationMd5": {"type": "string", "title": "礼物动画资源md5"}, "giftExt": {"type": "object", "additionalProperties": {"type": "string"}, "description": "扩展字段", "title": "字段说明\ngift_mark_type 礼物标记\ngift_play_scenes 礼物玩法场景\ngift_attribute 礼物属性\ngift_icon_large 礼物图片 360*360尺寸"}, "giftAnimationId": {"type": "string", "format": "int64", "title": "礼物动画资源id"}}, "title": "礼物信息"}, "adapter_unified_assetsGiftPackageInfo": {"type": "object", "properties": {"giftPackageId": {"type": "string", "format": "int64", "title": "礼包ID"}, "seriesId": {"type": "string", "format": "int64", "title": "礼包序列ID，标识礼包ID属于某个系列活动"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_unified_assetsRewardItem"}, "title": "礼包内包含资产列表"}, "giftPackageValue": {"type": "string", "format": "int64", "title": "福利价值单位:分"}, "expireTs": {"type": "string", "format": "int64", "title": "过期时间"}, "encryGiftPackageId": {"type": "string", "title": "加密礼包ID"}, "giftPackageReason": {"type": "string", "title": "礼包发放原因"}}, "title": "礼包信息"}, "adapter_unified_assetsGiveGiftsInfo": {"type": "object", "properties": {"recvGameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "被打赏者"}, "payInfo": {"$ref": "#/definitions/adapter_unified_assetsPayInfo", "title": "支付信息"}}, "title": "收礼信息"}, "adapter_unified_assetsGiveGiftsReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "payApp": {"$ref": "#/definitions/adapter_unified_assetsPayApp", "title": "支付app"}, "payAmount": {"type": "string", "format": "int64", "title": "总价"}, "giveGifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_unified_assetsGiveGiftsInfo"}, "title": "打赏信息"}, "paySceneInfo": {"$ref": "#/definitions/adapter_unified_assetsPaySceneInfo", "title": "扣费场景信息"}, "orderId": {"type": "string", "title": "订单id，幂等使用 (订单号必须使用web下单后获取的 不允许业务自己生成)"}}, "title": "GiveGifts 打赏(送礼)"}, "adapter_unified_assetsGiveGiftsRsp": {"type": "object", "properties": {"payResult": {"$ref": "#/definitions/adapter_unified_assetsPayResult", "title": "扣费结果"}}}, "adapter_unified_assetsGoodsItem": {"type": "object", "properties": {"goodsId": {"type": "string", "title": "商品Id"}, "num": {"type": "string", "format": "int64"}}, "title": "商品信息"}, "adapter_unified_assetsPayAndGiveGiftsReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "payApp": {"$ref": "#/definitions/adapter_unified_assetsPayApp", "title": "支付app"}, "payInfo": {"$ref": "#/definitions/adapter_unified_assetsPayInfo", "title": "扣费信息"}, "payAmount": {"type": "string", "format": "int64", "title": "总价"}, "giveGifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_unified_assetsGiveGiftsInfo"}, "title": "打赏信息"}, "paySceneInfo": {"$ref": "#/definitions/adapter_unified_assetsPaySceneInfo", "title": "扣费场景信息"}, "orderId": {"type": "string", "title": "订单id，幂等使用"}}, "title": "PayAndGiveGifts 扣费+打赏(送礼) 这里需要等额扣+打赏"}, "adapter_unified_assetsPayAndGiveGiftsRsp": {"type": "object", "properties": {"payResult": {"$ref": "#/definitions/adapter_unified_assetsPayResult", "title": "扣费结果"}}}, "adapter_unified_assetsPayApp": {"type": "object", "properties": {"actId": {"type": "string", "title": "活动id,平台分配给具体活动的标识"}, "businessId": {"type": "string", "format": "int64", "title": "支付businessid，支付平台分配"}, "currencyType": {"type": "string", "format": "int64", "title": "货币类型"}}, "title": "支付app"}, "adapter_unified_assetsPayInfo": {"type": "object", "properties": {"goodsItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_unified_assetsGoodsItem"}, "title": "商品列表"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "description": "额外信息", "title": "==========mlive begin\nposition q音歌房 麦序\n==========mlive end"}}, "title": "支付信息"}, "adapter_unified_assetsPayReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "payApp": {"$ref": "#/definitions/adapter_unified_assetsPayApp", "title": "支付app"}, "payInfo": {"$ref": "#/definitions/adapter_unified_assetsPayInfo", "title": "扣费信息"}, "payAmount": {"type": "string", "format": "int64", "title": "总价"}, "paySceneInfo": {"$ref": "#/definitions/adapter_unified_assetsPaySceneInfo", "title": "扣费场景信息"}, "orderId": {"type": "string", "title": "订单id，幂等使用"}}, "title": "Pay 扣费"}, "adapter_unified_assetsPayResult": {"type": "object", "properties": {"orderId": {"type": "string"}, "balance": {"type": "string", "format": "int64"}, "errMsg": {"type": "string"}}, "title": "支付结果"}, "adapter_unified_assetsPayRsp": {"type": "object", "properties": {"payResult": {"$ref": "#/definitions/adapter_unified_assetsPayResult", "title": "扣费结果"}}}, "adapter_unified_assetsPaySceneInfo": {"type": "object", "properties": {"paySceneType": {"$ref": "#/definitions/adapter_unified_assetsPaySceneType", "title": "见枚举说明PaySceneType"}, "anchorId": {"type": "string", "title": "主播id"}, "roomId": {"type": "string", "title": "房间id"}, "showId": {"type": "string", "title": "直播id"}, "ugcId": {"type": "string", "title": "作品id"}}, "title": "支付场景信息"}, "adapter_unified_assetsPaySceneType": {"type": "string", "enum": ["PAY_SCENE_TYPE_UNKNOWN", "PAY_SCENE_TYPE_LIVE", "PAY_SCENE_TYPE_KTV", "PAY_SCENE_TYPE_ASYNC_UGC", "Pay_SCENE_TYPE_ASYNC_MAIL", "PAY_SCENE_TYPE_ASYNC_HOMEPAGE"], "default": "PAY_SCENE_TYPE_UNKNOWN", "description": "- PAY_SCENE_TYPE_LIVE: 直播 必填参数:anchor_id、room_id、show_id\n - PAY_SCENE_TYPE_KTV: 歌房 必填参数:anchor_id、room_id、show_id\n - PAY_SCENE_TYPE_ASYNC_UGC: 异步作品 必填参数:ugc_id\n - Pay_SCENE_TYPE_ASYNC_MAIL: 异步私信 必填参数:无\n - PAY_SCENE_TYPE_ASYNC_HOMEPAGE: 异步个人 必填参数:无", "title": "支付场景"}, "adapter_unified_assetsRewardItem": {"type": "object", "properties": {"rewardId": {"type": "string", "title": "奖励ID"}, "rewardNum": {"type": "string", "format": "int64", "title": "数量"}, "rewardType": {"type": "string", "format": "int64", "title": "奖励类型"}, "unitPrice": {"type": "string", "format": "int64", "title": "单价"}, "rewardName": {"type": "string", "title": "资产名称"}, "rewardLogo": {"type": "string", "title": "资产icon"}, "universal": {"type": "string", "title": "万能字段 透传配置系统上配置的信息"}}, "title": "礼包内的奖项"}, "adapter_unified_assetsSendExtensionId": {"type": "string", "enum": ["SendExtensionIdUnknow", "SendExtensionIdBlessingGodPlay", "SendExtensionIdChefGodPlay", "SendExtensionIdPetPKPlay", "SendExtensionIdGodPlacePlay", "SendExtensionIdHonorWarPlay", "SendExtensionIdKingWar", "SendExtensionIdGoldEgg", "SendExtensionIdFortuneExplore"], "default": "SendExtensionIdUnknow", "title": "- SendExtensionIdUnknow: 未知\n - SendExtensionIdBlessingGodPlay: 福神玩法\n - SendExtensionIdChefGodPlay: 全民厨神\n - SendExtensionIdPetPKPlay: 宠物PK\n - SendExtensionIdGodPlacePlay: 仙域\n - SendExtensionIdHonorWarPlay: 荣耀空战\n - SendExtensionIdKingWar: 国王战争\n - SendExtensionIdGoldEgg: 砸金蛋\n - SendExtensionIdFortuneExplore: 福禄探秘"}, "adapter_unified_assetsSendGiftPackageReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "giftPackageId": {"type": "string", "format": "int64", "title": "礼包id"}, "num": {"type": "string", "format": "int64", "title": "数量"}, "orderId": {"type": "string", "title": "订单id，幂等使用 长度限制52以内"}, "program": {"type": "string", "title": "主调业务(不能包含中文)"}, "reason": {"type": "string", "title": "(不能包含中文)主调方发放原因"}, "indentifiers": {"type": "string", "title": "标识某个具体业务的发放，填抽奖ID、任务ID、活动ID"}, "sendTs": {"type": "string", "format": "int64", "title": "业务方发放时间秒级，用于统计，重试的时候，要保持不变"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "限制个数4个以内 key=_extensionId 已占用"}, "extensionId": {"$ref": "#/definitions/adapter_unified_assetsSendExtensionId", "description": "业务id SendExtensionId枚举见common.proto", "title": "这里必填实现福利发放回调检查 协议 pb/adapter_unified_assets/callback/callback.proto"}}, "title": "SendGiftPackage 送礼包"}, "adapter_unified_assetsSendGiftPackageRsp": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}