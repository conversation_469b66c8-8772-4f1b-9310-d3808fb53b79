// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_unified_assets/code/code.proto

package code

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrCode int32

const (
	ErrCode_ErrOK ErrCode = 0
	// 公共错误码
	ErrCode_ErrParam                  ErrCode = 10003 // 参数错误
	ErrCode_ErrCallbackUpdateConflict ErrCode = 32892 // 更新冲突
	// 送礼
	ErrCode_ErrGiftsBusinessCheckFail ErrCode = 40000 // 送礼check失败
	// 扣费
	ErrCode_ErrBalanceNotEnough  ErrCode = 30414 // 账户余额不足
	ErrCode_ErrMidasLoginExpired ErrCode = 30415 // midas登录过期 无法进行扣费
	ErrCode_ErrMidasTimeout      ErrCode = 30416 // midas超时 (支付系统内部会重试，留意发货通知)
	ErrCode_ErrMidasProcessing   ErrCode = 30417 // midas处理中 (支付系统内部会重试，留意发货通知)
	ErrCode_ErrOrderLocking      ErrCode = 30440 // 订单id状态锁定处理中,已实际发货通知为准
	ErrCode_ErrOrderException    ErrCode = 30441 // 订单id使用异常 无扣费 已无法使用
	ErrCode_ErrOrderCanclled     ErrCode = 30443 // 订单id被系统取消 无法使用
	ErrCode_ErrOrderTimeout      ErrCode = 30444 // 订单使用间隔超时 无法使用
	ErrCode_ErrOrderNotExist     ErrCode = 30445 // 订单id不存在
	ErrCode_ErrMidasLimit        ErrCode = 32641 // midas频率控制 无法进行扣费
	ErrCode_ErrOrderCompleted    ErrCode = 30442 // 订单id已经正常使用 已扣费完成 留意发货通知
	// 礼包查询&发放
	ErrCode_ErrGiftPackageNotExist      ErrCode = 11417 // 礼包不存在
	ErrCode_ErrGiftPackageExpired       ErrCode = 11419 // 礼包已过期
	ErrCode_ErrGiftPackageDelist        ErrCode = 32645 // 礼包已下架
	ErrCode_ErrGiftPackageSendCheckFail ErrCode = 30438 // 礼包发放校验失败
	// 礼物查询
	ErrCode_ErrGiftNotExist ErrCode = 32638 // 礼物不存在
	// 查询余额
	ErrCode_ErrBalanceAccountNotExist ErrCode = 13200 // 余额查询账户不存在
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:     "ErrOK",
		10003: "ErrParam",
		32892: "ErrCallbackUpdateConflict",
		40000: "ErrGiftsBusinessCheckFail",
		30414: "ErrBalanceNotEnough",
		30415: "ErrMidasLoginExpired",
		30416: "ErrMidasTimeout",
		30417: "ErrMidasProcessing",
		30440: "ErrOrderLocking",
		30441: "ErrOrderException",
		30443: "ErrOrderCanclled",
		30444: "ErrOrderTimeout",
		30445: "ErrOrderNotExist",
		32641: "ErrMidasLimit",
		30442: "ErrOrderCompleted",
		11417: "ErrGiftPackageNotExist",
		11419: "ErrGiftPackageExpired",
		32645: "ErrGiftPackageDelist",
		30438: "ErrGiftPackageSendCheckFail",
		32638: "ErrGiftNotExist",
		13200: "ErrBalanceAccountNotExist",
	}
	ErrCode_value = map[string]int32{
		"ErrOK":                       0,
		"ErrParam":                    10003,
		"ErrCallbackUpdateConflict":   32892,
		"ErrGiftsBusinessCheckFail":   40000,
		"ErrBalanceNotEnough":         30414,
		"ErrMidasLoginExpired":        30415,
		"ErrMidasTimeout":             30416,
		"ErrMidasProcessing":          30417,
		"ErrOrderLocking":             30440,
		"ErrOrderException":           30441,
		"ErrOrderCanclled":            30443,
		"ErrOrderTimeout":             30444,
		"ErrOrderNotExist":            30445,
		"ErrMidasLimit":               32641,
		"ErrOrderCompleted":           30442,
		"ErrGiftPackageNotExist":      11417,
		"ErrGiftPackageExpired":       11419,
		"ErrGiftPackageDelist":        32645,
		"ErrGiftPackageSendCheckFail": 30438,
		"ErrGiftNotExist":             32638,
		"ErrBalanceAccountNotExist":   13200,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_unified_assets_code_code_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_pb_adapter_unified_assets_code_code_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_unified_assets_code_code_proto_rawDescGZIP(), []int{0}
}

var File_pb_adapter_unified_assets_code_code_proto protoreflect.FileDescriptor

var file_pb_adapter_unified_assets_code_code_proto_rawDesc = []byte{
	0x0a, 0x29, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x64, 0x65,
	0x2f, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x2a, 0xa1, 0x04, 0x0a, 0x07, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x09, 0x0a,
	0x05, 0x45, 0x72, 0x72, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x08, 0x45, 0x72, 0x72, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x10, 0x93, 0x4e, 0x12, 0x1f, 0x0a, 0x19, 0x45, 0x72, 0x72, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x6c, 0x69, 0x63, 0x74, 0x10, 0xfc, 0x80, 0x02, 0x12, 0x1f, 0x0a, 0x19, 0x45, 0x72, 0x72, 0x47,
	0x69, 0x66, 0x74, 0x73, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x46, 0x61, 0x69, 0x6c, 0x10, 0xc0, 0xb8, 0x02, 0x12, 0x19, 0x0a, 0x13, 0x45, 0x72, 0x72,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68,
	0x10, 0xce, 0xed, 0x01, 0x12, 0x1a, 0x0a, 0x14, 0x45, 0x72, 0x72, 0x4d, 0x69, 0x64, 0x61, 0x73,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0xcf, 0xed, 0x01,
	0x12, 0x15, 0x0a, 0x0f, 0x45, 0x72, 0x72, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x10, 0xd0, 0xed, 0x01, 0x12, 0x18, 0x0a, 0x12, 0x45, 0x72, 0x72, 0x4d, 0x69,
	0x64, 0x61, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0xd1, 0xed,
	0x01, 0x12, 0x15, 0x0a, 0x0f, 0x45, 0x72, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x10, 0xe8, 0xed, 0x01, 0x12, 0x17, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0xe9, 0xed,
	0x01, 0x12, 0x16, 0x0a, 0x10, 0x45, 0x72, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x61, 0x6e,
	0x63, 0x6c, 0x6c, 0x65, 0x64, 0x10, 0xeb, 0xed, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x45, 0x72, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0xec, 0xed, 0x01,
	0x12, 0x16, 0x0a, 0x10, 0x45, 0x72, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x10, 0xed, 0xed, 0x01, 0x12, 0x13, 0x0a, 0x0d, 0x45, 0x72, 0x72, 0x4d,
	0x69, 0x64, 0x61, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x81, 0xff, 0x01, 0x12, 0x17, 0x0a,
	0x11, 0x45, 0x72, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x10, 0xea, 0xed, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x72, 0x72, 0x47, 0x69, 0x66,
	0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x10, 0x99, 0x59, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x72, 0x72, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x9b, 0x59, 0x12,
	0x1a, 0x0a, 0x14, 0x45, 0x72, 0x72, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x44, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x10, 0x85, 0xff, 0x01, 0x12, 0x21, 0x0a, 0x1b, 0x45,
	0x72, 0x72, 0x47, 0x69, 0x66, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x53, 0x65, 0x6e,
	0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x10, 0xe6, 0xed, 0x01, 0x12, 0x15,
	0x0a, 0x0f, 0x45, 0x72, 0x72, 0x47, 0x69, 0x66, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73,
	0x74, 0x10, 0xfe, 0xfe, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x45, 0x72, 0x72, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x10, 0x90, 0x67, 0x42, 0x53, 0x5a, 0x51, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x61,
	0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x64, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_adapter_unified_assets_code_code_proto_rawDescOnce sync.Once
	file_pb_adapter_unified_assets_code_code_proto_rawDescData = file_pb_adapter_unified_assets_code_code_proto_rawDesc
)

func file_pb_adapter_unified_assets_code_code_proto_rawDescGZIP() []byte {
	file_pb_adapter_unified_assets_code_code_proto_rawDescOnce.Do(func() {
		file_pb_adapter_unified_assets_code_code_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_unified_assets_code_code_proto_rawDescData)
	})
	return file_pb_adapter_unified_assets_code_code_proto_rawDescData
}

var file_pb_adapter_unified_assets_code_code_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_adapter_unified_assets_code_code_proto_goTypes = []interface{}{
	(ErrCode)(0), // 0: code.ErrCode
}
var file_pb_adapter_unified_assets_code_code_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_adapter_unified_assets_code_code_proto_init() }
func file_pb_adapter_unified_assets_code_code_proto_init() {
	if File_pb_adapter_unified_assets_code_code_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_unified_assets_code_code_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_adapter_unified_assets_code_code_proto_goTypes,
		DependencyIndexes: file_pb_adapter_unified_assets_code_code_proto_depIdxs,
		EnumInfos:         file_pb_adapter_unified_assets_code_code_proto_enumTypes,
	}.Build()
	File_pb_adapter_unified_assets_code_code_proto = out.File
	file_pb_adapter_unified_assets_code_code_proto_rawDesc = nil
	file_pb_adapter_unified_assets_code_code_proto_goTypes = nil
	file_pb_adapter_unified_assets_code_code_proto_depIdxs = nil
}
