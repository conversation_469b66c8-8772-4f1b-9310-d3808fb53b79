// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_divide_day_activity/common/common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskStatus int32

const (
	TaskStatus_Task_Status_Unknown     TaskStatus = 0
	TaskStatus_Task_Status_Participate TaskStatus = 1 // 参与中
	TaskStatus_Task_Status_Done        TaskStatus = 2 // 已完成
	TaskStatus_Task_Status_Reward      TaskStatus = 3 // 已领奖
)

// Enum value maps for TaskStatus.
var (
	TaskStatus_name = map[int32]string{
		0: "Task_Status_Unknown",
		1: "Task_Status_Participate",
		2: "Task_Status_Done",
		3: "Task_Status_Reward",
	}
	TaskStatus_value = map[string]int32{
		"Task_Status_Unknown":     0,
		"Task_Status_Participate": 1,
		"Task_Status_Done":        2,
		"Task_Status_Reward":      3,
	}
)

func (x TaskStatus) Enum() *TaskStatus {
	p := new(TaskStatus)
	*p = x
	return p
}

func (x TaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[0].Descriptor()
}

func (TaskStatus) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[0]
}

func (x TaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatus.Descriptor instead.
func (TaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{0}
}

type DayType int32

const (
	DayType_Day_Type_Unknown DayType = 0
	DayType_Day_Type_One     DayType = 1 // 第一天
	DayType_Day_Type_Two     DayType = 2 // 第二天
	DayType_Day_Type_Three   DayType = 3 // 第三天
)

// Enum value maps for DayType.
var (
	DayType_name = map[int32]string{
		0: "Day_Type_Unknown",
		1: "Day_Type_One",
		2: "Day_Type_Two",
		3: "Day_Type_Three",
	}
	DayType_value = map[string]int32{
		"Day_Type_Unknown": 0,
		"Day_Type_One":     1,
		"Day_Type_Two":     2,
		"Day_Type_Three":   3,
	}
)

func (x DayType) Enum() *DayType {
	p := new(DayType)
	*p = x
	return p
}

func (x DayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DayType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[1].Descriptor()
}

func (DayType) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[1]
}

func (x DayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DayType.Descriptor instead.
func (DayType) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{1}
}

type ActivityStatus int32

const (
	ActivityStatus_Activity_Status_Unknown    ActivityStatus = 0 // 活动未开始
	ActivityStatus_Activity_Status_InProgress ActivityStatus = 1 // 活动进行中
	ActivityStatus_Activity_Status_End        ActivityStatus = 2 // 活动结束
)

// Enum value maps for ActivityStatus.
var (
	ActivityStatus_name = map[int32]string{
		0: "Activity_Status_Unknown",
		1: "Activity_Status_InProgress",
		2: "Activity_Status_End",
	}
	ActivityStatus_value = map[string]int32{
		"Activity_Status_Unknown":    0,
		"Activity_Status_InProgress": 1,
		"Activity_Status_End":        2,
	}
)

func (x ActivityStatus) Enum() *ActivityStatus {
	p := new(ActivityStatus)
	*p = x
	return p
}

func (x ActivityStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[2].Descriptor()
}

func (ActivityStatus) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[2]
}

func (x ActivityStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityStatus.Descriptor instead.
func (ActivityStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{2}
}

type GoodsSortType int32

const (
	GoodsSortType_Goods_Sort_Type_Unknown  GoodsSortType = 0 // 活动未开始
	GoodsSortType_Goods_Sort_Type_Platform GoodsSortType = 1 // 平台物资排前面
	GoodsSortType_Goods_Sort_Type_Game     GoodsSortType = 2 // 游戏物资排前面
)

// Enum value maps for GoodsSortType.
var (
	GoodsSortType_name = map[int32]string{
		0: "Goods_Sort_Type_Unknown",
		1: "Goods_Sort_Type_Platform",
		2: "Goods_Sort_Type_Game",
	}
	GoodsSortType_value = map[string]int32{
		"Goods_Sort_Type_Unknown":  0,
		"Goods_Sort_Type_Platform": 1,
		"Goods_Sort_Type_Game":     2,
	}
)

func (x GoodsSortType) Enum() *GoodsSortType {
	p := new(GoodsSortType)
	*p = x
	return p
}

func (x GoodsSortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoodsSortType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[3].Descriptor()
}

func (GoodsSortType) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes[3]
}

func (x GoodsSortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoodsSortType.Descriptor instead.
func (GoodsSortType) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{3}
}

type RewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`     // 奖励id
	Num  uint32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`   // 奖励数量 限时道具表示分钟数
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`  // 奖励名称
	Type uint32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"` // 资产类型
}

func (x *RewardItem) Reset() {
	*x = RewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardItem) ProtoMessage() {}

func (x *RewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardItem.ProtoReflect.Descriptor instead.
func (*RewardItem) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{0}
}

func (x *RewardItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RewardItem) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *RewardItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RewardItem) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type DayCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayCardType int32         `protobuf:"varint,1,opt,name=dayCardType,proto3" json:"dayCardType,omitempty"` // 参考DayType
	Status      int32         `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`           // 任务状态 参考 TaskStatus
	PackageId   int32         `protobuf:"varint,3,opt,name=packageId,proto3" json:"packageId,omitempty"`     // 礼包ID
	Rewards     []*RewardItem `protobuf:"bytes,4,rep,name=rewards,proto3" json:"rewards,omitempty"`
	Title       string        `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`           // 任务标题
	BottonDesc  string        `protobuf:"bytes,6,opt,name=bottonDesc,proto3" json:"bottonDesc,omitempty"` // 底部标题
	GoodSort    int32         `protobuf:"varint,7,opt,name=goodSort,proto3" json:"goodSort,omitempty"`    // 物资排序 参考 GoodsSortType
}

func (x *DayCard) Reset() {
	*x = DayCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DayCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DayCard) ProtoMessage() {}

func (x *DayCard) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DayCard.ProtoReflect.Descriptor instead.
func (*DayCard) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{1}
}

func (x *DayCard) GetDayCardType() int32 {
	if x != nil {
		return x.DayCardType
	}
	return 0
}

func (x *DayCard) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DayCard) GetPackageId() int32 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *DayCard) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *DayCard) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DayCard) GetBottonDesc() string {
	if x != nil {
		return x.BottonDesc
	}
	return ""
}

func (x *DayCard) GetGoodSort() int32 {
	if x != nil {
		return x.GoodSort
	}
	return 0
}

type Entrance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId int32 `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"` // 当前活动ID
	Process    int32 `protobuf:"varint,2,opt,name=process,proto3" json:"process,omitempty"`       // 进度，百分比
	HasAward   bool  `protobuf:"varint,3,opt,name=hasAward,proto3" json:"hasAward,omitempty"`     // 是否有奖励
}

func (x *Entrance) Reset() {
	*x = Entrance{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Entrance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entrance) ProtoMessage() {}

func (x *Entrance) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entrance.ProtoReflect.Descriptor instead.
func (*Entrance) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP(), []int{2}
}

func (x *Entrance) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *Entrance) GetProcess() int32 {
	if x != nil {
		return x.Process
	}
	return 0
}

func (x *Entrance) GetHasAward() bool {
	if x != nil {
		return x.HasAward
	}
	return false
}

var File_pb_cwsx_game_divide_day_activity_common_common_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDesc = []byte{
	0x0a, 0x34, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x64,
	0x69, 0x76, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x56, 0x0a, 0x0a,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0xff, 0x01, 0x0a, 0x07, 0x44, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x61, 0x79, 0x43, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x6f,
	0x6f, 0x64, 0x53, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x67, 0x6f,
	0x6f, 0x64, 0x53, 0x6f, 0x72, 0x74, 0x22, 0x60, 0x0a, 0x08, 0x45, 0x6e, 0x74, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x68, 0x61, 0x73, 0x41, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x68, 0x61, 0x73, 0x41, 0x77, 0x61, 0x72, 0x64, 0x2a, 0x70, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x1b, 0x0a, 0x17, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10,
	0x54, 0x61, 0x73, 0x6b, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x44, 0x6f, 0x6e, 0x65,
	0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x03, 0x2a, 0x57, 0x0a, 0x07, 0x44, 0x61,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x61, 0x79, 0x5f, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x44,
	0x61, 0x79, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4f, 0x6e, 0x65, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x44, 0x61, 0x79, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x77, 0x6f, 0x10, 0x02, 0x12,
	0x12, 0x0a, 0x0e, 0x44, 0x61, 0x79, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x68, 0x72, 0x65,
	0x65, 0x10, 0x03, 0x2a, 0x66, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x45, 0x6e, 0x64, 0x10, 0x02, 0x2a, 0x64, 0x0a, 0x0d, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x53, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x5f, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x5f, 0x53, 0x6f, 0x72, 0x74, 0x5f, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x10,
	0x02, 0x42, 0x5c, 0x5a, 0x5a, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescData = file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDesc
)

func file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescData)
	})
	return file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDescData
}

var file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pb_cwsx_game_divide_day_activity_common_common_proto_goTypes = []interface{}{
	(TaskStatus)(0),     // 0: cwsx_game_divide_day_activity.common.TaskStatus
	(DayType)(0),        // 1: cwsx_game_divide_day_activity.common.DayType
	(ActivityStatus)(0), // 2: cwsx_game_divide_day_activity.common.ActivityStatus
	(GoodsSortType)(0),  // 3: cwsx_game_divide_day_activity.common.GoodsSortType
	(*RewardItem)(nil),  // 4: cwsx_game_divide_day_activity.common.RewardItem
	(*DayCard)(nil),     // 5: cwsx_game_divide_day_activity.common.DayCard
	(*Entrance)(nil),    // 6: cwsx_game_divide_day_activity.common.Entrance
}
var file_pb_cwsx_game_divide_day_activity_common_common_proto_depIdxs = []int32{
	4, // 0: cwsx_game_divide_day_activity.common.DayCard.rewards:type_name -> cwsx_game_divide_day_activity.common.RewardItem
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_divide_day_activity_common_common_proto_init() }
func file_pb_cwsx_game_divide_day_activity_common_common_proto_init() {
	if File_pb_cwsx_game_divide_day_activity_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DayCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Entrance); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_cwsx_game_divide_day_activity_common_common_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_divide_day_activity_common_common_proto_depIdxs,
		EnumInfos:         file_pb_cwsx_game_divide_day_activity_common_common_proto_enumTypes,
		MessageInfos:      file_pb_cwsx_game_divide_day_activity_common_common_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_divide_day_activity_common_common_proto = out.File
	file_pb_cwsx_game_divide_day_activity_common_common_proto_rawDesc = nil
	file_pb_cwsx_game_divide_day_activity_common_common_proto_goTypes = nil
	file_pb_cwsx_game_divide_day_activity_common_common_proto_depIdxs = nil
}
