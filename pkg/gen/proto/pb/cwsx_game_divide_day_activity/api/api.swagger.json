{"swagger": "2.0", "info": {"title": "pb/cwsx_game_divide_day_activity/api/api.proto", "version": "version not set"}, "tags": [{"name": "Api"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/cwsx_game_divide_day_activity.Api/ActivityPage": {"post": {"summary": "查询活动页面", "operationId": "Api_ActivityPage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_divide_day_activityActivityPageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_divide_day_activityActivityPageReq"}}], "tags": ["Api"]}}, "/cwsx_game_divide_day_activity.Api/ClaimReward": {"post": {"summary": "领取奖励", "operationId": "<PERSON><PERSON>_ClaimReward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_divide_day_activityClaimRewardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_divide_day_activityClaimRewardReq"}}], "tags": ["Api"]}}}, "definitions": {"commonDayCard": {"type": "object", "properties": {"dayCardType": {"type": "integer", "format": "int32", "title": "参考DayType"}, "status": {"type": "integer", "format": "int32", "title": "任务状态 参考 TaskStatus"}, "packageId": {"type": "integer", "format": "int32", "title": "礼包ID"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonRewardItem"}}, "title": {"type": "string", "title": "任务标题"}, "bottonDesc": {"type": "string", "title": "底部标题"}, "goodSort": {"type": "integer", "format": "int32", "title": "物资排序 参考 GoodsSortType"}}}, "commonRewardItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "奖励id"}, "num": {"type": "integer", "format": "int64", "title": "奖励数量 限时道具表示分钟数"}, "name": {"type": "string", "title": "奖励名称"}, "type": {"type": "integer", "format": "int64", "title": "资产类型"}}}, "cwsx_game_divide_day_activityActivityPageReq": {"type": "object"}, "cwsx_game_divide_day_activityActivityPageRsp": {"type": "object", "properties": {"version": {"type": "integer", "format": "int32", "title": "当前活动版本"}, "status": {"type": "integer", "format": "int32", "title": "活动状态 参考 ActivityStatus"}, "targetAmount": {"type": "integer", "format": "int32", "title": "目标金额(分)"}, "rechargeAmount": {"type": "integer", "format": "int32", "title": "当前充值金额（分）"}, "banner": {"type": "string", "title": "banner图"}, "description": {"type": "string", "title": "描述"}, "rule": {"type": "string", "title": "规则"}, "dayCard": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonDayCard"}, "title": "每日礼包"}}}, "cwsx_game_divide_day_activityClaimRewardReq": {"type": "object", "properties": {"dayCardType": {"type": "integer", "format": "int32", "title": "日期类型"}, "device": {"$ref": "#/definitions/deviceDevice"}}}, "cwsx_game_divide_day_activityClaimRewardRsp": {"type": "object", "properties": {"dayCardType": {"type": "integer", "format": "int32"}}}, "deviceDevice": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台信息 kugou、qqmusic、qmkege、kuwo、lanren"}, "version": {"type": "string", "title": "客户端版本 1.2.3"}, "os": {"type": "string", "title": "系统 android、ios"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}