// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/cwsx_game_divide_day_activity/api/api.proto

package cwsx_game_divide_day_activity

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Api_ActivityPage_FullMethodName = "/cwsx_game_divide_day_activity.Api/ActivityPage"
	Api_ClaimReward_FullMethodName  = "/cwsx_game_divide_day_activity.Api/ClaimReward"
)

// ApiClient is the client API for Api service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApiClient interface {
	// 查询活动页面
	ActivityPage(ctx context.Context, in *ActivityPageReq, opts ...grpc.CallOption) (*ActivityPageRsp, error)
	// 领取奖励
	ClaimReward(ctx context.Context, in *ClaimRewardReq, opts ...grpc.CallOption) (*ClaimRewardRsp, error)
}

type apiClient struct {
	cc grpc.ClientConnInterface
}

func NewApiClient(cc grpc.ClientConnInterface) ApiClient {
	return &apiClient{cc}
}

func (c *apiClient) ActivityPage(ctx context.Context, in *ActivityPageReq, opts ...grpc.CallOption) (*ActivityPageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivityPageRsp)
	err := c.cc.Invoke(ctx, Api_ActivityPage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) ClaimReward(ctx context.Context, in *ClaimRewardReq, opts ...grpc.CallOption) (*ClaimRewardRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClaimRewardRsp)
	err := c.cc.Invoke(ctx, Api_ClaimReward_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiServer is the server API for Api service.
// All implementations should embed UnimplementedApiServer
// for forward compatibility
type ApiServer interface {
	// 查询活动页面
	ActivityPage(context.Context, *ActivityPageReq) (*ActivityPageRsp, error)
	// 领取奖励
	ClaimReward(context.Context, *ClaimRewardReq) (*ClaimRewardRsp, error)
}

// UnimplementedApiServer should be embedded to have forward compatible implementations.
type UnimplementedApiServer struct {
}

func (UnimplementedApiServer) ActivityPage(context.Context, *ActivityPageReq) (*ActivityPageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityPage not implemented")
}
func (UnimplementedApiServer) ClaimReward(context.Context, *ClaimRewardReq) (*ClaimRewardRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClaimReward not implemented")
}

// UnsafeApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiServer will
// result in compilation errors.
type UnsafeApiServer interface {
	mustEmbedUnimplementedApiServer()
}

func RegisterApiServer(s grpc.ServiceRegistrar, srv ApiServer) {
	s.RegisterService(&Api_ServiceDesc, srv)
}

func _Api_ActivityPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityPageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ActivityPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ActivityPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ActivityPage(ctx, req.(*ActivityPageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_ClaimReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).ClaimReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_ClaimReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).ClaimReward(ctx, req.(*ClaimRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Api_ServiceDesc is the grpc.ServiceDesc for Api service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Api_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cwsx_game_divide_day_activity.Api",
	HandlerType: (*ApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ActivityPage",
			Handler:    _Api_ActivityPage_Handler,
		},
		{
			MethodName: "ClaimReward",
			Handler:    _Api_ClaimReward_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/cwsx_game_divide_day_activity/api/api.proto",
}
