// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/uni_pay/common/common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OperatingSystem int32

const (
	OperatingSystem_OperatingSystemUnknown OperatingSystem = 0
	OperatingSystem_OperatingSystemAndroid OperatingSystem = 1
	OperatingSystem_OperatingSystemIOS     OperatingSystem = 2
)

// Enum value maps for OperatingSystem.
var (
	OperatingSystem_name = map[int32]string{
		0: "OperatingSystemUnknown",
		1: "OperatingSystemAndroid",
		2: "OperatingSystemIOS",
	}
	OperatingSystem_value = map[string]int32{
		"OperatingSystemUnknown": 0,
		"OperatingSystemAndroid": 1,
		"OperatingSystemIOS":     2,
	}
)

func (x OperatingSystem) Enum() *OperatingSystem {
	p := new(OperatingSystem)
	*p = x
	return p
}

func (x OperatingSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperatingSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_uni_pay_common_common_proto_enumTypes[0].Descriptor()
}

func (OperatingSystem) Type() protoreflect.EnumType {
	return &file_pb_uni_pay_common_common_proto_enumTypes[0]
}

func (x OperatingSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperatingSystem.Descriptor instead.
func (OperatingSystem) EnumDescriptor() ([]byte, []int) {
	return file_pb_uni_pay_common_common_proto_rawDescGZIP(), []int{0}
}

type StatusOfOrder int32

const (
	StatusOfOrder_Unknown   StatusOfOrder = 0
	StatusOfOrder_Placed    StatusOfOrder = 1 // 已下单
	StatusOfOrder_Completed StatusOfOrder = 2 // 已完成
	StatusOfOrder_Ongoing   StatusOfOrder = 3 // 进行中-check完成
)

// Enum value maps for StatusOfOrder.
var (
	StatusOfOrder_name = map[int32]string{
		0: "Unknown",
		1: "Placed",
		2: "Completed",
		3: "Ongoing",
	}
	StatusOfOrder_value = map[string]int32{
		"Unknown":   0,
		"Placed":    1,
		"Completed": 2,
		"Ongoing":   3,
	}
)

func (x StatusOfOrder) Enum() *StatusOfOrder {
	p := new(StatusOfOrder)
	*p = x
	return p
}

func (x StatusOfOrder) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusOfOrder) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_uni_pay_common_common_proto_enumTypes[1].Descriptor()
}

func (StatusOfOrder) Type() protoreflect.EnumType {
	return &file_pb_uni_pay_common_common_proto_enumTypes[1]
}

func (x StatusOfOrder) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusOfOrder.Descriptor instead.
func (StatusOfOrder) EnumDescriptor() ([]byte, []int) {
	return file_pb_uni_pay_common_common_proto_rawDescGZIP(), []int{1}
}

// PayScene 货币类型
type PayScene int32

const (
	PayScene_AppPlatform PayScene = 0 // 平台支付:绿钻
	PayScene_PayPlatform PayScene = 1 // 支付中台-人民币支付
	PayScene_KugouVip    PayScene = 2 // 酷狗vip
)

// Enum value maps for PayScene.
var (
	PayScene_name = map[int32]string{
		0: "AppPlatform",
		1: "PayPlatform",
		2: "KugouVip",
	}
	PayScene_value = map[string]int32{
		"AppPlatform": 0,
		"PayPlatform": 1,
		"KugouVip":    2,
	}
)

func (x PayScene) Enum() *PayScene {
	p := new(PayScene)
	*p = x
	return p
}

func (x PayScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayScene) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_uni_pay_common_common_proto_enumTypes[2].Descriptor()
}

func (PayScene) Type() protoreflect.EnumType {
	return &file_pb_uni_pay_common_common_proto_enumTypes[2]
}

func (x PayScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayScene.Descriptor instead.
func (PayScene) EnumDescriptor() ([]byte, []int) {
	return file_pb_uni_pay_common_common_proto_rawDescGZIP(), []int{2}
}

type PayFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          string            `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	BusinessId      int64             `protobuf:"varint,3,opt,name=businessId,proto3" json:"businessId,omitempty"`                                                                                 // 业务
	ProductId       string            `protobuf:"bytes,4,opt,name=productId,proto3" json:"productId,omitempty"`                                                                                    // 购买商品
	Amount          int64             `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`                                                                                         // 价格 单位分
	OrderId         string            `protobuf:"bytes,6,opt,name=orderId,proto3" json:"orderId,omitempty"`                                                                                        // 游戏订单号
	PlatformOrderId string            `protobuf:"bytes,7,opt,name=platformOrderId,proto3" json:"platformOrderId,omitempty"`                                                                        // 平台订单号
	Timestamp       int64             `protobuf:"varint,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                                                   // 时间
	Os              uint32            `protobuf:"varint,9,opt,name=os,proto3" json:"os,omitempty"`                                                                                                 // 系统
	Status          uint32            `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`                                                                                        // 状态
	CurrencyType    int64             `protobuf:"varint,11,opt,name=currencyType,proto3" json:"currencyType,omitempty"`                                                                            // 货币类型
	CurrencyId      int64             `protobuf:"varint,12,opt,name=currencyId,proto3" json:"currencyId,omitempty"`                                                                                // 货币id
	MapExt          map[string]string `protobuf:"bytes,20,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展字段
}

func (x *PayFlow) Reset() {
	*x = PayFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayFlow) ProtoMessage() {}

func (x *PayFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayFlow.ProtoReflect.Descriptor instead.
func (*PayFlow) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_common_common_proto_rawDescGZIP(), []int{0}
}

func (x *PayFlow) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PayFlow) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PayFlow) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PayFlow) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *PayFlow) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PayFlow) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PayFlow) GetPlatformOrderId() string {
	if x != nil {
		return x.PlatformOrderId
	}
	return ""
}

func (x *PayFlow) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *PayFlow) GetOs() uint32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *PayFlow) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PayFlow) GetCurrencyType() int64 {
	if x != nil {
		return x.CurrencyType
	}
	return 0
}

func (x *PayFlow) GetCurrencyId() int64 {
	if x != nil {
		return x.CurrencyId
	}
	return 0
}

func (x *PayFlow) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

var File_pb_uni_pay_common_common_proto protoreflect.FileDescriptor

var file_pb_uni_pay_common_common_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x2f, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x07, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x22, 0xcc, 0x03, 0x0a, 0x07, 0x50, 0x61,
	0x79, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61,
	0x79, 0x2e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a,
	0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x61, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x16, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x2a, 0x44, 0x0a, 0x0d, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x66, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0b, 0x0a, 0x07,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e, 0x67, 0x10,
	0x03, 0x2a, 0x3a, 0x0a, 0x08, 0x50, 0x61, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x0f, 0x0a,
	0x0b, 0x41, 0x70, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x50, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x10, 0x01, 0x12,
	0x0c, 0x0a, 0x08, 0x4b, 0x75, 0x67, 0x6f, 0x75, 0x56, 0x69, 0x70, 0x10, 0x02, 0x42, 0x46, 0x5a,
	0x44, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_uni_pay_common_common_proto_rawDescOnce sync.Once
	file_pb_uni_pay_common_common_proto_rawDescData = file_pb_uni_pay_common_common_proto_rawDesc
)

func file_pb_uni_pay_common_common_proto_rawDescGZIP() []byte {
	file_pb_uni_pay_common_common_proto_rawDescOnce.Do(func() {
		file_pb_uni_pay_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_uni_pay_common_common_proto_rawDescData)
	})
	return file_pb_uni_pay_common_common_proto_rawDescData
}

var file_pb_uni_pay_common_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pb_uni_pay_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_uni_pay_common_common_proto_goTypes = []interface{}{
	(OperatingSystem)(0), // 0: uni_pay.OperatingSystem
	(StatusOfOrder)(0),   // 1: uni_pay.StatusOfOrder
	(PayScene)(0),        // 2: uni_pay.PayScene
	(*PayFlow)(nil),      // 3: uni_pay.PayFlow
	nil,                  // 4: uni_pay.PayFlow.MapExtEntry
}
var file_pb_uni_pay_common_common_proto_depIdxs = []int32{
	4, // 0: uni_pay.PayFlow.mapExt:type_name -> uni_pay.PayFlow.MapExtEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_uni_pay_common_common_proto_init() }
func file_pb_uni_pay_common_common_proto_init() {
	if File_pb_uni_pay_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_uni_pay_common_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_uni_pay_common_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_uni_pay_common_common_proto_goTypes,
		DependencyIndexes: file_pb_uni_pay_common_common_proto_depIdxs,
		EnumInfos:         file_pb_uni_pay_common_common_proto_enumTypes,
		MessageInfos:      file_pb_uni_pay_common_common_proto_msgTypes,
	}.Build()
	File_pb_uni_pay_common_common_proto = out.File
	file_pb_uni_pay_common_common_proto_rawDesc = nil
	file_pb_uni_pay_common_common_proto_goTypes = nil
	file_pb_uni_pay_common_common_proto_depIdxs = nil
}
