// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/uni_pay/server/server.proto

package server

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlaceOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessId int64             `protobuf:"varint,1,opt,name=businessId,proto3" json:"businessId,omitempty"` // 业务id
	AppId      string            `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string            `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`       // openId
	ProductId  string            `protobuf:"bytes,4,opt,name=productId,proto3" json:"productId,omitempty"` // 商品id--对应业务礼包
	Price      int64             `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`        // 价格
	Os         uint32            `protobuf:"varint,6,opt,name=os,proto3" json:"os,omitempty"`              // 系统
	MapExt     map[string]string `protobuf:"bytes,7,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CurrencyId int64             `protobuf:"varint,8,opt,name=currencyId,proto3" json:"currencyId,omitempty"` // 货币id--扣费使用
	PayScene   uint32            `protobuf:"varint,9,opt,name=payScene,proto3" json:"payScene,omitempty"`     // 货币类型 见 common PayScene
}

func (x *PlaceOrderReq) Reset() {
	*x = PlaceOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_server_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceOrderReq) ProtoMessage() {}

func (x *PlaceOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_server_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceOrderReq.ProtoReflect.Descriptor instead.
func (*PlaceOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_server_server_proto_rawDescGZIP(), []int{0}
}

func (x *PlaceOrderReq) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PlaceOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PlaceOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PlaceOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *PlaceOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *PlaceOrderReq) GetOs() uint32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *PlaceOrderReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *PlaceOrderReq) GetCurrencyId() int64 {
	if x != nil {
		return x.CurrencyId
	}
	return 0
}

func (x *PlaceOrderReq) GetPayScene() uint32 {
	if x != nil {
		return x.PayScene
	}
	return 0
}

type PlaceOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"` // 订单号
}

func (x *PlaceOrderRsp) Reset() {
	*x = PlaceOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_server_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceOrderRsp) ProtoMessage() {}

func (x *PlaceOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_server_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceOrderRsp.ProtoReflect.Descriptor instead.
func (*PlaceOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_server_server_proto_rawDescGZIP(), []int{1}
}

func (x *PlaceOrderRsp) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type CheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Uid     int64  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	OpenId  string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	OrderId string `protobuf:"bytes,4,opt,name=orderId,proto3" json:"orderId,omitempty"`
	Price   int64  `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`
}

func (x *CheckOrderReq) Reset() {
	*x = CheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_server_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderReq) ProtoMessage() {}

func (x *CheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_server_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderReq.ProtoReflect.Descriptor instead.
func (*CheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_server_server_proto_rawDescGZIP(), []int{2}
}

func (x *CheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckOrderReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CheckOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CheckOrderReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CheckOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type CheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckOrderRsp) Reset() {
	*x = CheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_server_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderRsp) ProtoMessage() {}

func (x *CheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_server_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderRsp.ProtoReflect.Descriptor instead.
func (*CheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_server_server_proto_rawDescGZIP(), []int{3}
}

type DeliveryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Uid             int64  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	OpenId          string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	OrderId         string `protobuf:"bytes,4,opt,name=orderId,proto3" json:"orderId,omitempty"`
	ExternalOrderId string `protobuf:"bytes,5,opt,name=externalOrderId,proto3" json:"externalOrderId,omitempty"` // 外部订单号
	Price           int64  `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"`
	Timestamp       int64  `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *DeliveryReq) Reset() {
	*x = DeliveryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_server_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryReq) ProtoMessage() {}

func (x *DeliveryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_server_server_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryReq.ProtoReflect.Descriptor instead.
func (*DeliveryReq) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_server_server_proto_rawDescGZIP(), []int{4}
}

func (x *DeliveryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DeliveryReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *DeliveryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DeliveryReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *DeliveryReq) GetExternalOrderId() string {
	if x != nil {
		return x.ExternalOrderId
	}
	return ""
}

func (x *DeliveryReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *DeliveryReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type DeliveryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeliveryRsp) Reset() {
	*x = DeliveryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_uni_pay_server_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryRsp) ProtoMessage() {}

func (x *DeliveryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_uni_pay_server_server_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryRsp.ProtoReflect.Descriptor instead.
func (*DeliveryRsp) Descriptor() ([]byte, []int) {
	return file_pb_uni_pay_server_server_proto_rawDescGZIP(), []int{5}
}

var File_pb_uni_pay_server_server_proto protoreflect.FileDescriptor

var file_pb_uni_pay_server_server_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x2f, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x22, 0xdb, 0x02, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x41, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x79, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x29,
	0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x0d, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0xc5, 0x01, 0x0a, 0x0b,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x22, 0x0d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52,
	0x73, 0x70, 0x32, 0xe6, 0x01, 0x0a, 0x06, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x4a, 0x0a,
	0x0a, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x75, 0x6e,
	0x69, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x75, 0x6e, 0x69,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0a, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61,
	0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x08, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x12, 0x1b, 0x2e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1b,
	0x2e, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x42, 0x46, 0x5a, 0x44, 0x74,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x75, 0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_uni_pay_server_server_proto_rawDescOnce sync.Once
	file_pb_uni_pay_server_server_proto_rawDescData = file_pb_uni_pay_server_server_proto_rawDesc
)

func file_pb_uni_pay_server_server_proto_rawDescGZIP() []byte {
	file_pb_uni_pay_server_server_proto_rawDescOnce.Do(func() {
		file_pb_uni_pay_server_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_uni_pay_server_server_proto_rawDescData)
	})
	return file_pb_uni_pay_server_server_proto_rawDescData
}

var file_pb_uni_pay_server_server_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_pb_uni_pay_server_server_proto_goTypes = []interface{}{
	(*PlaceOrderReq)(nil), // 0: uni_pay_server.PlaceOrderReq
	(*PlaceOrderRsp)(nil), // 1: uni_pay_server.PlaceOrderRsp
	(*CheckOrderReq)(nil), // 2: uni_pay_server.CheckOrderReq
	(*CheckOrderRsp)(nil), // 3: uni_pay_server.CheckOrderRsp
	(*DeliveryReq)(nil),   // 4: uni_pay_server.DeliveryReq
	(*DeliveryRsp)(nil),   // 5: uni_pay_server.DeliveryRsp
	nil,                   // 6: uni_pay_server.PlaceOrderReq.MapExtEntry
}
var file_pb_uni_pay_server_server_proto_depIdxs = []int32{
	6, // 0: uni_pay_server.PlaceOrderReq.mapExt:type_name -> uni_pay_server.PlaceOrderReq.MapExtEntry
	0, // 1: uni_pay_server.Server.PlaceOrder:input_type -> uni_pay_server.PlaceOrderReq
	2, // 2: uni_pay_server.Server.CheckOrder:input_type -> uni_pay_server.CheckOrderReq
	4, // 3: uni_pay_server.Server.Delivery:input_type -> uni_pay_server.DeliveryReq
	1, // 4: uni_pay_server.Server.PlaceOrder:output_type -> uni_pay_server.PlaceOrderRsp
	3, // 5: uni_pay_server.Server.CheckOrder:output_type -> uni_pay_server.CheckOrderRsp
	5, // 6: uni_pay_server.Server.Delivery:output_type -> uni_pay_server.DeliveryRsp
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_uni_pay_server_server_proto_init() }
func file_pb_uni_pay_server_server_proto_init() {
	if File_pb_uni_pay_server_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_uni_pay_server_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_uni_pay_server_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_uni_pay_server_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_uni_pay_server_server_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_uni_pay_server_server_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_uni_pay_server_server_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_uni_pay_server_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_uni_pay_server_server_proto_goTypes,
		DependencyIndexes: file_pb_uni_pay_server_server_proto_depIdxs,
		MessageInfos:      file_pb_uni_pay_server_server_proto_msgTypes,
	}.Build()
	File_pb_uni_pay_server_server_proto = out.File
	file_pb_uni_pay_server_server_proto_rawDesc = nil
	file_pb_uni_pay_server_server_proto_goTypes = nil
	file_pb_uni_pay_server_server_proto_depIdxs = nil
}
