{"swagger": "2.0", "info": {"title": "pb/uni_pay/server/server.proto", "version": "version not set"}, "tags": [{"name": "Server"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/uni_pay_server.Server/CheckOrder": {"post": {"summary": "订单校验", "operationId": "Server_CheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/uni_pay_serverCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/uni_pay_serverCheckOrderReq"}}], "tags": ["Server"]}}, "/uni_pay_server.Server/Delivery": {"post": {"summary": "订单发货", "operationId": "Server_Delivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/uni_pay_serverDeliveryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/uni_pay_serverDeliveryReq"}}], "tags": ["Server"]}}, "/uni_pay_server.Server/PlaceOrder": {"post": {"summary": "下单", "operationId": "Server_PlaceOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/uni_pay_serverPlaceOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/uni_pay_serverPlaceOrderReq"}}], "tags": ["Server"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "uni_pay_serverCheckOrderReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "int64"}, "openId": {"type": "string"}, "orderId": {"type": "string"}, "price": {"type": "string", "format": "int64"}}}, "uni_pay_serverCheckOrderRsp": {"type": "object"}, "uni_pay_serverDeliveryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "int64"}, "openId": {"type": "string"}, "orderId": {"type": "string"}, "externalOrderId": {"type": "string", "title": "外部订单号"}, "price": {"type": "string", "format": "int64"}, "timestamp": {"type": "string", "format": "int64"}}}, "uni_pay_serverDeliveryRsp": {"type": "object"}, "uni_pay_serverPlaceOrderReq": {"type": "object", "properties": {"businessId": {"type": "string", "format": "int64", "title": "业务id"}, "appId": {"type": "string"}, "openId": {"type": "string", "title": "openId"}, "productId": {"type": "string", "title": "商品id--对应业务礼包"}, "price": {"type": "string", "format": "int64", "title": "价格"}, "os": {"type": "integer", "format": "int64", "title": "系统"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}, "currencyId": {"type": "string", "format": "int64", "title": "货币id--扣费使用"}, "payScene": {"type": "integer", "format": "int64", "title": "货币类型 见 common PayScene"}}}, "uni_pay_serverPlaceOrderRsp": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单号"}}}}}