// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/adapter_safety/adapter_safety.proto

package adapter_safety

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AdapterSafety_SafeCheckPic_FullMethodName = "/component.game.AdapterSafety/SafeCheckPic"
)

// AdapterSafetyClient is the client API for AdapterSafety service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterSafetyClient interface {
	// 图片安全上报
	SafeCheckPic(ctx context.Context, in *SafeCheckPicReq, opts ...grpc.CallOption) (*SafeCheckPicRsp, error)
}

type adapterSafetyClient struct {
	cc grpc.ClientConnInterface
}

func NewAdapterSafetyClient(cc grpc.ClientConnInterface) AdapterSafetyClient {
	return &adapterSafetyClient{cc}
}

func (c *adapterSafetyClient) SafeCheckPic(ctx context.Context, in *SafeCheckPicReq, opts ...grpc.CallOption) (*SafeCheckPicRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SafeCheckPicRsp)
	err := c.cc.Invoke(ctx, AdapterSafety_SafeCheckPic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterSafetyServer is the server API for AdapterSafety service.
// All implementations should embed UnimplementedAdapterSafetyServer
// for forward compatibility
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterSafetyServer interface {
	// 图片安全上报
	SafeCheckPic(context.Context, *SafeCheckPicReq) (*SafeCheckPicRsp, error)
}

// UnimplementedAdapterSafetyServer should be embedded to have forward compatible implementations.
type UnimplementedAdapterSafetyServer struct {
}

func (UnimplementedAdapterSafetyServer) SafeCheckPic(context.Context, *SafeCheckPicReq) (*SafeCheckPicRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SafeCheckPic not implemented")
}

// UnsafeAdapterSafetyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdapterSafetyServer will
// result in compilation errors.
type UnsafeAdapterSafetyServer interface {
	mustEmbedUnimplementedAdapterSafetyServer()
}

func RegisterAdapterSafetyServer(s grpc.ServiceRegistrar, srv AdapterSafetyServer) {
	s.RegisterService(&AdapterSafety_ServiceDesc, srv)
}

func _AdapterSafety_SafeCheckPic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SafeCheckPicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterSafetyServer).SafeCheckPic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterSafety_SafeCheckPic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterSafetyServer).SafeCheckPic(ctx, req.(*SafeCheckPicReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdapterSafety_ServiceDesc is the grpc.ServiceDesc for AdapterSafety service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdapterSafety_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.AdapterSafety",
	HandlerType: (*AdapterSafetyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SafeCheckPic",
			Handler:    _AdapterSafety_SafeCheckPic_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/adapter_safety/adapter_safety.proto",
}
