// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_relationship/game_relationship.proto

package game_relationship

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryGameAllBindFriendsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId      string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`           // 用户ID
	AppId       string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`             // 小游戏AppId
	Cookie      string `protobuf:"bytes,3,opt,name=cookie,proto3" json:"cookie,omitempty"`           // 登录态信息[q音]
	AccessToken string `protobuf:"bytes,4,opt,name=accessToken,proto3" json:"accessToken,omitempty"` // 登录态信息[酷狗]
}

func (x *QueryGameAllBindFriendsReq) Reset() {
	*x = QueryGameAllBindFriendsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameAllBindFriendsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameAllBindFriendsReq) ProtoMessage() {}

func (x *QueryGameAllBindFriendsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameAllBindFriendsReq.ProtoReflect.Descriptor instead.
func (*QueryGameAllBindFriendsReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{0}
}

func (x *QueryGameAllBindFriendsReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryGameAllBindFriendsReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryGameAllBindFriendsReq) GetCookie() string {
	if x != nil {
		return x.Cookie
	}
	return ""
}

func (x *QueryGameAllBindFriendsReq) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type QueryGameAllBindFriendsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenIds       []string `protobuf:"bytes,1,rep,name=openIds,proto3" json:"openIds,omitempty"`              // 所有绑定好友
	FriendApiAuth int32    `protobuf:"varint,2,opt,name=friendApiAuth,proto3" json:"friendApiAuth,omitempty"` // 好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆
	BingStatus    int32    `protobuf:"varint,3,opt,name=bingStatus,proto3" json:"bingStatus,omitempty"`       // 绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定
}

func (x *QueryGameAllBindFriendsRsp) Reset() {
	*x = QueryGameAllBindFriendsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameAllBindFriendsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameAllBindFriendsRsp) ProtoMessage() {}

func (x *QueryGameAllBindFriendsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameAllBindFriendsRsp.ProtoReflect.Descriptor instead.
func (*QueryGameAllBindFriendsRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{1}
}

func (x *QueryGameAllBindFriendsRsp) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

func (x *QueryGameAllBindFriendsRsp) GetFriendApiAuth() int32 {
	if x != nil {
		return x.FriendApiAuth
	}
	return 0
}

func (x *QueryGameAllBindFriendsRsp) GetBingStatus() int32 {
	if x != nil {
		return x.BingStatus
	}
	return 0
}

type QueryGameFriendsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId    string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`       // 用户ID
	AppId     string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`         // 小游戏AppId
	Passback  int32  `protobuf:"varint,3,opt,name=passback,proto3" json:"passback,omitempty"`  // 回传参数,首次不用传
	FilterTag string `protobuf:"bytes,4,opt,name=filterTag,proto3" json:"filterTag,omitempty"` // 过滤标签，可选
}

func (x *QueryGameFriendsReq) Reset() {
	*x = QueryGameFriendsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameFriendsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameFriendsReq) ProtoMessage() {}

func (x *QueryGameFriendsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameFriendsReq.ProtoReflect.Descriptor instead.
func (*QueryGameFriendsReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{2}
}

func (x *QueryGameFriendsReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryGameFriendsReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryGameFriendsReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *QueryGameFriendsReq) GetFilterTag() string {
	if x != nil {
		return x.FilterTag
	}
	return ""
}

type QueryGameFriendsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenIds  []string       `protobuf:"bytes,1,rep,name=openIds,proto3" json:"openIds,omitempty"`    // 在线观众
	Passback int32          `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"` // 回传参数,首次不用传
	HasNext  bool           `protobuf:"varint,3,opt,name=hasNext,proto3" json:"hasNext,omitempty"`   // 是否还有数据
	TagInfo  []*GameTagInfo `protobuf:"bytes,4,rep,name=TagInfo,proto3" json:"TagInfo,omitempty"`    // 过滤标签信息
}

func (x *QueryGameFriendsRsp) Reset() {
	*x = QueryGameFriendsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameFriendsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameFriendsRsp) ProtoMessage() {}

func (x *QueryGameFriendsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameFriendsRsp.ProtoReflect.Descriptor instead.
func (*QueryGameFriendsRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{3}
}

func (x *QueryGameFriendsRsp) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

func (x *QueryGameFriendsRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *QueryGameFriendsRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *QueryGameFriendsRsp) GetTagInfo() []*GameTagInfo {
	if x != nil {
		return x.TagInfo
	}
	return nil
}

type QueryGameAudiencesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`         // 小游戏AppId
	RoomId    string `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`       // 房间ID,必传参数
	ShowId    string `protobuf:"bytes,3,opt,name=showId,proto3" json:"showId,omitempty"`       // showId
	OpenId    string `protobuf:"bytes,4,opt,name=openId,proto3" json:"openId,omitempty"`       // 用户ID
	Passback  int32  `protobuf:"varint,5,opt,name=passback,proto3" json:"passback,omitempty"`  // 回传参数,首次不用传
	FilterTag string `protobuf:"bytes,6,opt,name=filterTag,proto3" json:"filterTag,omitempty"` // 过滤标签，可选
}

func (x *QueryGameAudiencesReq) Reset() {
	*x = QueryGameAudiencesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameAudiencesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameAudiencesReq) ProtoMessage() {}

func (x *QueryGameAudiencesReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameAudiencesReq.ProtoReflect.Descriptor instead.
func (*QueryGameAudiencesReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{4}
}

func (x *QueryGameAudiencesReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryGameAudiencesReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *QueryGameAudiencesReq) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

func (x *QueryGameAudiencesReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryGameAudiencesReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *QueryGameAudiencesReq) GetFilterTag() string {
	if x != nil {
		return x.FilterTag
	}
	return ""
}

type QueryGameAudiencesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenIds  []string       `protobuf:"bytes,1,rep,name=openIds,proto3" json:"openIds,omitempty"`    // 在线观众
	Passback int32          `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"` // 回传参数,首次不用传
	HasNext  bool           `protobuf:"varint,3,opt,name=hasNext,proto3" json:"hasNext,omitempty"`   // 是否还有数据
	TagInfo  []*GameTagInfo `protobuf:"bytes,4,rep,name=TagInfo,proto3" json:"TagInfo,omitempty"`    // 过滤标签信息
}

func (x *QueryGameAudiencesRsp) Reset() {
	*x = QueryGameAudiencesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameAudiencesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameAudiencesRsp) ProtoMessage() {}

func (x *QueryGameAudiencesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameAudiencesRsp.ProtoReflect.Descriptor instead.
func (*QueryGameAudiencesRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{5}
}

func (x *QueryGameAudiencesRsp) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

func (x *QueryGameAudiencesRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *QueryGameAudiencesRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *QueryGameAudiencesRsp) GetTagInfo() []*GameTagInfo {
	if x != nil {
		return x.TagInfo
	}
	return nil
}

type QueryAudienceDurationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`                                     // 小游戏appID
	OpenIds       []string `protobuf:"bytes,2,rep,name=openIds,proto3" json:"openIds,omitempty"`                                 // openId列表
	RoomType      RoomType `protobuf:"varint,3,opt,name=roomType,proto3,enum=component.game.RoomType" json:"roomType,omitempty"` // 场景类型,见RoomType
	FromTimestamp int64    `protobuf:"varint,4,opt,name=fromTimestamp,proto3" json:"fromTimestamp,omitempty"`                    // 开始时间戳(s), 最长只支持最近24小时
}

func (x *QueryAudienceDurationReq) Reset() {
	*x = QueryAudienceDurationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAudienceDurationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAudienceDurationReq) ProtoMessage() {}

func (x *QueryAudienceDurationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAudienceDurationReq.ProtoReflect.Descriptor instead.
func (*QueryAudienceDurationReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{6}
}

func (x *QueryAudienceDurationReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryAudienceDurationReq) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

func (x *QueryAudienceDurationReq) GetRoomType() RoomType {
	if x != nil {
		return x.RoomType
	}
	return RoomType_RoomTypeUnknown
}

func (x *QueryAudienceDurationReq) GetFromTimestamp() int64 {
	if x != nil {
		return x.FromTimestamp
	}
	return 0
}

type QueryAudienceDurationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info map[string]int32 `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // map<openId,int32> 秒
}

func (x *QueryAudienceDurationRsp) Reset() {
	*x = QueryAudienceDurationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAudienceDurationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAudienceDurationRsp) ProtoMessage() {}

func (x *QueryAudienceDurationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAudienceDurationRsp.ProtoReflect.Descriptor instead.
func (*QueryAudienceDurationRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{7}
}

func (x *QueryAudienceDurationRsp) GetInfo() map[string]int32 {
	if x != nil {
		return x.Info
	}
	return nil
}

type QueryHaveUserPlayedReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`     // 小游戏 appID
	OpenIds []string `protobuf:"bytes,2,rep,name=openIds,proto3" json:"openIds,omitempty"` // openId 列表
}

func (x *QueryHaveUserPlayedReq) Reset() {
	*x = QueryHaveUserPlayedReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryHaveUserPlayedReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryHaveUserPlayedReq) ProtoMessage() {}

func (x *QueryHaveUserPlayedReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryHaveUserPlayedReq.ProtoReflect.Descriptor instead.
func (*QueryHaveUserPlayedReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{8}
}

func (x *QueryHaveUserPlayedReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryHaveUserPlayedReq) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type QueryHaveUserPlayedRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenIds []string `protobuf:"bytes,1,rep,name=openIds,proto3" json:"openIds,omitempty"` // 玩过的 openId 列表
}

func (x *QueryHaveUserPlayedRsp) Reset() {
	*x = QueryHaveUserPlayedRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryHaveUserPlayedRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryHaveUserPlayedRsp) ProtoMessage() {}

func (x *QueryHaveUserPlayedRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryHaveUserPlayedRsp.ProtoReflect.Descriptor instead.
func (*QueryHaveUserPlayedRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{9}
}

func (x *QueryHaveUserPlayedRsp) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type ReportGameTagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string             `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"` // 小游戏 appID
	OpenId      string             `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	TagInfoList []*RelationshipTag `protobuf:"bytes,3,rep,name=tagInfoList,proto3" json:"tagInfoList,omitempty"` // 标签列表
}

func (x *ReportGameTagReq) Reset() {
	*x = ReportGameTagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportGameTagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportGameTagReq) ProtoMessage() {}

func (x *ReportGameTagReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportGameTagReq.ProtoReflect.Descriptor instead.
func (*ReportGameTagReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{10}
}

func (x *ReportGameTagReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportGameTagReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReportGameTagReq) GetTagInfoList() []*RelationshipTag {
	if x != nil {
		return x.TagInfoList
	}
	return nil
}

type ReportGameTagRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret int64 `protobuf:"varint,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *ReportGameTagRsp) Reset() {
	*x = ReportGameTagRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportGameTagRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportGameTagRsp) ProtoMessage() {}

func (x *ReportGameTagRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_game_relationship_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportGameTagRsp.ProtoReflect.Descriptor instead.
func (*ReportGameTagRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_game_relationship_proto_rawDescGZIP(), []int{11}
}

func (x *ReportGameTagRsp) GetRet() int64 {
	if x != nil {
		return x.Ret
	}
	return 0
}

var File_pb_game_relationship_game_relationship_proto protoreflect.FileDescriptor

var file_pb_game_relationship_game_relationship_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1f,
	0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x84, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x6c, 0x6c,
	0x42, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f,
	0x6f, 0x6b, 0x69, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7c, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47,
	0x61, 0x6d, 0x65, 0x41, 0x6c, 0x6c, 0x42, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x24,
	0x0a, 0x0d, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x41, 0x70, 0x69, 0x41, 0x75, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x41, 0x70, 0x69,
	0x41, 0x75, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x7d, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d,
	0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54,
	0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x22, 0x9c, 0x01, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d,
	0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x07, 0x54,
	0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x54, 0x61, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xaf, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68,
	0x6f, 0x77, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x22, 0x9e, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61,
	0x6d, 0x65, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x35,
	0x0a, 0x07, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x54, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa6, 0x01, 0x0a, 0x18, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x73, 0x12, 0x34, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x72, 0x6f, 0x6d,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x9b,
	0x01, 0x0a, 0x18, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x1a, 0x37, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x48, 0x0a, 0x16,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x61, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x64, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x32, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48,
	0x61, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x10, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x41, 0x0a,
	0x0b, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70,
	0x54, 0x61, 0x67, 0x52, 0x0b, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x24, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x61,
	0x67, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x72, 0x65, 0x74, 0x32, 0x9e, 0x07, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x5c, 0x0a, 0x10, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x12,
	0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46,
	0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x12, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12,
	0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d,
	0x65, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x6b, 0x0a,
	0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x16, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x46, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46,
	0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x47, 0x61, 0x6d, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x66,
	0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x42, 0x6f, 0x74, 0x68, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x23, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x72, 0x69, 0x65,
	0x6e, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x60, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47,
	0x61, 0x6d, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x23,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x71, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x6c, 0x6c, 0x42, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x69, 0x65,
	0x6e, 0x64, 0x73, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x6c,
	0x6c, 0x42, 0x69, 0x6e, 0x64, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x6c, 0x6c, 0x42, 0x69, 0x6e,
	0x64, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12, 0x65, 0x0a, 0x13, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x48, 0x61, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x64, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x61, 0x76, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x48, 0x61, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x53, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x6d, 0x65,
	0x54, 0x61, 0x67, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x54,
	0x61, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x61, 0x6d,
	0x65, 0x54, 0x61, 0x67, 0x52, 0x73, 0x70, 0x42, 0x49, 0x5a, 0x47, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68,
	0x69, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_relationship_game_relationship_proto_rawDescOnce sync.Once
	file_pb_game_relationship_game_relationship_proto_rawDescData = file_pb_game_relationship_game_relationship_proto_rawDesc
)

func file_pb_game_relationship_game_relationship_proto_rawDescGZIP() []byte {
	file_pb_game_relationship_game_relationship_proto_rawDescOnce.Do(func() {
		file_pb_game_relationship_game_relationship_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_relationship_game_relationship_proto_rawDescData)
	})
	return file_pb_game_relationship_game_relationship_proto_rawDescData
}

var file_pb_game_relationship_game_relationship_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_pb_game_relationship_game_relationship_proto_goTypes = []interface{}{
	(*QueryGameAllBindFriendsReq)(nil), // 0: component.game.QueryGameAllBindFriendsReq
	(*QueryGameAllBindFriendsRsp)(nil), // 1: component.game.QueryGameAllBindFriendsRsp
	(*QueryGameFriendsReq)(nil),        // 2: component.game.QueryGameFriendsReq
	(*QueryGameFriendsRsp)(nil),        // 3: component.game.QueryGameFriendsRsp
	(*QueryGameAudiencesReq)(nil),      // 4: component.game.QueryGameAudiencesReq
	(*QueryGameAudiencesRsp)(nil),      // 5: component.game.QueryGameAudiencesRsp
	(*QueryAudienceDurationReq)(nil),   // 6: component.game.QueryAudienceDurationReq
	(*QueryAudienceDurationRsp)(nil),   // 7: component.game.QueryAudienceDurationRsp
	(*QueryHaveUserPlayedReq)(nil),     // 8: component.game.QueryHaveUserPlayedReq
	(*QueryHaveUserPlayedRsp)(nil),     // 9: component.game.QueryHaveUserPlayedRsp
	(*ReportGameTagReq)(nil),           // 10: component.game.ReportGameTagReq
	(*ReportGameTagRsp)(nil),           // 11: component.game.ReportGameTagRsp
	nil,                                // 12: component.game.QueryAudienceDurationRsp.InfoEntry
	(*GameTagInfo)(nil),                // 13: component.game.GameTagInfo
	(RoomType)(0),                      // 14: component.game.RoomType
	(*RelationshipTag)(nil),            // 15: component.game.RelationshipTag
}
var file_pb_game_relationship_game_relationship_proto_depIdxs = []int32{
	13, // 0: component.game.QueryGameFriendsRsp.TagInfo:type_name -> component.game.GameTagInfo
	13, // 1: component.game.QueryGameAudiencesRsp.TagInfo:type_name -> component.game.GameTagInfo
	14, // 2: component.game.QueryAudienceDurationReq.roomType:type_name -> component.game.RoomType
	12, // 3: component.game.QueryAudienceDurationRsp.info:type_name -> component.game.QueryAudienceDurationRsp.InfoEntry
	15, // 4: component.game.ReportGameTagReq.tagInfoList:type_name -> component.game.RelationshipTag
	2,  // 5: component.game.GameRelationship.QueryGameFriends:input_type -> component.game.QueryGameFriendsReq
	4,  // 6: component.game.GameRelationship.QueryGameAudiences:input_type -> component.game.QueryGameAudiencesReq
	6,  // 7: component.game.GameRelationship.QueryAudienceDuration:input_type -> component.game.QueryAudienceDurationReq
	2,  // 8: component.game.GameRelationship.QueryGameFollowFriends:input_type -> component.game.QueryGameFriendsReq
	2,  // 9: component.game.GameRelationship.QueryGameBothFollowFriends:input_type -> component.game.QueryGameFriendsReq
	2,  // 10: component.game.GameRelationship.QueryGameBindFriends:input_type -> component.game.QueryGameFriendsReq
	0,  // 11: component.game.GameRelationship.QueryGameAllBindFriends:input_type -> component.game.QueryGameAllBindFriendsReq
	8,  // 12: component.game.GameRelationship.QueryHaveUserPlayed:input_type -> component.game.QueryHaveUserPlayedReq
	10, // 13: component.game.GameRelationship.ReportGameTag:input_type -> component.game.ReportGameTagReq
	3,  // 14: component.game.GameRelationship.QueryGameFriends:output_type -> component.game.QueryGameFriendsRsp
	5,  // 15: component.game.GameRelationship.QueryGameAudiences:output_type -> component.game.QueryGameAudiencesRsp
	7,  // 16: component.game.GameRelationship.QueryAudienceDuration:output_type -> component.game.QueryAudienceDurationRsp
	3,  // 17: component.game.GameRelationship.QueryGameFollowFriends:output_type -> component.game.QueryGameFriendsRsp
	3,  // 18: component.game.GameRelationship.QueryGameBothFollowFriends:output_type -> component.game.QueryGameFriendsRsp
	3,  // 19: component.game.GameRelationship.QueryGameBindFriends:output_type -> component.game.QueryGameFriendsRsp
	1,  // 20: component.game.GameRelationship.QueryGameAllBindFriends:output_type -> component.game.QueryGameAllBindFriendsRsp
	9,  // 21: component.game.GameRelationship.QueryHaveUserPlayed:output_type -> component.game.QueryHaveUserPlayedRsp
	11, // 22: component.game.GameRelationship.ReportGameTag:output_type -> component.game.ReportGameTagRsp
	14, // [14:23] is the sub-list for method output_type
	5,  // [5:14] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pb_game_relationship_game_relationship_proto_init() }
func file_pb_game_relationship_game_relationship_proto_init() {
	if File_pb_game_relationship_game_relationship_proto != nil {
		return
	}
	file_pb_game_relationship_comm_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_game_relationship_game_relationship_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameAllBindFriendsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameAllBindFriendsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameFriendsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameFriendsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameAudiencesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameAudiencesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAudienceDurationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAudienceDurationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryHaveUserPlayedReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryHaveUserPlayedRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportGameTagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_game_relationship_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportGameTagRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_relationship_game_relationship_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_relationship_game_relationship_proto_goTypes,
		DependencyIndexes: file_pb_game_relationship_game_relationship_proto_depIdxs,
		MessageInfos:      file_pb_game_relationship_game_relationship_proto_msgTypes,
	}.Build()
	File_pb_game_relationship_game_relationship_proto = out.File
	file_pb_game_relationship_game_relationship_proto_rawDesc = nil
	file_pb_game_relationship_game_relationship_proto_goTypes = nil
	file_pb_game_relationship_game_relationship_proto_depIdxs = nil
}
