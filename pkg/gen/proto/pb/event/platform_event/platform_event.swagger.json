{"swagger": "2.0", "info": {"title": "pb/event/platform_event/platform_event.proto", "version": "version not set"}, "tags": [{"name": "PlatformEvent"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/event.PlatformEvent/ConfigSync": {"post": {"summary": "配置同步", "operationId": "PlatformEvent_ConfigSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/eventConfigSyncRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/eventConfigSyncReq"}}], "tags": ["PlatformEvent"]}}, "/event.PlatformEvent/GameDeliveryPackage": {"post": {"summary": "游戏下发礼包", "operationId": "PlatformEvent_GameDeliveryPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/eventGameDeliveryPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/eventGameDeliveryPackageReq"}}], "tags": ["PlatformEvent"]}}, "/event.PlatformEvent/Notify": {"post": {"operationId": "PlatformEvent_Notify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/eventTmePlatformEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/eventTmePlatformEventReq"}}], "tags": ["PlatformEvent"]}}}, "definitions": {"eventConfigSyncReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "eventType": {"type": "string", "title": "事件类型"}, "filterRule": {"type": "string", "title": "过滤规则，反射查询对应字段"}, "callbackAddr": {"type": "string", "title": "回调地址"}, "appID": {"type": "string", "format": "int64", "title": "游戏appID"}, "status": {"type": "string", "title": "上架状态"}}}, "eventConfigSyncRsp": {"type": "object", "properties": {"retCode": {"type": "integer", "format": "int32", "title": "错误码"}, "retMsg": {"type": "string", "title": "错误提示语"}}}, "eventGameDeliveryPackageReq": {"type": "object", "properties": {"appID": {"type": "string", "title": "游戏appID"}, "userID": {"type": "string", "format": "int64"}, "packageID": {"type": "string", "format": "int64"}, "billNo": {"type": "string", "title": "唯一订单 id"}, "num": {"type": "string", "format": "int64", "title": "发几个礼包,默认一个"}}}, "eventGameDeliveryPackageRsp": {"type": "object", "properties": {"errMsg": {"type": "string"}}}, "eventTmePlatformEventReq": {"type": "object", "properties": {"plat": {"type": "integer", "format": "int64", "title": "平台ID gopen.EPlatID"}, "openID": {"type": "string"}, "toOpenID": {"type": "string", "title": "目标用户"}, "eventType": {"type": "integer", "format": "int64", "title": "事件类型 event.TmePlatformEventType"}, "eventInfo": {"type": "string", "title": "事件对应结构，json 序列化数据"}, "ts": {"type": "string", "format": "int64", "title": "时间戳"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "额外信息"}}}, "eventTmePlatformEventRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}