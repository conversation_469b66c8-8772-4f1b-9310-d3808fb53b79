// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/event/platform_event/platform_event.proto

package platform_event

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	event "kugou_adapter_service/pkg/gen/proto/pb/event"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TmePlatformEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plat      uint32            `protobuf:"varint,1,opt,name=plat,proto3" json:"plat,omitempty"` // 平台ID gopen.EPlatID
	OpenID    string            `protobuf:"bytes,2,opt,name=openID,proto3" json:"openID,omitempty"`
	ToOpenID  string            `protobuf:"bytes,3,opt,name=toOpenID,proto3" json:"toOpenID,omitempty"`                                                                                     // 目标用户
	EventType uint32            `protobuf:"varint,4,opt,name=eventType,proto3" json:"eventType,omitempty"`                                                                                  // 事件类型 event.TmePlatformEventType
	EventInfo string            `protobuf:"bytes,5,opt,name=eventInfo,proto3" json:"eventInfo,omitempty"`                                                                                   // 事件对应结构，json 序列化数据
	Ts        int64             `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`                                                                                                // 时间戳
	MapExt    map[string]string `protobuf:"bytes,7,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 额外信息
}

func (x *TmePlatformEventReq) Reset() {
	*x = TmePlatformEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmePlatformEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmePlatformEventReq) ProtoMessage() {}

func (x *TmePlatformEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmePlatformEventReq.ProtoReflect.Descriptor instead.
func (*TmePlatformEventReq) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{0}
}

func (x *TmePlatformEventReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *TmePlatformEventReq) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *TmePlatformEventReq) GetToOpenID() string {
	if x != nil {
		return x.ToOpenID
	}
	return ""
}

func (x *TmePlatformEventReq) GetEventType() uint32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *TmePlatformEventReq) GetEventInfo() string {
	if x != nil {
		return x.EventInfo
	}
	return ""
}

func (x *TmePlatformEventReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *TmePlatformEventReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type TmePlatformEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TmePlatformEventRsp) Reset() {
	*x = TmePlatformEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmePlatformEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmePlatformEventRsp) ProtoMessage() {}

func (x *TmePlatformEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmePlatformEventRsp.ProtoReflect.Descriptor instead.
func (*TmePlatformEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{1}
}

type ConfigSyncReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EventType    string `protobuf:"bytes,2,opt,name=eventType,proto3" json:"eventType,omitempty"`       // 事件类型
	FilterRule   string `protobuf:"bytes,3,opt,name=filterRule,proto3" json:"filterRule,omitempty"`     // 过滤规则，反射查询对应字段
	CallbackAddr string `protobuf:"bytes,4,opt,name=callbackAddr,proto3" json:"callbackAddr,omitempty"` // 回调地址
	AppID        int64  `protobuf:"varint,5,opt,name=appID,proto3" json:"appID,omitempty"`              // 游戏appID
	Status       string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`             //上架状态
}

func (x *ConfigSyncReq) Reset() {
	*x = ConfigSyncReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigSyncReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigSyncReq) ProtoMessage() {}

func (x *ConfigSyncReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigSyncReq.ProtoReflect.Descriptor instead.
func (*ConfigSyncReq) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{2}
}

func (x *ConfigSyncReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConfigSyncReq) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *ConfigSyncReq) GetFilterRule() string {
	if x != nil {
		return x.FilterRule
	}
	return ""
}

func (x *ConfigSyncReq) GetCallbackAddr() string {
	if x != nil {
		return x.CallbackAddr
	}
	return ""
}

func (x *ConfigSyncReq) GetAppID() int64 {
	if x != nil {
		return x.AppID
	}
	return 0
}

func (x *ConfigSyncReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ConfigSyncRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RetCode int32  `protobuf:"varint,1,opt,name=retCode,proto3" json:"retCode,omitempty"` // 错误码
	RetMsg  string `protobuf:"bytes,2,opt,name=retMsg,proto3" json:"retMsg,omitempty"`    // 错误提示语
}

func (x *ConfigSyncRsp) Reset() {
	*x = ConfigSyncRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigSyncRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigSyncRsp) ProtoMessage() {}

func (x *ConfigSyncRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigSyncRsp.ProtoReflect.Descriptor instead.
func (*ConfigSyncRsp) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{3}
}

func (x *ConfigSyncRsp) GetRetCode() int32 {
	if x != nil {
		return x.RetCode
	}
	return 0
}

func (x *ConfigSyncRsp) GetRetMsg() string {
	if x != nil {
		return x.RetMsg
	}
	return ""
}

// 平台事件流配置(表)
type PlatformEventConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EventType    event.TmePlatformEventType `protobuf:"varint,2,opt,name=eventType,proto3,enum=event.TmePlatformEventType" json:"eventType,omitempty"` // 事件类型 TmePlatformEventType
	FilterRule   *FilterRuleStruct          `protobuf:"bytes,3,opt,name=filterRule,proto3" json:"filterRule,omitempty"`                                // 过滤字段
	CallbackAddr string                     `protobuf:"bytes,4,opt,name=callbackAddr,proto3" json:"callbackAddr,omitempty"`                            // 回调地址
	AppID        int64                      `protobuf:"varint,5,opt,name=appID,proto3" json:"appID,omitempty"`                                         // 游戏appID
}

func (x *PlatformEventConfig) Reset() {
	*x = PlatformEventConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatformEventConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformEventConfig) ProtoMessage() {}

func (x *PlatformEventConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformEventConfig.ProtoReflect.Descriptor instead.
func (*PlatformEventConfig) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{4}
}

func (x *PlatformEventConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlatformEventConfig) GetEventType() event.TmePlatformEventType {
	if x != nil {
		return x.EventType
	}
	return event.TmePlatformEventType(0)
}

func (x *PlatformEventConfig) GetFilterRule() *FilterRuleStruct {
	if x != nil {
		return x.FilterRule
	}
	return nil
}

func (x *PlatformEventConfig) GetCallbackAddr() string {
	if x != nil {
		return x.CallbackAddr
	}
	return ""
}

func (x *PlatformEventConfig) GetAppID() int64 {
	if x != nil {
		return x.AppID
	}
	return 0
}

type FilterRuleStruct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MatchList []*MatchInfo `protobuf:"bytes,2,rep,name=matchList,proto3" json:"matchList,omitempty"`
}

func (x *FilterRuleStruct) Reset() {
	*x = FilterRuleStruct{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterRuleStruct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterRuleStruct) ProtoMessage() {}

func (x *FilterRuleStruct) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterRuleStruct.ProtoReflect.Descriptor instead.
func (*FilterRuleStruct) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{5}
}

func (x *FilterRuleStruct) GetMatchList() []*MatchInfo {
	if x != nil {
		return x.MatchList
	}
	return nil
}

type MatchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilterKey  string   `protobuf:"bytes,1,opt,name=filterKey,proto3" json:"filterKey,omitempty"`   // 过滤字段
	MatchValue []string `protobuf:"bytes,2,rep,name=matchValue,proto3" json:"matchValue,omitempty"` // 匹配值
}

func (x *MatchInfo) Reset() {
	*x = MatchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchInfo) ProtoMessage() {}

func (x *MatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchInfo.ProtoReflect.Descriptor instead.
func (*MatchInfo) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{6}
}

func (x *MatchInfo) GetFilterKey() string {
	if x != nil {
		return x.FilterKey
	}
	return ""
}

func (x *MatchInfo) GetMatchValue() []string {
	if x != nil {
		return x.MatchValue
	}
	return nil
}

type GameDeliveryPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID     string `protobuf:"bytes,1,opt,name=appID,proto3" json:"appID,omitempty"` // 游戏appID
	UserID    int64  `protobuf:"varint,2,opt,name=userID,proto3" json:"userID,omitempty"`
	PackageID int64  `protobuf:"varint,3,opt,name=packageID,proto3" json:"packageID,omitempty"`
	BillNo    string `protobuf:"bytes,4,opt,name=billNo,proto3" json:"billNo,omitempty"` // 唯一订单 id
	Num       int64  `protobuf:"varint,6,opt,name=num,proto3" json:"num,omitempty"`      // 发几个礼包,默认一个
}

func (x *GameDeliveryPackageReq) Reset() {
	*x = GameDeliveryPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageReq) ProtoMessage() {}

func (x *GameDeliveryPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageReq.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{7}
}

func (x *GameDeliveryPackageReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *GameDeliveryPackageReq) GetPackageID() int64 {
	if x != nil {
		return x.PackageID
	}
	return 0
}

func (x *GameDeliveryPackageReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type GameDeliveryPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrMsg string `protobuf:"bytes,1,opt,name=errMsg,proto3" json:"errMsg,omitempty"`
}

func (x *GameDeliveryPackageRsp) Reset() {
	*x = GameDeliveryPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageRsp) ProtoMessage() {}

func (x *GameDeliveryPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_event_platform_event_platform_event_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageRsp.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_event_platform_event_platform_event_proto_rawDescGZIP(), []int{8}
}

func (x *GameDeliveryPackageRsp) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

var File_pb_event_platform_event_platform_event_proto protoreflect.FileDescriptor

var file_pb_event_platform_event_platform_event_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x70, 0x62, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x02, 0x0a, 0x13,
	0x54, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e,
	0x54, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x15, 0x0a, 0x13, 0x54, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0xaf, 0x01, 0x0a, 0x0d, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x0d, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x74, 0x4d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x22, 0xd3,
	0x01, 0x0a, 0x13, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x39, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x37, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0a,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x44, 0x22, 0x42, 0x0a, 0x10, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x75,
	0x6c, 0x65, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x49, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x16, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x69,
	0x6c, 0x6c, 0x4e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c,
	0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x22, 0x30, 0x0a, 0x16, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x32, 0xe0, 0x01, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x12, 0x1a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x6d, 0x65, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x0a, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x14, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x14,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x13, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x42, 0x4c, 0x5a, 0x4a, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_event_platform_event_platform_event_proto_rawDescOnce sync.Once
	file_pb_event_platform_event_platform_event_proto_rawDescData = file_pb_event_platform_event_platform_event_proto_rawDesc
)

func file_pb_event_platform_event_platform_event_proto_rawDescGZIP() []byte {
	file_pb_event_platform_event_platform_event_proto_rawDescOnce.Do(func() {
		file_pb_event_platform_event_platform_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_event_platform_event_platform_event_proto_rawDescData)
	})
	return file_pb_event_platform_event_platform_event_proto_rawDescData
}

var file_pb_event_platform_event_platform_event_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_event_platform_event_platform_event_proto_goTypes = []interface{}{
	(*TmePlatformEventReq)(nil),     // 0: event.TmePlatformEventReq
	(*TmePlatformEventRsp)(nil),     // 1: event.TmePlatformEventRsp
	(*ConfigSyncReq)(nil),           // 2: event.ConfigSyncReq
	(*ConfigSyncRsp)(nil),           // 3: event.ConfigSyncRsp
	(*PlatformEventConfig)(nil),     // 4: event.PlatformEventConfig
	(*FilterRuleStruct)(nil),        // 5: event.FilterRuleStruct
	(*MatchInfo)(nil),               // 6: event.matchInfo
	(*GameDeliveryPackageReq)(nil),  // 7: event.GameDeliveryPackageReq
	(*GameDeliveryPackageRsp)(nil),  // 8: event.GameDeliveryPackageRsp
	nil,                             // 9: event.TmePlatformEventReq.MapExtEntry
	(event.TmePlatformEventType)(0), // 10: event.TmePlatformEventType
}
var file_pb_event_platform_event_platform_event_proto_depIdxs = []int32{
	9,  // 0: event.TmePlatformEventReq.mapExt:type_name -> event.TmePlatformEventReq.MapExtEntry
	10, // 1: event.PlatformEventConfig.eventType:type_name -> event.TmePlatformEventType
	5,  // 2: event.PlatformEventConfig.filterRule:type_name -> event.FilterRuleStruct
	6,  // 3: event.FilterRuleStruct.matchList:type_name -> event.matchInfo
	0,  // 4: event.PlatformEvent.Notify:input_type -> event.TmePlatformEventReq
	2,  // 5: event.PlatformEvent.ConfigSync:input_type -> event.ConfigSyncReq
	7,  // 6: event.PlatformEvent.GameDeliveryPackage:input_type -> event.GameDeliveryPackageReq
	1,  // 7: event.PlatformEvent.Notify:output_type -> event.TmePlatformEventRsp
	3,  // 8: event.PlatformEvent.ConfigSync:output_type -> event.ConfigSyncRsp
	8,  // 9: event.PlatformEvent.GameDeliveryPackage:output_type -> event.GameDeliveryPackageRsp
	7,  // [7:10] is the sub-list for method output_type
	4,  // [4:7] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_pb_event_platform_event_platform_event_proto_init() }
func file_pb_event_platform_event_platform_event_proto_init() {
	if File_pb_event_platform_event_platform_event_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_event_platform_event_platform_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmePlatformEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmePlatformEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigSyncReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigSyncRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatformEventConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterRuleStruct); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_event_platform_event_platform_event_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_event_platform_event_platform_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_event_platform_event_platform_event_proto_goTypes,
		DependencyIndexes: file_pb_event_platform_event_platform_event_proto_depIdxs,
		MessageInfos:      file_pb_event_platform_event_platform_event_proto_msgTypes,
	}.Build()
	File_pb_event_platform_event_platform_event_proto = out.File
	file_pb_event_platform_event_platform_event_proto_rawDesc = nil
	file_pb_event_platform_event_platform_event_proto_goTypes = nil
	file_pb_event_platform_event_platform_event_proto_depIdxs = nil
}
