{"swagger": "2.0", "info": {"title": "pb/match_score/match_score.proto", "version": "version not set"}, "tags": [{"name": "MatchScoreSvr"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.MatchScoreSvr/MatchScoreQuery": {"post": {"summary": "匹配分查询接口", "operationId": "MatchScoreSvr_MatchScoreQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameMatchScoreRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameMatchScoreReq"}}], "tags": ["MatchScoreSvr"]}}}, "definitions": {"gameMatchPara": {"type": "object", "properties": {"intimacy": {"type": "number", "format": "float", "description": "亲密度系数", "title": "系数，例如intimacy == 100 表示亲密度的系数为1"}, "ageGap": {"type": "number", "format": "float", "title": "年龄差系数"}, "cityGap": {"type": "number", "format": "float", "title": "城市得分系数"}, "gender": {"type": "number", "format": "float", "title": "性别系数"}, "level": {"type": "number", "format": "float", "title": "用户等级系数"}}}, "gameMatchScore": {"type": "object", "properties": {"openId": {"type": "string", "title": "open_id"}, "score": {"type": "number", "format": "float", "title": "根据公式和请求中的系数算出来的得分"}}}, "gameMatchScoreReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "mainOpenId": {"type": "string", "title": "open id"}, "poolOpenIdList": {"type": "array", "items": {"type": "string"}, "title": "匹配池的openid列表，单次最多49个"}, "matchPara": {"$ref": "#/definitions/gameMatchPara", "title": "计算Score的系数，业务侧提供下，避免两头配置"}}}, "gameMatchScoreRsp": {"type": "object", "properties": {"matchDataList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameMatchScore"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}