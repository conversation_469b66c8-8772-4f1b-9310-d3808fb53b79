// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/cwsx_game_lava_volvano_api/cwsx_game_lava_volvano_api.proto

/*
Package cwsx_game_lava_volvano_api is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package cwsx_game_lava_volvano_api

import (
	"context"
	"io"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_CwsxGameLavaVolvanoApi_QueryStatus_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameLavaVolvanoApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryStatusReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryStatus(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameLavaVolvanoApi_QueryStatus_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameLavaVolvanoApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryStatusReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryStatus(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameLavaVolvanoApi_QueryDetail_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameLavaVolvanoApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryDetailReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryDetail(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameLavaVolvanoApi_QueryDetail_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameLavaVolvanoApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryDetailReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryDetail(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameLavaVolvanoApi_Attend_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameLavaVolvanoApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq AttendReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.Attend(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameLavaVolvanoApi_Attend_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameLavaVolvanoApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq AttendReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.Attend(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameLavaVolvanoApi_Claim_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameLavaVolvanoApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq ClaimReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.Claim(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameLavaVolvanoApi_Claim_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameLavaVolvanoApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq ClaimReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.Claim(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterCwsxGameLavaVolvanoApiHandlerServer registers the http handlers for service CwsxGameLavaVolvanoApi to "mux".
// UnaryRPC     :call CwsxGameLavaVolvanoApiServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterCwsxGameLavaVolvanoApiHandlerFromEndpoint instead.
func RegisterCwsxGameLavaVolvanoApiHandlerServer(ctx context.Context, mux *runtime.ServeMux, server CwsxGameLavaVolvanoApiServer) error {

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_QueryStatus_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/QueryStatus", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/QueryStatus"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameLavaVolvanoApi_QueryStatus_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_QueryStatus_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_QueryDetail_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/QueryDetail", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/QueryDetail"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameLavaVolvanoApi_QueryDetail_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_QueryDetail_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_Attend_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/Attend", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/Attend"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameLavaVolvanoApi_Attend_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_Attend_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_Claim_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/Claim", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/Claim"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameLavaVolvanoApi_Claim_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_Claim_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterCwsxGameLavaVolvanoApiHandlerFromEndpoint is same as RegisterCwsxGameLavaVolvanoApiHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterCwsxGameLavaVolvanoApiHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterCwsxGameLavaVolvanoApiHandler(ctx, mux, conn)
}

// RegisterCwsxGameLavaVolvanoApiHandler registers the http handlers for service CwsxGameLavaVolvanoApi to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterCwsxGameLavaVolvanoApiHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterCwsxGameLavaVolvanoApiHandlerClient(ctx, mux, NewCwsxGameLavaVolvanoApiClient(conn))
}

// RegisterCwsxGameLavaVolvanoApiHandlerClient registers the http handlers for service CwsxGameLavaVolvanoApi
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "CwsxGameLavaVolvanoApiClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "CwsxGameLavaVolvanoApiClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "CwsxGameLavaVolvanoApiClient" to call the correct interceptors.
func RegisterCwsxGameLavaVolvanoApiHandlerClient(ctx context.Context, mux *runtime.ServeMux, client CwsxGameLavaVolvanoApiClient) error {

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_QueryStatus_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/QueryStatus", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/QueryStatus"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameLavaVolvanoApi_QueryStatus_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_QueryStatus_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_QueryDetail_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/QueryDetail", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/QueryDetail"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameLavaVolvanoApi_QueryDetail_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_QueryDetail_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_Attend_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/Attend", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/Attend"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameLavaVolvanoApi_Attend_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_Attend_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameLavaVolvanoApi_Claim_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game.CwsxGameLavaVolvanoApi/Claim", runtime.WithHTTPPathPattern("/cwsx_game.CwsxGameLavaVolvanoApi/Claim"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameLavaVolvanoApi_Claim_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameLavaVolvanoApi_Claim_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_CwsxGameLavaVolvanoApi_QueryStatus_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game.CwsxGameLavaVolvanoApi", "QueryStatus"}, ""))

	pattern_CwsxGameLavaVolvanoApi_QueryDetail_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game.CwsxGameLavaVolvanoApi", "QueryDetail"}, ""))

	pattern_CwsxGameLavaVolvanoApi_Attend_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game.CwsxGameLavaVolvanoApi", "Attend"}, ""))

	pattern_CwsxGameLavaVolvanoApi_Claim_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game.CwsxGameLavaVolvanoApi", "Claim"}, ""))
)

var (
	forward_CwsxGameLavaVolvanoApi_QueryStatus_0 = runtime.ForwardResponseMessage

	forward_CwsxGameLavaVolvanoApi_QueryDetail_0 = runtime.ForwardResponseMessage

	forward_CwsxGameLavaVolvanoApi_Attend_0 = runtime.ForwardResponseMessage

	forward_CwsxGameLavaVolvanoApi_Claim_0 = runtime.ForwardResponseMessage
)
