// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/cwsx_game_lava_volvano_api/cwsx_game_lava_volvano_api.proto

package cwsx_game_lava_volvano_api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CwsxGameLavaVolvanoApi_QueryStatus_FullMethodName = "/cwsx_game.CwsxGameLavaVolvanoApi/QueryStatus"
	CwsxGameLavaVolvanoApi_QueryDetail_FullMethodName = "/cwsx_game.CwsxGameLavaVolvanoApi/QueryDetail"
	CwsxGameLavaVolvanoApi_Attend_FullMethodName      = "/cwsx_game.CwsxGameLavaVolvanoApi/Attend"
	CwsxGameLavaVolvanoApi_Claim_FullMethodName       = "/cwsx_game.CwsxGameLavaVolvanoApi/Claim"
)

// CwsxGameLavaVolvanoApiClient is the client API for CwsxGameLavaVolvanoApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// cwsx 火山熔岩
type CwsxGameLavaVolvanoApiClient interface {
	// 活动状态
	QueryStatus(ctx context.Context, in *QueryStatusReq, opts ...grpc.CallOption) (*QueryStatusRsp, error)
	// 活动详情
	QueryDetail(ctx context.Context, in *QueryDetailReq, opts ...grpc.CallOption) (*QueryDetailRsp, error)
	// 参加活动
	Attend(ctx context.Context, in *AttendReq, opts ...grpc.CallOption) (*AttendRsp, error)
	// 领奖
	Claim(ctx context.Context, in *ClaimReq, opts ...grpc.CallOption) (*ClaimRsp, error)
}

type cwsxGameLavaVolvanoApiClient struct {
	cc grpc.ClientConnInterface
}

func NewCwsxGameLavaVolvanoApiClient(cc grpc.ClientConnInterface) CwsxGameLavaVolvanoApiClient {
	return &cwsxGameLavaVolvanoApiClient{cc}
}

func (c *cwsxGameLavaVolvanoApiClient) QueryStatus(ctx context.Context, in *QueryStatusReq, opts ...grpc.CallOption) (*QueryStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryStatusRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoApi_QueryStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoApiClient) QueryDetail(ctx context.Context, in *QueryDetailReq, opts ...grpc.CallOption) (*QueryDetailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryDetailRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoApi_QueryDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoApiClient) Attend(ctx context.Context, in *AttendReq, opts ...grpc.CallOption) (*AttendRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttendRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoApi_Attend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoApiClient) Claim(ctx context.Context, in *ClaimReq, opts ...grpc.CallOption) (*ClaimRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClaimRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoApi_Claim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CwsxGameLavaVolvanoApiServer is the server API for CwsxGameLavaVolvanoApi service.
// All implementations should embed UnimplementedCwsxGameLavaVolvanoApiServer
// for forward compatibility
//
// cwsx 火山熔岩
type CwsxGameLavaVolvanoApiServer interface {
	// 活动状态
	QueryStatus(context.Context, *QueryStatusReq) (*QueryStatusRsp, error)
	// 活动详情
	QueryDetail(context.Context, *QueryDetailReq) (*QueryDetailRsp, error)
	// 参加活动
	Attend(context.Context, *AttendReq) (*AttendRsp, error)
	// 领奖
	Claim(context.Context, *ClaimReq) (*ClaimRsp, error)
}

// UnimplementedCwsxGameLavaVolvanoApiServer should be embedded to have forward compatible implementations.
type UnimplementedCwsxGameLavaVolvanoApiServer struct {
}

func (UnimplementedCwsxGameLavaVolvanoApiServer) QueryStatus(context.Context, *QueryStatusReq) (*QueryStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStatus not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoApiServer) QueryDetail(context.Context, *QueryDetailReq) (*QueryDetailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryDetail not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoApiServer) Attend(context.Context, *AttendReq) (*AttendRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Attend not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoApiServer) Claim(context.Context, *ClaimReq) (*ClaimRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Claim not implemented")
}

// UnsafeCwsxGameLavaVolvanoApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CwsxGameLavaVolvanoApiServer will
// result in compilation errors.
type UnsafeCwsxGameLavaVolvanoApiServer interface {
	mustEmbedUnimplementedCwsxGameLavaVolvanoApiServer()
}

func RegisterCwsxGameLavaVolvanoApiServer(s grpc.ServiceRegistrar, srv CwsxGameLavaVolvanoApiServer) {
	s.RegisterService(&CwsxGameLavaVolvanoApi_ServiceDesc, srv)
}

func _CwsxGameLavaVolvanoApi_QueryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoApiServer).QueryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoApi_QueryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoApiServer).QueryStatus(ctx, req.(*QueryStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoApi_QueryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoApiServer).QueryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoApi_QueryDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoApiServer).QueryDetail(ctx, req.(*QueryDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoApi_Attend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoApiServer).Attend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoApi_Attend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoApiServer).Attend(ctx, req.(*AttendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoApi_Claim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoApiServer).Claim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoApi_Claim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoApiServer).Claim(ctx, req.(*ClaimReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CwsxGameLavaVolvanoApi_ServiceDesc is the grpc.ServiceDesc for CwsxGameLavaVolvanoApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CwsxGameLavaVolvanoApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cwsx_game.CwsxGameLavaVolvanoApi",
	HandlerType: (*CwsxGameLavaVolvanoApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryStatus",
			Handler:    _CwsxGameLavaVolvanoApi_QueryStatus_Handler,
		},
		{
			MethodName: "QueryDetail",
			Handler:    _CwsxGameLavaVolvanoApi_QueryDetail_Handler,
		},
		{
			MethodName: "Attend",
			Handler:    _CwsxGameLavaVolvanoApi_Attend_Handler,
		},
		{
			MethodName: "Claim",
			Handler:    _CwsxGameLavaVolvanoApi_Claim_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/cwsx_game_lava_volvano_api/cwsx_game_lava_volvano_api.proto",
}
