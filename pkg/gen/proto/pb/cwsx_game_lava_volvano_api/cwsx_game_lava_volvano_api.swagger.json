{"swagger": "2.0", "info": {"title": "pb/cwsx_game_lava_volvano_api/cwsx_game_lava_volvano_api.proto", "version": "version not set"}, "tags": [{"name": "CwsxGameLavaVolvanoApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/cwsx_game.CwsxGameLavaVolvanoApi/Attend": {"post": {"summary": "参加活动", "operationId": "CwsxGameLavaVolvanoApi_Attend", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gameAttendRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_gameAttendReq"}}], "tags": ["CwsxGameLavaVolvanoApi"]}}, "/cwsx_game.CwsxGameLavaVolvanoApi/Claim": {"post": {"summary": "领奖", "operationId": "CwsxGameLavaVolvanoApi_Claim", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gameClaimRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_gameClaimReq"}}], "tags": ["CwsxGameLavaVolvanoApi"]}}, "/cwsx_game.CwsxGameLavaVolvanoApi/QueryDetail": {"post": {"summary": "活动详情", "operationId": "CwsxGameLavaVolvanoApi_QueryDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gameQueryDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_gameQueryDetailReq"}}], "tags": ["CwsxGameLavaVolvanoApi"]}}, "/cwsx_game.CwsxGameLavaVolvanoApi/QueryStatus": {"post": {"summary": "活动状态", "operationId": "CwsxGameLavaVolvanoApi_QueryStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gameQueryStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_gameQueryStatusReq"}}], "tags": ["CwsxGameLavaVolvanoApi"]}}}, "definitions": {"QueryDetailRspWinPlayer": {"type": "object", "properties": {"avatar": {"type": "string", "title": "头像"}, "nick": {"type": "string", "title": "姓名"}, "openId": {"type": "string", "title": "openId"}, "rewardNum": {"type": "integer", "format": "int64", "title": "奖励数量"}}}, "cwsx_gameAttendReq": {"type": "object"}, "cwsx_gameAttendRsp": {"type": "object", "properties": {"self": {"$ref": "#/definitions/cwsx_gamePlayer", "title": "用户自己"}, "attendPlayers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/cwsx_gamePlayer"}, "title": "参与玩家"}, "totalGoldens": {"type": "integer", "format": "int64", "title": "总金币数"}}}, "cwsx_gameClaimReq": {"type": "object", "properties": {"device": {"$ref": "#/definitions/deviceDevice"}}}, "cwsx_gameClaimRsp": {"type": "object", "properties": {"totalGoldens": {"type": "integer", "format": "int64", "title": "总金币数"}, "rewardNum": {"type": "integer", "format": "int64", "title": "奖励数量"}, "successPlayers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/cwsx_gamePlayer"}, "title": "成功用户"}, "propsIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "礼包奖励Id"}}}, "cwsx_gameDanInfo": {"type": "object", "properties": {"dan": {"type": "integer", "format": "int32"}, "cd": {"type": "integer", "format": "int32"}}}, "cwsx_gameFrame": {"type": "object", "properties": {"cwsx_plg_1": {"type": "string"}, "cwsx_plg_2": {"type": "string"}}}, "cwsx_gamePlayer": {"type": "object", "properties": {"avatar": {"type": "string"}, "nick": {"type": "string"}, "openId": {"type": "string"}, "frame": {"$ref": "#/definitions/cwsx_gameFrame", "title": "头像框"}, "danInfo": {"$ref": "#/definitions/cwsx_gameDanInfo", "title": "玩家段位相关"}}}, "cwsx_gameQueryDetailReq": {"type": "object"}, "cwsx_gameQueryDetailRsp": {"type": "object", "properties": {"round": {"type": "string", "title": "当前轮次"}, "leftTime": {"type": "integer", "format": "int64", "title": "剩余时间"}, "totalGoldens": {"type": "integer", "format": "int64", "title": "总金币数"}, "stauts": {"type": "integer", "format": "int64", "title": "活动状态"}, "currentPlayers": {"type": "integer", "format": "int64", "description": "当前层人数", "title": "players"}, "totalPlayers": {"type": "integer", "format": "int64", "title": "总人数"}, "userCompleteLevel": {"type": "integer", "format": "int64", "description": "已完成关卡数", "title": "levels"}, "maxLevel": {"type": "integer", "format": "int64", "title": "总关卡数"}, "self": {"$ref": "#/definitions/cwsx_gamePlayer", "description": "用户自己", "title": "players"}, "successPlayers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/cwsx_gamePlayer"}, "title": "成功用户"}, "failPlayers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/cwsx_gamePlayer"}, "title": "失败用户"}, "latestResult": {"type": "integer", "format": "int64", "description": "最近一次闯关结果 0闯关失败 1闯关成功", "title": "result"}, "rewardId": {"type": "string", "format": "int64", "title": "奖励礼包Id"}, "rewardNum": {"type": "integer", "format": "int64", "title": "奖励数量"}, "propsIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "礼包奖励Id"}, "historyWins": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QueryDetailRspWinPlayer"}, "title": "历史获奖玩家"}}}, "cwsx_gameQueryStatusReq": {"type": "object"}, "cwsx_gameQueryStatusRsp": {"type": "object", "properties": {"round": {"type": "string", "title": "当前轮次"}, "status": {"type": "integer", "format": "int64", "title": "活动状态"}, "leftTime": {"type": "integer", "format": "int64", "title": "剩余时间"}, "completeLevel": {"type": "integer", "format": "int64", "title": "已完成"}, "maxLevel": {"type": "integer", "format": "int64", "title": "总关卡数"}, "nextRoundInterval": {"type": "integer", "format": "int64", "title": "下一场活动间隔"}, "noticeInterval": {"type": "integer", "format": "int64", "title": "提醒次数"}}}, "deviceDevice": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台信息 kugou、qqmusic、qmkege、kuwo、lanren"}, "version": {"type": "string", "title": "客户端版本 1.2.3"}, "os": {"type": "string", "title": "系统 android、ios"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}