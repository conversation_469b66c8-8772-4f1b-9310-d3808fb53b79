// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_lava_volvano_api/common.proto

package cwsx_game_lava_volvano_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActivityStatus int32

const (
	ActivityStatus_ActivityStatusDefault       ActivityStatus = 0 // 默认 当前没有活动
	ActivityStatus_ActivityStatusOngoing       ActivityStatus = 1 // 未参加
	ActivityStatus_ActivityStatusParticipated  ActivityStatus = 2 // 参加进行中
	ActivityStatus_ActivityStatusFinished      ActivityStatus = 3 // 已完成 已领奖
	ActivityStatus_ActivityStatusFailed        ActivityStatus = 4 // 失败惩罚中
	ActivityStatus_ActivityStatusFinishNotView ActivityStatus = 5 // 已完成 未领奖
	ActivityStatus_ActivityStatusFailEnd       ActivityStatus = 6 // 已失败 活动结束
)

// Enum value maps for ActivityStatus.
var (
	ActivityStatus_name = map[int32]string{
		0: "ActivityStatusDefault",
		1: "ActivityStatusOngoing",
		2: "ActivityStatusParticipated",
		3: "ActivityStatusFinished",
		4: "ActivityStatusFailed",
		5: "ActivityStatusFinishNotView",
		6: "ActivityStatusFailEnd",
	}
	ActivityStatus_value = map[string]int32{
		"ActivityStatusDefault":       0,
		"ActivityStatusOngoing":       1,
		"ActivityStatusParticipated":  2,
		"ActivityStatusFinished":      3,
		"ActivityStatusFailed":        4,
		"ActivityStatusFinishNotView": 5,
		"ActivityStatusFailEnd":       6,
	}
)

func (x ActivityStatus) Enum() *ActivityStatus {
	p := new(ActivityStatus)
	*p = x
	return p
}

func (x ActivityStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_enumTypes[0].Descriptor()
}

func (ActivityStatus) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_lava_volvano_api_common_proto_enumTypes[0]
}

func (x ActivityStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityStatus.Descriptor instead.
func (ActivityStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP(), []int{0}
}

// 闯关进度
type StageProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurStage   uint32 `protobuf:"varint,1,opt,name=curStage,proto3" json:"curStage,omitempty"`     // 当前关卡数
	CreateTime int64  `protobuf:"varint,2,opt,name=createTime,proto3" json:"createTime,omitempty"` // 闯关结算时间
}

func (x *StageProgress) Reset() {
	*x = StageProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageProgress) ProtoMessage() {}

func (x *StageProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageProgress.ProtoReflect.Descriptor instead.
func (*StageProgress) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP(), []int{0}
}

func (x *StageProgress) GetCurStage() uint32 {
	if x != nil {
		return x.CurStage
	}
	return 0
}

func (x *StageProgress) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// CKV+
type UserProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// user
	Status        uint32           `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`              // 活动状态
	StartStage    uint32           `protobuf:"varint,2,opt,name=startStage,proto3" json:"startStage,omitempty"`      // 开始活动时的关卡数
	StageProgress []*StageProgress `protobuf:"bytes,3,rep,name=stageProgress,proto3" json:"stageProgress,omitempty"` // 闯关成功进度
	RoundId       string           `protobuf:"bytes,4,opt,name=roundId,proto3" json:"roundId,omitempty"`             // 轮次Id {activityId}_{dailyStart}
	StartTime     int64            `protobuf:"varint,5,opt,name=startTime,proto3" json:"startTime,omitempty"`        // 开始时间
	// config
	MaxLevel      uint32  `protobuf:"varint,6,opt,name=maxLevel,proto3" json:"maxLevel,omitempty"`            // 关卡数
	MaxPlayer     uint32  `protobuf:"varint,7,opt,name=maxPlayer,proto3" json:"maxPlayer,omitempty"`          // 最大参与人数
	RewardId      int64   `protobuf:"varint,8,opt,name=rewardId,proto3" json:"rewardId,omitempty"`            // 奖励礼包Id
	PropsIds      []int64 `protobuf:"varint,9,rep,packed,name=propsIds,proto3" json:"propsIds,omitempty"`     // 礼包奖励Id
	RewardSum     uint32  `protobuf:"varint,10,opt,name=rewardSum,proto3" json:"rewardSum,omitempty"`         // 奖励总数
	FailPunish    int64   `protobuf:"varint,11,opt,name=failPunish,proto3" json:"failPunish,omitempty"`       // 失败惩罚时间
	RoundDuration int64   `protobuf:"varint,12,opt,name=roundDuration,proto3" json:"roundDuration,omitempty"` // 参与后活动持续时间
	// players
	SuccessPlayers []string `protobuf:"bytes,13,rep,name=successPlayers,proto3" json:"successPlayers,omitempty"` // 成功玩家
	FailPlayers    []string `protobuf:"bytes,14,rep,name=failPlayers,proto3" json:"failPlayers,omitempty"`       // 失败玩家
	// result
	SettleTime     int64  `protobuf:"varint,15,opt,name=settleTime,proto3" json:"settleTime,omitempty"`         // 结算时间
	Rank           uint32 `protobuf:"varint,16,opt,name=rank,proto3" json:"rank,omitempty"`                     // 结算排名
	RewardNum      uint32 `protobuf:"varint,17,opt,name=rewardNum,proto3" json:"rewardNum,omitempty"`           // 奖励数量
	NoticeInterval uint32 `protobuf:"varint,18,opt,name=noticeInterval,proto3" json:"noticeInterval,omitempty"` // 提醒间隔
	// 主键信息
	MPlayerIds map[string]int64 `protobuf:"bytes,19,rep,name=mPlayerIds,proto3" json:"mPlayerIds,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	SelfId     int64            `protobuf:"varint,20,opt,name=selfId,proto3" json:"selfId,omitempty"`
}

func (x *UserProgress) Reset() {
	*x = UserProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProgress) ProtoMessage() {}

func (x *UserProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProgress.ProtoReflect.Descriptor instead.
func (*UserProgress) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP(), []int{1}
}

func (x *UserProgress) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserProgress) GetStartStage() uint32 {
	if x != nil {
		return x.StartStage
	}
	return 0
}

func (x *UserProgress) GetStageProgress() []*StageProgress {
	if x != nil {
		return x.StageProgress
	}
	return nil
}

func (x *UserProgress) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *UserProgress) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *UserProgress) GetMaxLevel() uint32 {
	if x != nil {
		return x.MaxLevel
	}
	return 0
}

func (x *UserProgress) GetMaxPlayer() uint32 {
	if x != nil {
		return x.MaxPlayer
	}
	return 0
}

func (x *UserProgress) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *UserProgress) GetPropsIds() []int64 {
	if x != nil {
		return x.PropsIds
	}
	return nil
}

func (x *UserProgress) GetRewardSum() uint32 {
	if x != nil {
		return x.RewardSum
	}
	return 0
}

func (x *UserProgress) GetFailPunish() int64 {
	if x != nil {
		return x.FailPunish
	}
	return 0
}

func (x *UserProgress) GetRoundDuration() int64 {
	if x != nil {
		return x.RoundDuration
	}
	return 0
}

func (x *UserProgress) GetSuccessPlayers() []string {
	if x != nil {
		return x.SuccessPlayers
	}
	return nil
}

func (x *UserProgress) GetFailPlayers() []string {
	if x != nil {
		return x.FailPlayers
	}
	return nil
}

func (x *UserProgress) GetSettleTime() int64 {
	if x != nil {
		return x.SettleTime
	}
	return 0
}

func (x *UserProgress) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *UserProgress) GetRewardNum() uint32 {
	if x != nil {
		return x.RewardNum
	}
	return 0
}

func (x *UserProgress) GetNoticeInterval() uint32 {
	if x != nil {
		return x.NoticeInterval
	}
	return 0
}

func (x *UserProgress) GetMPlayerIds() map[string]int64 {
	if x != nil {
		return x.MPlayerIds
	}
	return nil
}

func (x *UserProgress) GetSelfId() int64 {
	if x != nil {
		return x.SelfId
	}
	return 0
}

type CwsxGameLavaVolvanoConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                // 活动id
	MaxLevel          uint32   `protobuf:"varint,2,opt,name=maxLevel,proto3" json:"maxLevel,omitempty"`                    // 最大关卡数
	MaxPlayer         uint32   `protobuf:"varint,3,opt,name=maxPlayer,proto3" json:"maxPlayer,omitempty"`                  // 最大参与人数
	FailPunish        int64    `protobuf:"varint,4,opt,name=failPunish,proto3" json:"failPunish,omitempty"`                // 失败惩罚时间
	RoundDuration     int64    `protobuf:"varint,5,opt,name=roundDuration,proto3" json:"roundDuration,omitempty"`          // 参与后活动持续时间
	StartTime         int64    `protobuf:"varint,6,opt,name=startTime,proto3" json:"startTime,omitempty"`                  // 开始时间
	EndTime           int64    `protobuf:"varint,7,opt,name=endTime,proto3" json:"endTime,omitempty"`                      // 结束时间
	DailyStart        string   `protobuf:"bytes,8,opt,name=dailyStart,proto3" json:"dailyStart,omitempty"`                 // 每日开始时间
	DailyEnd          string   `protobuf:"bytes,9,opt,name=dailyEnd,proto3" json:"dailyEnd,omitempty"`                     // 每日结束时间
	RewardId          int64    `protobuf:"varint,10,opt,name=rewardId,proto3" json:"rewardId,omitempty"`                   // 奖励礼包Id
	RewardSum         uint32   `protobuf:"varint,11,opt,name=rewardSum,proto3" json:"rewardSum,omitempty"`                 // 奖励总数
	HistoryJoinPlayer uint32   `protobuf:"varint,12,opt,name=historyJoinPlayer,proto3" json:"historyJoinPlayer,omitempty"` // 历史用户
	WhiteOpenIds      []string `protobuf:"bytes,13,rep,name=whiteOpenIds,proto3" json:"whiteOpenIds,omitempty"`            // 白名单
	PropsIds          []int64  `protobuf:"varint,14,rep,packed,name=propsIds,proto3" json:"propsIds,omitempty"`            // 礼包奖励Id
	AllowStage        uint32   `protobuf:"varint,15,opt,name=allowStage,proto3" json:"allowStage,omitempty"`               // 允许显示关卡
	NoticeInterval    uint32   `protobuf:"varint,16,opt,name=noticeInterval,proto3" json:"noticeInterval,omitempty"`       // 提醒间隔
}

func (x *CwsxGameLavaVolvanoConfig) Reset() {
	*x = CwsxGameLavaVolvanoConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CwsxGameLavaVolvanoConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CwsxGameLavaVolvanoConfig) ProtoMessage() {}

func (x *CwsxGameLavaVolvanoConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CwsxGameLavaVolvanoConfig.ProtoReflect.Descriptor instead.
func (*CwsxGameLavaVolvanoConfig) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP(), []int{2}
}

func (x *CwsxGameLavaVolvanoConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetMaxLevel() uint32 {
	if x != nil {
		return x.MaxLevel
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetMaxPlayer() uint32 {
	if x != nil {
		return x.MaxPlayer
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetFailPunish() int64 {
	if x != nil {
		return x.FailPunish
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetRoundDuration() int64 {
	if x != nil {
		return x.RoundDuration
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetDailyStart() string {
	if x != nil {
		return x.DailyStart
	}
	return ""
}

func (x *CwsxGameLavaVolvanoConfig) GetDailyEnd() string {
	if x != nil {
		return x.DailyEnd
	}
	return ""
}

func (x *CwsxGameLavaVolvanoConfig) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetRewardSum() uint32 {
	if x != nil {
		return x.RewardSum
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetHistoryJoinPlayer() uint32 {
	if x != nil {
		return x.HistoryJoinPlayer
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetWhiteOpenIds() []string {
	if x != nil {
		return x.WhiteOpenIds
	}
	return nil
}

func (x *CwsxGameLavaVolvanoConfig) GetPropsIds() []int64 {
	if x != nil {
		return x.PropsIds
	}
	return nil
}

func (x *CwsxGameLavaVolvanoConfig) GetAllowStage() uint32 {
	if x != nil {
		return x.AllowStage
	}
	return 0
}

func (x *CwsxGameLavaVolvanoConfig) GetNoticeInterval() uint32 {
	if x != nil {
		return x.NoticeInterval
	}
	return 0
}

type ClaimRecords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*ClaimRecords_Record `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *ClaimRecords) Reset() {
	*x = ClaimRecords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimRecords) ProtoMessage() {}

func (x *ClaimRecords) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimRecords.ProtoReflect.Descriptor instead.
func (*ClaimRecords) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP(), []int{3}
}

func (x *ClaimRecords) GetRecords() []*ClaimRecords_Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ClaimRecords_Record struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatar    string `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Nick      string `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick,omitempty"`
	OpenId    string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	RewardNum uint32 `protobuf:"varint,4,opt,name=rewardNum,proto3" json:"rewardNum,omitempty"`
	UTs       uint32 `protobuf:"varint,5,opt,name=uTs,proto3" json:"uTs,omitempty"`
}

func (x *ClaimRecords_Record) Reset() {
	*x = ClaimRecords_Record{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimRecords_Record) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimRecords_Record) ProtoMessage() {}

func (x *ClaimRecords_Record) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimRecords_Record.ProtoReflect.Descriptor instead.
func (*ClaimRecords_Record) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ClaimRecords_Record) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ClaimRecords_Record) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *ClaimRecords_Record) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ClaimRecords_Record) GetRewardNum() uint32 {
	if x != nil {
		return x.RewardNum
	}
	return 0
}

func (x *ClaimRecords_Record) GetUTs() uint32 {
	if x != nil {
		return x.UTs
	}
	return 0
}

var File_pb_cwsx_game_lava_volvano_api_common_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_lava_volvano_api_common_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c,
	0x61, 0x76, 0x61, 0x5f, 0x76, 0x6f, 0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x5f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x4b, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0xf8, 0x05, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0d,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x53, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x53, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x50, 0x75, 0x6e,
	0x69, 0x73, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x50,
	0x75, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12,
	0x47, 0x0a, 0x0a, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x13, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x4d, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x6d, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x6c, 0x66,
	0x49, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6c, 0x66, 0x49, 0x64,
	0x1a, 0x3d, 0x0a, 0x0f, 0x4d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x8f, 0x04, 0x0a, 0x19, 0x43, 0x77, 0x73, 0x78, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x61, 0x76, 0x61,
	0x56, 0x6f, 0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x61, 0x78,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x50,
	0x75, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x66, 0x61, 0x69,
	0x6c, 0x50, 0x75, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x45, 0x6e,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x45, 0x6e,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x75, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x11, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4a,
	0x6f, 0x69, 0x6e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x77, 0x68, 0x69, 0x74, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x22, 0xc6, 0x01, 0x0a, 0x0c, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x38, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x1a, 0x7c, 0x0a, 0x06,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x69,
	0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x54, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x75, 0x54, 0x73, 0x2a, 0xd8, 0x01, 0x0a, 0x0e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a,
	0x15, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x6e, 0x67, 0x6f, 0x69, 0x6e,
	0x67, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65,
	0x64, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x03, 0x12,
	0x18, 0x0a, 0x14, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73,
	0x68, 0x4e, 0x6f, 0x74, 0x56, 0x69, 0x65, 0x77, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x61, 0x69, 0x6c,
	0x45, 0x6e, 0x64, 0x10, 0x06, 0x42, 0x52, 0x5a, 0x50, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x76, 0x61, 0x5f, 0x76, 0x6f,
	0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x5f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescData = file_pb_cwsx_game_lava_volvano_api_common_proto_rawDesc
)

func file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescData)
	})
	return file_pb_cwsx_game_lava_volvano_api_common_proto_rawDescData
}

var file_pb_cwsx_game_lava_volvano_api_common_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_cwsx_game_lava_volvano_api_common_proto_goTypes = []interface{}{
	(ActivityStatus)(0),               // 0: cwsx_game.ActivityStatus
	(*StageProgress)(nil),             // 1: cwsx_game.StageProgress
	(*UserProgress)(nil),              // 2: cwsx_game.UserProgress
	(*CwsxGameLavaVolvanoConfig)(nil), // 3: cwsx_game.CwsxGameLavaVolvanoConfig
	(*ClaimRecords)(nil),              // 4: cwsx_game.ClaimRecords
	nil,                               // 5: cwsx_game.UserProgress.MPlayerIdsEntry
	(*ClaimRecords_Record)(nil),       // 6: cwsx_game.ClaimRecords.Record
}
var file_pb_cwsx_game_lava_volvano_api_common_proto_depIdxs = []int32{
	1, // 0: cwsx_game.UserProgress.stageProgress:type_name -> cwsx_game.StageProgress
	5, // 1: cwsx_game.UserProgress.mPlayerIds:type_name -> cwsx_game.UserProgress.MPlayerIdsEntry
	6, // 2: cwsx_game.ClaimRecords.records:type_name -> cwsx_game.ClaimRecords.Record
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_lava_volvano_api_common_proto_init() }
func file_pb_cwsx_game_lava_volvano_api_common_proto_init() {
	if File_pb_cwsx_game_lava_volvano_api_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CwsxGameLavaVolvanoConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimRecords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimRecords_Record); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_lava_volvano_api_common_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_cwsx_game_lava_volvano_api_common_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_lava_volvano_api_common_proto_depIdxs,
		EnumInfos:         file_pb_cwsx_game_lava_volvano_api_common_proto_enumTypes,
		MessageInfos:      file_pb_cwsx_game_lava_volvano_api_common_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_lava_volvano_api_common_proto = out.File
	file_pb_cwsx_game_lava_volvano_api_common_proto_rawDesc = nil
	file_pb_cwsx_game_lava_volvano_api_common_proto_goTypes = nil
	file_pb_cwsx_game_lava_volvano_api_common_proto_depIdxs = nil
}
