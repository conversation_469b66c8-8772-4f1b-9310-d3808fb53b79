{"swagger": "2.0", "info": {"title": "pb/order/order.proto", "version": "version not set"}, "tags": [{"name": "Order"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.Order/Place": {"post": {"summary": "下单", "operationId": "Order_Place", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOrderPlaceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOrderPlaceReq"}}], "tags": ["Order"]}}, "/component.game.Order/Query": {"post": {"summary": "查询", "operationId": "Order_Query", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOrderQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOrderQueryReq"}}], "tags": ["Order"]}}, "/component.game.Order/Turn": {"post": {"summary": "变更状态", "operationId": "Order_Turn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOrderTurnRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOrderTurnReq"}}], "tags": ["Order"]}}}, "definitions": {"gameOrderPlaceReq": {"type": "object", "properties": {"appId": {"type": "string"}, "orderId": {"type": "string", "title": "订单 id"}, "openId": {"type": "string"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间 秒 默认 3600"}}}, "gameOrderPlaceRsp": {"type": "object"}, "gameOrderQueryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "orderId": {"type": "string", "title": "订单 id"}}}, "gameOrderQueryRsp": {"type": "object", "properties": {"status": {"$ref": "#/definitions/gameStatusOfOrder"}, "openId": {"type": "string"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据"}}}, "gameOrderTurnReq": {"type": "object", "properties": {"appId": {"type": "string"}, "orderId": {"type": "string", "title": "订单 id"}, "status": {"$ref": "#/definitions/gameStatusOfOrder"}}}, "gameOrderTurnRsp": {"type": "object"}, "gameStatusOfOrder": {"type": "string", "enum": ["Unknown", "Placed", "Completed", "Cancel"], "default": "Unknown", "title": "- Placed: 已下单\n - Completed: 已完成\n - Cancel: 已取消"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}