// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/order/order.proto

package order

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Order_Place_FullMethodName = "/component.game.Order/Place"
	Order_Turn_FullMethodName  = "/component.game.Order/Turn"
	Order_Query_FullMethodName = "/component.game.Order/Query"
)

// OrderClient is the client API for Order service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrderClient interface {
	// 下单
	Place(ctx context.Context, in *OrderPlaceReq, opts ...grpc.CallOption) (*OrderPlaceRsp, error)
	// 变更状态
	Turn(ctx context.Context, in *OrderTurnReq, opts ...grpc.CallOption) (*OrderTurnRsp, error)
	// 查询
	Query(ctx context.Context, in *OrderQueryReq, opts ...grpc.CallOption) (*OrderQueryRsp, error)
}

type orderClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderClient(cc grpc.ClientConnInterface) OrderClient {
	return &orderClient{cc}
}

func (c *orderClient) Place(ctx context.Context, in *OrderPlaceReq, opts ...grpc.CallOption) (*OrderPlaceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderPlaceRsp)
	err := c.cc.Invoke(ctx, Order_Place_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) Turn(ctx context.Context, in *OrderTurnReq, opts ...grpc.CallOption) (*OrderTurnRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderTurnRsp)
	err := c.cc.Invoke(ctx, Order_Turn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) Query(ctx context.Context, in *OrderQueryReq, opts ...grpc.CallOption) (*OrderQueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderQueryRsp)
	err := c.cc.Invoke(ctx, Order_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderServer is the server API for Order service.
// All implementations should embed UnimplementedOrderServer
// for forward compatibility
type OrderServer interface {
	// 下单
	Place(context.Context, *OrderPlaceReq) (*OrderPlaceRsp, error)
	// 变更状态
	Turn(context.Context, *OrderTurnReq) (*OrderTurnRsp, error)
	// 查询
	Query(context.Context, *OrderQueryReq) (*OrderQueryRsp, error)
}

// UnimplementedOrderServer should be embedded to have forward compatible implementations.
type UnimplementedOrderServer struct {
}

func (UnimplementedOrderServer) Place(context.Context, *OrderPlaceReq) (*OrderPlaceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Place not implemented")
}
func (UnimplementedOrderServer) Turn(context.Context, *OrderTurnReq) (*OrderTurnRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Turn not implemented")
}
func (UnimplementedOrderServer) Query(context.Context, *OrderQueryReq) (*OrderQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}

// UnsafeOrderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrderServer will
// result in compilation errors.
type UnsafeOrderServer interface {
	mustEmbedUnimplementedOrderServer()
}

func RegisterOrderServer(s grpc.ServiceRegistrar, srv OrderServer) {
	s.RegisterService(&Order_ServiceDesc, srv)
}

func _Order_Place_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderPlaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).Place(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_Place_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).Place(ctx, req.(*OrderPlaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_Turn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderTurnReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).Turn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_Turn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).Turn(ctx, req.(*OrderTurnReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Order_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).Query(ctx, req.(*OrderQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Order_ServiceDesc is the grpc.ServiceDesc for Order service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Order_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.Order",
	HandlerType: (*OrderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Place",
			Handler:    _Order_Place_Handler,
		},
		{
			MethodName: "Turn",
			Handler:    _Order_Turn_Handler,
		},
		{
			MethodName: "Query",
			Handler:    _Order_Query_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/order/order.proto",
}
