{"swagger": "2.0", "info": {"title": "pb/reward_sender_admin/reward_sender_admin.proto", "version": "version not set"}, "tags": [{"name": "<PERSON>ward<PERSON>ender<PERSON><PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/reward/preSync": {"post": {"summary": "预同步", "operationId": "RewardSenderAdmin_PreSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gamePreSyncRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gamePreSyncReq"}}], "tags": ["<PERSON>ward<PERSON>ender<PERSON><PERSON><PERSON>"]}}, "/reward/syncOneExp": {"post": {"summary": "体验环境同步单条记录", "operationId": "RewardSenderAdmin_SyncOneExp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSyncOneExpRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSyncOneExpReq"}}], "tags": ["<PERSON>ward<PERSON>ender<PERSON><PERSON><PERSON>"]}}, "/reward/syncOneProd": {"post": {"summary": "外网环境同步单条记录", "operationId": "RewardSenderAdmin_SyncOneProd", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSyncOneProdRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSyncOneProdReq"}}], "tags": ["<PERSON>ward<PERSON>ender<PERSON><PERSON><PERSON>"]}}, "/reward/upsert": {"post": {"summary": "插入更新", "operationId": "RewardSenderAdmin_Upsert", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameUpsertRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameUpsertReq"}}], "tags": ["<PERSON>ward<PERSON>ender<PERSON><PERSON><PERSON>"]}}}, "definitions": {"gamePreSyncReq": {"type": "object", "properties": {"rewardId": {"type": "string", "format": "int64", "title": "reward_id 奖励ID"}, "updateUser": {"type": "string", "title": "更新人"}}}, "gamePreSyncRsp": {"type": "object"}, "gameRewardAdmin": {"type": "object", "properties": {"giftId": {"type": "string", "title": "gift_id 奖品ID"}, "giftType": {"type": "integer", "format": "int64", "title": "gift_type 奖品Type 参考GiftType"}, "giftNum": {"type": "string", "format": "int64", "title": "gift_num 发放数量"}, "giftReason": {"type": "string", "title": "gift_reason 发放理由"}, "subGift": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameSubRewardAdmin"}, "title": "SubRewardAdmin 子奖品信息, 如无子奖品，则为主奖品"}, "goodsService": {"type": "string", "title": "goods_service 回调服务"}}}, "gameRewardConfigAdmin": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameRewardAdmin"}}, "rewardName": {"type": "string"}, "gameAppid": {"type": "string"}, "platId": {"type": "string", "format": "uint64"}, "msgContent": {"type": "string"}, "msgLinkName": {"type": "string"}, "msgLinkUrl": {"type": "string"}}}, "gameSubRewardAdmin": {"type": "object", "properties": {"subGiftId": {"type": "string", "title": "sub_gift_id 子奖品ID"}, "subGiftType": {"type": "string", "title": "sub_gift_type 子奖品Type"}, "subGiftNum": {"type": "string", "format": "int64", "title": "sub_gift_num 子奖品数量"}, "subGiftName": {"type": "string", "title": "sub_gift_name 子奖品名称"}, "subGiftLogo": {"type": "string", "title": "sub_gift_logo 子奖品logo"}, "subGiftModifyName": {"type": "string", "title": "sub_gift_modify_name 修改后子奖品名称"}, "subGiftModifyLogo": {"type": "string", "title": "sub_gift_modify_logo 修改后子奖品logo"}, "subGiftUnitPrice": {"type": "integer", "format": "int64", "title": "sub_gift_unit_price 子奖品单价"}, "subGiftTypeName": {"type": "string", "title": "sub_gift_type_name 子奖品类型名"}, "subGiftExpireType": {"type": "integer", "format": "int64", "title": "expire_type 过期类型, 1相对过期, 2绝对过期, 0表示不过期"}, "subGiftExpireTime": {"type": "integer", "format": "int64", "title": "expire_sec 过期时间(s), 相对过期是x秒后过期, 绝对时间是过期时间戳"}}}, "gameSyncOneExpReq": {"type": "object", "properties": {"rewardId": {"type": "string", "format": "int64", "title": "reward_id 奖励ID"}, "updateUser": {"type": "string", "title": "更新人"}}}, "gameSyncOneExpRsp": {"type": "object"}, "gameSyncOneProdReq": {"type": "object", "properties": {"rewardId": {"type": "string", "format": "int64", "title": "reward_id 奖励ID"}, "updateUser": {"type": "string", "title": "更新人"}}}, "gameSyncOneProdRsp": {"type": "object"}, "gameUpsertReq": {"type": "object", "properties": {"rewardId": {"type": "string", "format": "int64", "title": "reward_id 奖励ID"}, "type": {"type": "integer", "format": "int64", "title": "type type:1 插入 type:2 更新"}, "config": {"$ref": "#/definitions/gameRewardConfigAdmin", "title": "RewardConfigAdmin 配置config"}, "updateUser": {"type": "string", "title": "更新人"}}}, "gameUpsertRsp": {"type": "object", "properties": {"rewardId": {"type": "string", "format": "int64", "title": "reward_id 奖励ID"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}