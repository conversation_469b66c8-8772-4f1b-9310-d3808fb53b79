// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/txmsg/txmsg.proto

package txmsg

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BeginRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	MessageType string `protobuf:"bytes,2,opt,name=messageType,proto3" json:"messageType,omitempty"`
	Data        []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BeginRequest) Reset() {
	*x = BeginRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginRequest) ProtoMessage() {}

func (x *BeginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginRequest.ProtoReflect.Descriptor instead.
func (*BeginRequest) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{0}
}

func (x *BeginRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BeginRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *BeginRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type BeginResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId string `protobuf:"bytes,1,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
}

func (x *BeginResponse) Reset() {
	*x = BeginResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginResponse) ProtoMessage() {}

func (x *BeginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginResponse.ProtoReflect.Descriptor instead.
func (*BeginResponse) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{1}
}

func (x *BeginResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type CommitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId            string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	TransactionId    string `protobuf:"bytes,2,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	InvokeSync       bool   `protobuf:"varint,3,opt,name=invokeSync,proto3" json:"invokeSync,omitempty"`
	AsyncWaitingTime int64  `protobuf:"varint,4,opt,name=asyncWaitingTime,proto3" json:"asyncWaitingTime,omitempty"` // 毫秒
}

func (x *CommitRequest) Reset() {
	*x = CommitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitRequest) ProtoMessage() {}

func (x *CommitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitRequest.ProtoReflect.Descriptor instead.
func (*CommitRequest) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{2}
}

func (x *CommitRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CommitRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *CommitRequest) GetInvokeSync() bool {
	if x != nil {
		return x.InvokeSync
	}
	return false
}

func (x *CommitRequest) GetAsyncWaitingTime() int64 {
	if x != nil {
		return x.AsyncWaitingTime
	}
	return 0
}

type CommitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommitResponse) Reset() {
	*x = CommitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitResponse) ProtoMessage() {}

func (x *CommitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitResponse.ProtoReflect.Descriptor instead.
func (*CommitResponse) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{3}
}

type RollbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	TransactionId string `protobuf:"bytes,2,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
}

func (x *RollbackRequest) Reset() {
	*x = RollbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackRequest) ProtoMessage() {}

func (x *RollbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackRequest.ProtoReflect.Descriptor instead.
func (*RollbackRequest) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{4}
}

func (x *RollbackRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RollbackRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type RollbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RollbackResponse) Reset() {
	*x = RollbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackResponse) ProtoMessage() {}

func (x *RollbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackResponse.ProtoReflect.Descriptor instead.
func (*RollbackResponse) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{5}
}

type TriggerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	MessageType   string `protobuf:"bytes,2,opt,name=messageType,proto3" json:"messageType,omitempty"`
	TransactionId string `protobuf:"bytes,3,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Data          []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TriggerRequest) Reset() {
	*x = TriggerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerRequest) ProtoMessage() {}

func (x *TriggerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerRequest.ProtoReflect.Descriptor instead.
func (*TriggerRequest) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{6}
}

func (x *TriggerRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TriggerRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *TriggerRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *TriggerRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type TriggerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TriggerResponse) Reset() {
	*x = TriggerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TriggerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TriggerResponse) ProtoMessage() {}

func (x *TriggerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TriggerResponse.ProtoReflect.Descriptor instead.
func (*TriggerResponse) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{7}
}

type TxQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	MessageType   string `protobuf:"bytes,2,opt,name=messageType,proto3" json:"messageType,omitempty"`
	TransactionId string `protobuf:"bytes,3,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Data          []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TxQueryRequest) Reset() {
	*x = TxQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxQueryRequest) ProtoMessage() {}

func (x *TxQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxQueryRequest.ProtoReflect.Descriptor instead.
func (*TxQueryRequest) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{8}
}

func (x *TxQueryRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TxQueryRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *TxQueryRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *TxQueryRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type TxQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Committed bool `protobuf:"varint,1,opt,name=committed,proto3" json:"committed,omitempty"`
	Rollback  bool `protobuf:"varint,2,opt,name=rollback,proto3" json:"rollback,omitempty"`
}

func (x *TxQueryResponse) Reset() {
	*x = TxQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxQueryResponse) ProtoMessage() {}

func (x *TxQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxQueryResponse.ProtoReflect.Descriptor instead.
func (*TxQueryResponse) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{9}
}

func (x *TxQueryResponse) GetCommitted() bool {
	if x != nil {
		return x.Committed
	}
	return false
}

func (x *TxQueryResponse) GetRollback() bool {
	if x != nil {
		return x.Rollback
	}
	return false
}

type TxProcessRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	MessageType   string `protobuf:"bytes,2,opt,name=messageType,proto3" json:"messageType,omitempty"`
	TransactionId string `protobuf:"bytes,3,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Data          []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TxProcessRequest) Reset() {
	*x = TxProcessRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxProcessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxProcessRequest) ProtoMessage() {}

func (x *TxProcessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxProcessRequest.ProtoReflect.Descriptor instead.
func (*TxProcessRequest) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{10}
}

func (x *TxProcessRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TxProcessRequest) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *TxProcessRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *TxProcessRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type TxProcessResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TxProcessResponse) Reset() {
	*x = TxProcessResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_txmsg_txmsg_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxProcessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxProcessResponse) ProtoMessage() {}

func (x *TxProcessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_txmsg_txmsg_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TxProcessResponse.ProtoReflect.Descriptor instead.
func (*TxProcessResponse) Descriptor() ([]byte, []int) {
	return file_pb_txmsg_txmsg_proto_rawDescGZIP(), []int{11}
}

var File_pb_txmsg_txmsg_proto protoreflect.FileDescriptor

var file_pb_txmsg_txmsg_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x2f, 0x74, 0x78, 0x6d, 0x73, 0x67, 0x2f, 0x74, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x78, 0x6d, 0x73, 0x67, 0x22, 0x5a, 0x0a,
	0x0c, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x35, 0x0a, 0x0d, 0x42, 0x65, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x22, 0x97, 0x01, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x2a,
	0x0a, 0x10, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x57,
	0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x43, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4d, 0x0a, 0x0f,
	0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x52,
	0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x82, 0x01, 0x0a, 0x0e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x11, 0x0a, 0x0f, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0e, 0x54, 0x78, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x4b, 0x0a, 0x0f,
	0x54, 0x78, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x72, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x84, 0x01, 0x0a, 0x10, 0x54, 0x78,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x13, 0x0a, 0x11, 0x54, 0x78, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xe9, 0x01, 0x0a, 0x05, 0x54, 0x78, 0x6d, 0x73, 0x67, 0x12,
	0x32, 0x0a, 0x05, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x13, 0x2e, 0x74, 0x78, 0x6d, 0x73, 0x67,
	0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e,
	0x74, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x2e,
	0x74, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x74, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x08, 0x52, 0x6f,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x16, 0x2e, 0x74, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x52,
	0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17,
	0x2e, 0x74, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x07, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x12, 0x15, 0x2e, 0x74, 0x78, 0x6d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x74, 0x78, 0x6d, 0x73,
	0x67, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x3d, 0x5a, 0x3b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x74, 0x78, 0x6d, 0x73, 0x67,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_txmsg_txmsg_proto_rawDescOnce sync.Once
	file_pb_txmsg_txmsg_proto_rawDescData = file_pb_txmsg_txmsg_proto_rawDesc
)

func file_pb_txmsg_txmsg_proto_rawDescGZIP() []byte {
	file_pb_txmsg_txmsg_proto_rawDescOnce.Do(func() {
		file_pb_txmsg_txmsg_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_txmsg_txmsg_proto_rawDescData)
	})
	return file_pb_txmsg_txmsg_proto_rawDescData
}

var file_pb_txmsg_txmsg_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_pb_txmsg_txmsg_proto_goTypes = []interface{}{
	(*BeginRequest)(nil),      // 0: txmsg.BeginRequest
	(*BeginResponse)(nil),     // 1: txmsg.BeginResponse
	(*CommitRequest)(nil),     // 2: txmsg.CommitRequest
	(*CommitResponse)(nil),    // 3: txmsg.CommitResponse
	(*RollbackRequest)(nil),   // 4: txmsg.RollbackRequest
	(*RollbackResponse)(nil),  // 5: txmsg.RollbackResponse
	(*TriggerRequest)(nil),    // 6: txmsg.TriggerRequest
	(*TriggerResponse)(nil),   // 7: txmsg.TriggerResponse
	(*TxQueryRequest)(nil),    // 8: txmsg.TxQueryRequest
	(*TxQueryResponse)(nil),   // 9: txmsg.TxQueryResponse
	(*TxProcessRequest)(nil),  // 10: txmsg.TxProcessRequest
	(*TxProcessResponse)(nil), // 11: txmsg.TxProcessResponse
}
var file_pb_txmsg_txmsg_proto_depIdxs = []int32{
	0, // 0: txmsg.Txmsg.Begin:input_type -> txmsg.BeginRequest
	2, // 1: txmsg.Txmsg.Commit:input_type -> txmsg.CommitRequest
	4, // 2: txmsg.Txmsg.Rollback:input_type -> txmsg.RollbackRequest
	6, // 3: txmsg.Txmsg.Trigger:input_type -> txmsg.TriggerRequest
	1, // 4: txmsg.Txmsg.Begin:output_type -> txmsg.BeginResponse
	3, // 5: txmsg.Txmsg.Commit:output_type -> txmsg.CommitResponse
	5, // 6: txmsg.Txmsg.Rollback:output_type -> txmsg.RollbackResponse
	7, // 7: txmsg.Txmsg.Trigger:output_type -> txmsg.TriggerResponse
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_txmsg_txmsg_proto_init() }
func file_pb_txmsg_txmsg_proto_init() {
	if File_pb_txmsg_txmsg_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_txmsg_txmsg_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TriggerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxProcessRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_txmsg_txmsg_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxProcessResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_txmsg_txmsg_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_txmsg_txmsg_proto_goTypes,
		DependencyIndexes: file_pb_txmsg_txmsg_proto_depIdxs,
		MessageInfos:      file_pb_txmsg_txmsg_proto_msgTypes,
	}.Build()
	File_pb_txmsg_txmsg_proto = out.File
	file_pb_txmsg_txmsg_proto_rawDesc = nil
	file_pb_txmsg_txmsg_proto_goTypes = nil
	file_pb_txmsg_txmsg_proto_depIdxs = nil
}
