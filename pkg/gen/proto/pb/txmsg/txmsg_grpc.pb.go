// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/txmsg/txmsg.proto

package txmsg

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Txmsg_Begin_FullMethodName    = "/txmsg.Txmsg/Begin"
	Txmsg_Commit_FullMethodName   = "/txmsg.Txmsg/Commit"
	Txmsg_Rollback_FullMethodName = "/txmsg.Txmsg/Rollback"
	Txmsg_Trigger_FullMethodName  = "/txmsg.Txmsg/Trigger"
)

// TxmsgClient is the client API for Txmsg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TxmsgClient interface {
	// 开启事务
	Begin(ctx context.Context, in *BeginRequest, opts ...grpc.CallOption) (*BeginResponse, error)
	// 提交事务
	Commit(ctx context.Context, in *CommitRequest, opts ...grpc.CallOption) (*CommitResponse, error)
	// 回滚事务
	Rollback(ctx context.Context, in *RollbackRequest, opts ...grpc.CallOption) (*RollbackResponse, error)
	// 触发事务
	Trigger(ctx context.Context, in *TriggerRequest, opts ...grpc.CallOption) (*TriggerResponse, error)
}

type txmsgClient struct {
	cc grpc.ClientConnInterface
}

func NewTxmsgClient(cc grpc.ClientConnInterface) TxmsgClient {
	return &txmsgClient{cc}
}

func (c *txmsgClient) Begin(ctx context.Context, in *BeginRequest, opts ...grpc.CallOption) (*BeginResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BeginResponse)
	err := c.cc.Invoke(ctx, Txmsg_Begin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *txmsgClient) Commit(ctx context.Context, in *CommitRequest, opts ...grpc.CallOption) (*CommitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommitResponse)
	err := c.cc.Invoke(ctx, Txmsg_Commit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *txmsgClient) Rollback(ctx context.Context, in *RollbackRequest, opts ...grpc.CallOption) (*RollbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RollbackResponse)
	err := c.cc.Invoke(ctx, Txmsg_Rollback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *txmsgClient) Trigger(ctx context.Context, in *TriggerRequest, opts ...grpc.CallOption) (*TriggerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TriggerResponse)
	err := c.cc.Invoke(ctx, Txmsg_Trigger_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TxmsgServer is the server API for Txmsg service.
// All implementations should embed UnimplementedTxmsgServer
// for forward compatibility
type TxmsgServer interface {
	// 开启事务
	Begin(context.Context, *BeginRequest) (*BeginResponse, error)
	// 提交事务
	Commit(context.Context, *CommitRequest) (*CommitResponse, error)
	// 回滚事务
	Rollback(context.Context, *RollbackRequest) (*RollbackResponse, error)
	// 触发事务
	Trigger(context.Context, *TriggerRequest) (*TriggerResponse, error)
}

// UnimplementedTxmsgServer should be embedded to have forward compatible implementations.
type UnimplementedTxmsgServer struct {
}

func (UnimplementedTxmsgServer) Begin(context.Context, *BeginRequest) (*BeginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Begin not implemented")
}
func (UnimplementedTxmsgServer) Commit(context.Context, *CommitRequest) (*CommitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Commit not implemented")
}
func (UnimplementedTxmsgServer) Rollback(context.Context, *RollbackRequest) (*RollbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Rollback not implemented")
}
func (UnimplementedTxmsgServer) Trigger(context.Context, *TriggerRequest) (*TriggerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Trigger not implemented")
}

// UnsafeTxmsgServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TxmsgServer will
// result in compilation errors.
type UnsafeTxmsgServer interface {
	mustEmbedUnimplementedTxmsgServer()
}

func RegisterTxmsgServer(s grpc.ServiceRegistrar, srv TxmsgServer) {
	s.RegisterService(&Txmsg_ServiceDesc, srv)
}

func _Txmsg_Begin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TxmsgServer).Begin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Txmsg_Begin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TxmsgServer).Begin(ctx, req.(*BeginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Txmsg_Commit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TxmsgServer).Commit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Txmsg_Commit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TxmsgServer).Commit(ctx, req.(*CommitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Txmsg_Rollback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TxmsgServer).Rollback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Txmsg_Rollback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TxmsgServer).Rollback(ctx, req.(*RollbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Txmsg_Trigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TxmsgServer).Trigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Txmsg_Trigger_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TxmsgServer).Trigger(ctx, req.(*TriggerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Txmsg_ServiceDesc is the grpc.ServiceDesc for Txmsg service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Txmsg_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "txmsg.Txmsg",
	HandlerType: (*TxmsgServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Begin",
			Handler:    _Txmsg_Begin_Handler,
		},
		{
			MethodName: "Commit",
			Handler:    _Txmsg_Commit_Handler,
		},
		{
			MethodName: "Rollback",
			Handler:    _Txmsg_Rollback_Handler,
		},
		{
			MethodName: "Trigger",
			Handler:    _Txmsg_Trigger_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/txmsg/txmsg.proto",
}
