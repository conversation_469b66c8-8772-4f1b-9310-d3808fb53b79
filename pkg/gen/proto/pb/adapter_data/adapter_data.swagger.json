{"swagger": "2.0", "info": {"title": "pb/adapter_data/adapter_data.proto", "version": "version not set"}, "tags": [{"name": "AdapterData"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/adapter_data.AdapterData/AlarmProxy": {"post": {"summary": "告警", "operationId": "AdapterData_AlarmProxy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataAlarmProxyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataAlarmProxyReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/ConfigSync": {"post": {"summary": "配置同步", "operationId": "AdapterData_ConfigSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataConfigSyncRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataConfigSyncReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/CwsxAdSpotTimes": {"post": {"summary": "cwsx广告点位", "operationId": "AdapterData_CwsxAdSpotTimes", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataCwsxAdSpotTimesRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataCwsxAdSpotTimesReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/CwsxGetSuggest": {"post": {"summary": "三消拉取推荐信息", "operationId": "AdapterData_CwsxGetSuggest", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataCwsxGetSuggestRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataCwsxGetSuggestReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/CwsxQueryOrders": {"post": {"summary": "三消对账 查询平台订单", "operationId": "AdapterData_CwsxQueryOrders", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataCwsxQueryOrdersRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataCwsxQueryOrdersReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/DCReport": {"post": {"summary": "上报 IDC DC表; 上报公有云DC不可用该接口", "operationId": "AdapterData_DCReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataDCReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataDCReportReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/DataReport": {"post": {"summary": "数据上报", "operationId": "AdapterData_DataReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataDataReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataDataReportReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/GetCwGameRankInfo": {"post": {"summary": "获取cw游戏信息", "operationId": "AdapterData_GetCwGameRankInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataGetCwGameRankInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataGetCwGameRankInfoReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/NoteRank": {"post": {"summary": "排行榜", "operationId": "AdapterData_NoteRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataNoteRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataNoteRankReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/NoteWeekRank": {"post": {"summary": "排行榜 周", "operationId": "AdapterData_NoteWeekRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataNoteWeekRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataNoteWeekRankReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/QzaReport": {"post": {"summary": "kb 写上报", "operationId": "AdapterData_QzaReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataQzaReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataQzaReportReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/ReportEvent": {"post": {"summary": "上报事件", "operationId": "AdapterData_ReportEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataReportEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataReportEventReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/ReportEventPlatform": {"post": {"summary": "上报事件", "operationId": "AdapterData_ReportEventPlatform", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataReportEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataReportEventReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/RiskReport": {"post": {"summary": "风控上报", "operationId": "AdapterData_RiskReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataRiskReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataRiskReportReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/TDBankReport": {"post": {"summary": "数据上报", "operationId": "AdapterData_TDBankReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataTDBankReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataTDBankReportReq"}}], "tags": ["AdapterData"]}}, "/adapter_data.AdapterData/ZhiYanReport": {"post": {"summary": "智研上报", "operationId": "AdapterData_ZhiYanReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_dataZhiYanReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_dataZhiYanReportReq"}}], "tags": ["AdapterData"]}}}, "definitions": {"CwsxGetSuggestReqDevice": {"type": "object", "properties": {"os": {"$ref": "#/definitions/CwsxGetSuggestReqOsType", "title": "操作系统"}}}, "CwsxGetSuggestReqGameContext": {"type": "object", "properties": {"curStageId": {"type": "integer", "format": "int32", "title": "当前闯关id"}, "curHealth": {"type": "integer", "format": "int32", "title": "当前体力值"}, "curCoins": {"type": "string", "format": "int64", "title": "当前金币数"}, "maxNormalFloor": {"type": "integer", "format": "int32", "title": "界面上展示的第x关"}, "beforeProps": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_dataCwsxGetSuggestReqPropInfo"}, "title": "战前使用道具"}, "maxExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展字段"}}}, "CwsxGetSuggestReqOsType": {"type": "string", "enum": ["OsTypeAndriod", "OsTypeIOS"], "default": "OsTypeAndriod"}, "CwsxGetSuggestRspDifficultyType": {"type": "string", "enum": ["DifficultyEasy", "DifficultyNormal", "DifficultyHard"], "default": "DifficultyEasy"}, "CwsxGetSuggestRspSuggestion": {"type": "object", "properties": {"type": {"$ref": "#/definitions/CwsxGetSuggestRspSuggestionType", "title": "推荐类型"}, "value": {"$ref": "#/definitions/CwsxGetSuggestRspSuggestionValue", "title": "推荐内容"}}}, "CwsxGetSuggestRspSuggestionType": {"type": "string", "enum": ["ModifyDifficulty", "ModifyStep", "ModifyInlineStep"], "default": "ModifyDifficulty", "title": "- ModifyDifficulty: 推荐类型-难度修改\n - ModifyStep: 推荐类型-最后一步\n - ModifyInlineStep: 推荐类型-局内推荐"}, "CwsxGetSuggestRspSuggestionValue": {"type": "object", "properties": {"difficultyType": {"$ref": "#/definitions/CwsxGetSuggestRspDifficultyType", "title": "难度"}, "difficultyValue": {"type": "number", "format": "float", "title": "随机概率"}}}, "RiskReportReqAssetInfo": {"type": "object", "properties": {"strAssetType": {"type": "string"}, "strAssetId": {"type": "string"}, "lAssetAmount": {"type": "string", "format": "int64"}, "lAssetPrice": {"type": "string", "format": "int64"}}}, "RiskReportReqAssetOperationItem": {"type": "object", "properties": {"stCommInfo": {"$ref": "#/definitions/RiskReportReqCommInfo"}, "stUserInfo": {"$ref": "#/definitions/RiskReportReqUserInfo", "title": "填openid,会根据strAppId自动替换为uid"}, "stAssetInfo": {"$ref": "#/definitions/RiskReportReqAssetInfo"}, "stSceneInfo": {"$ref": "#/definitions/RiskReportReqSceneInfo"}, "stDeviceInfo": {"$ref": "#/definitions/RiskReportReqDeviceInfo"}, "mapExtInfo": {"type": "object", "additionalProperties": {"type": "string"}}}}, "RiskReportReqCommInfo": {"type": "object", "properties": {"eAppType": {"type": "integer", "format": "int64"}, "strOrderId": {"type": "string"}, "lTimestamp": {"type": "string", "format": "int64"}, "strTraceId": {"type": "string"}}}, "RiskReportReqDeviceInfo": {"type": "object", "properties": {"strOsPlatform": {"type": "string"}, "strQimei36": {"type": "string"}, "strUuid": {"type": "string"}, "strClientIp": {"type": "string"}}}, "RiskReportReqSceneInfo": {"type": "object", "properties": {"strRoiId": {"type": "string"}, "strSceneId": {"type": "string"}}}, "RiskReportReqUserInfo": {"type": "object", "properties": {"strUserId": {"type": "string"}, "eUserType": {"type": "integer", "format": "int64"}}}, "adapter_dataAlarmProxyReq": {"type": "object", "properties": {"alarmType": {"$ref": "#/definitions/adapter_dataAlarmType"}, "msg": {"type": "string"}}}, "adapter_dataAlarmProxyRsp": {"type": "object"}, "adapter_dataAlarmType": {"type": "string", "enum": ["AlarmTypeUnknow", "AlarmTypeMsg", "AlarmTypeEmail", "AlarmTypePhone"], "default": "AlarmTypeUnknow"}, "adapter_dataConfigSyncReq": {"type": "object", "properties": {"key": {"type": "string"}, "data": {"type": "string", "format": "byte"}, "pid": {"type": "string", "format": "uint64"}, "openId": {"type": "string"}, "appId": {"type": "string"}}}, "adapter_dataConfigSyncRsp": {"type": "object"}, "adapter_dataCwGameRankInfo": {"type": "object", "properties": {"friendPetId": {"type": "string", "format": "int64", "title": "好友的宠物id"}, "friendPetCover": {"type": "string", "title": "宠物头像"}, "friendPetStatus": {"$ref": "#/definitions/adapter_dataPetStatus", "title": "宠物状态"}, "friendPetLiveStatus": {"$ref": "#/definitions/adapter_dataPetLiveStatus", "title": "宠物饥饿状态"}, "petInteractiveStatus": {"$ref": "#/definitions/adapter_dataPetInteractiveStatus", "title": "宠物互动操作状态"}, "concerned": {"type": "integer", "format": "int32", "title": "是否关注 1:展示关注icon 2:展示微信 3:展示qq"}}}, "adapter_dataCwsxAdSpotTimesReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "adapter_dataCwsxAdSpotTimesRsp": {"type": "object", "properties": {"spotTimes": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}}}}, "adapter_dataCwsxGetSuggestReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "gameContext": {"$ref": "#/definitions/CwsxGetSuggestReqGameContext", "title": "闯关信息"}, "device": {"$ref": "#/definitions/CwsxGetSuggestReqDevice", "title": "设备信息"}}}, "adapter_dataCwsxGetSuggestReqPropInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "道具id"}, "num": {"type": "integer", "format": "int64", "title": "道具数量"}}}, "adapter_dataCwsxGetSuggestRsp": {"type": "object", "properties": {"suggests": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CwsxGetSuggestRspSuggestion"}}, "algorithmInfo": {"type": "string", "title": "上报用, 给到前端, 前端上报到 algorithm_id 字段"}, "traceId": {"type": "string", "title": "上报用, 给到前端, 前端上报到trace_id字段"}}}, "adapter_dataCwsxQueryOrdersReq": {"type": "object", "properties": {"appId": {"type": "string"}, "startTime": {"type": "string", "format": "int64"}, "endTime": {"type": "string", "format": "int64"}, "passback": {"type": "string", "format": "int64"}}}, "adapter_dataCwsxQueryOrdersRsp": {"type": "object", "properties": {"data": {"type": "string"}, "passback": {"type": "string", "format": "int64"}, "hasMore": {"type": "boolean"}}}, "adapter_dataDCReportReq": {"type": "object", "properties": {"tableName": {"type": "string"}, "data": {"type": "array", "items": {"type": "string"}}}}, "adapter_dataDCReportRsp": {"type": "object"}, "adapter_dataDataReportReq": {"type": "object", "properties": {"type": {"type": "string", "title": "type 数据类型"}, "data": {"type": "string", "title": "data 数据"}, "appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "open id"}, "platId": {"type": "string", "format": "uint64", "title": "plat id"}}}, "adapter_dataDataReportRsp": {"type": "object"}, "adapter_dataGetCwGameRankInfoReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "主人态 open_id"}, "openIdList": {"type": "array", "items": {"type": "string"}, "title": "榜单 open_id 列表,支持查自己"}}}, "adapter_dataGetCwGameRankInfoRsp": {"type": "object", "properties": {"cwGameRankInfos": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_dataCwGameRankInfo"}, "title": "用户资料 key 为 open id"}}}, "adapter_dataNoteRankItem": {"type": "object", "properties": {"openId": {"type": "string", "title": "用户id"}, "notes": {"type": "string", "format": "uint64", "title": "音符"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "cwGameRankInfo": {"$ref": "#/definitions/adapter_dataCwGameRankInfo", "title": "cw游戏排行榜信息"}, "extraNote": {"type": "integer", "format": "int32", "title": "是否有额外音符可收"}}}, "adapter_dataNoteRankReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}}}, "adapter_dataNoteRankRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_dataNoteRankItem"}, "title": "排行榜列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "再次翻页的时候需要把这个东西传过来"}, "friendApiAuth": {"type": "integer", "format": "int32", "title": "好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆"}, "bingStatus": {"type": "integer", "format": "int32", "title": "绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定"}, "selfRankItem": {"$ref": "#/definitions/adapter_dataSelfRankItem", "title": "自己的榜单信息"}}}, "adapter_dataNoteWeekRankReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}}}, "adapter_dataNoteWeekRankRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_dataNoteRankItem"}, "title": "排行榜列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "再次翻页的时候需要把这个东西传过来"}, "friendApiAuth": {"type": "integer", "format": "int32", "title": "好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆"}, "bingStatus": {"type": "integer", "format": "int32", "title": "绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定"}, "selfRankItem": {"$ref": "#/definitions/adapter_dataSelfRankItem", "title": "自己的榜单信息"}}}, "adapter_dataPetInteractiveStatus": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "PetReceivingNote", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "PetUnHappy", "PetSick"], "default": "<PERSON><PERSON><PERSON><PERSON>", "title": "- PetDefault: 无\n - PetReceivingNote: 收音符\n - PetHunger: 饿了\n - PetDirty: 脏了\n - PetUnHappy: 不开心\n - PetSick: 生病"}, "adapter_dataPetLiveStatus": {"type": "string", "enum": ["PetLSNormal", "PetLSHungry"], "default": "PetLSNormal", "title": "- PetLSNormal: 正常状态\n - PetLSHungry: 饥饿状态"}, "adapter_dataPetStatus": {"type": "string", "enum": ["PetNotAdopt", "Pet<PERSON><PERSON><PERSON>", "PetHatched", "PetIdle", "PetOutWaiting", "PetOutting", "PetBack", "PetDying", "Pet<PERSON><PERSON>ing"], "default": "PetNotAdopt", "title": "- PetNotAdopt: 尚未被领取, 所有宠物的初始状态\n - PetAdopted: 已领取孵化中, 用户点击收下宠物蛋后转这个状态\n - PetHatched: 已孵化成功, 成功孵化后转这个状态\n - PetIdle: 在小窝, 用户点击领养宠物/出门回家被查看后转这个状态\n - PetOutWaiting: 点击出门之后, 宠物处于出门等待中状态\n - PetOutting: 出门中, 宠物匹配到后转这个状态\n - PetBack: 宠物刚刚回家\n - PetDying: 濒临死亡状态\n - PetRescuing: 听歌抢救状态"}, "adapter_dataQzaReportReq": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "string"}}, "newReport": {"type": "boolean", "title": "上报到music_dc00109, K歌表"}, "newReportQmusic": {"type": "boolean", "title": "上报到music_dc01918,Q音表"}}, "title": "优先级:\n1. 当new_report_qmusic = true时, 只上报到music_dc01918(Q音表), 否则:\n2. 当new_report = true时, 只上报到music_dc00109(K歌表), 否则:\n3. 当new_report = false时, 上报到k币写上报表(k歌表)"}, "adapter_dataQzaReportRsp": {"type": "object"}, "adapter_dataReportEventReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "单个用户事件的时候需要填写"}, "eventType": {"type": "integer", "format": "int64", "title": "事件类型 event.TmeEventType"}, "eventId": {"type": "string", "title": "事件ID"}, "room": {"$ref": "#/definitions/eventRoomBase", "title": "房间基础信息"}, "ts": {"type": "integer", "format": "int64", "title": "时间时间戳"}, "toOpenId": {"type": "string", "title": "动作朝向 open_id"}, "eventInfo": {"type": "string", "title": "event_type对接的结构体 json.Marshal 之后的结果"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "过滤信息 && 额外设置信息"}, "uid": {"type": "string"}, "toUid": {"type": "string"}}}, "adapter_dataReportEventRsp": {"type": "object"}, "adapter_dataRiskReportReq": {"type": "object", "properties": {"vctIncomes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RiskReportReqAssetOperationItem"}}, "vctPayouts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RiskReportReqAssetOperationItem"}}, "strAppId": {"type": "string"}}}, "adapter_dataRiskReportRsp": {"type": "object", "properties": {"iPlaceholder": {"type": "integer", "format": "int32"}}}, "adapter_dataSelfRankItem": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "用户排名"}, "notes": {"type": "string", "format": "uint64", "title": "音符"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "friendPetCover": {"type": "string", "title": "宠物头像"}}}, "adapter_dataTDBankReportReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "open id"}, "table": {"type": "string", "title": "表名"}, "program": {"type": "string", "title": "服务名"}, "message": {"type": "string", "title": "数据"}, "bussId": {"type": "string", "title": "业务"}}}, "adapter_dataTDBankReportRsp": {"type": "object"}, "adapter_dataZhiYanReportReq": {"type": "object", "properties": {"appMark": {"type": "string", "title": "上报标识"}, "metricGroup": {"type": "string", "title": "指标组"}, "reportIndex": {"type": "object", "additionalProperties": {"type": "number", "format": "double"}, "title": "指标"}, "reportTag": {"type": "object", "additionalProperties": {"type": "string"}, "title": "维度"}, "reportTs": {"type": "string", "format": "int64", "title": "上报的秒级时间"}}}, "adapter_dataZhiYanReportRsp": {"type": "object"}, "eventGameRoomMakeUpConfig": {"type": "object", "properties": {"minPlayers": {"type": "integer", "format": "int64", "title": "最少开始人数"}, "maxPlayers": {"type": "integer", "format": "int64", "title": "最大加入人数"}}}, "eventGameRoomPayConfig": {"type": "object", "properties": {"mod": {"type": "integer", "format": "int64", "title": "PayMode*"}, "assetId": {"type": "integer", "format": "int64"}, "assetNum": {"type": "integer", "format": "int64"}, "payModeName": {"type": "string"}, "payModeId": {"type": "string"}}}, "eventRoomBase": {"type": "object", "properties": {"gameType": {"type": "integer", "format": "int64", "title": "游戏类别"}, "roundId": {"type": "string", "title": "轮次ID"}, "modId": {"type": "string", "title": "模式ID, 游戏配置, 1v1, 2v2v2, 5人 根据这个字段判断"}, "payConfig": {"$ref": "#/definitions/eventGameRoomPayConfig", "title": "游戏门票信息, 根据asset_num判断 初级场/高级场"}, "roomId": {"type": "string", "description": "歌房&直播间 ID", "title": "所在玩的房间信息"}, "anchorId": {"type": "string", "format": "uint64", "title": "房主uid"}, "showId": {"type": "string", "title": "show id"}, "matchRoomId": {"type": "string", "description": "匹配的游戏房间ID", "title": "对战类必填信息"}, "players": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "确定成员信息"}, "makeupConfig": {"$ref": "#/definitions/eventGameRoomMakeUpConfig", "title": "房间创建信息"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}