// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/cwsx_game_endless_svr/cwsx_game_endless_svr.proto

/*
Package cwsx_game_endless_svr is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package cwsx_game_endless_svr

import (
	"context"
	"io"
	"kugou_adapter_service/pkg/gen/proto/pb/open_pay_extension"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_CwsxGameEndlessSvr_CheckOrder_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessSvrClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.CheckOrderReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.CheckOrder(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessSvr_CheckOrder_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessSvrServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.CheckOrderReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.CheckOrder(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameEndlessSvr_Delivery_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessSvrClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.DeliveryReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.Delivery(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessSvr_Delivery_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessSvrServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.DeliveryReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.Delivery(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameEndlessSvr_LockProduct_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessSvrClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.LockProductReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.LockProduct(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessSvr_LockProduct_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessSvrServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.LockProductReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.LockProduct(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameEndlessSvr_UnlockProduct_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessSvrClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.UnlockProductReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.UnlockProduct(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessSvr_UnlockProduct_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessSvrServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq open_pay_extension.UnlockProductReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.UnlockProduct(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterCwsxGameEndlessSvrHandlerServer registers the http handlers for service CwsxGameEndlessSvr to "mux".
// UnaryRPC     :call CwsxGameEndlessSvrServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterCwsxGameEndlessSvrHandlerFromEndpoint instead.
func RegisterCwsxGameEndlessSvrHandlerServer(ctx context.Context, mux *runtime.ServeMux, server CwsxGameEndlessSvrServer) error {

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_CheckOrder_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/CheckOrder", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/CheckOrder"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessSvr_CheckOrder_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_CheckOrder_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_Delivery_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/Delivery", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/Delivery"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessSvr_Delivery_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_Delivery_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_LockProduct_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/LockProduct", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/LockProduct"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessSvr_LockProduct_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_LockProduct_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_UnlockProduct_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/UnlockProduct", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/UnlockProduct"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessSvr_UnlockProduct_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_UnlockProduct_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterCwsxGameEndlessSvrHandlerFromEndpoint is same as RegisterCwsxGameEndlessSvrHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterCwsxGameEndlessSvrHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterCwsxGameEndlessSvrHandler(ctx, mux, conn)
}

// RegisterCwsxGameEndlessSvrHandler registers the http handlers for service CwsxGameEndlessSvr to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterCwsxGameEndlessSvrHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterCwsxGameEndlessSvrHandlerClient(ctx, mux, NewCwsxGameEndlessSvrClient(conn))
}

// RegisterCwsxGameEndlessSvrHandlerClient registers the http handlers for service CwsxGameEndlessSvr
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "CwsxGameEndlessSvrClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "CwsxGameEndlessSvrClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "CwsxGameEndlessSvrClient" to call the correct interceptors.
func RegisterCwsxGameEndlessSvrHandlerClient(ctx context.Context, mux *runtime.ServeMux, client CwsxGameEndlessSvrClient) error {

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_CheckOrder_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/CheckOrder", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/CheckOrder"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessSvr_CheckOrder_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_CheckOrder_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_Delivery_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/Delivery", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/Delivery"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessSvr_Delivery_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_Delivery_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_LockProduct_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/LockProduct", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/LockProduct"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessSvr_LockProduct_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_LockProduct_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessSvr_UnlockProduct_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_svr.CwsxGameEndlessSvr/UnlockProduct", runtime.WithHTTPPathPattern("/cwsx_game_endless_svr.CwsxGameEndlessSvr/UnlockProduct"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessSvr_UnlockProduct_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessSvr_UnlockProduct_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_CwsxGameEndlessSvr_CheckOrder_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_svr.CwsxGameEndlessSvr", "CheckOrder"}, ""))

	pattern_CwsxGameEndlessSvr_Delivery_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_svr.CwsxGameEndlessSvr", "Delivery"}, ""))

	pattern_CwsxGameEndlessSvr_LockProduct_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_svr.CwsxGameEndlessSvr", "LockProduct"}, ""))

	pattern_CwsxGameEndlessSvr_UnlockProduct_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_svr.CwsxGameEndlessSvr", "UnlockProduct"}, ""))
)

var (
	forward_CwsxGameEndlessSvr_CheckOrder_0 = runtime.ForwardResponseMessage

	forward_CwsxGameEndlessSvr_Delivery_0 = runtime.ForwardResponseMessage

	forward_CwsxGameEndlessSvr_LockProduct_0 = runtime.ForwardResponseMessage

	forward_CwsxGameEndlessSvr_UnlockProduct_0 = runtime.ForwardResponseMessage
)
