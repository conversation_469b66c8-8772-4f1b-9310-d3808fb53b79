// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/cwsx_game_endless_svr/cwsx_game_endless_svr.proto

package cwsx_game_endless_svr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	open_pay_extension "kugou_adapter_service/pkg/gen/proto/pb/open_pay_extension"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CwsxGameEndlessSvr_CheckOrder_FullMethodName    = "/cwsx_game_endless_svr.CwsxGameEndlessSvr/CheckOrder"
	CwsxGameEndlessSvr_Delivery_FullMethodName      = "/cwsx_game_endless_svr.CwsxGameEndlessSvr/Delivery"
	CwsxGameEndlessSvr_LockProduct_FullMethodName   = "/cwsx_game_endless_svr.CwsxGameEndlessSvr/LockProduct"
	CwsxGameEndlessSvr_UnlockProduct_FullMethodName = "/cwsx_game_endless_svr.CwsxGameEndlessSvr/UnlockProduct"
)

// CwsxGameEndlessSvrClient is the client API for CwsxGameEndlessSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// cwsx 无尽宝藏
type CwsxGameEndlessSvrClient interface {
	// 校验订单
	CheckOrder(ctx context.Context, in *open_pay_extension.CheckOrderReq, opts ...grpc.CallOption) (*open_pay_extension.CheckOrderRsp, error)
	// 发货
	Delivery(ctx context.Context, in *open_pay_extension.DeliveryReq, opts ...grpc.CallOption) (*open_pay_extension.DeliveryRsp, error)
	// lock
	LockProduct(ctx context.Context, in *open_pay_extension.LockProductReq, opts ...grpc.CallOption) (*open_pay_extension.LockProductRsp, error)
	// unlock
	UnlockProduct(ctx context.Context, in *open_pay_extension.UnlockProductReq, opts ...grpc.CallOption) (*open_pay_extension.UnlockProductRsp, error)
}

type cwsxGameEndlessSvrClient struct {
	cc grpc.ClientConnInterface
}

func NewCwsxGameEndlessSvrClient(cc grpc.ClientConnInterface) CwsxGameEndlessSvrClient {
	return &cwsxGameEndlessSvrClient{cc}
}

func (c *cwsxGameEndlessSvrClient) CheckOrder(ctx context.Context, in *open_pay_extension.CheckOrderReq, opts ...grpc.CallOption) (*open_pay_extension.CheckOrderRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(open_pay_extension.CheckOrderRsp)
	err := c.cc.Invoke(ctx, CwsxGameEndlessSvr_CheckOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameEndlessSvrClient) Delivery(ctx context.Context, in *open_pay_extension.DeliveryReq, opts ...grpc.CallOption) (*open_pay_extension.DeliveryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(open_pay_extension.DeliveryRsp)
	err := c.cc.Invoke(ctx, CwsxGameEndlessSvr_Delivery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameEndlessSvrClient) LockProduct(ctx context.Context, in *open_pay_extension.LockProductReq, opts ...grpc.CallOption) (*open_pay_extension.LockProductRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(open_pay_extension.LockProductRsp)
	err := c.cc.Invoke(ctx, CwsxGameEndlessSvr_LockProduct_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameEndlessSvrClient) UnlockProduct(ctx context.Context, in *open_pay_extension.UnlockProductReq, opts ...grpc.CallOption) (*open_pay_extension.UnlockProductRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(open_pay_extension.UnlockProductRsp)
	err := c.cc.Invoke(ctx, CwsxGameEndlessSvr_UnlockProduct_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CwsxGameEndlessSvrServer is the server API for CwsxGameEndlessSvr service.
// All implementations should embed UnimplementedCwsxGameEndlessSvrServer
// for forward compatibility
//
// cwsx 无尽宝藏
type CwsxGameEndlessSvrServer interface {
	// 校验订单
	CheckOrder(context.Context, *open_pay_extension.CheckOrderReq) (*open_pay_extension.CheckOrderRsp, error)
	// 发货
	Delivery(context.Context, *open_pay_extension.DeliveryReq) (*open_pay_extension.DeliveryRsp, error)
	// lock
	LockProduct(context.Context, *open_pay_extension.LockProductReq) (*open_pay_extension.LockProductRsp, error)
	// unlock
	UnlockProduct(context.Context, *open_pay_extension.UnlockProductReq) (*open_pay_extension.UnlockProductRsp, error)
}

// UnimplementedCwsxGameEndlessSvrServer should be embedded to have forward compatible implementations.
type UnimplementedCwsxGameEndlessSvrServer struct {
}

func (UnimplementedCwsxGameEndlessSvrServer) CheckOrder(context.Context, *open_pay_extension.CheckOrderReq) (*open_pay_extension.CheckOrderRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckOrder not implemented")
}
func (UnimplementedCwsxGameEndlessSvrServer) Delivery(context.Context, *open_pay_extension.DeliveryReq) (*open_pay_extension.DeliveryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delivery not implemented")
}
func (UnimplementedCwsxGameEndlessSvrServer) LockProduct(context.Context, *open_pay_extension.LockProductReq) (*open_pay_extension.LockProductRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LockProduct not implemented")
}
func (UnimplementedCwsxGameEndlessSvrServer) UnlockProduct(context.Context, *open_pay_extension.UnlockProductReq) (*open_pay_extension.UnlockProductRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlockProduct not implemented")
}

// UnsafeCwsxGameEndlessSvrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CwsxGameEndlessSvrServer will
// result in compilation errors.
type UnsafeCwsxGameEndlessSvrServer interface {
	mustEmbedUnimplementedCwsxGameEndlessSvrServer()
}

func RegisterCwsxGameEndlessSvrServer(s grpc.ServiceRegistrar, srv CwsxGameEndlessSvrServer) {
	s.RegisterService(&CwsxGameEndlessSvr_ServiceDesc, srv)
}

func _CwsxGameEndlessSvr_CheckOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(open_pay_extension.CheckOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameEndlessSvrServer).CheckOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameEndlessSvr_CheckOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameEndlessSvrServer).CheckOrder(ctx, req.(*open_pay_extension.CheckOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameEndlessSvr_Delivery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(open_pay_extension.DeliveryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameEndlessSvrServer).Delivery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameEndlessSvr_Delivery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameEndlessSvrServer).Delivery(ctx, req.(*open_pay_extension.DeliveryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameEndlessSvr_LockProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(open_pay_extension.LockProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameEndlessSvrServer).LockProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameEndlessSvr_LockProduct_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameEndlessSvrServer).LockProduct(ctx, req.(*open_pay_extension.LockProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameEndlessSvr_UnlockProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(open_pay_extension.UnlockProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameEndlessSvrServer).UnlockProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameEndlessSvr_UnlockProduct_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameEndlessSvrServer).UnlockProduct(ctx, req.(*open_pay_extension.UnlockProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CwsxGameEndlessSvr_ServiceDesc is the grpc.ServiceDesc for CwsxGameEndlessSvr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CwsxGameEndlessSvr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cwsx_game_endless_svr.CwsxGameEndlessSvr",
	HandlerType: (*CwsxGameEndlessSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckOrder",
			Handler:    _CwsxGameEndlessSvr_CheckOrder_Handler,
		},
		{
			MethodName: "Delivery",
			Handler:    _CwsxGameEndlessSvr_Delivery_Handler,
		},
		{
			MethodName: "LockProduct",
			Handler:    _CwsxGameEndlessSvr_LockProduct_Handler,
		},
		{
			MethodName: "UnlockProduct",
			Handler:    _CwsxGameEndlessSvr_UnlockProduct_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/cwsx_game_endless_svr/cwsx_game_endless_svr.proto",
}
