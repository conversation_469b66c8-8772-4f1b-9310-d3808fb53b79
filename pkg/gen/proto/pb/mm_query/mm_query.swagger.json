{"swagger": "2.0", "info": {"title": "pb/mm_query/mm_query.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/mm_query.MMQuery/Query": {"post": {"summary": "查询配置信息", "operationId": "MMQuery_Query", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mm_queryQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mm_queryQueryReq"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "definitions": {"mm_queryQueryReq": {"type": "object", "properties": {"bussKey": {"type": "string"}, "skip": {"type": "integer", "format": "int32", "title": "默认0"}, "limit": {"type": "integer", "format": "int32", "title": "默认100条"}, "filterFlag": {"type": "string"}}}, "mm_queryQueryRsp": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}, "hasMore": {"type": "boolean"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}