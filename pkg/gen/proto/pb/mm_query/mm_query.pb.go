// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/mm_query/mm_query.proto

package mm_query

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BussKey    string `protobuf:"bytes,1,opt,name=buss_key,json=bussKey,proto3" json:"buss_key,omitempty"`
	Skip       int32  `protobuf:"varint,2,opt,name=skip,proto3" json:"skip,omitempty"`   // 默认0
	Limit      int32  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"` // 默认100条
	FilterFlag string `protobuf:"bytes,4,opt,name=filterFlag,proto3" json:"filterFlag,omitempty"`
}

func (x *QueryReq) Reset() {
	*x = QueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_mm_query_mm_query_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReq) ProtoMessage() {}

func (x *QueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_mm_query_mm_query_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReq.ProtoReflect.Descriptor instead.
func (*QueryReq) Descriptor() ([]byte, []int) {
	return file_pb_mm_query_mm_query_proto_rawDescGZIP(), []int{0}
}

func (x *QueryReq) GetBussKey() string {
	if x != nil {
		return x.BussKey
	}
	return ""
}

func (x *QueryReq) GetSkip() int32 {
	if x != nil {
		return x.Skip
	}
	return 0
}

func (x *QueryReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *QueryReq) GetFilterFlag() string {
	if x != nil {
		return x.FilterFlag
	}
	return ""
}

type QueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data    []string `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	HasMore bool     `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
}

func (x *QueryRsp) Reset() {
	*x = QueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_mm_query_mm_query_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRsp) ProtoMessage() {}

func (x *QueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_mm_query_mm_query_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRsp.ProtoReflect.Descriptor instead.
func (*QueryRsp) Descriptor() ([]byte, []int) {
	return file_pb_mm_query_mm_query_proto_rawDescGZIP(), []int{1}
}

func (x *QueryRsp) GetData() []string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *QueryRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

var File_pb_mm_query_mm_query_proto protoreflect.FileDescriptor

var file_pb_mm_query_mm_query_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x62, 0x2f, 0x6d, 0x6d, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x2f, 0x6d, 0x6d,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6d, 0x6d,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x6f, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x75, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x6b, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6b, 0x69,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x22, 0x39, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d,
	0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f,
	0x72, 0x65, 0x32, 0x3a, 0x0a, 0x07, 0x4d, 0x4d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x2f, 0x0a,
	0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x2e, 0x6d, 0x6d, 0x5f, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x6d, 0x6d, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x42, 0x40,
	0x5a, 0x3e, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x6d, 0x6d, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_mm_query_mm_query_proto_rawDescOnce sync.Once
	file_pb_mm_query_mm_query_proto_rawDescData = file_pb_mm_query_mm_query_proto_rawDesc
)

func file_pb_mm_query_mm_query_proto_rawDescGZIP() []byte {
	file_pb_mm_query_mm_query_proto_rawDescOnce.Do(func() {
		file_pb_mm_query_mm_query_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_mm_query_mm_query_proto_rawDescData)
	})
	return file_pb_mm_query_mm_query_proto_rawDescData
}

var file_pb_mm_query_mm_query_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_mm_query_mm_query_proto_goTypes = []interface{}{
	(*QueryReq)(nil), // 0: mm_query.QueryReq
	(*QueryRsp)(nil), // 1: mm_query.QueryRsp
}
var file_pb_mm_query_mm_query_proto_depIdxs = []int32{
	0, // 0: mm_query.MMQuery.Query:input_type -> mm_query.QueryReq
	1, // 1: mm_query.MMQuery.Query:output_type -> mm_query.QueryRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_mm_query_mm_query_proto_init() }
func file_pb_mm_query_mm_query_proto_init() {
	if File_pb_mm_query_mm_query_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_mm_query_mm_query_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_mm_query_mm_query_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_mm_query_mm_query_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_mm_query_mm_query_proto_goTypes,
		DependencyIndexes: file_pb_mm_query_mm_query_proto_depIdxs,
		MessageInfos:      file_pb_mm_query_mm_query_proto_msgTypes,
	}.Build()
	File_pb_mm_query_mm_query_proto = out.File
	file_pb_mm_query_mm_query_proto_rawDesc = nil
	file_pb_mm_query_mm_query_proto_goTypes = nil
	file_pb_mm_query_mm_query_proto_depIdxs = nil
}
