// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/mm_query/mm_query.proto

package mm_query

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	MMQuery_Query_FullMethodName = "/mm_query.MMQuery/Query"
)

// MMQueryClient is the client API for MMQuery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MMQueryClient interface {
	// 查询配置信息
	Query(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error)
}

type mMQueryClient struct {
	cc grpc.ClientConnInterface
}

func NewMMQueryClient(cc grpc.ClientConnInterface) MMQueryClient {
	return &mMQueryClient{cc}
}

func (c *mMQueryClient) Query(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRsp)
	err := c.cc.Invoke(ctx, MMQuery_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MMQueryServer is the server API for MMQuery service.
// All implementations should embed UnimplementedMMQueryServer
// for forward compatibility
type MMQueryServer interface {
	// 查询配置信息
	Query(context.Context, *QueryReq) (*QueryRsp, error)
}

// UnimplementedMMQueryServer should be embedded to have forward compatible implementations.
type UnimplementedMMQueryServer struct {
}

func (UnimplementedMMQueryServer) Query(context.Context, *QueryReq) (*QueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}

// UnsafeMMQueryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MMQueryServer will
// result in compilation errors.
type UnsafeMMQueryServer interface {
	mustEmbedUnimplementedMMQueryServer()
}

func RegisterMMQueryServer(s grpc.ServiceRegistrar, srv MMQueryServer) {
	s.RegisterService(&MMQuery_ServiceDesc, srv)
}

func _MMQuery_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MMQueryServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MMQuery_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MMQueryServer).Query(ctx, req.(*QueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// MMQuery_ServiceDesc is the grpc.ServiceDesc for MMQuery service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MMQuery_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "mm_query.MMQuery",
	HandlerType: (*MMQueryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Query",
			Handler:    _MMQuery_Query_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/mm_query/mm_query.proto",
}
