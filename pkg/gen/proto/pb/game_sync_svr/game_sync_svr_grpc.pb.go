// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_sync_svr/game_sync_svr.proto

package game_sync_svr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GameSyncSvr_GetGameConf_FullMethodName  = "/component.game.game_sync_svr/GetGameConf"
	GameSyncSvr_GameConfSync_FullMethodName = "/component.game.game_sync_svr/GameConfSync"
)

// GameSyncSvrClient is the client API for GameSyncSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GameSyncSvrClient interface {
	GetGameConf(ctx context.Context, in *GetGameConfReq, opts ...grpc.CallOption) (*GetGameConfRsp, error)
	GameConfSync(ctx context.Context, in *GameConfSyncReq, opts ...grpc.CallOption) (*GameConfSyncRsp, error)
}

type gameSyncSvrClient struct {
	cc grpc.ClientConnInterface
}

func NewGameSyncSvrClient(cc grpc.ClientConnInterface) GameSyncSvrClient {
	return &gameSyncSvrClient{cc}
}

func (c *gameSyncSvrClient) GetGameConf(ctx context.Context, in *GetGameConfReq, opts ...grpc.CallOption) (*GetGameConfRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGameConfRsp)
	err := c.cc.Invoke(ctx, GameSyncSvr_GetGameConf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncSvrClient) GameConfSync(ctx context.Context, in *GameConfSyncReq, opts ...grpc.CallOption) (*GameConfSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameConfSyncRsp)
	err := c.cc.Invoke(ctx, GameSyncSvr_GameConfSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameSyncSvrServer is the server API for GameSyncSvr service.
// All implementations should embed UnimplementedGameSyncSvrServer
// for forward compatibility
type GameSyncSvrServer interface {
	GetGameConf(context.Context, *GetGameConfReq) (*GetGameConfRsp, error)
	GameConfSync(context.Context, *GameConfSyncReq) (*GameConfSyncRsp, error)
}

// UnimplementedGameSyncSvrServer should be embedded to have forward compatible implementations.
type UnimplementedGameSyncSvrServer struct {
}

func (UnimplementedGameSyncSvrServer) GetGameConf(context.Context, *GetGameConfReq) (*GetGameConfRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameConf not implemented")
}
func (UnimplementedGameSyncSvrServer) GameConfSync(context.Context, *GameConfSyncReq) (*GameConfSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameConfSync not implemented")
}

// UnsafeGameSyncSvrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameSyncSvrServer will
// result in compilation errors.
type UnsafeGameSyncSvrServer interface {
	mustEmbedUnimplementedGameSyncSvrServer()
}

func RegisterGameSyncSvrServer(s grpc.ServiceRegistrar, srv GameSyncSvrServer) {
	s.RegisterService(&GameSyncSvr_ServiceDesc, srv)
}

func _GameSyncSvr_GetGameConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncSvrServer).GetGameConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncSvr_GetGameConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncSvrServer).GetGameConf(ctx, req.(*GetGameConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncSvr_GameConfSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameConfSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncSvrServer).GameConfSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncSvr_GameConfSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncSvrServer).GameConfSync(ctx, req.(*GameConfSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GameSyncSvr_ServiceDesc is the grpc.ServiceDesc for GameSyncSvr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GameSyncSvr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.game_sync_svr",
	HandlerType: (*GameSyncSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameConf",
			Handler:    _GameSyncSvr_GetGameConf_Handler,
		},
		{
			MethodName: "GameConfSync",
			Handler:    _GameSyncSvr_GameConfSync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_sync_svr/game_sync_svr.proto",
}
