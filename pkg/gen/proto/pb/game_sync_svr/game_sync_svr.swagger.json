{"swagger": "2.0", "info": {"title": "pb/game_sync_svr/game_sync_svr.proto", "version": "version not set"}, "tags": [{"name": "game_sync_svr"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.game_sync_svr/GameConfSync": {"post": {"operationId": "game_sync_svr_GameConfSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameConfSyncRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameConfSyncReq"}}], "tags": ["game_sync_svr"]}}, "/component.game.game_sync_svr/GetGameConf": {"post": {"operationId": "game_sync_svr_GetGameConf", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGetGameConfRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGetGameConfReq"}}], "tags": ["game_sync_svr"]}}}, "definitions": {"gameAssetConfig": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "expireType": {"type": "string", "format": "int64"}, "expireTime": {"type": "string", "format": "int64"}, "currencyType": {"type": "integer", "format": "int64", "title": "货币类型 1/游戏币 2/兑换币"}, "mapPlatConf": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gamePlatConf"}, "title": "游戏币平台配置，key为平台id，参考gopen.EPlatID"}, "tag": {"type": "string"}, "uniAssetId": {"type": "string", "format": "int64", "title": "对应的统一账户资产id"}, "modalType": {"type": "integer", "format": "int64"}, "assetRule": {"type": "string"}, "sendToUgc": {"type": "boolean"}}, "title": "资产配置"}, "gameAssetConfigMap": {"type": "object", "properties": {"configs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameAssetConfig"}, "title": "configs 该游戏各个资产的配置 key为assetId"}, "appName": {"type": "string", "title": "配置的发奖\nrepeated PrizeItem prizes = 2; 废弃"}, "gameConfigs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameGamePlatConf"}, "title": "游戏配置，key为平台id，参考gopen.EPlatID"}, "packageConfigs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gamePackageConfig"}, "title": "礼包配置"}, "uniAppId": {"type": "string", "title": "统一账户appID"}, "shareAppId": {"type": "string", "title": "共享账户appID"}, "useCustomOpenId": {"type": "string", "title": "使用自定义的OpenId"}}, "title": "资产配置列表"}, "gameGameAsset": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "name": {"type": "string"}, "icon": {"type": "string"}, "expireType": {"type": "string", "format": "int64"}, "expireTime": {"type": "string", "format": "int64"}, "currencyType": {"type": "integer", "format": "int64", "title": "货币类型 1/游戏币 2/兑换币"}, "plat": {"type": "integer", "format": "int64", "title": "平台ID gopen.EPlatID"}, "payType": {"type": "integer", "format": "int64", "title": "购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得"}, "giftOrPropUnitPrice": {"type": "integer", "format": "int64", "title": "道具/礼物单价"}, "giftOrPropId": {"type": "integer", "format": "int64", "title": "道具/礼物ID，如果支付类型为1,送出这个礼物，为3则送出这个道具"}, "exchangeRate": {"type": "integer", "format": "int64", "title": "汇率"}, "platBusinessID": {"type": "integer", "format": "int64", "title": "平台业务ID"}, "revenueType": {"type": "integer", "format": "int64", "title": "收入类型 1/计收入 2/不计收入"}, "tag": {"type": "string"}, "uniAssetId": {"type": "string", "format": "int64", "title": "对应的统一账户资产id"}, "payDesc": {"type": "string", "title": "购买弹窗文案"}, "payGear": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "购买弹窗前3档"}, "expireUrl": {"type": "string", "title": "过期链接"}, "modalType": {"type": "integer", "format": "int64"}, "assetRule": {"type": "string"}, "sendToUgc": {"type": "boolean"}}}, "gameGameConfSyncReq": {"type": "object", "properties": {"appId": {"type": "string"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGameAsset"}}, "appName": {"type": "string"}, "gameConfigs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameGamePlatConf"}, "title": "游戏配置，key为平台id，参考gopen.EPlatID"}, "packageConfigs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gamePackageConfig"}}, "uniAppId": {"type": "string"}, "shareAppId": {"type": "string"}, "useCustomOpenId": {"type": "string"}}}, "gameGameConfSyncRsp": {"type": "object", "properties": {"appId": {"type": "string"}}}, "gameGamePlatConf": {"type": "object", "properties": {"prizes": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "配置的发奖id"}}}, "gameGetGameConfReq": {"type": "object", "properties": {"appId": {"type": "string"}}}, "gameGetGameConfRsp": {"type": "object", "properties": {"config": {"$ref": "#/definitions/gameAssetConfigMap"}}}, "gamePackageConfig": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "giftDesc": {"type": "string"}, "mapPlatConf": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gamePlatConf"}}, "modalType": {"type": "integer", "format": "int64"}, "sendToUgc": {"type": "boolean"}, "extensionId": {"type": "string", "format": "int64"}}}, "gamePlatConf": {"type": "object", "properties": {"payType": {"type": "integer", "format": "int64", "title": "购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得"}, "giftOrPropUnitPrice": {"type": "integer", "format": "int64", "title": "道具单价"}, "giftOrPropId": {"type": "integer", "format": "int64", "title": "道具ID，如果支付类型为1,送出这个道具"}, "exchangeRate": {"type": "integer", "format": "int64", "title": "汇率"}, "platBusinessID": {"type": "integer", "format": "int64", "title": "平台业务ID"}, "revenueType": {"type": "integer", "format": "int64", "title": "收入类型 1/计收入 2/不计收入"}, "rewardId": {"type": "string", "format": "int64", "title": "奖品 id 礼包使用"}, "payDesc": {"type": "string", "title": "购买弹窗提示文案"}, "payGear": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "购买弹窗档位"}, "expireUrl": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}