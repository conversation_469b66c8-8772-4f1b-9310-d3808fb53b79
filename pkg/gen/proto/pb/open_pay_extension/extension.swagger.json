{"swagger": "2.0", "info": {"title": "pb/open_pay_extension/extension.proto", "version": "version not set"}, "tags": [{"name": "Extension"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/open_pay_extension.Extension/CheckOrder": {"post": {"summary": "校验订单", "operationId": "Extension_CheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extensionCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extensionCheckOrderReq"}}], "tags": ["Extension"]}}, "/open_pay_extension.Extension/Delivery": {"post": {"summary": "发货", "operationId": "Extension_Delivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extensionDeliveryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extensionDeliveryReq"}}], "tags": ["Extension"]}}, "/open_pay_extension.Extension/LockProduct": {"post": {"summary": "lock", "operationId": "Extension_LockProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extensionLockProductRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extensionLockProductReq"}}], "tags": ["Extension"]}}, "/open_pay_extension.Extension/UnlockProduct": {"post": {"summary": "unlock", "operationId": "Extension_UnlockProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extensionUnlockProductRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extensionUnlockProductReq"}}], "tags": ["Extension"]}}}, "definitions": {"DeliveryRspProduct": {"type": "object", "properties": {"name": {"type": "string"}, "num": {"type": "string", "format": "int64"}}}, "deviceDevice": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台信息 kugou、qqmusic、qmkege、kuwo、lanren"}, "version": {"type": "string", "title": "客户端版本 1.2.3"}, "os": {"type": "string", "title": "系统 android、ios"}}}, "open_pay_extensionCheckOrderReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "price": {"type": "string", "format": "int64", "title": "价格"}, "greenId": {"type": "string", "format": "int64", "title": "绿钻 id"}, "os": {"$ref": "#/definitions/open_pay_extensionOperatingSystem", "title": "系统"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "open_pay_extensionCheckOrderRsp": {"type": "object"}, "open_pay_extensionDeliveryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}, "os": {"$ref": "#/definitions/open_pay_extensionOperatingSystem", "title": "系统"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}, "device": {"$ref": "#/definitions/deviceDevice"}}}, "open_pay_extensionDeliveryRsp": {"type": "object", "properties": {"products": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/DeliveryRspProduct"}}}}, "open_pay_extensionLockProductReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间 0:不过期"}}}, "open_pay_extensionLockProductRsp": {"type": "object"}, "open_pay_extensionOperatingSystem": {"type": "string", "enum": ["OperatingSystemUnknown", "OperatingSystemAndroid", "OperatingSystemIOS"], "default": "OperatingSystemUnknown"}, "open_pay_extensionUnlockProductReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}}}, "open_pay_extensionUnlockProductRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}