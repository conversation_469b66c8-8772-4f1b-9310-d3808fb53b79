{"swagger": "2.0", "info": {"title": "pb/game_segment_api/game_segment_api.proto", "version": "version not set"}, "tags": [{"name": "SeasonSegmentAPI"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game.SeasonSegmentAPI/AckSeasonEvent": {"post": {"summary": "Ack赛季事件", "operationId": "SeasonSegmentAPI_AckSeasonEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameAckSeasonEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameAckSeasonEventReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/GetSeasonEvent": {"post": {"summary": "拉取赛季事件列表", "operationId": "SeasonSegmentAPI_GetSeasonEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGetSeasonEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGetSeasonEventReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/QuerySeasonRank": {"post": {"summary": "查询赛季榜单-巅峰榜", "operationId": "SeasonSegmentAPI_QuerySeasonRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQuerySeasonRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQuerySeasonRankReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/QuerySeasonRecord": {"post": {"summary": "查询赛季回顾纪录", "operationId": "SeasonSegmentAPI_QuerySeasonRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQuerySeasonRecordRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQuerySeasonRecordReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/QuerySeasonSegment": {"post": {"summary": "查询赛季和段位信息", "operationId": "SeasonSegmentAPI_QuerySeasonSegment", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQuerySeasonSegmentRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQuerySeasonSegmentReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/QuerySegmentAward": {"post": {"summary": "查询段位奖励", "operationId": "SeasonSegmentAPI_QuerySegmentAward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQuerySegmentAwardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQuerySegmentAwardReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/QuerySegmentRank": {"post": {"summary": "查询段位榜单-分组榜单", "operationId": "SeasonSegmentAPI_QuerySegmentRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQuerySegmentRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQuerySegmentRankReq"}}], "tags": ["SeasonSegmentAPI"]}}, "/game.SeasonSegmentAPI/ReceiveAward": {"post": {"summary": "领取段位奖励", "operationId": "SeasonSegmentAPI_ReceiveAward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameReceiveAwardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameReceiveAwardReq"}}], "tags": ["SeasonSegmentAPI"]}}}, "definitions": {"GetSeasonEventRspEvent": {"type": "object", "properties": {"eventID": {"type": "integer", "format": "int32", "title": "事件ID"}, "cmd": {"type": "integer", "format": "int32", "title": "主事件"}, "subCmd": {"type": "integer", "format": "int32", "title": "子事件"}, "data": {"type": "string", "title": "事件数据-json类型，根据cmd-subcmd找到对应结构解析"}}}, "QuerySeasonRecordRspSeasonHistory": {"type": "object", "properties": {"seasonName": {"type": "string", "title": "赛季名称"}, "startTime": {"type": "string", "format": "int64", "title": "赛季开始时间"}, "endTime": {"type": "string", "format": "int64", "title": "赛季结束时间"}, "segmentName": {"type": "string", "title": "最高段位名称，比如：黄金III"}, "segmentIcon": {"type": "string", "title": "最高段位Icon"}}}, "QuerySegmentAwardRspSegmentAwardItem": {"type": "object", "properties": {"mainName": {"type": "string", "title": "主段位名称，比如：黄金I"}, "mainIcon": {"type": "string", "title": "主段位Icon"}, "subSegmentList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QuerySegmentAwardRspSubSegAwardItem"}, "title": "子段位列表"}, "hasAward": {"type": "boolean", "title": "段位奖励小红点"}}}, "QuerySegmentAwardRspSubSegAwardItem": {"type": "object", "properties": {"segmentID": {"type": "integer", "format": "int32", "title": "段位ID"}, "subName": {"type": "string", "title": "子段位名称，比如：III"}, "subIcon": {"type": "string", "title": "子段位Icon"}, "status": {"$ref": "#/definitions/gameAwardStatus", "title": "奖励状态，参见：game_segment_comm.AwardStatus"}, "awardList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameAwardItem"}, "title": "奖励列表"}}}, "QuerySegmentRankRspSectionInfo": {"type": "object", "properties": {"sectionName": {"type": "string", "title": "分段名称, 例如: 升段区, 保段区 or 降段区"}, "sectionDesc": {"type": "string", "title": "分段描述, 例如: 排名前20名可升至白银I"}, "groupType": {"$ref": "#/definitions/gameGroupType", "title": "分组类型，参见：game_segment_comm.GroupType"}, "rankLine": {"type": "integer", "format": "int32", "title": "进入该分组的排名下限(userRank <= rankLine)"}}}, "SeasonSegmentInfoPeriodInfo": {"type": "object", "properties": {"startTime": {"type": "string", "format": "int64", "title": "周期开始时间"}, "endTime": {"type": "string", "format": "int64", "title": "周期结束时间"}}}, "gameAckSeasonEventReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "eventIDList": {"type": "array", "items": {"type": "integer", "format": "int32"}, "title": "事件ID列表"}}}, "gameAckSeasonEventRsp": {"type": "object"}, "gameAwardItem": {"type": "object", "properties": {"name": {"type": "string", "title": "奖励名称"}, "num": {"type": "integer", "format": "int32", "title": "奖励数量"}, "icon": {"type": "string", "title": "奖励Icon"}, "desc": {"type": "string", "title": "奖励描述"}}}, "gameAwardStatus": {"type": "string", "enum": ["AWARD_STATUS_NONE", "AWARD_STATUS_LOCK", "AWARD_STATUS_UNLOCK", "AWARD_STATUS_PICK"], "default": "AWARD_STATUS_NONE", "description": "- AWARD_STATUS_NONE: 未知状态\n - AWARD_STATUS_LOCK: 未解锁\n - AWARD_STATUS_UNLOCK: 已解锁未领取\n - AWARD_STATUS_PICK: 已领取", "title": "奖励领取状态"}, "gameGetSeasonEventReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}}}, "gameGetSeasonEventRsp": {"type": "object", "properties": {"eventList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/GetSeasonEventRspEvent"}, "title": "事件列表"}}}, "gameGroupType": {"type": "string", "enum": ["GROUP_TYPE_NONE", "GROUP_TYPE_UP", "GROUP_TYPE_KEEP", "GROUP_TYPE_DOWN"], "default": "GROUP_TYPE_NONE", "description": "- GROUP_TYPE_NONE: 无效类型\n - GROUP_TYPE_UP: 升段区\n - GROUP_TYPE_KEEP: 保段区\n - GROUP_TYPE_DOWN: 降段区", "title": "分组类型"}, "gameQuerySeasonRankReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "passback": {"type": "string", "title": "分页参数"}}}, "gameQuerySeasonRankRsp": {"type": "object", "properties": {"passback": {"type": "string", "title": "分页参数"}, "hasMore": {"type": "boolean", "title": "是否还有数据"}, "selfRank": {"$ref": "#/definitions/gameRankItem", "title": "自己排名信息(只有首页才会返回)"}, "bgImage": {"type": "string", "title": "赛季背景图"}, "rankList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameRankItem"}, "title": "排名列表"}, "seasonName": {"type": "string", "title": "赛季名称"}, "segmentName": {"type": "string", "title": "当前段位名称"}}}, "gameQuerySeasonRecordReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "passback": {"type": "string", "title": "分页参数"}}}, "gameQuerySeasonRecordRsp": {"type": "object", "properties": {"passback": {"type": "string", "title": "分页参数"}, "hasMore": {"type": "boolean", "title": "是否还有数据"}, "seasonList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QuerySeasonRecordRspSeasonHistory"}, "title": "历史赛季列表"}, "bgImage": {"type": "string", "title": "赛季背景图"}, "segmentName": {"type": "string", "title": "当前段位名称"}}}, "gameQuerySeasonSegmentReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "seasonID": {"type": "integer", "format": "int32", "title": "小于等于零，查当前最新赛季"}}}, "gameQuerySeasonSegmentRsp": {"type": "object", "properties": {"seasonSegmentInfo": {"$ref": "#/definitions/gameSeasonSegmentInfo", "title": "赛季段位信息"}, "selfAvatar": {"type": "string", "title": "本人头像"}, "topRankAvatar": {"type": "array", "items": {"type": "string"}, "title": "巅峰榜top3头像"}, "avatarFrame": {"type": "string", "title": "本人头像框"}, "hasEvent": {"type": "boolean", "title": "是否有事件"}, "status": {"$ref": "#/definitions/gameRankStatus", "title": "排名开放状态，参见：game_segment_comm.RankStatus"}}}, "gameQuerySegmentAwardReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "seasonID": {"type": "integer", "format": "int32", "title": "赛季ID"}}}, "gameQuerySegmentAwardRsp": {"type": "object", "properties": {"awardList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QuerySegmentAwardRspSegmentAwardItem"}}, "awardNum": {"type": "integer", "format": "int32", "title": "待领取奖励数量，0-表示没有"}, "segmentName": {"type": "string", "title": "当前段位名称"}}}, "gameQuerySegmentRankReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "rankType": {"$ref": "#/definitions/gameRankType", "title": "本期/上一期，参见：game_segment_comm.RankType"}, "passback": {"type": "string", "title": "分页参数"}}}, "gameQuerySegmentRankRsp": {"type": "object", "properties": {"passback": {"type": "string", "title": "分页参数"}, "hasMore": {"type": "boolean", "title": "是否还有数据"}, "status": {"$ref": "#/definitions/gameRankStatus", "title": "排名开放状态，参见：game_segment_comm.RankStatus"}, "selfRank": {"$ref": "#/definitions/gameRankItem", "title": "自己排名信息(只有首页才会返回)"}, "seasonSegmentInfo": {"$ref": "#/definitions/gameSeasonSegmentInfo", "title": "赛季段位信息"}, "hasAward": {"type": "boolean", "title": "段位奖励小红点(只有首页才会返回)"}, "bgImage": {"type": "string", "title": "段位背景图"}, "rankList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameRankItem"}, "title": "排名列表"}, "sectionInfo": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QuerySegmentRankRspSectionInfo"}, "title": "分段文案信息(只有首页才会返回)"}}}, "gameRankItem": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "排名(从1开始，-1表示没有进排名)"}, "nick": {"type": "string", "title": "昵称"}, "avatar": {"type": "string", "title": "头像url"}, "score": {"type": "string", "format": "int64", "title": "积分"}, "openID": {"type": "string", "title": "用户openid"}, "uid": {"type": "string", "format": "int64", "title": "用户uid"}, "isFollow": {"type": "boolean", "title": "是否已关注"}, "awardList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameAwardItem"}, "title": "奖励列表"}}}, "gameRankStatus": {"type": "string", "enum": ["RANK_STATUS_NONE", "RANK_STATUS_FOGGING", "RANK_STATUS_OPENING"], "default": "RANK_STATUS_NONE", "description": "- RANK_STATUS_NONE: 用户未参与\n - RANK_STATUS_FOGGING: 迷雾状态\n - RANK_STATUS_OPENING: 公开状态", "title": "分组排名状态"}, "gameRankType": {"type": "string", "enum": ["RANK_TYPE_NONE", "RANK_TYPE_CURR", "RANK_TYPE_PREV"], "default": "RANK_TYPE_NONE", "description": "- RANK_TYPE_NONE: 无效类型\n - RANK_TYPE_CURR: 本期\n - RANK_TYPE_PREV: 上一期", "title": "排名上一期/本期"}, "gameReceiveAwardReq": {"type": "object", "properties": {"bizAppID": {"type": "string", "title": "bizAppID=>gameAppID"}, "seasonID": {"type": "integer", "format": "int32", "title": "赛季ID"}}}, "gameReceiveAwardRsp": {"type": "object", "properties": {"ret": {"type": "integer", "format": "int32", "title": "0-成功，非0-失败"}, "msg": {"type": "string", "title": "错误提示信息"}, "awardList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameAwardItem"}, "title": "奖励列表"}}}, "gameSeasonSegmentInfo": {"type": "object", "properties": {"seasonID": {"type": "integer", "format": "int32", "title": "赛季ID（-1表示无赛季信息）"}, "seasonName": {"type": "string", "title": "赛季名称"}, "segmentName": {"type": "string", "title": "段位名称，比如：黄金III"}, "segmentIcon": {"type": "string", "title": "段位Icon"}, "rank": {"type": "integer", "format": "int32", "title": "排名(从1开始, -1:表示暂无排名)"}, "serverTs": {"type": "string", "format": "int64", "title": "服务器时间"}, "settlementTs": {"type": "string", "format": "int64", "title": "结算时间（废弃）"}, "groupType": {"$ref": "#/definitions/gameGroupType", "title": "分组类型，参见：game_segment_comm.GroupType"}, "seasonStartTime": {"type": "string", "format": "int64", "title": "赛季开始时间"}, "seasonEndTime": {"type": "string", "format": "int64", "title": "赛季结束时间"}, "seasonRecordBackground": {"type": "string", "title": "赛季回顾背景图"}, "desc": {"type": "string", "title": "周期结果描述"}, "currPeriod": {"$ref": "#/definitions/SeasonSegmentInfoPeriodInfo", "title": "当前周期（为空表示不在任何周期内）"}, "nextPeriod": {"$ref": "#/definitions/SeasonSegmentInfoPeriodInfo", "title": "下一个周期（为空表示没有下一个周期了）"}, "score": {"type": "string", "format": "int64", "title": "周期积分"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}