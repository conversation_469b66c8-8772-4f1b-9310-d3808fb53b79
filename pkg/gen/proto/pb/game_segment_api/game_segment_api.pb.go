// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_segment_api/game_segment_api.proto

package game_segment_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	game_segment "kugou_adapter_service/pkg/gen/proto/pb/game_segment"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QuerySegmentAwardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"`  //bizAppID=>gameAppID
	SeasonID int32  `protobuf:"varint,2,opt,name=seasonID,proto3" json:"seasonID,omitempty"` //赛季ID
}

func (x *QuerySegmentAwardReq) Reset() {
	*x = QuerySegmentAwardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentAwardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentAwardReq) ProtoMessage() {}

func (x *QuerySegmentAwardReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentAwardReq.ProtoReflect.Descriptor instead.
func (*QuerySegmentAwardReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{0}
}

func (x *QuerySegmentAwardReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *QuerySegmentAwardReq) GetSeasonID() int32 {
	if x != nil {
		return x.SeasonID
	}
	return 0
}

type QuerySegmentAwardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AwardList   []*QuerySegmentAwardRsp_SegmentAwardItem `protobuf:"bytes,1,rep,name=awardList,proto3" json:"awardList,omitempty"`
	AwardNum    int32                                    `protobuf:"varint,2,opt,name=awardNum,proto3" json:"awardNum,omitempty"`      //待领取奖励数量，0-表示没有
	SegmentName string                                   `protobuf:"bytes,3,opt,name=segmentName,proto3" json:"segmentName,omitempty"` //当前段位名称
}

func (x *QuerySegmentAwardRsp) Reset() {
	*x = QuerySegmentAwardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentAwardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentAwardRsp) ProtoMessage() {}

func (x *QuerySegmentAwardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentAwardRsp.ProtoReflect.Descriptor instead.
func (*QuerySegmentAwardRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{1}
}

func (x *QuerySegmentAwardRsp) GetAwardList() []*QuerySegmentAwardRsp_SegmentAwardItem {
	if x != nil {
		return x.AwardList
	}
	return nil
}

func (x *QuerySegmentAwardRsp) GetAwardNum() int32 {
	if x != nil {
		return x.AwardNum
	}
	return 0
}

func (x *QuerySegmentAwardRsp) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

type ReceiveAwardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"`  //bizAppID=>gameAppID
	SeasonID int32  `protobuf:"varint,2,opt,name=seasonID,proto3" json:"seasonID,omitempty"` //赛季ID
}

func (x *ReceiveAwardReq) Reset() {
	*x = ReceiveAwardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveAwardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveAwardReq) ProtoMessage() {}

func (x *ReceiveAwardReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveAwardReq.ProtoReflect.Descriptor instead.
func (*ReceiveAwardReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{2}
}

func (x *ReceiveAwardReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *ReceiveAwardReq) GetSeasonID() int32 {
	if x != nil {
		return x.SeasonID
	}
	return 0
}

type ReceiveAwardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       int32                     `protobuf:"varint,1,opt,name=ret,proto3" json:"ret,omitempty"`            //0-成功，非0-失败
	Msg       string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`             //错误提示信息
	AwardList []*game_segment.AwardItem `protobuf:"bytes,3,rep,name=awardList,proto3" json:"awardList,omitempty"` //奖励列表
}

func (x *ReceiveAwardRsp) Reset() {
	*x = ReceiveAwardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveAwardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveAwardRsp) ProtoMessage() {}

func (x *ReceiveAwardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveAwardRsp.ProtoReflect.Descriptor instead.
func (*ReceiveAwardRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{3}
}

func (x *ReceiveAwardRsp) GetRet() int32 {
	if x != nil {
		return x.Ret
	}
	return 0
}

func (x *ReceiveAwardRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ReceiveAwardRsp) GetAwardList() []*game_segment.AwardItem {
	if x != nil {
		return x.AwardList
	}
	return nil
}

type RankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank      int32                     `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`          //排名(从1开始，-1表示没有进排名)
	Nick      string                    `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick,omitempty"`           //昵称
	Avatar    string                    `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`       //头像url
	Score     int64                     `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`        //积分
	OpenID    string                    `protobuf:"bytes,5,opt,name=openID,proto3" json:"openID,omitempty"`       //用户openid
	Uid       int64                     `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`            //用户uid
	IsFollow  bool                      `protobuf:"varint,7,opt,name=isFollow,proto3" json:"isFollow,omitempty"`  //是否已关注
	AwardList []*game_segment.AwardItem `protobuf:"bytes,8,rep,name=awardList,proto3" json:"awardList,omitempty"` //奖励列表
}

func (x *RankItem) Reset() {
	*x = RankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankItem) ProtoMessage() {}

func (x *RankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankItem.ProtoReflect.Descriptor instead.
func (*RankItem) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{4}
}

func (x *RankItem) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RankItem) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *RankItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RankItem) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RankItem) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *RankItem) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RankItem) GetIsFollow() bool {
	if x != nil {
		return x.IsFollow
	}
	return false
}

func (x *RankItem) GetAwardList() []*game_segment.AwardItem {
	if x != nil {
		return x.AwardList
	}
	return nil
}

type QuerySeasonRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"` //bizAppID=>gameAppID
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"` //分页参数
}

func (x *QuerySeasonRankReq) Reset() {
	*x = QuerySeasonRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonRankReq) ProtoMessage() {}

func (x *QuerySeasonRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonRankReq.ProtoReflect.Descriptor instead.
func (*QuerySeasonRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{5}
}

func (x *QuerySeasonRankReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *QuerySeasonRankReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type QuerySeasonRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback    string      `protobuf:"bytes,1,opt,name=passback,proto3" json:"passback,omitempty"`       //分页参数
	HasMore     bool        `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`        //是否还有数据
	SelfRank    *RankItem   `protobuf:"bytes,3,opt,name=selfRank,proto3" json:"selfRank,omitempty"`       //自己排名信息(只有首页才会返回)
	BgImage     string      `protobuf:"bytes,4,opt,name=bgImage,proto3" json:"bgImage,omitempty"`         //赛季背景图
	RankList    []*RankItem `protobuf:"bytes,5,rep,name=rankList,proto3" json:"rankList,omitempty"`       //排名列表
	SeasonName  string      `protobuf:"bytes,6,opt,name=seasonName,proto3" json:"seasonName,omitempty"`   //赛季名称
	SegmentName string      `protobuf:"bytes,7,opt,name=segmentName,proto3" json:"segmentName,omitempty"` //当前段位名称
}

func (x *QuerySeasonRankRsp) Reset() {
	*x = QuerySeasonRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonRankRsp) ProtoMessage() {}

func (x *QuerySeasonRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonRankRsp.ProtoReflect.Descriptor instead.
func (*QuerySeasonRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{6}
}

func (x *QuerySeasonRankRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *QuerySeasonRankRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *QuerySeasonRankRsp) GetSelfRank() *RankItem {
	if x != nil {
		return x.SelfRank
	}
	return nil
}

func (x *QuerySeasonRankRsp) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *QuerySeasonRankRsp) GetRankList() []*RankItem {
	if x != nil {
		return x.RankList
	}
	return nil
}

func (x *QuerySeasonRankRsp) GetSeasonName() string {
	if x != nil {
		return x.SeasonName
	}
	return ""
}

func (x *QuerySeasonRankRsp) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

type QuerySegmentRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string                `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"`                     //bizAppID=>gameAppID
	RankType game_segment.RankType `protobuf:"varint,2,opt,name=rankType,proto3,enum=game.RankType" json:"rankType,omitempty"` //本期/上一期，参见：game_segment_comm.RankType
	Passback string                `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"`                     //分页参数
}

func (x *QuerySegmentRankReq) Reset() {
	*x = QuerySegmentRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentRankReq) ProtoMessage() {}

func (x *QuerySegmentRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentRankReq.ProtoReflect.Descriptor instead.
func (*QuerySegmentRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{7}
}

func (x *QuerySegmentRankReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *QuerySegmentRankReq) GetRankType() game_segment.RankType {
	if x != nil {
		return x.RankType
	}
	return game_segment.RankType(0)
}

func (x *QuerySegmentRankReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type SeasonSegmentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeasonID    int32  `protobuf:"varint,1,opt,name=seasonID,proto3" json:"seasonID,omitempty"`      //赛季ID（-1表示无赛季信息）
	SeasonName  string `protobuf:"bytes,2,opt,name=seasonName,proto3" json:"seasonName,omitempty"`   //赛季名称
	SegmentName string `protobuf:"bytes,3,opt,name=segmentName,proto3" json:"segmentName,omitempty"` //段位名称，比如：黄金III
	SegmentIcon string `protobuf:"bytes,4,opt,name=segmentIcon,proto3" json:"segmentIcon,omitempty"` //段位Icon
	Rank        int32  `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`              //排名(从1开始, -1:表示暂无排名)
	ServerTs    int64  `protobuf:"varint,6,opt,name=serverTs,proto3" json:"serverTs,omitempty"`      //服务器时间
	// Deprecated: Marked as deprecated in pb/game_segment_api/game_segment_api.proto.
	SettlementTs           int64                         `protobuf:"varint,7,opt,name=settlementTs,proto3" json:"settlementTs,omitempty"`                     //结算时间（废弃）
	GroupType              game_segment.GroupType        `protobuf:"varint,8,opt,name=groupType,proto3,enum=game.GroupType" json:"groupType,omitempty"`       //分组类型，参见：game_segment_comm.GroupType
	SeasonStartTime        int64                         `protobuf:"varint,9,opt,name=seasonStartTime,proto3" json:"seasonStartTime,omitempty"`               //赛季开始时间
	SeasonEndTime          int64                         `protobuf:"varint,10,opt,name=seasonEndTime,proto3" json:"seasonEndTime,omitempty"`                  //赛季结束时间
	SeasonRecordBackground string                        `protobuf:"bytes,11,opt,name=seasonRecordBackground,proto3" json:"seasonRecordBackground,omitempty"` //赛季回顾背景图
	Desc                   string                        `protobuf:"bytes,12,opt,name=desc,proto3" json:"desc,omitempty"`                                     //周期结果描述
	CurrPeriod             *SeasonSegmentInfo_PeriodInfo `protobuf:"bytes,13,opt,name=currPeriod,proto3" json:"currPeriod,omitempty"`                         //当前周期（为空表示不在任何周期内）
	NextPeriod             *SeasonSegmentInfo_PeriodInfo `protobuf:"bytes,14,opt,name=nextPeriod,proto3" json:"nextPeriod,omitempty"`                         //下一个周期（为空表示没有下一个周期了）
	Score                  int64                         `protobuf:"varint,15,opt,name=score,proto3" json:"score,omitempty"`                                  //周期积分
}

func (x *SeasonSegmentInfo) Reset() {
	*x = SeasonSegmentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeasonSegmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeasonSegmentInfo) ProtoMessage() {}

func (x *SeasonSegmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeasonSegmentInfo.ProtoReflect.Descriptor instead.
func (*SeasonSegmentInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{8}
}

func (x *SeasonSegmentInfo) GetSeasonID() int32 {
	if x != nil {
		return x.SeasonID
	}
	return 0
}

func (x *SeasonSegmentInfo) GetSeasonName() string {
	if x != nil {
		return x.SeasonName
	}
	return ""
}

func (x *SeasonSegmentInfo) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

func (x *SeasonSegmentInfo) GetSegmentIcon() string {
	if x != nil {
		return x.SegmentIcon
	}
	return ""
}

func (x *SeasonSegmentInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *SeasonSegmentInfo) GetServerTs() int64 {
	if x != nil {
		return x.ServerTs
	}
	return 0
}

// Deprecated: Marked as deprecated in pb/game_segment_api/game_segment_api.proto.
func (x *SeasonSegmentInfo) GetSettlementTs() int64 {
	if x != nil {
		return x.SettlementTs
	}
	return 0
}

func (x *SeasonSegmentInfo) GetGroupType() game_segment.GroupType {
	if x != nil {
		return x.GroupType
	}
	return game_segment.GroupType(0)
}

func (x *SeasonSegmentInfo) GetSeasonStartTime() int64 {
	if x != nil {
		return x.SeasonStartTime
	}
	return 0
}

func (x *SeasonSegmentInfo) GetSeasonEndTime() int64 {
	if x != nil {
		return x.SeasonEndTime
	}
	return 0
}

func (x *SeasonSegmentInfo) GetSeasonRecordBackground() string {
	if x != nil {
		return x.SeasonRecordBackground
	}
	return ""
}

func (x *SeasonSegmentInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SeasonSegmentInfo) GetCurrPeriod() *SeasonSegmentInfo_PeriodInfo {
	if x != nil {
		return x.CurrPeriod
	}
	return nil
}

func (x *SeasonSegmentInfo) GetNextPeriod() *SeasonSegmentInfo_PeriodInfo {
	if x != nil {
		return x.NextPeriod
	}
	return nil
}

func (x *SeasonSegmentInfo) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type QuerySegmentRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback          string                             `protobuf:"bytes,1,opt,name=passback,proto3" json:"passback,omitempty"`                   //分页参数
	HasMore           bool                               `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`                    //是否还有数据
	Status            game_segment.RankStatus            `protobuf:"varint,3,opt,name=status,proto3,enum=game.RankStatus" json:"status,omitempty"` //排名开放状态，参见：game_segment_comm.RankStatus
	SelfRank          *RankItem                          `protobuf:"bytes,4,opt,name=selfRank,proto3" json:"selfRank,omitempty"`                   //自己排名信息(只有首页才会返回)
	SeasonSegmentInfo *SeasonSegmentInfo                 `protobuf:"bytes,5,opt,name=seasonSegmentInfo,proto3" json:"seasonSegmentInfo,omitempty"` //赛季段位信息
	HasAward          bool                               `protobuf:"varint,6,opt,name=hasAward,proto3" json:"hasAward,omitempty"`                  //段位奖励小红点(只有首页才会返回)
	BgImage           string                             `protobuf:"bytes,7,opt,name=bgImage,proto3" json:"bgImage,omitempty"`                     //段位背景图
	RankList          []*RankItem                        `protobuf:"bytes,8,rep,name=rankList,proto3" json:"rankList,omitempty"`                   //排名列表
	SectionInfo       []*QuerySegmentRankRsp_SectionInfo `protobuf:"bytes,9,rep,name=sectionInfo,proto3" json:"sectionInfo,omitempty"`             //分段文案信息(只有首页才会返回)
}

func (x *QuerySegmentRankRsp) Reset() {
	*x = QuerySegmentRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentRankRsp) ProtoMessage() {}

func (x *QuerySegmentRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentRankRsp.ProtoReflect.Descriptor instead.
func (*QuerySegmentRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{9}
}

func (x *QuerySegmentRankRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *QuerySegmentRankRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *QuerySegmentRankRsp) GetStatus() game_segment.RankStatus {
	if x != nil {
		return x.Status
	}
	return game_segment.RankStatus(0)
}

func (x *QuerySegmentRankRsp) GetSelfRank() *RankItem {
	if x != nil {
		return x.SelfRank
	}
	return nil
}

func (x *QuerySegmentRankRsp) GetSeasonSegmentInfo() *SeasonSegmentInfo {
	if x != nil {
		return x.SeasonSegmentInfo
	}
	return nil
}

func (x *QuerySegmentRankRsp) GetHasAward() bool {
	if x != nil {
		return x.HasAward
	}
	return false
}

func (x *QuerySegmentRankRsp) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *QuerySegmentRankRsp) GetRankList() []*RankItem {
	if x != nil {
		return x.RankList
	}
	return nil
}

func (x *QuerySegmentRankRsp) GetSectionInfo() []*QuerySegmentRankRsp_SectionInfo {
	if x != nil {
		return x.SectionInfo
	}
	return nil
}

type QuerySeasonSegmentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"`  //bizAppID=>gameAppID
	SeasonID int32  `protobuf:"varint,2,opt,name=seasonID,proto3" json:"seasonID,omitempty"` //小于等于零，查当前最新赛季
}

func (x *QuerySeasonSegmentReq) Reset() {
	*x = QuerySeasonSegmentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonSegmentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonSegmentReq) ProtoMessage() {}

func (x *QuerySeasonSegmentReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonSegmentReq.ProtoReflect.Descriptor instead.
func (*QuerySeasonSegmentReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{10}
}

func (x *QuerySeasonSegmentReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *QuerySeasonSegmentReq) GetSeasonID() int32 {
	if x != nil {
		return x.SeasonID
	}
	return 0
}

type QuerySeasonSegmentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeasonSegmentInfo *SeasonSegmentInfo      `protobuf:"bytes,1,opt,name=seasonSegmentInfo,proto3" json:"seasonSegmentInfo,omitempty"` //赛季段位信息
	SelfAvatar        string                  `protobuf:"bytes,2,opt,name=selfAvatar,proto3" json:"selfAvatar,omitempty"`               //本人头像
	TopRankAvatar     []string                `protobuf:"bytes,3,rep,name=topRankAvatar,proto3" json:"topRankAvatar,omitempty"`         //巅峰榜top3头像
	AvatarFrame       string                  `protobuf:"bytes,4,opt,name=avatarFrame,proto3" json:"avatarFrame,omitempty"`             //本人头像框
	HasEvent          bool                    `protobuf:"varint,5,opt,name=hasEvent,proto3" json:"hasEvent,omitempty"`                  //是否有事件
	Status            game_segment.RankStatus `protobuf:"varint,6,opt,name=status,proto3,enum=game.RankStatus" json:"status,omitempty"` //排名开放状态，参见：game_segment_comm.RankStatus
}

func (x *QuerySeasonSegmentRsp) Reset() {
	*x = QuerySeasonSegmentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonSegmentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonSegmentRsp) ProtoMessage() {}

func (x *QuerySeasonSegmentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonSegmentRsp.ProtoReflect.Descriptor instead.
func (*QuerySeasonSegmentRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{11}
}

func (x *QuerySeasonSegmentRsp) GetSeasonSegmentInfo() *SeasonSegmentInfo {
	if x != nil {
		return x.SeasonSegmentInfo
	}
	return nil
}

func (x *QuerySeasonSegmentRsp) GetSelfAvatar() string {
	if x != nil {
		return x.SelfAvatar
	}
	return ""
}

func (x *QuerySeasonSegmentRsp) GetTopRankAvatar() []string {
	if x != nil {
		return x.TopRankAvatar
	}
	return nil
}

func (x *QuerySeasonSegmentRsp) GetAvatarFrame() string {
	if x != nil {
		return x.AvatarFrame
	}
	return ""
}

func (x *QuerySeasonSegmentRsp) GetHasEvent() bool {
	if x != nil {
		return x.HasEvent
	}
	return false
}

func (x *QuerySeasonSegmentRsp) GetStatus() game_segment.RankStatus {
	if x != nil {
		return x.Status
	}
	return game_segment.RankStatus(0)
}

type QuerySeasonRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"` //bizAppID=>gameAppID
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"` //分页参数
}

func (x *QuerySeasonRecordReq) Reset() {
	*x = QuerySeasonRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonRecordReq) ProtoMessage() {}

func (x *QuerySeasonRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonRecordReq.ProtoReflect.Descriptor instead.
func (*QuerySeasonRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{12}
}

func (x *QuerySeasonRecordReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *QuerySeasonRecordReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type QuerySeasonRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback    string                                `protobuf:"bytes,1,opt,name=passback,proto3" json:"passback,omitempty"`       //分页参数
	HasMore     bool                                  `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`        //是否还有数据
	SeasonList  []*QuerySeasonRecordRsp_SeasonHistory `protobuf:"bytes,3,rep,name=seasonList,proto3" json:"seasonList,omitempty"`   //历史赛季列表
	BgImage     string                                `protobuf:"bytes,4,opt,name=bgImage,proto3" json:"bgImage,omitempty"`         //赛季背景图
	SegmentName string                                `protobuf:"bytes,5,opt,name=segmentName,proto3" json:"segmentName,omitempty"` //当前段位名称
}

func (x *QuerySeasonRecordRsp) Reset() {
	*x = QuerySeasonRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonRecordRsp) ProtoMessage() {}

func (x *QuerySeasonRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonRecordRsp.ProtoReflect.Descriptor instead.
func (*QuerySeasonRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{13}
}

func (x *QuerySeasonRecordRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *QuerySeasonRecordRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *QuerySeasonRecordRsp) GetSeasonList() []*QuerySeasonRecordRsp_SeasonHistory {
	if x != nil {
		return x.SeasonList
	}
	return nil
}

func (x *QuerySeasonRecordRsp) GetBgImage() string {
	if x != nil {
		return x.BgImage
	}
	return ""
}

func (x *QuerySeasonRecordRsp) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

type GetSeasonEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID string `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"` //bizAppID=>gameAppID
}

func (x *GetSeasonEventReq) Reset() {
	*x = GetSeasonEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSeasonEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSeasonEventReq) ProtoMessage() {}

func (x *GetSeasonEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSeasonEventReq.ProtoReflect.Descriptor instead.
func (*GetSeasonEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{14}
}

func (x *GetSeasonEventReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

type GetSeasonEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventList []*GetSeasonEventRsp_Event `protobuf:"bytes,1,rep,name=eventList,proto3" json:"eventList,omitempty"` //事件列表
}

func (x *GetSeasonEventRsp) Reset() {
	*x = GetSeasonEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSeasonEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSeasonEventRsp) ProtoMessage() {}

func (x *GetSeasonEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSeasonEventRsp.ProtoReflect.Descriptor instead.
func (*GetSeasonEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetSeasonEventRsp) GetEventList() []*GetSeasonEventRsp_Event {
	if x != nil {
		return x.EventList
	}
	return nil
}

type AckSeasonEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizAppID    string  `protobuf:"bytes,1,opt,name=bizAppID,proto3" json:"bizAppID,omitempty"`               //bizAppID=>gameAppID
	EventIDList []int32 `protobuf:"varint,2,rep,packed,name=eventIDList,proto3" json:"eventIDList,omitempty"` //事件ID列表
}

func (x *AckSeasonEventReq) Reset() {
	*x = AckSeasonEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AckSeasonEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AckSeasonEventReq) ProtoMessage() {}

func (x *AckSeasonEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AckSeasonEventReq.ProtoReflect.Descriptor instead.
func (*AckSeasonEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{16}
}

func (x *AckSeasonEventReq) GetBizAppID() string {
	if x != nil {
		return x.BizAppID
	}
	return ""
}

func (x *AckSeasonEventReq) GetEventIDList() []int32 {
	if x != nil {
		return x.EventIDList
	}
	return nil
}

type AckSeasonEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AckSeasonEventRsp) Reset() {
	*x = AckSeasonEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AckSeasonEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AckSeasonEventRsp) ProtoMessage() {}

func (x *AckSeasonEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AckSeasonEventRsp.ProtoReflect.Descriptor instead.
func (*AckSeasonEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{17}
}

type QuerySegmentAwardRsp_SubSegAwardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SegmentID int32                     `protobuf:"varint,1,opt,name=segmentID,proto3" json:"segmentID,omitempty"`                 //段位ID
	SubName   string                    `protobuf:"bytes,2,opt,name=subName,proto3" json:"subName,omitempty"`                      //子段位名称，比如：III
	SubIcon   string                    `protobuf:"bytes,3,opt,name=subIcon,proto3" json:"subIcon,omitempty"`                      //子段位Icon
	Status    game_segment.AwardStatus  `protobuf:"varint,4,opt,name=status,proto3,enum=game.AwardStatus" json:"status,omitempty"` //奖励状态，参见：game_segment_comm.AwardStatus
	AwardList []*game_segment.AwardItem `protobuf:"bytes,5,rep,name=awardList,proto3" json:"awardList,omitempty"`                  //奖励列表
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) Reset() {
	*x = QuerySegmentAwardRsp_SubSegAwardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentAwardRsp_SubSegAwardItem) ProtoMessage() {}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentAwardRsp_SubSegAwardItem.ProtoReflect.Descriptor instead.
func (*QuerySegmentAwardRsp_SubSegAwardItem) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) GetSegmentID() int32 {
	if x != nil {
		return x.SegmentID
	}
	return 0
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) GetSubName() string {
	if x != nil {
		return x.SubName
	}
	return ""
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) GetSubIcon() string {
	if x != nil {
		return x.SubIcon
	}
	return ""
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) GetStatus() game_segment.AwardStatus {
	if x != nil {
		return x.Status
	}
	return game_segment.AwardStatus(0)
}

func (x *QuerySegmentAwardRsp_SubSegAwardItem) GetAwardList() []*game_segment.AwardItem {
	if x != nil {
		return x.AwardList
	}
	return nil
}

type QuerySegmentAwardRsp_SegmentAwardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MainName       string                                  `protobuf:"bytes,2,opt,name=mainName,proto3" json:"mainName,omitempty"`             //主段位名称，比如：黄金I
	MainIcon       string                                  `protobuf:"bytes,3,opt,name=mainIcon,proto3" json:"mainIcon,omitempty"`             //主段位Icon
	SubSegmentList []*QuerySegmentAwardRsp_SubSegAwardItem `protobuf:"bytes,4,rep,name=subSegmentList,proto3" json:"subSegmentList,omitempty"` //子段位列表
	HasAward       bool                                    `protobuf:"varint,5,opt,name=hasAward,proto3" json:"hasAward,omitempty"`            //段位奖励小红点
}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) Reset() {
	*x = QuerySegmentAwardRsp_SegmentAwardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentAwardRsp_SegmentAwardItem) ProtoMessage() {}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentAwardRsp_SegmentAwardItem.ProtoReflect.Descriptor instead.
func (*QuerySegmentAwardRsp_SegmentAwardItem) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) GetMainName() string {
	if x != nil {
		return x.MainName
	}
	return ""
}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) GetMainIcon() string {
	if x != nil {
		return x.MainIcon
	}
	return ""
}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) GetSubSegmentList() []*QuerySegmentAwardRsp_SubSegAwardItem {
	if x != nil {
		return x.SubSegmentList
	}
	return nil
}

func (x *QuerySegmentAwardRsp_SegmentAwardItem) GetHasAward() bool {
	if x != nil {
		return x.HasAward
	}
	return false
}

type SeasonSegmentInfo_PeriodInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime int64 `protobuf:"varint,1,opt,name=startTime,proto3" json:"startTime,omitempty"` //周期开始时间
	EndTime   int64 `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`     //周期结束时间
}

func (x *SeasonSegmentInfo_PeriodInfo) Reset() {
	*x = SeasonSegmentInfo_PeriodInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeasonSegmentInfo_PeriodInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeasonSegmentInfo_PeriodInfo) ProtoMessage() {}

func (x *SeasonSegmentInfo_PeriodInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeasonSegmentInfo_PeriodInfo.ProtoReflect.Descriptor instead.
func (*SeasonSegmentInfo_PeriodInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *SeasonSegmentInfo_PeriodInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *SeasonSegmentInfo_PeriodInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type QuerySegmentRankRsp_SectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SectionName string                 `protobuf:"bytes,1,opt,name=sectionName,proto3" json:"sectionName,omitempty"`                  //分段名称, 例如: 升段区, 保段区 or 降段区
	SectionDesc string                 `protobuf:"bytes,2,opt,name=sectionDesc,proto3" json:"sectionDesc,omitempty"`                  //分段描述, 例如: 排名前20名可升至白银I
	GroupType   game_segment.GroupType `protobuf:"varint,3,opt,name=groupType,proto3,enum=game.GroupType" json:"groupType,omitempty"` //分组类型，参见：game_segment_comm.GroupType
	RankLine    int32                  `protobuf:"varint,4,opt,name=rankLine,proto3" json:"rankLine,omitempty"`                       //进入该分组的排名下限(userRank <= rankLine)
}

func (x *QuerySegmentRankRsp_SectionInfo) Reset() {
	*x = QuerySegmentRankRsp_SectionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySegmentRankRsp_SectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySegmentRankRsp_SectionInfo) ProtoMessage() {}

func (x *QuerySegmentRankRsp_SectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySegmentRankRsp_SectionInfo.ProtoReflect.Descriptor instead.
func (*QuerySegmentRankRsp_SectionInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{9, 0}
}

func (x *QuerySegmentRankRsp_SectionInfo) GetSectionName() string {
	if x != nil {
		return x.SectionName
	}
	return ""
}

func (x *QuerySegmentRankRsp_SectionInfo) GetSectionDesc() string {
	if x != nil {
		return x.SectionDesc
	}
	return ""
}

func (x *QuerySegmentRankRsp_SectionInfo) GetGroupType() game_segment.GroupType {
	if x != nil {
		return x.GroupType
	}
	return game_segment.GroupType(0)
}

func (x *QuerySegmentRankRsp_SectionInfo) GetRankLine() int32 {
	if x != nil {
		return x.RankLine
	}
	return 0
}

type QuerySeasonRecordRsp_SeasonHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeasonName  string `protobuf:"bytes,1,opt,name=seasonName,proto3" json:"seasonName,omitempty"`   //赛季名称
	StartTime   int64  `protobuf:"varint,2,opt,name=startTime,proto3" json:"startTime,omitempty"`    //赛季开始时间
	EndTime     int64  `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime,omitempty"`        //赛季结束时间
	SegmentName string `protobuf:"bytes,4,opt,name=segmentName,proto3" json:"segmentName,omitempty"` //最高段位名称，比如：黄金III
	SegmentIcon string `protobuf:"bytes,5,opt,name=segmentIcon,proto3" json:"segmentIcon,omitempty"` //最高段位Icon
}

func (x *QuerySeasonRecordRsp_SeasonHistory) Reset() {
	*x = QuerySeasonRecordRsp_SeasonHistory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySeasonRecordRsp_SeasonHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySeasonRecordRsp_SeasonHistory) ProtoMessage() {}

func (x *QuerySeasonRecordRsp_SeasonHistory) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySeasonRecordRsp_SeasonHistory.ProtoReflect.Descriptor instead.
func (*QuerySeasonRecordRsp_SeasonHistory) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{13, 0}
}

func (x *QuerySeasonRecordRsp_SeasonHistory) GetSeasonName() string {
	if x != nil {
		return x.SeasonName
	}
	return ""
}

func (x *QuerySeasonRecordRsp_SeasonHistory) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *QuerySeasonRecordRsp_SeasonHistory) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *QuerySeasonRecordRsp_SeasonHistory) GetSegmentName() string {
	if x != nil {
		return x.SegmentName
	}
	return ""
}

func (x *QuerySeasonRecordRsp_SeasonHistory) GetSegmentIcon() string {
	if x != nil {
		return x.SegmentIcon
	}
	return ""
}

type GetSeasonEventRsp_Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventID int32  `protobuf:"varint,1,opt,name=eventID,proto3" json:"eventID,omitempty"` //事件ID
	Cmd     int32  `protobuf:"varint,2,opt,name=cmd,proto3" json:"cmd,omitempty"`         //主事件
	SubCmd  int32  `protobuf:"varint,3,opt,name=subCmd,proto3" json:"subCmd,omitempty"`   //子事件
	Data    string `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`        //事件数据-json类型，根据cmd-subcmd找到对应结构解析
}

func (x *GetSeasonEventRsp_Event) Reset() {
	*x = GetSeasonEventRsp_Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSeasonEventRsp_Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSeasonEventRsp_Event) ProtoMessage() {}

func (x *GetSeasonEventRsp_Event) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_segment_api_game_segment_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSeasonEventRsp_Event.ProtoReflect.Descriptor instead.
func (*GetSeasonEventRsp_Event) Descriptor() ([]byte, []int) {
	return file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP(), []int{15, 0}
}

func (x *GetSeasonEventRsp_Event) GetEventID() int32 {
	if x != nil {
		return x.EventID
	}
	return 0
}

func (x *GetSeasonEventRsp_Event) GetCmd() int32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *GetSeasonEventRsp_Event) GetSubCmd() int32 {
	if x != nil {
		return x.SubCmd
	}
	return 0
}

func (x *GetSeasonEventRsp_Event) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

var File_pb_game_segment_api_game_segment_api_proto protoreflect.FileDescriptor

var file_pb_game_segment_api_game_segment_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61,
	0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4e, 0x0a, 0x14, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x7a, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x9c, 0x04, 0x0a, 0x14,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x73, 0x70, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xbd, 0x01,
	0x0a, 0x0f, 0x53, 0x75, 0x62, 0x53, 0x65, 0x67, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d,
	0x0a, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xba, 0x01,
	0x0a, 0x10, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x0e, 0x73, 0x75,
	0x62, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x53,
	0x75, 0x62, 0x53, 0x65, 0x67, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e,
	0x73, 0x75, 0x62, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x68, 0x61, 0x73, 0x41, 0x77, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x68, 0x61, 0x73, 0x41, 0x77, 0x61, 0x72, 0x64, 0x22, 0x49, 0x0a, 0x0f, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x62, 0x69, 0x7a, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x69, 0x7a, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x64, 0x0a, 0x0f, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x2d, 0x0a, 0x09,
	0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xd5, 0x01, 0x0a, 0x08,
	0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x69, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x69, 0x63, 0x6b,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x12, 0x2d, 0x0a, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x4c, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x7a,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x22, 0xfe, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x2a,
	0x0a, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x52, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x67,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x61,
	0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x79, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x7a,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52,
	0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x9a, 0x05,
	0x0a, 0x11, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x44, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x63, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x54, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x54, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x73, 0x12, 0x2d, 0x0a, 0x09, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x16, 0x73, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x42, 0x0a, 0x0a, 0x63, 0x75, 0x72, 0x72, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x63, 0x75, 0x72, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x42, 0x0a, 0x0a, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x1a, 0x44, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb2, 0x04, 0x0a, 0x13, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18,
	0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x52, 0x61, 0x6e, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x61, 0x6e, 0x6b,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x45,
	0x0a, 0x11, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x11, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x72,
	0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x72,
	0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x1a, 0x9c, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x73,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x73, 0x63, 0x12, 0x2d, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x6e, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x6e, 0x65, 0x22,
	0x4f, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x41,
	0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x7a, 0x41,
	0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x49, 0x44,
	0x22, 0x8c, 0x02, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x11, 0x73, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11,
	0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x6c, 0x66, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x66, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x6f, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x6f, 0x70, 0x52, 0x61, 0x6e,
	0x6b, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x73,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x61, 0x6e,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x4e, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x7a, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22,
	0x80, 0x03, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x48,
	0x0a, 0x0a, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x53,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x73, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x67, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xab, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x63,
	0x6f, 0x6e, 0x22, 0x2f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x7a, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x22, 0xb1, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x5f, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x75, 0x62, 0x43, 0x6d, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x75, 0x62,
	0x43, 0x6d, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x11, 0x41, 0x63, 0x6b, 0x53, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x62, 0x69, 0x7a, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x69, 0x7a, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x13, 0x0a, 0x11, 0x41, 0x63,
	0x6b, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x32,
	0xd3, 0x04, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x41, 0x50, 0x49, 0x12, 0x4b, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73,
	0x70, 0x12, 0x3c, 0x0a, 0x0c, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x12, 0x15, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12,
	0x45, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x61,
	0x6e, 0x6b, 0x12, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x19, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x61,
	0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70,
	0x12, 0x4e, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53,
	0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x4b, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x42, 0x0a, 0x0e, 0x41, 0x63, 0x6b, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x63, 0x6b, 0x53, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x41, 0x63, 0x6b, 0x53, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x48, 0x5a, 0x46, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_segment_api_game_segment_api_proto_rawDescOnce sync.Once
	file_pb_game_segment_api_game_segment_api_proto_rawDescData = file_pb_game_segment_api_game_segment_api_proto_rawDesc
)

func file_pb_game_segment_api_game_segment_api_proto_rawDescGZIP() []byte {
	file_pb_game_segment_api_game_segment_api_proto_rawDescOnce.Do(func() {
		file_pb_game_segment_api_game_segment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_segment_api_game_segment_api_proto_rawDescData)
	})
	return file_pb_game_segment_api_game_segment_api_proto_rawDescData
}

var file_pb_game_segment_api_game_segment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_pb_game_segment_api_game_segment_api_proto_goTypes = []interface{}{
	(*QuerySegmentAwardReq)(nil),                  // 0: game.QuerySegmentAwardReq
	(*QuerySegmentAwardRsp)(nil),                  // 1: game.QuerySegmentAwardRsp
	(*ReceiveAwardReq)(nil),                       // 2: game.ReceiveAwardReq
	(*ReceiveAwardRsp)(nil),                       // 3: game.ReceiveAwardRsp
	(*RankItem)(nil),                              // 4: game.RankItem
	(*QuerySeasonRankReq)(nil),                    // 5: game.QuerySeasonRankReq
	(*QuerySeasonRankRsp)(nil),                    // 6: game.QuerySeasonRankRsp
	(*QuerySegmentRankReq)(nil),                   // 7: game.QuerySegmentRankReq
	(*SeasonSegmentInfo)(nil),                     // 8: game.SeasonSegmentInfo
	(*QuerySegmentRankRsp)(nil),                   // 9: game.QuerySegmentRankRsp
	(*QuerySeasonSegmentReq)(nil),                 // 10: game.QuerySeasonSegmentReq
	(*QuerySeasonSegmentRsp)(nil),                 // 11: game.QuerySeasonSegmentRsp
	(*QuerySeasonRecordReq)(nil),                  // 12: game.QuerySeasonRecordReq
	(*QuerySeasonRecordRsp)(nil),                  // 13: game.QuerySeasonRecordRsp
	(*GetSeasonEventReq)(nil),                     // 14: game.GetSeasonEventReq
	(*GetSeasonEventRsp)(nil),                     // 15: game.GetSeasonEventRsp
	(*AckSeasonEventReq)(nil),                     // 16: game.AckSeasonEventReq
	(*AckSeasonEventRsp)(nil),                     // 17: game.AckSeasonEventRsp
	(*QuerySegmentAwardRsp_SubSegAwardItem)(nil),  // 18: game.QuerySegmentAwardRsp.SubSegAwardItem
	(*QuerySegmentAwardRsp_SegmentAwardItem)(nil), // 19: game.QuerySegmentAwardRsp.SegmentAwardItem
	(*SeasonSegmentInfo_PeriodInfo)(nil),          // 20: game.SeasonSegmentInfo.PeriodInfo
	(*QuerySegmentRankRsp_SectionInfo)(nil),       // 21: game.QuerySegmentRankRsp.SectionInfo
	(*QuerySeasonRecordRsp_SeasonHistory)(nil),    // 22: game.QuerySeasonRecordRsp.SeasonHistory
	(*GetSeasonEventRsp_Event)(nil),               // 23: game.GetSeasonEventRsp.Event
	(*game_segment.AwardItem)(nil),                // 24: game.AwardItem
	(game_segment.RankType)(0),                    // 25: game.RankType
	(game_segment.GroupType)(0),                   // 26: game.GroupType
	(game_segment.RankStatus)(0),                  // 27: game.RankStatus
	(game_segment.AwardStatus)(0),                 // 28: game.AwardStatus
}
var file_pb_game_segment_api_game_segment_api_proto_depIdxs = []int32{
	19, // 0: game.QuerySegmentAwardRsp.awardList:type_name -> game.QuerySegmentAwardRsp.SegmentAwardItem
	24, // 1: game.ReceiveAwardRsp.awardList:type_name -> game.AwardItem
	24, // 2: game.RankItem.awardList:type_name -> game.AwardItem
	4,  // 3: game.QuerySeasonRankRsp.selfRank:type_name -> game.RankItem
	4,  // 4: game.QuerySeasonRankRsp.rankList:type_name -> game.RankItem
	25, // 5: game.QuerySegmentRankReq.rankType:type_name -> game.RankType
	26, // 6: game.SeasonSegmentInfo.groupType:type_name -> game.GroupType
	20, // 7: game.SeasonSegmentInfo.currPeriod:type_name -> game.SeasonSegmentInfo.PeriodInfo
	20, // 8: game.SeasonSegmentInfo.nextPeriod:type_name -> game.SeasonSegmentInfo.PeriodInfo
	27, // 9: game.QuerySegmentRankRsp.status:type_name -> game.RankStatus
	4,  // 10: game.QuerySegmentRankRsp.selfRank:type_name -> game.RankItem
	8,  // 11: game.QuerySegmentRankRsp.seasonSegmentInfo:type_name -> game.SeasonSegmentInfo
	4,  // 12: game.QuerySegmentRankRsp.rankList:type_name -> game.RankItem
	21, // 13: game.QuerySegmentRankRsp.sectionInfo:type_name -> game.QuerySegmentRankRsp.SectionInfo
	8,  // 14: game.QuerySeasonSegmentRsp.seasonSegmentInfo:type_name -> game.SeasonSegmentInfo
	27, // 15: game.QuerySeasonSegmentRsp.status:type_name -> game.RankStatus
	22, // 16: game.QuerySeasonRecordRsp.seasonList:type_name -> game.QuerySeasonRecordRsp.SeasonHistory
	23, // 17: game.GetSeasonEventRsp.eventList:type_name -> game.GetSeasonEventRsp.Event
	28, // 18: game.QuerySegmentAwardRsp.SubSegAwardItem.status:type_name -> game.AwardStatus
	24, // 19: game.QuerySegmentAwardRsp.SubSegAwardItem.awardList:type_name -> game.AwardItem
	18, // 20: game.QuerySegmentAwardRsp.SegmentAwardItem.subSegmentList:type_name -> game.QuerySegmentAwardRsp.SubSegAwardItem
	26, // 21: game.QuerySegmentRankRsp.SectionInfo.groupType:type_name -> game.GroupType
	0,  // 22: game.SeasonSegmentAPI.QuerySegmentAward:input_type -> game.QuerySegmentAwardReq
	2,  // 23: game.SeasonSegmentAPI.ReceiveAward:input_type -> game.ReceiveAwardReq
	5,  // 24: game.SeasonSegmentAPI.QuerySeasonRank:input_type -> game.QuerySeasonRankReq
	7,  // 25: game.SeasonSegmentAPI.QuerySegmentRank:input_type -> game.QuerySegmentRankReq
	10, // 26: game.SeasonSegmentAPI.QuerySeasonSegment:input_type -> game.QuerySeasonSegmentReq
	12, // 27: game.SeasonSegmentAPI.QuerySeasonRecord:input_type -> game.QuerySeasonRecordReq
	14, // 28: game.SeasonSegmentAPI.GetSeasonEvent:input_type -> game.GetSeasonEventReq
	16, // 29: game.SeasonSegmentAPI.AckSeasonEvent:input_type -> game.AckSeasonEventReq
	1,  // 30: game.SeasonSegmentAPI.QuerySegmentAward:output_type -> game.QuerySegmentAwardRsp
	3,  // 31: game.SeasonSegmentAPI.ReceiveAward:output_type -> game.ReceiveAwardRsp
	6,  // 32: game.SeasonSegmentAPI.QuerySeasonRank:output_type -> game.QuerySeasonRankRsp
	9,  // 33: game.SeasonSegmentAPI.QuerySegmentRank:output_type -> game.QuerySegmentRankRsp
	11, // 34: game.SeasonSegmentAPI.QuerySeasonSegment:output_type -> game.QuerySeasonSegmentRsp
	13, // 35: game.SeasonSegmentAPI.QuerySeasonRecord:output_type -> game.QuerySeasonRecordRsp
	15, // 36: game.SeasonSegmentAPI.GetSeasonEvent:output_type -> game.GetSeasonEventRsp
	17, // 37: game.SeasonSegmentAPI.AckSeasonEvent:output_type -> game.AckSeasonEventRsp
	30, // [30:38] is the sub-list for method output_type
	22, // [22:30] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_pb_game_segment_api_game_segment_api_proto_init() }
func file_pb_game_segment_api_game_segment_api_proto_init() {
	if File_pb_game_segment_api_game_segment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentAwardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentAwardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveAwardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveAwardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeasonSegmentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonSegmentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonSegmentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSeasonEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSeasonEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AckSeasonEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AckSeasonEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentAwardRsp_SubSegAwardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentAwardRsp_SegmentAwardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeasonSegmentInfo_PeriodInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySegmentRankRsp_SectionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySeasonRecordRsp_SeasonHistory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_segment_api_game_segment_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSeasonEventRsp_Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_segment_api_game_segment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_segment_api_game_segment_api_proto_goTypes,
		DependencyIndexes: file_pb_game_segment_api_game_segment_api_proto_depIdxs,
		MessageInfos:      file_pb_game_segment_api_game_segment_api_proto_msgTypes,
	}.Build()
	File_pb_game_segment_api_game_segment_api_proto = out.File
	file_pb_game_segment_api_game_segment_api_proto_rawDesc = nil
	file_pb_game_segment_api_game_segment_api_proto_goTypes = nil
	file_pb_game_segment_api_game_segment_api_proto_depIdxs = nil
}
