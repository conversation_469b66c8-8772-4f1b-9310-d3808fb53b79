// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/open_pay_extension_api/extension_api.proto

package open_pay_extension_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OperatingSystem int32

const (
	OperatingSystem_OperatingSystemUnknown OperatingSystem = 0
	OperatingSystem_OperatingSystemAndroid OperatingSystem = 1
	OperatingSystem_OperatingSystemIOS     OperatingSystem = 2
)

// Enum value maps for OperatingSystem.
var (
	OperatingSystem_name = map[int32]string{
		0: "OperatingSystemUnknown",
		1: "OperatingSystemAndroid",
		2: "OperatingSystemIOS",
	}
	OperatingSystem_value = map[string]int32{
		"OperatingSystemUnknown": 0,
		"OperatingSystemAndroid": 1,
		"OperatingSystemIOS":     2,
	}
)

func (x OperatingSystem) Enum() *OperatingSystem {
	p := new(OperatingSystem)
	*p = x
	return p
}

func (x OperatingSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperatingSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_open_pay_extension_api_extension_api_proto_enumTypes[0].Descriptor()
}

func (OperatingSystem) Type() protoreflect.EnumType {
	return &file_pb_open_pay_extension_api_extension_api_proto_enumTypes[0]
}

func (x OperatingSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperatingSystem.Descriptor instead.
func (OperatingSystem) EnumDescriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{0}
}

type LockProductReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExtensionId   int64  `protobuf:"varint,1,opt,name=extensionId,proto3" json:"extensionId,omitempty"`
	AppId         string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId     string `protobuf:"bytes,4,opt,name=productId,proto3" json:"productId,omitempty"`         // 商品 id
	TransactionId string `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"` // 唯一订单 id
	ExpireTime    int64  `protobuf:"varint,6,opt,name=expireTime,proto3" json:"expireTime,omitempty"`      // 过期时间 0:不过期
}

func (x *LockProductReq) Reset() {
	*x = LockProductReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LockProductReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LockProductReq) ProtoMessage() {}

func (x *LockProductReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LockProductReq.ProtoReflect.Descriptor instead.
func (*LockProductReq) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{0}
}

func (x *LockProductReq) GetExtensionId() int64 {
	if x != nil {
		return x.ExtensionId
	}
	return 0
}

func (x *LockProductReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *LockProductReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *LockProductReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *LockProductReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *LockProductReq) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

type LockProductRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LockProductRsp) Reset() {
	*x = LockProductRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LockProductRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LockProductRsp) ProtoMessage() {}

func (x *LockProductRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LockProductRsp.ProtoReflect.Descriptor instead.
func (*LockProductRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{1}
}

type UnlockProductReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExtensionId   int64  `protobuf:"varint,1,opt,name=extensionId,proto3" json:"extensionId,omitempty"`
	AppId         string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId     string `protobuf:"bytes,4,opt,name=productId,proto3" json:"productId,omitempty"`         // 商品 id
	TransactionId string `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"` // 唯一订单 id
}

func (x *UnlockProductReq) Reset() {
	*x = UnlockProductReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlockProductReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlockProductReq) ProtoMessage() {}

func (x *UnlockProductReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlockProductReq.ProtoReflect.Descriptor instead.
func (*UnlockProductReq) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{2}
}

func (x *UnlockProductReq) GetExtensionId() int64 {
	if x != nil {
		return x.ExtensionId
	}
	return 0
}

func (x *UnlockProductReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *UnlockProductReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *UnlockProductReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *UnlockProductReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type UnlockProductRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnlockProductRsp) Reset() {
	*x = UnlockProductRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnlockProductRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnlockProductRsp) ProtoMessage() {}

func (x *UnlockProductRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnlockProductRsp.ProtoReflect.Descriptor instead.
func (*UnlockProductRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{3}
}

type CheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExtensionId int64             `protobuf:"varint,1,opt,name=extensionId,proto3" json:"extensionId,omitempty"`
	AppId       string            `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId      string            `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId   string            `protobuf:"bytes,4,opt,name=productId,proto3" json:"productId,omitempty"`                                // 商品 id
	Price       int64             `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`                                       // 价格
	GreenId     int64             `protobuf:"varint,6,opt,name=greenId,proto3" json:"greenId,omitempty"`                                   // 绿钻 id
	Os          OperatingSystem   `protobuf:"varint,7,opt,name=os,proto3,enum=open_pay_extension_api.OperatingSystem" json:"os,omitempty"` // 系统
	MapExt      map[string]string `protobuf:"bytes,8,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CheckOrderReq) Reset() {
	*x = CheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderReq) ProtoMessage() {}

func (x *CheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderReq.ProtoReflect.Descriptor instead.
func (*CheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{4}
}

func (x *CheckOrderReq) GetExtensionId() int64 {
	if x != nil {
		return x.ExtensionId
	}
	return 0
}

func (x *CheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CheckOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CheckOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CheckOrderReq) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

func (x *CheckOrderReq) GetOs() OperatingSystem {
	if x != nil {
		return x.Os
	}
	return OperatingSystem_OperatingSystemUnknown
}

func (x *CheckOrderReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type CheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckOrderRsp) Reset() {
	*x = CheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderRsp) ProtoMessage() {}

func (x *CheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderRsp.ProtoReflect.Descriptor instead.
func (*CheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{5}
}

type DeliveryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExtensionId   int64             `protobuf:"varint,1,opt,name=extensionId,proto3" json:"extensionId,omitempty"`
	AppId         string            `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string            `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId     string            `protobuf:"bytes,4,opt,name=productId,proto3" json:"productId,omitempty"`                                // 商品 id
	TransactionId string            `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`                        // 唯一订单 id
	Timestamp     int64             `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                               // 发货时间戳
	Os            OperatingSystem   `protobuf:"varint,7,opt,name=os,proto3,enum=open_pay_extension_api.OperatingSystem" json:"os,omitempty"` // 系统
	MapExt        map[string]string `protobuf:"bytes,8,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DeliveryReq) Reset() {
	*x = DeliveryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryReq) ProtoMessage() {}

func (x *DeliveryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryReq.ProtoReflect.Descriptor instead.
func (*DeliveryReq) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{6}
}

func (x *DeliveryReq) GetExtensionId() int64 {
	if x != nil {
		return x.ExtensionId
	}
	return 0
}

func (x *DeliveryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DeliveryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DeliveryReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *DeliveryReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *DeliveryReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DeliveryReq) GetOs() OperatingSystem {
	if x != nil {
		return x.Os
	}
	return OperatingSystem_OperatingSystemUnknown
}

func (x *DeliveryReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type DeliveryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardText string `protobuf:"bytes,1,opt,name=rewardText,proto3" json:"rewardText,omitempty"`
}

func (x *DeliveryRsp) Reset() {
	*x = DeliveryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryRsp) ProtoMessage() {}

func (x *DeliveryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryRsp.ProtoReflect.Descriptor instead.
func (*DeliveryRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{7}
}

func (x *DeliveryRsp) GetRewardText() string {
	if x != nil {
		return x.RewardText
	}
	return ""
}

type PrefetchUgcReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UniqueId string `protobuf:"bytes,2,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
}

func (x *PrefetchUgcReq) Reset() {
	*x = PrefetchUgcReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrefetchUgcReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrefetchUgcReq) ProtoMessage() {}

func (x *PrefetchUgcReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrefetchUgcReq.ProtoReflect.Descriptor instead.
func (*PrefetchUgcReq) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{8}
}

func (x *PrefetchUgcReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PrefetchUgcReq) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

type PrefetchUgcRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrefetchUgcRsp) Reset() {
	*x = PrefetchUgcRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrefetchUgcRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrefetchUgcRsp) ProtoMessage() {}

func (x *PrefetchUgcRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrefetchUgcRsp.ProtoReflect.Descriptor instead.
func (*PrefetchUgcRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{9}
}

type GetUgcResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueId string `protobuf:"bytes,1,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
}

func (x *GetUgcResultReq) Reset() {
	*x = GetUgcResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUgcResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUgcResultReq) ProtoMessage() {}

func (x *GetUgcResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUgcResultReq.ProtoReflect.Descriptor instead.
func (*GetUgcResultReq) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetUgcResultReq) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

type GetUgcResultRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *UgcResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *GetUgcResultRsp) Reset() {
	*x = GetUgcResultRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUgcResultRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUgcResultRsp) ProtoMessage() {}

func (x *GetUgcResultRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUgcResultRsp.ProtoReflect.Descriptor instead.
func (*GetUgcResultRsp) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetUgcResultRsp) GetResult() *UgcResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UgcResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatar    string `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Nickname  string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Cover     string `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover,omitempty"`
	Title     string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	PlayerNum int64  `protobuf:"varint,5,opt,name=playerNum,proto3" json:"playerNum,omitempty"`
	UgcId     string `protobuf:"bytes,6,opt,name=ugcId,proto3" json:"ugcId,omitempty"`
	AuthorId  string `protobuf:"bytes,7,opt,name=authorId,proto3" json:"authorId,omitempty"`
}

func (x *UgcResult) Reset() {
	*x = UgcResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UgcResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UgcResult) ProtoMessage() {}

func (x *UgcResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UgcResult.ProtoReflect.Descriptor instead.
func (*UgcResult) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{12}
}

func (x *UgcResult) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UgcResult) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UgcResult) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *UgcResult) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UgcResult) GetPlayerNum() int64 {
	if x != nil {
		return x.PlayerNum
	}
	return 0
}

func (x *UgcResult) GetUgcId() string {
	if x != nil {
		return x.UgcId
	}
	return ""
}

func (x *UgcResult) GetAuthorId() string {
	if x != nil {
		return x.AuthorId
	}
	return ""
}

type ExtensionConfigs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Configs map[int64]*ExtensionConfigs_Config `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ExtensionConfigs) Reset() {
	*x = ExtensionConfigs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtensionConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionConfigs) ProtoMessage() {}

func (x *ExtensionConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionConfigs.ProtoReflect.Descriptor instead.
func (*ExtensionConfigs) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{13}
}

func (x *ExtensionConfigs) GetConfigs() map[int64]*ExtensionConfigs_Config {
	if x != nil {
		return x.Configs
	}
	return nil
}

type ExtensionConfigs_Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckOrderService string `protobuf:"bytes,1,opt,name=checkOrderService,proto3" json:"checkOrderService,omitempty"`
	DeliveryService   string `protobuf:"bytes,2,opt,name=deliveryService,proto3" json:"deliveryService,omitempty"`
}

func (x *ExtensionConfigs_Config) Reset() {
	*x = ExtensionConfigs_Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtensionConfigs_Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionConfigs_Config) ProtoMessage() {}

func (x *ExtensionConfigs_Config) ProtoReflect() protoreflect.Message {
	mi := &file_pb_open_pay_extension_api_extension_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionConfigs_Config.ProtoReflect.Descriptor instead.
func (*ExtensionConfigs_Config) Descriptor() ([]byte, []int) {
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP(), []int{13, 0}
}

func (x *ExtensionConfigs_Config) GetCheckOrderService() string {
	if x != nil {
		return x.CheckOrderService
	}
	return ""
}

func (x *ExtensionConfigs_Config) GetDeliveryService() string {
	if x != nil {
		return x.DeliveryService
	}
	return ""
}

var File_pb_open_pay_extension_api_extension_api_proto protoreflect.FileDescriptor

var file_pb_open_pay_extension_api_extension_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x16, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x22, 0xc4, 0x01, 0x0a, 0x0e, 0x4c, 0x6f, 0x63, 0x6b,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x10,
	0x0a, 0x0e, 0x4c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x73, 0x70,
	0x22, 0xa6, 0x01, 0x0a, 0x10, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x73, 0x70, 0x22, 0xec, 0x02,
	0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x02, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x49, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61,
	0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61,
	0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x0f, 0x0a, 0x0d,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0xfc, 0x02,
	0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a,
	0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x37, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x47, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61,
	0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2d, 0x0a, 0x0b,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x78, 0x74, 0x22, 0x3e, 0x0a, 0x0e, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x55, 0x67, 0x63, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x55, 0x67, 0x63, 0x52, 0x73, 0x70, 0x22, 0x2d, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x55, 0x67, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x22, 0x4c, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x55, 0x67, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x39, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x67, 0x63, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xbb, 0x01, 0x0a, 0x09, 0x55,
	0x67, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb2, 0x02, 0x0a, 0x10, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x4f, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x1a, 0x60,
	0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x1a, 0x6b, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x45, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x61, 0x0a,
	0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41,
	0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x4f, 0x53, 0x10, 0x02,
	0x32, 0xc5, 0x04, 0x0a, 0x0c, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x70,
	0x69, 0x12, 0x5a, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61,
	0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a,
	0x08, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x23, 0x2e, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x23,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x52, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x0b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x55,
	0x67, 0x63, 0x12, 0x26, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x74, 0x63, 0x68, 0x55, 0x67, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x55, 0x67, 0x63, 0x52,
	0x73, 0x70, 0x12, 0x60, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x55, 0x67, 0x63, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x27, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x67, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x67, 0x63, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x0b, 0x4c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x12, 0x26, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x6f, 0x63,
	0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x0d, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x12, 0x28, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e,
	0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x28,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x73, 0x70, 0x42, 0x4e, 0x5a, 0x4c, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_open_pay_extension_api_extension_api_proto_rawDescOnce sync.Once
	file_pb_open_pay_extension_api_extension_api_proto_rawDescData = file_pb_open_pay_extension_api_extension_api_proto_rawDesc
)

func file_pb_open_pay_extension_api_extension_api_proto_rawDescGZIP() []byte {
	file_pb_open_pay_extension_api_extension_api_proto_rawDescOnce.Do(func() {
		file_pb_open_pay_extension_api_extension_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_open_pay_extension_api_extension_api_proto_rawDescData)
	})
	return file_pb_open_pay_extension_api_extension_api_proto_rawDescData
}

var file_pb_open_pay_extension_api_extension_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_open_pay_extension_api_extension_api_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_pb_open_pay_extension_api_extension_api_proto_goTypes = []interface{}{
	(OperatingSystem)(0),            // 0: open_pay_extension_api.OperatingSystem
	(*LockProductReq)(nil),          // 1: open_pay_extension_api.LockProductReq
	(*LockProductRsp)(nil),          // 2: open_pay_extension_api.LockProductRsp
	(*UnlockProductReq)(nil),        // 3: open_pay_extension_api.UnlockProductReq
	(*UnlockProductRsp)(nil),        // 4: open_pay_extension_api.UnlockProductRsp
	(*CheckOrderReq)(nil),           // 5: open_pay_extension_api.CheckOrderReq
	(*CheckOrderRsp)(nil),           // 6: open_pay_extension_api.CheckOrderRsp
	(*DeliveryReq)(nil),             // 7: open_pay_extension_api.DeliveryReq
	(*DeliveryRsp)(nil),             // 8: open_pay_extension_api.DeliveryRsp
	(*PrefetchUgcReq)(nil),          // 9: open_pay_extension_api.PrefetchUgcReq
	(*PrefetchUgcRsp)(nil),          // 10: open_pay_extension_api.PrefetchUgcRsp
	(*GetUgcResultReq)(nil),         // 11: open_pay_extension_api.GetUgcResultReq
	(*GetUgcResultRsp)(nil),         // 12: open_pay_extension_api.GetUgcResultRsp
	(*UgcResult)(nil),               // 13: open_pay_extension_api.UgcResult
	(*ExtensionConfigs)(nil),        // 14: open_pay_extension_api.ExtensionConfigs
	nil,                             // 15: open_pay_extension_api.CheckOrderReq.MapExtEntry
	nil,                             // 16: open_pay_extension_api.DeliveryReq.MapExtEntry
	(*ExtensionConfigs_Config)(nil), // 17: open_pay_extension_api.ExtensionConfigs.Config
	nil,                             // 18: open_pay_extension_api.ExtensionConfigs.ConfigsEntry
}
var file_pb_open_pay_extension_api_extension_api_proto_depIdxs = []int32{
	0,  // 0: open_pay_extension_api.CheckOrderReq.os:type_name -> open_pay_extension_api.OperatingSystem
	15, // 1: open_pay_extension_api.CheckOrderReq.mapExt:type_name -> open_pay_extension_api.CheckOrderReq.MapExtEntry
	0,  // 2: open_pay_extension_api.DeliveryReq.os:type_name -> open_pay_extension_api.OperatingSystem
	16, // 3: open_pay_extension_api.DeliveryReq.mapExt:type_name -> open_pay_extension_api.DeliveryReq.MapExtEntry
	13, // 4: open_pay_extension_api.GetUgcResultRsp.result:type_name -> open_pay_extension_api.UgcResult
	18, // 5: open_pay_extension_api.ExtensionConfigs.configs:type_name -> open_pay_extension_api.ExtensionConfigs.ConfigsEntry
	17, // 6: open_pay_extension_api.ExtensionConfigs.ConfigsEntry.value:type_name -> open_pay_extension_api.ExtensionConfigs.Config
	5,  // 7: open_pay_extension_api.ExtensionApi.CheckOrder:input_type -> open_pay_extension_api.CheckOrderReq
	7,  // 8: open_pay_extension_api.ExtensionApi.Delivery:input_type -> open_pay_extension_api.DeliveryReq
	9,  // 9: open_pay_extension_api.ExtensionApi.PrefetchUgc:input_type -> open_pay_extension_api.PrefetchUgcReq
	11, // 10: open_pay_extension_api.ExtensionApi.GetUgcResult:input_type -> open_pay_extension_api.GetUgcResultReq
	1,  // 11: open_pay_extension_api.ExtensionApi.LockProduct:input_type -> open_pay_extension_api.LockProductReq
	3,  // 12: open_pay_extension_api.ExtensionApi.UnlockProduct:input_type -> open_pay_extension_api.UnlockProductReq
	6,  // 13: open_pay_extension_api.ExtensionApi.CheckOrder:output_type -> open_pay_extension_api.CheckOrderRsp
	8,  // 14: open_pay_extension_api.ExtensionApi.Delivery:output_type -> open_pay_extension_api.DeliveryRsp
	10, // 15: open_pay_extension_api.ExtensionApi.PrefetchUgc:output_type -> open_pay_extension_api.PrefetchUgcRsp
	12, // 16: open_pay_extension_api.ExtensionApi.GetUgcResult:output_type -> open_pay_extension_api.GetUgcResultRsp
	2,  // 17: open_pay_extension_api.ExtensionApi.LockProduct:output_type -> open_pay_extension_api.LockProductRsp
	4,  // 18: open_pay_extension_api.ExtensionApi.UnlockProduct:output_type -> open_pay_extension_api.UnlockProductRsp
	13, // [13:19] is the sub-list for method output_type
	7,  // [7:13] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_pb_open_pay_extension_api_extension_api_proto_init() }
func file_pb_open_pay_extension_api_extension_api_proto_init() {
	if File_pb_open_pay_extension_api_extension_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LockProductReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LockProductRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlockProductReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnlockProductRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrefetchUgcReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrefetchUgcRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUgcResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUgcResultRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UgcResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtensionConfigs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_open_pay_extension_api_extension_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtensionConfigs_Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_open_pay_extension_api_extension_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_open_pay_extension_api_extension_api_proto_goTypes,
		DependencyIndexes: file_pb_open_pay_extension_api_extension_api_proto_depIdxs,
		EnumInfos:         file_pb_open_pay_extension_api_extension_api_proto_enumTypes,
		MessageInfos:      file_pb_open_pay_extension_api_extension_api_proto_msgTypes,
	}.Build()
	File_pb_open_pay_extension_api_extension_api_proto = out.File
	file_pb_open_pay_extension_api_extension_api_proto_rawDesc = nil
	file_pb_open_pay_extension_api_extension_api_proto_goTypes = nil
	file_pb_open_pay_extension_api_extension_api_proto_depIdxs = nil
}
