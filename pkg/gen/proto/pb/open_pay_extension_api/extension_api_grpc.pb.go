// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/open_pay_extension_api/extension_api.proto

package open_pay_extension_api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ExtensionApi_CheckOrder_FullMethodName    = "/open_pay_extension_api.ExtensionApi/CheckOrder"
	ExtensionApi_Delivery_FullMethodName      = "/open_pay_extension_api.ExtensionApi/Delivery"
	ExtensionApi_PrefetchUgc_FullMethodName   = "/open_pay_extension_api.ExtensionApi/PrefetchUgc"
	ExtensionApi_GetUgcResult_FullMethodName  = "/open_pay_extension_api.ExtensionApi/GetUgcResult"
	ExtensionApi_LockProduct_FullMethodName   = "/open_pay_extension_api.ExtensionApi/LockProduct"
	ExtensionApi_UnlockProduct_FullMethodName = "/open_pay_extension_api.ExtensionApi/UnlockProduct"
)

// ExtensionApiClient is the client API for ExtensionApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExtensionApiClient interface {
	// 校验订单
	CheckOrder(ctx context.Context, in *CheckOrderReq, opts ...grpc.CallOption) (*CheckOrderRsp, error)
	// 发货
	Delivery(ctx context.Context, in *DeliveryReq, opts ...grpc.CallOption) (*DeliveryRsp, error)
	// 预拉取作品
	PrefetchUgc(ctx context.Context, in *PrefetchUgcReq, opts ...grpc.CallOption) (*PrefetchUgcRsp, error)
	// 获取 ugc 结果
	GetUgcResult(ctx context.Context, in *GetUgcResultReq, opts ...grpc.CallOption) (*GetUgcResultRsp, error)
	// lock
	LockProduct(ctx context.Context, in *LockProductReq, opts ...grpc.CallOption) (*LockProductRsp, error)
	// unlock
	UnlockProduct(ctx context.Context, in *UnlockProductReq, opts ...grpc.CallOption) (*UnlockProductRsp, error)
}

type extensionApiClient struct {
	cc grpc.ClientConnInterface
}

func NewExtensionApiClient(cc grpc.ClientConnInterface) ExtensionApiClient {
	return &extensionApiClient{cc}
}

func (c *extensionApiClient) CheckOrder(ctx context.Context, in *CheckOrderReq, opts ...grpc.CallOption) (*CheckOrderRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckOrderRsp)
	err := c.cc.Invoke(ctx, ExtensionApi_CheckOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extensionApiClient) Delivery(ctx context.Context, in *DeliveryReq, opts ...grpc.CallOption) (*DeliveryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeliveryRsp)
	err := c.cc.Invoke(ctx, ExtensionApi_Delivery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extensionApiClient) PrefetchUgc(ctx context.Context, in *PrefetchUgcReq, opts ...grpc.CallOption) (*PrefetchUgcRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrefetchUgcRsp)
	err := c.cc.Invoke(ctx, ExtensionApi_PrefetchUgc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extensionApiClient) GetUgcResult(ctx context.Context, in *GetUgcResultReq, opts ...grpc.CallOption) (*GetUgcResultRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUgcResultRsp)
	err := c.cc.Invoke(ctx, ExtensionApi_GetUgcResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extensionApiClient) LockProduct(ctx context.Context, in *LockProductReq, opts ...grpc.CallOption) (*LockProductRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LockProductRsp)
	err := c.cc.Invoke(ctx, ExtensionApi_LockProduct_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extensionApiClient) UnlockProduct(ctx context.Context, in *UnlockProductReq, opts ...grpc.CallOption) (*UnlockProductRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnlockProductRsp)
	err := c.cc.Invoke(ctx, ExtensionApi_UnlockProduct_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExtensionApiServer is the server API for ExtensionApi service.
// All implementations should embed UnimplementedExtensionApiServer
// for forward compatibility
type ExtensionApiServer interface {
	// 校验订单
	CheckOrder(context.Context, *CheckOrderReq) (*CheckOrderRsp, error)
	// 发货
	Delivery(context.Context, *DeliveryReq) (*DeliveryRsp, error)
	// 预拉取作品
	PrefetchUgc(context.Context, *PrefetchUgcReq) (*PrefetchUgcRsp, error)
	// 获取 ugc 结果
	GetUgcResult(context.Context, *GetUgcResultReq) (*GetUgcResultRsp, error)
	// lock
	LockProduct(context.Context, *LockProductReq) (*LockProductRsp, error)
	// unlock
	UnlockProduct(context.Context, *UnlockProductReq) (*UnlockProductRsp, error)
}

// UnimplementedExtensionApiServer should be embedded to have forward compatible implementations.
type UnimplementedExtensionApiServer struct {
}

func (UnimplementedExtensionApiServer) CheckOrder(context.Context, *CheckOrderReq) (*CheckOrderRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckOrder not implemented")
}
func (UnimplementedExtensionApiServer) Delivery(context.Context, *DeliveryReq) (*DeliveryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delivery not implemented")
}
func (UnimplementedExtensionApiServer) PrefetchUgc(context.Context, *PrefetchUgcReq) (*PrefetchUgcRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrefetchUgc not implemented")
}
func (UnimplementedExtensionApiServer) GetUgcResult(context.Context, *GetUgcResultReq) (*GetUgcResultRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUgcResult not implemented")
}
func (UnimplementedExtensionApiServer) LockProduct(context.Context, *LockProductReq) (*LockProductRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LockProduct not implemented")
}
func (UnimplementedExtensionApiServer) UnlockProduct(context.Context, *UnlockProductReq) (*UnlockProductRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlockProduct not implemented")
}

// UnsafeExtensionApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExtensionApiServer will
// result in compilation errors.
type UnsafeExtensionApiServer interface {
	mustEmbedUnimplementedExtensionApiServer()
}

func RegisterExtensionApiServer(s grpc.ServiceRegistrar, srv ExtensionApiServer) {
	s.RegisterService(&ExtensionApi_ServiceDesc, srv)
}

func _ExtensionApi_CheckOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtensionApiServer).CheckOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExtensionApi_CheckOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtensionApiServer).CheckOrder(ctx, req.(*CheckOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtensionApi_Delivery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliveryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtensionApiServer).Delivery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExtensionApi_Delivery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtensionApiServer).Delivery(ctx, req.(*DeliveryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtensionApi_PrefetchUgc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrefetchUgcReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtensionApiServer).PrefetchUgc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExtensionApi_PrefetchUgc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtensionApiServer).PrefetchUgc(ctx, req.(*PrefetchUgcReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtensionApi_GetUgcResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUgcResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtensionApiServer).GetUgcResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExtensionApi_GetUgcResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtensionApiServer).GetUgcResult(ctx, req.(*GetUgcResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtensionApi_LockProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LockProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtensionApiServer).LockProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExtensionApi_LockProduct_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtensionApiServer).LockProduct(ctx, req.(*LockProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtensionApi_UnlockProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnlockProductReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtensionApiServer).UnlockProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExtensionApi_UnlockProduct_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtensionApiServer).UnlockProduct(ctx, req.(*UnlockProductReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ExtensionApi_ServiceDesc is the grpc.ServiceDesc for ExtensionApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExtensionApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "open_pay_extension_api.ExtensionApi",
	HandlerType: (*ExtensionApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckOrder",
			Handler:    _ExtensionApi_CheckOrder_Handler,
		},
		{
			MethodName: "Delivery",
			Handler:    _ExtensionApi_Delivery_Handler,
		},
		{
			MethodName: "PrefetchUgc",
			Handler:    _ExtensionApi_PrefetchUgc_Handler,
		},
		{
			MethodName: "GetUgcResult",
			Handler:    _ExtensionApi_GetUgcResult_Handler,
		},
		{
			MethodName: "LockProduct",
			Handler:    _ExtensionApi_LockProduct_Handler,
		},
		{
			MethodName: "UnlockProduct",
			Handler:    _ExtensionApi_UnlockProduct_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/open_pay_extension_api/extension_api.proto",
}
