{"swagger": "2.0", "info": {"title": "pb/open_pay_extension_api/extension_api.proto", "version": "version not set"}, "tags": [{"name": "ExtensionApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/open_pay_extension_api.ExtensionApi/CheckOrder": {"post": {"summary": "校验订单", "operationId": "ExtensionApi_CheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extension_apiCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extension_apiCheckOrderReq"}}], "tags": ["ExtensionApi"]}}, "/open_pay_extension_api.ExtensionApi/Delivery": {"post": {"summary": "发货", "operationId": "ExtensionApi_Delivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extension_apiDeliveryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extension_apiDeliveryReq"}}], "tags": ["ExtensionApi"]}}, "/open_pay_extension_api.ExtensionApi/GetUgcResult": {"post": {"summary": "获取 ugc 结果", "operationId": "ExtensionApi_GetUgcResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extension_apiGetUgcResultRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extension_apiGetUgcResultReq"}}], "tags": ["ExtensionApi"]}}, "/open_pay_extension_api.ExtensionApi/LockProduct": {"post": {"summary": "lock", "operationId": "ExtensionApi_LockProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extension_apiLockProductRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extension_apiLockProductReq"}}], "tags": ["ExtensionApi"]}}, "/open_pay_extension_api.ExtensionApi/PrefetchUgc": {"post": {"summary": "预拉取作品", "operationId": "ExtensionApi_PrefetchUgc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extension_apiPrefetchUgcRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extension_apiPrefetchUgcReq"}}], "tags": ["ExtensionApi"]}}, "/open_pay_extension_api.ExtensionApi/UnlockProduct": {"post": {"summary": "unlock", "operationId": "ExtensionApi_UnlockProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_pay_extension_apiUnlockProductRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_pay_extension_apiUnlockProductReq"}}], "tags": ["ExtensionApi"]}}}, "definitions": {"open_pay_extension_apiCheckOrderReq": {"type": "object", "properties": {"extensionId": {"type": "string", "format": "int64"}, "appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "price": {"type": "string", "format": "int64", "title": "价格"}, "greenId": {"type": "string", "format": "int64", "title": "绿钻 id"}, "os": {"$ref": "#/definitions/open_pay_extension_apiOperatingSystem", "title": "系统"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "open_pay_extension_apiCheckOrderRsp": {"type": "object"}, "open_pay_extension_apiDeliveryReq": {"type": "object", "properties": {"extensionId": {"type": "string", "format": "int64"}, "appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}, "os": {"$ref": "#/definitions/open_pay_extension_apiOperatingSystem", "title": "系统"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "open_pay_extension_apiDeliveryRsp": {"type": "object", "properties": {"rewardText": {"type": "string"}}}, "open_pay_extension_apiGetUgcResultReq": {"type": "object", "properties": {"uniqueId": {"type": "string"}}}, "open_pay_extension_apiGetUgcResultRsp": {"type": "object", "properties": {"result": {"$ref": "#/definitions/open_pay_extension_apiUgcResult"}}}, "open_pay_extension_apiLockProductReq": {"type": "object", "properties": {"extensionId": {"type": "string", "format": "int64"}, "appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间 0:不过期"}}}, "open_pay_extension_apiLockProductRsp": {"type": "object"}, "open_pay_extension_apiOperatingSystem": {"type": "string", "enum": ["OperatingSystemUnknown", "OperatingSystemAndroid", "OperatingSystemIOS"], "default": "OperatingSystemUnknown"}, "open_pay_extension_apiPrefetchUgcReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "uniqueId": {"type": "string"}}}, "open_pay_extension_apiPrefetchUgcRsp": {"type": "object"}, "open_pay_extension_apiUgcResult": {"type": "object", "properties": {"avatar": {"type": "string"}, "nickname": {"type": "string"}, "cover": {"type": "string"}, "title": {"type": "string"}, "playerNum": {"type": "string", "format": "int64"}, "ugcId": {"type": "string"}, "authorId": {"type": "string"}}}, "open_pay_extension_apiUnlockProductReq": {"type": "object", "properties": {"extensionId": {"type": "string", "format": "int64"}, "appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}}}, "open_pay_extension_apiUnlockProductRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}