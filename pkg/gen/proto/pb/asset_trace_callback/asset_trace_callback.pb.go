// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/asset_trace_callback/asset_trace_callback.proto

package asset_trace_callback

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionType int32

const (
	ActionType_ActionTypeInvalid ActionType = 0
	ActionType_ActionTypeAdd     ActionType = 1
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0: "ActionTypeInvalid",
		1: "ActionTypeAdd",
	}
	ActionType_value = map[string]int32{
		"ActionTypeInvalid": 0,
		"ActionTypeAdd":     1,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_asset_trace_callback_asset_trace_callback_proto_enumTypes[0].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_pb_asset_trace_callback_asset_trace_callback_proto_enumTypes[0]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescGZIP(), []int{0}
}

type AssetItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId   int64  `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`
	AssetNum  int64  `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"`
	AssetName string `protobuf:"bytes,3,opt,name=assetName,proto3" json:"assetName,omitempty"`
}

func (x *AssetItem) Reset() {
	*x = AssetItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetItem) ProtoMessage() {}

func (x *AssetItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetItem.ProtoReflect.Descriptor instead.
func (*AssetItem) Descriptor() ([]byte, []int) {
	return file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescGZIP(), []int{0}
}

func (x *AssetItem) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *AssetItem) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AssetItem) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

type AssetTraceCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string       `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string       `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Items  []*AssetItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	Action ActionType   `protobuf:"varint,4,opt,name=action,proto3,enum=asset_trace_callback.ActionType" json:"action,omitempty"`
	Reason string       `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	Ts     int64        `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *AssetTraceCallbackReq) Reset() {
	*x = AssetTraceCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetTraceCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetTraceCallbackReq) ProtoMessage() {}

func (x *AssetTraceCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetTraceCallbackReq.ProtoReflect.Descriptor instead.
func (*AssetTraceCallbackReq) Descriptor() ([]byte, []int) {
	return file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescGZIP(), []int{1}
}

func (x *AssetTraceCallbackReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AssetTraceCallbackReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AssetTraceCallbackReq) GetItems() []*AssetItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *AssetTraceCallbackReq) GetAction() ActionType {
	if x != nil {
		return x.Action
	}
	return ActionType_ActionTypeInvalid
}

func (x *AssetTraceCallbackReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AssetTraceCallbackReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type AssetTraceCallbackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AssetTraceCallbackRsp) Reset() {
	*x = AssetTraceCallbackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetTraceCallbackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetTraceCallbackRsp) ProtoMessage() {}

func (x *AssetTraceCallbackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetTraceCallbackRsp.ProtoReflect.Descriptor instead.
func (*AssetTraceCallbackRsp) Descriptor() ([]byte, []int) {
	return file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescGZIP(), []int{2}
}

var File_pb_asset_trace_callback_asset_trace_callback_proto protoreflect.FileDescriptor

var file_pb_asset_trace_callback_asset_trace_callback_proto_rawDesc = []byte{
	0x0a, 0x32, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x5f, 0x0a, 0x09, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xde, 0x01, 0x0a, 0x15,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x38, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0x17, 0x0a, 0x15,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x73, 0x70, 0x2a, 0x36, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x41, 0x64, 0x64, 0x10, 0x01, 0x42, 0x4c, 0x5a,
	0x4a, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescOnce sync.Once
	file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescData = file_pb_asset_trace_callback_asset_trace_callback_proto_rawDesc
)

func file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescGZIP() []byte {
	file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescOnce.Do(func() {
		file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescData)
	})
	return file_pb_asset_trace_callback_asset_trace_callback_proto_rawDescData
}

var file_pb_asset_trace_callback_asset_trace_callback_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pb_asset_trace_callback_asset_trace_callback_proto_goTypes = []interface{}{
	(ActionType)(0),               // 0: asset_trace_callback.ActionType
	(*AssetItem)(nil),             // 1: asset_trace_callback.AssetItem
	(*AssetTraceCallbackReq)(nil), // 2: asset_trace_callback.AssetTraceCallbackReq
	(*AssetTraceCallbackRsp)(nil), // 3: asset_trace_callback.AssetTraceCallbackRsp
}
var file_pb_asset_trace_callback_asset_trace_callback_proto_depIdxs = []int32{
	1, // 0: asset_trace_callback.AssetTraceCallbackReq.items:type_name -> asset_trace_callback.AssetItem
	0, // 1: asset_trace_callback.AssetTraceCallbackReq.action:type_name -> asset_trace_callback.ActionType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_asset_trace_callback_asset_trace_callback_proto_init() }
func file_pb_asset_trace_callback_asset_trace_callback_proto_init() {
	if File_pb_asset_trace_callback_asset_trace_callback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetTraceCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetTraceCallbackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_asset_trace_callback_asset_trace_callback_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_asset_trace_callback_asset_trace_callback_proto_goTypes,
		DependencyIndexes: file_pb_asset_trace_callback_asset_trace_callback_proto_depIdxs,
		EnumInfos:         file_pb_asset_trace_callback_asset_trace_callback_proto_enumTypes,
		MessageInfos:      file_pb_asset_trace_callback_asset_trace_callback_proto_msgTypes,
	}.Build()
	File_pb_asset_trace_callback_asset_trace_callback_proto = out.File
	file_pb_asset_trace_callback_asset_trace_callback_proto_rawDesc = nil
	file_pb_asset_trace_callback_asset_trace_callback_proto_goTypes = nil
	file_pb_asset_trace_callback_asset_trace_callback_proto_depIdxs = nil
}
