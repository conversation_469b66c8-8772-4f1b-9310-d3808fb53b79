{"swagger": "2.0", "info": {"title": "pb/comm_api/comm_api.proto", "version": "version not set"}, "tags": [{"name": "CommApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/comm_api/batchOpenid2PlatUid": {"post": {"operationId": "CommApi_BatchOpenid2PlatUid", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBatchOpenid2PlatUidRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBatchOpenid2PlatUidReq"}}], "tags": ["CommApi"]}}}, "definitions": {"BatchOpenid2PlatUidRspItem": {"type": "object", "properties": {"platid": {"type": "string", "format": "uint64", "title": "平台"}, "uid": {"type": "string", "title": "平台对应用户"}}}, "gameBatchOpenid2PlatUidReq": {"type": "object", "properties": {"openidList": {"type": "array", "items": {"type": "string"}, "title": "用户Openid,  批量大小限制100"}}, "title": "MOpenid2PlatUidReq multi openid转平台uid"}, "gameBatchOpenid2PlatUidRsp": {"type": "object", "properties": {"mapSucc": {"type": "object", "additionalProperties": {"$ref": "#/definitions/BatchOpenid2PlatUidRspItem"}, "title": "转换成功, key: openid  value: Item"}}, "title": "参考了GOpenService的结构体"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}