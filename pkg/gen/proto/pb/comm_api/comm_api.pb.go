// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/comm_api/comm_api.proto

package comm_api

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MOpenid2PlatUidReq multi openid转平台uid
type BatchOpenid2PlatUidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenidList []string `protobuf:"bytes,1,rep,name=openid_list,json=openidList,proto3" json:"openid_list,omitempty"` // 用户Openid,  批量大小限制100
}

func (x *BatchOpenid2PlatUidReq) Reset() {
	*x = BatchOpenid2PlatUidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_comm_api_comm_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchOpenid2PlatUidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchOpenid2PlatUidReq) ProtoMessage() {}

func (x *BatchOpenid2PlatUidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_comm_api_comm_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchOpenid2PlatUidReq.ProtoReflect.Descriptor instead.
func (*BatchOpenid2PlatUidReq) Descriptor() ([]byte, []int) {
	return file_pb_comm_api_comm_api_proto_rawDescGZIP(), []int{0}
}

func (x *BatchOpenid2PlatUidReq) GetOpenidList() []string {
	if x != nil {
		return x.OpenidList
	}
	return nil
}

// 参考了GOpenService的结构体
type BatchOpenid2PlatUidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapSucc map[string]*BatchOpenid2PlatUidRsp_Item `protobuf:"bytes,2,rep,name=mapSucc,proto3" json:"mapSucc,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 转换成功, key: openid  value: Item
}

func (x *BatchOpenid2PlatUidRsp) Reset() {
	*x = BatchOpenid2PlatUidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_comm_api_comm_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchOpenid2PlatUidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchOpenid2PlatUidRsp) ProtoMessage() {}

func (x *BatchOpenid2PlatUidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_comm_api_comm_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchOpenid2PlatUidRsp.ProtoReflect.Descriptor instead.
func (*BatchOpenid2PlatUidRsp) Descriptor() ([]byte, []int) {
	return file_pb_comm_api_comm_api_proto_rawDescGZIP(), []int{1}
}

func (x *BatchOpenid2PlatUidRsp) GetMapSucc() map[string]*BatchOpenid2PlatUidRsp_Item {
	if x != nil {
		return x.MapSucc
	}
	return nil
}

type BatchOpenid2PlatUidRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platid uint64 `protobuf:"varint,1,opt,name=platid,proto3" json:"platid,omitempty"` // 平台
	Uid    string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`        // 平台对应用户
}

func (x *BatchOpenid2PlatUidRsp_Item) Reset() {
	*x = BatchOpenid2PlatUidRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_comm_api_comm_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchOpenid2PlatUidRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchOpenid2PlatUidRsp_Item) ProtoMessage() {}

func (x *BatchOpenid2PlatUidRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_comm_api_comm_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchOpenid2PlatUidRsp_Item.ProtoReflect.Descriptor instead.
func (*BatchOpenid2PlatUidRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_comm_api_comm_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BatchOpenid2PlatUidRsp_Item) GetPlatid() uint64 {
	if x != nil {
		return x.Platid
	}
	return 0
}

func (x *BatchOpenid2PlatUidRsp_Item) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

var File_pb_comm_api_comm_api_proto protoreflect.FileDescriptor

var file_pb_comm_api_comm_api_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a, 0x16, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x69,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x82, 0x02, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x4d, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50,
	0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63,
	0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x1a,
	0x30, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x1a, 0x67, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32,
	0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x9b, 0x01, 0x0a, 0x07, 0x43,
	0x6f, 0x6d, 0x6d, 0x41, 0x70, 0x69, 0x12, 0x8f, 0x01, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x12, 0x26,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74,
	0x55, 0x69, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x22, 0x28,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a, 0x22, 0x1d, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x5f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x42, 0x40, 0x5a, 0x3e, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_comm_api_comm_api_proto_rawDescOnce sync.Once
	file_pb_comm_api_comm_api_proto_rawDescData = file_pb_comm_api_comm_api_proto_rawDesc
)

func file_pb_comm_api_comm_api_proto_rawDescGZIP() []byte {
	file_pb_comm_api_comm_api_proto_rawDescOnce.Do(func() {
		file_pb_comm_api_comm_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_comm_api_comm_api_proto_rawDescData)
	})
	return file_pb_comm_api_comm_api_proto_rawDescData
}

var file_pb_comm_api_comm_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_comm_api_comm_api_proto_goTypes = []interface{}{
	(*BatchOpenid2PlatUidReq)(nil),      // 0: component.game.BatchOpenid2PlatUidReq
	(*BatchOpenid2PlatUidRsp)(nil),      // 1: component.game.BatchOpenid2PlatUidRsp
	(*BatchOpenid2PlatUidRsp_Item)(nil), // 2: component.game.BatchOpenid2PlatUidRsp.Item
	nil,                                 // 3: component.game.BatchOpenid2PlatUidRsp.MapSuccEntry
}
var file_pb_comm_api_comm_api_proto_depIdxs = []int32{
	3, // 0: component.game.BatchOpenid2PlatUidRsp.mapSucc:type_name -> component.game.BatchOpenid2PlatUidRsp.MapSuccEntry
	2, // 1: component.game.BatchOpenid2PlatUidRsp.MapSuccEntry.value:type_name -> component.game.BatchOpenid2PlatUidRsp.Item
	0, // 2: component.game.CommApi.BatchOpenid2PlatUid:input_type -> component.game.BatchOpenid2PlatUidReq
	1, // 3: component.game.CommApi.BatchOpenid2PlatUid:output_type -> component.game.BatchOpenid2PlatUidRsp
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_comm_api_comm_api_proto_init() }
func file_pb_comm_api_comm_api_proto_init() {
	if File_pb_comm_api_comm_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_comm_api_comm_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchOpenid2PlatUidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_comm_api_comm_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchOpenid2PlatUidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_comm_api_comm_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchOpenid2PlatUidRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_comm_api_comm_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_comm_api_comm_api_proto_goTypes,
		DependencyIndexes: file_pb_comm_api_comm_api_proto_depIdxs,
		MessageInfos:      file_pb_comm_api_comm_api_proto_msgTypes,
	}.Build()
	File_pb_comm_api_comm_api_proto = out.File
	file_pb_comm_api_comm_api_proto_rawDesc = nil
	file_pb_comm_api_comm_api_proto_goTypes = nil
	file_pb_comm_api_comm_api_proto_depIdxs = nil
}
