// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/asset_admin/asset_admin.proto

package game_admin

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GameAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon       string `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	ExpireType int64  `protobuf:"varint,4,opt,name=expireType,proto3" json:"expireType,omitempty"`
	ExpireTime int64  `protobuf:"varint,5,opt,name=expireTime,proto3" json:"expireTime,omitempty"`
	// 货币类型 1/游戏币 2/兑换币
	CurrencyType uint32 `protobuf:"varint,6,opt,name=currencyType,proto3" json:"currencyType,omitempty"`
	// 平台ID gopen.EPlatID
	Plat uint32 `protobuf:"varint,7,opt,name=plat,proto3" json:"plat,omitempty"`
	// 购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得
	PayType uint32 `protobuf:"varint,8,opt,name=payType,proto3" json:"payType,omitempty"`
	// 道具/礼物单价
	GiftOrPropUnitPrice uint32 `protobuf:"varint,9,opt,name=giftOrPropUnitPrice,proto3" json:"giftOrPropUnitPrice,omitempty"`
	// 道具/礼物ID，如果支付类型为1,送出这个礼物，为3则送出这个道具
	GiftOrPropId uint32 `protobuf:"varint,10,opt,name=giftOrPropId,proto3" json:"giftOrPropId,omitempty"`
	// 汇率
	ExchangeRate uint32 `protobuf:"varint,11,opt,name=exchangeRate,proto3" json:"exchangeRate,omitempty"`
	// 平台业务ID
	PlatBusinessID uint32 `protobuf:"varint,12,opt,name=platBusinessID,proto3" json:"platBusinessID,omitempty"`
	// 收入类型 1/计收入 2/不计收入
	RevenueType uint32 `protobuf:"varint,13,opt,name=revenueType,proto3" json:"revenueType,omitempty"`
	Tag         string `protobuf:"bytes,14,opt,name=tag,proto3" json:"tag,omitempty"`
	// 对应的统一账户资产id
	UniAssetId int64 `protobuf:"varint,16,opt,name=uniAssetId,proto3" json:"uniAssetId,omitempty"`
	// 购买弹窗文案
	PayDesc string `protobuf:"bytes,17,opt,name=payDesc,proto3" json:"payDesc,omitempty"`
	// 购买弹窗前3档
	PayGear []int64 `protobuf:"varint,18,rep,packed,name=payGear,proto3" json:"payGear,omitempty"`
	// 过期链接
	ExpireUrl string `protobuf:"bytes,19,opt,name=expireUrl,proto3" json:"expireUrl,omitempty"`
	ModalType uint32 `protobuf:"varint,20,opt,name=modalType,proto3" json:"modalType,omitempty"`
	AssetRule string `protobuf:"bytes,21,opt,name=assetRule,proto3" json:"assetRule,omitempty"`
	SendToUgc bool   `protobuf:"varint,22,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
}

func (x *GameAsset) Reset() {
	*x = GameAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAsset) ProtoMessage() {}

func (x *GameAsset) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAsset.ProtoReflect.Descriptor instead.
func (*GameAsset) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{0}
}

func (x *GameAsset) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GameAsset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameAsset) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GameAsset) GetExpireType() int64 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *GameAsset) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *GameAsset) GetCurrencyType() uint32 {
	if x != nil {
		return x.CurrencyType
	}
	return 0
}

func (x *GameAsset) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *GameAsset) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *GameAsset) GetGiftOrPropUnitPrice() uint32 {
	if x != nil {
		return x.GiftOrPropUnitPrice
	}
	return 0
}

func (x *GameAsset) GetGiftOrPropId() uint32 {
	if x != nil {
		return x.GiftOrPropId
	}
	return 0
}

func (x *GameAsset) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *GameAsset) GetPlatBusinessID() uint32 {
	if x != nil {
		return x.PlatBusinessID
	}
	return 0
}

func (x *GameAsset) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *GameAsset) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GameAsset) GetUniAssetId() int64 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

func (x *GameAsset) GetPayDesc() string {
	if x != nil {
		return x.PayDesc
	}
	return ""
}

func (x *GameAsset) GetPayGear() []int64 {
	if x != nil {
		return x.PayGear
	}
	return nil
}

func (x *GameAsset) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

func (x *GameAsset) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *GameAsset) GetAssetRule() string {
	if x != nil {
		return x.AssetRule
	}
	return ""
}

func (x *GameAsset) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

type PrizeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId int64 `protobuf:"varint,1,opt,name=RewardId,proto3" json:"RewardId,omitempty"`
}

func (x *PrizeItem) Reset() {
	*x = PrizeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrizeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrizeItem) ProtoMessage() {}

func (x *PrizeItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrizeItem.ProtoReflect.Descriptor instead.
func (*PrizeItem) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{1}
}

func (x *PrizeItem) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

type SyncRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string       `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Assets []*GameAsset `protobuf:"bytes,2,rep,name=assets,proto3" json:"assets,omitempty"`
	// 发奖时用
	// repeated PrizeItem prizes = 3; 废弃
	AppName string `protobuf:"bytes,4,opt,name=appName,proto3" json:"appName,omitempty"`
	// 游戏配置，key为平台id，参考gopen.EPlatID
	GameConfigs map[uint32]*GamePlatConf `protobuf:"bytes,5,rep,name=gameConfigs,proto3" json:"gameConfigs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UniAppId    string                   `protobuf:"bytes,6,opt,name=uniAppId,proto3" json:"uniAppId,omitempty"`     // 统一账户Appid
	ShareAppId  string                   `protobuf:"bytes,7,opt,name=shareAppId,proto3" json:"shareAppId,omitempty"` // 共享账户Appid
}

func (x *SyncRequest) Reset() {
	*x = SyncRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRequest) ProtoMessage() {}

func (x *SyncRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRequest.ProtoReflect.Descriptor instead.
func (*SyncRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{2}
}

func (x *SyncRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SyncRequest) GetAssets() []*GameAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *SyncRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *SyncRequest) GetGameConfigs() map[uint32]*GamePlatConf {
	if x != nil {
		return x.GameConfigs
	}
	return nil
}

func (x *SyncRequest) GetUniAppId() string {
	if x != nil {
		return x.UniAppId
	}
	return ""
}

func (x *SyncRequest) GetShareAppId() string {
	if x != nil {
		return x.ShareAppId
	}
	return ""
}

type SyncResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *SyncResponse) Reset() {
	*x = SyncResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncResponse) ProtoMessage() {}

func (x *SyncResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncResponse.ProtoReflect.Descriptor instead.
func (*SyncResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{3}
}

func (x *SyncResponse) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type PlatConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得
	PayType uint32 `protobuf:"varint,1,opt,name=payType,proto3" json:"payType,omitempty"`
	// 道具单价
	GiftOrPropUnitPrice uint32 `protobuf:"varint,2,opt,name=giftOrPropUnitPrice,proto3" json:"giftOrPropUnitPrice,omitempty"`
	// 道具ID，如果支付类型为1,送出这个道具
	GiftOrPropId uint32 `protobuf:"varint,3,opt,name=giftOrPropId,proto3" json:"giftOrPropId,omitempty"`
	// 汇率
	ExchangeRate uint32 `protobuf:"varint,4,opt,name=exchangeRate,proto3" json:"exchangeRate,omitempty"`
	// 平台业务ID
	PlatBusinessID uint32 `protobuf:"varint,5,opt,name=platBusinessID,proto3" json:"platBusinessID,omitempty"`
	// 收入类型 1/计收入 2/不计收入
	RevenueType uint32 `protobuf:"varint,6,opt,name=revenueType,proto3" json:"revenueType,omitempty"`
	// 奖品 id 礼包使用
	RewardId int64 `protobuf:"varint,7,opt,name=rewardId,proto3" json:"rewardId,omitempty"`
	// 购买弹窗提示文案
	PayDesc string `protobuf:"bytes,8,opt,name=payDesc,proto3" json:"payDesc,omitempty"`
	// 购买弹窗档位
	PayGear   []int64 `protobuf:"varint,9,rep,packed,name=payGear,proto3" json:"payGear,omitempty"`
	ExpireUrl string  `protobuf:"bytes,10,opt,name=expireUrl,proto3" json:"expireUrl,omitempty"`
}

func (x *PlatConf) Reset() {
	*x = PlatConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatConf) ProtoMessage() {}

func (x *PlatConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatConf.ProtoReflect.Descriptor instead.
func (*PlatConf) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{4}
}

func (x *PlatConf) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *PlatConf) GetGiftOrPropUnitPrice() uint32 {
	if x != nil {
		return x.GiftOrPropUnitPrice
	}
	return 0
}

func (x *PlatConf) GetGiftOrPropId() uint32 {
	if x != nil {
		return x.GiftOrPropId
	}
	return 0
}

func (x *PlatConf) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *PlatConf) GetPlatBusinessID() uint32 {
	if x != nil {
		return x.PlatBusinessID
	}
	return 0
}

func (x *PlatConf) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *PlatConf) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *PlatConf) GetPayDesc() string {
	if x != nil {
		return x.PayDesc
	}
	return ""
}

func (x *PlatConf) GetPayGear() []int64 {
	if x != nil {
		return x.PayGear
	}
	return nil
}

func (x *PlatConf) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

// 资产配置
type AssetConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AppId      string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon       string `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	ExpireType int64  `protobuf:"varint,5,opt,name=expireType,proto3" json:"expireType,omitempty"`
	ExpireTime int64  `protobuf:"varint,6,opt,name=expireTime,proto3" json:"expireTime,omitempty"`
	// 货币类型 1/游戏币 2/兑换币
	CurrencyType uint32 `protobuf:"varint,7,opt,name=currencyType,proto3" json:"currencyType,omitempty"`
	// 游戏币平台配置，key为平台id，参考gopen.EPlatID
	MapPlatConf map[uint32]*PlatConf `protobuf:"bytes,8,rep,name=mapPlatConf,proto3" json:"mapPlatConf,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Tag         string               `protobuf:"bytes,9,opt,name=tag,proto3" json:"tag,omitempty"`
	// 对应的统一账户资产id
	UniAssetId int64  `protobuf:"varint,10,opt,name=uniAssetId,proto3" json:"uniAssetId,omitempty"`
	ModalType  uint32 `protobuf:"varint,11,opt,name=modalType,proto3" json:"modalType,omitempty"`
	AssetRule  string `protobuf:"bytes,12,opt,name=assetRule,proto3" json:"assetRule,omitempty"`
	SendToUgc  bool   `protobuf:"varint,13,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
}

func (x *AssetConfig) Reset() {
	*x = AssetConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetConfig) ProtoMessage() {}

func (x *AssetConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetConfig.ProtoReflect.Descriptor instead.
func (*AssetConfig) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{5}
}

func (x *AssetConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssetConfig) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AssetConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AssetConfig) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *AssetConfig) GetExpireType() int64 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *AssetConfig) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *AssetConfig) GetCurrencyType() uint32 {
	if x != nil {
		return x.CurrencyType
	}
	return 0
}

func (x *AssetConfig) GetMapPlatConf() map[uint32]*PlatConf {
	if x != nil {
		return x.MapPlatConf
	}
	return nil
}

func (x *AssetConfig) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *AssetConfig) GetUniAssetId() int64 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

func (x *AssetConfig) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *AssetConfig) GetAssetRule() string {
	if x != nil {
		return x.AssetRule
	}
	return ""
}

func (x *AssetConfig) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

type PackageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AppId       string               `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Name        string               `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon        string               `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	GiftDesc    string               `protobuf:"bytes,5,opt,name=giftDesc,proto3" json:"giftDesc,omitempty"`
	MapPlatConf map[uint32]*PlatConf `protobuf:"bytes,8,rep,name=mapPlatConf,proto3" json:"mapPlatConf,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ModalType   uint32               `protobuf:"varint,9,opt,name=modalType,proto3" json:"modalType,omitempty"`
	SendToUgc   bool                 `protobuf:"varint,10,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
	ExtensionId int64                `protobuf:"varint,11,opt,name=extensionId,proto3" json:"extensionId,omitempty"`
}

func (x *PackageConfig) Reset() {
	*x = PackageConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PackageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageConfig) ProtoMessage() {}

func (x *PackageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageConfig.ProtoReflect.Descriptor instead.
func (*PackageConfig) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{6}
}

func (x *PackageConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PackageConfig) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PackageConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PackageConfig) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *PackageConfig) GetGiftDesc() string {
	if x != nil {
		return x.GiftDesc
	}
	return ""
}

func (x *PackageConfig) GetMapPlatConf() map[uint32]*PlatConf {
	if x != nil {
		return x.MapPlatConf
	}
	return nil
}

func (x *PackageConfig) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *PackageConfig) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

func (x *PackageConfig) GetExtensionId() int64 {
	if x != nil {
		return x.ExtensionId
	}
	return 0
}

type GamePlatConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 配置的发奖id
	Prizes []uint32 `protobuf:"varint,1,rep,packed,name=prizes,proto3" json:"prizes,omitempty"`
}

func (x *GamePlatConf) Reset() {
	*x = GamePlatConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePlatConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePlatConf) ProtoMessage() {}

func (x *GamePlatConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePlatConf.ProtoReflect.Descriptor instead.
func (*GamePlatConf) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{7}
}

func (x *GamePlatConf) GetPrizes() []uint32 {
	if x != nil {
		return x.Prizes
	}
	return nil
}

// 资产配置列表
type AssetConfigMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// configs 该游戏各个资产的配置 key为assetId
	Configs map[int64]*AssetConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 配置的发奖
	// repeated PrizeItem prizes = 2; 废弃
	AppName string `protobuf:"bytes,3,opt,name=appName,proto3" json:"appName,omitempty"`
	// 游戏配置，key为平台id，参考gopen.EPlatID
	GameConfigs map[uint32]*GamePlatConf `protobuf:"bytes,4,rep,name=gameConfigs,proto3" json:"gameConfigs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 礼包配置
	PackageConfigs  map[int64]*PackageConfig `protobuf:"bytes,5,rep,name=packageConfigs,proto3" json:"packageConfigs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UniAppId        string                   `protobuf:"bytes,6,opt,name=uniAppId,proto3" json:"uniAppId,omitempty"`               // 统一账户appID
	ShareAppId      string                   `protobuf:"bytes,7,opt,name=shareAppId,proto3" json:"shareAppId,omitempty"`           // 共享账户appID
	UseCustomOpenId string                   `protobuf:"bytes,8,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"` // 使用自定义的OpenId
}

func (x *AssetConfigMap) Reset() {
	*x = AssetConfigMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetConfigMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetConfigMap) ProtoMessage() {}

func (x *AssetConfigMap) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetConfigMap.ProtoReflect.Descriptor instead.
func (*AssetConfigMap) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{8}
}

func (x *AssetConfigMap) GetConfigs() map[int64]*AssetConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

func (x *AssetConfigMap) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AssetConfigMap) GetGameConfigs() map[uint32]*GamePlatConf {
	if x != nil {
		return x.GameConfigs
	}
	return nil
}

func (x *AssetConfigMap) GetPackageConfigs() map[int64]*PackageConfig {
	if x != nil {
		return x.PackageConfigs
	}
	return nil
}

func (x *AssetConfigMap) GetUniAppId() string {
	if x != nil {
		return x.UniAppId
	}
	return ""
}

func (x *AssetConfigMap) GetShareAppId() string {
	if x != nil {
		return x.ShareAppId
	}
	return ""
}

func (x *AssetConfigMap) GetUseCustomOpenId() string {
	if x != nil {
		return x.UseCustomOpenId
	}
	return ""
}

type AdminQueryUserAssetsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Uid   uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *AdminQueryUserAssetsReq) Reset() {
	*x = AdminQueryUserAssetsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminQueryUserAssetsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminQueryUserAssetsReq) ProtoMessage() {}

func (x *AdminQueryUserAssetsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminQueryUserAssetsReq.ProtoReflect.Descriptor instead.
func (*AdminQueryUserAssetsReq) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{9}
}

func (x *AdminQueryUserAssetsReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AdminQueryUserAssetsReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type AdminQueryUserAssetsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *AdminQueryUserAssetsRsp) Reset() {
	*x = AdminQueryUserAssetsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdminQueryUserAssetsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminQueryUserAssetsRsp) ProtoMessage() {}

func (x *AdminQueryUserAssetsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminQueryUserAssetsRsp.ProtoReflect.Descriptor instead.
func (*AdminQueryUserAssetsRsp) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{10}
}

func (x *AdminQueryUserAssetsRsp) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type ExpireConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpireAppIdList []string `protobuf:"bytes,1,rep,name=expireAppIdList,proto3" json:"expireAppIdList,omitempty"`
}

func (x *ExpireConfig) Reset() {
	*x = ExpireConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireConfig) ProtoMessage() {}

func (x *ExpireConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_admin_asset_admin_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireConfig.ProtoReflect.Descriptor instead.
func (*ExpireConfig) Descriptor() ([]byte, []int) {
	return file_pb_asset_admin_asset_admin_proto_rawDescGZIP(), []int{11}
}

func (x *ExpireConfig) GetExpireAppIdList() []string {
	if x != nil {
		return x.ExpireAppIdList
	}
	return nil
}

var File_pb_asset_admin_asset_admin_proto protoreflect.FileDescriptor

var file_pb_asset_admin_asset_admin_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xf7, 0x04, 0x0a, 0x09, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c,
	0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x67, 0x69, 0x66, 0x74,
	0x4f, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x67, 0x69, 0x66, 0x74, 0x4f, 0x72, 0x50, 0x72, 0x6f,
	0x70, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x69,
	0x66, 0x74, 0x4f, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x67, 0x69, 0x66, 0x74, 0x4f, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1e,
	0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x61, 0x79, 0x44, 0x65, 0x73, 0x63, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x47,
	0x65, 0x61, 0x72, 0x18, 0x12, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x79, 0x47, 0x65,
	0x61, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x22, 0x27, 0x0a, 0x09, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x22, 0xda, 0x02, 0x0a, 0x0b, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x1a, 0x5c, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x6c, 0x61,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x24, 0x0a, 0x0c, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0xd6, 0x02, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a,
	0x13, 0x67, 0x69, 0x66, 0x74, 0x4f, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x55, 0x6e, 0x69, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x67, 0x69, 0x66, 0x74,
	0x4f, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x67, 0x69, 0x66, 0x74, 0x4f, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x67, 0x69, 0x66, 0x74, 0x4f, 0x72, 0x50, 0x72, 0x6f,
	0x70, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12,
	0x20, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x61, 0x79, 0x44, 0x65, 0x73, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x47, 0x65,
	0x61, 0x72, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x79, 0x47, 0x65, 0x61,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x22,
	0xf5, 0x03, 0x0a, 0x0b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x4e, 0x0a, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x74, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x1a, 0x58, 0x0a,
	0x10, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x83, 0x03, 0x0a, 0x0d, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x44,
	0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x50, 0x0a, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67,
	0x63, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x1a, 0x58, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x26, 0x0a,
	0x0c, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x70,
	0x72, 0x69, 0x7a, 0x65, 0x73, 0x22, 0x9f, 0x05, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x45, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x0b, 0x67, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0b, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x5a, 0x0a, 0x0e,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x4d, 0x61, 0x70, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x75,
	0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x1a, 0x57,
	0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5c, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x60, 0x0a, 0x13, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x41, 0x0a, 0x17, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x17, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x38,
	0x0a, 0x0c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x28,
	0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x32, 0xfc, 0x01, 0x0a, 0x0a, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x5f, 0x0a, 0x04, 0x53, 0x79, 0x6e, 0x63, 0x12,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x8c, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x27, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x73, 0x70, 0x22, 0x27,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x42, 0x5a, 0x40, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_asset_admin_asset_admin_proto_rawDescOnce sync.Once
	file_pb_asset_admin_asset_admin_proto_rawDescData = file_pb_asset_admin_asset_admin_proto_rawDesc
)

func file_pb_asset_admin_asset_admin_proto_rawDescGZIP() []byte {
	file_pb_asset_admin_asset_admin_proto_rawDescOnce.Do(func() {
		file_pb_asset_admin_asset_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_asset_admin_asset_admin_proto_rawDescData)
	})
	return file_pb_asset_admin_asset_admin_proto_rawDescData
}

var file_pb_asset_admin_asset_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_pb_asset_admin_asset_admin_proto_goTypes = []interface{}{
	(*GameAsset)(nil),               // 0: component.game.GameAsset
	(*PrizeItem)(nil),               // 1: component.game.PrizeItem
	(*SyncRequest)(nil),             // 2: component.game.SyncRequest
	(*SyncResponse)(nil),            // 3: component.game.SyncResponse
	(*PlatConf)(nil),                // 4: component.game.PlatConf
	(*AssetConfig)(nil),             // 5: component.game.AssetConfig
	(*PackageConfig)(nil),           // 6: component.game.PackageConfig
	(*GamePlatConf)(nil),            // 7: component.game.GamePlatConf
	(*AssetConfigMap)(nil),          // 8: component.game.AssetConfigMap
	(*AdminQueryUserAssetsReq)(nil), // 9: component.game.AdminQueryUserAssetsReq
	(*AdminQueryUserAssetsRsp)(nil), // 10: component.game.AdminQueryUserAssetsRsp
	(*ExpireConfig)(nil),            // 11: component.game.ExpireConfig
	nil,                             // 12: component.game.SyncRequest.GameConfigsEntry
	nil,                             // 13: component.game.AssetConfig.MapPlatConfEntry
	nil,                             // 14: component.game.PackageConfig.MapPlatConfEntry
	nil,                             // 15: component.game.AssetConfigMap.ConfigsEntry
	nil,                             // 16: component.game.AssetConfigMap.GameConfigsEntry
	nil,                             // 17: component.game.AssetConfigMap.PackageConfigsEntry
}
var file_pb_asset_admin_asset_admin_proto_depIdxs = []int32{
	0,  // 0: component.game.SyncRequest.assets:type_name -> component.game.GameAsset
	12, // 1: component.game.SyncRequest.gameConfigs:type_name -> component.game.SyncRequest.GameConfigsEntry
	13, // 2: component.game.AssetConfig.mapPlatConf:type_name -> component.game.AssetConfig.MapPlatConfEntry
	14, // 3: component.game.PackageConfig.mapPlatConf:type_name -> component.game.PackageConfig.MapPlatConfEntry
	15, // 4: component.game.AssetConfigMap.configs:type_name -> component.game.AssetConfigMap.ConfigsEntry
	16, // 5: component.game.AssetConfigMap.gameConfigs:type_name -> component.game.AssetConfigMap.GameConfigsEntry
	17, // 6: component.game.AssetConfigMap.packageConfigs:type_name -> component.game.AssetConfigMap.PackageConfigsEntry
	7,  // 7: component.game.SyncRequest.GameConfigsEntry.value:type_name -> component.game.GamePlatConf
	4,  // 8: component.game.AssetConfig.MapPlatConfEntry.value:type_name -> component.game.PlatConf
	4,  // 9: component.game.PackageConfig.MapPlatConfEntry.value:type_name -> component.game.PlatConf
	5,  // 10: component.game.AssetConfigMap.ConfigsEntry.value:type_name -> component.game.AssetConfig
	7,  // 11: component.game.AssetConfigMap.GameConfigsEntry.value:type_name -> component.game.GamePlatConf
	6,  // 12: component.game.AssetConfigMap.PackageConfigsEntry.value:type_name -> component.game.PackageConfig
	2,  // 13: component.game.AssetAdmin.Sync:input_type -> component.game.SyncRequest
	9,  // 14: component.game.AssetAdmin.QueryUserAssets:input_type -> component.game.AdminQueryUserAssetsReq
	3,  // 15: component.game.AssetAdmin.Sync:output_type -> component.game.SyncResponse
	10, // 16: component.game.AssetAdmin.QueryUserAssets:output_type -> component.game.AdminQueryUserAssetsRsp
	15, // [15:17] is the sub-list for method output_type
	13, // [13:15] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_pb_asset_admin_asset_admin_proto_init() }
func file_pb_asset_admin_asset_admin_proto_init() {
	if File_pb_asset_admin_asset_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_asset_admin_asset_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrizeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PackageConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePlatConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetConfigMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminQueryUserAssetsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdminQueryUserAssetsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_admin_asset_admin_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_asset_admin_asset_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_asset_admin_asset_admin_proto_goTypes,
		DependencyIndexes: file_pb_asset_admin_asset_admin_proto_depIdxs,
		MessageInfos:      file_pb_asset_admin_asset_admin_proto_msgTypes,
	}.Build()
	File_pb_asset_admin_asset_admin_proto = out.File
	file_pb_asset_admin_asset_admin_proto_rawDesc = nil
	file_pb_asset_admin_asset_admin_proto_goTypes = nil
	file_pb_asset_admin_asset_admin_proto_depIdxs = nil
}
