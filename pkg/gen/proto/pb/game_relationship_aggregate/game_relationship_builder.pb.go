// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_relationship_aggregate/game_relationship_builder.proto

package game_relationship_aggregate

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	game_relationship "kugou_adapter_service/pkg/gen/proto/pb/game_relationship"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HeartType int32

const (
	HeartType_ReportTypeUnknown   HeartType = 0
	HeartType_ReportTypeEnter     HeartType = 1 // 进入
	HeartType_ReportTypeHeartbeat HeartType = 2 // 心跳
	HeartType_ReportTypeLeave     HeartType = 3 // 离开
)

// Enum value maps for HeartType.
var (
	HeartType_name = map[int32]string{
		0: "ReportTypeUnknown",
		1: "ReportTypeEnter",
		2: "ReportTypeHeartbeat",
		3: "ReportTypeLeave",
	}
	HeartType_value = map[string]int32{
		"ReportTypeUnknown":   0,
		"ReportTypeEnter":     1,
		"ReportTypeHeartbeat": 2,
		"ReportTypeLeave":     3,
	}
)

func (x HeartType) Enum() *HeartType {
	p := new(HeartType)
	*p = x
	return p
}

func (x HeartType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HeartType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_enumTypes[0].Descriptor()
}

func (HeartType) Type() protoreflect.EnumType {
	return &file_pb_game_relationship_aggregate_game_relationship_builder_proto_enumTypes[0]
}

func (x HeartType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HeartType.Descriptor instead.
func (HeartType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescGZIP(), []int{0}
}

type LoginReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   int64  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppId string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Ts    int64  `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *LoginReportReq) Reset() {
	*x = LoginReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReportReq) ProtoMessage() {}

func (x *LoginReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReportReq.ProtoReflect.Descriptor instead.
func (*LoginReportReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescGZIP(), []int{0}
}

func (x *LoginReportReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *LoginReportReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *LoginReportReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type LoginReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *LoginReportRsp) Reset() {
	*x = LoginReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReportRsp) ProtoMessage() {}

func (x *LoginReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReportRsp.ProtoReflect.Descriptor instead.
func (*LoginReportRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescGZIP(), []int{1}
}

func (x *LoginReportRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type HeartbeatReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid           int64                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HeartbeatType HeartType                  `protobuf:"varint,2,opt,name=heartbeatType,proto3,enum=component.game.HeartType" json:"heartbeatType,omitempty"`
	RoomType      game_relationship.RoomType `protobuf:"varint,3,opt,name=roomType,proto3,enum=component.game.RoomType" json:"roomType,omitempty"`
	RoomId        string                     `protobuf:"bytes,4,opt,name=roomId,proto3" json:"roomId,omitempty"`
	ShowId        string                     `protobuf:"bytes,5,opt,name=showId,proto3" json:"showId,omitempty"`
	BeginTime     int64                      `protobuf:"varint,6,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	ReportTime    int64                      `protobuf:"varint,7,opt,name=reportTime,proto3" json:"reportTime,omitempty"`
}

func (x *HeartbeatReportReq) Reset() {
	*x = HeartbeatReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReportReq) ProtoMessage() {}

func (x *HeartbeatReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReportReq.ProtoReflect.Descriptor instead.
func (*HeartbeatReportReq) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescGZIP(), []int{2}
}

func (x *HeartbeatReportReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *HeartbeatReportReq) GetHeartbeatType() HeartType {
	if x != nil {
		return x.HeartbeatType
	}
	return HeartType_ReportTypeUnknown
}

func (x *HeartbeatReportReq) GetRoomType() game_relationship.RoomType {
	if x != nil {
		return x.RoomType
	}
	return game_relationship.RoomType(0)
}

func (x *HeartbeatReportReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *HeartbeatReportReq) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

func (x *HeartbeatReportReq) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *HeartbeatReportReq) GetReportTime() int64 {
	if x != nil {
		return x.ReportTime
	}
	return 0
}

type HeartbeatReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *HeartbeatReportRsp) Reset() {
	*x = HeartbeatReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReportRsp) ProtoMessage() {}

func (x *HeartbeatReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReportRsp.ProtoReflect.Descriptor instead.
func (*HeartbeatReportRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescGZIP(), []int{3}
}

func (x *HeartbeatReportRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_pb_game_relationship_aggregate_game_relationship_builder_proto protoreflect.FileDescriptor

var file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68,
	0x69, 0x70, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x48, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0x22, 0x0a, 0x0e, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x8b, 0x02,
	0x0a, 0x12, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x26, 0x0a, 0x12, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x2a, 0x65, 0x0a, 0x09, 0x48, 0x65, 0x61, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62,
	0x65, 0x61, 0x74, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x10, 0x03, 0x32, 0xf9, 0x01, 0x0a, 0x15, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x12, 0x77, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65,
	0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22,
	0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x68, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x67, 0x0a,
	0x0b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x1e, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0x18, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x53, 0x5a, 0x51, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69,
	0x70, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescOnce sync.Once
	file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescData = file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDesc
)

func file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescGZIP() []byte {
	file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescOnce.Do(func() {
		file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescData)
	})
	return file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDescData
}

var file_pb_game_relationship_aggregate_game_relationship_builder_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_game_relationship_aggregate_game_relationship_builder_proto_goTypes = []interface{}{
	(HeartType)(0),                  // 0: component.game.HeartType
	(*LoginReportReq)(nil),          // 1: component.game.LoginReportReq
	(*LoginReportRsp)(nil),          // 2: component.game.LoginReportRsp
	(*HeartbeatReportReq)(nil),      // 3: component.game.HeartbeatReportReq
	(*HeartbeatReportRsp)(nil),      // 4: component.game.HeartbeatReportRsp
	(game_relationship.RoomType)(0), // 5: component.game.RoomType
}
var file_pb_game_relationship_aggregate_game_relationship_builder_proto_depIdxs = []int32{
	0, // 0: component.game.HeartbeatReportReq.heartbeatType:type_name -> component.game.HeartType
	5, // 1: component.game.HeartbeatReportReq.roomType:type_name -> component.game.RoomType
	3, // 2: component.game.RelationshipAggregate.ReportHeartbeat:input_type -> component.game.HeartbeatReportReq
	1, // 3: component.game.RelationshipAggregate.ReportLogin:input_type -> component.game.LoginReportReq
	4, // 4: component.game.RelationshipAggregate.ReportHeartbeat:output_type -> component.game.HeartbeatReportRsp
	2, // 5: component.game.RelationshipAggregate.ReportLogin:output_type -> component.game.LoginReportRsp
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_game_relationship_aggregate_game_relationship_builder_proto_init() }
func file_pb_game_relationship_aggregate_game_relationship_builder_proto_init() {
	if File_pb_game_relationship_aggregate_game_relationship_builder_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_relationship_aggregate_game_relationship_builder_proto_goTypes,
		DependencyIndexes: file_pb_game_relationship_aggregate_game_relationship_builder_proto_depIdxs,
		EnumInfos:         file_pb_game_relationship_aggregate_game_relationship_builder_proto_enumTypes,
		MessageInfos:      file_pb_game_relationship_aggregate_game_relationship_builder_proto_msgTypes,
	}.Build()
	File_pb_game_relationship_aggregate_game_relationship_builder_proto = out.File
	file_pb_game_relationship_aggregate_game_relationship_builder_proto_rawDesc = nil
	file_pb_game_relationship_aggregate_game_relationship_builder_proto_goTypes = nil
	file_pb_game_relationship_aggregate_game_relationship_builder_proto_depIdxs = nil
}
