{"swagger": "2.0", "info": {"title": "pb/asset_api/asset_api.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game.AssetApi/Balance": {"post": {"summary": "查询资产", "operationId": "AssetApi_Balance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBalanceReq"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "/game.AssetApi/FlowerBalance": {"post": {"summary": "查询鲜花", "operationId": "AssetApi_FlowerBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameFlowerBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameFlowerBalanceReq"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"]}}, "/game.AssetApi/RecordList": {"post": {"summary": "获取RecordList", "operationId": "AssetApi_RecordList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameRecordListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameRecordListReq"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"]}}}, "definitions": {"gameBalanceReq": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产id"}, "requireExpireInfo": {"type": "boolean", "title": "是否需要过期信息"}}}, "gameBalanceRsp": {"type": "object", "properties": {"asset": {"$ref": "#/definitions/gameUserAsset", "title": "结果资产列表"}, "nowTs": {"type": "string", "format": "int64", "title": "当前时间戳"}}}, "gameExpireInfo": {"type": "object", "properties": {"assetNum": {"type": "string", "format": "int64", "title": "资产数量"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间戳 单位秒"}}}, "gameFlowerBalanceReq": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64"}}}, "gameFlowerBalanceRsp": {"type": "object", "properties": {"balance": {"type": "string", "format": "int64"}, "icon": {"type": "string"}, "name": {"type": "string"}}}, "gameRecordItem": {"type": "object", "properties": {"strIcon": {"type": "string"}, "strTitle": {"type": "string"}, "strTag": {"type": "string"}, "iNum": {"type": "integer", "format": "int32"}, "uTimestamp": {"type": "integer", "format": "int64"}}}, "gameRecordListReq": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 ID"}, "cursor": {"type": "string", "title": "透传字段 第一次传空"}, "pageSize": {"type": "string", "format": "int64", "title": "页大小 默认 20"}}}, "gameRecordListRsp": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameRecordItem"}}, "cursor": {"type": "string"}, "next": {"type": "boolean", "title": "是否有下一页"}}}, "gameUserAsset": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量"}, "expires": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExpireInfo"}, "title": "过期信息"}, "name": {"type": "string", "title": "资产名称"}, "icon": {"type": "string", "title": "资产图标"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}