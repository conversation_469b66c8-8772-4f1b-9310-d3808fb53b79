// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/asset_api/asset_api.proto

package asset_api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AssetApi_RecordList_FullMethodName    = "/game.AssetApi/RecordList"
	AssetApi_Balance_FullMethodName       = "/game.AssetApi/Balance"
	AssetApi_FlowerBalance_FullMethodName = "/game.AssetApi/FlowerBalance"
)

// AssetApiClient is the client API for AssetApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 与 web 交互
type AssetApiClient interface {
	// 获取RecordList
	RecordList(ctx context.Context, in *RecordListReq, opts ...grpc.CallOption) (*RecordListRsp, error)
	// 查询资产
	Balance(ctx context.Context, in *BalanceReq, opts ...grpc.CallOption) (*BalanceRsp, error)
	// 查询鲜花
	FlowerBalance(ctx context.Context, in *FlowerBalanceReq, opts ...grpc.CallOption) (*FlowerBalanceRsp, error)
}

type assetApiClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetApiClient(cc grpc.ClientConnInterface) AssetApiClient {
	return &assetApiClient{cc}
}

func (c *assetApiClient) RecordList(ctx context.Context, in *RecordListReq, opts ...grpc.CallOption) (*RecordListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecordListRsp)
	err := c.cc.Invoke(ctx, AssetApi_RecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetApiClient) Balance(ctx context.Context, in *BalanceReq, opts ...grpc.CallOption) (*BalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BalanceRsp)
	err := c.cc.Invoke(ctx, AssetApi_Balance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetApiClient) FlowerBalance(ctx context.Context, in *FlowerBalanceReq, opts ...grpc.CallOption) (*FlowerBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FlowerBalanceRsp)
	err := c.cc.Invoke(ctx, AssetApi_FlowerBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetApiServer is the server API for AssetApi service.
// All implementations should embed UnimplementedAssetApiServer
// for forward compatibility
//
// 与 web 交互
type AssetApiServer interface {
	// 获取RecordList
	RecordList(context.Context, *RecordListReq) (*RecordListRsp, error)
	// 查询资产
	Balance(context.Context, *BalanceReq) (*BalanceRsp, error)
	// 查询鲜花
	FlowerBalance(context.Context, *FlowerBalanceReq) (*FlowerBalanceRsp, error)
}

// UnimplementedAssetApiServer should be embedded to have forward compatible implementations.
type UnimplementedAssetApiServer struct {
}

func (UnimplementedAssetApiServer) RecordList(context.Context, *RecordListReq) (*RecordListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordList not implemented")
}
func (UnimplementedAssetApiServer) Balance(context.Context, *BalanceReq) (*BalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Balance not implemented")
}
func (UnimplementedAssetApiServer) FlowerBalance(context.Context, *FlowerBalanceReq) (*FlowerBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlowerBalance not implemented")
}

// UnsafeAssetApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetApiServer will
// result in compilation errors.
type UnsafeAssetApiServer interface {
	mustEmbedUnimplementedAssetApiServer()
}

func RegisterAssetApiServer(s grpc.ServiceRegistrar, srv AssetApiServer) {
	s.RegisterService(&AssetApi_ServiceDesc, srv)
}

func _AssetApi_RecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetApiServer).RecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetApi_RecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetApiServer).RecordList(ctx, req.(*RecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetApi_Balance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetApiServer).Balance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetApi_Balance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetApiServer).Balance(ctx, req.(*BalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssetApi_FlowerBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlowerBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetApiServer).FlowerBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssetApi_FlowerBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetApiServer).FlowerBalance(ctx, req.(*FlowerBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AssetApi_ServiceDesc is the grpc.ServiceDesc for AssetApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AssetApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.AssetApi",
	HandlerType: (*AssetApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecordList",
			Handler:    _AssetApi_RecordList_Handler,
		},
		{
			MethodName: "Balance",
			Handler:    _AssetApi_Balance_Handler,
		},
		{
			MethodName: "FlowerBalance",
			Handler:    _AssetApi_FlowerBalance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/asset_api/asset_api.proto",
}
