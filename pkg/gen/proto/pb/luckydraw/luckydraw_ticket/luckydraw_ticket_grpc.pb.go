// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/luckydraw/luckydraw_ticket/luckydraw_ticket.proto

package luckydraw

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	luckydraw_callback "kugou_adapter_service/pkg/gen/proto/pb/luckydraw/luckydraw_callback"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Ticketsvr_AddTicket_FullMethodName    = "/luckydraw.ticketsvr/AddTicket"
	Ticketsvr_SubTicket_FullMethodName    = "/luckydraw.ticketsvr/SubTicket"
	Ticketsvr_GetTicketCnt_FullMethodName = "/luckydraw.ticketsvr/GetTicketCnt"
)

// TicketsvrClient is the client API for Ticketsvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketsvrClient interface {
	// 加抽奖券
	AddTicket(ctx context.Context, in *AddTicketReq, opts ...grpc.CallOption) (*AddTicketRsp, error)
	// 扣抽奖券
	SubTicket(ctx context.Context, in *luckydraw_callback.SubTicketReq, opts ...grpc.CallOption) (*luckydraw_callback.SubTicketRsp, error)
	// 查询抽奖券数量
	GetTicketCnt(ctx context.Context, in *GetTicketCntReq, opts ...grpc.CallOption) (*GetTicketCntRsp, error)
}

type ticketsvrClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketsvrClient(cc grpc.ClientConnInterface) TicketsvrClient {
	return &ticketsvrClient{cc}
}

func (c *ticketsvrClient) AddTicket(ctx context.Context, in *AddTicketReq, opts ...grpc.CallOption) (*AddTicketRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddTicketRsp)
	err := c.cc.Invoke(ctx, Ticketsvr_AddTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketsvrClient) SubTicket(ctx context.Context, in *luckydraw_callback.SubTicketReq, opts ...grpc.CallOption) (*luckydraw_callback.SubTicketRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(luckydraw_callback.SubTicketRsp)
	err := c.cc.Invoke(ctx, Ticketsvr_SubTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketsvrClient) GetTicketCnt(ctx context.Context, in *GetTicketCntReq, opts ...grpc.CallOption) (*GetTicketCntRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTicketCntRsp)
	err := c.cc.Invoke(ctx, Ticketsvr_GetTicketCnt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketsvrServer is the server API for Ticketsvr service.
// All implementations should embed UnimplementedTicketsvrServer
// for forward compatibility
type TicketsvrServer interface {
	// 加抽奖券
	AddTicket(context.Context, *AddTicketReq) (*AddTicketRsp, error)
	// 扣抽奖券
	SubTicket(context.Context, *luckydraw_callback.SubTicketReq) (*luckydraw_callback.SubTicketRsp, error)
	// 查询抽奖券数量
	GetTicketCnt(context.Context, *GetTicketCntReq) (*GetTicketCntRsp, error)
}

// UnimplementedTicketsvrServer should be embedded to have forward compatible implementations.
type UnimplementedTicketsvrServer struct {
}

func (UnimplementedTicketsvrServer) AddTicket(context.Context, *AddTicketReq) (*AddTicketRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTicket not implemented")
}
func (UnimplementedTicketsvrServer) SubTicket(context.Context, *luckydraw_callback.SubTicketReq) (*luckydraw_callback.SubTicketRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubTicket not implemented")
}
func (UnimplementedTicketsvrServer) GetTicketCnt(context.Context, *GetTicketCntReq) (*GetTicketCntRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicketCnt not implemented")
}

// UnsafeTicketsvrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketsvrServer will
// result in compilation errors.
type UnsafeTicketsvrServer interface {
	mustEmbedUnimplementedTicketsvrServer()
}

func RegisterTicketsvrServer(s grpc.ServiceRegistrar, srv TicketsvrServer) {
	s.RegisterService(&Ticketsvr_ServiceDesc, srv)
}

func _Ticketsvr_AddTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketsvrServer).AddTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticketsvr_AddTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketsvrServer).AddTicket(ctx, req.(*AddTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticketsvr_SubTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(luckydraw_callback.SubTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketsvrServer).SubTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticketsvr_SubTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketsvrServer).SubTicket(ctx, req.(*luckydraw_callback.SubTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticketsvr_GetTicketCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketsvrServer).GetTicketCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticketsvr_GetTicketCnt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketsvrServer).GetTicketCnt(ctx, req.(*GetTicketCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Ticketsvr_ServiceDesc is the grpc.ServiceDesc for Ticketsvr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ticketsvr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "luckydraw.ticketsvr",
	HandlerType: (*TicketsvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddTicket",
			Handler:    _Ticketsvr_AddTicket_Handler,
		},
		{
			MethodName: "SubTicket",
			Handler:    _Ticketsvr_SubTicket_Handler,
		},
		{
			MethodName: "GetTicketCnt",
			Handler:    _Ticketsvr_GetTicketCnt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/luckydraw/luckydraw_ticket/luckydraw_ticket.proto",
}
