// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/luckydraw/luckydraw_sync/luckydraw_sync.proto

package luckydraw

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/luckydraw/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DoSyncReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId string `protobuf:"bytes,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"` // 活动id
	PlatId     uint32 `protobuf:"varint,2,opt,name=plat_id,json=platId,proto3" json:"plat_id,omitempty"`            // 平台id, see EnumPlatId
}

func (x *DoSyncReq) Reset() {
	*x = DoSyncReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoSyncReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoSyncReq) ProtoMessage() {}

func (x *DoSyncReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoSyncReq.ProtoReflect.Descriptor instead.
func (*DoSyncReq) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescGZIP(), []int{0}
}

func (x *DoSyncReq) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *DoSyncReq) GetPlatId() uint32 {
	if x != nil {
		return x.PlatId
	}
	return 0
}

type DoSyncRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.CommRet `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
}

func (x *DoSyncRsp) Reset() {
	*x = DoSyncRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoSyncRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoSyncRsp) ProtoMessage() {}

func (x *DoSyncRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoSyncRsp.ProtoReflect.Descriptor instead.
func (*DoSyncRsp) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescGZIP(), []int{1}
}

func (x *DoSyncRsp) GetRet() *common.CommRet {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto protoreflect.FileDescriptor

var file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDesc = []byte{
	0x0a, 0x30, 0x70, 0x62, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2f, 0x6c,
	0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2f, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x09, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x1a, 0x28, 0x70,
	0x62, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x45, 0x0a, 0x09, 0x44, 0x6f, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x49, 0x64, 0x22, 0x38,
	0x0a, 0x09, 0x44, 0x6f, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79,
	0x64, 0x72, 0x61, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x52, 0x65, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x32, 0x3c, 0x0a, 0x04, 0x73, 0x79, 0x6e, 0x63,
	0x12, 0x34, 0x0a, 0x06, 0x44, 0x6f, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x14, 0x2e, 0x6c, 0x75, 0x63,
	0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x44, 0x6f, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71,
	0x1a, 0x14, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x44, 0x6f, 0x53,
	0x79, 0x6e, 0x63, 0x52, 0x73, 0x70, 0x42, 0x41, 0x5a, 0x3f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescOnce sync.Once
	file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescData = file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDesc
)

func file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescGZIP() []byte {
	file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescOnce.Do(func() {
		file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescData)
	})
	return file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDescData
}

var file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_goTypes = []interface{}{
	(*DoSyncReq)(nil),      // 0: luckydraw.DoSyncReq
	(*DoSyncRsp)(nil),      // 1: luckydraw.DoSyncRsp
	(*common.CommRet)(nil), // 2: luckydraw.common.CommRet
}
var file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_depIdxs = []int32{
	2, // 0: luckydraw.DoSyncRsp.ret:type_name -> luckydraw.common.CommRet
	0, // 1: luckydraw.sync.DoSync:input_type -> luckydraw.DoSyncReq
	1, // 2: luckydraw.sync.DoSync:output_type -> luckydraw.DoSyncRsp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_init() }
func file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_init() {
	if File_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoSyncReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoSyncRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_goTypes,
		DependencyIndexes: file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_depIdxs,
		MessageInfos:      file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_msgTypes,
	}.Build()
	File_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto = out.File
	file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_rawDesc = nil
	file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_goTypes = nil
	file_pb_luckydraw_luckydraw_sync_luckydraw_sync_proto_depIdxs = nil
}
