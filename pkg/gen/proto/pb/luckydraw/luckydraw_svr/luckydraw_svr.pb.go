// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/luckydraw/luckydraw_svr/luckydraw_svr.proto

package luckydraw

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/luckydraw/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 奖项信息
type AwardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                            // 奖项id
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                        // 奖项名称
	AwardId      string `protobuf:"bytes,3,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`                   // 奖励id
	AwardType    uint32 `protobuf:"varint,4,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`            // 奖项类型, see EnumAwardType
	AwardSubType uint32 `protobuf:"varint,5,opt,name=award_sub_type,json=awardSubType,proto3" json:"award_sub_type,omitempty"` // 奖项子类型, see EnumAwardSubType
	Pic          string `protobuf:"bytes,6,opt,name=pic,proto3" json:"pic,omitempty"`                                          // 奖项图片
	Remarks      string `protobuf:"bytes,7,opt,name=remarks,proto3" json:"remarks,omitempty"`                                  // 万能备注
	Doc          string `protobuf:"bytes,8,opt,name=doc,proto3" json:"doc,omitempty"`                                          // 中奖文案
}

func (x *AwardInfo) Reset() {
	*x = AwardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AwardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AwardInfo) ProtoMessage() {}

func (x *AwardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AwardInfo.ProtoReflect.Descriptor instead.
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{0}
}

func (x *AwardInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AwardInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AwardInfo) GetAwardId() string {
	if x != nil {
		return x.AwardId
	}
	return ""
}

func (x *AwardInfo) GetAwardType() uint32 {
	if x != nil {
		return x.AwardType
	}
	return 0
}

func (x *AwardInfo) GetAwardSubType() uint32 {
	if x != nil {
		return x.AwardSubType
	}
	return 0
}

func (x *AwardInfo) GetPic() string {
	if x != nil {
		return x.Pic
	}
	return ""
}

func (x *AwardInfo) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AwardInfo) GetDoc() string {
	if x != nil {
		return x.Doc
	}
	return ""
}

// 返回错误码说明：
// ERR_CODE_NEED_SAFE_VERIFY   = 10030; // 错误提示语: 验证码url
// ERR_CODE_TICKET_NOT_ENOUGH  = 22403; // 抽奖券不足
// ERR_CODE_ASYNC_LOTTERY      = 22405; // 抽奖进行中，稍后留意抽奖结果
// RetOverLimit                = 5018;   // 请求被限流
// ERR_CODE_NOT_FOUND          = 22408; // 活动不存在
type DoLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid         string            `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`                                                                                           // appid
	Openid        string            `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`                                                                                         // 用户openid
	ActivityId    string            `protobuf:"bytes,3,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`                                                               // 抽奖活动id
	Params        map[string]string `protobuf:"bytes,4,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传参数
	CustomizeKey  string            `protobuf:"bytes,5,opt,name=customize_key,json=customizeKey,proto3" json:"customize_key,omitempty"`                                                         // 业务自定义key
	CustomizeMask int64             `protobuf:"varint,6,opt,name=customize_mask,json=customizeMask,proto3" json:"customize_mask,omitempty"`                                                     // 业务自定义key的mask, see EnumCustomizeKeyMask
	BizId         string            `protobuf:"bytes,7,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`                                                                              // 业务幂等id
}

func (x *DoLotteryReq) Reset() {
	*x = DoLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoLotteryReq) ProtoMessage() {}

func (x *DoLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoLotteryReq.ProtoReflect.Descriptor instead.
func (*DoLotteryReq) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{1}
}

func (x *DoLotteryReq) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *DoLotteryReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *DoLotteryReq) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *DoLotteryReq) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *DoLotteryReq) GetCustomizeKey() string {
	if x != nil {
		return x.CustomizeKey
	}
	return ""
}

func (x *DoLotteryReq) GetCustomizeMask() int64 {
	if x != nil {
		return x.CustomizeMask
	}
	return 0
}

func (x *DoLotteryReq) GetBizId() string {
	if x != nil {
		return x.BizId
	}
	return ""
}

type DoLotteryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret          *common.CommRet      `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Award        *AwardInfo           `protobuf:"bytes,2,opt,name=award,proto3" json:"award,omitempty"`
	SafetyParams *common.SafetyParams `protobuf:"bytes,3,opt,name=safety_params,json=safetyParams,proto3" json:"safety_params,omitempty"` // 安全打击的参数
	Seqid        string               `protobuf:"bytes,4,opt,name=seqid,proto3" json:"seqid,omitempty"`                                   // 抽奖唯一id
}

func (x *DoLotteryRsp) Reset() {
	*x = DoLotteryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoLotteryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoLotteryRsp) ProtoMessage() {}

func (x *DoLotteryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoLotteryRsp.ProtoReflect.Descriptor instead.
func (*DoLotteryRsp) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{2}
}

func (x *DoLotteryRsp) GetRet() *common.CommRet {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *DoLotteryRsp) GetAward() *AwardInfo {
	if x != nil {
		return x.Award
	}
	return nil
}

func (x *DoLotteryRsp) GetSafetyParams() *common.SafetyParams {
	if x != nil {
		return x.SafetyParams
	}
	return nil
}

func (x *DoLotteryRsp) GetSeqid() string {
	if x != nil {
		return x.Seqid
	}
	return ""
}

type GetActivityDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid      string            `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`                                                                                           // appid
	Openid     string            `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`                                                                                         // 用户openid(可选)
	ActivityId string            `protobuf:"bytes,3,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`                                                               // 抽奖活动id
	Params     map[string]string `protobuf:"bytes,4,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传参数
}

func (x *GetActivityDetailReq) Reset() {
	*x = GetActivityDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityDetailReq) ProtoMessage() {}

func (x *GetActivityDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityDetailReq.ProtoReflect.Descriptor instead.
func (*GetActivityDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{3}
}

func (x *GetActivityDetailReq) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *GetActivityDetailReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *GetActivityDetailReq) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *GetActivityDetailReq) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

type GetActivityDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.CommRet `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	ActivityId string          `protobuf:"bytes,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`  // 抽奖活动id
	Name       string          `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                // 活动名称
	BeginTs    int64           `protobuf:"varint,4,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`          // 活动开始时间戳
	EndTs      int64           `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`                // 活动结束时间戳
	ServerTs   int64           `protobuf:"varint,6,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`       // 服务器当前时间戳
	TicketCnt  uint64          `protobuf:"varint,7,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`    // 用户拥有的抽奖券数量(openid有传才有,否则为0)
	TicketCost uint64          `protobuf:"varint,8,opt,name=ticket_cost,json=ticketCost,proto3" json:"ticket_cost,omitempty"` // 一次抽奖消耗抽奖券数量
	AwardList  []*AwardInfo    `protobuf:"bytes,9,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`     // 奖项信息
}

func (x *GetActivityDetailRsp) Reset() {
	*x = GetActivityDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityDetailRsp) ProtoMessage() {}

func (x *GetActivityDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityDetailRsp.ProtoReflect.Descriptor instead.
func (*GetActivityDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{4}
}

func (x *GetActivityDetailRsp) GetRet() *common.CommRet {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetActivityDetailRsp) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *GetActivityDetailRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetActivityDetailRsp) GetBeginTs() int64 {
	if x != nil {
		return x.BeginTs
	}
	return 0
}

func (x *GetActivityDetailRsp) GetEndTs() int64 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *GetActivityDetailRsp) GetServerTs() int64 {
	if x != nil {
		return x.ServerTs
	}
	return 0
}

func (x *GetActivityDetailRsp) GetTicketCnt() uint64 {
	if x != nil {
		return x.TicketCnt
	}
	return 0
}

func (x *GetActivityDetailRsp) GetTicketCost() uint64 {
	if x != nil {
		return x.TicketCost
	}
	return 0
}

func (x *GetActivityDetailRsp) GetAwardList() []*AwardInfo {
	if x != nil {
		return x.AwardList
	}
	return nil
}

type LotteryRecordItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                            // 奖项id
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                        // 奖项名称
	AwardId      string `protobuf:"bytes,3,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`                   // 奖励id
	AwardType    uint32 `protobuf:"varint,4,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`            // 奖项类型, see EnumAwardType
	AwardSubType uint32 `protobuf:"varint,5,opt,name=award_sub_type,json=awardSubType,proto3" json:"award_sub_type,omitempty"` // 奖项子类型, see EnumAwardSubType
	Pic          string `protobuf:"bytes,6,opt,name=pic,proto3" json:"pic,omitempty"`                                          // 奖项图片
	Ts           int64  `protobuf:"varint,7,opt,name=ts,proto3" json:"ts,omitempty"`                                           // 中奖时间戳
}

func (x *LotteryRecordItem) Reset() {
	*x = LotteryRecordItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotteryRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotteryRecordItem) ProtoMessage() {}

func (x *LotteryRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotteryRecordItem.ProtoReflect.Descriptor instead.
func (*LotteryRecordItem) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{5}
}

func (x *LotteryRecordItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LotteryRecordItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LotteryRecordItem) GetAwardId() string {
	if x != nil {
		return x.AwardId
	}
	return ""
}

func (x *LotteryRecordItem) GetAwardType() uint32 {
	if x != nil {
		return x.AwardType
	}
	return 0
}

func (x *LotteryRecordItem) GetAwardSubType() uint32 {
	if x != nil {
		return x.AwardSubType
	}
	return 0
}

func (x *LotteryRecordItem) GetPic() string {
	if x != nil {
		return x.Pic
	}
	return ""
}

func (x *LotteryRecordItem) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type GetLotteryRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid      string `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`                             // appid
	Openid     string `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`                           // 用户openid(可选)
	ActivityId string `protobuf:"bytes,3,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"` // 抽奖活动id
	Passback   string `protobuf:"bytes,4,opt,name=passback,proto3" json:"passback,omitempty"`                       // 分页参数, 第一次请求不用传
	Order      uint32 `protobuf:"varint,5,opt,name=order,proto3" json:"order,omitempty"`                            // 排序类型, see EnumRecordOrderType
}

func (x *GetLotteryRecordReq) Reset() {
	*x = GetLotteryRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryRecordReq) ProtoMessage() {}

func (x *GetLotteryRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryRecordReq.ProtoReflect.Descriptor instead.
func (*GetLotteryRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{6}
}

func (x *GetLotteryRecordReq) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *GetLotteryRecordReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *GetLotteryRecordReq) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *GetLotteryRecordReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *GetLotteryRecordReq) GetOrder() uint32 {
	if x != nil {
		return x.Order
	}
	return 0
}

type GetLotteryRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.CommRet      `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	RecordList []*LotteryRecordItem `protobuf:"bytes,2,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"` // 中奖记录
	Passback   string               `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"`                       // 分页参数
	HasMore    int32                `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`         // 分页参数, 0:没有更多, 1:还有更多
}

func (x *GetLotteryRecordRsp) Reset() {
	*x = GetLotteryRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryRecordRsp) ProtoMessage() {}

func (x *GetLotteryRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryRecordRsp.ProtoReflect.Descriptor instead.
func (*GetLotteryRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{7}
}

func (x *GetLotteryRecordRsp) GetRet() *common.CommRet {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetLotteryRecordRsp) GetRecordList() []*LotteryRecordItem {
	if x != nil {
		return x.RecordList
	}
	return nil
}

func (x *GetLotteryRecordRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *GetLotteryRecordRsp) GetHasMore() int32 {
	if x != nil {
		return x.HasMore
	}
	return 0
}

type LampLotteryRecordItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Appid     string             `protobuf:"bytes,1,opt,name=appid,proto3" json:"appid,omitempty"`   // appid
	Openid    string             `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"` // 用户openid
	AwardItem *LotteryRecordItem `protobuf:"bytes,3,opt,name=award_item,json=awardItem,proto3" json:"award_item,omitempty"`
}

func (x *LampLotteryRecordItem) Reset() {
	*x = LampLotteryRecordItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LampLotteryRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LampLotteryRecordItem) ProtoMessage() {}

func (x *LampLotteryRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LampLotteryRecordItem.ProtoReflect.Descriptor instead.
func (*LampLotteryRecordItem) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{8}
}

func (x *LampLotteryRecordItem) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

func (x *LampLotteryRecordItem) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *LampLotteryRecordItem) GetAwardItem() *LotteryRecordItem {
	if x != nil {
		return x.AwardItem
	}
	return nil
}

type GetLotteryRecordLampReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId string `protobuf:"bytes,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"` // 抽奖活动id
	Passback   string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`                       // 分页参数, 第一次请求不用传
	LampType   uint32 `protobuf:"varint,3,opt,name=lamp_type,json=lampType,proto3" json:"lamp_type,omitempty"`      // 跑马灯类型, see EnumLampRecordType
}

func (x *GetLotteryRecordLampReq) Reset() {
	*x = GetLotteryRecordLampReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryRecordLampReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryRecordLampReq) ProtoMessage() {}

func (x *GetLotteryRecordLampReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryRecordLampReq.ProtoReflect.Descriptor instead.
func (*GetLotteryRecordLampReq) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{9}
}

func (x *GetLotteryRecordLampReq) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *GetLotteryRecordLampReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *GetLotteryRecordLampReq) GetLampType() uint32 {
	if x != nil {
		return x.LampType
	}
	return 0
}

type GetLotteryRecordLampRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.CommRet          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	RecordList []*LampLotteryRecordItem `protobuf:"bytes,2,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"` // 中奖记录
	Passback   string                   `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"`                       // 分页参数
	HasMore    int32                    `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`         // 分页参数, 0:没有更多, 1:还有更多
}

func (x *GetLotteryRecordLampRsp) Reset() {
	*x = GetLotteryRecordLampRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryRecordLampRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryRecordLampRsp) ProtoMessage() {}

func (x *GetLotteryRecordLampRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryRecordLampRsp.ProtoReflect.Descriptor instead.
func (*GetLotteryRecordLampRsp) Descriptor() ([]byte, []int) {
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP(), []int{10}
}

func (x *GetLotteryRecordLampRsp) GetRet() *common.CommRet {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetLotteryRecordLampRsp) GetRecordList() []*LampLotteryRecordItem {
	if x != nil {
		return x.RecordList
	}
	return nil
}

func (x *GetLotteryRecordLampRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *GetLotteryRecordLampRsp) GetHasMore() int32 {
	if x != nil {
		return x.HasMore
	}
	return 0
}

var File_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto protoreflect.FileDescriptor

var file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x70, 0x62, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2f, 0x6c,
	0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x76, 0x72, 0x2f, 0x6c, 0x75, 0x63,
	0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x09, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x1a, 0x28, 0x70, 0x62, 0x2f,
	0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x01, 0x0a, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x77, 0x61, 0x72, 0x64,
	0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x64, 0x6f, 0x63, 0x22, 0xb8, 0x02, 0x0a, 0x0c, 0x44, 0x6f, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61,
	0x77, 0x2e, 0x44, 0x6f, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x15,
	0x0a, 0x06, 0x62, 0x69, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x62, 0x69, 0x7a, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xc2, 0x01, 0x0a, 0x0c, 0x44, 0x6f, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x73,
	0x70, 0x12, 0x2b, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x65, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2a,
	0x0a, 0x05, 0x61, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x61, 0x77, 0x61, 0x72, 0x64, 0x12, 0x43, 0x0a, 0x0d, 0x73, 0x61,
	0x66, 0x65, 0x74, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x52, 0x0c, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x65, 0x71, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x65, 0x71, 0x69, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbc, 0x02,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x65, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x5f, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x54, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb9, 0x01, 0x0a,
	0x11, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x77, 0x61, 0x72, 0x64, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x63, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x69, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0xb8, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72,
	0x61, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x65,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x3d, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x22, 0x82, 0x01, 0x0a,
	0x15, 0x4c, 0x61, 0x6d, 0x70, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79,
	0x64, 0x72, 0x61, 0x77, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x22, 0x73, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x6d,
	0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6c, 0x61,
	0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x61, 0x6d, 0x70, 0x52,
	0x73, 0x70, 0x12, 0x2b, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x65, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x41, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77,
	0x2e, 0x4c, 0x61, 0x6d, 0x70, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x19,
	0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x32, 0xcf, 0x02, 0x0a, 0x03, 0x53, 0x76,
	0x72, 0x12, 0x3d, 0x0a, 0x09, 0x44, 0x6f, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x17,
	0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x44, 0x6f, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64,
	0x72, 0x61, 0x77, 0x2e, 0x44, 0x6f, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70,
	0x12, 0x55, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61,
	0x77, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72,
	0x61, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1e, 0x2e, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x5e, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c,
	0x61, 0x6d, 0x70, 0x12, 0x22, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x61, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64,
	0x72, 0x61, 0x77, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4c, 0x61, 0x6d, 0x70, 0x52, 0x73, 0x70, 0x42, 0x41, 0x5a, 0x3f, 0x74,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x64, 0x72, 0x61, 0x77, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescOnce sync.Once
	file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescData = file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDesc
)

func file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescGZIP() []byte {
	file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescOnce.Do(func() {
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescData)
	})
	return file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDescData
}

var file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_goTypes = []interface{}{
	(*AwardInfo)(nil),               // 0: luckydraw.AwardInfo
	(*DoLotteryReq)(nil),            // 1: luckydraw.DoLotteryReq
	(*DoLotteryRsp)(nil),            // 2: luckydraw.DoLotteryRsp
	(*GetActivityDetailReq)(nil),    // 3: luckydraw.GetActivityDetailReq
	(*GetActivityDetailRsp)(nil),    // 4: luckydraw.GetActivityDetailRsp
	(*LotteryRecordItem)(nil),       // 5: luckydraw.LotteryRecordItem
	(*GetLotteryRecordReq)(nil),     // 6: luckydraw.GetLotteryRecordReq
	(*GetLotteryRecordRsp)(nil),     // 7: luckydraw.GetLotteryRecordRsp
	(*LampLotteryRecordItem)(nil),   // 8: luckydraw.LampLotteryRecordItem
	(*GetLotteryRecordLampReq)(nil), // 9: luckydraw.GetLotteryRecordLampReq
	(*GetLotteryRecordLampRsp)(nil), // 10: luckydraw.GetLotteryRecordLampRsp
	nil,                             // 11: luckydraw.DoLotteryReq.ParamsEntry
	nil,                             // 12: luckydraw.GetActivityDetailReq.ParamsEntry
	(*common.CommRet)(nil),          // 13: luckydraw.common.CommRet
	(*common.SafetyParams)(nil),     // 14: luckydraw.common.SafetyParams
}
var file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_depIdxs = []int32{
	11, // 0: luckydraw.DoLotteryReq.params:type_name -> luckydraw.DoLotteryReq.ParamsEntry
	13, // 1: luckydraw.DoLotteryRsp.ret:type_name -> luckydraw.common.CommRet
	0,  // 2: luckydraw.DoLotteryRsp.award:type_name -> luckydraw.AwardInfo
	14, // 3: luckydraw.DoLotteryRsp.safety_params:type_name -> luckydraw.common.SafetyParams
	12, // 4: luckydraw.GetActivityDetailReq.params:type_name -> luckydraw.GetActivityDetailReq.ParamsEntry
	13, // 5: luckydraw.GetActivityDetailRsp.ret:type_name -> luckydraw.common.CommRet
	0,  // 6: luckydraw.GetActivityDetailRsp.award_list:type_name -> luckydraw.AwardInfo
	13, // 7: luckydraw.GetLotteryRecordRsp.ret:type_name -> luckydraw.common.CommRet
	5,  // 8: luckydraw.GetLotteryRecordRsp.record_list:type_name -> luckydraw.LotteryRecordItem
	5,  // 9: luckydraw.LampLotteryRecordItem.award_item:type_name -> luckydraw.LotteryRecordItem
	13, // 10: luckydraw.GetLotteryRecordLampRsp.ret:type_name -> luckydraw.common.CommRet
	8,  // 11: luckydraw.GetLotteryRecordLampRsp.record_list:type_name -> luckydraw.LampLotteryRecordItem
	1,  // 12: luckydraw.Svr.DoLottery:input_type -> luckydraw.DoLotteryReq
	3,  // 13: luckydraw.Svr.GetActivityDetail:input_type -> luckydraw.GetActivityDetailReq
	6,  // 14: luckydraw.Svr.GetLotteryRecord:input_type -> luckydraw.GetLotteryRecordReq
	9,  // 15: luckydraw.Svr.GetLotteryRecordLamp:input_type -> luckydraw.GetLotteryRecordLampReq
	2,  // 16: luckydraw.Svr.DoLottery:output_type -> luckydraw.DoLotteryRsp
	4,  // 17: luckydraw.Svr.GetActivityDetail:output_type -> luckydraw.GetActivityDetailRsp
	7,  // 18: luckydraw.Svr.GetLotteryRecord:output_type -> luckydraw.GetLotteryRecordRsp
	10, // 19: luckydraw.Svr.GetLotteryRecordLamp:output_type -> luckydraw.GetLotteryRecordLampRsp
	16, // [16:20] is the sub-list for method output_type
	12, // [12:16] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_init() }
func file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_init() {
	if File_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AwardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoLotteryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotteryRecordItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LampLotteryRecordItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryRecordLampReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryRecordLampRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_goTypes,
		DependencyIndexes: file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_depIdxs,
		MessageInfos:      file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_msgTypes,
	}.Build()
	File_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto = out.File
	file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_rawDesc = nil
	file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_goTypes = nil
	file_pb_luckydraw_luckydraw_svr_luckydraw_svr_proto_depIdxs = nil
}
