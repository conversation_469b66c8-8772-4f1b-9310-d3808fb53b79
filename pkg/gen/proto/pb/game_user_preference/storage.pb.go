// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_user_preference/storage.proto

package game_user_preference

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Kv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdateTime int64             `protobuf:"varint,1,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	Data       map[string]string `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Kv) Reset() {
	*x = Kv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_user_preference_storage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Kv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Kv) ProtoMessage() {}

func (x *Kv) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_user_preference_storage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Kv.ProtoReflect.Descriptor instead.
func (*Kv) Descriptor() ([]byte, []int) {
	return file_pb_game_user_preference_storage_proto_rawDescGZIP(), []int{0}
}

func (x *Kv) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Kv) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_pb_game_user_preference_storage_proto protoreflect.FileDescriptor

var file_pb_game_user_preference_storage_proto_rawDesc = []byte{
	0x0a, 0x25, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x85, 0x01,
	0x0a, 0x02, 0x4b, 0x76, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4b, 0x76, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x4c, 0x5a, 0x4a, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_user_preference_storage_proto_rawDescOnce sync.Once
	file_pb_game_user_preference_storage_proto_rawDescData = file_pb_game_user_preference_storage_proto_rawDesc
)

func file_pb_game_user_preference_storage_proto_rawDescGZIP() []byte {
	file_pb_game_user_preference_storage_proto_rawDescOnce.Do(func() {
		file_pb_game_user_preference_storage_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_user_preference_storage_proto_rawDescData)
	})
	return file_pb_game_user_preference_storage_proto_rawDescData
}

var file_pb_game_user_preference_storage_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_game_user_preference_storage_proto_goTypes = []interface{}{
	(*Kv)(nil), // 0: game.Kv
	nil,        // 1: game.Kv.DataEntry
}
var file_pb_game_user_preference_storage_proto_depIdxs = []int32{
	1, // 0: game.Kv.data:type_name -> game.Kv.DataEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_game_user_preference_storage_proto_init() }
func file_pb_game_user_preference_storage_proto_init() {
	if File_pb_game_user_preference_storage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_user_preference_storage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Kv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_user_preference_storage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_user_preference_storage_proto_goTypes,
		DependencyIndexes: file_pb_game_user_preference_storage_proto_depIdxs,
		MessageInfos:      file_pb_game_user_preference_storage_proto_msgTypes,
	}.Build()
	File_pb_game_user_preference_storage_proto = out.File
	file_pb_game_user_preference_storage_proto_rawDesc = nil
	file_pb_game_user_preference_storage_proto_goTypes = nil
	file_pb_game_user_preference_storage_proto_depIdxs = nil
}
