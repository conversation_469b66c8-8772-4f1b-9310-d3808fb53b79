// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_user_preference/user_preference.proto

package game_user_preference

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	UserPreference_Query_FullMethodName      = "/game.UserPreference/Query"
	UserPreference_BatchQuery_FullMethodName = "/game.UserPreference/BatchQuery"
	UserPreference_Modify_FullMethodName     = "/game.UserPreference/Modify"
)

// UserPreferenceClient is the client API for UserPreference service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserPreferenceClient interface {
	Query(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error)
	BatchQuery(ctx context.Context, in *BatchQueryReq, opts ...grpc.CallOption) (*BatchQueryRsp, error)
	Modify(ctx context.Context, in *ModifyReq, opts ...grpc.CallOption) (*ModifyRsp, error)
}

type userPreferenceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserPreferenceClient(cc grpc.ClientConnInterface) UserPreferenceClient {
	return &userPreferenceClient{cc}
}

func (c *userPreferenceClient) Query(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRsp)
	err := c.cc.Invoke(ctx, UserPreference_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPreferenceClient) BatchQuery(ctx context.Context, in *BatchQueryReq, opts ...grpc.CallOption) (*BatchQueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchQueryRsp)
	err := c.cc.Invoke(ctx, UserPreference_BatchQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userPreferenceClient) Modify(ctx context.Context, in *ModifyReq, opts ...grpc.CallOption) (*ModifyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ModifyRsp)
	err := c.cc.Invoke(ctx, UserPreference_Modify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserPreferenceServer is the server API for UserPreference service.
// All implementations should embed UnimplementedUserPreferenceServer
// for forward compatibility
type UserPreferenceServer interface {
	Query(context.Context, *QueryReq) (*QueryRsp, error)
	BatchQuery(context.Context, *BatchQueryReq) (*BatchQueryRsp, error)
	Modify(context.Context, *ModifyReq) (*ModifyRsp, error)
}

// UnimplementedUserPreferenceServer should be embedded to have forward compatible implementations.
type UnimplementedUserPreferenceServer struct {
}

func (UnimplementedUserPreferenceServer) Query(context.Context, *QueryReq) (*QueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (UnimplementedUserPreferenceServer) BatchQuery(context.Context, *BatchQueryReq) (*BatchQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQuery not implemented")
}
func (UnimplementedUserPreferenceServer) Modify(context.Context, *ModifyReq) (*ModifyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Modify not implemented")
}

// UnsafeUserPreferenceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserPreferenceServer will
// result in compilation errors.
type UnsafeUserPreferenceServer interface {
	mustEmbedUnimplementedUserPreferenceServer()
}

func RegisterUserPreferenceServer(s grpc.ServiceRegistrar, srv UserPreferenceServer) {
	s.RegisterService(&UserPreference_ServiceDesc, srv)
}

func _UserPreference_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPreferenceServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPreference_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPreferenceServer).Query(ctx, req.(*QueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPreference_BatchQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPreferenceServer).BatchQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPreference_BatchQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPreferenceServer).BatchQuery(ctx, req.(*BatchQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserPreference_Modify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserPreferenceServer).Modify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserPreference_Modify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserPreferenceServer).Modify(ctx, req.(*ModifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserPreference_ServiceDesc is the grpc.ServiceDesc for UserPreference service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserPreference_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.UserPreference",
	HandlerType: (*UserPreferenceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Query",
			Handler:    _UserPreference_Query_Handler,
		},
		{
			MethodName: "BatchQuery",
			Handler:    _UserPreference_BatchQuery_Handler,
		},
		{
			MethodName: "Modify",
			Handler:    _UserPreference_Modify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_user_preference/user_preference.proto",
}
