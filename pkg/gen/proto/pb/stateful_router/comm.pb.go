// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/stateful_router/comm.proto

package stateful_router

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 新流量: NodeStatus>=2 对于2,可以路由接入,其他屏蔽接入
// 旧流量: NodeStatus>=2 按照原有接入,即使失败
type NodeStatus int32

const (
	NodeStatus_Invalid         NodeStatus = 0  // 无效
	NodeStatus_Pending         NodeStatus = 1  // 等待拉取负载
	NodeStatus_Running         NodeStatus = 2  // 正常
	NodeStatus_Isolation       NodeStatus = 4  // 被隔离
	NodeStatus_PullLoadFail    NodeStatus = 8  // 拉取不到负载(为正常提供负载拉取接口/拉取接口异常)
	NodeStatus_ClusterCritical NodeStatus = 16 // 所在服务健康状态异常(critical)
	NodeStatus_NodeCritical    NodeStatus = 32 // 非健康状态(critical/warning)
	NodeStatus_TagFail         NodeStatus = 64 // 打标签失败
)

// Enum value maps for NodeStatus.
var (
	NodeStatus_name = map[int32]string{
		0:  "Invalid",
		1:  "Pending",
		2:  "Running",
		4:  "Isolation",
		8:  "PullLoadFail",
		16: "ClusterCritical",
		32: "NodeCritical",
		64: "TagFail",
	}
	NodeStatus_value = map[string]int32{
		"Invalid":         0,
		"Pending":         1,
		"Running":         2,
		"Isolation":       4,
		"PullLoadFail":    8,
		"ClusterCritical": 16,
		"NodeCritical":    32,
		"TagFail":         64,
	}
)

func (x NodeStatus) Enum() *NodeStatus {
	p := new(NodeStatus)
	*p = x
	return p
}

func (x NodeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_stateful_router_comm_proto_enumTypes[0].Descriptor()
}

func (NodeStatus) Type() protoreflect.EnumType {
	return &file_pb_stateful_router_comm_proto_enumTypes[0]
}

func (x NodeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeStatus.Descriptor instead.
func (NodeStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{0}
}

// NodeLoad 节点负载量化指标
// 具体获取方式可参照例子:
// app/stateful_router/server/node_example/internal/load/load_monitor.go
// 负载均衡公式,加权平均:
// nodes = [node1, node2, node3, ...]
// 给每个节点计算负载 nodeRank = 2.0 * c + m
// selected = min_nodeRank(nodes)
type NodeLoad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	M float32 `protobuf:"fixed32,1,opt,name=m,proto3" json:"m,omitempty"` // 内存占用 m = 当前进程所占内存(RES) / 容器限制内存(CGroupLimit), 这里特指物理内存
	C float32 `protobuf:"fixed32,2,opt,name=c,proto3" json:"c,omitempty"` // CPU占用 c = 当前进程所占用CPU核数  / 容器限制核数(CGroupLimit)
}

func (x *NodeLoad) Reset() {
	*x = NodeLoad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeLoad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeLoad) ProtoMessage() {}

func (x *NodeLoad) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeLoad.ProtoReflect.Descriptor instead.
func (*NodeLoad) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{0}
}

func (x *NodeLoad) GetM() float32 {
	if x != nil {
		return x.M
	}
	return 0
}

func (x *NodeLoad) GetC() float32 {
	if x != nil {
		return x.C
	}
	return 0
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip           string     `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`                                          // IP
	Label        string     `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`                                    // 标签
	Port         int32      `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`                                     // port
	Load         *NodeLoad  `protobuf:"bytes,4,opt,name=load,proto3" json:"load,omitempty"`                                      // 负载详情
	Status       NodeStatus `protobuf:"varint,5,opt,name=status,proto3,enum=stateful_router.NodeStatus" json:"status,omitempty"` // 状态
	Sids         []*NodeSID `protobuf:"bytes,6,rep,name=sids,proto3" json:"sids,omitempty"`                                      // 绑定信息
	LastUpdateTs int64      `protobuf:"varint,7,opt,name=lastUpdateTs,proto3" json:"lastUpdateTs,omitempty"`                     // 最后更新时间
	LoadRank     float32    `protobuf:"fixed32,8,opt,name=loadRank,proto3" json:"loadRank,omitempty"`
	Weight       int32      `protobuf:"varint,9,opt,name=weight,proto3" json:"weight,omitempty"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{1}
}

func (x *Node) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Node) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Node) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Node) GetLoad() *NodeLoad {
	if x != nil {
		return x.Load
	}
	return nil
}

func (x *Node) GetStatus() NodeStatus {
	if x != nil {
		return x.Status
	}
	return NodeStatus_Invalid
}

func (x *Node) GetSids() []*NodeSID {
	if x != nil {
		return x.Sids
	}
	return nil
}

func (x *Node) GetLastUpdateTs() int64 {
	if x != nil {
		return x.LastUpdateTs
	}
	return 0
}

func (x *Node) GetLoadRank() float32 {
	if x != nil {
		return x.LoadRank
	}
	return 0
}

func (x *Node) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

type NodeSID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid      string `protobuf:"bytes,1,opt,name=sid,proto3" json:"sid,omitempty"`            // roomid-roundid
	CreateTs int64  `protobuf:"varint,2,opt,name=createTs,proto3" json:"createTs,omitempty"` // 创建时间
}

func (x *NodeSID) Reset() {
	*x = NodeSID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeSID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSID) ProtoMessage() {}

func (x *NodeSID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSID.ProtoReflect.Descriptor instead.
func (*NodeSID) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{2}
}

func (x *NodeSID) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

func (x *NodeSID) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

// 数据结构存储映射
// appId -> node列表
// node -> sid列表(sid可以推出roomId和roundId)
type Nodes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId        string  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Nodes        []*Node `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`
	LastUpdateTs int64   `protobuf:"varint,6,opt,name=lastUpdateTs,proto3" json:"lastUpdateTs,omitempty"` // 最后更新时间
}

func (x *Nodes) Reset() {
	*x = Nodes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Nodes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nodes) ProtoMessage() {}

func (x *Nodes) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nodes.ProtoReflect.Descriptor instead.
func (*Nodes) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{3}
}

func (x *Nodes) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Nodes) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *Nodes) GetLastUpdateTs() int64 {
	if x != nil {
		return x.LastUpdateTs
	}
	return 0
}

// 游戏侧自己实现http/rpc接口
// 入参: ReportReq
// 出参: ReportRsp
// 有状态路由服务会自己去拉取机器负载
type LoadPullReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LoadPullReq) Reset() {
	*x = LoadPullReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadPullReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadPullReq) ProtoMessage() {}

func (x *LoadPullReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadPullReq.ProtoReflect.Descriptor instead.
func (*LoadPullReq) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{4}
}

type LoadPullRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Load *NodeLoad `protobuf:"bytes,1,opt,name=load,proto3" json:"load,omitempty"` // 负载信息
}

func (x *LoadPullRsp) Reset() {
	*x = LoadPullRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadPullRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadPullRsp) ProtoMessage() {}

func (x *LoadPullRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadPullRsp.ProtoReflect.Descriptor instead.
func (*LoadPullRsp) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{5}
}

func (x *LoadPullRsp) GetLoad() *NodeLoad {
	if x != nil {
		return x.Load
	}
	return nil
}

// 红石配置项
// http://gm.tmeoa.com/?type=stateful_router_config_test
type MonitorCfgItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                    // 配置ID
	AppId        string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`               // 小游戏AppID
	Service      string `protobuf:"bytes,3,opt,name=service,proto3" json:"service,omitempty"`           // 微服务名
	Enable       bool   `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`            // 是否启用
	UpdateTs     int64  `protobuf:"varint,5,opt,name=updateTs,proto3" json:"updateTs,omitempty"`        // 最后一次更新时间
	Interval     int64  `protobuf:"varint,6,opt,name=interval,proto3" json:"interval,omitempty"`        // 拉取定时器间隔(s)
	Namespace    string `protobuf:"bytes,7,opt,name=namespace,proto3" json:"namespace,omitempty"`       // namespace(test|production)
	ServiceOwner string `protobuf:"bytes,8,opt,name=serviceOwner,proto3" json:"serviceOwner,omitempty"` // 服务负责人
	PullLoadAPI  string `protobuf:"bytes,9,opt,name=pullLoadAPI,proto3" json:"pullLoadAPI,omitempty"`   // 配置拉取接口
}

func (x *MonitorCfgItem) Reset() {
	*x = MonitorCfgItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_comm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorCfgItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorCfgItem) ProtoMessage() {}

func (x *MonitorCfgItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_comm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorCfgItem.ProtoReflect.Descriptor instead.
func (*MonitorCfgItem) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_comm_proto_rawDescGZIP(), []int{6}
}

func (x *MonitorCfgItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MonitorCfgItem) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *MonitorCfgItem) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *MonitorCfgItem) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *MonitorCfgItem) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *MonitorCfgItem) GetInterval() int64 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *MonitorCfgItem) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *MonitorCfgItem) GetServiceOwner() string {
	if x != nil {
		return x.ServiceOwner
	}
	return ""
}

func (x *MonitorCfgItem) GetPullLoadAPI() string {
	if x != nil {
		return x.PullLoadAPI
	}
	return ""
}

var File_pb_stateful_router_comm_proto protoreflect.FileDescriptor

var file_pb_stateful_router_comm_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x62, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72,
	0x22, 0x26, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x0c, 0x0a, 0x01,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x6d, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x63, 0x22, 0xaa, 0x02, 0x0a, 0x04, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x4c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2c, 0x0a, 0x04, 0x73, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x53, 0x49, 0x44, 0x52, 0x04, 0x73, 0x69, 0x64, 0x73, 0x12, 0x22, 0x0a,
	0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x16, 0x0a,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x37, 0x0a, 0x07, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x49, 0x44,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0x6e,
	0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x22, 0x0d,
	0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x64, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x22, 0x3c, 0x0a,
	0x0b, 0x4c, 0x6f, 0x61, 0x64, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x04,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x04, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x84, 0x02, 0x0a, 0x0e,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x66, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x75, 0x6c, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x50, 0x49, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x75, 0x6c, 0x6c, 0x4c, 0x6f, 0x61, 0x64, 0x41,
	0x50, 0x49, 0x2a, 0x88, 0x01, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52,
	0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x73, 0x6f, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x75, 0x6c, 0x6c, 0x4c,
	0x6f, 0x61, 0x64, 0x46, 0x61, 0x69, 0x6c, 0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x10, 0x10, 0x12, 0x10,
	0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x72, 0x69, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x10, 0x20,
	0x12, 0x0b, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x46, 0x61, 0x69, 0x6c, 0x10, 0x40, 0x42, 0x47, 0x5a,
	0x45, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f,
	0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_stateful_router_comm_proto_rawDescOnce sync.Once
	file_pb_stateful_router_comm_proto_rawDescData = file_pb_stateful_router_comm_proto_rawDesc
)

func file_pb_stateful_router_comm_proto_rawDescGZIP() []byte {
	file_pb_stateful_router_comm_proto_rawDescOnce.Do(func() {
		file_pb_stateful_router_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_stateful_router_comm_proto_rawDescData)
	})
	return file_pb_stateful_router_comm_proto_rawDescData
}

var file_pb_stateful_router_comm_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_stateful_router_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_pb_stateful_router_comm_proto_goTypes = []interface{}{
	(NodeStatus)(0),        // 0: stateful_router.NodeStatus
	(*NodeLoad)(nil),       // 1: stateful_router.NodeLoad
	(*Node)(nil),           // 2: stateful_router.Node
	(*NodeSID)(nil),        // 3: stateful_router.NodeSID
	(*Nodes)(nil),          // 4: stateful_router.Nodes
	(*LoadPullReq)(nil),    // 5: stateful_router.LoadPullReq
	(*LoadPullRsp)(nil),    // 6: stateful_router.LoadPullRsp
	(*MonitorCfgItem)(nil), // 7: stateful_router.MonitorCfgItem
}
var file_pb_stateful_router_comm_proto_depIdxs = []int32{
	1, // 0: stateful_router.Node.load:type_name -> stateful_router.NodeLoad
	0, // 1: stateful_router.Node.status:type_name -> stateful_router.NodeStatus
	3, // 2: stateful_router.Node.sids:type_name -> stateful_router.NodeSID
	2, // 3: stateful_router.Nodes.nodes:type_name -> stateful_router.Node
	1, // 4: stateful_router.LoadPullRsp.load:type_name -> stateful_router.NodeLoad
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pb_stateful_router_comm_proto_init() }
func file_pb_stateful_router_comm_proto_init() {
	if File_pb_stateful_router_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_stateful_router_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeLoad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_comm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeSID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_comm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Nodes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_comm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadPullReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_comm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadPullRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_comm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorCfgItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_stateful_router_comm_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_stateful_router_comm_proto_goTypes,
		DependencyIndexes: file_pb_stateful_router_comm_proto_depIdxs,
		EnumInfos:         file_pb_stateful_router_comm_proto_enumTypes,
		MessageInfos:      file_pb_stateful_router_comm_proto_msgTypes,
	}.Build()
	File_pb_stateful_router_comm_proto = out.File
	file_pb_stateful_router_comm_proto_rawDesc = nil
	file_pb_stateful_router_comm_proto_goTypes = nil
	file_pb_stateful_router_comm_proto_depIdxs = nil
}
