// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/business_intelligence/business_intelligence.proto

package business_intelligence

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TmeGameOpterationCmd int32

const (
	TmeGameOpterationCmd_TmeGameOpterationCmdUnknown TmeGameOpterationCmd = 0
	TmeGameOpterationCmd_TmeGameOpterationCmdInput   TmeGameOpterationCmd = 1 // 游戏投入,花了多少钱来玩游戏
	TmeGameOpterationCmd_TmeGameOpterationCmdWrite   TmeGameOpterationCmd = 2 // 游戏写行为, 概率类游戏直接按照投注(鲜花/道具)
	TmeGameOpterationCmd_TmeGameOpterationCmdQuit    TmeGameOpterationCmd = 3 // 游戏退出,指互动游戏组局结束
)

// Enum value maps for TmeGameOpterationCmd.
var (
	TmeGameOpterationCmd_name = map[int32]string{
		0: "TmeGameOpterationCmdUnknown",
		1: "TmeGameOpterationCmdInput",
		2: "TmeGameOpterationCmdWrite",
		3: "TmeGameOpterationCmdQuit",
	}
	TmeGameOpterationCmd_value = map[string]int32{
		"TmeGameOpterationCmdUnknown": 0,
		"TmeGameOpterationCmdInput":   1,
		"TmeGameOpterationCmdWrite":   2,
		"TmeGameOpterationCmdQuit":    3,
	}
)

func (x TmeGameOpterationCmd) Enum() *TmeGameOpterationCmd {
	p := new(TmeGameOpterationCmd)
	*p = x
	return p
}

func (x TmeGameOpterationCmd) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TmeGameOpterationCmd) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_business_intelligence_business_intelligence_proto_enumTypes[0].Descriptor()
}

func (TmeGameOpterationCmd) Type() protoreflect.EnumType {
	return &file_pb_business_intelligence_business_intelligence_proto_enumTypes[0]
}

func (x TmeGameOpterationCmd) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TmeGameOpterationCmd.Descriptor instead.
func (TmeGameOpterationCmd) EnumDescriptor() ([]byte, []int) {
	return file_pb_business_intelligence_business_intelligence_proto_rawDescGZIP(), []int{0}
}

// TmeGameOpterationLog
type TmeGameOpterationLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppName   string               `protobuf:"bytes,1,opt,name=strAppName,proto3" json:"strAppName,omitempty"`
	StrGameId    string               `protobuf:"bytes,2,opt,name=strGameId,proto3" json:"strGameId,omitempty"`
	StrGameName  string               `protobuf:"bytes,3,opt,name=strGameName,proto3" json:"strGameName,omitempty"`
	StrEnv       string               `protobuf:"bytes,4,opt,name=strEnv,proto3" json:"strEnv,omitempty"`
	IOpertTime   int64                `protobuf:"varint,5,opt,name=iOpertTime,proto3" json:"iOpertTime,omitempty"`
	IActDuration int64                `protobuf:"varint,6,opt,name=iActDuration,proto3" json:"iActDuration,omitempty"`
	IUid         int64                `protobuf:"varint,7,opt,name=iUid,proto3" json:"iUid,omitempty"`
	ECmd         TmeGameOpterationCmd `protobuf:"varint,8,opt,name=eCmd,proto3,enum=kg.game.TmeGameOpterationCmd" json:"eCmd,omitempty"`
	IInputPrice  int64                `protobuf:"varint,9,opt,name=iInputPrice,proto3" json:"iInputPrice,omitempty"`
}

func (x *TmeGameOpterationLog) Reset() {
	*x = TmeGameOpterationLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_business_intelligence_business_intelligence_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmeGameOpterationLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmeGameOpterationLog) ProtoMessage() {}

func (x *TmeGameOpterationLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_business_intelligence_business_intelligence_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmeGameOpterationLog.ProtoReflect.Descriptor instead.
func (*TmeGameOpterationLog) Descriptor() ([]byte, []int) {
	return file_pb_business_intelligence_business_intelligence_proto_rawDescGZIP(), []int{0}
}

func (x *TmeGameOpterationLog) GetStrAppName() string {
	if x != nil {
		return x.StrAppName
	}
	return ""
}

func (x *TmeGameOpterationLog) GetStrGameId() string {
	if x != nil {
		return x.StrGameId
	}
	return ""
}

func (x *TmeGameOpterationLog) GetStrGameName() string {
	if x != nil {
		return x.StrGameName
	}
	return ""
}

func (x *TmeGameOpterationLog) GetStrEnv() string {
	if x != nil {
		return x.StrEnv
	}
	return ""
}

func (x *TmeGameOpterationLog) GetIOpertTime() int64 {
	if x != nil {
		return x.IOpertTime
	}
	return 0
}

func (x *TmeGameOpterationLog) GetIActDuration() int64 {
	if x != nil {
		return x.IActDuration
	}
	return 0
}

func (x *TmeGameOpterationLog) GetIUid() int64 {
	if x != nil {
		return x.IUid
	}
	return 0
}

func (x *TmeGameOpterationLog) GetECmd() TmeGameOpterationCmd {
	if x != nil {
		return x.ECmd
	}
	return TmeGameOpterationCmd_TmeGameOpterationCmdUnknown
}

func (x *TmeGameOpterationLog) GetIInputPrice() int64 {
	if x != nil {
		return x.IInputPrice
	}
	return 0
}

var File_pb_business_intelligence_business_intelligence_proto protoreflect.FileDescriptor

var file_pb_business_intelligence_business_intelligence_proto_rawDesc = []byte{
	0x0a, 0x34, 0x70, 0x62, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x22,
	0xbb, 0x02, 0x0a, 0x14, 0x54, 0x6d, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x70, 0x74, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x41,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x47,
	0x61, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72,
	0x47, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x47, 0x61, 0x6d,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72,
	0x47, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x45,
	0x6e, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x45, 0x6e, 0x76,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x4f, 0x70, 0x65, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x4f, 0x70, 0x65, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x69, 0x41, 0x63, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x41, 0x63, 0x74, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x55, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x69, 0x55, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x65, 0x43, 0x6d, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x54, 0x6d, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x70, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6d, 0x64, 0x52, 0x04, 0x65, 0x43, 0x6d, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x69, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x2a, 0x93, 0x01,
	0x0a, 0x14, 0x54, 0x6d, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x70, 0x74, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6d, 0x64, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x6d, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x4f, 0x70, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6d, 0x64, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x6d, 0x65, 0x47, 0x61,
	0x6d, 0x65, 0x4f, 0x70, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6d, 0x64, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x6d, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x4f, 0x70, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6d, 0x64, 0x57, 0x72,
	0x69, 0x74, 0x65, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x6d, 0x65, 0x47, 0x61, 0x6d, 0x65,
	0x4f, 0x70, 0x74, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6d, 0x64, 0x51, 0x75, 0x69,
	0x74, 0x10, 0x03, 0x42, 0x4d, 0x5a, 0x4b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65,
	0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f,
	0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x6c, 0x69, 0x67, 0x65, 0x6e,
	0x63, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_business_intelligence_business_intelligence_proto_rawDescOnce sync.Once
	file_pb_business_intelligence_business_intelligence_proto_rawDescData = file_pb_business_intelligence_business_intelligence_proto_rawDesc
)

func file_pb_business_intelligence_business_intelligence_proto_rawDescGZIP() []byte {
	file_pb_business_intelligence_business_intelligence_proto_rawDescOnce.Do(func() {
		file_pb_business_intelligence_business_intelligence_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_business_intelligence_business_intelligence_proto_rawDescData)
	})
	return file_pb_business_intelligence_business_intelligence_proto_rawDescData
}

var file_pb_business_intelligence_business_intelligence_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_business_intelligence_business_intelligence_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pb_business_intelligence_business_intelligence_proto_goTypes = []interface{}{
	(TmeGameOpterationCmd)(0),    // 0: kg.game.TmeGameOpterationCmd
	(*TmeGameOpterationLog)(nil), // 1: kg.game.TmeGameOpterationLog
}
var file_pb_business_intelligence_business_intelligence_proto_depIdxs = []int32{
	0, // 0: kg.game.TmeGameOpterationLog.eCmd:type_name -> kg.game.TmeGameOpterationCmd
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_business_intelligence_business_intelligence_proto_init() }
func file_pb_business_intelligence_business_intelligence_proto_init() {
	if File_pb_business_intelligence_business_intelligence_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_business_intelligence_business_intelligence_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmeGameOpterationLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_business_intelligence_business_intelligence_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_business_intelligence_business_intelligence_proto_goTypes,
		DependencyIndexes: file_pb_business_intelligence_business_intelligence_proto_depIdxs,
		EnumInfos:         file_pb_business_intelligence_business_intelligence_proto_enumTypes,
		MessageInfos:      file_pb_business_intelligence_business_intelligence_proto_msgTypes,
	}.Build()
	File_pb_business_intelligence_business_intelligence_proto = out.File
	file_pb_business_intelligence_business_intelligence_proto_rawDesc = nil
	file_pb_business_intelligence_business_intelligence_proto_goTypes = nil
	file_pb_business_intelligence_business_intelligence_proto_depIdxs = nil
}
