// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_segment/game_segment.proto

package game_segment

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Segment_SegmentCalculate_FullMethodName    = "/game.Segment/SegmentCalculate"
	Segment_BatchGetUserSegment_FullMethodName = "/game.Segment/BatchGetUserSegment"
	Segment_GetSegmentLevel_FullMethodName     = "/game.Segment/GetSegmentLevel"
)

// SegmentClient is the client API for Segment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 段位服务
type SegmentClient interface {
	// 段位积分计算接口（游戏结果流水 -> SegmentCalculate -> 段位体系）
	SegmentCalculate(ctx context.Context, in *SegmentCalculateReq, opts ...grpc.CallOption) (*SegmentCalculateRsp, error)
	// 批量查询用户段位（最多50个）
	BatchGetUserSegment(ctx context.Context, in *BatchGetUserSegmentReq, opts ...grpc.CallOption) (*BatchGetUserSegmentRsp, error)
	// 查询当前赛季段位配置
	GetSegmentLevel(ctx context.Context, in *GetSegmentLevelReq, opts ...grpc.CallOption) (*GetSegmentLevelRsp, error)
}

type segmentClient struct {
	cc grpc.ClientConnInterface
}

func NewSegmentClient(cc grpc.ClientConnInterface) SegmentClient {
	return &segmentClient{cc}
}

func (c *segmentClient) SegmentCalculate(ctx context.Context, in *SegmentCalculateReq, opts ...grpc.CallOption) (*SegmentCalculateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SegmentCalculateRsp)
	err := c.cc.Invoke(ctx, Segment_SegmentCalculate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentClient) BatchGetUserSegment(ctx context.Context, in *BatchGetUserSegmentReq, opts ...grpc.CallOption) (*BatchGetUserSegmentRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetUserSegmentRsp)
	err := c.cc.Invoke(ctx, Segment_BatchGetUserSegment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentClient) GetSegmentLevel(ctx context.Context, in *GetSegmentLevelReq, opts ...grpc.CallOption) (*GetSegmentLevelRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSegmentLevelRsp)
	err := c.cc.Invoke(ctx, Segment_GetSegmentLevel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SegmentServer is the server API for Segment service.
// All implementations should embed UnimplementedSegmentServer
// for forward compatibility
//
// 段位服务
type SegmentServer interface {
	// 段位积分计算接口（游戏结果流水 -> SegmentCalculate -> 段位体系）
	SegmentCalculate(context.Context, *SegmentCalculateReq) (*SegmentCalculateRsp, error)
	// 批量查询用户段位（最多50个）
	BatchGetUserSegment(context.Context, *BatchGetUserSegmentReq) (*BatchGetUserSegmentRsp, error)
	// 查询当前赛季段位配置
	GetSegmentLevel(context.Context, *GetSegmentLevelReq) (*GetSegmentLevelRsp, error)
}

// UnimplementedSegmentServer should be embedded to have forward compatible implementations.
type UnimplementedSegmentServer struct {
}

func (UnimplementedSegmentServer) SegmentCalculate(context.Context, *SegmentCalculateReq) (*SegmentCalculateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SegmentCalculate not implemented")
}
func (UnimplementedSegmentServer) BatchGetUserSegment(context.Context, *BatchGetUserSegmentReq) (*BatchGetUserSegmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetUserSegment not implemented")
}
func (UnimplementedSegmentServer) GetSegmentLevel(context.Context, *GetSegmentLevelReq) (*GetSegmentLevelRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSegmentLevel not implemented")
}

// UnsafeSegmentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SegmentServer will
// result in compilation errors.
type UnsafeSegmentServer interface {
	mustEmbedUnimplementedSegmentServer()
}

func RegisterSegmentServer(s grpc.ServiceRegistrar, srv SegmentServer) {
	s.RegisterService(&Segment_ServiceDesc, srv)
}

func _Segment_SegmentCalculate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SegmentCalculateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentServer).SegmentCalculate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Segment_SegmentCalculate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentServer).SegmentCalculate(ctx, req.(*SegmentCalculateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Segment_BatchGetUserSegment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserSegmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentServer).BatchGetUserSegment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Segment_BatchGetUserSegment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentServer).BatchGetUserSegment(ctx, req.(*BatchGetUserSegmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Segment_GetSegmentLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSegmentLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentServer).GetSegmentLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Segment_GetSegmentLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentServer).GetSegmentLevel(ctx, req.(*GetSegmentLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Segment_ServiceDesc is the grpc.ServiceDesc for Segment service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Segment_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.Segment",
	HandlerType: (*SegmentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SegmentCalculate",
			Handler:    _Segment_SegmentCalculate_Handler,
		},
		{
			MethodName: "BatchGetUserSegment",
			Handler:    _Segment_BatchGetUserSegment_Handler,
		},
		{
			MethodName: "GetSegmentLevel",
			Handler:    _Segment_GetSegmentLevel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_segment/game_segment.proto",
}
