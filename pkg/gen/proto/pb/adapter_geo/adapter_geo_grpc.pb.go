// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/adapter_geo/adapter_geo.proto

package adapter_geo

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AdapterGeo_Query_FullMethodName = "/adapter_geo.AdapterGeo/Query"
)

// AdapterGeoClient is the client API for AdapterGeo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdapterGeoClient interface {
	Query(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error)
}

type adapterGeoClient struct {
	cc grpc.ClientConnInterface
}

func NewAdapterGeoClient(cc grpc.ClientConnInterface) AdapterGeoClient {
	return &adapterGeoClient{cc}
}

func (c *adapterGeoClient) Query(ctx context.Context, in *QueryReq, opts ...grpc.CallOption) (*QueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRsp)
	err := c.cc.Invoke(ctx, AdapterGeo_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterGeoServer is the server API for AdapterGeo service.
// All implementations should embed UnimplementedAdapterGeoServer
// for forward compatibility
type AdapterGeoServer interface {
	Query(context.Context, *QueryReq) (*QueryRsp, error)
}

// UnimplementedAdapterGeoServer should be embedded to have forward compatible implementations.
type UnimplementedAdapterGeoServer struct {
}

func (UnimplementedAdapterGeoServer) Query(context.Context, *QueryReq) (*QueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}

// UnsafeAdapterGeoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdapterGeoServer will
// result in compilation errors.
type UnsafeAdapterGeoServer interface {
	mustEmbedUnimplementedAdapterGeoServer()
}

func RegisterAdapterGeoServer(s grpc.ServiceRegistrar, srv AdapterGeoServer) {
	s.RegisterService(&AdapterGeo_ServiceDesc, srv)
}

func _AdapterGeo_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterGeoServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterGeo_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterGeoServer).Query(ctx, req.(*QueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdapterGeo_ServiceDesc is the grpc.ServiceDesc for AdapterGeo service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdapterGeo_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "adapter_geo.AdapterGeo",
	HandlerType: (*AdapterGeoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Query",
			Handler:    _AdapterGeo_Query_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/adapter_geo/adapter_geo.proto",
}
