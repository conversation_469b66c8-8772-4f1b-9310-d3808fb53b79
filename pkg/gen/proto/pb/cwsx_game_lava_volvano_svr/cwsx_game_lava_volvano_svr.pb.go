// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_lava_volvano_svr/cwsx_game_lava_volvano_svr.proto

package cwsx_game_lava_volvano_svr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	cwsx_game_lava_volvano_api "kugou_adapter_service/pkg/gen/proto/pb/cwsx_game_lava_volvano_api"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AttendActivityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	StartStage uint32 `protobuf:"varint,2,opt,name=startStage,proto3" json:"startStage,omitempty"` // 开始闯关数
	GroupSize  uint32 `protobuf:"varint,3,opt,name=groupSize,proto3" json:"groupSize,omitempty"`   // 组队人数
	StartTime  uint32 `protobuf:"varint,4,opt,name=startTime,proto3" json:"startTime,omitempty"`   // 开始时间
}

func (x *AttendActivityReq) Reset() {
	*x = AttendActivityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttendActivityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendActivityReq) ProtoMessage() {}

func (x *AttendActivityReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendActivityReq.ProtoReflect.Descriptor instead.
func (*AttendActivityReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{0}
}

func (x *AttendActivityReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AttendActivityReq) GetStartStage() uint32 {
	if x != nil {
		return x.StartStage
	}
	return 0
}

func (x *AttendActivityReq) GetGroupSize() uint32 {
	if x != nil {
		return x.GroupSize
	}
	return 0
}

func (x *AttendActivityReq) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

type AttendActivityRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Players    []string         `protobuf:"bytes,1,rep,name=players,proto3" json:"players,omitempty"`                                                                                                // openIds
	MPlayerIds map[string]int64 `protobuf:"bytes,2,rep,name=mPlayerIds,proto3" json:"mPlayerIds,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // openId->主键id
	SelfId     int64            `protobuf:"varint,3,opt,name=selfId,proto3" json:"selfId,omitempty"`                                                                                                 // 主键id
}

func (x *AttendActivityRsp) Reset() {
	*x = AttendActivityRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttendActivityRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendActivityRsp) ProtoMessage() {}

func (x *AttendActivityRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendActivityRsp.ProtoReflect.Descriptor instead.
func (*AttendActivityRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{1}
}

func (x *AttendActivityRsp) GetPlayers() []string {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *AttendActivityRsp) GetMPlayerIds() map[string]int64 {
	if x != nil {
		return x.MPlayerIds
	}
	return nil
}

func (x *AttendActivityRsp) GetSelfId() int64 {
	if x != nil {
		return x.SelfId
	}
	return 0
}

type UpdateProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId       string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	CurrentStage uint32 `protobuf:"varint,2,opt,name=currentStage,proto3" json:"currentStage,omitempty"` // 当前关卡数
	IsSuccess    bool   `protobuf:"varint,3,opt,name=isSuccess,proto3" json:"isSuccess,omitempty"`       // 是否成功
}

func (x *UpdateProgressReq) Reset() {
	*x = UpdateProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProgressReq) ProtoMessage() {}

func (x *UpdateProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProgressReq.ProtoReflect.Descriptor instead.
func (*UpdateProgressReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateProgressReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *UpdateProgressReq) GetCurrentStage() uint32 {
	if x != nil {
		return x.CurrentStage
	}
	return 0
}

func (x *UpdateProgressReq) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

type UpdateProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateProgressRsp) Reset() {
	*x = UpdateProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProgressRsp) ProtoMessage() {}

func (x *UpdateProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProgressRsp.ProtoReflect.Descriptor instead.
func (*UpdateProgressRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{3}
}

type SettleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardId        int64  `protobuf:"varint,1,opt,name=rewardId,proto3" json:"rewardId,omitempty"`
	RewardNum       uint32 `protobuf:"varint,2,opt,name=rewardNum,proto3" json:"rewardNum,omitempty"`
	SuccessionStage uint32 `protobuf:"varint,3,opt,name=successionStage,proto3" json:"successionStage,omitempty"`
}

func (x *SettleResult) Reset() {
	*x = SettleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleResult) ProtoMessage() {}

func (x *SettleResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleResult.ProtoReflect.Descriptor instead.
func (*SettleResult) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{4}
}

func (x *SettleResult) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *SettleResult) GetRewardNum() uint32 {
	if x != nil {
		return x.RewardNum
	}
	return 0
}

func (x *SettleResult) GetSuccessionStage() uint32 {
	if x != nil {
		return x.SuccessionStage
	}
	return 0
}

type ActivityRoundSettleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId       string                                   `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	UserProgress *cwsx_game_lava_volvano_api.UserProgress `protobuf:"bytes,2,opt,name=userProgress,proto3" json:"userProgress,omitempty"` // 进度状态
	Result       *SettleResult                            `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`             // 结算结果
	SettleTime   int64                                    `protobuf:"varint,4,opt,name=settleTime,proto3" json:"settleTime,omitempty"`    // 结算时间
	Id           int64                                    `protobuf:"varint,5,opt,name=id,proto3" json:"id,omitempty"`                    // 主键id
}

func (x *ActivityRoundSettleReq) Reset() {
	*x = ActivityRoundSettleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityRoundSettleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityRoundSettleReq) ProtoMessage() {}

func (x *ActivityRoundSettleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityRoundSettleReq.ProtoReflect.Descriptor instead.
func (*ActivityRoundSettleReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{5}
}

func (x *ActivityRoundSettleReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ActivityRoundSettleReq) GetUserProgress() *cwsx_game_lava_volvano_api.UserProgress {
	if x != nil {
		return x.UserProgress
	}
	return nil
}

func (x *ActivityRoundSettleReq) GetResult() *SettleResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ActivityRoundSettleReq) GetSettleTime() int64 {
	if x != nil {
		return x.SettleTime
	}
	return 0
}

func (x *ActivityRoundSettleReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ActivityRoundSettleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ActivityRoundSettleRsp) Reset() {
	*x = ActivityRoundSettleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityRoundSettleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityRoundSettleRsp) ProtoMessage() {}

func (x *ActivityRoundSettleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityRoundSettleRsp.ProtoReflect.Descriptor instead.
func (*ActivityRoundSettleRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{6}
}

type CheckGroupProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId          string           `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	SuccessionStage uint32           `protobuf:"varint,2,opt,name=successionStage,proto3" json:"successionStage,omitempty"`                                                                               // 连续关卡数
	Players         []string         `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`                                                                                                // 组队用户
	IsSuccess       bool             `protobuf:"varint,4,opt,name=isSuccess,proto3" json:"isSuccess,omitempty"`                                                                                           // 是否成功
	MPlayerIds      map[string]int64 `protobuf:"bytes,5,rep,name=mPlayerIds,proto3" json:"mPlayerIds,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // openId->主键id
}

func (x *CheckGroupProgressReq) Reset() {
	*x = CheckGroupProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckGroupProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGroupProgressReq) ProtoMessage() {}

func (x *CheckGroupProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGroupProgressReq.ProtoReflect.Descriptor instead.
func (*CheckGroupProgressReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{7}
}

func (x *CheckGroupProgressReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CheckGroupProgressReq) GetSuccessionStage() uint32 {
	if x != nil {
		return x.SuccessionStage
	}
	return 0
}

func (x *CheckGroupProgressReq) GetPlayers() []string {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *CheckGroupProgressReq) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *CheckGroupProgressReq) GetMPlayerIds() map[string]int64 {
	if x != nil {
		return x.MPlayerIds
	}
	return nil
}

type CheckGroupProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessPlayers []string `protobuf:"bytes,1,rep,name=successPlayers,proto3" json:"successPlayers,omitempty"` // 成功玩家
	FailPlayers    []string `protobuf:"bytes,2,rep,name=failPlayers,proto3" json:"failPlayers,omitempty"`       // 失败玩家
}

func (x *CheckGroupProgressRsp) Reset() {
	*x = CheckGroupProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckGroupProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGroupProgressRsp) ProtoMessage() {}

func (x *CheckGroupProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGroupProgressRsp.ProtoReflect.Descriptor instead.
func (*CheckGroupProgressRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP(), []int{8}
}

func (x *CheckGroupProgressRsp) GetSuccessPlayers() []string {
	if x != nil {
		return x.SuccessPlayers
	}
	return nil
}

func (x *CheckGroupProgressRsp) GetFailPlayers() []string {
	if x != nil {
		return x.FailPlayers
	}
	return nil
}

var File_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c,
	0x61, 0x76, 0x61, 0x5f, 0x76, 0x6f, 0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x5f, 0x73, 0x76, 0x72, 0x2f,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x76, 0x61, 0x5f, 0x76,
	0x6f, 0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x5f, 0x73, 0x76, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x09, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x2a, 0x70, 0x62, 0x2f,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x76, 0x61, 0x5f, 0x76,
	0x6f, 0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x87, 0x01, 0x0a, 0x11, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd2, 0x01,
	0x0a, 0x11, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a,
	0x0a, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x74,
	0x74, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x2e,
	0x4d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0a, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x6c, 0x66, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6c,
	0x66, 0x49, 0x64, 0x1a, 0x3d, 0x0a, 0x0f, 0x4d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x6d, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x22, 0x13, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x22, 0x72, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d,
	0x12, 0x28, 0x0a, 0x0f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x16, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x3b, 0x0a,
	0x0c, 0x75, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0c, 0x75, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x73, 0x70, 0x22, 0xa2, 0x02, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x69,
	0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x50, 0x0a, 0x0a, 0x6d, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x2e,
	0x4d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0a, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x4d,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x61, 0x0a, 0x15, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x66,
	0x61, 0x69, 0x6c, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x32, 0xfa, 0x03,
	0x0a, 0x16, 0x43, 0x77, 0x73, 0x78, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x61, 0x76, 0x61, 0x56, 0x6f,
	0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x53, 0x76, 0x72, 0x12, 0x4c, 0x0a, 0x0e, 0x41, 0x74, 0x74, 0x65,
	0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x1c, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x21, 0x2e, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x58, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0e, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x1b, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x6f, 0x70, 0x75, 0x70, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42, 0x52, 0x5a, 0x50, 0x74, 0x63,
	0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61,
	0x76, 0x61, 0x5f, 0x76, 0x6f, 0x6c, 0x76, 0x61, 0x6e, 0x6f, 0x5f, 0x73, 0x76, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescData = file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDesc
)

func file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescData)
	})
	return file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDescData
}

var file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_goTypes = []interface{}{
	(*AttendActivityReq)(nil),      // 0: cwsx_game.AttendActivityReq
	(*AttendActivityRsp)(nil),      // 1: cwsx_game.AttendActivityRsp
	(*UpdateProgressReq)(nil),      // 2: cwsx_game.UpdateProgressReq
	(*UpdateProgressRsp)(nil),      // 3: cwsx_game.UpdateProgressRsp
	(*SettleResult)(nil),           // 4: cwsx_game.SettleResult
	(*ActivityRoundSettleReq)(nil), // 5: cwsx_game.ActivityRoundSettleReq
	(*ActivityRoundSettleRsp)(nil), // 6: cwsx_game.ActivityRoundSettleRsp
	(*CheckGroupProgressReq)(nil),  // 7: cwsx_game.CheckGroupProgressReq
	(*CheckGroupProgressRsp)(nil),  // 8: cwsx_game.CheckGroupProgressRsp
	nil,                            // 9: cwsx_game.AttendActivityRsp.MPlayerIdsEntry
	nil,                            // 10: cwsx_game.CheckGroupProgressReq.MPlayerIdsEntry
	(*cwsx_game_lava_volvano_api.UserProgress)(nil), // 11: cwsx_game.UserProgress
	(*game_api.RetentionPopupReq)(nil),              // 12: game_api.RetentionPopupReq
	(*inlet.ActivityStateReq)(nil),                  // 13: inlet.ActivityStateReq
	(*game_api.RetentionPopupRsp)(nil),              // 14: game_api.RetentionPopupRsp
	(*inlet.ActivityStateRsp)(nil),                  // 15: inlet.ActivityStateRsp
}
var file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_depIdxs = []int32{
	9,  // 0: cwsx_game.AttendActivityRsp.mPlayerIds:type_name -> cwsx_game.AttendActivityRsp.MPlayerIdsEntry
	11, // 1: cwsx_game.ActivityRoundSettleReq.userProgress:type_name -> cwsx_game.UserProgress
	4,  // 2: cwsx_game.ActivityRoundSettleReq.result:type_name -> cwsx_game.SettleResult
	10, // 3: cwsx_game.CheckGroupProgressReq.mPlayerIds:type_name -> cwsx_game.CheckGroupProgressReq.MPlayerIdsEntry
	0,  // 4: cwsx_game.CwsxGameLavaVolvanoSvr.AttendActivity:input_type -> cwsx_game.AttendActivityReq
	2,  // 5: cwsx_game.CwsxGameLavaVolvanoSvr.UpdateProgress:input_type -> cwsx_game.UpdateProgressReq
	5,  // 6: cwsx_game.CwsxGameLavaVolvanoSvr.ActivityRoundSettle:input_type -> cwsx_game.ActivityRoundSettleReq
	7,  // 7: cwsx_game.CwsxGameLavaVolvanoSvr.CheckGroupProgress:input_type -> cwsx_game.CheckGroupProgressReq
	12, // 8: cwsx_game.CwsxGameLavaVolvanoSvr.RetentionPopup:input_type -> game_api.RetentionPopupReq
	13, // 9: cwsx_game.CwsxGameLavaVolvanoSvr.ActivityState:input_type -> inlet.ActivityStateReq
	1,  // 10: cwsx_game.CwsxGameLavaVolvanoSvr.AttendActivity:output_type -> cwsx_game.AttendActivityRsp
	3,  // 11: cwsx_game.CwsxGameLavaVolvanoSvr.UpdateProgress:output_type -> cwsx_game.UpdateProgressRsp
	6,  // 12: cwsx_game.CwsxGameLavaVolvanoSvr.ActivityRoundSettle:output_type -> cwsx_game.ActivityRoundSettleRsp
	8,  // 13: cwsx_game.CwsxGameLavaVolvanoSvr.CheckGroupProgress:output_type -> cwsx_game.CheckGroupProgressRsp
	14, // 14: cwsx_game.CwsxGameLavaVolvanoSvr.RetentionPopup:output_type -> game_api.RetentionPopupRsp
	15, // 15: cwsx_game.CwsxGameLavaVolvanoSvr.ActivityState:output_type -> inlet.ActivityStateRsp
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_init() }
func file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_init() {
	if File_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttendActivityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttendActivityRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityRoundSettleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityRoundSettleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckGroupProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckGroupProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_depIdxs,
		MessageInfos:      file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto = out.File
	file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_rawDesc = nil
	file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_goTypes = nil
	file_pb_cwsx_game_lava_volvano_svr_cwsx_game_lava_volvano_svr_proto_depIdxs = nil
}
