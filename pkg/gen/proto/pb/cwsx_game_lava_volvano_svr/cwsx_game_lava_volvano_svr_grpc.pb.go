// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/cwsx_game_lava_volvano_svr/cwsx_game_lava_volvano_svr.proto

package cwsx_game_lava_volvano_svr

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CwsxGameLavaVolvanoSvr_AttendActivity_FullMethodName      = "/cwsx_game.CwsxGameLavaVolvanoSvr/AttendActivity"
	CwsxGameLavaVolvanoSvr_UpdateProgress_FullMethodName      = "/cwsx_game.CwsxGameLavaVolvanoSvr/UpdateProgress"
	CwsxGameLavaVolvanoSvr_ActivityRoundSettle_FullMethodName = "/cwsx_game.CwsxGameLavaVolvanoSvr/ActivityRoundSettle"
	CwsxGameLavaVolvanoSvr_CheckGroupProgress_FullMethodName  = "/cwsx_game.CwsxGameLavaVolvanoSvr/CheckGroupProgress"
	CwsxGameLavaVolvanoSvr_RetentionPopup_FullMethodName      = "/cwsx_game.CwsxGameLavaVolvanoSvr/RetentionPopup"
	CwsxGameLavaVolvanoSvr_ActivityState_FullMethodName       = "/cwsx_game.CwsxGameLavaVolvanoSvr/ActivityState"
)

// CwsxGameLavaVolvanoSvrClient is the client API for CwsxGameLavaVolvanoSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// cwsx 火山熔岩
type CwsxGameLavaVolvanoSvrClient interface {
	// 参加活动
	AttendActivity(ctx context.Context, in *AttendActivityReq, opts ...grpc.CallOption) (*AttendActivityRsp, error)
	// 更新进度
	UpdateProgress(ctx context.Context, in *UpdateProgressReq, opts ...grpc.CallOption) (*UpdateProgressRsp, error)
	// 活动轮次结算
	ActivityRoundSettle(ctx context.Context, in *ActivityRoundSettleReq, opts ...grpc.CallOption) (*ActivityRoundSettleRsp, error)
	// 检查队伍进度
	CheckGroupProgress(ctx context.Context, in *CheckGroupProgressReq, opts ...grpc.CallOption) (*CheckGroupProgressRsp, error)
	// 挽留弹窗
	RetentionPopup(ctx context.Context, in *game_api.RetentionPopupReq, opts ...grpc.CallOption) (*game_api.RetentionPopupRsp, error)
	// 入口查询【仅供入口服务查询】
	ActivityState(ctx context.Context, in *inlet.ActivityStateReq, opts ...grpc.CallOption) (*inlet.ActivityStateRsp, error)
}

type cwsxGameLavaVolvanoSvrClient struct {
	cc grpc.ClientConnInterface
}

func NewCwsxGameLavaVolvanoSvrClient(cc grpc.ClientConnInterface) CwsxGameLavaVolvanoSvrClient {
	return &cwsxGameLavaVolvanoSvrClient{cc}
}

func (c *cwsxGameLavaVolvanoSvrClient) AttendActivity(ctx context.Context, in *AttendActivityReq, opts ...grpc.CallOption) (*AttendActivityRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttendActivityRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoSvr_AttendActivity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoSvrClient) UpdateProgress(ctx context.Context, in *UpdateProgressReq, opts ...grpc.CallOption) (*UpdateProgressRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateProgressRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoSvr_UpdateProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoSvrClient) ActivityRoundSettle(ctx context.Context, in *ActivityRoundSettleReq, opts ...grpc.CallOption) (*ActivityRoundSettleRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivityRoundSettleRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoSvr_ActivityRoundSettle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoSvrClient) CheckGroupProgress(ctx context.Context, in *CheckGroupProgressReq, opts ...grpc.CallOption) (*CheckGroupProgressRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckGroupProgressRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoSvr_CheckGroupProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoSvrClient) RetentionPopup(ctx context.Context, in *game_api.RetentionPopupReq, opts ...grpc.CallOption) (*game_api.RetentionPopupRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game_api.RetentionPopupRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoSvr_RetentionPopup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cwsxGameLavaVolvanoSvrClient) ActivityState(ctx context.Context, in *inlet.ActivityStateReq, opts ...grpc.CallOption) (*inlet.ActivityStateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(inlet.ActivityStateRsp)
	err := c.cc.Invoke(ctx, CwsxGameLavaVolvanoSvr_ActivityState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CwsxGameLavaVolvanoSvrServer is the server API for CwsxGameLavaVolvanoSvr service.
// All implementations should embed UnimplementedCwsxGameLavaVolvanoSvrServer
// for forward compatibility
//
// cwsx 火山熔岩
type CwsxGameLavaVolvanoSvrServer interface {
	// 参加活动
	AttendActivity(context.Context, *AttendActivityReq) (*AttendActivityRsp, error)
	// 更新进度
	UpdateProgress(context.Context, *UpdateProgressReq) (*UpdateProgressRsp, error)
	// 活动轮次结算
	ActivityRoundSettle(context.Context, *ActivityRoundSettleReq) (*ActivityRoundSettleRsp, error)
	// 检查队伍进度
	CheckGroupProgress(context.Context, *CheckGroupProgressReq) (*CheckGroupProgressRsp, error)
	// 挽留弹窗
	RetentionPopup(context.Context, *game_api.RetentionPopupReq) (*game_api.RetentionPopupRsp, error)
	// 入口查询【仅供入口服务查询】
	ActivityState(context.Context, *inlet.ActivityStateReq) (*inlet.ActivityStateRsp, error)
}

// UnimplementedCwsxGameLavaVolvanoSvrServer should be embedded to have forward compatible implementations.
type UnimplementedCwsxGameLavaVolvanoSvrServer struct {
}

func (UnimplementedCwsxGameLavaVolvanoSvrServer) AttendActivity(context.Context, *AttendActivityReq) (*AttendActivityRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AttendActivity not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoSvrServer) UpdateProgress(context.Context, *UpdateProgressReq) (*UpdateProgressRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProgress not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoSvrServer) ActivityRoundSettle(context.Context, *ActivityRoundSettleReq) (*ActivityRoundSettleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityRoundSettle not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoSvrServer) CheckGroupProgress(context.Context, *CheckGroupProgressReq) (*CheckGroupProgressRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckGroupProgress not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoSvrServer) RetentionPopup(context.Context, *game_api.RetentionPopupReq) (*game_api.RetentionPopupRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetentionPopup not implemented")
}
func (UnimplementedCwsxGameLavaVolvanoSvrServer) ActivityState(context.Context, *inlet.ActivityStateReq) (*inlet.ActivityStateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityState not implemented")
}

// UnsafeCwsxGameLavaVolvanoSvrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CwsxGameLavaVolvanoSvrServer will
// result in compilation errors.
type UnsafeCwsxGameLavaVolvanoSvrServer interface {
	mustEmbedUnimplementedCwsxGameLavaVolvanoSvrServer()
}

func RegisterCwsxGameLavaVolvanoSvrServer(s grpc.ServiceRegistrar, srv CwsxGameLavaVolvanoSvrServer) {
	s.RegisterService(&CwsxGameLavaVolvanoSvr_ServiceDesc, srv)
}

func _CwsxGameLavaVolvanoSvr_AttendActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttendActivityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoSvrServer).AttendActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoSvr_AttendActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoSvrServer).AttendActivity(ctx, req.(*AttendActivityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoSvr_UpdateProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoSvrServer).UpdateProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoSvr_UpdateProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoSvrServer).UpdateProgress(ctx, req.(*UpdateProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoSvr_ActivityRoundSettle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityRoundSettleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoSvrServer).ActivityRoundSettle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoSvr_ActivityRoundSettle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoSvrServer).ActivityRoundSettle(ctx, req.(*ActivityRoundSettleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoSvr_CheckGroupProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckGroupProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoSvrServer).CheckGroupProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoSvr_CheckGroupProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoSvrServer).CheckGroupProgress(ctx, req.(*CheckGroupProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoSvr_RetentionPopup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_api.RetentionPopupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoSvrServer).RetentionPopup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoSvr_RetentionPopup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoSvrServer).RetentionPopup(ctx, req.(*game_api.RetentionPopupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CwsxGameLavaVolvanoSvr_ActivityState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(inlet.ActivityStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CwsxGameLavaVolvanoSvrServer).ActivityState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CwsxGameLavaVolvanoSvr_ActivityState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CwsxGameLavaVolvanoSvrServer).ActivityState(ctx, req.(*inlet.ActivityStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CwsxGameLavaVolvanoSvr_ServiceDesc is the grpc.ServiceDesc for CwsxGameLavaVolvanoSvr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CwsxGameLavaVolvanoSvr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cwsx_game.CwsxGameLavaVolvanoSvr",
	HandlerType: (*CwsxGameLavaVolvanoSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AttendActivity",
			Handler:    _CwsxGameLavaVolvanoSvr_AttendActivity_Handler,
		},
		{
			MethodName: "UpdateProgress",
			Handler:    _CwsxGameLavaVolvanoSvr_UpdateProgress_Handler,
		},
		{
			MethodName: "ActivityRoundSettle",
			Handler:    _CwsxGameLavaVolvanoSvr_ActivityRoundSettle_Handler,
		},
		{
			MethodName: "CheckGroupProgress",
			Handler:    _CwsxGameLavaVolvanoSvr_CheckGroupProgress_Handler,
		},
		{
			MethodName: "RetentionPopup",
			Handler:    _CwsxGameLavaVolvanoSvr_RetentionPopup_Handler,
		},
		{
			MethodName: "ActivityState",
			Handler:    _CwsxGameLavaVolvanoSvr_ActivityState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/cwsx_game_lava_volvano_svr/cwsx_game_lava_volvano_svr.proto",
}
