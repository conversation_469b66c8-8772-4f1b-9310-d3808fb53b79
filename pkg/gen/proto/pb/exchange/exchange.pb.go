// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/exchange/exchange.proto

package exchange

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetExchangeCurrencyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// strAppID
	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`
	// strOpenID
	StrOpenID string `protobuf:"bytes,2,opt,name=strOpenID,proto3" json:"strOpenID,omitempty"`
	// uActID 活动ID
	UActID uint32 `protobuf:"varint,3,opt,name=uActID,proto3" json:"uActID,omitempty"`
}

func (x *GetExchangeCurrencyReq) Reset() {
	*x = GetExchangeCurrencyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeCurrencyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeCurrencyReq) ProtoMessage() {}

func (x *GetExchangeCurrencyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeCurrencyReq.ProtoReflect.Descriptor instead.
func (*GetExchangeCurrencyReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{0}
}

func (x *GetExchangeCurrencyReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetExchangeCurrencyReq) GetStrOpenID() string {
	if x != nil {
		return x.StrOpenID
	}
	return ""
}

func (x *GetExchangeCurrencyReq) GetUActID() uint32 {
	if x != nil {
		return x.UActID
	}
	return 0
}

type GetExchangeCurrencyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lBalance
	LBalance uint64 `protobuf:"varint,1,opt,name=lBalance,proto3" json:"lBalance,omitempty"`
	// strIcon 图标
	StrIcon string `protobuf:"bytes,2,opt,name=strIcon,proto3" json:"strIcon,omitempty"`
	// strTag
	StrTag string `protobuf:"bytes,3,opt,name=strTag,proto3" json:"strTag,omitempty"`
	// strCurrencyName 游戏币名称
	StrCurrencyName string `protobuf:"bytes,4,opt,name=strCurrencyName,proto3" json:"strCurrencyName,omitempty"`
}

func (x *GetExchangeCurrencyRsp) Reset() {
	*x = GetExchangeCurrencyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeCurrencyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeCurrencyRsp) ProtoMessage() {}

func (x *GetExchangeCurrencyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeCurrencyRsp.ProtoReflect.Descriptor instead.
func (*GetExchangeCurrencyRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{1}
}

func (x *GetExchangeCurrencyRsp) GetLBalance() uint64 {
	if x != nil {
		return x.LBalance
	}
	return 0
}

func (x *GetExchangeCurrencyRsp) GetStrIcon() string {
	if x != nil {
		return x.StrIcon
	}
	return ""
}

func (x *GetExchangeCurrencyRsp) GetStrTag() string {
	if x != nil {
		return x.StrTag
	}
	return ""
}

func (x *GetExchangeCurrencyRsp) GetStrCurrencyName() string {
	if x != nil {
		return x.StrCurrencyName
	}
	return ""
}

type DoExchangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// strAppID
	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`
	// strOpenID
	StrOpenID string `protobuf:"bytes,2,opt,name=strOpenID,proto3" json:"strOpenID,omitempty"`
	// uActID 活动ID
	UActID uint32 `protobuf:"varint,3,opt,name=uActID,proto3" json:"uActID,omitempty"`
	// uExchangeID 兑换ID
	UExchangeID uint32 `protobuf:"varint,4,opt,name=uExchangeID,proto3" json:"uExchangeID,omitempty"`
	// uNum 数量
	UNum uint32 `protobuf:"varint,5,opt,name=uNum,proto3" json:"uNum,omitempty"`
	// strUniqueId 唯一ID
	StrUniqueId string `protobuf:"bytes,6,opt,name=strUniqueId,proto3" json:"strUniqueId,omitempty"`
}

func (x *DoExchangeReq) Reset() {
	*x = DoExchangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoExchangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoExchangeReq) ProtoMessage() {}

func (x *DoExchangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoExchangeReq.ProtoReflect.Descriptor instead.
func (*DoExchangeReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{2}
}

func (x *DoExchangeReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *DoExchangeReq) GetStrOpenID() string {
	if x != nil {
		return x.StrOpenID
	}
	return ""
}

func (x *DoExchangeReq) GetUActID() uint32 {
	if x != nil {
		return x.UActID
	}
	return 0
}

func (x *DoExchangeReq) GetUExchangeID() uint32 {
	if x != nil {
		return x.UExchangeID
	}
	return 0
}

func (x *DoExchangeReq) GetUNum() uint32 {
	if x != nil {
		return x.UNum
	}
	return 0
}

func (x *DoExchangeReq) GetStrUniqueId() string {
	if x != nil {
		return x.StrUniqueId
	}
	return ""
}

type DoExchangeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// iRes 返回码
	IRes int32 `protobuf:"varint,1,opt,name=iRes,proto3" json:"iRes,omitempty"`
	// strMsg 提示文案
	StrMsg string `protobuf:"bytes,2,opt,name=strMsg,proto3" json:"strMsg,omitempty"`
}

func (x *DoExchangeRsp) Reset() {
	*x = DoExchangeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoExchangeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoExchangeRsp) ProtoMessage() {}

func (x *DoExchangeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoExchangeRsp.ProtoReflect.Descriptor instead.
func (*DoExchangeRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{3}
}

func (x *DoExchangeRsp) GetIRes() int32 {
	if x != nil {
		return x.IRes
	}
	return 0
}

func (x *DoExchangeRsp) GetStrMsg() string {
	if x != nil {
		return x.StrMsg
	}
	return ""
}

type ExchangeListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// strAppID
	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`
	// strOpenID
	StrOpenID string `protobuf:"bytes,2,opt,name=strOpenID,proto3" json:"strOpenID,omitempty"`
	// uActID 活动ID
	UActID uint32 `protobuf:"varint,3,opt,name=uActID,proto3" json:"uActID,omitempty"`
	// vctActIDTabList Tab活动ID列表
	VctActIDTabList []uint32 `protobuf:"varint,4,rep,packed,name=vctActIDTabList,proto3" json:"vctActIDTabList,omitempty"`
}

func (x *ExchangeListReq) Reset() {
	*x = ExchangeListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeListReq) ProtoMessage() {}

func (x *ExchangeListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeListReq.ProtoReflect.Descriptor instead.
func (*ExchangeListReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{4}
}

func (x *ExchangeListReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *ExchangeListReq) GetStrOpenID() string {
	if x != nil {
		return x.StrOpenID
	}
	return ""
}

func (x *ExchangeListReq) GetUActID() uint32 {
	if x != nil {
		return x.UActID
	}
	return 0
}

func (x *ExchangeListReq) GetVctActIDTabList() []uint32 {
	if x != nil {
		return x.VctActIDTabList
	}
	return nil
}

type ExchangeListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stExchList 物品列表
	StExchList *ActExchList `protobuf:"bytes,1,opt,name=stExchList,proto3" json:"stExchList,omitempty"`
}

func (x *ExchangeListRsp) Reset() {
	*x = ExchangeListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeListRsp) ProtoMessage() {}

func (x *ExchangeListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeListRsp.ProtoReflect.Descriptor instead.
func (*ExchangeListRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{5}
}

func (x *ExchangeListRsp) GetStExchList() *ActExchList {
	if x != nil {
		return x.StExchList
	}
	return nil
}

type ExchangeCurrencyRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// strAppID
	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`
	// strOpenID
	StrOpenID string `protobuf:"bytes,2,opt,name=strOpenID,proto3" json:"strOpenID,omitempty"`
	// uActID 活动ID
	UActID uint32 `protobuf:"varint,3,opt,name=uActID,proto3" json:"uActID,omitempty"`
	// strPassBack
	StrPassBack string `protobuf:"bytes,4,opt,name=strPassBack,proto3" json:"strPassBack,omitempty"`
}

func (x *ExchangeCurrencyRecordReq) Reset() {
	*x = ExchangeCurrencyRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyRecordReq) ProtoMessage() {}

func (x *ExchangeCurrencyRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyRecordReq.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{6}
}

func (x *ExchangeCurrencyRecordReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *ExchangeCurrencyRecordReq) GetStrOpenID() string {
	if x != nil {
		return x.StrOpenID
	}
	return ""
}

func (x *ExchangeCurrencyRecordReq) GetUActID() uint32 {
	if x != nil {
		return x.UActID
	}
	return 0
}

func (x *ExchangeCurrencyRecordReq) GetStrPassBack() string {
	if x != nil {
		return x.StrPassBack
	}
	return ""
}

type ExchangeCurrencyRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// bHasMore
	BHasMore bool `protobuf:"varint,1,opt,name=bHasMore,proto3" json:"bHasMore,omitempty"`
	// strPassBack
	StrPassBack string `protobuf:"bytes,2,opt,name=strPassBack,proto3" json:"strPassBack,omitempty"`
	// vctRecord 兑换币明细
	VctRecord []*ExchCurrencyRecord `protobuf:"bytes,3,rep,name=vctRecord,proto3" json:"vctRecord,omitempty"`
}

func (x *ExchangeCurrencyRecordRsp) Reset() {
	*x = ExchangeCurrencyRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyRecordRsp) ProtoMessage() {}

func (x *ExchangeCurrencyRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyRecordRsp.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{7}
}

func (x *ExchangeCurrencyRecordRsp) GetBHasMore() bool {
	if x != nil {
		return x.BHasMore
	}
	return false
}

func (x *ExchangeCurrencyRecordRsp) GetStrPassBack() string {
	if x != nil {
		return x.StrPassBack
	}
	return ""
}

func (x *ExchangeCurrencyRecordRsp) GetVctRecord() []*ExchCurrencyRecord {
	if x != nil {
		return x.VctRecord
	}
	return nil
}

type DoExchCurrencyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appId
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	// openId
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	// fromAsset 扣除的货币
	FromAsset *ExchAsset `protobuf:"bytes,3,opt,name=fromAsset,proto3" json:"fromAsset,omitempty"`
	// toAsset 获得的货币
	ToAsset *ExchAsset `protobuf:"bytes,4,opt,name=toAsset,proto3" json:"toAsset,omitempty"`
	// strUniqueId 唯一ID
	UniqueId string `protobuf:"bytes,5,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
}

func (x *DoExchCurrencyReq) Reset() {
	*x = DoExchCurrencyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoExchCurrencyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoExchCurrencyReq) ProtoMessage() {}

func (x *DoExchCurrencyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoExchCurrencyReq.ProtoReflect.Descriptor instead.
func (*DoExchCurrencyReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{8}
}

func (x *DoExchCurrencyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DoExchCurrencyReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DoExchCurrencyReq) GetFromAsset() *ExchAsset {
	if x != nil {
		return x.FromAsset
	}
	return nil
}

func (x *DoExchCurrencyReq) GetToAsset() *ExchAsset {
	if x != nil {
		return x.ToAsset
	}
	return nil
}

func (x *DoExchCurrencyReq) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

type DoExchCurrencyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// iRes 返回码
	IRes int32 `protobuf:"varint,1,opt,name=iRes,proto3" json:"iRes,omitempty"`
	// msg 返回文案
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// assets 结果资产列表
	Assets []*UserExchangeAsset `protobuf:"bytes,3,rep,name=assets,proto3" json:"assets,omitempty"`
}

func (x *DoExchCurrencyRsp) Reset() {
	*x = DoExchCurrencyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoExchCurrencyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoExchCurrencyRsp) ProtoMessage() {}

func (x *DoExchCurrencyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoExchCurrencyRsp.ProtoReflect.Descriptor instead.
func (*DoExchCurrencyRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{9}
}

func (x *DoExchCurrencyRsp) GetIRes() int32 {
	if x != nil {
		return x.IRes
	}
	return 0
}

func (x *DoExchCurrencyRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoExchCurrencyRsp) GetAssets() []*UserExchangeAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type DoExchCurrencyByFlowerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appId
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	// openId
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	// 兑换消耗的鲜花数目
	FlowerNum uint32 `protobuf:"varint,3,opt,name=flowerNum,proto3" json:"flowerNum,omitempty"`
	// toAsset 兑换期望获得的asset
	ToAsset *ExchAsset `protobuf:"bytes,4,opt,name=toAsset,proto3" json:"toAsset,omitempty"`
	// strUniqueId 唯一ID，长度不超过50
	UniqueId string `protobuf:"bytes,5,opt,name=uniqueId,proto3" json:"uniqueId,omitempty"`
}

func (x *DoExchCurrencyByFlowerReq) Reset() {
	*x = DoExchCurrencyByFlowerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoExchCurrencyByFlowerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoExchCurrencyByFlowerReq) ProtoMessage() {}

func (x *DoExchCurrencyByFlowerReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoExchCurrencyByFlowerReq.ProtoReflect.Descriptor instead.
func (*DoExchCurrencyByFlowerReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{10}
}

func (x *DoExchCurrencyByFlowerReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DoExchCurrencyByFlowerReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DoExchCurrencyByFlowerReq) GetFlowerNum() uint32 {
	if x != nil {
		return x.FlowerNum
	}
	return 0
}

func (x *DoExchCurrencyByFlowerReq) GetToAsset() *ExchAsset {
	if x != nil {
		return x.ToAsset
	}
	return nil
}

func (x *DoExchCurrencyByFlowerReq) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

type DoExchCurrencyByFlowerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// assets 结果资产列表
	Assets []*UserExchangeAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
}

func (x *DoExchCurrencyByFlowerRsp) Reset() {
	*x = DoExchCurrencyByFlowerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_exchange_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoExchCurrencyByFlowerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoExchCurrencyByFlowerRsp) ProtoMessage() {}

func (x *DoExchCurrencyByFlowerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_exchange_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoExchCurrencyByFlowerRsp.ProtoReflect.Descriptor instead.
func (*DoExchCurrencyByFlowerRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_exchange_proto_rawDescGZIP(), []int{11}
}

func (x *DoExchCurrencyByFlowerRsp) GetAssets() []*UserExchangeAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

var File_pb_exchange_exchange_proto protoreflect.FileDescriptor

var file_pb_exchange_exchange_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x62, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2f, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1f, 0x70, 0x62,
	0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6a, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x44, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x22, 0x90, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x72, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x54,
	0x61, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb9, 0x01, 0x0a,
	0x0d, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x41, 0x63, 0x74,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x44, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x75, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x22, 0x3b, 0x0a, 0x0d, 0x44, 0x6f, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x52, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x69, 0x52, 0x65, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x12, 0x28, 0x0a, 0x0f, 0x76,
	0x63, 0x74, 0x41, 0x63, 0x74, 0x49, 0x44, 0x54, 0x61, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x0f, 0x76, 0x63, 0x74, 0x41, 0x63, 0x74, 0x49, 0x44, 0x54, 0x61,
	0x62, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0a, 0x73, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x63,
	0x74, 0x45, 0x78, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0a, 0x73, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x8f, 0x01, 0x0a, 0x19, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75,
	0x41, 0x63, 0x74, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73,
	0x42, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x50,
	0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x9b, 0x01, 0x0a, 0x19, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x62, 0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42,
	0x61, 0x63, 0x6b, 0x12, 0x40, 0x0a, 0x09, 0x76, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x09, 0x76, 0x63, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0xcb, 0x01, 0x0a, 0x11, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f,
	0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78,
	0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x07,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x11, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x52, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x69, 0x52, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x39,
	0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0xb8, 0x01, 0x0a, 0x19, 0x44, 0x6f,
	0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x46, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4e,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x07, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x19, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x73,
	0x70, 0x12, 0x39, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x32, 0xc7, 0x04, 0x0a,
	0x08, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x65, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x73, 0x70,
	0x12, 0x4a, 0x0a, 0x0a, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1d,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44,
	0x6f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x0c,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6e,
	0x0a, 0x16, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x56,
	0x0a, 0x0e, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x73, 0x70, 0x12, 0x6e, 0x0a, 0x16, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x44, 0x6f, 0x45, 0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x42, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x6f, 0x45,
	0x78, 0x63, 0x68, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x79, 0x46, 0x6c, 0x6f,
	0x77, 0x65, 0x72, 0x52, 0x73, 0x70, 0x42, 0x40, 0x5a, 0x3e, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_exchange_exchange_proto_rawDescOnce sync.Once
	file_pb_exchange_exchange_proto_rawDescData = file_pb_exchange_exchange_proto_rawDesc
)

func file_pb_exchange_exchange_proto_rawDescGZIP() []byte {
	file_pb_exchange_exchange_proto_rawDescOnce.Do(func() {
		file_pb_exchange_exchange_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_exchange_exchange_proto_rawDescData)
	})
	return file_pb_exchange_exchange_proto_rawDescData
}

var file_pb_exchange_exchange_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_pb_exchange_exchange_proto_goTypes = []interface{}{
	(*GetExchangeCurrencyReq)(nil),    // 0: component.game.GetExchangeCurrencyReq
	(*GetExchangeCurrencyRsp)(nil),    // 1: component.game.GetExchangeCurrencyRsp
	(*DoExchangeReq)(nil),             // 2: component.game.DoExchangeReq
	(*DoExchangeRsp)(nil),             // 3: component.game.DoExchangeRsp
	(*ExchangeListReq)(nil),           // 4: component.game.ExchangeListReq
	(*ExchangeListRsp)(nil),           // 5: component.game.ExchangeListRsp
	(*ExchangeCurrencyRecordReq)(nil), // 6: component.game.ExchangeCurrencyRecordReq
	(*ExchangeCurrencyRecordRsp)(nil), // 7: component.game.ExchangeCurrencyRecordRsp
	(*DoExchCurrencyReq)(nil),         // 8: component.game.DoExchCurrencyReq
	(*DoExchCurrencyRsp)(nil),         // 9: component.game.DoExchCurrencyRsp
	(*DoExchCurrencyByFlowerReq)(nil), // 10: component.game.DoExchCurrencyByFlowerReq
	(*DoExchCurrencyByFlowerRsp)(nil), // 11: component.game.DoExchCurrencyByFlowerRsp
	(*ActExchList)(nil),               // 12: component.game.ActExchList
	(*ExchCurrencyRecord)(nil),        // 13: component.game.ExchCurrencyRecord
	(*ExchAsset)(nil),                 // 14: component.game.ExchAsset
	(*UserExchangeAsset)(nil),         // 15: component.game.UserExchangeAsset
}
var file_pb_exchange_exchange_proto_depIdxs = []int32{
	12, // 0: component.game.ExchangeListRsp.stExchList:type_name -> component.game.ActExchList
	13, // 1: component.game.ExchangeCurrencyRecordRsp.vctRecord:type_name -> component.game.ExchCurrencyRecord
	14, // 2: component.game.DoExchCurrencyReq.fromAsset:type_name -> component.game.ExchAsset
	14, // 3: component.game.DoExchCurrencyReq.toAsset:type_name -> component.game.ExchAsset
	15, // 4: component.game.DoExchCurrencyRsp.assets:type_name -> component.game.UserExchangeAsset
	14, // 5: component.game.DoExchCurrencyByFlowerReq.toAsset:type_name -> component.game.ExchAsset
	15, // 6: component.game.DoExchCurrencyByFlowerRsp.assets:type_name -> component.game.UserExchangeAsset
	0,  // 7: component.game.Exchange.GetExchangeCurrency:input_type -> component.game.GetExchangeCurrencyReq
	2,  // 8: component.game.Exchange.DoExchange:input_type -> component.game.DoExchangeReq
	4,  // 9: component.game.Exchange.ExchangeList:input_type -> component.game.ExchangeListReq
	6,  // 10: component.game.Exchange.ExchangeCurrencyRecord:input_type -> component.game.ExchangeCurrencyRecordReq
	8,  // 11: component.game.Exchange.DoExchCurrency:input_type -> component.game.DoExchCurrencyReq
	10, // 12: component.game.Exchange.DoExchCurrencyByFlower:input_type -> component.game.DoExchCurrencyByFlowerReq
	1,  // 13: component.game.Exchange.GetExchangeCurrency:output_type -> component.game.GetExchangeCurrencyRsp
	3,  // 14: component.game.Exchange.DoExchange:output_type -> component.game.DoExchangeRsp
	5,  // 15: component.game.Exchange.ExchangeList:output_type -> component.game.ExchangeListRsp
	7,  // 16: component.game.Exchange.ExchangeCurrencyRecord:output_type -> component.game.ExchangeCurrencyRecordRsp
	9,  // 17: component.game.Exchange.DoExchCurrency:output_type -> component.game.DoExchCurrencyRsp
	11, // 18: component.game.Exchange.DoExchCurrencyByFlower:output_type -> component.game.DoExchCurrencyByFlowerRsp
	13, // [13:19] is the sub-list for method output_type
	7,  // [7:13] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_pb_exchange_exchange_proto_init() }
func file_pb_exchange_exchange_proto_init() {
	if File_pb_exchange_exchange_proto != nil {
		return
	}
	file_pb_exchange_exchange_comm_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_exchange_exchange_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeCurrencyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeCurrencyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoExchangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoExchangeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoExchCurrencyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoExchCurrencyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoExchCurrencyByFlowerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_exchange_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DoExchCurrencyByFlowerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_exchange_exchange_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_exchange_exchange_proto_goTypes,
		DependencyIndexes: file_pb_exchange_exchange_proto_depIdxs,
		MessageInfos:      file_pb_exchange_exchange_proto_msgTypes,
	}.Build()
	File_pb_exchange_exchange_proto = out.File
	file_pb_exchange_exchange_proto_rawDesc = nil
	file_pb_exchange_exchange_proto_goTypes = nil
	file_pb_exchange_exchange_proto_depIdxs = nil
}
