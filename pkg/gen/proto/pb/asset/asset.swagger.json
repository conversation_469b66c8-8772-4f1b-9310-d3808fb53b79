{"swagger": "2.0", "info": {"title": "pb/asset/asset.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.Asset/Add": {"post": {"summary": "增加资产", "operationId": "Asset_Add", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameAddResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameAddRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/BatchList": {"post": {"summary": "批量查询资产", "operationId": "Asset_BatchList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBatchListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBatchListReq"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/CancelSub": {"post": {"summary": "回滚", "operationId": "Asset_CancelSub", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameCancelSubResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameCancelSubRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/Exchange": {"post": {"summary": "游戏币兑换", "operationId": "Asset_Exchange", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/Expire": {"post": {"summary": "过期", "operationId": "Asset_Expire", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExpireResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExpireRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/List": {"post": {"summary": "查询资产列表", "operationId": "Asset_List", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameListRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/QueryAssetInfo": {"post": {"summary": "查询资产配置信息", "operationId": "Asset_QueryAssetInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQueryAssetInfoResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQueryAssetInfoRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/QueryNum": {"post": {"summary": "查询资产数量", "operationId": "Asset_QueryNum", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameQueryNumResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameQueryNumRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/RecordList": {"post": {"summary": "查询资产记录", "operationId": "Asset_RecordList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameRecordListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameRecordListRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/SettleAll": {"post": {"summary": "资产切换结算, app独立资产切换为统一资产, 例如: 五子棋 悔棋卡切换为统一道具卡", "operationId": "Asset_SettleAll", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSettleAllResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSettleAllRequest"}}], "tags": ["<PERSON><PERSON>"]}}, "/component.game.Asset/Sub": {"post": {"summary": "扣减资产", "operationId": "Asset_Sub", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSubResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSubRequest"}}], "tags": ["<PERSON><PERSON>"]}}}, "definitions": {"AssetInfoType": {"type": "string", "enum": ["AssetInvalid", "AssetGameCurrency", "AssetExchangeCurrency"], "default": "AssetInvalid"}, "BatchListReqOption": {"type": "object", "properties": {"UsePlatUid": {"type": "boolean", "title": "是否使用平台Uid,当UsePlatUid=true时,openId传平台uid字符串即可,会自动转化为中台openId"}, "UseCache": {"type": "boolean", "title": "为true时从ckv读取缓存,否则读DB,当指定读缓存时,不会返回资产过期信息切不支持根据标签拉取资产\n多读场景考虑设置UseCache=true"}}}, "gameAddRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAssetChange"}, "title": "增加资产列表"}, "transactionId": {"type": "string", "title": "唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式"}, "reason": {"type": "string", "title": "操作原因"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位秒"}, "restore": {"type": "boolean", "title": "是否补发请求"}, "roundId": {"type": "string", "title": "游戏场次 id"}, "source": {"$ref": "#/definitions/gameAssetSource", "title": "来源"}, "from": {"$ref": "#/definitions/gameAssetFromType", "title": "来自付费购买 还是 免费兑换"}, "reportId": {"type": "string", "format": "int64", "title": "上报 id"}, "reportStr": {"type": "string", "title": "上报 str"}, "trace": {"$ref": "#/definitions/gameAssetTraceType", "title": "业务调用标记,由链路调用起点开始标记,请在pb中定义,防止不同来源出现混淆"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "通用透传字段"}}}, "gameAddResponse": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}, "limits": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameLimitInfo"}, "title": "限制信息(asset_id -> LimitInfo)"}}}, "gameAssetAddOption": {"type": "object", "properties": {"limit": {"type": "string", "format": "int64", "title": "增加上限, 不指定着不限制, 否则最多只能加到limit封顶"}}}, "gameAssetFromType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>", "AssetFromFree", "AssetFromPay"], "default": "<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>", "title": "- AssetFromDefault: 默认, 会被重置为来自免费兑换\n - AssetFromFree: 来自免费兑换\n - AssetFromPay: 来自K币购买"}, "gameAssetInfo": {"type": "object", "properties": {"name": {"type": "string", "title": "资产名称"}, "icon": {"type": "string", "title": "资产图标"}, "tag": {"type": "string", "title": "资产标签"}, "price": {"type": "string", "format": "int64", "title": "单价 相对于平台币的价格 精度为 0.001"}, "type": {"$ref": "#/definitions/AssetInfoType"}}, "title": "资产信息"}, "gameAssetRecord": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64"}, "assetNum": {"type": "string", "format": "int64"}, "action": {"type": "string"}, "timestamp": {"type": "string", "format": "int64"}, "reason": {"type": "string", "title": "透传reason，业务可自行使用"}}}, "gameAssetSource": {"type": "string", "enum": ["SourceDefault", "SourceRestore", "SourceReturn"], "default": "SourceDefault", "title": "- SourceRestore: 补发\n - SourceReturn: 返还"}, "gameAssetTraceType": {"type": "string", "enum": ["AssetTraceTypeUnknown", "AssettraceTypeIDCReward"], "default": "AssetTraceTypeUnknown", "description": "- AssetTraceTypeUnknown: 未知\n - AssettraceTypeIDCReward: 来自IDC礼包发放游戏资产", "title": "AssetTraceType 资产链路追踪"}, "gameBatchListReq": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameBatchListReqItem"}}, "option": {"$ref": "#/definitions/BatchListReqOption"}}}, "gameBatchListReqItem": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产 ID 不填拉所有"}, "requireExpireInfo": {"type": "boolean", "title": "是否需要过期信息"}, "tag": {"type": "string", "title": "根据标签拉资产"}, "requireEmptyAsset": {"type": "boolean", "title": "是否需要数目为0的资产"}}}, "gameBatchListRsp": {"type": "object", "properties": {"items": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameBatchListRspItem"}}, "mpUniqAppIds": {"type": "object", "additionalProperties": {"type": "string"}, "title": "key: sourceAppId, value: uniqAppId, 先检查是否有uniqAppId, 再从item取结果"}}}, "gameBatchListRspItem": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameCancelSubRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "transactionId": {"type": "string"}, "openId": {"type": "string"}}}, "gameCancelSubResponse": {"type": "object"}, "gameExchangeRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "fromAsset": {"$ref": "#/definitions/gameUserAssetChange"}, "toAsset": {"$ref": "#/definitions/gameUserAssetChange"}, "transactionId": {"type": "string", "title": "唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式"}, "reason": {"type": "string", "title": "操作原因"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位秒"}, "roundId": {"type": "string", "title": "游戏场次 id"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "通用透传字段"}}}, "gameExchangeResponse": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameExpireInfo": {"type": "object", "properties": {"assetNum": {"type": "string", "format": "int64", "title": "资产数量"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间戳 单位秒"}}}, "gameExpireRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetId": {"type": "string", "format": "int64"}}}, "gameExpireResponse": {"type": "object", "properties": {"expireNum": {"type": "string", "format": "int64"}, "asset": {"$ref": "#/definitions/gameUserAsset"}}}, "gameLimitInfo": {"type": "object", "properties": {"reqNum": {"type": "string", "format": "int64", "title": "请求增加的数量"}, "reqLimitNum": {"type": "string", "format": "int64", "title": "限制的上限"}, "fixNum": {"type": "string", "format": "int64", "title": "实际增加的数量"}}}, "gameListRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产 ID 不填拉所有"}, "requireExpireInfo": {"type": "boolean", "title": "是否需要过期信息"}, "tag": {"type": "string", "title": "根据标签拉资产"}, "requireEmptyAsset": {"type": "boolean", "title": "是否需要数目为0的资产"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}}}, "gameListResponse": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameQueryAssetInfoRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产 ID 不填查所有"}}}, "gameQueryAssetInfoResponse": {"type": "object", "properties": {"infos": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameAssetInfo"}, "title": "资产 ID -> 资产配置"}}}, "gameQueryNumRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "array", "items": {"type": "string"}}, "assetId": {"type": "string", "format": "int64"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}}}, "gameQueryNumResponse": {"type": "object", "properties": {"assetNums": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "openId -> 数量"}}}, "gameRecordListRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产 ID 不填拉所有"}, "cursor": {"type": "string", "title": "透传字段 第一次传空"}, "pageSize": {"type": "string", "format": "int64", "title": "页大小 默认 20"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}}}, "gameRecordListResponse": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameAssetRecord"}}, "cursor": {"type": "string"}, "next": {"type": "boolean", "title": "是否有下一页"}}}, "gameSettleAllRequest": {"type": "object", "properties": {"appId": {"type": "string", "title": "资产存储appId"}, "openId": {"type": "string"}, "assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "toAppId": {"type": "string", "title": "结算结果 appId"}, "toAssetId": {"type": "string", "format": "int64", "title": "结算结果 assetId"}, "fRate": {"type": "number", "format": "float", "title": "切换比例"}, "transactionId": {"type": "string", "title": "唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式"}, "reason": {"type": "string", "title": "操作原因"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位秒"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}}}, "gameSettleAllResponse": {"type": "object", "properties": {"fromAssetNum": {"type": "string", "format": "int64"}, "toAssetNum": {"type": "string", "format": "int64"}}}, "gameSubRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAssetChange"}, "title": "扣减资产列表"}, "transactionId": {"type": "string", "title": "唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式"}, "reason": {"type": "string", "title": "操作原因"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位秒"}, "roundId": {"type": "string", "title": "游戏场次 id"}, "reportId": {"type": "string", "format": "int64", "title": "上报 id"}, "reportStr": {"type": "string", "title": "上报 str"}, "useCustomOpenId": {"type": "boolean", "title": "对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "通用透传字段"}}}, "gameSubResponse": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameUserAsset": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量(付费:来自K币购买 + 免费:来自兑换)"}, "expires": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExpireInfo"}, "title": "过期信息"}, "name": {"type": "string", "title": "资产名称"}, "icon": {"type": "string", "title": "资产图标"}, "assetNumFromFree": {"type": "string", "format": "int64", "title": "资产数量(免费:来自兑换)"}, "currencyType": {"type": "integer", "format": "int64", "title": "资产类型"}}}, "gameUserAssetChange": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量(免费+付费)"}, "freeNum": {"type": "string", "format": "int64", "title": "资产数量(免费),只读"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间 单位秒"}, "addOpetion": {"$ref": "#/definitions/gameAssetAddOption", "title": "只对增加资产有效,可选"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}