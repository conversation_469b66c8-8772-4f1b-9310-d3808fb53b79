// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/asset/asset.proto

package asset

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AssetSource int32

const (
	AssetSource_SourceDefault AssetSource = 0
	AssetSource_SourceRestore AssetSource = 1 // 补发
	AssetSource_SourceReturn  AssetSource = 2 // 返还
)

// Enum value maps for AssetSource.
var (
	AssetSource_name = map[int32]string{
		0: "SourceDefault",
		1: "SourceRestore",
		2: "SourceReturn",
	}
	AssetSource_value = map[string]int32{
		"SourceDefault": 0,
		"SourceRestore": 1,
		"SourceReturn":  2,
	}
)

func (x AssetSource) Enum() *AssetSource {
	p := new(AssetSource)
	*p = x
	return p
}

func (x AssetSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetSource) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_asset_asset_proto_enumTypes[0].Descriptor()
}

func (AssetSource) Type() protoreflect.EnumType {
	return &file_pb_asset_asset_proto_enumTypes[0]
}

func (x AssetSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetSource.Descriptor instead.
func (AssetSource) EnumDescriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{0}
}

// AssetTraceType 资产链路追踪
type AssetTraceType int32

const (
	AssetTraceType_AssetTraceTypeUnknown   AssetTraceType = 0 // 未知
	AssetTraceType_AssettraceTypeIDCReward AssetTraceType = 1 // 来自IDC礼包发放游戏资产
)

// Enum value maps for AssetTraceType.
var (
	AssetTraceType_name = map[int32]string{
		0: "AssetTraceTypeUnknown",
		1: "AssettraceTypeIDCReward",
	}
	AssetTraceType_value = map[string]int32{
		"AssetTraceTypeUnknown":   0,
		"AssettraceTypeIDCReward": 1,
	}
)

func (x AssetTraceType) Enum() *AssetTraceType {
	p := new(AssetTraceType)
	*p = x
	return p
}

func (x AssetTraceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetTraceType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_asset_asset_proto_enumTypes[1].Descriptor()
}

func (AssetTraceType) Type() protoreflect.EnumType {
	return &file_pb_asset_asset_proto_enumTypes[1]
}

func (x AssetTraceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetTraceType.Descriptor instead.
func (AssetTraceType) EnumDescriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{1}
}

type AssetFromType int32

const (
	AssetFromType_AssetFromDefault AssetFromType = 0 // 默认, 会被重置为来自免费兑换
	AssetFromType_AssetFromFree    AssetFromType = 1 // 来自免费兑换
	AssetFromType_AssetFromPay     AssetFromType = 2 // 来自K币购买
)

// Enum value maps for AssetFromType.
var (
	AssetFromType_name = map[int32]string{
		0: "AssetFromDefault",
		1: "AssetFromFree",
		2: "AssetFromPay",
	}
	AssetFromType_value = map[string]int32{
		"AssetFromDefault": 0,
		"AssetFromFree":    1,
		"AssetFromPay":     2,
	}
)

func (x AssetFromType) Enum() *AssetFromType {
	p := new(AssetFromType)
	*p = x
	return p
}

func (x AssetFromType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetFromType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_asset_asset_proto_enumTypes[2].Descriptor()
}

func (AssetFromType) Type() protoreflect.EnumType {
	return &file_pb_asset_asset_proto_enumTypes[2]
}

func (x AssetFromType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetFromType.Descriptor instead.
func (AssetFromType) EnumDescriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{2}
}

type AssetInfo_Type int32

const (
	AssetInfo_AssetInvalid          AssetInfo_Type = 0
	AssetInfo_AssetGameCurrency     AssetInfo_Type = 1
	AssetInfo_AssetExchangeCurrency AssetInfo_Type = 2
)

// Enum value maps for AssetInfo_Type.
var (
	AssetInfo_Type_name = map[int32]string{
		0: "AssetInvalid",
		1: "AssetGameCurrency",
		2: "AssetExchangeCurrency",
	}
	AssetInfo_Type_value = map[string]int32{
		"AssetInvalid":          0,
		"AssetGameCurrency":     1,
		"AssetExchangeCurrency": 2,
	}
)

func (x AssetInfo_Type) Enum() *AssetInfo_Type {
	p := new(AssetInfo_Type)
	*p = x
	return p
}

func (x AssetInfo_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AssetInfo_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_asset_asset_proto_enumTypes[3].Descriptor()
}

func (AssetInfo_Type) Type() protoreflect.EnumType {
	return &file_pb_asset_asset_proto_enumTypes[3]
}

func (x AssetInfo_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AssetInfo_Type.Descriptor instead.
func (AssetInfo_Type) EnumDescriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{26, 0}
}

type BatchListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items  []*BatchListReq_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Option *BatchListReq_Option `protobuf:"bytes,2,opt,name=option,proto3" json:"option,omitempty"`
}

func (x *BatchListReq) Reset() {
	*x = BatchListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListReq) ProtoMessage() {}

func (x *BatchListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListReq.ProtoReflect.Descriptor instead.
func (*BatchListReq) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{0}
}

func (x *BatchListReq) GetItems() []*BatchListReq_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BatchListReq) GetOption() *BatchListReq_Option {
	if x != nil {
		return x.Option
	}
	return nil
}

type BatchListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items        map[string]*BatchListRsp_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MpUniqAppIds map[string]string             `protobuf:"bytes,2,rep,name=mpUniqAppIds,proto3" json:"mpUniqAppIds,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // key: sourceAppId, value: uniqAppId, 先检查是否有uniqAppId, 再从item取结果
}

func (x *BatchListRsp) Reset() {
	*x = BatchListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListRsp) ProtoMessage() {}

func (x *BatchListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListRsp.ProtoReflect.Descriptor instead.
func (*BatchListRsp) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{1}
}

func (x *BatchListRsp) GetItems() map[string]*BatchListRsp_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BatchListRsp) GetMpUniqAppIds() map[string]string {
	if x != nil {
		return x.MpUniqAppIds
	}
	return nil
}

type ExpireInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNum   int64 `protobuf:"varint,1,opt,name=assetNum,proto3" json:"assetNum,omitempty"`     // 资产数量
	ExpireTime int64 `protobuf:"varint,2,opt,name=expireTime,proto3" json:"expireTime,omitempty"` // 过期时间戳 单位秒
}

func (x *ExpireInfo) Reset() {
	*x = ExpireInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireInfo) ProtoMessage() {}

func (x *ExpireInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireInfo.ProtoReflect.Descriptor instead.
func (*ExpireInfo) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{2}
}

func (x *ExpireInfo) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *ExpireInfo) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

type UserAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId          int64         `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`                   // 资产 id
	AssetNum         int64         `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"`                 // 资产数量(付费:来自K币购买 + 免费:来自兑换)
	Expires          []*ExpireInfo `protobuf:"bytes,3,rep,name=expires,proto3" json:"expires,omitempty"`                    // 过期信息
	Name             string        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                          // 资产名称
	Icon             string        `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`                          // 资产图标
	AssetNumFromFree int64         `protobuf:"varint,6,opt,name=assetNumFromFree,proto3" json:"assetNumFromFree,omitempty"` // 资产数量(免费:来自兑换)
	CurrencyType     uint32        `protobuf:"varint,7,opt,name=currencyType,proto3" json:"currencyType,omitempty"`         // 资产类型
}

func (x *UserAsset) Reset() {
	*x = UserAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAsset) ProtoMessage() {}

func (x *UserAsset) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAsset.ProtoReflect.Descriptor instead.
func (*UserAsset) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{3}
}

func (x *UserAsset) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *UserAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *UserAsset) GetExpires() []*ExpireInfo {
	if x != nil {
		return x.Expires
	}
	return nil
}

func (x *UserAsset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserAsset) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *UserAsset) GetAssetNumFromFree() int64 {
	if x != nil {
		return x.AssetNumFromFree
	}
	return 0
}

func (x *UserAsset) GetCurrencyType() uint32 {
	if x != nil {
		return x.CurrencyType
	}
	return 0
}

type AssetAddOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limit int64 `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"` // 增加上限, 不指定着不限制, 否则最多只能加到limit封顶
}

func (x *AssetAddOption) Reset() {
	*x = AssetAddOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetAddOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetAddOption) ProtoMessage() {}

func (x *AssetAddOption) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetAddOption.ProtoReflect.Descriptor instead.
func (*AssetAddOption) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{4}
}

func (x *AssetAddOption) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type UserAssetChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId    int64           `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`       // 资产 id
	AssetNum   int64           `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"`     // 资产数量(免费+付费)
	FreeNum    int64           `protobuf:"varint,3,opt,name=freeNum,proto3" json:"freeNum,omitempty"`       // 资产数量(免费),只读
	ExpireTime int64           `protobuf:"varint,4,opt,name=expireTime,proto3" json:"expireTime,omitempty"` // 过期时间 单位秒
	AddOpetion *AssetAddOption `protobuf:"bytes,5,opt,name=addOpetion,proto3" json:"addOpetion,omitempty"`  // 只对增加资产有效,可选
}

func (x *UserAssetChange) Reset() {
	*x = UserAssetChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAssetChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAssetChange) ProtoMessage() {}

func (x *UserAssetChange) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAssetChange.ProtoReflect.Descriptor instead.
func (*UserAssetChange) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{5}
}

func (x *UserAssetChange) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *UserAssetChange) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *UserAssetChange) GetFreeNum() int64 {
	if x != nil {
		return x.FreeNum
	}
	return 0
}

func (x *UserAssetChange) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *UserAssetChange) GetAddOpetion() *AssetAddOption {
	if x != nil {
		return x.AddOpetion
	}
	return nil
}

type AddRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string             `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          string             `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Assets          []*UserAssetChange `protobuf:"bytes,3,rep,name=assets,proto3" json:"assets,omitempty"`                                                                                          // 增加资产列表
	TransactionId   string             `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"`                                                                            // 唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式
	Reason          string             `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`                                                                                          // 操作原因
	Timestamp       int64              `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                                                   // 操作时间戳 单位秒
	Restore         bool               `protobuf:"varint,7,opt,name=restore,proto3" json:"restore,omitempty"`                                                                                       // 是否补发请求
	RoundId         string             `protobuf:"bytes,8,opt,name=roundId,proto3" json:"roundId,omitempty"`                                                                                        // 游戏场次 id
	Source          AssetSource        `protobuf:"varint,9,opt,name=source,proto3,enum=component.game.AssetSource" json:"source,omitempty"`                                                         // 来源
	From            AssetFromType      `protobuf:"varint,10,opt,name=from,proto3,enum=component.game.AssetFromType" json:"from,omitempty"`                                                          // 来自付费购买 还是 免费兑换
	ReportId        int64              `protobuf:"varint,11,opt,name=reportId,proto3" json:"reportId,omitempty"`                                                                                    // 上报 id
	ReportStr       string             `protobuf:"bytes,12,opt,name=reportStr,proto3" json:"reportStr,omitempty"`                                                                                   // 上报 str
	Trace           AssetTraceType     `protobuf:"varint,13,opt,name=trace,proto3,enum=component.game.AssetTraceType" json:"trace,omitempty"`                                                       // 业务调用标记,由链路调用起点开始标记,请在pb中定义,防止不同来源出现混淆
	UseCustomOpenId bool               `protobuf:"varint,14,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"`                                                                      // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
	MapExt          map[string]string  `protobuf:"bytes,15,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 通用透传字段
}

func (x *AddRequest) Reset() {
	*x = AddRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRequest) ProtoMessage() {}

func (x *AddRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRequest.ProtoReflect.Descriptor instead.
func (*AddRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{6}
}

func (x *AddRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AddRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AddRequest) GetAssets() []*UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *AddRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *AddRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AddRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AddRequest) GetRestore() bool {
	if x != nil {
		return x.Restore
	}
	return false
}

func (x *AddRequest) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *AddRequest) GetSource() AssetSource {
	if x != nil {
		return x.Source
	}
	return AssetSource_SourceDefault
}

func (x *AddRequest) GetFrom() AssetFromType {
	if x != nil {
		return x.From
	}
	return AssetFromType_AssetFromDefault
}

func (x *AddRequest) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *AddRequest) GetReportStr() string {
	if x != nil {
		return x.ReportStr
	}
	return ""
}

func (x *AddRequest) GetTrace() AssetTraceType {
	if x != nil {
		return x.Trace
	}
	return AssetTraceType_AssetTraceTypeUnknown
}

func (x *AddRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

func (x *AddRequest) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type LimitInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqNum      int64 `protobuf:"varint,1,opt,name=reqNum,proto3" json:"reqNum,omitempty"`           // 请求增加的数量
	ReqLimitNum int64 `protobuf:"varint,2,opt,name=reqLimitNum,proto3" json:"reqLimitNum,omitempty"` // 限制的上限
	FixNum      int64 `protobuf:"varint,3,opt,name=fixNum,proto3" json:"fixNum,omitempty"`           // 实际增加的数量
}

func (x *LimitInfo) Reset() {
	*x = LimitInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitInfo) ProtoMessage() {}

func (x *LimitInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitInfo.ProtoReflect.Descriptor instead.
func (*LimitInfo) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{7}
}

func (x *LimitInfo) GetReqNum() int64 {
	if x != nil {
		return x.ReqNum
	}
	return 0
}

func (x *LimitInfo) GetReqLimitNum() int64 {
	if x != nil {
		return x.ReqLimitNum
	}
	return 0
}

func (x *LimitInfo) GetFixNum() int64 {
	if x != nil {
		return x.FixNum
	}
	return 0
}

type AddResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UserAsset         `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`                                                                                          // 结果资产列表
	Limits map[int64]*LimitInfo `protobuf:"bytes,2,rep,name=limits,proto3" json:"limits,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 限制信息(asset_id -> LimitInfo)
}

func (x *AddResponse) Reset() {
	*x = AddResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddResponse) ProtoMessage() {}

func (x *AddResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddResponse.ProtoReflect.Descriptor instead.
func (*AddResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{8}
}

func (x *AddResponse) GetAssets() []*UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *AddResponse) GetLimits() map[int64]*LimitInfo {
	if x != nil {
		return x.Limits
	}
	return nil
}

type SubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string             `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          string             `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Assets          []*UserAssetChange `protobuf:"bytes,3,rep,name=assets,proto3" json:"assets,omitempty"`                                                                                          // 扣减资产列表
	TransactionId   string             `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"`                                                                            // 唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式
	Reason          string             `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`                                                                                          // 操作原因
	Timestamp       int64              `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                                                   // 操作时间戳 单位秒
	RoundId         string             `protobuf:"bytes,7,opt,name=roundId,proto3" json:"roundId,omitempty"`                                                                                        // 游戏场次 id
	ReportId        int64              `protobuf:"varint,8,opt,name=reportId,proto3" json:"reportId,omitempty"`                                                                                     // 上报 id
	ReportStr       string             `protobuf:"bytes,9,opt,name=reportStr,proto3" json:"reportStr,omitempty"`                                                                                    // 上报 str
	UseCustomOpenId bool               `protobuf:"varint,10,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"`                                                                      // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
	MapExt          map[string]string  `protobuf:"bytes,11,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 通用透传字段
}

func (x *SubRequest) Reset() {
	*x = SubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubRequest) ProtoMessage() {}

func (x *SubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubRequest.ProtoReflect.Descriptor instead.
func (*SubRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{9}
}

func (x *SubRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SubRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SubRequest) GetAssets() []*UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *SubRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *SubRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *SubRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SubRequest) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *SubRequest) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *SubRequest) GetReportStr() string {
	if x != nil {
		return x.ReportStr
	}
	return ""
}

func (x *SubRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

func (x *SubRequest) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type SubResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UserAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 结果资产列表
}

func (x *SubResponse) Reset() {
	*x = SubResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubResponse) ProtoMessage() {}

func (x *SubResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubResponse.ProtoReflect.Descriptor instead.
func (*SubResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{10}
}

func (x *SubResponse) GetAssets() []*UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type ExchangeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          string            `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	FromAsset       *UserAssetChange  `protobuf:"bytes,3,opt,name=fromAsset,proto3" json:"fromAsset,omitempty"`
	ToAsset         *UserAssetChange  `protobuf:"bytes,4,opt,name=toAsset,proto3" json:"toAsset,omitempty"`
	TransactionId   string            `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`                                                                            // 唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式
	Reason          string            `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                                                                                          // 操作原因
	Timestamp       int64             `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                                                   // 操作时间戳 单位秒
	RoundId         string            `protobuf:"bytes,8,opt,name=roundId,proto3" json:"roundId,omitempty"`                                                                                        // 游戏场次 id
	UseCustomOpenId bool              `protobuf:"varint,9,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"`                                                                       // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
	MapExt          map[string]string `protobuf:"bytes,10,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 通用透传字段
}

func (x *ExchangeRequest) Reset() {
	*x = ExchangeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRequest) ProtoMessage() {}

func (x *ExchangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRequest.ProtoReflect.Descriptor instead.
func (*ExchangeRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{11}
}

func (x *ExchangeRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ExchangeRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ExchangeRequest) GetFromAsset() *UserAssetChange {
	if x != nil {
		return x.FromAsset
	}
	return nil
}

func (x *ExchangeRequest) GetToAsset() *UserAssetChange {
	if x != nil {
		return x.ToAsset
	}
	return nil
}

func (x *ExchangeRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ExchangeRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ExchangeRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ExchangeRequest) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *ExchangeRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

func (x *ExchangeRequest) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type ExchangeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UserAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 结果资产列表
}

func (x *ExchangeResponse) Reset() {
	*x = ExchangeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeResponse) ProtoMessage() {}

func (x *ExchangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeResponse.ProtoReflect.Descriptor instead.
func (*ExchangeResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{12}
}

func (x *ExchangeResponse) GetAssets() []*UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type ListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId             string  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId            string  `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetIds          []int64 `protobuf:"varint,3,rep,packed,name=assetIds,proto3" json:"assetIds,omitempty"`            // 资产 ID 不填拉所有
	RequireExpireInfo bool    `protobuf:"varint,4,opt,name=requireExpireInfo,proto3" json:"requireExpireInfo,omitempty"` // 是否需要过期信息
	Tag               string  `protobuf:"bytes,5,opt,name=tag,proto3" json:"tag,omitempty"`                              // 根据标签拉资产
	RequireEmptyAsset bool    `protobuf:"varint,6,opt,name=requireEmptyAsset,proto3" json:"requireEmptyAsset,omitempty"` // 是否需要数目为0的资产
	UseCustomOpenId   bool    `protobuf:"varint,7,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"`     // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{13}
}

func (x *ListRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ListRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ListRequest) GetAssetIds() []int64 {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *ListRequest) GetRequireExpireInfo() bool {
	if x != nil {
		return x.RequireExpireInfo
	}
	return false
}

func (x *ListRequest) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ListRequest) GetRequireEmptyAsset() bool {
	if x != nil {
		return x.RequireEmptyAsset
	}
	return false
}

func (x *ListRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

type ListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UserAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 结果资产列表
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{14}
}

func (x *ListResponse) GetAssets() []*UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

type QueryNumRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          []string `protobuf:"bytes,2,rep,name=openId,proto3" json:"openId,omitempty"`
	AssetId         int64    `protobuf:"varint,3,opt,name=assetId,proto3" json:"assetId,omitempty"`
	UseCustomOpenId bool     `protobuf:"varint,4,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"` // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
}

func (x *QueryNumRequest) Reset() {
	*x = QueryNumRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryNumRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryNumRequest) ProtoMessage() {}

func (x *QueryNumRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryNumRequest.ProtoReflect.Descriptor instead.
func (*QueryNumRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{15}
}

func (x *QueryNumRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryNumRequest) GetOpenId() []string {
	if x != nil {
		return x.OpenId
	}
	return nil
}

func (x *QueryNumRequest) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *QueryNumRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

type QueryNumResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNums map[string]int64 `protobuf:"bytes,1,rep,name=assetNums,proto3" json:"assetNums,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // openId -> 数量
}

func (x *QueryNumResponse) Reset() {
	*x = QueryNumResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryNumResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryNumResponse) ProtoMessage() {}

func (x *QueryNumResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryNumResponse.ProtoReflect.Descriptor instead.
func (*QueryNumResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{16}
}

func (x *QueryNumResponse) GetAssetNums() map[string]int64 {
	if x != nil {
		return x.AssetNums
	}
	return nil
}

type ExpireRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId  string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetId int64  `protobuf:"varint,3,opt,name=assetId,proto3" json:"assetId,omitempty"`
}

func (x *ExpireRequest) Reset() {
	*x = ExpireRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireRequest) ProtoMessage() {}

func (x *ExpireRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireRequest.ProtoReflect.Descriptor instead.
func (*ExpireRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{17}
}

func (x *ExpireRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ExpireRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ExpireRequest) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

type ExpireResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpireNum int64      `protobuf:"varint,1,opt,name=expireNum,proto3" json:"expireNum,omitempty"`
	Asset     *UserAsset `protobuf:"bytes,2,opt,name=asset,proto3" json:"asset,omitempty"`
}

func (x *ExpireResponse) Reset() {
	*x = ExpireResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireResponse) ProtoMessage() {}

func (x *ExpireResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireResponse.ProtoReflect.Descriptor instead.
func (*ExpireResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{18}
}

func (x *ExpireResponse) GetExpireNum() int64 {
	if x != nil {
		return x.ExpireNum
	}
	return 0
}

func (x *ExpireResponse) GetAsset() *UserAsset {
	if x != nil {
		return x.Asset
	}
	return nil
}

type AssetRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId   int64  `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`
	AssetNum  int64  `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"`
	Action    string `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	Timestamp int64  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Reason    string `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"` // 透传reason，业务可自行使用
}

func (x *AssetRecord) Reset() {
	*x = AssetRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetRecord) ProtoMessage() {}

func (x *AssetRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetRecord.ProtoReflect.Descriptor instead.
func (*AssetRecord) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{19}
}

func (x *AssetRecord) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *AssetRecord) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AssetRecord) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *AssetRecord) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AssetRecord) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type RecordListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId          string  `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetIds        []int64 `protobuf:"varint,3,rep,packed,name=assetIds,proto3" json:"assetIds,omitempty"`        // 资产 ID 不填拉所有
	Cursor          string  `protobuf:"bytes,4,opt,name=cursor,proto3" json:"cursor,omitempty"`                    // 透传字段 第一次传空
	PageSize        int64   `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`               // 页大小 默认 20
	UseCustomOpenId bool    `protobuf:"varint,6,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"` // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
}

func (x *RecordListRequest) Reset() {
	*x = RecordListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordListRequest) ProtoMessage() {}

func (x *RecordListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordListRequest.ProtoReflect.Descriptor instead.
func (*RecordListRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{20}
}

func (x *RecordListRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RecordListRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RecordListRequest) GetAssetIds() []int64 {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *RecordListRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *RecordListRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RecordListRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

type RecordListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*AssetRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	Cursor  string         `protobuf:"bytes,2,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Next    bool           `protobuf:"varint,3,opt,name=next,proto3" json:"next,omitempty"` // 是否有下一页
}

func (x *RecordListResponse) Reset() {
	*x = RecordListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordListResponse) ProtoMessage() {}

func (x *RecordListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordListResponse.ProtoReflect.Descriptor instead.
func (*RecordListResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{21}
}

func (x *RecordListResponse) GetRecords() []*AssetRecord {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *RecordListResponse) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *RecordListResponse) GetNext() bool {
	if x != nil {
		return x.Next
	}
	return false
}

type QueryAssetInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	AssetIds []int64 `protobuf:"varint,2,rep,packed,name=assetIds,proto3" json:"assetIds,omitempty"` // 资产 ID 不填查所有
}

func (x *QueryAssetInfoRequest) Reset() {
	*x = QueryAssetInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAssetInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAssetInfoRequest) ProtoMessage() {}

func (x *QueryAssetInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAssetInfoRequest.ProtoReflect.Descriptor instead.
func (*QueryAssetInfoRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{22}
}

func (x *QueryAssetInfoRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryAssetInfoRequest) GetAssetIds() []int64 {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

type QueryAssetInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos map[int64]*AssetInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 资产 ID -> 资产配置
}

func (x *QueryAssetInfoResponse) Reset() {
	*x = QueryAssetInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAssetInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAssetInfoResponse) ProtoMessage() {}

func (x *QueryAssetInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAssetInfoResponse.ProtoReflect.Descriptor instead.
func (*QueryAssetInfoResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{23}
}

func (x *QueryAssetInfoResponse) GetInfos() map[int64]*AssetInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

type CancelSubRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	TransactionId string `protobuf:"bytes,2,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	OpenId        string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *CancelSubRequest) Reset() {
	*x = CancelSubRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubRequest) ProtoMessage() {}

func (x *CancelSubRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubRequest.ProtoReflect.Descriptor instead.
func (*CancelSubRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{24}
}

func (x *CancelSubRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CancelSubRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *CancelSubRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type CancelSubResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelSubResponse) Reset() {
	*x = CancelSubResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubResponse) ProtoMessage() {}

func (x *CancelSubResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubResponse.ProtoReflect.Descriptor instead.
func (*CancelSubResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{25}
}

// 资产信息
type AssetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`    // 资产名称
	Icon  string         `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`    // 资产图标
	Tag   string         `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`      // 资产标签
	Price int64          `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"` // 单价 相对于平台币的价格 精度为 0.001
	Type  AssetInfo_Type `protobuf:"varint,5,opt,name=type,proto3,enum=component.game.AssetInfo_Type" json:"type,omitempty"`
}

func (x *AssetInfo) Reset() {
	*x = AssetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetInfo) ProtoMessage() {}

func (x *AssetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetInfo.ProtoReflect.Descriptor instead.
func (*AssetInfo) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{26}
}

func (x *AssetInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AssetInfo) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *AssetInfo) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *AssetInfo) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *AssetInfo) GetType() AssetInfo_Type {
	if x != nil {
		return x.Type
	}
	return AssetInfo_AssetInvalid
}

// 资产详情过期信息
type AssetDetailExpire struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNum   int64         `protobuf:"varint,1,opt,name=assetNum,proto3" json:"assetNum,omitempty"`
	ExpireTime int64         `protobuf:"varint,2,opt,name=expireTime,proto3" json:"expireTime,omitempty"`
	From       AssetFromType `protobuf:"varint,3,opt,name=from,proto3,enum=component.game.AssetFromType" json:"from,omitempty"`
}

func (x *AssetDetailExpire) Reset() {
	*x = AssetDetailExpire{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetDetailExpire) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetDetailExpire) ProtoMessage() {}

func (x *AssetDetailExpire) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetDetailExpire.ProtoReflect.Descriptor instead.
func (*AssetDetailExpire) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{27}
}

func (x *AssetDetailExpire) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AssetDetailExpire) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *AssetDetailExpire) GetFrom() AssetFromType {
	if x != nil {
		return x.From
	}
	return AssetFromType_AssetFromDefault
}

// 资产详情
type AssetDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expires   []*AssetDetailExpire `protobuf:"bytes,1,rep,name=expires,proto3" json:"expires,omitempty"`
	Permanent int64                `protobuf:"varint,2,opt,name=permanent,proto3" json:"permanent,omitempty"` // 免费(资产来自兑换) + 付费(资产来自K币购买)
	Free      int64                `protobuf:"varint,3,opt,name=free,proto3" json:"free,omitempty"`           // 免费(资产来自兑换)
}

func (x *AssetDetail) Reset() {
	*x = AssetDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetDetail) ProtoMessage() {}

func (x *AssetDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetDetail.ProtoReflect.Descriptor instead.
func (*AssetDetail) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{28}
}

func (x *AssetDetail) GetExpires() []*AssetDetailExpire {
	if x != nil {
		return x.Expires
	}
	return nil
}

func (x *AssetDetail) GetPermanent() int64 {
	if x != nil {
		return x.Permanent
	}
	return 0
}

func (x *AssetDetail) GetFree() int64 {
	if x != nil {
		return x.Free
	}
	return 0
}

// 资产变更记录
type UserAssetChangeLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId          string               `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	AppId           string               `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Assets          []*UserAssetChange   `protobuf:"bytes,3,rep,name=assets,proto3" json:"assets,omitempty"`
	TransactionId   string               `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Reason          string               `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	Action          string               `protobuf:"bytes,6,opt,name=action,proto3" json:"action,omitempty"`
	Timestamp       int64                `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	RoundId         string               `protobuf:"bytes,8,opt,name=roundId,proto3" json:"roundId,omitempty"`                                                                                      // 场次 id
	Infos           map[int64]*AssetInfo `protobuf:"bytes,9,rep,name=infos,proto3" json:"infos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 资产 id -> 资产配置信息
	OriginalAppId   string               `protobuf:"bytes,10,opt,name=originalAppId,proto3" json:"originalAppId,omitempty"`                                                                         // 原始appid
	ReportId        int64                `protobuf:"varint,11,opt,name=reportId,proto3" json:"reportId,omitempty"`
	ReportStr       string               `protobuf:"bytes,12,opt,name=reportStr,proto3" json:"reportStr,omitempty"`                                                                                        // 上报 str
	From            AssetFromType        `protobuf:"varint,13,opt,name=from,proto3,enum=component.game.AssetFromType" json:"from,omitempty"`                                                               // 对于add操作, 如果是来自pay,则isFromPay=true
	AddLimit        map[int64]*LimitInfo `protobuf:"bytes,14,rep,name=addLimit,proto3" json:"addLimit,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 增加的上限信息
	TraceType       AssetTraceType       `protobuf:"varint,15,opt,name=traceType,proto3,enum=component.game.AssetTraceType" json:"traceType,omitempty"`                                                    // 调用方标记
	UseCustomOpenId bool                 `protobuf:"varint,16,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"`                                                                           // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
	Balance         map[int64]int64      `protobuf:"bytes,17,rep,name=balance,proto3" json:"balance,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *UserAssetChangeLog) Reset() {
	*x = UserAssetChangeLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAssetChangeLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAssetChangeLog) ProtoMessage() {}

func (x *UserAssetChangeLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAssetChangeLog.ProtoReflect.Descriptor instead.
func (*UserAssetChangeLog) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{29}
}

func (x *UserAssetChangeLog) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *UserAssetChangeLog) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *UserAssetChangeLog) GetAssets() []*UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *UserAssetChangeLog) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *UserAssetChangeLog) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *UserAssetChangeLog) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *UserAssetChangeLog) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *UserAssetChangeLog) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *UserAssetChangeLog) GetInfos() map[int64]*AssetInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

func (x *UserAssetChangeLog) GetOriginalAppId() string {
	if x != nil {
		return x.OriginalAppId
	}
	return ""
}

func (x *UserAssetChangeLog) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *UserAssetChangeLog) GetReportStr() string {
	if x != nil {
		return x.ReportStr
	}
	return ""
}

func (x *UserAssetChangeLog) GetFrom() AssetFromType {
	if x != nil {
		return x.From
	}
	return AssetFromType_AssetFromDefault
}

func (x *UserAssetChangeLog) GetAddLimit() map[int64]*LimitInfo {
	if x != nil {
		return x.AddLimit
	}
	return nil
}

func (x *UserAssetChangeLog) GetTraceType() AssetTraceType {
	if x != nil {
		return x.TraceType
	}
	return AssetTraceType_AssetTraceTypeUnknown
}

func (x *UserAssetChangeLog) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

func (x *UserAssetChangeLog) GetBalance() map[int64]int64 {
	if x != nil {
		return x.Balance
	}
	return nil
}

// 事务详情
type TransactionDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action    string                 `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Assets    []*UserAssetChange     `protobuf:"bytes,2,rep,name=assets,proto3" json:"assets,omitempty"`        // 增加资产列表
	Reason    string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`        // 操作原因
	Timestamp int64                  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 操作时间戳 单位秒
	Deducted  map[int64]*AssetDetail `protobuf:"bytes,5,rep,name=deducted,proto3" json:"deducted,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TransactionDetail) Reset() {
	*x = TransactionDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetail) ProtoMessage() {}

func (x *TransactionDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetail.ProtoReflect.Descriptor instead.
func (*TransactionDetail) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{30}
}

func (x *TransactionDetail) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *TransactionDetail) GetAssets() []*UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *TransactionDetail) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TransactionDetail) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *TransactionDetail) GetDeducted() map[int64]*AssetDetail {
	if x != nil {
		return x.Deducted
	}
	return nil
}

// 游标
type RecordListCursor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp      int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Id             int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	IdShare        int64 `protobuf:"varint,3,opt,name=idShare,proto3" json:"idShare,omitempty"`
	TimestampShare int64 `protobuf:"varint,4,opt,name=timestampShare,proto3" json:"timestampShare,omitempty"`
}

func (x *RecordListCursor) Reset() {
	*x = RecordListCursor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordListCursor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordListCursor) ProtoMessage() {}

func (x *RecordListCursor) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordListCursor.ProtoReflect.Descriptor instead.
func (*RecordListCursor) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{31}
}

func (x *RecordListCursor) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RecordListCursor) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RecordListCursor) GetIdShare() int64 {
	if x != nil {
		return x.IdShare
	}
	return 0
}

func (x *RecordListCursor) GetTimestampShare() int64 {
	if x != nil {
		return x.TimestampShare
	}
	return 0
}

type SettleAllRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId           string  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"` // 资产存储appId
	OpenId          string  `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetId         int64   `protobuf:"varint,3,opt,name=assetId,proto3" json:"assetId,omitempty"`                 // 资产 id
	ToAppId         string  `protobuf:"bytes,4,opt,name=toAppId,proto3" json:"toAppId,omitempty"`                  // 结算结果 appId
	ToAssetId       int64   `protobuf:"varint,5,opt,name=toAssetId,proto3" json:"toAssetId,omitempty"`             // 结算结果 assetId
	FRate           float32 `protobuf:"fixed32,7,opt,name=fRate,proto3" json:"fRate,omitempty"`                    // 切换比例
	TransactionId   string  `protobuf:"bytes,8,opt,name=transactionId,proto3" json:"transactionId,omitempty"`      // 唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX,如果是共享类型账户(以uid方式存储),则必须使用20211201_appid_assetid-XXXXXXX的格式
	Reason          string  `protobuf:"bytes,9,opt,name=reason,proto3" json:"reason,omitempty"`                    // 操作原因
	Timestamp       int64   `protobuf:"varint,10,opt,name=timestamp,proto3" json:"timestamp,omitempty"`            // 操作时间戳 单位秒
	UseCustomOpenId bool    `protobuf:"varint,6,opt,name=useCustomOpenId,proto3" json:"useCustomOpenId,omitempty"` // 对于以uid方式存储的账户,如果useCustomOpenId=true,则openId填用户uid,否则会自动将openid转为平台uid, 对于非uid方式存储账户,此设置无效
}

func (x *SettleAllRequest) Reset() {
	*x = SettleAllRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleAllRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleAllRequest) ProtoMessage() {}

func (x *SettleAllRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleAllRequest.ProtoReflect.Descriptor instead.
func (*SettleAllRequest) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{32}
}

func (x *SettleAllRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SettleAllRequest) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SettleAllRequest) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *SettleAllRequest) GetToAppId() string {
	if x != nil {
		return x.ToAppId
	}
	return ""
}

func (x *SettleAllRequest) GetToAssetId() int64 {
	if x != nil {
		return x.ToAssetId
	}
	return 0
}

func (x *SettleAllRequest) GetFRate() float32 {
	if x != nil {
		return x.FRate
	}
	return 0
}

func (x *SettleAllRequest) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *SettleAllRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *SettleAllRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SettleAllRequest) GetUseCustomOpenId() bool {
	if x != nil {
		return x.UseCustomOpenId
	}
	return false
}

type SettleAllResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromAssetNum int64 `protobuf:"varint,1,opt,name=fromAssetNum,proto3" json:"fromAssetNum,omitempty"`
	ToAssetNum   int64 `protobuf:"varint,2,opt,name=toAssetNum,proto3" json:"toAssetNum,omitempty"`
}

func (x *SettleAllResponse) Reset() {
	*x = SettleAllResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleAllResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleAllResponse) ProtoMessage() {}

func (x *SettleAllResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleAllResponse.ProtoReflect.Descriptor instead.
func (*SettleAllResponse) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{33}
}

func (x *SettleAllResponse) GetFromAssetNum() int64 {
	if x != nil {
		return x.FromAssetNum
	}
	return 0
}

func (x *SettleAllResponse) GetToAssetNum() int64 {
	if x != nil {
		return x.ToAssetNum
	}
	return 0
}

type BatchListReq_Option struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否使用平台Uid,当UsePlatUid=true时,openId传平台uid字符串即可,会自动转化为中台openId
	UsePlatUid bool `protobuf:"varint,1,opt,name=UsePlatUid,proto3" json:"UsePlatUid,omitempty"`
	// 为true时从ckv读取缓存,否则读DB,当指定读缓存时,不会返回资产过期信息切不支持根据标签拉取资产
	// 多读场景考虑设置UseCache=true
	UseCache bool `protobuf:"varint,2,opt,name=UseCache,proto3" json:"UseCache,omitempty"`
}

func (x *BatchListReq_Option) Reset() {
	*x = BatchListReq_Option{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListReq_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListReq_Option) ProtoMessage() {}

func (x *BatchListReq_Option) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListReq_Option.ProtoReflect.Descriptor instead.
func (*BatchListReq_Option) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BatchListReq_Option) GetUsePlatUid() bool {
	if x != nil {
		return x.UsePlatUid
	}
	return false
}

func (x *BatchListReq_Option) GetUseCache() bool {
	if x != nil {
		return x.UseCache
	}
	return false
}

type BatchListReq_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId             string  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId            string  `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetIds          []int64 `protobuf:"varint,3,rep,packed,name=assetIds,proto3" json:"assetIds,omitempty"`            // 资产 ID 不填拉所有
	RequireExpireInfo bool    `protobuf:"varint,4,opt,name=requireExpireInfo,proto3" json:"requireExpireInfo,omitempty"` // 是否需要过期信息
	Tag               string  `protobuf:"bytes,5,opt,name=tag,proto3" json:"tag,omitempty"`                              // 根据标签拉资产
	RequireEmptyAsset bool    `protobuf:"varint,6,opt,name=requireEmptyAsset,proto3" json:"requireEmptyAsset,omitempty"` // 是否需要数目为0的资产
}

func (x *BatchListReq_Item) Reset() {
	*x = BatchListReq_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListReq_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListReq_Item) ProtoMessage() {}

func (x *BatchListReq_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListReq_Item.ProtoReflect.Descriptor instead.
func (*BatchListReq_Item) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{0, 1}
}

func (x *BatchListReq_Item) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchListReq_Item) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchListReq_Item) GetAssetIds() []int64 {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *BatchListReq_Item) GetRequireExpireInfo() bool {
	if x != nil {
		return x.RequireExpireInfo
	}
	return false
}

func (x *BatchListReq_Item) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *BatchListReq_Item) GetRequireEmptyAsset() bool {
	if x != nil {
		return x.RequireEmptyAsset
	}
	return false
}

type BatchListRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UserAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"` // 结果资产列表
}

func (x *BatchListRsp_Item) Reset() {
	*x = BatchListRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asset_asset_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchListRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchListRsp_Item) ProtoMessage() {}

func (x *BatchListRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asset_asset_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchListRsp_Item.ProtoReflect.Descriptor instead.
func (*BatchListRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_asset_asset_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BatchListRsp_Item) GetAssets() []*UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

var File_pb_asset_asset_proto protoreflect.FileDescriptor

var file_pb_asset_asset_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x8b, 0x03, 0x0a, 0x0c, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x3b, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x44, 0x0a,
	0x06, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x50, 0x6c,
	0x61, 0x74, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x55, 0x73, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x55, 0x73, 0x65, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x1a, 0xbe, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x22, 0xfa, 0x02, 0x0a, 0x0c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x52, 0x0a, 0x0c, 0x6d, 0x70, 0x55, 0x6e, 0x69, 0x71, 0x41, 0x70,
	0x70, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x70, 0x55, 0x6e, 0x69, 0x71, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6d, 0x70, 0x55, 0x6e,
	0x69, 0x71, 0x41, 0x70, 0x70, 0x49, 0x64, 0x73, 0x1a, 0x39, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x31, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x1a, 0x5b, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3f, 0x0a, 0x11, 0x4d, 0x70, 0x55, 0x6e, 0x69, 0x71, 0x41, 0x70, 0x70, 0x49, 0x64, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x48, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xef, 0x01, 0x0a, 0x09,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12,
	0x34, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x2a, 0x0a,
	0x10, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x46, 0x72, 0x6f, 0x6d, 0x46, 0x72, 0x65,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x46, 0x72, 0x6f, 0x6d, 0x46, 0x72, 0x65, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x26, 0x0a,
	0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xc1, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12,
	0x18, 0x0a, 0x07, 0x66, 0x72, 0x65, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x66, 0x72, 0x65, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x0a, 0x61, 0x64, 0x64,
	0x4f, 0x70, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x41, 0x64, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x61,
	0x64, 0x64, 0x4f, 0x70, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x80, 0x05, 0x0a, 0x0a, 0x41, 0x64,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74,
	0x72, 0x12, 0x34, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x3e, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x61,
	0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5d, 0x0a, 0x09,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x71,
	0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x65, 0x71, 0x4e, 0x75,
	0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x4e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x78, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x78, 0x4e, 0x75, 0x6d, 0x22, 0xd7, 0x01, 0x0a, 0x0b,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x3f,
	0x0a, 0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x1a,
	0x54, 0x0a, 0x0b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc8, 0x03, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x75,
	0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x40, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x31, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x22, 0xd9, 0x03, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x12, 0x39, 0x0a, 0x07, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x07, 0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x75,
	0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x45,
	0x0a, 0x10, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0xef, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12,
	0x2c, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x74, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12,
	0x2c, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x28, 0x0a,
	0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x22, 0x83, 0x01, 0x0a, 0x0f, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x22, 0x9f, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e,
	0x75, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x57, 0x0a, 0x0d, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x0e, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x2f, 0x0a, 0x05, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x05, 0x61, 0x73, 0x73, 0x65, 0x74, 0x22, 0x91, 0x01, 0x0a,
	0x0b, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e,
	0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0xbb, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x75,
	0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x77,
	0x0a, 0x12, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x04, 0x6e, 0x65, 0x78, 0x74, 0x22, 0x49, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a,
	0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x53, 0x0a, 0x0a, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x10, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xdb, 0x01, 0x0a, 0x09, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x4a, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x65, 0x74, 0x47, 0x61, 0x6d,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x10, 0x02, 0x22, 0x82, 0x01, 0x0a, 0x11, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x22, 0x7c, 0x0a, 0x0b, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3b, 0x0a, 0x07, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x52, 0x07,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x61,
	0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x72, 0x6d,
	0x61, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x65, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x66, 0x72, 0x65, 0x65, 0x22, 0xcb, 0x07, 0x0a, 0x12, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x37,
	0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46,
	0x72, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x4c, 0x0a,
	0x08, 0x61, 0x64, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x4c, 0x6f, 0x67, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x61, 0x64, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x53,
	0x0a, 0x0a, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x56, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc1, 0x02, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x4b, 0x0a, 0x08, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x65,
	0x64, 0x1a, 0x58, 0x0a, 0x0d, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x82, 0x01, 0x0a, 0x10,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x69, 0x64, 0x53, 0x68, 0x61, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x69, 0x64, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x53, 0x68, 0x61, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x22, 0xae, 0x02, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x6f, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x6f, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x6f, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x52, 0x61, 0x74, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x66, 0x52, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x75, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x22, 0x57, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x66, 0x72,
	0x6f, 0x6d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x74, 0x6f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x2a, 0x45, 0x0a, 0x0b, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x10, 0x01, 0x12,
	0x10, 0x0a, 0x0c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x10,
	0x02, 0x2a, 0x48, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x73, 0x73, 0x65, 0x74, 0x74, 0x72, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x49, 0x44, 0x43, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x01, 0x2a, 0x4a, 0x0a, 0x0d, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d, 0x46,
	0x72, 0x65, 0x65, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72,
	0x6f, 0x6d, 0x50, 0x61, 0x79, 0x10, 0x02, 0x32, 0xd4, 0x06, 0x0a, 0x05, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x12, 0x3e, 0x0a, 0x03, 0x41, 0x64, 0x64, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3e, 0x0a, 0x03, 0x53, 0x75, 0x62, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4d, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1f, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x41, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x12,
	0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x53, 0x0a, 0x0a, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x06, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x5f, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x50, 0x0a, 0x09, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x12, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x6c, 0x6c,
	0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x42, 0x3d,
	0x5a, 0x3b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_asset_asset_proto_rawDescOnce sync.Once
	file_pb_asset_asset_proto_rawDescData = file_pb_asset_asset_proto_rawDesc
)

func file_pb_asset_asset_proto_rawDescGZIP() []byte {
	file_pb_asset_asset_proto_rawDescOnce.Do(func() {
		file_pb_asset_asset_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_asset_asset_proto_rawDescData)
	})
	return file_pb_asset_asset_proto_rawDescData
}

var file_pb_asset_asset_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_asset_asset_proto_msgTypes = make([]protoimpl.MessageInfo, 49)
var file_pb_asset_asset_proto_goTypes = []interface{}{
	(AssetSource)(0),               // 0: component.game.AssetSource
	(AssetTraceType)(0),            // 1: component.game.AssetTraceType
	(AssetFromType)(0),             // 2: component.game.AssetFromType
	(AssetInfo_Type)(0),            // 3: component.game.AssetInfo.Type
	(*BatchListReq)(nil),           // 4: component.game.BatchListReq
	(*BatchListRsp)(nil),           // 5: component.game.BatchListRsp
	(*ExpireInfo)(nil),             // 6: component.game.ExpireInfo
	(*UserAsset)(nil),              // 7: component.game.UserAsset
	(*AssetAddOption)(nil),         // 8: component.game.AssetAddOption
	(*UserAssetChange)(nil),        // 9: component.game.UserAssetChange
	(*AddRequest)(nil),             // 10: component.game.AddRequest
	(*LimitInfo)(nil),              // 11: component.game.LimitInfo
	(*AddResponse)(nil),            // 12: component.game.AddResponse
	(*SubRequest)(nil),             // 13: component.game.SubRequest
	(*SubResponse)(nil),            // 14: component.game.SubResponse
	(*ExchangeRequest)(nil),        // 15: component.game.ExchangeRequest
	(*ExchangeResponse)(nil),       // 16: component.game.ExchangeResponse
	(*ListRequest)(nil),            // 17: component.game.ListRequest
	(*ListResponse)(nil),           // 18: component.game.ListResponse
	(*QueryNumRequest)(nil),        // 19: component.game.QueryNumRequest
	(*QueryNumResponse)(nil),       // 20: component.game.QueryNumResponse
	(*ExpireRequest)(nil),          // 21: component.game.ExpireRequest
	(*ExpireResponse)(nil),         // 22: component.game.ExpireResponse
	(*AssetRecord)(nil),            // 23: component.game.AssetRecord
	(*RecordListRequest)(nil),      // 24: component.game.RecordListRequest
	(*RecordListResponse)(nil),     // 25: component.game.RecordListResponse
	(*QueryAssetInfoRequest)(nil),  // 26: component.game.QueryAssetInfoRequest
	(*QueryAssetInfoResponse)(nil), // 27: component.game.QueryAssetInfoResponse
	(*CancelSubRequest)(nil),       // 28: component.game.CancelSubRequest
	(*CancelSubResponse)(nil),      // 29: component.game.CancelSubResponse
	(*AssetInfo)(nil),              // 30: component.game.AssetInfo
	(*AssetDetailExpire)(nil),      // 31: component.game.AssetDetailExpire
	(*AssetDetail)(nil),            // 32: component.game.AssetDetail
	(*UserAssetChangeLog)(nil),     // 33: component.game.UserAssetChangeLog
	(*TransactionDetail)(nil),      // 34: component.game.TransactionDetail
	(*RecordListCursor)(nil),       // 35: component.game.RecordListCursor
	(*SettleAllRequest)(nil),       // 36: component.game.SettleAllRequest
	(*SettleAllResponse)(nil),      // 37: component.game.SettleAllResponse
	(*BatchListReq_Option)(nil),    // 38: component.game.BatchListReq.Option
	(*BatchListReq_Item)(nil),      // 39: component.game.BatchListReq.Item
	(*BatchListRsp_Item)(nil),      // 40: component.game.BatchListRsp.Item
	nil,                            // 41: component.game.BatchListRsp.ItemsEntry
	nil,                            // 42: component.game.BatchListRsp.MpUniqAppIdsEntry
	nil,                            // 43: component.game.AddRequest.MapExtEntry
	nil,                            // 44: component.game.AddResponse.LimitsEntry
	nil,                            // 45: component.game.SubRequest.MapExtEntry
	nil,                            // 46: component.game.ExchangeRequest.MapExtEntry
	nil,                            // 47: component.game.QueryNumResponse.AssetNumsEntry
	nil,                            // 48: component.game.QueryAssetInfoResponse.InfosEntry
	nil,                            // 49: component.game.UserAssetChangeLog.InfosEntry
	nil,                            // 50: component.game.UserAssetChangeLog.AddLimitEntry
	nil,                            // 51: component.game.UserAssetChangeLog.BalanceEntry
	nil,                            // 52: component.game.TransactionDetail.DeductedEntry
}
var file_pb_asset_asset_proto_depIdxs = []int32{
	39, // 0: component.game.BatchListReq.items:type_name -> component.game.BatchListReq.Item
	38, // 1: component.game.BatchListReq.option:type_name -> component.game.BatchListReq.Option
	41, // 2: component.game.BatchListRsp.items:type_name -> component.game.BatchListRsp.ItemsEntry
	42, // 3: component.game.BatchListRsp.mpUniqAppIds:type_name -> component.game.BatchListRsp.MpUniqAppIdsEntry
	6,  // 4: component.game.UserAsset.expires:type_name -> component.game.ExpireInfo
	8,  // 5: component.game.UserAssetChange.addOpetion:type_name -> component.game.AssetAddOption
	9,  // 6: component.game.AddRequest.assets:type_name -> component.game.UserAssetChange
	0,  // 7: component.game.AddRequest.source:type_name -> component.game.AssetSource
	2,  // 8: component.game.AddRequest.from:type_name -> component.game.AssetFromType
	1,  // 9: component.game.AddRequest.trace:type_name -> component.game.AssetTraceType
	43, // 10: component.game.AddRequest.mapExt:type_name -> component.game.AddRequest.MapExtEntry
	7,  // 11: component.game.AddResponse.assets:type_name -> component.game.UserAsset
	44, // 12: component.game.AddResponse.limits:type_name -> component.game.AddResponse.LimitsEntry
	9,  // 13: component.game.SubRequest.assets:type_name -> component.game.UserAssetChange
	45, // 14: component.game.SubRequest.mapExt:type_name -> component.game.SubRequest.MapExtEntry
	7,  // 15: component.game.SubResponse.assets:type_name -> component.game.UserAsset
	9,  // 16: component.game.ExchangeRequest.fromAsset:type_name -> component.game.UserAssetChange
	9,  // 17: component.game.ExchangeRequest.toAsset:type_name -> component.game.UserAssetChange
	46, // 18: component.game.ExchangeRequest.mapExt:type_name -> component.game.ExchangeRequest.MapExtEntry
	7,  // 19: component.game.ExchangeResponse.assets:type_name -> component.game.UserAsset
	7,  // 20: component.game.ListResponse.assets:type_name -> component.game.UserAsset
	47, // 21: component.game.QueryNumResponse.assetNums:type_name -> component.game.QueryNumResponse.AssetNumsEntry
	7,  // 22: component.game.ExpireResponse.asset:type_name -> component.game.UserAsset
	23, // 23: component.game.RecordListResponse.records:type_name -> component.game.AssetRecord
	48, // 24: component.game.QueryAssetInfoResponse.infos:type_name -> component.game.QueryAssetInfoResponse.InfosEntry
	3,  // 25: component.game.AssetInfo.type:type_name -> component.game.AssetInfo.Type
	2,  // 26: component.game.AssetDetailExpire.from:type_name -> component.game.AssetFromType
	31, // 27: component.game.AssetDetail.expires:type_name -> component.game.AssetDetailExpire
	9,  // 28: component.game.UserAssetChangeLog.assets:type_name -> component.game.UserAssetChange
	49, // 29: component.game.UserAssetChangeLog.infos:type_name -> component.game.UserAssetChangeLog.InfosEntry
	2,  // 30: component.game.UserAssetChangeLog.from:type_name -> component.game.AssetFromType
	50, // 31: component.game.UserAssetChangeLog.addLimit:type_name -> component.game.UserAssetChangeLog.AddLimitEntry
	1,  // 32: component.game.UserAssetChangeLog.traceType:type_name -> component.game.AssetTraceType
	51, // 33: component.game.UserAssetChangeLog.balance:type_name -> component.game.UserAssetChangeLog.BalanceEntry
	9,  // 34: component.game.TransactionDetail.assets:type_name -> component.game.UserAssetChange
	52, // 35: component.game.TransactionDetail.deducted:type_name -> component.game.TransactionDetail.DeductedEntry
	7,  // 36: component.game.BatchListRsp.Item.assets:type_name -> component.game.UserAsset
	40, // 37: component.game.BatchListRsp.ItemsEntry.value:type_name -> component.game.BatchListRsp.Item
	11, // 38: component.game.AddResponse.LimitsEntry.value:type_name -> component.game.LimitInfo
	30, // 39: component.game.QueryAssetInfoResponse.InfosEntry.value:type_name -> component.game.AssetInfo
	30, // 40: component.game.UserAssetChangeLog.InfosEntry.value:type_name -> component.game.AssetInfo
	11, // 41: component.game.UserAssetChangeLog.AddLimitEntry.value:type_name -> component.game.LimitInfo
	32, // 42: component.game.TransactionDetail.DeductedEntry.value:type_name -> component.game.AssetDetail
	10, // 43: component.game.Asset.Add:input_type -> component.game.AddRequest
	13, // 44: component.game.Asset.Sub:input_type -> component.game.SubRequest
	15, // 45: component.game.Asset.Exchange:input_type -> component.game.ExchangeRequest
	17, // 46: component.game.Asset.List:input_type -> component.game.ListRequest
	19, // 47: component.game.Asset.QueryNum:input_type -> component.game.QueryNumRequest
	24, // 48: component.game.Asset.RecordList:input_type -> component.game.RecordListRequest
	21, // 49: component.game.Asset.Expire:input_type -> component.game.ExpireRequest
	26, // 50: component.game.Asset.QueryAssetInfo:input_type -> component.game.QueryAssetInfoRequest
	28, // 51: component.game.Asset.CancelSub:input_type -> component.game.CancelSubRequest
	36, // 52: component.game.Asset.SettleAll:input_type -> component.game.SettleAllRequest
	4,  // 53: component.game.Asset.BatchList:input_type -> component.game.BatchListReq
	12, // 54: component.game.Asset.Add:output_type -> component.game.AddResponse
	14, // 55: component.game.Asset.Sub:output_type -> component.game.SubResponse
	16, // 56: component.game.Asset.Exchange:output_type -> component.game.ExchangeResponse
	18, // 57: component.game.Asset.List:output_type -> component.game.ListResponse
	20, // 58: component.game.Asset.QueryNum:output_type -> component.game.QueryNumResponse
	25, // 59: component.game.Asset.RecordList:output_type -> component.game.RecordListResponse
	22, // 60: component.game.Asset.Expire:output_type -> component.game.ExpireResponse
	27, // 61: component.game.Asset.QueryAssetInfo:output_type -> component.game.QueryAssetInfoResponse
	29, // 62: component.game.Asset.CancelSub:output_type -> component.game.CancelSubResponse
	37, // 63: component.game.Asset.SettleAll:output_type -> component.game.SettleAllResponse
	5,  // 64: component.game.Asset.BatchList:output_type -> component.game.BatchListRsp
	54, // [54:65] is the sub-list for method output_type
	43, // [43:54] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_pb_asset_asset_proto_init() }
func file_pb_asset_asset_proto_init() {
	if File_pb_asset_asset_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_asset_asset_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetAddOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAssetChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryNumRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryNumResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAssetInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAssetInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetDetailExpire); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAssetChangeLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordListCursor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleAllRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleAllResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListReq_Option); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListReq_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_asset_asset_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchListRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_asset_asset_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   49,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_asset_asset_proto_goTypes,
		DependencyIndexes: file_pb_asset_asset_proto_depIdxs,
		EnumInfos:         file_pb_asset_asset_proto_enumTypes,
		MessageInfos:      file_pb_asset_asset_proto_msgTypes,
	}.Build()
	File_pb_asset_asset_proto = out.File
	file_pb_asset_asset_proto_rawDesc = nil
	file_pb_asset_asset_proto_goTypes = nil
	file_pb_asset_asset_proto_depIdxs = nil
}
