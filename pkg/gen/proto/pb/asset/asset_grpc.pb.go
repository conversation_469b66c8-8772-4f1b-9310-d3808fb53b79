// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/asset/asset.proto

package asset

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Asset_Add_FullMethodName            = "/component.game.Asset/Add"
	Asset_Sub_FullMethodName            = "/component.game.Asset/Sub"
	Asset_Exchange_FullMethodName       = "/component.game.Asset/Exchange"
	Asset_List_FullMethodName           = "/component.game.Asset/List"
	Asset_QueryNum_FullMethodName       = "/component.game.Asset/QueryNum"
	Asset_RecordList_FullMethodName     = "/component.game.Asset/RecordList"
	Asset_Expire_FullMethodName         = "/component.game.Asset/Expire"
	Asset_QueryAssetInfo_FullMethodName = "/component.game.Asset/QueryAssetInfo"
	Asset_CancelSub_FullMethodName      = "/component.game.Asset/CancelSub"
	Asset_SettleAll_FullMethodName      = "/component.game.Asset/SettleAll"
	Asset_BatchList_FullMethodName      = "/component.game.Asset/BatchList"
)

// AssetClient is the client API for Asset service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssetClient interface {
	// 增加资产
	Add(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*AddResponse, error)
	// 扣减资产
	Sub(ctx context.Context, in *SubRequest, opts ...grpc.CallOption) (*SubResponse, error)
	// 游戏币兑换
	Exchange(ctx context.Context, in *ExchangeRequest, opts ...grpc.CallOption) (*ExchangeResponse, error)
	// 查询资产列表
	List(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListResponse, error)
	// 查询资产数量
	QueryNum(ctx context.Context, in *QueryNumRequest, opts ...grpc.CallOption) (*QueryNumResponse, error)
	// 查询资产记录
	RecordList(ctx context.Context, in *RecordListRequest, opts ...grpc.CallOption) (*RecordListResponse, error)
	// 过期
	Expire(ctx context.Context, in *ExpireRequest, opts ...grpc.CallOption) (*ExpireResponse, error)
	// 查询资产配置信息
	QueryAssetInfo(ctx context.Context, in *QueryAssetInfoRequest, opts ...grpc.CallOption) (*QueryAssetInfoResponse, error)
	// 回滚
	CancelSub(ctx context.Context, in *CancelSubRequest, opts ...grpc.CallOption) (*CancelSubResponse, error)
	// 资产切换结算, app独立资产切换为统一资产, 例如: 五子棋 悔棋卡切换为统一道具卡
	SettleAll(ctx context.Context, in *SettleAllRequest, opts ...grpc.CallOption) (*SettleAllResponse, error)
	// 批量查询资产
	BatchList(ctx context.Context, in *BatchListReq, opts ...grpc.CallOption) (*BatchListRsp, error)
}

type assetClient struct {
	cc grpc.ClientConnInterface
}

func NewAssetClient(cc grpc.ClientConnInterface) AssetClient {
	return &assetClient{cc}
}

func (c *assetClient) Add(ctx context.Context, in *AddRequest, opts ...grpc.CallOption) (*AddResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddResponse)
	err := c.cc.Invoke(ctx, Asset_Add_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) Sub(ctx context.Context, in *SubRequest, opts ...grpc.CallOption) (*SubResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubResponse)
	err := c.cc.Invoke(ctx, Asset_Sub_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) Exchange(ctx context.Context, in *ExchangeRequest, opts ...grpc.CallOption) (*ExchangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExchangeResponse)
	err := c.cc.Invoke(ctx, Asset_Exchange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) List(ctx context.Context, in *ListRequest, opts ...grpc.CallOption) (*ListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListResponse)
	err := c.cc.Invoke(ctx, Asset_List_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) QueryNum(ctx context.Context, in *QueryNumRequest, opts ...grpc.CallOption) (*QueryNumResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryNumResponse)
	err := c.cc.Invoke(ctx, Asset_QueryNum_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) RecordList(ctx context.Context, in *RecordListRequest, opts ...grpc.CallOption) (*RecordListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecordListResponse)
	err := c.cc.Invoke(ctx, Asset_RecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) Expire(ctx context.Context, in *ExpireRequest, opts ...grpc.CallOption) (*ExpireResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExpireResponse)
	err := c.cc.Invoke(ctx, Asset_Expire_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) QueryAssetInfo(ctx context.Context, in *QueryAssetInfoRequest, opts ...grpc.CallOption) (*QueryAssetInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryAssetInfoResponse)
	err := c.cc.Invoke(ctx, Asset_QueryAssetInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) CancelSub(ctx context.Context, in *CancelSubRequest, opts ...grpc.CallOption) (*CancelSubResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelSubResponse)
	err := c.cc.Invoke(ctx, Asset_CancelSub_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) SettleAll(ctx context.Context, in *SettleAllRequest, opts ...grpc.CallOption) (*SettleAllResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SettleAllResponse)
	err := c.cc.Invoke(ctx, Asset_SettleAll_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assetClient) BatchList(ctx context.Context, in *BatchListReq, opts ...grpc.CallOption) (*BatchListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchListRsp)
	err := c.cc.Invoke(ctx, Asset_BatchList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssetServer is the server API for Asset service.
// All implementations should embed UnimplementedAssetServer
// for forward compatibility
type AssetServer interface {
	// 增加资产
	Add(context.Context, *AddRequest) (*AddResponse, error)
	// 扣减资产
	Sub(context.Context, *SubRequest) (*SubResponse, error)
	// 游戏币兑换
	Exchange(context.Context, *ExchangeRequest) (*ExchangeResponse, error)
	// 查询资产列表
	List(context.Context, *ListRequest) (*ListResponse, error)
	// 查询资产数量
	QueryNum(context.Context, *QueryNumRequest) (*QueryNumResponse, error)
	// 查询资产记录
	RecordList(context.Context, *RecordListRequest) (*RecordListResponse, error)
	// 过期
	Expire(context.Context, *ExpireRequest) (*ExpireResponse, error)
	// 查询资产配置信息
	QueryAssetInfo(context.Context, *QueryAssetInfoRequest) (*QueryAssetInfoResponse, error)
	// 回滚
	CancelSub(context.Context, *CancelSubRequest) (*CancelSubResponse, error)
	// 资产切换结算, app独立资产切换为统一资产, 例如: 五子棋 悔棋卡切换为统一道具卡
	SettleAll(context.Context, *SettleAllRequest) (*SettleAllResponse, error)
	// 批量查询资产
	BatchList(context.Context, *BatchListReq) (*BatchListRsp, error)
}

// UnimplementedAssetServer should be embedded to have forward compatible implementations.
type UnimplementedAssetServer struct {
}

func (UnimplementedAssetServer) Add(context.Context, *AddRequest) (*AddResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Add not implemented")
}
func (UnimplementedAssetServer) Sub(context.Context, *SubRequest) (*SubResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Sub not implemented")
}
func (UnimplementedAssetServer) Exchange(context.Context, *ExchangeRequest) (*ExchangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Exchange not implemented")
}
func (UnimplementedAssetServer) List(context.Context, *ListRequest) (*ListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedAssetServer) QueryNum(context.Context, *QueryNumRequest) (*QueryNumResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryNum not implemented")
}
func (UnimplementedAssetServer) RecordList(context.Context, *RecordListRequest) (*RecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordList not implemented")
}
func (UnimplementedAssetServer) Expire(context.Context, *ExpireRequest) (*ExpireResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Expire not implemented")
}
func (UnimplementedAssetServer) QueryAssetInfo(context.Context, *QueryAssetInfoRequest) (*QueryAssetInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAssetInfo not implemented")
}
func (UnimplementedAssetServer) CancelSub(context.Context, *CancelSubRequest) (*CancelSubResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelSub not implemented")
}
func (UnimplementedAssetServer) SettleAll(context.Context, *SettleAllRequest) (*SettleAllResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SettleAll not implemented")
}
func (UnimplementedAssetServer) BatchList(context.Context, *BatchListReq) (*BatchListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchList not implemented")
}

// UnsafeAssetServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssetServer will
// result in compilation errors.
type UnsafeAssetServer interface {
	mustEmbedUnimplementedAssetServer()
}

func RegisterAssetServer(s grpc.ServiceRegistrar, srv AssetServer) {
	s.RegisterService(&Asset_ServiceDesc, srv)
}

func _Asset_Add_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).Add(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_Add_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).Add(ctx, req.(*AddRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_Sub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).Sub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_Sub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).Sub(ctx, req.(*SubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_Exchange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).Exchange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_Exchange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).Exchange(ctx, req.(*ExchangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).List(ctx, req.(*ListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_QueryNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).QueryNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_QueryNum_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).QueryNum(ctx, req.(*QueryNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_RecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).RecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_RecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).RecordList(ctx, req.(*RecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_Expire_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpireRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).Expire(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_Expire_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).Expire(ctx, req.(*ExpireRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_QueryAssetInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAssetInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).QueryAssetInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_QueryAssetInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).QueryAssetInfo(ctx, req.(*QueryAssetInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_CancelSub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).CancelSub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_CancelSub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).CancelSub(ctx, req.(*CancelSubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_SettleAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettleAllRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).SettleAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_SettleAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).SettleAll(ctx, req.(*SettleAllRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Asset_BatchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssetServer).BatchList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Asset_BatchList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssetServer).BatchList(ctx, req.(*BatchListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Asset_ServiceDesc is the grpc.ServiceDesc for Asset service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Asset_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.Asset",
	HandlerType: (*AssetServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Add",
			Handler:    _Asset_Add_Handler,
		},
		{
			MethodName: "Sub",
			Handler:    _Asset_Sub_Handler,
		},
		{
			MethodName: "Exchange",
			Handler:    _Asset_Exchange_Handler,
		},
		{
			MethodName: "List",
			Handler:    _Asset_List_Handler,
		},
		{
			MethodName: "QueryNum",
			Handler:    _Asset_QueryNum_Handler,
		},
		{
			MethodName: "RecordList",
			Handler:    _Asset_RecordList_Handler,
		},
		{
			MethodName: "Expire",
			Handler:    _Asset_Expire_Handler,
		},
		{
			MethodName: "QueryAssetInfo",
			Handler:    _Asset_QueryAssetInfo_Handler,
		},
		{
			MethodName: "CancelSub",
			Handler:    _Asset_CancelSub_Handler,
		},
		{
			MethodName: "SettleAll",
			Handler:    _Asset_SettleAll_Handler,
		},
		{
			MethodName: "BatchList",
			Handler:    _Asset_BatchList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/asset/asset.proto",
}
