// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_endless_api/cwsx_game_endless_api.proto

package cwsx_game_endless_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	device "kugou_adapter_service/pkg/gen/proto/pb/device"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OperatingSystem int32

const (
	OperatingSystem_OperatingSystemDefault OperatingSystem = 0 // k歌 默认，不区分系统
	OperatingSystem_OperatingSystemAndroid OperatingSystem = 1
	OperatingSystem_OperatingSystemIOS     OperatingSystem = 2
)

// Enum value maps for OperatingSystem.
var (
	OperatingSystem_name = map[int32]string{
		0: "OperatingSystemDefault",
		1: "OperatingSystemAndroid",
		2: "OperatingSystemIOS",
	}
	OperatingSystem_value = map[string]int32{
		"OperatingSystemDefault": 0,
		"OperatingSystemAndroid": 1,
		"OperatingSystemIOS":     2,
	}
)

func (x OperatingSystem) Enum() *OperatingSystem {
	p := new(OperatingSystem)
	*p = x
	return p
}

func (x OperatingSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperatingSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[0].Descriptor()
}

func (OperatingSystem) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[0]
}

func (x OperatingSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperatingSystem.Descriptor instead.
func (OperatingSystem) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{0}
}

type BoxType int32

const (
	BoxType_BoxTypeCommon BoxType = 0 // 默认格子
	BoxType_BoxTypeAd     BoxType = 1 // 广告格子
)

// Enum value maps for BoxType.
var (
	BoxType_name = map[int32]string{
		0: "BoxTypeCommon",
		1: "BoxTypeAd",
	}
	BoxType_value = map[string]int32{
		"BoxTypeCommon": 0,
		"BoxTypeAd":     1,
	}
)

func (x BoxType) Enum() *BoxType {
	p := new(BoxType)
	*p = x
	return p
}

func (x BoxType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BoxType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[1].Descriptor()
}

func (BoxType) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[1]
}

func (x BoxType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BoxType.Descriptor instead.
func (BoxType) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{1}
}

type RewardItemType int32

const (
	RewardItemType_Reward_From_GameNormal      RewardItemType = 0 // 游戏物品-默认
	RewardItemType_Reward_From_Platform        RewardItemType = 1 // 平台物品
	RewardItemType_Reward_From_GameLimitedTime RewardItemType = 2 // 游戏物品-限时
)

// Enum value maps for RewardItemType.
var (
	RewardItemType_name = map[int32]string{
		0: "Reward_From_GameNormal",
		1: "Reward_From_Platform",
		2: "Reward_From_GameLimitedTime",
	}
	RewardItemType_value = map[string]int32{
		"Reward_From_GameNormal":      0,
		"Reward_From_Platform":        1,
		"Reward_From_GameLimitedTime": 2,
	}
)

func (x RewardItemType) Enum() *RewardItemType {
	p := new(RewardItemType)
	*p = x
	return p
}

func (x RewardItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[2].Descriptor()
}

func (RewardItemType) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[2]
}

func (x RewardItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardItemType.Descriptor instead.
func (RewardItemType) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{2}
}

type EndlessTreasureActivityStatusType int32

const (
	EndlessTreasureActivityStatusType_EndlessTreasureActivityStatusDefault          EndlessTreasureActivityStatusType = 0 // 默认 不存在
	EndlessTreasureActivityStatusType_EndlessTreasureActivityStatusAvailableClaimed EndlessTreasureActivityStatusType = 1 // 待领取
	EndlessTreasureActivityStatusType_EndlessTreasureActivityStatusOngoing          EndlessTreasureActivityStatusType = 2 // 进行中
)

// Enum value maps for EndlessTreasureActivityStatusType.
var (
	EndlessTreasureActivityStatusType_name = map[int32]string{
		0: "EndlessTreasureActivityStatusDefault",
		1: "EndlessTreasureActivityStatusAvailableClaimed",
		2: "EndlessTreasureActivityStatusOngoing",
	}
	EndlessTreasureActivityStatusType_value = map[string]int32{
		"EndlessTreasureActivityStatusDefault":          0,
		"EndlessTreasureActivityStatusAvailableClaimed": 1,
		"EndlessTreasureActivityStatusOngoing":          2,
	}
)

func (x EndlessTreasureActivityStatusType) Enum() *EndlessTreasureActivityStatusType {
	p := new(EndlessTreasureActivityStatusType)
	*p = x
	return p
}

func (x EndlessTreasureActivityStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EndlessTreasureActivityStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[3].Descriptor()
}

func (EndlessTreasureActivityStatusType) Type() protoreflect.EnumType {
	return &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes[3]
}

func (x EndlessTreasureActivityStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EndlessTreasureActivityStatusType.Descriptor instead.
func (EndlessTreasureActivityStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{3}
}

// 查询详情
type QueryEndlessTreasureReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os uint32 `protobuf:"varint,1,opt,name=os,proto3" json:"os,omitempty"` // 手机类型
}

func (x *QueryEndlessTreasureReq) Reset() {
	*x = QueryEndlessTreasureReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryEndlessTreasureReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEndlessTreasureReq) ProtoMessage() {}

func (x *QueryEndlessTreasureReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEndlessTreasureReq.ProtoReflect.Descriptor instead.
func (*QueryEndlessTreasureReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryEndlessTreasureReq) GetOs() uint32 {
	if x != nil {
		return x.Os
	}
	return 0
}

type QueryEndlessTreasureRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title      string          `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`            // 标题
	Banner     string          `protobuf:"bytes,2,opt,name=banner,proto3" json:"banner,omitempty"`          // banner
	LeftTime   uint32          `protobuf:"varint,3,opt,name=leftTime,proto3" json:"leftTime,omitempty"`     // 剩余时间
	DetailDesc string          `protobuf:"bytes,4,opt,name=detailDesc,proto3" json:"detailDesc,omitempty"`  // 详细描述
	PackageId  uint32          `protobuf:"varint,5,opt,name=packageId,proto3" json:"packageId,omitempty"`   // 付费礼包id(扣款使用)
	Nodes      []*TreasureNode `protobuf:"bytes,6,rep,name=nodes,proto3" json:"nodes,omitempty"`            // 宝藏节点
	ActivityId uint32          `protobuf:"varint,7,opt,name=activityId,proto3" json:"activityId,omitempty"` // 活动id
	NodeNum    uint32          `protobuf:"varint,8,opt,name=nodeNum,proto3" json:"nodeNum,omitempty"`       // 格子数量
}

func (x *QueryEndlessTreasureRsp) Reset() {
	*x = QueryEndlessTreasureRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryEndlessTreasureRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEndlessTreasureRsp) ProtoMessage() {}

func (x *QueryEndlessTreasureRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEndlessTreasureRsp.ProtoReflect.Descriptor instead.
func (*QueryEndlessTreasureRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryEndlessTreasureRsp) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *QueryEndlessTreasureRsp) GetBanner() string {
	if x != nil {
		return x.Banner
	}
	return ""
}

func (x *QueryEndlessTreasureRsp) GetLeftTime() uint32 {
	if x != nil {
		return x.LeftTime
	}
	return 0
}

func (x *QueryEndlessTreasureRsp) GetDetailDesc() string {
	if x != nil {
		return x.DetailDesc
	}
	return ""
}

func (x *QueryEndlessTreasureRsp) GetPackageId() uint32 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *QueryEndlessTreasureRsp) GetNodes() []*TreasureNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *QueryEndlessTreasureRsp) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *QueryEndlessTreasureRsp) GetNodeNum() uint32 {
	if x != nil {
		return x.NodeNum
	}
	return 0
}

type TreasureNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                    // 递增id(领奖)
	NodeId      string                   `protobuf:"bytes,2,opt,name=nodeId,proto3" json:"nodeId,omitempty"`            // 格子唯一id(上报)
	Status      uint32                   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`           // 节点状态
	PackageType uint32                   `protobuf:"varint,4,opt,name=packageType,proto3" json:"packageType,omitempty"` // 包装方式
	BeforePrice uint32                   `protobuf:"varint,5,opt,name=beforePrice,proto3" json:"beforePrice,omitempty"` // 打折前价格
	AfterPrice  uint32                   `protobuf:"varint,6,opt,name=afterPrice,proto3" json:"afterPrice,omitempty"`   // 打折后价格
	RewardItems []*RewardItem            `protobuf:"bytes,7,rep,name=rewardItems,proto3" json:"rewardItems,omitempty"`  // 奖励
	GreenMidas  *TreasureNode_GreenMidas `protobuf:"bytes,8,opt,name=greenMidas,proto3" json:"greenMidas,omitempty"`    // q音购买使用
	BoxType     uint32                   `protobuf:"varint,9,opt,name=boxType,proto3" json:"boxType,omitempty"`         // 节点类型
}

func (x *TreasureNode) Reset() {
	*x = TreasureNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreasureNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreasureNode) ProtoMessage() {}

func (x *TreasureNode) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreasureNode.ProtoReflect.Descriptor instead.
func (*TreasureNode) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{2}
}

func (x *TreasureNode) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TreasureNode) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *TreasureNode) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TreasureNode) GetPackageType() uint32 {
	if x != nil {
		return x.PackageType
	}
	return 0
}

func (x *TreasureNode) GetBeforePrice() uint32 {
	if x != nil {
		return x.BeforePrice
	}
	return 0
}

func (x *TreasureNode) GetAfterPrice() uint32 {
	if x != nil {
		return x.AfterPrice
	}
	return 0
}

func (x *TreasureNode) GetRewardItems() []*RewardItem {
	if x != nil {
		return x.RewardItems
	}
	return nil
}

func (x *TreasureNode) GetGreenMidas() *TreasureNode_GreenMidas {
	if x != nil {
		return x.GreenMidas
	}
	return nil
}

func (x *TreasureNode) GetBoxType() uint32 {
	if x != nil {
		return x.BoxType
	}
	return 0
}

type RewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`     // 奖励id
	Num  uint32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`   // 奖励数量 限时道具表示分钟数
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`  // 奖励名称
	Type uint32 `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"` // 资产类型
}

func (x *RewardItem) Reset() {
	*x = RewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardItem) ProtoMessage() {}

func (x *RewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardItem.ProtoReflect.Descriptor instead.
func (*RewardItem) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{3}
}

func (x *RewardItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RewardItem) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *RewardItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RewardItem) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// 查询状态
type QueryEndlessTreasureStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os uint32 `protobuf:"varint,1,opt,name=os,proto3" json:"os,omitempty"` // 手机类型
}

func (x *QueryEndlessTreasureStatusReq) Reset() {
	*x = QueryEndlessTreasureStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryEndlessTreasureStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEndlessTreasureStatusReq) ProtoMessage() {}

func (x *QueryEndlessTreasureStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEndlessTreasureStatusReq.ProtoReflect.Descriptor instead.
func (*QueryEndlessTreasureStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{4}
}

func (x *QueryEndlessTreasureStatusReq) GetOs() uint32 {
	if x != nil {
		return x.Os
	}
	return 0
}

type QueryEndlessTreasureStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     uint32      `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`         // 活动状态
	LeftTime   uint32      `protobuf:"varint,2,opt,name=leftTime,proto3" json:"leftTime,omitempty"`     // 剩余时间
	ActivityId uint32      `protobuf:"varint,3,opt,name=activityId,proto3" json:"activityId,omitempty"` // 活动id
	Period     string      `protobuf:"bytes,4,opt,name=period,proto3" json:"period,omitempty"`          // 活动周期
	NodeNum    uint32      `protobuf:"varint,5,opt,name=nodeNum,proto3" json:"nodeNum,omitempty"`       // 格子数量
	SkinConfig *SkinConfig `protobuf:"bytes,6,opt,name=skinConfig,proto3" json:"skinConfig,omitempty"`  // 皮肤配置
}

func (x *QueryEndlessTreasureStatusRsp) Reset() {
	*x = QueryEndlessTreasureStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryEndlessTreasureStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEndlessTreasureStatusRsp) ProtoMessage() {}

func (x *QueryEndlessTreasureStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEndlessTreasureStatusRsp.ProtoReflect.Descriptor instead.
func (*QueryEndlessTreasureStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{5}
}

func (x *QueryEndlessTreasureStatusRsp) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *QueryEndlessTreasureStatusRsp) GetLeftTime() uint32 {
	if x != nil {
		return x.LeftTime
	}
	return 0
}

func (x *QueryEndlessTreasureStatusRsp) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *QueryEndlessTreasureStatusRsp) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

func (x *QueryEndlessTreasureStatusRsp) GetNodeNum() uint32 {
	if x != nil {
		return x.NodeNum
	}
	return 0
}

func (x *QueryEndlessTreasureStatusRsp) GetSkinConfig() *SkinConfig {
	if x != nil {
		return x.SkinConfig
	}
	return nil
}

// 领取
type CompleteEndlessTreasureReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`  // id
	Os         uint32         `protobuf:"varint,2,opt,name=os,proto3" json:"os,omitempty"` // 手机类型
	Device     *device.Device `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`
	IsDoubleAd bool           `protobuf:"varint,4,opt,name=is_double_ad,json=isDoubleAd,proto3" json:"is_double_ad,omitempty"` // 是否双倍广告领取
}

func (x *CompleteEndlessTreasureReq) Reset() {
	*x = CompleteEndlessTreasureReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteEndlessTreasureReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteEndlessTreasureReq) ProtoMessage() {}

func (x *CompleteEndlessTreasureReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteEndlessTreasureReq.ProtoReflect.Descriptor instead.
func (*CompleteEndlessTreasureReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{6}
}

func (x *CompleteEndlessTreasureReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CompleteEndlessTreasureReq) GetOs() uint32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *CompleteEndlessTreasureReq) GetDevice() *device.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *CompleteEndlessTreasureReq) GetIsDoubleAd() bool {
	if x != nil {
		return x.IsDoubleAd
	}
	return false
}

type CompleteEndlessTreasureRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CompleteEndlessTreasureRsp) Reset() {
	*x = CompleteEndlessTreasureRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteEndlessTreasureRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteEndlessTreasureRsp) ProtoMessage() {}

func (x *CompleteEndlessTreasureRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteEndlessTreasureRsp.ProtoReflect.Descriptor instead.
func (*CompleteEndlessTreasureRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{7}
}

type TreasureNode_GreenMidas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId     string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId,omitempty"`
	Price         int64  `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	SkuId         string `protobuf:"bytes,3,opt,name=skuId,proto3" json:"skuId,omitempty"`
	AvailableTime string `protobuf:"bytes,4,opt,name=availableTime,proto3" json:"availableTime,omitempty"`
	ActivityId    string `protobuf:"bytes,5,opt,name=activityId,proto3" json:"activityId,omitempty"`
	GreenId       int64  `protobuf:"varint,6,opt,name=greenId,proto3" json:"greenId,omitempty"` // 平台货币Id
	Extra         string `protobuf:"bytes,7,opt,name=extra,proto3" json:"extra,omitempty"`      // 额外信息
}

func (x *TreasureNode_GreenMidas) Reset() {
	*x = TreasureNode_GreenMidas{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreasureNode_GreenMidas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreasureNode_GreenMidas) ProtoMessage() {}

func (x *TreasureNode_GreenMidas) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreasureNode_GreenMidas.ProtoReflect.Descriptor instead.
func (*TreasureNode_GreenMidas) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *TreasureNode_GreenMidas) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *TreasureNode_GreenMidas) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *TreasureNode_GreenMidas) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *TreasureNode_GreenMidas) GetAvailableTime() string {
	if x != nil {
		return x.AvailableTime
	}
	return ""
}

func (x *TreasureNode_GreenMidas) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *TreasureNode_GreenMidas) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

func (x *TreasureNode_GreenMidas) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

var File_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x1a, 0x25, 0x70,
	0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c,
	0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74,
	0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x29, 0x0a, 0x17,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x6f, 0x73, 0x22, 0x96, 0x02, 0x0a, 0x17, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x6e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x75,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x75, 0x6d,
	0x22, 0xb0, 0x04, 0x0a, 0x0c, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x66, 0x74, 0x65, 0x72, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x4e, 0x0a, 0x0a, 0x67, 0x72,
	0x65, 0x65, 0x6e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x2e, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x52, 0x0a,
	0x67, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x6f,
	0x78, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x62, 0x6f, 0x78,
	0x54, 0x79, 0x70, 0x65, 0x1a, 0xcc, 0x01, 0x0a, 0x0a, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x69,
	0x64, 0x61, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x24, 0x0a,
	0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x22, 0x56, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x2f, 0x0a, 0x1d, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x6f, 0x73, 0x22, 0xe8, 0x01, 0x0a,
	0x1d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x6f,
	0x64, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6e, 0x6f, 0x64,
	0x65, 0x4e, 0x75, 0x6d, 0x12, 0x41, 0x0a, 0x0a, 0x73, 0x6b, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x53, 0x6b, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x73, 0x6b, 0x69,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x86, 0x01, 0x0a, 0x1a, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x20,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x41, 0x64,
	0x22, 0x1c, 0x0a, 0x1a, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6c,
	0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x73, 0x70, 0x2a, 0x61,
	0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x1a, 0x0a,
	0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x4f, 0x53, 0x10,
	0x02, 0x2a, 0x2b, 0x0a, 0x07, 0x42, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d,
	0x42, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x42, 0x6f, 0x78, 0x54, 0x79, 0x70, 0x65, 0x41, 0x64, 0x10, 0x01, 0x2a, 0x67,
	0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x16, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f,
	0x47, 0x61, 0x6d, 0x65, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x10, 0x02, 0x2a, 0xaa, 0x01, 0x0a, 0x21, 0x45, 0x6e, 0x64, 0x6c,
	0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a,
	0x24, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x45, 0x6e, 0x64, 0x6c, 0x65,
	0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x45, 0x6e,
	0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x6e, 0x67, 0x6f, 0x69,
	0x6e, 0x67, 0x10, 0x02, 0x32, 0xdb, 0x03, 0x0a, 0x12, 0x43, 0x77, 0x73, 0x78, 0x47, 0x61, 0x6d,
	0x65, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x41, 0x70, 0x69, 0x12, 0x76, 0x0a, 0x14, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x12, 0x2e, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x88, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64,
	0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65,
	0x61, 0x73, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x7f,
	0x0a, 0x17, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73,
	0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x12, 0x31, 0x2e, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x6c, 0x65, 0x73,
	0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64,
	0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x41, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65,
	0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x42, 0x4d, 0x5a, 0x4b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x70,
	0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescData = file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDesc
)

func file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescData)
	})
	return file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDescData
}

var file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_goTypes = []interface{}{
	(OperatingSystem)(0),                   // 0: cwsx_game_endless_api.OperatingSystem
	(BoxType)(0),                           // 1: cwsx_game_endless_api.BoxType
	(RewardItemType)(0),                    // 2: cwsx_game_endless_api.RewardItemType
	(EndlessTreasureActivityStatusType)(0), // 3: cwsx_game_endless_api.EndlessTreasureActivityStatusType
	(*QueryEndlessTreasureReq)(nil),        // 4: cwsx_game_endless_api.QueryEndlessTreasureReq
	(*QueryEndlessTreasureRsp)(nil),        // 5: cwsx_game_endless_api.QueryEndlessTreasureRsp
	(*TreasureNode)(nil),                   // 6: cwsx_game_endless_api.TreasureNode
	(*RewardItem)(nil),                     // 7: cwsx_game_endless_api.RewardItem
	(*QueryEndlessTreasureStatusReq)(nil),  // 8: cwsx_game_endless_api.QueryEndlessTreasureStatusReq
	(*QueryEndlessTreasureStatusRsp)(nil),  // 9: cwsx_game_endless_api.QueryEndlessTreasureStatusRsp
	(*CompleteEndlessTreasureReq)(nil),     // 10: cwsx_game_endless_api.CompleteEndlessTreasureReq
	(*CompleteEndlessTreasureRsp)(nil),     // 11: cwsx_game_endless_api.CompleteEndlessTreasureRsp
	(*TreasureNode_GreenMidas)(nil),        // 12: cwsx_game_endless_api.TreasureNode.GreenMidas
	(*SkinConfig)(nil),                     // 13: cwsx_game_endless_api.SkinConfig
	(*device.Device)(nil),                  // 14: device.Device
	(*inlet.ActivityStateReq)(nil),         // 15: inlet.ActivityStateReq
	(*inlet.ActivityStateRsp)(nil),         // 16: inlet.ActivityStateRsp
}
var file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_depIdxs = []int32{
	6,  // 0: cwsx_game_endless_api.QueryEndlessTreasureRsp.nodes:type_name -> cwsx_game_endless_api.TreasureNode
	7,  // 1: cwsx_game_endless_api.TreasureNode.rewardItems:type_name -> cwsx_game_endless_api.RewardItem
	12, // 2: cwsx_game_endless_api.TreasureNode.greenMidas:type_name -> cwsx_game_endless_api.TreasureNode.GreenMidas
	13, // 3: cwsx_game_endless_api.QueryEndlessTreasureStatusRsp.skinConfig:type_name -> cwsx_game_endless_api.SkinConfig
	14, // 4: cwsx_game_endless_api.CompleteEndlessTreasureReq.device:type_name -> device.Device
	4,  // 5: cwsx_game_endless_api.CwsxGameEndlessApi.QueryEndlessTreasure:input_type -> cwsx_game_endless_api.QueryEndlessTreasureReq
	8,  // 6: cwsx_game_endless_api.CwsxGameEndlessApi.QueryEndlessTreasureStatus:input_type -> cwsx_game_endless_api.QueryEndlessTreasureStatusReq
	10, // 7: cwsx_game_endless_api.CwsxGameEndlessApi.CompleteEndlessTreasure:input_type -> cwsx_game_endless_api.CompleteEndlessTreasureReq
	15, // 8: cwsx_game_endless_api.CwsxGameEndlessApi.ActivityState:input_type -> inlet.ActivityStateReq
	5,  // 9: cwsx_game_endless_api.CwsxGameEndlessApi.QueryEndlessTreasure:output_type -> cwsx_game_endless_api.QueryEndlessTreasureRsp
	9,  // 10: cwsx_game_endless_api.CwsxGameEndlessApi.QueryEndlessTreasureStatus:output_type -> cwsx_game_endless_api.QueryEndlessTreasureStatusRsp
	11, // 11: cwsx_game_endless_api.CwsxGameEndlessApi.CompleteEndlessTreasure:output_type -> cwsx_game_endless_api.CompleteEndlessTreasureRsp
	16, // 12: cwsx_game_endless_api.CwsxGameEndlessApi.ActivityState:output_type -> inlet.ActivityStateRsp
	9,  // [9:13] is the sub-list for method output_type
	5,  // [5:9] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_init() }
func file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_init() {
	if File_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto != nil {
		return
	}
	file_pb_cwsx_game_endless_api_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryEndlessTreasureReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryEndlessTreasureRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreasureNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryEndlessTreasureStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryEndlessTreasureStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteEndlessTreasureReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteEndlessTreasureRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreasureNode_GreenMidas); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_depIdxs,
		EnumInfos:         file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_enumTypes,
		MessageInfos:      file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto = out.File
	file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_rawDesc = nil
	file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_goTypes = nil
	file_pb_cwsx_game_endless_api_cwsx_game_endless_api_proto_depIdxs = nil
}
