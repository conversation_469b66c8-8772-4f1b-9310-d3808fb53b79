// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/cwsx_game_endless_api/cwsx_game_endless_api.proto

/*
Package cwsx_game_endless_api is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package cwsx_game_endless_api

import (
	"context"
	"io"
	"kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_CwsxGameEndlessApi_QueryEndlessTreasure_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryEndlessTreasureReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryEndlessTreasure(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessApi_QueryEndlessTreasure_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryEndlessTreasureReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryEndlessTreasure(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryEndlessTreasureStatusReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryEndlessTreasureStatus(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryEndlessTreasureStatusReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryEndlessTreasureStatus(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameEndlessApi_CompleteEndlessTreasure_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq CompleteEndlessTreasureReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.CompleteEndlessTreasure(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessApi_CompleteEndlessTreasure_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq CompleteEndlessTreasureReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.CompleteEndlessTreasure(ctx, &protoReq)
	return msg, metadata, err

}

func request_CwsxGameEndlessApi_ActivityState_0(ctx context.Context, marshaler runtime.Marshaler, client CwsxGameEndlessApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq inlet.ActivityStateReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.ActivityState(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CwsxGameEndlessApi_ActivityState_0(ctx context.Context, marshaler runtime.Marshaler, server CwsxGameEndlessApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq inlet.ActivityStateReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.ActivityState(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterCwsxGameEndlessApiHandlerServer registers the http handlers for service CwsxGameEndlessApi to "mux".
// UnaryRPC     :call CwsxGameEndlessApiServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterCwsxGameEndlessApiHandlerFromEndpoint instead.
func RegisterCwsxGameEndlessApiHandlerServer(ctx context.Context, mux *runtime.ServeMux, server CwsxGameEndlessApiServer) error {

	mux.Handle("POST", pattern_CwsxGameEndlessApi_QueryEndlessTreasure_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasure", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasure"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessApi_QueryEndlessTreasure_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_QueryEndlessTreasure_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasureStatus", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasureStatus"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessApi_CompleteEndlessTreasure_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/CompleteEndlessTreasure", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/CompleteEndlessTreasure"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessApi_CompleteEndlessTreasure_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_CompleteEndlessTreasure_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessApi_ActivityState_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/ActivityState", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/ActivityState"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CwsxGameEndlessApi_ActivityState_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_ActivityState_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterCwsxGameEndlessApiHandlerFromEndpoint is same as RegisterCwsxGameEndlessApiHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterCwsxGameEndlessApiHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterCwsxGameEndlessApiHandler(ctx, mux, conn)
}

// RegisterCwsxGameEndlessApiHandler registers the http handlers for service CwsxGameEndlessApi to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterCwsxGameEndlessApiHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterCwsxGameEndlessApiHandlerClient(ctx, mux, NewCwsxGameEndlessApiClient(conn))
}

// RegisterCwsxGameEndlessApiHandlerClient registers the http handlers for service CwsxGameEndlessApi
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "CwsxGameEndlessApiClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "CwsxGameEndlessApiClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "CwsxGameEndlessApiClient" to call the correct interceptors.
func RegisterCwsxGameEndlessApiHandlerClient(ctx context.Context, mux *runtime.ServeMux, client CwsxGameEndlessApiClient) error {

	mux.Handle("POST", pattern_CwsxGameEndlessApi_QueryEndlessTreasure_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasure", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasure"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessApi_QueryEndlessTreasure_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_QueryEndlessTreasure_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasureStatus", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/QueryEndlessTreasureStatus"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessApi_CompleteEndlessTreasure_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/CompleteEndlessTreasure", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/CompleteEndlessTreasure"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessApi_CompleteEndlessTreasure_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_CompleteEndlessTreasure_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CwsxGameEndlessApi_ActivityState_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/cwsx_game_endless_api.CwsxGameEndlessApi/ActivityState", runtime.WithHTTPPathPattern("/cwsx_game_endless_api.CwsxGameEndlessApi/ActivityState"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CwsxGameEndlessApi_ActivityState_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CwsxGameEndlessApi_ActivityState_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_CwsxGameEndlessApi_QueryEndlessTreasure_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_api.CwsxGameEndlessApi", "QueryEndlessTreasure"}, ""))

	pattern_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_api.CwsxGameEndlessApi", "QueryEndlessTreasureStatus"}, ""))

	pattern_CwsxGameEndlessApi_CompleteEndlessTreasure_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_api.CwsxGameEndlessApi", "CompleteEndlessTreasure"}, ""))

	pattern_CwsxGameEndlessApi_ActivityState_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"cwsx_game_endless_api.CwsxGameEndlessApi", "ActivityState"}, ""))
)

var (
	forward_CwsxGameEndlessApi_QueryEndlessTreasure_0 = runtime.ForwardResponseMessage

	forward_CwsxGameEndlessApi_QueryEndlessTreasureStatus_0 = runtime.ForwardResponseMessage

	forward_CwsxGameEndlessApi_CompleteEndlessTreasure_0 = runtime.ForwardResponseMessage

	forward_CwsxGameEndlessApi_ActivityState_0 = runtime.ForwardResponseMessage
)
