// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_private_api/game_private_api.proto

package game_private_api

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RoomSourceType int32

const (
	RoomSourceType_ROOM_SOURCE_TYPE_NONE       RoomSourceType = 0
	RoomSourceType_ROOM_SOURCE_TYPE_LIVE       RoomSourceType = 1 //直播
	RoomSourceType_ROOM_SOURCE_TYPE_FRIEND_KTV RoomSourceType = 2 //好友歌房
	RoomSourceType_ROOM_SOURCE_TYPE_KTV        RoomSourceType = 3 //单麦歌房
	RoomSourceType_ROOM_SOURCE_TYPE_MUTIL_KTV  RoomSourceType = 4 //多麦歌房
)

// Enum value maps for RoomSourceType.
var (
	RoomSourceType_name = map[int32]string{
		0: "ROOM_SOURCE_TYPE_NONE",
		1: "ROOM_SOURCE_TYPE_LIVE",
		2: "ROOM_SOURCE_TYPE_FRIEND_KTV",
		3: "ROOM_SOURCE_TYPE_KTV",
		4: "ROOM_SOURCE_TYPE_MUTIL_KTV",
	}
	RoomSourceType_value = map[string]int32{
		"ROOM_SOURCE_TYPE_NONE":       0,
		"ROOM_SOURCE_TYPE_LIVE":       1,
		"ROOM_SOURCE_TYPE_FRIEND_KTV": 2,
		"ROOM_SOURCE_TYPE_KTV":        3,
		"ROOM_SOURCE_TYPE_MUTIL_KTV":  4,
	}
)

func (x RoomSourceType) Enum() *RoomSourceType {
	p := new(RoomSourceType)
	*p = x
	return p
}

func (x RoomSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoomSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_private_api_game_private_api_proto_enumTypes[0].Descriptor()
}

func (RoomSourceType) Type() protoreflect.EnumType {
	return &file_pb_game_private_api_game_private_api_proto_enumTypes[0]
}

func (x RoomSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoomSourceType.Descriptor instead.
func (RoomSourceType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{0}
}

// 生成图片信息
type NormalMsgLuckyFarmAnimalEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	SeqId     string   `protobuf:"bytes,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	AnimalIds []string `protobuf:"bytes,3,rep,name=animal_ids,json=animalIds,proto3" json:"animal_ids,omitempty"`
}

func (x *NormalMsgLuckyFarmAnimalEventReq) Reset() {
	*x = NormalMsgLuckyFarmAnimalEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgLuckyFarmAnimalEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgLuckyFarmAnimalEventReq) ProtoMessage() {}

func (x *NormalMsgLuckyFarmAnimalEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgLuckyFarmAnimalEventReq.ProtoReflect.Descriptor instead.
func (*NormalMsgLuckyFarmAnimalEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{0}
}

func (x *NormalMsgLuckyFarmAnimalEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *NormalMsgLuckyFarmAnimalEventReq) GetSeqId() string {
	if x != nil {
		return x.SeqId
	}
	return ""
}

func (x *NormalMsgLuckyFarmAnimalEventReq) GetAnimalIds() []string {
	if x != nil {
		return x.AnimalIds
	}
	return nil
}

type NormalMsgLuckyFarmAnimalEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *NormalMsgLuckyFarmAnimalEventRsp) Reset() {
	*x = NormalMsgLuckyFarmAnimalEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgLuckyFarmAnimalEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgLuckyFarmAnimalEventRsp) ProtoMessage() {}

func (x *NormalMsgLuckyFarmAnimalEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgLuckyFarmAnimalEventRsp.ProtoReflect.Descriptor instead.
func (*NormalMsgLuckyFarmAnimalEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{1}
}

func (x *NormalMsgLuckyFarmAnimalEventRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NormalMsgLuckyFarmAnimalEventRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type NormalMsgUserSwitchEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uid    uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	IsOpen bool   `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	SwType uint32 `protobuf:"varint,4,opt,name=sw_type,json=swType,proto3" json:"sw_type,omitempty"` // 脚手架类型
}

func (x *NormalMsgUserSwitchEventReq) Reset() {
	*x = NormalMsgUserSwitchEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgUserSwitchEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgUserSwitchEventReq) ProtoMessage() {}

func (x *NormalMsgUserSwitchEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgUserSwitchEventReq.ProtoReflect.Descriptor instead.
func (*NormalMsgUserSwitchEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{2}
}

func (x *NormalMsgUserSwitchEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *NormalMsgUserSwitchEventReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *NormalMsgUserSwitchEventReq) GetIsOpen() bool {
	if x != nil {
		return x.IsOpen
	}
	return false
}

func (x *NormalMsgUserSwitchEventReq) GetSwType() uint32 {
	if x != nil {
		return x.SwType
	}
	return 0
}

type NormalMsgUserSwitchEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *NormalMsgUserSwitchEventRsp) Reset() {
	*x = NormalMsgUserSwitchEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgUserSwitchEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgUserSwitchEventRsp) ProtoMessage() {}

func (x *NormalMsgUserSwitchEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgUserSwitchEventRsp.ProtoReflect.Descriptor instead.
func (*NormalMsgUserSwitchEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{3}
}

func (x *NormalMsgUserSwitchEventRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NormalMsgUserSwitchEventRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type NormalMsgLuckyFarmLuckyMomentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	SeqId     string `protobuf:"bytes,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	LuckyText string `protobuf:"bytes,3,opt,name=lucky_text,json=luckyText,proto3" json:"lucky_text,omitempty"`
	// 新增狂欢时刻接口
	JoinNum   uint32 `protobuf:"varint,4,opt,name=join_num,json=joinNum,proto3" json:"join_num,omitempty"`       // 参与人数
	FlowerNum uint32 `protobuf:"varint,5,opt,name=flower_num,json=flowerNum,proto3" json:"flower_num,omitempty"` // 鲜花数量
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) Reset() {
	*x = NormalMsgLuckyFarmLuckyMomentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgLuckyFarmLuckyMomentReq) ProtoMessage() {}

func (x *NormalMsgLuckyFarmLuckyMomentReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgLuckyFarmLuckyMomentReq.ProtoReflect.Descriptor instead.
func (*NormalMsgLuckyFarmLuckyMomentReq) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{4}
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) GetSeqId() string {
	if x != nil {
		return x.SeqId
	}
	return ""
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) GetLuckyText() string {
	if x != nil {
		return x.LuckyText
	}
	return ""
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) GetJoinNum() uint32 {
	if x != nil {
		return x.JoinNum
	}
	return 0
}

func (x *NormalMsgLuckyFarmLuckyMomentReq) GetFlowerNum() uint32 {
	if x != nil {
		return x.FlowerNum
	}
	return 0
}

type NormalMsgLuckyFarmLuckyMomentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *NormalMsgLuckyFarmLuckyMomentRsp) Reset() {
	*x = NormalMsgLuckyFarmLuckyMomentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgLuckyFarmLuckyMomentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgLuckyFarmLuckyMomentRsp) ProtoMessage() {}

func (x *NormalMsgLuckyFarmLuckyMomentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgLuckyFarmLuckyMomentRsp.ProtoReflect.Descriptor instead.
func (*NormalMsgLuckyFarmLuckyMomentRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{5}
}

func (x *NormalMsgLuckyFarmLuckyMomentRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NormalMsgLuckyFarmLuckyMomentRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type LuckyFarmNoAnimalCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnimalId    string `protobuf:"bytes,1,opt,name=animal_id,json=animalId,proto3" json:"animal_id,omitempty"`
	AnimalCount uint32 `protobuf:"varint,2,opt,name=animal_count,json=animalCount,proto3" json:"animal_count,omitempty"`
}

func (x *LuckyFarmNoAnimalCount) Reset() {
	*x = LuckyFarmNoAnimalCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LuckyFarmNoAnimalCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LuckyFarmNoAnimalCount) ProtoMessage() {}

func (x *LuckyFarmNoAnimalCount) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LuckyFarmNoAnimalCount.ProtoReflect.Descriptor instead.
func (*LuckyFarmNoAnimalCount) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{6}
}

func (x *LuckyFarmNoAnimalCount) GetAnimalId() string {
	if x != nil {
		return x.AnimalId
	}
	return ""
}

func (x *LuckyFarmNoAnimalCount) GetAnimalCount() uint32 {
	if x != nil {
		return x.AnimalCount
	}
	return 0
}

// 幸运牧场: 没有出现怪兽信息
type NormalMsgLuckyFarmNoAnimalEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string                    `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	SeqId      string                    `protobuf:"bytes,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	AnimalInfo []*LuckyFarmNoAnimalCount `protobuf:"bytes,3,rep,name=animal_info,json=animalInfo,proto3" json:"animal_info,omitempty"`
}

func (x *NormalMsgLuckyFarmNoAnimalEventReq) Reset() {
	*x = NormalMsgLuckyFarmNoAnimalEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgLuckyFarmNoAnimalEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgLuckyFarmNoAnimalEventReq) ProtoMessage() {}

func (x *NormalMsgLuckyFarmNoAnimalEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgLuckyFarmNoAnimalEventReq.ProtoReflect.Descriptor instead.
func (*NormalMsgLuckyFarmNoAnimalEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{7}
}

func (x *NormalMsgLuckyFarmNoAnimalEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *NormalMsgLuckyFarmNoAnimalEventReq) GetSeqId() string {
	if x != nil {
		return x.SeqId
	}
	return ""
}

func (x *NormalMsgLuckyFarmNoAnimalEventReq) GetAnimalInfo() []*LuckyFarmNoAnimalCount {
	if x != nil {
		return x.AnimalInfo
	}
	return nil
}

type NormalMsgLuckyFarmNoAnimalEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *NormalMsgLuckyFarmNoAnimalEventRsp) Reset() {
	*x = NormalMsgLuckyFarmNoAnimalEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalMsgLuckyFarmNoAnimalEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalMsgLuckyFarmNoAnimalEventRsp) ProtoMessage() {}

func (x *NormalMsgLuckyFarmNoAnimalEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalMsgLuckyFarmNoAnimalEventRsp.ProtoReflect.Descriptor instead.
func (*NormalMsgLuckyFarmNoAnimalEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{8}
}

func (x *NormalMsgLuckyFarmNoAnimalEventRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NormalMsgLuckyFarmNoAnimalEventRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

// 获取数据详细信息
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nick   string `protobuf:"bytes,1,opt,name=nick,proto3" json:"nick,omitempty"`
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Uid    uint64 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{9}
}

func (x *UserInfo) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type RoomInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId     string         `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	RoomName   string         `protobuf:"bytes,2,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	FaceUrl    string         `protobuf:"bytes,3,opt,name=face_url,json=faceUrl,proto3" json:"face_url,omitempty"`
	Anchor     *UserInfo      `protobuf:"bytes,4,opt,name=anchor,proto3" json:"anchor,omitempty"`
	SourceType RoomSourceType `protobuf:"varint,5,opt,name=source_type,json=sourceType,proto3,enum=kg.game_private_api.RoomSourceType" json:"source_type,omitempty"`
}

func (x *RoomInfo) Reset() {
	*x = RoomInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomInfo) ProtoMessage() {}

func (x *RoomInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomInfo.ProtoReflect.Descriptor instead.
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{10}
}

func (x *RoomInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomInfo) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *RoomInfo) GetFaceUrl() string {
	if x != nil {
		return x.FaceUrl
	}
	return ""
}

func (x *RoomInfo) GetAnchor() *UserInfo {
	if x != nil {
		return x.Anchor
	}
	return nil
}

func (x *RoomInfo) GetSourceType() RoomSourceType {
	if x != nil {
		return x.SourceType
	}
	return RoomSourceType_ROOM_SOURCE_TYPE_NONE
}

type SurpriseDancingGetRoomDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Num   uint32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *SurpriseDancingGetRoomDataReq) Reset() {
	*x = SurpriseDancingGetRoomDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurpriseDancingGetRoomDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurpriseDancingGetRoomDataReq) ProtoMessage() {}

func (x *SurpriseDancingGetRoomDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurpriseDancingGetRoomDataReq.ProtoReflect.Descriptor instead.
func (*SurpriseDancingGetRoomDataReq) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{11}
}

func (x *SurpriseDancingGetRoomDataReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SurpriseDancingGetRoomDataReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type SurpriseDancingGetRoomDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode   int32       `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg    string      `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
	ManAnchor   []*RoomInfo `protobuf:"bytes,3,rep,name=man_anchor,json=manAnchor,proto3" json:"man_anchor,omitempty"`
	WomanAnchor []*RoomInfo `protobuf:"bytes,4,rep,name=woman_anchor,json=womanAnchor,proto3" json:"woman_anchor,omitempty"`
	KtvRoom     []*RoomInfo `protobuf:"bytes,5,rep,name=ktv_room,json=ktvRoom,proto3" json:"ktv_room,omitempty"`
}

func (x *SurpriseDancingGetRoomDataRsp) Reset() {
	*x = SurpriseDancingGetRoomDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurpriseDancingGetRoomDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurpriseDancingGetRoomDataRsp) ProtoMessage() {}

func (x *SurpriseDancingGetRoomDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurpriseDancingGetRoomDataRsp.ProtoReflect.Descriptor instead.
func (*SurpriseDancingGetRoomDataRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{12}
}

func (x *SurpriseDancingGetRoomDataRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *SurpriseDancingGetRoomDataRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *SurpriseDancingGetRoomDataRsp) GetManAnchor() []*RoomInfo {
	if x != nil {
		return x.ManAnchor
	}
	return nil
}

func (x *SurpriseDancingGetRoomDataRsp) GetWomanAnchor() []*RoomInfo {
	if x != nil {
		return x.WomanAnchor
	}
	return nil
}

func (x *SurpriseDancingGetRoomDataRsp) GetKtvRoom() []*RoomInfo {
	if x != nil {
		return x.KtvRoom
	}
	return nil
}

type SurpriseDancingGetLiveStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	RoomIdList []string `protobuf:"bytes,2,rep,name=room_id_list,json=roomIdList,proto3" json:"room_id_list,omitempty"`
}

func (x *SurpriseDancingGetLiveStatusReq) Reset() {
	*x = SurpriseDancingGetLiveStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurpriseDancingGetLiveStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurpriseDancingGetLiveStatusReq) ProtoMessage() {}

func (x *SurpriseDancingGetLiveStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurpriseDancingGetLiveStatusReq.ProtoReflect.Descriptor instead.
func (*SurpriseDancingGetLiveStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{13}
}

func (x *SurpriseDancingGetLiveStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SurpriseDancingGetLiveStatusReq) GetRoomIdList() []string {
	if x != nil {
		return x.RoomIdList
	}
	return nil
}

type SurpriseDancingGetLiveStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32           `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string          `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
	MapStatus map[string]bool `protobuf:"bytes,3,rep,name=map_status,json=mapStatus,proto3" json:"map_status,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *SurpriseDancingGetLiveStatusRsp) Reset() {
	*x = SurpriseDancingGetLiveStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurpriseDancingGetLiveStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurpriseDancingGetLiveStatusRsp) ProtoMessage() {}

func (x *SurpriseDancingGetLiveStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_private_api_game_private_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurpriseDancingGetLiveStatusRsp.ProtoReflect.Descriptor instead.
func (*SurpriseDancingGetLiveStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_private_api_game_private_api_proto_rawDescGZIP(), []int{14}
}

func (x *SurpriseDancingGetLiveStatusRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *SurpriseDancingGetLiveStatusRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *SurpriseDancingGetLiveStatusRsp) GetMapStatus() map[string]bool {
	if x != nil {
		return x.MapStatus
	}
	return nil
}

var File_pb_game_private_api_game_private_api_proto protoreflect.FileDescriptor

var file_pb_game_private_api_game_private_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6b, 0x67,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x6f, 0x0a, 0x20, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b,
	0x79, 0x46, 0x61, 0x72, 0x6d, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x65, 0x71, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x49, 0x64, 0x73,
	0x22, 0x60, 0x0a, 0x20, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63,
	0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d,
	0x73, 0x67, 0x22, 0x78, 0x0a, 0x1b, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73,
	0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4f,
	0x70, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x77, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5b, 0x0a, 0x1b,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x22, 0xa9, 0x01, 0x0a, 0x20, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d,
	0x4c, 0x75, 0x63, 0x6b, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x15,
	0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x65, 0x71, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x6c, 0x75, 0x63, 0x6b, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6a,
	0x6f, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x6a,
	0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x6c, 0x6f, 0x77,
	0x65, 0x72, 0x4e, 0x75, 0x6d, 0x22, 0x60, 0x0a, 0x20, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d,
	0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4c, 0x75, 0x63, 0x6b, 0x79,
	0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x22, 0x58, 0x0a, 0x16, 0x4c, 0x75, 0x63, 0x6b, 0x79,
	0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xa0, 0x01, 0x0a, 0x22, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c,
	0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x73, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x65, 0x71, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0b, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6b, 0x67,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f, 0x41, 0x6e, 0x69,
	0x6d, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x62, 0x0a, 0x22, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73,
	0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f, 0x41, 0x6e, 0x69, 0x6d,
	0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x22, 0x48, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x22, 0xd8, 0x01, 0x0a, 0x08, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6f,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x61, 0x63, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x35, 0x0a, 0x06, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x06, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x44, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6b,
	0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x48, 0x0a,
	0x1d, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x15,
	0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x97, 0x02, 0x0a, 0x1d, 0x53, 0x75, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x61, 0x6e, 0x5f, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x67,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6d, 0x61, 0x6e, 0x41,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x40, 0x0a, 0x0c, 0x77, 0x6f, 0x6d, 0x61, 0x6e, 0x5f, 0x61,
	0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x67,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x77, 0x6f, 0x6d, 0x61,
	0x6e, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x08, 0x6b, 0x74, 0x76, 0x5f, 0x72,
	0x6f, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x67, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x6b, 0x74, 0x76, 0x52, 0x6f, 0x6f,
	0x6d, 0x22, 0x5a, 0x0a, 0x1f, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e,
	0x63, 0x69, 0x6e, 0x67, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x72,
	0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x81, 0x02,
	0x0a, 0x1f, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e,
	0x67, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x12,
	0x62, 0x0a, 0x0a, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x2a, 0xa1, 0x01, 0x0a, 0x0e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x4f,
	0x4f, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x52, 0x49, 0x45, 0x4e, 0x44, 0x5f, 0x4b, 0x54, 0x56, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x4f, 0x4f, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4b, 0x54, 0x56, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x54, 0x49, 0x4c, 0x5f,
	0x4b, 0x54, 0x56, 0x10, 0x04, 0x32, 0xd0, 0x09, 0x0a, 0x0e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x41, 0x70, 0x69, 0x12, 0xb4, 0x01, 0x0a, 0x18, 0x4e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x2f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0xc9, 0x01, 0x0a, 0x1d, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63,
	0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x35, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73,
	0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72,
	0x6d, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22,
	0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x3a, 0x01, 0x2a, 0x22, 0x2f, 0x2f, 0x6d, 0x69, 0x6e,
	0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f,
	0x6d, 0x73, 0x67, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x5f, 0x66, 0x61, 0x72, 0x6d, 0x2f, 0x61,
	0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0xc9, 0x01, 0x0a, 0x1d,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61,
	0x72, 0x6d, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x35, 0x2e,
	0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63,
	0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4c, 0x75, 0x63,
	0x6b, 0x79, 0x4d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x3a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x34, 0x3a, 0x01, 0x2a, 0x22, 0x2f, 0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x2f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x2f,
	0x6c, 0x75, 0x63, 0x6b, 0x79, 0x5f, 0x66, 0x61, 0x72, 0x6d, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79,
	0x5f, 0x6d, 0x6f, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0xd2, 0x01, 0x0a, 0x1f, 0x4e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f,
	0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x6b, 0x67,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79,
	0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x37, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x4d, 0x73, 0x67, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x46, 0x61, 0x72, 0x6d, 0x4e, 0x6f, 0x41,
	0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x3d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x37, 0x3a, 0x01, 0x2a, 0x22, 0x32, 0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x6d, 0x73,
	0x67, 0x2f, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x5f, 0x66, 0x61, 0x72, 0x6d, 0x2f, 0x6e, 0x6f, 0x5f,
	0x61, 0x6e, 0x69, 0x6d, 0x61, 0x6c, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0xc7, 0x01, 0x0a,
	0x1a, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x2e, 0x6b, 0x67,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e,
	0x67, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x1a,
	0x32, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61,
	0x6e, 0x63, 0x69, 0x6e, 0x67, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x73, 0x70, 0x22, 0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3b, 0x3a, 0x01, 0x2a, 0x22, 0x36,
	0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x2f, 0x73, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x64, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x6f, 0x6f,
	0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x12, 0xcf, 0x01, 0x0a, 0x1c, 0x53, 0x75, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x2e, 0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x47, 0x65, 0x74,
	0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x34, 0x2e,
	0x6b, 0x67, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x61, 0x6e, 0x63,
	0x69, 0x6e, 0x67, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x73, 0x70, 0x22, 0x43, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3d, 0x3a, 0x01, 0x2a, 0x22, 0x38,
	0x2f, 0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x67, 0x2f, 0x73, 0x75, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x64, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x76,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x48, 0x5a, 0x46, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61,
	0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_private_api_game_private_api_proto_rawDescOnce sync.Once
	file_pb_game_private_api_game_private_api_proto_rawDescData = file_pb_game_private_api_game_private_api_proto_rawDesc
)

func file_pb_game_private_api_game_private_api_proto_rawDescGZIP() []byte {
	file_pb_game_private_api_game_private_api_proto_rawDescOnce.Do(func() {
		file_pb_game_private_api_game_private_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_private_api_game_private_api_proto_rawDescData)
	})
	return file_pb_game_private_api_game_private_api_proto_rawDescData
}

var file_pb_game_private_api_game_private_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_game_private_api_game_private_api_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_pb_game_private_api_game_private_api_proto_goTypes = []interface{}{
	(RoomSourceType)(0),                        // 0: kg.game_private_api.RoomSourceType
	(*NormalMsgLuckyFarmAnimalEventReq)(nil),   // 1: kg.game_private_api.NormalMsgLuckyFarmAnimalEventReq
	(*NormalMsgLuckyFarmAnimalEventRsp)(nil),   // 2: kg.game_private_api.NormalMsgLuckyFarmAnimalEventRsp
	(*NormalMsgUserSwitchEventReq)(nil),        // 3: kg.game_private_api.NormalMsgUserSwitchEventReq
	(*NormalMsgUserSwitchEventRsp)(nil),        // 4: kg.game_private_api.NormalMsgUserSwitchEventRsp
	(*NormalMsgLuckyFarmLuckyMomentReq)(nil),   // 5: kg.game_private_api.NormalMsgLuckyFarmLuckyMomentReq
	(*NormalMsgLuckyFarmLuckyMomentRsp)(nil),   // 6: kg.game_private_api.NormalMsgLuckyFarmLuckyMomentRsp
	(*LuckyFarmNoAnimalCount)(nil),             // 7: kg.game_private_api.LuckyFarmNoAnimalCount
	(*NormalMsgLuckyFarmNoAnimalEventReq)(nil), // 8: kg.game_private_api.NormalMsgLuckyFarmNoAnimalEventReq
	(*NormalMsgLuckyFarmNoAnimalEventRsp)(nil), // 9: kg.game_private_api.NormalMsgLuckyFarmNoAnimalEventRsp
	(*UserInfo)(nil),                           // 10: kg.game_private_api.UserInfo
	(*RoomInfo)(nil),                           // 11: kg.game_private_api.RoomInfo
	(*SurpriseDancingGetRoomDataReq)(nil),      // 12: kg.game_private_api.SurpriseDancingGetRoomDataReq
	(*SurpriseDancingGetRoomDataRsp)(nil),      // 13: kg.game_private_api.SurpriseDancingGetRoomDataRsp
	(*SurpriseDancingGetLiveStatusReq)(nil),    // 14: kg.game_private_api.SurpriseDancingGetLiveStatusReq
	(*SurpriseDancingGetLiveStatusRsp)(nil),    // 15: kg.game_private_api.SurpriseDancingGetLiveStatusRsp
	nil,                                        // 16: kg.game_private_api.SurpriseDancingGetLiveStatusRsp.MapStatusEntry
}
var file_pb_game_private_api_game_private_api_proto_depIdxs = []int32{
	7,  // 0: kg.game_private_api.NormalMsgLuckyFarmNoAnimalEventReq.animal_info:type_name -> kg.game_private_api.LuckyFarmNoAnimalCount
	10, // 1: kg.game_private_api.RoomInfo.anchor:type_name -> kg.game_private_api.UserInfo
	0,  // 2: kg.game_private_api.RoomInfo.source_type:type_name -> kg.game_private_api.RoomSourceType
	11, // 3: kg.game_private_api.SurpriseDancingGetRoomDataRsp.man_anchor:type_name -> kg.game_private_api.RoomInfo
	11, // 4: kg.game_private_api.SurpriseDancingGetRoomDataRsp.woman_anchor:type_name -> kg.game_private_api.RoomInfo
	11, // 5: kg.game_private_api.SurpriseDancingGetRoomDataRsp.ktv_room:type_name -> kg.game_private_api.RoomInfo
	16, // 6: kg.game_private_api.SurpriseDancingGetLiveStatusRsp.map_status:type_name -> kg.game_private_api.SurpriseDancingGetLiveStatusRsp.MapStatusEntry
	3,  // 7: kg.game_private_api.GamePrivateApi.NormalMsgUserSwitchEvent:input_type -> kg.game_private_api.NormalMsgUserSwitchEventReq
	1,  // 8: kg.game_private_api.GamePrivateApi.NormalMsgLuckyFarmAnimalEvent:input_type -> kg.game_private_api.NormalMsgLuckyFarmAnimalEventReq
	5,  // 9: kg.game_private_api.GamePrivateApi.NormalMsgLuckyFarmLuckyMoment:input_type -> kg.game_private_api.NormalMsgLuckyFarmLuckyMomentReq
	8,  // 10: kg.game_private_api.GamePrivateApi.NormalMsgLuckyFarmNoAnimalEvent:input_type -> kg.game_private_api.NormalMsgLuckyFarmNoAnimalEventReq
	12, // 11: kg.game_private_api.GamePrivateApi.SurpriseDancingGetRoomData:input_type -> kg.game_private_api.SurpriseDancingGetRoomDataReq
	14, // 12: kg.game_private_api.GamePrivateApi.SurpriseDancingGetLiveStatus:input_type -> kg.game_private_api.SurpriseDancingGetLiveStatusReq
	4,  // 13: kg.game_private_api.GamePrivateApi.NormalMsgUserSwitchEvent:output_type -> kg.game_private_api.NormalMsgUserSwitchEventRsp
	2,  // 14: kg.game_private_api.GamePrivateApi.NormalMsgLuckyFarmAnimalEvent:output_type -> kg.game_private_api.NormalMsgLuckyFarmAnimalEventRsp
	6,  // 15: kg.game_private_api.GamePrivateApi.NormalMsgLuckyFarmLuckyMoment:output_type -> kg.game_private_api.NormalMsgLuckyFarmLuckyMomentRsp
	9,  // 16: kg.game_private_api.GamePrivateApi.NormalMsgLuckyFarmNoAnimalEvent:output_type -> kg.game_private_api.NormalMsgLuckyFarmNoAnimalEventRsp
	13, // 17: kg.game_private_api.GamePrivateApi.SurpriseDancingGetRoomData:output_type -> kg.game_private_api.SurpriseDancingGetRoomDataRsp
	15, // 18: kg.game_private_api.GamePrivateApi.SurpriseDancingGetLiveStatus:output_type -> kg.game_private_api.SurpriseDancingGetLiveStatusRsp
	13, // [13:19] is the sub-list for method output_type
	7,  // [7:13] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_pb_game_private_api_game_private_api_proto_init() }
func file_pb_game_private_api_game_private_api_proto_init() {
	if File_pb_game_private_api_game_private_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_private_api_game_private_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgLuckyFarmAnimalEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgLuckyFarmAnimalEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgUserSwitchEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgUserSwitchEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgLuckyFarmLuckyMomentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgLuckyFarmLuckyMomentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LuckyFarmNoAnimalCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgLuckyFarmNoAnimalEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalMsgLuckyFarmNoAnimalEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurpriseDancingGetRoomDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurpriseDancingGetRoomDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurpriseDancingGetLiveStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_private_api_game_private_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurpriseDancingGetLiveStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_private_api_game_private_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_private_api_game_private_api_proto_goTypes,
		DependencyIndexes: file_pb_game_private_api_game_private_api_proto_depIdxs,
		EnumInfos:         file_pb_game_private_api_game_private_api_proto_enumTypes,
		MessageInfos:      file_pb_game_private_api_game_private_api_proto_msgTypes,
	}.Build()
	File_pb_game_private_api_game_private_api_proto = out.File
	file_pb_game_private_api_game_private_api_proto_rawDesc = nil
	file_pb_game_private_api_game_private_api_proto_goTypes = nil
	file_pb_game_private_api_game_private_api_proto_depIdxs = nil
}
