// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/targeting_consumer/consumer.proto

package targeting_consumer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/uni_pay/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserAssetChangeLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId  string          `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	AppId   string          `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Balance map[int64]int64 `protobuf:"bytes,17,rep,name=balance,proto3" json:"balance,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *UserAssetChangeLog) Reset() {
	*x = UserAssetChangeLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAssetChangeLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAssetChangeLog) ProtoMessage() {}

func (x *UserAssetChangeLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAssetChangeLog.ProtoReflect.Descriptor instead.
func (*UserAssetChangeLog) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *UserAssetChangeLog) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *UserAssetChangeLog) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *UserAssetChangeLog) GetBalance() map[int64]int64 {
	if x != nil {
		return x.Balance
	}
	return nil
}

type ConsumeUserAssetChangeLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId string              `protobuf:"bytes,1,opt,name=messageId,proto3" json:"messageId,omitempty"`
	ChangeLog *UserAssetChangeLog `protobuf:"bytes,2,opt,name=changeLog,proto3" json:"changeLog,omitempty"`
}

func (x *ConsumeUserAssetChangeLogReq) Reset() {
	*x = ConsumeUserAssetChangeLogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeUserAssetChangeLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUserAssetChangeLogReq) ProtoMessage() {}

func (x *ConsumeUserAssetChangeLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUserAssetChangeLogReq.ProtoReflect.Descriptor instead.
func (*ConsumeUserAssetChangeLogReq) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ConsumeUserAssetChangeLogReq) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ConsumeUserAssetChangeLogReq) GetChangeLog() *UserAssetChangeLog {
	if x != nil {
		return x.ChangeLog
	}
	return nil
}

type ConsumeUserAssetChangeLogRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConsumeUserAssetChangeLogRsp) Reset() {
	*x = ConsumeUserAssetChangeLogRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeUserAssetChangeLogRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUserAssetChangeLogRsp) ProtoMessage() {}

func (x *ConsumeUserAssetChangeLogRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUserAssetChangeLogRsp.ProtoReflect.Descriptor instead.
func (*ConsumeUserAssetChangeLogRsp) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{2}
}

type TmeEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId    string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	EventType uint32 `protobuf:"varint,3,opt,name=eventType,proto3" json:"eventType,omitempty"`
	EventId   string `protobuf:"bytes,4,opt,name=eventId,proto3" json:"eventId,omitempty"`
	EventInfo string `protobuf:"bytes,10,opt,name=eventInfo,proto3" json:"eventInfo,omitempty"`
}

func (x *TmeEvent) Reset() {
	*x = TmeEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmeEvent) ProtoMessage() {}

func (x *TmeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmeEvent.ProtoReflect.Descriptor instead.
func (*TmeEvent) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *TmeEvent) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TmeEvent) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TmeEvent) GetEventType() uint32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *TmeEvent) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *TmeEvent) GetEventInfo() string {
	if x != nil {
		return x.EventInfo
	}
	return ""
}

type ConsumeTmeEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId string    `protobuf:"bytes,1,opt,name=messageId,proto3" json:"messageId,omitempty"`
	TmeEvent  *TmeEvent `protobuf:"bytes,2,opt,name=tmeEvent,proto3" json:"tmeEvent,omitempty"`
}

func (x *ConsumeTmeEventReq) Reset() {
	*x = ConsumeTmeEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeTmeEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeTmeEventReq) ProtoMessage() {}

func (x *ConsumeTmeEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeTmeEventReq.ProtoReflect.Descriptor instead.
func (*ConsumeTmeEventReq) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{4}
}

func (x *ConsumeTmeEventReq) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ConsumeTmeEventReq) GetTmeEvent() *TmeEvent {
	if x != nil {
		return x.TmeEvent
	}
	return nil
}

type ConsumeTmeEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConsumeTmeEventRsp) Reset() {
	*x = ConsumeTmeEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeTmeEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeTmeEventRsp) ProtoMessage() {}

func (x *ConsumeTmeEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeTmeEventRsp.ProtoReflect.Descriptor instead.
func (*ConsumeTmeEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{5}
}

type OpenPayFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string                   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId      string                   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ConsumeInfo *OpenPayFlow_ConsumeInfo `protobuf:"bytes,3,opt,name=consumeInfo,proto3" json:"consumeInfo,omitempty"`
	ConsumeId   string                   `protobuf:"bytes,4,opt,name=consumeId,proto3" json:"consumeId,omitempty"`
	Timestamp   int64                    `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *OpenPayFlow) Reset() {
	*x = OpenPayFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenPayFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenPayFlow) ProtoMessage() {}

func (x *OpenPayFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenPayFlow.ProtoReflect.Descriptor instead.
func (*OpenPayFlow) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{6}
}

func (x *OpenPayFlow) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *OpenPayFlow) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *OpenPayFlow) GetConsumeInfo() *OpenPayFlow_ConsumeInfo {
	if x != nil {
		return x.ConsumeInfo
	}
	return nil
}

func (x *OpenPayFlow) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *OpenPayFlow) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type ConsumeOpenPayFlowReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId string          `protobuf:"bytes,1,opt,name=messageId,proto3" json:"messageId,omitempty"`
	PayFlow   *common.PayFlow `protobuf:"bytes,2,opt,name=payFlow,proto3" json:"payFlow,omitempty"`
}

func (x *ConsumeOpenPayFlowReq) Reset() {
	*x = ConsumeOpenPayFlowReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeOpenPayFlowReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeOpenPayFlowReq) ProtoMessage() {}

func (x *ConsumeOpenPayFlowReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeOpenPayFlowReq.ProtoReflect.Descriptor instead.
func (*ConsumeOpenPayFlowReq) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{7}
}

func (x *ConsumeOpenPayFlowReq) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ConsumeOpenPayFlowReq) GetPayFlow() *common.PayFlow {
	if x != nil {
		return x.PayFlow
	}
	return nil
}

type ConsumeOpenPayFlowRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConsumeOpenPayFlowRsp) Reset() {
	*x = ConsumeOpenPayFlowRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeOpenPayFlowRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeOpenPayFlowRsp) ProtoMessage() {}

func (x *ConsumeOpenPayFlowRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeOpenPayFlowRsp.ProtoReflect.Descriptor instead.
func (*ConsumeOpenPayFlowRsp) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{8}
}

type OpenPayFlow_ConsumeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount uint32 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *OpenPayFlow_ConsumeInfo) Reset() {
	*x = OpenPayFlow_ConsumeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenPayFlow_ConsumeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenPayFlow_ConsumeInfo) ProtoMessage() {}

func (x *OpenPayFlow_ConsumeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_targeting_consumer_consumer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenPayFlow_ConsumeInfo.ProtoReflect.Descriptor instead.
func (*OpenPayFlow_ConsumeInfo) Descriptor() ([]byte, []int) {
	return file_pb_targeting_consumer_consumer_proto_rawDescGZIP(), []int{6, 0}
}

func (x *OpenPayFlow_ConsumeInfo) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

var File_pb_targeting_consumer_consumer_proto protoreflect.FileDescriptor

var file_pb_targeting_consumer_consumer_proto_rawDesc = []byte{
	0x0a, 0x24, 0x70, 0x62, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x75,
	0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcd, 0x01, 0x0a, 0x12, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f,
	0x67, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x4d, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x3a,
	0x0a, 0x0c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x82, 0x01, 0x0a, 0x1c, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x09, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4c, 0x6f, 0x67, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x22,
	0x1e, 0x0a, 0x1c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x73, 0x70, 0x22,
	0x8e, 0x01, 0x0a, 0x08, 0x54, 0x6d, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x6c, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x6d, 0x65, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x74, 0x6d, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x6d, 0x65, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x74, 0x6d, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x22, 0x14,
	0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x6d, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x22, 0xed, 0x01, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79,
	0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x4d, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x4f, 0x70, 0x65,
	0x6e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0x25, 0x0a,
	0x0b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x61, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f,
	0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a,
	0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x70,
	0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75,
	0x6e, 0x69, 0x5f, 0x70, 0x61, 0x79, 0x2e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70,
	0x32, 0xda, 0x02, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x7f, 0x0a,
	0x19, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x30, 0x2e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x73, 0x70, 0x12, 0x61,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x6d, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x26, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x6d,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x6d, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x6a, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f, 0x70, 0x65, 0x6e,
	0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x29, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x71, 0x1a, 0x29, 0x2e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4f,
	0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x73, 0x70, 0x42, 0x4a, 0x5a,
	0x48, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_targeting_consumer_consumer_proto_rawDescOnce sync.Once
	file_pb_targeting_consumer_consumer_proto_rawDescData = file_pb_targeting_consumer_consumer_proto_rawDesc
)

func file_pb_targeting_consumer_consumer_proto_rawDescGZIP() []byte {
	file_pb_targeting_consumer_consumer_proto_rawDescOnce.Do(func() {
		file_pb_targeting_consumer_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_targeting_consumer_consumer_proto_rawDescData)
	})
	return file_pb_targeting_consumer_consumer_proto_rawDescData
}

var file_pb_targeting_consumer_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pb_targeting_consumer_consumer_proto_goTypes = []interface{}{
	(*UserAssetChangeLog)(nil),           // 0: targeting_consumer.UserAssetChangeLog
	(*ConsumeUserAssetChangeLogReq)(nil), // 1: targeting_consumer.ConsumeUserAssetChangeLogReq
	(*ConsumeUserAssetChangeLogRsp)(nil), // 2: targeting_consumer.ConsumeUserAssetChangeLogRsp
	(*TmeEvent)(nil),                     // 3: targeting_consumer.TmeEvent
	(*ConsumeTmeEventReq)(nil),           // 4: targeting_consumer.ConsumeTmeEventReq
	(*ConsumeTmeEventRsp)(nil),           // 5: targeting_consumer.ConsumeTmeEventRsp
	(*OpenPayFlow)(nil),                  // 6: targeting_consumer.OpenPayFlow
	(*ConsumeOpenPayFlowReq)(nil),        // 7: targeting_consumer.ConsumeOpenPayFlowReq
	(*ConsumeOpenPayFlowRsp)(nil),        // 8: targeting_consumer.ConsumeOpenPayFlowRsp
	nil,                                  // 9: targeting_consumer.UserAssetChangeLog.BalanceEntry
	(*OpenPayFlow_ConsumeInfo)(nil),      // 10: targeting_consumer.OpenPayFlow.ConsumeInfo
	(*common.PayFlow)(nil),               // 11: uni_pay.PayFlow
}
var file_pb_targeting_consumer_consumer_proto_depIdxs = []int32{
	9,  // 0: targeting_consumer.UserAssetChangeLog.balance:type_name -> targeting_consumer.UserAssetChangeLog.BalanceEntry
	0,  // 1: targeting_consumer.ConsumeUserAssetChangeLogReq.changeLog:type_name -> targeting_consumer.UserAssetChangeLog
	3,  // 2: targeting_consumer.ConsumeTmeEventReq.tmeEvent:type_name -> targeting_consumer.TmeEvent
	10, // 3: targeting_consumer.OpenPayFlow.consumeInfo:type_name -> targeting_consumer.OpenPayFlow.ConsumeInfo
	11, // 4: targeting_consumer.ConsumeOpenPayFlowReq.payFlow:type_name -> uni_pay.PayFlow
	1,  // 5: targeting_consumer.Consumer.ConsumeUserAssetChangeLog:input_type -> targeting_consumer.ConsumeUserAssetChangeLogReq
	4,  // 6: targeting_consumer.Consumer.ConsumeTmeEvent:input_type -> targeting_consumer.ConsumeTmeEventReq
	7,  // 7: targeting_consumer.Consumer.ConsumeOpenPayFlow:input_type -> targeting_consumer.ConsumeOpenPayFlowReq
	2,  // 8: targeting_consumer.Consumer.ConsumeUserAssetChangeLog:output_type -> targeting_consumer.ConsumeUserAssetChangeLogRsp
	5,  // 9: targeting_consumer.Consumer.ConsumeTmeEvent:output_type -> targeting_consumer.ConsumeTmeEventRsp
	8,  // 10: targeting_consumer.Consumer.ConsumeOpenPayFlow:output_type -> targeting_consumer.ConsumeOpenPayFlowRsp
	8,  // [8:11] is the sub-list for method output_type
	5,  // [5:8] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pb_targeting_consumer_consumer_proto_init() }
func file_pb_targeting_consumer_consumer_proto_init() {
	if File_pb_targeting_consumer_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_targeting_consumer_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAssetChangeLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeUserAssetChangeLogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeUserAssetChangeLogRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmeEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeTmeEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeTmeEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenPayFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeOpenPayFlowReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeOpenPayFlowRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_targeting_consumer_consumer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenPayFlow_ConsumeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_targeting_consumer_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_targeting_consumer_consumer_proto_goTypes,
		DependencyIndexes: file_pb_targeting_consumer_consumer_proto_depIdxs,
		MessageInfos:      file_pb_targeting_consumer_consumer_proto_msgTypes,
	}.Build()
	File_pb_targeting_consumer_consumer_proto = out.File
	file_pb_targeting_consumer_consumer_proto_rawDesc = nil
	file_pb_targeting_consumer_consumer_proto_goTypes = nil
	file_pb_targeting_consumer_consumer_proto_depIdxs = nil
}
