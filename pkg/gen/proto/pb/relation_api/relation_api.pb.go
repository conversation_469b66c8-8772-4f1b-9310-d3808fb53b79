// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/relation_api/relation_api.proto

package relation_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FollowOptType int32

const (
	FollowOptType_FOLLOW_TYPE_NONE FollowOptType = 0
	FollowOptType_FOLLOW_TYPE_ADD  FollowOptType = 1
	FollowOptType_FOLLOW_TYPE_SUB  FollowOptType = 2
)

// Enum value maps for FollowOptType.
var (
	FollowOptType_name = map[int32]string{
		0: "FOLLOW_TYPE_NONE",
		1: "FOLLOW_TYPE_ADD",
		2: "FOLLOW_TYPE_SUB",
	}
	FollowOptType_value = map[string]int32{
		"FOLLOW_TYPE_NONE": 0,
		"FOLLOW_TYPE_ADD":  1,
		"FOLLOW_TYPE_SUB":  2,
	}
)

func (x FollowOptType) Enum() *FollowOptType {
	p := new(FollowOptType)
	*p = x
	return p
}

func (x FollowOptType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FollowOptType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_relation_api_relation_api_proto_enumTypes[0].Descriptor()
}

func (FollowOptType) Type() protoreflect.EnumType {
	return &file_pb_relation_api_relation_api_proto_enumTypes[0]
}

func (x FollowOptType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FollowOptType.Descriptor instead.
func (FollowOptType) EnumDescriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{0}
}

type FollowResult int32

const (
	FollowResult_FOLLOW_TYPE_SUCC            FollowResult = 0
	FollowResult_FOLLOW_TYPE_FAIL            FollowResult = 1
	FollowResult_FOLLOW_TYPE_FAIL_SELF_BLOCK FollowResult = 2 // 无法关注, 因为你拉黑了别人
	FollowResult_FOLLOW_TYPE_SAFEY_REFUSE    FollowResult = 3 // 安全审核失败
	FollowResult_FOLLOW_TYPE_USER_FILTER     FollowResult = 4 // 黑产标记
)

// Enum value maps for FollowResult.
var (
	FollowResult_name = map[int32]string{
		0: "FOLLOW_TYPE_SUCC",
		1: "FOLLOW_TYPE_FAIL",
		2: "FOLLOW_TYPE_FAIL_SELF_BLOCK",
		3: "FOLLOW_TYPE_SAFEY_REFUSE",
		4: "FOLLOW_TYPE_USER_FILTER",
	}
	FollowResult_value = map[string]int32{
		"FOLLOW_TYPE_SUCC":            0,
		"FOLLOW_TYPE_FAIL":            1,
		"FOLLOW_TYPE_FAIL_SELF_BLOCK": 2,
		"FOLLOW_TYPE_SAFEY_REFUSE":    3,
		"FOLLOW_TYPE_USER_FILTER":     4,
	}
)

func (x FollowResult) Enum() *FollowResult {
	p := new(FollowResult)
	*p = x
	return p
}

func (x FollowResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FollowResult) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_relation_api_relation_api_proto_enumTypes[1].Descriptor()
}

func (FollowResult) Type() protoreflect.EnumType {
	return &file_pb_relation_api_relation_api_proto_enumTypes[1]
}

func (x FollowResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FollowResult.Descriptor instead.
func (FollowResult) EnumDescriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{1}
}

type RelatioMask int32

const (
	RelatioMask_RELATION_MASK_ALL    RelatioMask = 0 //所有关系
	RelatioMask_RELATION_MASK_FOLLOW RelatioMask = 1 //拉取关注
)

// Enum value maps for RelatioMask.
var (
	RelatioMask_name = map[int32]string{
		0: "RELATION_MASK_ALL",
		1: "RELATION_MASK_FOLLOW",
	}
	RelatioMask_value = map[string]int32{
		"RELATION_MASK_ALL":    0,
		"RELATION_MASK_FOLLOW": 1,
	}
)

func (x RelatioMask) Enum() *RelatioMask {
	p := new(RelatioMask)
	*p = x
	return p
}

func (x RelatioMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelatioMask) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_relation_api_relation_api_proto_enumTypes[2].Descriptor()
}

func (RelatioMask) Type() protoreflect.EnumType {
	return &file_pb_relation_api_relation_api_proto_enumTypes[2]
}

func (x RelatioMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelatioMask.Descriptor instead.
func (RelatioMask) EnumDescriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{2}
}

type RelationType int32

const (
	RelationType_RELATION_NONE   RelationType = 0 //无关系
	RelationType_RELATION_FOLLOW RelationType = 1 //关注
)

// Enum value maps for RelationType.
var (
	RelationType_name = map[int32]string{
		0: "RELATION_NONE",
		1: "RELATION_FOLLOW",
	}
	RelationType_value = map[string]int32{
		"RELATION_NONE":   0,
		"RELATION_FOLLOW": 1,
	}
)

func (x RelationType) Enum() *RelationType {
	p := new(RelationType)
	*p = x
	return p
}

func (x RelationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_relation_api_relation_api_proto_enumTypes[3].Descriptor()
}

func (RelationType) Type() protoreflect.EnumType {
	return &file_pb_relation_api_relation_api_proto_enumTypes[3]
}

func (x RelationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationType.Descriptor instead.
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{3}
}

type FollowOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// open id 列表
	OpenIdList []string `protobuf:"bytes,1,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	// qua 设备qua
	Qua string `protobuf:"bytes,2,opt,name=qua,proto3" json:"qua,omitempty"`
	// deviceInfo
	DeviceInfo string `protobuf:"bytes,3,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
	// optType
	Type FollowOptType `protobuf:"varint,4,opt,name=type,proto3,enum=game.FollowOptType" json:"type,omitempty"`
	// source 业务来源id
	Source int64 `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *FollowOptReq) Reset() {
	*x = FollowOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_relation_api_relation_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowOptReq) ProtoMessage() {}

func (x *FollowOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_relation_api_relation_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowOptReq.ProtoReflect.Descriptor instead.
func (*FollowOptReq) Descriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{0}
}

func (x *FollowOptReq) GetOpenIdList() []string {
	if x != nil {
		return x.OpenIdList
	}
	return nil
}

func (x *FollowOptReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *FollowOptReq) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

func (x *FollowOptReq) GetType() FollowOptType {
	if x != nil {
		return x.Type
	}
	return FollowOptType_FOLLOW_TYPE_NONE
}

func (x *FollowOptReq) GetSource() int64 {
	if x != nil {
		return x.Source
	}
	return 0
}

type FollowInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result FollowResult `protobuf:"varint,1,opt,name=result,proto3,enum=game.FollowResult" json:"result,omitempty"`
	OpenId string       `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *FollowInfo) Reset() {
	*x = FollowInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_relation_api_relation_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowInfo) ProtoMessage() {}

func (x *FollowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_relation_api_relation_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowInfo.ProtoReflect.Descriptor instead.
func (*FollowInfo) Descriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{1}
}

func (x *FollowInfo) GetResult() FollowResult {
	if x != nil {
		return x.Result
	}
	return FollowResult_FOLLOW_TYPE_SUCC
}

func (x *FollowInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type FollowOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//key:openid
	Results map[string]*FollowInfo `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FollowOptRsp) Reset() {
	*x = FollowOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_relation_api_relation_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowOptRsp) ProtoMessage() {}

func (x *FollowOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_relation_api_relation_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowOptRsp.ProtoReflect.Descriptor instead.
func (*FollowOptRsp) Descriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{2}
}

func (x *FollowOptRsp) GetResults() map[string]*FollowInfo {
	if x != nil {
		return x.Results
	}
	return nil
}

type QueryRelationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// open id 列表
	OpenIdList []string `protobuf:"bytes,1,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	// mask
	Mask RelatioMask `protobuf:"varint,2,opt,name=mask,proto3,enum=game.RelatioMask" json:"mask,omitempty"`
}

func (x *QueryRelationReq) Reset() {
	*x = QueryRelationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_relation_api_relation_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRelationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRelationReq) ProtoMessage() {}

func (x *QueryRelationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_relation_api_relation_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRelationReq.ProtoReflect.Descriptor instead.
func (*QueryRelationReq) Descriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{3}
}

func (x *QueryRelationReq) GetOpenIdList() []string {
	if x != nil {
		return x.OpenIdList
	}
	return nil
}

func (x *QueryRelationReq) GetMask() RelatioMask {
	if x != nil {
		return x.Mask
	}
	return RelatioMask_RELATION_MASK_ALL
}

type RalationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   RelationType `protobuf:"varint,1,opt,name=type,proto3,enum=game.RelationType" json:"type,omitempty"`
	OpenId string       `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *RalationInfo) Reset() {
	*x = RalationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_relation_api_relation_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RalationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RalationInfo) ProtoMessage() {}

func (x *RalationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_relation_api_relation_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RalationInfo.ProtoReflect.Descriptor instead.
func (*RalationInfo) Descriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{4}
}

func (x *RalationInfo) GetType() RelationType {
	if x != nil {
		return x.Type
	}
	return RelationType_RELATION_NONE
}

func (x *RalationInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QueryRelationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//key:openid
	Ralations map[string]*RalationInfo `protobuf:"bytes,1,rep,name=ralations,proto3" json:"ralations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *QueryRelationRsp) Reset() {
	*x = QueryRelationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_relation_api_relation_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRelationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRelationRsp) ProtoMessage() {}

func (x *QueryRelationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_relation_api_relation_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRelationRsp.ProtoReflect.Descriptor instead.
func (*QueryRelationRsp) Descriptor() ([]byte, []int) {
	return file_pb_relation_api_relation_api_proto_rawDescGZIP(), []int{5}
}

func (x *QueryRelationRsp) GetRalations() map[string]*RalationInfo {
	if x != nil {
		return x.Ralations
	}
	return nil
}

var File_pb_relation_api_relation_api_proto protoreflect.FileDescriptor

var file_pb_relation_api_relation_api_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x0c, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x71, 0x75, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x51, 0x0a, 0x0a, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x22, 0x97, 0x01, 0x0a, 0x0c, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a,
	0x4c, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5b, 0x0a,
	0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x4d, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x22, 0x4f, 0x0a, 0x0c, 0x52, 0x61,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0xa9, 0x01, 0x0a, 0x10,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x43, 0x0a, 0x09, 0x72, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x61, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x72, 0x61, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x50, 0x0a, 0x0e, 0x52, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x4f, 0x0a, 0x0d, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x4f, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x4f, 0x4c, 0x4c,
	0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x13,
	0x0a, 0x0f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x44,
	0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x10, 0x02, 0x2a, 0x96, 0x01, 0x0a, 0x0c, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x4f, 0x4c,
	0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x42,
	0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x59, 0x5f, 0x52, 0x45, 0x46, 0x55,
	0x53, 0x45, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x10,
	0x04, 0x2a, 0x3e, 0x0a, 0x0b, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x4c, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x10,
	0x01, 0x2a, 0x36, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x32, 0x83, 0x01, 0x0a, 0x0b, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x33, 0x0a, 0x09, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x12, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3f,
	0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x42,
	0x44, 0x5a, 0x42, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_relation_api_relation_api_proto_rawDescOnce sync.Once
	file_pb_relation_api_relation_api_proto_rawDescData = file_pb_relation_api_relation_api_proto_rawDesc
)

func file_pb_relation_api_relation_api_proto_rawDescGZIP() []byte {
	file_pb_relation_api_relation_api_proto_rawDescOnce.Do(func() {
		file_pb_relation_api_relation_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_relation_api_relation_api_proto_rawDescData)
	})
	return file_pb_relation_api_relation_api_proto_rawDescData
}

var file_pb_relation_api_relation_api_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_relation_api_relation_api_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_relation_api_relation_api_proto_goTypes = []interface{}{
	(FollowOptType)(0),       // 0: game.FollowOptType
	(FollowResult)(0),        // 1: game.FollowResult
	(RelatioMask)(0),         // 2: game.RelatioMask
	(RelationType)(0),        // 3: game.RelationType
	(*FollowOptReq)(nil),     // 4: game.FollowOptReq
	(*FollowInfo)(nil),       // 5: game.FollowInfo
	(*FollowOptRsp)(nil),     // 6: game.FollowOptRsp
	(*QueryRelationReq)(nil), // 7: game.QueryRelationReq
	(*RalationInfo)(nil),     // 8: game.RalationInfo
	(*QueryRelationRsp)(nil), // 9: game.QueryRelationRsp
	nil,                      // 10: game.FollowOptRsp.ResultsEntry
	nil,                      // 11: game.QueryRelationRsp.RalationsEntry
}
var file_pb_relation_api_relation_api_proto_depIdxs = []int32{
	0,  // 0: game.FollowOptReq.type:type_name -> game.FollowOptType
	1,  // 1: game.FollowInfo.result:type_name -> game.FollowResult
	10, // 2: game.FollowOptRsp.results:type_name -> game.FollowOptRsp.ResultsEntry
	2,  // 3: game.QueryRelationReq.mask:type_name -> game.RelatioMask
	3,  // 4: game.RalationInfo.type:type_name -> game.RelationType
	11, // 5: game.QueryRelationRsp.ralations:type_name -> game.QueryRelationRsp.RalationsEntry
	5,  // 6: game.FollowOptRsp.ResultsEntry.value:type_name -> game.FollowInfo
	8,  // 7: game.QueryRelationRsp.RalationsEntry.value:type_name -> game.RalationInfo
	4,  // 8: game.RelationApi.FollowOpt:input_type -> game.FollowOptReq
	7,  // 9: game.RelationApi.QueryRelation:input_type -> game.QueryRelationReq
	6,  // 10: game.RelationApi.FollowOpt:output_type -> game.FollowOptRsp
	9,  // 11: game.RelationApi.QueryRelation:output_type -> game.QueryRelationRsp
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_pb_relation_api_relation_api_proto_init() }
func file_pb_relation_api_relation_api_proto_init() {
	if File_pb_relation_api_relation_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_relation_api_relation_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_relation_api_relation_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_relation_api_relation_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_relation_api_relation_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRelationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_relation_api_relation_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RalationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_relation_api_relation_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRelationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_relation_api_relation_api_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_relation_api_relation_api_proto_goTypes,
		DependencyIndexes: file_pb_relation_api_relation_api_proto_depIdxs,
		EnumInfos:         file_pb_relation_api_relation_api_proto_enumTypes,
		MessageInfos:      file_pb_relation_api_relation_api_proto_msgTypes,
	}.Build()
	File_pb_relation_api_relation_api_proto = out.File
	file_pb_relation_api_relation_api_proto_rawDesc = nil
	file_pb_relation_api_relation_api_proto_goTypes = nil
	file_pb_relation_api_relation_api_proto_depIdxs = nil
}
