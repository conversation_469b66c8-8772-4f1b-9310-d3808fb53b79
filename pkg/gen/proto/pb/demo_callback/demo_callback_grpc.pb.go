// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/demo_callback/demo_callback.proto

package demo_callback

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	reward_sender_callback "kugou_adapter_service/pkg/gen/proto/pb/reward_sender_callback"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	DemoCallback_RewardSenderCallback_FullMethodName = "/game.DemoCallback/RewardSenderCallback"
)

// DemoCallbackClient is the client API for DemoCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DemoCallbackClient interface {
	RewardSenderCallback(ctx context.Context, in *reward_sender_callback.SendGoodsCallbackReq, opts ...grpc.CallOption) (*reward_sender_callback.SendGoodsCallbackRsp, error)
}

type demoCallbackClient struct {
	cc grpc.ClientConnInterface
}

func NewDemoCallbackClient(cc grpc.ClientConnInterface) DemoCallbackClient {
	return &demoCallbackClient{cc}
}

func (c *demoCallbackClient) RewardSenderCallback(ctx context.Context, in *reward_sender_callback.SendGoodsCallbackReq, opts ...grpc.CallOption) (*reward_sender_callback.SendGoodsCallbackRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(reward_sender_callback.SendGoodsCallbackRsp)
	err := c.cc.Invoke(ctx, DemoCallback_RewardSenderCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DemoCallbackServer is the server API for DemoCallback service.
// All implementations should embed UnimplementedDemoCallbackServer
// for forward compatibility
type DemoCallbackServer interface {
	RewardSenderCallback(context.Context, *reward_sender_callback.SendGoodsCallbackReq) (*reward_sender_callback.SendGoodsCallbackRsp, error)
}

// UnimplementedDemoCallbackServer should be embedded to have forward compatible implementations.
type UnimplementedDemoCallbackServer struct {
}

func (UnimplementedDemoCallbackServer) RewardSenderCallback(context.Context, *reward_sender_callback.SendGoodsCallbackReq) (*reward_sender_callback.SendGoodsCallbackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RewardSenderCallback not implemented")
}

// UnsafeDemoCallbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DemoCallbackServer will
// result in compilation errors.
type UnsafeDemoCallbackServer interface {
	mustEmbedUnimplementedDemoCallbackServer()
}

func RegisterDemoCallbackServer(s grpc.ServiceRegistrar, srv DemoCallbackServer) {
	s.RegisterService(&DemoCallback_ServiceDesc, srv)
}

func _DemoCallback_RewardSenderCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reward_sender_callback.SendGoodsCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DemoCallbackServer).RewardSenderCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DemoCallback_RewardSenderCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DemoCallbackServer).RewardSenderCallback(ctx, req.(*reward_sender_callback.SendGoodsCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DemoCallback_ServiceDesc is the grpc.ServiceDesc for DemoCallback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DemoCallback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.DemoCallback",
	HandlerType: (*DemoCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RewardSenderCallback",
			Handler:    _DemoCallback_RewardSenderCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/demo_callback/demo_callback.proto",
}
