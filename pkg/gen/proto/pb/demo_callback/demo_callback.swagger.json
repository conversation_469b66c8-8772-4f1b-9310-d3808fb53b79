{"swagger": "2.0", "info": {"title": "pb/demo_callback/demo_callback.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game.DemoCallback/RewardSenderCallback": {"post": {"operationId": "DemoCallback_RewardSenderCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSendGoodsCallbackRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSendGoodsCallbackReq"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "definitions": {"gameSendGoodsCallbackReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "open id"}, "goodsId": {"type": "string", "format": "int64", "title": "物品 id"}, "goodsNum": {"type": "string", "format": "int64", "title": "物品数量"}, "billNo": {"type": "string", "title": "订单ID"}}}, "gameSendGoodsCallbackRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}