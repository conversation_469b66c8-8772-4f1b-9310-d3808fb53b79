{"swagger": "2.0", "info": {"title": "pb/petpk/notify/notify.proto", "version": "version not set"}, "tags": [{"name": "Notify"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/petpk.Notify/CallbackCheckSend": {"post": {"summary": "中台发福利礼包回调检查", "operationId": "Notify_CallbackCheckSend", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/callbackGiftPackageBusinessCheckSendRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/callbackGiftPackageBusinessCheckSendReq"}}], "tags": ["Notify"]}}, "/petpk.Notify/CallbackMBattleRevive": {"post": {"summary": "家族战神兽复活检查定时器回调", "operationId": "Notify_CallbackMBattleRevive", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkTimerCallbackRequest"}}], "tags": ["Notify"]}}, "/petpk.Notify/CallbackMBattleSettle": {"post": {"summary": "家族战-结算的定时器回调", "operationId": "Notify_CallbackMBattleSettle", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkTimerCallbackRequest"}}], "tags": ["Notify"]}}, "/petpk.Notify/CallbackMBattleSettlePending": {"post": {"summary": "家族战-结算中间定时器", "operationId": "Notify_CallbackMBattleSettlePending", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkTimerCallbackRequest"}}], "tags": ["Notify"]}}, "/petpk.Notify/DeliveryItem": {"post": {"summary": "游戏道具发货", "operationId": "Notify_DeliveryItem", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkDeliveryItemRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkDeliveryItemReq"}}], "tags": ["Notify"]}}, "/petpk.Notify/DeliveryItems": {"post": {"summary": "游戏道具批量发货", "operationId": "Notify_DeliveryItems", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkDeliveryItemsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkDeliveryItemsReq"}}], "tags": ["Notify"]}}, "/petpk.Notify/GetBattleStatus": {"post": {"summary": "读战斗结果及领奖状态", "operationId": "Notify_GetBattleStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkGetBattleStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkGetBattleStatusReq"}}], "tags": ["Notify"]}}}, "definitions": {"adapter_commonGameMiddleInfo": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "gameOpenId": {"type": "string"}, "uid": {"type": "string"}}, "title": "方式二 宿主平台游戏账号体系\n   必填参数：uid"}, "callbackCallInfo": {"type": "object", "properties": {"callBackCmd": {"type": "string"}}}, "callbackCheckInfo": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id"}, "giftPackageId": {"type": "string", "title": "礼包id"}, "num": {"type": "integer", "format": "int64", "title": "数量"}, "sendTs": {"type": "integer", "format": "int64", "title": "发放时间 秒"}, "reason": {"type": "string", "title": "发放原因"}, "extId": {"type": "string", "title": "业务方带过来的teaceid 用于链路跟踪，填抽奖ID、任务ID、活动ID"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传字段 对应发礼包传入的map_ext"}}, "title": "礼包发货check==================begin\n这里针对订单号需要校验：礼包id、发放人、发放数量是否匹配\n如果正确返回 pass = true\n如果错误返回 pass = false"}, "callbackGiftPackageBusinessCheckSendReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo"}, "checkInfo": {"$ref": "#/definitions/callbackCheckInfo"}, "callInfo": {"$ref": "#/definitions/callbackCallInfo"}}}, "callbackGiftPackageBusinessCheckSendRsp": {"type": "object", "properties": {"pass": {"type": "boolean", "title": "校验是否通过"}}}, "petpkDeliveryItemReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏ID"}, "uid": {"type": "string", "title": "用户ID"}, "orderId": {"type": "string", "title": "订单id"}, "itemID": {"type": "integer", "format": "int32", "title": "道具ID"}, "itemNum": {"type": "integer", "format": "int32", "title": "数量"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "ext扩展信息"}}}, "petpkDeliveryItemRsp": {"type": "object", "properties": {"added": {"type": "integer", "format": "int32", "title": "本次实际增加"}, "total": {"type": "integer", "format": "int32", "title": "上限"}}}, "petpkDeliveryItemsReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏ID"}, "uid": {"type": "string", "title": "用户ID"}, "orderId": {"type": "string", "title": "订单id"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkGItem"}, "title": "道具列表"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "ext扩展信息"}}}, "petpkDeliveryItemsRsp": {"type": "object", "properties": {"result": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkItemAddedResult"}}}}, "petpkGItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "title": "道具ID"}, "num": {"type": "integer", "format": "int32", "title": "道具数量"}}}, "petpkGetBattleStatusReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏ID"}, "uid": {"type": "string", "title": "用户ID"}, "roomId": {"type": "string", "title": "房间ID"}, "roundId": {"type": "string", "title": "轮次ID"}}}, "petpkGetBattleStatusRsp": {"type": "object", "properties": {"roomRank": {"type": "integer", "format": "int32", "title": "房间排名, -1:未参战, 1: 第一名"}, "roomClaimed": {"type": "integer", "format": "int32", "title": "房间奖励是否领取, -1: 未达领奖条件, 1:有奖可领, 2: 已领奖"}, "globalClaimed": {"type": "integer", "format": "int32", "title": "全局伤害奖励是否领取, -1: 未达领奖条件, 1:有奖可领, 2: 已领奖"}}}, "petpkItemAddedResult": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "title": "道具id"}, "added": {"type": "integer", "format": "int32", "title": "本次实际增加"}, "total": {"type": "integer", "format": "int32", "title": "余额"}}}, "petpkTimerCallbackRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "timerId": {"type": "string"}, "fireTime": {"type": "string", "format": "int64", "title": "触发时间 毫秒"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据"}}}, "petpkTimerCallbackResponse": {"type": "object", "properties": {"cancel": {"type": "boolean", "title": "用于周期定时器取消"}, "nextFireTime": {"type": "string", "format": "int64", "title": "用于一次性定时器指定下次触发时间"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据 不为 nil 则覆盖"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}