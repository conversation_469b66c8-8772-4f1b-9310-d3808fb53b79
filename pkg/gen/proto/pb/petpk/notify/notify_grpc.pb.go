// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/petpk/notify/notify.proto

package notify

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Notify_CallbackMBattleSettle_FullMethodName        = "/petpk.Notify/CallbackMBattleSettle"
	Notify_CallbackMBattleSettlePending_FullMethodName = "/petpk.Notify/CallbackMBattleSettlePending"
	Notify_CallbackMBattleRevive_FullMethodName        = "/petpk.Notify/CallbackMBattleRevive"
	Notify_DeliveryItem_FullMethodName                 = "/petpk.Notify/DeliveryItem"
	Notify_DeliveryItems_FullMethodName                = "/petpk.Notify/DeliveryItems"
	Notify_GetBattleStatus_FullMethodName              = "/petpk.Notify/GetBattleStatus"
	Notify_CallbackCheckSend_FullMethodName            = "/petpk.Notify/CallbackCheckSend"
)

// NotifyClient is the client API for Notify service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotifyClient interface {
	// 家族战-结算的定时器回调
	CallbackMBattleSettle(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error)
	// 家族战-结算中间定时器
	CallbackMBattleSettlePending(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error)
	// 家族战神兽复活检查定时器回调
	CallbackMBattleRevive(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error)
	// 游戏道具发货
	DeliveryItem(ctx context.Context, in *DeliveryItemReq, opts ...grpc.CallOption) (*DeliveryItemRsp, error)
	// 游戏道具批量发货
	DeliveryItems(ctx context.Context, in *DeliveryItemsReq, opts ...grpc.CallOption) (*DeliveryItemsRsp, error)
	// 读战斗结果及领奖状态
	GetBattleStatus(ctx context.Context, in *GetBattleStatusReq, opts ...grpc.CallOption) (*GetBattleStatusRsp, error)
	// 中台发福利礼包回调检查
	CallbackCheckSend(ctx context.Context, in *callback.GiftPackageBusinessCheckSendReq, opts ...grpc.CallOption) (*callback.GiftPackageBusinessCheckSendRsp, error)
}

type notifyClient struct {
	cc grpc.ClientConnInterface
}

func NewNotifyClient(cc grpc.ClientConnInterface) NotifyClient {
	return &notifyClient{cc}
}

func (c *notifyClient) CallbackMBattleSettle(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerCallbackResponse)
	err := c.cc.Invoke(ctx, Notify_CallbackMBattleSettle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) CallbackMBattleSettlePending(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerCallbackResponse)
	err := c.cc.Invoke(ctx, Notify_CallbackMBattleSettlePending_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) CallbackMBattleRevive(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerCallbackResponse)
	err := c.cc.Invoke(ctx, Notify_CallbackMBattleRevive_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) DeliveryItem(ctx context.Context, in *DeliveryItemReq, opts ...grpc.CallOption) (*DeliveryItemRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeliveryItemRsp)
	err := c.cc.Invoke(ctx, Notify_DeliveryItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) DeliveryItems(ctx context.Context, in *DeliveryItemsReq, opts ...grpc.CallOption) (*DeliveryItemsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeliveryItemsRsp)
	err := c.cc.Invoke(ctx, Notify_DeliveryItems_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) GetBattleStatus(ctx context.Context, in *GetBattleStatusReq, opts ...grpc.CallOption) (*GetBattleStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBattleStatusRsp)
	err := c.cc.Invoke(ctx, Notify_GetBattleStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notifyClient) CallbackCheckSend(ctx context.Context, in *callback.GiftPackageBusinessCheckSendReq, opts ...grpc.CallOption) (*callback.GiftPackageBusinessCheckSendRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(callback.GiftPackageBusinessCheckSendRsp)
	err := c.cc.Invoke(ctx, Notify_CallbackCheckSend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotifyServer is the server API for Notify service.
// All implementations should embed UnimplementedNotifyServer
// for forward compatibility
type NotifyServer interface {
	// 家族战-结算的定时器回调
	CallbackMBattleSettle(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error)
	// 家族战-结算中间定时器
	CallbackMBattleSettlePending(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error)
	// 家族战神兽复活检查定时器回调
	CallbackMBattleRevive(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error)
	// 游戏道具发货
	DeliveryItem(context.Context, *DeliveryItemReq) (*DeliveryItemRsp, error)
	// 游戏道具批量发货
	DeliveryItems(context.Context, *DeliveryItemsReq) (*DeliveryItemsRsp, error)
	// 读战斗结果及领奖状态
	GetBattleStatus(context.Context, *GetBattleStatusReq) (*GetBattleStatusRsp, error)
	// 中台发福利礼包回调检查
	CallbackCheckSend(context.Context, *callback.GiftPackageBusinessCheckSendReq) (*callback.GiftPackageBusinessCheckSendRsp, error)
}

// UnimplementedNotifyServer should be embedded to have forward compatible implementations.
type UnimplementedNotifyServer struct {
}

func (UnimplementedNotifyServer) CallbackMBattleSettle(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackMBattleSettle not implemented")
}
func (UnimplementedNotifyServer) CallbackMBattleSettlePending(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackMBattleSettlePending not implemented")
}
func (UnimplementedNotifyServer) CallbackMBattleRevive(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackMBattleRevive not implemented")
}
func (UnimplementedNotifyServer) DeliveryItem(context.Context, *DeliveryItemReq) (*DeliveryItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliveryItem not implemented")
}
func (UnimplementedNotifyServer) DeliveryItems(context.Context, *DeliveryItemsReq) (*DeliveryItemsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeliveryItems not implemented")
}
func (UnimplementedNotifyServer) GetBattleStatus(context.Context, *GetBattleStatusReq) (*GetBattleStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBattleStatus not implemented")
}
func (UnimplementedNotifyServer) CallbackCheckSend(context.Context, *callback.GiftPackageBusinessCheckSendReq) (*callback.GiftPackageBusinessCheckSendRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackCheckSend not implemented")
}

// UnsafeNotifyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotifyServer will
// result in compilation errors.
type UnsafeNotifyServer interface {
	mustEmbedUnimplementedNotifyServer()
}

func RegisterNotifyServer(s grpc.ServiceRegistrar, srv NotifyServer) {
	s.RegisterService(&Notify_ServiceDesc, srv)
}

func _Notify_CallbackMBattleSettle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).CallbackMBattleSettle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_CallbackMBattleSettle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).CallbackMBattleSettle(ctx, req.(*TimerCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_CallbackMBattleSettlePending_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).CallbackMBattleSettlePending(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_CallbackMBattleSettlePending_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).CallbackMBattleSettlePending(ctx, req.(*TimerCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_CallbackMBattleRevive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).CallbackMBattleRevive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_CallbackMBattleRevive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).CallbackMBattleRevive(ctx, req.(*TimerCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_DeliveryItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliveryItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).DeliveryItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_DeliveryItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).DeliveryItem(ctx, req.(*DeliveryItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_DeliveryItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeliveryItemsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).DeliveryItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_DeliveryItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).DeliveryItems(ctx, req.(*DeliveryItemsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_GetBattleStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBattleStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).GetBattleStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_GetBattleStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).GetBattleStatus(ctx, req.(*GetBattleStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notify_CallbackCheckSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(callback.GiftPackageBusinessCheckSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotifyServer).CallbackCheckSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notify_CallbackCheckSend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotifyServer).CallbackCheckSend(ctx, req.(*callback.GiftPackageBusinessCheckSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Notify_ServiceDesc is the grpc.ServiceDesc for Notify service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Notify_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "petpk.Notify",
	HandlerType: (*NotifyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CallbackMBattleSettle",
			Handler:    _Notify_CallbackMBattleSettle_Handler,
		},
		{
			MethodName: "CallbackMBattleSettlePending",
			Handler:    _Notify_CallbackMBattleSettlePending_Handler,
		},
		{
			MethodName: "CallbackMBattleRevive",
			Handler:    _Notify_CallbackMBattleRevive_Handler,
		},
		{
			MethodName: "DeliveryItem",
			Handler:    _Notify_DeliveryItem_Handler,
		},
		{
			MethodName: "DeliveryItems",
			Handler:    _Notify_DeliveryItems_Handler,
		},
		{
			MethodName: "GetBattleStatus",
			Handler:    _Notify_GetBattleStatus_Handler,
		},
		{
			MethodName: "CallbackCheckSend",
			Handler:    _Notify_CallbackCheckSend_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/petpk/notify/notify.proto",
}
