// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/petpk/battle/battle.proto

package battle

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Battle_Config_FullMethodName           = "/petpk.Battle/Config"
	Battle_BaseInfo_FullMethodName         = "/petpk.Battle/BaseInfo"
	Battle_Info_FullMethodName             = "/petpk.Battle/Info"
	Battle_BInfo_FullMethodName            = "/petpk.Battle/BInfo"
	Battle_Balance_FullMethodName          = "/petpk.Battle/Balance"
	Battle_State_FullMethodName            = "/petpk.Battle/State"
	Battle_Attack_FullMethodName           = "/petpk.Battle/Attack"
	Battle_GiftAttack_FullMethodName       = "/petpk.Battle/GiftAttack"
	Battle_GiftHeal_FullMethodName         = "/petpk.Battle/GiftHeal"
	Battle_GiftBuy_FullMethodName          = "/petpk.Battle/GiftBuy"
	Battle_CBPay_FullMethodName            = "/petpk.Battle/CBPay"
	Battle_Mark_FullMethodName             = "/petpk.Battle/Mark"
	Battle_Rank_FullMethodName             = "/petpk.Battle/Rank"
	Battle_BResult_FullMethodName          = "/petpk.Battle/BResult"
	Battle_PrevStats_FullMethodName        = "/petpk.Battle/PrevStats"
	Battle_DamageRank_FullMethodName       = "/petpk.Battle/DamageRank"
	Battle_Convene_FullMethodName          = "/petpk.Battle/Convene"
	Battle_DamageGlobalRank_FullMethodName = "/petpk.Battle/DamageGlobalRank"
	Battle_MarqueeDamage_FullMethodName    = "/petpk.Battle/MarqueeDamage"
	Battle_ListItem_FullMethodName         = "/petpk.Battle/ListItem"
	Battle_Settle_FullMethodName           = "/petpk.Battle/Settle"
	Battle_HpContributeRank_FullMethodName = "/petpk.Battle/HpContributeRank"
	Battle_RoomRank_FullMethodName         = "/petpk.Battle/RoomRank"
	Battle_BState_FullMethodName           = "/petpk.Battle/BState"
)

// BattleClient is the client API for Battle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BattleClient interface {
	// 场次安排时间配置
	Config(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*ConfigRsp, error)
	// 输出一些简单的信息, 返回自己房间的一些基础信息 和 自己当前的战斗id
	BaseInfo(ctx context.Context, in *BaseInfoReq, opts ...grpc.CallOption) (*BaseInfoRsp, error)
	// 宠物信息<非准备&战斗阶段主界面数据>
	Info(ctx context.Context, in *InfoReq, opts ...grpc.CallOption) (*InfoRsp, error)
	// 战斗信息
	BInfo(ctx context.Context, in *BInfoReq, opts ...grpc.CallOption) (*BInfoRsp, error)
	// 查询余额
	Balance(ctx context.Context, in *BalanceReq, opts ...grpc.CallOption) (*BalanceRsp, error)
	// 状态+战斗积分贡献topN【轮询】
	State(ctx context.Context, in *StateReq, opts ...grpc.CallOption) (*StateRsp, error)
	// 攻击
	Attack(ctx context.Context, in *AttackReq, opts ...grpc.CallOption) (*AttackRsp, error)
	// 礼物攻击
	GiftAttack(ctx context.Context, in *GiftAttackReq, opts ...grpc.CallOption) (*GiftAttackRsp, error)
	// 礼物回血
	GiftHeal(ctx context.Context, in *GiftHealReq, opts ...grpc.CallOption) (*GiftHealRsp, error)
	// 礼物购买道具
	GiftBuy(ctx context.Context, in *GiftBuyReq, opts ...grpc.CallOption) (*GiftBuyRsp, error)
	// 平台支付回调
	CBPay(ctx context.Context, in *callback.OrderShipmentReq, opts ...grpc.CallOption) (*callback.OrderShipmentRsp, error)
	// 标记神兽
	Mark(ctx context.Context, in *MarkReq, opts ...grpc.CallOption) (*MarkRsp, error)
	// 实时排行【战斗内伤害top5】
	Rank(ctx context.Context, in *RankReq, opts ...grpc.CallOption) (*RankRsp, error)
	// 结算界面数据
	BResult(ctx context.Context, in *BResultReq, opts ...grpc.CallOption) (*BResultRsp, error)
	// 上场战况
	PrevStats(ctx context.Context, in *PrevStatsReq, opts ...grpc.CallOption) (*PrevStatsRsp, error)
	// 宠物/房间段位榜
	//
	//	rpc PetRank(PetRankReq) returns (PetRankRsp);
	//
	// 房间-个人伤害榜
	DamageRank(ctx context.Context, in *DamageRankReq, opts ...grpc.CallOption) (*DamageRankRsp, error)
	// 召集玩家
	Convene(ctx context.Context, in *ConveneReq, opts ...grpc.CallOption) (*ConveneRsp, error)
	// 全服实时伤害榜 (开战前返回上场战斗的全服伤害榜; 开战后返回本场战斗的实时全服伤害榜)
	DamageGlobalRank(ctx context.Context, in *DamageGlobalRankReq, opts ...grpc.CallOption) (*DamageGlobalRankRsp, error)
	// 伤害跑马灯
	MarqueeDamage(ctx context.Context, in *MarqueeDamageReq, opts ...grpc.CallOption) (*MarqueeDamageRsp, error)
	// 背包
	ListItem(ctx context.Context, in *ListItemReq, opts ...grpc.CallOption) (*ListItemRsp, error)
	// 结算接口
	Settle(ctx context.Context, in *SettleReq, opts ...grpc.CallOption) (*SettleRsp, error)
	// 血量贡献榜
	HpContributeRank(ctx context.Context, in *HpContributeRankReq, opts ...grpc.CallOption) (*HpContributeRankRsp, error)
	// 房间榜
	RoomRank(ctx context.Context, in *RoomRankReq, opts ...grpc.CallOption) (*RoomRankRsp, error)
	// 【中台】房间排名, 第一名奖励
	BState(ctx context.Context, in *BStateReq, opts ...grpc.CallOption) (*BStateRsp, error)
}

type battleClient struct {
	cc grpc.ClientConnInterface
}

func NewBattleClient(cc grpc.ClientConnInterface) BattleClient {
	return &battleClient{cc}
}

func (c *battleClient) Config(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*ConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfigRsp)
	err := c.cc.Invoke(ctx, Battle_Config_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) BaseInfo(ctx context.Context, in *BaseInfoReq, opts ...grpc.CallOption) (*BaseInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseInfoRsp)
	err := c.cc.Invoke(ctx, Battle_BaseInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Info(ctx context.Context, in *InfoReq, opts ...grpc.CallOption) (*InfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InfoRsp)
	err := c.cc.Invoke(ctx, Battle_Info_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) BInfo(ctx context.Context, in *BInfoReq, opts ...grpc.CallOption) (*BInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BInfoRsp)
	err := c.cc.Invoke(ctx, Battle_BInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Balance(ctx context.Context, in *BalanceReq, opts ...grpc.CallOption) (*BalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BalanceRsp)
	err := c.cc.Invoke(ctx, Battle_Balance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) State(ctx context.Context, in *StateReq, opts ...grpc.CallOption) (*StateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StateRsp)
	err := c.cc.Invoke(ctx, Battle_State_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Attack(ctx context.Context, in *AttackReq, opts ...grpc.CallOption) (*AttackRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttackRsp)
	err := c.cc.Invoke(ctx, Battle_Attack_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) GiftAttack(ctx context.Context, in *GiftAttackReq, opts ...grpc.CallOption) (*GiftAttackRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GiftAttackRsp)
	err := c.cc.Invoke(ctx, Battle_GiftAttack_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) GiftHeal(ctx context.Context, in *GiftHealReq, opts ...grpc.CallOption) (*GiftHealRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GiftHealRsp)
	err := c.cc.Invoke(ctx, Battle_GiftHeal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) GiftBuy(ctx context.Context, in *GiftBuyReq, opts ...grpc.CallOption) (*GiftBuyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GiftBuyRsp)
	err := c.cc.Invoke(ctx, Battle_GiftBuy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) CBPay(ctx context.Context, in *callback.OrderShipmentReq, opts ...grpc.CallOption) (*callback.OrderShipmentRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(callback.OrderShipmentRsp)
	err := c.cc.Invoke(ctx, Battle_CBPay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Mark(ctx context.Context, in *MarkReq, opts ...grpc.CallOption) (*MarkRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkRsp)
	err := c.cc.Invoke(ctx, Battle_Mark_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Rank(ctx context.Context, in *RankReq, opts ...grpc.CallOption) (*RankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RankRsp)
	err := c.cc.Invoke(ctx, Battle_Rank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) BResult(ctx context.Context, in *BResultReq, opts ...grpc.CallOption) (*BResultRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BResultRsp)
	err := c.cc.Invoke(ctx, Battle_BResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) PrevStats(ctx context.Context, in *PrevStatsReq, opts ...grpc.CallOption) (*PrevStatsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrevStatsRsp)
	err := c.cc.Invoke(ctx, Battle_PrevStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) DamageRank(ctx context.Context, in *DamageRankReq, opts ...grpc.CallOption) (*DamageRankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DamageRankRsp)
	err := c.cc.Invoke(ctx, Battle_DamageRank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Convene(ctx context.Context, in *ConveneReq, opts ...grpc.CallOption) (*ConveneRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConveneRsp)
	err := c.cc.Invoke(ctx, Battle_Convene_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) DamageGlobalRank(ctx context.Context, in *DamageGlobalRankReq, opts ...grpc.CallOption) (*DamageGlobalRankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DamageGlobalRankRsp)
	err := c.cc.Invoke(ctx, Battle_DamageGlobalRank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) MarqueeDamage(ctx context.Context, in *MarqueeDamageReq, opts ...grpc.CallOption) (*MarqueeDamageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarqueeDamageRsp)
	err := c.cc.Invoke(ctx, Battle_MarqueeDamage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) ListItem(ctx context.Context, in *ListItemReq, opts ...grpc.CallOption) (*ListItemRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListItemRsp)
	err := c.cc.Invoke(ctx, Battle_ListItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) Settle(ctx context.Context, in *SettleReq, opts ...grpc.CallOption) (*SettleRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SettleRsp)
	err := c.cc.Invoke(ctx, Battle_Settle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) HpContributeRank(ctx context.Context, in *HpContributeRankReq, opts ...grpc.CallOption) (*HpContributeRankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HpContributeRankRsp)
	err := c.cc.Invoke(ctx, Battle_HpContributeRank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) RoomRank(ctx context.Context, in *RoomRankReq, opts ...grpc.CallOption) (*RoomRankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RoomRankRsp)
	err := c.cc.Invoke(ctx, Battle_RoomRank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *battleClient) BState(ctx context.Context, in *BStateReq, opts ...grpc.CallOption) (*BStateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BStateRsp)
	err := c.cc.Invoke(ctx, Battle_BState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BattleServer is the server API for Battle service.
// All implementations should embed UnimplementedBattleServer
// for forward compatibility
type BattleServer interface {
	// 场次安排时间配置
	Config(context.Context, *ConfigReq) (*ConfigRsp, error)
	// 输出一些简单的信息, 返回自己房间的一些基础信息 和 自己当前的战斗id
	BaseInfo(context.Context, *BaseInfoReq) (*BaseInfoRsp, error)
	// 宠物信息<非准备&战斗阶段主界面数据>
	Info(context.Context, *InfoReq) (*InfoRsp, error)
	// 战斗信息
	BInfo(context.Context, *BInfoReq) (*BInfoRsp, error)
	// 查询余额
	Balance(context.Context, *BalanceReq) (*BalanceRsp, error)
	// 状态+战斗积分贡献topN【轮询】
	State(context.Context, *StateReq) (*StateRsp, error)
	// 攻击
	Attack(context.Context, *AttackReq) (*AttackRsp, error)
	// 礼物攻击
	GiftAttack(context.Context, *GiftAttackReq) (*GiftAttackRsp, error)
	// 礼物回血
	GiftHeal(context.Context, *GiftHealReq) (*GiftHealRsp, error)
	// 礼物购买道具
	GiftBuy(context.Context, *GiftBuyReq) (*GiftBuyRsp, error)
	// 平台支付回调
	CBPay(context.Context, *callback.OrderShipmentReq) (*callback.OrderShipmentRsp, error)
	// 标记神兽
	Mark(context.Context, *MarkReq) (*MarkRsp, error)
	// 实时排行【战斗内伤害top5】
	Rank(context.Context, *RankReq) (*RankRsp, error)
	// 结算界面数据
	BResult(context.Context, *BResultReq) (*BResultRsp, error)
	// 上场战况
	PrevStats(context.Context, *PrevStatsReq) (*PrevStatsRsp, error)
	// 宠物/房间段位榜
	//
	//	rpc PetRank(PetRankReq) returns (PetRankRsp);
	//
	// 房间-个人伤害榜
	DamageRank(context.Context, *DamageRankReq) (*DamageRankRsp, error)
	// 召集玩家
	Convene(context.Context, *ConveneReq) (*ConveneRsp, error)
	// 全服实时伤害榜 (开战前返回上场战斗的全服伤害榜; 开战后返回本场战斗的实时全服伤害榜)
	DamageGlobalRank(context.Context, *DamageGlobalRankReq) (*DamageGlobalRankRsp, error)
	// 伤害跑马灯
	MarqueeDamage(context.Context, *MarqueeDamageReq) (*MarqueeDamageRsp, error)
	// 背包
	ListItem(context.Context, *ListItemReq) (*ListItemRsp, error)
	// 结算接口
	Settle(context.Context, *SettleReq) (*SettleRsp, error)
	// 血量贡献榜
	HpContributeRank(context.Context, *HpContributeRankReq) (*HpContributeRankRsp, error)
	// 房间榜
	RoomRank(context.Context, *RoomRankReq) (*RoomRankRsp, error)
	// 【中台】房间排名, 第一名奖励
	BState(context.Context, *BStateReq) (*BStateRsp, error)
}

// UnimplementedBattleServer should be embedded to have forward compatible implementations.
type UnimplementedBattleServer struct {
}

func (UnimplementedBattleServer) Config(context.Context, *ConfigReq) (*ConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Config not implemented")
}
func (UnimplementedBattleServer) BaseInfo(context.Context, *BaseInfoReq) (*BaseInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BaseInfo not implemented")
}
func (UnimplementedBattleServer) Info(context.Context, *InfoReq) (*InfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Info not implemented")
}
func (UnimplementedBattleServer) BInfo(context.Context, *BInfoReq) (*BInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BInfo not implemented")
}
func (UnimplementedBattleServer) Balance(context.Context, *BalanceReq) (*BalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Balance not implemented")
}
func (UnimplementedBattleServer) State(context.Context, *StateReq) (*StateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method State not implemented")
}
func (UnimplementedBattleServer) Attack(context.Context, *AttackReq) (*AttackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Attack not implemented")
}
func (UnimplementedBattleServer) GiftAttack(context.Context, *GiftAttackReq) (*GiftAttackRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiftAttack not implemented")
}
func (UnimplementedBattleServer) GiftHeal(context.Context, *GiftHealReq) (*GiftHealRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiftHeal not implemented")
}
func (UnimplementedBattleServer) GiftBuy(context.Context, *GiftBuyReq) (*GiftBuyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiftBuy not implemented")
}
func (UnimplementedBattleServer) CBPay(context.Context, *callback.OrderShipmentReq) (*callback.OrderShipmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CBPay not implemented")
}
func (UnimplementedBattleServer) Mark(context.Context, *MarkReq) (*MarkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Mark not implemented")
}
func (UnimplementedBattleServer) Rank(context.Context, *RankReq) (*RankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Rank not implemented")
}
func (UnimplementedBattleServer) BResult(context.Context, *BResultReq) (*BResultRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BResult not implemented")
}
func (UnimplementedBattleServer) PrevStats(context.Context, *PrevStatsReq) (*PrevStatsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrevStats not implemented")
}
func (UnimplementedBattleServer) DamageRank(context.Context, *DamageRankReq) (*DamageRankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DamageRank not implemented")
}
func (UnimplementedBattleServer) Convene(context.Context, *ConveneReq) (*ConveneRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Convene not implemented")
}
func (UnimplementedBattleServer) DamageGlobalRank(context.Context, *DamageGlobalRankReq) (*DamageGlobalRankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DamageGlobalRank not implemented")
}
func (UnimplementedBattleServer) MarqueeDamage(context.Context, *MarqueeDamageReq) (*MarqueeDamageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarqueeDamage not implemented")
}
func (UnimplementedBattleServer) ListItem(context.Context, *ListItemReq) (*ListItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListItem not implemented")
}
func (UnimplementedBattleServer) Settle(context.Context, *SettleReq) (*SettleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Settle not implemented")
}
func (UnimplementedBattleServer) HpContributeRank(context.Context, *HpContributeRankReq) (*HpContributeRankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HpContributeRank not implemented")
}
func (UnimplementedBattleServer) RoomRank(context.Context, *RoomRankReq) (*RoomRankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RoomRank not implemented")
}
func (UnimplementedBattleServer) BState(context.Context, *BStateReq) (*BStateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BState not implemented")
}

// UnsafeBattleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BattleServer will
// result in compilation errors.
type UnsafeBattleServer interface {
	mustEmbedUnimplementedBattleServer()
}

func RegisterBattleServer(s grpc.ServiceRegistrar, srv BattleServer) {
	s.RegisterService(&Battle_ServiceDesc, srv)
}

func _Battle_Config_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Config(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Config_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Config(ctx, req.(*ConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_BaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).BaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_BaseInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).BaseInfo(ctx, req.(*BaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Info_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Info(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Info_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Info(ctx, req.(*InfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_BInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).BInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_BInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).BInfo(ctx, req.(*BInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Balance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Balance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Balance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Balance(ctx, req.(*BalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_State_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).State(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_State_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).State(ctx, req.(*StateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Attack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Attack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Attack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Attack(ctx, req.(*AttackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_GiftAttack_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftAttackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).GiftAttack(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_GiftAttack_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).GiftAttack(ctx, req.(*GiftAttackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_GiftHeal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftHealReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).GiftHeal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_GiftHeal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).GiftHeal(ctx, req.(*GiftHealReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_GiftBuy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftBuyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).GiftBuy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_GiftBuy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).GiftBuy(ctx, req.(*GiftBuyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_CBPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(callback.OrderShipmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).CBPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_CBPay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).CBPay(ctx, req.(*callback.OrderShipmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Mark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Mark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Mark_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Mark(ctx, req.(*MarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Rank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Rank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Rank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Rank(ctx, req.(*RankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_BResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).BResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_BResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).BResult(ctx, req.(*BResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_PrevStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrevStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).PrevStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_PrevStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).PrevStats(ctx, req.(*PrevStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_DamageRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DamageRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).DamageRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_DamageRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).DamageRank(ctx, req.(*DamageRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Convene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConveneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Convene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Convene_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Convene(ctx, req.(*ConveneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_DamageGlobalRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DamageGlobalRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).DamageGlobalRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_DamageGlobalRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).DamageGlobalRank(ctx, req.(*DamageGlobalRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_MarqueeDamage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarqueeDamageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).MarqueeDamage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_MarqueeDamage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).MarqueeDamage(ctx, req.(*MarqueeDamageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_ListItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).ListItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_ListItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).ListItem(ctx, req.(*ListItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_Settle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).Settle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_Settle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).Settle(ctx, req.(*SettleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_HpContributeRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HpContributeRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).HpContributeRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_HpContributeRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).HpContributeRank(ctx, req.(*HpContributeRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_RoomRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoomRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).RoomRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_RoomRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).RoomRank(ctx, req.(*RoomRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Battle_BState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BattleServer).BState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Battle_BState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BattleServer).BState(ctx, req.(*BStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Battle_ServiceDesc is the grpc.ServiceDesc for Battle service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Battle_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "petpk.Battle",
	HandlerType: (*BattleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Config",
			Handler:    _Battle_Config_Handler,
		},
		{
			MethodName: "BaseInfo",
			Handler:    _Battle_BaseInfo_Handler,
		},
		{
			MethodName: "Info",
			Handler:    _Battle_Info_Handler,
		},
		{
			MethodName: "BInfo",
			Handler:    _Battle_BInfo_Handler,
		},
		{
			MethodName: "Balance",
			Handler:    _Battle_Balance_Handler,
		},
		{
			MethodName: "State",
			Handler:    _Battle_State_Handler,
		},
		{
			MethodName: "Attack",
			Handler:    _Battle_Attack_Handler,
		},
		{
			MethodName: "GiftAttack",
			Handler:    _Battle_GiftAttack_Handler,
		},
		{
			MethodName: "GiftHeal",
			Handler:    _Battle_GiftHeal_Handler,
		},
		{
			MethodName: "GiftBuy",
			Handler:    _Battle_GiftBuy_Handler,
		},
		{
			MethodName: "CBPay",
			Handler:    _Battle_CBPay_Handler,
		},
		{
			MethodName: "Mark",
			Handler:    _Battle_Mark_Handler,
		},
		{
			MethodName: "Rank",
			Handler:    _Battle_Rank_Handler,
		},
		{
			MethodName: "BResult",
			Handler:    _Battle_BResult_Handler,
		},
		{
			MethodName: "PrevStats",
			Handler:    _Battle_PrevStats_Handler,
		},
		{
			MethodName: "DamageRank",
			Handler:    _Battle_DamageRank_Handler,
		},
		{
			MethodName: "Convene",
			Handler:    _Battle_Convene_Handler,
		},
		{
			MethodName: "DamageGlobalRank",
			Handler:    _Battle_DamageGlobalRank_Handler,
		},
		{
			MethodName: "MarqueeDamage",
			Handler:    _Battle_MarqueeDamage_Handler,
		},
		{
			MethodName: "ListItem",
			Handler:    _Battle_ListItem_Handler,
		},
		{
			MethodName: "Settle",
			Handler:    _Battle_Settle_Handler,
		},
		{
			MethodName: "HpContributeRank",
			Handler:    _Battle_HpContributeRank_Handler,
		},
		{
			MethodName: "RoomRank",
			Handler:    _Battle_RoomRank_Handler,
		},
		{
			MethodName: "BState",
			Handler:    _Battle_BState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/petpk/battle/battle.proto",
}
