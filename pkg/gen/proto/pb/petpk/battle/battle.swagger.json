{"swagger": "2.0", "info": {"title": "pb/petpk/battle/battle.proto", "version": "version not set"}, "tags": [{"name": "Battle"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/petpk.Battle/Attack": {"post": {"summary": "攻击", "operationId": "Battle_Attack", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkAttackRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkAttackReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/BInfo": {"post": {"summary": "战斗信息", "operationId": "Battle_BInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkBInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkBInfoReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/BResult": {"post": {"summary": "结算界面数据", "operationId": "Battle_BResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkBResultRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkBResultReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/BState": {"post": {"summary": "【中台】房间排名, 第一名奖励", "operationId": "Battle_BState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkBStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkBStateReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Balance": {"post": {"summary": "查询余额", "operationId": "Battle_Balance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkBalanceReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/BaseInfo": {"post": {"summary": "输出一些简单的信息, 返回自己房间的一些基础信息 和 自己当前的战斗id", "operationId": "Battle_BaseInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkBaseInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkBaseInfoReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/CBPay": {"post": {"summary": "平台支付回调", "operationId": "Battle_CBPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/callbackOrderShipmentRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/callbackOrderShipmentReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Config": {"post": {"summary": "场次安排时间配置", "operationId": "Battle_Config", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkConfigReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Convene": {"post": {"summary": "召集玩家", "operationId": "Battle_Convene", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkConveneRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkConveneReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/DamageGlobalRank": {"post": {"summary": "全服实时伤害榜 (开战前返回上场战斗的全服伤害榜; 开战后返回本场战斗的实时全服伤害榜)", "operationId": "Battle_DamageGlobalRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkDamageGlobalRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkDamageGlobalRankReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/DamageRank": {"post": {"summary": "宠物/房间段位榜\n rpc PetRank(PetRankReq) returns (PetRankRsp);\n房间-个人伤害榜", "operationId": "Battle_DamageRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkDamageRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkDamageRankReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/GiftAttack": {"post": {"summary": "礼物攻击", "operationId": "Battle_GiftAttack", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkGiftAttackRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkGiftAttackReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/GiftBuy": {"post": {"summary": "礼物购买道具", "operationId": "Battle_GiftBuy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkGiftBuyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkGiftBuyReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/GiftHeal": {"post": {"summary": "礼物回血", "operationId": "Battle_GiftHeal", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkGiftHealRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkGiftHealReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/HpContributeRank": {"post": {"summary": "血量贡献榜", "operationId": "Battle_HpContributeRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkHpContributeRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkHpContributeRankReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Info": {"post": {"summary": "宠物信息<非准备&战斗阶段主界面数据>", "operationId": "Battle_Info", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkInfoReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/ListItem": {"post": {"summary": "背包", "operationId": "Battle_ListItem", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkListItemRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkListItemReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Mark": {"post": {"summary": "标记神兽", "operationId": "Battle_Mark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkMarkRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkMarkReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/MarqueeDamage": {"post": {"summary": "伤害跑马灯", "operationId": "Battle_MarqueeDamage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkMarqueeDamageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkMarqueeDamageReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/PrevStats": {"post": {"summary": "上场战况", "operationId": "Battle_PrevStats", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkPrevStatsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkPrevStatsReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Rank": {"post": {"summary": "实时排行【战斗内伤害top5】", "operationId": "Battle_Rank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkRankReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/RoomRank": {"post": {"summary": "房间榜", "operationId": "Battle_RoomRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkRoomRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkRoomRankReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/Settle": {"post": {"summary": "结算接口", "operationId": "Battle_Settle", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkSettleRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkSettleReq"}}], "tags": ["Battle"]}}, "/petpk.Battle/State": {"post": {"summary": "状态+战斗积分贡献topN【轮询】", "operationId": "Battle_State", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkStateReq"}}], "tags": ["Battle"]}}}, "definitions": {"adapter_commonGameMiddleInfo": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "gameOpenId": {"type": "string"}, "uid": {"type": "string"}}, "title": "方式二 宿主平台游戏账号体系\n   必填参数：uid"}, "callbackCommodityItem": {"type": "object", "properties": {"commodityId": {"type": "integer", "format": "int64", "title": "消费的道具id"}, "num": {"type": "integer", "format": "int64", "title": "消费的道具数量"}}, "title": "支付代理delivery==================begin"}, "callbackConsumeInfo": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/callbackCommodityItem"}}, "amount": {"type": "integer", "format": "int64", "title": "总价"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传字段"}, "currencyType": {"type": "integer", "format": "int64", "title": "货币类型 0/1=k币"}}}, "callbackOrderShipmentReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "consumeInfo": {"$ref": "#/definitions/callbackConsumeInfo", "title": "消费信息-下单时传入的参数"}, "consumeId": {"type": "string", "title": "订单id"}, "vecData": {"type": "string", "format": "byte", "title": "业务透传数据-下单时传入的参数"}, "payScene": {"type": "integer", "format": "int64", "title": "付费场景 1=直播 2=歌房 3=异步作品"}, "paySceneData": {"type": "string", "format": "byte", "title": "付费场景数据-云上应该暂时用不到，先只透传吧"}, "businessId": {"type": "string", "format": "int64", "title": "支付businessid，支付平台分配"}}}, "callbackOrderShipmentRsp": {"type": "object"}, "commonXPlan": {"type": "object", "properties": {"rid": {"type": "string", "title": "轮次ID,分配连续"}, "start": {"type": "string", "format": "int64", "title": "轮次-开始时间"}, "match": {"type": "string", "format": "int64", "title": "匹配阶段-开始时间"}, "prepare": {"type": "string", "format": "int64", "title": "准备阶段-开始时间"}, "battle": {"type": "string", "format": "int64", "title": "战斗阶段-开始时间"}, "settle": {"type": "string", "format": "int64", "title": "结算阶段-开始时间"}, "end": {"type": "string", "format": "int64", "title": "结算结束时间"}}, "title": "战斗排期"}, "petpkAttackReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间id"}, "bid": {"type": "string", "title": "战斗ID"}, "target": {"type": "string", "title": "攻击目标(房间ID)"}, "itemId": {"type": "string", "format": "int64", "title": "使用的道具ID"}}}, "petpkAttackRsp": {"type": "object", "properties": {"rate": {"type": "string", "format": "int64", "title": "攻击倍率"}, "atkVal": {"type": "string", "format": "int64", "title": "攻击力"}, "damage": {"type": "string", "format": "int64", "title": "造成的伤害"}, "selfScore": {"type": "string", "format": "int64", "title": "本房间-战斗积分"}, "selfHp": {"type": "string", "format": "int64", "title": "本房间-当前血量"}, "dstScore": {"type": "string", "format": "int64", "title": "目标-战斗积分"}, "dstHp": {"type": "string", "format": "int64", "title": "目标-当前血量"}, "scoreDelta": {"type": "string", "format": "int64", "title": "本次攻击增加战斗积分"}}}, "petpkBInfoReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}}}, "petpkBInfoRsp": {"type": "object", "properties": {"bid": {"type": "string", "title": "战斗ID"}, "state": {"type": "integer", "format": "int32", "title": "状态: -1-无战斗; 0-未开始;1-准备阶段;2-战斗;3-结算"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRoomFull"}, "title": "房间列表"}, "grank": {"type": "integer", "format": "int32", "title": "全服房间排名"}, "buff": {"type": "string", "title": "战神buff 房间ID"}, "atkAmount": {"type": "string", "format": "int64", "title": "个人当前战斗总伤害"}, "balance": {"type": "string", "format": "int64", "title": "平台币余额"}}, "title": "读取战斗所需的所有数据"}, "petpkBResultReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}}}, "petpkBResultRsp": {"type": "object", "properties": {"petList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkPet"}, "title": "4个房间列表"}, "selfAtk": {"type": "integer", "format": "int32", "title": "自己在该房间的输出伤害"}, "bid": {"type": "string", "title": "战斗ID"}, "settleOver": {"type": "boolean", "title": "结算结束"}, "settle": {"type": "boolean", "title": "是否需要结算"}}}, "petpkBStateReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏ID"}, "room": {"type": "string", "title": "房间ID"}, "roundId": {"type": "string", "title": "轮次ID"}}}, "petpkBStateRsp": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "当前房间排名"}, "score": {"type": "integer", "format": "int32", "title": "当前房间战斗积分"}, "rewardTop1": {"$ref": "#/definitions/petpkRankReward", "title": "第一名奖励乘过系数"}}}, "petpkBalanceReq": {"type": "object"}, "petpkBalanceRsp": {"type": "object", "properties": {"balance": {"type": "string", "format": "int64"}}}, "petpkBaseInfoReq": {"type": "object", "properties": {"room": {"type": "string", "title": "房间id"}}}, "petpkBaseInfoRsp": {"type": "object", "properties": {"room": {"type": "string", "title": "房间id"}, "name": {"type": "string", "title": "房间名"}, "avatar": {"type": "string", "title": "房间头像"}, "uname": {"type": "string", "title": "玩家昵称"}, "uavatar": {"type": "string", "title": "玩家头像"}, "bid": {"type": "string", "title": "战斗id"}, "dan": {"type": "integer", "format": "int32", "title": "房间段位"}, "role": {"type": "integer", "format": "int32", "title": "角色类型, 参考 petpk.PlayerType"}, "petId": {"type": "string", "title": "宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)"}, "petPhase": {"type": "string", "title": "宠物形态所在的阶段"}, "rrs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRankReward"}, "title": "1~4名的奖励, 匹配前后可能不一样, 奖励只会变高, 不会变少"}, "settle": {"type": "boolean", "title": "是否可以结算"}, "debug": {"type": "string"}}}, "petpkCRItem": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "用户ID"}, "avatar": {"type": "string", "title": "用户头像"}, "name": {"type": "string", "title": "玩家昵称"}, "roomName": {"type": "string", "title": "归属房间名"}, "rank": {"type": "integer", "format": "int32", "title": "排名,-1:未上榜, 其他实际排名(从1开始)"}, "value": {"type": "string", "format": "int64", "title": "伤害值"}, "value2": {"type": "integer", "format": "int32", "title": "扩展数值(攻击力)"}}, "title": "RItem - 玩家排行榜元素"}, "petpkConfigReq": {"type": "object", "properties": {"itemVer": {"type": "integer", "format": "int32"}}}, "petpkConfigRsp": {"type": "object", "properties": {"ts": {"type": "string", "format": "int64", "title": "服务器时间"}, "itemVer": {"type": "string", "format": "int64", "title": "道具配置版本"}, "curr": {"$ref": "#/definitions/commonXPlan", "title": "当前轮时间配置"}, "next": {"$ref": "#/definitions/commonXPlan", "title": "下轮时间配置"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkZItem"}, "title": "道具配置"}, "normal": {"$ref": "#/definitions/petpkNormalDef", "title": "常规配置"}, "shopItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkZShop"}, "title": "可购买道具列表"}}}, "petpkConveneReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}, "bid": {"type": "string", "title": "战斗id"}}}, "petpkConveneRsp": {"type": "object"}, "petpkDamageGlobalRankReq": {"type": "object"}, "petpkDamageGlobalRankRsp": {"type": "object", "properties": {"tops": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkDamageRankItem"}, "title": "榜上玩家数据"}, "rankRewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRankReward"}, "title": "名次及对应的奖励"}, "self": {"$ref": "#/definitions/petpkDamageRankItem", "title": "自己的数据"}}}, "petpkDamageRankItem": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "排名"}, "uid": {"type": "string", "format": "uint64", "title": "玩家id"}, "name": {"type": "string", "title": "玩家昵称"}, "avatar": {"type": "string", "title": "头像"}, "damage": {"type": "string", "format": "int64", "title": "造成的伤害"}}}, "petpkDamageRankReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}, "rank": {"type": "integer", "format": "int32", "title": "从这个排名(包含)开始读取"}}}, "petpkDamageRankRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkCRItem"}}, "self": {"$ref": "#/definitions/petpkCRItem"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkReward"}, "title": "房间奖励"}}}, "petpkGiftAttackReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间id"}, "bid": {"type": "string", "title": "战斗ID"}, "target": {"type": "string", "title": "攻击目标(房间ID)"}, "tid": {"type": "string", "title": "订单ID"}, "giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "meta": {"$ref": "#/definitions/petpkRoomMeta", "title": "透传数据"}}}, "petpkGiftAttackRsp": {"type": "object", "properties": {"atkVal": {"type": "string", "format": "int64", "title": "攻击力"}, "damage": {"type": "string", "format": "int64", "title": "造成的伤害"}, "selfScore": {"type": "string", "format": "int64", "title": "本房间-战斗积分"}, "selfHp": {"type": "string", "format": "int64", "title": "本房间-当前血量"}, "dstScore": {"type": "string", "format": "int64", "title": "目标-战斗积分"}, "dstHp": {"type": "string", "format": "int64", "title": "目标-当前血量"}, "scoreDelta": {"type": "string", "format": "int64", "title": "本次攻击增加战斗积分"}}}, "petpkGiftBuyReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间id"}, "bid": {"type": "string", "title": "战斗ID"}, "tid": {"type": "string", "title": "订单ID"}, "giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "giftNum": {"type": "integer", "format": "int32", "title": "礼物份数"}, "itemTarget": {"type": "integer", "format": "int32", "title": "待购买道具"}, "meta": {"$ref": "#/definitions/petpkRoomMeta", "title": "透传数据"}}}, "petpkGiftBuyRsp": {"type": "object", "properties": {"balance": {"type": "integer", "format": "int32", "title": "购买的道具余额"}}}, "petpkGiftHealReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间id"}, "bid": {"type": "string", "title": "战斗ID"}, "tid": {"type": "string", "title": "订单ID"}, "giftId": {"type": "string", "format": "int64", "title": "礼物ID"}, "meta": {"$ref": "#/definitions/petpkRoomMeta", "title": "透传数据"}}}, "petpkGiftHealRsp": {"type": "object", "properties": {"blood": {"type": "string", "format": "int64", "title": "恢复的血量"}, "hp": {"type": "string", "format": "int64", "title": "剩余血量"}, "status": {"type": "string", "format": "int64", "title": "恢复状态: -1: 已死亡, 1: 血量满, 0: 正常加血了"}}}, "petpkHpContributeRankItem": {"type": "object", "properties": {"playerId": {"type": "string", "title": "玩家ID"}, "playerName": {"type": "string", "title": "玩家名"}, "rankPlace": {"type": "integer", "format": "int64", "title": "排行榜中排名，从1开始, 0代表不在排行榜中"}, "rankValue": {"type": "number", "format": "double", "title": "排行榜中数值"}, "playerAvatar": {"type": "string", "title": "玩家头像"}}}, "petpkHpContributeRankReq": {"type": "object", "properties": {"rid": {"type": "string", "title": "轮次id"}, "room": {"type": "string", "title": "房间id"}, "passback": {"type": "string", "title": "第一页无须传, 第N页透传第N-1页回包中的"}}}, "petpkHpContributeRankRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkHpContributeRankItem"}, "title": "榜单项"}, "self": {"$ref": "#/definitions/petpkHpContributeRankItem", "title": "自己的贡献信息"}, "hasMore": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "string"}}}, "petpkInfoReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}}}, "petpkInfoRsp": {"type": "object", "properties": {"score": {"type": "string", "format": "int64", "title": "段位积分"}, "hpDan": {"type": "string", "format": "int64", "title": "段位血量"}, "hpExt": {"type": "string", "format": "int64", "title": "加成血量"}, "hpMax": {"type": "string", "format": "int64", "title": "血量上限"}, "nextScore": {"type": "string", "format": "int64", "title": "距下一段位积分"}, "grank": {"type": "integer", "format": "int32", "title": "房间上期排名, 0-未上榜"}}}, "petpkListItemReq": {"type": "object"}, "petpkListItemRsp": {"type": "object", "properties": {"roundEndTime": {"type": "string", "format": "int64", "title": "轮次结束时间"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkXItem"}, "title": "道具列表"}, "roundId": {"type": "string", "title": "轮次ID"}}}, "petpkMarkReq": {"type": "object", "properties": {"bid": {"type": "string", "title": "战斗ID"}, "room": {"type": "string", "title": "归属房间id"}, "target": {"type": "string", "title": "标记目标(房间ID)"}}}, "petpkMarkRsp": {"type": "object", "properties": {"mark": {"type": "boolean", "title": "标记成功还是失败"}}}, "petpkMarqueeDamageReq": {"type": "object"}, "petpkMarqueeDamageRsp": {"type": "object", "properties": {"tops": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkDamageRankItem"}, "title": "榜上玩家数据"}}}, "petpkNormalDef": {"type": "object", "properties": {"atkValMin": {"type": "string", "format": "int64", "title": "发奖最小伤害值"}, "mdr": {"type": "string", "format": "int64", "title": "怪物死亡复活时间(秒)"}, "atkFactor": {"type": "string", "format": "int64", "title": "战神攻击加成 1000=100%"}, "title": {"type": "string", "title": "战斗排期文案"}}}, "petpkPet": {"type": "object", "properties": {"id": {"type": "string", "title": "房间ID"}, "avatar": {"type": "string", "title": "房间头像"}, "name": {"type": "string", "title": "房间名称"}, "danScore": {"type": "integer", "format": "int32", "title": "段位分数"}, "danDelta": {"type": "integer", "format": "int32", "title": "段位分增量"}, "dan": {"type": "integer", "format": "int32", "title": "段位标识"}, "tdamage": {"type": "string", "format": "int64", "title": "总伤害"}, "score": {"type": "string", "format": "int64", "title": "本场获得战斗积分"}}}, "petpkPrevStatsReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}}}, "petpkPrevStatsRsp": {"type": "object", "properties": {"settleTime": {"type": "string", "format": "int64", "title": "结算日期"}, "bid": {"type": "string", "title": "战斗ID"}, "selfAtk": {"type": "integer", "format": "int32", "title": "自己的总伤害"}, "us": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkCRItem"}, "title": "本厂最佳战神 x5"}, "ssm": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkSSM"}, "title": "战况数据 x4"}}}, "petpkRankReq": {"type": "object", "properties": {"bid": {"type": "string", "title": "战斗ID"}, "room": {"type": "string", "title": "归属房间id"}}}, "petpkRankReward": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "排名"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkReward"}, "title": "奖励"}}}, "petpkRankRsp": {"type": "object", "properties": {"us": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkCRItem"}, "title": "最佳战神TOP5"}}}, "petpkReward": {"type": "object", "properties": {"id": {"type": "string", "title": "奖励ID"}, "qua": {"type": "string", "format": "int64", "title": "奖励数量"}, "img": {"type": "string", "title": "图标"}, "name": {"type": "string", "title": "名称"}}}, "petpkRoomFull": {"type": "object", "properties": {"id": {"type": "string", "title": "房间ID"}, "name": {"type": "string", "title": "房间名"}, "avatar": {"type": "string", "title": "房间头像"}, "dan": {"type": "string", "format": "int64", "title": "段位"}, "score": {"type": "string", "format": "int64", "title": "战斗积分"}, "maxHp": {"type": "string", "format": "int64", "title": "血量上限"}, "hp": {"type": "string", "format": "int64", "title": "血量"}, "reviveTime": {"type": "string", "format": "int64", "title": "宠物复活时间, HP <= 0时有效"}, "petId": {"type": "string", "title": "宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)"}, "petPhase": {"type": "string", "title": "宠物形态所在的阶段"}, "damaged": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "当前宠物受到的其他3个房间伤害"}, "tdamaged": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "当前战斗受到的其他3个房间伤害"}}}, "petpkRoomMeta": {"type": "object", "properties": {"anchorId": {"type": "string", "title": "主播ID"}, "roomId": {"type": "string", "title": "房间ID"}, "showId": {"type": "string"}, "ugcId": {"type": "string"}, "payScene": {"type": "integer", "format": "int32", "title": "支付场景: 0-未知,1-直播,2-歌房"}, "target": {"type": "string", "title": "送礼目标"}, "position": {"type": "integer", "format": "int32", "title": "麦位(Q音)"}}}, "petpkRoomRankItem": {"type": "object", "properties": {"roomId": {"type": "string", "title": "玩家ID"}, "roomName": {"type": "string", "title": "玩家名"}, "rankId": {"type": "string", "format": "int64", "title": "房间段位"}, "subRankId": {"type": "string", "format": "int64", "title": "房间子段位"}, "rankPlace": {"type": "integer", "format": "int64", "title": "排行榜中排名，从1开始, 0代表不在排行榜中"}, "rankValue": {"type": "number", "format": "double", "title": "排行榜中数值"}, "roomCover": {"type": "string", "title": "房间封面"}}}, "petpkRoomRankReq": {"type": "object", "properties": {"room": {"type": "string"}, "passback": {"type": "string", "title": "第一页无须传, 第N页透传第N-1页回包中的"}}}, "petpkRoomRankRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRoomRankItem"}, "title": "榜单项"}, "self": {"$ref": "#/definitions/petpkRoomRankItem"}, "hasMore": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "string"}}}, "petpkRoomSimple": {"type": "object", "properties": {"id": {"type": "string", "title": "房间ID"}, "hp": {"type": "string", "format": "int64", "title": "当前血量"}, "score": {"type": "string", "format": "int64", "title": "战斗积分"}, "reviveTime": {"type": "string", "format": "int64", "title": "宠物复活时间, HP <= 0时有效"}, "focus": {"type": "integer", "format": "int32", "title": "0-未被集火; 1-房主标记; 2-超管标记"}, "focusAvatar": {"type": "string", "title": "集火标记头像"}, "damaged": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "当前宠物受到的其他3个房间伤害"}, "tdamaged": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "当前战斗受到的其他3个房间总伤害"}, "splitScore": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "瓜分积分"}}}, "petpkSSM": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "排名"}, "id": {"type": "string", "title": "房间ID"}, "name": {"type": "string", "title": "房间名"}, "avatar": {"type": "string", "title": "房间头像"}, "score": {"type": "string", "format": "int64", "title": "本场获得战斗积分"}, "totalDamage": {"type": "string", "format": "int64", "title": "本场输出伤害"}, "danScore": {"type": "string", "format": "int64", "title": "段位分"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkReward"}, "title": "战斗内奖励"}}}, "petpkScoreItem": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "score": {"type": "string", "format": "int64", "title": "攻击获得战斗积分"}, "ts": {"type": "string", "format": "int64", "title": "攻击时间"}}}, "petpkSettleReq": {"type": "object", "properties": {"room": {"type": "string"}}}, "petpkSettleRsp": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkReward"}, "title": "战斗奖励"}, "gdRankRewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkReward"}, "title": "全服伤害排行榜奖励"}}}, "petpkStateReq": {"type": "object", "properties": {"room": {"type": "string", "title": "归属房间ID"}, "bid": {"type": "string", "title": "战斗ID"}, "lastTs": {"type": "string", "format": "int64", "title": "上次请求的时间戳, 第一次的话传0"}}}, "petpkStateRsp": {"type": "object", "properties": {"ts": {"type": "string", "format": "int64", "title": "服务器时间"}, "buff": {"type": "string", "title": "战神buff: 房间ID"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRoomSimple"}, "title": "房间列表"}, "scores": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkScoreItem"}, "title": "最近一段时间攻击过的玩家"}}}, "petpkXItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "道具id"}, "num": {"type": "string", "format": "int64", "title": "道具数量"}}}, "petpkZItem": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "道具id"}, "type": {"type": "integer", "format": "int32", "title": "类型 1-普通道具, 2-攻击礼物, 3-回血礼物, 4-购买"}, "name": {"type": "string", "title": "名称"}, "val1": {"type": "integer", "format": "int32", "title": "攻击力 | 回血比例1000=100%"}, "val2": {"type": "integer", "format": "int32", "title": "无用 | 价格"}, "score": {"type": "integer", "format": "int32", "title": "战斗积分"}, "max": {"type": "integer", "format": "int32", "title": "持有上限"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展"}}}, "petpkZShop": {"type": "object", "properties": {"num": {"type": "integer", "format": "int32", "title": "礼物份数"}, "price": {"type": "integer", "format": "int32", "title": "价格"}, "x1Num": {"type": "integer", "format": "int32", "title": "道具1数量"}, "x2Num": {"type": "integer", "format": "int32", "title": "道具2数量"}, "x3Num": {"type": "integer", "format": "int32", "title": "道具3数量"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}