// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/petpk/battle/battle.proto

package battle

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	common "kugou_adapter_service/pkg/gen/proto/pb/petpk/common"
	pet "kugou_adapter_service/pkg/gen/proto/pb/petpk/pet"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HpContributeRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rid      string `protobuf:"bytes,1,opt,name=rid,proto3" json:"rid,omitempty"`           // 轮次id
	Room     string `protobuf:"bytes,2,opt,name=room,proto3" json:"room,omitempty"`         // 房间id
	Passback string `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"` // 第一页无须传, 第N页透传第N-1页回包中的
}

func (x *HpContributeRankReq) Reset() {
	*x = HpContributeRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HpContributeRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HpContributeRankReq) ProtoMessage() {}

func (x *HpContributeRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HpContributeRankReq.ProtoReflect.Descriptor instead.
func (*HpContributeRankReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{0}
}

func (x *HpContributeRankReq) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *HpContributeRankReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *HpContributeRankReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type HpContributeRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items    []*pet.HpContributeRankItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`      // 榜单项
	Self     *pet.HpContributeRankItem   `protobuf:"bytes,2,opt,name=self,proto3" json:"self,omitempty"`        // 自己的贡献信息
	HasMore  bool                        `protobuf:"varint,3,opt,name=hasMore,proto3" json:"hasMore,omitempty"` // 是否还有下一页
	Passback string                      `protobuf:"bytes,4,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *HpContributeRankRsp) Reset() {
	*x = HpContributeRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HpContributeRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HpContributeRankRsp) ProtoMessage() {}

func (x *HpContributeRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HpContributeRankRsp.ProtoReflect.Descriptor instead.
func (*HpContributeRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{1}
}

func (x *HpContributeRankRsp) GetItems() []*pet.HpContributeRankItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *HpContributeRankRsp) GetSelf() *pet.HpContributeRankItem {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *HpContributeRankRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *HpContributeRankRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type RoomRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room     string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"` // 第一页无须传, 第N页透传第N-1页回包中的
}

func (x *RoomRankReq) Reset() {
	*x = RoomRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomRankReq) ProtoMessage() {}

func (x *RoomRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomRankReq.ProtoReflect.Descriptor instead.
func (*RoomRankReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{2}
}

func (x *RoomRankReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *RoomRankReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type RoomRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items    []*pet.RoomRankItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"` // 榜单项
	Self     *pet.RoomRankItem   `protobuf:"bytes,2,opt,name=self,proto3" json:"self,omitempty"`
	HasMore  bool                `protobuf:"varint,3,opt,name=hasMore,proto3" json:"hasMore,omitempty"` // 是否还有下一页
	Passback string              `protobuf:"bytes,4,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *RoomRankRsp) Reset() {
	*x = RoomRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomRankRsp) ProtoMessage() {}

func (x *RoomRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomRankRsp.ProtoReflect.Descriptor instead.
func (*RoomRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{3}
}

func (x *RoomRankRsp) GetItems() []*pet.RoomRankItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *RoomRankRsp) GetSelf() *pet.RoomRankItem {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *RoomRankRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *RoomRankRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type SettleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`
}

func (x *SettleReq) Reset() {
	*x = SettleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleReq) ProtoMessage() {}

func (x *SettleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleReq.ProtoReflect.Descriptor instead.
func (*SettleReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{4}
}

func (x *SettleReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type SettleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards       []*Reward `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"`                                    // 战斗奖励
	GdRankRewards []*Reward `protobuf:"bytes,2,rep,name=gd_rank_rewards,json=gdRankRewards,proto3" json:"gd_rank_rewards,omitempty"` // 全服伤害排行榜奖励
}

func (x *SettleRsp) Reset() {
	*x = SettleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleRsp) ProtoMessage() {}

func (x *SettleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleRsp.ProtoReflect.Descriptor instead.
func (*SettleRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{5}
}

func (x *SettleRsp) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *SettleRsp) GetGdRankRewards() []*Reward {
	if x != nil {
		return x.GdRankRewards
	}
	return nil
}

type RoomMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnchorId string `protobuf:"bytes,1,opt,name=anchor_id,json=anchorId,proto3" json:"anchor_id,omitempty"` // 主播ID
	RoomId   string `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`       // 房间ID
	ShowId   string `protobuf:"bytes,3,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`
	UgcId    string `protobuf:"bytes,4,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	PayScene int32  `protobuf:"varint,5,opt,name=pay_scene,json=payScene,proto3" json:"pay_scene,omitempty"` // 支付场景: 0-未知,1-直播,2-歌房
	Target   string `protobuf:"bytes,6,opt,name=target,proto3" json:"target,omitempty"`                      // 送礼目标
	Position int32  `protobuf:"varint,7,opt,name=position,proto3" json:"position,omitempty"`                 // 麦位(Q音)
}

func (x *RoomMeta) Reset() {
	*x = RoomMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomMeta) ProtoMessage() {}

func (x *RoomMeta) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomMeta.ProtoReflect.Descriptor instead.
func (*RoomMeta) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{6}
}

func (x *RoomMeta) GetAnchorId() string {
	if x != nil {
		return x.AnchorId
	}
	return ""
}

func (x *RoomMeta) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomMeta) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

func (x *RoomMeta) GetUgcId() string {
	if x != nil {
		return x.UgcId
	}
	return ""
}

func (x *RoomMeta) GetPayScene() int32 {
	if x != nil {
		return x.PayScene
	}
	return 0
}

func (x *RoomMeta) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *RoomMeta) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

type ConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemVer int32 `protobuf:"varint,1,opt,name=item_ver,json=itemVer,proto3" json:"item_ver,omitempty"`
}

func (x *ConfigReq) Reset() {
	*x = ConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigReq) ProtoMessage() {}

func (x *ConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigReq.ProtoReflect.Descriptor instead.
func (*ConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{7}
}

func (x *ConfigReq) GetItemVer() int32 {
	if x != nil {
		return x.ItemVer
	}
	return 0
}

type NormalDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AtkValMin int64  `protobuf:"varint,1,opt,name=atk_val_min,json=atkValMin,proto3" json:"atk_val_min,omitempty"` // 发奖最小伤害值
	Mdr       int64  `protobuf:"varint,2,opt,name=mdr,proto3" json:"mdr,omitempty"`                                // 怪物死亡复活时间(秒)
	AtkFactor int64  `protobuf:"varint,3,opt,name=atk_factor,json=atkFactor,proto3" json:"atk_factor,omitempty"`   // 战神攻击加成 1000=100%
	Title     string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`                             // 战斗排期文案
}

func (x *NormalDef) Reset() {
	*x = NormalDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NormalDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalDef) ProtoMessage() {}

func (x *NormalDef) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalDef.ProtoReflect.Descriptor instead.
func (*NormalDef) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{8}
}

func (x *NormalDef) GetAtkValMin() int64 {
	if x != nil {
		return x.AtkValMin
	}
	return 0
}

func (x *NormalDef) GetMdr() int64 {
	if x != nil {
		return x.Mdr
	}
	return 0
}

func (x *NormalDef) GetAtkFactor() int64 {
	if x != nil {
		return x.AtkFactor
	}
	return 0
}

func (x *NormalDef) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts        int64         `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`                               // 服务器时间
	ItemVer   int64         `protobuf:"varint,2,opt,name=item_ver,json=itemVer,proto3" json:"item_ver,omitempty"`      // 道具配置版本
	Curr      *common.XPlan `protobuf:"bytes,3,opt,name=curr,proto3" json:"curr,omitempty"`                            // 当前轮时间配置
	Next      *common.XPlan `protobuf:"bytes,4,opt,name=next,proto3" json:"next,omitempty"`                            // 下轮时间配置
	Items     []*ZItem      `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"`                          // 道具配置
	Normal    *NormalDef    `protobuf:"bytes,6,opt,name=normal,proto3" json:"normal,omitempty"`                        // 常规配置
	ShopItems []*ZShop      `protobuf:"bytes,7,rep,name=shop_items,json=shopItems,proto3" json:"shop_items,omitempty"` // 可购买道具列表
}

func (x *ConfigRsp) Reset() {
	*x = ConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigRsp) ProtoMessage() {}

func (x *ConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigRsp.ProtoReflect.Descriptor instead.
func (*ConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{9}
}

func (x *ConfigRsp) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *ConfigRsp) GetItemVer() int64 {
	if x != nil {
		return x.ItemVer
	}
	return 0
}

func (x *ConfigRsp) GetCurr() *common.XPlan {
	if x != nil {
		return x.Curr
	}
	return nil
}

func (x *ConfigRsp) GetNext() *common.XPlan {
	if x != nil {
		return x.Next
	}
	return nil
}

func (x *ConfigRsp) GetItems() []*ZItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ConfigRsp) GetNormal() *NormalDef {
	if x != nil {
		return x.Normal
	}
	return nil
}

func (x *ConfigRsp) GetShopItems() []*ZShop {
	if x != nil {
		return x.ShopItems
	}
	return nil
}

// RItem - 玩家排行榜元素
type CRItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                          // 用户ID
	Avatar   string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`                     // 用户头像
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                         // 玩家昵称
	RoomName string `protobuf:"bytes,4,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"` // 归属房间名
	Rank     int32  `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`                        // 排名,-1:未上榜, 其他实际排名(从1开始)
	Value    int64  `protobuf:"varint,6,opt,name=value,proto3" json:"value,omitempty"`                      // 伤害值
	Value2   int32  `protobuf:"varint,7,opt,name=value2,proto3" json:"value2,omitempty"`                    // 扩展数值(攻击力)
}

func (x *CRItem) Reset() {
	*x = CRItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CRItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CRItem) ProtoMessage() {}

func (x *CRItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CRItem.ProtoReflect.Descriptor instead.
func (*CRItem) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{10}
}

func (x *CRItem) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CRItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *CRItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CRItem) GetRoomName() string {
	if x != nil {
		return x.RoomName
	}
	return ""
}

func (x *CRItem) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *CRItem) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *CRItem) GetValue2() int32 {
	if x != nil {
		return x.Value2
	}
	return 0
}

type InfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"` // 归属房间ID
}

func (x *InfoReq) Reset() {
	*x = InfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoReq) ProtoMessage() {}

func (x *InfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoReq.ProtoReflect.Descriptor instead.
func (*InfoReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{11}
}

func (x *InfoReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type InfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score     int64 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`                          // 段位积分
	HpDan     int64 `protobuf:"varint,2,opt,name=hp_dan,json=hpDan,proto3" json:"hp_dan,omitempty"`             // 段位血量
	HpExt     int64 `protobuf:"varint,3,opt,name=hp_ext,json=hpExt,proto3" json:"hp_ext,omitempty"`             // 加成血量
	HpMax     int64 `protobuf:"varint,4,opt,name=hp_max,json=hpMax,proto3" json:"hp_max,omitempty"`             // 血量上限
	NextScore int64 `protobuf:"varint,5,opt,name=next_score,json=nextScore,proto3" json:"next_score,omitempty"` // 距下一段位积分
	Grank     int32 `protobuf:"varint,6,opt,name=grank,proto3" json:"grank,omitempty"`                          // 房间上期排名, 0-未上榜
}

func (x *InfoRsp) Reset() {
	*x = InfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InfoRsp) ProtoMessage() {}

func (x *InfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InfoRsp.ProtoReflect.Descriptor instead.
func (*InfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{12}
}

func (x *InfoRsp) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *InfoRsp) GetHpDan() int64 {
	if x != nil {
		return x.HpDan
	}
	return 0
}

func (x *InfoRsp) GetHpExt() int64 {
	if x != nil {
		return x.HpExt
	}
	return 0
}

func (x *InfoRsp) GetHpMax() int64 {
	if x != nil {
		return x.HpMax
	}
	return 0
}

func (x *InfoRsp) GetNextScore() int64 {
	if x != nil {
		return x.NextScore
	}
	return 0
}

func (x *InfoRsp) GetGrank() int32 {
	if x != nil {
		return x.Grank
	}
	return 0
}

type RoomFull struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string           `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                       // 房间ID
	Name       string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                                                   // 房间名
	Avatar     string           `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`                                                                                               // 房间头像
	Dan        int64            `protobuf:"varint,4,opt,name=dan,proto3" json:"dan,omitempty"`                                                                                                    // 段位
	Score      int64            `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`                                                                                                // 战斗积分
	MaxHp      int64            `protobuf:"varint,6,opt,name=max_hp,json=maxHp,proto3" json:"max_hp,omitempty"`                                                                                   // 血量上限
	Hp         int64            `protobuf:"varint,7,opt,name=hp,proto3" json:"hp,omitempty"`                                                                                                      // 血量
	ReviveTime int64            `protobuf:"varint,8,opt,name=revive_time,json=reviveTime,proto3" json:"revive_time,omitempty"`                                                                    // 宠物复活时间, HP <= 0时有效
	PetId      string           `protobuf:"bytes,9,opt,name=petId,proto3" json:"petId,omitempty"`                                                                                                 // 宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)
	PetPhase   string           `protobuf:"bytes,10,opt,name=petPhase,proto3" json:"petPhase,omitempty"`                                                                                          // 宠物形态所在的阶段
	Damaged    map[string]int64 `protobuf:"bytes,11,rep,name=damaged,proto3" json:"damaged,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`   // 当前宠物受到的其他3个房间伤害
	Tdamaged   map[string]int64 `protobuf:"bytes,12,rep,name=tdamaged,proto3" json:"tdamaged,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 当前战斗受到的其他3个房间伤害
}

func (x *RoomFull) Reset() {
	*x = RoomFull{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomFull) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomFull) ProtoMessage() {}

func (x *RoomFull) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomFull.ProtoReflect.Descriptor instead.
func (*RoomFull) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{13}
}

func (x *RoomFull) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RoomFull) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RoomFull) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RoomFull) GetDan() int64 {
	if x != nil {
		return x.Dan
	}
	return 0
}

func (x *RoomFull) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RoomFull) GetMaxHp() int64 {
	if x != nil {
		return x.MaxHp
	}
	return 0
}

func (x *RoomFull) GetHp() int64 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *RoomFull) GetReviveTime() int64 {
	if x != nil {
		return x.ReviveTime
	}
	return 0
}

func (x *RoomFull) GetPetId() string {
	if x != nil {
		return x.PetId
	}
	return ""
}

func (x *RoomFull) GetPetPhase() string {
	if x != nil {
		return x.PetPhase
	}
	return ""
}

func (x *RoomFull) GetDamaged() map[string]int64 {
	if x != nil {
		return x.Damaged
	}
	return nil
}

func (x *RoomFull) GetTdamaged() map[string]int64 {
	if x != nil {
		return x.Tdamaged
	}
	return nil
}

type RoomSimple struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string           `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                                            // 房间ID
	Hp          int64            `protobuf:"varint,2,opt,name=hp,proto3" json:"hp,omitempty"`                                                                                                                           // 当前血量
	Score       int64            `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`                                                                                                                     // 战斗积分
	ReviveTime  int64            `protobuf:"varint,4,opt,name=revive_time,json=reviveTime,proto3" json:"revive_time,omitempty"`                                                                                         // 宠物复活时间, HP <= 0时有效
	Focus       int32            `protobuf:"varint,5,opt,name=focus,proto3" json:"focus,omitempty"`                                                                                                                     // 0-未被集火; 1-房主标记; 2-超管标记
	FocusAvatar string           `protobuf:"bytes,6,opt,name=focus_avatar,json=focusAvatar,proto3" json:"focus_avatar,omitempty"`                                                                                       // 集火标记头像
	Damaged     map[string]int64 `protobuf:"bytes,7,rep,name=damaged,proto3" json:"damaged,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                         // 当前宠物受到的其他3个房间伤害
	Tdamaged    map[string]int64 `protobuf:"bytes,8,rep,name=tdamaged,proto3" json:"tdamaged,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`                       // 当前战斗受到的其他3个房间总伤害
	SplitScore  map[string]int64 `protobuf:"bytes,9,rep,name=split_score,json=splitScore,proto3" json:"split_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 瓜分积分
}

func (x *RoomSimple) Reset() {
	*x = RoomSimple{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomSimple) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomSimple) ProtoMessage() {}

func (x *RoomSimple) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomSimple.ProtoReflect.Descriptor instead.
func (*RoomSimple) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{14}
}

func (x *RoomSimple) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RoomSimple) GetHp() int64 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *RoomSimple) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RoomSimple) GetReviveTime() int64 {
	if x != nil {
		return x.ReviveTime
	}
	return 0
}

func (x *RoomSimple) GetFocus() int32 {
	if x != nil {
		return x.Focus
	}
	return 0
}

func (x *RoomSimple) GetFocusAvatar() string {
	if x != nil {
		return x.FocusAvatar
	}
	return ""
}

func (x *RoomSimple) GetDamaged() map[string]int64 {
	if x != nil {
		return x.Damaged
	}
	return nil
}

func (x *RoomSimple) GetTdamaged() map[string]int64 {
	if x != nil {
		return x.Tdamaged
	}
	return nil
}

func (x *RoomSimple) GetSplitScore() map[string]int64 {
	if x != nil {
		return x.SplitScore
	}
	return nil
}

type BInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"` // 归属房间ID
}

func (x *BInfoReq) Reset() {
	*x = BInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BInfoReq) ProtoMessage() {}

func (x *BInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BInfoReq.ProtoReflect.Descriptor instead.
func (*BInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{15}
}

func (x *BInfoReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`     // 奖励ID
	Qua  int64  `protobuf:"varint,2,opt,name=qua,proto3" json:"qua,omitempty"`  // 奖励数量
	Img  string `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`   // 图标
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"` // 名称
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{16}
}

func (x *Reward) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Reward) GetQua() int64 {
	if x != nil {
		return x.Qua
	}
	return 0
}

func (x *Reward) GetImg() string {
	if x != nil {
		return x.Img
	}
	return ""
}

func (x *Reward) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RankReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank    int32     `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`      // 排名
	Rewards []*Reward `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"` // 奖励
}

func (x *RankReward) Reset() {
	*x = RankReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankReward) ProtoMessage() {}

func (x *RankReward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankReward.ProtoReflect.Descriptor instead.
func (*RankReward) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{17}
}

func (x *RankReward) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RankReward) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 读取战斗所需的所有数据
type BInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid       string      `protobuf:"bytes,1,opt,name=bid,proto3" json:"bid,omitempty"`                               // 战斗ID
	State     int32       `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`                          // 状态: -1-无战斗; 0-未开始;1-准备阶段;2-战斗;3-结算
	List      []*RoomFull `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                             // 房间列表
	Grank     int32       `protobuf:"varint,4,opt,name=grank,proto3" json:"grank,omitempty"`                          // 全服房间排名
	Buff      string      `protobuf:"bytes,5,opt,name=buff,proto3" json:"buff,omitempty"`                             // 战神buff 房间ID
	AtkAmount int64       `protobuf:"varint,6,opt,name=atk_amount,json=atkAmount,proto3" json:"atk_amount,omitempty"` // 个人当前战斗总伤害
	Balance   int64       `protobuf:"varint,7,opt,name=balance,proto3" json:"balance,omitempty"`                      // 平台币余额
}

func (x *BInfoRsp) Reset() {
	*x = BInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BInfoRsp) ProtoMessage() {}

func (x *BInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BInfoRsp.ProtoReflect.Descriptor instead.
func (*BInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{18}
}

func (x *BInfoRsp) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *BInfoRsp) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *BInfoRsp) GetList() []*RoomFull {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *BInfoRsp) GetGrank() int32 {
	if x != nil {
		return x.Grank
	}
	return 0
}

func (x *BInfoRsp) GetBuff() string {
	if x != nil {
		return x.Buff
	}
	return ""
}

func (x *BInfoRsp) GetAtkAmount() int64 {
	if x != nil {
		return x.AtkAmount
	}
	return 0
}

func (x *BInfoRsp) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type BalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BalanceReq) Reset() {
	*x = BalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceReq) ProtoMessage() {}

func (x *BalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceReq.ProtoReflect.Descriptor instead.
func (*BalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{19}
}

type BalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance int64 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *BalanceRsp) Reset() {
	*x = BalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceRsp) ProtoMessage() {}

func (x *BalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceRsp.ProtoReflect.Descriptor instead.
func (*BalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{20}
}

func (x *BalanceRsp) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type StateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room   string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`                    // 归属房间ID
	Bid    string `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`                      // 战斗ID
	LastTs int64  `protobuf:"varint,3,opt,name=last_ts,json=lastTs,proto3" json:"last_ts,omitempty"` // 上次请求的时间戳, 第一次的话传0
}

func (x *StateReq) Reset() {
	*x = StateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateReq) ProtoMessage() {}

func (x *StateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateReq.ProtoReflect.Descriptor instead.
func (*StateReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{21}
}

func (x *StateReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *StateReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *StateReq) GetLastTs() int64 {
	if x != nil {
		return x.LastTs
	}
	return 0
}

type StateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts     int64         `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`        // 服务器时间
	Buff   string        `protobuf:"bytes,2,opt,name=buff,proto3" json:"buff,omitempty"`     // 战神buff: 房间ID
	List   []*RoomSimple `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`     // 房间列表
	Scores []*ScoreItem  `protobuf:"bytes,4,rep,name=scores,proto3" json:"scores,omitempty"` // 最近一段时间攻击过的玩家
}

func (x *StateRsp) Reset() {
	*x = StateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateRsp) ProtoMessage() {}

func (x *StateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateRsp.ProtoReflect.Descriptor instead.
func (*StateRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{22}
}

func (x *StateRsp) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *StateRsp) GetBuff() string {
	if x != nil {
		return x.Buff
	}
	return ""
}

func (x *StateRsp) GetList() []*RoomSimple {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *StateRsp) GetScores() []*ScoreItem {
	if x != nil {
		return x.Scores
	}
	return nil
}

type ScoreItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Score  int64  `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"` // 攻击获得战斗积分
	Ts     int64  `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`       // 攻击时间
}

func (x *ScoreItem) Reset() {
	*x = ScoreItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScoreItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScoreItem) ProtoMessage() {}

func (x *ScoreItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScoreItem.ProtoReflect.Descriptor instead.
func (*ScoreItem) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{23}
}

func (x *ScoreItem) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *ScoreItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ScoreItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ScoreItem) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ScoreItem) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type AttackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room   string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`                    // 归属房间id
	Bid    string `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`                      // 战斗ID
	Target string `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`                // 攻击目标(房间ID)
	ItemId int64  `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"` // 使用的道具ID
}

func (x *AttackReq) Reset() {
	*x = AttackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttackReq) ProtoMessage() {}

func (x *AttackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttackReq.ProtoReflect.Descriptor instead.
func (*AttackReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{24}
}

func (x *AttackReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *AttackReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *AttackReq) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *AttackReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

type AttackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rate       int64 `protobuf:"varint,1,opt,name=rate,proto3" json:"rate,omitempty"`                               // 攻击倍率
	AtkVal     int64 `protobuf:"varint,2,opt,name=atk_val,json=atkVal,proto3" json:"atk_val,omitempty"`             // 攻击力
	Damage     int64 `protobuf:"varint,3,opt,name=damage,proto3" json:"damage,omitempty"`                           // 造成的伤害
	SelfScore  int64 `protobuf:"varint,4,opt,name=self_score,json=selfScore,proto3" json:"self_score,omitempty"`    // 本房间-战斗积分
	SelfHp     int64 `protobuf:"varint,5,opt,name=self_hp,json=selfHp,proto3" json:"self_hp,omitempty"`             // 本房间-当前血量
	DstScore   int64 `protobuf:"varint,6,opt,name=dst_score,json=dstScore,proto3" json:"dst_score,omitempty"`       // 目标-战斗积分
	DstHp      int64 `protobuf:"varint,7,opt,name=dst_hp,json=dstHp,proto3" json:"dst_hp,omitempty"`                // 目标-当前血量
	ScoreDelta int64 `protobuf:"varint,8,opt,name=score_delta,json=scoreDelta,proto3" json:"score_delta,omitempty"` // 本次攻击增加战斗积分
}

func (x *AttackRsp) Reset() {
	*x = AttackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttackRsp) ProtoMessage() {}

func (x *AttackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttackRsp.ProtoReflect.Descriptor instead.
func (*AttackRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{25}
}

func (x *AttackRsp) GetRate() int64 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *AttackRsp) GetAtkVal() int64 {
	if x != nil {
		return x.AtkVal
	}
	return 0
}

func (x *AttackRsp) GetDamage() int64 {
	if x != nil {
		return x.Damage
	}
	return 0
}

func (x *AttackRsp) GetSelfScore() int64 {
	if x != nil {
		return x.SelfScore
	}
	return 0
}

func (x *AttackRsp) GetSelfHp() int64 {
	if x != nil {
		return x.SelfHp
	}
	return 0
}

func (x *AttackRsp) GetDstScore() int64 {
	if x != nil {
		return x.DstScore
	}
	return 0
}

func (x *AttackRsp) GetDstHp() int64 {
	if x != nil {
		return x.DstHp
	}
	return 0
}

func (x *AttackRsp) GetScoreDelta() int64 {
	if x != nil {
		return x.ScoreDelta
	}
	return 0
}

type GiftAttackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room   string    `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`                    // 归属房间id
	Bid    string    `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`                      // 战斗ID
	Target string    `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`                // 攻击目标(房间ID)
	Tid    string    `protobuf:"bytes,4,opt,name=tid,proto3" json:"tid,omitempty"`                      // 订单ID
	GiftId int64     `protobuf:"varint,5,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"` // 礼物ID
	Meta   *RoomMeta `protobuf:"bytes,6,opt,name=meta,proto3" json:"meta,omitempty"`                    // 透传数据
}

func (x *GiftAttackReq) Reset() {
	*x = GiftAttackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftAttackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftAttackReq) ProtoMessage() {}

func (x *GiftAttackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftAttackReq.ProtoReflect.Descriptor instead.
func (*GiftAttackReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{26}
}

func (x *GiftAttackReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *GiftAttackReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *GiftAttackReq) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *GiftAttackReq) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *GiftAttackReq) GetGiftId() int64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *GiftAttackReq) GetMeta() *RoomMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type GiftAttackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AtkVal     int64 `protobuf:"varint,1,opt,name=atk_val,json=atkVal,proto3" json:"atk_val,omitempty"`             // 攻击力
	Damage     int64 `protobuf:"varint,2,opt,name=damage,proto3" json:"damage,omitempty"`                           // 造成的伤害
	SelfScore  int64 `protobuf:"varint,4,opt,name=self_score,json=selfScore,proto3" json:"self_score,omitempty"`    // 本房间-战斗积分
	SelfHp     int64 `protobuf:"varint,5,opt,name=self_hp,json=selfHp,proto3" json:"self_hp,omitempty"`             // 本房间-当前血量
	DstScore   int64 `protobuf:"varint,6,opt,name=dst_score,json=dstScore,proto3" json:"dst_score,omitempty"`       // 目标-战斗积分
	DstHp      int64 `protobuf:"varint,7,opt,name=dst_hp,json=dstHp,proto3" json:"dst_hp,omitempty"`                // 目标-当前血量
	ScoreDelta int64 `protobuf:"varint,8,opt,name=score_delta,json=scoreDelta,proto3" json:"score_delta,omitempty"` // 本次攻击增加战斗积分
}

func (x *GiftAttackRsp) Reset() {
	*x = GiftAttackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftAttackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftAttackRsp) ProtoMessage() {}

func (x *GiftAttackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftAttackRsp.ProtoReflect.Descriptor instead.
func (*GiftAttackRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{27}
}

func (x *GiftAttackRsp) GetAtkVal() int64 {
	if x != nil {
		return x.AtkVal
	}
	return 0
}

func (x *GiftAttackRsp) GetDamage() int64 {
	if x != nil {
		return x.Damage
	}
	return 0
}

func (x *GiftAttackRsp) GetSelfScore() int64 {
	if x != nil {
		return x.SelfScore
	}
	return 0
}

func (x *GiftAttackRsp) GetSelfHp() int64 {
	if x != nil {
		return x.SelfHp
	}
	return 0
}

func (x *GiftAttackRsp) GetDstScore() int64 {
	if x != nil {
		return x.DstScore
	}
	return 0
}

func (x *GiftAttackRsp) GetDstHp() int64 {
	if x != nil {
		return x.DstHp
	}
	return 0
}

func (x *GiftAttackRsp) GetScoreDelta() int64 {
	if x != nil {
		return x.ScoreDelta
	}
	return 0
}

type GiftHealReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room   string    `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`                    // 归属房间id
	Bid    string    `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`                      // 战斗ID
	Tid    string    `protobuf:"bytes,3,opt,name=tid,proto3" json:"tid,omitempty"`                      // 订单ID
	GiftId int64     `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"` // 礼物ID
	Meta   *RoomMeta `protobuf:"bytes,5,opt,name=meta,proto3" json:"meta,omitempty"`                    // 透传数据
}

func (x *GiftHealReq) Reset() {
	*x = GiftHealReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftHealReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftHealReq) ProtoMessage() {}

func (x *GiftHealReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftHealReq.ProtoReflect.Descriptor instead.
func (*GiftHealReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{28}
}

func (x *GiftHealReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *GiftHealReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *GiftHealReq) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *GiftHealReq) GetGiftId() int64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *GiftHealReq) GetMeta() *RoomMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type GiftHealRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Blood  int64 `protobuf:"varint,1,opt,name=blood,proto3" json:"blood,omitempty"`   // 恢复的血量
	Hp     int64 `protobuf:"varint,2,opt,name=hp,proto3" json:"hp,omitempty"`         // 剩余血量
	Status int64 `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"` // 恢复状态: -1: 已死亡, 1: 血量满, 0: 正常加血了
}

func (x *GiftHealRsp) Reset() {
	*x = GiftHealRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftHealRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftHealRsp) ProtoMessage() {}

func (x *GiftHealRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftHealRsp.ProtoReflect.Descriptor instead.
func (*GiftHealRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{29}
}

func (x *GiftHealRsp) GetBlood() int64 {
	if x != nil {
		return x.Blood
	}
	return 0
}

func (x *GiftHealRsp) GetHp() int64 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *GiftHealRsp) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type GiftBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room       string    `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`                                // 归属房间id
	Bid        string    `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`                                  // 战斗ID
	Tid        string    `protobuf:"bytes,3,opt,name=tid,proto3" json:"tid,omitempty"`                                  // 订单ID
	GiftId     int64     `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`             // 礼物ID
	GiftNum    int32     `protobuf:"varint,5,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`          // 礼物份数
	ItemTarget int32     `protobuf:"varint,6,opt,name=item_target,json=itemTarget,proto3" json:"item_target,omitempty"` // 待购买道具
	Meta       *RoomMeta `protobuf:"bytes,7,opt,name=meta,proto3" json:"meta,omitempty"`                                // 透传数据
}

func (x *GiftBuyReq) Reset() {
	*x = GiftBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftBuyReq) ProtoMessage() {}

func (x *GiftBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftBuyReq.ProtoReflect.Descriptor instead.
func (*GiftBuyReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{30}
}

func (x *GiftBuyReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *GiftBuyReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *GiftBuyReq) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *GiftBuyReq) GetGiftId() int64 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *GiftBuyReq) GetGiftNum() int32 {
	if x != nil {
		return x.GiftNum
	}
	return 0
}

func (x *GiftBuyReq) GetItemTarget() int32 {
	if x != nil {
		return x.ItemTarget
	}
	return 0
}

func (x *GiftBuyReq) GetMeta() *RoomMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type GiftBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance int32 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"` // 购买的道具余额
}

func (x *GiftBuyRsp) Reset() {
	*x = GiftBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftBuyRsp) ProtoMessage() {}

func (x *GiftBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftBuyRsp.ProtoReflect.Descriptor instead.
func (*GiftBuyRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{31}
}

func (x *GiftBuyRsp) GetBalance() int32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type RankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid  string `protobuf:"bytes,1,opt,name=bid,proto3" json:"bid,omitempty"`   // 战斗ID
	Room string `protobuf:"bytes,2,opt,name=room,proto3" json:"room,omitempty"` // 归属房间id
}

func (x *RankReq) Reset() {
	*x = RankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankReq) ProtoMessage() {}

func (x *RankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankReq.ProtoReflect.Descriptor instead.
func (*RankReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{32}
}

func (x *RankReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *RankReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type RankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Us []*CRItem `protobuf:"bytes,1,rep,name=us,proto3" json:"us,omitempty"` // 最佳战神TOP5
}

func (x *RankRsp) Reset() {
	*x = RankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankRsp) ProtoMessage() {}

func (x *RankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankRsp.ProtoReflect.Descriptor instead.
func (*RankRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{33}
}

func (x *RankRsp) GetUs() []*CRItem {
	if x != nil {
		return x.Us
	}
	return nil
}

type BResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"` // 归属房间ID
}

func (x *BResultReq) Reset() {
	*x = BResultReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BResultReq) ProtoMessage() {}

func (x *BResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BResultReq.ProtoReflect.Descriptor instead.
func (*BResultReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{34}
}

func (x *BResultReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type Pet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                              // 房间ID
	Avatar   string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`                      // 房间头像
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                          // 房间名称
	DanScore int32  `protobuf:"varint,4,opt,name=dan_score,json=danScore,proto3" json:"dan_score,omitempty"` // 段位分数
	DanDelta int32  `protobuf:"varint,5,opt,name=dan_delta,json=danDelta,proto3" json:"dan_delta,omitempty"` // 段位分增量
	Dan      int32  `protobuf:"varint,6,opt,name=dan,proto3" json:"dan,omitempty"`                           // 段位标识
	Tdamage  int64  `protobuf:"varint,7,opt,name=tdamage,proto3" json:"tdamage,omitempty"`                   // 总伤害
	Score    int64  `protobuf:"varint,8,opt,name=score,proto3" json:"score,omitempty"`                       // 本场获得战斗积分
}

func (x *Pet) Reset() {
	*x = Pet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet) ProtoMessage() {}

func (x *Pet) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet.ProtoReflect.Descriptor instead.
func (*Pet) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{35}
}

func (x *Pet) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Pet) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Pet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Pet) GetDanScore() int32 {
	if x != nil {
		return x.DanScore
	}
	return 0
}

func (x *Pet) GetDanDelta() int32 {
	if x != nil {
		return x.DanDelta
	}
	return 0
}

func (x *Pet) GetDan() int32 {
	if x != nil {
		return x.Dan
	}
	return 0
}

func (x *Pet) GetTdamage() int64 {
	if x != nil {
		return x.Tdamage
	}
	return 0
}

func (x *Pet) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type BResultRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PetList    []*Pet `protobuf:"bytes,1,rep,name=pet_list,json=petList,proto3" json:"pet_list,omitempty"`           // 4个房间列表
	SelfAtk    int32  `protobuf:"varint,2,opt,name=self_atk,json=selfAtk,proto3" json:"self_atk,omitempty"`          // 自己在该房间的输出伤害
	Bid        string `protobuf:"bytes,3,opt,name=bid,proto3" json:"bid,omitempty"`                                  // 战斗ID
	SettleOver bool   `protobuf:"varint,4,opt,name=settle_over,json=settleOver,proto3" json:"settle_over,omitempty"` // 结算结束
	Settle     bool   `protobuf:"varint,5,opt,name=settle,proto3" json:"settle,omitempty"`                           // 是否需要结算
}

func (x *BResultRsp) Reset() {
	*x = BResultRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BResultRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BResultRsp) ProtoMessage() {}

func (x *BResultRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BResultRsp.ProtoReflect.Descriptor instead.
func (*BResultRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{36}
}

func (x *BResultRsp) GetPetList() []*Pet {
	if x != nil {
		return x.PetList
	}
	return nil
}

func (x *BResultRsp) GetSelfAtk() int32 {
	if x != nil {
		return x.SelfAtk
	}
	return 0
}

func (x *BResultRsp) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *BResultRsp) GetSettleOver() bool {
	if x != nil {
		return x.SettleOver
	}
	return false
}

func (x *BResultRsp) GetSettle() bool {
	if x != nil {
		return x.Settle
	}
	return false
}

type PrevStatsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"` // 归属房间ID
}

func (x *PrevStatsReq) Reset() {
	*x = PrevStatsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrevStatsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrevStatsReq) ProtoMessage() {}

func (x *PrevStatsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrevStatsReq.ProtoReflect.Descriptor instead.
func (*PrevStatsReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{37}
}

func (x *PrevStatsReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type SSM struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank        int32     `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`                                  // 排名
	Id          string    `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                                       // 房间ID
	Name        string    `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                   // 房间名
	Avatar      string    `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`                               // 房间头像
	Score       int64     `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`                                // 本场获得战斗积分
	TotalDamage int64     `protobuf:"varint,6,opt,name=total_damage,json=totalDamage,proto3" json:"total_damage,omitempty"` // 本场输出伤害
	DanScore    int64     `protobuf:"varint,7,opt,name=dan_score,json=danScore,proto3" json:"dan_score,omitempty"`          // 段位分
	Rewards     []*Reward `protobuf:"bytes,8,rep,name=rewards,proto3" json:"rewards,omitempty"`                             // 战斗内奖励
}

func (x *SSM) Reset() {
	*x = SSM{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SSM) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSM) ProtoMessage() {}

func (x *SSM) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSM.ProtoReflect.Descriptor instead.
func (*SSM) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{38}
}

func (x *SSM) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *SSM) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SSM) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SSM) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *SSM) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SSM) GetTotalDamage() int64 {
	if x != nil {
		return x.TotalDamage
	}
	return 0
}

func (x *SSM) GetDanScore() int64 {
	if x != nil {
		return x.DanScore
	}
	return 0
}

func (x *SSM) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type PrevStatsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SettleTime int64     `protobuf:"varint,1,opt,name=settle_time,json=settleTime,proto3" json:"settle_time,omitempty"` // 结算日期
	Bid        string    `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`                                  // 战斗ID
	SelfAtk    int32     `protobuf:"varint,3,opt,name=self_atk,json=selfAtk,proto3" json:"self_atk,omitempty"`          // 自己的总伤害
	Us         []*CRItem `protobuf:"bytes,4,rep,name=us,proto3" json:"us,omitempty"`                                    // 本厂最佳战神 x5
	Ssm        []*SSM    `protobuf:"bytes,5,rep,name=ssm,proto3" json:"ssm,omitempty"`                                  // 战况数据 x4
}

func (x *PrevStatsRsp) Reset() {
	*x = PrevStatsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrevStatsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrevStatsRsp) ProtoMessage() {}

func (x *PrevStatsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrevStatsRsp.ProtoReflect.Descriptor instead.
func (*PrevStatsRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{39}
}

func (x *PrevStatsRsp) GetSettleTime() int64 {
	if x != nil {
		return x.SettleTime
	}
	return 0
}

func (x *PrevStatsRsp) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *PrevStatsRsp) GetSelfAtk() int32 {
	if x != nil {
		return x.SelfAtk
	}
	return 0
}

func (x *PrevStatsRsp) GetUs() []*CRItem {
	if x != nil {
		return x.Us
	}
	return nil
}

func (x *PrevStatsRsp) GetSsm() []*SSM {
	if x != nil {
		return x.Ssm
	}
	return nil
}

type DamageRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`  // 归属房间ID
	Rank int32  `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"` // 从这个排名(包含)开始读取
}

func (x *DamageRankReq) Reset() {
	*x = DamageRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DamageRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DamageRankReq) ProtoMessage() {}

func (x *DamageRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DamageRankReq.ProtoReflect.Descriptor instead.
func (*DamageRankReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{40}
}

func (x *DamageRankReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *DamageRankReq) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

type DamageRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List    []*CRItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Self    *CRItem   `protobuf:"bytes,2,opt,name=self,proto3" json:"self,omitempty"`
	Rewards []*Reward `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"` // 房间奖励
}

func (x *DamageRankRsp) Reset() {
	*x = DamageRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DamageRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DamageRankRsp) ProtoMessage() {}

func (x *DamageRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DamageRankRsp.ProtoReflect.Descriptor instead.
func (*DamageRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{41}
}

func (x *DamageRankRsp) GetList() []*CRItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DamageRankRsp) GetSelf() *CRItem {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *DamageRankRsp) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type ConveneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"` // 归属房间ID
	Bid  string `protobuf:"bytes,2,opt,name=bid,proto3" json:"bid,omitempty"`   // 战斗id
}

func (x *ConveneReq) Reset() {
	*x = ConveneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConveneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConveneReq) ProtoMessage() {}

func (x *ConveneReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConveneReq.ProtoReflect.Descriptor instead.
func (*ConveneReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{42}
}

func (x *ConveneReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *ConveneReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

type ConveneRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConveneRsp) Reset() {
	*x = ConveneRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConveneRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConveneRsp) ProtoMessage() {}

func (x *ConveneRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConveneRsp.ProtoReflect.Descriptor instead.
func (*ConveneRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{43}
}

type BaseInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room string `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"` // 房间id
}

func (x *BaseInfoReq) Reset() {
	*x = BaseInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseInfoReq) ProtoMessage() {}

func (x *BaseInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseInfoReq.ProtoReflect.Descriptor instead.
func (*BaseInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{44}
}

func (x *BaseInfoReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

type BaseInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Room     string        `protobuf:"bytes,1,opt,name=room,proto3" json:"room,omitempty"`          // 房间id
	Name     string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`          // 房间名
	Avatar   string        `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`      // 房间头像
	Uname    string        `protobuf:"bytes,4,opt,name=uname,proto3" json:"uname,omitempty"`        // 玩家昵称
	Uavatar  string        `protobuf:"bytes,5,opt,name=uavatar,proto3" json:"uavatar,omitempty"`    // 玩家头像
	Bid      string        `protobuf:"bytes,6,opt,name=bid,proto3" json:"bid,omitempty"`            // 战斗id
	Dan      int32         `protobuf:"varint,7,opt,name=dan,proto3" json:"dan,omitempty"`           // 房间段位
	Role     int32         `protobuf:"varint,8,opt,name=role,proto3" json:"role,omitempty"`         // 角色类型, 参考 petpk.PlayerType
	PetId    string        `protobuf:"bytes,9,opt,name=petId,proto3" json:"petId,omitempty"`        // 宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)
	PetPhase string        `protobuf:"bytes,10,opt,name=petPhase,proto3" json:"petPhase,omitempty"` // 宠物形态所在的阶段
	Rrs      []*RankReward `protobuf:"bytes,11,rep,name=rrs,proto3" json:"rrs,omitempty"`           // 1~4名的奖励, 匹配前后可能不一样, 奖励只会变高, 不会变少
	Settle   bool          `protobuf:"varint,12,opt,name=settle,proto3" json:"settle,omitempty"`    // 是否可以结算
	Debug    string        `protobuf:"bytes,13,opt,name=debug,proto3" json:"debug,omitempty"`
}

func (x *BaseInfoRsp) Reset() {
	*x = BaseInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BaseInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseInfoRsp) ProtoMessage() {}

func (x *BaseInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseInfoRsp.ProtoReflect.Descriptor instead.
func (*BaseInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{45}
}

func (x *BaseInfoRsp) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *BaseInfoRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BaseInfoRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *BaseInfoRsp) GetUname() string {
	if x != nil {
		return x.Uname
	}
	return ""
}

func (x *BaseInfoRsp) GetUavatar() string {
	if x != nil {
		return x.Uavatar
	}
	return ""
}

func (x *BaseInfoRsp) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *BaseInfoRsp) GetDan() int32 {
	if x != nil {
		return x.Dan
	}
	return 0
}

func (x *BaseInfoRsp) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *BaseInfoRsp) GetPetId() string {
	if x != nil {
		return x.PetId
	}
	return ""
}

func (x *BaseInfoRsp) GetPetPhase() string {
	if x != nil {
		return x.PetPhase
	}
	return ""
}

func (x *BaseInfoRsp) GetRrs() []*RankReward {
	if x != nil {
		return x.Rrs
	}
	return nil
}

func (x *BaseInfoRsp) GetSettle() bool {
	if x != nil {
		return x.Settle
	}
	return false
}

func (x *BaseInfoRsp) GetDebug() string {
	if x != nil {
		return x.Debug
	}
	return ""
}

type DamageGlobalRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DamageGlobalRankReq) Reset() {
	*x = DamageGlobalRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DamageGlobalRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DamageGlobalRankReq) ProtoMessage() {}

func (x *DamageGlobalRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DamageGlobalRankReq.ProtoReflect.Descriptor instead.
func (*DamageGlobalRankReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{46}
}

type DamageRankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank   int32  `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`     // 排名
	Uid    uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`       // 玩家id
	Name   string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`      // 玩家昵称
	Avatar string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`  // 头像
	Damage int64  `protobuf:"varint,5,opt,name=damage,proto3" json:"damage,omitempty"` // 造成的伤害
}

func (x *DamageRankItem) Reset() {
	*x = DamageRankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DamageRankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DamageRankItem) ProtoMessage() {}

func (x *DamageRankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DamageRankItem.ProtoReflect.Descriptor instead.
func (*DamageRankItem) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{47}
}

func (x *DamageRankItem) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *DamageRankItem) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *DamageRankItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DamageRankItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *DamageRankItem) GetDamage() int64 {
	if x != nil {
		return x.Damage
	}
	return 0
}

type DamageGlobalRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tops        []*DamageRankItem `protobuf:"bytes,1,rep,name=tops,proto3" json:"tops,omitempty"`                                  // 榜上玩家数据
	RankRewards []*RankReward     `protobuf:"bytes,2,rep,name=rank_rewards,json=rankRewards,proto3" json:"rank_rewards,omitempty"` // 名次及对应的奖励
	Self        *DamageRankItem   `protobuf:"bytes,3,opt,name=self,proto3" json:"self,omitempty"`                                  // 自己的数据
}

func (x *DamageGlobalRankRsp) Reset() {
	*x = DamageGlobalRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DamageGlobalRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DamageGlobalRankRsp) ProtoMessage() {}

func (x *DamageGlobalRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DamageGlobalRankRsp.ProtoReflect.Descriptor instead.
func (*DamageGlobalRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{48}
}

func (x *DamageGlobalRankRsp) GetTops() []*DamageRankItem {
	if x != nil {
		return x.Tops
	}
	return nil
}

func (x *DamageGlobalRankRsp) GetRankRewards() []*RankReward {
	if x != nil {
		return x.RankRewards
	}
	return nil
}

func (x *DamageGlobalRankRsp) GetSelf() *DamageRankItem {
	if x != nil {
		return x.Self
	}
	return nil
}

type MarqueeDamageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MarqueeDamageReq) Reset() {
	*x = MarqueeDamageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarqueeDamageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarqueeDamageReq) ProtoMessage() {}

func (x *MarqueeDamageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarqueeDamageReq.ProtoReflect.Descriptor instead.
func (*MarqueeDamageReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{49}
}

type MarqueeDamageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tops []*DamageRankItem `protobuf:"bytes,1,rep,name=tops,proto3" json:"tops,omitempty"` // 榜上玩家数据
}

func (x *MarqueeDamageRsp) Reset() {
	*x = MarqueeDamageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarqueeDamageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarqueeDamageRsp) ProtoMessage() {}

func (x *MarqueeDamageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarqueeDamageRsp.ProtoReflect.Descriptor instead.
func (*MarqueeDamageRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{50}
}

func (x *MarqueeDamageRsp) GetTops() []*DamageRankItem {
	if x != nil {
		return x.Tops
	}
	return nil
}

type MarkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bid    string `protobuf:"bytes,1,opt,name=bid,proto3" json:"bid,omitempty"`       // 战斗ID
	Room   string `protobuf:"bytes,2,opt,name=room,proto3" json:"room,omitempty"`     // 归属房间id
	Target string `protobuf:"bytes,4,opt,name=target,proto3" json:"target,omitempty"` // 标记目标(房间ID)
}

func (x *MarkReq) Reset() {
	*x = MarkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkReq) ProtoMessage() {}

func (x *MarkReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkReq.ProtoReflect.Descriptor instead.
func (*MarkReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{51}
}

func (x *MarkReq) GetBid() string {
	if x != nil {
		return x.Bid
	}
	return ""
}

func (x *MarkReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *MarkReq) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

type MarkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mark bool `protobuf:"varint,1,opt,name=mark,proto3" json:"mark,omitempty"` // 标记成功还是失败
}

func (x *MarkRsp) Reset() {
	*x = MarkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkRsp) ProtoMessage() {}

func (x *MarkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkRsp.ProtoReflect.Descriptor instead.
func (*MarkRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{52}
}

func (x *MarkRsp) GetMark() bool {
	if x != nil {
		return x.Mark
	}
	return false
}

type ListItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListItemReq) Reset() {
	*x = ListItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListItemReq) ProtoMessage() {}

func (x *ListItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListItemReq.ProtoReflect.Descriptor instead.
func (*ListItemReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{53}
}

type ZItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                          // 道具id
	Type  int32             `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                                                                      // 类型 1-普通道具, 2-攻击礼物, 3-回血礼物, 4-购买
	Name  string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                       // 名称
	Val1  int32             `protobuf:"varint,4,opt,name=val1,proto3" json:"val1,omitempty"`                                                                                      // 攻击力 | 回血比例1000=100%
	Val2  int32             `protobuf:"varint,5,opt,name=val2,proto3" json:"val2,omitempty"`                                                                                      // 无用 | 价格
	Score int32             `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`                                                                                    // 战斗积分
	Max   int32             `protobuf:"varint,7,opt,name=max,proto3" json:"max,omitempty"`                                                                                        // 持有上限
	Ext   map[string]string `protobuf:"bytes,8,rep,name=ext,proto3" json:"ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展
}

func (x *ZItem) Reset() {
	*x = ZItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZItem) ProtoMessage() {}

func (x *ZItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZItem.ProtoReflect.Descriptor instead.
func (*ZItem) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{54}
}

func (x *ZItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ZItem) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ZItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ZItem) GetVal1() int32 {
	if x != nil {
		return x.Val1
	}
	return 0
}

func (x *ZItem) GetVal2() int32 {
	if x != nil {
		return x.Val2
	}
	return 0
}

func (x *ZItem) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ZItem) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *ZItem) GetExt() map[string]string {
	if x != nil {
		return x.Ext
	}
	return nil
}

type ZShop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num   int32 `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`                  // 礼物份数
	Price int32 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`              // 价格
	X1Num int32 `protobuf:"varint,3,opt,name=x1_num,json=x1Num,proto3" json:"x1_num,omitempty"` // 道具1数量
	X2Num int32 `protobuf:"varint,4,opt,name=x2_num,json=x2Num,proto3" json:"x2_num,omitempty"` // 道具2数量
	X3Num int32 `protobuf:"varint,5,opt,name=x3_num,json=x3Num,proto3" json:"x3_num,omitempty"` // 道具3数量
}

func (x *ZShop) Reset() {
	*x = ZShop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZShop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZShop) ProtoMessage() {}

func (x *ZShop) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZShop.ProtoReflect.Descriptor instead.
func (*ZShop) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{55}
}

func (x *ZShop) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *ZShop) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ZShop) GetX1Num() int32 {
	if x != nil {
		return x.X1Num
	}
	return 0
}

func (x *ZShop) GetX2Num() int32 {
	if x != nil {
		return x.X2Num
	}
	return 0
}

func (x *ZShop) GetX3Num() int32 {
	if x != nil {
		return x.X3Num
	}
	return 0
}

type XItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`   // 道具id
	Num int64 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` // 道具数量
}

func (x *XItem) Reset() {
	*x = XItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *XItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*XItem) ProtoMessage() {}

func (x *XItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use XItem.ProtoReflect.Descriptor instead.
func (*XItem) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{56}
}

func (x *XItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *XItem) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type ListItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundEndTime int64    `protobuf:"varint,1,opt,name=round_end_time,json=roundEndTime,proto3" json:"round_end_time,omitempty"` // 轮次结束时间
	Items        []*XItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`                                      // 道具列表
	RoundId      string   `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"`                                  // 轮次ID
}

func (x *ListItemRsp) Reset() {
	*x = ListItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListItemRsp) ProtoMessage() {}

func (x *ListItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListItemRsp.ProtoReflect.Descriptor instead.
func (*ListItemRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{57}
}

func (x *ListItemRsp) GetRoundEndTime() int64 {
	if x != nil {
		return x.RoundEndTime
	}
	return 0
}

func (x *ListItemRsp) GetItems() []*XItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListItemRsp) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type BStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`     // 游戏ID
	Room    string `protobuf:"bytes,2,opt,name=room,proto3" json:"room,omitempty"`       // 房间ID
	RoundId string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 轮次ID
}

func (x *BStateReq) Reset() {
	*x = BStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BStateReq) ProtoMessage() {}

func (x *BStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BStateReq.ProtoReflect.Descriptor instead.
func (*BStateReq) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{58}
}

func (x *BStateReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BStateReq) GetRoom() string {
	if x != nil {
		return x.Room
	}
	return ""
}

func (x *BStateReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type BStateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank       int32       `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`                              // 当前房间排名
	Score      int32       `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`                            // 当前房间战斗积分
	RewardTop1 *RankReward `protobuf:"bytes,3,opt,name=reward_top1,json=rewardTop1,proto3" json:"reward_top1,omitempty"` // 第一名奖励乘过系数
}

func (x *BStateRsp) Reset() {
	*x = BStateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_battle_battle_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BStateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BStateRsp) ProtoMessage() {}

func (x *BStateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_battle_battle_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BStateRsp.ProtoReflect.Descriptor instead.
func (*BStateRsp) Descriptor() ([]byte, []int) {
	return file_pb_petpk_battle_battle_proto_rawDescGZIP(), []int{59}
}

func (x *BStateRsp) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *BStateRsp) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *BStateRsp) GetRewardTop1() *RankReward {
	if x != nil {
		return x.RewardTop1
	}
	return nil
}

var File_pb_petpk_battle_battle_proto protoreflect.FileDescriptor

var file_pb_petpk_battle_battle_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2f, 0x62, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x2f, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x1a, 0x31, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65,
	0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x70, 0x62, 0x2f, 0x70, 0x65, 0x74,
	0x70, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x62, 0x2f, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2f, 0x70, 0x65, 0x74, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x13, 0x48, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xaf, 0x01, 0x0a,
	0x13, 0x48, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x6b, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x48, 0x70, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x48, 0x70,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d,
	0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f,
	0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x3d,
	0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f,
	0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x97, 0x01,
	0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x73, 0x65, 0x6c,
	0x66, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x1f, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x22, 0x6b, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x35,
	0x0a, 0x0f, 0x67, 0x64, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0d, 0x67, 0x64, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xc1, 0x01, 0x0a, 0x08, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49,
	0x64, 0x12, 0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x5f,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x79,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x26, 0x0a, 0x09, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x76,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x56, 0x65,
	0x72, 0x22, 0x72, 0x0a, 0x09, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x44, 0x65, 0x66, 0x12, 0x1e,
	0x0a, 0x0b, 0x61, 0x74, 0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x74, 0x6b, 0x56, 0x61, 0x6c, 0x4d, 0x69, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x64, 0x72,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x6b, 0x5f, 0x66, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x74, 0x6b, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x83, 0x02, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x74, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x12, 0x27,
	0x0a, 0x04, 0x63, 0x75, 0x72, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x58, 0x50, 0x6c, 0x61,
	0x6e, 0x52, 0x04, 0x63, 0x75, 0x72, 0x72, 0x12, 0x27, 0x0a, 0x04, 0x6e, 0x65, 0x78, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x58, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x04, 0x6e, 0x65, 0x78, 0x74,
	0x12, 0x22, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x5a, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x4e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x44, 0x65, 0x66, 0x52, 0x06, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x2b,
	0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x70, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x5a, 0x53, 0x68, 0x6f, 0x70,
	0x52, 0x09, 0x73, 0x68, 0x6f, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xa5, 0x01, 0x0a, 0x06,
	0x43, 0x52, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x32, 0x22, 0x1d, 0x0a, 0x07, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6f, 0x6d, 0x22, 0x99, 0x01, 0x0a, 0x07, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x68, 0x70, 0x5f, 0x64, 0x61, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x68, 0x70, 0x44, 0x61, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x68,
	0x70, 0x5f, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x68, 0x70, 0x45,
	0x78, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x68, 0x70, 0x5f, 0x6d, 0x61, 0x78, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x68, 0x70, 0x4d, 0x61, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e,
	0x65, 0x78, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x67, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0xd4,
	0x03, 0x0a, 0x08, 0x52, 0x6f, 0x6f, 0x6d, 0x46, 0x75, 0x6c, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x64, 0x61, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x6d, 0x61, 0x78, 0x5f, 0x68, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6d, 0x61, 0x78, 0x48, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x68, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x76, 0x69, 0x76, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x76,
	0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x65, 0x74, 0x50, 0x68, 0x61, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x65, 0x74, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x07, 0x64, 0x61, 0x6d,
	0x61, 0x67, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x65, 0x74,
	0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x46, 0x75, 0x6c, 0x6c, 0x2e, 0x44, 0x61, 0x6d, 0x61,
	0x67, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65,
	0x64, 0x12, 0x39, 0x0a, 0x08, 0x74, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d,
	0x46, 0x75, 0x6c, 0x6c, 0x2e, 0x54, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x08, 0x74, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x1a, 0x3a, 0x0a, 0x0c,
	0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x54, 0x64, 0x61, 0x6d,
	0x61, 0x67, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8f, 0x04, 0x0a, 0x0a, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x68, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x76, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x72, 0x65, 0x76, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x6f, 0x63, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x66, 0x6f, 0x63, 0x75,
	0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x6f, 0x63, 0x75, 0x73, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x38, 0x0a, 0x07, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x12, 0x3b,
	0x0a, 0x08, 0x74, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x2e, 0x54, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x74, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x0b, 0x73,
	0x70, 0x6c, 0x69, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x2e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x1a,
	0x3a, 0x0a, 0x0c, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x54,
	0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x1e, 0x0a, 0x08, 0x42, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x22, 0x50, 0x0a, 0x06, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x71, 0x75, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x49, 0x0a, 0x0a, 0x52, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x07, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x08, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x62, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e,
	0x52, 0x6f, 0x6f, 0x6d, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x67, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x67,
	0x72, 0x61, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x75, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x62, 0x75, 0x66, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x6b, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x74,
	0x6b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x0c, 0x0a, 0x0a, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x22,
	0x26, 0x0a, 0x0a, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x49, 0x0a, 0x08, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74,
	0x54, 0x73, 0x22, 0x7f, 0x0a, 0x08, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x62, 0x75, 0x66, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x75,
	0x66, 0x66, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x73, 0x22, 0x6f, 0x0a, 0x09, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x74, 0x73, 0x22, 0x62, 0x0a, 0x09, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x22, 0xdd, 0x01, 0x0a, 0x09, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x74,
	0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x74, 0x6b,
	0x56, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x6c, 0x66, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x6c, 0x66, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65,
	0x6c, 0x66, 0x5f, 0x68, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6c,
	0x66, 0x48, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x73, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x73, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x64, 0x73, 0x74, 0x5f, 0x68, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x64, 0x73, 0x74, 0x48, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x5f, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x22, 0x9d, 0x01, 0x0a, 0x0d, 0x47, 0x69, 0x66,
	0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x10,
	0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x67, 0x69, 0x66,
	0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x22, 0xcd, 0x01, 0x0a, 0x0d, 0x47, 0x69, 0x66,
	0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x74,
	0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x74, 0x6b,
	0x56, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x6c, 0x66, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x6c, 0x66, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65,
	0x6c, 0x66, 0x5f, 0x68, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6c,
	0x66, 0x48, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x73, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x73, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x64, 0x73, 0x74, 0x5f, 0x68, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x64, 0x73, 0x74, 0x48, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x5f, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x22, 0x83, 0x01, 0x0a, 0x0b, 0x47, 0x69, 0x66,
	0x74, 0x48, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x10, 0x0a, 0x03,
	0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e,
	0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x22, 0x4b,
	0x0a, 0x0b, 0x47, 0x69, 0x66, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x62, 0x6c, 0x6f, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x62, 0x6c,
	0x6f, 0x6f, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x68, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x0a,
	0x47, 0x69, 0x66, 0x74, 0x42, 0x75, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x10,
	0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x69, 0x66, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67,
	0x69, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x74, 0x65,
	0x6d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x23, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x22, 0x26, 0x0a, 0x0a,
	0x47, 0x69, 0x66, 0x74, 0x42, 0x75, 0x79, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x22, 0x2f, 0x0a, 0x07, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x22, 0x28, 0x0a, 0x07, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70,
	0x12, 0x1d, 0x0a, 0x02, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x43, 0x52, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x75, 0x73, 0x22,
	0x20, 0x0a, 0x0a, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f,
	0x6d, 0x22, 0xbd, 0x01, 0x0a, 0x03, 0x50, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x6e, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x6e, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x6e, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x12,
	0x10, 0x0a, 0x03, 0x64, 0x61, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x61,
	0x6e, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x74, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x22, 0x99, 0x01, 0x0a, 0x0a, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x25, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x50, 0x65, 0x74, 0x52, 0x07,
	0x70, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x66, 0x5f,
	0x61, 0x74, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x65, 0x6c, 0x66, 0x41,
	0x74, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x62, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x22, 0x22, 0x0a,
	0x0c, 0x50, 0x72, 0x65, 0x76, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f,
	0x6d, 0x22, 0xd4, 0x01, 0x0a, 0x03, 0x53, 0x53, 0x4d, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x61, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x6e, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x61, 0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x27, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x0c, 0x50, 0x72, 0x65,
	0x76, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x65, 0x6c, 0x66, 0x5f, 0x61, 0x74, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x73, 0x65, 0x6c, 0x66, 0x41, 0x74, 0x6b, 0x12, 0x1d, 0x0a, 0x02, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x43, 0x52, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x02, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x03, 0x73, 0x73, 0x6d, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x53, 0x53, 0x4d, 0x52,
	0x03, 0x73, 0x73, 0x6d, 0x22, 0x37, 0x0a, 0x0d, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x61,
	0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0x7e, 0x0a,
	0x0d, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x21,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x43, 0x52, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x43, 0x52, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04,
	0x73, 0x65, 0x6c, 0x66, 0x12, 0x27, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x32, 0x0a,
	0x0a, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12,
	0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x62, 0x69,
	0x64, 0x22, 0x0c, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x22,
	0x21, 0x0a, 0x0b, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f,
	0x6f, 0x6d, 0x22, 0xba, 0x02, 0x0a, 0x0b, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x75, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x62, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x64, 0x61, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x50, 0x68, 0x61, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x03,
	0x72, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x03, 0x72, 0x72,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x62,
	0x75, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x22,
	0x15, 0x0a, 0x13, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x22, 0x7a, 0x0a, 0x0e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x61, 0x6d, 0x61,
	0x67, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x13, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x6f,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x74, 0x6f, 0x70, 0x73, 0x12, 0x34, 0x0a, 0x0c, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0b,
	0x72, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x04, 0x73,
	0x65, 0x6c, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x22, 0x12, 0x0a, 0x10, 0x4d, 0x61, 0x72, 0x71, 0x75, 0x65,
	0x65, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x22, 0x3d, 0x0a, 0x10, 0x4d, 0x61,
	0x72, 0x71, 0x75, 0x65, 0x65, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x29,
	0x0a, 0x04, 0x74, 0x6f, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x04, 0x74, 0x6f, 0x70, 0x73, 0x22, 0x47, 0x0a, 0x07, 0x4d, 0x61, 0x72,
	0x6b, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x22, 0x1d, 0x0a, 0x07, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6d, 0x61, 0x72,
	0x6b, 0x22, 0x0d, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71,
	0x22, 0xf0, 0x01, 0x0a, 0x05, 0x5a, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x61, 0x6c, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x76, 0x61, 0x6c, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x61, 0x6c, 0x32, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x76, 0x61, 0x6c, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d,
	0x61, 0x78, 0x12, 0x27, 0x0a, 0x03, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x5a, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x45, 0x78,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x65, 0x78, 0x74, 0x1a, 0x36, 0x0a, 0x08, 0x45,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x74, 0x0a, 0x05, 0x5a, 0x53, 0x68, 0x6f, 0x70, 0x12, 0x10, 0x0a, 0x03,
	0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x78, 0x31, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x78, 0x31, 0x4e, 0x75, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x78,
	0x32, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x78, 0x32, 0x4e,
	0x75, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x78, 0x33, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x78, 0x33, 0x4e, 0x75, 0x6d, 0x22, 0x29, 0x0a, 0x05, 0x58, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x22, 0x71, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x58, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x09, 0x42, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f,
	0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x09, 0x42, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x32, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x70, 0x31, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x6f, 0x70, 0x31, 0x32, 0xe9, 0x09, 0x0a, 0x06, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x2c,
	0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x70, 0x65, 0x74,
	0x70, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x08,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x12, 0x26, 0x0a, 0x04, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x05, 0x42, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x42, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x42, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x07, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x11,
	0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0f, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x0f,
	0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x2c, 0x0a, 0x06, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x10, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x38, 0x0a,
	0x0a, 0x47, 0x69, 0x66, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x14, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x08, 0x47, 0x69, 0x66, 0x74, 0x48,
	0x65, 0x61, 0x6c, 0x12, 0x12, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x47, 0x69, 0x66, 0x74,
	0x48, 0x65, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e,
	0x47, 0x69, 0x66, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x07, 0x47,
	0x69, 0x66, 0x74, 0x42, 0x75, 0x79, 0x12, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x47,
	0x69, 0x66, 0x74, 0x42, 0x75, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x42, 0x75, 0x79, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x05,
	0x43, 0x42, 0x50, 0x61, 0x79, 0x12, 0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a,
	0x04, 0x4d, 0x61, 0x72, 0x6b, 0x12, 0x0e, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x4d, 0x61,
	0x72, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x4d, 0x61,
	0x72, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x04, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x0e, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x0e, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a,
	0x07, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x11, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b,
	0x2e, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x42, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x73, 0x70, 0x12, 0x35,
	0x0a, 0x09, 0x50, 0x72, 0x65, 0x76, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x13, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x13, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x0a, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x61, 0x6e, 0x6b, 0x12, 0x14, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x44, 0x61, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x2f, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x12, 0x11, 0x2e, 0x70, 0x65, 0x74,
	0x70, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x4a, 0x0a, 0x10, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c,
	0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x44, 0x61, 0x6d,
	0x61, 0x67, 0x65, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x47,
	0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d,
	0x4d, 0x61, 0x72, 0x71, 0x75, 0x65, 0x65, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x17, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x4d, 0x61, 0x72, 0x71, 0x75, 0x65, 0x65, 0x44, 0x61, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x4d,
	0x61, 0x72, 0x71, 0x75, 0x65, 0x65, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x32, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x1a,
	0x12, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x10, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x10, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x4a, 0x0a, 0x10, 0x48, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x48, 0x70,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x48, 0x70, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a,
	0x08, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x12, 0x2e, 0x70, 0x65, 0x74, 0x70,
	0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73,
	0x70, 0x12, 0x2c, 0x0a, 0x06, 0x42, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x2e, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2e, 0x42, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x42, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42,
	0x49, 0x5a, 0x47, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f,
	0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x70, 0x65, 0x74,
	0x70, 0x6b, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2f, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_petpk_battle_battle_proto_rawDescOnce sync.Once
	file_pb_petpk_battle_battle_proto_rawDescData = file_pb_petpk_battle_battle_proto_rawDesc
)

func file_pb_petpk_battle_battle_proto_rawDescGZIP() []byte {
	file_pb_petpk_battle_battle_proto_rawDescOnce.Do(func() {
		file_pb_petpk_battle_battle_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_petpk_battle_battle_proto_rawDescData)
	})
	return file_pb_petpk_battle_battle_proto_rawDescData
}

var file_pb_petpk_battle_battle_proto_msgTypes = make([]protoimpl.MessageInfo, 66)
var file_pb_petpk_battle_battle_proto_goTypes = []interface{}{
	(*HpContributeRankReq)(nil),       // 0: petpk.HpContributeRankReq
	(*HpContributeRankRsp)(nil),       // 1: petpk.HpContributeRankRsp
	(*RoomRankReq)(nil),               // 2: petpk.RoomRankReq
	(*RoomRankRsp)(nil),               // 3: petpk.RoomRankRsp
	(*SettleReq)(nil),                 // 4: petpk.SettleReq
	(*SettleRsp)(nil),                 // 5: petpk.SettleRsp
	(*RoomMeta)(nil),                  // 6: petpk.RoomMeta
	(*ConfigReq)(nil),                 // 7: petpk.ConfigReq
	(*NormalDef)(nil),                 // 8: petpk.NormalDef
	(*ConfigRsp)(nil),                 // 9: petpk.ConfigRsp
	(*CRItem)(nil),                    // 10: petpk.CRItem
	(*InfoReq)(nil),                   // 11: petpk.InfoReq
	(*InfoRsp)(nil),                   // 12: petpk.InfoRsp
	(*RoomFull)(nil),                  // 13: petpk.RoomFull
	(*RoomSimple)(nil),                // 14: petpk.RoomSimple
	(*BInfoReq)(nil),                  // 15: petpk.BInfoReq
	(*Reward)(nil),                    // 16: petpk.Reward
	(*RankReward)(nil),                // 17: petpk.RankReward
	(*BInfoRsp)(nil),                  // 18: petpk.BInfoRsp
	(*BalanceReq)(nil),                // 19: petpk.BalanceReq
	(*BalanceRsp)(nil),                // 20: petpk.BalanceRsp
	(*StateReq)(nil),                  // 21: petpk.StateReq
	(*StateRsp)(nil),                  // 22: petpk.StateRsp
	(*ScoreItem)(nil),                 // 23: petpk.ScoreItem
	(*AttackReq)(nil),                 // 24: petpk.AttackReq
	(*AttackRsp)(nil),                 // 25: petpk.AttackRsp
	(*GiftAttackReq)(nil),             // 26: petpk.GiftAttackReq
	(*GiftAttackRsp)(nil),             // 27: petpk.GiftAttackRsp
	(*GiftHealReq)(nil),               // 28: petpk.GiftHealReq
	(*GiftHealRsp)(nil),               // 29: petpk.GiftHealRsp
	(*GiftBuyReq)(nil),                // 30: petpk.GiftBuyReq
	(*GiftBuyRsp)(nil),                // 31: petpk.GiftBuyRsp
	(*RankReq)(nil),                   // 32: petpk.RankReq
	(*RankRsp)(nil),                   // 33: petpk.RankRsp
	(*BResultReq)(nil),                // 34: petpk.BResultReq
	(*Pet)(nil),                       // 35: petpk.Pet
	(*BResultRsp)(nil),                // 36: petpk.BResultRsp
	(*PrevStatsReq)(nil),              // 37: petpk.PrevStatsReq
	(*SSM)(nil),                       // 38: petpk.SSM
	(*PrevStatsRsp)(nil),              // 39: petpk.PrevStatsRsp
	(*DamageRankReq)(nil),             // 40: petpk.DamageRankReq
	(*DamageRankRsp)(nil),             // 41: petpk.DamageRankRsp
	(*ConveneReq)(nil),                // 42: petpk.ConveneReq
	(*ConveneRsp)(nil),                // 43: petpk.ConveneRsp
	(*BaseInfoReq)(nil),               // 44: petpk.BaseInfoReq
	(*BaseInfoRsp)(nil),               // 45: petpk.BaseInfoRsp
	(*DamageGlobalRankReq)(nil),       // 46: petpk.DamageGlobalRankReq
	(*DamageRankItem)(nil),            // 47: petpk.DamageRankItem
	(*DamageGlobalRankRsp)(nil),       // 48: petpk.DamageGlobalRankRsp
	(*MarqueeDamageReq)(nil),          // 49: petpk.MarqueeDamageReq
	(*MarqueeDamageRsp)(nil),          // 50: petpk.MarqueeDamageRsp
	(*MarkReq)(nil),                   // 51: petpk.MarkReq
	(*MarkRsp)(nil),                   // 52: petpk.MarkRsp
	(*ListItemReq)(nil),               // 53: petpk.ListItemReq
	(*ZItem)(nil),                     // 54: petpk.ZItem
	(*ZShop)(nil),                     // 55: petpk.ZShop
	(*XItem)(nil),                     // 56: petpk.XItem
	(*ListItemRsp)(nil),               // 57: petpk.ListItemRsp
	(*BStateReq)(nil),                 // 58: petpk.BStateReq
	(*BStateRsp)(nil),                 // 59: petpk.BStateRsp
	nil,                               // 60: petpk.RoomFull.DamagedEntry
	nil,                               // 61: petpk.RoomFull.TdamagedEntry
	nil,                               // 62: petpk.RoomSimple.DamagedEntry
	nil,                               // 63: petpk.RoomSimple.TdamagedEntry
	nil,                               // 64: petpk.RoomSimple.SplitScoreEntry
	nil,                               // 65: petpk.ZItem.ExtEntry
	(*pet.HpContributeRankItem)(nil),  // 66: petpk.HpContributeRankItem
	(*pet.RoomRankItem)(nil),          // 67: petpk.RoomRankItem
	(*common.XPlan)(nil),              // 68: petpk.common.XPlan
	(*callback.OrderShipmentReq)(nil), // 69: callback.OrderShipmentReq
	(*callback.OrderShipmentRsp)(nil), // 70: callback.OrderShipmentRsp
}
var file_pb_petpk_battle_battle_proto_depIdxs = []int32{
	66, // 0: petpk.HpContributeRankRsp.items:type_name -> petpk.HpContributeRankItem
	66, // 1: petpk.HpContributeRankRsp.self:type_name -> petpk.HpContributeRankItem
	67, // 2: petpk.RoomRankRsp.items:type_name -> petpk.RoomRankItem
	67, // 3: petpk.RoomRankRsp.self:type_name -> petpk.RoomRankItem
	16, // 4: petpk.SettleRsp.rewards:type_name -> petpk.Reward
	16, // 5: petpk.SettleRsp.gd_rank_rewards:type_name -> petpk.Reward
	68, // 6: petpk.ConfigRsp.curr:type_name -> petpk.common.XPlan
	68, // 7: petpk.ConfigRsp.next:type_name -> petpk.common.XPlan
	54, // 8: petpk.ConfigRsp.items:type_name -> petpk.ZItem
	8,  // 9: petpk.ConfigRsp.normal:type_name -> petpk.NormalDef
	55, // 10: petpk.ConfigRsp.shop_items:type_name -> petpk.ZShop
	60, // 11: petpk.RoomFull.damaged:type_name -> petpk.RoomFull.DamagedEntry
	61, // 12: petpk.RoomFull.tdamaged:type_name -> petpk.RoomFull.TdamagedEntry
	62, // 13: petpk.RoomSimple.damaged:type_name -> petpk.RoomSimple.DamagedEntry
	63, // 14: petpk.RoomSimple.tdamaged:type_name -> petpk.RoomSimple.TdamagedEntry
	64, // 15: petpk.RoomSimple.split_score:type_name -> petpk.RoomSimple.SplitScoreEntry
	16, // 16: petpk.RankReward.rewards:type_name -> petpk.Reward
	13, // 17: petpk.BInfoRsp.list:type_name -> petpk.RoomFull
	14, // 18: petpk.StateRsp.list:type_name -> petpk.RoomSimple
	23, // 19: petpk.StateRsp.scores:type_name -> petpk.ScoreItem
	6,  // 20: petpk.GiftAttackReq.meta:type_name -> petpk.RoomMeta
	6,  // 21: petpk.GiftHealReq.meta:type_name -> petpk.RoomMeta
	6,  // 22: petpk.GiftBuyReq.meta:type_name -> petpk.RoomMeta
	10, // 23: petpk.RankRsp.us:type_name -> petpk.CRItem
	35, // 24: petpk.BResultRsp.pet_list:type_name -> petpk.Pet
	16, // 25: petpk.SSM.rewards:type_name -> petpk.Reward
	10, // 26: petpk.PrevStatsRsp.us:type_name -> petpk.CRItem
	38, // 27: petpk.PrevStatsRsp.ssm:type_name -> petpk.SSM
	10, // 28: petpk.DamageRankRsp.list:type_name -> petpk.CRItem
	10, // 29: petpk.DamageRankRsp.self:type_name -> petpk.CRItem
	16, // 30: petpk.DamageRankRsp.rewards:type_name -> petpk.Reward
	17, // 31: petpk.BaseInfoRsp.rrs:type_name -> petpk.RankReward
	47, // 32: petpk.DamageGlobalRankRsp.tops:type_name -> petpk.DamageRankItem
	17, // 33: petpk.DamageGlobalRankRsp.rank_rewards:type_name -> petpk.RankReward
	47, // 34: petpk.DamageGlobalRankRsp.self:type_name -> petpk.DamageRankItem
	47, // 35: petpk.MarqueeDamageRsp.tops:type_name -> petpk.DamageRankItem
	65, // 36: petpk.ZItem.ext:type_name -> petpk.ZItem.ExtEntry
	56, // 37: petpk.ListItemRsp.items:type_name -> petpk.XItem
	17, // 38: petpk.BStateRsp.reward_top1:type_name -> petpk.RankReward
	7,  // 39: petpk.Battle.Config:input_type -> petpk.ConfigReq
	44, // 40: petpk.Battle.BaseInfo:input_type -> petpk.BaseInfoReq
	11, // 41: petpk.Battle.Info:input_type -> petpk.InfoReq
	15, // 42: petpk.Battle.BInfo:input_type -> petpk.BInfoReq
	19, // 43: petpk.Battle.Balance:input_type -> petpk.BalanceReq
	21, // 44: petpk.Battle.State:input_type -> petpk.StateReq
	24, // 45: petpk.Battle.Attack:input_type -> petpk.AttackReq
	26, // 46: petpk.Battle.GiftAttack:input_type -> petpk.GiftAttackReq
	28, // 47: petpk.Battle.GiftHeal:input_type -> petpk.GiftHealReq
	30, // 48: petpk.Battle.GiftBuy:input_type -> petpk.GiftBuyReq
	69, // 49: petpk.Battle.CBPay:input_type -> callback.OrderShipmentReq
	51, // 50: petpk.Battle.Mark:input_type -> petpk.MarkReq
	32, // 51: petpk.Battle.Rank:input_type -> petpk.RankReq
	34, // 52: petpk.Battle.BResult:input_type -> petpk.BResultReq
	37, // 53: petpk.Battle.PrevStats:input_type -> petpk.PrevStatsReq
	40, // 54: petpk.Battle.DamageRank:input_type -> petpk.DamageRankReq
	42, // 55: petpk.Battle.Convene:input_type -> petpk.ConveneReq
	46, // 56: petpk.Battle.DamageGlobalRank:input_type -> petpk.DamageGlobalRankReq
	49, // 57: petpk.Battle.MarqueeDamage:input_type -> petpk.MarqueeDamageReq
	53, // 58: petpk.Battle.ListItem:input_type -> petpk.ListItemReq
	4,  // 59: petpk.Battle.Settle:input_type -> petpk.SettleReq
	0,  // 60: petpk.Battle.HpContributeRank:input_type -> petpk.HpContributeRankReq
	2,  // 61: petpk.Battle.RoomRank:input_type -> petpk.RoomRankReq
	58, // 62: petpk.Battle.BState:input_type -> petpk.BStateReq
	9,  // 63: petpk.Battle.Config:output_type -> petpk.ConfigRsp
	45, // 64: petpk.Battle.BaseInfo:output_type -> petpk.BaseInfoRsp
	12, // 65: petpk.Battle.Info:output_type -> petpk.InfoRsp
	18, // 66: petpk.Battle.BInfo:output_type -> petpk.BInfoRsp
	20, // 67: petpk.Battle.Balance:output_type -> petpk.BalanceRsp
	22, // 68: petpk.Battle.State:output_type -> petpk.StateRsp
	25, // 69: petpk.Battle.Attack:output_type -> petpk.AttackRsp
	27, // 70: petpk.Battle.GiftAttack:output_type -> petpk.GiftAttackRsp
	29, // 71: petpk.Battle.GiftHeal:output_type -> petpk.GiftHealRsp
	31, // 72: petpk.Battle.GiftBuy:output_type -> petpk.GiftBuyRsp
	70, // 73: petpk.Battle.CBPay:output_type -> callback.OrderShipmentRsp
	52, // 74: petpk.Battle.Mark:output_type -> petpk.MarkRsp
	33, // 75: petpk.Battle.Rank:output_type -> petpk.RankRsp
	36, // 76: petpk.Battle.BResult:output_type -> petpk.BResultRsp
	39, // 77: petpk.Battle.PrevStats:output_type -> petpk.PrevStatsRsp
	41, // 78: petpk.Battle.DamageRank:output_type -> petpk.DamageRankRsp
	43, // 79: petpk.Battle.Convene:output_type -> petpk.ConveneRsp
	48, // 80: petpk.Battle.DamageGlobalRank:output_type -> petpk.DamageGlobalRankRsp
	50, // 81: petpk.Battle.MarqueeDamage:output_type -> petpk.MarqueeDamageRsp
	57, // 82: petpk.Battle.ListItem:output_type -> petpk.ListItemRsp
	5,  // 83: petpk.Battle.Settle:output_type -> petpk.SettleRsp
	1,  // 84: petpk.Battle.HpContributeRank:output_type -> petpk.HpContributeRankRsp
	3,  // 85: petpk.Battle.RoomRank:output_type -> petpk.RoomRankRsp
	59, // 86: petpk.Battle.BState:output_type -> petpk.BStateRsp
	63, // [63:87] is the sub-list for method output_type
	39, // [39:63] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_pb_petpk_battle_battle_proto_init() }
func file_pb_petpk_battle_battle_proto_init() {
	if File_pb_petpk_battle_battle_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_petpk_battle_battle_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HpContributeRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HpContributeRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NormalDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CRItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomFull); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomSimple); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScoreItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftAttackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftAttackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftHealReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftHealRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BResultReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BResultRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrevStatsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SSM); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrevStatsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DamageRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DamageRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConveneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConveneRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BaseInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DamageGlobalRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DamageRankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DamageGlobalRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarqueeDamageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarqueeDamageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZShop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*XItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_petpk_battle_battle_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BStateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_petpk_battle_battle_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   66,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_petpk_battle_battle_proto_goTypes,
		DependencyIndexes: file_pb_petpk_battle_battle_proto_depIdxs,
		MessageInfos:      file_pb_petpk_battle_battle_proto_msgTypes,
	}.Build()
	File_pb_petpk_battle_battle_proto = out.File
	file_pb_petpk_battle_battle_proto_rawDesc = nil
	file_pb_petpk_battle_battle_proto_goTypes = nil
	file_pb_petpk_battle_battle_proto_depIdxs = nil
}
