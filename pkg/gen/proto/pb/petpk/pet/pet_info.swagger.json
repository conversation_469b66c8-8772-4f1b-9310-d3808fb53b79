{"swagger": "2.0", "info": {"title": "pb/petpk/pet/pet_info.proto", "version": "version not set"}, "tags": [{"name": "PetInfo"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/petpk/syncRankConfig": {"post": {"summary": "同步段位配置", "operationId": "PetInfo_SyncRankConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkSyncRankConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkSyncRankConfigReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/AddHp": {"post": {"summary": "添加血量", "operationId": "PetInfo_AddHp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkAddHpRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkAddHpReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/EventNotify": {"post": {"summary": "事件通知", "operationId": "PetInfo_EventNotify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkEventNotifyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkEventNotifyReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/GetPlayerRole": {"post": {"summary": "获取玩家身份", "operationId": "PetInfo_GetPlayerRole", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkGetPlayerRoleRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkGetPlayerRoleReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/GetRankConfig": {"post": {"summary": "获取段位配置", "operationId": "PetInfo_GetRankConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkRankConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkRankConfigReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/HpContributeRankPlace": {"post": {"summary": "获取某用户对某房间的血量贡献&排名", "operationId": "PetInfo_HpContributeRankPlace", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkHpContributeRankPlaceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkHpContributeRankPlaceReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/HpContributeRankTopN": {"post": {"summary": "获取某房间的血量贡献榜topn", "operationId": "PetInfo_HpContributeRankTopN", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkHpContributeRankTopNRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkHpContributeRankTopNReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/Match": {"post": {"summary": "匹配", "operationId": "PetInfo_Match", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkMatchReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/RankScoreRankPlace": {"post": {"summary": "某个房间在房间榜的排名", "operationId": "PetInfo_RankScoreRankPlace", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkRoomRankPlaceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkRoomRankPlaceReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/RoomInfo": {"post": {"summary": "房间宠物相关信息", "operationId": "PetInfo_RoomInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkRoomInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkRoomInfoReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/RoomRankTopN": {"post": {"summary": "房间榜topn", "operationId": "PetInfo_RoomRankTopN", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkRoomRankTopNRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkRoomRankTopNReq"}}], "tags": ["PetInfo"]}}, "/petpk.PetInfo/UpdateRankScore": {"post": {"summary": "更新段位分", "operationId": "PetInfo_UpdateRankScore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/petpkUpdateRankScoreRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/petpkUpdateRankScoreReq"}}], "tags": ["PetInfo"]}}}, "definitions": {"petpkAddHpReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roundId": {"type": "string", "title": "轮次ID"}, "uniqId": {"type": "string", "title": "唯一ID, 用于操作幂等判断"}, "roomId": {"type": "string", "title": "房间ID"}, "hpDelta": {"type": "string", "format": "int64", "title": "hp增量, 暂时只允许传正数"}, "sourcePlayerId": {"type": "string", "title": "本次贡献了血量的玩家ID, 为空时不记入玩家血量贡献榜单"}}, "title": "添加血量"}, "petpkAddHpRsp": {"type": "object", "properties": {"hpAdded": {"type": "string", "format": "int64", "title": "实际增加的血量值"}}}, "petpkEventNotifyReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "eventType": {"$ref": "#/definitions/petpkEventType", "title": "类型"}, "roundId": {"type": "string", "title": "轮次ID"}, "timestamp": {"type": "string", "format": "int64", "title": "时间发生时的unix时间戳, 单位: 秒"}, "roomId": {"type": "string", "title": "房间ID"}}}, "petpkEventNotifyRsp": {"type": "object"}, "petpkEventType": {"type": "string", "enum": ["EventTypeUnknown", "EventTypeConvene", "EventTypePetDied", "EventTypePetRespawn"], "default": "EventTypeUnknown", "description": "- EventTypeUnknown: 未知\n - EventTypeConvene: 召集\n - EventTypePetDied: 宠物死亡\n - EventTypePetRespawn: 宠物复活", "title": "事件通知"}, "petpkGetPlayerRoleReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roomId": {"type": "string", "title": "房间ID"}, "playerId": {"type": "string", "title": "玩家ID"}}, "title": "获取玩家身份"}, "petpkGetPlayerRoleRsp": {"type": "object", "properties": {"playerType": {"$ref": "#/definitions/petpkPlayerType", "title": "玩家类型"}}}, "petpkHpContributeRankItem": {"type": "object", "properties": {"playerId": {"type": "string", "title": "玩家ID"}, "playerName": {"type": "string", "title": "玩家名"}, "rankPlace": {"type": "integer", "format": "int64", "title": "排行榜中排名，从1开始, 0代表不在排行榜中"}, "rankValue": {"type": "number", "format": "double", "title": "排行榜中数值"}, "playerAvatar": {"type": "string", "title": "玩家头像"}}}, "petpkHpContributeRankPlaceReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roundId": {"type": "string", "title": "轮次ID"}, "roomId": {"type": "string", "title": "房间ID"}, "playerId": {"type": "string", "title": "玩家ID"}}, "title": "获取某用户对某房间的血量贡献&排名"}, "petpkHpContributeRankPlaceRsp": {"type": "object", "properties": {"item": {"$ref": "#/definitions/petpkHpContributeRankItem", "title": "榜单项"}}}, "petpkHpContributeRankTopNReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roundId": {"type": "string", "title": "轮次ID"}, "roomId": {"type": "string", "title": "房间ID"}, "passback": {"type": "string", "title": "第一页无须传, 第N页透传第N-1页回包中的"}}, "title": "获取某房间的血量贡献榜topn"}, "petpkHpContributeRankTopNRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkHpContributeRankItem"}, "title": "榜单项"}, "hasMore": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "string"}}}, "petpkMatchReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roundId": {"type": "string", "title": "轮次ID"}, "roomId": {"type": "string", "title": "房间ID"}}, "title": "匹配"}, "petpkMatchRsp": {"type": "object", "properties": {"rooms": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRoomInfo"}, "title": "匹配到的房间(包括自身)"}, "uniqId": {"type": "string", "title": "本次匹配的唯一ID"}}}, "petpkPlayerType": {"type": "string", "enum": ["PlayerTypeUnknown", "PlayerTypeRoomOwner", "PlayerTypeSuperAdmin", "PlayerTypeAdmin", "PlayerTypeNormal"], "default": "PlayerTypeUnknown", "title": "- PlayerTypeUnknown: 未知\n - PlayerTypeRoomOwner: 房主\n - PlayerTypeSuperAdmin: 超级管理员\n - PlayerTypeAdmin: 管理员\n - PlayerTypeNormal: 普通"}, "petpkRankConfigItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "title": "主段位id, 具体见配置平台"}, "name": {"type": "string", "description": "主段位名, 如: 青铜, 白银, ..."}, "subId": {"type": "integer", "format": "int32", "title": "子段位id, 具体见配置平台"}, "subName": {"type": "string", "description": "子段位名, 如: 青铜-1, 青铜-2, ..."}, "score": {"type": "string", "format": "int64", "title": "对应的段位分, >= 该分数即满足段位要求"}, "baseHp": {"type": "string", "format": "int64", "title": "基础hp"}, "maxHp": {"type": "string", "format": "int64", "title": "hp上限"}, "subHp": {"type": "string", "format": "int64", "title": "扣减的血量"}}, "title": "段位配置项"}, "petpkRankConfigReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}}, "title": "获取段位配置"}, "petpkRankConfigRsp": {"type": "object", "properties": {"configItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRankConfigItem"}}}}, "petpkRoomInfo": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间ID"}, "roomName": {"type": "string", "title": "房间名"}, "roomCover": {"type": "string", "title": "房间封面url"}, "baseHp": {"type": "string", "format": "int64", "title": "基础hp"}, "hpAdded": {"type": "string", "format": "int64", "title": "通过完成任务获得的血量, 该值是增量, 不包括 baseHp"}, "maxHp": {"type": "string", "format": "int64", "title": "hp上限"}, "rankId": {"type": "string", "format": "int64", "title": "房间段位"}, "subRankId": {"type": "string", "format": "int64", "title": "房间子段位"}, "petId": {"type": "string", "title": "宠物ID(用于区分宠物的种族, 比如: 1(龙)/2(凤凰)"}, "petPhase": {"type": "string", "title": "宠物形态所在的阶段, 比如: 蛋/初级/中级/高级"}, "isRobot": {"type": "boolean", "title": "是否机器人"}, "score": {"type": "string", "format": "int64", "title": "段位分"}, "roomLevel": {"type": "integer", "format": "int32", "title": "房间等级"}, "roomOwnerId": {"type": "string", "title": "房主ID"}}}, "petpkRoomInfoReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roundId": {"type": "string", "title": "轮次ID"}, "roomId": {"type": "string", "title": "房间ID"}}, "title": "房间宠物相关信息"}, "petpkRoomInfoRsp": {"type": "object", "properties": {"info": {"$ref": "#/definitions/petpkRoomInfo"}}}, "petpkRoomRankItem": {"type": "object", "properties": {"roomId": {"type": "string", "title": "玩家ID"}, "roomName": {"type": "string", "title": "玩家名"}, "rankId": {"type": "string", "format": "int64", "title": "房间段位"}, "subRankId": {"type": "string", "format": "int64", "title": "房间子段位"}, "rankPlace": {"type": "integer", "format": "int64", "title": "排行榜中排名，从1开始, 0代表不在排行榜中"}, "rankValue": {"type": "number", "format": "double", "title": "排行榜中数值"}, "roomCover": {"type": "string", "title": "房间封面"}}}, "petpkRoomRankPlaceReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roomId": {"type": "string", "title": "房间ID"}}, "title": "获取某用户对某房间的血量贡献&排名"}, "petpkRoomRankPlaceRsp": {"type": "object", "properties": {"item": {"$ref": "#/definitions/petpkRoomRankItem", "title": "榜单项"}}}, "petpkRoomRankTopNReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "passback": {"type": "string", "title": "第一页无须传, 第N页透传第N-1页回包中的"}}, "title": "房间榜topn"}, "petpkRoomRankTopNRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRoomRankItem"}, "title": "榜单项"}, "hasMore": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "string"}}}, "petpkSyncRankConfigReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "configItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/petpkRankConfigItem"}}}, "title": "同步段位配置"}, "petpkSyncRankConfigRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "petpkUpdateRankScoreReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "应用ID, 方面后面其它业务复用该服务"}, "roundId": {"type": "string", "title": "轮次ID"}, "uniqId": {"type": "string", "title": "唯一ID, 用于操作幂等判断"}, "roomId": {"type": "string", "title": "房间ID"}, "scoreDelta": {"type": "string", "format": "int64", "title": "段位分变化量"}}, "title": "更新段位分"}, "petpkUpdateRankScoreRsp": {"type": "object", "properties": {"scoreAfter": {"type": "string", "format": "int64", "title": "更新后的段位分"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}