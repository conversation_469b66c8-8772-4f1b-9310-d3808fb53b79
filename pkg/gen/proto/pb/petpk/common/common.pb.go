// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/petpk/common/common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 战斗排期
type XPlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rid     string `protobuf:"bytes,1,opt,name=rid,proto3" json:"rid,omitempty"`          // 轮次ID,分配连续
	Start   int64  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`     // 轮次-开始时间
	Match   int64  `protobuf:"varint,3,opt,name=match,proto3" json:"match,omitempty"`     // 匹配阶段-开始时间
	Prepare int64  `protobuf:"varint,4,opt,name=prepare,proto3" json:"prepare,omitempty"` // 准备阶段-开始时间
	Battle  int64  `protobuf:"varint,5,opt,name=battle,proto3" json:"battle,omitempty"`   // 战斗阶段-开始时间
	Settle  int64  `protobuf:"varint,6,opt,name=settle,proto3" json:"settle,omitempty"`   // 结算阶段-开始时间
	End     int64  `protobuf:"varint,7,opt,name=end,proto3" json:"end,omitempty"`         // 结算结束时间
}

func (x *XPlan) Reset() {
	*x = XPlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_petpk_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *XPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*XPlan) ProtoMessage() {}

func (x *XPlan) ProtoReflect() protoreflect.Message {
	mi := &file_pb_petpk_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use XPlan.ProtoReflect.Descriptor instead.
func (*XPlan) Descriptor() ([]byte, []int) {
	return file_pb_petpk_common_common_proto_rawDescGZIP(), []int{0}
}

func (x *XPlan) GetRid() string {
	if x != nil {
		return x.Rid
	}
	return ""
}

func (x *XPlan) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *XPlan) GetMatch() int64 {
	if x != nil {
		return x.Match
	}
	return 0
}

func (x *XPlan) GetPrepare() int64 {
	if x != nil {
		return x.Prepare
	}
	return 0
}

func (x *XPlan) GetBattle() int64 {
	if x != nil {
		return x.Battle
	}
	return 0
}

func (x *XPlan) GetSettle() int64 {
	if x != nil {
		return x.Settle
	}
	return 0
}

func (x *XPlan) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

var File_pb_petpk_common_common_proto protoreflect.FileDescriptor

var file_pb_petpk_common_common_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x70, 0x65, 0x74, 0x70, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c,
	0x70, 0x65, 0x74, 0x70, 0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xa1, 0x01, 0x0a,
	0x05, 0x58, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x65, 0x70, 0x61, 0x72, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x42, 0x49, 0x5a, 0x47, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63,
	0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x70, 0x65,
	0x74, 0x70, 0x6b, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70,
	0x65, 0x74, 0x70, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_petpk_common_common_proto_rawDescOnce sync.Once
	file_pb_petpk_common_common_proto_rawDescData = file_pb_petpk_common_common_proto_rawDesc
)

func file_pb_petpk_common_common_proto_rawDescGZIP() []byte {
	file_pb_petpk_common_common_proto_rawDescOnce.Do(func() {
		file_pb_petpk_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_petpk_common_common_proto_rawDescData)
	})
	return file_pb_petpk_common_common_proto_rawDescData
}

var file_pb_petpk_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pb_petpk_common_common_proto_goTypes = []interface{}{
	(*XPlan)(nil), // 0: petpk.common.XPlan
}
var file_pb_petpk_common_common_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_petpk_common_common_proto_init() }
func file_pb_petpk_common_common_proto_init() {
	if File_pb_petpk_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_petpk_common_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*XPlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_petpk_common_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_petpk_common_common_proto_goTypes,
		DependencyIndexes: file_pb_petpk_common_common_proto_depIdxs,
		MessageInfos:      file_pb_petpk_common_common_proto_msgTypes,
	}.Build()
	File_pb_petpk_common_common_proto = out.File
	file_pb_petpk_common_common_proto_rawDesc = nil
	file_pb_petpk_common_common_proto_goTypes = nil
	file_pb_petpk_common_common_proto_depIdxs = nil
}
