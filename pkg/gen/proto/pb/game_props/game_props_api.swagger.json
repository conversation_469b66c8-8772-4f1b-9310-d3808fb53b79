{"swagger": "2.0", "info": {"title": "pb/game_props/game_props_api.proto", "version": "version not set"}, "tags": [{"name": "GamePropsApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_props_backend.GamePropsApi/AddUserPropsAsset": {"post": {"summary": "增加道具", "operationId": "GamePropsApi_AddUserPropsAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_props_backendAddUserPropsAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_props_backendAddUserPropsAssetReq"}}], "tags": ["GamePropsApi"]}}, "/game_props_backend.GamePropsApi/BatchAddUserPropsAsset": {"post": {"summary": "批量增加道具", "operationId": "GamePropsApi_BatchAddUserPropsAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_props_backendBatchAddUserPropsAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_props_backendBatchAddUserPropsAssetReq"}}], "tags": ["GamePropsApi"]}}, "/game_props_backend.GamePropsApi/GetPropsInfo": {"post": {"summary": "查询道具信息", "operationId": "GamePropsApi_GetPropsInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_props_backendGetPropsInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_props_backendGetPropsInfoReq"}}], "tags": ["GamePropsApi"]}}, "/game_props_backend.GamePropsApi/GetUserPropsAsset": {"post": {"summary": "获取背包道具列表", "operationId": "GamePropsApi_GetUserPropsAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_props_backendGetUserPropsAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_props_backendGetUserPropsAssetReq"}}], "tags": ["GamePropsApi"]}}, "/game_props_backend.GamePropsApi/SubUserPropsAsset": {"post": {"summary": "扣减道具", "operationId": "GamePropsApi_SubUserPropsAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_props_backendSubUserPropsAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_props_backendSubUserPropsAssetReq"}}], "tags": ["GamePropsApi"]}}}, "definitions": {"game_props_backendAddResult": {"type": "object", "properties": {"propID": {"type": "integer", "format": "int32", "title": "道具ID"}, "actualAddNum": {"type": "integer", "format": "int32", "title": "本次请求实际增加的数量"}, "totalNum": {"type": "integer", "format": "int32", "title": "当前余额数量"}}}, "game_props_backendAddUnit": {"type": "object", "properties": {"propID": {"type": "integer", "format": "int32", "title": "道具ID"}, "num": {"type": "integer", "format": "int32", "title": "数量"}, "deadLine": {"type": "string", "format": "int64", "title": "过期时间"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "ext扩展信息"}}}, "game_props_backendAddUserPropsAssetReq": {"type": "object", "properties": {"appID": {"type": "string", "title": "游戏ID"}, "userInfo": {"$ref": "#/definitions/game_props_backendUserIDUnit", "title": "用户ID信息"}, "filterTag": {"type": "string", "title": "指定标记;可用于周期性道具"}, "orderID": {"type": "string", "title": "订单id"}, "propID": {"type": "integer", "format": "int32", "title": "道具ID"}, "num": {"type": "integer", "format": "int32", "title": "数量"}, "endTime": {"type": "string", "format": "int64", "title": "过期时间"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "ext扩展信息"}}}, "game_props_backendAddUserPropsAssetRsp": {"type": "object", "properties": {"msg": {"type": "string", "title": "回包信息,ok"}, "actualAddNum": {"type": "integer", "format": "int32", "title": "本次请求实际增加的数量"}, "totalNum": {"type": "integer", "format": "int32", "title": "当前余额数量"}}}, "game_props_backendBatchAddUserPropsAssetReq": {"type": "object", "properties": {"appID": {"type": "string", "title": "游戏ID"}, "userInfo": {"$ref": "#/definitions/game_props_backendUserIDUnit", "title": "用户ID信息"}, "filterTag": {"type": "string", "title": "指定标记;可用于周期性道具"}, "orderID": {"type": "string", "title": "订单id"}, "propList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_props_backendAddUnit"}, "title": "道具列表"}}}, "game_props_backendBatchAddUserPropsAssetRsp": {"type": "object", "properties": {"msg": {"type": "string", "title": "回包信息,ok"}, "resultMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/game_props_backendAddResult"}, "title": "propID->增加结果"}}}, "game_props_backendGetPropsInfoReq": {"type": "object", "properties": {"propIDList": {"type": "array", "items": {"type": "integer", "format": "int32"}, "title": "道具ID"}}}, "game_props_backendGetPropsInfoRsp": {"type": "object", "properties": {"infoMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/game_props_backendPropInfo"}, "title": "道具信息"}}}, "game_props_backendGetUserPropsAssetReq": {"type": "object", "properties": {"appID": {"type": "string", "title": "游戏ID"}, "userInfo": {"$ref": "#/definitions/game_props_backendUserIDUnit", "title": "用户ID信息"}, "filterTag": {"type": "string", "title": "指定道具标记,空为查所有(可用于过滤周期性道具)"}}}, "game_props_backendGetUserPropsAssetRsp": {"type": "object", "properties": {"infoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_props_backendPopUnit"}, "title": "道具信息"}, "sysTs": {"type": "integer", "format": "int32", "title": "系统时间"}}}, "game_props_backendPopUnit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "title": "id"}, "name": {"type": "string", "title": "名称"}, "num": {"type": "integer", "format": "int32", "title": "数量"}, "pic": {"type": "string", "title": "图片"}, "lastExpireTs": {"type": "integer", "format": "int32", "title": "最近一批过期时间"}, "filterTag": {"type": "string", "title": "指定标记"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展信息"}}}, "game_props_backendPropInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "title": "id"}, "name": {"type": "string", "title": "名称"}, "pic": {"type": "string", "title": "图片"}, "desc": {"type": "string", "title": "描述"}, "configID": {"type": "integer", "format": "int32", "title": "配置ID"}}}, "game_props_backendSubUserPropsAssetReq": {"type": "object", "properties": {"appID": {"type": "string", "title": "游戏ID"}, "userInfo": {"$ref": "#/definitions/game_props_backendUserIDUnit", "title": "用户ID信息"}, "filterTag": {"type": "string", "title": "指定标记;可用于周期性道具"}, "orderID": {"type": "string", "title": "订单id"}, "propID": {"type": "integer", "format": "int32", "title": "道具ID"}, "num": {"type": "integer", "format": "int32", "title": "数量"}}}, "game_props_backendSubUserPropsAssetRsp": {"type": "object", "properties": {"msg": {"type": "string", "title": "回包信息,ok"}, "totalNum": {"type": "integer", "format": "int32", "title": "当前余额数量"}}}, "game_props_backendUserIDUnit": {"type": "object", "properties": {"idType": {"type": "integer", "format": "int32", "title": "UserIDType 用户id类型;0为openid;1为端内登录态uin"}, "userID": {"type": "string", "title": "用户id(参考idType)"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}