// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_props/game_props_api.proto

package game_props

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GamePropsApi_GetUserPropsAsset_FullMethodName      = "/game_props_backend.GamePropsApi/GetUserPropsAsset"
	GamePropsApi_AddUserPropsAsset_FullMethodName      = "/game_props_backend.GamePropsApi/AddUserPropsAsset"
	GamePropsApi_SubUserPropsAsset_FullMethodName      = "/game_props_backend.GamePropsApi/SubUserPropsAsset"
	GamePropsApi_GetPropsInfo_FullMethodName           = "/game_props_backend.GamePropsApi/GetPropsInfo"
	GamePropsApi_BatchAddUserPropsAsset_FullMethodName = "/game_props_backend.GamePropsApi/BatchAddUserPropsAsset"
)

// GamePropsApiClient is the client API for GamePropsApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 游戏道具背包协议
type GamePropsApiClient interface {
	// 获取背包道具列表
	GetUserPropsAsset(ctx context.Context, in *GetUserPropsAssetReq, opts ...grpc.CallOption) (*GetUserPropsAssetRsp, error)
	// 增加道具
	AddUserPropsAsset(ctx context.Context, in *AddUserPropsAssetReq, opts ...grpc.CallOption) (*AddUserPropsAssetRsp, error)
	// 扣减道具
	SubUserPropsAsset(ctx context.Context, in *SubUserPropsAssetReq, opts ...grpc.CallOption) (*SubUserPropsAssetRsp, error)
	// 查询道具信息
	GetPropsInfo(ctx context.Context, in *GetPropsInfoReq, opts ...grpc.CallOption) (*GetPropsInfoRsp, error)
	// 批量增加道具
	BatchAddUserPropsAsset(ctx context.Context, in *BatchAddUserPropsAssetReq, opts ...grpc.CallOption) (*BatchAddUserPropsAssetRsp, error)
}

type gamePropsApiClient struct {
	cc grpc.ClientConnInterface
}

func NewGamePropsApiClient(cc grpc.ClientConnInterface) GamePropsApiClient {
	return &gamePropsApiClient{cc}
}

func (c *gamePropsApiClient) GetUserPropsAsset(ctx context.Context, in *GetUserPropsAssetReq, opts ...grpc.CallOption) (*GetUserPropsAssetRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserPropsAssetRsp)
	err := c.cc.Invoke(ctx, GamePropsApi_GetUserPropsAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePropsApiClient) AddUserPropsAsset(ctx context.Context, in *AddUserPropsAssetReq, opts ...grpc.CallOption) (*AddUserPropsAssetRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddUserPropsAssetRsp)
	err := c.cc.Invoke(ctx, GamePropsApi_AddUserPropsAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePropsApiClient) SubUserPropsAsset(ctx context.Context, in *SubUserPropsAssetReq, opts ...grpc.CallOption) (*SubUserPropsAssetRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubUserPropsAssetRsp)
	err := c.cc.Invoke(ctx, GamePropsApi_SubUserPropsAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePropsApiClient) GetPropsInfo(ctx context.Context, in *GetPropsInfoReq, opts ...grpc.CallOption) (*GetPropsInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPropsInfoRsp)
	err := c.cc.Invoke(ctx, GamePropsApi_GetPropsInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePropsApiClient) BatchAddUserPropsAsset(ctx context.Context, in *BatchAddUserPropsAssetReq, opts ...grpc.CallOption) (*BatchAddUserPropsAssetRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchAddUserPropsAssetRsp)
	err := c.cc.Invoke(ctx, GamePropsApi_BatchAddUserPropsAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GamePropsApiServer is the server API for GamePropsApi service.
// All implementations should embed UnimplementedGamePropsApiServer
// for forward compatibility
//
// 游戏道具背包协议
type GamePropsApiServer interface {
	// 获取背包道具列表
	GetUserPropsAsset(context.Context, *GetUserPropsAssetReq) (*GetUserPropsAssetRsp, error)
	// 增加道具
	AddUserPropsAsset(context.Context, *AddUserPropsAssetReq) (*AddUserPropsAssetRsp, error)
	// 扣减道具
	SubUserPropsAsset(context.Context, *SubUserPropsAssetReq) (*SubUserPropsAssetRsp, error)
	// 查询道具信息
	GetPropsInfo(context.Context, *GetPropsInfoReq) (*GetPropsInfoRsp, error)
	// 批量增加道具
	BatchAddUserPropsAsset(context.Context, *BatchAddUserPropsAssetReq) (*BatchAddUserPropsAssetRsp, error)
}

// UnimplementedGamePropsApiServer should be embedded to have forward compatible implementations.
type UnimplementedGamePropsApiServer struct {
}

func (UnimplementedGamePropsApiServer) GetUserPropsAsset(context.Context, *GetUserPropsAssetReq) (*GetUserPropsAssetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserPropsAsset not implemented")
}
func (UnimplementedGamePropsApiServer) AddUserPropsAsset(context.Context, *AddUserPropsAssetReq) (*AddUserPropsAssetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserPropsAsset not implemented")
}
func (UnimplementedGamePropsApiServer) SubUserPropsAsset(context.Context, *SubUserPropsAssetReq) (*SubUserPropsAssetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubUserPropsAsset not implemented")
}
func (UnimplementedGamePropsApiServer) GetPropsInfo(context.Context, *GetPropsInfoReq) (*GetPropsInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPropsInfo not implemented")
}
func (UnimplementedGamePropsApiServer) BatchAddUserPropsAsset(context.Context, *BatchAddUserPropsAssetReq) (*BatchAddUserPropsAssetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchAddUserPropsAsset not implemented")
}

// UnsafeGamePropsApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GamePropsApiServer will
// result in compilation errors.
type UnsafeGamePropsApiServer interface {
	mustEmbedUnimplementedGamePropsApiServer()
}

func RegisterGamePropsApiServer(s grpc.ServiceRegistrar, srv GamePropsApiServer) {
	s.RegisterService(&GamePropsApi_ServiceDesc, srv)
}

func _GamePropsApi_GetUserPropsAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPropsAssetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePropsApiServer).GetUserPropsAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePropsApi_GetUserPropsAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePropsApiServer).GetUserPropsAsset(ctx, req.(*GetUserPropsAssetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePropsApi_AddUserPropsAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserPropsAssetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePropsApiServer).AddUserPropsAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePropsApi_AddUserPropsAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePropsApiServer).AddUserPropsAsset(ctx, req.(*AddUserPropsAssetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePropsApi_SubUserPropsAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubUserPropsAssetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePropsApiServer).SubUserPropsAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePropsApi_SubUserPropsAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePropsApiServer).SubUserPropsAsset(ctx, req.(*SubUserPropsAssetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePropsApi_GetPropsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPropsInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePropsApiServer).GetPropsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePropsApi_GetPropsInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePropsApiServer).GetPropsInfo(ctx, req.(*GetPropsInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePropsApi_BatchAddUserPropsAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddUserPropsAssetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePropsApiServer).BatchAddUserPropsAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePropsApi_BatchAddUserPropsAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePropsApiServer).BatchAddUserPropsAsset(ctx, req.(*BatchAddUserPropsAssetReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GamePropsApi_ServiceDesc is the grpc.ServiceDesc for GamePropsApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GamePropsApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_props_backend.GamePropsApi",
	HandlerType: (*GamePropsApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserPropsAsset",
			Handler:    _GamePropsApi_GetUserPropsAsset_Handler,
		},
		{
			MethodName: "AddUserPropsAsset",
			Handler:    _GamePropsApi_AddUserPropsAsset_Handler,
		},
		{
			MethodName: "SubUserPropsAsset",
			Handler:    _GamePropsApi_SubUserPropsAsset_Handler,
		},
		{
			MethodName: "GetPropsInfo",
			Handler:    _GamePropsApi_GetPropsInfo_Handler,
		},
		{
			MethodName: "BatchAddUserPropsAsset",
			Handler:    _GamePropsApi_BatchAddUserPropsAsset_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_props/game_props_api.proto",
}
