{"swagger": "2.0", "info": {"title": "pb/exchange_api/exchange_api.proto", "version": "version not set"}, "tags": [{"name": "ExchangeApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/exchange_api/doExchCurrency": {"post": {"summary": "兑换币换游戏币", "operationId": "ExchangeApi_DoExchCurrencyApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameDoExchCurrencyApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameDoExchCurrencyApiReq"}}], "tags": ["ExchangeApi"]}}, "/exchange_api/doExchange": {"post": {"summary": "兑换", "operationId": "ExchangeApi_DoExchangeApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameDoExchangeApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameDoExchangeApiReq"}}], "tags": ["ExchangeApi"]}}, "/exchange_api/exchangeCurrencyRecord": {"post": {"summary": "兑换币明细", "operationId": "ExchangeApi_ExchangeCurrencyRecordApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeCurrencyRecordApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeCurrencyRecordApiReq"}}], "tags": ["ExchangeApi"]}}, "/exchange_api/exchangeList": {"post": {"summary": "兑换列表", "operationId": "ExchangeApi_ExchangeListApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeListApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeListApiReq"}}], "tags": ["ExchangeApi"]}}, "/exchange_api/exchangeRecord": {"post": {"summary": "兑换记录", "operationId": "ExchangeApi_ExchangeRecordApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeRecordApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeRecordApiReq"}}], "tags": ["ExchangeApi"]}}, "/exchange_api/getExchangeCurrency": {"post": {"summary": "获取兑换券余额", "operationId": "ExchangeApi_GetExchangeCurrencyApi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGetExchangeCurrencyApiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGetExchangeCurrencyApiReq"}}], "tags": ["ExchangeApi"]}}}, "definitions": {"gameActExchList": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "活动ID"}, "vctExchList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExchInfo"}, "title": "兑换列表"}, "strDesc": {"type": "string", "title": "活动描述"}, "vctActList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExchAct"}, "title": "活动列表"}}}, "gameDoExchCurrencyApiReq": {"type": "object", "properties": {"fromAsset": {"$ref": "#/definitions/gameExchAsset", "title": "fromAsset 扣除的货币"}, "toAsset": {"$ref": "#/definitions/gameExchAsset", "title": "toAsset 获得的货币"}, "uniqueId": {"type": "string", "title": "strUniqueId 唯一ID"}}}, "gameDoExchCurrencyApiRsp": {"type": "object", "properties": {"iRes": {"type": "integer", "format": "int32", "title": "iRes 返回码"}, "msg": {"type": "string", "title": "msg 返回文案"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserExchangeAsset"}, "title": "assets 结果资产列表"}}}, "gameDoExchangeApiReq": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}, "uExchangeID": {"type": "integer", "format": "int64", "title": "uExchangeID 兑换ID"}, "uNum": {"type": "integer", "format": "int64", "title": "uNum 数量"}, "strUniqueId": {"type": "string", "title": "strUniqueId 唯一ID"}}}, "gameDoExchangeApiRsp": {"type": "object", "properties": {"iRes": {"type": "integer", "format": "int32", "title": "iRes 返回码"}, "strMsg": {"type": "string", "title": "strMsg 提示文案"}}}, "gameExchAct": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "活动 ID"}, "strName": {"type": "string", "title": "活动名称"}}}, "gameExchAsset": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "assetId 资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "assetNum 数量"}}}, "gameExchBaseInfo": {"type": "object", "properties": {"uExchangeID": {"type": "integer", "format": "int64", "title": "兑换ID"}, "lRewardID": {"type": "string", "format": "int64", "title": "奖励ID"}, "strGiftName": {"type": "string", "title": "礼物名称"}, "strGiftLogo": {"type": "string", "title": "礼物图片"}, "lPrice": {"type": "string", "format": "uint64", "title": "价格"}, "strBottomBanner": {"type": "string", "title": "底部文案"}, "stExchLimit": {"$ref": "#/definitions/gameExchLimit", "title": "兑换限制（以人为纬度）"}, "uBeginTs": {"type": "integer", "format": "int64", "title": "兑换开启时间"}, "uEndTs": {"type": "integer", "format": "int64", "title": "兑换结束时间"}, "lTotalStock": {"type": "string", "format": "int64", "title": "库存 -1为无限"}, "uTreasureLevel": {"type": "integer", "format": "int64", "title": "财富等级限制"}, "strLimitTips": {"type": "string", "title": "兑换限制文案"}, "stTotalLimit": {"$ref": "#/definitions/gameExchLimit", "title": "累计限制"}, "strTopRightLabel": {"type": "string", "title": "右上角标签"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展字段"}, "uExchangeType": {"$ref": "#/definitions/gameExchangeType", "title": "兑换类型"}, "detailLogo": {"type": "array", "items": {"type": "string"}, "title": "详细图片列表"}, "detailDesc": {"type": "string", "title": "详细文案"}, "isTimeLimit": {"type": "boolean", "title": "是否限时"}, "isPreview": {"type": "boolean", "title": "是否预览"}, "giftId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "礼物id"}}}, "gameExchCurrencyRecord": {"type": "object", "properties": {"strIcon": {"type": "string"}, "strTitle": {"type": "string"}, "strTag": {"type": "string"}, "iChangeNum": {"type": "integer", "format": "int32"}, "uTimestamp": {"type": "integer", "format": "int64"}}}, "gameExchInfo": {"type": "object", "properties": {"stInfo": {"$ref": "#/definitions/gameExchBaseInfo", "title": "兑换基本信息"}, "stRemainLimit": {"$ref": "#/definitions/gameExchLimit", "title": "个人限制"}, "lRemainStock": {"type": "string", "format": "int64", "title": "剩余库存"}, "strUniqueId": {"type": "string", "title": "唯一兑换ID"}, "stRemainTotalLimit": {"$ref": "#/definitions/gameExchLimit", "title": "剩余累计限制"}}}, "gameExchLimit": {"type": "object", "properties": {"iDayLimit": {"type": "string", "format": "int64", "title": "日限"}, "iWeekLimit": {"type": "string", "format": "int64", "title": "周限"}, "iMonthLimit": {"type": "string", "format": "int64", "title": "月限"}, "iTotalLimit": {"type": "string", "format": "int64", "title": "总限"}}}, "gameExchRecord": {"type": "object", "properties": {"stExchInfo": {"$ref": "#/definitions/gameExchBaseInfo", "title": "兑换基本信息"}, "uNum": {"type": "integer", "format": "int64", "title": "兑换数量"}, "lPay": {"type": "string", "format": "uint64", "title": "支付价格"}, "uExchTs": {"type": "integer", "format": "int64", "title": "兑换时间"}}}, "gameExchangeCurrencyRecordApiReq": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}, "strPassBack": {"type": "string", "title": "strPassBack"}}}, "gameExchangeCurrencyRecordApiRsp": {"type": "object", "properties": {"bHasMore": {"type": "boolean", "title": "bHasMore"}, "strPassBack": {"type": "string", "title": "strPassBack"}, "vctRecord": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExchCurrencyRecord"}, "title": "vctRecord 兑换币明细"}}}, "gameExchangeListApiReq": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}, "vctActIDTabList": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "vctActIDTabList Tab活动ID列表"}}}, "gameExchangeListApiRsp": {"type": "object", "properties": {"stExchList": {"$ref": "#/definitions/gameActExchList", "title": "stExchList 物品列表"}, "strObtainUrl": {"type": "string", "title": "strObtainUrl 获取链接"}}}, "gameExchangeRecordApiReq": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}, "strPassBack": {"type": "string", "title": "strPassBack"}}}, "gameExchangeRecordApiRsp": {"type": "object", "properties": {"bHasMore": {"type": "boolean", "title": "bHasMore"}, "strPassBack": {"type": "string", "title": "strPassBack"}, "vctRecord": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExchRecord"}, "title": "vctRecord 兑换记录"}}}, "gameExchangeType": {"type": "string", "enum": ["ExchangeGift", "ExchangeAsset"], "default": "ExchangeGift"}, "gameGetExchangeCurrencyApiReq": {"type": "object", "properties": {"uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}}}, "gameGetExchangeCurrencyApiRsp": {"type": "object", "properties": {"lBalance": {"type": "string", "format": "uint64", "title": "lBalance"}, "strIcon": {"type": "string", "title": "strIcon 图标"}, "strTag": {"type": "string", "title": "strTag"}, "strCurrencyName": {"type": "string", "title": "strCurrencyName 游戏币名称"}}}, "gameUserExchangeAsset": {"type": "object", "properties": {"asset": {"$ref": "#/definitions/gameExchAsset", "title": "asset 货币信息"}, "Icon": {"type": "string", "title": "Icon 图标"}, "tag": {"type": "string", "title": "tag 提示"}, "currencyName": {"type": "string", "title": "currencyName 游戏币名称"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}