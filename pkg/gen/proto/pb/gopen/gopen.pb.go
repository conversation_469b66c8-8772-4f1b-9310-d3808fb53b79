// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/gopen/gopen.proto

package gopen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetAuthCodeReq 获取授权码
type GetAuthCodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StAuth         *AuthInfo `protobuf:"bytes,1,opt,name=stAuth,proto3" json:"stAuth,omitempty"`                  // 平台登录态
	StrAppID       string    `protobuf:"bytes,2,opt,name=strAppID,proto3" json:"strAppID,omitempty"`              // 应用id
	VctScope       []string  `protobuf:"bytes,3,rep,name=vctScope,proto3" json:"vctScope,omitempty"`              // 预留，暂无用处
	LAnchor        int64     `protobuf:"varint,4,opt,name=lAnchor,proto3" json:"lAnchor,omitempty"`               // 登录设备类型
	StrUserIP      string    `protobuf:"bytes,5,opt,name=strUserIP,proto3" json:"strUserIP,omitempty"`            // 用户IP
	BCheckRedirect bool      `protobuf:"varint,6,opt,name=bCheckRedirect,proto3" json:"bCheckRedirect,omitempty"` // 是否检查跳转
	StrRedirect    string    `protobuf:"bytes,7,opt,name=strRedirect,proto3" json:"strRedirect,omitempty"`        // 跳转地址
}

func (x *GetAuthCodeReq) Reset() {
	*x = GetAuthCodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthCodeReq) ProtoMessage() {}

func (x *GetAuthCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthCodeReq.ProtoReflect.Descriptor instead.
func (*GetAuthCodeReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{0}
}

func (x *GetAuthCodeReq) GetStAuth() *AuthInfo {
	if x != nil {
		return x.StAuth
	}
	return nil
}

func (x *GetAuthCodeReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetAuthCodeReq) GetVctScope() []string {
	if x != nil {
		return x.VctScope
	}
	return nil
}

func (x *GetAuthCodeReq) GetLAnchor() int64 {
	if x != nil {
		return x.LAnchor
	}
	return 0
}

func (x *GetAuthCodeReq) GetStrUserIP() string {
	if x != nil {
		return x.StrUserIP
	}
	return ""
}

func (x *GetAuthCodeReq) GetBCheckRedirect() bool {
	if x != nil {
		return x.BCheckRedirect
	}
	return false
}

func (x *GetAuthCodeReq) GetStrRedirect() string {
	if x != nil {
		return x.StrRedirect
	}
	return ""
}

type GetAuthCodeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrCode    string `protobuf:"bytes,1,opt,name=strCode,proto3" json:"strCode,omitempty"`
	BNeedAuth  bool   `protobuf:"varint,2,opt,name=bNeedAuth,proto3" json:"bNeedAuth,omitempty"`  // 是否需要实名验证
	StrAuthURL string `protobuf:"bytes,3,opt,name=strAuthURL,proto3" json:"strAuthURL,omitempty"` // 实名验证地址
}

func (x *GetAuthCodeRsp) Reset() {
	*x = GetAuthCodeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthCodeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthCodeRsp) ProtoMessage() {}

func (x *GetAuthCodeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthCodeRsp.ProtoReflect.Descriptor instead.
func (*GetAuthCodeRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{1}
}

func (x *GetAuthCodeRsp) GetStrCode() string {
	if x != nil {
		return x.StrCode
	}
	return ""
}

func (x *GetAuthCodeRsp) GetBNeedAuth() bool {
	if x != nil {
		return x.BNeedAuth
	}
	return false
}

func (x *GetAuthCodeRsp) GetStrAuthURL() string {
	if x != nil {
		return x.StrAuthURL
	}
	return ""
}

// GetTokenReq 获取应用token
type GetTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"` // 应用id
}

func (x *GetTokenReq) Reset() {
	*x = GetTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenReq) ProtoMessage() {}

func (x *GetTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenReq.ProtoReflect.Descriptor instead.
func (*GetTokenReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{2}
}

func (x *GetTokenReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

type GetTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAccessToken string `protobuf:"bytes,1,opt,name=strAccessToken,proto3" json:"strAccessToken,omitempty"` // token
	UExpiresIn     uint32 `protobuf:"varint,2,opt,name=uExpiresIn,proto3" json:"uExpiresIn,omitempty"`        // 过期时间
}

func (x *GetTokenRsp) Reset() {
	*x = GetTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenRsp) ProtoMessage() {}

func (x *GetTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenRsp.ProtoReflect.Descriptor instead.
func (*GetTokenRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{3}
}

func (x *GetTokenRsp) GetStrAccessToken() string {
	if x != nil {
		return x.StrAccessToken
	}
	return ""
}

func (x *GetTokenRsp) GetUExpiresIn() uint32 {
	if x != nil {
		return x.UExpiresIn
	}
	return 0
}

// GetAuthorizeReq 获取用户对应用的授权
type GetAuthorizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid string   `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户Openid
	StrAppID  string   `protobuf:"bytes,2,opt,name=strAppID,proto3" json:"strAppID,omitempty"`   // 应用id
	VctScope  []string `protobuf:"bytes,3,rep,name=vctScope,proto3" json:"vctScope,omitempty"`   // 需要查询指定授权时，填入
}

func (x *GetAuthorizeReq) Reset() {
	*x = GetAuthorizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthorizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthorizeReq) ProtoMessage() {}

func (x *GetAuthorizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthorizeReq.ProtoReflect.Descriptor instead.
func (*GetAuthorizeReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{4}
}

func (x *GetAuthorizeReq) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *GetAuthorizeReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetAuthorizeReq) GetVctScope() []string {
	if x != nil {
		return x.VctScope
	}
	return nil
}

type GetAuthorizeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VctScopeConf []*ScopeConf `protobuf:"bytes,1,rep,name=vctScopeConf,proto3" json:"vctScopeConf,omitempty"` // 授权情况
}

func (x *GetAuthorizeRsp) Reset() {
	*x = GetAuthorizeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAuthorizeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthorizeRsp) ProtoMessage() {}

func (x *GetAuthorizeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthorizeRsp.ProtoReflect.Descriptor instead.
func (*GetAuthorizeRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{5}
}

func (x *GetAuthorizeRsp) GetVctScopeConf() []*ScopeConf {
	if x != nil {
		return x.VctScopeConf
	}
	return nil
}

// SetAuthorizeReq 设置用户对应用的授权
type SetAuthorizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid string   `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户Openid
	StrAppID  string   `protobuf:"bytes,2,opt,name=strAppID,proto3" json:"strAppID,omitempty"`   // 应用id
	BEnable   bool     `protobuf:"varint,3,opt,name=bEnable,proto3" json:"bEnable,omitempty"`    // 开启或关闭授权
	VctScope  []string `protobuf:"bytes,4,rep,name=vctScope,proto3" json:"vctScope,omitempty"`   // 需要变更的授权
}

func (x *SetAuthorizeReq) Reset() {
	*x = SetAuthorizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAuthorizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAuthorizeReq) ProtoMessage() {}

func (x *SetAuthorizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAuthorizeReq.ProtoReflect.Descriptor instead.
func (*SetAuthorizeReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{6}
}

func (x *SetAuthorizeReq) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *SetAuthorizeReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *SetAuthorizeReq) GetBEnable() bool {
	if x != nil {
		return x.BEnable
	}
	return false
}

func (x *SetAuthorizeReq) GetVctScope() []string {
	if x != nil {
		return x.VctScope
	}
	return nil
}

type SetAuthorizeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VctScopeConf []*ScopeConf `protobuf:"bytes,1,rep,name=vctScopeConf,proto3" json:"vctScopeConf,omitempty"` // 返回当前授权情况
}

func (x *SetAuthorizeRsp) Reset() {
	*x = SetAuthorizeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAuthorizeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAuthorizeRsp) ProtoMessage() {}

func (x *SetAuthorizeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAuthorizeRsp.ProtoReflect.Descriptor instead.
func (*SetAuthorizeRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{7}
}

func (x *SetAuthorizeRsp) GetVctScopeConf() []*ScopeConf {
	if x != nil {
		return x.VctScopeConf
	}
	return nil
}

// Openid2PlatUidReq openid转平台uid
type Openid2PlatUidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID  string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`   // 应用id
	StrOpenid string `protobuf:"bytes,2,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户Openid
}

func (x *Openid2PlatUidReq) Reset() {
	*x = Openid2PlatUidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Openid2PlatUidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Openid2PlatUidReq) ProtoMessage() {}

func (x *Openid2PlatUidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Openid2PlatUidReq.ProtoReflect.Descriptor instead.
func (*Openid2PlatUidReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{8}
}

func (x *Openid2PlatUidReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *Openid2PlatUidReq) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

type Openid2PlatUidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LPlatID     uint64    `protobuf:"varint,1,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`                            // 平台
	LUid        uint64    `protobuf:"varint,2,opt,name=lUid,proto3" json:"lUid,omitempty"`                                  // 平台对应用户
	LVirtualUid uint64    `protobuf:"varint,3,opt,name=lVirtualUid,proto3" json:"lVirtualUid,omitempty"`                    // 虚拟uid
	EType       EAcntType `protobuf:"varint,4,opt,name=eType,proto3,enum=component.gopen.EAcntType" json:"eType,omitempty"` // 账号类型
}

func (x *Openid2PlatUidRsp) Reset() {
	*x = Openid2PlatUidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Openid2PlatUidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Openid2PlatUidRsp) ProtoMessage() {}

func (x *Openid2PlatUidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Openid2PlatUidRsp.ProtoReflect.Descriptor instead.
func (*Openid2PlatUidRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{9}
}

func (x *Openid2PlatUidRsp) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *Openid2PlatUidRsp) GetLUid() uint64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

func (x *Openid2PlatUidRsp) GetLVirtualUid() uint64 {
	if x != nil {
		return x.LVirtualUid
	}
	return 0
}

func (x *Openid2PlatUidRsp) GetEType() EAcntType {
	if x != nil {
		return x.EType
	}
	return EAcntType_ACNT_NORMAL
}

// PlatUid2OpenidReq 平台uid转openid
type PlatUid2OpenidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"` // 应用id
	LPlatID  uint64 `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`  // 平台id
	LUid     uint64 `protobuf:"varint,3,opt,name=lUid,proto3" json:"lUid,omitempty"`        // 平台对应用户
}

func (x *PlatUid2OpenidReq) Reset() {
	*x = PlatUid2OpenidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatUid2OpenidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatUid2OpenidReq) ProtoMessage() {}

func (x *PlatUid2OpenidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatUid2OpenidReq.ProtoReflect.Descriptor instead.
func (*PlatUid2OpenidReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{10}
}

func (x *PlatUid2OpenidReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *PlatUid2OpenidReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *PlatUid2OpenidReq) GetLUid() uint64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

type PlatUid2OpenidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户Openid
}

func (x *PlatUid2OpenidRsp) Reset() {
	*x = PlatUid2OpenidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlatUid2OpenidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatUid2OpenidRsp) ProtoMessage() {}

func (x *PlatUid2OpenidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatUid2OpenidRsp.ProtoReflect.Descriptor instead.
func (*PlatUid2OpenidRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{11}
}

func (x *PlatUid2OpenidRsp) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

// MOpenid2PlatUidReq multi openid转平台uid
type MOpenid2PlatUidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID   string   `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`     // 应用id
	VecOpenids []string `protobuf:"bytes,2,rep,name=vecOpenids,proto3" json:"vecOpenids,omitempty"` // 用户Openid,  批量大小限制100
}

func (x *MOpenid2PlatUidReq) Reset() {
	*x = MOpenid2PlatUidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MOpenid2PlatUidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MOpenid2PlatUidReq) ProtoMessage() {}

func (x *MOpenid2PlatUidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MOpenid2PlatUidReq.ProtoReflect.Descriptor instead.
func (*MOpenid2PlatUidReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{12}
}

func (x *MOpenid2PlatUidReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *MOpenid2PlatUidReq) GetVecOpenids() []string {
	if x != nil {
		return x.VecOpenids
	}
	return nil
}

type MOpenid2PlatUidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapFail map[string]int32                    `protobuf:"bytes,1,rep,name=mapFail,proto3" json:"mapFail,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 转换失败, key: openid  value: error code
	MapSucc map[string]*MOpenid2PlatUidRsp_Item `protobuf:"bytes,2,rep,name=mapSucc,proto3" json:"mapSucc,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`  // 转换成功, key: openid  value: Item
}

func (x *MOpenid2PlatUidRsp) Reset() {
	*x = MOpenid2PlatUidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MOpenid2PlatUidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MOpenid2PlatUidRsp) ProtoMessage() {}

func (x *MOpenid2PlatUidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MOpenid2PlatUidRsp.ProtoReflect.Descriptor instead.
func (*MOpenid2PlatUidRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{13}
}

func (x *MOpenid2PlatUidRsp) GetMapFail() map[string]int32 {
	if x != nil {
		return x.MapFail
	}
	return nil
}

func (x *MOpenid2PlatUidRsp) GetMapSucc() map[string]*MOpenid2PlatUidRsp_Item {
	if x != nil {
		return x.MapSucc
	}
	return nil
}

// MPlatUid2OpenidReq multi 平台uid转openid. 单次转同一平台的
type MPlatUid2OpenidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string   `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`       // 应用id
	LPlatID  uint64   `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`        // 平台
	VecUids  []uint64 `protobuf:"varint,3,rep,packed,name=vecUids,proto3" json:"vecUids,omitempty"` // 平台对应用户,  批量大小限制100
}

func (x *MPlatUid2OpenidReq) Reset() {
	*x = MPlatUid2OpenidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MPlatUid2OpenidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MPlatUid2OpenidReq) ProtoMessage() {}

func (x *MPlatUid2OpenidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MPlatUid2OpenidReq.ProtoReflect.Descriptor instead.
func (*MPlatUid2OpenidReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{14}
}

func (x *MPlatUid2OpenidReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *MPlatUid2OpenidReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *MPlatUid2OpenidReq) GetVecUids() []uint64 {
	if x != nil {
		return x.VecUids
	}
	return nil
}

type MPlatUid2OpenidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapFail map[uint64]int32                    `protobuf:"bytes,1,rep,name=mapFail,proto3" json:"mapFail,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 转换失败, key: uid  value: error code
	MapSucc map[uint64]*MPlatUid2OpenidRsp_Item `protobuf:"bytes,2,rep,name=mapSucc,proto3" json:"mapSucc,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`  // 转换成功, key: uid  value: openid
}

func (x *MPlatUid2OpenidRsp) Reset() {
	*x = MPlatUid2OpenidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MPlatUid2OpenidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MPlatUid2OpenidRsp) ProtoMessage() {}

func (x *MPlatUid2OpenidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MPlatUid2OpenidRsp.ProtoReflect.Descriptor instead.
func (*MPlatUid2OpenidRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{15}
}

func (x *MPlatUid2OpenidRsp) GetMapFail() map[uint64]int32 {
	if x != nil {
		return x.MapFail
	}
	return nil
}

func (x *MPlatUid2OpenidRsp) GetMapSucc() map[uint64]*MPlatUid2OpenidRsp_Item {
	if x != nil {
		return x.MapSucc
	}
	return nil
}

// Code2SessionReq code2session
type Code2SessionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"` // 应用id
	StrCode  string `protobuf:"bytes,2,opt,name=strCode,proto3" json:"strCode,omitempty"`   // 授权码
}

func (x *Code2SessionReq) Reset() {
	*x = Code2SessionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Code2SessionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Code2SessionReq) ProtoMessage() {}

func (x *Code2SessionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Code2SessionReq.ProtoReflect.Descriptor instead.
func (*Code2SessionReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{16}
}

func (x *Code2SessionReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *Code2SessionReq) GetStrCode() string {
	if x != nil {
		return x.StrCode
	}
	return ""
}

type Code2SessionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid     string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"`         // 用户Openid，应用下唯一标识
	StrSessionKey string `protobuf:"bytes,2,opt,name=strSessionKey,proto3" json:"strSessionKey,omitempty"` // session_key
	StrUnionid    string `protobuf:"bytes,3,opt,name=strUnionid,proto3" json:"strUnionid,omitempty"`       // 用户unionid, 开发者下唯一标识
}

func (x *Code2SessionRsp) Reset() {
	*x = Code2SessionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Code2SessionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Code2SessionRsp) ProtoMessage() {}

func (x *Code2SessionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Code2SessionRsp.ProtoReflect.Descriptor instead.
func (*Code2SessionRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{17}
}

func (x *Code2SessionRsp) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *Code2SessionRsp) GetStrSessionKey() string {
	if x != nil {
		return x.StrSessionKey
	}
	return ""
}

func (x *Code2SessionRsp) GetStrUnionid() string {
	if x != nil {
		return x.StrUnionid
	}
	return ""
}

// GetAppReq 获取平台信息
type GetAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"` // 应用id
}

func (x *GetAppReq) Reset() {
	*x = GetAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppReq) ProtoMessage() {}

func (x *GetAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppReq.ProtoReflect.Descriptor instead.
func (*GetAppReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{18}
}

func (x *GetAppReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

type GetAppRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrName string `protobuf:"bytes,1,opt,name=strName,proto3" json:"strName,omitempty"`  // 应用名
	LPlatID uint64 `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"` // 平台id
	LScope  int64  `protobuf:"varint,3,opt,name=lScope,proto3" json:"lScope,omitempty"`   // 应用权限
}

func (x *GetAppRsp) Reset() {
	*x = GetAppRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppRsp) ProtoMessage() {}

func (x *GetAppRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppRsp.ProtoReflect.Descriptor instead.
func (*GetAppRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{19}
}

func (x *GetAppRsp) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

func (x *GetAppRsp) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *GetAppRsp) GetLScope() int64 {
	if x != nil {
		return x.LScope
	}
	return 0
}

// GetDevReq 获取开发者信息
type GetDevReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrDevID string `protobuf:"bytes,1,opt,name=strDevID,proto3" json:"strDevID,omitempty"` // 应用id
}

func (x *GetDevReq) Reset() {
	*x = GetDevReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDevReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDevReq) ProtoMessage() {}

func (x *GetDevReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDevReq.ProtoReflect.Descriptor instead.
func (*GetDevReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{20}
}

func (x *GetDevReq) GetStrDevID() string {
	if x != nil {
		return x.StrDevID
	}
	return ""
}

type GetDevRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrName string `protobuf:"bytes,1,opt,name=strName,proto3" json:"strName,omitempty"`  // 开发者/公司名
	LPlatID uint64 `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"` // 平台id
	LScope  int64  `protobuf:"varint,3,opt,name=lScope,proto3" json:"lScope,omitempty"`   // 开发者权限
}

func (x *GetDevRsp) Reset() {
	*x = GetDevRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDevRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDevRsp) ProtoMessage() {}

func (x *GetDevRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDevRsp.ProtoReflect.Descriptor instead.
func (*GetDevRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{21}
}

func (x *GetDevRsp) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

func (x *GetDevRsp) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *GetDevRsp) GetLScope() int64 {
	if x != nil {
		return x.LScope
	}
	return 0
}

// ValidateTokenSignReq 应用token校验 & 签名校验
type ValidateTokenSignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StData       *SignData `protobuf:"bytes,1,opt,name=stData,proto3" json:"stData,omitempty"`              // 签名数据，校验签名&token
	BIgnoreToken bool      `protobuf:"varint,2,opt,name=bIgnoreToken,proto3" json:"bIgnoreToken,omitempty"` // 是否忽略校验access token, 用于区分应用签名与应用token签名
}

func (x *ValidateTokenSignReq) Reset() {
	*x = ValidateTokenSignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateTokenSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTokenSignReq) ProtoMessage() {}

func (x *ValidateTokenSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTokenSignReq.ProtoReflect.Descriptor instead.
func (*ValidateTokenSignReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{22}
}

func (x *ValidateTokenSignReq) GetStData() *SignData {
	if x != nil {
		return x.StData
	}
	return nil
}

func (x *ValidateTokenSignReq) GetBIgnoreToken() bool {
	if x != nil {
		return x.BIgnoreToken
	}
	return false
}

type ValidateTokenSignRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ValidateTokenSignRsp) Reset() {
	*x = ValidateTokenSignRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateTokenSignRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateTokenSignRsp) ProtoMessage() {}

func (x *ValidateTokenSignRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateTokenSignRsp.ProtoReflect.Descriptor instead.
func (*ValidateTokenSignRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{23}
}

// ValidateSessionKeySignReq 用户态签名校验
type ValidateSessionKeySignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StData *SignData `protobuf:"bytes,1,opt,name=stData,proto3" json:"stData,omitempty"` // 签名数据, 校验签名
}

func (x *ValidateSessionKeySignReq) Reset() {
	*x = ValidateSessionKeySignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSessionKeySignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionKeySignReq) ProtoMessage() {}

func (x *ValidateSessionKeySignReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionKeySignReq.ProtoReflect.Descriptor instead.
func (*ValidateSessionKeySignReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{24}
}

func (x *ValidateSessionKeySignReq) GetStData() *SignData {
	if x != nil {
		return x.StData
	}
	return nil
}

type ValidateSessionKeySignRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrSessionKey string `protobuf:"bytes,1,opt,name=strSessionKey,proto3" json:"strSessionKey,omitempty"` // sessionkey. 鉴权成功时返回
}

func (x *ValidateSessionKeySignRsp) Reset() {
	*x = ValidateSessionKeySignRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSessionKeySignRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionKeySignRsp) ProtoMessage() {}

func (x *ValidateSessionKeySignRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionKeySignRsp.ProtoReflect.Descriptor instead.
func (*ValidateSessionKeySignRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{25}
}

func (x *ValidateSessionKeySignRsp) GetStrSessionKey() string {
	if x != nil {
		return x.StrSessionKey
	}
	return ""
}

// ValidateSessionKeyReq 用户态校验
type ValidateSessionKeyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID      string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`           // 应用id
	StrOpenid     string `protobuf:"bytes,2,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"`         // 用户openid
	StrSessionKey string `protobuf:"bytes,3,opt,name=strSessionKey,proto3" json:"strSessionKey,omitempty"` // sessionkey
	LAnchor       int64  `protobuf:"varint,4,opt,name=lAnchor,proto3" json:"lAnchor,omitempty"`            // 登录设备类型
}

func (x *ValidateSessionKeyReq) Reset() {
	*x = ValidateSessionKeyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSessionKeyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionKeyReq) ProtoMessage() {}

func (x *ValidateSessionKeyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionKeyReq.ProtoReflect.Descriptor instead.
func (*ValidateSessionKeyReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{26}
}

func (x *ValidateSessionKeyReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *ValidateSessionKeyReq) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *ValidateSessionKeyReq) GetStrSessionKey() string {
	if x != nil {
		return x.StrSessionKey
	}
	return ""
}

func (x *ValidateSessionKeyReq) GetLAnchor() int64 {
	if x != nil {
		return x.LAnchor
	}
	return 0
}

type ValidateSessionKeyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ValidateSessionKeyRsp) Reset() {
	*x = ValidateSessionKeyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidateSessionKeyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSessionKeyRsp) ProtoMessage() {}

func (x *ValidateSessionKeyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSessionKeyRsp.ProtoReflect.Descriptor instead.
func (*ValidateSessionKeyRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{27}
}

// QuickSessionReq 快速授权登录，直接获取session_key和openid信息
type QuickSessionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StAuth    *AuthInfo `protobuf:"bytes,1,opt,name=stAuth,proto3" json:"stAuth,omitempty"`       // 平台登录态
	StrAppID  string    `protobuf:"bytes,2,opt,name=strAppID,proto3" json:"strAppID,omitempty"`   // 应用id
	VctScope  []string  `protobuf:"bytes,3,rep,name=vctScope,proto3" json:"vctScope,omitempty"`   // 预留，暂无用处
	LAnchor   int64     `protobuf:"varint,4,opt,name=lAnchor,proto3" json:"lAnchor,omitempty"`    // 登录设备类型
	StrUserIP string    `protobuf:"bytes,5,opt,name=strUserIP,proto3" json:"strUserIP,omitempty"` // 用户IP
}

func (x *QuickSessionReq) Reset() {
	*x = QuickSessionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuickSessionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickSessionReq) ProtoMessage() {}

func (x *QuickSessionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickSessionReq.ProtoReflect.Descriptor instead.
func (*QuickSessionReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{28}
}

func (x *QuickSessionReq) GetStAuth() *AuthInfo {
	if x != nil {
		return x.StAuth
	}
	return nil
}

func (x *QuickSessionReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *QuickSessionReq) GetVctScope() []string {
	if x != nil {
		return x.VctScope
	}
	return nil
}

func (x *QuickSessionReq) GetLAnchor() int64 {
	if x != nil {
		return x.LAnchor
	}
	return 0
}

func (x *QuickSessionReq) GetStrUserIP() string {
	if x != nil {
		return x.StrUserIP
	}
	return ""
}

type QuickSessionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid     string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"`         // 用户Openid，应用下唯一标识
	StrSessionKey string `protobuf:"bytes,2,opt,name=strSessionKey,proto3" json:"strSessionKey,omitempty"` // session_key
	StrUnionid    string `protobuf:"bytes,3,opt,name=strUnionid,proto3" json:"strUnionid,omitempty"`       // 用户unionid, 开发者下唯一标识
}

func (x *QuickSessionRsp) Reset() {
	*x = QuickSessionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuickSessionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickSessionRsp) ProtoMessage() {}

func (x *QuickSessionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickSessionRsp.ProtoReflect.Descriptor instead.
func (*QuickSessionRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{29}
}

func (x *QuickSessionRsp) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *QuickSessionRsp) GetStrSessionKey() string {
	if x != nil {
		return x.StrSessionKey
	}
	return ""
}

func (x *QuickSessionRsp) GetStrUnionid() string {
	if x != nil {
		return x.StrUnionid
	}
	return ""
}

// CreateAppReq 应用创建
type CreateAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrName    string `protobuf:"bytes,1,opt,name=strName,proto3" json:"strName,omitempty"`       // 应用名
	LPlatID    uint64 `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`      // 平台id
	LScope     int64  `protobuf:"varint,3,opt,name=lScope,proto3" json:"lScope,omitempty"`        // 应用权限
	StrComment string `protobuf:"bytes,4,opt,name=strComment,proto3" json:"strComment,omitempty"` // 备注信息
	StrDevID   string `protobuf:"bytes,5,opt,name=strDevID,proto3" json:"strDevID,omitempty"`     // 开发者id, 传递开发者ID时，将进行应用绑定
}

func (x *CreateAppReq) Reset() {
	*x = CreateAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppReq) ProtoMessage() {}

func (x *CreateAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppReq.ProtoReflect.Descriptor instead.
func (*CreateAppReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{30}
}

func (x *CreateAppReq) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

func (x *CreateAppReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *CreateAppReq) GetLScope() int64 {
	if x != nil {
		return x.LScope
	}
	return 0
}

func (x *CreateAppReq) GetStrComment() string {
	if x != nil {
		return x.StrComment
	}
	return ""
}

func (x *CreateAppReq) GetStrDevID() string {
	if x != nil {
		return x.StrDevID
	}
	return ""
}

type CreateAppRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID  string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`   // 应用id
	StrName   string `protobuf:"bytes,2,opt,name=strName,proto3" json:"strName,omitempty"`     // 应用名
	LPlatID   uint64 `protobuf:"varint,3,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`    // 平台id
	StrSecret string `protobuf:"bytes,4,opt,name=strSecret,proto3" json:"strSecret,omitempty"` // 应用密钥
	LScope    int64  `protobuf:"varint,5,opt,name=lScope,proto3" json:"lScope,omitempty"`      // 应用权限
}

func (x *CreateAppRsp) Reset() {
	*x = CreateAppRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppRsp) ProtoMessage() {}

func (x *CreateAppRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppRsp.ProtoReflect.Descriptor instead.
func (*CreateAppRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{31}
}

func (x *CreateAppRsp) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *CreateAppRsp) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

func (x *CreateAppRsp) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *CreateAppRsp) GetStrSecret() string {
	if x != nil {
		return x.StrSecret
	}
	return ""
}

func (x *CreateAppRsp) GetLScope() int64 {
	if x != nil {
		return x.LScope
	}
	return 0
}

// CreateDevReq 开发者创建
type CreateDevReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrName    string `protobuf:"bytes,1,opt,name=strName,proto3" json:"strName,omitempty"`       // 开发者名(公司/组织名)
	LPlatID    uint64 `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`      // 平台id
	LScope     int64  `protobuf:"varint,3,opt,name=lScope,proto3" json:"lScope,omitempty"`        // 开发者权限
	StrComment string `protobuf:"bytes,4,opt,name=strComment,proto3" json:"strComment,omitempty"` // 备注信息
}

func (x *CreateDevReq) Reset() {
	*x = CreateDevReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDevReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDevReq) ProtoMessage() {}

func (x *CreateDevReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDevReq.ProtoReflect.Descriptor instead.
func (*CreateDevReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{32}
}

func (x *CreateDevReq) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

func (x *CreateDevReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *CreateDevReq) GetLScope() int64 {
	if x != nil {
		return x.LScope
	}
	return 0
}

func (x *CreateDevReq) GetStrComment() string {
	if x != nil {
		return x.StrComment
	}
	return ""
}

type CreateDevRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrDevID  string `protobuf:"bytes,1,opt,name=strDevID,proto3" json:"strDevID,omitempty"`   // 开发者id
	StrName   string `protobuf:"bytes,2,opt,name=strName,proto3" json:"strName,omitempty"`     // 开发者名(公司/组织名)
	LPlatID   uint64 `protobuf:"varint,3,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`    // 平台id
	StrSecret string `protobuf:"bytes,4,opt,name=strSecret,proto3" json:"strSecret,omitempty"` // 开发者密钥
	LScope    int64  `protobuf:"varint,5,opt,name=lScope,proto3" json:"lScope,omitempty"`      // 开发者权限
}

func (x *CreateDevRsp) Reset() {
	*x = CreateDevRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDevRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDevRsp) ProtoMessage() {}

func (x *CreateDevRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDevRsp.ProtoReflect.Descriptor instead.
func (*CreateDevRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{33}
}

func (x *CreateDevRsp) GetStrDevID() string {
	if x != nil {
		return x.StrDevID
	}
	return ""
}

func (x *CreateDevRsp) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

func (x *CreateDevRsp) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *CreateDevRsp) GetStrSecret() string {
	if x != nil {
		return x.StrSecret
	}
	return ""
}

func (x *CreateDevRsp) GetLScope() int64 {
	if x != nil {
		return x.LScope
	}
	return 0
}

// RsyncAppReq 应用同步 接收
type RsyncAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrData string `protobuf:"bytes,1,opt,name=strData,proto3" json:"strData,omitempty"` // 加密隐私数据，私密接口
}

func (x *RsyncAppReq) Reset() {
	*x = RsyncAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RsyncAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RsyncAppReq) ProtoMessage() {}

func (x *RsyncAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RsyncAppReq.ProtoReflect.Descriptor instead.
func (*RsyncAppReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{34}
}

func (x *RsyncAppReq) GetStrData() string {
	if x != nil {
		return x.StrData
	}
	return ""
}

type RsyncAppRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RsyncAppRsp) Reset() {
	*x = RsyncAppRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RsyncAppRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RsyncAppRsp) ProtoMessage() {}

func (x *RsyncAppRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RsyncAppRsp.ProtoReflect.Descriptor instead.
func (*RsyncAppRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{35}
}

// RsyncDevReq 开发者同步 接收
type RsyncDevReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrData string `protobuf:"bytes,1,opt,name=strData,proto3" json:"strData,omitempty"` // 加密隐私数据，私密接口
}

func (x *RsyncDevReq) Reset() {
	*x = RsyncDevReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RsyncDevReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RsyncDevReq) ProtoMessage() {}

func (x *RsyncDevReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RsyncDevReq.ProtoReflect.Descriptor instead.
func (*RsyncDevReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{36}
}

func (x *RsyncDevReq) GetStrData() string {
	if x != nil {
		return x.StrData
	}
	return ""
}

type RsyncDevRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RsyncDevRsp) Reset() {
	*x = RsyncDevRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RsyncDevRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RsyncDevRsp) ProtoMessage() {}

func (x *RsyncDevRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RsyncDevRsp.ProtoReflect.Descriptor instead.
func (*RsyncDevRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{37}
}

// SsyncAppReq 应用同步 发送
type SsyncAppReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"` // 应用id
}

func (x *SsyncAppReq) Reset() {
	*x = SsyncAppReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SsyncAppReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SsyncAppReq) ProtoMessage() {}

func (x *SsyncAppReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SsyncAppReq.ProtoReflect.Descriptor instead.
func (*SsyncAppReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{38}
}

func (x *SsyncAppReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

type SsyncAppRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SsyncAppRsp) Reset() {
	*x = SsyncAppRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SsyncAppRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SsyncAppRsp) ProtoMessage() {}

func (x *SsyncAppRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SsyncAppRsp.ProtoReflect.Descriptor instead.
func (*SsyncAppRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{39}
}

// SsyncDevReq 开发者同步 发送
type SsyncDevReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrDevID string `protobuf:"bytes,1,opt,name=strDevID,proto3" json:"strDevID,omitempty"` // 应用id
}

func (x *SsyncDevReq) Reset() {
	*x = SsyncDevReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SsyncDevReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SsyncDevReq) ProtoMessage() {}

func (x *SsyncDevReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SsyncDevReq.ProtoReflect.Descriptor instead.
func (*SsyncDevReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{40}
}

func (x *SsyncDevReq) GetStrDevID() string {
	if x != nil {
		return x.StrDevID
	}
	return ""
}

type SsyncDevRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SsyncDevRsp) Reset() {
	*x = SsyncDevRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SsyncDevRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SsyncDevRsp) ProtoMessage() {}

func (x *SsyncDevRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SsyncDevRsp.ProtoReflect.Descriptor instead.
func (*SsyncDevRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{41}
}

// GetBusiSessionReq 获取业务session
type GetBusiSessionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID  string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`   // 游戏id
	StrOpenid string `protobuf:"bytes,2,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户openid
	LAnchor   int64  `protobuf:"varint,3,opt,name=lAnchor,proto3" json:"lAnchor,omitempty"`    // 登录设备类型
}

func (x *GetBusiSessionReq) Reset() {
	*x = GetBusiSessionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusiSessionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusiSessionReq) ProtoMessage() {}

func (x *GetBusiSessionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusiSessionReq.ProtoReflect.Descriptor instead.
func (*GetBusiSessionReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{42}
}

func (x *GetBusiSessionReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetBusiSessionReq) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *GetBusiSessionReq) GetLAnchor() int64 {
	if x != nil {
		return x.LAnchor
	}
	return 0
}

type GetBusiSessionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StBusiSession *BusiSession `protobuf:"bytes,1,opt,name=stBusiSession,proto3" json:"stBusiSession,omitempty"` // 业务session
}

func (x *GetBusiSessionRsp) Reset() {
	*x = GetBusiSessionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBusiSessionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBusiSessionRsp) ProtoMessage() {}

func (x *GetBusiSessionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBusiSessionRsp.ProtoReflect.Descriptor instead.
func (*GetBusiSessionRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{43}
}

func (x *GetBusiSessionRsp) GetStBusiSession() *BusiSession {
	if x != nil {
		return x.StBusiSession
	}
	return nil
}

// MGetLoginInfoReq 获取登录信息
type MGetLoginInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID   string   `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`     // 应用id
	VecOpenids []string `protobuf:"bytes,2,rep,name=vecOpenids,proto3" json:"vecOpenids,omitempty"` // 用户Openid,  批量大小限制100
}

func (x *MGetLoginInfoReq) Reset() {
	*x = MGetLoginInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLoginInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLoginInfoReq) ProtoMessage() {}

func (x *MGetLoginInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLoginInfoReq.ProtoReflect.Descriptor instead.
func (*MGetLoginInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{44}
}

func (x *MGetLoginInfoReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *MGetLoginInfoReq) GetVecOpenids() []string {
	if x != nil {
		return x.VecOpenids
	}
	return nil
}

type MGetLoginInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapFail map[string]int32                  `protobuf:"bytes,1,rep,name=mapFail,proto3" json:"mapFail,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 转换失败, key: openid  value: error code
	MapSucc map[string]*MGetLoginInfoRsp_Item `protobuf:"bytes,2,rep,name=mapSucc,proto3" json:"mapSucc,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`  // 转换成功, key: openid  value: Item
}

func (x *MGetLoginInfoRsp) Reset() {
	*x = MGetLoginInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLoginInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLoginInfoRsp) ProtoMessage() {}

func (x *MGetLoginInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLoginInfoRsp.ProtoReflect.Descriptor instead.
func (*MGetLoginInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{45}
}

func (x *MGetLoginInfoRsp) GetMapFail() map[string]int32 {
	if x != nil {
		return x.MapFail
	}
	return nil
}

func (x *MGetLoginInfoRsp) GetMapSucc() map[string]*MGetLoginInfoRsp_Item {
	if x != nil {
		return x.MapSucc
	}
	return nil
}

// MOpenid2OpenidReq openid转openid
type MOpenid2OpenidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrSrcAppID string   `protobuf:"bytes,1,opt,name=strSrcAppID,proto3" json:"strSrcAppID,omitempty"` // openid归属的原appid
	StrDstAppID string   `protobuf:"bytes,2,opt,name=strDstAppID,proto3" json:"strDstAppID,omitempty"` // openid转换到的appid
	VecOpenids  []string `protobuf:"bytes,3,rep,name=vecOpenids,proto3" json:"vecOpenids,omitempty"`   // 用户Openid,  批量大小限制100
}

func (x *MOpenid2OpenidReq) Reset() {
	*x = MOpenid2OpenidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MOpenid2OpenidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MOpenid2OpenidReq) ProtoMessage() {}

func (x *MOpenid2OpenidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MOpenid2OpenidReq.ProtoReflect.Descriptor instead.
func (*MOpenid2OpenidReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{46}
}

func (x *MOpenid2OpenidReq) GetStrSrcAppID() string {
	if x != nil {
		return x.StrSrcAppID
	}
	return ""
}

func (x *MOpenid2OpenidReq) GetStrDstAppID() string {
	if x != nil {
		return x.StrDstAppID
	}
	return ""
}

func (x *MOpenid2OpenidReq) GetVecOpenids() []string {
	if x != nil {
		return x.VecOpenids
	}
	return nil
}

type MOpenid2OpenidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapFail map[string]int32                   `protobuf:"bytes,1,rep,name=mapFail,proto3" json:"mapFail,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 转换失败, key: openid  value: error code
	MapSucc map[string]*MOpenid2OpenidRsp_Item `protobuf:"bytes,2,rep,name=mapSucc,proto3" json:"mapSucc,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`  // 转换成功, key: openid  value: Item
}

func (x *MOpenid2OpenidRsp) Reset() {
	*x = MOpenid2OpenidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MOpenid2OpenidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MOpenid2OpenidRsp) ProtoMessage() {}

func (x *MOpenid2OpenidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MOpenid2OpenidRsp.ProtoReflect.Descriptor instead.
func (*MOpenid2OpenidRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{47}
}

func (x *MOpenid2OpenidRsp) GetMapFail() map[string]int32 {
	if x != nil {
		return x.MapFail
	}
	return nil
}

func (x *MOpenid2OpenidRsp) GetMapSucc() map[string]*MOpenid2OpenidRsp_Item {
	if x != nil {
		return x.MapSucc
	}
	return nil
}

// VirtualLoginReq 虚拟账号登录，直接获取session_key和openid信息
type VirtualLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID    string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`        // 应用id
	USceneID    uint32 `protobuf:"varint,2,opt,name=uSceneID,proto3" json:"uSceneID,omitempty"`       // 场景id, 只能使用低2字节, pb语法上不支持uint16, 所以定义为uint32
	LPlatID     uint64 `protobuf:"varint,3,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`         // 平台id
	LVirtualUid uint64 `protobuf:"varint,4,opt,name=lVirtualUid,proto3" json:"lVirtualUid,omitempty"` // 虚拟账号低六字节(注意: 这里虚拟uid只关注最末6字节), 最终虚拟uid为((uint64)iSceneID << 48) | (lVirtualUid & 0xFFFFFFFFFFFF)
}

func (x *VirtualLoginReq) Reset() {
	*x = VirtualLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualLoginReq) ProtoMessage() {}

func (x *VirtualLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualLoginReq.ProtoReflect.Descriptor instead.
func (*VirtualLoginReq) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{48}
}

func (x *VirtualLoginReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *VirtualLoginReq) GetUSceneID() uint32 {
	if x != nil {
		return x.USceneID
	}
	return 0
}

func (x *VirtualLoginReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *VirtualLoginReq) GetLVirtualUid() uint64 {
	if x != nil {
		return x.LVirtualUid
	}
	return 0
}

type VirtualLoginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid     string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"`         // 用户Openid，应用下唯一标识
	StrSessionKey string `protobuf:"bytes,2,opt,name=strSessionKey,proto3" json:"strSessionKey,omitempty"` // session_key
	StrUnionid    string `protobuf:"bytes,3,opt,name=strUnionid,proto3" json:"strUnionid,omitempty"`       // 用户unionid, 开发者下唯一标识
}

func (x *VirtualLoginRsp) Reset() {
	*x = VirtualLoginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualLoginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualLoginRsp) ProtoMessage() {}

func (x *VirtualLoginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualLoginRsp.ProtoReflect.Descriptor instead.
func (*VirtualLoginRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{49}
}

func (x *VirtualLoginRsp) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *VirtualLoginRsp) GetStrSessionKey() string {
	if x != nil {
		return x.StrSessionKey
	}
	return ""
}

func (x *VirtualLoginRsp) GetStrUnionid() string {
	if x != nil {
		return x.StrUnionid
	}
	return ""
}

type MOpenid2PlatUidRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LPlatID     uint64    `protobuf:"varint,1,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`                            // 平台
	LUid        uint64    `protobuf:"varint,2,opt,name=lUid,proto3" json:"lUid,omitempty"`                                  // 平台对应用户
	LVirtualUid uint64    `protobuf:"varint,3,opt,name=lVirtualUid,proto3" json:"lVirtualUid,omitempty"`                    // 虚拟uid
	EType       EAcntType `protobuf:"varint,4,opt,name=eType,proto3,enum=component.gopen.EAcntType" json:"eType,omitempty"` // 账号类型
}

func (x *MOpenid2PlatUidRsp_Item) Reset() {
	*x = MOpenid2PlatUidRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MOpenid2PlatUidRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MOpenid2PlatUidRsp_Item) ProtoMessage() {}

func (x *MOpenid2PlatUidRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MOpenid2PlatUidRsp_Item.ProtoReflect.Descriptor instead.
func (*MOpenid2PlatUidRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{13, 0}
}

func (x *MOpenid2PlatUidRsp_Item) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *MOpenid2PlatUidRsp_Item) GetLUid() uint64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

func (x *MOpenid2PlatUidRsp_Item) GetLVirtualUid() uint64 {
	if x != nil {
		return x.LVirtualUid
	}
	return 0
}

func (x *MOpenid2PlatUidRsp_Item) GetEType() EAcntType {
	if x != nil {
		return x.EType
	}
	return EAcntType_ACNT_NORMAL
}

type MPlatUid2OpenidRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户Openid
}

func (x *MPlatUid2OpenidRsp_Item) Reset() {
	*x = MPlatUid2OpenidRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MPlatUid2OpenidRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MPlatUid2OpenidRsp_Item) ProtoMessage() {}

func (x *MPlatUid2OpenidRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MPlatUid2OpenidRsp_Item.ProtoReflect.Descriptor instead.
func (*MPlatUid2OpenidRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{15, 0}
}

func (x *MPlatUid2OpenidRsp_Item) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

type MGetLoginInfoRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LRegistTime    uint64 `protobuf:"varint,1,opt,name=lRegistTime,proto3" json:"lRegistTime,omitempty"`       // 注册unix时间戳 单位 s
	LLastLoginTime uint64 `protobuf:"varint,2,opt,name=lLastLoginTime,proto3" json:"lLastLoginTime,omitempty"` // 上次登录unix时间戳 单位 s
	LPlatID        uint64 `protobuf:"varint,3,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`               // 平台
	LUid           uint64 `protobuf:"varint,4,opt,name=lUid,proto3" json:"lUid,omitempty"`                     // 平台对应用户
}

func (x *MGetLoginInfoRsp_Item) Reset() {
	*x = MGetLoginInfoRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MGetLoginInfoRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MGetLoginInfoRsp_Item) ProtoMessage() {}

func (x *MGetLoginInfoRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MGetLoginInfoRsp_Item.ProtoReflect.Descriptor instead.
func (*MGetLoginInfoRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{45, 0}
}

func (x *MGetLoginInfoRsp_Item) GetLRegistTime() uint64 {
	if x != nil {
		return x.LRegistTime
	}
	return 0
}

func (x *MGetLoginInfoRsp_Item) GetLLastLoginTime() uint64 {
	if x != nil {
		return x.LLastLoginTime
	}
	return 0
}

func (x *MGetLoginInfoRsp_Item) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *MGetLoginInfoRsp_Item) GetLUid() uint64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

type MOpenid2OpenidRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOpenid string `protobuf:"bytes,1,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"` // 用户openid
}

func (x *MOpenid2OpenidRsp_Item) Reset() {
	*x = MOpenid2OpenidRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopen_gopen_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MOpenid2OpenidRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MOpenid2OpenidRsp_Item) ProtoMessage() {}

func (x *MOpenid2OpenidRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopen_gopen_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MOpenid2OpenidRsp_Item.ProtoReflect.Descriptor instead.
func (*MOpenid2OpenidRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_gopen_gopen_proto_rawDescGZIP(), []int{47, 0}
}

func (x *MOpenid2OpenidRsp_Item) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

var File_pb_gopen_gopen_proto protoreflect.FileDescriptor

var file_pb_gopen_gopen_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x2f, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2f, 0x67, 0x6f, 0x70, 0x65, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x1a, 0x1b, 0x70, 0x62, 0x2f, 0x67, 0x6f, 0x70, 0x65,
	0x6e, 0x2f, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfd, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x06, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x50, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x50, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x62, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x52, 0x65, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x22, 0x68, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x62, 0x4e, 0x65, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x4e, 0x65, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x74, 0x72, 0x41, 0x75, 0x74, 0x68, 0x55, 0x52, 0x4c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x41, 0x75, 0x74, 0x68, 0x55, 0x52, 0x4c, 0x22, 0x29,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x22, 0x55, 0x0a, 0x0b, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x73, 0x74, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x49, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x49, 0x6e,
	0x22, 0x67, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a,
	0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x51, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0c,
	0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x0c,
	0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x22, 0x81, 0x01, 0x0a,
	0x0f, 0x53, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x51, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0c, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x0c, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x22, 0x4d, 0x0a, 0x11, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c,
	0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41,
	0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41,
	0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e,
	0x69, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c,
	0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61,
	0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74,
	0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x56, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6c, 0x56, 0x69,
	0x72, 0x74, 0x75, 0x61, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x45, 0x41, 0x63, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x05, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5d, 0x0a, 0x11, 0x50, 0x6c,
	0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x6c,
	0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50,
	0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x50, 0x6c, 0x61,
	0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x12,
	0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1e,
	0x0a, 0x0a, 0x76, 0x65, 0x63, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x63, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x22, 0xd9,
	0x03, 0x0a, 0x12, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55,
	0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x46,
	0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69,
	0x6c, 0x12, 0x4a, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61,
	0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x1a, 0x88, 0x01,
	0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x6c, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x55, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6c, 0x56, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x05, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x45, 0x41, 0x63, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x05, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x46,
	0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x64, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32,
	0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64, 0x0a, 0x12, 0x4d, 0x50,
	0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07,
	0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c,
	0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x63, 0x55, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x07, 0x76, 0x65, 0x63, 0x55, 0x69, 0x64, 0x73,
	0x22, 0xf4, 0x02, 0x0a, 0x12, 0x4d, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61,
	0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x50, 0x6c, 0x61, 0x74,
	0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61,
	0x70, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x46,
	0x61, 0x69, 0x6c, 0x12, 0x4a, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63,
	0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x1a,
	0x24, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x64, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x47, 0x0a, 0x0f, 0x43, 0x6f, 0x64, 0x65, 0x32,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x75, 0x0a, 0x0f, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69,
	0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x55, 0x6e,
	0x69, 0x6f, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72,
	0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x22, 0x27, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44,
	0x22, 0x57, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49,
	0x44, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x27, 0x0a, 0x09, 0x47, 0x65, 0x74,
	0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x44, 0x65, 0x76,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x44, 0x65, 0x76,
	0x49, 0x44, 0x22, 0x57, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x52, 0x73, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c,
	0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61,
	0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x22, 0x6d, 0x0a, 0x14, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x62, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x16, 0x0a, 0x14, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x52,
	0x73, 0x70, 0x22, 0x4e, 0x0a, 0x19, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x31, 0x0a, 0x06, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x41, 0x0a, 0x19, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x22, 0x91, 0x01, 0x0a, 0x15, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x72,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x22, 0x17, 0x0a, 0x15, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x52,
	0x73, 0x70, 0x22, 0xb4, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x06, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x63, 0x74, 0x53, 0x63, 0x6f, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x74, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x50, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x74, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x50, 0x22, 0x75, 0x0a, 0x0f, 0x51, 0x75, 0x69,
	0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74,
	0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x69, 0x64,
	0x22, 0x96, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c,
	0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50,
	0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x74, 0x72, 0x44, 0x65, 0x76, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x74, 0x72, 0x44, 0x65, 0x76, 0x49, 0x44, 0x22, 0x94, 0x01, 0x0a, 0x0c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x72, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x72, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x7a, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50,
	0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c,
	0x61, 0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x74, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x94, 0x01, 0x0a,
	0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x74, 0x72, 0x44, 0x65, 0x76, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x74, 0x72, 0x44, 0x65, 0x76, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x74, 0x72, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x72, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x53, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x22, 0x27, 0x0a, 0x0b, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22, 0x0d, 0x0a, 0x0b,
	0x52, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x22, 0x27, 0x0a, 0x0b, 0x52,
	0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x0d, 0x0a, 0x0b, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76,
	0x52, 0x73, 0x70, 0x22, 0x29, 0x0a, 0x0b, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x22, 0x0d,
	0x0a, 0x0b, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x22, 0x29, 0x0a,
	0x0b, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x74, 0x72, 0x44, 0x65, 0x76, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x74, 0x72, 0x44, 0x65, 0x76, 0x49, 0x44, 0x22, 0x0d, 0x0a, 0x0b, 0x53, 0x73, 0x79, 0x6e,
	0x63, 0x44, 0x65, 0x76, 0x52, 0x73, 0x70, 0x22, 0x67, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x75,
	0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72,
	0x22, 0x57, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x74, 0x42, 0x75, 0x73, 0x69, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x74, 0x42, 0x75,
	0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4e, 0x0a, 0x10, 0x4d, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x65, 0x63,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76,
	0x65, 0x63, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x22, 0xc6, 0x03, 0x0a, 0x10, 0x4d, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x48,
	0x0a, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x53,
	0x75, 0x63, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75,
	0x63, 0x63, 0x1a, 0x7e, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x6c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x6c, 0x4c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6c, 0x4c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x6c, 0x55,
	0x69, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x62,
	0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x77, 0x0a, 0x11, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x53, 0x72,
	0x63, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74,
	0x72, 0x53, 0x72, 0x63, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72,
	0x44, 0x73, 0x74, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x74, 0x72, 0x44, 0x73, 0x74, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x76,
	0x65, 0x63, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x76, 0x65, 0x63, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x22, 0xf0, 0x02, 0x0a, 0x11,
	0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73,
	0x70, 0x12, 0x49, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x12, 0x49, 0x0a, 0x07,
	0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73,
	0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07,
	0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x1a, 0x24, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x1a, 0x3a, 0x0a,
	0x0c, 0x4d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x63, 0x0a, 0x0c, 0x4d, 0x61, 0x70,
	0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x85,
	0x01, 0x0a, 0x0f, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x75, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50,
	0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c,
	0x61, 0x74, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x55, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x6c, 0x56, 0x69, 0x72, 0x74,
	0x75, 0x61, 0x6c, 0x55, 0x69, 0x64, 0x22, 0x75, 0x0a, 0x0f, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x74, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x1e, 0x0a,
	0x0a, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x32, 0xb9, 0x10,
	0x0a, 0x0c, 0x47, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4f,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x46, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x0c, 0x53,
	0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x65,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x53, 0x65, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x58, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69,
	0x64, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f,
	0x70, 0x65, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50,
	0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x0e, 0x50, 0x6c, 0x61,
	0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x22, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x50, 0x6c,
	0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x0f, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50,
	0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x5b, 0x0a, 0x0f, 0x4d, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x69, 0x64, 0x32, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x50, 0x6c, 0x61, 0x74,
	0x55, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a,
	0x0c, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x43, 0x6f, 0x64, 0x65, 0x32, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65,
	0x6e, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x73,
	0x70, 0x12, 0x40, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x12, 0x1a, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x12, 0x1a, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x76, 0x52, 0x73, 0x70, 0x12, 0x61, 0x0a, 0x11, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x25, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f,
	0x70, 0x65, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x70, 0x0a, 0x16, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x53, 0x69,
	0x67, 0x6e, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x2a,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e,
	0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4b, 0x65, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x64, 0x0a, 0x12, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79,
	0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70,
	0x65, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x52, 0x73, 0x70,
	0x12, 0x52, 0x0a, 0x0c, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70,
	0x65, 0x6e, 0x2e, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70,
	0x70, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f,
	0x70, 0x65, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70,
	0x65, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x52, 0x73, 0x70, 0x12,
	0x49, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x12, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x76, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x52, 0x73,
	0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x52,
	0x73, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x12, 0x1c,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e,
	0x2e, 0x52, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x52,
	0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x53, 0x73,
	0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x52,
	0x73, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x12, 0x1c,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e,
	0x2e, 0x53, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x53,
	0x73, 0x79, 0x6e, 0x63, 0x44, 0x65, 0x76, 0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x42, 0x75, 0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70,
	0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x73, 0x69, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x73, 0x70, 0x12, 0x55, 0x0a, 0x0d, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x0e, 0x4d,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x22, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e,
	0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f,
	0x70, 0x65, 0x6e, 0x2e, 0x4d, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x32, 0x4f, 0x70, 0x65, 0x6e,
	0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x0c, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x42, 0x43, 0x5a, 0x41, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x3b, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_gopen_gopen_proto_rawDescOnce sync.Once
	file_pb_gopen_gopen_proto_rawDescData = file_pb_gopen_gopen_proto_rawDesc
)

func file_pb_gopen_gopen_proto_rawDescGZIP() []byte {
	file_pb_gopen_gopen_proto_rawDescOnce.Do(func() {
		file_pb_gopen_gopen_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_gopen_gopen_proto_rawDescData)
	})
	return file_pb_gopen_gopen_proto_rawDescData
}

var file_pb_gopen_gopen_proto_msgTypes = make([]protoimpl.MessageInfo, 62)
var file_pb_gopen_gopen_proto_goTypes = []interface{}{
	(*GetAuthCodeReq)(nil),            // 0: component.gopen.GetAuthCodeReq
	(*GetAuthCodeRsp)(nil),            // 1: component.gopen.GetAuthCodeRsp
	(*GetTokenReq)(nil),               // 2: component.gopen.GetTokenReq
	(*GetTokenRsp)(nil),               // 3: component.gopen.GetTokenRsp
	(*GetAuthorizeReq)(nil),           // 4: component.gopen.GetAuthorizeReq
	(*GetAuthorizeRsp)(nil),           // 5: component.gopen.GetAuthorizeRsp
	(*SetAuthorizeReq)(nil),           // 6: component.gopen.SetAuthorizeReq
	(*SetAuthorizeRsp)(nil),           // 7: component.gopen.SetAuthorizeRsp
	(*Openid2PlatUidReq)(nil),         // 8: component.gopen.Openid2PlatUidReq
	(*Openid2PlatUidRsp)(nil),         // 9: component.gopen.Openid2PlatUidRsp
	(*PlatUid2OpenidReq)(nil),         // 10: component.gopen.PlatUid2OpenidReq
	(*PlatUid2OpenidRsp)(nil),         // 11: component.gopen.PlatUid2OpenidRsp
	(*MOpenid2PlatUidReq)(nil),        // 12: component.gopen.MOpenid2PlatUidReq
	(*MOpenid2PlatUidRsp)(nil),        // 13: component.gopen.MOpenid2PlatUidRsp
	(*MPlatUid2OpenidReq)(nil),        // 14: component.gopen.MPlatUid2OpenidReq
	(*MPlatUid2OpenidRsp)(nil),        // 15: component.gopen.MPlatUid2OpenidRsp
	(*Code2SessionReq)(nil),           // 16: component.gopen.Code2SessionReq
	(*Code2SessionRsp)(nil),           // 17: component.gopen.Code2SessionRsp
	(*GetAppReq)(nil),                 // 18: component.gopen.GetAppReq
	(*GetAppRsp)(nil),                 // 19: component.gopen.GetAppRsp
	(*GetDevReq)(nil),                 // 20: component.gopen.GetDevReq
	(*GetDevRsp)(nil),                 // 21: component.gopen.GetDevRsp
	(*ValidateTokenSignReq)(nil),      // 22: component.gopen.ValidateTokenSignReq
	(*ValidateTokenSignRsp)(nil),      // 23: component.gopen.ValidateTokenSignRsp
	(*ValidateSessionKeySignReq)(nil), // 24: component.gopen.ValidateSessionKeySignReq
	(*ValidateSessionKeySignRsp)(nil), // 25: component.gopen.ValidateSessionKeySignRsp
	(*ValidateSessionKeyReq)(nil),     // 26: component.gopen.ValidateSessionKeyReq
	(*ValidateSessionKeyRsp)(nil),     // 27: component.gopen.ValidateSessionKeyRsp
	(*QuickSessionReq)(nil),           // 28: component.gopen.QuickSessionReq
	(*QuickSessionRsp)(nil),           // 29: component.gopen.QuickSessionRsp
	(*CreateAppReq)(nil),              // 30: component.gopen.CreateAppReq
	(*CreateAppRsp)(nil),              // 31: component.gopen.CreateAppRsp
	(*CreateDevReq)(nil),              // 32: component.gopen.CreateDevReq
	(*CreateDevRsp)(nil),              // 33: component.gopen.CreateDevRsp
	(*RsyncAppReq)(nil),               // 34: component.gopen.RsyncAppReq
	(*RsyncAppRsp)(nil),               // 35: component.gopen.RsyncAppRsp
	(*RsyncDevReq)(nil),               // 36: component.gopen.RsyncDevReq
	(*RsyncDevRsp)(nil),               // 37: component.gopen.RsyncDevRsp
	(*SsyncAppReq)(nil),               // 38: component.gopen.SsyncAppReq
	(*SsyncAppRsp)(nil),               // 39: component.gopen.SsyncAppRsp
	(*SsyncDevReq)(nil),               // 40: component.gopen.SsyncDevReq
	(*SsyncDevRsp)(nil),               // 41: component.gopen.SsyncDevRsp
	(*GetBusiSessionReq)(nil),         // 42: component.gopen.GetBusiSessionReq
	(*GetBusiSessionRsp)(nil),         // 43: component.gopen.GetBusiSessionRsp
	(*MGetLoginInfoReq)(nil),          // 44: component.gopen.MGetLoginInfoReq
	(*MGetLoginInfoRsp)(nil),          // 45: component.gopen.MGetLoginInfoRsp
	(*MOpenid2OpenidReq)(nil),         // 46: component.gopen.MOpenid2OpenidReq
	(*MOpenid2OpenidRsp)(nil),         // 47: component.gopen.MOpenid2OpenidRsp
	(*VirtualLoginReq)(nil),           // 48: component.gopen.VirtualLoginReq
	(*VirtualLoginRsp)(nil),           // 49: component.gopen.VirtualLoginRsp
	(*MOpenid2PlatUidRsp_Item)(nil),   // 50: component.gopen.MOpenid2PlatUidRsp.Item
	nil,                               // 51: component.gopen.MOpenid2PlatUidRsp.MapFailEntry
	nil,                               // 52: component.gopen.MOpenid2PlatUidRsp.MapSuccEntry
	(*MPlatUid2OpenidRsp_Item)(nil),   // 53: component.gopen.MPlatUid2OpenidRsp.Item
	nil,                               // 54: component.gopen.MPlatUid2OpenidRsp.MapFailEntry
	nil,                               // 55: component.gopen.MPlatUid2OpenidRsp.MapSuccEntry
	(*MGetLoginInfoRsp_Item)(nil),     // 56: component.gopen.MGetLoginInfoRsp.Item
	nil,                               // 57: component.gopen.MGetLoginInfoRsp.MapFailEntry
	nil,                               // 58: component.gopen.MGetLoginInfoRsp.MapSuccEntry
	(*MOpenid2OpenidRsp_Item)(nil),    // 59: component.gopen.MOpenid2OpenidRsp.Item
	nil,                               // 60: component.gopen.MOpenid2OpenidRsp.MapFailEntry
	nil,                               // 61: component.gopen.MOpenid2OpenidRsp.MapSuccEntry
	(*AuthInfo)(nil),                  // 62: component.gopen.AuthInfo
	(*ScopeConf)(nil),                 // 63: component.gopen.ScopeConf
	(EAcntType)(0),                    // 64: component.gopen.EAcntType
	(*SignData)(nil),                  // 65: component.gopen.SignData
	(*BusiSession)(nil),               // 66: component.gopen.BusiSession
}
var file_pb_gopen_gopen_proto_depIdxs = []int32{
	62, // 0: component.gopen.GetAuthCodeReq.stAuth:type_name -> component.gopen.AuthInfo
	63, // 1: component.gopen.GetAuthorizeRsp.vctScopeConf:type_name -> component.gopen.ScopeConf
	63, // 2: component.gopen.SetAuthorizeRsp.vctScopeConf:type_name -> component.gopen.ScopeConf
	64, // 3: component.gopen.Openid2PlatUidRsp.eType:type_name -> component.gopen.EAcntType
	51, // 4: component.gopen.MOpenid2PlatUidRsp.mapFail:type_name -> component.gopen.MOpenid2PlatUidRsp.MapFailEntry
	52, // 5: component.gopen.MOpenid2PlatUidRsp.mapSucc:type_name -> component.gopen.MOpenid2PlatUidRsp.MapSuccEntry
	54, // 6: component.gopen.MPlatUid2OpenidRsp.mapFail:type_name -> component.gopen.MPlatUid2OpenidRsp.MapFailEntry
	55, // 7: component.gopen.MPlatUid2OpenidRsp.mapSucc:type_name -> component.gopen.MPlatUid2OpenidRsp.MapSuccEntry
	65, // 8: component.gopen.ValidateTokenSignReq.stData:type_name -> component.gopen.SignData
	65, // 9: component.gopen.ValidateSessionKeySignReq.stData:type_name -> component.gopen.SignData
	62, // 10: component.gopen.QuickSessionReq.stAuth:type_name -> component.gopen.AuthInfo
	66, // 11: component.gopen.GetBusiSessionRsp.stBusiSession:type_name -> component.gopen.BusiSession
	57, // 12: component.gopen.MGetLoginInfoRsp.mapFail:type_name -> component.gopen.MGetLoginInfoRsp.MapFailEntry
	58, // 13: component.gopen.MGetLoginInfoRsp.mapSucc:type_name -> component.gopen.MGetLoginInfoRsp.MapSuccEntry
	60, // 14: component.gopen.MOpenid2OpenidRsp.mapFail:type_name -> component.gopen.MOpenid2OpenidRsp.MapFailEntry
	61, // 15: component.gopen.MOpenid2OpenidRsp.mapSucc:type_name -> component.gopen.MOpenid2OpenidRsp.MapSuccEntry
	64, // 16: component.gopen.MOpenid2PlatUidRsp.Item.eType:type_name -> component.gopen.EAcntType
	50, // 17: component.gopen.MOpenid2PlatUidRsp.MapSuccEntry.value:type_name -> component.gopen.MOpenid2PlatUidRsp.Item
	53, // 18: component.gopen.MPlatUid2OpenidRsp.MapSuccEntry.value:type_name -> component.gopen.MPlatUid2OpenidRsp.Item
	56, // 19: component.gopen.MGetLoginInfoRsp.MapSuccEntry.value:type_name -> component.gopen.MGetLoginInfoRsp.Item
	59, // 20: component.gopen.MOpenid2OpenidRsp.MapSuccEntry.value:type_name -> component.gopen.MOpenid2OpenidRsp.Item
	0,  // 21: component.gopen.GOpenService.GetAuthCode:input_type -> component.gopen.GetAuthCodeReq
	2,  // 22: component.gopen.GOpenService.GetToken:input_type -> component.gopen.GetTokenReq
	4,  // 23: component.gopen.GOpenService.GetAuthorize:input_type -> component.gopen.GetAuthorizeReq
	6,  // 24: component.gopen.GOpenService.SetAuthorize:input_type -> component.gopen.SetAuthorizeReq
	8,  // 25: component.gopen.GOpenService.Openid2PlatUid:input_type -> component.gopen.Openid2PlatUidReq
	10, // 26: component.gopen.GOpenService.PlatUid2Openid:input_type -> component.gopen.PlatUid2OpenidReq
	12, // 27: component.gopen.GOpenService.MOpenid2PlatUid:input_type -> component.gopen.MOpenid2PlatUidReq
	14, // 28: component.gopen.GOpenService.MPlatUid2Openid:input_type -> component.gopen.MPlatUid2OpenidReq
	16, // 29: component.gopen.GOpenService.Code2Session:input_type -> component.gopen.Code2SessionReq
	18, // 30: component.gopen.GOpenService.GetApp:input_type -> component.gopen.GetAppReq
	20, // 31: component.gopen.GOpenService.GetDev:input_type -> component.gopen.GetDevReq
	22, // 32: component.gopen.GOpenService.ValidateTokenSign:input_type -> component.gopen.ValidateTokenSignReq
	24, // 33: component.gopen.GOpenService.ValidateSessionKeySign:input_type -> component.gopen.ValidateSessionKeySignReq
	26, // 34: component.gopen.GOpenService.ValidateSessionKey:input_type -> component.gopen.ValidateSessionKeyReq
	28, // 35: component.gopen.GOpenService.QuickSession:input_type -> component.gopen.QuickSessionReq
	30, // 36: component.gopen.GOpenService.CreateApp:input_type -> component.gopen.CreateAppReq
	32, // 37: component.gopen.GOpenService.CreateDev:input_type -> component.gopen.CreateDevReq
	34, // 38: component.gopen.GOpenService.RsyncApp:input_type -> component.gopen.RsyncAppReq
	36, // 39: component.gopen.GOpenService.RsyncDev:input_type -> component.gopen.RsyncDevReq
	38, // 40: component.gopen.GOpenService.SsyncApp:input_type -> component.gopen.SsyncAppReq
	40, // 41: component.gopen.GOpenService.SsyncDev:input_type -> component.gopen.SsyncDevReq
	42, // 42: component.gopen.GOpenService.GetBusiSession:input_type -> component.gopen.GetBusiSessionReq
	44, // 43: component.gopen.GOpenService.MGetLoginInfo:input_type -> component.gopen.MGetLoginInfoReq
	46, // 44: component.gopen.GOpenService.MOpenid2Openid:input_type -> component.gopen.MOpenid2OpenidReq
	48, // 45: component.gopen.GOpenService.VirtualLogin:input_type -> component.gopen.VirtualLoginReq
	1,  // 46: component.gopen.GOpenService.GetAuthCode:output_type -> component.gopen.GetAuthCodeRsp
	3,  // 47: component.gopen.GOpenService.GetToken:output_type -> component.gopen.GetTokenRsp
	5,  // 48: component.gopen.GOpenService.GetAuthorize:output_type -> component.gopen.GetAuthorizeRsp
	7,  // 49: component.gopen.GOpenService.SetAuthorize:output_type -> component.gopen.SetAuthorizeRsp
	9,  // 50: component.gopen.GOpenService.Openid2PlatUid:output_type -> component.gopen.Openid2PlatUidRsp
	11, // 51: component.gopen.GOpenService.PlatUid2Openid:output_type -> component.gopen.PlatUid2OpenidRsp
	13, // 52: component.gopen.GOpenService.MOpenid2PlatUid:output_type -> component.gopen.MOpenid2PlatUidRsp
	15, // 53: component.gopen.GOpenService.MPlatUid2Openid:output_type -> component.gopen.MPlatUid2OpenidRsp
	17, // 54: component.gopen.GOpenService.Code2Session:output_type -> component.gopen.Code2SessionRsp
	19, // 55: component.gopen.GOpenService.GetApp:output_type -> component.gopen.GetAppRsp
	21, // 56: component.gopen.GOpenService.GetDev:output_type -> component.gopen.GetDevRsp
	23, // 57: component.gopen.GOpenService.ValidateTokenSign:output_type -> component.gopen.ValidateTokenSignRsp
	25, // 58: component.gopen.GOpenService.ValidateSessionKeySign:output_type -> component.gopen.ValidateSessionKeySignRsp
	27, // 59: component.gopen.GOpenService.ValidateSessionKey:output_type -> component.gopen.ValidateSessionKeyRsp
	29, // 60: component.gopen.GOpenService.QuickSession:output_type -> component.gopen.QuickSessionRsp
	31, // 61: component.gopen.GOpenService.CreateApp:output_type -> component.gopen.CreateAppRsp
	33, // 62: component.gopen.GOpenService.CreateDev:output_type -> component.gopen.CreateDevRsp
	35, // 63: component.gopen.GOpenService.RsyncApp:output_type -> component.gopen.RsyncAppRsp
	37, // 64: component.gopen.GOpenService.RsyncDev:output_type -> component.gopen.RsyncDevRsp
	39, // 65: component.gopen.GOpenService.SsyncApp:output_type -> component.gopen.SsyncAppRsp
	41, // 66: component.gopen.GOpenService.SsyncDev:output_type -> component.gopen.SsyncDevRsp
	43, // 67: component.gopen.GOpenService.GetBusiSession:output_type -> component.gopen.GetBusiSessionRsp
	45, // 68: component.gopen.GOpenService.MGetLoginInfo:output_type -> component.gopen.MGetLoginInfoRsp
	47, // 69: component.gopen.GOpenService.MOpenid2Openid:output_type -> component.gopen.MOpenid2OpenidRsp
	49, // 70: component.gopen.GOpenService.VirtualLogin:output_type -> component.gopen.VirtualLoginRsp
	46, // [46:71] is the sub-list for method output_type
	21, // [21:46] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_pb_gopen_gopen_proto_init() }
func file_pb_gopen_gopen_proto_init() {
	if File_pb_gopen_gopen_proto != nil {
		return
	}
	file_pb_gopen_gopen_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_gopen_gopen_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthCodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthCodeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthorizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAuthorizeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAuthorizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAuthorizeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Openid2PlatUidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Openid2PlatUidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatUid2OpenidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlatUid2OpenidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MOpenid2PlatUidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MOpenid2PlatUidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MPlatUid2OpenidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MPlatUid2OpenidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Code2SessionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Code2SessionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDevReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDevRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateTokenSignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateTokenSignRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSessionKeySignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSessionKeySignRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSessionKeyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidateSessionKeyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuickSessionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuickSessionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDevReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDevRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RsyncAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RsyncAppRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RsyncDevReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RsyncDevRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SsyncAppReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SsyncAppRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SsyncDevReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SsyncDevRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusiSessionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBusiSessionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLoginInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLoginInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MOpenid2OpenidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MOpenid2OpenidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualLoginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MOpenid2PlatUidRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MPlatUid2OpenidRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MGetLoginInfoRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopen_gopen_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MOpenid2OpenidRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_gopen_gopen_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   62,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_gopen_gopen_proto_goTypes,
		DependencyIndexes: file_pb_gopen_gopen_proto_depIdxs,
		MessageInfos:      file_pb_gopen_gopen_proto_msgTypes,
	}.Build()
	File_pb_gopen_gopen_proto = out.File
	file_pb_gopen_gopen_proto_rawDesc = nil
	file_pb_gopen_gopen_proto_goTypes = nil
	file_pb_gopen_gopen_proto_depIdxs = nil
}
