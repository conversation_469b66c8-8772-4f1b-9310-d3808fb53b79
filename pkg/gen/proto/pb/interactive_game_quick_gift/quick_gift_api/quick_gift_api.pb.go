// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_quick_gift/quick_gift_api/quick_gift_api.proto

package qucik_gift_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game_quick_gift/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryRecvRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid         string `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`                                       //用户openid
	GameAppid      string `protobuf:"bytes,2,opt,name=game_appid,json=gameAppid,proto3" json:"game_appid,omitempty"`                //游戏appid
	CurrentMatchid string `protobuf:"bytes,3,opt,name=current_matchid,json=currentMatchid,proto3" json:"current_matchid,omitempty"` //当前游戏的场次ID
	PassBack       string `protobuf:"bytes,4,opt,name=pass_back,json=passBack,proto3" json:"pass_back,omitempty"`                   //passback 透传后台,第一次传空
	// int64 query_type = 5; //查询类型 QueryType
	QueryType uint32 `protobuf:"varint,6,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"` //查询类型 QueryType
}

func (x *QueryRecvRecordReq) Reset() {
	*x = QueryRecvRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRecvRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRecvRecordReq) ProtoMessage() {}

func (x *QueryRecvRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRecvRecordReq.ProtoReflect.Descriptor instead.
func (*QueryRecvRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryRecvRecordReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *QueryRecvRecordReq) GetGameAppid() string {
	if x != nil {
		return x.GameAppid
	}
	return ""
}

func (x *QueryRecvRecordReq) GetCurrentMatchid() string {
	if x != nil {
		return x.CurrentMatchid
	}
	return ""
}

func (x *QueryRecvRecordReq) GetPassBack() string {
	if x != nil {
		return x.PassBack
	}
	return ""
}

func (x *QueryRecvRecordReq) GetQueryType() uint32 {
	if x != nil {
		return x.QueryType
	}
	return 0
}

type QueryRecvRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// int64 hase_more = 1; //是否还有 0:没有  1:还有
	PassBack string `protobuf:"bytes,2,opt,name=pass_back,json=passBack,proto3" json:"pass_back,omitempty"` //passback
	GameName string `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"` //游戏名称
	// common.GiftInfo back_gift = 4; //回礼的礼物信息
	Items    []*common.RecordItem `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"`                        //记录列表
	HaseMore uint32               `protobuf:"varint,6,opt,name=hase_more,json=haseMore,proto3" json:"hase_more,omitempty"` //是否还有 0:没有  1:还有
}

func (x *QueryRecvRecordRsp) Reset() {
	*x = QueryRecvRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRecvRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRecvRecordRsp) ProtoMessage() {}

func (x *QueryRecvRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRecvRecordRsp.ProtoReflect.Descriptor instead.
func (*QueryRecvRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryRecvRecordRsp) GetPassBack() string {
	if x != nil {
		return x.PassBack
	}
	return ""
}

func (x *QueryRecvRecordRsp) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

func (x *QueryRecvRecordRsp) GetItems() []*common.RecordItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *QueryRecvRecordRsp) GetHaseMore() uint32 {
	if x != nil {
		return x.HaseMore
	}
	return 0
}

type UpdateRecvRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid    string             `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`                        //用户openid
	GameAppid string             `protobuf:"bytes,2,opt,name=game_appid,json=gameAppid,proto3" json:"game_appid,omitempty"` //游戏appid
	Items     *common.RecordItem `protobuf:"bytes,3,opt,name=items,proto3" json:"items,omitempty"`                          //要更改的item
}

func (x *UpdateRecvRecordReq) Reset() {
	*x = UpdateRecvRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRecvRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRecvRecordReq) ProtoMessage() {}

func (x *UpdateRecvRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRecvRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateRecvRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateRecvRecordReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *UpdateRecvRecordReq) GetGameAppid() string {
	if x != nil {
		return x.GameAppid
	}
	return ""
}

func (x *UpdateRecvRecordReq) GetItems() *common.RecordItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UpdateRecvRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// int64 result_code = 1;
	ResultCode uint32 `protobuf:"varint,2,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
}

func (x *UpdateRecvRecordRsp) Reset() {
	*x = UpdateRecvRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRecvRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRecvRecordRsp) ProtoMessage() {}

func (x *UpdateRecvRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRecvRecordRsp.ProtoReflect.Descriptor instead.
func (*UpdateRecvRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateRecvRecordRsp) GetResultCode() uint32 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

var File_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto protoreflect.FileDescriptor

var file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDesc = []byte{
	0x0a, 0x42, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74,
	0x2f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2f,
	0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66,
	0x74, 0x1a, 0x32, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66,
	0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb0, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x70,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70,
	0x70, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63,
	0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x68, 0x61, 0x73, 0x65, 0x4d, 0x6f, 0x72, 0x65, 0x22, 0x92, 0x01, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x69, 0x64, 0x12, 0x44, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75,
	0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x36, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x76, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x32, 0x8a, 0x02, 0x0a, 0x1b, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x51, 0x75, 0x69,
	0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x41, 0x70, 0x69, 0x12, 0x73, 0x0a, 0x0f, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2f, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x76,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x30, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69,
	0x66, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x76, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x42, 0x62, 0x5a, 0x60, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2f, 0x71, 0x75, 0x63, 0x69,
	0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescOnce sync.Once
	file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescData = file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDesc
)

func file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescData)
	})
	return file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDescData
}

var file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_goTypes = []interface{}{
	(*QueryRecvRecordReq)(nil),  // 0: interactive_game_quick_gift.QueryRecvRecordReq
	(*QueryRecvRecordRsp)(nil),  // 1: interactive_game_quick_gift.QueryRecvRecordRsp
	(*UpdateRecvRecordReq)(nil), // 2: interactive_game_quick_gift.UpdateRecvRecordReq
	(*UpdateRecvRecordRsp)(nil), // 3: interactive_game_quick_gift.UpdateRecvRecordRsp
	(*common.RecordItem)(nil),   // 4: interactive_game_quick_gift.common.RecordItem
}
var file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_depIdxs = []int32{
	4, // 0: interactive_game_quick_gift.QueryRecvRecordRsp.items:type_name -> interactive_game_quick_gift.common.RecordItem
	4, // 1: interactive_game_quick_gift.UpdateRecvRecordReq.items:type_name -> interactive_game_quick_gift.common.RecordItem
	0, // 2: interactive_game_quick_gift.InteractiveGameQuickGiftApi.QueryRecvRecord:input_type -> interactive_game_quick_gift.QueryRecvRecordReq
	2, // 3: interactive_game_quick_gift.InteractiveGameQuickGiftApi.UpdateRecvRecord:input_type -> interactive_game_quick_gift.UpdateRecvRecordReq
	1, // 4: interactive_game_quick_gift.InteractiveGameQuickGiftApi.QueryRecvRecord:output_type -> interactive_game_quick_gift.QueryRecvRecordRsp
	3, // 5: interactive_game_quick_gift.InteractiveGameQuickGiftApi.UpdateRecvRecord:output_type -> interactive_game_quick_gift.UpdateRecvRecordRsp
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_init() }
func file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_init() {
	if File_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRecvRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRecvRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRecvRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRecvRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto = out.File
	file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_rawDesc = nil
	file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_goTypes = nil
	file_pb_interactive_game_quick_gift_quick_gift_api_quick_gift_api_proto_depIdxs = nil
}
