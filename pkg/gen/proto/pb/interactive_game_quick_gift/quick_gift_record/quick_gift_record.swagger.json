{"swagger": "2.0", "info": {"title": "pb/interactive_game_quick_gift/quick_gift_record/quick_gift_record.proto", "version": "version not set"}, "tags": [{"name": "QuickGiftRecordService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game_quick_gift.QuickGiftRecordService/AddQuickGiftRecord": {"post": {"summary": "添加记录", "operationId": "QuickGiftRecordService_AddQuickGiftRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_quick_giftAddQuickGiftRecordRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_quick_giftAddQuickGiftRecordReq"}}], "tags": ["QuickGiftRecordService"]}}, "/interactive_game_quick_gift.QuickGiftRecordService/GetQuickGiftRecord": {"post": {"summary": "查询记录", "operationId": "QuickGiftRecordService_GetQuickGiftRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_quick_giftGetQuickGiftRecordRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_quick_giftGetQuickGiftRecordReq"}}], "tags": ["QuickGiftRecordService"]}}, "/interactive_game_quick_gift.QuickGiftRecordService/UpdateQuickGiftRecord": {"post": {"summary": "更新记录", "operationId": "QuickGiftRecordService_UpdateQuickGiftRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_quick_giftUpdateQuickGiftRecordRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_quick_giftUpdateQuickGiftRecordReq"}}], "tags": ["QuickGiftRecordService"]}}}, "definitions": {"interactive_game_quick_giftAddQuickGiftRecordReq": {"type": "object", "properties": {"gameAppid": {"type": "string", "title": "游戏appid"}, "matchId": {"type": "string", "title": "游戏的场次ID"}, "addItem": {"$ref": "#/definitions/interactive_game_quick_giftItem", "title": "添加数据"}}, "title": "添加记录"}, "interactive_game_quick_giftAddQuickGiftRecordRsp": {"type": "object", "properties": {"resultCode": {"type": "string", "format": "int64"}}}, "interactive_game_quick_giftGetQuickGiftRecordReq": {"type": "object", "properties": {"userOpenid": {"type": "string"}, "gameAppid": {"type": "string", "title": "游戏appid"}, "matchId": {"type": "string", "title": "场次ID"}, "queryType": {"type": "string", "format": "int64", "title": "查询类型 common.QueryType"}, "passBack": {"type": "string"}}, "title": "查询记录"}, "interactive_game_quick_giftGetQuickGiftRecordRsp": {"type": "object", "properties": {"haseMore": {"type": "string", "format": "int64", "title": "是否还有 0:没有  1:还有"}, "passBack": {"type": "string", "title": "passback"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_game_quick_giftItem"}}, "gameName": {"type": "string"}}}, "interactive_game_quick_giftItem": {"type": "object", "properties": {"sendId": {"type": "string", "format": "int64", "title": "赠送的礼物ID"}, "sendNum": {"type": "string", "format": "int64", "title": "赠送个数"}, "sendTs": {"type": "string", "format": "int64", "title": "赠送时间"}, "sendType": {"type": "string", "format": "int64", "title": "参考:common.RecordType"}, "pay": {"type": "string", "format": "int64"}, "billNo": {"type": "string", "title": "订单ID"}, "sendOpenid": {"type": "string", "title": "赠送者"}, "recvOpenid": {"type": "string", "title": "接收者"}, "roomid": {"type": "string", "title": "直播间/歌房的房间ID"}, "showid": {"type": "string", "title": "直播间的场次ID"}, "haseBack": {"type": "string", "format": "int64", "title": "改记录是否回礼过 0:无  1:已回礼"}}}, "interactive_game_quick_giftUpdateItem": {"type": "object", "properties": {"sendOpenid": {"type": "string", "title": "赠送者"}, "recvOpenid": {"type": "string", "title": "接收者"}, "billNo": {"type": "string", "title": "订单ID"}, "sendId": {"type": "string", "format": "int64", "title": "赠送的礼物ID"}, "sendNum": {"type": "string", "format": "int64", "title": "赠送个数"}, "sendTs": {"type": "string", "format": "int64", "title": "赠送时间"}, "sendType": {"type": "string", "format": "int64", "title": "参考:common.RecordType"}, "haseBack": {"type": "string", "format": "int64", "title": "改记录是否回礼过 0:无  1:已回礼"}}}, "interactive_game_quick_giftUpdateQuickGiftRecordReq": {"type": "object", "properties": {"gameAppid": {"type": "string", "title": "游戏appid"}, "updateItem": {"$ref": "#/definitions/interactive_game_quick_giftUpdateItem"}}, "title": "更新记录"}, "interactive_game_quick_giftUpdateQuickGiftRecordRsp": {"type": "object", "properties": {"resultCode": {"type": "string", "format": "int64"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}