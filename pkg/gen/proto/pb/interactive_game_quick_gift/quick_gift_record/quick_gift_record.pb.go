// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_quick_gift/quick_gift_record/quick_gift_record.proto

package quick_gift_record

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SendId     int64  `protobuf:"varint,1,opt,name=send_id,json=sendId,proto3" json:"send_id,omitempty"`       //赠送的礼物ID
	SendNum    int64  `protobuf:"varint,2,opt,name=send_num,json=sendNum,proto3" json:"send_num,omitempty"`    //赠送个数
	SendTs     int64  `protobuf:"varint,3,opt,name=send_ts,json=sendTs,proto3" json:"send_ts,omitempty"`       //赠送时间
	SendType   int64  `protobuf:"varint,4,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"` //参考:common.RecordType
	Pay        int64  `protobuf:"varint,5,opt,name=pay,proto3" json:"pay,omitempty"`
	BillNo     string `protobuf:"bytes,6,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`             //订单ID
	SendOpenid string `protobuf:"bytes,7,opt,name=send_openid,json=sendOpenid,proto3" json:"send_openid,omitempty"` //赠送者
	RecvOpenid string `protobuf:"bytes,8,opt,name=recv_openid,json=recvOpenid,proto3" json:"recv_openid,omitempty"` //接收者
	Roomid     string `protobuf:"bytes,9,opt,name=roomid,proto3" json:"roomid,omitempty"`                           //直播间/歌房的房间ID
	Showid     string `protobuf:"bytes,10,opt,name=showid,proto3" json:"showid,omitempty"`                          //直播间的场次ID
	HaseBack   int64  `protobuf:"varint,11,opt,name=hase_back,json=haseBack,proto3" json:"hase_back,omitempty"`     //改记录是否回礼过 0:无  1:已回礼
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{0}
}

func (x *Item) GetSendId() int64 {
	if x != nil {
		return x.SendId
	}
	return 0
}

func (x *Item) GetSendNum() int64 {
	if x != nil {
		return x.SendNum
	}
	return 0
}

func (x *Item) GetSendTs() int64 {
	if x != nil {
		return x.SendTs
	}
	return 0
}

func (x *Item) GetSendType() int64 {
	if x != nil {
		return x.SendType
	}
	return 0
}

func (x *Item) GetPay() int64 {
	if x != nil {
		return x.Pay
	}
	return 0
}

func (x *Item) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *Item) GetSendOpenid() string {
	if x != nil {
		return x.SendOpenid
	}
	return ""
}

func (x *Item) GetRecvOpenid() string {
	if x != nil {
		return x.RecvOpenid
	}
	return ""
}

func (x *Item) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *Item) GetShowid() string {
	if x != nil {
		return x.Showid
	}
	return ""
}

func (x *Item) GetHaseBack() int64 {
	if x != nil {
		return x.HaseBack
	}
	return 0
}

// 添加记录
type AddQuickGiftRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppid string `protobuf:"bytes,1,opt,name=game_appid,json=gameAppid,proto3" json:"game_appid,omitempty"` //游戏appid
	MatchId   string `protobuf:"bytes,2,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`       //游戏的场次ID
	AddItem   *Item  `protobuf:"bytes,3,opt,name=add_item,json=addItem,proto3" json:"add_item,omitempty"`       //添加数据
}

func (x *AddQuickGiftRecordReq) Reset() {
	*x = AddQuickGiftRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddQuickGiftRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddQuickGiftRecordReq) ProtoMessage() {}

func (x *AddQuickGiftRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddQuickGiftRecordReq.ProtoReflect.Descriptor instead.
func (*AddQuickGiftRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{1}
}

func (x *AddQuickGiftRecordReq) GetGameAppid() string {
	if x != nil {
		return x.GameAppid
	}
	return ""
}

func (x *AddQuickGiftRecordReq) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *AddQuickGiftRecordReq) GetAddItem() *Item {
	if x != nil {
		return x.AddItem
	}
	return nil
}

type AddQuickGiftRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultCode int64 `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
}

func (x *AddQuickGiftRecordRsp) Reset() {
	*x = AddQuickGiftRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddQuickGiftRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddQuickGiftRecordRsp) ProtoMessage() {}

func (x *AddQuickGiftRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddQuickGiftRecordRsp.ProtoReflect.Descriptor instead.
func (*AddQuickGiftRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{2}
}

func (x *AddQuickGiftRecordRsp) GetResultCode() int64 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

// 查询记录
type GetQuickGiftRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserOpenid string `protobuf:"bytes,1,opt,name=user_openid,json=userOpenid,proto3" json:"user_openid,omitempty"`
	GameAppid  string `protobuf:"bytes,2,opt,name=game_appid,json=gameAppid,proto3" json:"game_appid,omitempty"`  //游戏appid
	MatchId    string `protobuf:"bytes,3,opt,name=match_id,json=matchId,proto3" json:"match_id,omitempty"`        //场次ID
	QueryType  int64  `protobuf:"varint,4,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"` //查询类型 common.QueryType
	PassBack   string `protobuf:"bytes,5,opt,name=pass_back,json=passBack,proto3" json:"pass_back,omitempty"`
}

func (x *GetQuickGiftRecordReq) Reset() {
	*x = GetQuickGiftRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuickGiftRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuickGiftRecordReq) ProtoMessage() {}

func (x *GetQuickGiftRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuickGiftRecordReq.ProtoReflect.Descriptor instead.
func (*GetQuickGiftRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{3}
}

func (x *GetQuickGiftRecordReq) GetUserOpenid() string {
	if x != nil {
		return x.UserOpenid
	}
	return ""
}

func (x *GetQuickGiftRecordReq) GetGameAppid() string {
	if x != nil {
		return x.GameAppid
	}
	return ""
}

func (x *GetQuickGiftRecordReq) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *GetQuickGiftRecordReq) GetQueryType() int64 {
	if x != nil {
		return x.QueryType
	}
	return 0
}

func (x *GetQuickGiftRecordReq) GetPassBack() string {
	if x != nil {
		return x.PassBack
	}
	return ""
}

type GetQuickGiftRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HaseMore int64   `protobuf:"varint,1,opt,name=hase_more,json=haseMore,proto3" json:"hase_more,omitempty"` //是否还有 0:没有  1:还有
	PassBack string  `protobuf:"bytes,2,opt,name=pass_back,json=passBack,proto3" json:"pass_back,omitempty"`  //passback
	Items    []*Item `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	GameName string  `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
}

func (x *GetQuickGiftRecordRsp) Reset() {
	*x = GetQuickGiftRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuickGiftRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuickGiftRecordRsp) ProtoMessage() {}

func (x *GetQuickGiftRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuickGiftRecordRsp.ProtoReflect.Descriptor instead.
func (*GetQuickGiftRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{4}
}

func (x *GetQuickGiftRecordRsp) GetHaseMore() int64 {
	if x != nil {
		return x.HaseMore
	}
	return 0
}

func (x *GetQuickGiftRecordRsp) GetPassBack() string {
	if x != nil {
		return x.PassBack
	}
	return ""
}

func (x *GetQuickGiftRecordRsp) GetItems() []*Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GetQuickGiftRecordRsp) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

type UpdateItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SendOpenid string `protobuf:"bytes,1,opt,name=send_openid,json=sendOpenid,proto3" json:"send_openid,omitempty"` //赠送者
	RecvOpenid string `protobuf:"bytes,2,opt,name=recv_openid,json=recvOpenid,proto3" json:"recv_openid,omitempty"` //接收者
	BillNo     string `protobuf:"bytes,3,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`             //订单ID
	SendId     int64  `protobuf:"varint,4,opt,name=send_id,json=sendId,proto3" json:"send_id,omitempty"`            //赠送的礼物ID
	SendNum    int64  `protobuf:"varint,5,opt,name=send_num,json=sendNum,proto3" json:"send_num,omitempty"`         //赠送个数
	SendTs     int64  `protobuf:"varint,6,opt,name=send_ts,json=sendTs,proto3" json:"send_ts,omitempty"`            //赠送时间
	SendType   int64  `protobuf:"varint,7,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`      //参考:common.RecordType
	HaseBack   int64  `protobuf:"varint,8,opt,name=hase_back,json=haseBack,proto3" json:"hase_back,omitempty"`      //改记录是否回礼过 0:无  1:已回礼
}

func (x *UpdateItem) Reset() {
	*x = UpdateItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateItem) ProtoMessage() {}

func (x *UpdateItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateItem.ProtoReflect.Descriptor instead.
func (*UpdateItem) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateItem) GetSendOpenid() string {
	if x != nil {
		return x.SendOpenid
	}
	return ""
}

func (x *UpdateItem) GetRecvOpenid() string {
	if x != nil {
		return x.RecvOpenid
	}
	return ""
}

func (x *UpdateItem) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *UpdateItem) GetSendId() int64 {
	if x != nil {
		return x.SendId
	}
	return 0
}

func (x *UpdateItem) GetSendNum() int64 {
	if x != nil {
		return x.SendNum
	}
	return 0
}

func (x *UpdateItem) GetSendTs() int64 {
	if x != nil {
		return x.SendTs
	}
	return 0
}

func (x *UpdateItem) GetSendType() int64 {
	if x != nil {
		return x.SendType
	}
	return 0
}

func (x *UpdateItem) GetHaseBack() int64 {
	if x != nil {
		return x.HaseBack
	}
	return 0
}

// 更新记录
type UpdateQuickGiftRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppid  string      `protobuf:"bytes,1,opt,name=game_appid,json=gameAppid,proto3" json:"game_appid,omitempty"` //游戏appid
	UpdateItem *UpdateItem `protobuf:"bytes,2,opt,name=update_item,json=updateItem,proto3" json:"update_item,omitempty"`
}

func (x *UpdateQuickGiftRecordReq) Reset() {
	*x = UpdateQuickGiftRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateQuickGiftRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQuickGiftRecordReq) ProtoMessage() {}

func (x *UpdateQuickGiftRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQuickGiftRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateQuickGiftRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateQuickGiftRecordReq) GetGameAppid() string {
	if x != nil {
		return x.GameAppid
	}
	return ""
}

func (x *UpdateQuickGiftRecordReq) GetUpdateItem() *UpdateItem {
	if x != nil {
		return x.UpdateItem
	}
	return nil
}

type UpdateQuickGiftRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResultCode int64 `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3" json:"result_code,omitempty"`
}

func (x *UpdateQuickGiftRecordRsp) Reset() {
	*x = UpdateQuickGiftRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateQuickGiftRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateQuickGiftRecordRsp) ProtoMessage() {}

func (x *UpdateQuickGiftRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateQuickGiftRecordRsp.ProtoReflect.Descriptor instead.
func (*UpdateQuickGiftRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateQuickGiftRecordRsp) GetResultCode() int64 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

var File_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto protoreflect.FileDescriptor

var file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDesc = []byte{
	0x0a, 0x48, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74,
	0x2f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x2f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69,
	0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x22, 0xaa, 0x02, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x65, 0x6e,
	0x64, 0x4e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x70, 0x61, 0x79, 0x12, 0x17, 0x0a, 0x07,
	0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x70,
	0x65, 0x6e, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x6f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x63,
	0x76, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x68, 0x6f, 0x77, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x65, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x68, 0x61, 0x73, 0x65,
	0x42, 0x61, 0x63, 0x6b, 0x22, 0x8f, 0x01, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x51, 0x75, 0x69, 0x63,
	0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x69, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75,
	0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x38, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x51, 0x75, 0x69,
	0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0xae, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x73, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63,
	0x6b, 0x22, 0xa7, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69,
	0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x68, 0x61, 0x73, 0x65, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x73, 0x73,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69,
	0x66, 0x74, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xee, 0x01, 0x0a, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x6e, 0x64, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x6e, 0x64, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x63, 0x76, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x65, 0x63, 0x76, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x73, 0x65, 0x6e, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x68, 0x61, 0x73, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x83, 0x01, 0x0a,
	0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x69, 0x64, 0x12, 0x48, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x22, 0x3b, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x69, 0x63,
	0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x32,
	0x9c, 0x03, 0x0a, 0x16, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x12, 0x41, 0x64,
	0x64, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x32, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x41,
	0x64, 0x64, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x32, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69,
	0x66, 0x74, 0x2e, 0x41, 0x64, 0x64, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x7c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x51,
	0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x32,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x32, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x35, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x35, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b,
	0x5f, 0x67, 0x69, 0x66, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x51, 0x75, 0x69, 0x63,
	0x6b, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x42, 0x65,
	0x5a, 0x63, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67,
	0x69, 0x66, 0x74, 0x2f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescOnce sync.Once
	file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescData = file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDesc
)

func file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescData)
	})
	return file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDescData
}

var file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_goTypes = []interface{}{
	(*Item)(nil),                     // 0: interactive_game_quick_gift.Item
	(*AddQuickGiftRecordReq)(nil),    // 1: interactive_game_quick_gift.AddQuickGiftRecordReq
	(*AddQuickGiftRecordRsp)(nil),    // 2: interactive_game_quick_gift.AddQuickGiftRecordRsp
	(*GetQuickGiftRecordReq)(nil),    // 3: interactive_game_quick_gift.GetQuickGiftRecordReq
	(*GetQuickGiftRecordRsp)(nil),    // 4: interactive_game_quick_gift.GetQuickGiftRecordRsp
	(*UpdateItem)(nil),               // 5: interactive_game_quick_gift.UpdateItem
	(*UpdateQuickGiftRecordReq)(nil), // 6: interactive_game_quick_gift.UpdateQuickGiftRecordReq
	(*UpdateQuickGiftRecordRsp)(nil), // 7: interactive_game_quick_gift.UpdateQuickGiftRecordRsp
}
var file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_depIdxs = []int32{
	0, // 0: interactive_game_quick_gift.AddQuickGiftRecordReq.add_item:type_name -> interactive_game_quick_gift.Item
	0, // 1: interactive_game_quick_gift.GetQuickGiftRecordRsp.items:type_name -> interactive_game_quick_gift.Item
	5, // 2: interactive_game_quick_gift.UpdateQuickGiftRecordReq.update_item:type_name -> interactive_game_quick_gift.UpdateItem
	1, // 3: interactive_game_quick_gift.QuickGiftRecordService.AddQuickGiftRecord:input_type -> interactive_game_quick_gift.AddQuickGiftRecordReq
	3, // 4: interactive_game_quick_gift.QuickGiftRecordService.GetQuickGiftRecord:input_type -> interactive_game_quick_gift.GetQuickGiftRecordReq
	6, // 5: interactive_game_quick_gift.QuickGiftRecordService.UpdateQuickGiftRecord:input_type -> interactive_game_quick_gift.UpdateQuickGiftRecordReq
	2, // 6: interactive_game_quick_gift.QuickGiftRecordService.AddQuickGiftRecord:output_type -> interactive_game_quick_gift.AddQuickGiftRecordRsp
	4, // 7: interactive_game_quick_gift.QuickGiftRecordService.GetQuickGiftRecord:output_type -> interactive_game_quick_gift.GetQuickGiftRecordRsp
	7, // 8: interactive_game_quick_gift.QuickGiftRecordService.UpdateQuickGiftRecord:output_type -> interactive_game_quick_gift.UpdateQuickGiftRecordRsp
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_init() }
func file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_init() {
	if File_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddQuickGiftRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddQuickGiftRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuickGiftRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuickGiftRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateQuickGiftRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateQuickGiftRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto = out.File
	file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_rawDesc = nil
	file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_goTypes = nil
	file_pb_interactive_game_quick_gift_quick_gift_record_quick_gift_record_proto_depIdxs = nil
}
