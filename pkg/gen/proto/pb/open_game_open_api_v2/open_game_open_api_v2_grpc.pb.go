// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/open_game_open_api_v2/open_game_open_api_v2.proto

package open_game_open_api_v2

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	OpenGameOpenApiV2_PayV2_FullMethodName              = "/interface.open_game_open_api_v2.OpenGameOpenApiV2/PayV2"
	OpenGameOpenApiV2_SendSingleRewardV2_FullMethodName = "/interface.open_game_open_api_v2.OpenGameOpenApiV2/SendSingleRewardV2"
	OpenGameOpenApiV2_RewardDetailV2_FullMethodName     = "/interface.open_game_open_api_v2.OpenGameOpenApiV2/RewardDetailV2"
)

// OpenGameOpenApiV2Client is the client API for OpenGameOpenApiV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OpenGameOpenApiV2Client interface {
	// 支付V2
	PayV2(ctx context.Context, in *PayV2Req, opts ...grpc.CallOption) (*PayV2Rsp, error)
	// 发送平台礼物V2
	SendSingleRewardV2(ctx context.Context, in *SendSingleRewardV2Req, opts ...grpc.CallOption) (*SendSingleRewardV2Rsp, error)
	// 礼物信息V2
	RewardDetailV2(ctx context.Context, in *RewardDetailV2Req, opts ...grpc.CallOption) (*RewardDetailV2Rsp, error)
}

type openGameOpenApiV2Client struct {
	cc grpc.ClientConnInterface
}

func NewOpenGameOpenApiV2Client(cc grpc.ClientConnInterface) OpenGameOpenApiV2Client {
	return &openGameOpenApiV2Client{cc}
}

func (c *openGameOpenApiV2Client) PayV2(ctx context.Context, in *PayV2Req, opts ...grpc.CallOption) (*PayV2Rsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayV2Rsp)
	err := c.cc.Invoke(ctx, OpenGameOpenApiV2_PayV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openGameOpenApiV2Client) SendSingleRewardV2(ctx context.Context, in *SendSingleRewardV2Req, opts ...grpc.CallOption) (*SendSingleRewardV2Rsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendSingleRewardV2Rsp)
	err := c.cc.Invoke(ctx, OpenGameOpenApiV2_SendSingleRewardV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *openGameOpenApiV2Client) RewardDetailV2(ctx context.Context, in *RewardDetailV2Req, opts ...grpc.CallOption) (*RewardDetailV2Rsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RewardDetailV2Rsp)
	err := c.cc.Invoke(ctx, OpenGameOpenApiV2_RewardDetailV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OpenGameOpenApiV2Server is the server API for OpenGameOpenApiV2 service.
// All implementations should embed UnimplementedOpenGameOpenApiV2Server
// for forward compatibility
type OpenGameOpenApiV2Server interface {
	// 支付V2
	PayV2(context.Context, *PayV2Req) (*PayV2Rsp, error)
	// 发送平台礼物V2
	SendSingleRewardV2(context.Context, *SendSingleRewardV2Req) (*SendSingleRewardV2Rsp, error)
	// 礼物信息V2
	RewardDetailV2(context.Context, *RewardDetailV2Req) (*RewardDetailV2Rsp, error)
}

// UnimplementedOpenGameOpenApiV2Server should be embedded to have forward compatible implementations.
type UnimplementedOpenGameOpenApiV2Server struct {
}

func (UnimplementedOpenGameOpenApiV2Server) PayV2(context.Context, *PayV2Req) (*PayV2Rsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayV2 not implemented")
}
func (UnimplementedOpenGameOpenApiV2Server) SendSingleRewardV2(context.Context, *SendSingleRewardV2Req) (*SendSingleRewardV2Rsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSingleRewardV2 not implemented")
}
func (UnimplementedOpenGameOpenApiV2Server) RewardDetailV2(context.Context, *RewardDetailV2Req) (*RewardDetailV2Rsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RewardDetailV2 not implemented")
}

// UnsafeOpenGameOpenApiV2Server may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OpenGameOpenApiV2Server will
// result in compilation errors.
type UnsafeOpenGameOpenApiV2Server interface {
	mustEmbedUnimplementedOpenGameOpenApiV2Server()
}

func RegisterOpenGameOpenApiV2Server(s grpc.ServiceRegistrar, srv OpenGameOpenApiV2Server) {
	s.RegisterService(&OpenGameOpenApiV2_ServiceDesc, srv)
}

func _OpenGameOpenApiV2_PayV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenGameOpenApiV2Server).PayV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenGameOpenApiV2_PayV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenGameOpenApiV2Server).PayV2(ctx, req.(*PayV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenGameOpenApiV2_SendSingleRewardV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSingleRewardV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenGameOpenApiV2Server).SendSingleRewardV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenGameOpenApiV2_SendSingleRewardV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenGameOpenApiV2Server).SendSingleRewardV2(ctx, req.(*SendSingleRewardV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _OpenGameOpenApiV2_RewardDetailV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RewardDetailV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OpenGameOpenApiV2Server).RewardDetailV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OpenGameOpenApiV2_RewardDetailV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OpenGameOpenApiV2Server).RewardDetailV2(ctx, req.(*RewardDetailV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

// OpenGameOpenApiV2_ServiceDesc is the grpc.ServiceDesc for OpenGameOpenApiV2 service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OpenGameOpenApiV2_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interface.open_game_open_api_v2.OpenGameOpenApiV2",
	HandlerType: (*OpenGameOpenApiV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayV2",
			Handler:    _OpenGameOpenApiV2_PayV2_Handler,
		},
		{
			MethodName: "SendSingleRewardV2",
			Handler:    _OpenGameOpenApiV2_SendSingleRewardV2_Handler,
		},
		{
			MethodName: "RewardDetailV2",
			Handler:    _OpenGameOpenApiV2_RewardDetailV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/open_game_open_api_v2/open_game_open_api_v2.proto",
}
