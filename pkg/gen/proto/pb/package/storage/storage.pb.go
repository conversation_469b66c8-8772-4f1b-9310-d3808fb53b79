// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/package/storage/storage.proto

package storage

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/package/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Package struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId           int64                    `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
	Name                string                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description         string                   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	PriceType           common.PackagePriceType  `protobuf:"varint,4,opt,name=priceType,proto3,enum=package.common.PackagePriceType" json:"priceType,omitempty"`
	PriceKB             int64                    `protobuf:"varint,5,opt,name=priceKB,proto3" json:"priceKB,omitempty"` // 价格
	PriceGreen          *Package_PriceGreen      `protobuf:"bytes,6,opt,name=priceGreen,proto3" json:"priceGreen,omitempty"`
	Targeting           *Package_Targeting       `protobuf:"bytes,7,opt,name=targeting,proto3" json:"targeting,omitempty"`
	Visibility          common.PackageVisibility `protobuf:"varint,8,opt,name=visibility,proto3,enum=package.common.PackageVisibility" json:"visibility,omitempty"`
	SceneLobbyInterval  int64                    `protobuf:"varint,9,opt,name=sceneLobbyInterval,proto3" json:"sceneLobbyInterval,omitempty"` // 大厅弹框间隔 秒
	Items               []*Package_Item          `protobuf:"bytes,10,rep,name=items,proto3" json:"items,omitempty"`
	VisibilityTimeRange *Package_TimeRange       `protobuf:"bytes,11,opt,name=visibilityTimeRange,proto3" json:"visibilityTimeRange,omitempty"`
	VisibilityFrequency *Package_Frequency       `protobuf:"bytes,12,opt,name=visibilityFrequency,proto3" json:"visibilityFrequency,omitempty"`
	SceneUIs            []*Package_SceneUI       `protobuf:"bytes,13,rep,name=sceneUIs,proto3" json:"sceneUIs,omitempty"`
	AndroidDiscount     int64                    `protobuf:"varint,14,opt,name=androidDiscount,proto3" json:"androidDiscount,omitempty"` // 折扣
	Enable              bool                     `protobuf:"varint,15,opt,name=enable,proto3" json:"enable,omitempty"`
	SalePlatform        int32                    `protobuf:"varint,16,opt,name=salePlatform,proto3" json:"salePlatform,omitempty"`
	IosDiscount         int64                    `protobuf:"varint,17,opt,name=iosDiscount,proto3" json:"iosDiscount,omitempty"` // 折扣
	BundleId            int64                    `protobuf:"varint,18,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
	TargetingId         int64                    `protobuf:"varint,19,opt,name=targetingId,proto3" json:"targetingId,omitempty"`
	WeekCycle           *Package_WeekCycle       `protobuf:"bytes,20,opt,name=weekCycle,proto3" json:"weekCycle,omitempty"`
	MMarketInfo         map[string]string        `protobuf:"bytes,21,rep,name=mMarketInfo,proto3" json:"mMarketInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 商品营销属性
	DayCycle            *Package_DayCycle        `protobuf:"bytes,22,opt,name=dayCycle,proto3" json:"dayCycle,omitempty"`
	PricePay            *Package_PriceGreen      `protobuf:"bytes,23,opt,name=pricePay,proto3" json:"pricePay,omitempty"`
	AndroidPayDiscount  int64                    `protobuf:"varint,24,opt,name=androidPayDiscount,proto3" json:"androidPayDiscount,omitempty"` // 折扣
	IosPayDiscount      int64                    `protobuf:"varint,25,opt,name=iosPayDiscount,proto3" json:"iosPayDiscount,omitempty"`         // 折扣
	// music svip {"exclusive_music_svip":true}
	ExtraData string `protobuf:"bytes,26,opt,name=extraData,proto3" json:"extraData,omitempty"` // 额外信息
}

func (x *Package) Reset() {
	*x = Package{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0}
}

func (x *Package) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *Package) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Package) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Package) GetPriceType() common.PackagePriceType {
	if x != nil {
		return x.PriceType
	}
	return common.PackagePriceType(0)
}

func (x *Package) GetPriceKB() int64 {
	if x != nil {
		return x.PriceKB
	}
	return 0
}

func (x *Package) GetPriceGreen() *Package_PriceGreen {
	if x != nil {
		return x.PriceGreen
	}
	return nil
}

func (x *Package) GetTargeting() *Package_Targeting {
	if x != nil {
		return x.Targeting
	}
	return nil
}

func (x *Package) GetVisibility() common.PackageVisibility {
	if x != nil {
		return x.Visibility
	}
	return common.PackageVisibility(0)
}

func (x *Package) GetSceneLobbyInterval() int64 {
	if x != nil {
		return x.SceneLobbyInterval
	}
	return 0
}

func (x *Package) GetItems() []*Package_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Package) GetVisibilityTimeRange() *Package_TimeRange {
	if x != nil {
		return x.VisibilityTimeRange
	}
	return nil
}

func (x *Package) GetVisibilityFrequency() *Package_Frequency {
	if x != nil {
		return x.VisibilityFrequency
	}
	return nil
}

func (x *Package) GetSceneUIs() []*Package_SceneUI {
	if x != nil {
		return x.SceneUIs
	}
	return nil
}

func (x *Package) GetAndroidDiscount() int64 {
	if x != nil {
		return x.AndroidDiscount
	}
	return 0
}

func (x *Package) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Package) GetSalePlatform() int32 {
	if x != nil {
		return x.SalePlatform
	}
	return 0
}

func (x *Package) GetIosDiscount() int64 {
	if x != nil {
		return x.IosDiscount
	}
	return 0
}

func (x *Package) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

func (x *Package) GetTargetingId() int64 {
	if x != nil {
		return x.TargetingId
	}
	return 0
}

func (x *Package) GetWeekCycle() *Package_WeekCycle {
	if x != nil {
		return x.WeekCycle
	}
	return nil
}

func (x *Package) GetMMarketInfo() map[string]string {
	if x != nil {
		return x.MMarketInfo
	}
	return nil
}

func (x *Package) GetDayCycle() *Package_DayCycle {
	if x != nil {
		return x.DayCycle
	}
	return nil
}

func (x *Package) GetPricePay() *Package_PriceGreen {
	if x != nil {
		return x.PricePay
	}
	return nil
}

func (x *Package) GetAndroidPayDiscount() int64 {
	if x != nil {
		return x.AndroidPayDiscount
	}
	return 0
}

func (x *Package) GetIosPayDiscount() int64 {
	if x != nil {
		return x.IosPayDiscount
	}
	return 0
}

func (x *Package) GetExtraData() string {
	if x != nil {
		return x.ExtraData
	}
	return ""
}

type ScenePackages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageIds []int64                  `protobuf:"varint,1,rep,packed,name=packageIds,proto3" json:"packageIds,omitempty"`
	Whitelist  *ScenePackages_Whitelist `protobuf:"bytes,2,opt,name=whitelist,proto3" json:"whitelist,omitempty"`
}

func (x *ScenePackages) Reset() {
	*x = ScenePackages{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScenePackages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScenePackages) ProtoMessage() {}

func (x *ScenePackages) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScenePackages.ProtoReflect.Descriptor instead.
func (*ScenePackages) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{1}
}

func (x *ScenePackages) GetPackageIds() []int64 {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

func (x *ScenePackages) GetWhitelist() *ScenePackages_Whitelist {
	if x != nil {
		return x.Whitelist
	}
	return nil
}

type UserPackageVisibility struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PurchaseTimes          int64           `protobuf:"varint,1,opt,name=purchaseTimes,proto3" json:"purchaseTimes,omitempty"`
	LastPurchaseTime       int64           `protobuf:"varint,2,opt,name=lastPurchaseTime,proto3" json:"lastPurchaseTime,omitempty"`
	LastExposureTime       int64           `protobuf:"varint,3,opt,name=lastExposureTime,proto3" json:"lastExposureTime,omitempty"`
	TransactionIds         []string        `protobuf:"bytes,4,rep,name=transactionIds,proto3" json:"transactionIds,omitempty"`
	SceneLastExposureTimes map[int32]int64 `protobuf:"bytes,5,rep,name=sceneLastExposureTimes,proto3" json:"sceneLastExposureTimes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	WeekCyclePurchaseTimes map[int64]int64 `protobuf:"bytes,6,rep,name=weekCyclePurchaseTimes,proto3" json:"weekCyclePurchaseTimes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // key:每周一零点
	DayCyclePurchaseTimes  map[int64]int64 `protobuf:"bytes,7,rep,name=dayCyclePurchaseTimes,proto3" json:"dayCyclePurchaseTimes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`   // key:每天零点
}

func (x *UserPackageVisibility) Reset() {
	*x = UserPackageVisibility{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserPackageVisibility) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserPackageVisibility) ProtoMessage() {}

func (x *UserPackageVisibility) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserPackageVisibility.ProtoReflect.Descriptor instead.
func (*UserPackageVisibility) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{2}
}

func (x *UserPackageVisibility) GetPurchaseTimes() int64 {
	if x != nil {
		return x.PurchaseTimes
	}
	return 0
}

func (x *UserPackageVisibility) GetLastPurchaseTime() int64 {
	if x != nil {
		return x.LastPurchaseTime
	}
	return 0
}

func (x *UserPackageVisibility) GetLastExposureTime() int64 {
	if x != nil {
		return x.LastExposureTime
	}
	return 0
}

func (x *UserPackageVisibility) GetTransactionIds() []string {
	if x != nil {
		return x.TransactionIds
	}
	return nil
}

func (x *UserPackageVisibility) GetSceneLastExposureTimes() map[int32]int64 {
	if x != nil {
		return x.SceneLastExposureTimes
	}
	return nil
}

func (x *UserPackageVisibility) GetWeekCyclePurchaseTimes() map[int64]int64 {
	if x != nil {
		return x.WeekCyclePurchaseTimes
	}
	return nil
}

func (x *UserPackageVisibility) GetDayCyclePurchaseTimes() map[int64]int64 {
	if x != nil {
		return x.DayCyclePurchaseTimes
	}
	return nil
}

type PayTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId string `protobuf:"bytes,1,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
}

func (x *PayTransaction) Reset() {
	*x = PayTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayTransaction) ProtoMessage() {}

func (x *PayTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayTransaction.ProtoReflect.Descriptor instead.
func (*PayTransaction) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{3}
}

func (x *PayTransaction) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type Bundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleId int64          `protobuf:"varint,1,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
	Items    []*Bundle_Item `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	AppId    string         `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"` // appId
}

func (x *Bundle) Reset() {
	*x = Bundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bundle) ProtoMessage() {}

func (x *Bundle) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bundle.ProtoReflect.Descriptor instead.
func (*Bundle) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{4}
}

func (x *Bundle) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

func (x *Bundle) GetItems() []*Bundle_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Bundle) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type BundleLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	BundleId      int64  `protobuf:"varint,3,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
	BundleNum     int64  `protobuf:"varint,4,opt,name=bundleNum,proto3" json:"bundleNum,omitempty"`
	TransactionId string `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Timestamp     int64  `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *BundleLog) Reset() {
	*x = BundleLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleLog) ProtoMessage() {}

func (x *BundleLog) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleLog.ProtoReflect.Descriptor instead.
func (*BundleLog) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{5}
}

func (x *BundleLog) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BundleLog) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BundleLog) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

func (x *BundleLog) GetBundleNum() int64 {
	if x != nil {
		return x.BundleNum
	}
	return 0
}

func (x *BundleLog) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BundleLog) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type GreenTradeInfoGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Android *common.GreenTradeInfo `protobuf:"bytes,1,opt,name=android,proto3" json:"android,omitempty"`
	Ios     *common.GreenTradeInfo `protobuf:"bytes,2,opt,name=ios,proto3" json:"ios,omitempty"`
}

func (x *GreenTradeInfoGroup) Reset() {
	*x = GreenTradeInfoGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GreenTradeInfoGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GreenTradeInfoGroup) ProtoMessage() {}

func (x *GreenTradeInfoGroup) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GreenTradeInfoGroup.ProtoReflect.Descriptor instead.
func (*GreenTradeInfoGroup) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{6}
}

func (x *GreenTradeInfoGroup) GetAndroid() *common.GreenTradeInfo {
	if x != nil {
		return x.Android
	}
	return nil
}

func (x *GreenTradeInfoGroup) GetIos() *common.GreenTradeInfo {
	if x != nil {
		return x.Ios
	}
	return nil
}

type BundleTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId string `protobuf:"bytes,1,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Timestamp     int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *BundleTransaction) Reset() {
	*x = BundleTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleTransaction) ProtoMessage() {}

func (x *BundleTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleTransaction.ProtoReflect.Descriptor instead.
func (*BundleTransaction) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{7}
}

func (x *BundleTransaction) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BundleTransaction) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type Package_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId     int64                  `protobuf:"varint,1,opt,name=itemId,proto3" json:"itemId,omitempty"`
	Num        int64                  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	Type       common.PackageItemType `protobuf:"varint,3,opt,name=type,proto3,enum=package.common.PackageItemType" json:"type,omitempty"`
	ExternalId int64                  `protobuf:"varint,4,opt,name=externalId,proto3" json:"externalId,omitempty"`
}

func (x *Package_Item) Reset() {
	*x = Package_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_Item) ProtoMessage() {}

func (x *Package_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_Item.ProtoReflect.Descriptor instead.
func (*Package_Item) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Package_Item) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *Package_Item) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Package_Item) GetType() common.PackageItemType {
	if x != nil {
		return x.Type
	}
	return common.PackageItemType(0)
}

func (x *Package_Item) GetExternalId() int64 {
	if x != nil {
		return x.ExternalId
	}
	return 0
}

type Package_ABTest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable          bool     `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	ExperimentGroup []string `protobuf:"bytes,2,rep,name=experimentGroup,proto3" json:"experimentGroup,omitempty"`
	ControlGroup    []string `protobuf:"bytes,3,rep,name=controlGroup,proto3" json:"controlGroup,omitempty"`
	BusinessId      string   `protobuf:"bytes,4,opt,name=businessId,proto3" json:"businessId,omitempty"` // 业务 id
	ChannelId       string   `protobuf:"bytes,5,opt,name=channelId,proto3" json:"channelId,omitempty"`   // 渠道 id
	ModuleId        string   `protobuf:"bytes,6,opt,name=moduleId,proto3" json:"moduleId,omitempty"`     // 模块 id
}

func (x *Package_ABTest) Reset() {
	*x = Package_ABTest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_ABTest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_ABTest) ProtoMessage() {}

func (x *Package_ABTest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_ABTest.ProtoReflect.Descriptor instead.
func (*Package_ABTest) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Package_ABTest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Package_ABTest) GetExperimentGroup() []string {
	if x != nil {
		return x.ExperimentGroup
	}
	return nil
}

func (x *Package_ABTest) GetControlGroup() []string {
	if x != nil {
		return x.ControlGroup
	}
	return nil
}

func (x *Package_ABTest) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *Package_ABTest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *Package_ABTest) GetModuleId() string {
	if x != nil {
		return x.ModuleId
	}
	return ""
}

type Package_Targeting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleExpr  string          `protobuf:"bytes,1,opt,name=ruleExpr,proto3" json:"ruleExpr,omitempty"`
	CacheTime int64           `protobuf:"varint,2,opt,name=cacheTime,proto3" json:"cacheTime,omitempty"` // 秒
	AbTest    *Package_ABTest `protobuf:"bytes,3,opt,name=abTest,proto3" json:"abTest,omitempty"`
	Whitelist []string        `protobuf:"bytes,4,rep,name=whitelist,proto3" json:"whitelist,omitempty"`
}

func (x *Package_Targeting) Reset() {
	*x = Package_Targeting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_Targeting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_Targeting) ProtoMessage() {}

func (x *Package_Targeting) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_Targeting.ProtoReflect.Descriptor instead.
func (*Package_Targeting) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Package_Targeting) GetRuleExpr() string {
	if x != nil {
		return x.RuleExpr
	}
	return ""
}

func (x *Package_Targeting) GetCacheTime() int64 {
	if x != nil {
		return x.CacheTime
	}
	return 0
}

func (x *Package_Targeting) GetAbTest() *Package_ABTest {
	if x != nil {
		return x.AbTest
	}
	return nil
}

func (x *Package_Targeting) GetWhitelist() []string {
	if x != nil {
		return x.Whitelist
	}
	return nil
}

type Package_TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeginTime        int64 `protobuf:"varint,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime          int64 `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	MaxPurchaseTimes int64 `protobuf:"varint,3,opt,name=maxPurchaseTimes,proto3" json:"maxPurchaseTimes,omitempty"` // 最大购买次数
}

func (x *Package_TimeRange) Reset() {
	*x = Package_TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_TimeRange) ProtoMessage() {}

func (x *Package_TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_TimeRange.ProtoReflect.Descriptor instead.
func (*Package_TimeRange) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Package_TimeRange) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *Package_TimeRange) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Package_TimeRange) GetMaxPurchaseTimes() int64 {
	if x != nil {
		return x.MaxPurchaseTimes
	}
	return 0
}

type Package_Frequency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PurchaseInterval int64 `protobuf:"varint,1,opt,name=purchaseInterval,proto3" json:"purchaseInterval,omitempty"` // 可买间隔 秒
	VisibleDuration  int64 `protobuf:"varint,2,opt,name=visibleDuration,proto3" json:"visibleDuration,omitempty"`   // 可见时长 秒
	VisibleInterval  int64 `protobuf:"varint,3,opt,name=visibleInterval,proto3" json:"visibleInterval,omitempty"`   // 可见间隔 秒
}

func (x *Package_Frequency) Reset() {
	*x = Package_Frequency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_Frequency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_Frequency) ProtoMessage() {}

func (x *Package_Frequency) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_Frequency.ProtoReflect.Descriptor instead.
func (*Package_Frequency) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Package_Frequency) GetPurchaseInterval() int64 {
	if x != nil {
		return x.PurchaseInterval
	}
	return 0
}

func (x *Package_Frequency) GetVisibleDuration() int64 {
	if x != nil {
		return x.VisibleDuration
	}
	return 0
}

func (x *Package_Frequency) GetVisibleInterval() int64 {
	if x != nil {
		return x.VisibleInterval
	}
	return 0
}

type Package_PriceGreenMidasGoods struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId     string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId,omitempty"`
	Price         int64  `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	SkuId         string `protobuf:"bytes,3,opt,name=skuId,proto3" json:"skuId,omitempty"`
	AvailableTime string `protobuf:"bytes,4,opt,name=availableTime,proto3" json:"availableTime,omitempty"`
	ActivityId    string `protobuf:"bytes,5,opt,name=activityId,proto3" json:"activityId,omitempty"`
	Extra         string `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"` // 额外信息
}

func (x *Package_PriceGreenMidasGoods) Reset() {
	*x = Package_PriceGreenMidasGoods{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_PriceGreenMidasGoods) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_PriceGreenMidasGoods) ProtoMessage() {}

func (x *Package_PriceGreenMidasGoods) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_PriceGreenMidasGoods.ProtoReflect.Descriptor instead.
func (*Package_PriceGreenMidasGoods) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 5}
}

func (x *Package_PriceGreenMidasGoods) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *Package_PriceGreenMidasGoods) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Package_PriceGreenMidasGoods) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *Package_PriceGreenMidasGoods) GetAvailableTime() string {
	if x != nil {
		return x.AvailableTime
	}
	return ""
}

func (x *Package_PriceGreenMidasGoods) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *Package_PriceGreenMidasGoods) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

type Package_PriceGreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Android *Package_PriceGreenMidasGoods `protobuf:"bytes,1,opt,name=android,proto3" json:"android,omitempty"`
	Ios     *Package_PriceGreenMidasGoods `protobuf:"bytes,2,opt,name=ios,proto3" json:"ios,omitempty"`
	GreenId int64                         `protobuf:"varint,3,opt,name=greenId,proto3" json:"greenId,omitempty"`
}

func (x *Package_PriceGreen) Reset() {
	*x = Package_PriceGreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_PriceGreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_PriceGreen) ProtoMessage() {}

func (x *Package_PriceGreen) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_PriceGreen.ProtoReflect.Descriptor instead.
func (*Package_PriceGreen) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 6}
}

func (x *Package_PriceGreen) GetAndroid() *Package_PriceGreenMidasGoods {
	if x != nil {
		return x.Android
	}
	return nil
}

func (x *Package_PriceGreen) GetIos() *Package_PriceGreenMidasGoods {
	if x != nil {
		return x.Ios
	}
	return nil
}

func (x *Package_PriceGreen) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

type Package_SceneUI struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scene      common.Scene `protobuf:"varint,1,opt,name=scene,proto3,enum=package.common.Scene" json:"scene,omitempty"`
	UiConfigId string       `protobuf:"bytes,2,opt,name=uiConfigId,proto3" json:"uiConfigId,omitempty"`
	BgProperty string       `protobuf:"bytes,3,opt,name=bgProperty,proto3" json:"bgProperty,omitempty"`
}

func (x *Package_SceneUI) Reset() {
	*x = Package_SceneUI{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_SceneUI) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_SceneUI) ProtoMessage() {}

func (x *Package_SceneUI) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_SceneUI.ProtoReflect.Descriptor instead.
func (*Package_SceneUI) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 7}
}

func (x *Package_SceneUI) GetScene() common.Scene {
	if x != nil {
		return x.Scene
	}
	return common.Scene(0)
}

func (x *Package_SceneUI) GetUiConfigId() string {
	if x != nil {
		return x.UiConfigId
	}
	return ""
}

func (x *Package_SceneUI) GetBgProperty() string {
	if x != nil {
		return x.BgProperty
	}
	return ""
}

type Package_WeekCycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeginTime             string `protobuf:"bytes,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"`                          //开始时间，格式为 00:00:00
	EndTime               string `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime,omitempty"`                              //结束时间，格式为 00:00:00
	BeginWeekday          uint32 `protobuf:"varint,3,opt,name=beginWeekday,proto3" json:"beginWeekday,omitempty"`                   //周日:0  周一 ～ 周六：1～6
	EndWeekday            uint32 `protobuf:"varint,4,opt,name=endWeekday,proto3" json:"endWeekday,omitempty"`                       //周日:0  周一 ～ 周六：1～6
	CycleMaxPurchaseTimes int64  `protobuf:"varint,5,opt,name=cycleMaxPurchaseTimes,proto3" json:"cycleMaxPurchaseTimes,omitempty"` // 周期最大购买次数
}

func (x *Package_WeekCycle) Reset() {
	*x = Package_WeekCycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_WeekCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_WeekCycle) ProtoMessage() {}

func (x *Package_WeekCycle) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_WeekCycle.ProtoReflect.Descriptor instead.
func (*Package_WeekCycle) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 8}
}

func (x *Package_WeekCycle) GetBeginTime() string {
	if x != nil {
		return x.BeginTime
	}
	return ""
}

func (x *Package_WeekCycle) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *Package_WeekCycle) GetBeginWeekday() uint32 {
	if x != nil {
		return x.BeginWeekday
	}
	return 0
}

func (x *Package_WeekCycle) GetEndWeekday() uint32 {
	if x != nil {
		return x.EndWeekday
	}
	return 0
}

func (x *Package_WeekCycle) GetCycleMaxPurchaseTimes() int64 {
	if x != nil {
		return x.CycleMaxPurchaseTimes
	}
	return 0
}

type Package_DayCycle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeginTime             string `protobuf:"bytes,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"`                          // 开始时间，格式为 00:00:00
	EndTime               string `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime,omitempty"`                              // 结束时间，格式为 00:00:00
	CycleMaxPurchaseTimes int64  `protobuf:"varint,3,opt,name=cycleMaxPurchaseTimes,proto3" json:"cycleMaxPurchaseTimes,omitempty"` // 周期最大购买次数
}

func (x *Package_DayCycle) Reset() {
	*x = Package_DayCycle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_DayCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_DayCycle) ProtoMessage() {}

func (x *Package_DayCycle) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_DayCycle.ProtoReflect.Descriptor instead.
func (*Package_DayCycle) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{0, 9}
}

func (x *Package_DayCycle) GetBeginTime() string {
	if x != nil {
		return x.BeginTime
	}
	return ""
}

func (x *Package_DayCycle) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *Package_DayCycle) GetCycleMaxPurchaseTimes() int64 {
	if x != nil {
		return x.CycleMaxPurchaseTimes
	}
	return 0
}

type ScenePackages_Whitelist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string  `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"` // 白名单使用
	PackageIds []int64 `protobuf:"varint,2,rep,packed,name=packageIds,proto3" json:"packageIds,omitempty"`
}

func (x *ScenePackages_Whitelist) Reset() {
	*x = ScenePackages_Whitelist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScenePackages_Whitelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScenePackages_Whitelist) ProtoMessage() {}

func (x *ScenePackages_Whitelist) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScenePackages_Whitelist.ProtoReflect.Descriptor instead.
func (*ScenePackages_Whitelist) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ScenePackages_Whitelist) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ScenePackages_Whitelist) GetPackageIds() []int64 {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

type Bundle_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId       int64                  `protobuf:"varint,1,opt,name=itemId,proto3" json:"itemId,omitempty"`
	Num          int64                  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	Type         common.PackageItemType `protobuf:"varint,3,opt,name=type,proto3,enum=package.common.PackageItemType" json:"type,omitempty"`
	ExternalId   int64                  `protobuf:"varint,4,opt,name=externalId,proto3" json:"externalId,omitempty"`
	ExternalType uint32                 `protobuf:"varint,5,opt,name=externalType,proto3" json:"externalType,omitempty"`
	Img          string                 `protobuf:"bytes,6,opt,name=img,proto3" json:"img,omitempty"`
	Name         string                 `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	Desc         string                 `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc,omitempty"` // 描述文案
}

func (x *Bundle_Item) Reset() {
	*x = Bundle_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_storage_storage_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bundle_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bundle_Item) ProtoMessage() {}

func (x *Bundle_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_storage_storage_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bundle_Item.ProtoReflect.Descriptor instead.
func (*Bundle_Item) Descriptor() ([]byte, []int) {
	return file_pb_package_storage_storage_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Bundle_Item) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *Bundle_Item) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Bundle_Item) GetType() common.PackageItemType {
	if x != nil {
		return x.Type
	}
	return common.PackageItemType(0)
}

func (x *Bundle_Item) GetExternalId() int64 {
	if x != nil {
		return x.ExternalId
	}
	return 0
}

func (x *Bundle_Item) GetExternalType() uint32 {
	if x != nil {
		return x.ExternalType
	}
	return 0
}

func (x *Bundle_Item) GetImg() string {
	if x != nil {
		return x.Img
	}
	return ""
}

func (x *Bundle_Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Bundle_Item) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

var File_pb_package_storage_storage_proto protoreflect.FileDescriptor

var file_pb_package_storage_storage_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf0, 0x16, 0x0a, 0x07, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4b, 0x42, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x69, 0x63, 0x65, 0x4b, 0x42, 0x12, 0x43, 0x0a,
	0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x47, 0x72, 0x65,
	0x65, 0x6e, 0x12, 0x40, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x41, 0x0a, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0a, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x4c, 0x6f, 0x62, 0x62, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x12, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x4c, 0x6f, 0x62, 0x62, 0x79, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x33, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x54, 0x0a, 0x13,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x13, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x54, 0x0a, 0x13, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x13, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x3c, 0x0a, 0x08, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x55, 0x49, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x55, 0x49, 0x52, 0x08, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x55, 0x49, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x61, 0x6c, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x73, 0x61, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x20, 0x0a, 0x0b,
	0x69, 0x6f, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x69, 0x6f, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x09,
	0x77, 0x65, 0x65, 0x6b, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x79,
	0x63, 0x6c, 0x65, 0x52, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x4b,
	0x0a, 0x0b, 0x6d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x15, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x4d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b,
	0x6d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x08, 0x64,
	0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x52, 0x08, 0x64, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x50, 0x61, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x47, 0x72, 0x65, 0x65,
	0x6e, 0x52, 0x08, 0x70, 0x72, 0x69, 0x63, 0x65, 0x50, 0x61, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x61,
	0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x50, 0x61, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64,
	0x50, 0x61, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x69,
	0x6f, 0x73, 0x50, 0x61, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x19, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x69, 0x6f, 0x73, 0x50, 0x61, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x61, 0x74,
	0x61, 0x1a, 0x85, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74,
	0x65, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x1a, 0xc8, 0x01, 0x0a, 0x06, 0x41, 0x42,
	0x54, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0f,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x1a, 0x9c, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x45, 0x78, 0x70, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x61, 0x63, 0x68, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x06,
	0x61, 0x62, 0x54, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x06, 0x61,
	0x62, 0x54, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x1a, 0x6f, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x50,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x1a, 0x8b, 0x01, 0x0a, 0x09, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x70, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x28,
	0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69,
	0x62, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x1a, 0xbc, 0x01, 0x0a, 0x14, 0x50, 0x72, 0x69, 0x63, 0x65, 0x47, 0x72, 0x65, 0x65,
	0x6e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x1a, 0xb0, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x63, 0x65, 0x47, 0x72, 0x65, 0x65, 0x6e,
	0x12, 0x47, 0x0a, 0x07, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x52, 0x07, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x03, 0x69, 0x6f, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x69, 0x64, 0x61, 0x73,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x03, 0x69, 0x6f, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72,
	0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x65,
	0x65, 0x6e, 0x49, 0x64, 0x1a, 0x76, 0x0a, 0x07, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x55, 0x49, 0x12,
	0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x75, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x62, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x62, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x1a, 0xbd, 0x01, 0x0a,
	0x09, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x65,
	0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62,
	0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x57, 0x65, 0x65, 0x6b, 0x64,
	0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x57,
	0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x64, 0x57, 0x65, 0x65,
	0x6b, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x57,
	0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4d,
	0x61, 0x78, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x78, 0x50,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0x78, 0x0a, 0x08,
	0x44, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x65, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x34, 0x0a, 0x15, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x78, 0x50, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x15, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x78, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbc, 0x01, 0x0a, 0x0d, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x1a, 0x43, 0x0a, 0x09, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x73, 0x22, 0x8e, 0x06, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x24, 0x0a, 0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x6c, 0x61, 0x73, 0x74, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6c, 0x61, 0x73,
	0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x7a, 0x0a, 0x16, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x4c, 0x61,
	0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x53, 0x63,
	0x65, 0x6e, 0x65, 0x4c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x4c, 0x61, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x12, 0x7a, 0x0a, 0x16, 0x77, 0x65, 0x65, 0x6b, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x42, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x79,
	0x63, 0x6c, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x77, 0x65, 0x65, 0x6b, 0x43, 0x79, 0x63, 0x6c, 0x65,
	0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x77, 0x0a,
	0x15, 0x64, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x2e, 0x44, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x15, 0x64, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x1a, 0x49, 0x0a, 0x1b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x4c,
	0x61, 0x73, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x49, 0x0a, 0x1b, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x48, 0x0a, 0x1a,
	0x44, 0x61, 0x79, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x36, 0x0a, 0x0e, 0x50, 0x61, 0x79, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xd4,
	0x02, 0x0a, 0x06, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x2e, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x1a,
	0xe3, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e,
	0x75, 0x6d, 0x12, 0x33, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x6d, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0xb7, 0x01, 0x0a, 0x09, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65,
	0x4c, 0x6f, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0x81, 0x01, 0x0a, 0x13, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x38, 0x0a, 0x07, 0x61, 0x6e, 0x64, 0x72, 0x6f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69,
	0x64, 0x12, 0x30, 0x0a, 0x03, 0x69, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x47, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03,
	0x69, 0x6f, 0x73, 0x22, 0x57, 0x0a, 0x11, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x47, 0x5a, 0x45,
	0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_package_storage_storage_proto_rawDescOnce sync.Once
	file_pb_package_storage_storage_proto_rawDescData = file_pb_package_storage_storage_proto_rawDesc
)

func file_pb_package_storage_storage_proto_rawDescGZIP() []byte {
	file_pb_package_storage_storage_proto_rawDescOnce.Do(func() {
		file_pb_package_storage_storage_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_package_storage_storage_proto_rawDescData)
	})
	return file_pb_package_storage_storage_proto_rawDescData
}

var file_pb_package_storage_storage_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_pb_package_storage_storage_proto_goTypes = []interface{}{
	(*Package)(nil),                      // 0: package.storage.Package
	(*ScenePackages)(nil),                // 1: package.storage.ScenePackages
	(*UserPackageVisibility)(nil),        // 2: package.storage.UserPackageVisibility
	(*PayTransaction)(nil),               // 3: package.storage.PayTransaction
	(*Bundle)(nil),                       // 4: package.storage.Bundle
	(*BundleLog)(nil),                    // 5: package.storage.BundleLog
	(*GreenTradeInfoGroup)(nil),          // 6: package.storage.GreenTradeInfoGroup
	(*BundleTransaction)(nil),            // 7: package.storage.BundleTransaction
	(*Package_Item)(nil),                 // 8: package.storage.Package.Item
	(*Package_ABTest)(nil),               // 9: package.storage.Package.ABTest
	(*Package_Targeting)(nil),            // 10: package.storage.Package.Targeting
	(*Package_TimeRange)(nil),            // 11: package.storage.Package.TimeRange
	(*Package_Frequency)(nil),            // 12: package.storage.Package.Frequency
	(*Package_PriceGreenMidasGoods)(nil), // 13: package.storage.Package.PriceGreenMidasGoods
	(*Package_PriceGreen)(nil),           // 14: package.storage.Package.PriceGreen
	(*Package_SceneUI)(nil),              // 15: package.storage.Package.SceneUI
	(*Package_WeekCycle)(nil),            // 16: package.storage.Package.WeekCycle
	(*Package_DayCycle)(nil),             // 17: package.storage.Package.DayCycle
	nil,                                  // 18: package.storage.Package.MMarketInfoEntry
	(*ScenePackages_Whitelist)(nil),      // 19: package.storage.ScenePackages.Whitelist
	nil,                                  // 20: package.storage.UserPackageVisibility.SceneLastExposureTimesEntry
	nil,                                  // 21: package.storage.UserPackageVisibility.WeekCyclePurchaseTimesEntry
	nil,                                  // 22: package.storage.UserPackageVisibility.DayCyclePurchaseTimesEntry
	(*Bundle_Item)(nil),                  // 23: package.storage.Bundle.Item
	(common.PackagePriceType)(0),         // 24: package.common.PackagePriceType
	(common.PackageVisibility)(0),        // 25: package.common.PackageVisibility
	(*common.GreenTradeInfo)(nil),        // 26: package.common.GreenTradeInfo
	(common.PackageItemType)(0),          // 27: package.common.PackageItemType
	(common.Scene)(0),                    // 28: package.common.Scene
}
var file_pb_package_storage_storage_proto_depIdxs = []int32{
	24, // 0: package.storage.Package.priceType:type_name -> package.common.PackagePriceType
	14, // 1: package.storage.Package.priceGreen:type_name -> package.storage.Package.PriceGreen
	10, // 2: package.storage.Package.targeting:type_name -> package.storage.Package.Targeting
	25, // 3: package.storage.Package.visibility:type_name -> package.common.PackageVisibility
	8,  // 4: package.storage.Package.items:type_name -> package.storage.Package.Item
	11, // 5: package.storage.Package.visibilityTimeRange:type_name -> package.storage.Package.TimeRange
	12, // 6: package.storage.Package.visibilityFrequency:type_name -> package.storage.Package.Frequency
	15, // 7: package.storage.Package.sceneUIs:type_name -> package.storage.Package.SceneUI
	16, // 8: package.storage.Package.weekCycle:type_name -> package.storage.Package.WeekCycle
	18, // 9: package.storage.Package.mMarketInfo:type_name -> package.storage.Package.MMarketInfoEntry
	17, // 10: package.storage.Package.dayCycle:type_name -> package.storage.Package.DayCycle
	14, // 11: package.storage.Package.pricePay:type_name -> package.storage.Package.PriceGreen
	19, // 12: package.storage.ScenePackages.whitelist:type_name -> package.storage.ScenePackages.Whitelist
	20, // 13: package.storage.UserPackageVisibility.sceneLastExposureTimes:type_name -> package.storage.UserPackageVisibility.SceneLastExposureTimesEntry
	21, // 14: package.storage.UserPackageVisibility.weekCyclePurchaseTimes:type_name -> package.storage.UserPackageVisibility.WeekCyclePurchaseTimesEntry
	22, // 15: package.storage.UserPackageVisibility.dayCyclePurchaseTimes:type_name -> package.storage.UserPackageVisibility.DayCyclePurchaseTimesEntry
	23, // 16: package.storage.Bundle.items:type_name -> package.storage.Bundle.Item
	26, // 17: package.storage.GreenTradeInfoGroup.android:type_name -> package.common.GreenTradeInfo
	26, // 18: package.storage.GreenTradeInfoGroup.ios:type_name -> package.common.GreenTradeInfo
	27, // 19: package.storage.Package.Item.type:type_name -> package.common.PackageItemType
	9,  // 20: package.storage.Package.Targeting.abTest:type_name -> package.storage.Package.ABTest
	13, // 21: package.storage.Package.PriceGreen.android:type_name -> package.storage.Package.PriceGreenMidasGoods
	13, // 22: package.storage.Package.PriceGreen.ios:type_name -> package.storage.Package.PriceGreenMidasGoods
	28, // 23: package.storage.Package.SceneUI.scene:type_name -> package.common.Scene
	27, // 24: package.storage.Bundle.Item.type:type_name -> package.common.PackageItemType
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_pb_package_storage_storage_proto_init() }
func file_pb_package_storage_storage_proto_init() {
	if File_pb_package_storage_storage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_package_storage_storage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScenePackages); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserPackageVisibility); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GreenTradeInfoGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_ABTest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_Targeting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_Frequency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_PriceGreenMidasGoods); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_PriceGreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_SceneUI); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_WeekCycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_DayCycle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScenePackages_Whitelist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_storage_storage_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bundle_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_package_storage_storage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_package_storage_storage_proto_goTypes,
		DependencyIndexes: file_pb_package_storage_storage_proto_depIdxs,
		MessageInfos:      file_pb_package_storage_storage_proto_msgTypes,
	}.Build()
	File_pb_package_storage_storage_proto = out.File
	file_pb_package_storage_storage_proto_rawDesc = nil
	file_pb_package_storage_storage_proto_goTypes = nil
	file_pb_package_storage_storage_proto_depIdxs = nil
}
