// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/package/callback/callback.proto

package package_callback

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	open_pay_extension "kugou_adapter_service/pkg/gen/proto/pb/open_pay_extension"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Callback_CheckOrder_FullMethodName = "/package_callback.Callback/CheckOrder"
	Callback_Delivery_FullMethodName   = "/package_callback.Callback/Delivery"
)

// CallbackClient is the client API for Callback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CallbackClient interface {
	// 校验订单
	CheckOrder(ctx context.Context, in *open_pay_extension.CheckOrderReq, opts ...grpc.CallOption) (*open_pay_extension.CheckOrderRsp, error)
	// 发货
	Delivery(ctx context.Context, in *open_pay_extension.DeliveryReq, opts ...grpc.CallOption) (*open_pay_extension.DeliveryRsp, error)
}

type callbackClient struct {
	cc grpc.ClientConnInterface
}

func NewCallbackClient(cc grpc.ClientConnInterface) CallbackClient {
	return &callbackClient{cc}
}

func (c *callbackClient) CheckOrder(ctx context.Context, in *open_pay_extension.CheckOrderReq, opts ...grpc.CallOption) (*open_pay_extension.CheckOrderRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(open_pay_extension.CheckOrderRsp)
	err := c.cc.Invoke(ctx, Callback_CheckOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *callbackClient) Delivery(ctx context.Context, in *open_pay_extension.DeliveryReq, opts ...grpc.CallOption) (*open_pay_extension.DeliveryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(open_pay_extension.DeliveryRsp)
	err := c.cc.Invoke(ctx, Callback_Delivery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CallbackServer is the server API for Callback service.
// All implementations should embed UnimplementedCallbackServer
// for forward compatibility
type CallbackServer interface {
	// 校验订单
	CheckOrder(context.Context, *open_pay_extension.CheckOrderReq) (*open_pay_extension.CheckOrderRsp, error)
	// 发货
	Delivery(context.Context, *open_pay_extension.DeliveryReq) (*open_pay_extension.DeliveryRsp, error)
}

// UnimplementedCallbackServer should be embedded to have forward compatible implementations.
type UnimplementedCallbackServer struct {
}

func (UnimplementedCallbackServer) CheckOrder(context.Context, *open_pay_extension.CheckOrderReq) (*open_pay_extension.CheckOrderRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckOrder not implemented")
}
func (UnimplementedCallbackServer) Delivery(context.Context, *open_pay_extension.DeliveryReq) (*open_pay_extension.DeliveryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delivery not implemented")
}

// UnsafeCallbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CallbackServer will
// result in compilation errors.
type UnsafeCallbackServer interface {
	mustEmbedUnimplementedCallbackServer()
}

func RegisterCallbackServer(s grpc.ServiceRegistrar, srv CallbackServer) {
	s.RegisterService(&Callback_ServiceDesc, srv)
}

func _Callback_CheckOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(open_pay_extension.CheckOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallbackServer).CheckOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Callback_CheckOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallbackServer).CheckOrder(ctx, req.(*open_pay_extension.CheckOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Callback_Delivery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(open_pay_extension.DeliveryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CallbackServer).Delivery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Callback_Delivery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CallbackServer).Delivery(ctx, req.(*open_pay_extension.DeliveryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Callback_ServiceDesc is the grpc.ServiceDesc for Callback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Callback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "package_callback.Callback",
	HandlerType: (*CallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckOrder",
			Handler:    _Callback_CheckOrder_Handler,
		},
		{
			MethodName: "Delivery",
			Handler:    _Callback_Delivery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/package/callback/callback.proto",
}
