// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/package/api/api.proto

package package_api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Api_GrantBundle_FullMethodName = "/package_api.Api/GrantBundle"
	Api_QueryBundle_FullMethodName = "/package_api.Api/QueryBundle"
)

// ApiClient is the client API for Api service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApiClient interface {
	// 下发道具包
	GrantBundle(ctx context.Context, in *GrantBundleReq, opts ...grpc.CallOption) (*GrantBundleRsp, error)
	// 查询道具包
	QueryBundle(ctx context.Context, in *QueryBundleReq, opts ...grpc.CallOption) (*QueryBundleRsp, error)
}

type apiClient struct {
	cc grpc.ClientConnInterface
}

func NewApiClient(cc grpc.ClientConnInterface) ApiClient {
	return &apiClient{cc}
}

func (c *apiClient) GrantBundle(ctx context.Context, in *GrantBundleReq, opts ...grpc.CallOption) (*GrantBundleRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GrantBundleRsp)
	err := c.cc.Invoke(ctx, Api_GrantBundle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *apiClient) QueryBundle(ctx context.Context, in *QueryBundleReq, opts ...grpc.CallOption) (*QueryBundleRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryBundleRsp)
	err := c.cc.Invoke(ctx, Api_QueryBundle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiServer is the server API for Api service.
// All implementations should embed UnimplementedApiServer
// for forward compatibility
type ApiServer interface {
	// 下发道具包
	GrantBundle(context.Context, *GrantBundleReq) (*GrantBundleRsp, error)
	// 查询道具包
	QueryBundle(context.Context, *QueryBundleReq) (*QueryBundleRsp, error)
}

// UnimplementedApiServer should be embedded to have forward compatible implementations.
type UnimplementedApiServer struct {
}

func (UnimplementedApiServer) GrantBundle(context.Context, *GrantBundleReq) (*GrantBundleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GrantBundle not implemented")
}
func (UnimplementedApiServer) QueryBundle(context.Context, *QueryBundleReq) (*QueryBundleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryBundle not implemented")
}

// UnsafeApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiServer will
// result in compilation errors.
type UnsafeApiServer interface {
	mustEmbedUnimplementedApiServer()
}

func RegisterApiServer(s grpc.ServiceRegistrar, srv ApiServer) {
	s.RegisterService(&Api_ServiceDesc, srv)
}

func _Api_GrantBundle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantBundleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).GrantBundle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_GrantBundle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).GrantBundle(ctx, req.(*GrantBundleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Api_QueryBundle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryBundleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiServer).QueryBundle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Api_QueryBundle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiServer).QueryBundle(ctx, req.(*QueryBundleReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Api_ServiceDesc is the grpc.ServiceDesc for Api service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Api_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "package_api.Api",
	HandlerType: (*ApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GrantBundle",
			Handler:    _Api_GrantBundle_Handler,
		},
		{
			MethodName: "QueryBundle",
			Handler:    _Api_QueryBundle_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/package/api/api.proto",
}
