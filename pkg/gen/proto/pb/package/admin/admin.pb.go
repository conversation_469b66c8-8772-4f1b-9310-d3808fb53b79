// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/package/admin/admin.proto

package package_sync

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId int64 `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
}

func (x *SyncPackageReq) Reset() {
	*x = SyncPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPackageReq) ProtoMessage() {}

func (x *SyncPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPackageReq.ProtoReflect.Descriptor instead.
func (*SyncPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{0}
}

func (x *SyncPackageReq) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

type SyncPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SyncPackageRsp) Reset() {
	*x = SyncPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPackageRsp) ProtoMessage() {}

func (x *SyncPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPackageRsp.ProtoReflect.Descriptor instead.
func (*SyncPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{1}
}

func (x *SyncPackageRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncPackageRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SyncScenePackagesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SceneId int64 `protobuf:"varint,1,opt,name=sceneId,proto3" json:"sceneId,omitempty"`
}

func (x *SyncScenePackagesReq) Reset() {
	*x = SyncScenePackagesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncScenePackagesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncScenePackagesReq) ProtoMessage() {}

func (x *SyncScenePackagesReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncScenePackagesReq.ProtoReflect.Descriptor instead.
func (*SyncScenePackagesReq) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{2}
}

func (x *SyncScenePackagesReq) GetSceneId() int64 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

type SyncScenePackagesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SyncScenePackagesRsp) Reset() {
	*x = SyncScenePackagesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncScenePackagesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncScenePackagesRsp) ProtoMessage() {}

func (x *SyncScenePackagesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncScenePackagesRsp.ProtoReflect.Descriptor instead.
func (*SyncScenePackagesRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{3}
}

func (x *SyncScenePackagesRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncScenePackagesRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SyncBundleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleId int64 `protobuf:"varint,1,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
}

func (x *SyncBundleReq) Reset() {
	*x = SyncBundleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncBundleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncBundleReq) ProtoMessage() {}

func (x *SyncBundleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncBundleReq.ProtoReflect.Descriptor instead.
func (*SyncBundleReq) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{4}
}

func (x *SyncBundleReq) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

type SyncBundleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SyncBundleRsp) Reset() {
	*x = SyncBundleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncBundleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncBundleRsp) ProtoMessage() {}

func (x *SyncBundleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncBundleRsp.ProtoReflect.Descriptor instead.
func (*SyncBundleRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{5}
}

func (x *SyncBundleRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncBundleRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SyncGreenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GreenId int32 `protobuf:"varint,1,opt,name=greenId,proto3" json:"greenId,omitempty"`
}

func (x *SyncGreenReq) Reset() {
	*x = SyncGreenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncGreenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncGreenReq) ProtoMessage() {}

func (x *SyncGreenReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncGreenReq.ProtoReflect.Descriptor instead.
func (*SyncGreenReq) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{6}
}

func (x *SyncGreenReq) GetGreenId() int32 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

type SyncGreenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SyncGreenRsp) Reset() {
	*x = SyncGreenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_admin_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncGreenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncGreenRsp) ProtoMessage() {}

func (x *SyncGreenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_admin_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncGreenRsp.ProtoReflect.Descriptor instead.
func (*SyncGreenRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_admin_admin_proto_rawDescGZIP(), []int{7}
}

func (x *SyncGreenRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncGreenRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_pb_package_admin_admin_proto protoreflect.FileDescriptor

var file_pb_package_admin_admin_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x22, 0x2e, 0x0a, 0x0e,
	0x53, 0x79, 0x6e, 0x63, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x3e, 0x0a, 0x0e,
	0x53, 0x79, 0x6e, 0x63, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x30, 0x0a, 0x14,
	0x53, 0x79, 0x6e, 0x63, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x22, 0x44,
	0x0a, 0x14, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x2b, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0x3d, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0x28, 0x0a, 0x0c, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0c, 0x53, 0x79,
	0x6e, 0x63, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xbb, 0x02, 0x0a, 0x04, 0x53, 0x79, 0x6e,
	0x63, 0x12, 0x49, 0x0a, 0x0b, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x12, 0x1c, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e,
	0x53, 0x79, 0x6e, 0x63, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x11,
	0x53, 0x79, 0x6e, 0x63, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x0a, 0x53, 0x79, 0x6e,
	0x63, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x43, 0x0a, 0x09, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x1a,
	0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x72,
	0x65, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x42, 0x44, 0x5a, 0x42, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_package_admin_admin_proto_rawDescOnce sync.Once
	file_pb_package_admin_admin_proto_rawDescData = file_pb_package_admin_admin_proto_rawDesc
)

func file_pb_package_admin_admin_proto_rawDescGZIP() []byte {
	file_pb_package_admin_admin_proto_rawDescOnce.Do(func() {
		file_pb_package_admin_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_package_admin_admin_proto_rawDescData)
	})
	return file_pb_package_admin_admin_proto_rawDescData
}

var file_pb_package_admin_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_package_admin_admin_proto_goTypes = []interface{}{
	(*SyncPackageReq)(nil),       // 0: package_sync.SyncPackageReq
	(*SyncPackageRsp)(nil),       // 1: package_sync.SyncPackageRsp
	(*SyncScenePackagesReq)(nil), // 2: package_sync.SyncScenePackagesReq
	(*SyncScenePackagesRsp)(nil), // 3: package_sync.SyncScenePackagesRsp
	(*SyncBundleReq)(nil),        // 4: package_sync.SyncBundleReq
	(*SyncBundleRsp)(nil),        // 5: package_sync.SyncBundleRsp
	(*SyncGreenReq)(nil),         // 6: package_sync.SyncGreenReq
	(*SyncGreenRsp)(nil),         // 7: package_sync.SyncGreenRsp
}
var file_pb_package_admin_admin_proto_depIdxs = []int32{
	0, // 0: package_sync.Sync.SyncPackage:input_type -> package_sync.SyncPackageReq
	2, // 1: package_sync.Sync.SyncScenePackages:input_type -> package_sync.SyncScenePackagesReq
	4, // 2: package_sync.Sync.SyncBundle:input_type -> package_sync.SyncBundleReq
	6, // 3: package_sync.Sync.SyncGreen:input_type -> package_sync.SyncGreenReq
	1, // 4: package_sync.Sync.SyncPackage:output_type -> package_sync.SyncPackageRsp
	3, // 5: package_sync.Sync.SyncScenePackages:output_type -> package_sync.SyncScenePackagesRsp
	5, // 6: package_sync.Sync.SyncBundle:output_type -> package_sync.SyncBundleRsp
	7, // 7: package_sync.Sync.SyncGreen:output_type -> package_sync.SyncGreenRsp
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_package_admin_admin_proto_init() }
func file_pb_package_admin_admin_proto_init() {
	if File_pb_package_admin_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_package_admin_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncScenePackagesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncScenePackagesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncBundleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncBundleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncGreenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_admin_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncGreenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_package_admin_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_package_admin_admin_proto_goTypes,
		DependencyIndexes: file_pb_package_admin_admin_proto_depIdxs,
		MessageInfos:      file_pb_package_admin_admin_proto_msgTypes,
	}.Build()
	File_pb_package_admin_admin_proto = out.File
	file_pb_package_admin_admin_proto_rawDesc = nil
	file_pb_package_admin_admin_proto_goTypes = nil
	file_pb_package_admin_admin_proto_depIdxs = nil
}
