// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/package/sync/sync.proto

package package_sync

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Sync_SyncPackage_FullMethodName       = "/package_sync.Sync/SyncPackage"
	Sync_SyncScenePackages_FullMethodName = "/package_sync.Sync/SyncScenePackages"
	Sync_SyncBundle_FullMethodName        = "/package_sync.Sync/SyncBundle"
	Sync_SyncGreen_FullMethodName         = "/package_sync.Sync/SyncGreen"
	Sync_SyncTargeting_FullMethodName     = "/package_sync.Sync/SyncTargeting"
)

// SyncClient is the client API for Sync service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SyncClient interface {
	// 同步单个礼包
	SyncPackage(ctx context.Context, in *SyncPackageReq, opts ...grpc.CallOption) (*SyncPackageRsp, error)
	// 同步场景礼包列表
	SyncScenePackages(ctx context.Context, in *SyncScenePackagesReq, opts ...grpc.CallOption) (*SyncScenePackagesRsp, error)
	// 同步道具包
	SyncBundle(ctx context.Context, in *SyncBundleReq, opts ...grpc.CallOption) (*SyncBundleRsp, error)
	// 同步绿钻
	SyncGreen(ctx context.Context, in *SyncGreenReq, opts ...grpc.CallOption) (*SyncGreenRsp, error)
	// 同步定向
	SyncTargeting(ctx context.Context, in *SyncTargetingReq, opts ...grpc.CallOption) (*SyncTargetingRsp, error)
}

type syncClient struct {
	cc grpc.ClientConnInterface
}

func NewSyncClient(cc grpc.ClientConnInterface) SyncClient {
	return &syncClient{cc}
}

func (c *syncClient) SyncPackage(ctx context.Context, in *SyncPackageReq, opts ...grpc.CallOption) (*SyncPackageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncPackageRsp)
	err := c.cc.Invoke(ctx, Sync_SyncPackage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncClient) SyncScenePackages(ctx context.Context, in *SyncScenePackagesReq, opts ...grpc.CallOption) (*SyncScenePackagesRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncScenePackagesRsp)
	err := c.cc.Invoke(ctx, Sync_SyncScenePackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncClient) SyncBundle(ctx context.Context, in *SyncBundleReq, opts ...grpc.CallOption) (*SyncBundleRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncBundleRsp)
	err := c.cc.Invoke(ctx, Sync_SyncBundle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncClient) SyncGreen(ctx context.Context, in *SyncGreenReq, opts ...grpc.CallOption) (*SyncGreenRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncGreenRsp)
	err := c.cc.Invoke(ctx, Sync_SyncGreen_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncClient) SyncTargeting(ctx context.Context, in *SyncTargetingReq, opts ...grpc.CallOption) (*SyncTargetingRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncTargetingRsp)
	err := c.cc.Invoke(ctx, Sync_SyncTargeting_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SyncServer is the server API for Sync service.
// All implementations should embed UnimplementedSyncServer
// for forward compatibility
type SyncServer interface {
	// 同步单个礼包
	SyncPackage(context.Context, *SyncPackageReq) (*SyncPackageRsp, error)
	// 同步场景礼包列表
	SyncScenePackages(context.Context, *SyncScenePackagesReq) (*SyncScenePackagesRsp, error)
	// 同步道具包
	SyncBundle(context.Context, *SyncBundleReq) (*SyncBundleRsp, error)
	// 同步绿钻
	SyncGreen(context.Context, *SyncGreenReq) (*SyncGreenRsp, error)
	// 同步定向
	SyncTargeting(context.Context, *SyncTargetingReq) (*SyncTargetingRsp, error)
}

// UnimplementedSyncServer should be embedded to have forward compatible implementations.
type UnimplementedSyncServer struct {
}

func (UnimplementedSyncServer) SyncPackage(context.Context, *SyncPackageReq) (*SyncPackageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncPackage not implemented")
}
func (UnimplementedSyncServer) SyncScenePackages(context.Context, *SyncScenePackagesReq) (*SyncScenePackagesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncScenePackages not implemented")
}
func (UnimplementedSyncServer) SyncBundle(context.Context, *SyncBundleReq) (*SyncBundleRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncBundle not implemented")
}
func (UnimplementedSyncServer) SyncGreen(context.Context, *SyncGreenReq) (*SyncGreenRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncGreen not implemented")
}
func (UnimplementedSyncServer) SyncTargeting(context.Context, *SyncTargetingReq) (*SyncTargetingRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncTargeting not implemented")
}

// UnsafeSyncServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SyncServer will
// result in compilation errors.
type UnsafeSyncServer interface {
	mustEmbedUnimplementedSyncServer()
}

func RegisterSyncServer(s grpc.ServiceRegistrar, srv SyncServer) {
	s.RegisterService(&Sync_ServiceDesc, srv)
}

func _Sync_SyncPackage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncPackageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServer).SyncPackage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sync_SyncPackage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServer).SyncPackage(ctx, req.(*SyncPackageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sync_SyncScenePackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncScenePackagesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServer).SyncScenePackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sync_SyncScenePackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServer).SyncScenePackages(ctx, req.(*SyncScenePackagesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sync_SyncBundle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncBundleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServer).SyncBundle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sync_SyncBundle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServer).SyncBundle(ctx, req.(*SyncBundleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sync_SyncGreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncGreenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServer).SyncGreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sync_SyncGreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServer).SyncGreen(ctx, req.(*SyncGreenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sync_SyncTargeting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncTargetingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncServer).SyncTargeting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sync_SyncTargeting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncServer).SyncTargeting(ctx, req.(*SyncTargetingReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Sync_ServiceDesc is the grpc.ServiceDesc for Sync service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Sync_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "package_sync.Sync",
	HandlerType: (*SyncServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncPackage",
			Handler:    _Sync_SyncPackage_Handler,
		},
		{
			MethodName: "SyncScenePackages",
			Handler:    _Sync_SyncScenePackages_Handler,
		},
		{
			MethodName: "SyncBundle",
			Handler:    _Sync_SyncBundle_Handler,
		},
		{
			MethodName: "SyncGreen",
			Handler:    _Sync_SyncGreen_Handler,
		},
		{
			MethodName: "SyncTargeting",
			Handler:    _Sync_SyncTargeting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/package/sync/sync.proto",
}
