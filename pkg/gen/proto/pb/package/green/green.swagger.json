{"swagger": "2.0", "info": {"title": "pb/package/green/green.proto", "version": "version not set"}, "tags": [{"name": "Green"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/package_green.Green/CommonOrderEvent": {"post": {"summary": "发货", "operationId": "Green_CommonOrderEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/package_greenCommonOrderEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/package_greenCommonOrderEventReq"}}], "tags": ["Green"]}}, "/package_green.Green/GetTradeInfo": {"post": {"summary": "查询信息", "operationId": "Green_GetTradeInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/package_greenGetTradeInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/package_greenGetTradeInfoReq"}}], "tags": ["Green"]}}, "/package_green.Green/PreCheckOrder": {"post": {"summary": "校验订单", "operationId": "Green_PreCheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/package_greenPreCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/package_greenPreCheckOrderReq"}}], "tags": ["Green"]}}}, "definitions": {"GetTradeInfoRspItem": {"type": "object", "properties": {"android": {"$ref": "#/definitions/commonGreenTradeInfo"}, "ios": {"$ref": "#/definitions/commonGreenTradeInfo"}}}, "commonGreenTradeInfo": {"type": "object", "properties": {"productId": {"type": "string"}, "price": {"type": "string", "format": "int64"}, "skuId": {"type": "string"}, "availableTime": {"type": "string"}, "activityId": {"type": "string"}, "payProductId": {"type": "string"}, "extra": {"type": "string", "title": "额外信息"}}}, "package_greenCommonOrderEventReq": {"type": "object", "properties": {"packageId": {"type": "string", "format": "int64"}, "transactionId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "timestamp": {"type": "string", "format": "int64"}, "price": {"type": "string", "format": "int64"}, "greenId": {"type": "string", "format": "int64"}, "businessId": {"type": "string", "format": "int64"}, "goodsId": {"type": "string"}, "productId": {"type": "string"}, "skuId": {"type": "string"}, "appId": {"type": "string"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "package_greenCommonOrderEventRsp": {"type": "object"}, "package_greenGetTradeInfoReq": {"type": "object", "properties": {"greenIds": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "package_greenGetTradeInfoRsp": {"type": "object", "properties": {"items": {"type": "object", "additionalProperties": {"$ref": "#/definitions/GetTradeInfoRspItem"}}}}, "package_greenPreCheckOrderReq": {"type": "object", "properties": {"packageId": {"type": "string", "format": "int64"}, "productId": {"type": "string"}, "skuId": {"type": "string"}, "price": {"type": "string", "format": "int64"}, "uid": {"type": "string", "format": "uint64"}, "greenId": {"type": "string", "format": "int64"}, "businessId": {"type": "string", "format": "int64"}, "goodsId": {"type": "string"}, "appId": {"type": "string"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "package_greenPreCheckOrderRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}