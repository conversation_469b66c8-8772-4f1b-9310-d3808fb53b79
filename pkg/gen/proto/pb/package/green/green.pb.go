// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/package/green/green.proto

package package_green

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/package/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PreCheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId  int64             `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
	ProductId  string            `protobuf:"bytes,2,opt,name=productId,proto3" json:"productId,omitempty"`
	SkuId      string            `protobuf:"bytes,3,opt,name=skuId,proto3" json:"skuId,omitempty"`
	Price      int64             `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Uid        uint64            `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	GreenId    int64             `protobuf:"varint,6,opt,name=greenId,proto3" json:"greenId,omitempty"`
	BusinessId int64             `protobuf:"varint,7,opt,name=businessId,proto3" json:"businessId,omitempty"`
	GoodsId    string            `protobuf:"bytes,8,opt,name=goodsId,proto3" json:"goodsId,omitempty"`
	AppId      string            `protobuf:"bytes,9,opt,name=appId,proto3" json:"appId,omitempty"`
	MapExt     map[string]string `protobuf:"bytes,10,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PreCheckOrderReq) Reset() {
	*x = PreCheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCheckOrderReq) ProtoMessage() {}

func (x *PreCheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCheckOrderReq.ProtoReflect.Descriptor instead.
func (*PreCheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{0}
}

func (x *PreCheckOrderReq) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *PreCheckOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *PreCheckOrderReq) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *PreCheckOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *PreCheckOrderReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PreCheckOrderReq) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

func (x *PreCheckOrderReq) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreCheckOrderReq) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *PreCheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PreCheckOrderReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type PreCheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PreCheckOrderRsp) Reset() {
	*x = PreCheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCheckOrderRsp) ProtoMessage() {}

func (x *PreCheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCheckOrderRsp.ProtoReflect.Descriptor instead.
func (*PreCheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{1}
}

type CommonOrderEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId     int64             `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
	TransactionId string            `protobuf:"bytes,2,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Uid           uint64            `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Timestamp     int64             `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Price         int64             `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`
	GreenId       int64             `protobuf:"varint,6,opt,name=greenId,proto3" json:"greenId,omitempty"`
	BusinessId    int64             `protobuf:"varint,7,opt,name=businessId,proto3" json:"businessId,omitempty"`
	GoodsId       string            `protobuf:"bytes,8,opt,name=goodsId,proto3" json:"goodsId,omitempty"`
	ProductId     string            `protobuf:"bytes,9,opt,name=productId,proto3" json:"productId,omitempty"`
	SkuId         string            `protobuf:"bytes,10,opt,name=skuId,proto3" json:"skuId,omitempty"`
	AppId         string            `protobuf:"bytes,11,opt,name=appId,proto3" json:"appId,omitempty"`
	MapExt        map[string]string `protobuf:"bytes,12,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CommonOrderEventReq) Reset() {
	*x = CommonOrderEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonOrderEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonOrderEventReq) ProtoMessage() {}

func (x *CommonOrderEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonOrderEventReq.ProtoReflect.Descriptor instead.
func (*CommonOrderEventReq) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{2}
}

func (x *CommonOrderEventReq) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *CommonOrderEventReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *CommonOrderEventReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CommonOrderEventReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *CommonOrderEventReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CommonOrderEventReq) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

func (x *CommonOrderEventReq) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CommonOrderEventReq) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *CommonOrderEventReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CommonOrderEventReq) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *CommonOrderEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CommonOrderEventReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type CommonOrderEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonOrderEventRsp) Reset() {
	*x = CommonOrderEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonOrderEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonOrderEventRsp) ProtoMessage() {}

func (x *CommonOrderEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonOrderEventRsp.ProtoReflect.Descriptor instead.
func (*CommonOrderEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{3}
}

type GetTradeInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GreenIds []int64 `protobuf:"varint,1,rep,packed,name=greenIds,proto3" json:"greenIds,omitempty"`
}

func (x *GetTradeInfoReq) Reset() {
	*x = GetTradeInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTradeInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTradeInfoReq) ProtoMessage() {}

func (x *GetTradeInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTradeInfoReq.ProtoReflect.Descriptor instead.
func (*GetTradeInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{4}
}

func (x *GetTradeInfoReq) GetGreenIds() []int64 {
	if x != nil {
		return x.GreenIds
	}
	return nil
}

type GetTradeInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items map[int64]*GetTradeInfoRsp_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetTradeInfoRsp) Reset() {
	*x = GetTradeInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTradeInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTradeInfoRsp) ProtoMessage() {}

func (x *GetTradeInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTradeInfoRsp.ProtoReflect.Descriptor instead.
func (*GetTradeInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{5}
}

func (x *GetTradeInfoRsp) GetItems() map[int64]*GetTradeInfoRsp_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetTradeInfoRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Android *common.GreenTradeInfo `protobuf:"bytes,1,opt,name=android,proto3" json:"android,omitempty"`
	Ios     *common.GreenTradeInfo `protobuf:"bytes,2,opt,name=ios,proto3" json:"ios,omitempty"`
}

func (x *GetTradeInfoRsp_Item) Reset() {
	*x = GetTradeInfoRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_green_green_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTradeInfoRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTradeInfoRsp_Item) ProtoMessage() {}

func (x *GetTradeInfoRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_green_green_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTradeInfoRsp_Item.ProtoReflect.Descriptor instead.
func (*GetTradeInfoRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_package_green_green_proto_rawDescGZIP(), []int{5, 0}
}

func (x *GetTradeInfoRsp_Item) GetAndroid() *common.GreenTradeInfo {
	if x != nil {
		return x.Android
	}
	return nil
}

func (x *GetTradeInfoRsp_Item) GetIos() *common.GreenTradeInfo {
	if x != nil {
		return x.Ios
	}
	return nil
}

var File_pb_package_green_green_proto protoreflect.FileDescriptor

var file_pb_package_green_green_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x67, 0x72, 0x65,
	0x65, 0x6e, 0x2f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x1a, 0x1e, 0x70,
	0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf6, 0x02,
	0x0a, 0x10, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x6b, 0x75, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67,
	0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x12, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0xc0, 0x03, 0x0a, 0x13, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67,
	0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x6b, 0x75, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x06, 0x6d, 0x61,
	0x70, 0x45, 0x78, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x4d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x15, 0x0a,
	0x13, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x22, 0x2d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72, 0x65, 0x65, 0x6e,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x67, 0x72, 0x65, 0x65, 0x6e,
	0x49, 0x64, 0x73, 0x22, 0xa5, 0x02, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x72, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x38, 0x0a, 0x07, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x03, 0x69, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x69, 0x6f, 0x73, 0x1a, 0x5d, 0x0a, 0x0a,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x86, 0x02, 0x0a, 0x05,
	0x47, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x51, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x5a, 0x0a, 0x10, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x2e, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67,
	0x72, 0x65, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67,
	0x72, 0x65, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x42, 0x45, 0x5a, 0x43, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_package_green_green_proto_rawDescOnce sync.Once
	file_pb_package_green_green_proto_rawDescData = file_pb_package_green_green_proto_rawDesc
)

func file_pb_package_green_green_proto_rawDescGZIP() []byte {
	file_pb_package_green_green_proto_rawDescOnce.Do(func() {
		file_pb_package_green_green_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_package_green_green_proto_rawDescData)
	})
	return file_pb_package_green_green_proto_rawDescData
}

var file_pb_package_green_green_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_package_green_green_proto_goTypes = []interface{}{
	(*PreCheckOrderReq)(nil),      // 0: package_green.PreCheckOrderReq
	(*PreCheckOrderRsp)(nil),      // 1: package_green.PreCheckOrderRsp
	(*CommonOrderEventReq)(nil),   // 2: package_green.CommonOrderEventReq
	(*CommonOrderEventRsp)(nil),   // 3: package_green.CommonOrderEventRsp
	(*GetTradeInfoReq)(nil),       // 4: package_green.GetTradeInfoReq
	(*GetTradeInfoRsp)(nil),       // 5: package_green.GetTradeInfoRsp
	nil,                           // 6: package_green.PreCheckOrderReq.MapExtEntry
	nil,                           // 7: package_green.CommonOrderEventReq.MapExtEntry
	(*GetTradeInfoRsp_Item)(nil),  // 8: package_green.GetTradeInfoRsp.Item
	nil,                           // 9: package_green.GetTradeInfoRsp.ItemsEntry
	(*common.GreenTradeInfo)(nil), // 10: package.common.GreenTradeInfo
}
var file_pb_package_green_green_proto_depIdxs = []int32{
	6,  // 0: package_green.PreCheckOrderReq.mapExt:type_name -> package_green.PreCheckOrderReq.MapExtEntry
	7,  // 1: package_green.CommonOrderEventReq.mapExt:type_name -> package_green.CommonOrderEventReq.MapExtEntry
	9,  // 2: package_green.GetTradeInfoRsp.items:type_name -> package_green.GetTradeInfoRsp.ItemsEntry
	10, // 3: package_green.GetTradeInfoRsp.Item.android:type_name -> package.common.GreenTradeInfo
	10, // 4: package_green.GetTradeInfoRsp.Item.ios:type_name -> package.common.GreenTradeInfo
	8,  // 5: package_green.GetTradeInfoRsp.ItemsEntry.value:type_name -> package_green.GetTradeInfoRsp.Item
	0,  // 6: package_green.Green.PreCheckOrder:input_type -> package_green.PreCheckOrderReq
	2,  // 7: package_green.Green.CommonOrderEvent:input_type -> package_green.CommonOrderEventReq
	4,  // 8: package_green.Green.GetTradeInfo:input_type -> package_green.GetTradeInfoReq
	1,  // 9: package_green.Green.PreCheckOrder:output_type -> package_green.PreCheckOrderRsp
	3,  // 10: package_green.Green.CommonOrderEvent:output_type -> package_green.CommonOrderEventRsp
	5,  // 11: package_green.Green.GetTradeInfo:output_type -> package_green.GetTradeInfoRsp
	9,  // [9:12] is the sub-list for method output_type
	6,  // [6:9] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_pb_package_green_green_proto_init() }
func file_pb_package_green_green_proto_init() {
	if File_pb_package_green_green_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_package_green_green_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreCheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_green_green_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreCheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_green_green_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonOrderEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_green_green_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonOrderEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_green_green_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTradeInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_green_green_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTradeInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_green_green_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTradeInfoRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_package_green_green_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_package_green_green_proto_goTypes,
		DependencyIndexes: file_pb_package_green_green_proto_depIdxs,
		MessageInfos:      file_pb_package_green_green_proto_msgTypes,
	}.Build()
	File_pb_package_green_green_proto = out.File
	file_pb_package_green_green_proto_rawDesc = nil
	file_pb_package_green_green_proto_goTypes = nil
	file_pb_package_green_green_proto_depIdxs = nil
}
