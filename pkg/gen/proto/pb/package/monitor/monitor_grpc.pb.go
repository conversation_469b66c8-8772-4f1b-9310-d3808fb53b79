// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/package/monitor/monitor.proto

package package_monitor

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Monitor_Produce_FullMethodName     = "/package_monitor.Monitor/Produce"
	Monitor_Consume_FullMethodName     = "/package_monitor.Monitor/Consume"
	Monitor_Alarm_FullMethodName       = "/package_monitor.Monitor/Alarm"
	Monitor_AlarmQuery_FullMethodName  = "/package_monitor.Monitor/AlarmQuery"
	Monitor_SyncConfig_FullMethodName  = "/package_monitor.Monitor/SyncConfig"
	Monitor_QueryConfig_FullMethodName = "/package_monitor.Monitor/QueryConfig"
)

// MonitorClient is the client API for Monitor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MonitorClient interface {
	// produce
	Produce(ctx context.Context, in *ProduceReq, opts ...grpc.CallOption) (*ProduceRsp, error)
	// consume
	Consume(ctx context.Context, in *ConsumeReq, opts ...grpc.CallOption) (*ConsumeRsp, error)
	// alarm
	Alarm(ctx context.Context, in *AlarmReq, opts ...grpc.CallOption) (*AlarmRsp, error)
	AlarmQuery(ctx context.Context, in *AlarmQueryReq, opts ...grpc.CallOption) (*AlarmQueryRsp, error)
	// sync config
	SyncConfig(ctx context.Context, in *SyncConfigReq, opts ...grpc.CallOption) (*SyncConfigRsp, error)
	QueryConfig(ctx context.Context, in *QueryConfigReq, opts ...grpc.CallOption) (*QueryConfigRsp, error)
}

type monitorClient struct {
	cc grpc.ClientConnInterface
}

func NewMonitorClient(cc grpc.ClientConnInterface) MonitorClient {
	return &monitorClient{cc}
}

func (c *monitorClient) Produce(ctx context.Context, in *ProduceReq, opts ...grpc.CallOption) (*ProduceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProduceRsp)
	err := c.cc.Invoke(ctx, Monitor_Produce_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) Consume(ctx context.Context, in *ConsumeReq, opts ...grpc.CallOption) (*ConsumeRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConsumeRsp)
	err := c.cc.Invoke(ctx, Monitor_Consume_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) Alarm(ctx context.Context, in *AlarmReq, opts ...grpc.CallOption) (*AlarmRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AlarmRsp)
	err := c.cc.Invoke(ctx, Monitor_Alarm_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) AlarmQuery(ctx context.Context, in *AlarmQueryReq, opts ...grpc.CallOption) (*AlarmQueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AlarmQueryRsp)
	err := c.cc.Invoke(ctx, Monitor_AlarmQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) SyncConfig(ctx context.Context, in *SyncConfigReq, opts ...grpc.CallOption) (*SyncConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncConfigRsp)
	err := c.cc.Invoke(ctx, Monitor_SyncConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *monitorClient) QueryConfig(ctx context.Context, in *QueryConfigReq, opts ...grpc.CallOption) (*QueryConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryConfigRsp)
	err := c.cc.Invoke(ctx, Monitor_QueryConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MonitorServer is the server API for Monitor service.
// All implementations should embed UnimplementedMonitorServer
// for forward compatibility
type MonitorServer interface {
	// produce
	Produce(context.Context, *ProduceReq) (*ProduceRsp, error)
	// consume
	Consume(context.Context, *ConsumeReq) (*ConsumeRsp, error)
	// alarm
	Alarm(context.Context, *AlarmReq) (*AlarmRsp, error)
	AlarmQuery(context.Context, *AlarmQueryReq) (*AlarmQueryRsp, error)
	// sync config
	SyncConfig(context.Context, *SyncConfigReq) (*SyncConfigRsp, error)
	QueryConfig(context.Context, *QueryConfigReq) (*QueryConfigRsp, error)
}

// UnimplementedMonitorServer should be embedded to have forward compatible implementations.
type UnimplementedMonitorServer struct {
}

func (UnimplementedMonitorServer) Produce(context.Context, *ProduceReq) (*ProduceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Produce not implemented")
}
func (UnimplementedMonitorServer) Consume(context.Context, *ConsumeReq) (*ConsumeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Consume not implemented")
}
func (UnimplementedMonitorServer) Alarm(context.Context, *AlarmReq) (*AlarmRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Alarm not implemented")
}
func (UnimplementedMonitorServer) AlarmQuery(context.Context, *AlarmQueryReq) (*AlarmQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AlarmQuery not implemented")
}
func (UnimplementedMonitorServer) SyncConfig(context.Context, *SyncConfigReq) (*SyncConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncConfig not implemented")
}
func (UnimplementedMonitorServer) QueryConfig(context.Context, *QueryConfigReq) (*QueryConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryConfig not implemented")
}

// UnsafeMonitorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MonitorServer will
// result in compilation errors.
type UnsafeMonitorServer interface {
	mustEmbedUnimplementedMonitorServer()
}

func RegisterMonitorServer(s grpc.ServiceRegistrar, srv MonitorServer) {
	s.RegisterService(&Monitor_ServiceDesc, srv)
}

func _Monitor_Produce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProduceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).Produce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_Produce_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).Produce(ctx, req.(*ProduceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_Consume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).Consume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_Consume_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).Consume(ctx, req.(*ConsumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_Alarm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlarmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).Alarm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_Alarm_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).Alarm(ctx, req.(*AlarmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_AlarmQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlarmQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).AlarmQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_AlarmQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).AlarmQuery(ctx, req.(*AlarmQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_SyncConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).SyncConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_SyncConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).SyncConfig(ctx, req.(*SyncConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Monitor_QueryConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MonitorServer).QueryConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Monitor_QueryConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MonitorServer).QueryConfig(ctx, req.(*QueryConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Monitor_ServiceDesc is the grpc.ServiceDesc for Monitor service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Monitor_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "package_monitor.Monitor",
	HandlerType: (*MonitorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Produce",
			Handler:    _Monitor_Produce_Handler,
		},
		{
			MethodName: "Consume",
			Handler:    _Monitor_Consume_Handler,
		},
		{
			MethodName: "Alarm",
			Handler:    _Monitor_Alarm_Handler,
		},
		{
			MethodName: "AlarmQuery",
			Handler:    _Monitor_AlarmQuery_Handler,
		},
		{
			MethodName: "SyncConfig",
			Handler:    _Monitor_SyncConfig_Handler,
		},
		{
			MethodName: "QueryConfig",
			Handler:    _Monitor_QueryConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/package/monitor/monitor.proto",
}
