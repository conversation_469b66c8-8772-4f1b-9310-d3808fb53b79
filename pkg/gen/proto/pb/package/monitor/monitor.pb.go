// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/package/monitor/monitor.proto

package package_monitor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AlarmQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId  string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetId int64  `protobuf:"varint,3,opt,name=assetId,proto3" json:"assetId,omitempty"` // 资产id
	Start   int64  `protobuf:"varint,4,opt,name=start,proto3" json:"start,omitempty"`
	End     int64  `protobuf:"varint,5,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *AlarmQueryReq) Reset() {
	*x = AlarmQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmQueryReq) ProtoMessage() {}

func (x *AlarmQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmQueryReq.ProtoReflect.Descriptor instead.
func (*AlarmQueryReq) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{0}
}

func (x *AlarmQueryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AlarmQueryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AlarmQueryReq) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *AlarmQueryReq) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *AlarmQueryReq) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type AlarmQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*AlarmQueryRsp_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *AlarmQueryRsp) Reset() {
	*x = AlarmQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmQueryRsp) ProtoMessage() {}

func (x *AlarmQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmQueryRsp.ProtoReflect.Descriptor instead.
func (*AlarmQueryRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{1}
}

func (x *AlarmQueryRsp) GetItems() []*AlarmQueryRsp_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type QueryConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *QueryConfigReq) Reset() {
	*x = QueryConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryConfigReq) ProtoMessage() {}

func (x *QueryConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryConfigReq.ProtoReflect.Descriptor instead.
func (*QueryConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{2}
}

func (x *QueryConfigReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type QueryConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *QueryConfigRsp) Reset() {
	*x = QueryConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryConfigRsp) ProtoMessage() {}

func (x *QueryConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryConfigRsp.ProtoReflect.Descriptor instead.
func (*QueryConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{3}
}

func (x *QueryConfigRsp) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type SyncConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SyncConfigReq) Reset() {
	*x = SyncConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncConfigReq) ProtoMessage() {}

func (x *SyncConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncConfigReq.ProtoReflect.Descriptor instead.
func (*SyncConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{4}
}

func (x *SyncConfigReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SyncConfigReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type SyncConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncConfigRsp) Reset() {
	*x = SyncConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncConfigRsp) ProtoMessage() {}

func (x *SyncConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncConfigRsp.ProtoReflect.Descriptor instead.
func (*SyncConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{5}
}

type ProduceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Flow *BundleFlow `protobuf:"bytes,1,opt,name=flow,proto3" json:"flow,omitempty"`
}

func (x *ProduceReq) Reset() {
	*x = ProduceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProduceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceReq) ProtoMessage() {}

func (x *ProduceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceReq.ProtoReflect.Descriptor instead.
func (*ProduceReq) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{6}
}

func (x *ProduceReq) GetFlow() *BundleFlow {
	if x != nil {
		return x.Flow
	}
	return nil
}

type ProduceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProduceRsp) Reset() {
	*x = ProduceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProduceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceRsp) ProtoMessage() {}

func (x *ProduceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceRsp.ProtoReflect.Descriptor instead.
func (*ProduceRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{7}
}

type ConsumeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MessageId string      `protobuf:"bytes,1,opt,name=messageId,proto3" json:"messageId,omitempty"`
	Flow      *BundleFlow `protobuf:"bytes,2,opt,name=flow,proto3" json:"flow,omitempty"`
}

func (x *ConsumeReq) Reset() {
	*x = ConsumeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeReq) ProtoMessage() {}

func (x *ConsumeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeReq.ProtoReflect.Descriptor instead.
func (*ConsumeReq) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{8}
}

func (x *ConsumeReq) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *ConsumeReq) GetFlow() *BundleFlow {
	if x != nil {
		return x.Flow
	}
	return nil
}

type ConsumeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConsumeRsp) Reset() {
	*x = ConsumeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRsp) ProtoMessage() {}

func (x *ConsumeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRsp.ProtoReflect.Descriptor instead.
func (*ConsumeRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{9}
}

type BundleFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string             `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string             `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	BundleId      int64              `protobuf:"varint,3,opt,name=bundleId,proto3" json:"bundleId,omitempty"`
	Items         []*BundleFlow_Item `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`
	TransactionId string             `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Reason        string             `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	IsPaid        bool               `protobuf:"varint,7,opt,name=isPaid,proto3" json:"isPaid,omitempty"`
	BundleNum     int64              `protobuf:"varint,8,opt,name=bundleNum,proto3" json:"bundleNum,omitempty"`
	Timestamp     int64              `protobuf:"varint,9,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *BundleFlow) Reset() {
	*x = BundleFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleFlow) ProtoMessage() {}

func (x *BundleFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleFlow.ProtoReflect.Descriptor instead.
func (*BundleFlow) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{10}
}

func (x *BundleFlow) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BundleFlow) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BundleFlow) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

func (x *BundleFlow) GetItems() []*BundleFlow_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BundleFlow) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BundleFlow) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *BundleFlow) GetIsPaid() bool {
	if x != nil {
		return x.IsPaid
	}
	return false
}

func (x *BundleFlow) GetBundleNum() int64 {
	if x != nil {
		return x.BundleNum
	}
	return 0
}

func (x *BundleFlow) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type AlarmReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetId       int64  `protobuf:"varint,3,opt,name=assetId,proto3" json:"assetId,omitempty"`    // 资产id
	AssetName     string `protobuf:"bytes,4,opt,name=assetName,proto3" json:"assetName,omitempty"` // 资产名称
	Reason        string `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`       // 道具发放原因
	Date          string `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	Timestamp     int64  `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`         // 告警日期
	AssetNum      int64  `protobuf:"varint,8,opt,name=assetNum,proto3" json:"assetNum,omitempty"`           // 实际发放数量
	AssetNumLimit int64  `protobuf:"varint,9,opt,name=assetNumLimit,proto3" json:"assetNumLimit,omitempty"` // 预期发放数量
}

func (x *AlarmReq) Reset() {
	*x = AlarmReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmReq) ProtoMessage() {}

func (x *AlarmReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmReq.ProtoReflect.Descriptor instead.
func (*AlarmReq) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{11}
}

func (x *AlarmReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AlarmReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AlarmReq) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *AlarmReq) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *AlarmReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AlarmReq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *AlarmReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AlarmReq) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AlarmReq) GetAssetNumLimit() int64 {
	if x != nil {
		return x.AssetNumLimit
	}
	return 0
}

type AlarmRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AlarmRsp) Reset() {
	*x = AlarmRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmRsp) ProtoMessage() {}

func (x *AlarmRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmRsp.ProtoReflect.Descriptor instead.
func (*AlarmRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{12}
}

type AlarmQueryRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	AssetId       int64  `protobuf:"varint,3,opt,name=assetId,proto3" json:"assetId,omitempty"`    // 资产id
	AssetName     string `protobuf:"bytes,4,opt,name=assetName,proto3" json:"assetName,omitempty"` // 资产名称
	Reason        string `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`       // 道具发放原因
	Date          string `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	Timestamp     int64  `protobuf:"varint,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`         // 告警日期
	AssetNum      int64  `protobuf:"varint,8,opt,name=assetNum,proto3" json:"assetNum,omitempty"`           // 实际发放数量
	AssetNumLimit int64  `protobuf:"varint,9,opt,name=assetNumLimit,proto3" json:"assetNumLimit,omitempty"` // 预期发放数量
}

func (x *AlarmQueryRsp_Item) Reset() {
	*x = AlarmQueryRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlarmQueryRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmQueryRsp_Item) ProtoMessage() {}

func (x *AlarmQueryRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmQueryRsp_Item.ProtoReflect.Descriptor instead.
func (*AlarmQueryRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{1, 0}
}

func (x *AlarmQueryRsp_Item) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AlarmQueryRsp_Item) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AlarmQueryRsp_Item) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *AlarmQueryRsp_Item) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *AlarmQueryRsp_Item) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AlarmQueryRsp_Item) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *AlarmQueryRsp_Item) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AlarmQueryRsp_Item) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *AlarmQueryRsp_Item) GetAssetNumLimit() int64 {
	if x != nil {
		return x.AssetNumLimit
	}
	return 0
}

type BundleFlow_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Num           int32  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	TransactionId string `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Type          uint32 `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	ExternalId    int64  `protobuf:"varint,6,opt,name=externalId,proto3" json:"externalId,omitempty"`
	ExternalType  uint32 `protobuf:"varint,7,opt,name=externalType,proto3" json:"externalType,omitempty"`
}

func (x *BundleFlow_Item) Reset() {
	*x = BundleFlow_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_monitor_monitor_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BundleFlow_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BundleFlow_Item) ProtoMessage() {}

func (x *BundleFlow_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_monitor_monitor_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BundleFlow_Item.ProtoReflect.Descriptor instead.
func (*BundleFlow_Item) Descriptor() ([]byte, []int) {
	return file_pb_package_monitor_monitor_proto_rawDescGZIP(), []int{10, 0}
}

func (x *BundleFlow_Item) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BundleFlow_Item) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *BundleFlow_Item) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BundleFlow_Item) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BundleFlow_Item) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BundleFlow_Item) GetExternalId() int64 {
	if x != nil {
		return x.ExternalId
	}
	return 0
}

func (x *BundleFlow_Item) GetExternalType() uint32 {
	if x != nil {
		return x.ExternalType
	}
	return 0
}

var File_pb_package_monitor_monitor_proto protoreflect.FileDescriptor

var file_pb_package_monitor_monitor_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x22, 0x7f, 0x0a, 0x0d, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x65, 0x6e, 0x64, 0x22, 0xc5, 0x02, 0x0a, 0x0d, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x1a, 0xf8, 0x01, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e,
	0x75, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x22, 0x0a, 0x0e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x22, 0x24, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x35, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x0f, 0x0a,
	0x0d, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x22, 0x3d,
	0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x04,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x0c, 0x0a,
	0x0a, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x5b, 0x0a, 0x0a, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x46, 0x6c,
	0x6f, 0x77, 0x52, 0x04, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x0c, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0xdd, 0x03, 0x0a, 0x0a, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x36, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x2e, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x50, 0x61, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a, 0xba, 0x01, 0x0a, 0x04, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0xfc, 0x01, 0x0a, 0x08, 0x41, 0x6c, 0x61, 0x72, 0x6d,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12,
	0x24, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x0a, 0x0a, 0x08, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x73,
	0x70, 0x32, 0xbf, 0x03, 0x0a, 0x07, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x43, 0x0a,
	0x07, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x12, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x43, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x1b, 0x2e,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x05, 0x41, 0x6c, 0x61, 0x72, 0x6d,
	0x12, 0x19, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x6c,
	0x61, 0x72, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0a, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x73, 0x70, 0x12, 0x4f, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x1f, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x73, 0x70, 0x42, 0x47, 0x5a, 0x45, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65,
	0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f,
	0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_package_monitor_monitor_proto_rawDescOnce sync.Once
	file_pb_package_monitor_monitor_proto_rawDescData = file_pb_package_monitor_monitor_proto_rawDesc
)

func file_pb_package_monitor_monitor_proto_rawDescGZIP() []byte {
	file_pb_package_monitor_monitor_proto_rawDescOnce.Do(func() {
		file_pb_package_monitor_monitor_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_package_monitor_monitor_proto_rawDescData)
	})
	return file_pb_package_monitor_monitor_proto_rawDescData
}

var file_pb_package_monitor_monitor_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pb_package_monitor_monitor_proto_goTypes = []interface{}{
	(*AlarmQueryReq)(nil),      // 0: package_monitor.AlarmQueryReq
	(*AlarmQueryRsp)(nil),      // 1: package_monitor.AlarmQueryRsp
	(*QueryConfigReq)(nil),     // 2: package_monitor.QueryConfigReq
	(*QueryConfigRsp)(nil),     // 3: package_monitor.QueryConfigRsp
	(*SyncConfigReq)(nil),      // 4: package_monitor.SyncConfigReq
	(*SyncConfigRsp)(nil),      // 5: package_monitor.SyncConfigRsp
	(*ProduceReq)(nil),         // 6: package_monitor.ProduceReq
	(*ProduceRsp)(nil),         // 7: package_monitor.ProduceRsp
	(*ConsumeReq)(nil),         // 8: package_monitor.ConsumeReq
	(*ConsumeRsp)(nil),         // 9: package_monitor.ConsumeRsp
	(*BundleFlow)(nil),         // 10: package_monitor.BundleFlow
	(*AlarmReq)(nil),           // 11: package_monitor.AlarmReq
	(*AlarmRsp)(nil),           // 12: package_monitor.AlarmRsp
	(*AlarmQueryRsp_Item)(nil), // 13: package_monitor.AlarmQueryRsp.Item
	(*BundleFlow_Item)(nil),    // 14: package_monitor.BundleFlow.Item
}
var file_pb_package_monitor_monitor_proto_depIdxs = []int32{
	13, // 0: package_monitor.AlarmQueryRsp.items:type_name -> package_monitor.AlarmQueryRsp.Item
	10, // 1: package_monitor.ProduceReq.flow:type_name -> package_monitor.BundleFlow
	10, // 2: package_monitor.ConsumeReq.flow:type_name -> package_monitor.BundleFlow
	14, // 3: package_monitor.BundleFlow.items:type_name -> package_monitor.BundleFlow.Item
	6,  // 4: package_monitor.Monitor.Produce:input_type -> package_monitor.ProduceReq
	8,  // 5: package_monitor.Monitor.Consume:input_type -> package_monitor.ConsumeReq
	11, // 6: package_monitor.Monitor.Alarm:input_type -> package_monitor.AlarmReq
	0,  // 7: package_monitor.Monitor.AlarmQuery:input_type -> package_monitor.AlarmQueryReq
	4,  // 8: package_monitor.Monitor.SyncConfig:input_type -> package_monitor.SyncConfigReq
	2,  // 9: package_monitor.Monitor.QueryConfig:input_type -> package_monitor.QueryConfigReq
	7,  // 10: package_monitor.Monitor.Produce:output_type -> package_monitor.ProduceRsp
	9,  // 11: package_monitor.Monitor.Consume:output_type -> package_monitor.ConsumeRsp
	12, // 12: package_monitor.Monitor.Alarm:output_type -> package_monitor.AlarmRsp
	1,  // 13: package_monitor.Monitor.AlarmQuery:output_type -> package_monitor.AlarmQueryRsp
	5,  // 14: package_monitor.Monitor.SyncConfig:output_type -> package_monitor.SyncConfigRsp
	3,  // 15: package_monitor.Monitor.QueryConfig:output_type -> package_monitor.QueryConfigRsp
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_pb_package_monitor_monitor_proto_init() }
func file_pb_package_monitor_monitor_proto_init() {
	if File_pb_package_monitor_monitor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_package_monitor_monitor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProduceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProduceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlarmQueryRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_monitor_monitor_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BundleFlow_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_package_monitor_monitor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_package_monitor_monitor_proto_goTypes,
		DependencyIndexes: file_pb_package_monitor_monitor_proto_depIdxs,
		MessageInfos:      file_pb_package_monitor_monitor_proto_msgTypes,
	}.Build()
	File_pb_package_monitor_monitor_proto = out.File
	file_pb_package_monitor_monitor_proto_rawDesc = nil
	file_pb_package_monitor_monitor_proto_goTypes = nil
	file_pb_package_monitor_monitor_proto_depIdxs = nil
}
