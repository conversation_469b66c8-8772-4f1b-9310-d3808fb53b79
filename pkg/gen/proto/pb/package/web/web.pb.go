// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/package/web/web.proto

package package_web

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	common "kugou_adapter_service/pkg/gen/proto/pb/package/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Package struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId        int32             `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
	Price            int32             `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"` // 价格
	OpenPayAssetId   int32             `protobuf:"varint,3,opt,name=openPayAssetId,proto3" json:"openPayAssetId,omitempty"`
	OpenPayProductId string            `protobuf:"bytes,4,opt,name=openPayProductId,proto3" json:"openPayProductId,omitempty"`
	UiConfigId       string            `protobuf:"bytes,5,opt,name=uiConfigId,proto3" json:"uiConfigId,omitempty"`
	Items            []*Package_Item   `protobuf:"bytes,6,rep,name=items,proto3" json:"items,omitempty"`
	Discount         int32             `protobuf:"varint,7,opt,name=discount,proto3" json:"discount,omitempty"`           // 折扣
	PurchaseLimit    int32             `protobuf:"varint,8,opt,name=purchaseLimit,proto3" json:"purchaseLimit,omitempty"` // 限购次数 0 表示无限
	CountDownTime    int32             `protobuf:"varint,9,opt,name=countDownTime,proto3" json:"countDownTime,omitempty"` // 倒计时 秒
	PackageName      string            `protobuf:"bytes,10,opt,name=packageName,proto3" json:"packageName,omitempty"`     // 礼包名
	MusicVip         *Package_MusicVip `protobuf:"bytes,11,opt,name=musicVip,proto3" json:"musicVip,omitempty"`           // 绿钻
	UiBgProperty     string            `protobuf:"bytes,12,opt,name=uiBgProperty,proto3" json:"uiBgProperty,omitempty"`
	MMarketInfo      map[string]string `protobuf:"bytes,13,rep,name=mMarketInfo,proto3" json:"mMarketInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 商品营销属性
}

func (x *Package) Reset() {
	*x = Package{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{0}
}

func (x *Package) GetPackageId() int32 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *Package) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Package) GetOpenPayAssetId() int32 {
	if x != nil {
		return x.OpenPayAssetId
	}
	return 0
}

func (x *Package) GetOpenPayProductId() string {
	if x != nil {
		return x.OpenPayProductId
	}
	return ""
}

func (x *Package) GetUiConfigId() string {
	if x != nil {
		return x.UiConfigId
	}
	return ""
}

func (x *Package) GetItems() []*Package_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Package) GetDiscount() int32 {
	if x != nil {
		return x.Discount
	}
	return 0
}

func (x *Package) GetPurchaseLimit() int32 {
	if x != nil {
		return x.PurchaseLimit
	}
	return 0
}

func (x *Package) GetCountDownTime() int32 {
	if x != nil {
		return x.CountDownTime
	}
	return 0
}

func (x *Package) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Package) GetMusicVip() *Package_MusicVip {
	if x != nil {
		return x.MusicVip
	}
	return nil
}

func (x *Package) GetUiBgProperty() string {
	if x != nil {
		return x.UiBgProperty
	}
	return ""
}

func (x *Package) GetMMarketInfo() map[string]string {
	if x != nil {
		return x.MMarketInfo
	}
	return nil
}

type ListBySceneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scene    common.Scene           `protobuf:"varint,1,opt,name=scene,proto3,enum=package.common.Scene" json:"scene,omitempty"`
	Os       common.OperatingSystem `protobuf:"varint,2,opt,name=os,proto3,enum=package.common.OperatingSystem" json:"os,omitempty"`
	Passback string                 `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"` // 回传参数
}

func (x *ListBySceneReq) Reset() {
	*x = ListBySceneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBySceneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBySceneReq) ProtoMessage() {}

func (x *ListBySceneReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBySceneReq.ProtoReflect.Descriptor instead.
func (*ListBySceneReq) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{1}
}

func (x *ListBySceneReq) GetScene() common.Scene {
	if x != nil {
		return x.Scene
	}
	return common.Scene(0)
}

func (x *ListBySceneReq) GetOs() common.OperatingSystem {
	if x != nil {
		return x.Os
	}
	return common.OperatingSystem(0)
}

func (x *ListBySceneReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type ListBySceneRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Packages []*Package `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty"`
	Passback string     `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
	HasMore  bool       `protobuf:"varint,3,opt,name=hasMore,proto3" json:"hasMore,omitempty"`
}

func (x *ListBySceneRsp) Reset() {
	*x = ListBySceneRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBySceneRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBySceneRsp) ProtoMessage() {}

func (x *ListBySceneRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBySceneRsp.ProtoReflect.Descriptor instead.
func (*ListBySceneRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{2}
}

func (x *ListBySceneRsp) GetPackages() []*Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

func (x *ListBySceneRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *ListBySceneRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

type ExposureReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId int32        `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
	Scene     common.Scene `protobuf:"varint,2,opt,name=scene,proto3,enum=package.common.Scene" json:"scene,omitempty"`
}

func (x *ExposureReq) Reset() {
	*x = ExposureReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureReq) ProtoMessage() {}

func (x *ExposureReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureReq.ProtoReflect.Descriptor instead.
func (*ExposureReq) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{3}
}

func (x *ExposureReq) GetPackageId() int32 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *ExposureReq) GetScene() common.Scene {
	if x != nil {
		return x.Scene
	}
	return common.Scene(0)
}

type ExposureRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountDownTime int32 `protobuf:"varint,1,opt,name=countDownTime,proto3" json:"countDownTime,omitempty"` // 倒计时 秒
}

func (x *ExposureRsp) Reset() {
	*x = ExposureRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExposureRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExposureRsp) ProtoMessage() {}

func (x *ExposureRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExposureRsp.ProtoReflect.Descriptor instead.
func (*ExposureRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{4}
}

func (x *ExposureRsp) GetCountDownTime() int32 {
	if x != nil {
		return x.CountDownTime
	}
	return 0
}

type BatchExposureReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageIds []int32      `protobuf:"varint,1,rep,packed,name=packageIds,proto3" json:"packageIds,omitempty"`
	Scene      common.Scene `protobuf:"varint,2,opt,name=scene,proto3,enum=package.common.Scene" json:"scene,omitempty"`
}

func (x *BatchExposureReq) Reset() {
	*x = BatchExposureReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchExposureReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchExposureReq) ProtoMessage() {}

func (x *BatchExposureReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchExposureReq.ProtoReflect.Descriptor instead.
func (*BatchExposureReq) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{5}
}

func (x *BatchExposureReq) GetPackageIds() []int32 {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

func (x *BatchExposureReq) GetScene() common.Scene {
	if x != nil {
		return x.Scene
	}
	return common.Scene(0)
}

type BatchExposureRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExposureInfos []*BatchExposureRsp_ExposureInfo `protobuf:"bytes,1,rep,name=exposureInfos,proto3" json:"exposureInfos,omitempty"`
}

func (x *BatchExposureRsp) Reset() {
	*x = BatchExposureRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchExposureRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchExposureRsp) ProtoMessage() {}

func (x *BatchExposureRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchExposureRsp.ProtoReflect.Descriptor instead.
func (*BatchExposureRsp) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{6}
}

func (x *BatchExposureRsp) GetExposureInfos() []*BatchExposureRsp_ExposureInfo {
	if x != nil {
		return x.ExposureInfos
	}
	return nil
}

type Package_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *Package_Item) Reset() {
	*x = Package_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_Item) ProtoMessage() {}

func (x *Package_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_Item.ProtoReflect.Descriptor instead.
func (*Package_Item) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Package_Item) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Package_Item) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type Package_MusicVip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId     string `protobuf:"bytes,1,opt,name=productId,proto3" json:"productId,omitempty"`
	SkuId         string `protobuf:"bytes,2,opt,name=skuId,proto3" json:"skuId,omitempty"`
	ActivityId    string `protobuf:"bytes,3,opt,name=activityId,proto3" json:"activityId,omitempty"`
	AvailableTime string `protobuf:"bytes,4,opt,name=availableTime,proto3" json:"availableTime,omitempty"`
	GreenId       int64  `protobuf:"varint,5,opt,name=greenId,proto3" json:"greenId,omitempty"`
	Extra         string `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *Package_MusicVip) Reset() {
	*x = Package_MusicVip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package_MusicVip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package_MusicVip) ProtoMessage() {}

func (x *Package_MusicVip) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package_MusicVip.ProtoReflect.Descriptor instead.
func (*Package_MusicVip) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Package_MusicVip) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *Package_MusicVip) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *Package_MusicVip) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *Package_MusicVip) GetAvailableTime() string {
	if x != nil {
		return x.AvailableTime
	}
	return ""
}

func (x *Package_MusicVip) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

func (x *Package_MusicVip) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

type BatchExposureRsp_ExposureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId     int32 `protobuf:"varint,1,opt,name=packageId,proto3" json:"packageId,omitempty"`
	CountDownTime int32 `protobuf:"varint,2,opt,name=countDownTime,proto3" json:"countDownTime,omitempty"`
}

func (x *BatchExposureRsp_ExposureInfo) Reset() {
	*x = BatchExposureRsp_ExposureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_package_web_web_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchExposureRsp_ExposureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchExposureRsp_ExposureInfo) ProtoMessage() {}

func (x *BatchExposureRsp_ExposureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_package_web_web_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchExposureRsp_ExposureInfo.ProtoReflect.Descriptor instead.
func (*BatchExposureRsp_ExposureInfo) Descriptor() ([]byte, []int) {
	return file_pb_package_web_web_proto_rawDescGZIP(), []int{6, 0}
}

func (x *BatchExposureRsp_ExposureInfo) GetPackageId() int32 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *BatchExposureRsp_ExposureInfo) GetCountDownTime() int32 {
	if x != nil {
		return x.CountDownTime
	}
	return 0
}

var File_pb_package_web_web_proto protoreflect.FileDescriptor

var file_pb_package_web_web_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x77, 0x65, 0x62,
	0x2f, 0x77, 0x65, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x06, 0x0a, 0x07, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x50,
	0x61, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x50,
	0x61, 0x79, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x6d, 0x75, 0x73, 0x69, 0x63, 0x56,
	0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x4d,
	0x75, 0x73, 0x69, 0x63, 0x56, 0x69, 0x70, 0x52, 0x08, 0x6d, 0x75, 0x73, 0x69, 0x63, 0x56, 0x69,
	0x70, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x69, 0x42, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x69, 0x42, 0x67, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x0b, 0x6d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x2e, 0x4d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0b, 0x6d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x28,
	0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x1a, 0xb4, 0x01, 0x0a, 0x08, 0x4d, 0x75, 0x73,
	0x69, 0x63, 0x56, 0x69, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x1a,
	0x3e, 0x0a, 0x10, 0x4d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x8a, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12,
	0x2f, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x78, 0x0a, 0x0e,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x12, 0x30,
	0x0a, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x22, 0x58, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x22, 0x33, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x5f, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78,
	0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0a, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52,
	0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x0d, 0x65,
	0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x73,
	0x70, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x52, 0x0a,
	0x0c, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x32, 0xdd, 0x02, 0x0a, 0x03, 0x57, 0x65, 0x62, 0x12, 0x47, 0x0a, 0x0b, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x63, 0x65,
	0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x77, 0x65, 0x62, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x3e, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x18,
	0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x45, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x4d, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x12, 0x1d, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65,
	0x62, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x65, 0x62,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x15, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e,
	0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x41,
	0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73,
	0x70, 0x42, 0x43, 0x5a, 0x41, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x77, 0x65, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_package_web_web_proto_rawDescOnce sync.Once
	file_pb_package_web_web_proto_rawDescData = file_pb_package_web_web_proto_rawDesc
)

func file_pb_package_web_web_proto_rawDescGZIP() []byte {
	file_pb_package_web_web_proto_rawDescOnce.Do(func() {
		file_pb_package_web_web_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_package_web_web_proto_rawDescData)
	})
	return file_pb_package_web_web_proto_rawDescData
}

var file_pb_package_web_web_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pb_package_web_web_proto_goTypes = []interface{}{
	(*Package)(nil),                       // 0: package_web.Package
	(*ListBySceneReq)(nil),                // 1: package_web.ListBySceneReq
	(*ListBySceneRsp)(nil),                // 2: package_web.ListBySceneRsp
	(*ExposureReq)(nil),                   // 3: package_web.ExposureReq
	(*ExposureRsp)(nil),                   // 4: package_web.ExposureRsp
	(*BatchExposureReq)(nil),              // 5: package_web.BatchExposureReq
	(*BatchExposureRsp)(nil),              // 6: package_web.BatchExposureRsp
	(*Package_Item)(nil),                  // 7: package_web.Package.Item
	(*Package_MusicVip)(nil),              // 8: package_web.Package.MusicVip
	nil,                                   // 9: package_web.Package.MMarketInfoEntry
	(*BatchExposureRsp_ExposureInfo)(nil), // 10: package_web.BatchExposureRsp.ExposureInfo
	(common.Scene)(0),                     // 11: package.common.Scene
	(common.OperatingSystem)(0),           // 12: package.common.OperatingSystem
	(*inlet.InletStatusReq)(nil),          // 13: inlet.InletStatusReq
	(*inlet.ActivityStateReq)(nil),        // 14: inlet.ActivityStateReq
	(*inlet.InletStatusRsp)(nil),          // 15: inlet.InletStatusRsp
	(*inlet.ActivityStateRsp)(nil),        // 16: inlet.ActivityStateRsp
}
var file_pb_package_web_web_proto_depIdxs = []int32{
	7,  // 0: package_web.Package.items:type_name -> package_web.Package.Item
	8,  // 1: package_web.Package.musicVip:type_name -> package_web.Package.MusicVip
	9,  // 2: package_web.Package.mMarketInfo:type_name -> package_web.Package.MMarketInfoEntry
	11, // 3: package_web.ListBySceneReq.scene:type_name -> package.common.Scene
	12, // 4: package_web.ListBySceneReq.os:type_name -> package.common.OperatingSystem
	0,  // 5: package_web.ListBySceneRsp.packages:type_name -> package_web.Package
	11, // 6: package_web.ExposureReq.scene:type_name -> package.common.Scene
	11, // 7: package_web.BatchExposureReq.scene:type_name -> package.common.Scene
	10, // 8: package_web.BatchExposureRsp.exposureInfos:type_name -> package_web.BatchExposureRsp.ExposureInfo
	1,  // 9: package_web.Web.ListByScene:input_type -> package_web.ListBySceneReq
	3,  // 10: package_web.Web.Exposure:input_type -> package_web.ExposureReq
	5,  // 11: package_web.Web.BatchExposure:input_type -> package_web.BatchExposureReq
	13, // 12: package_web.Web.InletStatus:input_type -> inlet.InletStatusReq
	14, // 13: package_web.Web.ActivityState:input_type -> inlet.ActivityStateReq
	2,  // 14: package_web.Web.ListByScene:output_type -> package_web.ListBySceneRsp
	4,  // 15: package_web.Web.Exposure:output_type -> package_web.ExposureRsp
	6,  // 16: package_web.Web.BatchExposure:output_type -> package_web.BatchExposureRsp
	15, // 17: package_web.Web.InletStatus:output_type -> inlet.InletStatusRsp
	16, // 18: package_web.Web.ActivityState:output_type -> inlet.ActivityStateRsp
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_package_web_web_proto_init() }
func file_pb_package_web_web_proto_init() {
	if File_pb_package_web_web_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_package_web_web_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBySceneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBySceneRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExposureRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchExposureReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchExposureRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package_MusicVip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_package_web_web_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchExposureRsp_ExposureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_package_web_web_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_package_web_web_proto_goTypes,
		DependencyIndexes: file_pb_package_web_web_proto_depIdxs,
		MessageInfos:      file_pb_package_web_web_proto_msgTypes,
	}.Build()
	File_pb_package_web_web_proto = out.File
	file_pb_package_web_web_proto_rawDesc = nil
	file_pb_package_web_web_proto_goTypes = nil
	file_pb_package_web_web_proto_depIdxs = nil
}
