// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/package/web/web.proto

package package_web

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Web_ListByScene_FullMethodName   = "/package_web.Web/ListByScene"
	Web_Exposure_FullMethodName      = "/package_web.Web/Exposure"
	Web_BatchExposure_FullMethodName = "/package_web.Web/BatchExposure"
	Web_InletStatus_FullMethodName   = "/package_web.Web/InletStatus"
	Web_ActivityState_FullMethodName = "/package_web.Web/ActivityState"
)

// WebClient is the client API for Web service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebClient interface {
	// 通过场景查询
	ListByScene(ctx context.Context, in *ListBySceneReq, opts ...grpc.CallOption) (*ListBySceneRsp, error)
	// 曝光
	Exposure(ctx context.Context, in *ExposureReq, opts ...grpc.CallOption) (*ExposureRsp, error)
	// 批量曝光
	BatchExposure(ctx context.Context, in *BatchExposureReq, opts ...grpc.CallOption) (*BatchExposureRsp, error)
	// 查询活动状态
	InletStatus(ctx context.Context, in *inlet.InletStatusReq, opts ...grpc.CallOption) (*inlet.InletStatusRsp, error)
	// 入口管理
	ActivityState(ctx context.Context, in *inlet.ActivityStateReq, opts ...grpc.CallOption) (*inlet.ActivityStateRsp, error)
}

type webClient struct {
	cc grpc.ClientConnInterface
}

func NewWebClient(cc grpc.ClientConnInterface) WebClient {
	return &webClient{cc}
}

func (c *webClient) ListByScene(ctx context.Context, in *ListBySceneReq, opts ...grpc.CallOption) (*ListBySceneRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBySceneRsp)
	err := c.cc.Invoke(ctx, Web_ListByScene_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webClient) Exposure(ctx context.Context, in *ExposureReq, opts ...grpc.CallOption) (*ExposureRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExposureRsp)
	err := c.cc.Invoke(ctx, Web_Exposure_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webClient) BatchExposure(ctx context.Context, in *BatchExposureReq, opts ...grpc.CallOption) (*BatchExposureRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchExposureRsp)
	err := c.cc.Invoke(ctx, Web_BatchExposure_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webClient) InletStatus(ctx context.Context, in *inlet.InletStatusReq, opts ...grpc.CallOption) (*inlet.InletStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(inlet.InletStatusRsp)
	err := c.cc.Invoke(ctx, Web_InletStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webClient) ActivityState(ctx context.Context, in *inlet.ActivityStateReq, opts ...grpc.CallOption) (*inlet.ActivityStateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(inlet.ActivityStateRsp)
	err := c.cc.Invoke(ctx, Web_ActivityState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebServer is the server API for Web service.
// All implementations should embed UnimplementedWebServer
// for forward compatibility
type WebServer interface {
	// 通过场景查询
	ListByScene(context.Context, *ListBySceneReq) (*ListBySceneRsp, error)
	// 曝光
	Exposure(context.Context, *ExposureReq) (*ExposureRsp, error)
	// 批量曝光
	BatchExposure(context.Context, *BatchExposureReq) (*BatchExposureRsp, error)
	// 查询活动状态
	InletStatus(context.Context, *inlet.InletStatusReq) (*inlet.InletStatusRsp, error)
	// 入口管理
	ActivityState(context.Context, *inlet.ActivityStateReq) (*inlet.ActivityStateRsp, error)
}

// UnimplementedWebServer should be embedded to have forward compatible implementations.
type UnimplementedWebServer struct {
}

func (UnimplementedWebServer) ListByScene(context.Context, *ListBySceneReq) (*ListBySceneRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListByScene not implemented")
}
func (UnimplementedWebServer) Exposure(context.Context, *ExposureReq) (*ExposureRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Exposure not implemented")
}
func (UnimplementedWebServer) BatchExposure(context.Context, *BatchExposureReq) (*BatchExposureRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchExposure not implemented")
}
func (UnimplementedWebServer) InletStatus(context.Context, *inlet.InletStatusReq) (*inlet.InletStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InletStatus not implemented")
}
func (UnimplementedWebServer) ActivityState(context.Context, *inlet.ActivityStateReq) (*inlet.ActivityStateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityState not implemented")
}

// UnsafeWebServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebServer will
// result in compilation errors.
type UnsafeWebServer interface {
	mustEmbedUnimplementedWebServer()
}

func RegisterWebServer(s grpc.ServiceRegistrar, srv WebServer) {
	s.RegisterService(&Web_ServiceDesc, srv)
}

func _Web_ListByScene_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBySceneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).ListByScene(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_ListByScene_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).ListByScene(ctx, req.(*ListBySceneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Web_Exposure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExposureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).Exposure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_Exposure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).Exposure(ctx, req.(*ExposureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Web_BatchExposure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchExposureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).BatchExposure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_BatchExposure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).BatchExposure(ctx, req.(*BatchExposureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Web_InletStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(inlet.InletStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).InletStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_InletStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).InletStatus(ctx, req.(*inlet.InletStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Web_ActivityState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(inlet.ActivityStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebServer).ActivityState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Web_ActivityState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebServer).ActivityState(ctx, req.(*inlet.ActivityStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Web_ServiceDesc is the grpc.ServiceDesc for Web service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Web_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "package_web.Web",
	HandlerType: (*WebServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListByScene",
			Handler:    _Web_ListByScene_Handler,
		},
		{
			MethodName: "Exposure",
			Handler:    _Web_Exposure_Handler,
		},
		{
			MethodName: "BatchExposure",
			Handler:    _Web_BatchExposure_Handler,
		},
		{
			MethodName: "InletStatus",
			Handler:    _Web_InletStatus_Handler,
		},
		{
			MethodName: "ActivityState",
			Handler:    _Web_ActivityState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/package/web/web.proto",
}
