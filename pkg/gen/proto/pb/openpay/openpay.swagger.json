{"swagger": "2.0", "info": {"title": "pb/openpay/openpay.proto", "version": "version not set"}, "tags": [{"name": "Openpay"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/openpay/consume": {"post": {"summary": "发货", "operationId": "Openpay_OpenConsume", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOpenConsumeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOpenConsumeReq"}}], "tags": ["Openpay"]}}, "/openpay/currency_gift": {"post": {"summary": "游戏币礼物信息", "operationId": "Openpay_OpenCurrencyGift", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOpenCurrencyGiftRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOpenCurrencyGiftReq"}}], "tags": ["Openpay"]}}, "/openpay/package_gift": {"post": {"summary": "礼包礼物信息", "operationId": "Openpay_OpenPackageGift", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOpenPackageGiftRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOpenPackageGiftReq"}}], "tags": ["Openpay"]}}, "/openpay/place_order": {"post": {"summary": "下单", "operationId": "Openpay_OpenPlaceOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOpenPlaceOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOpenPlaceOrderReq"}}], "tags": ["Openpay"]}}}, "definitions": {"gameAssetAddOption": {"type": "object", "properties": {"limit": {"type": "string", "format": "int64", "title": "增加上限, 不指定着不限制, 否则最多只能加到limit封顶"}}}, "gameAssetFromType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>", "AssetFromFree", "AssetFromPay"], "default": "<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>", "title": "- AssetFromDefault: 默认, 会被重置为来自免费兑换\n - AssetFromFree: 来自免费兑换\n - AssetFromPay: 来自K币购买"}, "gameCommRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "gameConsumeInfo": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAssetChange"}, "title": "要得到的资产"}, "amount": {"type": "integer", "format": "int64", "title": "web算的kb/饭票数，用于校验"}, "consumeType": {"$ref": "#/definitions/gameOpenPayConsumeType"}, "from": {"$ref": "#/definitions/gameAssetFromType", "title": "标识 来自免费兑换 还是 付费购买, 默认为付费购买,只针对游戏币有效"}, "productId": {"type": "string", "title": "商品 id"}}}, "gameDevice": {"type": "object", "properties": {"qua": {"type": "string"}, "deviceInfo": {"type": "string"}}}, "gameGear": {"type": "object", "properties": {"num": {"type": "integer", "format": "int64", "title": "数量"}, "tag": {"type": "string"}, "marketingGifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameMarketingGift"}}}}, "gameMarketingGift": {"type": "object", "properties": {"name": {"type": "string"}, "tag": {"type": "string"}, "num": {"type": "integer", "format": "int64"}, "icon": {"type": "string"}, "reason": {"type": "string", "title": "发放理由"}}}, "gameMidas": {"type": "object", "properties": {"pf": {"type": "string"}, "pfKey": {"type": "string"}, "sessionId": {"type": "string"}, "sessionType": {"type": "string"}, "payToken": {"type": "string"}}}, "gameOpenConsumeReq": {"type": "object", "properties": {"consumeId": {"type": "string"}, "scene": {"$ref": "#/definitions/gameScene"}, "device": {"$ref": "#/definitions/gameDevice"}, "midas": {"$ref": "#/definitions/gameMidas"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}, "title": "登陆态应该在cookie里，需要解出来"}, "gameOpenConsumeRsp": {"type": "object", "properties": {"commRsp": {"$ref": "#/definitions/gameCommRsp"}, "balance": {"type": "integer", "format": "int64"}, "ugc": {"$ref": "#/definitions/gameOpenConsumeUgc"}, "giftText": {"type": "string", "title": "道具文案 eg. 漂流瓶x10"}, "rewardText": {"type": "string", "title": "奖励文案 eg. 三消金币+10"}}}, "gameOpenConsumeUgc": {"type": "object", "properties": {"avatar": {"type": "string", "title": "头像"}, "nickname": {"type": "string", "title": "昵称"}, "cover": {"type": "string", "title": "封面"}, "title": {"type": "string", "title": "名称"}, "playerNum": {"type": "string", "format": "int64", "title": "收听数"}}}, "gameOpenCurrencyGiftReq": {"type": "object", "properties": {"assetId": {"type": "integer", "format": "int64"}}}, "gameOpenCurrencyGiftRsp": {"type": "object", "properties": {"commRsp": {"$ref": "#/definitions/gameCommRsp"}, "giftId": {"type": "integer", "format": "int64"}, "giftName": {"type": "string"}, "giftDesc": {"type": "string"}, "giftIcon": {"type": "string"}, "unitPrice": {"type": "string", "title": "单价"}, "gear": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGear"}, "title": "出售档位"}, "assetId": {"type": "integer", "format": "int64"}, "exchangeRate": {"type": "integer", "format": "int64"}, "assetName": {"type": "string"}, "payCurrencyName": {"type": "string", "title": "如果是K歌，就是K币，Q音为饭票"}, "payType": {"type": "integer", "format": "int64", "title": "payType 如下"}, "modalType": {"$ref": "#/definitions/gameOpenPayModalType"}, "assetRule": {"type": "string", "title": "规则文案"}, "assetIcon": {"type": "string", "title": "资产图标"}, "extraDescription": {"type": "string", "title": "补充文案"}, "marketingCopywriting": {"type": "string", "title": "营销文案"}, "marketingImage": {"type": "string", "title": "营销图片"}, "marketingImageUrl": {"type": "string", "title": "营销跳转链接"}, "marketingGiftThreshold": {"type": "integer", "format": "int64", "title": "营销礼物阈值"}, "marketingGifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameMarketingGift"}, "title": "营销礼物"}, "marketingId": {"type": "string", "title": "营销 id"}, "marketingTag": {"type": "string", "title": "营销标签"}, "sendToUgc": {"type": "boolean"}}}, "gameOpenPackageGiftReq": {"type": "object", "properties": {"packageId": {"type": "integer", "format": "int64"}}}, "gameOpenPackageGiftRsp": {"type": "object", "properties": {"commRsp": {"$ref": "#/definitions/gameCommRsp"}, "giftId": {"type": "integer", "format": "int64"}, "giftName": {"type": "string"}, "giftDesc": {"type": "string"}, "giftIcon": {"type": "string"}, "unitPrice": {"type": "string", "title": "单价"}, "gear": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGear"}, "title": "出售档位"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gamePackageGiftReward"}, "title": "礼包奖励信息"}, "assetId": {"type": "integer", "format": "int64"}, "exchangeRate": {"type": "integer", "format": "int64", "title": "兑换汇率"}, "assetName": {"type": "string"}, "payCurrencyName": {"type": "string", "title": "如果是K歌，就是K币，Q音为饭票"}, "modalType": {"$ref": "#/definitions/gameOpenPayModalType", "title": "风险模型"}, "payType": {"type": "integer", "format": "int64", "title": "支付类型 1 赠送礼物获得 2 直接花k币获得 3 赠送道具获得"}, "assetIcon": {"type": "string", "title": "资产图标"}, "sendToUgc": {"type": "boolean"}}}, "gameOpenPayConsumeType": {"type": "string", "enum": ["OpenPayAsset", "OpenPayPackage", "OpenPayExtensionProduct"], "default": "OpenPayAsset", "title": "- OpenPayAsset: 游戏资产\n - OpenPayPackage: 礼包\n - OpenPayExtensionProduct: 其他商品"}, "gameOpenPayModalType": {"type": "string", "enum": ["OpenPayModalDefault", "OpenPayModalLowRisk", "OpenPayModalHighRisk"], "default": "OpenPayModalDefault", "title": "- OpenPayModalDefault: 默认弹窗\n - OpenPayModalLowRisk: 低风险弹窗\n - OpenPayModalHighRisk: 高风险弹窗"}, "gameOpenPlaceOrderReq": {"type": "object", "properties": {"consumeInfo": {"$ref": "#/definitions/gameConsumeInfo"}, "scene": {"$ref": "#/definitions/gameScene"}, "device": {"$ref": "#/definitions/gameDevice"}, "midas": {"$ref": "#/definitions/gameMidas"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}, "sysTs": {"type": "integer", "format": "int64"}, "marketingId": {"type": "string"}}}, "gameOpenPlaceOrderRsp": {"type": "object", "properties": {"commRsp": {"$ref": "#/definitions/gameCommRsp"}, "consumeId": {"type": "string"}, "sysTs": {"type": "integer", "format": "int64"}}}, "gamePackageGiftReward": {"type": "object", "properties": {"rewardName": {"type": "string", "title": "奖励名称"}, "rewardNum": {"type": "integer", "format": "int64", "title": "奖励数量"}}}, "gameScene": {"type": "object", "properties": {"sceneType": {"type": "integer", "format": "int64", "title": "参考 SceneType"}, "roomId": {"type": "string"}, "showId": {"type": "string"}, "ugcId": {"type": "string"}, "tarUid": {"type": "string", "title": "例如ugc作者，ktv房主，LIVE主播"}}}, "gameUserAssetChange": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量(免费+付费)"}, "freeNum": {"type": "string", "format": "int64", "title": "资产数量(免费),只读"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间 单位秒"}, "addOpetion": {"$ref": "#/definitions/gameAssetAddOption", "title": "只对增加资产有效,可选"}}}, "googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}}}