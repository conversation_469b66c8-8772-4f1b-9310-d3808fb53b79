// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/openpay/openpay.proto

package openpay

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	asset "kugou_adapter_service/pkg/gen/proto/pb/asset"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SceneType int32

const (
	SceneType_NONE SceneType = 0
	SceneType_LIVE SceneType = 1 // 直播
	SceneType_KTV  SceneType = 2 // KTV
	SceneType_UGC  SceneType = 3 // UGC
	SceneType_PLOP SceneType = 4 // 扑通
)

// Enum value maps for SceneType.
var (
	SceneType_name = map[int32]string{
		0: "NONE",
		1: "LIVE",
		2: "KTV",
		3: "UGC",
		4: "PLOP",
	}
	SceneType_value = map[string]int32{
		"NONE": 0,
		"LIVE": 1,
		"KTV":  2,
		"UGC":  3,
		"PLOP": 4,
	}
)

func (x SceneType) Enum() *SceneType {
	p := new(SceneType)
	*p = x
	return p
}

func (x SceneType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SceneType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_openpay_openpay_proto_enumTypes[0].Descriptor()
}

func (SceneType) Type() protoreflect.EnumType {
	return &file_pb_openpay_openpay_proto_enumTypes[0]
}

func (x SceneType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SceneType.Descriptor instead.
func (SceneType) EnumDescriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{0}
}

type OpenPayConsumeType int32

const (
	OpenPayConsumeType_OpenPayAsset            OpenPayConsumeType = 0 // 游戏资产
	OpenPayConsumeType_OpenPayPackage          OpenPayConsumeType = 1 // 礼包
	OpenPayConsumeType_OpenPayExtensionProduct OpenPayConsumeType = 2 // 其他商品
)

// Enum value maps for OpenPayConsumeType.
var (
	OpenPayConsumeType_name = map[int32]string{
		0: "OpenPayAsset",
		1: "OpenPayPackage",
		2: "OpenPayExtensionProduct",
	}
	OpenPayConsumeType_value = map[string]int32{
		"OpenPayAsset":            0,
		"OpenPayPackage":          1,
		"OpenPayExtensionProduct": 2,
	}
)

func (x OpenPayConsumeType) Enum() *OpenPayConsumeType {
	p := new(OpenPayConsumeType)
	*p = x
	return p
}

func (x OpenPayConsumeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OpenPayConsumeType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_openpay_openpay_proto_enumTypes[1].Descriptor()
}

func (OpenPayConsumeType) Type() protoreflect.EnumType {
	return &file_pb_openpay_openpay_proto_enumTypes[1]
}

func (x OpenPayConsumeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OpenPayConsumeType.Descriptor instead.
func (OpenPayConsumeType) EnumDescriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{1}
}

type OpenPayModalType int32

const (
	OpenPayModalType_OpenPayModalDefault  OpenPayModalType = 0 // 默认弹窗
	OpenPayModalType_OpenPayModalLowRisk  OpenPayModalType = 1 // 低风险弹窗
	OpenPayModalType_OpenPayModalHighRisk OpenPayModalType = 2 // 高风险弹窗
)

// Enum value maps for OpenPayModalType.
var (
	OpenPayModalType_name = map[int32]string{
		0: "OpenPayModalDefault",
		1: "OpenPayModalLowRisk",
		2: "OpenPayModalHighRisk",
	}
	OpenPayModalType_value = map[string]int32{
		"OpenPayModalDefault":  0,
		"OpenPayModalLowRisk":  1,
		"OpenPayModalHighRisk": 2,
	}
)

func (x OpenPayModalType) Enum() *OpenPayModalType {
	p := new(OpenPayModalType)
	*p = x
	return p
}

func (x OpenPayModalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OpenPayModalType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_openpay_openpay_proto_enumTypes[2].Descriptor()
}

func (OpenPayModalType) Type() protoreflect.EnumType {
	return &file_pb_openpay_openpay_proto_enumTypes[2]
}

func (x OpenPayModalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OpenPayModalType.Descriptor instead.
func (OpenPayModalType) EnumDescriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{2}
}

type Status int32

const (
	Status_PENDING_PAYMENT Status = 0 // 待付款
	Status_ALREADY_SHIPPED Status = 1 // 已发货
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "PENDING_PAYMENT",
		1: "ALREADY_SHIPPED",
	}
	Status_value = map[string]int32{
		"PENDING_PAYMENT": 0,
		"ALREADY_SHIPPED": 1,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_openpay_openpay_proto_enumTypes[3].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_pb_openpay_openpay_proto_enumTypes[3]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{3}
}

type CommRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *CommRsp) Reset() {
	*x = CommRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommRsp) ProtoMessage() {}

func (x *CommRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommRsp.ProtoReflect.Descriptor instead.
func (*CommRsp) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{0}
}

func (x *CommRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Midas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pf          string `protobuf:"bytes,1,opt,name=pf,proto3" json:"pf,omitempty"`
	PfKey       string `protobuf:"bytes,2,opt,name=pf_key,json=pfKey,proto3" json:"pf_key,omitempty"`
	SessionId   string `protobuf:"bytes,3,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty"`
	SessionType string `protobuf:"bytes,4,opt,name=session_type,json=sessionType,proto3" json:"session_type,omitempty"`
	PayToken    string `protobuf:"bytes,5,opt,name=pay_token,json=payToken,proto3" json:"pay_token,omitempty"`
}

func (x *Midas) Reset() {
	*x = Midas{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Midas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Midas) ProtoMessage() {}

func (x *Midas) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Midas.ProtoReflect.Descriptor instead.
func (*Midas) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{1}
}

func (x *Midas) GetPf() string {
	if x != nil {
		return x.Pf
	}
	return ""
}

func (x *Midas) GetPfKey() string {
	if x != nil {
		return x.PfKey
	}
	return ""
}

func (x *Midas) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *Midas) GetSessionType() string {
	if x != nil {
		return x.SessionType
	}
	return ""
}

func (x *Midas) GetPayToken() string {
	if x != nil {
		return x.PayToken
	}
	return ""
}

type Device struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Qua        string `protobuf:"bytes,1,opt,name=qua,proto3" json:"qua,omitempty"`
	DeviceInfo string `protobuf:"bytes,2,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
}

func (x *Device) Reset() {
	*x = Device{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{2}
}

func (x *Device) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *Device) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

type Scene struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 参考 SceneType
	SceneType uint32 `protobuf:"varint,1,opt,name=scene_type,json=sceneType,proto3" json:"scene_type,omitempty"`
	RoomId    string `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	ShowId    string `protobuf:"bytes,3,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`
	UgcId     string `protobuf:"bytes,4,opt,name=ugc_id,json=ugcId,proto3" json:"ugc_id,omitempty"`
	TarUid    string `protobuf:"bytes,5,opt,name=tar_uid,json=tarUid,proto3" json:"tar_uid,omitempty"` // 例如ugc作者，ktv房主，LIVE主播
}

func (x *Scene) Reset() {
	*x = Scene{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scene) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scene) ProtoMessage() {}

func (x *Scene) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scene.ProtoReflect.Descriptor instead.
func (*Scene) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{3}
}

func (x *Scene) GetSceneType() uint32 {
	if x != nil {
		return x.SceneType
	}
	return 0
}

func (x *Scene) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *Scene) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

func (x *Scene) GetUgcId() string {
	if x != nil {
		return x.UgcId
	}
	return ""
}

func (x *Scene) GetTarUid() string {
	if x != nil {
		return x.TarUid
	}
	return ""
}

type ConsumeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets      []*asset.UserAssetChange `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`  // 要得到的资产
	Amount      uint32                   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"` // web算的kb/饭票数，用于校验
	ConsumeType OpenPayConsumeType       `protobuf:"varint,3,opt,name=consume_type,json=consumeType,proto3,enum=component.game.OpenPayConsumeType" json:"consume_type,omitempty"`
	From        asset.AssetFromType      `protobuf:"varint,4,opt,name=from,proto3,enum=component.game.AssetFromType" json:"from,omitempty"` // 标识 来自免费兑换 还是 付费购买, 默认为付费购买,只针对游戏币有效
	ProductId   string                   `protobuf:"bytes,5,opt,name=productId,proto3" json:"productId,omitempty"`                          // 商品 id
}

func (x *ConsumeInfo) Reset() {
	*x = ConsumeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeInfo) ProtoMessage() {}

func (x *ConsumeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeInfo.ProtoReflect.Descriptor instead.
func (*ConsumeInfo) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{4}
}

func (x *ConsumeInfo) GetAssets() []*asset.UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *ConsumeInfo) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ConsumeInfo) GetConsumeType() OpenPayConsumeType {
	if x != nil {
		return x.ConsumeType
	}
	return OpenPayConsumeType_OpenPayAsset
}

func (x *ConsumeInfo) GetFrom() asset.AssetFromType {
	if x != nil {
		return x.From
	}
	return asset.AssetFromType(0)
}

func (x *ConsumeInfo) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

type OpenPlaceOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsumeInfo *ConsumeInfo      `protobuf:"bytes,1,opt,name=consume_info,json=consumeInfo,proto3" json:"consume_info,omitempty"`
	Scene       *Scene            `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	Device      *Device           `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`
	Midas       *Midas            `protobuf:"bytes,4,opt,name=midas,proto3" json:"midas,omitempty"`
	MapExt      map[string]string `protobuf:"bytes,5,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SysTs       uint32            `protobuf:"varint,6,opt,name=sys_ts,json=sysTs,proto3" json:"sys_ts,omitempty"`
	MarketingId string            `protobuf:"bytes,7,opt,name=marketingId,proto3" json:"marketingId,omitempty"`
}

func (x *OpenPlaceOrderReq) Reset() {
	*x = OpenPlaceOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenPlaceOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenPlaceOrderReq) ProtoMessage() {}

func (x *OpenPlaceOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenPlaceOrderReq.ProtoReflect.Descriptor instead.
func (*OpenPlaceOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{5}
}

func (x *OpenPlaceOrderReq) GetConsumeInfo() *ConsumeInfo {
	if x != nil {
		return x.ConsumeInfo
	}
	return nil
}

func (x *OpenPlaceOrderReq) GetScene() *Scene {
	if x != nil {
		return x.Scene
	}
	return nil
}

func (x *OpenPlaceOrderReq) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *OpenPlaceOrderReq) GetMidas() *Midas {
	if x != nil {
		return x.Midas
	}
	return nil
}

func (x *OpenPlaceOrderReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *OpenPlaceOrderReq) GetSysTs() uint32 {
	if x != nil {
		return x.SysTs
	}
	return 0
}

func (x *OpenPlaceOrderReq) GetMarketingId() string {
	if x != nil {
		return x.MarketingId
	}
	return ""
}

type OpenPlaceOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommRsp   *CommRsp `protobuf:"bytes,1,opt,name=comm_rsp,json=commRsp,proto3" json:"comm_rsp,omitempty"`
	ConsumeId string   `protobuf:"bytes,2,opt,name=consume_id,json=consumeId,proto3" json:"consume_id,omitempty"`
	SysTs     uint32   `protobuf:"varint,3,opt,name=sys_ts,json=sysTs,proto3" json:"sys_ts,omitempty"`
}

func (x *OpenPlaceOrderRsp) Reset() {
	*x = OpenPlaceOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenPlaceOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenPlaceOrderRsp) ProtoMessage() {}

func (x *OpenPlaceOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenPlaceOrderRsp.ProtoReflect.Descriptor instead.
func (*OpenPlaceOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{6}
}

func (x *OpenPlaceOrderRsp) GetCommRsp() *CommRsp {
	if x != nil {
		return x.CommRsp
	}
	return nil
}

func (x *OpenPlaceOrderRsp) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *OpenPlaceOrderRsp) GetSysTs() uint32 {
	if x != nil {
		return x.SysTs
	}
	return 0
}

// 登陆态应该在cookie里，需要解出来
type OpenConsumeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsumeId string            `protobuf:"bytes,1,opt,name=consume_id,json=consumeId,proto3" json:"consume_id,omitempty"`
	Scene     *Scene            `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"`
	Device    *Device           `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`
	Midas     *Midas            `protobuf:"bytes,4,opt,name=midas,proto3" json:"midas,omitempty"`
	MapExt    map[string]string `protobuf:"bytes,5,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *OpenConsumeReq) Reset() {
	*x = OpenConsumeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenConsumeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenConsumeReq) ProtoMessage() {}

func (x *OpenConsumeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenConsumeReq.ProtoReflect.Descriptor instead.
func (*OpenConsumeReq) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{7}
}

func (x *OpenConsumeReq) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *OpenConsumeReq) GetScene() *Scene {
	if x != nil {
		return x.Scene
	}
	return nil
}

func (x *OpenConsumeReq) GetDevice() *Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *OpenConsumeReq) GetMidas() *Midas {
	if x != nil {
		return x.Midas
	}
	return nil
}

func (x *OpenConsumeReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type OpenConsumeUgc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatar    string `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`        // 头像
	Nickname  string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`    // 昵称
	Cover     string `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover,omitempty"`          // 封面
	Title     string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`          // 名称
	PlayerNum int64  `protobuf:"varint,5,opt,name=playerNum,proto3" json:"playerNum,omitempty"` // 收听数
}

func (x *OpenConsumeUgc) Reset() {
	*x = OpenConsumeUgc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenConsumeUgc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenConsumeUgc) ProtoMessage() {}

func (x *OpenConsumeUgc) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenConsumeUgc.ProtoReflect.Descriptor instead.
func (*OpenConsumeUgc) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{8}
}

func (x *OpenConsumeUgc) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *OpenConsumeUgc) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *OpenConsumeUgc) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *OpenConsumeUgc) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OpenConsumeUgc) GetPlayerNum() int64 {
	if x != nil {
		return x.PlayerNum
	}
	return 0
}

type OpenConsumeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommRsp    *CommRsp        `protobuf:"bytes,1,opt,name=comm_rsp,json=commRsp,proto3" json:"comm_rsp,omitempty"`
	Balance    uint32          `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
	Ugc        *OpenConsumeUgc `protobuf:"bytes,3,opt,name=ugc,proto3" json:"ugc,omitempty"`
	GiftText   string          `protobuf:"bytes,4,opt,name=giftText,proto3" json:"giftText,omitempty"`     // 道具文案 eg. 漂流瓶x10
	RewardText string          `protobuf:"bytes,5,opt,name=rewardText,proto3" json:"rewardText,omitempty"` // 奖励文案 eg. 三消金币+10
}

func (x *OpenConsumeRsp) Reset() {
	*x = OpenConsumeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenConsumeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenConsumeRsp) ProtoMessage() {}

func (x *OpenConsumeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenConsumeRsp.ProtoReflect.Descriptor instead.
func (*OpenConsumeRsp) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{9}
}

func (x *OpenConsumeRsp) GetCommRsp() *CommRsp {
	if x != nil {
		return x.CommRsp
	}
	return nil
}

func (x *OpenConsumeRsp) GetBalance() uint32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *OpenConsumeRsp) GetUgc() *OpenConsumeUgc {
	if x != nil {
		return x.Ugc
	}
	return nil
}

func (x *OpenConsumeRsp) GetGiftText() string {
	if x != nil {
		return x.GiftText
	}
	return ""
}

func (x *OpenConsumeRsp) GetRewardText() string {
	if x != nil {
		return x.RewardText
	}
	return ""
}

type OpenCurrencyGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId uint32 `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
}

func (x *OpenCurrencyGiftReq) Reset() {
	*x = OpenCurrencyGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenCurrencyGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenCurrencyGiftReq) ProtoMessage() {}

func (x *OpenCurrencyGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenCurrencyGiftReq.ProtoReflect.Descriptor instead.
func (*OpenCurrencyGiftReq) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{10}
}

func (x *OpenCurrencyGiftReq) GetAssetId() uint32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

type MarketingGift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Tag    string `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	Num    uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	Icon   string `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Reason string `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"` // 发放理由
}

func (x *MarketingGift) Reset() {
	*x = MarketingGift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarketingGift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingGift) ProtoMessage() {}

func (x *MarketingGift) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingGift.ProtoReflect.Descriptor instead.
func (*MarketingGift) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{11}
}

func (x *MarketingGift) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MarketingGift) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *MarketingGift) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *MarketingGift) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *MarketingGift) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type Gear struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num            uint32           `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"` // 数量
	Tag            string           `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	MarketingGifts []*MarketingGift `protobuf:"bytes,3,rep,name=marketingGifts,proto3" json:"marketingGifts,omitempty"`
}

func (x *Gear) Reset() {
	*x = Gear{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gear) ProtoMessage() {}

func (x *Gear) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gear.ProtoReflect.Descriptor instead.
func (*Gear) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{12}
}

func (x *Gear) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Gear) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *Gear) GetMarketingGifts() []*MarketingGift {
	if x != nil {
		return x.MarketingGifts
	}
	return nil
}

type OpenCurrencyGiftRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommRsp                *CommRsp         `protobuf:"bytes,1,opt,name=comm_rsp,json=commRsp,proto3" json:"comm_rsp,omitempty"`
	GiftId                 uint32           `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName               string           `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftDesc               string           `protobuf:"bytes,4,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc,omitempty"`
	GiftIcon               string           `protobuf:"bytes,5,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	UnitPrice              string           `protobuf:"bytes,6,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"` // 单价
	Gear                   []*Gear          `protobuf:"bytes,7,rep,name=gear,proto3" json:"gear,omitempty"`                            // 出售档位
	AssetId                uint32           `protobuf:"varint,8,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	ExchangeRate           uint32           `protobuf:"varint,9,opt,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"`
	AssetName              string           `protobuf:"bytes,10,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	PayCurrencyName        string           `protobuf:"bytes,11,opt,name=pay_currency_name,json=payCurrencyName,proto3" json:"pay_currency_name,omitempty"` // 如果是K歌，就是K币，Q音为饭票
	PayType                uint32           `protobuf:"varint,12,opt,name=pay_type,json=payType,proto3" json:"pay_type,omitempty"`                          // payType 如下
	ModalType              OpenPayModalType `protobuf:"varint,13,opt,name=modalType,proto3,enum=component.game.OpenPayModalType" json:"modalType,omitempty"`
	AssetRule              string           `protobuf:"bytes,14,opt,name=assetRule,proto3" json:"assetRule,omitempty"`                            // 规则文案
	AssetIcon              string           `protobuf:"bytes,15,opt,name=assetIcon,proto3" json:"assetIcon,omitempty"`                            // 资产图标
	ExtraDescription       string           `protobuf:"bytes,16,opt,name=extraDescription,proto3" json:"extraDescription,omitempty"`              // 补充文案
	MarketingCopywriting   string           `protobuf:"bytes,17,opt,name=marketingCopywriting,proto3" json:"marketingCopywriting,omitempty"`      // 营销文案
	MarketingImage         string           `protobuf:"bytes,18,opt,name=marketingImage,proto3" json:"marketingImage,omitempty"`                  // 营销图片
	MarketingImageUrl      string           `protobuf:"bytes,19,opt,name=marketingImageUrl,proto3" json:"marketingImageUrl,omitempty"`            // 营销跳转链接
	MarketingGiftThreshold uint32           `protobuf:"varint,20,opt,name=marketingGiftThreshold,proto3" json:"marketingGiftThreshold,omitempty"` // 营销礼物阈值
	MarketingGifts         []*MarketingGift `protobuf:"bytes,21,rep,name=marketingGifts,proto3" json:"marketingGifts,omitempty"`                  // 营销礼物
	MarketingId            string           `protobuf:"bytes,22,opt,name=marketingId,proto3" json:"marketingId,omitempty"`                        // 营销 id
	MarketingTag           string           `protobuf:"bytes,23,opt,name=marketingTag,proto3" json:"marketingTag,omitempty"`                      // 营销标签
	SendToUgc              bool             `protobuf:"varint,24,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
}

func (x *OpenCurrencyGiftRsp) Reset() {
	*x = OpenCurrencyGiftRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenCurrencyGiftRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenCurrencyGiftRsp) ProtoMessage() {}

func (x *OpenCurrencyGiftRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenCurrencyGiftRsp.ProtoReflect.Descriptor instead.
func (*OpenCurrencyGiftRsp) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{13}
}

func (x *OpenCurrencyGiftRsp) GetCommRsp() *CommRsp {
	if x != nil {
		return x.CommRsp
	}
	return nil
}

func (x *OpenCurrencyGiftRsp) GetGiftId() uint32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *OpenCurrencyGiftRsp) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetGiftDesc() string {
	if x != nil {
		return x.GiftDesc
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetGiftIcon() string {
	if x != nil {
		return x.GiftIcon
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetUnitPrice() string {
	if x != nil {
		return x.UnitPrice
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetGear() []*Gear {
	if x != nil {
		return x.Gear
	}
	return nil
}

func (x *OpenCurrencyGiftRsp) GetAssetId() uint32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *OpenCurrencyGiftRsp) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *OpenCurrencyGiftRsp) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetPayCurrencyName() string {
	if x != nil {
		return x.PayCurrencyName
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *OpenCurrencyGiftRsp) GetModalType() OpenPayModalType {
	if x != nil {
		return x.ModalType
	}
	return OpenPayModalType_OpenPayModalDefault
}

func (x *OpenCurrencyGiftRsp) GetAssetRule() string {
	if x != nil {
		return x.AssetRule
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetAssetIcon() string {
	if x != nil {
		return x.AssetIcon
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetExtraDescription() string {
	if x != nil {
		return x.ExtraDescription
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetMarketingCopywriting() string {
	if x != nil {
		return x.MarketingCopywriting
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetMarketingImage() string {
	if x != nil {
		return x.MarketingImage
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetMarketingImageUrl() string {
	if x != nil {
		return x.MarketingImageUrl
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetMarketingGiftThreshold() uint32 {
	if x != nil {
		return x.MarketingGiftThreshold
	}
	return 0
}

func (x *OpenCurrencyGiftRsp) GetMarketingGifts() []*MarketingGift {
	if x != nil {
		return x.MarketingGifts
	}
	return nil
}

func (x *OpenCurrencyGiftRsp) GetMarketingId() string {
	if x != nil {
		return x.MarketingId
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetMarketingTag() string {
	if x != nil {
		return x.MarketingTag
	}
	return ""
}

func (x *OpenCurrencyGiftRsp) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

type PackageGiftReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardName string `protobuf:"bytes,1,opt,name=reward_name,json=rewardName,proto3" json:"reward_name,omitempty"` // 奖励名称
	RewardNum  uint32 `protobuf:"varint,2,opt,name=reward_num,json=rewardNum,proto3" json:"reward_num,omitempty"`   // 奖励数量
}

func (x *PackageGiftReward) Reset() {
	*x = PackageGiftReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PackageGiftReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageGiftReward) ProtoMessage() {}

func (x *PackageGiftReward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageGiftReward.ProtoReflect.Descriptor instead.
func (*PackageGiftReward) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{14}
}

func (x *PackageGiftReward) GetRewardName() string {
	if x != nil {
		return x.RewardName
	}
	return ""
}

func (x *PackageGiftReward) GetRewardNum() uint32 {
	if x != nil {
		return x.RewardNum
	}
	return 0
}

type OpenPackageGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId uint32 `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
}

func (x *OpenPackageGiftReq) Reset() {
	*x = OpenPackageGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenPackageGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenPackageGiftReq) ProtoMessage() {}

func (x *OpenPackageGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenPackageGiftReq.ProtoReflect.Descriptor instead.
func (*OpenPackageGiftReq) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{15}
}

func (x *OpenPackageGiftReq) GetPackageId() uint32 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

type OpenPackageGiftRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommRsp         *CommRsp             `protobuf:"bytes,1,opt,name=comm_rsp,json=commRsp,proto3" json:"comm_rsp,omitempty"`
	GiftId          uint32               `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName        string               `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftDesc        string               `protobuf:"bytes,4,opt,name=gift_desc,json=giftDesc,proto3" json:"gift_desc,omitempty"`
	GiftIcon        string               `protobuf:"bytes,5,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	UnitPrice       string               `protobuf:"bytes,6,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"` // 单价
	Gear            []*Gear              `protobuf:"bytes,7,rep,name=gear,proto3" json:"gear,omitempty"`                            // 出售档位
	Rewards         []*PackageGiftReward `protobuf:"bytes,8,rep,name=rewards,proto3" json:"rewards,omitempty"`                      // 礼包奖励信息
	AssetId         uint32               `protobuf:"varint,9,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	ExchangeRate    uint32               `protobuf:"varint,10,opt,name=exchange_rate,json=exchangeRate,proto3" json:"exchange_rate,omitempty"` // 兑换汇率
	AssetName       string               `protobuf:"bytes,11,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	PayCurrencyName string               `protobuf:"bytes,12,opt,name=pay_currency_name,json=payCurrencyName,proto3" json:"pay_currency_name,omitempty"`  // 如果是K歌，就是K币，Q音为饭票
	ModalType       OpenPayModalType     `protobuf:"varint,13,opt,name=modalType,proto3,enum=component.game.OpenPayModalType" json:"modalType,omitempty"` // 风险模型
	PayType         uint32               `protobuf:"varint,14,opt,name=pay_type,json=payType,proto3" json:"pay_type,omitempty"`                           // 支付类型 1 赠送礼物获得 2 直接花k币获得 3 赠送道具获得
	AssetIcon       string               `protobuf:"bytes,15,opt,name=asset_icon,json=assetIcon,proto3" json:"asset_icon,omitempty"`                      // 资产图标
	SendToUgc       bool                 `protobuf:"varint,16,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
}

func (x *OpenPackageGiftRsp) Reset() {
	*x = OpenPackageGiftRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenPackageGiftRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenPackageGiftRsp) ProtoMessage() {}

func (x *OpenPackageGiftRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenPackageGiftRsp.ProtoReflect.Descriptor instead.
func (*OpenPackageGiftRsp) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{16}
}

func (x *OpenPackageGiftRsp) GetCommRsp() *CommRsp {
	if x != nil {
		return x.CommRsp
	}
	return nil
}

func (x *OpenPackageGiftRsp) GetGiftId() uint32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

func (x *OpenPackageGiftRsp) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetGiftDesc() string {
	if x != nil {
		return x.GiftDesc
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetGiftIcon() string {
	if x != nil {
		return x.GiftIcon
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetUnitPrice() string {
	if x != nil {
		return x.UnitPrice
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetGear() []*Gear {
	if x != nil {
		return x.Gear
	}
	return nil
}

func (x *OpenPackageGiftRsp) GetRewards() []*PackageGiftReward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *OpenPackageGiftRsp) GetAssetId() uint32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *OpenPackageGiftRsp) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *OpenPackageGiftRsp) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetPayCurrencyName() string {
	if x != nil {
		return x.PayCurrencyName
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetModalType() OpenPayModalType {
	if x != nil {
		return x.ModalType
	}
	return OpenPayModalType_OpenPayModalDefault
}

func (x *OpenPackageGiftRsp) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *OpenPackageGiftRsp) GetAssetIcon() string {
	if x != nil {
		return x.AssetIcon
	}
	return ""
}

func (x *OpenPackageGiftRsp) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

type OrderStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status uint32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // enum status
	Ts     uint32 `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *OrderStatus) Reset() {
	*x = OrderStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderStatus) ProtoMessage() {}

func (x *OrderStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderStatus.ProtoReflect.Descriptor instead.
func (*OrderStatus) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{17}
}

func (x *OrderStatus) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *OrderStatus) GetTs() uint32 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type OrderConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 收入类型 1/计收入 2/不计收入
	RevenueType uint32 `protobuf:"varint,1,opt,name=revenue_type,json=revenueType,proto3" json:"revenue_type,omitempty"`
	// 购买模式 1/赠送礼物获得 2/直接获得
	PayType uint32 `protobuf:"varint,2,opt,name=pay_type,json=payType,proto3" json:"pay_type,omitempty"`
	// 平台业务ID
	PlatBusinessId uint32 `protobuf:"varint,3,opt,name=plat_business_id,json=platBusinessId,proto3" json:"plat_business_id,omitempty"`
	// 送出礼物id，或道具Id
	PayGiftId uint32 `protobuf:"varint,4,opt,name=pay_gift_id,json=payGiftId,proto3" json:"pay_gift_id,omitempty"`
	// 送出礼物个数，或道具个数（建议这里的礼物id+礼物个数算出来的饭票数，和amount校验下是否相等）
	PayGiftNum uint32 `protobuf:"varint,5,opt,name=pay_gift_num,json=payGiftNum,proto3" json:"pay_gift_num,omitempty"`
	// 奖励 id
	RewardId int64 `protobuf:"varint,6,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
}

func (x *OrderConf) Reset() {
	*x = OrderConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderConf) ProtoMessage() {}

func (x *OrderConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderConf.ProtoReflect.Descriptor instead.
func (*OrderConf) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{18}
}

func (x *OrderConf) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *OrderConf) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *OrderConf) GetPlatBusinessId() uint32 {
	if x != nil {
		return x.PlatBusinessId
	}
	return 0
}

func (x *OrderConf) GetPayGiftId() uint32 {
	if x != nil {
		return x.PayGiftId
	}
	return 0
}

func (x *OrderConf) GetPayGiftNum() uint32 {
	if x != nil {
		return x.PayGiftNum
	}
	return 0
}

func (x *OrderConf) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

type CkvOrderRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                uint64            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PlatId             uint32            `protobuf:"varint,2,opt,name=plat_id,json=platId,proto3" json:"plat_id,omitempty"`
	PlatBusinessId     uint32            `protobuf:"varint,3,opt,name=plat_business_id,json=platBusinessId,proto3" json:"plat_business_id,omitempty"`
	Status             []*OrderStatus    `protobuf:"bytes,4,rep,name=status,proto3" json:"status,omitempty"`
	ConsumeInfo        *ConsumeInfo      `protobuf:"bytes,5,opt,name=consume_info,json=consumeInfo,proto3" json:"consume_info,omitempty"`
	OrderConf          *OrderConf        `protobuf:"bytes,6,opt,name=order_conf,json=orderConf,proto3" json:"order_conf,omitempty"`
	MapExt             map[string]string `protobuf:"bytes,7,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Sig                string            `protobuf:"bytes,8,opt,name=sig,proto3" json:"sig,omitempty"`
	ConsumeId          string            `protobuf:"bytes,9,opt,name=consume_id,json=consumeId,proto3" json:"consume_id,omitempty"` // 业务的消费id
	MarketingId        string            `protobuf:"bytes,10,opt,name=marketing_id,json=marketingId,proto3" json:"marketing_id,omitempty"`
	MarketingRewardId  int64             `protobuf:"varint,11,opt,name=marketing_reward_id,json=marketingRewardId,proto3" json:"marketing_reward_id,omitempty"`
	MarketingTimestamp int64             `protobuf:"varint,12,opt,name=marketing_timestamp,json=marketingTimestamp,proto3" json:"marketing_timestamp,omitempty"`
	OrderTimestamp     int64             `protobuf:"varint,13,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	ExtensionId        int64             `protobuf:"varint,14,opt,name=extension_id,json=extensionId,proto3" json:"extension_id,omitempty"`
	SendToUgc          bool              `protobuf:"varint,15,opt,name=send_to_ugc,json=sendToUgc,proto3" json:"send_to_ugc,omitempty"`
	UgcAvatar          string            `protobuf:"bytes,16,opt,name=ugc_avatar,json=ugcAvatar,proto3" json:"ugc_avatar,omitempty"`
	UgcNickname        string            `protobuf:"bytes,17,opt,name=ugc_nickname,json=ugcNickname,proto3" json:"ugc_nickname,omitempty"`
	UgcCover           string            `protobuf:"bytes,18,opt,name=ugc_cover,json=ugcCover,proto3" json:"ugc_cover,omitempty"`
	UgcTitle           string            `protobuf:"bytes,19,opt,name=ugc_title,json=ugcTitle,proto3" json:"ugc_title,omitempty"`
	UgcPlayerNum       int64             `protobuf:"varint,20,opt,name=ugc_player_num,json=ugcPlayerNum,proto3" json:"ugc_player_num,omitempty"`
	ExtensionApiOs     int32             `protobuf:"varint,21,opt,name=extension_api_os,json=extensionApiOs,proto3" json:"extension_api_os,omitempty"`
}

func (x *CkvOrderRecord) Reset() {
	*x = CkvOrderRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CkvOrderRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CkvOrderRecord) ProtoMessage() {}

func (x *CkvOrderRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CkvOrderRecord.ProtoReflect.Descriptor instead.
func (*CkvOrderRecord) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{19}
}

func (x *CkvOrderRecord) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CkvOrderRecord) GetPlatId() uint32 {
	if x != nil {
		return x.PlatId
	}
	return 0
}

func (x *CkvOrderRecord) GetPlatBusinessId() uint32 {
	if x != nil {
		return x.PlatBusinessId
	}
	return 0
}

func (x *CkvOrderRecord) GetStatus() []*OrderStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CkvOrderRecord) GetConsumeInfo() *ConsumeInfo {
	if x != nil {
		return x.ConsumeInfo
	}
	return nil
}

func (x *CkvOrderRecord) GetOrderConf() *OrderConf {
	if x != nil {
		return x.OrderConf
	}
	return nil
}

func (x *CkvOrderRecord) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *CkvOrderRecord) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

func (x *CkvOrderRecord) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *CkvOrderRecord) GetMarketingId() string {
	if x != nil {
		return x.MarketingId
	}
	return ""
}

func (x *CkvOrderRecord) GetMarketingRewardId() int64 {
	if x != nil {
		return x.MarketingRewardId
	}
	return 0
}

func (x *CkvOrderRecord) GetMarketingTimestamp() int64 {
	if x != nil {
		return x.MarketingTimestamp
	}
	return 0
}

func (x *CkvOrderRecord) GetOrderTimestamp() int64 {
	if x != nil {
		return x.OrderTimestamp
	}
	return 0
}

func (x *CkvOrderRecord) GetExtensionId() int64 {
	if x != nil {
		return x.ExtensionId
	}
	return 0
}

func (x *CkvOrderRecord) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

func (x *CkvOrderRecord) GetUgcAvatar() string {
	if x != nil {
		return x.UgcAvatar
	}
	return ""
}

func (x *CkvOrderRecord) GetUgcNickname() string {
	if x != nil {
		return x.UgcNickname
	}
	return ""
}

func (x *CkvOrderRecord) GetUgcCover() string {
	if x != nil {
		return x.UgcCover
	}
	return ""
}

func (x *CkvOrderRecord) GetUgcTitle() string {
	if x != nil {
		return x.UgcTitle
	}
	return ""
}

func (x *CkvOrderRecord) GetUgcPlayerNum() int64 {
	if x != nil {
		return x.UgcPlayerNum
	}
	return 0
}

func (x *CkvOrderRecord) GetExtensionApiOs() int32 {
	if x != nil {
		return x.ExtensionApiOs
	}
	return 0
}

type CkvSnowFlake struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxID int64 `protobuf:"varint,1,opt,name=maxID,proto3" json:"maxID,omitempty"`
}

func (x *CkvSnowFlake) Reset() {
	*x = CkvSnowFlake{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CkvSnowFlake) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CkvSnowFlake) ProtoMessage() {}

func (x *CkvSnowFlake) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CkvSnowFlake.ProtoReflect.Descriptor instead.
func (*CkvSnowFlake) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{20}
}

func (x *CkvSnowFlake) GetMaxID() int64 {
	if x != nil {
		return x.MaxID
	}
	return 0
}

type CkvOrderLock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsumeId string `protobuf:"bytes,1,opt,name=consume_id,json=consumeId,proto3" json:"consume_id,omitempty"`
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *CkvOrderLock) Reset() {
	*x = CkvOrderLock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CkvOrderLock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CkvOrderLock) ProtoMessage() {}

func (x *CkvOrderLock) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CkvOrderLock.ProtoReflect.Descriptor instead.
func (*CkvOrderLock) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{21}
}

func (x *CkvOrderLock) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *CkvOrderLock) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type ConsumeFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string       `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId      string       `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ConsumeInfo *ConsumeInfo `protobuf:"bytes,3,opt,name=consumeInfo,proto3" json:"consumeInfo,omitempty"`
	ConsumeId   string       `protobuf:"bytes,4,opt,name=consumeId,proto3" json:"consumeId,omitempty"`
	Timestamp   int64        `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *ConsumeFlow) Reset() {
	*x = ConsumeFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_openpay_openpay_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeFlow) ProtoMessage() {}

func (x *ConsumeFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_openpay_openpay_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeFlow.ProtoReflect.Descriptor instead.
func (*ConsumeFlow) Descriptor() ([]byte, []int) {
	return file_pb_openpay_openpay_proto_rawDescGZIP(), []int{22}
}

func (x *ConsumeFlow) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ConsumeFlow) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ConsumeFlow) GetConsumeInfo() *ConsumeInfo {
	if x != nil {
		return x.ConsumeInfo
	}
	return nil
}

func (x *ConsumeFlow) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *ConsumeFlow) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_pb_openpay_openpay_proto protoreflect.FileDescriptor

var file_pb_openpay_openpay_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65,
	0x6e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x37,
	0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x05, 0x4d, 0x69, 0x64, 0x61,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x70,
	0x66, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x66, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x66, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x79, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x79, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x3b, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x71, 0x75, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x88, 0x01, 0x0a, 0x05, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x75, 0x67, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x75, 0x67, 0x63, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x55, 0x69, 0x64, 0x22,
	0xf6, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x37, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x45, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x72, 0x6f, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x22, 0x98, 0x03, 0x0a, 0x11, 0x4f, 0x70, 0x65,
	0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x3e,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b,
	0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x6d,
	0x69, 0x64, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x69, 0x64, 0x61,
	0x73, 0x52, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x12, 0x45, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x5f, 0x74, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x73, 0x79, 0x73, 0x54, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x7d, 0x0a, 0x11, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x52, 0x73, 0x70, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x73,
	0x79, 0x73, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x79, 0x73,
	0x54, 0x73, 0x22, 0xb8, 0x02, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x12, 0x2e, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x2b, 0x0a, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x52, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x12, 0x42,
	0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x4d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8e, 0x01,
	0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x67, 0x63,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x22, 0xcc,
	0x01, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x73, 0x70, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x30, 0x0a, 0x03, 0x75, 0x67, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70,
	0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x67, 0x63, 0x52, 0x03, 0x75, 0x67,
	0x63, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x54, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x65, 0x78, 0x74, 0x22, 0x30, 0x0a,
	0x13, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x47, 0x69, 0x66,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x73, 0x0a, 0x0d, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x69, 0x66, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x22, 0x71, 0x0a, 0x04, 0x47, 0x65, 0x61, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67,
	0x12, 0x45, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x69, 0x66,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x47, 0x69, 0x66, 0x74, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x47, 0x69, 0x66, 0x74, 0x73, 0x22, 0xbd, 0x07, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x6e,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x47, 0x69, 0x66, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x32, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x52, 0x73, 0x70, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66,
	0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69,
	0x66, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x49,
	0x63, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70,
	0x61, 0x79, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x4d, 0x6f,
	0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x2a,
	0x0a, 0x10, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x74, 0x72, 0x61, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x14, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x70, 0x79, 0x77, 0x72, 0x69, 0x74, 0x69,
	0x6e, 0x67, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x70, 0x79, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x26,
	0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x36, 0x0a, 0x16, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x47, 0x69, 0x66, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x16, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x47,
	0x69, 0x66, 0x74, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x45, 0x0a, 0x0e,
	0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x69, 0x66, 0x74, 0x73, 0x18, 0x15,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x47,
	0x69, 0x66, 0x74, 0x52, 0x0e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x47, 0x69,
	0x66, 0x74, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x54, 0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e,
	0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65,
	0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x22, 0x53, 0x0a, 0x11, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x22, 0x33, 0x0a, 0x12,
	0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x22, 0xe1, 0x04, 0x0a, 0x12, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x6d,
	0x5f, 0x72, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x52, 0x73, 0x70, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07,
	0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67,
	0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x67,
	0x65, 0x61, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x52,
	0x04, 0x67, 0x65, 0x61, 0x72, 0x12, 0x3b, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x47,
	0x69, 0x66, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61,
	0x79, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54,
	0x6f, 0x55, 0x67, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x6f, 0x55, 0x67, 0x63, 0x22, 0x35, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74, 0x73, 0x22, 0xd2, 0x01, 0x0a,
	0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x74,
	0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x79, 0x47, 0x69, 0x66, 0x74,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x47, 0x69, 0x66,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x64, 0x22, 0x80, 0x07, 0x0a, 0x0e, 0x43, 0x6b, 0x76, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38,
	0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x42, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6b, 0x76, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x67, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0b, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x67, 0x63, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x67, 0x63, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x67, 0x63, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x75, 0x67, 0x63, 0x5f, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x75, 0x67, 0x63, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x67, 0x63, 0x5f, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x67, 0x63, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x75, 0x67, 0x63, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x67, 0x63, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x67, 0x63,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x75, 0x67, 0x63, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12,
	0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69,
	0x5f, 0x6f, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x4f, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x24, 0x0a, 0x0c, 0x43, 0x6b, 0x76, 0x53, 0x6e, 0x6f, 0x77, 0x46,
	0x6c, 0x61, 0x6b, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x78, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x61, 0x78, 0x49, 0x44, 0x22, 0x4b, 0x0a, 0x0c, 0x43, 0x6b,
	0x76, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xb6, 0x01, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2a, 0x3b, 0x0a, 0x09, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a,
	0x04, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x49, 0x56, 0x45, 0x10,
	0x01, 0x12, 0x07, 0x0a, 0x03, 0x4b, 0x54, 0x56, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x47,
	0x43, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x4c, 0x4f, 0x50, 0x10, 0x04, 0x2a, 0x57, 0x0a,
	0x12, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x70, 0x65,
	0x6e, 0x50, 0x61, 0x79, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x10, 0x02, 0x2a, 0x5e, 0x0a, 0x10, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61,
	0x79, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x70,
	0x65, 0x6e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x4d, 0x6f,
	0x64, 0x61, 0x6c, 0x4c, 0x6f, 0x77, 0x52, 0x69, 0x73, 0x6b, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14,
	0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x48, 0x69, 0x67, 0x68,
	0x52, 0x69, 0x73, 0x6b, 0x10, 0x02, 0x2a, 0x32, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x5f, 0x53, 0x48, 0x49, 0x50, 0x50, 0x45, 0x44, 0x10, 0x01, 0x32, 0xec, 0x03, 0x0a, 0x07, 0x4f,
	0x70, 0x65, 0x6e, 0x70, 0x61, 0x79, 0x12, 0x77, 0x0a, 0x0e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x6c,
	0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65,
	0x6e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0x1f,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x70, 0x61, 0x79, 0x2f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x6a, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x1b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x70, 0x61, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x7f, 0x0a, 0x10, 0x4f,
	0x70, 0x65, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x47, 0x69, 0x66, 0x74, 0x12,
	0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x47, 0x69, 0x66,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x47, 0x69, 0x66, 0x74, 0x52, 0x73, 0x70, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x61, 0x79, 0x2f, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x12, 0x7b, 0x0a, 0x0f,
	0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x47, 0x69, 0x66, 0x74, 0x12,
	0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x47, 0x69, 0x66, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x47, 0x69, 0x66, 0x74, 0x52, 0x73, 0x70, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a,
	0x01, 0x2a, 0x22, 0x15, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x61, 0x79, 0x2f, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x42, 0x3f, 0x5a, 0x3d, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_openpay_openpay_proto_rawDescOnce sync.Once
	file_pb_openpay_openpay_proto_rawDescData = file_pb_openpay_openpay_proto_rawDesc
)

func file_pb_openpay_openpay_proto_rawDescGZIP() []byte {
	file_pb_openpay_openpay_proto_rawDescOnce.Do(func() {
		file_pb_openpay_openpay_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_openpay_openpay_proto_rawDescData)
	})
	return file_pb_openpay_openpay_proto_rawDescData
}

var file_pb_openpay_openpay_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_openpay_openpay_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_pb_openpay_openpay_proto_goTypes = []interface{}{
	(SceneType)(0),                // 0: component.game.SceneType
	(OpenPayConsumeType)(0),       // 1: component.game.OpenPayConsumeType
	(OpenPayModalType)(0),         // 2: component.game.OpenPayModalType
	(Status)(0),                   // 3: component.game.Status
	(*CommRsp)(nil),               // 4: component.game.CommRsp
	(*Midas)(nil),                 // 5: component.game.Midas
	(*Device)(nil),                // 6: component.game.Device
	(*Scene)(nil),                 // 7: component.game.Scene
	(*ConsumeInfo)(nil),           // 8: component.game.ConsumeInfo
	(*OpenPlaceOrderReq)(nil),     // 9: component.game.OpenPlaceOrderReq
	(*OpenPlaceOrderRsp)(nil),     // 10: component.game.OpenPlaceOrderRsp
	(*OpenConsumeReq)(nil),        // 11: component.game.OpenConsumeReq
	(*OpenConsumeUgc)(nil),        // 12: component.game.OpenConsumeUgc
	(*OpenConsumeRsp)(nil),        // 13: component.game.OpenConsumeRsp
	(*OpenCurrencyGiftReq)(nil),   // 14: component.game.OpenCurrencyGiftReq
	(*MarketingGift)(nil),         // 15: component.game.MarketingGift
	(*Gear)(nil),                  // 16: component.game.Gear
	(*OpenCurrencyGiftRsp)(nil),   // 17: component.game.OpenCurrencyGiftRsp
	(*PackageGiftReward)(nil),     // 18: component.game.PackageGiftReward
	(*OpenPackageGiftReq)(nil),    // 19: component.game.OpenPackageGiftReq
	(*OpenPackageGiftRsp)(nil),    // 20: component.game.OpenPackageGiftRsp
	(*OrderStatus)(nil),           // 21: component.game.OrderStatus
	(*OrderConf)(nil),             // 22: component.game.OrderConf
	(*CkvOrderRecord)(nil),        // 23: component.game.CkvOrderRecord
	(*CkvSnowFlake)(nil),          // 24: component.game.CkvSnowFlake
	(*CkvOrderLock)(nil),          // 25: component.game.CkvOrderLock
	(*ConsumeFlow)(nil),           // 26: component.game.ConsumeFlow
	nil,                           // 27: component.game.OpenPlaceOrderReq.MapExtEntry
	nil,                           // 28: component.game.OpenConsumeReq.MapExtEntry
	nil,                           // 29: component.game.CkvOrderRecord.MapExtEntry
	(*asset.UserAssetChange)(nil), // 30: component.game.UserAssetChange
	(asset.AssetFromType)(0),      // 31: component.game.AssetFromType
}
var file_pb_openpay_openpay_proto_depIdxs = []int32{
	30, // 0: component.game.ConsumeInfo.assets:type_name -> component.game.UserAssetChange
	1,  // 1: component.game.ConsumeInfo.consume_type:type_name -> component.game.OpenPayConsumeType
	31, // 2: component.game.ConsumeInfo.from:type_name -> component.game.AssetFromType
	8,  // 3: component.game.OpenPlaceOrderReq.consume_info:type_name -> component.game.ConsumeInfo
	7,  // 4: component.game.OpenPlaceOrderReq.scene:type_name -> component.game.Scene
	6,  // 5: component.game.OpenPlaceOrderReq.device:type_name -> component.game.Device
	5,  // 6: component.game.OpenPlaceOrderReq.midas:type_name -> component.game.Midas
	27, // 7: component.game.OpenPlaceOrderReq.mapExt:type_name -> component.game.OpenPlaceOrderReq.MapExtEntry
	4,  // 8: component.game.OpenPlaceOrderRsp.comm_rsp:type_name -> component.game.CommRsp
	7,  // 9: component.game.OpenConsumeReq.scene:type_name -> component.game.Scene
	6,  // 10: component.game.OpenConsumeReq.device:type_name -> component.game.Device
	5,  // 11: component.game.OpenConsumeReq.midas:type_name -> component.game.Midas
	28, // 12: component.game.OpenConsumeReq.mapExt:type_name -> component.game.OpenConsumeReq.MapExtEntry
	4,  // 13: component.game.OpenConsumeRsp.comm_rsp:type_name -> component.game.CommRsp
	12, // 14: component.game.OpenConsumeRsp.ugc:type_name -> component.game.OpenConsumeUgc
	15, // 15: component.game.Gear.marketingGifts:type_name -> component.game.MarketingGift
	4,  // 16: component.game.OpenCurrencyGiftRsp.comm_rsp:type_name -> component.game.CommRsp
	16, // 17: component.game.OpenCurrencyGiftRsp.gear:type_name -> component.game.Gear
	2,  // 18: component.game.OpenCurrencyGiftRsp.modalType:type_name -> component.game.OpenPayModalType
	15, // 19: component.game.OpenCurrencyGiftRsp.marketingGifts:type_name -> component.game.MarketingGift
	4,  // 20: component.game.OpenPackageGiftRsp.comm_rsp:type_name -> component.game.CommRsp
	16, // 21: component.game.OpenPackageGiftRsp.gear:type_name -> component.game.Gear
	18, // 22: component.game.OpenPackageGiftRsp.rewards:type_name -> component.game.PackageGiftReward
	2,  // 23: component.game.OpenPackageGiftRsp.modalType:type_name -> component.game.OpenPayModalType
	21, // 24: component.game.CkvOrderRecord.status:type_name -> component.game.OrderStatus
	8,  // 25: component.game.CkvOrderRecord.consume_info:type_name -> component.game.ConsumeInfo
	22, // 26: component.game.CkvOrderRecord.order_conf:type_name -> component.game.OrderConf
	29, // 27: component.game.CkvOrderRecord.mapExt:type_name -> component.game.CkvOrderRecord.MapExtEntry
	8,  // 28: component.game.ConsumeFlow.consumeInfo:type_name -> component.game.ConsumeInfo
	9,  // 29: component.game.Openpay.OpenPlaceOrder:input_type -> component.game.OpenPlaceOrderReq
	11, // 30: component.game.Openpay.OpenConsume:input_type -> component.game.OpenConsumeReq
	14, // 31: component.game.Openpay.OpenCurrencyGift:input_type -> component.game.OpenCurrencyGiftReq
	19, // 32: component.game.Openpay.OpenPackageGift:input_type -> component.game.OpenPackageGiftReq
	10, // 33: component.game.Openpay.OpenPlaceOrder:output_type -> component.game.OpenPlaceOrderRsp
	13, // 34: component.game.Openpay.OpenConsume:output_type -> component.game.OpenConsumeRsp
	17, // 35: component.game.Openpay.OpenCurrencyGift:output_type -> component.game.OpenCurrencyGiftRsp
	20, // 36: component.game.Openpay.OpenPackageGift:output_type -> component.game.OpenPackageGiftRsp
	33, // [33:37] is the sub-list for method output_type
	29, // [29:33] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_pb_openpay_openpay_proto_init() }
func file_pb_openpay_openpay_proto_init() {
	if File_pb_openpay_openpay_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_openpay_openpay_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Midas); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Device); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scene); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenPlaceOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenPlaceOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenConsumeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenConsumeUgc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenConsumeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenCurrencyGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarketingGift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gear); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenCurrencyGiftRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PackageGiftReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenPackageGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenPackageGiftRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CkvOrderRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CkvSnowFlake); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CkvOrderLock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_openpay_openpay_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_openpay_openpay_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_openpay_openpay_proto_goTypes,
		DependencyIndexes: file_pb_openpay_openpay_proto_depIdxs,
		EnumInfos:         file_pb_openpay_openpay_proto_enumTypes,
		MessageInfos:      file_pb_openpay_openpay_proto_msgTypes,
	}.Build()
	File_pb_openpay_openpay_proto = out.File
	file_pb_openpay_openpay_proto_rawDesc = nil
	file_pb_openpay_openpay_proto_goTypes = nil
	file_pb_openpay_openpay_proto_depIdxs = nil
}
