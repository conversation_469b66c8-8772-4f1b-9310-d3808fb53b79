{"swagger": "2.0", "info": {"title": "pb/exchange_record/exchange_record.proto", "version": "version not set"}, "tags": [{"name": "ExchangeRecord"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.ExchangeRecord/AddExchangeFlow": {"post": {"summary": "增加兑换记录", "operationId": "ExchangeRecord_AddExchangeFlow", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameAddExchangeFlowRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameAddExchangeFlowReq"}}], "tags": ["ExchangeRecord"]}}, "/component.game.ExchangeRecord/GetExchangeRecord": {"post": {"summary": "获取兑换记录列表", "operationId": "ExchangeRecord_GetExchangeRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGetExchangeRecordRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGetExchangeRecordReq"}}], "tags": ["ExchangeRecord"]}}}, "definitions": {"gameAddExchangeFlowReq": {"type": "object", "properties": {"stFlow": {"$ref": "#/definitions/gameExchangeMongoFlow", "title": "stFlow 兑换流水"}}}, "gameAddExchangeFlowRsp": {"type": "object", "properties": {"iRes": {"type": "integer", "format": "int32", "title": "iRes 返回码"}}}, "gameExchBaseInfo": {"type": "object", "properties": {"uExchangeID": {"type": "integer", "format": "int64", "title": "兑换ID"}, "lRewardID": {"type": "string", "format": "int64", "title": "奖励ID"}, "strGiftName": {"type": "string", "title": "礼物名称"}, "strGiftLogo": {"type": "string", "title": "礼物图片"}, "lPrice": {"type": "string", "format": "uint64", "title": "价格"}, "strBottomBanner": {"type": "string", "title": "底部文案"}, "stExchLimit": {"$ref": "#/definitions/gameExchLimit", "title": "兑换限制（以人为纬度）"}, "uBeginTs": {"type": "integer", "format": "int64", "title": "兑换开启时间"}, "uEndTs": {"type": "integer", "format": "int64", "title": "兑换结束时间"}, "lTotalStock": {"type": "string", "format": "int64", "title": "库存 -1为无限"}, "uTreasureLevel": {"type": "integer", "format": "int64", "title": "财富等级限制"}, "strLimitTips": {"type": "string", "title": "兑换限制文案"}, "stTotalLimit": {"$ref": "#/definitions/gameExchLimit", "title": "累计限制"}, "strTopRightLabel": {"type": "string", "title": "右上角标签"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展字段"}, "uExchangeType": {"$ref": "#/definitions/gameExchangeType", "title": "兑换类型"}, "detailLogo": {"type": "array", "items": {"type": "string"}, "title": "详细图片列表"}, "detailDesc": {"type": "string", "title": "详细文案"}, "isTimeLimit": {"type": "boolean", "title": "是否限时"}, "isPreview": {"type": "boolean", "title": "是否预览"}, "giftId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "礼物id"}}}, "gameExchLimit": {"type": "object", "properties": {"iDayLimit": {"type": "string", "format": "int64", "title": "日限"}, "iWeekLimit": {"type": "string", "format": "int64", "title": "周限"}, "iMonthLimit": {"type": "string", "format": "int64", "title": "月限"}, "iTotalLimit": {"type": "string", "format": "int64", "title": "总限"}}}, "gameExchRecord": {"type": "object", "properties": {"stExchInfo": {"$ref": "#/definitions/gameExchBaseInfo", "title": "兑换基本信息"}, "uNum": {"type": "integer", "format": "int64", "title": "兑换数量"}, "lPay": {"type": "string", "format": "uint64", "title": "支付价格"}, "uExchTs": {"type": "integer", "format": "int64", "title": "兑换时间"}}}, "gameExchangeMongoFlow": {"type": "object", "properties": {"strAppID": {"type": "string", "title": "strAppID"}, "strOpenID": {"type": "string", "title": "strOpenID"}, "stInfo": {"$ref": "#/definitions/gameExchBaseInfo", "title": "stInfo 兑换物品基本信息"}, "uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}, "uNum": {"type": "integer", "format": "int64", "title": "uNum 数量"}, "lPay": {"type": "string", "format": "uint64", "title": "lPay 支付价格"}, "uExchTs": {"type": "integer", "format": "int64", "title": "uExchTs 兑换时间"}, "strUniqueId": {"type": "string", "title": "strUniqueId 唯一ID"}}}, "gameExchangeType": {"type": "string", "enum": ["ExchangeGift", "ExchangeAsset"], "default": "ExchangeGift"}, "gameGetExchangeRecordReq": {"type": "object", "properties": {"strAppID": {"type": "string", "title": "strAppID"}, "strOpenID": {"type": "string", "title": "strOpenID"}, "uActID": {"type": "integer", "format": "int64", "title": "uActID 活动ID"}, "strPassBack": {"type": "string", "title": "strPassBack"}}}, "gameGetExchangeRecordRsp": {"type": "object", "properties": {"bHasMore": {"type": "boolean", "title": "bHasMore"}, "strPassBack": {"type": "string", "title": "strPassBack"}, "vctRecord": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExchRecord"}, "title": "vctRecord 兑换记录"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}