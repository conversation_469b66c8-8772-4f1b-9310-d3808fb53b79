// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/exchange_record/exchange_record.proto

package exchange_record

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	exchange "kugou_adapter_service/pkg/gen/proto/pb/exchange"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExchangeMongoFlow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// strAppID
	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`
	// strOpenID
	StrOpenID string `protobuf:"bytes,2,opt,name=strOpenID,proto3" json:"strOpenID,omitempty"`
	// stInfo 兑换物品基本信息
	StInfo *exchange.ExchBaseInfo `protobuf:"bytes,3,opt,name=stInfo,proto3" json:"stInfo,omitempty"`
	// uActID 活动ID
	UActID uint32 `protobuf:"varint,4,opt,name=uActID,proto3" json:"uActID,omitempty"`
	// uNum 数量
	UNum uint32 `protobuf:"varint,5,opt,name=uNum,proto3" json:"uNum,omitempty"`
	// lPay 支付价格
	LPay uint64 `protobuf:"varint,6,opt,name=lPay,proto3" json:"lPay,omitempty"`
	// uExchTs 兑换时间
	UExchTs uint32 `protobuf:"varint,7,opt,name=uExchTs,proto3" json:"uExchTs,omitempty"`
	// strUniqueId 唯一ID
	StrUniqueId string `protobuf:"bytes,8,opt,name=strUniqueId,proto3" json:"strUniqueId,omitempty"`
}

func (x *ExchangeMongoFlow) Reset() {
	*x = ExchangeMongoFlow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeMongoFlow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeMongoFlow) ProtoMessage() {}

func (x *ExchangeMongoFlow) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeMongoFlow.ProtoReflect.Descriptor instead.
func (*ExchangeMongoFlow) Descriptor() ([]byte, []int) {
	return file_pb_exchange_record_exchange_record_proto_rawDescGZIP(), []int{0}
}

func (x *ExchangeMongoFlow) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *ExchangeMongoFlow) GetStrOpenID() string {
	if x != nil {
		return x.StrOpenID
	}
	return ""
}

func (x *ExchangeMongoFlow) GetStInfo() *exchange.ExchBaseInfo {
	if x != nil {
		return x.StInfo
	}
	return nil
}

func (x *ExchangeMongoFlow) GetUActID() uint32 {
	if x != nil {
		return x.UActID
	}
	return 0
}

func (x *ExchangeMongoFlow) GetUNum() uint32 {
	if x != nil {
		return x.UNum
	}
	return 0
}

func (x *ExchangeMongoFlow) GetLPay() uint64 {
	if x != nil {
		return x.LPay
	}
	return 0
}

func (x *ExchangeMongoFlow) GetUExchTs() uint32 {
	if x != nil {
		return x.UExchTs
	}
	return 0
}

func (x *ExchangeMongoFlow) GetStrUniqueId() string {
	if x != nil {
		return x.StrUniqueId
	}
	return ""
}

type AddExchangeFlowReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stFlow 兑换流水
	StFlow *ExchangeMongoFlow `protobuf:"bytes,1,opt,name=stFlow,proto3" json:"stFlow,omitempty"`
}

func (x *AddExchangeFlowReq) Reset() {
	*x = AddExchangeFlowReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExchangeFlowReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExchangeFlowReq) ProtoMessage() {}

func (x *AddExchangeFlowReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExchangeFlowReq.ProtoReflect.Descriptor instead.
func (*AddExchangeFlowReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_record_exchange_record_proto_rawDescGZIP(), []int{1}
}

func (x *AddExchangeFlowReq) GetStFlow() *ExchangeMongoFlow {
	if x != nil {
		return x.StFlow
	}
	return nil
}

type AddExchangeFlowRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// iRes 返回码
	IRes int32 `protobuf:"varint,1,opt,name=iRes,proto3" json:"iRes,omitempty"`
}

func (x *AddExchangeFlowRsp) Reset() {
	*x = AddExchangeFlowRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddExchangeFlowRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddExchangeFlowRsp) ProtoMessage() {}

func (x *AddExchangeFlowRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddExchangeFlowRsp.ProtoReflect.Descriptor instead.
func (*AddExchangeFlowRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_record_exchange_record_proto_rawDescGZIP(), []int{2}
}

func (x *AddExchangeFlowRsp) GetIRes() int32 {
	if x != nil {
		return x.IRes
	}
	return 0
}

type GetExchangeRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// strAppID
	StrAppID string `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`
	// strOpenID
	StrOpenID string `protobuf:"bytes,2,opt,name=strOpenID,proto3" json:"strOpenID,omitempty"`
	// uActID 活动ID
	UActID uint32 `protobuf:"varint,3,opt,name=uActID,proto3" json:"uActID,omitempty"`
	// strPassBack
	StrPassBack string `protobuf:"bytes,4,opt,name=strPassBack,proto3" json:"strPassBack,omitempty"`
}

func (x *GetExchangeRecordReq) Reset() {
	*x = GetExchangeRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeRecordReq) ProtoMessage() {}

func (x *GetExchangeRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeRecordReq.ProtoReflect.Descriptor instead.
func (*GetExchangeRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_exchange_record_exchange_record_proto_rawDescGZIP(), []int{3}
}

func (x *GetExchangeRecordReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetExchangeRecordReq) GetStrOpenID() string {
	if x != nil {
		return x.StrOpenID
	}
	return ""
}

func (x *GetExchangeRecordReq) GetUActID() uint32 {
	if x != nil {
		return x.UActID
	}
	return 0
}

func (x *GetExchangeRecordReq) GetStrPassBack() string {
	if x != nil {
		return x.StrPassBack
	}
	return ""
}

type GetExchangeRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// bHasMore
	BHasMore bool `protobuf:"varint,1,opt,name=bHasMore,proto3" json:"bHasMore,omitempty"`
	// strPassBack
	StrPassBack string `protobuf:"bytes,2,opt,name=strPassBack,proto3" json:"strPassBack,omitempty"`
	// vctRecord 兑换记录
	VctRecord []*exchange.ExchRecord `protobuf:"bytes,3,rep,name=vctRecord,proto3" json:"vctRecord,omitempty"`
}

func (x *GetExchangeRecordRsp) Reset() {
	*x = GetExchangeRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeRecordRsp) ProtoMessage() {}

func (x *GetExchangeRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_exchange_record_exchange_record_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeRecordRsp.ProtoReflect.Descriptor instead.
func (*GetExchangeRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_exchange_record_exchange_record_proto_rawDescGZIP(), []int{4}
}

func (x *GetExchangeRecordRsp) GetBHasMore() bool {
	if x != nil {
		return x.BHasMore
	}
	return false
}

func (x *GetExchangeRecordRsp) GetStrPassBack() string {
	if x != nil {
		return x.StrPassBack
	}
	return ""
}

func (x *GetExchangeRecordRsp) GetVctRecord() []*exchange.ExchRecord {
	if x != nil {
		return x.VctRecord
	}
	return nil
}

var File_pb_exchange_record_exchange_record_proto protoreflect.FileDescriptor

var file_pb_exchange_record_exchange_record_proto_rawDesc = []byte{
	0x0a, 0x28, 0x70, 0x62, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1f, 0x70, 0x62, 0x2f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x01, 0x0a, 0x11,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x6e, 0x67, 0x6f, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x34, 0x0a, 0x06, 0x73,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x75, 0x41, 0x63, 0x74, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x4e, 0x75,
	0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x75, 0x4e, 0x75, 0x6d, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x50, 0x61, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x6c, 0x50, 0x61,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x45, 0x78, 0x63, 0x68, 0x54, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x75, 0x45, 0x78, 0x63, 0x68, 0x54, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73,
	0x74, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x22, 0x4f, 0x0a,
	0x12, 0x41, 0x64, 0x64, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x6f, 0x6e,
	0x67, 0x6f, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x06, 0x73, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x22, 0x28,
	0x0a, 0x12, 0x41, 0x64, 0x64, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x52, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x69, 0x52, 0x65, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x41, 0x63, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x75, 0x41, 0x63,
	0x74, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61,
	0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x50, 0x61, 0x73,
	0x73, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x8e, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x62, 0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x62, 0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74,
	0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x38, 0x0a, 0x09,
	0x76, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x45, 0x78, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x09, 0x76, 0x63, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x32, 0xcc, 0x01, 0x0a, 0x0e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x59, 0x0a, 0x0f, 0x41, 0x64, 0x64,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x22, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64,
	0x64, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x41, 0x64, 0x64, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x6c, 0x6f,
	0x77, 0x52, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x73, 0x70, 0x42, 0x47, 0x5a, 0x45, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_exchange_record_exchange_record_proto_rawDescOnce sync.Once
	file_pb_exchange_record_exchange_record_proto_rawDescData = file_pb_exchange_record_exchange_record_proto_rawDesc
)

func file_pb_exchange_record_exchange_record_proto_rawDescGZIP() []byte {
	file_pb_exchange_record_exchange_record_proto_rawDescOnce.Do(func() {
		file_pb_exchange_record_exchange_record_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_exchange_record_exchange_record_proto_rawDescData)
	})
	return file_pb_exchange_record_exchange_record_proto_rawDescData
}

var file_pb_exchange_record_exchange_record_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_pb_exchange_record_exchange_record_proto_goTypes = []interface{}{
	(*ExchangeMongoFlow)(nil),     // 0: component.game.ExchangeMongoFlow
	(*AddExchangeFlowReq)(nil),    // 1: component.game.AddExchangeFlowReq
	(*AddExchangeFlowRsp)(nil),    // 2: component.game.AddExchangeFlowRsp
	(*GetExchangeRecordReq)(nil),  // 3: component.game.GetExchangeRecordReq
	(*GetExchangeRecordRsp)(nil),  // 4: component.game.GetExchangeRecordRsp
	(*exchange.ExchBaseInfo)(nil), // 5: component.game.ExchBaseInfo
	(*exchange.ExchRecord)(nil),   // 6: component.game.ExchRecord
}
var file_pb_exchange_record_exchange_record_proto_depIdxs = []int32{
	5, // 0: component.game.ExchangeMongoFlow.stInfo:type_name -> component.game.ExchBaseInfo
	0, // 1: component.game.AddExchangeFlowReq.stFlow:type_name -> component.game.ExchangeMongoFlow
	6, // 2: component.game.GetExchangeRecordRsp.vctRecord:type_name -> component.game.ExchRecord
	1, // 3: component.game.ExchangeRecord.AddExchangeFlow:input_type -> component.game.AddExchangeFlowReq
	3, // 4: component.game.ExchangeRecord.GetExchangeRecord:input_type -> component.game.GetExchangeRecordReq
	2, // 5: component.game.ExchangeRecord.AddExchangeFlow:output_type -> component.game.AddExchangeFlowRsp
	4, // 6: component.game.ExchangeRecord.GetExchangeRecord:output_type -> component.game.GetExchangeRecordRsp
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_exchange_record_exchange_record_proto_init() }
func file_pb_exchange_record_exchange_record_proto_init() {
	if File_pb_exchange_record_exchange_record_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_exchange_record_exchange_record_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeMongoFlow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_record_exchange_record_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExchangeFlowReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_record_exchange_record_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddExchangeFlowRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_record_exchange_record_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_exchange_record_exchange_record_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_exchange_record_exchange_record_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_exchange_record_exchange_record_proto_goTypes,
		DependencyIndexes: file_pb_exchange_record_exchange_record_proto_depIdxs,
		MessageInfos:      file_pb_exchange_record_exchange_record_proto_msgTypes,
	}.Build()
	File_pb_exchange_record_exchange_record_proto = out.File
	file_pb_exchange_record_exchange_record_proto_rawDesc = nil
	file_pb_exchange_record_exchange_record_proto_goTypes = nil
	file_pb_exchange_record_exchange_record_proto_depIdxs = nil
}
