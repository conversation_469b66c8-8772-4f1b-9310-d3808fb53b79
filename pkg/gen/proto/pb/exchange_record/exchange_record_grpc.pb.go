// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/exchange_record/exchange_record.proto

package exchange_record

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ExchangeRecord_AddExchangeFlow_FullMethodName   = "/component.game.ExchangeRecord/AddExchangeFlow"
	ExchangeRecord_GetExchangeRecord_FullMethodName = "/component.game.ExchangeRecord/GetExchangeRecord"
)

// ExchangeRecordClient is the client API for ExchangeRecord service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExchangeRecordClient interface {
	// 增加兑换记录
	AddExchangeFlow(ctx context.Context, in *AddExchangeFlowReq, opts ...grpc.CallOption) (*AddExchangeFlowRsp, error)
	// 获取兑换记录列表
	GetExchangeRecord(ctx context.Context, in *GetExchangeRecordReq, opts ...grpc.CallOption) (*GetExchangeRecordRsp, error)
}

type exchangeRecordClient struct {
	cc grpc.ClientConnInterface
}

func NewExchangeRecordClient(cc grpc.ClientConnInterface) ExchangeRecordClient {
	return &exchangeRecordClient{cc}
}

func (c *exchangeRecordClient) AddExchangeFlow(ctx context.Context, in *AddExchangeFlowReq, opts ...grpc.CallOption) (*AddExchangeFlowRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddExchangeFlowRsp)
	err := c.cc.Invoke(ctx, ExchangeRecord_AddExchangeFlow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *exchangeRecordClient) GetExchangeRecord(ctx context.Context, in *GetExchangeRecordReq, opts ...grpc.CallOption) (*GetExchangeRecordRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExchangeRecordRsp)
	err := c.cc.Invoke(ctx, ExchangeRecord_GetExchangeRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExchangeRecordServer is the server API for ExchangeRecord service.
// All implementations should embed UnimplementedExchangeRecordServer
// for forward compatibility
type ExchangeRecordServer interface {
	// 增加兑换记录
	AddExchangeFlow(context.Context, *AddExchangeFlowReq) (*AddExchangeFlowRsp, error)
	// 获取兑换记录列表
	GetExchangeRecord(context.Context, *GetExchangeRecordReq) (*GetExchangeRecordRsp, error)
}

// UnimplementedExchangeRecordServer should be embedded to have forward compatible implementations.
type UnimplementedExchangeRecordServer struct {
}

func (UnimplementedExchangeRecordServer) AddExchangeFlow(context.Context, *AddExchangeFlowReq) (*AddExchangeFlowRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddExchangeFlow not implemented")
}
func (UnimplementedExchangeRecordServer) GetExchangeRecord(context.Context, *GetExchangeRecordReq) (*GetExchangeRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExchangeRecord not implemented")
}

// UnsafeExchangeRecordServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExchangeRecordServer will
// result in compilation errors.
type UnsafeExchangeRecordServer interface {
	mustEmbedUnimplementedExchangeRecordServer()
}

func RegisterExchangeRecordServer(s grpc.ServiceRegistrar, srv ExchangeRecordServer) {
	s.RegisterService(&ExchangeRecord_ServiceDesc, srv)
}

func _ExchangeRecord_AddExchangeFlow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddExchangeFlowReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeRecordServer).AddExchangeFlow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExchangeRecord_AddExchangeFlow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeRecordServer).AddExchangeFlow(ctx, req.(*AddExchangeFlowReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExchangeRecord_GetExchangeRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExchangeRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExchangeRecordServer).GetExchangeRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExchangeRecord_GetExchangeRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExchangeRecordServer).GetExchangeRecord(ctx, req.(*GetExchangeRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ExchangeRecord_ServiceDesc is the grpc.ServiceDesc for ExchangeRecord service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExchangeRecord_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.ExchangeRecord",
	HandlerType: (*ExchangeRecordServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddExchangeFlow",
			Handler:    _ExchangeRecord_AddExchangeFlow_Handler,
		},
		{
			MethodName: "GetExchangeRecord",
			Handler:    _ExchangeRecord_GetExchangeRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/exchange_record/exchange_record.proto",
}
