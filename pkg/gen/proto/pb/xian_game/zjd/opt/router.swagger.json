{"swagger": "2.0", "info": {"title": "pb/xian_game/zjd/opt/router.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_zjd.Opt/SyncOpt": {"post": {"summary": "配置中心", "operationId": "Opt_SyncOpt", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdSyncOptRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdSyncOptReq"}}], "tags": ["<PERSON><PERSON>"]}}}, "definitions": {"game_zjdSyncOptReq": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台"}, "name": {"type": "string", "title": "key名"}, "data": {"type": "string", "title": "数据"}}, "title": "SyncOptReq 配置中心"}, "game_zjdSyncOptRsp": {"type": "object", "properties": {"type": {"type": "string", "title": "类型 'success' | 'warning' | 'error';"}, "msg": {"type": "string", "title": "string;"}}, "title": "SyncOptRsp 配置中心"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}