// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_game/zjd/comm/comm.proto

package comm

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrCode int32

const (
	ErrCode_Code_Success ErrCode = 0
	// 内部错误
	ErrCode_Err_Net    ErrCode = 1100 // 内部网络错误
	ErrCode_Err_DB     ErrCode = 1101 // 数据库错误
	ErrCode_Err_CKV    ErrCode = 1102 // CKV错误
	ErrCode_Err_OptErr ErrCode = 1103 // 配置有误
	// 锁
	ErrCode_Err_LockFail ErrCode = 1150 // 并发锁定失败
	// 业务错误
	ErrCode_Err_ActNotAllowed        ErrCode = 2000 // 当前操作不允许
	ErrCode_Err_ClientData           ErrCode = 2001 // 客户端数据错误
	ErrCode_Err_EmptyUID             ErrCode = 2002 // 用户uid为空
	ErrCode_Err_AssetNotEnough       ErrCode = 2003 // 资产不足
	ErrCode_Err_PayErr               ErrCode = 2004 // 调用Pay服务异常
	ErrCode_Err_PayPending           ErrCode = 2005 // 支付等待中
	ErrCode_Err_EmptyConsumeID       ErrCode = 2006 // 空订单
	ErrCode_Err_AdCheck              ErrCode = 2007 // 广告验证失败
	ErrCode_Err_RepeatAdToken        ErrCode = 2008 // 重复adToken
	ErrCode_Err_AdTraceIdEmpty       ErrCode = 2009 // 广告traceID为空
	ErrCode_Err_AdCntNotEnough       ErrCode = 2010 // 广告次数不满足
	ErrCode_Err_LevelRewardNotEnough ErrCode = 2011 // 未达到领取条件
	ErrCode_Err_LevelStatusIsBoom    ErrCode = 2012 // 当前关卡处于被炸状态,需要复活
	ErrCode_Err_LevelIsMax           ErrCode = 2014 // 已到达最大关卡
	ErrCode_Err_DayPlayCntUpperLimit ErrCode = 2015 // 用户每日闯关次数达到上限
	ErrCode_Err_NoPlayGame           ErrCode = 2016 // 未在游戏中
	ErrCode_Err_PlayGameNoBoomAD     ErrCode = 2017 // 未处于广告复活场景中
	ErrCode_Err_PlayFinish           ErrCode = 2018 // 已完成当前所有关卡
	ErrCode_Err_TransactionId        ErrCode = 2019 // 错误的交易ID
	ErrCode_Err_GearNotExists        ErrCode = 2020 // 档位选择错误
	ErrCode_Err_MethodArgsErr        ErrCode = 2022 // 方法参数异常
	ErrCode_Err_SmashLevelRepeat     ErrCode = 2023 // 当前关卡已消耗
	ErrCode_Err_SmashBoomNoReceive   ErrCode = 2024 // 处于被炸状态无法领奖
	ErrCode_Err_AdViewCntRepeated    ErrCode = 2025 // 广告次数重复
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:    "Code_Success",
		1100: "Err_Net",
		1101: "Err_DB",
		1102: "Err_CKV",
		1103: "Err_OptErr",
		1150: "Err_LockFail",
		2000: "Err_ActNotAllowed",
		2001: "Err_ClientData",
		2002: "Err_EmptyUID",
		2003: "Err_AssetNotEnough",
		2004: "Err_PayErr",
		2005: "Err_PayPending",
		2006: "Err_EmptyConsumeID",
		2007: "Err_AdCheck",
		2008: "Err_RepeatAdToken",
		2009: "Err_AdTraceIdEmpty",
		2010: "Err_AdCntNotEnough",
		2011: "Err_LevelRewardNotEnough",
		2012: "Err_LevelStatusIsBoom",
		2014: "Err_LevelIsMax",
		2015: "Err_DayPlayCntUpperLimit",
		2016: "Err_NoPlayGame",
		2017: "Err_PlayGameNoBoomAD",
		2018: "Err_PlayFinish",
		2019: "Err_TransactionId",
		2020: "Err_GearNotExists",
		2022: "Err_MethodArgsErr",
		2023: "Err_SmashLevelRepeat",
		2024: "Err_SmashBoomNoReceive",
		2025: "Err_AdViewCntRepeated",
	}
	ErrCode_value = map[string]int32{
		"Code_Success":             0,
		"Err_Net":                  1100,
		"Err_DB":                   1101,
		"Err_CKV":                  1102,
		"Err_OptErr":               1103,
		"Err_LockFail":             1150,
		"Err_ActNotAllowed":        2000,
		"Err_ClientData":           2001,
		"Err_EmptyUID":             2002,
		"Err_AssetNotEnough":       2003,
		"Err_PayErr":               2004,
		"Err_PayPending":           2005,
		"Err_EmptyConsumeID":       2006,
		"Err_AdCheck":              2007,
		"Err_RepeatAdToken":        2008,
		"Err_AdTraceIdEmpty":       2009,
		"Err_AdCntNotEnough":       2010,
		"Err_LevelRewardNotEnough": 2011,
		"Err_LevelStatusIsBoom":    2012,
		"Err_LevelIsMax":           2014,
		"Err_DayPlayCntUpperLimit": 2015,
		"Err_NoPlayGame":           2016,
		"Err_PlayGameNoBoomAD":     2017,
		"Err_PlayFinish":           2018,
		"Err_TransactionId":        2019,
		"Err_GearNotExists":        2020,
		"Err_MethodArgsErr":        2022,
		"Err_SmashLevelRepeat":     2023,
		"Err_SmashBoomNoReceive":   2024,
		"Err_AdViewCntRepeated":    2025,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_zjd_comm_comm_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_pb_xian_game_zjd_comm_comm_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_comm_comm_proto_rawDescGZIP(), []int{0}
}

type TradeStatusType int32

const (
	TradeStatusType_TS_Init        TradeStatusType = 0  // 默认状态(初次保存)
	TradeStatusType_TS_Pay         TradeStatusType = 1  // 开启支付
	TradeStatusType_TS_Pay_Pending TradeStatusType = 2  // 支付进行中(Pay接口未明确返回错误)
	TradeStatusType_TS_Pay_Success TradeStatusType = 3  // 支付成功(Pay接口明确返回OK) 开始真正扣除库存
	TradeStatusType_TS_Pay_Error   TradeStatusType = 4  // 支付失败(Pay接口明确返回失败)
	TradeStatusType_TS_Pay_Unknown TradeStatusType = 5  // 支付网络失败
	TradeStatusType_TS_ChanceRand  TradeStatusType = 7  // 概率计算
	TradeStatusType_TS_LevelSet    TradeStatusType = 8  // 关卡构建
	TradeStatusType_TS_End         TradeStatusType = 20 // 所有流程执行完成
)

// Enum value maps for TradeStatusType.
var (
	TradeStatusType_name = map[int32]string{
		0:  "TS_Init",
		1:  "TS_Pay",
		2:  "TS_Pay_Pending",
		3:  "TS_Pay_Success",
		4:  "TS_Pay_Error",
		5:  "TS_Pay_Unknown",
		7:  "TS_ChanceRand",
		8:  "TS_LevelSet",
		20: "TS_End",
	}
	TradeStatusType_value = map[string]int32{
		"TS_Init":        0,
		"TS_Pay":         1,
		"TS_Pay_Pending": 2,
		"TS_Pay_Success": 3,
		"TS_Pay_Error":   4,
		"TS_Pay_Unknown": 5,
		"TS_ChanceRand":  7,
		"TS_LevelSet":    8,
		"TS_End":         20,
	}
)

func (x TradeStatusType) Enum() *TradeStatusType {
	p := new(TradeStatusType)
	*p = x
	return p
}

func (x TradeStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TradeStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_zjd_comm_comm_proto_enumTypes[1].Descriptor()
}

func (TradeStatusType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_zjd_comm_comm_proto_enumTypes[1]
}

func (x TradeStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TradeStatusType.Descriptor instead.
func (TradeStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_comm_comm_proto_rawDescGZIP(), []int{1}
}

type Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 执行结果code码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误消息
}

func (x *Error) Reset() {
	*x = Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_comm_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_comm_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_comm_comm_proto_rawDescGZIP(), []int{0}
}

func (x *Error) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Error) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type RewardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftId        string `protobuf:"bytes,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`                         // 礼物编码 子商品ID 小礼包不会使用
	GiftType      uint32 `protobuf:"varint,2,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`                  // 类型
	GiftNum       int64  `protobuf:"varint,3,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`                     // 名称
	GiftName      string `protobuf:"bytes,4,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`                   // logo
	GiftLogo      string `protobuf:"bytes,5,opt,name=gift_logo,json=giftLogo,proto3" json:"gift_logo,omitempty"`                   // 数量
	GiftUnitPrice uint32 `protobuf:"varint,6,opt,name=gift_unit_price,json=giftUnitPrice,proto3" json:"gift_unit_price,omitempty"` // 礼物价值，配置接口有用
	GiftPlatType  string `protobuf:"bytes,7,opt,name=gift_plat_type,json=giftPlatType,proto3" json:"gift_plat_type,omitempty"`     // 透传透传宿主平台子奖品类型
}

func (x *RewardInfo) Reset() {
	*x = RewardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_comm_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardInfo) ProtoMessage() {}

func (x *RewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_comm_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardInfo.ProtoReflect.Descriptor instead.
func (*RewardInfo) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_comm_comm_proto_rawDescGZIP(), []int{1}
}

func (x *RewardInfo) GetGiftId() string {
	if x != nil {
		return x.GiftId
	}
	return ""
}

func (x *RewardInfo) GetGiftType() uint32 {
	if x != nil {
		return x.GiftType
	}
	return 0
}

func (x *RewardInfo) GetGiftNum() int64 {
	if x != nil {
		return x.GiftNum
	}
	return 0
}

func (x *RewardInfo) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *RewardInfo) GetGiftLogo() string {
	if x != nil {
		return x.GiftLogo
	}
	return ""
}

func (x *RewardInfo) GetGiftUnitPrice() uint32 {
	if x != nil {
		return x.GiftUnitPrice
	}
	return 0
}

func (x *RewardInfo) GetGiftPlatType() string {
	if x != nil {
		return x.GiftPlatType
	}
	return ""
}

var File_pb_xian_game_zjd_comm_comm_proto protoreflect.FileDescriptor

var file_pb_xian_game_zjd_comm_comm_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a,
	0x6a, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x22, 0x2d, 0x0a, 0x05,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0xe5, 0x01, 0x0a, 0x0a,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x66,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x69, 0x66, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x67, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74,
	0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66,
	0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d,
	0x67, 0x69, 0x66, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x69, 0x66, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x2a, 0xac, 0x05, 0x0a, 0x07, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x5f, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x07, 0x45, 0x72, 0x72, 0x5f, 0x4e, 0x65, 0x74, 0x10, 0xcc, 0x08, 0x12,
	0x0b, 0x0a, 0x06, 0x45, 0x72, 0x72, 0x5f, 0x44, 0x42, 0x10, 0xcd, 0x08, 0x12, 0x0c, 0x0a, 0x07,
	0x45, 0x72, 0x72, 0x5f, 0x43, 0x4b, 0x56, 0x10, 0xce, 0x08, 0x12, 0x0f, 0x0a, 0x0a, 0x45, 0x72,
	0x72, 0x5f, 0x4f, 0x70, 0x74, 0x45, 0x72, 0x72, 0x10, 0xcf, 0x08, 0x12, 0x11, 0x0a, 0x0c, 0x45,
	0x72, 0x72, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x10, 0xfe, 0x08, 0x12, 0x16,
	0x0a, 0x11, 0x45, 0x72, 0x72, 0x5f, 0x41, 0x63, 0x74, 0x4e, 0x6f, 0x74, 0x41, 0x6c, 0x6c, 0x6f,
	0x77, 0x65, 0x64, 0x10, 0xd0, 0x0f, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x72, 0x72, 0x5f, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x10, 0xd1, 0x0f, 0x12, 0x11, 0x0a, 0x0c, 0x45,
	0x72, 0x72, 0x5f, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x55, 0x49, 0x44, 0x10, 0xd2, 0x0f, 0x12, 0x17,
	0x0a, 0x12, 0x45, 0x72, 0x72, 0x5f, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x6e,
	0x6f, 0x75, 0x67, 0x68, 0x10, 0xd3, 0x0f, 0x12, 0x0f, 0x0a, 0x0a, 0x45, 0x72, 0x72, 0x5f, 0x50,
	0x61, 0x79, 0x45, 0x72, 0x72, 0x10, 0xd4, 0x0f, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x72, 0x72, 0x5f,
	0x50, 0x61, 0x79, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0xd5, 0x0f, 0x12, 0x17, 0x0a,
	0x12, 0x45, 0x72, 0x72, 0x5f, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x49, 0x44, 0x10, 0xd6, 0x0f, 0x12, 0x10, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x5f, 0x41, 0x64,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x10, 0xd7, 0x0f, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x5f,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x10, 0xd8, 0x0f,
	0x12, 0x17, 0x0a, 0x12, 0x45, 0x72, 0x72, 0x5f, 0x41, 0x64, 0x54, 0x72, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x10, 0xd9, 0x0f, 0x12, 0x17, 0x0a, 0x12, 0x45, 0x72, 0x72,
	0x5f, 0x41, 0x64, 0x43, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10,
	0xda, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x72, 0x72, 0x5f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xdb,
	0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x72, 0x72, 0x5f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x49, 0x73, 0x42, 0x6f, 0x6f, 0x6d, 0x10, 0xdc, 0x0f, 0x12, 0x13, 0x0a,
	0x0e, 0x45, 0x72, 0x72, 0x5f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x73, 0x4d, 0x61, 0x78, 0x10,
	0xde, 0x0f, 0x12, 0x1d, 0x0a, 0x18, 0x45, 0x72, 0x72, 0x5f, 0x44, 0x61, 0x79, 0x50, 0x6c, 0x61,
	0x79, 0x43, 0x6e, 0x74, 0x55, 0x70, 0x70, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xdf,
	0x0f, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x72, 0x72, 0x5f, 0x4e, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x47,
	0x61, 0x6d, 0x65, 0x10, 0xe0, 0x0f, 0x12, 0x19, 0x0a, 0x14, 0x45, 0x72, 0x72, 0x5f, 0x50, 0x6c,
	0x61, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x4e, 0x6f, 0x42, 0x6f, 0x6f, 0x6d, 0x41, 0x44, 0x10, 0xe1,
	0x0f, 0x12, 0x13, 0x0a, 0x0e, 0x45, 0x72, 0x72, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x46, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x10, 0xe2, 0x0f, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x5f, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x10, 0xe3, 0x0f, 0x12, 0x16,
	0x0a, 0x11, 0x45, 0x72, 0x72, 0x5f, 0x47, 0x65, 0x61, 0x72, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x73, 0x10, 0xe4, 0x0f, 0x12, 0x16, 0x0a, 0x11, 0x45, 0x72, 0x72, 0x5f, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x41, 0x72, 0x67, 0x73, 0x45, 0x72, 0x72, 0x10, 0xe6, 0x0f, 0x12, 0x19,
	0x0a, 0x14, 0x45, 0x72, 0x72, 0x5f, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x10, 0xe7, 0x0f, 0x12, 0x1b, 0x0a, 0x16, 0x45, 0x72, 0x72,
	0x5f, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x6f, 0x6d, 0x4e, 0x6f, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x10, 0xe8, 0x0f, 0x12, 0x1a, 0x0a, 0x15, 0x45, 0x72, 0x72, 0x5f, 0x41, 0x64,
	0x56, 0x69, 0x65, 0x77, 0x43, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x10,
	0xe9, 0x0f, 0x2a, 0xa8, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x53, 0x5f, 0x49, 0x6e, 0x69,
	0x74, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x10, 0x01, 0x12,
	0x12, 0x0a, 0x0e, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x5f, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x5f, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x53, 0x5f, 0x50, 0x61,
	0x79, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x53, 0x5f,
	0x50, 0x61, 0x79, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x05, 0x12, 0x11, 0x0a,
	0x0d, 0x54, 0x53, 0x5f, 0x43, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x61, 0x6e, 0x64, 0x10, 0x07,
	0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x53, 0x5f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x65, 0x74, 0x10,
	0x08, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x53, 0x5f, 0x45, 0x6e, 0x64, 0x10, 0x14, 0x42, 0x4d, 0x5a,
	0x4b, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70,
	0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a, 0x6a, 0x64, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x7a, 0x6a, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_xian_game_zjd_comm_comm_proto_rawDescOnce sync.Once
	file_pb_xian_game_zjd_comm_comm_proto_rawDescData = file_pb_xian_game_zjd_comm_comm_proto_rawDesc
)

func file_pb_xian_game_zjd_comm_comm_proto_rawDescGZIP() []byte {
	file_pb_xian_game_zjd_comm_comm_proto_rawDescOnce.Do(func() {
		file_pb_xian_game_zjd_comm_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_game_zjd_comm_comm_proto_rawDescData)
	})
	return file_pb_xian_game_zjd_comm_comm_proto_rawDescData
}

var file_pb_xian_game_zjd_comm_comm_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_xian_game_zjd_comm_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_xian_game_zjd_comm_comm_proto_goTypes = []interface{}{
	(ErrCode)(0),         // 0: game_zjd.ErrCode
	(TradeStatusType)(0), // 1: game_zjd.TradeStatusType
	(*Error)(nil),        // 2: game_zjd.Error
	(*RewardInfo)(nil),   // 3: game_zjd.RewardInfo
}
var file_pb_xian_game_zjd_comm_comm_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_xian_game_zjd_comm_comm_proto_init() }
func file_pb_xian_game_zjd_comm_comm_proto_init() {
	if File_pb_xian_game_zjd_comm_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_game_zjd_comm_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_comm_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_game_zjd_comm_comm_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_xian_game_zjd_comm_comm_proto_goTypes,
		DependencyIndexes: file_pb_xian_game_zjd_comm_comm_proto_depIdxs,
		EnumInfos:         file_pb_xian_game_zjd_comm_comm_proto_enumTypes,
		MessageInfos:      file_pb_xian_game_zjd_comm_comm_proto_msgTypes,
	}.Build()
	File_pb_xian_game_zjd_comm_comm_proto = out.File
	file_pb_xian_game_zjd_comm_comm_proto_rawDesc = nil
	file_pb_xian_game_zjd_comm_comm_proto_goTypes = nil
	file_pb_xian_game_zjd_comm_comm_proto_depIdxs = nil
}
