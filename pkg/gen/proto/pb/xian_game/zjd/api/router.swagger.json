{"swagger": "2.0", "info": {"title": "pb/xian_game/zjd/api/router.proto", "version": "version not set"}, "tags": [{"name": "Api"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_zjd.Api/AdCheck": {"post": {"summary": "广告", "description": "观看完广告调用", "operationId": "Api_AdCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdAdCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdAdCheckReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/AdRevives": {"post": {"summary": "最后一次复活", "operationId": "Api_AdRevives", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdAdRevivesRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdAdRevivesReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/Cfg": {"post": {"summary": "配置中心", "operationId": "Api_Cfg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdCfgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdCfgReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/CoinPay": {"post": {"summary": "金币支付", "operationId": "Api_CoinPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdCoinPayRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdCoinPayReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/CoinPayCallback": {"post": {"summary": "-----\n-----下面的接口需要在网关中禁止请求,仅支持内部调用,前端可忽略-----\n-----", "description": "金币支付接口回调[中台回调游戏,网关需要禁止访问]", "operationId": "Api_CoinPayCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/callbackOrderShipmentRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/callbackOrderShipmentReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/CoinPayRes": {"post": {"summary": "金币支付-结果查询", "operationId": "Api_CoinPayRes", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdCoinPayResRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdCoinPayResReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/GiveUp": {"post": {"summary": "放弃闯关", "operationId": "Api_GiveUp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdGiveUpRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdGiveUpReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/ReceiveAwards": {"post": {"summary": "领取奖励", "operationId": "Api_ReceiveAwards", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdReceiveAwardsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdReceiveAwardsReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/ReceiveRecords": {"post": {"summary": "闯关记录", "operationId": "Api_ReceiveRecords", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdReceiveRecordsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdReceiveRecordsReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/Smash": {"post": {"summary": "玩法", "description": "砸金蛋", "operationId": "Api_Smash", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdSmashRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdSmashReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/Status": {"post": {"summary": "当前状态=重进游戏请求", "operationId": "Api_Status", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdStatusReq"}}], "tags": ["Api"]}}, "/game_zjd.Api/Ticket": {"post": {"summary": "支付", "operationId": "Api_Ticket", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_zjdTicketRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_zjdTicketReq"}}], "tags": ["Api"]}}}, "definitions": {"adapter_commonGameMiddleInfo": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "gameOpenId": {"type": "string"}, "uid": {"type": "string"}}, "title": "方式二 宿主平台游戏账号体系\n   必填参数：uid"}, "callbackCommodityItem": {"type": "object", "properties": {"commodityId": {"type": "integer", "format": "int64", "title": "消费的道具id"}, "num": {"type": "integer", "format": "int64", "title": "消费的道具数量"}}, "title": "支付代理delivery==================begin"}, "callbackConsumeInfo": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/callbackCommodityItem"}}, "amount": {"type": "integer", "format": "int64", "title": "总价"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传字段"}, "currencyType": {"type": "integer", "format": "int64", "title": "货币类型 0/1=k币"}}}, "callbackOrderShipmentReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/adapter_commonGameMiddleInfo", "title": "中台数据"}, "consumeInfo": {"$ref": "#/definitions/callbackConsumeInfo", "title": "消费信息-下单时传入的参数"}, "consumeId": {"type": "string", "title": "订单id"}, "vecData": {"type": "string", "format": "byte", "title": "业务透传数据-下单时传入的参数"}, "payScene": {"type": "integer", "format": "int64", "title": "付费场景 1=直播 2=歌房 3=异步作品"}, "paySceneData": {"type": "string", "format": "byte", "title": "付费场景数据-云上应该暂时用不到，先只透传吧"}, "businessId": {"type": "string", "format": "int64", "title": "支付businessid，支付平台分配"}}}, "callbackOrderShipmentRsp": {"type": "object"}, "game_zjdAd": {"type": "object", "properties": {"adToken": {"type": "string", "title": "广告token"}, "adPosId": {"type": "string", "title": "广告位id"}, "device": {"$ref": "#/definitions/game_zjdDeviceInfo", "title": "设备信息"}}}, "game_zjdAdCheckReq": {"type": "object", "properties": {"viewCnt": {"type": "integer", "format": "int64", "title": "从0累计,第几次观看"}, "ad": {"$ref": "#/definitions/game_zjdAd", "title": "广告信息"}}, "title": "广告上报 Req"}, "game_zjdAdCheckRsp": {"type": "object", "properties": {"viewCnt": {"type": "integer", "format": "int64", "title": "服务端统计已观看的次数,包含本地"}}, "title": "广告上报 Rsp"}, "game_zjdAdRevivesReq": {"type": "object", "title": "看广告复活 Req"}, "game_zjdAdRevivesRsp": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32", "title": "订单状态 =>comm.TradeStatusType"}}, "title": "看广告复活 Rsp"}, "game_zjdBoomInfo": {"type": "object", "properties": {"receiveAdCnt": {"type": "integer", "format": "int64", "title": "复活需要的广告数量 处于被炸状态的时候"}, "site": {"type": "integer", "format": "int32", "title": "最后被炸的位置"}}}, "game_zjdCfgNormal": {"type": "object", "properties": {"version": {"type": "integer", "format": "int64", "title": "版本号"}, "levelOpt": {"type": "string", "title": "关卡配置   // (total=>总关卡数), (l1=>重要关卡数1 5), (l2=>重要关卡数2 10),(l3=>重要关卡数3 15)"}, "beginOptList": {"type": "string", "title": "关卡开局花费"}}}, "game_zjdCfgReq": {"type": "object", "properties": {"ver": {"type": "integer", "format": "int64", "title": "版本号"}, "verReward": {"type": "integer", "format": "int64", "title": "礼包版本号"}}}, "game_zjdCfgRsp": {"type": "object", "properties": {"ctg": {"$ref": "#/definitions/game_zjdCfgNormal", "title": "配置信息"}, "gmVersion": {"type": "integer", "format": "int64", "title": "礼物版本号"}, "gm": {"type": "object", "additionalProperties": {"$ref": "#/definitions/game_zjdRewardInfo"}, "title": "礼物信息"}}}, "game_zjdCoinPayReq": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id，幂等使用"}, "payAmount": {"type": "string", "format": "int64", "title": "总价"}, "gear": {"type": "integer", "format": "int64", "title": "选择的档位,传输下标索引即可"}}}, "game_zjdCoinPayResReq": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id，幂等使用"}}}, "game_zjdCoinPayResRsp": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32", "title": "订单状态 =>TradeStatusType"}}}, "game_zjdCoinPayRsp": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32", "title": "订单状态 =>comm.TradeStatusType"}}}, "game_zjdDeviceInfo": {"type": "object", "properties": {"ip": {"type": "string"}, "mac": {"type": "string"}, "imei": {"type": "string"}, "idfa": {"type": "string"}, "idfv": {"type": "string"}, "mobileFlag": {"type": "integer", "format": "int64", "title": "是否来自手机"}, "mobleQUA": {"type": "string", "title": "qua"}, "uuid": {"type": "string"}, "udid": {"type": "string"}, "qimei36": {"type": "string"}, "deviceInfo": {"type": "string"}}}, "game_zjdGiveUpReq": {"type": "object", "title": "放弃闯关 Req"}, "game_zjdGiveUpRsp": {"type": "object", "title": "放弃闯关 Rsp"}, "game_zjdLevelReward": {"type": "object", "properties": {"level": {"type": "integer", "format": "int64", "title": "第几关 忽略,后端返回已合并相同id"}, "rewardId": {"type": "integer", "format": "int64", "title": "商品ID"}, "rewardCnt": {"type": "integer", "format": "int64", "title": "数量"}}}, "game_zjdReceiveAwardsReq": {"type": "object", "title": "领取奖励 Req"}, "game_zjdReceiveAwardsRsp": {"type": "object", "title": "领取奖励 Rsp"}, "game_zjdReceiveRecord": {"type": "object", "properties": {"time": {"type": "string", "format": "int64", "title": "领奖时间"}, "number": {"type": "string", "title": "编号"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_zjdRewardInfo"}, "title": "礼物信息"}}}, "game_zjdReceiveRecordsReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "title": "页码"}}, "title": "领取记录 Req"}, "game_zjdReceiveRecordsRsp": {"type": "object", "properties": {"ll": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_zjdReceiveRecord"}}, "isLast": {"type": "boolean", "title": "是否最后一页"}}, "title": "领取记录 Rsp"}, "game_zjdRewardInfo": {"type": "object", "properties": {"giftId": {"type": "string", "title": "礼物编码 子商品ID 小礼包不会使用"}, "giftType": {"type": "integer", "format": "int64", "title": "类型"}, "giftNum": {"type": "string", "format": "int64", "title": "名称"}, "giftName": {"type": "string", "title": "logo"}, "giftLogo": {"type": "string", "title": "数量"}, "giftUnitPrice": {"type": "integer", "format": "int64", "title": "礼物价值，配置接口有用"}, "giftPlatType": {"type": "string", "title": "透传透传宿主平台子奖品类型"}}}, "game_zjdScreenType": {"type": "string", "enum": ["ST_None", "ST_NeedData"], "default": "ST_None", "description": "- ST_None: 0默认请求\n - ST_NeedData: 1拉起未完成的信息", "title": "ScreenType 用户当前的游戏状态"}, "game_zjdSmash": {"type": "object", "properties": {"st": {"type": "integer", "format": "int64", "title": "奖品类型 SmashType"}, "rewardId": {"type": "integer", "format": "int64", "title": "礼包ID"}, "rewardCnt": {"type": "integer", "format": "int64", "title": "礼包数量"}}}, "game_zjdSmashReq": {"type": "object", "properties": {"site": {"type": "integer", "format": "int64", "title": "位置 从0下标开始"}, "level": {"type": "integer", "format": "int64", "title": "砸蛋关卡"}}, "title": "砸金蛋 Req"}, "game_zjdSmashRsp": {"type": "object", "properties": {"smashRes": {"$ref": "#/definitions/game_zjdSmash", "title": "传输的site奖品类型"}, "smashOth": {"type": "object", "additionalProperties": {"$ref": "#/definitions/game_zjdSmash"}, "title": "非砸金蛋位置的结果【如果是炸弹无结果】"}, "receiveAdCnt": {"type": "integer", "format": "int64", "title": "复活需要的广告数量"}}, "title": "砸金蛋 Rsp"}, "game_zjdStatusReq": {"type": "object", "properties": {"st": {"$ref": "#/definitions/game_zjdScreenType", "title": "打开场景"}}}, "game_zjdStatusRsp": {"type": "object", "properties": {"coinCnt": {"type": "string", "format": "int64", "title": "网赚金币余额"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_zjdLevelReward"}, "title": "礼物ID"}, "dayPlayCnt": {"type": "string", "format": "int64", "title": "今日剩余次数 -1代表无限制"}, "status": {"type": "integer", "format": "int64", "title": "用户当前的游戏状态 GameStatusType"}, "level": {"type": "integer", "format": "int64", "title": "用户当前关卡"}, "boomInfo": {"$ref": "#/definitions/game_zjdBoomInfo", "title": "被炸后的位置与广告次数"}}}, "game_zjdTicketReq": {"type": "object", "title": "票据 req"}, "game_zjdTicketRsp": {"type": "object", "properties": {"tid": {"type": "string", "title": "事务ID"}}, "title": "票据 rsp"}, "googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}}}