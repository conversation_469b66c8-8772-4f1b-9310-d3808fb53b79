// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_game/zjd/api/ad.proto

package api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 广告上报 Req
type AdCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViewCnt uint32 `protobuf:"varint,1,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"` // 从0累计,第几次观看
	Ad      *Ad    `protobuf:"bytes,2,opt,name=ad,proto3" json:"ad,omitempty"`                           // 广告信息
}

func (x *AdCheckReq) Reset() {
	*x = AdCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdCheckReq) ProtoMessage() {}

func (x *AdCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdCheckReq.ProtoReflect.Descriptor instead.
func (*AdCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP(), []int{0}
}

func (x *AdCheckReq) GetViewCnt() uint32 {
	if x != nil {
		return x.ViewCnt
	}
	return 0
}

func (x *AdCheckReq) GetAd() *Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

// 广告上报 Rsp
type AdCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ViewCnt uint32 `protobuf:"varint,1,opt,name=view_cnt,json=viewCnt,proto3" json:"view_cnt,omitempty"` // 服务端统计已观看的次数,包含本地
}

func (x *AdCheckRsp) Reset() {
	*x = AdCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdCheckRsp) ProtoMessage() {}

func (x *AdCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdCheckRsp.ProtoReflect.Descriptor instead.
func (*AdCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP(), []int{1}
}

func (x *AdCheckRsp) GetViewCnt() uint32 {
	if x != nil {
		return x.ViewCnt
	}
	return 0
}

type Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdToken string      `protobuf:"bytes,1,opt,name=ad_token,json=adToken,proto3" json:"ad_token,omitempty"`   // 广告token
	AdPosId string      `protobuf:"bytes,2,opt,name=ad_pos_id,json=adPosId,proto3" json:"ad_pos_id,omitempty"` // 广告位id
	Device  *DeviceInfo `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`                    // 设备信息
}

func (x *Ad) Reset() {
	*x = Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP(), []int{2}
}

func (x *Ad) GetAdToken() string {
	if x != nil {
		return x.AdToken
	}
	return ""
}

func (x *Ad) GetAdPosId() string {
	if x != nil {
		return x.AdPosId
	}
	return ""
}

func (x *Ad) GetDevice() *DeviceInfo {
	if x != nil {
		return x.Device
	}
	return nil
}

type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip         string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Mac        string `protobuf:"bytes,2,opt,name=mac,proto3" json:"mac,omitempty"`
	Imei       string `protobuf:"bytes,3,opt,name=imei,proto3" json:"imei,omitempty"`
	Idfa       string `protobuf:"bytes,4,opt,name=idfa,proto3" json:"idfa,omitempty"`
	Idfv       string `protobuf:"bytes,5,opt,name=idfv,proto3" json:"idfv,omitempty"`
	MobileFlag uint32 `protobuf:"varint,6,opt,name=mobileFlag,proto3" json:"mobileFlag,omitempty"` // 是否来自手机
	MobleQUA   string `protobuf:"bytes,7,opt,name=mobleQUA,proto3" json:"mobleQUA,omitempty"`      // qua
	Uuid       string `protobuf:"bytes,8,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Udid       string `protobuf:"bytes,9,opt,name=udid,proto3" json:"udid,omitempty"`
	Qimei36    string `protobuf:"bytes,10,opt,name=qimei36,proto3" json:"qimei36,omitempty"`
	DeviceInfo string `protobuf:"bytes,11,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP(), []int{3}
}

func (x *DeviceInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeviceInfo) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *DeviceInfo) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *DeviceInfo) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *DeviceInfo) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *DeviceInfo) GetMobileFlag() uint32 {
	if x != nil {
		return x.MobileFlag
	}
	return 0
}

func (x *DeviceInfo) GetMobleQUA() string {
	if x != nil {
		return x.MobleQUA
	}
	return ""
}

func (x *DeviceInfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DeviceInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *DeviceInfo) GetQimei36() string {
	if x != nil {
		return x.Qimei36
	}
	return ""
}

func (x *DeviceInfo) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

// 看广告复活 Req
type AdRevivesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AdRevivesReq) Reset() {
	*x = AdRevivesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdRevivesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdRevivesReq) ProtoMessage() {}

func (x *AdRevivesReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdRevivesReq.ProtoReflect.Descriptor instead.
func (*AdRevivesReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP(), []int{4}
}

// 看广告复活 Rsp
type AdRevivesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 订单状态 =>comm.TradeStatusType
}

func (x *AdRevivesRsp) Reset() {
	*x = AdRevivesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdRevivesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdRevivesRsp) ProtoMessage() {}

func (x *AdRevivesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_ad_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdRevivesRsp.ProtoReflect.Descriptor instead.
func (*AdRevivesRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP(), []int{5}
}

func (x *AdRevivesRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

var File_pb_xian_game_zjd_api_ad_proto protoreflect.FileDescriptor

var file_pb_xian_game_zjd_api_ad_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a,
	0x6a, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x22, 0x45, 0x0a, 0x0a, 0x41, 0x64, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x63, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x69, 0x65, 0x77, 0x43,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x41, 0x64, 0x52, 0x02, 0x61, 0x64,
	0x22, 0x27, 0x0a, 0x0a, 0x41, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x19,
	0x0a, 0x08, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6e, 0x74, 0x22, 0x69, 0x0a, 0x02, 0x41, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x09, 0x61, 0x64,
	0x5f, 0x70, 0x6f, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x50, 0x6f, 0x73, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a,
	0x64, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x22, 0x88, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x64, 0x66, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66,
	0x76, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x62, 0x6c, 0x65, 0x51, 0x55, 0x41, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x62, 0x6c, 0x65, 0x51, 0x55, 0x41, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x0e, 0x0a, 0x0c, 0x41, 0x64, 0x52, 0x65, 0x76, 0x69, 0x76, 0x65, 0x73, 0x52, 0x65, 0x71, 0x22,
	0x26, 0x0a, 0x0c, 0x41, 0x64, 0x52, 0x65, 0x76, 0x69, 0x76, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x2e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a, 0x6a, 0x64, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x6d, 0x2f, 0x7a, 0x6a, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_xian_game_zjd_api_ad_proto_rawDescOnce sync.Once
	file_pb_xian_game_zjd_api_ad_proto_rawDescData = file_pb_xian_game_zjd_api_ad_proto_rawDesc
)

func file_pb_xian_game_zjd_api_ad_proto_rawDescGZIP() []byte {
	file_pb_xian_game_zjd_api_ad_proto_rawDescOnce.Do(func() {
		file_pb_xian_game_zjd_api_ad_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_game_zjd_api_ad_proto_rawDescData)
	})
	return file_pb_xian_game_zjd_api_ad_proto_rawDescData
}

var file_pb_xian_game_zjd_api_ad_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_xian_game_zjd_api_ad_proto_goTypes = []interface{}{
	(*AdCheckReq)(nil),   // 0: game_zjd.AdCheckReq
	(*AdCheckRsp)(nil),   // 1: game_zjd.AdCheckRsp
	(*Ad)(nil),           // 2: game_zjd.Ad
	(*DeviceInfo)(nil),   // 3: game_zjd.DeviceInfo
	(*AdRevivesReq)(nil), // 4: game_zjd.AdRevivesReq
	(*AdRevivesRsp)(nil), // 5: game_zjd.AdRevivesRsp
}
var file_pb_xian_game_zjd_api_ad_proto_depIdxs = []int32{
	2, // 0: game_zjd.AdCheckReq.ad:type_name -> game_zjd.Ad
	3, // 1: game_zjd.Ad.device:type_name -> game_zjd.DeviceInfo
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_xian_game_zjd_api_ad_proto_init() }
func file_pb_xian_game_zjd_api_ad_proto_init() {
	if File_pb_xian_game_zjd_api_ad_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_game_zjd_api_ad_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_ad_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_ad_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_ad_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_ad_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdRevivesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_ad_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdRevivesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_game_zjd_api_ad_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_xian_game_zjd_api_ad_proto_goTypes,
		DependencyIndexes: file_pb_xian_game_zjd_api_ad_proto_depIdxs,
		MessageInfos:      file_pb_xian_game_zjd_api_ad_proto_msgTypes,
	}.Build()
	File_pb_xian_game_zjd_api_ad_proto = out.File
	file_pb_xian_game_zjd_api_ad_proto_rawDesc = nil
	file_pb_xian_game_zjd_api_ad_proto_goTypes = nil
	file_pb_xian_game_zjd_api_ad_proto_depIdxs = nil
}
