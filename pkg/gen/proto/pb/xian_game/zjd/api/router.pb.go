// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_game/zjd/api/router.proto

package api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	comm "kugou_adapter_service/pkg/gen/proto/pb/xian_game/zjd/comm"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ScreenType 用户当前的游戏状态
type ScreenType int32

const (
	ScreenType_ST_None     ScreenType = 0 // 0默认请求
	ScreenType_ST_NeedData ScreenType = 1 // 1拉起未完成的信息
)

// Enum value maps for ScreenType.
var (
	ScreenType_name = map[int32]string{
		0: "ST_None",
		1: "ST_NeedData",
	}
	ScreenType_value = map[string]int32{
		"ST_None":     0,
		"ST_NeedData": 1,
	}
)

func (x ScreenType) Enum() *ScreenType {
	p := new(ScreenType)
	*p = x
	return p
}

func (x ScreenType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_zjd_api_router_proto_enumTypes[0].Descriptor()
}

func (ScreenType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_zjd_api_router_proto_enumTypes[0]
}

func (x ScreenType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenType.Descriptor instead.
func (ScreenType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{0}
}

// GameStatusType 用户当前的游戏状态
type GameStatusType int32

const (
	GameStatusType_GST_None       GameStatusType = 0 // 无状态
	GameStatusType_GST_Playing    GameStatusType = 1 // 游戏中
	GameStatusType_GST_BoomByGrid GameStatusType = 2 // 炸弹需要金币复活
	GameStatusType_GST_BoomByAd   GameStatusType = 3 // 炸弹需要广告复活
)

// Enum value maps for GameStatusType.
var (
	GameStatusType_name = map[int32]string{
		0: "GST_None",
		1: "GST_Playing",
		2: "GST_BoomByGrid",
		3: "GST_BoomByAd",
	}
	GameStatusType_value = map[string]int32{
		"GST_None":       0,
		"GST_Playing":    1,
		"GST_BoomByGrid": 2,
		"GST_BoomByAd":   3,
	}
)

func (x GameStatusType) Enum() *GameStatusType {
	p := new(GameStatusType)
	*p = x
	return p
}

func (x GameStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_zjd_api_router_proto_enumTypes[1].Descriptor()
}

func (GameStatusType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_zjd_api_router_proto_enumTypes[1]
}

func (x GameStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameStatusType.Descriptor instead.
func (GameStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{1}
}

type SmashType int32

const (
	SmashType_SmashType_Boom   SmashType = 0 // 炸弹
	SmashType_SmashType_Reward SmashType = 1 // 奖品
)

// Enum value maps for SmashType.
var (
	SmashType_name = map[int32]string{
		0: "SmashType_Boom",
		1: "SmashType_Reward",
	}
	SmashType_value = map[string]int32{
		"SmashType_Boom":   0,
		"SmashType_Reward": 1,
	}
)

func (x SmashType) Enum() *SmashType {
	p := new(SmashType)
	*p = x
	return p
}

func (x SmashType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SmashType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_zjd_api_router_proto_enumTypes[2].Descriptor()
}

func (SmashType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_zjd_api_router_proto_enumTypes[2]
}

func (x SmashType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SmashType.Descriptor instead.
func (SmashType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{2}
}

type CfgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ver       uint32 `protobuf:"varint,1,opt,name=ver,proto3" json:"ver,omitempty"`                              // 版本号
	VerReward uint32 `protobuf:"varint,2,opt,name=ver_reward,json=verReward,proto3" json:"ver_reward,omitempty"` // 礼包版本号
}

func (x *CfgReq) Reset() {
	*x = CfgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CfgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CfgReq) ProtoMessage() {}

func (x *CfgReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CfgReq.ProtoReflect.Descriptor instead.
func (*CfgReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{0}
}

func (x *CfgReq) GetVer() uint32 {
	if x != nil {
		return x.Ver
	}
	return 0
}

func (x *CfgReq) GetVerReward() uint32 {
	if x != nil {
		return x.VerReward
	}
	return 0
}

type CfgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ctg       *CfgNormal                 `protobuf:"bytes,1,opt,name=ctg,proto3" json:"ctg,omitempty"`                                                                                        // 配置信息
	GmVersion uint32                     `protobuf:"varint,2,opt,name=gm_version,json=gmVersion,proto3" json:"gm_version,omitempty"`                                                          // 礼物版本号
	Gm        map[int64]*comm.RewardInfo `protobuf:"bytes,3,rep,name=gm,proto3" json:"gm,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 礼物信息
}

func (x *CfgRsp) Reset() {
	*x = CfgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CfgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CfgRsp) ProtoMessage() {}

func (x *CfgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CfgRsp.ProtoReflect.Descriptor instead.
func (*CfgRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{1}
}

func (x *CfgRsp) GetCtg() *CfgNormal {
	if x != nil {
		return x.Ctg
	}
	return nil
}

func (x *CfgRsp) GetGmVersion() uint32 {
	if x != nil {
		return x.GmVersion
	}
	return 0
}

func (x *CfgRsp) GetGm() map[int64]*comm.RewardInfo {
	if x != nil {
		return x.Gm
	}
	return nil
}

type CfgNormal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version      uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`                                // 版本号
	LevelOpt     string `protobuf:"bytes,2,opt,name=level_opt,json=levelOpt,proto3" json:"level_opt,omitempty"`               // 关卡配置   // (total=>总关卡数), (l1=>重要关卡数1 5), (l2=>重要关卡数2 10),(l3=>重要关卡数3 15)
	BeginOptList string `protobuf:"bytes,3,opt,name=begin_opt_list,json=beginOptList,proto3" json:"begin_opt_list,omitempty"` // 关卡开局花费
}

func (x *CfgNormal) Reset() {
	*x = CfgNormal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CfgNormal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CfgNormal) ProtoMessage() {}

func (x *CfgNormal) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CfgNormal.ProtoReflect.Descriptor instead.
func (*CfgNormal) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{2}
}

func (x *CfgNormal) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *CfgNormal) GetLevelOpt() string {
	if x != nil {
		return x.LevelOpt
	}
	return ""
}

func (x *CfgNormal) GetBeginOptList() string {
	if x != nil {
		return x.BeginOptList
	}
	return ""
}

// 票据 req
type TicketReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TicketReq) Reset() {
	*x = TicketReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketReq) ProtoMessage() {}

func (x *TicketReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketReq.ProtoReflect.Descriptor instead.
func (*TicketReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{3}
}

// 票据 rsp
type TicketRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid string `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"` // 事务ID
}

func (x *TicketRsp) Reset() {
	*x = TicketRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketRsp) ProtoMessage() {}

func (x *TicketRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketRsp.ProtoReflect.Descriptor instead.
func (*TicketRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{4}
}

func (x *TicketRsp) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

type CoinPayReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId   string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`        // 订单id，幂等使用
	PayAmount int64  `protobuf:"varint,2,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount,omitempty"` // 总价
	Gear      uint32 `protobuf:"varint,3,opt,name=gear,proto3" json:"gear,omitempty"`                            // 选择的档位,传输下标索引即可
}

func (x *CoinPayReq) Reset() {
	*x = CoinPayReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinPayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinPayReq) ProtoMessage() {}

func (x *CoinPayReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinPayReq.ProtoReflect.Descriptor instead.
func (*CoinPayReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{5}
}

func (x *CoinPayReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CoinPayReq) GetPayAmount() int64 {
	if x != nil {
		return x.PayAmount
	}
	return 0
}

func (x *CoinPayReq) GetGear() uint32 {
	if x != nil {
		return x.Gear
	}
	return 0
}

type CoinPayRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 订单状态 =>comm.TradeStatusType
}

func (x *CoinPayRsp) Reset() {
	*x = CoinPayRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinPayRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinPayRsp) ProtoMessage() {}

func (x *CoinPayRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinPayRsp.ProtoReflect.Descriptor instead.
func (*CoinPayRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{6}
}

func (x *CoinPayRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type CoinPayResReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"` // 订单id，幂等使用
}

func (x *CoinPayResReq) Reset() {
	*x = CoinPayResReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinPayResReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinPayResReq) ProtoMessage() {}

func (x *CoinPayResReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinPayResReq.ProtoReflect.Descriptor instead.
func (*CoinPayResReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{7}
}

func (x *CoinPayResReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type CoinPayResRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 订单状态 =>TradeStatusType
}

func (x *CoinPayResRsp) Reset() {
	*x = CoinPayResRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinPayResRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinPayResRsp) ProtoMessage() {}

func (x *CoinPayResRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinPayResRsp.ProtoReflect.Descriptor instead.
func (*CoinPayResRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{8}
}

func (x *CoinPayResRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type StatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	St ScreenType `protobuf:"varint,1,opt,name=st,proto3,enum=game_zjd.ScreenType" json:"st,omitempty"` // 打开场景
}

func (x *StatusReq) Reset() {
	*x = StatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusReq) ProtoMessage() {}

func (x *StatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusReq.ProtoReflect.Descriptor instead.
func (*StatusReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{9}
}

func (x *StatusReq) GetSt() ScreenType {
	if x != nil {
		return x.St
	}
	return ScreenType_ST_None
}

type StatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CoinCnt    int64          `protobuf:"varint,1,opt,name=coin_cnt,json=coinCnt,proto3" json:"coin_cnt,omitempty"`            // 网赚金币余额
	Rewards    []*LevelReward `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`                            // 礼物ID
	DayPlayCnt int64          `protobuf:"varint,3,opt,name=day_play_cnt,json=dayPlayCnt,proto3" json:"day_play_cnt,omitempty"` // 今日剩余次数 -1代表无限制
	Status     uint32         `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`                             // 用户当前的游戏状态 GameStatusType
	Level      uint32         `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`                               // 用户当前关卡
	BoomInfo   *BoomInfo      `protobuf:"bytes,6,opt,name=boom_info,json=boomInfo,proto3" json:"boom_info,omitempty"`          // 被炸后的位置与广告次数
}

func (x *StatusRsp) Reset() {
	*x = StatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRsp) ProtoMessage() {}

func (x *StatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusRsp.ProtoReflect.Descriptor instead.
func (*StatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{10}
}

func (x *StatusRsp) GetCoinCnt() int64 {
	if x != nil {
		return x.CoinCnt
	}
	return 0
}

func (x *StatusRsp) GetRewards() []*LevelReward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *StatusRsp) GetDayPlayCnt() int64 {
	if x != nil {
		return x.DayPlayCnt
	}
	return 0
}

func (x *StatusRsp) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *StatusRsp) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *StatusRsp) GetBoomInfo() *BoomInfo {
	if x != nil {
		return x.BoomInfo
	}
	return nil
}

type BoomInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReceiveAdCnt uint32 `protobuf:"varint,1,opt,name=receive_ad_cnt,json=receiveAdCnt,proto3" json:"receive_ad_cnt,omitempty"` // 复活需要的广告数量 处于被炸状态的时候
	Site         int32  `protobuf:"varint,2,opt,name=site,proto3" json:"site,omitempty"`                                       // 最后被炸的位置
}

func (x *BoomInfo) Reset() {
	*x = BoomInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoomInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoomInfo) ProtoMessage() {}

func (x *BoomInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoomInfo.ProtoReflect.Descriptor instead.
func (*BoomInfo) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{11}
}

func (x *BoomInfo) GetReceiveAdCnt() uint32 {
	if x != nil {
		return x.ReceiveAdCnt
	}
	return 0
}

func (x *BoomInfo) GetSite() int32 {
	if x != nil {
		return x.Site
	}
	return 0
}

type LevelReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level     uint32 `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`                          // 第几关 忽略,后端返回已合并相同id
	RewardId  uint32 `protobuf:"varint,2,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`    // 商品ID
	RewardCnt uint32 `protobuf:"varint,3,opt,name=reward_cnt,json=rewardCnt,proto3" json:"reward_cnt,omitempty"` // 数量
}

func (x *LevelReward) Reset() {
	*x = LevelReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LevelReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelReward) ProtoMessage() {}

func (x *LevelReward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelReward.ProtoReflect.Descriptor instead.
func (*LevelReward) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{12}
}

func (x *LevelReward) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *LevelReward) GetRewardId() uint32 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *LevelReward) GetRewardCnt() uint32 {
	if x != nil {
		return x.RewardCnt
	}
	return 0
}

// 砸金蛋 Req
type SmashReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site  uint32 `protobuf:"varint,1,opt,name=site,proto3" json:"site,omitempty"`   // 位置 从0下标开始
	Level uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"` // 砸蛋关卡
}

func (x *SmashReq) Reset() {
	*x = SmashReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmashReq) ProtoMessage() {}

func (x *SmashReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmashReq.ProtoReflect.Descriptor instead.
func (*SmashReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{13}
}

func (x *SmashReq) GetSite() uint32 {
	if x != nil {
		return x.Site
	}
	return 0
}

func (x *SmashReq) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 砸金蛋 Rsp
type SmashRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SmashRes     *Smash            `protobuf:"bytes,1,opt,name=smash_res,json=smashRes,proto3" json:"smash_res,omitempty"`                                                                                          // 传输的site奖品类型
	SmashOth     map[uint32]*Smash `protobuf:"bytes,2,rep,name=smash_oth,json=smashOth,proto3" json:"smash_oth,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 非砸金蛋位置的结果【如果是炸弹无结果】
	ReceiveAdCnt uint32            `protobuf:"varint,6,opt,name=receive_ad_cnt,json=receiveAdCnt,proto3" json:"receive_ad_cnt,omitempty"`                                                                           // 复活需要的广告数量
}

func (x *SmashRsp) Reset() {
	*x = SmashRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SmashRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmashRsp) ProtoMessage() {}

func (x *SmashRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmashRsp.ProtoReflect.Descriptor instead.
func (*SmashRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{14}
}

func (x *SmashRsp) GetSmashRes() *Smash {
	if x != nil {
		return x.SmashRes
	}
	return nil
}

func (x *SmashRsp) GetSmashOth() map[uint32]*Smash {
	if x != nil {
		return x.SmashOth
	}
	return nil
}

func (x *SmashRsp) GetReceiveAdCnt() uint32 {
	if x != nil {
		return x.ReceiveAdCnt
	}
	return 0
}

type Smash struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	St        uint32 `protobuf:"varint,1,opt,name=st,proto3" json:"st,omitempty"`                                // 奖品类型 SmashType
	RewardId  uint32 `protobuf:"varint,2,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`    // 礼包ID
	RewardCnt uint32 `protobuf:"varint,3,opt,name=reward_cnt,json=rewardCnt,proto3" json:"reward_cnt,omitempty"` // 礼包数量
}

func (x *Smash) Reset() {
	*x = Smash{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Smash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Smash) ProtoMessage() {}

func (x *Smash) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Smash.ProtoReflect.Descriptor instead.
func (*Smash) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{15}
}

func (x *Smash) GetSt() uint32 {
	if x != nil {
		return x.St
	}
	return 0
}

func (x *Smash) GetRewardId() uint32 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *Smash) GetRewardCnt() uint32 {
	if x != nil {
		return x.RewardCnt
	}
	return 0
}

// 领取奖励 Req
type ReceiveAwardsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReceiveAwardsReq) Reset() {
	*x = ReceiveAwardsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveAwardsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveAwardsReq) ProtoMessage() {}

func (x *ReceiveAwardsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveAwardsReq.ProtoReflect.Descriptor instead.
func (*ReceiveAwardsReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{16}
}

// 领取奖励 Rsp
type ReceiveAwardsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReceiveAwardsRsp) Reset() {
	*x = ReceiveAwardsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveAwardsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveAwardsRsp) ProtoMessage() {}

func (x *ReceiveAwardsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveAwardsRsp.ProtoReflect.Descriptor instead.
func (*ReceiveAwardsRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{17}
}

// 放弃闯关 Req
type GiveUpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GiveUpReq) Reset() {
	*x = GiveUpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiveUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiveUpReq) ProtoMessage() {}

func (x *GiveUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiveUpReq.ProtoReflect.Descriptor instead.
func (*GiveUpReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{18}
}

// 放弃闯关 Rsp
type GiveUpRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GiveUpRsp) Reset() {
	*x = GiveUpRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiveUpRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiveUpRsp) ProtoMessage() {}

func (x *GiveUpRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiveUpRsp.ProtoReflect.Descriptor instead.
func (*GiveUpRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{19}
}

// 领取记录 Req
type ReceiveRecordsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"` // 页码
}

func (x *ReceiveRecordsReq) Reset() {
	*x = ReceiveRecordsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveRecordsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveRecordsReq) ProtoMessage() {}

func (x *ReceiveRecordsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveRecordsReq.ProtoReflect.Descriptor instead.
func (*ReceiveRecordsReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{20}
}

func (x *ReceiveRecordsReq) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

// 领取记录 Rsp
type ReceiveRecordsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ll     []*ReceiveRecord `protobuf:"bytes,1,rep,name=ll,proto3" json:"ll,omitempty"`
	IsLast bool             `protobuf:"varint,2,opt,name=is_last,json=isLast,proto3" json:"is_last,omitempty"` // 是否最后一页
}

func (x *ReceiveRecordsRsp) Reset() {
	*x = ReceiveRecordsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveRecordsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveRecordsRsp) ProtoMessage() {}

func (x *ReceiveRecordsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveRecordsRsp.ProtoReflect.Descriptor instead.
func (*ReceiveRecordsRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{21}
}

func (x *ReceiveRecordsRsp) GetLl() []*ReceiveRecord {
	if x != nil {
		return x.Ll
	}
	return nil
}

func (x *ReceiveRecordsRsp) GetIsLast() bool {
	if x != nil {
		return x.IsLast
	}
	return false
}

type ReceiveRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time    int64              `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`      // 领奖时间
	Number  string             `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"`   // 编号
	Rewards []*comm.RewardInfo `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"` // 礼物信息
}

func (x *ReceiveRecord) Reset() {
	*x = ReceiveRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveRecord) ProtoMessage() {}

func (x *ReceiveRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_zjd_api_router_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveRecord.ProtoReflect.Descriptor instead.
func (*ReceiveRecord) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_zjd_api_router_proto_rawDescGZIP(), []int{22}
}

func (x *ReceiveRecord) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *ReceiveRecord) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ReceiveRecord) GetRewards() []*comm.RewardInfo {
	if x != nil {
		return x.Rewards
	}
	return nil
}

var File_pb_xian_game_zjd_api_router_proto protoreflect.FileDescriptor

var file_pb_xian_game_zjd_api_router_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a,
	0x6a, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x1a, 0x31, 0x70,
	0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1d, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a,
	0x6a, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x7a, 0x6a,
	0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x39, 0x0a, 0x06, 0x43, 0x66, 0x67, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x76, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x76, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0xc5, 0x01, 0x0a,
	0x06, 0x43, 0x66, 0x67, 0x52, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x03, 0x63, 0x74, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e,
	0x43, 0x66, 0x67, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x52, 0x03, 0x63, 0x74, 0x67, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x67, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a,
	0x02, 0x67, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x43, 0x66, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x47, 0x6d, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x02, 0x67, 0x6d, 0x1a, 0x4b, 0x0a, 0x07, 0x47, 0x6d, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x68, 0x0a, 0x09, 0x43, 0x66, 0x67, 0x4e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x4f, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x0b,
	0x0a, 0x09, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x22, 0x1d, 0x0a, 0x09, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x22, 0x5a, 0x0a, 0x0a, 0x43, 0x6f,
	0x69, 0x6e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x22, 0x24, 0x0a, 0x0a, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x61,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2a, 0x0a, 0x0d,
	0x43, 0x6f, 0x69, 0x6e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x27, 0x0a, 0x0d, 0x43, 0x6f, 0x69, 0x6e,
	0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x31, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x24,
	0x0a, 0x02, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x02, 0x73, 0x74, 0x22, 0xd8, 0x01, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x69, 0x6e, 0x43, 0x6e, 0x74, 0x12, 0x2f, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x20,
	0x0a, 0x0c, 0x64, 0x61, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x43, 0x6e, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2f,
	0x0a, 0x09, 0x62, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x42, 0x6f, 0x6f,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x62, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x44, 0x0a, 0x08, 0x42, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x64, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x64, 0x43, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x69, 0x74, 0x65, 0x22, 0x5f, 0x0a, 0x0b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6e, 0x74, 0x22, 0x34, 0x0a, 0x08, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x73, 0x69, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0xeb, 0x01, 0x0a,
	0x08, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x09, 0x73, 0x6d, 0x61,
	0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x52, 0x08, 0x73,
	0x6d, 0x61, 0x73, 0x68, 0x52, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x09, 0x73, 0x6d, 0x61, 0x73, 0x68,
	0x5f, 0x6f, 0x74, 0x68, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x53,
	0x6d, 0x61, 0x73, 0x68, 0x4f, 0x74, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x6d,
	0x61, 0x73, 0x68, 0x4f, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x5f, 0x61, 0x64, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x64, 0x43, 0x6e, 0x74, 0x1a, 0x4c, 0x0a, 0x0d,
	0x53, 0x6d, 0x61, 0x73, 0x68, 0x4f, 0x74, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x53, 0x0a, 0x05, 0x53, 0x6d,
	0x61, 0x73, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x02, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6e, 0x74, 0x22,
	0x12, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x22, 0x12, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x52, 0x73, 0x70, 0x22, 0x0b, 0x0a, 0x09, 0x47, 0x69, 0x76, 0x65, 0x55,
	0x70, 0x52, 0x65, 0x71, 0x22, 0x0b, 0x0a, 0x09, 0x47, 0x69, 0x76, 0x65, 0x55, 0x70, 0x52, 0x73,
	0x70, 0x22, 0x27, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x55, 0x0a, 0x11, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x27, 0x0a, 0x02, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x02, 0x6c, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6c,
	0x61, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4c, 0x61, 0x73,
	0x74, 0x22, 0x6b, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e,
	0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2a, 0x2a,
	0x0a, 0x0a, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x54, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x5f,
	0x4e, 0x65, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x10, 0x01, 0x2a, 0x55, 0x0a, 0x0e, 0x47, 0x61,
	0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08,
	0x47, 0x53, 0x54, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x53,
	0x54, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x47,
	0x53, 0x54, 0x5f, 0x42, 0x6f, 0x6f, 0x6d, 0x42, 0x79, 0x47, 0x72, 0x69, 0x64, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x47, 0x53, 0x54, 0x5f, 0x42, 0x6f, 0x6f, 0x6d, 0x42, 0x79, 0x41, 0x64, 0x10,
	0x03, 0x2a, 0x35, 0x0a, 0x09, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x0e, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x42, 0x6f, 0x6f, 0x6d,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x01, 0x32, 0xc8, 0x05, 0x0a, 0x03, 0x41, 0x70, 0x69,
	0x12, 0x29, 0x0a, 0x03, 0x43, 0x66, 0x67, 0x12, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a,
	0x6a, 0x64, 0x2e, 0x43, 0x66, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x43, 0x66, 0x67, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x32, 0x0a, 0x06, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x13,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x07, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x61, 0x79, 0x12, 0x14,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x61,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e,
	0x43, 0x6f, 0x69, 0x6e, 0x50, 0x61, 0x79, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0a, 0x43, 0x6f,
	0x69, 0x6e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x12, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x7a, 0x6a, 0x64, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x43, 0x6f, 0x69,
	0x6e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x05, 0x53, 0x6d,
	0x61, 0x73, 0x68, 0x12, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x53,
	0x6d, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a,
	0x6a, 0x64, 0x2e, 0x53, 0x6d, 0x61, 0x73, 0x68, 0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x0d, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x1a, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x7a, 0x6a, 0x64, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x06, 0x47, 0x69, 0x76, 0x65, 0x55, 0x70, 0x12, 0x13,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x47, 0x69, 0x76, 0x65, 0x55, 0x70,
	0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x47,
	0x69, 0x76, 0x65, 0x55, 0x70, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1b, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a,
	0x6a, 0x64, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x07, 0x41, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12,
	0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x41, 0x64, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64,
	0x2e, 0x41, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x09, 0x41,
	0x64, 0x52, 0x65, 0x76, 0x69, 0x76, 0x65, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x7a, 0x6a, 0x64, 0x2e, 0x41, 0x64, 0x52, 0x65, 0x76, 0x69, 0x76, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x7a, 0x6a, 0x64, 0x2e, 0x41, 0x64, 0x52, 0x65,
	0x76, 0x69, 0x76, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x0f, 0x43, 0x6f, 0x69, 0x6e,
	0x50, 0x61, 0x79, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1a, 0x2e, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x7a, 0x6a, 0x64, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x6d, 0x2f, 0x7a, 0x6a, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_xian_game_zjd_api_router_proto_rawDescOnce sync.Once
	file_pb_xian_game_zjd_api_router_proto_rawDescData = file_pb_xian_game_zjd_api_router_proto_rawDesc
)

func file_pb_xian_game_zjd_api_router_proto_rawDescGZIP() []byte {
	file_pb_xian_game_zjd_api_router_proto_rawDescOnce.Do(func() {
		file_pb_xian_game_zjd_api_router_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_game_zjd_api_router_proto_rawDescData)
	})
	return file_pb_xian_game_zjd_api_router_proto_rawDescData
}

var file_pb_xian_game_zjd_api_router_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pb_xian_game_zjd_api_router_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_pb_xian_game_zjd_api_router_proto_goTypes = []interface{}{
	(ScreenType)(0),                   // 0: game_zjd.ScreenType
	(GameStatusType)(0),               // 1: game_zjd.GameStatusType
	(SmashType)(0),                    // 2: game_zjd.SmashType
	(*CfgReq)(nil),                    // 3: game_zjd.CfgReq
	(*CfgRsp)(nil),                    // 4: game_zjd.CfgRsp
	(*CfgNormal)(nil),                 // 5: game_zjd.CfgNormal
	(*TicketReq)(nil),                 // 6: game_zjd.TicketReq
	(*TicketRsp)(nil),                 // 7: game_zjd.TicketRsp
	(*CoinPayReq)(nil),                // 8: game_zjd.CoinPayReq
	(*CoinPayRsp)(nil),                // 9: game_zjd.CoinPayRsp
	(*CoinPayResReq)(nil),             // 10: game_zjd.CoinPayResReq
	(*CoinPayResRsp)(nil),             // 11: game_zjd.CoinPayResRsp
	(*StatusReq)(nil),                 // 12: game_zjd.StatusReq
	(*StatusRsp)(nil),                 // 13: game_zjd.StatusRsp
	(*BoomInfo)(nil),                  // 14: game_zjd.BoomInfo
	(*LevelReward)(nil),               // 15: game_zjd.LevelReward
	(*SmashReq)(nil),                  // 16: game_zjd.SmashReq
	(*SmashRsp)(nil),                  // 17: game_zjd.SmashRsp
	(*Smash)(nil),                     // 18: game_zjd.Smash
	(*ReceiveAwardsReq)(nil),          // 19: game_zjd.ReceiveAwardsReq
	(*ReceiveAwardsRsp)(nil),          // 20: game_zjd.ReceiveAwardsRsp
	(*GiveUpReq)(nil),                 // 21: game_zjd.GiveUpReq
	(*GiveUpRsp)(nil),                 // 22: game_zjd.GiveUpRsp
	(*ReceiveRecordsReq)(nil),         // 23: game_zjd.ReceiveRecordsReq
	(*ReceiveRecordsRsp)(nil),         // 24: game_zjd.ReceiveRecordsRsp
	(*ReceiveRecord)(nil),             // 25: game_zjd.ReceiveRecord
	nil,                               // 26: game_zjd.CfgRsp.GmEntry
	nil,                               // 27: game_zjd.SmashRsp.SmashOthEntry
	(*comm.RewardInfo)(nil),           // 28: game_zjd.RewardInfo
	(*AdCheckReq)(nil),                // 29: game_zjd.AdCheckReq
	(*AdRevivesReq)(nil),              // 30: game_zjd.AdRevivesReq
	(*callback.OrderShipmentReq)(nil), // 31: callback.OrderShipmentReq
	(*AdCheckRsp)(nil),                // 32: game_zjd.AdCheckRsp
	(*AdRevivesRsp)(nil),              // 33: game_zjd.AdRevivesRsp
	(*callback.OrderShipmentRsp)(nil), // 34: callback.OrderShipmentRsp
}
var file_pb_xian_game_zjd_api_router_proto_depIdxs = []int32{
	5,  // 0: game_zjd.CfgRsp.ctg:type_name -> game_zjd.CfgNormal
	26, // 1: game_zjd.CfgRsp.gm:type_name -> game_zjd.CfgRsp.GmEntry
	0,  // 2: game_zjd.StatusReq.st:type_name -> game_zjd.ScreenType
	15, // 3: game_zjd.StatusRsp.rewards:type_name -> game_zjd.LevelReward
	14, // 4: game_zjd.StatusRsp.boom_info:type_name -> game_zjd.BoomInfo
	18, // 5: game_zjd.SmashRsp.smash_res:type_name -> game_zjd.Smash
	27, // 6: game_zjd.SmashRsp.smash_oth:type_name -> game_zjd.SmashRsp.SmashOthEntry
	25, // 7: game_zjd.ReceiveRecordsRsp.ll:type_name -> game_zjd.ReceiveRecord
	28, // 8: game_zjd.ReceiveRecord.rewards:type_name -> game_zjd.RewardInfo
	28, // 9: game_zjd.CfgRsp.GmEntry.value:type_name -> game_zjd.RewardInfo
	18, // 10: game_zjd.SmashRsp.SmashOthEntry.value:type_name -> game_zjd.Smash
	3,  // 11: game_zjd.Api.Cfg:input_type -> game_zjd.CfgReq
	12, // 12: game_zjd.Api.Status:input_type -> game_zjd.StatusReq
	6,  // 13: game_zjd.Api.Ticket:input_type -> game_zjd.TicketReq
	8,  // 14: game_zjd.Api.CoinPay:input_type -> game_zjd.CoinPayReq
	10, // 15: game_zjd.Api.CoinPayRes:input_type -> game_zjd.CoinPayResReq
	16, // 16: game_zjd.Api.Smash:input_type -> game_zjd.SmashReq
	19, // 17: game_zjd.Api.ReceiveAwards:input_type -> game_zjd.ReceiveAwardsReq
	21, // 18: game_zjd.Api.GiveUp:input_type -> game_zjd.GiveUpReq
	23, // 19: game_zjd.Api.ReceiveRecords:input_type -> game_zjd.ReceiveRecordsReq
	29, // 20: game_zjd.Api.AdCheck:input_type -> game_zjd.AdCheckReq
	30, // 21: game_zjd.Api.AdRevives:input_type -> game_zjd.AdRevivesReq
	31, // 22: game_zjd.Api.CoinPayCallback:input_type -> callback.OrderShipmentReq
	4,  // 23: game_zjd.Api.Cfg:output_type -> game_zjd.CfgRsp
	13, // 24: game_zjd.Api.Status:output_type -> game_zjd.StatusRsp
	7,  // 25: game_zjd.Api.Ticket:output_type -> game_zjd.TicketRsp
	9,  // 26: game_zjd.Api.CoinPay:output_type -> game_zjd.CoinPayRsp
	11, // 27: game_zjd.Api.CoinPayRes:output_type -> game_zjd.CoinPayResRsp
	17, // 28: game_zjd.Api.Smash:output_type -> game_zjd.SmashRsp
	20, // 29: game_zjd.Api.ReceiveAwards:output_type -> game_zjd.ReceiveAwardsRsp
	22, // 30: game_zjd.Api.GiveUp:output_type -> game_zjd.GiveUpRsp
	24, // 31: game_zjd.Api.ReceiveRecords:output_type -> game_zjd.ReceiveRecordsRsp
	32, // 32: game_zjd.Api.AdCheck:output_type -> game_zjd.AdCheckRsp
	33, // 33: game_zjd.Api.AdRevives:output_type -> game_zjd.AdRevivesRsp
	34, // 34: game_zjd.Api.CoinPayCallback:output_type -> callback.OrderShipmentRsp
	23, // [23:35] is the sub-list for method output_type
	11, // [11:23] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pb_xian_game_zjd_api_router_proto_init() }
func file_pb_xian_game_zjd_api_router_proto_init() {
	if File_pb_xian_game_zjd_api_router_proto != nil {
		return
	}
	file_pb_xian_game_zjd_api_ad_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_game_zjd_api_router_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CfgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CfgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CfgNormal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinPayReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinPayRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinPayResReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinPayResRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoomInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LevelReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmashReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SmashRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Smash); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveAwardsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveAwardsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiveUpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiveUpRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveRecordsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveRecordsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_zjd_api_router_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_game_zjd_api_router_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_xian_game_zjd_api_router_proto_goTypes,
		DependencyIndexes: file_pb_xian_game_zjd_api_router_proto_depIdxs,
		EnumInfos:         file_pb_xian_game_zjd_api_router_proto_enumTypes,
		MessageInfos:      file_pb_xian_game_zjd_api_router_proto_msgTypes,
	}.Build()
	File_pb_xian_game_zjd_api_router_proto = out.File
	file_pb_xian_game_zjd_api_router_proto_rawDesc = nil
	file_pb_xian_game_zjd_api_router_proto_goTypes = nil
	file_pb_xian_game_zjd_api_router_proto_depIdxs = nil
}
