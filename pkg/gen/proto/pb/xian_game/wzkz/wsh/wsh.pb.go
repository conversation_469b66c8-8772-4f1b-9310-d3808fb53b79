// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_game/wzkz/wsh/wsh.proto

package wsh

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	game "kugou_adapter_service/pkg/gen/proto/pb/xian_game/wzkz/game"
	gm "kugou_adapter_service/pkg/gen/proto/pb/xian_game/wzkz/gm"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Code int32

const (
	Code_OK                   Code = 0    // 正常
	Code_NO_ROUTE             Code = 1000 // 房间找不到对应服务器
	Code_NO_ROOM              Code = 1001 // 找不到房间
	Code_NO_DEFINE            Code = 1002 // 无法识别的命令
	Code_NEED_MATCH           Code = 1003 // 需要重新匹配
	Code_FIRE_SYNC_ERR        Code = 1004 // 资产同步失败
	Code_NO_PLANE             Code = 1005 // 敌机找不到
	Code_OP_LIMIT             Code = 1006 // 限流
	Code_NO_FLOWER_LIMIT      Code = 1007 // 没有鲜花射击次数了
	Code_UPD_FLOWER_LIMIT_ERR Code = 1008 // 更新鲜花射击次数失败
	Code_GET_FLOWER_LIMIT_ERR Code = 1009 // 获取鲜花设计次数失败
	Code_NO_USER              Code = 2000 // 找不到用户
	Code_NO_FIRE              Code = 2001 // 没火力(火力不足)
	Code_NO_STAR              Code = 2002 // 没金币(金币不足)
	Code_NO_QUOTA             Code = 2003 // 金币兑换火力额度不足
	Code_NET_BUSY             Code = 2004 // 网络繁忙, redis、数据库操作错误等
	Code_HACK                 Code = 3000 // 非规定数值， 客户端若收到理应踢掉
)

// Enum value maps for Code.
var (
	Code_name = map[int32]string{
		0:    "OK",
		1000: "NO_ROUTE",
		1001: "NO_ROOM",
		1002: "NO_DEFINE",
		1003: "NEED_MATCH",
		1004: "FIRE_SYNC_ERR",
		1005: "NO_PLANE",
		1006: "OP_LIMIT",
		1007: "NO_FLOWER_LIMIT",
		1008: "UPD_FLOWER_LIMIT_ERR",
		1009: "GET_FLOWER_LIMIT_ERR",
		2000: "NO_USER",
		2001: "NO_FIRE",
		2002: "NO_STAR",
		2003: "NO_QUOTA",
		2004: "NET_BUSY",
		3000: "HACK",
	}
	Code_value = map[string]int32{
		"OK":                   0,
		"NO_ROUTE":             1000,
		"NO_ROOM":              1001,
		"NO_DEFINE":            1002,
		"NEED_MATCH":           1003,
		"FIRE_SYNC_ERR":        1004,
		"NO_PLANE":             1005,
		"OP_LIMIT":             1006,
		"NO_FLOWER_LIMIT":      1007,
		"UPD_FLOWER_LIMIT_ERR": 1008,
		"GET_FLOWER_LIMIT_ERR": 1009,
		"NO_USER":              2000,
		"NO_FIRE":              2001,
		"NO_STAR":              2002,
		"NO_QUOTA":             2003,
		"NET_BUSY":             2004,
		"HACK":                 3000,
	}
)

func (x Code) Enum() *Code {
	p := new(Code)
	*p = x
	return p
}

func (x Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Code) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_enumTypes[0].Descriptor()
}

func (Code) Type() protoreflect.EnumType {
	return &file_pb_xian_game_wzkz_wsh_wsh_proto_enumTypes[0]
}

func (x Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Code.Descriptor instead.
func (Code) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{0}
}

type Cmd int32

const (
	Cmd_CHeartbeat Cmd = 0   // 心跳
	Cmd_CShoot     Cmd = 1   // 发弹
	Cmd_CHit       Cmd = 2   // 击中
	Cmd_CMob       Cmd = 3   // 刷怪
	Cmd_CWithhold  Cmd = 97  // 钻石预扣
	Cmd_CRecharge  Cmd = 98  // 用户充值同步
	Cmd_CExchange  Cmd = 99  // 零件兑换火力
	Cmd_CJoin      Cmd = 100 // 加入房间
	Cmd_CLeave     Cmd = 101 // 退出房间
	Cmd_CKick      Cmd = 102 // 被踢出房间
	Cmd_CAddFL     Cmd = 105 // 购买鲜花射击次数
	Cmd_CSyncFL    Cmd = 106 // 同步鲜花射击次数(重置鲜花次数时会用到)
	Cmd_CQuitRoom  Cmd = 107 // 同一个账号不同设备登录互踢
)

// Enum value maps for Cmd.
var (
	Cmd_name = map[int32]string{
		0:   "CHeartbeat",
		1:   "CShoot",
		2:   "CHit",
		3:   "CMob",
		97:  "CWithhold",
		98:  "CRecharge",
		99:  "CExchange",
		100: "CJoin",
		101: "CLeave",
		102: "CKick",
		105: "CAddFL",
		106: "CSyncFL",
		107: "CQuitRoom",
	}
	Cmd_value = map[string]int32{
		"CHeartbeat": 0,
		"CShoot":     1,
		"CHit":       2,
		"CMob":       3,
		"CWithhold":  97,
		"CRecharge":  98,
		"CExchange":  99,
		"CJoin":      100,
		"CLeave":     101,
		"CKick":      102,
		"CAddFL":     105,
		"CSyncFL":    106,
		"CQuitRoom":  107,
	}
)

func (x Cmd) Enum() *Cmd {
	p := new(Cmd)
	*p = x
	return p
}

func (x Cmd) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Cmd) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_enumTypes[1].Descriptor()
}

func (Cmd) Type() protoreflect.EnumType {
	return &file_pb_xian_game_wzkz_wsh_wsh_proto_enumTypes[1]
}

func (x Cmd) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Cmd.Descriptor instead.
func (Cmd) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{1}
}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Code   `protobuf:"varint,1,opt,name=code,proto3,enum=xian_wzkz.Code" json:"code,omitempty"` // 0 - 正常， 其他错误
	Cmd  Cmd    `protobuf:"varint,2,opt,name=cmd,proto3,enum=xian_wzkz.Cmd" json:"cmd,omitempty"`    // 操作的命令
	Fid  uint32 `protobuf:"varint,14,opt,name=fid,proto3" json:"fid,omitempty"`                      // 执行帧ID
	Seq  uint32 `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                      // 操作ID，仅透传
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{0}
}

func (x *Reply) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_OK
}

func (x *Reply) GetCmd() Cmd {
	if x != nil {
		return x.Cmd
	}
	return Cmd_CHeartbeat
}

func (x *Reply) GetFid() uint32 {
	if x != nil {
		return x.Fid
	}
	return 0
}

func (x *Reply) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type CEmpty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seq uint32 `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"` // 操作ID，仅透传
}

func (x *CEmpty) Reset() {
	*x = CEmpty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CEmpty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CEmpty) ProtoMessage() {}

func (x *CEmpty) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CEmpty.ProtoReflect.Descriptor instead.
func (*CEmpty) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{1}
}

func (x *CEmpty) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type JoinReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seq    uint32  `protobuf:"varint,1,opt,name=seq,proto3" json:"seq,omitempty"`        // 操作ID，仅透传
	Aspect float32 `protobuf:"fixed32,2,opt,name=aspect,proto3" json:"aspect,omitempty"` // 客户端高宽比
	Scene  int32   `protobuf:"varint,3,opt,name=scene,proto3" json:"scene,omitempty"`    // 场景
}

func (x *JoinReq) Reset() {
	*x = JoinReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinReq) ProtoMessage() {}

func (x *JoinReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinReq.ProtoReflect.Descriptor instead.
func (*JoinReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{2}
}

func (x *JoinReq) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *JoinReq) GetAspect() float32 {
	if x != nil {
		return x.Aspect
	}
	return 0
}

func (x *JoinReq) GetScene() int32 {
	if x != nil {
		return x.Scene
	}
	return 0
}

type JoinAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seat uint32 `protobuf:"varint,1,opt,name=seat,proto3" json:"seat,omitempty"`                      // 座位号
	Fid  uint32 `protobuf:"varint,14,opt,name=fid,proto3" json:"fid,omitempty"`                       // 执行帧ID
	Seq  uint32 `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                       // 操作ID，仅透传
	Code Code   `protobuf:"varint,16,opt,name=code,proto3,enum=xian_wzkz.Code" json:"code,omitempty"` // 错误码
}

func (x *JoinAck) Reset() {
	*x = JoinAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinAck) ProtoMessage() {}

func (x *JoinAck) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinAck.ProtoReflect.Descriptor instead.
func (*JoinAck) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{3}
}

func (x *JoinAck) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *JoinAck) GetFid() uint32 {
	if x != nil {
		return x.Fid
	}
	return 0
}

func (x *JoinAck) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *JoinAck) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_OK
}

type Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                   // 昵称
	Seat        uint32             `protobuf:"varint,2,opt,name=seat,proto3" json:"seat,omitempty"`                                  // 座位号
	Diamond     int64              `protobuf:"varint,3,opt,name=diamond,proto3" json:"diamond,omitempty"`                            // 网赚金币
	Wt          int64              `protobuf:"varint,4,opt,name=wt,proto3" json:"wt,omitempty"`                                      // 总火力
	Wlv         int64              `protobuf:"varint,5,opt,name=wlv,proto3" json:"wlv,omitempty"`                                    // 火力lv(最后一次发弹火力值)
	Part        int64              `protobuf:"varint,6,opt,name=part,proto3" json:"part,omitempty"`                                  // 零件数量
	Avatar      string             `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`                               // 头像
	Aspect      float32            `protobuf:"fixed32,8,opt,name=aspect,proto3" json:"aspect,omitempty"`                             // 高宽比
	FlowerLimit uint32             `protobuf:"varint,9,opt,name=flower_limit,json=flowerLimit,proto3" json:"flower_limit,omitempty"` // 鲜花射击次数
	Uid         string             `protobuf:"bytes,10,opt,name=uid,proto3" json:"uid,omitempty"`                                    // 用户uid
	FixedPlanes []*FixedBloodPlane `protobuf:"bytes,11,rep,name=fixed_planes,json=fixedPlanes,proto3" json:"fixed_planes,omitempty"` // 固定血量飞机
}

func (x *Player) Reset() {
	*x = Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Player) ProtoMessage() {}

func (x *Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Player.ProtoReflect.Descriptor instead.
func (*Player) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{4}
}

func (x *Player) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Player) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *Player) GetDiamond() int64 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *Player) GetWt() int64 {
	if x != nil {
		return x.Wt
	}
	return 0
}

func (x *Player) GetWlv() int64 {
	if x != nil {
		return x.Wlv
	}
	return 0
}

func (x *Player) GetPart() int64 {
	if x != nil {
		return x.Part
	}
	return 0
}

func (x *Player) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Player) GetAspect() float32 {
	if x != nil {
		return x.Aspect
	}
	return 0
}

func (x *Player) GetFlowerLimit() uint32 {
	if x != nil {
		return x.FlowerLimit
	}
	return 0
}

func (x *Player) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Player) GetFixedPlanes() []*FixedBloodPlane {
	if x != nil {
		return x.FixedPlanes
	}
	return nil
}

type RoomState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fid     uint32        `protobuf:"varint,1,opt,name=fid,proto3" json:"fid,omitempty"`                        // 已同步帧ID
	Players []*Player     `protobuf:"bytes,2,rep,name=players,proto3" json:"players,omitempty"`                 // 玩家信息
	Tracks  []*InputTrack `protobuf:"bytes,3,rep,name=tracks,proto3" json:"tracks,omitempty"`                   // 当前小循环的敌机信息
	Alive   []uint32      `protobuf:"varint,4,rep,packed,name=alive,proto3" json:"alive,omitempty"`             // 存活敌机的实例ID列表
	Seq     uint32        `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                       // 操作ID，仅透传
	Code    Code          `protobuf:"varint,16,opt,name=code,proto3,enum=xian_wzkz.Code" json:"code,omitempty"` // 错误码
}

func (x *RoomState) Reset() {
	*x = RoomState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomState) ProtoMessage() {}

func (x *RoomState) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomState.ProtoReflect.Descriptor instead.
func (*RoomState) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{5}
}

func (x *RoomState) GetFid() uint32 {
	if x != nil {
		return x.Fid
	}
	return 0
}

func (x *RoomState) GetPlayers() []*Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *RoomState) GetTracks() []*InputTrack {
	if x != nil {
		return x.Tracks
	}
	return nil
}

func (x *RoomState) GetAlive() []uint32 {
	if x != nil {
		return x.Alive
	}
	return nil
}

func (x *RoomState) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *RoomState) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_OK
}

type ReissueReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pos uint32 `protobuf:"varint,1,opt,name=pos,proto3" json:"pos,omitempty"`  // 补发起始帧
	Seq uint32 `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"` // 透传序号
}

func (x *ReissueReq) Reset() {
	*x = ReissueReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReissueReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReissueReq) ProtoMessage() {}

func (x *ReissueReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReissueReq.ProtoReflect.Descriptor instead.
func (*ReissueReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{6}
}

func (x *ReissueReq) GetPos() uint32 {
	if x != nil {
		return x.Pos
	}
	return 0
}

func (x *ReissueReq) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type ReissueRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Frames []*Frame `protobuf:"bytes,1,rep,name=frames,proto3" json:"frames,omitempty"`                   // 帧序列, 返回距离最新帧的10帧以内数据
	Seq    uint32   `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                       // 操作ID，仅透传
	Code   Code     `protobuf:"varint,16,opt,name=code,proto3,enum=xian_wzkz.Code" json:"code,omitempty"` // 错误码
}

func (x *ReissueRsp) Reset() {
	*x = ReissueRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReissueRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReissueRsp) ProtoMessage() {}

func (x *ReissueRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReissueRsp.ProtoReflect.Descriptor instead.
func (*ReissueRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{7}
}

func (x *ReissueRsp) GetFrames() []*Frame {
	if x != nil {
		return x.Frames
	}
	return nil
}

func (x *ReissueRsp) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ReissueRsp) GetCode() Code {
	if x != nil {
		return x.Code
	}
	return Code_OK
}

// 帧输入
type InputOp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                      // 房间内操作ID
	Cmd Cmd    `protobuf:"varint,2,opt,name=cmd,proto3,enum=xian_wzkz.Cmd" json:"cmd,omitempty"` // 操作类型
	// Types that are assignable to Data:
	//
	//	*InputOp_Shoot
	//	*InputOp_Hit
	//	*InputOp_Track
	//	*InputOp_Withhold
	//	*InputOp_Recharge
	//	*InputOp_Exchange
	//	*InputOp_Join
	//	*InputOp_Leave
	//	*InputOp_AddFL
	//	*InputOp_SyncFL
	//	*InputOp_QuitRoom
	Data isInputOp_Data `protobuf_oneof:"data"`
	Seq  uint32         `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"` // 透传序号
}

func (x *InputOp) Reset() {
	*x = InputOp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputOp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputOp) ProtoMessage() {}

func (x *InputOp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputOp.ProtoReflect.Descriptor instead.
func (*InputOp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{8}
}

func (x *InputOp) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InputOp) GetCmd() Cmd {
	if x != nil {
		return x.Cmd
	}
	return Cmd_CHeartbeat
}

func (m *InputOp) GetData() isInputOp_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *InputOp) GetShoot() *InputShoot {
	if x, ok := x.GetData().(*InputOp_Shoot); ok {
		return x.Shoot
	}
	return nil
}

func (x *InputOp) GetHit() *InputHit {
	if x, ok := x.GetData().(*InputOp_Hit); ok {
		return x.Hit
	}
	return nil
}

func (x *InputOp) GetTrack() *InputTrack {
	if x, ok := x.GetData().(*InputOp_Track); ok {
		return x.Track
	}
	return nil
}

func (x *InputOp) GetWithhold() *OutputWithhold {
	if x, ok := x.GetData().(*InputOp_Withhold); ok {
		return x.Withhold
	}
	return nil
}

func (x *InputOp) GetRecharge() *InputRecharge {
	if x, ok := x.GetData().(*InputOp_Recharge); ok {
		return x.Recharge
	}
	return nil
}

func (x *InputOp) GetExchange() *InputExchange {
	if x, ok := x.GetData().(*InputOp_Exchange); ok {
		return x.Exchange
	}
	return nil
}

func (x *InputOp) GetJoin() *InputJoin {
	if x, ok := x.GetData().(*InputOp_Join); ok {
		return x.Join
	}
	return nil
}

func (x *InputOp) GetLeave() *InputLeave {
	if x, ok := x.GetData().(*InputOp_Leave); ok {
		return x.Leave
	}
	return nil
}

func (x *InputOp) GetAddFL() *InputAddFL {
	if x, ok := x.GetData().(*InputOp_AddFL); ok {
		return x.AddFL
	}
	return nil
}

func (x *InputOp) GetSyncFL() *InputSyncFL {
	if x, ok := x.GetData().(*InputOp_SyncFL); ok {
		return x.SyncFL
	}
	return nil
}

func (x *InputOp) GetQuitRoom() *InputQuitRoom {
	if x, ok := x.GetData().(*InputOp_QuitRoom); ok {
		return x.QuitRoom
	}
	return nil
}

func (x *InputOp) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type isInputOp_Data interface {
	isInputOp_Data()
}

type InputOp_Shoot struct {
	// 发弹
	Shoot *InputShoot `protobuf:"bytes,5,opt,name=shoot,proto3,oneof"`
}

type InputOp_Hit struct {
	// 击中
	Hit *InputHit `protobuf:"bytes,6,opt,name=hit,proto3,oneof"`
}

type InputOp_Track struct {
	// 敌机刷新
	Track *InputTrack `protobuf:"bytes,7,opt,name=track,proto3,oneof"`
}

type InputOp_Withhold struct {
	// 钻石预扣
	Withhold *OutputWithhold `protobuf:"bytes,97,opt,name=withhold,proto3,oneof"`
}

type InputOp_Recharge struct {
	// 充值火力
	Recharge *InputRecharge `protobuf:"bytes,98,opt,name=recharge,proto3,oneof"`
}

type InputOp_Exchange struct {
	// 兑换火力
	Exchange *InputExchange `protobuf:"bytes,99,opt,name=exchange,proto3,oneof"`
}

type InputOp_Join struct {
	// 用户加入
	Join *InputJoin `protobuf:"bytes,100,opt,name=join,proto3,oneof"`
}

type InputOp_Leave struct {
	// 用户退出
	Leave *InputLeave `protobuf:"bytes,101,opt,name=leave,proto3,oneof"`
}

type InputOp_AddFL struct {
	// 购买鲜花次数
	AddFL *InputAddFL `protobuf:"bytes,105,opt,name=add_f_l,json=addFL,proto3,oneof"`
}

type InputOp_SyncFL struct {
	// 同步鲜花次数
	SyncFL *InputSyncFL `protobuf:"bytes,106,opt,name=sync_f_l,json=syncFL,proto3,oneof"`
}

type InputOp_QuitRoom struct {
	// 同一账号多设备登录互踢
	QuitRoom *InputQuitRoom `protobuf:"bytes,107,opt,name=quit_room,json=quitRoom,proto3,oneof"`
}

func (*InputOp_Shoot) isInputOp_Data() {}

func (*InputOp_Hit) isInputOp_Data() {}

func (*InputOp_Track) isInputOp_Data() {}

func (*InputOp_Withhold) isInputOp_Data() {}

func (*InputOp_Recharge) isInputOp_Data() {}

func (*InputOp_Exchange) isInputOp_Data() {}

func (*InputOp_Join) isInputOp_Data() {}

func (*InputOp_Leave) isInputOp_Data() {}

func (*InputOp_AddFL) isInputOp_Data() {}

func (*InputOp_SyncFL) isInputOp_Data() {}

func (*InputOp_QuitRoom) isInputOp_Data() {}

// 预扣输出
type OutputWithhold struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seat    uint32 `protobuf:"varint,1,opt,name=seat,proto3" json:"seat,omitempty"`       // 座位号
	Diamond int64  `protobuf:"varint,2,opt,name=diamond,proto3" json:"diamond,omitempty"` // 网赚金币余额
	Delta   int64  `protobuf:"varint,3,opt,name=delta,proto3" json:"delta,omitempty"`     // 本次钻石减少(=火力增加)额度
}

func (x *OutputWithhold) Reset() {
	*x = OutputWithhold{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutputWithhold) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputWithhold) ProtoMessage() {}

func (x *OutputWithhold) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputWithhold.ProtoReflect.Descriptor instead.
func (*OutputWithhold) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{9}
}

func (x *OutputWithhold) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *OutputWithhold) GetDiamond() int64 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *OutputWithhold) GetDelta() int64 {
	if x != nil {
		return x.Delta
	}
	return 0
}

type InputExchange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wt    int64  `protobuf:"varint,1,opt,name=wt,proto3" json:"wt,omitempty"`       // 获得火力
	Wt2   int64  `protobuf:"varint,2,opt,name=wt2,proto3" json:"wt2,omitempty"`     // 消耗金币
	Quota int64  `protobuf:"varint,3,opt,name=quota,proto3" json:"quota,omitempty"` // 剩余可兑换数量
	Et    int32  `protobuf:"varint,4,opt,name=et,proto3" json:"et,omitempty"`       //  兑换类型, (0:其他道具兑火力) (1:金币兑换火力)
	Seat  uint32 `protobuf:"varint,15,opt,name=seat,proto3" json:"seat,omitempty"`  // 座位号
}

func (x *InputExchange) Reset() {
	*x = InputExchange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputExchange) ProtoMessage() {}

func (x *InputExchange) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputExchange.ProtoReflect.Descriptor instead.
func (*InputExchange) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{10}
}

func (x *InputExchange) GetWt() int64 {
	if x != nil {
		return x.Wt
	}
	return 0
}

func (x *InputExchange) GetWt2() int64 {
	if x != nil {
		return x.Wt2
	}
	return 0
}

func (x *InputExchange) GetQuota() int64 {
	if x != nil {
		return x.Quota
	}
	return 0
}

func (x *InputExchange) GetEt() int32 {
	if x != nil {
		return x.Et
	}
	return 0
}

func (x *InputExchange) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

type InputRecharge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Diamond int64  `protobuf:"varint,1,opt,name=diamond,proto3" json:"diamond,omitempty"` // 网赚金币余额
	Seat    uint32 `protobuf:"varint,15,opt,name=seat,proto3" json:"seat,omitempty"`      // 座位号
}

func (x *InputRecharge) Reset() {
	*x = InputRecharge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputRecharge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputRecharge) ProtoMessage() {}

func (x *InputRecharge) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputRecharge.ProtoReflect.Descriptor instead.
func (*InputRecharge) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{11}
}

func (x *InputRecharge) GetDiamond() int64 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *InputRecharge) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

type InputJoin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string             `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                   // 用户昵称
	Wt          int64              `protobuf:"varint,2,opt,name=wt,proto3" json:"wt,omitempty"`                                      // 用户总火力值
	Wt2         int64              `protobuf:"varint,3,opt,name=wt2,proto3" json:"wt2,omitempty"`                                    // 零件
	Diamond     int64              `protobuf:"varint,4,opt,name=diamond,proto3" json:"diamond,omitempty"`                            // 网赚金币
	Seat        uint32             `protobuf:"varint,5,opt,name=seat,proto3" json:"seat,omitempty"`                                  // 座位
	Avatar      string             `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`                               // 头像
	Aspect      float32            `protobuf:"fixed32,7,opt,name=aspect,proto3" json:"aspect,omitempty"`                             // 高宽比
	FlowerLimit uint32             `protobuf:"varint,8,opt,name=flower_limit,json=flowerLimit,proto3" json:"flower_limit,omitempty"` // 鲜花场射击次数
	Uid         string             `protobuf:"bytes,9,opt,name=uid,proto3" json:"uid,omitempty"`
	FixedPlanes []*FixedBloodPlane `protobuf:"bytes,10,rep,name=fixed_planes,json=fixedPlanes,proto3" json:"fixed_planes,omitempty"` // 固定血量飞机
	Seq         uint32             `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                                   // 操作ID，仅透传
}

func (x *InputJoin) Reset() {
	*x = InputJoin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputJoin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputJoin) ProtoMessage() {}

func (x *InputJoin) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputJoin.ProtoReflect.Descriptor instead.
func (*InputJoin) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{12}
}

func (x *InputJoin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InputJoin) GetWt() int64 {
	if x != nil {
		return x.Wt
	}
	return 0
}

func (x *InputJoin) GetWt2() int64 {
	if x != nil {
		return x.Wt2
	}
	return 0
}

func (x *InputJoin) GetDiamond() int64 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *InputJoin) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *InputJoin) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *InputJoin) GetAspect() float32 {
	if x != nil {
		return x.Aspect
	}
	return 0
}

func (x *InputJoin) GetFlowerLimit() uint32 {
	if x != nil {
		return x.FlowerLimit
	}
	return 0
}

func (x *InputJoin) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *InputJoin) GetFixedPlanes() []*FixedBloodPlane {
	if x != nil {
		return x.FixedPlanes
	}
	return nil
}

func (x *InputJoin) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type FixedBloodPlane struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plane    *game.Plane `protobuf:"bytes,1,opt,name=plane,proto3" json:"plane,omitempty"`        // 飞机内容: id, name
	Blood    int64       `protobuf:"varint,2,opt,name=blood,proto3" json:"blood,omitempty"`       // 飞机的血量
	Cumulant int64       `protobuf:"varint,3,opt,name=cumulant,proto3" json:"cumulant,omitempty"` // 累计量
	Ttl      uint32      `protobuf:"varint,4,opt,name=ttl,proto3" json:"ttl,omitempty"`           // 过期秒数
	Gift     *game.Gift  `protobuf:"bytes,5,opt,name=gift,proto3" json:"gift,omitempty"`          // 礼物
}

func (x *FixedBloodPlane) Reset() {
	*x = FixedBloodPlane{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FixedBloodPlane) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FixedBloodPlane) ProtoMessage() {}

func (x *FixedBloodPlane) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FixedBloodPlane.ProtoReflect.Descriptor instead.
func (*FixedBloodPlane) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{13}
}

func (x *FixedBloodPlane) GetPlane() *game.Plane {
	if x != nil {
		return x.Plane
	}
	return nil
}

func (x *FixedBloodPlane) GetBlood() int64 {
	if x != nil {
		return x.Blood
	}
	return 0
}

func (x *FixedBloodPlane) GetCumulant() int64 {
	if x != nil {
		return x.Cumulant
	}
	return 0
}

func (x *FixedBloodPlane) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

func (x *FixedBloodPlane) GetGift() *game.Gift {
	if x != nil {
		return x.Gift
	}
	return nil
}

type InputLeave struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seat uint32 `protobuf:"varint,1,opt,name=seat,proto3" json:"seat,omitempty"` // 座位
	Uid  string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Seq  uint32 `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"` // 操作ID，仅透传
}

func (x *InputLeave) Reset() {
	*x = InputLeave{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputLeave) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputLeave) ProtoMessage() {}

func (x *InputLeave) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputLeave.ProtoReflect.Descriptor instead.
func (*InputLeave) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{14}
}

func (x *InputLeave) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *InputLeave) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *InputLeave) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type InputAddFL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlowerLimit uint32 `protobuf:"varint,1,opt,name=flower_limit,json=flowerLimit,proto3" json:"flower_limit,omitempty"` // 鲜花总发弹次数
	Seat        uint32 `protobuf:"varint,2,opt,name=seat,proto3" json:"seat,omitempty"`                                  // 座位号
}

func (x *InputAddFL) Reset() {
	*x = InputAddFL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputAddFL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputAddFL) ProtoMessage() {}

func (x *InputAddFL) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputAddFL.ProtoReflect.Descriptor instead.
func (*InputAddFL) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{15}
}

func (x *InputAddFL) GetFlowerLimit() uint32 {
	if x != nil {
		return x.FlowerLimit
	}
	return 0
}

func (x *InputAddFL) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

type InputSyncFL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FlowerLimit uint32 `protobuf:"varint,1,opt,name=flower_limit,json=flowerLimit,proto3" json:"flower_limit,omitempty"`
	Seat        uint32 `protobuf:"varint,2,opt,name=seat,proto3" json:"seat,omitempty"`
}

func (x *InputSyncFL) Reset() {
	*x = InputSyncFL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputSyncFL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputSyncFL) ProtoMessage() {}

func (x *InputSyncFL) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputSyncFL.ProtoReflect.Descriptor instead.
func (*InputSyncFL) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{16}
}

func (x *InputSyncFL) GetFlowerLimit() uint32 {
	if x != nil {
		return x.FlowerLimit
	}
	return 0
}

func (x *InputSyncFL) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

type InputQuitRoom struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seat uint32 `protobuf:"varint,1,opt,name=seat,proto3" json:"seat,omitempty"`
}

func (x *InputQuitRoom) Reset() {
	*x = InputQuitRoom{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputQuitRoom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputQuitRoom) ProtoMessage() {}

func (x *InputQuitRoom) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputQuitRoom.ProtoReflect.Descriptor instead.
func (*InputQuitRoom) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{17}
}

func (x *InputQuitRoom) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

type InputShoot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                      // 子弹ID
	Seat        uint32 `protobuf:"varint,2,opt,name=seat,proto3" json:"seat,omitempty"`                                  // 座位
	X           int32  `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`                                        // 操作x
	Y           int32  `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`                                        // 操作y
	Wager       int64  `protobuf:"varint,5,opt,name=wager,proto3" json:"wager,omitempty"`                                // 操作子弹火力值
	Miid        uint32 `protobuf:"varint,6,opt,name=miid,proto3" json:"miid,omitempty"`                                  // 跟踪敌机ID
	FlowerLimit uint32 `protobuf:"varint,7,opt,name=flower_limit,json=flowerLimit,proto3" json:"flower_limit,omitempty"` // 鲜花次数
	Delta       int64  `protobuf:"varint,11,opt,name=delta,proto3" json:"delta,omitempty"`                               // 资产转换额度
	Ext         uint32 `protobuf:"varint,12,opt,name=ext,proto3" json:"ext,omitempty"`                                   // 0-保留,1-转换资产(发射)
	Fid         uint32 `protobuf:"varint,13,opt,name=fid,proto3" json:"fid,omitempty"`                                   // 发射帧ID-必填
	Wt          int64  `protobuf:"varint,14,opt,name=wt,proto3" json:"wt,omitempty"`                                     // 实际持有火力
	Seq         uint32 `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                                   // 操作ID，仅透传
}

func (x *InputShoot) Reset() {
	*x = InputShoot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputShoot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputShoot) ProtoMessage() {}

func (x *InputShoot) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputShoot.ProtoReflect.Descriptor instead.
func (*InputShoot) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{18}
}

func (x *InputShoot) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InputShoot) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *InputShoot) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *InputShoot) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *InputShoot) GetWager() int64 {
	if x != nil {
		return x.Wager
	}
	return 0
}

func (x *InputShoot) GetMiid() uint32 {
	if x != nil {
		return x.Miid
	}
	return 0
}

func (x *InputShoot) GetFlowerLimit() uint32 {
	if x != nil {
		return x.FlowerLimit
	}
	return 0
}

func (x *InputShoot) GetDelta() int64 {
	if x != nil {
		return x.Delta
	}
	return 0
}

func (x *InputShoot) GetExt() uint32 {
	if x != nil {
		return x.Ext
	}
	return 0
}

func (x *InputShoot) GetFid() uint32 {
	if x != nil {
		return x.Fid
	}
	return 0
}

func (x *InputShoot) GetWt() int64 {
	if x != nil {
		return x.Wt
	}
	return 0
}

func (x *InputShoot) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type InputHit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                   // 子弹ID
	Seat       uint32           `protobuf:"varint,2,opt,name=seat,proto3" json:"seat,omitempty"`                               // 座位
	X          int32            `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`                                     // 碰撞x
	Y          int32            `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`                                     // 碰撞y
	Wager      int64            `protobuf:"varint,5,opt,name=wager,proto3" json:"wager,omitempty"`                             // 碰撞子弹火力值
	Plane      []*Fighter       `protobuf:"bytes,6,rep,name=plane,proto3" json:"plane,omitempty"`                              // 碰撞的敌机数组. 如果一堆飞机叠在一起, 将这些飞机都传上来; 如果这堆飞机中有炸弹飞机, 那么数组第一位是炸弹飞机且只传炸弹飞机
	Obtain     int64            `protobuf:"varint,7,opt,name=obtain,proto3" json:"obtain,omitempty"`                           // 获得的零件，0则表示概率miss
	OtherPlane []*Fighter       `protobuf:"bytes,8,rep,name=other_plane,json=otherPlane,proto3" json:"other_plane,omitempty"`  // 当plane中只有炸弹飞机的时候, 用这个字段装其他飞机
	Gifts      []*game.Gift     `protobuf:"bytes,9,rep,name=gifts,proto3" json:"gifts,omitempty"`                              // 获得的礼物列表
	GiftAsset  int64            `protobuf:"varint,10,opt,name=gift_asset,json=giftAsset,proto3" json:"gift_asset,omitempty"`   // 礼物总价值
	FixedPlane *FixedBloodPlane `protobuf:"bytes,11,opt,name=fixed_plane,json=fixedPlane,proto3" json:"fixed_plane,omitempty"` // 本次打的固定血量飞机的信息
	Fid        uint32           `protobuf:"varint,14,opt,name=fid,proto3" json:"fid,omitempty"`                                // 碰撞帧ID-必填
	Seq        uint32           `protobuf:"varint,15,opt,name=seq,proto3" json:"seq,omitempty"`                                // 操作ID，仅透传
}

func (x *InputHit) Reset() {
	*x = InputHit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputHit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputHit) ProtoMessage() {}

func (x *InputHit) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputHit.ProtoReflect.Descriptor instead.
func (*InputHit) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{19}
}

func (x *InputHit) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InputHit) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *InputHit) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *InputHit) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *InputHit) GetWager() int64 {
	if x != nil {
		return x.Wager
	}
	return 0
}

func (x *InputHit) GetPlane() []*Fighter {
	if x != nil {
		return x.Plane
	}
	return nil
}

func (x *InputHit) GetObtain() int64 {
	if x != nil {
		return x.Obtain
	}
	return 0
}

func (x *InputHit) GetOtherPlane() []*Fighter {
	if x != nil {
		return x.OtherPlane
	}
	return nil
}

func (x *InputHit) GetGifts() []*game.Gift {
	if x != nil {
		return x.Gifts
	}
	return nil
}

func (x *InputHit) GetGiftAsset() int64 {
	if x != nil {
		return x.GiftAsset
	}
	return 0
}

func (x *InputHit) GetFixedPlane() *FixedBloodPlane {
	if x != nil {
		return x.FixedPlane
	}
	return nil
}

func (x *InputHit) GetFid() uint32 {
	if x != nil {
		return x.Fid
	}
	return 0
}

func (x *InputHit) GetSeq() uint32 {
	if x != nil {
		return x.Seq
	}
	return 0
}

// 敌机[组成员]
type Fighter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mid    uint32 `protobuf:"varint,1,opt,name=mid,proto3" json:"mid,omitempty"`       // 敌机类ID
	Miid   uint32 `protobuf:"varint,2,opt,name=miid,proto3" json:"miid,omitempty"`     // 敌机实例编号
	X      int32  `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`           // 偏移x
	Y      int32  `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`           // 偏移y
	Dead   bool   `protobuf:"varint,5,opt,name=dead,proto3" json:"dead,omitempty"`     // 是否死亡
	Obtain int64  `protobuf:"varint,6,opt,name=obtain,proto3" json:"obtain,omitempty"` // 获得
}

func (x *Fighter) Reset() {
	*x = Fighter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fighter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fighter) ProtoMessage() {}

func (x *Fighter) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fighter.ProtoReflect.Descriptor instead.
func (*Fighter) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{20}
}

func (x *Fighter) GetMid() uint32 {
	if x != nil {
		return x.Mid
	}
	return 0
}

func (x *Fighter) GetMiid() uint32 {
	if x != nil {
		return x.Miid
	}
	return 0
}

func (x *Fighter) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Fighter) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *Fighter) GetDead() bool {
	if x != nil {
		return x.Dead
	}
	return false
}

func (x *Fighter) GetObtain() int64 {
	if x != nil {
		return x.Obtain
	}
	return 0
}

// 轨迹
type InputTrack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid     uint32     `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`        // 路径ID
	Dir     uint32     `protobuf:"varint,2,opt,name=dir,proto3" json:"dir,omitempty"`        // 路径起始索引(即路径方向, 0-正向，1-反向)
	Fighter []*Fighter `protobuf:"bytes,3,rep,name=fighter,proto3" json:"fighter,omitempty"` // 敌机[组]
	Speed   uint32     `protobuf:"varint,4,opt,name=speed,proto3" json:"speed,omitempty"`    // 速度
	Fid     uint32     `protobuf:"varint,15,opt,name=fid,proto3" json:"fid,omitempty"`       // 出现帧
	Fend    uint32     `protobuf:"varint,16,opt,name=fend,proto3" json:"fend,omitempty"`     // 消失帧
}

func (x *InputTrack) Reset() {
	*x = InputTrack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputTrack) ProtoMessage() {}

func (x *InputTrack) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputTrack.ProtoReflect.Descriptor instead.
func (*InputTrack) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{21}
}

func (x *InputTrack) GetTid() uint32 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *InputTrack) GetDir() uint32 {
	if x != nil {
		return x.Dir
	}
	return 0
}

func (x *InputTrack) GetFighter() []*Fighter {
	if x != nil {
		return x.Fighter
	}
	return nil
}

func (x *InputTrack) GetSpeed() uint32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *InputTrack) GetFid() uint32 {
	if x != nil {
		return x.Fid
	}
	return 0
}

func (x *InputTrack) GetFend() uint32 {
	if x != nil {
		return x.Fend
	}
	return 0
}

// 帧数据
type Frame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`      // 帧ID
	St    int64      `protobuf:"varint,2,opt,name=st,proto3" json:"st,omitempty"`      // 服务器时间
	Input []*InputOp `protobuf:"bytes,3,rep,name=input,proto3" json:"input,omitempty"` // 操作输入
}

func (x *Frame) Reset() {
	*x = Frame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Frame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Frame) ProtoMessage() {}

func (x *Frame) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Frame.ProtoReflect.Descriptor instead.
func (*Frame) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP(), []int{22}
}

func (x *Frame) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Frame) GetSt() int64 {
	if x != nil {
		return x.St
	}
	return 0
}

func (x *Frame) GetInput() []*InputOp {
	if x != nil {
		return x.Input
	}
	return nil
}

var File_pb_xian_game_wzkz_wsh_wsh_proto protoreflect.FileDescriptor

var file_pb_xian_game_wzkz_wsh_wsh_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2f, 0x77, 0x73, 0x68, 0x2f, 0x77, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x1a, 0x21, 0x70, 0x62,
	0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a, 0x6b, 0x7a, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2f, 0x67, 0x6d, 0x2f, 0x67, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72,
	0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x23, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x03,
	0x63, 0x6d, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6d, 0x64, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x66, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73,
	0x65, 0x71, 0x22, 0x1a, 0x0a, 0x06, 0x43, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x22, 0x49,
	0x0a, 0x07, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x61, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x66, 0x0a, 0x07, 0x4a, 0x6f, 0x69,
	0x6e, 0x41, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x66, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65,
	0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0xa4, 0x02, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x73, 0x65, 0x61, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x77, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x77, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x77, 0x6c, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x77, 0x6c, 0x76,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x70, 0x61, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x73, 0x70, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x61, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x66, 0x6c, 0x6f, 0x77,
	0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x52, 0x0b, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x73, 0x22, 0xc6, 0x01, 0x0a, 0x09, 0x52, 0x6f, 0x6f,
	0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x66, 0x69, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x06, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x05, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65,
	0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x22, 0x30, 0x0a, 0x0a, 0x52, 0x65, 0x69, 0x73, 0x73, 0x75, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x6f,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x73, 0x65, 0x71, 0x22, 0x6d, 0x0a, 0x0a, 0x52, 0x65, 0x69, 0x73, 0x73, 0x75, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x28, 0x0a, 0x06, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x52, 0x06, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x23, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0xfe, 0x04, 0x0a, 0x07, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20,
	0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6d, 0x64, 0x52, 0x03, 0x63, 0x6d, 0x64,
	0x12, 0x2d, 0x0a, 0x05, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x53, 0x68, 0x6f, 0x6f, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73, 0x68, 0x6f, 0x6f, 0x74, 0x12,
	0x27, 0x0a, 0x03, 0x68, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x48, 0x69,
	0x74, 0x48, 0x00, 0x52, 0x03, 0x68, 0x69, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x48, 0x00,
	0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x37, 0x0a, 0x08, 0x77, 0x69, 0x74, 0x68, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x61, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x57, 0x69, 0x74, 0x68,
	0x68, 0x6f, 0x6c, 0x64, 0x48, 0x00, 0x52, 0x08, 0x77, 0x69, 0x74, 0x68, 0x68, 0x6f, 0x6c, 0x64,
	0x12, 0x36, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x62, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x48, 0x00, 0x52, 0x08,
	0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x08, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x2a, 0x0a, 0x04, 0x6a, 0x6f, 0x69, 0x6e, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x4a, 0x6f, 0x69, 0x6e, 0x48, 0x00, 0x52, 0x04, 0x6a, 0x6f, 0x69, 0x6e, 0x12, 0x2d, 0x0a, 0x05,
	0x6c, 0x65, 0x61, 0x76, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4c, 0x65, 0x61,
	0x76, 0x65, 0x48, 0x00, 0x52, 0x05, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x61,
	0x64, 0x64, 0x5f, 0x66, 0x5f, 0x6c, 0x18, 0x69, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x64,
	0x64, 0x46, 0x4c, 0x48, 0x00, 0x52, 0x05, 0x61, 0x64, 0x64, 0x46, 0x4c, 0x12, 0x32, 0x0a, 0x08,
	0x73, 0x79, 0x6e, 0x63, 0x5f, 0x66, 0x5f, 0x6c, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x53, 0x79, 0x6e, 0x63, 0x46, 0x4c, 0x48, 0x00, 0x52, 0x06, 0x73, 0x79, 0x6e, 0x63, 0x46, 0x4c,
	0x12, 0x37, 0x0a, 0x09, 0x71, 0x75, 0x69, 0x74, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x6b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x51, 0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x48, 0x00, 0x52,
	0x08, 0x71, 0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x42, 0x06, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x0e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x61,
	0x6d, 0x6f, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x69, 0x61, 0x6d,
	0x6f, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x22, 0x6b, 0x0a, 0x0d, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x77, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x77, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x74,
	0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x77, 0x74, 0x32, 0x12, 0x14, 0x0a, 0x05,
	0x71, 0x75, 0x6f, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x71, 0x75, 0x6f,
	0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x22, 0x3d, 0x0a, 0x0d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f,
	0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x73, 0x65, 0x61, 0x74, 0x22, 0xa5, 0x02, 0x0a, 0x09, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4a,
	0x6f, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x77, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x77, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x74, 0x32, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x77, 0x74, 0x32, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x61,
	0x6d, 0x6f, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x69, 0x61, 0x6d,
	0x6f, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x73, 0x70, 0x65, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x06, 0x61, 0x73, 0x70, 0x65, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x66,
	0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3d, 0x0a, 0x0c,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x52, 0x0b,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x22, 0xa2, 0x01,
	0x0a, 0x0f, 0x46, 0x69, 0x78, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x50, 0x6c, 0x61, 0x6e,
	0x65, 0x12, 0x26, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x50, 0x6c, 0x61,
	0x6e, 0x65, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x6c, 0x6f,
	0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x62, 0x6c, 0x6f, 0x6f, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x74,
	0x74, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x12, 0x23, 0x0a,
	0x04, 0x67, 0x69, 0x66, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x52, 0x04, 0x67, 0x69,
	0x66, 0x74, 0x22, 0x44, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x73, 0x65, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71, 0x22, 0x43, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x41, 0x64, 0x64, 0x46, 0x4c, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x66, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x22, 0x44, 0x0a,
	0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x4c, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73,
	0x65, 0x61, 0x74, 0x22, 0x23, 0x0a, 0x0d, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x51, 0x75, 0x69, 0x74,
	0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x22, 0xf5, 0x01, 0x0a, 0x0a, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x53, 0x68, 0x6f, 0x6f, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x61, 0x67, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x77, 0x61, 0x67, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x69, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6d, 0x69, 0x69,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x65, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x66, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x66, 0x69, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x77, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x77, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65, 0x71,
	0x22, 0xfe, 0x02, 0x0a, 0x08, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x48, 0x69, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x61,
	0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12,
	0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x77, 0x61, 0x67, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x77, 0x61,
	0x67, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x46,
	0x69, 0x67, 0x68, 0x74, 0x65, 0x72, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f,
	0x62, 0x74, 0x61, 0x69, 0x6e, 0x12, 0x33, 0x0a, 0x0b, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x46, 0x69, 0x67, 0x68, 0x74, 0x65, 0x72, 0x52, 0x0a,
	0x6f, 0x74, 0x68, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x67, 0x69,
	0x66, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x52, 0x05, 0x67, 0x69, 0x66, 0x74,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x3b, 0x0a, 0x0b, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x50, 0x6c, 0x61, 0x6e,
	0x65, 0x52, 0x0a, 0x66, 0x69, 0x78, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x66, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x66, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x73, 0x65,
	0x71, 0x22, 0x77, 0x0a, 0x07, 0x46, 0x69, 0x67, 0x68, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x69, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6d, 0x69,
	0x69, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78,
	0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x64, 0x65,
	0x61, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x0a, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x64,
	0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x64, 0x69, 0x72, 0x12, 0x2c, 0x0a,
	0x07, 0x66, 0x69, 0x67, 0x68, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x46, 0x69, 0x67, 0x68, 0x74,
	0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x67, 0x68, 0x74, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03,
	0x66, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x65, 0x6e, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x66, 0x65, 0x6e, 0x64, 0x22, 0x51, 0x0a, 0x05, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x4f, 0x70, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x2a, 0x9d, 0x02, 0x0a, 0x04, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x08, 0x4e,
	0x4f, 0x5f, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x10, 0xe8, 0x07, 0x12, 0x0c, 0x0a, 0x07, 0x4e, 0x4f,
	0x5f, 0x52, 0x4f, 0x4f, 0x4d, 0x10, 0xe9, 0x07, 0x12, 0x0e, 0x0a, 0x09, 0x4e, 0x4f, 0x5f, 0x44,
	0x45, 0x46, 0x49, 0x4e, 0x45, 0x10, 0xea, 0x07, 0x12, 0x0f, 0x0a, 0x0a, 0x4e, 0x45, 0x45, 0x44,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xeb, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x46, 0x49, 0x52,
	0x45, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xec, 0x07, 0x12, 0x0d, 0x0a,
	0x08, 0x4e, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x45, 0x10, 0xed, 0x07, 0x12, 0x0d, 0x0a, 0x08,
	0x4f, 0x50, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xee, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x4e,
	0x4f, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xef,
	0x07, 0x12, 0x19, 0x0a, 0x14, 0x55, 0x50, 0x44, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x10, 0xf0, 0x07, 0x12, 0x19, 0x0a, 0x14,
	0x47, 0x45, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x45, 0x52, 0x52, 0x10, 0xf1, 0x07, 0x12, 0x0c, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x55, 0x53,
	0x45, 0x52, 0x10, 0xd0, 0x0f, 0x12, 0x0c, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x46, 0x49, 0x52, 0x45,
	0x10, 0xd1, 0x0f, 0x12, 0x0c, 0x0a, 0x07, 0x4e, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x10, 0xd2,
	0x0f, 0x12, 0x0d, 0x0a, 0x08, 0x4e, 0x4f, 0x5f, 0x51, 0x55, 0x4f, 0x54, 0x41, 0x10, 0xd3, 0x0f,
	0x12, 0x0d, 0x0a, 0x08, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x55, 0x53, 0x59, 0x10, 0xd4, 0x0f, 0x12,
	0x09, 0x0a, 0x04, 0x48, 0x41, 0x43, 0x4b, 0x10, 0xb8, 0x17, 0x2a, 0xac, 0x01, 0x0a, 0x03, 0x43,
	0x6d, 0x64, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x53, 0x68, 0x6f, 0x6f, 0x74, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x43, 0x48, 0x69, 0x74, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x4d, 0x6f, 0x62,
	0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x57, 0x69, 0x74, 0x68, 0x68, 0x6f, 0x6c, 0x64, 0x10,
	0x61, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x10, 0x62,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x10, 0x63, 0x12,
	0x09, 0x0a, 0x05, 0x43, 0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x64, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x4c,
	0x65, 0x61, 0x76, 0x65, 0x10, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x43, 0x4b, 0x69, 0x63, 0x6b, 0x10,
	0x66, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x41, 0x64, 0x64, 0x46, 0x4c, 0x10, 0x69, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x4c, 0x10, 0x6a, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x51,
	0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x10, 0x6b, 0x32, 0xec, 0x02, 0x0a, 0x03, 0x57, 0x73,
	0x68, 0x12, 0x2e, 0x0a, 0x04, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x12, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x41, 0x63,
	0x6b, 0x12, 0x2f, 0x0a, 0x04, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x52, 0x65, 0x69, 0x73, 0x73, 0x75, 0x65, 0x12, 0x15, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x52, 0x65, 0x69, 0x73, 0x73, 0x75, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x06, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x1a, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x4c,
	0x65, 0x61, 0x76, 0x65, 0x12, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x43, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x52, 0x6f, 0x6f,
	0x6d, 0x73, 0x12, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x6f, 0x6f, 0x6d, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x34, 0x0a, 0x04, 0x4b, 0x69, 0x63, 0x6b, 0x12, 0x15, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x4b, 0x69, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d,
	0x2e, 0x4b, 0x69, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x42, 0x4e, 0x5a, 0x4c, 0x67, 0x69, 0x74, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a, 0x6b, 0x7a, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f,
	0x77, 0x7a, 0x6b, 0x7a, 0x2f, 0x77, 0x73, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescOnce sync.Once
	file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescData = file_pb_xian_game_wzkz_wsh_wsh_proto_rawDesc
)

func file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescGZIP() []byte {
	file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescOnce.Do(func() {
		file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescData)
	})
	return file_pb_xian_game_wzkz_wsh_wsh_proto_rawDescData
}

var file_pb_xian_game_wzkz_wsh_wsh_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_pb_xian_game_wzkz_wsh_wsh_proto_goTypes = []interface{}{
	(Code)(0),                // 0: xian_wzkz.Code
	(Cmd)(0),                 // 1: xian_wzkz.Cmd
	(*Reply)(nil),            // 2: xian_wzkz.Reply
	(*CEmpty)(nil),           // 3: xian_wzkz.CEmpty
	(*JoinReq)(nil),          // 4: xian_wzkz.JoinReq
	(*JoinAck)(nil),          // 5: xian_wzkz.JoinAck
	(*Player)(nil),           // 6: xian_wzkz.Player
	(*RoomState)(nil),        // 7: xian_wzkz.RoomState
	(*ReissueReq)(nil),       // 8: xian_wzkz.ReissueReq
	(*ReissueRsp)(nil),       // 9: xian_wzkz.ReissueRsp
	(*InputOp)(nil),          // 10: xian_wzkz.InputOp
	(*OutputWithhold)(nil),   // 11: xian_wzkz.OutputWithhold
	(*InputExchange)(nil),    // 12: xian_wzkz.InputExchange
	(*InputRecharge)(nil),    // 13: xian_wzkz.InputRecharge
	(*InputJoin)(nil),        // 14: xian_wzkz.InputJoin
	(*FixedBloodPlane)(nil),  // 15: xian_wzkz.FixedBloodPlane
	(*InputLeave)(nil),       // 16: xian_wzkz.InputLeave
	(*InputAddFL)(nil),       // 17: xian_wzkz.InputAddFL
	(*InputSyncFL)(nil),      // 18: xian_wzkz.InputSyncFL
	(*InputQuitRoom)(nil),    // 19: xian_wzkz.InputQuitRoom
	(*InputShoot)(nil),       // 20: xian_wzkz.InputShoot
	(*InputHit)(nil),         // 21: xian_wzkz.InputHit
	(*Fighter)(nil),          // 22: xian_wzkz.Fighter
	(*InputTrack)(nil),       // 23: xian_wzkz.InputTrack
	(*Frame)(nil),            // 24: xian_wzkz.Frame
	(*game.Plane)(nil),       // 25: xian_wzkz.Plane
	(*game.Gift)(nil),        // 26: xian_wzkz.Gift
	(*gm.KickReq)(nil),       // 27: xian_wzkz_gm.KickReq
	(*gm.NodesRoomsRsp)(nil), // 28: xian_wzkz_gm.NodesRoomsRsp
	(*gm.KickRsp)(nil),       // 29: xian_wzkz_gm.KickRsp
}
var file_pb_xian_game_wzkz_wsh_wsh_proto_depIdxs = []int32{
	0,  // 0: xian_wzkz.Reply.code:type_name -> xian_wzkz.Code
	1,  // 1: xian_wzkz.Reply.cmd:type_name -> xian_wzkz.Cmd
	0,  // 2: xian_wzkz.JoinAck.code:type_name -> xian_wzkz.Code
	15, // 3: xian_wzkz.Player.fixed_planes:type_name -> xian_wzkz.FixedBloodPlane
	6,  // 4: xian_wzkz.RoomState.players:type_name -> xian_wzkz.Player
	23, // 5: xian_wzkz.RoomState.tracks:type_name -> xian_wzkz.InputTrack
	0,  // 6: xian_wzkz.RoomState.code:type_name -> xian_wzkz.Code
	24, // 7: xian_wzkz.ReissueRsp.frames:type_name -> xian_wzkz.Frame
	0,  // 8: xian_wzkz.ReissueRsp.code:type_name -> xian_wzkz.Code
	1,  // 9: xian_wzkz.InputOp.cmd:type_name -> xian_wzkz.Cmd
	20, // 10: xian_wzkz.InputOp.shoot:type_name -> xian_wzkz.InputShoot
	21, // 11: xian_wzkz.InputOp.hit:type_name -> xian_wzkz.InputHit
	23, // 12: xian_wzkz.InputOp.track:type_name -> xian_wzkz.InputTrack
	11, // 13: xian_wzkz.InputOp.withhold:type_name -> xian_wzkz.OutputWithhold
	13, // 14: xian_wzkz.InputOp.recharge:type_name -> xian_wzkz.InputRecharge
	12, // 15: xian_wzkz.InputOp.exchange:type_name -> xian_wzkz.InputExchange
	14, // 16: xian_wzkz.InputOp.join:type_name -> xian_wzkz.InputJoin
	16, // 17: xian_wzkz.InputOp.leave:type_name -> xian_wzkz.InputLeave
	17, // 18: xian_wzkz.InputOp.add_f_l:type_name -> xian_wzkz.InputAddFL
	18, // 19: xian_wzkz.InputOp.sync_f_l:type_name -> xian_wzkz.InputSyncFL
	19, // 20: xian_wzkz.InputOp.quit_room:type_name -> xian_wzkz.InputQuitRoom
	15, // 21: xian_wzkz.InputJoin.fixed_planes:type_name -> xian_wzkz.FixedBloodPlane
	25, // 22: xian_wzkz.FixedBloodPlane.plane:type_name -> xian_wzkz.Plane
	26, // 23: xian_wzkz.FixedBloodPlane.gift:type_name -> xian_wzkz.Gift
	22, // 24: xian_wzkz.InputHit.plane:type_name -> xian_wzkz.Fighter
	22, // 25: xian_wzkz.InputHit.other_plane:type_name -> xian_wzkz.Fighter
	26, // 26: xian_wzkz.InputHit.gifts:type_name -> xian_wzkz.Gift
	15, // 27: xian_wzkz.InputHit.fixed_plane:type_name -> xian_wzkz.FixedBloodPlane
	22, // 28: xian_wzkz.InputTrack.fighter:type_name -> xian_wzkz.Fighter
	10, // 29: xian_wzkz.Frame.input:type_name -> xian_wzkz.InputOp
	4,  // 30: xian_wzkz.Wsh.Join:input_type -> xian_wzkz.JoinReq
	3,  // 31: xian_wzkz.Wsh.Sync:input_type -> xian_wzkz.CEmpty
	8,  // 32: xian_wzkz.Wsh.Reissue:input_type -> xian_wzkz.ReissueReq
	10, // 33: xian_wzkz.Wsh.Action:input_type -> xian_wzkz.InputOp
	3,  // 34: xian_wzkz.Wsh.Leave:input_type -> xian_wzkz.CEmpty
	3,  // 35: xian_wzkz.Wsh.Rooms:input_type -> xian_wzkz.CEmpty
	27, // 36: xian_wzkz.Wsh.Kick:input_type -> xian_wzkz_gm.KickReq
	5,  // 37: xian_wzkz.Wsh.Join:output_type -> xian_wzkz.JoinAck
	7,  // 38: xian_wzkz.Wsh.Sync:output_type -> xian_wzkz.RoomState
	9,  // 39: xian_wzkz.Wsh.Reissue:output_type -> xian_wzkz.ReissueRsp
	2,  // 40: xian_wzkz.Wsh.Action:output_type -> xian_wzkz.Reply
	2,  // 41: xian_wzkz.Wsh.Leave:output_type -> xian_wzkz.Reply
	28, // 42: xian_wzkz.Wsh.Rooms:output_type -> xian_wzkz_gm.NodesRoomsRsp
	29, // 43: xian_wzkz.Wsh.Kick:output_type -> xian_wzkz_gm.KickRsp
	37, // [37:44] is the sub-list for method output_type
	30, // [30:37] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_pb_xian_game_wzkz_wsh_wsh_proto_init() }
func file_pb_xian_game_wzkz_wsh_wsh_proto_init() {
	if File_pb_xian_game_wzkz_wsh_wsh_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CEmpty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReissueReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReissueRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputOp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutputWithhold); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputExchange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputRecharge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputJoin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FixedBloodPlane); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputLeave); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputAddFL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputSyncFL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputQuitRoom); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputShoot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputHit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fighter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InputTrack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Frame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*InputOp_Shoot)(nil),
		(*InputOp_Hit)(nil),
		(*InputOp_Track)(nil),
		(*InputOp_Withhold)(nil),
		(*InputOp_Recharge)(nil),
		(*InputOp_Exchange)(nil),
		(*InputOp_Join)(nil),
		(*InputOp_Leave)(nil),
		(*InputOp_AddFL)(nil),
		(*InputOp_SyncFL)(nil),
		(*InputOp_QuitRoom)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_game_wzkz_wsh_wsh_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_xian_game_wzkz_wsh_wsh_proto_goTypes,
		DependencyIndexes: file_pb_xian_game_wzkz_wsh_wsh_proto_depIdxs,
		EnumInfos:         file_pb_xian_game_wzkz_wsh_wsh_proto_enumTypes,
		MessageInfos:      file_pb_xian_game_wzkz_wsh_wsh_proto_msgTypes,
	}.Build()
	File_pb_xian_game_wzkz_wsh_wsh_proto = out.File
	file_pb_xian_game_wzkz_wsh_wsh_proto_rawDesc = nil
	file_pb_xian_game_wzkz_wsh_wsh_proto_goTypes = nil
	file_pb_xian_game_wzkz_wsh_wsh_proto_depIdxs = nil
}
