// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/xian_game/wzkz/game/game.proto

package game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	gm "kugou_adapter_service/pkg/gen/proto/pb/xian_game/wzkz/gm"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Game_Register_FullMethodName       = "/xian_wzkz.Game/Register"
	Game_Config_FullMethodName         = "/xian_wzkz.Game/Config"
	Game_Match_FullMethodName          = "/xian_wzkz.Game/Match"
	Game_Records_FullMethodName        = "/xian_wzkz.Game/Records"
	Game_Assets_FullMethodName         = "/xian_wzkz.Game/Assets"
	Game_Notify_FullMethodName         = "/xian_wzkz.Game/Notify"
	Game_Switch_FullMethodName         = "/xian_wzkz.Game/Switch"
	Game_QuitRoom_FullMethodName       = "/xian_wzkz.Game/QuitRoom"
	Game_RechargeFAsset_FullMethodName = "/xian_wzkz.Game/RechargeFAsset"
	Game_MatchVer_FullMethodName       = "/xian_wzkz.Game/MatchVer"
	Game_Nodes_FullMethodName          = "/xian_wzkz.Game/Nodes"
	Game_NodeRooms_FullMethodName      = "/xian_wzkz.Game/NodeRooms"
	Game_Kick_FullMethodName           = "/xian_wzkz.Game/Kick"
	Game_Summary_FullMethodName        = "/xian_wzkz.Game/Summary"
	Game_SyncOpt_FullMethodName        = "/xian_wzkz.Game/SyncOpt"
	Game_Ticket_FullMethodName         = "/xian_wzkz.Game/Ticket"
	Game_ExFTG_FullMethodName          = "/xian_wzkz.Game/ExFTG"
	Game_ExFTGRecord_FullMethodName    = "/xian_wzkz.Game/ExFTGRecord"
	Game_ExFTGList_FullMethodName      = "/xian_wzkz.Game/ExFTGList"
	Game_ExCnt_FullMethodName          = "/xian_wzkz.Game/ExCnt"
	Game_ExCTFRecord_FullMethodName    = "/xian_wzkz.Game/ExCTFRecord"
	Game_CoinSubPay_FullMethodName     = "/xian_wzkz.Game/CoinSubPay"
	Game_CoinSubRes_FullMethodName     = "/xian_wzkz.Game/CoinSubRes"
	Game_CBCoinSubPay_FullMethodName   = "/xian_wzkz.Game/CBCoinSubPay"
)

// GameClient is the client API for Game service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GameClient interface {
	// 注册接口 - OK
	Register(ctx context.Context, in *RegReq, opts ...grpc.CallOption) (*RegRsp, error)
	// 配置
	Config(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*ConfigRsp, error)
	// 匹配房间 - OK
	Match(ctx context.Context, in *MatchReq, opts ...grpc.CallOption) (*MatchRsp, error)
	// 匹配房间版本2 - OK
	//
	//	rpc MatchV2(MatchReq) returns (MatchRsp);
	//
	// 击杀记录 - OK
	Records(ctx context.Context, in *RecordReq, opts ...grpc.CallOption) (*RecordRsp, error)
	// 资产信息 - OK
	Assets(ctx context.Context, in *AssetsReq, opts ...grpc.CallOption) (*AssetsRsp, error)
	// 兑换火力 - OK
	// rpc ExFire(ExFireReq) returns (ExFireRsp);
	// 兑换火力
	// rpc ExFireRecord(ExFireRecordReq) returns (ExFireRecordRsp);
	// 兑换回调
	Notify(ctx context.Context, in *NotifyReq, opts ...grpc.CallOption) (*NotifyRsp, error)
	// 获取业务开关配置状态
	Switch(ctx context.Context, in *SwitchReq, opts ...grpc.CallOption) (*SwitchRsp, error)
	// 主动退出房间
	QuitRoom(ctx context.Context, in *QuitRoomReq, opts ...grpc.CallOption) (*QuitRoomRsp, error)
	// 充值鲜花场水滴
	RechargeFAsset(ctx context.Context, in *RechargeFAssetReq, opts ...grpc.CallOption) (*RechargeFAssetRsp, error)
	// 匹配版本是否开着
	MatchVer(ctx context.Context, in *MatchVerReq, opts ...grpc.CallOption) (*MatchVerRsp, error)
	//	获取鲜花场射击次数
	//	rpc FlowerLimit(FlowerLimitReq) returns (FlowerLimitRsp);
	//
	// GM - 节点列表
	Nodes(ctx context.Context, in *gm.NodesReq, opts ...grpc.CallOption) (*gm.NodesRsp, error)
	// GM - 节点内房间列表
	NodeRooms(ctx context.Context, in *gm.NodesRoomsReq, opts ...grpc.CallOption) (*gm.NodesRoomsRsp, error)
	// GM - 踢人/解散房间
	Kick(ctx context.Context, in *gm.KickReq, opts ...grpc.CallOption) (*gm.KickRsp, error)
	// GM - 查看总计信息
	Summary(ctx context.Context, in *gm.SummaryReq, opts ...grpc.CallOption) (*gm.SummaryRsp, error)
	// 兑换功能
	SyncOpt(ctx context.Context, in *SyncOptReq, opts ...grpc.CallOption) (*SyncOptRsp, error)
	// 获取票据
	Ticket(ctx context.Context, in *TicketReq, opts ...grpc.CallOption) (*TicketRsp, error)
	ExFTG(ctx context.Context, in *ExFTGReq, opts ...grpc.CallOption) (*ExFTGRsp, error)
	ExFTGRecord(ctx context.Context, in *ExFTGRecordReq, opts ...grpc.CallOption) (*ExFTGRecordRsp, error)
	ExFTGList(ctx context.Context, in *ExFTGListReq, opts ...grpc.CallOption) (*ExFTGListRsp, error)
	// rpc ExCTF(ExCTFReq) returns (ExCTFRsp); // 金币兑换火力
	ExCnt(ctx context.Context, in *ExCntReq, opts ...grpc.CallOption) (*ExCntRsp, error)
	ExCTFRecord(ctx context.Context, in *ExCTFRecordReq, opts ...grpc.CallOption) (*ExCTFRecordRsp, error)
	CoinSubPay(ctx context.Context, in *CoinSubPayReq, opts ...grpc.CallOption) (*CoinSubPayRsp, error)
	CoinSubRes(ctx context.Context, in *CoinSubResReq, opts ...grpc.CallOption) (*CoinSubResRsp, error)
	CBCoinSubPay(ctx context.Context, in *callback.OrderShipmentReq, opts ...grpc.CallOption) (*callback.OrderShipmentRsp, error)
}

type gameClient struct {
	cc grpc.ClientConnInterface
}

func NewGameClient(cc grpc.ClientConnInterface) GameClient {
	return &gameClient{cc}
}

func (c *gameClient) Register(ctx context.Context, in *RegReq, opts ...grpc.CallOption) (*RegRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegRsp)
	err := c.cc.Invoke(ctx, Game_Register_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Config(ctx context.Context, in *ConfigReq, opts ...grpc.CallOption) (*ConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfigRsp)
	err := c.cc.Invoke(ctx, Game_Config_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Match(ctx context.Context, in *MatchReq, opts ...grpc.CallOption) (*MatchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MatchRsp)
	err := c.cc.Invoke(ctx, Game_Match_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Records(ctx context.Context, in *RecordReq, opts ...grpc.CallOption) (*RecordRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecordRsp)
	err := c.cc.Invoke(ctx, Game_Records_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Assets(ctx context.Context, in *AssetsReq, opts ...grpc.CallOption) (*AssetsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssetsRsp)
	err := c.cc.Invoke(ctx, Game_Assets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Notify(ctx context.Context, in *NotifyReq, opts ...grpc.CallOption) (*NotifyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NotifyRsp)
	err := c.cc.Invoke(ctx, Game_Notify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Switch(ctx context.Context, in *SwitchReq, opts ...grpc.CallOption) (*SwitchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SwitchRsp)
	err := c.cc.Invoke(ctx, Game_Switch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) QuitRoom(ctx context.Context, in *QuitRoomReq, opts ...grpc.CallOption) (*QuitRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuitRoomRsp)
	err := c.cc.Invoke(ctx, Game_QuitRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) RechargeFAsset(ctx context.Context, in *RechargeFAssetReq, opts ...grpc.CallOption) (*RechargeFAssetRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RechargeFAssetRsp)
	err := c.cc.Invoke(ctx, Game_RechargeFAsset_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) MatchVer(ctx context.Context, in *MatchVerReq, opts ...grpc.CallOption) (*MatchVerRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MatchVerRsp)
	err := c.cc.Invoke(ctx, Game_MatchVer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Nodes(ctx context.Context, in *gm.NodesReq, opts ...grpc.CallOption) (*gm.NodesRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(gm.NodesRsp)
	err := c.cc.Invoke(ctx, Game_Nodes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) NodeRooms(ctx context.Context, in *gm.NodesRoomsReq, opts ...grpc.CallOption) (*gm.NodesRoomsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(gm.NodesRoomsRsp)
	err := c.cc.Invoke(ctx, Game_NodeRooms_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Kick(ctx context.Context, in *gm.KickReq, opts ...grpc.CallOption) (*gm.KickRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(gm.KickRsp)
	err := c.cc.Invoke(ctx, Game_Kick_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Summary(ctx context.Context, in *gm.SummaryReq, opts ...grpc.CallOption) (*gm.SummaryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(gm.SummaryRsp)
	err := c.cc.Invoke(ctx, Game_Summary_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) SyncOpt(ctx context.Context, in *SyncOptReq, opts ...grpc.CallOption) (*SyncOptRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncOptRsp)
	err := c.cc.Invoke(ctx, Game_SyncOpt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) Ticket(ctx context.Context, in *TicketReq, opts ...grpc.CallOption) (*TicketRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TicketRsp)
	err := c.cc.Invoke(ctx, Game_Ticket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ExFTG(ctx context.Context, in *ExFTGReq, opts ...grpc.CallOption) (*ExFTGRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExFTGRsp)
	err := c.cc.Invoke(ctx, Game_ExFTG_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ExFTGRecord(ctx context.Context, in *ExFTGRecordReq, opts ...grpc.CallOption) (*ExFTGRecordRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExFTGRecordRsp)
	err := c.cc.Invoke(ctx, Game_ExFTGRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ExFTGList(ctx context.Context, in *ExFTGListReq, opts ...grpc.CallOption) (*ExFTGListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExFTGListRsp)
	err := c.cc.Invoke(ctx, Game_ExFTGList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ExCnt(ctx context.Context, in *ExCntReq, opts ...grpc.CallOption) (*ExCntRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExCntRsp)
	err := c.cc.Invoke(ctx, Game_ExCnt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) ExCTFRecord(ctx context.Context, in *ExCTFRecordReq, opts ...grpc.CallOption) (*ExCTFRecordRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExCTFRecordRsp)
	err := c.cc.Invoke(ctx, Game_ExCTFRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) CoinSubPay(ctx context.Context, in *CoinSubPayReq, opts ...grpc.CallOption) (*CoinSubPayRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CoinSubPayRsp)
	err := c.cc.Invoke(ctx, Game_CoinSubPay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) CoinSubRes(ctx context.Context, in *CoinSubResReq, opts ...grpc.CallOption) (*CoinSubResRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CoinSubResRsp)
	err := c.cc.Invoke(ctx, Game_CoinSubRes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameClient) CBCoinSubPay(ctx context.Context, in *callback.OrderShipmentReq, opts ...grpc.CallOption) (*callback.OrderShipmentRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(callback.OrderShipmentRsp)
	err := c.cc.Invoke(ctx, Game_CBCoinSubPay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameServer is the server API for Game service.
// All implementations should embed UnimplementedGameServer
// for forward compatibility
type GameServer interface {
	// 注册接口 - OK
	Register(context.Context, *RegReq) (*RegRsp, error)
	// 配置
	Config(context.Context, *ConfigReq) (*ConfigRsp, error)
	// 匹配房间 - OK
	Match(context.Context, *MatchReq) (*MatchRsp, error)
	// 匹配房间版本2 - OK
	//
	//	rpc MatchV2(MatchReq) returns (MatchRsp);
	//
	// 击杀记录 - OK
	Records(context.Context, *RecordReq) (*RecordRsp, error)
	// 资产信息 - OK
	Assets(context.Context, *AssetsReq) (*AssetsRsp, error)
	// 兑换火力 - OK
	// rpc ExFire(ExFireReq) returns (ExFireRsp);
	// 兑换火力
	// rpc ExFireRecord(ExFireRecordReq) returns (ExFireRecordRsp);
	// 兑换回调
	Notify(context.Context, *NotifyReq) (*NotifyRsp, error)
	// 获取业务开关配置状态
	Switch(context.Context, *SwitchReq) (*SwitchRsp, error)
	// 主动退出房间
	QuitRoom(context.Context, *QuitRoomReq) (*QuitRoomRsp, error)
	// 充值鲜花场水滴
	RechargeFAsset(context.Context, *RechargeFAssetReq) (*RechargeFAssetRsp, error)
	// 匹配版本是否开着
	MatchVer(context.Context, *MatchVerReq) (*MatchVerRsp, error)
	//	获取鲜花场射击次数
	//	rpc FlowerLimit(FlowerLimitReq) returns (FlowerLimitRsp);
	//
	// GM - 节点列表
	Nodes(context.Context, *gm.NodesReq) (*gm.NodesRsp, error)
	// GM - 节点内房间列表
	NodeRooms(context.Context, *gm.NodesRoomsReq) (*gm.NodesRoomsRsp, error)
	// GM - 踢人/解散房间
	Kick(context.Context, *gm.KickReq) (*gm.KickRsp, error)
	// GM - 查看总计信息
	Summary(context.Context, *gm.SummaryReq) (*gm.SummaryRsp, error)
	// 兑换功能
	SyncOpt(context.Context, *SyncOptReq) (*SyncOptRsp, error)
	// 获取票据
	Ticket(context.Context, *TicketReq) (*TicketRsp, error)
	ExFTG(context.Context, *ExFTGReq) (*ExFTGRsp, error)
	ExFTGRecord(context.Context, *ExFTGRecordReq) (*ExFTGRecordRsp, error)
	ExFTGList(context.Context, *ExFTGListReq) (*ExFTGListRsp, error)
	// rpc ExCTF(ExCTFReq) returns (ExCTFRsp); // 金币兑换火力
	ExCnt(context.Context, *ExCntReq) (*ExCntRsp, error)
	ExCTFRecord(context.Context, *ExCTFRecordReq) (*ExCTFRecordRsp, error)
	CoinSubPay(context.Context, *CoinSubPayReq) (*CoinSubPayRsp, error)
	CoinSubRes(context.Context, *CoinSubResReq) (*CoinSubResRsp, error)
	CBCoinSubPay(context.Context, *callback.OrderShipmentReq) (*callback.OrderShipmentRsp, error)
}

// UnimplementedGameServer should be embedded to have forward compatible implementations.
type UnimplementedGameServer struct {
}

func (UnimplementedGameServer) Register(context.Context, *RegReq) (*RegRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Register not implemented")
}
func (UnimplementedGameServer) Config(context.Context, *ConfigReq) (*ConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Config not implemented")
}
func (UnimplementedGameServer) Match(context.Context, *MatchReq) (*MatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Match not implemented")
}
func (UnimplementedGameServer) Records(context.Context, *RecordReq) (*RecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Records not implemented")
}
func (UnimplementedGameServer) Assets(context.Context, *AssetsReq) (*AssetsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Assets not implemented")
}
func (UnimplementedGameServer) Notify(context.Context, *NotifyReq) (*NotifyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Notify not implemented")
}
func (UnimplementedGameServer) Switch(context.Context, *SwitchReq) (*SwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Switch not implemented")
}
func (UnimplementedGameServer) QuitRoom(context.Context, *QuitRoomReq) (*QuitRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuitRoom not implemented")
}
func (UnimplementedGameServer) RechargeFAsset(context.Context, *RechargeFAssetReq) (*RechargeFAssetRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RechargeFAsset not implemented")
}
func (UnimplementedGameServer) MatchVer(context.Context, *MatchVerReq) (*MatchVerRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MatchVer not implemented")
}
func (UnimplementedGameServer) Nodes(context.Context, *gm.NodesReq) (*gm.NodesRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Nodes not implemented")
}
func (UnimplementedGameServer) NodeRooms(context.Context, *gm.NodesRoomsReq) (*gm.NodesRoomsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeRooms not implemented")
}
func (UnimplementedGameServer) Kick(context.Context, *gm.KickReq) (*gm.KickRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Kick not implemented")
}
func (UnimplementedGameServer) Summary(context.Context, *gm.SummaryReq) (*gm.SummaryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Summary not implemented")
}
func (UnimplementedGameServer) SyncOpt(context.Context, *SyncOptReq) (*SyncOptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncOpt not implemented")
}
func (UnimplementedGameServer) Ticket(context.Context, *TicketReq) (*TicketRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ticket not implemented")
}
func (UnimplementedGameServer) ExFTG(context.Context, *ExFTGReq) (*ExFTGRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExFTG not implemented")
}
func (UnimplementedGameServer) ExFTGRecord(context.Context, *ExFTGRecordReq) (*ExFTGRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExFTGRecord not implemented")
}
func (UnimplementedGameServer) ExFTGList(context.Context, *ExFTGListReq) (*ExFTGListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExFTGList not implemented")
}
func (UnimplementedGameServer) ExCnt(context.Context, *ExCntReq) (*ExCntRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExCnt not implemented")
}
func (UnimplementedGameServer) ExCTFRecord(context.Context, *ExCTFRecordReq) (*ExCTFRecordRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExCTFRecord not implemented")
}
func (UnimplementedGameServer) CoinSubPay(context.Context, *CoinSubPayReq) (*CoinSubPayRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoinSubPay not implemented")
}
func (UnimplementedGameServer) CoinSubRes(context.Context, *CoinSubResReq) (*CoinSubResRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CoinSubRes not implemented")
}
func (UnimplementedGameServer) CBCoinSubPay(context.Context, *callback.OrderShipmentReq) (*callback.OrderShipmentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CBCoinSubPay not implemented")
}

// UnsafeGameServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameServer will
// result in compilation errors.
type UnsafeGameServer interface {
	mustEmbedUnimplementedGameServer()
}

func RegisterGameServer(s grpc.ServiceRegistrar, srv GameServer) {
	s.RegisterService(&Game_ServiceDesc, srv)
}

func _Game_Register_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Register(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Register_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Register(ctx, req.(*RegReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Config_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Config(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Config_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Config(ctx, req.(*ConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Match_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Match(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Match_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Match(ctx, req.(*MatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Records_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Records(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Records_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Records(ctx, req.(*RecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Assets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssetsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Assets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Assets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Assets(ctx, req.(*AssetsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Notify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Notify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Notify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Notify(ctx, req.(*NotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Switch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Switch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Switch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Switch(ctx, req.(*SwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_QuitRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuitRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).QuitRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_QuitRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).QuitRoom(ctx, req.(*QuitRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_RechargeFAsset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RechargeFAssetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).RechargeFAsset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_RechargeFAsset_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).RechargeFAsset(ctx, req.(*RechargeFAssetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_MatchVer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchVerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).MatchVer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_MatchVer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).MatchVer(ctx, req.(*MatchVerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Nodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gm.NodesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Nodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Nodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Nodes(ctx, req.(*gm.NodesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_NodeRooms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gm.NodesRoomsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).NodeRooms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_NodeRooms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).NodeRooms(ctx, req.(*gm.NodesRoomsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Kick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gm.KickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Kick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Kick_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Kick(ctx, req.(*gm.KickReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Summary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(gm.SummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Summary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Summary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Summary(ctx, req.(*gm.SummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_SyncOpt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncOptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).SyncOpt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_SyncOpt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).SyncOpt(ctx, req.(*SyncOptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_Ticket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).Ticket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_Ticket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).Ticket(ctx, req.(*TicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ExFTG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExFTGReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ExFTG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ExFTG_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ExFTG(ctx, req.(*ExFTGReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ExFTGRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExFTGRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ExFTGRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ExFTGRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ExFTGRecord(ctx, req.(*ExFTGRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ExFTGList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExFTGListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ExFTGList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ExFTGList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ExFTGList(ctx, req.(*ExFTGListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ExCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ExCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ExCnt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ExCnt(ctx, req.(*ExCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_ExCTFRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExCTFRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).ExCTFRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_ExCTFRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).ExCTFRecord(ctx, req.(*ExCTFRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_CoinSubPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoinSubPayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).CoinSubPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_CoinSubPay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).CoinSubPay(ctx, req.(*CoinSubPayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_CoinSubRes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CoinSubResReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).CoinSubRes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_CoinSubRes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).CoinSubRes(ctx, req.(*CoinSubResReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Game_CBCoinSubPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(callback.OrderShipmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameServer).CBCoinSubPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Game_CBCoinSubPay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameServer).CBCoinSubPay(ctx, req.(*callback.OrderShipmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Game_ServiceDesc is the grpc.ServiceDesc for Game service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Game_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "xian_wzkz.Game",
	HandlerType: (*GameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Register",
			Handler:    _Game_Register_Handler,
		},
		{
			MethodName: "Config",
			Handler:    _Game_Config_Handler,
		},
		{
			MethodName: "Match",
			Handler:    _Game_Match_Handler,
		},
		{
			MethodName: "Records",
			Handler:    _Game_Records_Handler,
		},
		{
			MethodName: "Assets",
			Handler:    _Game_Assets_Handler,
		},
		{
			MethodName: "Notify",
			Handler:    _Game_Notify_Handler,
		},
		{
			MethodName: "Switch",
			Handler:    _Game_Switch_Handler,
		},
		{
			MethodName: "QuitRoom",
			Handler:    _Game_QuitRoom_Handler,
		},
		{
			MethodName: "RechargeFAsset",
			Handler:    _Game_RechargeFAsset_Handler,
		},
		{
			MethodName: "MatchVer",
			Handler:    _Game_MatchVer_Handler,
		},
		{
			MethodName: "Nodes",
			Handler:    _Game_Nodes_Handler,
		},
		{
			MethodName: "NodeRooms",
			Handler:    _Game_NodeRooms_Handler,
		},
		{
			MethodName: "Kick",
			Handler:    _Game_Kick_Handler,
		},
		{
			MethodName: "Summary",
			Handler:    _Game_Summary_Handler,
		},
		{
			MethodName: "SyncOpt",
			Handler:    _Game_SyncOpt_Handler,
		},
		{
			MethodName: "Ticket",
			Handler:    _Game_Ticket_Handler,
		},
		{
			MethodName: "ExFTG",
			Handler:    _Game_ExFTG_Handler,
		},
		{
			MethodName: "ExFTGRecord",
			Handler:    _Game_ExFTGRecord_Handler,
		},
		{
			MethodName: "ExFTGList",
			Handler:    _Game_ExFTGList_Handler,
		},
		{
			MethodName: "ExCnt",
			Handler:    _Game_ExCnt_Handler,
		},
		{
			MethodName: "ExCTFRecord",
			Handler:    _Game_ExCTFRecord_Handler,
		},
		{
			MethodName: "CoinSubPay",
			Handler:    _Game_CoinSubPay_Handler,
		},
		{
			MethodName: "CoinSubRes",
			Handler:    _Game_CoinSubRes_Handler,
		},
		{
			MethodName: "CBCoinSubPay",
			Handler:    _Game_CBCoinSubPay_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/xian_game/wzkz/game/game.proto",
}
