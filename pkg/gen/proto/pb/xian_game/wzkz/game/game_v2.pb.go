// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_game/wzkz/game/game_v2.proto

package game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	adapter_unified_assets "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/adapter_unified_assets"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TradeStatusType int32

const (
	TradeStatusType_TS_SubInventory     TradeStatusType = 0  // 默认状态(初次保存) 清理库存等内部操作
	TradeStatusType_TS_Pay              TradeStatusType = 1  // 开启支付
	TradeStatusType_TS_PayPending       TradeStatusType = 2  // 支付进行中(Pay接口未明确返回错误)
	TradeStatusType_TS_PaySuccess       TradeStatusType = 3  // 支付成功(Pay接口明确返回OK) 开始真正扣除库存
	TradeStatusType_TS_PayError         TradeStatusType = 4  // 支付失败(Pay接口明确返回失败)
	TradeStatusType_TS_PayUnkown        TradeStatusType = 5  // 支付网络失败
	TradeStatusType_TS_SendGoodsSuccess TradeStatusType = 6  // 火力发放
	TradeStatusType_TS_End              TradeStatusType = 20 // 所有流程执行完成
)

// Enum value maps for TradeStatusType.
var (
	TradeStatusType_name = map[int32]string{
		0:  "TS_SubInventory",
		1:  "TS_Pay",
		2:  "TS_PayPending",
		3:  "TS_PaySuccess",
		4:  "TS_PayError",
		5:  "TS_PayUnkown",
		6:  "TS_SendGoodsSuccess",
		20: "TS_End",
	}
	TradeStatusType_value = map[string]int32{
		"TS_SubInventory":     0,
		"TS_Pay":              1,
		"TS_PayPending":       2,
		"TS_PaySuccess":       3,
		"TS_PayError":         4,
		"TS_PayUnkown":        5,
		"TS_SendGoodsSuccess": 6,
		"TS_End":              20,
	}
)

func (x TradeStatusType) Enum() *TradeStatusType {
	p := new(TradeStatusType)
	*p = x
	return p
}

func (x TradeStatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TradeStatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_wzkz_game_game_v2_proto_enumTypes[0].Descriptor()
}

func (TradeStatusType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_wzkz_game_game_v2_proto_enumTypes[0]
}

func (x TradeStatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TradeStatusType.Descriptor instead.
func (TradeStatusType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{0}
}

type CoinSubPayReq_ExCTFType int32

const (
	CoinSubPayReq_ExCTFType_None CoinSubPayReq_ExCTFType = 0 // 默认
	CoinSubPayReq_ExCTFType_All  CoinSubPayReq_ExCTFType = 1 // 全部兑换
)

// Enum value maps for CoinSubPayReq_ExCTFType.
var (
	CoinSubPayReq_ExCTFType_name = map[int32]string{
		0: "ExCTFType_None",
		1: "ExCTFType_All",
	}
	CoinSubPayReq_ExCTFType_value = map[string]int32{
		"ExCTFType_None": 0,
		"ExCTFType_All":  1,
	}
)

func (x CoinSubPayReq_ExCTFType) Enum() *CoinSubPayReq_ExCTFType {
	p := new(CoinSubPayReq_ExCTFType)
	*p = x
	return p
}

func (x CoinSubPayReq_ExCTFType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CoinSubPayReq_ExCTFType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_wzkz_game_game_v2_proto_enumTypes[1].Descriptor()
}

func (CoinSubPayReq_ExCTFType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_wzkz_game_game_v2_proto_enumTypes[1]
}

func (x CoinSubPayReq_ExCTFType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CoinSubPayReq_ExCTFType.Descriptor instead.
func (CoinSubPayReq_ExCTFType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{20, 0}
}

type Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 执行结果code码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误消息
}

func (x *Error) Reset() {
	*x = Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{0}
}

func (x *Error) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Error) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// SyncOptReq 同步配置Req
type SyncOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // key名
	Data string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 数据
}

func (x *SyncOptReq) Reset() {
	*x = SyncOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOptReq) ProtoMessage() {}

func (x *SyncOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOptReq.ProtoReflect.Descriptor instead.
func (*SyncOptReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{1}
}

func (x *SyncOptReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SyncOptReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// SyncOptReq 同步配置Rsp
type SyncOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"` // 类型 'success' | 'warning' | 'error';
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`   // string;
}

func (x *SyncOptRsp) Reset() {
	*x = SyncOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOptRsp) ProtoMessage() {}

func (x *SyncOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOptRsp.ProtoReflect.Descriptor instead.
func (*SyncOptRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{2}
}

func (x *SyncOptRsp) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SyncOptRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ExFTGReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid        string `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"`                                  // 实物ID
	ExchangeId uint32 `protobuf:"varint,2,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"` // 兑换ID
	GiftId     uint32 `protobuf:"varint,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`             // 类型为FireToGift=>置换的礼包ID
}

func (x *ExFTGReq) Reset() {
	*x = ExFTGReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGReq) ProtoMessage() {}

func (x *ExFTGReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGReq.ProtoReflect.Descriptor instead.
func (*ExFTGReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{3}
}

func (x *ExFTGReq) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *ExFTGReq) GetExchangeId() uint32 {
	if x != nil {
		return x.ExchangeId
	}
	return 0
}

func (x *ExFTGReq) GetGiftId() uint32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

type ExFTGRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Error *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExFTGRsp) Reset() {
	*x = ExFTGRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGRsp) ProtoMessage() {}

func (x *ExFTGRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGRsp.ProtoReflect.Descriptor instead.
func (*ExFTGRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{4}
}

func (x *ExFTGRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExCTFRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"` // 页码
}

func (x *ExCTFRecordReq) Reset() {
	*x = ExCTFRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExCTFRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExCTFRecordReq) ProtoMessage() {}

func (x *ExCTFRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExCTFRecordReq.ProtoReflect.Descriptor instead.
func (*ExCTFRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{5}
}

func (x *ExCTFRecordReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type ExCTFRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ll    []*ExCTFRecord `protobuf:"bytes,1,rep,name=ll,proto3" json:"ll,omitempty"` // 记录列表
	Error *Error         `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExCTFRecordRsp) Reset() {
	*x = ExCTFRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExCTFRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExCTFRecordRsp) ProtoMessage() {}

func (x *ExCTFRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExCTFRecordRsp.ProtoReflect.Descriptor instead.
func (*ExCTFRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{6}
}

func (x *ExCTFRecordRsp) GetLl() []*ExCTFRecord {
	if x != nil {
		return x.Ll
	}
	return nil
}

func (x *ExCTFRecordRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExCTFRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tt      int64 `protobuf:"varint,1,opt,name=tt,proto3" json:"tt,omitempty"`                          // 操作时间
	CoinCnt int64 `protobuf:"varint,2,opt,name=coin_cnt,json=coinCnt,proto3" json:"coin_cnt,omitempty"` // 使用的网赚金币
	FireCnt int64 `protobuf:"varint,3,opt,name=fire_cnt,json=fireCnt,proto3" json:"fire_cnt,omitempty"` // 获得的火力数量
}

func (x *ExCTFRecord) Reset() {
	*x = ExCTFRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExCTFRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExCTFRecord) ProtoMessage() {}

func (x *ExCTFRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExCTFRecord.ProtoReflect.Descriptor instead.
func (*ExCTFRecord) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{7}
}

func (x *ExCTFRecord) GetTt() int64 {
	if x != nil {
		return x.Tt
	}
	return 0
}

func (x *ExCTFRecord) GetCoinCnt() int64 {
	if x != nil {
		return x.CoinCnt
	}
	return 0
}

func (x *ExCTFRecord) GetFireCnt() int64 {
	if x != nil {
		return x.FireCnt
	}
	return 0
}

type ExCntReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExCntReq) Reset() {
	*x = ExCntReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExCntReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExCntReq) ProtoMessage() {}

func (x *ExCntReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExCntReq.ProtoReflect.Descriptor instead.
func (*ExCntReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{8}
}

type ExCntRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExInfo *ExCnt `protobuf:"bytes,1,opt,name=ex_info,json=exInfo,proto3" json:"ex_info,omitempty"` // 记录列表
	Error  *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExCntRsp) Reset() {
	*x = ExCntRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExCntRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExCntRsp) ProtoMessage() {}

func (x *ExCntRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExCntRsp.ProtoReflect.Descriptor instead.
func (*ExCntRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{9}
}

func (x *ExCntRsp) GetExInfo() *ExCnt {
	if x != nil {
		return x.ExInfo
	}
	return nil
}

func (x *ExCntRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExCnt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RdAllCnt    uint64 `protobuf:"varint,1,opt,name=rd_all_cnt,json=rdAllCnt,proto3" json:"rd_all_cnt,omitempty"`          // 剩余兑换数量全局每日
	RdPersonCnt uint64 `protobuf:"varint,2,opt,name=rd_person_cnt,json=rdPersonCnt,proto3" json:"rd_person_cnt,omitempty"` // 剩余兑换数量个人每日
}

func (x *ExCnt) Reset() {
	*x = ExCnt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExCnt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExCnt) ProtoMessage() {}

func (x *ExCnt) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExCnt.ProtoReflect.Descriptor instead.
func (*ExCnt) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{10}
}

func (x *ExCnt) GetRdAllCnt() uint64 {
	if x != nil {
		return x.RdAllCnt
	}
	return 0
}

func (x *ExCnt) GetRdPersonCnt() uint64 {
	if x != nil {
		return x.RdPersonCnt
	}
	return 0
}

type ExFTGRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"` // 页码
}

func (x *ExFTGRecordReq) Reset() {
	*x = ExFTGRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGRecordReq) ProtoMessage() {}

func (x *ExFTGRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGRecordReq.ProtoReflect.Descriptor instead.
func (*ExFTGRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{11}
}

func (x *ExFTGRecordReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type ExFTGRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ll    []*ExFTGRecord `protobuf:"bytes,1,rep,name=ll,proto3" json:"ll,omitempty"` // 记录列表
	Error *Error         `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExFTGRecordRsp) Reset() {
	*x = ExFTGRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGRecordRsp) ProtoMessage() {}

func (x *ExFTGRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGRecordRsp.ProtoReflect.Descriptor instead.
func (*ExFTGRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{12}
}

func (x *ExFTGRecordRsp) GetLl() []*ExFTGRecord {
	if x != nil {
		return x.Ll
	}
	return nil
}

func (x *ExFTGRecordRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExFTGRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tt       int64     `protobuf:"varint,1,opt,name=tt,proto3" json:"tt,omitempty"`                            // 操作时间
	FireCnt  uint32    `protobuf:"varint,2,opt,name=fire_cnt,json=fireCnt,proto3" json:"fire_cnt,omitempty"`   // 使用的火力
	GiftInfo *GiftInfo `protobuf:"bytes,3,opt,name=gift_info,json=giftInfo,proto3" json:"gift_info,omitempty"` // 礼物信息
}

func (x *ExFTGRecord) Reset() {
	*x = ExFTGRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGRecord) ProtoMessage() {}

func (x *ExFTGRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGRecord.ProtoReflect.Descriptor instead.
func (*ExFTGRecord) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{13}
}

func (x *ExFTGRecord) GetTt() int64 {
	if x != nil {
		return x.Tt
	}
	return 0
}

func (x *ExFTGRecord) GetFireCnt() uint32 {
	if x != nil {
		return x.FireCnt
	}
	return 0
}

func (x *ExFTGRecord) GetGiftInfo() *GiftInfo {
	if x != nil {
		return x.GiftInfo
	}
	return nil
}

type GiftInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GIftId       string `protobuf:"bytes,1,opt,name=GIftId,proto3" json:"GIftId,omitempty"`                                   // 礼物编码
	Type         uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                      // 类型
	Name         string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                       // 名称
	Logo         string `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`                                       // logo
	Num          uint32 `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`                                        // 数量
	Price        int64  `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"`                                    // 礼物价值，配置接口有用
	GiftPlatType string `protobuf:"bytes,7,opt,name=gift_plat_type,json=giftPlatType,proto3" json:"gift_plat_type,omitempty"` // 透传透传宿主平台子奖品类型
}

func (x *GiftInfo) Reset() {
	*x = GiftInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftInfo) ProtoMessage() {}

func (x *GiftInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftInfo.ProtoReflect.Descriptor instead.
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{14}
}

func (x *GiftInfo) GetGIftId() string {
	if x != nil {
		return x.GIftId
	}
	return ""
}

func (x *GiftInfo) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GiftInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GiftInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *GiftInfo) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *GiftInfo) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GiftInfo) GetGiftPlatType() string {
	if x != nil {
		return x.GiftPlatType
	}
	return ""
}

type ExFTGListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExFTGListReq) Reset() {
	*x = ExFTGListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGListReq) ProtoMessage() {}

func (x *ExFTGListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGListReq.ProtoReflect.Descriptor instead.
func (*ExFTGListReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{15}
}

type ExFTGListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mm    map[int32]*ExFTG `protobuf:"bytes,1,rep,name=mm,proto3" json:"mm,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 兑换 兑换ID:兑换详情
	Error *Error           `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExFTGListRsp) Reset() {
	*x = ExFTGListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTGListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTGListRsp) ProtoMessage() {}

func (x *ExFTGListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTGListRsp.ProtoReflect.Descriptor instead.
func (*ExFTGListRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{16}
}

func (x *ExFTGListRsp) GetMm() map[int32]*ExFTG {
	if x != nil {
		return x.Mm
	}
	return nil
}

func (x *ExFTGListRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExFTG struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuCnt uint64 `protobuf:"varint,1,opt,name=au_cnt,json=auCnt,proto3" json:"au_cnt,omitempty"` // 使用数量:全局每日 all_used_cnt
	SuCnt uint64 `protobuf:"varint,2,opt,name=su_cnt,json=suCnt,proto3" json:"su_cnt,omitempty"` // 使用数量:个人每日 self_used_cnt
}

func (x *ExFTG) Reset() {
	*x = ExFTG{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFTG) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFTG) ProtoMessage() {}

func (x *ExFTG) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFTG.ProtoReflect.Descriptor instead.
func (*ExFTG) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{17}
}

func (x *ExFTG) GetAuCnt() uint64 {
	if x != nil {
		return x.AuCnt
	}
	return 0
}

func (x *ExFTG) GetSuCnt() uint64 {
	if x != nil {
		return x.SuCnt
	}
	return 0
}

// 票据 req
type TicketReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TicketReq) Reset() {
	*x = TicketReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketReq) ProtoMessage() {}

func (x *TicketReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketReq.ProtoReflect.Descriptor instead.
func (*TicketReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{18}
}

// 票据 rsp
type TicketRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tid   string `protobuf:"bytes,1,opt,name=tid,proto3" json:"tid,omitempty"` // 事务ID
	Error *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *TicketRsp) Reset() {
	*x = TicketRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TicketRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketRsp) ProtoMessage() {}

func (x *TicketRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketRsp.ProtoReflect.Descriptor instead.
func (*TicketRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{19}
}

func (x *TicketRsp) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *TicketRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// CoinSubPayReq 扣费金币
type CoinSubPayReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	T            CoinSubPayReq_ExCTFType              `protobuf:"varint,1,opt,name=t,proto3,enum=xian_wzkz.CoinSubPayReq_ExCTFType" json:"t,omitempty"`       // 兑换类型
	ExchangeId   uint32                               `protobuf:"varint,2,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"`          // 兑换ID
	CoinCnt      int64                                `protobuf:"varint,3,opt,name=coin_cnt,json=coinCnt,proto3" json:"coin_cnt,omitempty"`                   // 金币数量
	RoomId       string                               `protobuf:"bytes,4,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`                       // 房间号
	OrderId      string                               `protobuf:"bytes,101,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`                  // 订单id，幂等使用
	PayAmount    int64                                `protobuf:"varint,102,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount,omitempty"`           // 总价
	PaySceneInfo *adapter_unified_assets.PaySceneInfo `protobuf:"bytes,103,opt,name=pay_scene_info,json=paySceneInfo,proto3" json:"pay_scene_info,omitempty"` // 扣费场景信息
}

func (x *CoinSubPayReq) Reset() {
	*x = CoinSubPayReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinSubPayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinSubPayReq) ProtoMessage() {}

func (x *CoinSubPayReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinSubPayReq.ProtoReflect.Descriptor instead.
func (*CoinSubPayReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{20}
}

func (x *CoinSubPayReq) GetT() CoinSubPayReq_ExCTFType {
	if x != nil {
		return x.T
	}
	return CoinSubPayReq_ExCTFType_None
}

func (x *CoinSubPayReq) GetExchangeId() uint32 {
	if x != nil {
		return x.ExchangeId
	}
	return 0
}

func (x *CoinSubPayReq) GetCoinCnt() int64 {
	if x != nil {
		return x.CoinCnt
	}
	return 0
}

func (x *CoinSubPayReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CoinSubPayReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CoinSubPayReq) GetPayAmount() int64 {
	if x != nil {
		return x.PayAmount
	}
	return 0
}

func (x *CoinSubPayReq) GetPaySceneInfo() *adapter_unified_assets.PaySceneInfo {
	if x != nil {
		return x.PaySceneInfo
	}
	return nil
}

// 扣费金币
type CoinSubPayRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 订单状态 =>TradeStatusType
	Error  *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *CoinSubPayRsp) Reset() {
	*x = CoinSubPayRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinSubPayRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinSubPayRsp) ProtoMessage() {}

func (x *CoinSubPayRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinSubPayRsp.ProtoReflect.Descriptor instead.
func (*CoinSubPayRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{21}
}

func (x *CoinSubPayRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CoinSubPayRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// CoinSubResReq 扣费金币
type CoinSubResReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"` // 订单id，幂等使用
}

func (x *CoinSubResReq) Reset() {
	*x = CoinSubResReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinSubResReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinSubResReq) ProtoMessage() {}

func (x *CoinSubResReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinSubResReq.ProtoReflect.Descriptor instead.
func (*CoinSubResReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{22}
}

func (x *CoinSubResReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

// 扣费金币
type CoinSubResRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status int32  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"` // 订单状态 =>TradeStatusType
	Error  *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *CoinSubResRsp) Reset() {
	*x = CoinSubResRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CoinSubResRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinSubResRsp) ProtoMessage() {}

func (x *CoinSubResRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinSubResRsp.ProtoReflect.Descriptor instead.
func (*CoinSubResRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP(), []int{23}
}

func (x *CoinSubResRsp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CoinSubResRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_pb_xian_game_wzkz_game_game_v2_proto protoreflect.FileDescriptor

var file_pb_xian_game_wzkz_game_game_v2_proto_rawDesc = []byte{
	0x0a, 0x24, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x76, 0x32,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x1a, 0x3d, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x61, 0x64, 0x61,
	0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x2d, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22,
	0x34, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x32, 0x0a, 0x0a, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x70, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x56, 0x0a, 0x08, 0x45, 0x78, 0x46,
	0x54, 0x47, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49,
	0x64, 0x22, 0x32, 0x0a, 0x08, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x24, 0x0a, 0x0e, 0x45, 0x78, 0x43, 0x54, 0x46, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x60, 0x0a, 0x0e, 0x45,
	0x78, 0x43, 0x54, 0x46, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a,
	0x02, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x43, 0x54, 0x46, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x02, 0x6c, 0x6c, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x53, 0x0a,
	0x0b, 0x45, 0x78, 0x43, 0x54, 0x46, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02,
	0x74, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x63, 0x6f, 0x69, 0x6e, 0x43, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x72, 0x65, 0x5f,
	0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x66, 0x69, 0x72, 0x65, 0x43,
	0x6e, 0x74, 0x22, 0x0a, 0x0a, 0x08, 0x45, 0x78, 0x43, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x22, 0x5d,
	0x0a, 0x08, 0x45, 0x78, 0x43, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x07, 0x65, 0x78,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x43, 0x6e, 0x74, 0x52, 0x06, 0x65,
	0x78, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x49, 0x0a,
	0x05, 0x45, 0x78, 0x43, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x0a, 0x72, 0x64, 0x5f, 0x61, 0x6c, 0x6c,
	0x5f, 0x63, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x72, 0x64, 0x41, 0x6c,
	0x6c, 0x43, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x72, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x64, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x43, 0x6e, 0x74, 0x22, 0x24, 0x0a, 0x0e, 0x45, 0x78, 0x46, 0x54,
	0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x60,
	0x0a, 0x0e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x26, 0x0a, 0x02, 0x6c, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x02, 0x6c, 0x6c, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x22, 0x6a, 0x0a, 0x0b, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x74, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x66, 0x69, 0x72, 0x65, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x66, 0x69, 0x72, 0x65, 0x43, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x09, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xac, 0x01, 0x0a,
	0x08, 0x47, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x49, 0x66,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x49, 0x66, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67,
	0x69, 0x66, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x45,
	0x78, 0x46, 0x54, 0x47, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0xb0, 0x01, 0x0a, 0x0c,
	0x45, 0x78, 0x46, 0x54, 0x47, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x02,
	0x6d, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73,
	0x70, 0x2e, 0x4d, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x02, 0x6d, 0x6d, 0x12, 0x26, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x47, 0x0a, 0x07, 0x4d, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78,
	0x46, 0x54, 0x47, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x35,
	0x0a, 0x05, 0x45, 0x78, 0x46, 0x54, 0x47, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x75, 0x5f, 0x63, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x61, 0x75, 0x43, 0x6e, 0x74, 0x12, 0x15,
	0x0a, 0x06, 0x73, 0x75, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x73, 0x75, 0x43, 0x6e, 0x74, 0x22, 0x0b, 0x0a, 0x09, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x22, 0x45, 0x0a, 0x09, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x10, 0x0a, 0x03, 0x74, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xd0, 0x02, 0x0a, 0x0d, 0x43, 0x6f,
	0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x01, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x2e, 0x45, 0x78, 0x43, 0x54, 0x46, 0x54, 0x79, 0x70, 0x65, 0x52, 0x01, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x63, 0x6f, 0x69, 0x6e, 0x43, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x65,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x66, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0e,
	0x70, 0x61, 0x79, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x67,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75,
	0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x61,
	0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x32, 0x0a, 0x09, 0x45, 0x78, 0x43, 0x54,
	0x46, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x78, 0x43, 0x54, 0x46, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x78, 0x43,
	0x54, 0x46, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x41, 0x6c, 0x6c, 0x10, 0x01, 0x22, 0x4f, 0x0a, 0x0d,
	0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x2a, 0x0a,
	0x0d, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x0d, 0x43, 0x6f, 0x69,
	0x6e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0xa0, 0x01, 0x0a, 0x0f, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13,
	0x0a, 0x0f, 0x54, 0x53, 0x5f, 0x53, 0x75, 0x62, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72,
	0x79, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x10, 0x01, 0x12,
	0x11, 0x0a, 0x0d, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x53, 0x5f, 0x50, 0x61, 0x79,
	0x55, 0x6e, 0x6b, 0x6f, 0x77, 0x6e, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x53, 0x5f, 0x53,
	0x65, 0x6e, 0x64, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10,
	0x06, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x53, 0x5f, 0x45, 0x6e, 0x64, 0x10, 0x14, 0x42, 0x4f, 0x5a,
	0x4d, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70,
	0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a, 0x6b, 0x7a, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a, 0x6b, 0x7a, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_xian_game_wzkz_game_game_v2_proto_rawDescOnce sync.Once
	file_pb_xian_game_wzkz_game_game_v2_proto_rawDescData = file_pb_xian_game_wzkz_game_game_v2_proto_rawDesc
)

func file_pb_xian_game_wzkz_game_game_v2_proto_rawDescGZIP() []byte {
	file_pb_xian_game_wzkz_game_game_v2_proto_rawDescOnce.Do(func() {
		file_pb_xian_game_wzkz_game_game_v2_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_game_wzkz_game_game_v2_proto_rawDescData)
	})
	return file_pb_xian_game_wzkz_game_game_v2_proto_rawDescData
}

var file_pb_xian_game_wzkz_game_game_v2_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_pb_xian_game_wzkz_game_game_v2_proto_goTypes = []interface{}{
	(TradeStatusType)(0),                        // 0: xian_wzkz.TradeStatusType
	(CoinSubPayReq_ExCTFType)(0),                // 1: xian_wzkz.CoinSubPayReq.ExCTFType
	(*Error)(nil),                               // 2: xian_wzkz.Error
	(*SyncOptReq)(nil),                          // 3: xian_wzkz.SyncOptReq
	(*SyncOptRsp)(nil),                          // 4: xian_wzkz.SyncOptRsp
	(*ExFTGReq)(nil),                            // 5: xian_wzkz.ExFTGReq
	(*ExFTGRsp)(nil),                            // 6: xian_wzkz.ExFTGRsp
	(*ExCTFRecordReq)(nil),                      // 7: xian_wzkz.ExCTFRecordReq
	(*ExCTFRecordRsp)(nil),                      // 8: xian_wzkz.ExCTFRecordRsp
	(*ExCTFRecord)(nil),                         // 9: xian_wzkz.ExCTFRecord
	(*ExCntReq)(nil),                            // 10: xian_wzkz.ExCntReq
	(*ExCntRsp)(nil),                            // 11: xian_wzkz.ExCntRsp
	(*ExCnt)(nil),                               // 12: xian_wzkz.ExCnt
	(*ExFTGRecordReq)(nil),                      // 13: xian_wzkz.ExFTGRecordReq
	(*ExFTGRecordRsp)(nil),                      // 14: xian_wzkz.ExFTGRecordRsp
	(*ExFTGRecord)(nil),                         // 15: xian_wzkz.ExFTGRecord
	(*GiftInfo)(nil),                            // 16: xian_wzkz.GiftInfo
	(*ExFTGListReq)(nil),                        // 17: xian_wzkz.ExFTGListReq
	(*ExFTGListRsp)(nil),                        // 18: xian_wzkz.ExFTGListRsp
	(*ExFTG)(nil),                               // 19: xian_wzkz.ExFTG
	(*TicketReq)(nil),                           // 20: xian_wzkz.TicketReq
	(*TicketRsp)(nil),                           // 21: xian_wzkz.TicketRsp
	(*CoinSubPayReq)(nil),                       // 22: xian_wzkz.CoinSubPayReq
	(*CoinSubPayRsp)(nil),                       // 23: xian_wzkz.CoinSubPayRsp
	(*CoinSubResReq)(nil),                       // 24: xian_wzkz.CoinSubResReq
	(*CoinSubResRsp)(nil),                       // 25: xian_wzkz.CoinSubResRsp
	nil,                                         // 26: xian_wzkz.ExFTGListRsp.MmEntry
	(*adapter_unified_assets.PaySceneInfo)(nil), // 27: adapter_unified_assets.PaySceneInfo
}
var file_pb_xian_game_wzkz_game_game_v2_proto_depIdxs = []int32{
	2,  // 0: xian_wzkz.ExFTGRsp.error:type_name -> xian_wzkz.Error
	9,  // 1: xian_wzkz.ExCTFRecordRsp.ll:type_name -> xian_wzkz.ExCTFRecord
	2,  // 2: xian_wzkz.ExCTFRecordRsp.error:type_name -> xian_wzkz.Error
	12, // 3: xian_wzkz.ExCntRsp.ex_info:type_name -> xian_wzkz.ExCnt
	2,  // 4: xian_wzkz.ExCntRsp.error:type_name -> xian_wzkz.Error
	15, // 5: xian_wzkz.ExFTGRecordRsp.ll:type_name -> xian_wzkz.ExFTGRecord
	2,  // 6: xian_wzkz.ExFTGRecordRsp.error:type_name -> xian_wzkz.Error
	16, // 7: xian_wzkz.ExFTGRecord.gift_info:type_name -> xian_wzkz.GiftInfo
	26, // 8: xian_wzkz.ExFTGListRsp.mm:type_name -> xian_wzkz.ExFTGListRsp.MmEntry
	2,  // 9: xian_wzkz.ExFTGListRsp.error:type_name -> xian_wzkz.Error
	2,  // 10: xian_wzkz.TicketRsp.error:type_name -> xian_wzkz.Error
	1,  // 11: xian_wzkz.CoinSubPayReq.t:type_name -> xian_wzkz.CoinSubPayReq.ExCTFType
	27, // 12: xian_wzkz.CoinSubPayReq.pay_scene_info:type_name -> adapter_unified_assets.PaySceneInfo
	2,  // 13: xian_wzkz.CoinSubPayRsp.error:type_name -> xian_wzkz.Error
	2,  // 14: xian_wzkz.CoinSubResRsp.error:type_name -> xian_wzkz.Error
	19, // 15: xian_wzkz.ExFTGListRsp.MmEntry.value:type_name -> xian_wzkz.ExFTG
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_pb_xian_game_wzkz_game_game_v2_proto_init() }
func file_pb_xian_game_wzkz_game_game_v2_proto_init() {
	if File_pb_xian_game_wzkz_game_game_v2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExCTFRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExCTFRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExCTFRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExCntReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExCntRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExCnt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTGListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFTG); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TicketRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinSubPayReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinSubPayRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinSubResReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CoinSubResRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_game_wzkz_game_game_v2_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_xian_game_wzkz_game_game_v2_proto_goTypes,
		DependencyIndexes: file_pb_xian_game_wzkz_game_game_v2_proto_depIdxs,
		EnumInfos:         file_pb_xian_game_wzkz_game_game_v2_proto_enumTypes,
		MessageInfos:      file_pb_xian_game_wzkz_game_game_v2_proto_msgTypes,
	}.Build()
	File_pb_xian_game_wzkz_game_game_v2_proto = out.File
	file_pb_xian_game_wzkz_game_game_v2_proto_rawDesc = nil
	file_pb_xian_game_wzkz_game_game_v2_proto_goTypes = nil
	file_pb_xian_game_wzkz_game_game_v2_proto_depIdxs = nil
}
