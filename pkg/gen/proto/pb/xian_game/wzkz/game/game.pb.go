// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/xian_game/wzkz/game/game.proto

package game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	gm "kugou_adapter_service/pkg/gen/proto/pb/xian_game/wzkz/gm"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MatchReq_RoomType int32

const (
	MatchReq_KBPrimary  MatchReq_RoomType = 0 // value=2, KB场初级场
	MatchReq_KBMedium   MatchReq_RoomType = 1 // value=4, KB场中级场
	MatchReq_KBAdvanced MatchReq_RoomType = 2 // value=6, KB场高级场
	MatchReq_FPrimary   MatchReq_RoomType = 3 // value=3, 鲜花场初级场
	MatchReq_FMedium    MatchReq_RoomType = 4 // value=5, 鲜花场中级场
	MatchReq_FAdvanced  MatchReq_RoomType = 5 // value=7, 鲜花场高级场
)

// Enum value maps for MatchReq_RoomType.
var (
	MatchReq_RoomType_name = map[int32]string{
		0: "KBPrimary",
		1: "KBMedium",
		2: "KBAdvanced",
		3: "FPrimary",
		4: "FMedium",
		5: "FAdvanced",
	}
	MatchReq_RoomType_value = map[string]int32{
		"KBPrimary":  0,
		"KBMedium":   1,
		"KBAdvanced": 2,
		"FPrimary":   3,
		"FMedium":    4,
		"FAdvanced":  5,
	}
)

func (x MatchReq_RoomType) Enum() *MatchReq_RoomType {
	p := new(MatchReq_RoomType)
	*p = x
	return p
}

func (x MatchReq_RoomType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchReq_RoomType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_xian_game_wzkz_game_game_proto_enumTypes[0].Descriptor()
}

func (MatchReq_RoomType) Type() protoreflect.EnumType {
	return &file_pb_xian_game_wzkz_game_game_proto_enumTypes[0]
}

func (x MatchReq_RoomType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchReq_RoomType.Descriptor instead.
func (MatchReq_RoomType) EnumDescriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{10, 0}
}

type Gift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`        // 礼物编码
	Type  uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`   // 类型
	Name  string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`    // 名称
	Logo  string `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`    // logo
	Num   uint32 `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`     // 数量
	Price int64  `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"` // 礼物价值，配置接口有用
}

func (x *Gift) Reset() {
	*x = Gift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Gift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gift) ProtoMessage() {}

func (x *Gift) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gift.ProtoReflect.Descriptor instead.
func (*Gift) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{0}
}

func (x *Gift) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Gift) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Gift) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Gift) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *Gift) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Gift) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

// 注册请求体
type RegReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RegReq) Reset() {
	*x = RegReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegReq) ProtoMessage() {}

func (x *RegReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegReq.ProtoReflect.Descriptor instead.
func (*RegReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{1}
}

type RegRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureLevel uint32 `protobuf:"varint,1,opt,name=treasure_level,json=treasureLevel,proto3" json:"treasure_level,omitempty"`
	First         uint32 `protobuf:"varint,2,opt,name=first,proto3" json:"first,omitempty"`
	Nickname      string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"` // 昵称
	Avatar        string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`     // 头像
	Error         *Error `protobuf:"bytes,16,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RegRsp) Reset() {
	*x = RegRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegRsp) ProtoMessage() {}

func (x *RegRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegRsp.ProtoReflect.Descriptor instead.
func (*RegRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{2}
}

func (x *RegRsp) GetTreasureLevel() uint32 {
	if x != nil {
		return x.TreasureLevel
	}
	return 0
}

func (x *RegRsp) GetFirst() uint32 {
	if x != nil {
		return x.First
	}
	return 0
}

func (x *RegRsp) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RegRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RegRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastId uint32 `protobuf:"varint,1,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"` // 初次传0，再次则传初次返回数据的最后一条记录ID
	Flower bool   `protobuf:"varint,2,opt,name=flower,proto3" json:"flower,omitempty"`               // 默认false及礼物场记录, true为鲜花场记录
}

func (x *RecordReq) Reset() {
	*x = RecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordReq) ProtoMessage() {}

func (x *RecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordReq.ProtoReflect.Descriptor instead.
func (*RecordReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{3}
}

func (x *RecordReq) GetLastId() uint32 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *RecordReq) GetFlower() bool {
	if x != nil {
		return x.Flower
	}
	return false
}

type RecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*RecordRsp_Record `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Error *Error              `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RecordRsp) Reset() {
	*x = RecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordRsp) ProtoMessage() {}

func (x *RecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordRsp.ProtoReflect.Descriptor instead.
func (*RecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{4}
}

func (x *RecordRsp) GetList() []*RecordRsp_Record {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecordRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExFireRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastId uint32 `protobuf:"varint,1,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"` // 初次传0，再次则传初次返回数据的最后一条记录ID
	ExType uint32 `protobuf:"varint,2,opt,name=ex_type,json=exType,proto3" json:"ex_type,omitempty"` // 不传和传1是 金币兑换火力, 2-种子兑换水滴
}

func (x *ExFireRecordReq) Reset() {
	*x = ExFireRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFireRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFireRecordReq) ProtoMessage() {}

func (x *ExFireRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFireRecordReq.ProtoReflect.Descriptor instead.
func (*ExFireRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{5}
}

func (x *ExFireRecordReq) GetLastId() uint32 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *ExFireRecordReq) GetExType() uint32 {
	if x != nil {
		return x.ExType
	}
	return 0
}

type ExFireRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ExFireRecordRsp_Record `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Error *Error                    `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExFireRecordRsp) Reset() {
	*x = ExFireRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFireRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFireRecordRsp) ProtoMessage() {}

func (x *ExFireRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFireRecordRsp.ProtoReflect.Descriptor instead.
func (*ExFireRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{6}
}

func (x *ExFireRecordRsp) GetList() []*ExFireRecordRsp_Record {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ExFireRecordRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConfigReq) Reset() {
	*x = ConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigReq) ProtoMessage() {}

func (x *ConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigReq.ProtoReflect.Descriptor instead.
func (*ConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{7}
}

type Plane struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`     // 灰机ID
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`  // 名称
	Prob uint32 `protobuf:"varint,3,opt,name=prob,proto3" json:"prob,omitempty"` // 概率
	Odds uint32 `protobuf:"varint,4,opt,name=odds,proto3" json:"odds,omitempty"` // 倍率
}

func (x *Plane) Reset() {
	*x = Plane{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Plane) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plane) ProtoMessage() {}

func (x *Plane) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plane.ProtoReflect.Descriptor instead.
func (*Plane) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{8}
}

func (x *Plane) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Plane) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Plane) GetProb() uint32 {
	if x != nil {
		return x.Prob
	}
	return 0
}

func (x *Plane) GetOdds() uint32 {
	if x != nil {
		return x.Odds
	}
	return 0
}

type ConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExQuotaLimit      uint32   `protobuf:"varint,1,opt,name=ex_quota_limit,json=exQuotaLimit,proto3" json:"ex_quota_limit,omitempty"`                // 每日兑换火力上限
	Planes            []*Plane `protobuf:"bytes,2,rep,name=planes,proto3" json:"planes,omitempty"`                                                   // 灰机信息
	ResetCumulantDays uint32   `protobuf:"varint,3,opt,name=reset_cumulant_days,json=resetCumulantDays,proto3" json:"reset_cumulant_days,omitempty"` // 重置累积量天数
	ExchangeCtf       string   `protobuf:"bytes,4,opt,name=exchange_ctf,json=exchangeCtf,proto3" json:"exchange_ctf,omitempty"`                      // 金币兑换火力配置
	ExchangeFtg       string   `protobuf:"bytes,5,opt,name=exchange_ftg,json=exchangeFtg,proto3" json:"exchange_ftg,omitempty"`                      // 火力兑换礼包配置
	GiftMap           string   `protobuf:"bytes,6,opt,name=gift_map,json=giftMap,proto3" json:"gift_map,omitempty"`                                  // 礼包map
}

func (x *ConfigRsp) Reset() {
	*x = ConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigRsp) ProtoMessage() {}

func (x *ConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigRsp.ProtoReflect.Descriptor instead.
func (*ConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{9}
}

func (x *ConfigRsp) GetExQuotaLimit() uint32 {
	if x != nil {
		return x.ExQuotaLimit
	}
	return 0
}

func (x *ConfigRsp) GetPlanes() []*Plane {
	if x != nil {
		return x.Planes
	}
	return nil
}

func (x *ConfigRsp) GetResetCumulantDays() uint32 {
	if x != nil {
		return x.ResetCumulantDays
	}
	return 0
}

func (x *ConfigRsp) GetExchangeCtf() string {
	if x != nil {
		return x.ExchangeCtf
	}
	return ""
}

func (x *ConfigRsp) GetExchangeFtg() string {
	if x != nil {
		return x.ExchangeFtg
	}
	return ""
}

func (x *ConfigRsp) GetGiftMap() string {
	if x != nil {
		return x.GiftMap
	}
	return ""
}

type MatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId     string `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`              // 换房间前的房间号，非换房间情况不填
	RoomType   uint32 `protobuf:"varint,2,opt,name=room_type,json=roomType,proto3" json:"room_type,omitempty"`       // 参考下面的枚举, 值为其value
	WshVersion uint32 `protobuf:"varint,3,opt,name=wsh_version,json=wshVersion,proto3" json:"wsh_version,omitempty"` // 要匹配的房间版本
	Force      bool   `protobuf:"varint,4,opt,name=force,proto3" json:"force,omitempty"`                             // 是否强制, 内部使用
}

func (x *MatchReq) Reset() {
	*x = MatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchReq) ProtoMessage() {}

func (x *MatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchReq.ProtoReflect.Descriptor instead.
func (*MatchReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{10}
}

func (x *MatchReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *MatchReq) GetRoomType() uint32 {
	if x != nil {
		return x.RoomType
	}
	return 0
}

func (x *MatchReq) GetWshVersion() uint32 {
	if x != nil {
		return x.WshVersion
	}
	return 0
}

func (x *MatchReq) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type MatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` // 新房间号
	Error  *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *MatchRsp) Reset() {
	*x = MatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchRsp) ProtoMessage() {}

func (x *MatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchRsp.ProtoReflect.Descriptor instead.
func (*MatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{11}
}

func (x *MatchRsp) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *MatchRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AssetsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NeedExpire bool `protobuf:"varint,1,opt,name=need_expire,json=needExpire,proto3" json:"need_expire,omitempty"` // 是否需要过期信息
}

func (x *AssetsReq) Reset() {
	*x = AssetsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetsReq) ProtoMessage() {}

func (x *AssetsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetsReq.ProtoReflect.Descriptor instead.
func (*AssetsReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{12}
}

func (x *AssetsReq) GetNeedExpire() bool {
	if x != nil {
		return x.NeedExpire
	}
	return false
}

// 用户资产的过期信息
type ExpireInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNum   int64 `protobuf:"varint,1,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	ExpireTime int64 `protobuf:"varint,2,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
}

func (x *ExpireInfo) Reset() {
	*x = ExpireInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireInfo) ProtoMessage() {}

func (x *ExpireInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireInfo.ProtoReflect.Descriptor instead.
func (*ExpireInfo) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{13}
}

func (x *ExpireInfo) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *ExpireInfo) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

// 用户资产详情
type UAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  int64         `protobuf:"varint,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetNum int64         `protobuf:"varint,2,opt,name=asset_num,json=assetNum,proto3" json:"asset_num,omitempty"`
	Expires  []*ExpireInfo `protobuf:"bytes,3,rep,name=expires,proto3" json:"expires,omitempty"`
	Name     string        `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Icon     string        `protobuf:"bytes,5,opt,name=icon,proto3" json:"icon,omitempty"`
	Tag      string        `protobuf:"bytes,6,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *UAsset) Reset() {
	*x = UAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UAsset) ProtoMessage() {}

func (x *UAsset) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UAsset.ProtoReflect.Descriptor instead.
func (*UAsset) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{14}
}

func (x *UAsset) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *UAsset) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *UAsset) GetExpires() []*ExpireInfo {
	if x != nil {
		return x.Expires
	}
	return nil
}

func (x *UAsset) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UAsset) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *UAsset) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

type AssetsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets  []*UAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`    // 资产列表
	Diamond int64     `protobuf:"varint,2,opt,name=diamond,proto3" json:"diamond,omitempty"` // 网赚金币余额
	Error   *Error    `protobuf:"bytes,16,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AssetsRsp) Reset() {
	*x = AssetsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetsRsp) ProtoMessage() {}

func (x *AssetsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetsRsp.ProtoReflect.Descriptor instead.
func (*AssetsRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{15}
}

func (x *AssetsRsp) GetAssets() []*UAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *AssetsRsp) GetDiamond() int64 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *AssetsRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type ExFireReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Star   uint32 `protobuf:"varint,1,opt,name=star,proto3" json:"star,omitempty"`                  // 需要兑换的金币
	RoomId string `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` // 房间号
	Seed   uint32 `protobuf:"varint,3,opt,name=seed,proto3" json:"seed,omitempty"`                  // 需要对象的种子
}

func (x *ExFireReq) Reset() {
	*x = ExFireReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFireReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFireReq) ProtoMessage() {}

func (x *ExFireReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFireReq.ProtoReflect.Descriptor instead.
func (*ExFireReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{16}
}

func (x *ExFireReq) GetStar() uint32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *ExFireReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *ExFireReq) GetSeed() uint32 {
	if x != nil {
		return x.Seed
	}
	return 0
}

type ExFireRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StarCost uint32 `protobuf:"varint,1,opt,name=star_cost,json=starCost,proto3" json:"star_cost,omitempty"` // 耗费的金币
	Fire     uint32 `protobuf:"varint,2,opt,name=fire,proto3" json:"fire,omitempty"`                         // 获得的火力
	Used     uint32 `protobuf:"varint,3,opt,name=used,proto3" json:"used,omitempty"`                         // 已使用的额度
	SeedCost uint32 `protobuf:"varint,4,opt,name=seed_cost,json=seedCost,proto3" json:"seed_cost,omitempty"` // 耗费的种子
	Water    uint32 `protobuf:"varint,5,opt,name=water,proto3" json:"water,omitempty"`                       // 获得的水滴
	Error    *Error `protobuf:"bytes,16,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ExFireRsp) Reset() {
	*x = ExFireRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFireRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFireRsp) ProtoMessage() {}

func (x *ExFireRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFireRsp.ProtoReflect.Descriptor instead.
func (*ExFireRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{17}
}

func (x *ExFireRsp) GetStarCost() uint32 {
	if x != nil {
		return x.StarCost
	}
	return 0
}

func (x *ExFireRsp) GetFire() uint32 {
	if x != nil {
		return x.Fire
	}
	return 0
}

func (x *ExFireRsp) GetUsed() uint32 {
	if x != nil {
		return x.Used
	}
	return 0
}

func (x *ExFireRsp) GetSeedCost() uint32 {
	if x != nil {
		return x.SeedCost
	}
	return 0
}

func (x *ExFireRsp) GetWater() uint32 {
	if x != nil {
		return x.Water
	}
	return 0
}

func (x *ExFireRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NType uint32 `protobuf:"varint,1,opt,name=n_type,json=nType,proto3" json:"n_type,omitempty"` // 1-金币兑换礼物
}

func (x *NotifyReq) Reset() {
	*x = NotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyReq) ProtoMessage() {}

func (x *NotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyReq.ProtoReflect.Descriptor instead.
func (*NotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{18}
}

func (x *NotifyReq) GetNType() uint32 {
	if x != nil {
		return x.NType
	}
	return 0
}

type NotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Error *Error `protobuf:"bytes,100,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *NotifyRsp) Reset() {
	*x = NotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyRsp) ProtoMessage() {}

func (x *NotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyRsp.ProtoReflect.Descriptor instead.
func (*NotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{19}
}

func (x *NotifyRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type SwitchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SwitchReq) Reset() {
	*x = SwitchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchReq) ProtoMessage() {}

func (x *SwitchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchReq.ProtoReflect.Descriptor instead.
func (*SwitchReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{20}
}

type SwitchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExFire uint32 `protobuf:"varint,1,opt,name=ex_fire,json=exFire,proto3" json:"ex_fire,omitempty"` // 火力兑换财富等级（包含)
	ExGift uint32 `protobuf:"varint,2,opt,name=ex_gift,json=exGift,proto3" json:"ex_gift,omitempty"` // 礼物兑换财富等级（包含)
}

func (x *SwitchRsp) Reset() {
	*x = SwitchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchRsp) ProtoMessage() {}

func (x *SwitchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchRsp.ProtoReflect.Descriptor instead.
func (*SwitchRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{21}
}

func (x *SwitchRsp) GetExFire() uint32 {
	if x != nil {
		return x.ExFire
	}
	return 0
}

func (x *SwitchRsp) GetExGift() uint32 {
	if x != nil {
		return x.ExGift
	}
	return 0
}

type QuitRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QuitRoomReq) Reset() {
	*x = QuitRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuitRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitRoomReq) ProtoMessage() {}

func (x *QuitRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitRoomReq.ProtoReflect.Descriptor instead.
func (*QuitRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{22}
}

type QuitRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Error *Error `protobuf:"bytes,16,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *QuitRoomRsp) Reset() {
	*x = QuitRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuitRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitRoomRsp) ProtoMessage() {}

func (x *QuitRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitRoomRsp.ProtoReflect.Descriptor instead.
func (*QuitRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{23}
}

func (x *QuitRoomRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RechargeFAssetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cnt uint32 `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"` // 鲜花换水滴数量
}

func (x *RechargeFAssetReq) Reset() {
	*x = RechargeFAssetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeFAssetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeFAssetReq) ProtoMessage() {}

func (x *RechargeFAssetReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeFAssetReq.ProtoReflect.Descriptor instead.
func (*RechargeFAssetReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{24}
}

func (x *RechargeFAssetReq) GetCnt() uint32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

type RechargeFAssetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets []*UAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`
	Error  *Error    `protobuf:"bytes,16,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *RechargeFAssetRsp) Reset() {
	*x = RechargeFAssetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RechargeFAssetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeFAssetRsp) ProtoMessage() {}

func (x *RechargeFAssetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeFAssetRsp.ProtoReflect.Descriptor instead.
func (*RechargeFAssetRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{25}
}

func (x *RechargeFAssetRsp) GetAssets() []*UAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *RechargeFAssetRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type MatchVerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WshVersion uint32 `protobuf:"varint,1,opt,name=wsh_version,json=wshVersion,proto3" json:"wsh_version,omitempty"` // 匹配的版本
	Force      bool   `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`                             // 是否强制, 内部使用
}

func (x *MatchVerReq) Reset() {
	*x = MatchVerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchVerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchVerReq) ProtoMessage() {}

func (x *MatchVerReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchVerReq.ProtoReflect.Descriptor instead.
func (*MatchVerReq) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{26}
}

func (x *MatchVerReq) GetWshVersion() uint32 {
	if x != nil {
		return x.WshVersion
	}
	return 0
}

func (x *MatchVerReq) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type MatchVerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WshVersion uint32 `protobuf:"varint,1,opt,name=wsh_version,json=wshVersion,proto3" json:"wsh_version,omitempty"` // 透传
	Open       bool   `protobuf:"varint,2,opt,name=open,proto3" json:"open,omitempty"`                               // 是否开着的
	Error      *Error `protobuf:"bytes,16,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *MatchVerRsp) Reset() {
	*x = MatchVerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchVerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchVerRsp) ProtoMessage() {}

func (x *MatchVerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchVerRsp.ProtoReflect.Descriptor instead.
func (*MatchVerRsp) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{27}
}

func (x *MatchVerRsp) GetWshVersion() uint32 {
	if x != nil {
		return x.WshVersion
	}
	return 0
}

func (x *MatchVerRsp) GetOpen() bool {
	if x != nil {
		return x.Open
	}
	return false
}

func (x *MatchVerRsp) GetError() *Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RecordRsp_Record struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                // 索引
	Mid       uint32  `protobuf:"varint,2,opt,name=mid,proto3" json:"mid,omitempty"`                              // 灰机ID
	Odds      uint32  `protobuf:"varint,3,opt,name=odds,proto3" json:"odds,omitempty"`                            // 倍率
	Wager     uint32  `protobuf:"varint,4,opt,name=wager,proto3" json:"wager,omitempty"`                          // 消耗的火力
	Obtain    uint32  `protobuf:"varint,5,opt,name=obtain,proto3" json:"obtain,omitempty"`                        // 获得的星力
	Time      uint32  `protobuf:"varint,6,opt,name=time,proto3" json:"time,omitempty"`                            // 击杀时间
	Gifts     []*Gift `protobuf:"bytes,7,rep,name=gifts,proto3" json:"gifts,omitempty"`                           // 获得的礼物
	GiftAsset uint32  `protobuf:"varint,8,opt,name=gift_asset,json=giftAsset,proto3" json:"gift_asset,omitempty"` // 礼物总价值
}

func (x *RecordRsp_Record) Reset() {
	*x = RecordRsp_Record{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordRsp_Record) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordRsp_Record) ProtoMessage() {}

func (x *RecordRsp_Record) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordRsp_Record.ProtoReflect.Descriptor instead.
func (*RecordRsp_Record) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{4, 0}
}

func (x *RecordRsp_Record) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RecordRsp_Record) GetMid() uint32 {
	if x != nil {
		return x.Mid
	}
	return 0
}

func (x *RecordRsp_Record) GetOdds() uint32 {
	if x != nil {
		return x.Odds
	}
	return 0
}

func (x *RecordRsp_Record) GetWager() uint32 {
	if x != nil {
		return x.Wager
	}
	return 0
}

func (x *RecordRsp_Record) GetObtain() uint32 {
	if x != nil {
		return x.Obtain
	}
	return 0
}

func (x *RecordRsp_Record) GetTime() uint32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *RecordRsp_Record) GetGifts() []*Gift {
	if x != nil {
		return x.Gifts
	}
	return nil
}

func (x *RecordRsp_Record) GetGiftAsset() uint32 {
	if x != nil {
		return x.GiftAsset
	}
	return 0
}

type ExFireRecordRsp_Record struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         // 索引
	Obtain uint32 `protobuf:"varint,2,opt,name=obtain,proto3" json:"obtain,omitempty"` // 获得火力
	Cost   uint32 `protobuf:"varint,3,opt,name=cost,proto3" json:"cost,omitempty"`     // 花费金币
	Time   uint32 `protobuf:"varint,15,opt,name=time,proto3" json:"time,omitempty"`    // 兑换时间
}

func (x *ExFireRecordRsp_Record) Reset() {
	*x = ExFireRecordRsp_Record{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExFireRecordRsp_Record) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExFireRecordRsp_Record) ProtoMessage() {}

func (x *ExFireRecordRsp_Record) ProtoReflect() protoreflect.Message {
	mi := &file_pb_xian_game_wzkz_game_game_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExFireRecordRsp_Record.ProtoReflect.Descriptor instead.
func (*ExFireRecordRsp_Record) Descriptor() ([]byte, []int) {
	return file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ExFireRecordRsp_Record) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExFireRecordRsp_Record) GetObtain() uint32 {
	if x != nil {
		return x.Obtain
	}
	return 0
}

func (x *ExFireRecordRsp_Record) GetCost() uint32 {
	if x != nil {
		return x.Cost
	}
	return 0
}

func (x *ExFireRecordRsp_Record) GetTime() uint32 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_pb_xian_game_wzkz_game_game_proto protoreflect.FileDescriptor

var file_pb_xian_game_wzkz_game_game_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x09, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x1a, 0x31,
	0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x24, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f,
	0x77, 0x7a, 0x6b, 0x7a, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x76,
	0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a, 0x6b, 0x7a, 0x2f, 0x67, 0x6d, 0x2f, 0x67, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7a, 0x0a, 0x04, 0x47, 0x69, 0x66, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x22, 0x08, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x52, 0x65, 0x71, 0x22, 0xa1, 0x01, 0x0a,
	0x06, 0x52, 0x65, 0x67, 0x52, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x65, 0x61, 0x73,
	0x75, 0x72, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14,
	0x0a, 0x05, 0x66, 0x69, 0x72, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x22, 0x3c, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x07, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x22, 0xad,
	0x02, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0xc6, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x64, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x6f, 0x64, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x61, 0x67, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x61, 0x67, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x62,
	0x74, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x67, 0x69, 0x66, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x52, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x22, 0x43,
	0x0a, 0x0f, 0x45, 0x78, 0x46, 0x69, 0x72, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x78, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x46, 0x69, 0x72, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x45, 0x78, 0x46, 0x69, 0x72, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73,
	0x70, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x1a, 0x58, 0x0a, 0x06, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x6f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65,
	0x22, 0x0b, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x22, 0x53, 0x0a,
	0x05, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x72,
	0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x12, 0x12,
	0x0a, 0x04, 0x6f, 0x64, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6f, 0x64,
	0x64, 0x73, 0x22, 0xec, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70,
	0x12, 0x24, 0x0a, 0x0e, 0x65, 0x78, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78, 0x51, 0x75, 0x6f, 0x74,
	0x61, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x65, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61,
	0x6e, 0x74, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x65, 0x74, 0x43, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x6e, 0x74, 0x44, 0x61, 0x79, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x74, 0x66,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x43, 0x74, 0x66, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x66, 0x74, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x46, 0x74, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x69, 0x66, 0x74, 0x4d, 0x61,
	0x70, 0x22, 0xda, 0x01, 0x0a, 0x08, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x73, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x77, 0x73, 0x68, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x22, 0x61, 0x0a, 0x08, 0x52,
	0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x4b, 0x42, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4b, 0x42, 0x4d, 0x65, 0x64, 0x69,
	0x75, 0x6d, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4b, 0x42, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63,
	0x65, 0x64, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x50, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x10, 0x04, 0x12,
	0x0d, 0x0a, 0x09, 0x46, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x10, 0x05, 0x22, 0x4b,
	0x0a, 0x08, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f,
	0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x2c, 0x0a, 0x09, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x65, 0x65, 0x64,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6e,
	0x65, 0x65, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x22, 0x4a, 0x0a, 0x0a, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xab, 0x01, 0x0a, 0x06, 0x55, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x2f, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x74, 0x61, 0x67, 0x22, 0x78, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x29, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x55, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x69,
	0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x4c, 0x0a,
	0x09, 0x45, 0x78, 0x46, 0x69, 0x72, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74,
	0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x74, 0x61, 0x72, 0x12, 0x17,
	0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x73, 0x65, 0x65, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x09,
	0x45, 0x78, 0x46, 0x69, 0x72, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x74,
	0x61, 0x72, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x72, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x69, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x75, 0x73, 0x65, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x73, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x77,
	0x61, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x61, 0x74, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x22, 0x0a, 0x09, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x33, 0x0a,
	0x09, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x22, 0x0b, 0x0a, 0x09, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x22,
	0x3d, 0x0a, 0x09, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07,
	0x65, 0x78, 0x5f, 0x66, 0x69, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65,
	0x78, 0x46, 0x69, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x5f, 0x67, 0x69, 0x66, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x65, 0x78, 0x47, 0x69, 0x66, 0x74, 0x22, 0x0d,
	0x0a, 0x0b, 0x51, 0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x22, 0x35, 0x0a,
	0x0b, 0x51, 0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x22, 0x25, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x46, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x6e, 0x74, 0x22, 0x66, 0x0a, 0x11, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x29, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x55, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x22, 0x44, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x73, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x77, 0x73, 0x68, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x22, 0x6a, 0x0a, 0x0b, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x56, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x73, 0x68, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x77,
	0x73, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x26, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x32, 0x9f, 0x0b, 0x0a, 0x04, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x30,
	0x0a, 0x08, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x11, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x67, 0x52, 0x73, 0x70,
	0x12, 0x34, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x1a, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x05, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x07, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x34, 0x0a, 0x06, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x14, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x06, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x12, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x06,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x51, 0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x16,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x51, 0x75, 0x69, 0x74, 0x52,
	0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2e, 0x51, 0x75, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x4c,
	0x0a, 0x0e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x12, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x46, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x56, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x05, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x12, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x45, 0x0a, 0x09, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x73, 0x12, 0x1b,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x52, 0x6f, 0x6f, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x73,
	0x52, 0x6f, 0x6f, 0x6d, 0x73, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x04, 0x4b, 0x69, 0x63, 0x6b,
	0x12, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e,
	0x4b, 0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77,
	0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x4b, 0x69, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x3d,
	0x0a, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f, 0x67, 0x6d, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x5f,
	0x67, 0x6d, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a,
	0x07, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x70, 0x74, 0x12, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x53, 0x79, 0x6e, 0x63,
	0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x06, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a,
	0x6b, 0x7a, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x05,
	0x45, 0x78, 0x46, 0x54, 0x47, 0x12, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x73, 0x70, 0x12,
	0x43, 0x0a, 0x0b, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x19,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09, 0x45, 0x78, 0x46, 0x54, 0x47, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78,
	0x46, 0x54, 0x47, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x46, 0x54, 0x47, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x05, 0x45, 0x78, 0x43, 0x6e, 0x74, 0x12, 0x13, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x43, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78,
	0x43, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x0b, 0x45, 0x78, 0x43, 0x54, 0x46, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x45, 0x78, 0x43, 0x54, 0x46, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x45, 0x78, 0x43,
	0x54, 0x46, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0a, 0x43,
	0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79, 0x12, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e,
	0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a,
	0x0a, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x12, 0x18, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b, 0x7a, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x52,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x77, 0x7a, 0x6b,
	0x7a, 0x2e, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x46, 0x0a, 0x0c, 0x43, 0x42, 0x43, 0x6f, 0x69, 0x6e, 0x53, 0x75, 0x62, 0x50, 0x61, 0x79, 0x12,
	0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x4f, 0x5a, 0x4d, 0x67, 0x69, 0x74, 0x2e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77, 0x7a, 0x6b, 0x7a, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x77,
	0x7a, 0x6b, 0x7a, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_xian_game_wzkz_game_game_proto_rawDescOnce sync.Once
	file_pb_xian_game_wzkz_game_game_proto_rawDescData = file_pb_xian_game_wzkz_game_game_proto_rawDesc
)

func file_pb_xian_game_wzkz_game_game_proto_rawDescGZIP() []byte {
	file_pb_xian_game_wzkz_game_game_proto_rawDescOnce.Do(func() {
		file_pb_xian_game_wzkz_game_game_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_xian_game_wzkz_game_game_proto_rawDescData)
	})
	return file_pb_xian_game_wzkz_game_game_proto_rawDescData
}

var file_pb_xian_game_wzkz_game_game_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_xian_game_wzkz_game_game_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_pb_xian_game_wzkz_game_game_proto_goTypes = []interface{}{
	(MatchReq_RoomType)(0),            // 0: xian_wzkz.MatchReq.RoomType
	(*Gift)(nil),                      // 1: xian_wzkz.Gift
	(*RegReq)(nil),                    // 2: xian_wzkz.RegReq
	(*RegRsp)(nil),                    // 3: xian_wzkz.RegRsp
	(*RecordReq)(nil),                 // 4: xian_wzkz.RecordReq
	(*RecordRsp)(nil),                 // 5: xian_wzkz.RecordRsp
	(*ExFireRecordReq)(nil),           // 6: xian_wzkz.ExFireRecordReq
	(*ExFireRecordRsp)(nil),           // 7: xian_wzkz.ExFireRecordRsp
	(*ConfigReq)(nil),                 // 8: xian_wzkz.ConfigReq
	(*Plane)(nil),                     // 9: xian_wzkz.Plane
	(*ConfigRsp)(nil),                 // 10: xian_wzkz.ConfigRsp
	(*MatchReq)(nil),                  // 11: xian_wzkz.MatchReq
	(*MatchRsp)(nil),                  // 12: xian_wzkz.MatchRsp
	(*AssetsReq)(nil),                 // 13: xian_wzkz.AssetsReq
	(*ExpireInfo)(nil),                // 14: xian_wzkz.ExpireInfo
	(*UAsset)(nil),                    // 15: xian_wzkz.UAsset
	(*AssetsRsp)(nil),                 // 16: xian_wzkz.AssetsRsp
	(*ExFireReq)(nil),                 // 17: xian_wzkz.ExFireReq
	(*ExFireRsp)(nil),                 // 18: xian_wzkz.ExFireRsp
	(*NotifyReq)(nil),                 // 19: xian_wzkz.NotifyReq
	(*NotifyRsp)(nil),                 // 20: xian_wzkz.NotifyRsp
	(*SwitchReq)(nil),                 // 21: xian_wzkz.SwitchReq
	(*SwitchRsp)(nil),                 // 22: xian_wzkz.SwitchRsp
	(*QuitRoomReq)(nil),               // 23: xian_wzkz.QuitRoomReq
	(*QuitRoomRsp)(nil),               // 24: xian_wzkz.QuitRoomRsp
	(*RechargeFAssetReq)(nil),         // 25: xian_wzkz.RechargeFAssetReq
	(*RechargeFAssetRsp)(nil),         // 26: xian_wzkz.RechargeFAssetRsp
	(*MatchVerReq)(nil),               // 27: xian_wzkz.MatchVerReq
	(*MatchVerRsp)(nil),               // 28: xian_wzkz.MatchVerRsp
	(*RecordRsp_Record)(nil),          // 29: xian_wzkz.RecordRsp.Record
	(*ExFireRecordRsp_Record)(nil),    // 30: xian_wzkz.ExFireRecordRsp.Record
	(*Error)(nil),                     // 31: xian_wzkz.Error
	(*gm.NodesReq)(nil),               // 32: xian_wzkz_gm.NodesReq
	(*gm.NodesRoomsReq)(nil),          // 33: xian_wzkz_gm.NodesRoomsReq
	(*gm.KickReq)(nil),                // 34: xian_wzkz_gm.KickReq
	(*gm.SummaryReq)(nil),             // 35: xian_wzkz_gm.SummaryReq
	(*SyncOptReq)(nil),                // 36: xian_wzkz.SyncOptReq
	(*TicketReq)(nil),                 // 37: xian_wzkz.TicketReq
	(*ExFTGReq)(nil),                  // 38: xian_wzkz.ExFTGReq
	(*ExFTGRecordReq)(nil),            // 39: xian_wzkz.ExFTGRecordReq
	(*ExFTGListReq)(nil),              // 40: xian_wzkz.ExFTGListReq
	(*ExCntReq)(nil),                  // 41: xian_wzkz.ExCntReq
	(*ExCTFRecordReq)(nil),            // 42: xian_wzkz.ExCTFRecordReq
	(*CoinSubPayReq)(nil),             // 43: xian_wzkz.CoinSubPayReq
	(*CoinSubResReq)(nil),             // 44: xian_wzkz.CoinSubResReq
	(*callback.OrderShipmentReq)(nil), // 45: callback.OrderShipmentReq
	(*gm.NodesRsp)(nil),               // 46: xian_wzkz_gm.NodesRsp
	(*gm.NodesRoomsRsp)(nil),          // 47: xian_wzkz_gm.NodesRoomsRsp
	(*gm.KickRsp)(nil),                // 48: xian_wzkz_gm.KickRsp
	(*gm.SummaryRsp)(nil),             // 49: xian_wzkz_gm.SummaryRsp
	(*SyncOptRsp)(nil),                // 50: xian_wzkz.SyncOptRsp
	(*TicketRsp)(nil),                 // 51: xian_wzkz.TicketRsp
	(*ExFTGRsp)(nil),                  // 52: xian_wzkz.ExFTGRsp
	(*ExFTGRecordRsp)(nil),            // 53: xian_wzkz.ExFTGRecordRsp
	(*ExFTGListRsp)(nil),              // 54: xian_wzkz.ExFTGListRsp
	(*ExCntRsp)(nil),                  // 55: xian_wzkz.ExCntRsp
	(*ExCTFRecordRsp)(nil),            // 56: xian_wzkz.ExCTFRecordRsp
	(*CoinSubPayRsp)(nil),             // 57: xian_wzkz.CoinSubPayRsp
	(*CoinSubResRsp)(nil),             // 58: xian_wzkz.CoinSubResRsp
	(*callback.OrderShipmentRsp)(nil), // 59: callback.OrderShipmentRsp
}
var file_pb_xian_game_wzkz_game_game_proto_depIdxs = []int32{
	31, // 0: xian_wzkz.RegRsp.error:type_name -> xian_wzkz.Error
	29, // 1: xian_wzkz.RecordRsp.list:type_name -> xian_wzkz.RecordRsp.Record
	31, // 2: xian_wzkz.RecordRsp.error:type_name -> xian_wzkz.Error
	30, // 3: xian_wzkz.ExFireRecordRsp.list:type_name -> xian_wzkz.ExFireRecordRsp.Record
	31, // 4: xian_wzkz.ExFireRecordRsp.error:type_name -> xian_wzkz.Error
	9,  // 5: xian_wzkz.ConfigRsp.planes:type_name -> xian_wzkz.Plane
	31, // 6: xian_wzkz.MatchRsp.error:type_name -> xian_wzkz.Error
	14, // 7: xian_wzkz.UAsset.expires:type_name -> xian_wzkz.ExpireInfo
	15, // 8: xian_wzkz.AssetsRsp.assets:type_name -> xian_wzkz.UAsset
	31, // 9: xian_wzkz.AssetsRsp.error:type_name -> xian_wzkz.Error
	31, // 10: xian_wzkz.ExFireRsp.error:type_name -> xian_wzkz.Error
	31, // 11: xian_wzkz.NotifyRsp.error:type_name -> xian_wzkz.Error
	31, // 12: xian_wzkz.QuitRoomRsp.error:type_name -> xian_wzkz.Error
	15, // 13: xian_wzkz.RechargeFAssetRsp.assets:type_name -> xian_wzkz.UAsset
	31, // 14: xian_wzkz.RechargeFAssetRsp.error:type_name -> xian_wzkz.Error
	31, // 15: xian_wzkz.MatchVerRsp.error:type_name -> xian_wzkz.Error
	1,  // 16: xian_wzkz.RecordRsp.Record.gifts:type_name -> xian_wzkz.Gift
	2,  // 17: xian_wzkz.Game.Register:input_type -> xian_wzkz.RegReq
	8,  // 18: xian_wzkz.Game.Config:input_type -> xian_wzkz.ConfigReq
	11, // 19: xian_wzkz.Game.Match:input_type -> xian_wzkz.MatchReq
	4,  // 20: xian_wzkz.Game.Records:input_type -> xian_wzkz.RecordReq
	13, // 21: xian_wzkz.Game.Assets:input_type -> xian_wzkz.AssetsReq
	19, // 22: xian_wzkz.Game.Notify:input_type -> xian_wzkz.NotifyReq
	21, // 23: xian_wzkz.Game.Switch:input_type -> xian_wzkz.SwitchReq
	23, // 24: xian_wzkz.Game.QuitRoom:input_type -> xian_wzkz.QuitRoomReq
	25, // 25: xian_wzkz.Game.RechargeFAsset:input_type -> xian_wzkz.RechargeFAssetReq
	27, // 26: xian_wzkz.Game.MatchVer:input_type -> xian_wzkz.MatchVerReq
	32, // 27: xian_wzkz.Game.Nodes:input_type -> xian_wzkz_gm.NodesReq
	33, // 28: xian_wzkz.Game.NodeRooms:input_type -> xian_wzkz_gm.NodesRoomsReq
	34, // 29: xian_wzkz.Game.Kick:input_type -> xian_wzkz_gm.KickReq
	35, // 30: xian_wzkz.Game.Summary:input_type -> xian_wzkz_gm.SummaryReq
	36, // 31: xian_wzkz.Game.SyncOpt:input_type -> xian_wzkz.SyncOptReq
	37, // 32: xian_wzkz.Game.Ticket:input_type -> xian_wzkz.TicketReq
	38, // 33: xian_wzkz.Game.ExFTG:input_type -> xian_wzkz.ExFTGReq
	39, // 34: xian_wzkz.Game.ExFTGRecord:input_type -> xian_wzkz.ExFTGRecordReq
	40, // 35: xian_wzkz.Game.ExFTGList:input_type -> xian_wzkz.ExFTGListReq
	41, // 36: xian_wzkz.Game.ExCnt:input_type -> xian_wzkz.ExCntReq
	42, // 37: xian_wzkz.Game.ExCTFRecord:input_type -> xian_wzkz.ExCTFRecordReq
	43, // 38: xian_wzkz.Game.CoinSubPay:input_type -> xian_wzkz.CoinSubPayReq
	44, // 39: xian_wzkz.Game.CoinSubRes:input_type -> xian_wzkz.CoinSubResReq
	45, // 40: xian_wzkz.Game.CBCoinSubPay:input_type -> callback.OrderShipmentReq
	3,  // 41: xian_wzkz.Game.Register:output_type -> xian_wzkz.RegRsp
	10, // 42: xian_wzkz.Game.Config:output_type -> xian_wzkz.ConfigRsp
	12, // 43: xian_wzkz.Game.Match:output_type -> xian_wzkz.MatchRsp
	5,  // 44: xian_wzkz.Game.Records:output_type -> xian_wzkz.RecordRsp
	16, // 45: xian_wzkz.Game.Assets:output_type -> xian_wzkz.AssetsRsp
	20, // 46: xian_wzkz.Game.Notify:output_type -> xian_wzkz.NotifyRsp
	22, // 47: xian_wzkz.Game.Switch:output_type -> xian_wzkz.SwitchRsp
	24, // 48: xian_wzkz.Game.QuitRoom:output_type -> xian_wzkz.QuitRoomRsp
	26, // 49: xian_wzkz.Game.RechargeFAsset:output_type -> xian_wzkz.RechargeFAssetRsp
	28, // 50: xian_wzkz.Game.MatchVer:output_type -> xian_wzkz.MatchVerRsp
	46, // 51: xian_wzkz.Game.Nodes:output_type -> xian_wzkz_gm.NodesRsp
	47, // 52: xian_wzkz.Game.NodeRooms:output_type -> xian_wzkz_gm.NodesRoomsRsp
	48, // 53: xian_wzkz.Game.Kick:output_type -> xian_wzkz_gm.KickRsp
	49, // 54: xian_wzkz.Game.Summary:output_type -> xian_wzkz_gm.SummaryRsp
	50, // 55: xian_wzkz.Game.SyncOpt:output_type -> xian_wzkz.SyncOptRsp
	51, // 56: xian_wzkz.Game.Ticket:output_type -> xian_wzkz.TicketRsp
	52, // 57: xian_wzkz.Game.ExFTG:output_type -> xian_wzkz.ExFTGRsp
	53, // 58: xian_wzkz.Game.ExFTGRecord:output_type -> xian_wzkz.ExFTGRecordRsp
	54, // 59: xian_wzkz.Game.ExFTGList:output_type -> xian_wzkz.ExFTGListRsp
	55, // 60: xian_wzkz.Game.ExCnt:output_type -> xian_wzkz.ExCntRsp
	56, // 61: xian_wzkz.Game.ExCTFRecord:output_type -> xian_wzkz.ExCTFRecordRsp
	57, // 62: xian_wzkz.Game.CoinSubPay:output_type -> xian_wzkz.CoinSubPayRsp
	58, // 63: xian_wzkz.Game.CoinSubRes:output_type -> xian_wzkz.CoinSubResRsp
	59, // 64: xian_wzkz.Game.CBCoinSubPay:output_type -> callback.OrderShipmentRsp
	41, // [41:65] is the sub-list for method output_type
	17, // [17:41] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_pb_xian_game_wzkz_game_game_proto_init() }
func file_pb_xian_game_wzkz_game_game_proto_init() {
	if File_pb_xian_game_wzkz_game_game_proto != nil {
		return
	}
	file_pb_xian_game_wzkz_game_game_v2_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Gift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFireRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFireRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Plane); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFireReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFireRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuitRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuitRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeFAssetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RechargeFAssetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchVerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchVerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordRsp_Record); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_xian_game_wzkz_game_game_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExFireRecordRsp_Record); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_xian_game_wzkz_game_game_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_xian_game_wzkz_game_game_proto_goTypes,
		DependencyIndexes: file_pb_xian_game_wzkz_game_game_proto_depIdxs,
		EnumInfos:         file_pb_xian_game_wzkz_game_game_proto_enumTypes,
		MessageInfos:      file_pb_xian_game_wzkz_game_game_proto_msgTypes,
	}.Build()
	File_pb_xian_game_wzkz_game_game_proto = out.File
	file_pb_xian_game_wzkz_game_game_proto_rawDesc = nil
	file_pb_xian_game_wzkz_game_game_proto_goTypes = nil
	file_pb_xian_game_wzkz_game_game_proto_depIdxs = nil
}
