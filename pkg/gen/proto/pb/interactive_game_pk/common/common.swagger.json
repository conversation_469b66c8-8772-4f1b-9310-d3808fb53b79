{"swagger": "2.0", "info": {"title": "pb/interactive_game_pk/common/common.proto", "version": "version not set"}, "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}