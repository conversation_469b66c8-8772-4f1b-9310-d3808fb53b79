// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_pk/common/common.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventType int32

const (
	EventType_EventNone      EventType = 0
	EventType_EventBeginGame EventType = 1 // 开始游戏
	EventType_EventGameOver  EventType = 2 // 游戏结束
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0: "EventNone",
		1: "EventBeginGame",
		2: "EventGameOver",
	}
	EventType_value = map[string]int32{
		"EventNone":      0,
		"EventBeginGame": 1,
		"EventGameOver":  2,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_pk_common_common_proto_enumTypes[0].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_pk_common_common_proto_enumTypes[0]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_common_common_proto_rawDescGZIP(), []int{0}
}

type GameOverResultType int32

const (
	GameOverResultType_GameOverResultNone      GameOverResultType = 0
	GameOverResultType_GameOverResultSurrender GameOverResultType = 1 // 投降
)

// Enum value maps for GameOverResultType.
var (
	GameOverResultType_name = map[int32]string{
		0: "GameOverResultNone",
		1: "GameOverResultSurrender",
	}
	GameOverResultType_value = map[string]int32{
		"GameOverResultNone":      0,
		"GameOverResultSurrender": 1,
	}
)

func (x GameOverResultType) Enum() *GameOverResultType {
	p := new(GameOverResultType)
	*p = x
	return p
}

func (x GameOverResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameOverResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_pk_common_common_proto_enumTypes[1].Descriptor()
}

func (GameOverResultType) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_pk_common_common_proto_enumTypes[1]
}

func (x GameOverResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameOverResultType.Descriptor instead.
func (GameOverResultType) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_common_common_proto_rawDescGZIP(), []int{1}
}

type RoomEventGameOver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*RoomEventGameOver_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *RoomEventGameOver) Reset() {
	*x = RoomEventGameOver{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomEventGameOver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomEventGameOver) ProtoMessage() {}

func (x *RoomEventGameOver) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomEventGameOver.ProtoReflect.Descriptor instead.
func (*RoomEventGameOver) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_common_common_proto_rawDescGZIP(), []int{0}
}

func (x *RoomEventGameOver) GetResults() []*RoomEventGameOver_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type RoomEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      EventType           `protobuf:"varint,1,opt,name=type,proto3,enum=interactive_game_pk.common.EventType" json:"type,omitempty"`
	AppId     string              `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	RoundId   string              `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"`
	Timestamp int64               `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Players   []*RoomEvent_Player `protobuf:"bytes,5,rep,name=players,proto3" json:"players,omitempty"`
	// Types that are assignable to Data:
	//
	//	*RoomEvent_GameOver
	Data isRoomEvent_Data `protobuf_oneof:"data"`
}

func (x *RoomEvent) Reset() {
	*x = RoomEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomEvent) ProtoMessage() {}

func (x *RoomEvent) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomEvent.ProtoReflect.Descriptor instead.
func (*RoomEvent) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_common_common_proto_rawDescGZIP(), []int{1}
}

func (x *RoomEvent) GetType() EventType {
	if x != nil {
		return x.Type
	}
	return EventType_EventNone
}

func (x *RoomEvent) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RoomEvent) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *RoomEvent) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *RoomEvent) GetPlayers() []*RoomEvent_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (m *RoomEvent) GetData() isRoomEvent_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *RoomEvent) GetGameOver() *RoomEventGameOver {
	if x, ok := x.GetData().(*RoomEvent_GameOver); ok {
		return x.GameOver
	}
	return nil
}

type isRoomEvent_Data interface {
	isRoomEvent_Data()
}

type RoomEvent_GameOver struct {
	GameOver *RoomEventGameOver `protobuf:"bytes,6,opt,name=gameOver,proto3,oneof"`
}

func (*RoomEvent_GameOver) isRoomEvent_Data() {}

type RoomEventGameOver_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string             `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	RoomId     string             `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Rank       uint32             `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`                                                                // 排名 从 1 开始
	Score      int64              `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                                                              // 分数
	ResultType GameOverResultType `protobuf:"varint,5,opt,name=resultType,proto3,enum=interactive_game_pk.common.GameOverResultType" json:"resultType,omitempty"` //结束状态
}

func (x *RoomEventGameOver_Result) Reset() {
	*x = RoomEventGameOver_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomEventGameOver_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomEventGameOver_Result) ProtoMessage() {}

func (x *RoomEventGameOver_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomEventGameOver_Result.ProtoReflect.Descriptor instead.
func (*RoomEventGameOver_Result) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_common_common_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RoomEventGameOver_Result) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RoomEventGameOver_Result) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomEventGameOver_Result) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RoomEventGameOver_Result) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RoomEventGameOver_Result) GetResultType() GameOverResultType {
	if x != nil {
		return x.ResultType
	}
	return GameOverResultType_GameOverResultNone
}

type RoomEvent_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId      string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	RoomId      string `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`
	RoomGameURL string `protobuf:"bytes,3,opt,name=roomGameURL,proto3" json:"roomGameURL,omitempty"` // 房间游戏链接
}

func (x *RoomEvent_Player) Reset() {
	*x = RoomEvent_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomEvent_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomEvent_Player) ProtoMessage() {}

func (x *RoomEvent_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_common_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomEvent_Player.ProtoReflect.Descriptor instead.
func (*RoomEvent_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_common_common_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RoomEvent_Player) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RoomEvent_Player) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomEvent_Player) GetRoomGameURL() string {
	if x != nil {
		return x.RoomGameURL
	}
	return ""
}

var File_pb_interactive_game_pk_common_common_proto protoreflect.FileDescriptor

var file_pb_interactive_game_pk_common_common_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x98, 0x02, 0x0a, 0x11, 0x52, 0x6f, 0x6f,
	0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x12, 0x4e,
	0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f,
	0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0xb2,
	0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x8d, 0x03, 0x0a, 0x09, 0x52, 0x6f, 0x6f, 0x6d, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x46, 0x0a, 0x07, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x12, 0x4b, 0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x48, 0x00, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x1a,
	0x5a, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x6f, 0x6f,
	0x6d, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x52, 0x4c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x6f, 0x6f, 0x6d, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x52, 0x4c, 0x42, 0x06, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x2a, 0x41, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0d, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47, 0x61, 0x6d,
	0x65, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x47, 0x61, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x10, 0x02, 0x2a, 0x49, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12,
	0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x6f,
	0x6e, 0x65, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x10,
	0x01, 0x42, 0x52, 0x5a, 0x50, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_pk_common_common_proto_rawDescOnce sync.Once
	file_pb_interactive_game_pk_common_common_proto_rawDescData = file_pb_interactive_game_pk_common_common_proto_rawDesc
)

func file_pb_interactive_game_pk_common_common_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_pk_common_common_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_pk_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_pk_common_common_proto_rawDescData)
	})
	return file_pb_interactive_game_pk_common_common_proto_rawDescData
}

var file_pb_interactive_game_pk_common_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_interactive_game_pk_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_interactive_game_pk_common_common_proto_goTypes = []interface{}{
	(EventType)(0),                   // 0: interactive_game_pk.common.EventType
	(GameOverResultType)(0),          // 1: interactive_game_pk.common.GameOverResultType
	(*RoomEventGameOver)(nil),        // 2: interactive_game_pk.common.RoomEventGameOver
	(*RoomEvent)(nil),                // 3: interactive_game_pk.common.RoomEvent
	(*RoomEventGameOver_Result)(nil), // 4: interactive_game_pk.common.RoomEventGameOver.Result
	(*RoomEvent_Player)(nil),         // 5: interactive_game_pk.common.RoomEvent.Player
}
var file_pb_interactive_game_pk_common_common_proto_depIdxs = []int32{
	4, // 0: interactive_game_pk.common.RoomEventGameOver.results:type_name -> interactive_game_pk.common.RoomEventGameOver.Result
	0, // 1: interactive_game_pk.common.RoomEvent.type:type_name -> interactive_game_pk.common.EventType
	5, // 2: interactive_game_pk.common.RoomEvent.players:type_name -> interactive_game_pk.common.RoomEvent.Player
	2, // 3: interactive_game_pk.common.RoomEvent.gameOver:type_name -> interactive_game_pk.common.RoomEventGameOver
	1, // 4: interactive_game_pk.common.RoomEventGameOver.Result.resultType:type_name -> interactive_game_pk.common.GameOverResultType
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_pk_common_common_proto_init() }
func file_pb_interactive_game_pk_common_common_proto_init() {
	if File_pb_interactive_game_pk_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_pk_common_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomEventGameOver); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_common_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_common_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomEventGameOver_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_common_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomEvent_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pb_interactive_game_pk_common_common_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*RoomEvent_GameOver)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_pk_common_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_interactive_game_pk_common_common_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_pk_common_common_proto_depIdxs,
		EnumInfos:         file_pb_interactive_game_pk_common_common_proto_enumTypes,
		MessageInfos:      file_pb_interactive_game_pk_common_common_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_pk_common_common_proto = out.File
	file_pb_interactive_game_pk_common_common_proto_rawDesc = nil
	file_pb_interactive_game_pk_common_common_proto_goTypes = nil
	file_pb_interactive_game_pk_common_common_proto_depIdxs = nil
}
