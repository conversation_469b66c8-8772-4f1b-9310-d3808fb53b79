{"swagger": "2.0", "info": {"title": "pb/interactive_game_pk/web/web.proto", "version": "version not set"}, "tags": [{"name": "Game"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game_pk_web.Game/CloseGame": {"post": {"summary": "关闭游戏", "operationId": "Game_CloseGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_pk_webCloseGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_pk_webCloseGameReq"}}], "tags": ["Game"]}}}, "definitions": {"interactive_game_pk_webCloseGameReq": {"type": "object", "properties": {"roundId": {"type": "string", "title": "场次 id"}, "roomId": {"type": "string"}}}, "interactive_game_pk_webCloseGameRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}