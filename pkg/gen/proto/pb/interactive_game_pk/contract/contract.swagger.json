{"swagger": "2.0", "info": {"title": "pb/interactive_game_pk/contract/contract.proto", "version": "version not set"}, "tags": [{"name": "Contract"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game_pk.Contract/CloseGame": {"post": {"summary": "关闭游戏", "operationId": "Contract_CloseGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_pkCloseGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_pkCloseGameReq"}}], "tags": ["Contract"]}}, "/interactive_game_pk.Contract/GameOver": {"post": {"summary": "创建游戏 (游戏实现)\nrpc CreateGame(CreateGameReq) returns (CreateGameRsp);\n退出游戏 (游戏实现)\nrpc QuitGame(QuitGameReq) returns (QuitGameRsp);\n结束游戏", "operationId": "Contract_GameOver", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_pkGameOverRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_pkGameOverReq"}}], "tags": ["Contract"]}}}, "definitions": {"commonGameOverResultType": {"type": "string", "enum": ["GameOverResultNone", "GameOverResultSurrender"], "default": "GameOverResultNone", "title": "- GameOverResultSurrender: 投降"}, "interactive_game_pkCloseGameReq": {"type": "object", "properties": {"roundId": {"type": "string", "title": "场次 id"}, "openId": {"type": "string"}, "roomId": {"type": "string"}, "appId": {"type": "string"}}}, "interactive_game_pkCloseGameRsp": {"type": "object"}, "interactive_game_pkGameOverReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "roundId": {"type": "string", "title": "场次 id"}, "results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_game_pkGameOverReqResult"}, "title": "各玩家游戏结果"}}}, "interactive_game_pkGameOverReqResult": {"type": "object", "properties": {"openId": {"type": "string"}, "roomId": {"type": "string"}, "rank": {"type": "integer", "format": "int64", "title": "排名 从 1 开始"}, "score": {"type": "string", "format": "int64", "title": "分数"}, "resultType": {"$ref": "#/definitions/commonGameOverResultType", "title": "结束状态"}}}, "interactive_game_pkGameOverRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}