// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_pk/contract/contract.proto

package contract

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game_pk/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 根据 roundId 需要保证接口可重入
type CreateGameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string                  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`                                                                                             // 游戏 id
	RoundId string                  `protobuf:"bytes,2,opt,name=roundId,proto3" json:"roundId,omitempty"`                                                                                         // 场次 id (小于 128 字节)
	Players []*CreateGameReq_Player `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`                                                                                         // 玩家列表
	Configs map[string]string       `protobuf:"bytes,4,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 自定义配置
}

func (x *CreateGameReq) Reset() {
	*x = CreateGameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameReq) ProtoMessage() {}

func (x *CreateGameReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameReq.ProtoReflect.Descriptor instead.
func (*CreateGameReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{0}
}

func (x *CreateGameReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CreateGameReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *CreateGameReq) GetPlayers() []*CreateGameReq_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *CreateGameReq) GetConfigs() map[string]string {
	if x != nil {
		return x.Configs
	}
	return nil
}

type CreateGameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateGameRsp) Reset() {
	*x = CreateGameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameRsp) ProtoMessage() {}

func (x *CreateGameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameRsp.ProtoReflect.Descriptor instead.
func (*CreateGameRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{1}
}

type QuitGameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`     // appId
	RoundId string `protobuf:"bytes,2,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次 id
	OpenId  string `protobuf:"bytes,3,opt,name=openId,proto3" json:"openId,omitempty"`   // openId
	RoomId  string `protobuf:"bytes,4,opt,name=roomId,proto3" json:"roomId,omitempty"`   // 房间 id
}

func (x *QuitGameReq) Reset() {
	*x = QuitGameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuitGameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitGameReq) ProtoMessage() {}

func (x *QuitGameReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitGameReq.ProtoReflect.Descriptor instead.
func (*QuitGameReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{2}
}

func (x *QuitGameReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QuitGameReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *QuitGameReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QuitGameReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type QuitGameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QuitGameRsp) Reset() {
	*x = QuitGameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuitGameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitGameRsp) ProtoMessage() {}

func (x *QuitGameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitGameRsp.ProtoReflect.Descriptor instead.
func (*QuitGameRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{3}
}

type GameOverReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string                `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`     // appId
	RoundId string                `protobuf:"bytes,2,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次 id
	Results []*GameOverReq_Result `protobuf:"bytes,3,rep,name=results,proto3" json:"results,omitempty"` // 各玩家游戏结果
}

func (x *GameOverReq) Reset() {
	*x = GameOverReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameOverReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameOverReq) ProtoMessage() {}

func (x *GameOverReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameOverReq.ProtoReflect.Descriptor instead.
func (*GameOverReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{4}
}

func (x *GameOverReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GameOverReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *GameOverReq) GetResults() []*GameOverReq_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type GameOverRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GameOverRsp) Reset() {
	*x = GameOverRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameOverRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameOverRsp) ProtoMessage() {}

func (x *GameOverRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameOverRsp.ProtoReflect.Descriptor instead.
func (*GameOverRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{5}
}

type CloseGameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundId string `protobuf:"bytes,1,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次 id
	OpenId  string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	RoomId  string `protobuf:"bytes,3,opt,name=roomId,proto3" json:"roomId,omitempty"`
	AppId   string `protobuf:"bytes,4,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *CloseGameReq) Reset() {
	*x = CloseGameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseGameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseGameReq) ProtoMessage() {}

func (x *CloseGameReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseGameReq.ProtoReflect.Descriptor instead.
func (*CloseGameReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{6}
}

func (x *CloseGameReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *CloseGameReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CloseGameReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CloseGameReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type CloseGameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloseGameRsp) Reset() {
	*x = CloseGameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseGameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseGameRsp) ProtoMessage() {}

func (x *CloseGameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseGameRsp.ProtoReflect.Descriptor instead.
func (*CloseGameRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{7}
}

type CreateGameReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index  uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`  // 位置 从 1 开始
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"` // 房主 openId
	RoomId string `protobuf:"bytes,3,opt,name=roomId,proto3" json:"roomId,omitempty"` // 房间 id
}

func (x *CreateGameReq_Player) Reset() {
	*x = CreateGameReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGameReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGameReq_Player) ProtoMessage() {}

func (x *CreateGameReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGameReq_Player.ProtoReflect.Descriptor instead.
func (*CreateGameReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateGameReq_Player) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *CreateGameReq_Player) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CreateGameReq_Player) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GameOverReq_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string                    `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	RoomId     string                    `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Rank       uint32                    `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`                                                                // 排名 从 1 开始
	Score      int64                     `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                                                              // 分数
	ResultType common.GameOverResultType `protobuf:"varint,5,opt,name=resultType,proto3,enum=interactive_game_pk.common.GameOverResultType" json:"resultType,omitempty"` //结束状态
}

func (x *GameOverReq_Result) Reset() {
	*x = GameOverReq_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameOverReq_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameOverReq_Result) ProtoMessage() {}

func (x *GameOverReq_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_pk_contract_contract_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameOverReq_Result.ProtoReflect.Descriptor instead.
func (*GameOverReq_Result) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP(), []int{4, 0}
}

func (x *GameOverReq_Result) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GameOverReq_Result) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GameOverReq_Result) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *GameOverReq_Result) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *GameOverReq_Result) GetResultType() common.GameOverResultType {
	if x != nil {
		return x.ResultType
	}
	return common.GameOverResultType(0)
}

var File_pb_interactive_game_pk_contract_contract_proto protoreflect.FileDescriptor

var file_pb_interactive_game_pk_contract_contract_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x1a, 0x2a, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xdb, 0x02, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x1a, 0x4e, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x0f, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70,
	0x22, 0x6d, 0x0a, 0x0b, 0x51, 0x75, 0x69, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22,
	0x0d, 0x0a, 0x0b, 0x51, 0x75, 0x69, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0xb5,
	0x02, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x41,
	0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x1a, 0xb2, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x0d, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76,
	0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0x6e, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x61,
	0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x61,
	0x6d, 0x65, 0x52, 0x73, 0x70, 0x32, 0xad, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x12, 0x4e, 0x0a, 0x08, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x12, 0x20,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x1a, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x52,
	0x73, 0x70, 0x12, 0x51, 0x0a, 0x09, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6b, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x47, 0x61,
	0x6d, 0x65, 0x52, 0x73, 0x70, 0x42, 0x54, 0x5a, 0x52, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x6b, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_pk_contract_contract_proto_rawDescOnce sync.Once
	file_pb_interactive_game_pk_contract_contract_proto_rawDescData = file_pb_interactive_game_pk_contract_contract_proto_rawDesc
)

func file_pb_interactive_game_pk_contract_contract_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_pk_contract_contract_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_pk_contract_contract_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_pk_contract_contract_proto_rawDescData)
	})
	return file_pb_interactive_game_pk_contract_contract_proto_rawDescData
}

var file_pb_interactive_game_pk_contract_contract_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pb_interactive_game_pk_contract_contract_proto_goTypes = []interface{}{
	(*CreateGameReq)(nil),          // 0: interactive_game_pk.CreateGameReq
	(*CreateGameRsp)(nil),          // 1: interactive_game_pk.CreateGameRsp
	(*QuitGameReq)(nil),            // 2: interactive_game_pk.QuitGameReq
	(*QuitGameRsp)(nil),            // 3: interactive_game_pk.QuitGameRsp
	(*GameOverReq)(nil),            // 4: interactive_game_pk.GameOverReq
	(*GameOverRsp)(nil),            // 5: interactive_game_pk.GameOverRsp
	(*CloseGameReq)(nil),           // 6: interactive_game_pk.CloseGameReq
	(*CloseGameRsp)(nil),           // 7: interactive_game_pk.CloseGameRsp
	(*CreateGameReq_Player)(nil),   // 8: interactive_game_pk.CreateGameReq.Player
	nil,                            // 9: interactive_game_pk.CreateGameReq.ConfigsEntry
	(*GameOverReq_Result)(nil),     // 10: interactive_game_pk.GameOverReq.Result
	(common.GameOverResultType)(0), // 11: interactive_game_pk.common.GameOverResultType
}
var file_pb_interactive_game_pk_contract_contract_proto_depIdxs = []int32{
	8,  // 0: interactive_game_pk.CreateGameReq.players:type_name -> interactive_game_pk.CreateGameReq.Player
	9,  // 1: interactive_game_pk.CreateGameReq.configs:type_name -> interactive_game_pk.CreateGameReq.ConfigsEntry
	10, // 2: interactive_game_pk.GameOverReq.results:type_name -> interactive_game_pk.GameOverReq.Result
	11, // 3: interactive_game_pk.GameOverReq.Result.resultType:type_name -> interactive_game_pk.common.GameOverResultType
	4,  // 4: interactive_game_pk.Contract.GameOver:input_type -> interactive_game_pk.GameOverReq
	6,  // 5: interactive_game_pk.Contract.CloseGame:input_type -> interactive_game_pk.CloseGameReq
	5,  // 6: interactive_game_pk.Contract.GameOver:output_type -> interactive_game_pk.GameOverRsp
	7,  // 7: interactive_game_pk.Contract.CloseGame:output_type -> interactive_game_pk.CloseGameRsp
	6,  // [6:8] is the sub-list for method output_type
	4,  // [4:6] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_pk_contract_contract_proto_init() }
func file_pb_interactive_game_pk_contract_contract_proto_init() {
	if File_pb_interactive_game_pk_contract_contract_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuitGameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuitGameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameOverReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameOverRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseGameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseGameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGameReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_pk_contract_contract_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameOverReq_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_pk_contract_contract_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_pk_contract_contract_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_pk_contract_contract_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_pk_contract_contract_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_pk_contract_contract_proto = out.File
	file_pb_interactive_game_pk_contract_contract_proto_rawDesc = nil
	file_pb_interactive_game_pk_contract_contract_proto_goTypes = nil
	file_pb_interactive_game_pk_contract_contract_proto_depIdxs = nil
}
