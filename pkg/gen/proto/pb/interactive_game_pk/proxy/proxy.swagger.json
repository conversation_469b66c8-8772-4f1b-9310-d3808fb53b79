{"swagger": "2.0", "info": {"title": "pb/interactive_game_pk/proxy/proxy.proto", "version": "version not set"}, "tags": [{"name": "Game"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game_pk_proxy.Game/CreateGame": {"post": {"summary": "创建游戏", "operationId": "Game_CreateGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_pk_proxyCreateGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_pk_proxyCreateGameReq"}}], "tags": ["Game"]}}, "/interactive_game_pk_proxy.Game/QuitGame": {"post": {"summary": "退出游戏", "description": "结束游戏 (平台实现)\n rpc GameOver(GameOverReq) returns (GameOverRsp);", "operationId": "Game_QuitGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_pk_proxyQuitGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_pk_proxyQuitGameReq"}}], "tags": ["Game"]}}}, "definitions": {"CreateGameReqPlayer": {"type": "object", "properties": {"index": {"type": "integer", "format": "int64", "title": "位置 从 1 开始"}, "uid": {"type": "string", "format": "uint64", "title": "房主 openId"}, "roomId": {"type": "string", "title": "房间 id"}, "gameUrl": {"type": "string"}}}, "interactive_game_pk_proxyCreateGameReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏 id"}, "roundId": {"type": "string", "title": "场次 id (小于 128 字节)"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CreateGameReqPlayer"}, "title": "玩家列表"}, "configs": {"type": "object", "additionalProperties": {"type": "string"}, "title": "自定义配置"}}, "title": "根据 roundId 需要保证接口可重入"}, "interactive_game_pk_proxyCreateGameRsp": {"type": "object"}, "interactive_game_pk_proxyQuitGameReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "roundId": {"type": "string", "title": "场次 id"}, "uid": {"type": "string", "format": "uint64", "title": "uid"}, "roomId": {"type": "string", "title": "房间 id"}}}, "interactive_game_pk_proxyQuitGameRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}