{"swagger": "2.0", "info": {"title": "pb/cproxy/cproxy_prepare/proto_cproxy_prepare.proto", "version": "version not set"}, "tags": [{"name": "CproxyPrepare"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/kg.cproxy_prepare.CproxyPrepare/BroadcastAll": {"post": {"operationId": "CproxyPrepare_BroadcastAll", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cproxy_prepareBroadcastAllRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cproxy_prepareBroadcastAllReq"}}], "tags": ["CproxyPrepare"]}}, "/kg.cproxy_prepare.CproxyPrepare/BroadcastByRoomId": {"post": {"operationId": "CproxyPrepare_BroadcastByRoomId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cproxy_prepareBroadcastByRoomIdRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cproxy_prepareBroadcastByRoomIdReq"}}], "tags": ["CproxyPrepare"]}}, "/kg.cproxy_prepare.CproxyPrepare/Unicast": {"post": {"operationId": "CproxyPrepare_Unicast", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cproxy_prepareUnicastRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cproxy_prepareUnicastReq"}}], "tags": ["CproxyPrepare"]}}}, "definitions": {"cproxy_commHeader": {"type": "object", "properties": {"uMsgType": {"$ref": "#/definitions/cproxy_commMsgType", "title": "消息体类型"}, "strExt": {"type": "string", "title": "该服务对应的下游操作类型，由业务服务定义"}, "strRoomId": {"type": "string", "title": "房间ID"}, "strTraceId": {"type": "string", "title": "消息体ID，服务端透传至client，追踪链路消息送达情况"}, "uBodyType": {"type": "integer", "format": "int64", "title": "默认用二进制, BodyType"}, "lSeqId": {"type": "string", "format": "int64", "title": "消息序列号, TODO: 废弃字段信息"}, "iErrCode": {"type": "integer", "format": "int32", "title": "请求返回错误号"}, "strErrMsg": {"type": "string", "title": "请求返回错误信息"}, "strCmdService": {"type": "string", "title": "请求微服务名称"}, "strAppId": {"type": "string", "title": "应用ID"}, "lStartTime": {"type": "string", "format": "int64", "title": "消息到达长链接系统的时间"}, "strSeqId": {"type": "string", "title": "消息seqId"}, "bIsNotRsp": {"type": "boolean", "title": "是否不需要回包"}, "iPriority": {"type": "integer", "format": "int32", "title": "消息优先级, 值越高优先级越高"}, "strCmdServiceKey": {"type": "string", "title": "上行消息一致性哈希Key,可选"}}, "title": "Message Header;"}, "cproxy_commMessage": {"type": "object", "properties": {"stHeader": {"$ref": "#/definitions/cproxy_commHeader", "title": "消息头, 下游通过该结构体中的 strExt和 uMsgType 来确定如何解析 vctBody 字段"}, "vctBody": {"type": "string", "format": "byte", "title": "消息体"}}}, "cproxy_commMsgType": {"type": "string", "enum": ["MSGTYPE_INVALID", "MSGTYPE_UNICAST", "MSGTYPE_BROADCAST_ROOM", "MSGTYPE_BROADCAST_ALL", "MSGTYPE_RES", "MSGTYPE_META"], "default": "MSGTYPE_INVALID", "title": "- MSGTYPE_INVALID: 无效类型\n - MSGTYPE_UNICAST: 单播消息\n - MSGTYPE_BROADCAST_ROOM: 组播消息\n - MSGTYPE_BROADCAST_ALL: 全量广播消息\n - MSGTYPE_RES: websocket下游请求回包内容\n - MSGTYPE_META: 元数据(如心跳配置)"}, "cproxy_prepareBroadcastAllReq": {"type": "object", "properties": {"stMsg": {"$ref": "#/definitions/cproxy_commMessage"}}}, "cproxy_prepareBroadcastAllRsp": {"type": "object", "properties": {"iRet": {"type": "integer", "format": "int32"}, "strMsg": {"type": "string"}}}, "cproxy_prepareBroadcastByRoomIdReq": {"type": "object", "properties": {"stMsg": {"$ref": "#/definitions/cproxy_commMessage"}, "vecOpenId": {"type": "array", "items": {"type": "string"}, "title": "openId列表"}}}, "cproxy_prepareBroadcastByRoomIdRsp": {"type": "object", "properties": {"iRet": {"type": "integer", "format": "int32"}, "strMsg": {"type": "string"}}}, "cproxy_prepareUnicastReq": {"type": "object", "properties": {"strOpenId": {"type": "string"}, "stMsg": {"$ref": "#/definitions/cproxy_commMessage"}}}, "cproxy_prepareUnicastRsp": {"type": "object", "properties": {"iRet": {"type": "integer", "format": "int32"}, "strMsg": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}