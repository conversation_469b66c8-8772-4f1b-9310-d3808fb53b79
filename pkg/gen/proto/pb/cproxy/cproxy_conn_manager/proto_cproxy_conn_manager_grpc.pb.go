// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/cproxy/cproxy_conn_manager/proto_cproxy_conn_manager.proto

package cproxy_conn_manager

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CproxyConnMgr_Unicast_FullMethodName           = "/kg.cproxy_conn_manager.CproxyConnMgr/Unicast"
	CproxyConnMgr_BroadcastByRoomId_FullMethodName = "/kg.cproxy_conn_manager.CproxyConnMgr/BroadcastByRoomId"
	CproxyConnMgr_BroadcastAll_FullMethodName      = "/kg.cproxy_conn_manager.CproxyConnMgr/BroadcastAll"
	CproxyConnMgr_KickOff_FullMethodName           = "/kg.cproxy_conn_manager.CproxyConnMgr/KickOff"
	CproxyConnMgr_RunInfo_FullMethodName           = "/kg.cproxy_conn_manager.CproxyConnMgr/RunInfo"
)

// CproxyConnMgrClient is the client API for CproxyConnMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CproxyConnMgrClient interface {
	Unicast(ctx context.Context, in *UnicastReq, opts ...grpc.CallOption) (*UnicastRsp, error)
	BroadcastByRoomId(ctx context.Context, in *BroadcastByRoomIdReq, opts ...grpc.CallOption) (*BroadcastByRoomIdRsp, error)
	BroadcastAll(ctx context.Context, in *BroadcastAllReq, opts ...grpc.CallOption) (*BroadcastAllRsp, error)
	KickOff(ctx context.Context, in *KickOffReq, opts ...grpc.CallOption) (*KickOffRsp, error)
	RunInfo(ctx context.Context, in *RunInfoReq, opts ...grpc.CallOption) (*RunInfoRsp, error)
}

type cproxyConnMgrClient struct {
	cc grpc.ClientConnInterface
}

func NewCproxyConnMgrClient(cc grpc.ClientConnInterface) CproxyConnMgrClient {
	return &cproxyConnMgrClient{cc}
}

func (c *cproxyConnMgrClient) Unicast(ctx context.Context, in *UnicastReq, opts ...grpc.CallOption) (*UnicastRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UnicastRsp)
	err := c.cc.Invoke(ctx, CproxyConnMgr_Unicast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cproxyConnMgrClient) BroadcastByRoomId(ctx context.Context, in *BroadcastByRoomIdReq, opts ...grpc.CallOption) (*BroadcastByRoomIdRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BroadcastByRoomIdRsp)
	err := c.cc.Invoke(ctx, CproxyConnMgr_BroadcastByRoomId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cproxyConnMgrClient) BroadcastAll(ctx context.Context, in *BroadcastAllReq, opts ...grpc.CallOption) (*BroadcastAllRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BroadcastAllRsp)
	err := c.cc.Invoke(ctx, CproxyConnMgr_BroadcastAll_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cproxyConnMgrClient) KickOff(ctx context.Context, in *KickOffReq, opts ...grpc.CallOption) (*KickOffRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KickOffRsp)
	err := c.cc.Invoke(ctx, CproxyConnMgr_KickOff_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cproxyConnMgrClient) RunInfo(ctx context.Context, in *RunInfoReq, opts ...grpc.CallOption) (*RunInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RunInfoRsp)
	err := c.cc.Invoke(ctx, CproxyConnMgr_RunInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CproxyConnMgrServer is the server API for CproxyConnMgr service.
// All implementations should embed UnimplementedCproxyConnMgrServer
// for forward compatibility
type CproxyConnMgrServer interface {
	Unicast(context.Context, *UnicastReq) (*UnicastRsp, error)
	BroadcastByRoomId(context.Context, *BroadcastByRoomIdReq) (*BroadcastByRoomIdRsp, error)
	BroadcastAll(context.Context, *BroadcastAllReq) (*BroadcastAllRsp, error)
	KickOff(context.Context, *KickOffReq) (*KickOffRsp, error)
	RunInfo(context.Context, *RunInfoReq) (*RunInfoRsp, error)
}

// UnimplementedCproxyConnMgrServer should be embedded to have forward compatible implementations.
type UnimplementedCproxyConnMgrServer struct {
}

func (UnimplementedCproxyConnMgrServer) Unicast(context.Context, *UnicastReq) (*UnicastRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Unicast not implemented")
}
func (UnimplementedCproxyConnMgrServer) BroadcastByRoomId(context.Context, *BroadcastByRoomIdReq) (*BroadcastByRoomIdRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BroadcastByRoomId not implemented")
}
func (UnimplementedCproxyConnMgrServer) BroadcastAll(context.Context, *BroadcastAllReq) (*BroadcastAllRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BroadcastAll not implemented")
}
func (UnimplementedCproxyConnMgrServer) KickOff(context.Context, *KickOffReq) (*KickOffRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickOff not implemented")
}
func (UnimplementedCproxyConnMgrServer) RunInfo(context.Context, *RunInfoReq) (*RunInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunInfo not implemented")
}

// UnsafeCproxyConnMgrServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CproxyConnMgrServer will
// result in compilation errors.
type UnsafeCproxyConnMgrServer interface {
	mustEmbedUnimplementedCproxyConnMgrServer()
}

func RegisterCproxyConnMgrServer(s grpc.ServiceRegistrar, srv CproxyConnMgrServer) {
	s.RegisterService(&CproxyConnMgr_ServiceDesc, srv)
}

func _CproxyConnMgr_Unicast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnicastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CproxyConnMgrServer).Unicast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CproxyConnMgr_Unicast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CproxyConnMgrServer).Unicast(ctx, req.(*UnicastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CproxyConnMgr_BroadcastByRoomId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastByRoomIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CproxyConnMgrServer).BroadcastByRoomId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CproxyConnMgr_BroadcastByRoomId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CproxyConnMgrServer).BroadcastByRoomId(ctx, req.(*BroadcastByRoomIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CproxyConnMgr_BroadcastAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BroadcastAllReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CproxyConnMgrServer).BroadcastAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CproxyConnMgr_BroadcastAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CproxyConnMgrServer).BroadcastAll(ctx, req.(*BroadcastAllReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CproxyConnMgr_KickOff_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickOffReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CproxyConnMgrServer).KickOff(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CproxyConnMgr_KickOff_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CproxyConnMgrServer).KickOff(ctx, req.(*KickOffReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CproxyConnMgr_RunInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CproxyConnMgrServer).RunInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CproxyConnMgr_RunInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CproxyConnMgrServer).RunInfo(ctx, req.(*RunInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CproxyConnMgr_ServiceDesc is the grpc.ServiceDesc for CproxyConnMgr service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CproxyConnMgr_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "kg.cproxy_conn_manager.CproxyConnMgr",
	HandlerType: (*CproxyConnMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Unicast",
			Handler:    _CproxyConnMgr_Unicast_Handler,
		},
		{
			MethodName: "BroadcastByRoomId",
			Handler:    _CproxyConnMgr_BroadcastByRoomId_Handler,
		},
		{
			MethodName: "BroadcastAll",
			Handler:    _CproxyConnMgr_BroadcastAll_Handler,
		},
		{
			MethodName: "KickOff",
			Handler:    _CproxyConnMgr_KickOff_Handler,
		},
		{
			MethodName: "RunInfo",
			Handler:    _CproxyConnMgr_RunInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/cproxy/cproxy_conn_manager/proto_cproxy_conn_manager.proto",
}
