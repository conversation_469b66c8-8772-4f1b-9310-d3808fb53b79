// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/coindozer/coin_dozer/coin_dozer.proto

package coin_dozer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	CoinDozer_ListCoinDozer_FullMethodName     = "/rte.service.coin_dozer.CoinDozer/ListCoinDozer"
	CoinDozer_StartCoinDozer_FullMethodName    = "/rte.service.coin_dozer.CoinDozer/StartCoinDozer"
	CoinDozer_EndCoinDozer_FullMethodName      = "/rte.service.coin_dozer.CoinDozer/EndCoinDozer"
	CoinDozer_InsertCoin_FullMethodName        = "/rte.service.coin_dozer.CoinDozer/InsertCoin"
	CoinDozer_AddGoods_FullMethodName          = "/rte.service.coin_dozer.CoinDozer/AddGoods"
	CoinDozer_GetCoinDozerState_FullMethodName = "/rte.service.coin_dozer.CoinDozer/GetCoinDozerState"
	CoinDozer_SendBizMessage_FullMethodName    = "/rte.service.coin_dozer.CoinDozer/SendBizMessage"
	CoinDozer_TryClose_FullMethodName          = "/rte.service.coin_dozer.CoinDozer/TryClose"
)

// CoinDozerClient is the client API for CoinDozer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CoinDozerClient interface {
	// 获取推币机列表
	ListCoinDozer(ctx context.Context, in *ListCoinDozerReq, opts ...grpc.CallOption) (*ListCoinDozerRsp, error)
	// 开始游戏
	StartCoinDozer(ctx context.Context, in *StartCoinDozerReq, opts ...grpc.CallOption) (*StartCoinDozerRsp, error)
	// 结束游戏
	EndCoinDozer(ctx context.Context, in *EndCoinDozerReq, opts ...grpc.CallOption) (*EndCoinDozerRsp, error)
	// 投币
	InsertCoin(ctx context.Context, in *InsertCoinReq, opts ...grpc.CallOption) (*InsertCoinRsp, error)
	// 添加物品
	AddGoods(ctx context.Context, in *AddGoodsReq, opts ...grpc.CallOption) (*AddGoodsRsp, error)
	// 设备状态
	GetCoinDozerState(ctx context.Context, in *GetCoinDozerStateReq, opts ...grpc.CallOption) (*GetCoinDozerStateRsp, error)
	// 通用协议(业务方调用node)
	SendBizMessage(ctx context.Context, in *SendBizMessageReq, opts ...grpc.CallOption) (*SendBizMessageRsp, error)
	// TryClose
	TryClose(ctx context.Context, in *TryCloseReq, opts ...grpc.CallOption) (*TryCloseRsp, error)
}

type coinDozerClient struct {
	cc grpc.ClientConnInterface
}

func NewCoinDozerClient(cc grpc.ClientConnInterface) CoinDozerClient {
	return &coinDozerClient{cc}
}

func (c *coinDozerClient) ListCoinDozer(ctx context.Context, in *ListCoinDozerReq, opts ...grpc.CallOption) (*ListCoinDozerRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCoinDozerRsp)
	err := c.cc.Invoke(ctx, CoinDozer_ListCoinDozer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) StartCoinDozer(ctx context.Context, in *StartCoinDozerReq, opts ...grpc.CallOption) (*StartCoinDozerRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartCoinDozerRsp)
	err := c.cc.Invoke(ctx, CoinDozer_StartCoinDozer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) EndCoinDozer(ctx context.Context, in *EndCoinDozerReq, opts ...grpc.CallOption) (*EndCoinDozerRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EndCoinDozerRsp)
	err := c.cc.Invoke(ctx, CoinDozer_EndCoinDozer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) InsertCoin(ctx context.Context, in *InsertCoinReq, opts ...grpc.CallOption) (*InsertCoinRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InsertCoinRsp)
	err := c.cc.Invoke(ctx, CoinDozer_InsertCoin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) AddGoods(ctx context.Context, in *AddGoodsReq, opts ...grpc.CallOption) (*AddGoodsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddGoodsRsp)
	err := c.cc.Invoke(ctx, CoinDozer_AddGoods_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) GetCoinDozerState(ctx context.Context, in *GetCoinDozerStateReq, opts ...grpc.CallOption) (*GetCoinDozerStateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCoinDozerStateRsp)
	err := c.cc.Invoke(ctx, CoinDozer_GetCoinDozerState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) SendBizMessage(ctx context.Context, in *SendBizMessageReq, opts ...grpc.CallOption) (*SendBizMessageRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendBizMessageRsp)
	err := c.cc.Invoke(ctx, CoinDozer_SendBizMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *coinDozerClient) TryClose(ctx context.Context, in *TryCloseReq, opts ...grpc.CallOption) (*TryCloseRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TryCloseRsp)
	err := c.cc.Invoke(ctx, CoinDozer_TryClose_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CoinDozerServer is the server API for CoinDozer service.
// All implementations should embed UnimplementedCoinDozerServer
// for forward compatibility
type CoinDozerServer interface {
	// 获取推币机列表
	ListCoinDozer(context.Context, *ListCoinDozerReq) (*ListCoinDozerRsp, error)
	// 开始游戏
	StartCoinDozer(context.Context, *StartCoinDozerReq) (*StartCoinDozerRsp, error)
	// 结束游戏
	EndCoinDozer(context.Context, *EndCoinDozerReq) (*EndCoinDozerRsp, error)
	// 投币
	InsertCoin(context.Context, *InsertCoinReq) (*InsertCoinRsp, error)
	// 添加物品
	AddGoods(context.Context, *AddGoodsReq) (*AddGoodsRsp, error)
	// 设备状态
	GetCoinDozerState(context.Context, *GetCoinDozerStateReq) (*GetCoinDozerStateRsp, error)
	// 通用协议(业务方调用node)
	SendBizMessage(context.Context, *SendBizMessageReq) (*SendBizMessageRsp, error)
	// TryClose
	TryClose(context.Context, *TryCloseReq) (*TryCloseRsp, error)
}

// UnimplementedCoinDozerServer should be embedded to have forward compatible implementations.
type UnimplementedCoinDozerServer struct {
}

func (UnimplementedCoinDozerServer) ListCoinDozer(context.Context, *ListCoinDozerReq) (*ListCoinDozerRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCoinDozer not implemented")
}
func (UnimplementedCoinDozerServer) StartCoinDozer(context.Context, *StartCoinDozerReq) (*StartCoinDozerRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCoinDozer not implemented")
}
func (UnimplementedCoinDozerServer) EndCoinDozer(context.Context, *EndCoinDozerReq) (*EndCoinDozerRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EndCoinDozer not implemented")
}
func (UnimplementedCoinDozerServer) InsertCoin(context.Context, *InsertCoinReq) (*InsertCoinRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertCoin not implemented")
}
func (UnimplementedCoinDozerServer) AddGoods(context.Context, *AddGoodsReq) (*AddGoodsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGoods not implemented")
}
func (UnimplementedCoinDozerServer) GetCoinDozerState(context.Context, *GetCoinDozerStateReq) (*GetCoinDozerStateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCoinDozerState not implemented")
}
func (UnimplementedCoinDozerServer) SendBizMessage(context.Context, *SendBizMessageReq) (*SendBizMessageRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendBizMessage not implemented")
}
func (UnimplementedCoinDozerServer) TryClose(context.Context, *TryCloseReq) (*TryCloseRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TryClose not implemented")
}

// UnsafeCoinDozerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CoinDozerServer will
// result in compilation errors.
type UnsafeCoinDozerServer interface {
	mustEmbedUnimplementedCoinDozerServer()
}

func RegisterCoinDozerServer(s grpc.ServiceRegistrar, srv CoinDozerServer) {
	s.RegisterService(&CoinDozer_ServiceDesc, srv)
}

func _CoinDozer_ListCoinDozer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCoinDozerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).ListCoinDozer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_ListCoinDozer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).ListCoinDozer(ctx, req.(*ListCoinDozerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_StartCoinDozer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCoinDozerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).StartCoinDozer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_StartCoinDozer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).StartCoinDozer(ctx, req.(*StartCoinDozerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_EndCoinDozer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EndCoinDozerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).EndCoinDozer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_EndCoinDozer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).EndCoinDozer(ctx, req.(*EndCoinDozerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_InsertCoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertCoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).InsertCoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_InsertCoin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).InsertCoin(ctx, req.(*InsertCoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_AddGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGoodsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).AddGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_AddGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).AddGoods(ctx, req.(*AddGoodsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_GetCoinDozerState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoinDozerStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).GetCoinDozerState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_GetCoinDozerState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).GetCoinDozerState(ctx, req.(*GetCoinDozerStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_SendBizMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendBizMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).SendBizMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_SendBizMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).SendBizMessage(ctx, req.(*SendBizMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CoinDozer_TryClose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TryCloseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CoinDozerServer).TryClose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CoinDozer_TryClose_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CoinDozerServer).TryClose(ctx, req.(*TryCloseReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CoinDozer_ServiceDesc is the grpc.ServiceDesc for CoinDozer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CoinDozer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "rte.service.coin_dozer.CoinDozer",
	HandlerType: (*CoinDozerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCoinDozer",
			Handler:    _CoinDozer_ListCoinDozer_Handler,
		},
		{
			MethodName: "StartCoinDozer",
			Handler:    _CoinDozer_StartCoinDozer_Handler,
		},
		{
			MethodName: "EndCoinDozer",
			Handler:    _CoinDozer_EndCoinDozer_Handler,
		},
		{
			MethodName: "InsertCoin",
			Handler:    _CoinDozer_InsertCoin_Handler,
		},
		{
			MethodName: "AddGoods",
			Handler:    _CoinDozer_AddGoods_Handler,
		},
		{
			MethodName: "GetCoinDozerState",
			Handler:    _CoinDozer_GetCoinDozerState_Handler,
		},
		{
			MethodName: "SendBizMessage",
			Handler:    _CoinDozer_SendBizMessage_Handler,
		},
		{
			MethodName: "TryClose",
			Handler:    _CoinDozer_TryClose_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/coindozer/coin_dozer/coin_dozer.proto",
}
