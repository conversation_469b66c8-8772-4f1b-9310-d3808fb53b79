// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/coindozer/coin_dozer_callback/coin_dozer_callback.proto

/*
Package coin_dozer_callback is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package coin_dozer_callback

import (
	"context"
	"io"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_CoinDozerCallback_CallBackPushCoin_0(ctx context.Context, marshaler runtime.Marshaler, client CoinDozerCallbackClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq PushCoinReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.CallBackPushCoin(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CoinDozerCallback_CallBackPushCoin_0(ctx context.Context, marshaler runtime.Marshaler, server CoinDozerCallbackServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq PushCoinReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.CallBackPushCoin(ctx, &protoReq)
	return msg, metadata, err

}

func request_CoinDozerCallback_SendNodeMessage_0(ctx context.Context, marshaler runtime.Marshaler, client CoinDozerCallbackClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SendNodeMessageReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.SendNodeMessage(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_CoinDozerCallback_SendNodeMessage_0(ctx context.Context, marshaler runtime.Marshaler, server CoinDozerCallbackServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SendNodeMessageReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.SendNodeMessage(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterCoinDozerCallbackHandlerServer registers the http handlers for service CoinDozerCallback to "mux".
// UnaryRPC     :call CoinDozerCallbackServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterCoinDozerCallbackHandlerFromEndpoint instead.
func RegisterCoinDozerCallbackHandlerServer(ctx context.Context, mux *runtime.ServeMux, server CoinDozerCallbackServer) error {

	mux.Handle("POST", pattern_CoinDozerCallback_CallBackPushCoin_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/rte.service.coin_dozer_callback.CoinDozerCallback/CallBackPushCoin", runtime.WithHTTPPathPattern("/rte.service.coin_dozer_callback.CoinDozerCallback/CallBackPushCoin"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CoinDozerCallback_CallBackPushCoin_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CoinDozerCallback_CallBackPushCoin_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CoinDozerCallback_SendNodeMessage_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/rte.service.coin_dozer_callback.CoinDozerCallback/SendNodeMessage", runtime.WithHTTPPathPattern("/rte.service.coin_dozer_callback.CoinDozerCallback/SendNodeMessage"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_CoinDozerCallback_SendNodeMessage_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CoinDozerCallback_SendNodeMessage_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterCoinDozerCallbackHandlerFromEndpoint is same as RegisterCoinDozerCallbackHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterCoinDozerCallbackHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterCoinDozerCallbackHandler(ctx, mux, conn)
}

// RegisterCoinDozerCallbackHandler registers the http handlers for service CoinDozerCallback to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterCoinDozerCallbackHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterCoinDozerCallbackHandlerClient(ctx, mux, NewCoinDozerCallbackClient(conn))
}

// RegisterCoinDozerCallbackHandlerClient registers the http handlers for service CoinDozerCallback
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "CoinDozerCallbackClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "CoinDozerCallbackClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "CoinDozerCallbackClient" to call the correct interceptors.
func RegisterCoinDozerCallbackHandlerClient(ctx context.Context, mux *runtime.ServeMux, client CoinDozerCallbackClient) error {

	mux.Handle("POST", pattern_CoinDozerCallback_CallBackPushCoin_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/rte.service.coin_dozer_callback.CoinDozerCallback/CallBackPushCoin", runtime.WithHTTPPathPattern("/rte.service.coin_dozer_callback.CoinDozerCallback/CallBackPushCoin"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CoinDozerCallback_CallBackPushCoin_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CoinDozerCallback_CallBackPushCoin_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_CoinDozerCallback_SendNodeMessage_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/rte.service.coin_dozer_callback.CoinDozerCallback/SendNodeMessage", runtime.WithHTTPPathPattern("/rte.service.coin_dozer_callback.CoinDozerCallback/SendNodeMessage"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_CoinDozerCallback_SendNodeMessage_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_CoinDozerCallback_SendNodeMessage_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_CoinDozerCallback_CallBackPushCoin_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"rte.service.coin_dozer_callback.CoinDozerCallback", "CallBackPushCoin"}, ""))

	pattern_CoinDozerCallback_SendNodeMessage_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"rte.service.coin_dozer_callback.CoinDozerCallback", "SendNodeMessage"}, ""))
)

var (
	forward_CoinDozerCallback_CallBackPushCoin_0 = runtime.ForwardResponseMessage

	forward_CoinDozerCallback_SendNodeMessage_0 = runtime.ForwardResponseMessage
)
