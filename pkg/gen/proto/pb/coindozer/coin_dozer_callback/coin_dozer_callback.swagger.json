{"swagger": "2.0", "info": {"title": "pb/coindozer/coin_dozer_callback/coin_dozer_callback.proto", "version": "version not set"}, "tags": [{"name": "CoinDozerCallback"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/rte.service.coin_dozer_callback.CoinDozerCallback/CallBackPushCoin": {"post": {"summary": "推币(掉币)", "operationId": "CoinDozerCallback_CallBackPushCoin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/coin_dozer_callbackPushCoinRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/coin_dozer_callbackPushCoinReq"}}], "tags": ["CoinDozerCallback"]}}, "/rte.service.coin_dozer_callback.CoinDozerCallback/SendNodeMessage": {"post": {"summary": "通用协议(node调用业务方)", "operationId": "CoinDozerCallback_SendNodeMessage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/coin_dozer_callbackSendNodeMessageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/coin_dozer_callbackSendNodeMessageReq"}}], "tags": ["CoinDozerCallback"]}}}, "definitions": {"coin_dozerGoodsInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "id"}, "num": {"type": "integer", "format": "int64", "title": "数量"}}, "title": "物品信息"}, "coin_dozerMessageInfo": {"type": "object", "properties": {"machineId": {"type": "string", "title": "推币机ID"}, "cmd": {"type": "string", "title": "协议ID"}, "data": {"type": "string", "title": "数据"}}}, "coin_dozer_callbackPushCoinReq": {"type": "object", "properties": {"method": {"type": "string", "title": "回调类型，固定值. 默认值：coin_dozer_push"}, "machineId": {"type": "string", "title": "推币机ID"}, "gameId": {"type": "string", "title": "游戏ID"}, "idempotent": {"type": "string", "title": "幂等id"}, "num": {"type": "integer", "format": "int64", "title": "推币掉落金币数量"}, "goodsInfos": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/coin_dozerGoodsInfo"}, "title": "物品"}}}, "coin_dozer_callbackPushCoinRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64", "title": "0代表接口请求成功，其余失败"}, "msg": {"type": "string"}}}, "coin_dozer_callbackSendNodeMessageReq": {"type": "object", "properties": {"messageInfo": {"$ref": "#/definitions/coin_dozerMessageInfo"}}}, "coin_dozer_callbackSendNodeMessageRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "msg": {"type": "string"}, "messageInfo": {"$ref": "#/definitions/coin_dozerMessageInfo"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}