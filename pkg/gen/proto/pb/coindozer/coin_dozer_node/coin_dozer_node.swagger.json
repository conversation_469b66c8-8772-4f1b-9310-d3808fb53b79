{"swagger": "2.0", "info": {"title": "pb/coindozer/coin_dozer_node/coin_dozer_node.proto", "version": "version not set"}, "tags": [{"name": "CoinDozerNode"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/rte.service.coin_dozer_node.CoinDozerNode/HeartBeat": {"post": {"summary": "推币机心跳", "operationId": "CoinDozerNode_HeartBeat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/coin_dozer_nodeHeartBeatRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/coin_dozer_nodeHeartBeatReq"}}], "tags": ["CoinDozerNode"]}}, "/rte.service.coin_dozer_node.CoinDozerNode/PushCoin": {"post": {"summary": "推币(掉币)", "operationId": "CoinDozerNode_PushCoin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/coin_dozer_nodePushCoinRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/coin_dozer_nodePushCoinReq"}}], "tags": ["CoinDozerNode"]}}, "/rte.service.coin_dozer_node.CoinDozerNode/SendNodeMessage": {"post": {"summary": "通用协议(node调用业务方)", "operationId": "CoinDozerNode_SendNodeMessage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/coin_dozer_nodeSendNodeMessageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/coin_dozer_nodeSendNodeMessageReq"}}], "tags": ["CoinDozerNode"]}}}, "definitions": {"coin_dozerGoodsInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "id"}, "num": {"type": "integer", "format": "int64", "title": "数量"}}, "title": "物品信息"}, "coin_dozerMessageInfo": {"type": "object", "properties": {"machineId": {"type": "string", "title": "推币机ID"}, "cmd": {"type": "string", "title": "协议ID"}, "data": {"type": "string", "title": "数据"}}}, "coin_dozer_nodeHeartBeatReq": {"type": "object", "properties": {"machineId": {"type": "string", "title": "推币机ID"}, "num": {"type": "integer", "format": "int64", "title": "当前金币数量"}, "goodsInfos": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/coin_dozerGoodsInfo"}, "title": "当前物品列表"}}}, "coin_dozer_nodeHeartBeatRsp": {"type": "object"}, "coin_dozer_nodePushCoinReq": {"type": "object", "properties": {"machineId": {"type": "string", "title": "推币机ID"}, "idempotent": {"type": "string", "title": "幂等id"}, "result": {"$ref": "#/definitions/coin_dozer_nodePushCoinResult", "title": "推币信息"}, "blackHoleResult": {"$ref": "#/definitions/coin_dozer_nodePushCoinResult", "title": "黑洞推币信息"}}}, "coin_dozer_nodePushCoinResult": {"type": "object", "properties": {"num": {"type": "integer", "format": "int64", "title": "推币数量"}, "goodsInfos": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/coin_dozerGoodsInfo"}, "title": "物品"}}, "title": "推币结果"}, "coin_dozer_nodePushCoinRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64", "title": "0成功，其他失败，直接将msg展示给用户"}, "msg": {"type": "string"}}}, "coin_dozer_nodeSendNodeMessageReq": {"type": "object", "properties": {"messageInfo": {"$ref": "#/definitions/coin_dozerMessageInfo"}}}, "coin_dozer_nodeSendNodeMessageRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "msg": {"type": "string"}, "messageInfo": {"$ref": "#/definitions/coin_dozerMessageInfo"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}