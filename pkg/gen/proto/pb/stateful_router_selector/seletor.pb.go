// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/stateful_router_selector/seletor.proto

package stateful_router_selector

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SelectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`     // appid
	RoomId  string `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`   // roomId
	RoundId string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // roundid
}

func (x *SelectReq) Reset() {
	*x = SelectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_selector_seletor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectReq) ProtoMessage() {}

func (x *SelectReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_selector_seletor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectReq.ProtoReflect.Descriptor instead.
func (*SelectReq) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_selector_seletor_proto_rawDescGZIP(), []int{0}
}

func (x *SelectReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SelectReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SelectReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type SelectRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip    string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`       // ip和label必有其一
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"` // ip和label必有其一
	Port  int32  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`  // 端口
}

func (x *SelectRsp) Reset() {
	*x = SelectRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_stateful_router_selector_seletor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectRsp) ProtoMessage() {}

func (x *SelectRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_stateful_router_selector_seletor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectRsp.ProtoReflect.Descriptor instead.
func (*SelectRsp) Descriptor() ([]byte, []int) {
	return file_pb_stateful_router_selector_seletor_proto_rawDescGZIP(), []int{1}
}

func (x *SelectRsp) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SelectRsp) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *SelectRsp) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

var File_pb_stateful_router_selector_seletor_proto protoreflect.FileDescriptor

var file_pb_stateful_router_selector_seletor_proto_rawDesc = []byte{
	0x0a, 0x29, 0x70, 0x62, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x73, 0x65,
	0x6c, 0x65, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x22, 0x53, 0x0a, 0x09,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x22, 0x45, 0x0a, 0x09, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x73, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x32, 0x5a, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x66, 0x75, 0x6c, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x40, 0x0a, 0x06, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x1a, 0x2e, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x73, 0x70, 0x42, 0x50, 0x5a, 0x4e, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_stateful_router_selector_seletor_proto_rawDescOnce sync.Once
	file_pb_stateful_router_selector_seletor_proto_rawDescData = file_pb_stateful_router_selector_seletor_proto_rawDesc
)

func file_pb_stateful_router_selector_seletor_proto_rawDescGZIP() []byte {
	file_pb_stateful_router_selector_seletor_proto_rawDescOnce.Do(func() {
		file_pb_stateful_router_selector_seletor_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_stateful_router_selector_seletor_proto_rawDescData)
	})
	return file_pb_stateful_router_selector_seletor_proto_rawDescData
}

var file_pb_stateful_router_selector_seletor_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_stateful_router_selector_seletor_proto_goTypes = []interface{}{
	(*SelectReq)(nil), // 0: stateful_router.SelectReq
	(*SelectRsp)(nil), // 1: stateful_router.SelectRsp
}
var file_pb_stateful_router_selector_seletor_proto_depIdxs = []int32{
	0, // 0: stateful_router.StatefulRouterSelector.Select:input_type -> stateful_router.SelectReq
	1, // 1: stateful_router.StatefulRouterSelector.Select:output_type -> stateful_router.SelectRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_stateful_router_selector_seletor_proto_init() }
func file_pb_stateful_router_selector_seletor_proto_init() {
	if File_pb_stateful_router_selector_seletor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_stateful_router_selector_seletor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_stateful_router_selector_seletor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_stateful_router_selector_seletor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_stateful_router_selector_seletor_proto_goTypes,
		DependencyIndexes: file_pb_stateful_router_selector_seletor_proto_depIdxs,
		MessageInfos:      file_pb_stateful_router_selector_seletor_proto_msgTypes,
	}.Build()
	File_pb_stateful_router_selector_seletor_proto = out.File
	file_pb_stateful_router_selector_seletor_proto_rawDesc = nil
	file_pb_stateful_router_selector_seletor_proto_goTypes = nil
	file_pb_stateful_router_selector_seletor_proto_depIdxs = nil
}
