{"swagger": "2.0", "info": {"title": "pb/stateful_router_selector/seletor.proto", "version": "version not set"}, "tags": [{"name": "StatefulRouterSelector"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/stateful_router.StatefulRouterSelector/Select": {"post": {"operationId": "StatefulRouterSelector_Select", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stateful_routerSelectRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stateful_routerSelectReq"}}], "tags": ["StatefulRouterSelector"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "stateful_routerSelectReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appid"}, "roomId": {"type": "string", "title": "roomId"}, "roundId": {"type": "string", "title": "roundid"}}}, "stateful_routerSelectRsp": {"type": "object", "properties": {"ip": {"type": "string", "title": "ip和label必有其一"}, "label": {"type": "string", "title": "ip和label必有其一"}, "port": {"type": "integer", "format": "int32", "title": "端口"}}}}}