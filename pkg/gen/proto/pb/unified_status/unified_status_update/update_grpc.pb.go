// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/unified_status/unified_status_update/update.proto

package unified_status_update

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	UnifiedStatusUpdateApi_UpdateUnifiedStatus_FullMethodName = "/unified_status_update.UnifiedStatusUpdateApi/UpdateUnifiedStatus"
)

// UnifiedStatusUpdateApiClient is the client API for UnifiedStatusUpdateApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UnifiedStatusUpdateApiClient interface {
	// 更新状态
	UpdateUnifiedStatus(ctx context.Context, in *UpdateReq, opts ...grpc.CallOption) (*UpdateRsp, error)
}

type unifiedStatusUpdateApiClient struct {
	cc grpc.ClientConnInterface
}

func NewUnifiedStatusUpdateApiClient(cc grpc.ClientConnInterface) UnifiedStatusUpdateApiClient {
	return &unifiedStatusUpdateApiClient{cc}
}

func (c *unifiedStatusUpdateApiClient) UpdateUnifiedStatus(ctx context.Context, in *UpdateReq, opts ...grpc.CallOption) (*UpdateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRsp)
	err := c.cc.Invoke(ctx, UnifiedStatusUpdateApi_UpdateUnifiedStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UnifiedStatusUpdateApiServer is the server API for UnifiedStatusUpdateApi service.
// All implementations should embed UnimplementedUnifiedStatusUpdateApiServer
// for forward compatibility
type UnifiedStatusUpdateApiServer interface {
	// 更新状态
	UpdateUnifiedStatus(context.Context, *UpdateReq) (*UpdateRsp, error)
}

// UnimplementedUnifiedStatusUpdateApiServer should be embedded to have forward compatible implementations.
type UnimplementedUnifiedStatusUpdateApiServer struct {
}

func (UnimplementedUnifiedStatusUpdateApiServer) UpdateUnifiedStatus(context.Context, *UpdateReq) (*UpdateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUnifiedStatus not implemented")
}

// UnsafeUnifiedStatusUpdateApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UnifiedStatusUpdateApiServer will
// result in compilation errors.
type UnsafeUnifiedStatusUpdateApiServer interface {
	mustEmbedUnimplementedUnifiedStatusUpdateApiServer()
}

func RegisterUnifiedStatusUpdateApiServer(s grpc.ServiceRegistrar, srv UnifiedStatusUpdateApiServer) {
	s.RegisterService(&UnifiedStatusUpdateApi_ServiceDesc, srv)
}

func _UnifiedStatusUpdateApi_UpdateUnifiedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedStatusUpdateApiServer).UpdateUnifiedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UnifiedStatusUpdateApi_UpdateUnifiedStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedStatusUpdateApiServer).UpdateUnifiedStatus(ctx, req.(*UpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UnifiedStatusUpdateApi_ServiceDesc is the grpc.ServiceDesc for UnifiedStatusUpdateApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UnifiedStatusUpdateApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "unified_status_update.UnifiedStatusUpdateApi",
	HandlerType: (*UnifiedStatusUpdateApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateUnifiedStatus",
			Handler:    _UnifiedStatusUpdateApi_UpdateUnifiedStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/unified_status/unified_status_update/update.proto",
}
