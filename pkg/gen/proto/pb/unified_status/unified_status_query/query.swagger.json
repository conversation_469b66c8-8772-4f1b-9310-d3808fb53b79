{"swagger": "2.0", "info": {"title": "pb/unified_status/unified_status_query/query.proto", "version": "version not set"}, "tags": [{"name": "UnifiedStatusQueryApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/unified_status_query.UnifiedStatusQueryApi/QueryUnifiedStatus": {"post": {"summary": "查询状态", "operationId": "UnifiedStatusQueryApi_QueryUnifiedStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/unified_status_queryQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/unified_status_queryQueryReq"}}], "tags": ["UnifiedStatusQueryApi"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "unified_status_commonCommRet": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "title": "错误码 see EnumErrorCode"}, "msg": {"type": "string", "title": "错误提示语"}}}, "unified_status_commonUserUnifiedStatus": {"type": "object", "properties": {"uid": {"type": "string", "format": "int64"}, "status": {"type": "integer", "format": "int64"}}}, "unified_status_queryQueryReq": {"type": "object", "properties": {"strAppid": {"type": "string", "title": "业务方标志"}, "uidList": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "uid列表"}}}, "unified_status_queryQueryRsp": {"type": "object", "properties": {"Ret": {"$ref": "#/definitions/unified_status_commonCommRet"}, "userUnifiedStatusList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/unified_status_commonUserUnifiedStatus"}, "title": "uid状态列表"}}}}}