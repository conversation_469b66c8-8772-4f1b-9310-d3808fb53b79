{"swagger": "2.0", "info": {"title": "pb/openapi/openapi.proto", "version": "version not set"}, "tags": [{"name": "Openapi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/openapi/assetList": {"post": {"summary": "查询资产列表", "operationId": "Openapi_AssetList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameAssetListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameAssetListReq"}}], "tags": ["Openapi"]}}, "/openapi/assetRecordList": {"post": {"summary": "查询资产记录", "operationId": "Openapi_AssetRecordList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameAssetRecordListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameAssetRecordListReq"}}], "tags": ["Openapi"]}}, "/openapi/cancelSubAsset": {"post": {"summary": "取消扣减资产", "operationId": "Openapi_CancelSubAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameCancelSubAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameCancelSubAssetReq"}}], "tags": ["Openapi"]}}, "/openapi/customGameDelivery": {"post": {"summary": "游戏发货", "operationId": "Openapi_CustomGameDelivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/componentgameGameDeliveryPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/componentgameGameDeliveryPackageReq"}}], "tags": ["Openapi"]}}, "/openapi/delivery": {"post": {"summary": "发货", "operationId": "Openapi_Delivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameDeliveryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameDeliveryReq"}}], "tags": ["Openapi"]}}, "/openapi/orderQuery": {"post": {"summary": "查询订单", "operationId": "Openapi_OrderQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOpenOrderQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOpenOrderQueryReq"}}], "tags": ["Openapi"]}}, "/openapi/orderSuccNotify": {"post": {"summary": "确认扣费成功", "operationId": "Openapi_OrderSuccNotify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameOrderSuccNotifyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameOrderSuccNotifyReq"}}], "tags": ["Openapi"]}}, "/openapi/queryHaveUserPlayed": {"post": {"summary": "查询用户是否玩过", "operationId": "Openapi_QueryHaveUserPlayed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBatchQueryHaveUserPlayedRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBatchQueryHaveUserPlayedReq"}}], "tags": ["Openapi"]}}, "/openapi/subAsset": {"post": {"summary": "扣减资产", "operationId": "Openapi_SubAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSubAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSubAssetReq"}}], "tags": ["Openapi"]}}}, "definitions": {"componentgameGameDeliveryPackageReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "packageId": {"type": "string", "title": "礼包 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}}}, "componentgameGameDeliveryPackageRsp": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_apiRewardItem"}, "title": "奖励"}}}, "gameAssetAddOption": {"type": "object", "properties": {"limit": {"type": "string", "format": "int64", "title": "增加上限, 不指定着不限制, 否则最多只能加到limit封顶"}}}, "gameAssetFromType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>", "AssetFromFree", "AssetFromPay"], "default": "<PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON>", "title": "- AssetFromDefault: 默认, 会被重置为来自免费兑换\n - AssetFromFree: 来自免费兑换\n - AssetFromPay: 来自K币购买"}, "gameAssetListReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产 ID 不填拉所有"}, "requireExpireInfo": {"type": "boolean", "title": "是否需要过期信息"}, "requireEmptyAsset": {"type": "boolean", "title": "是否需要数目为0的资产"}}}, "gameAssetListRsp": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameAssetRecord": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64"}, "assetNum": {"type": "string", "format": "int64"}, "action": {"type": "string"}, "timestamp": {"type": "string", "format": "int64"}, "reason": {"type": "string", "title": "透传reason，业务可自行使用"}}}, "gameAssetRecordListReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产 ID 不填拉所有"}, "cursor": {"type": "string", "title": "透传字段 第一次传空"}, "pageSize": {"type": "string", "format": "int64", "title": "页大小 默认 20"}}}, "gameAssetRecordListRsp": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameAssetRecord"}}, "cursor": {"type": "string"}, "next": {"type": "boolean", "title": "是否有下一页"}}}, "gameBatchQueryHaveUserPlayedReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uids": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "请求列表"}}}, "gameBatchQueryHaveUserPlayedRsp": {"type": "object", "properties": {"uids": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "玩过的列表"}}}, "gameCancelSubAssetReq": {"type": "object", "properties": {"appId": {"type": "string"}, "transactionId": {"type": "string"}}}, "gameCancelSubAssetRsp": {"type": "object"}, "gameDeliveryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAssetChange"}, "title": "增加资产列表"}, "transactionId": {"type": "string", "title": "唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX"}, "reason": {"type": "string", "title": "操作原因"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位秒"}, "from": {"$ref": "#/definitions/gameAssetFromType", "title": "指明来自免费(兑换),还是付费(购买),不填默认走付费,指针对游戏币有效"}, "traceType": {"type": "string", "format": "uint64", "title": "具体定义见asset.proto中的AssetTraceType,会打上链路标记,不明确不要填这个"}}}, "gameDeliveryRsp": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameExpireInfo": {"type": "object", "properties": {"assetNum": {"type": "string", "format": "int64", "title": "资产数量"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间戳 单位秒"}}}, "gameOpenOrderQueryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "orderId": {"type": "string"}}}, "gameOpenOrderQueryRsp": {"type": "object", "properties": {"status": {"type": "integer", "format": "int32"}}}, "gameOrderSuccNotifyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "transactionId": {"type": "string", "title": "唯一 ID 透传pay接口中的"}, "reason": {"type": "string", "title": "操作原因"}}}, "gameOrderSuccNotifyRsp": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameSubAssetReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAssetChange"}, "title": "扣减资产列表"}, "transactionId": {"type": "string", "title": "唯一 ID 以日期开头 例如: 20211201-XXXXXXXXXXXXXXX"}, "reason": {"type": "string", "title": "操作原因"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位秒"}}}, "gameSubAssetRsp": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameUserAsset"}, "title": "结果资产列表"}}}, "gameUserAsset": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量(付费:来自K币购买 + 免费:来自兑换)"}, "expires": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExpireInfo"}, "title": "过期信息"}, "name": {"type": "string", "title": "资产名称"}, "icon": {"type": "string", "title": "资产图标"}, "assetNumFromFree": {"type": "string", "format": "int64", "title": "资产数量(免费:来自兑换)"}, "currencyType": {"type": "integer", "format": "int64", "title": "资产类型"}}}, "gameUserAssetChange": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量(免费+付费)"}, "freeNum": {"type": "string", "format": "int64", "title": "资产数量(免费),只读"}, "expireTime": {"type": "string", "format": "int64", "title": "过期时间 单位秒"}, "addOpetion": {"$ref": "#/definitions/gameAssetAddOption", "title": "只对增加资产有效,可选"}}}, "game_apiRewardItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "奖励id"}, "num": {"type": "integer", "format": "int64", "title": "奖励数量 限时道具表示分钟数"}, "name": {"type": "string", "title": "奖励名称"}, "type": {"$ref": "#/definitions/game_apiRewardItemType", "title": "资产类型"}, "img": {"type": "string", "title": "图片"}, "desc": {"type": "string", "title": "描述问题呢"}}}, "game_apiRewardItemType": {"type": "string", "enum": ["Reward_From_GameNormal", "Reward_From_Platform", "Reward_From_GameLimitedTime"], "default": "Reward_From_GameNormal", "title": "- Reward_From_GameNormal: 游戏物品-默认\n - Reward_From_Platform: 平台物品\n - Reward_From_GameLimitedTime: 游戏物品-限时"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}