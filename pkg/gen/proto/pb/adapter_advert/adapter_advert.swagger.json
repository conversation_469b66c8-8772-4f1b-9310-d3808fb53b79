{"swagger": "2.0", "info": {"title": "pb/adapter_advert/adapter_advert.proto", "version": "version not set"}, "tags": [{"name": "AdapterAdvert"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/adapter_advert.AdapterAdvert/AdvertCheck": {"post": {"summary": "广告曝光校验", "operationId": "AdapterAdvert_AdvertCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_advertAdvertCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_advertAdvertCheckReq"}}], "tags": ["AdapterAdvert"]}}, "/adapter_advert.AdapterAdvert/AdvertInfo": {"post": {"summary": "获取广告信息", "operationId": "AdapterAdvert_AdvertInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_advertAdvertInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_advertAdvertInfoReq"}}], "tags": ["AdapterAdvert"]}}, "/adapter_advert.AdapterAdvert/AdvertReceiveReward": {"post": {"summary": "领取广告奖励", "operationId": "AdapterAdvert_AdvertReceiveReward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_advertAdvertReceiveRewardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_advertAdvertReceiveRewardReq"}}], "tags": ["AdapterAdvert"]}}}, "definitions": {"adapter_advertAdCheckMask": {"type": "string", "enum": ["AdCheckMaskNone", "AdCheckMaskDownloadActive"], "default": "AdCheckMaskNone", "title": "- AdCheckMaskNone: 只需校验主广告曝光\n - AdCheckMaskDownloadActive: 需要校验广告点击激活是否通过"}, "adapter_advertAdvertCheckReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "adToken": {"type": "string", "title": "广告曝光token"}, "adPosId": {"type": "string", "title": "广告位id"}, "qimei36": {"type": "string"}, "sceneId": {"type": "string", "title": "场景id(可选)"}, "adCheckMask": {"$ref": "#/definitions/adapter_advertAdCheckMask"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "自定义透传"}}}, "adapter_advertAdvertCheckRsp": {"type": "object", "properties": {"traceId": {"type": "string", "title": "广告曝光唯一id"}, "result": {"type": "integer", "format": "int32", "title": "曝光校验结果，0是校验通过"}, "rewardNum": {"type": "string", "format": "uint64", "title": "广告ecpm数值奖励数量"}, "EncodedEcpm": {"type": "string", "title": "ecpm加密值"}, "ecpmCoin": {"type": "integer", "format": "int64", "title": "ecpm换算成金币数"}}}, "adapter_advertAdvertInfo": {"type": "object", "properties": {"incentiveType": {"$ref": "#/definitions/adapter_advertIncentiveType", "title": "广告奖励数值类型"}, "rewardNum": {"type": "string", "format": "uint64", "title": "后台计算出的金币值"}, "showAdvert": {"type": "boolean", "title": "是否展示广告"}, "leftAdvert": {"type": "integer", "format": "int64", "title": "剩余广告次数(仅用于前端展示次数，是否能看广告用showAdvert字段判断)"}}}, "adapter_advertAdvertInfoReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "advertSceneList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_advertAdvertScene"}, "title": "广告场景"}}}, "adapter_advertAdvertInfoRsp": {"type": "object", "properties": {"advertInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/adapter_advertAdvertInfo"}, "title": "广告信息"}}}, "adapter_advertAdvertReceiveRewardReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "adToken": {"type": "string", "title": "广告曝光token"}, "adPosId": {"type": "string", "title": "广告位id"}, "qimei36": {"type": "string"}, "sceneId": {"type": "string", "title": "场景id(可选)"}}}, "adapter_advertAdvertReceiveRewardRsp": {"type": "object", "properties": {"traceId": {"type": "string", "title": "广告曝光唯一id"}, "result": {"type": "integer", "format": "int32", "title": "领取结果，0是成功"}, "rewardNum": {"type": "string", "format": "uint64", "title": "广告ecpm数值奖励数量"}}}, "adapter_advertAdvertScene": {"type": "object", "properties": {"adPosId": {"type": "string", "title": "广告位id"}, "sceneId": {"type": "string", "title": "场景id(可选)"}}}, "adapter_advertIncentiveType": {"type": "string", "enum": ["IncentiveTypeNone", "IncentiveTypeBI", "IncentiveTypeECPM"], "default": "IncentiveTypeNone", "title": "- IncentiveTypeNone: 非法类型\n - IncentiveTypeBI: 使用后台下发的激励广告奖励数值\n - IncentiveTypeECPM: 使用商广返回的激励广告奖励数值（基于ecpm）"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}