// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/adapter_advert/adapter_advert.proto

package adapter_advert

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AdapterAdvert_AdvertCheck_FullMethodName         = "/adapter_advert.AdapterAdvert/AdvertCheck"
	AdapterAdvert_AdvertInfo_FullMethodName          = "/adapter_advert.AdapterAdvert/AdvertInfo"
	AdapterAdvert_AdvertReceiveReward_FullMethodName = "/adapter_advert.AdapterAdvert/AdvertReceiveReward"
)

// AdapterAdvertClient is the client API for AdapterAdvert service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterAdvertClient interface {
	// 广告曝光校验
	AdvertCheck(ctx context.Context, in *AdvertCheckReq, opts ...grpc.CallOption) (*AdvertCheckRsp, error)
	// 获取广告信息
	AdvertInfo(ctx context.Context, in *AdvertInfoReq, opts ...grpc.CallOption) (*AdvertInfoRsp, error)
	// 领取广告奖励
	AdvertReceiveReward(ctx context.Context, in *AdvertReceiveRewardReq, opts ...grpc.CallOption) (*AdvertReceiveRewardRsp, error)
}

type adapterAdvertClient struct {
	cc grpc.ClientConnInterface
}

func NewAdapterAdvertClient(cc grpc.ClientConnInterface) AdapterAdvertClient {
	return &adapterAdvertClient{cc}
}

func (c *adapterAdvertClient) AdvertCheck(ctx context.Context, in *AdvertCheckReq, opts ...grpc.CallOption) (*AdvertCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdvertCheckRsp)
	err := c.cc.Invoke(ctx, AdapterAdvert_AdvertCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterAdvertClient) AdvertInfo(ctx context.Context, in *AdvertInfoReq, opts ...grpc.CallOption) (*AdvertInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdvertInfoRsp)
	err := c.cc.Invoke(ctx, AdapterAdvert_AdvertInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterAdvertClient) AdvertReceiveReward(ctx context.Context, in *AdvertReceiveRewardReq, opts ...grpc.CallOption) (*AdvertReceiveRewardRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdvertReceiveRewardRsp)
	err := c.cc.Invoke(ctx, AdapterAdvert_AdvertReceiveReward_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterAdvertServer is the server API for AdapterAdvert service.
// All implementations should embed UnimplementedAdapterAdvertServer
// for forward compatibility
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterAdvertServer interface {
	// 广告曝光校验
	AdvertCheck(context.Context, *AdvertCheckReq) (*AdvertCheckRsp, error)
	// 获取广告信息
	AdvertInfo(context.Context, *AdvertInfoReq) (*AdvertInfoRsp, error)
	// 领取广告奖励
	AdvertReceiveReward(context.Context, *AdvertReceiveRewardReq) (*AdvertReceiveRewardRsp, error)
}

// UnimplementedAdapterAdvertServer should be embedded to have forward compatible implementations.
type UnimplementedAdapterAdvertServer struct {
}

func (UnimplementedAdapterAdvertServer) AdvertCheck(context.Context, *AdvertCheckReq) (*AdvertCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdvertCheck not implemented")
}
func (UnimplementedAdapterAdvertServer) AdvertInfo(context.Context, *AdvertInfoReq) (*AdvertInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdvertInfo not implemented")
}
func (UnimplementedAdapterAdvertServer) AdvertReceiveReward(context.Context, *AdvertReceiveRewardReq) (*AdvertReceiveRewardRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdvertReceiveReward not implemented")
}

// UnsafeAdapterAdvertServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdapterAdvertServer will
// result in compilation errors.
type UnsafeAdapterAdvertServer interface {
	mustEmbedUnimplementedAdapterAdvertServer()
}

func RegisterAdapterAdvertServer(s grpc.ServiceRegistrar, srv AdapterAdvertServer) {
	s.RegisterService(&AdapterAdvert_ServiceDesc, srv)
}

func _AdapterAdvert_AdvertCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdvertCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterAdvertServer).AdvertCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterAdvert_AdvertCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterAdvertServer).AdvertCheck(ctx, req.(*AdvertCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterAdvert_AdvertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdvertInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterAdvertServer).AdvertInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterAdvert_AdvertInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterAdvertServer).AdvertInfo(ctx, req.(*AdvertInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterAdvert_AdvertReceiveReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdvertReceiveRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterAdvertServer).AdvertReceiveReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterAdvert_AdvertReceiveReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterAdvertServer).AdvertReceiveReward(ctx, req.(*AdvertReceiveRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdapterAdvert_ServiceDesc is the grpc.ServiceDesc for AdapterAdvert service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdapterAdvert_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "adapter_advert.AdapterAdvert",
	HandlerType: (*AdapterAdvertServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AdvertCheck",
			Handler:    _AdapterAdvert_AdvertCheck_Handler,
		},
		{
			MethodName: "AdvertInfo",
			Handler:    _AdapterAdvert_AdvertInfo_Handler,
		},
		{
			MethodName: "AdvertReceiveReward",
			Handler:    _AdapterAdvert_AdvertReceiveReward_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/adapter_advert/adapter_advert.proto",
}
