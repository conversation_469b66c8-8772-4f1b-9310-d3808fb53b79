// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter/adapter.proto

package adapter

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	abtest "kugou_adapter_service/pkg/gen/proto/pb/abtest"
	asset "kugou_adapter_service/pkg/gen/proto/pb/asset"
	event "kugou_adapter_service/pkg/gen/proto/pb/event"
	openpay "kugou_adapter_service/pkg/gen/proto/pb/openpay"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Gender int32

const (
	Gender_GenderUnknown Gender = 0
	Gender_GenderMan     Gender = 1 // 男
	Gender_GenderWoman   Gender = 2 // 女
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "GenderUnknown",
		1: "GenderMan",
		2: "GenderWoman",
	}
	Gender_value = map[string]int32{
		"GenderUnknown": 0,
		"GenderMan":     1,
		"GenderWoman":   2,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[0].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[0]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{0}
}

type RoomMsgType int32

const (
	RoomMsgType_RoomMsgUnknown RoomMsgType = 0
	RoomMsgType_RoomMsgText    RoomMsgType = 1 // 文本消息
)

// Enum value maps for RoomMsgType.
var (
	RoomMsgType_name = map[int32]string{
		0: "RoomMsgUnknown",
		1: "RoomMsgText",
	}
	RoomMsgType_value = map[string]int32{
		"RoomMsgUnknown": 0,
		"RoomMsgText":    1,
	}
)

func (x RoomMsgType) Enum() *RoomMsgType {
	p := new(RoomMsgType)
	*p = x
	return p
}

func (x RoomMsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoomMsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[1].Descriptor()
}

func (RoomMsgType) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[1]
}

func (x RoomMsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoomMsgType.Descriptor instead.
func (RoomMsgType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{1}
}

type PlatAssetType int32

const (
	PlatAssetType_PLAT_BALANCE PlatAssetType = 0 //默认查平台货币余额
	PlatAssetType_KG_FLOWER    PlatAssetType = 1
)

// Enum value maps for PlatAssetType.
var (
	PlatAssetType_name = map[int32]string{
		0: "PLAT_BALANCE",
		1: "KG_FLOWER",
	}
	PlatAssetType_value = map[string]int32{
		"PLAT_BALANCE": 0,
		"KG_FLOWER":    1,
	}
)

func (x PlatAssetType) Enum() *PlatAssetType {
	p := new(PlatAssetType)
	*p = x
	return p
}

func (x PlatAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlatAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[2].Descriptor()
}

func (PlatAssetType) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[2]
}

func (x PlatAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlatAssetType.Descriptor instead.
func (PlatAssetType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{2}
}

type FollowOptType int32

const (
	FollowOptType_FOLLOW_TYPE_NONE FollowOptType = 0
	FollowOptType_FOLLOW_TYPE_ADD  FollowOptType = 1
	FollowOptType_FOLLOW_TYPE_SUB  FollowOptType = 2
)

// Enum value maps for FollowOptType.
var (
	FollowOptType_name = map[int32]string{
		0: "FOLLOW_TYPE_NONE",
		1: "FOLLOW_TYPE_ADD",
		2: "FOLLOW_TYPE_SUB",
	}
	FollowOptType_value = map[string]int32{
		"FOLLOW_TYPE_NONE": 0,
		"FOLLOW_TYPE_ADD":  1,
		"FOLLOW_TYPE_SUB":  2,
	}
)

func (x FollowOptType) Enum() *FollowOptType {
	p := new(FollowOptType)
	*p = x
	return p
}

func (x FollowOptType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FollowOptType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[3].Descriptor()
}

func (FollowOptType) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[3]
}

func (x FollowOptType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FollowOptType.Descriptor instead.
func (FollowOptType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{3}
}

type FollowResult int32

const (
	FollowResult_FOLLOW_TYPE_SUCC FollowResult = 0
	FollowResult_FOLLOW_TYPE_FAIL FollowResult = 1
)

// Enum value maps for FollowResult.
var (
	FollowResult_name = map[int32]string{
		0: "FOLLOW_TYPE_SUCC",
		1: "FOLLOW_TYPE_FAIL",
	}
	FollowResult_value = map[string]int32{
		"FOLLOW_TYPE_SUCC": 0,
		"FOLLOW_TYPE_FAIL": 1,
	}
)

func (x FollowResult) Enum() *FollowResult {
	p := new(FollowResult)
	*p = x
	return p
}

func (x FollowResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FollowResult) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[4].Descriptor()
}

func (FollowResult) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[4]
}

func (x FollowResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FollowResult.Descriptor instead.
func (FollowResult) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{4}
}

type RelatioMask int32

const (
	RelatioMask_RELATION_MASK_ALL    RelatioMask = 0 //所有关系
	RelatioMask_RELATION_MASK_FOLLOW RelatioMask = 1 //拉取关注
)

// Enum value maps for RelatioMask.
var (
	RelatioMask_name = map[int32]string{
		0: "RELATION_MASK_ALL",
		1: "RELATION_MASK_FOLLOW",
	}
	RelatioMask_value = map[string]int32{
		"RELATION_MASK_ALL":    0,
		"RELATION_MASK_FOLLOW": 1,
	}
)

func (x RelatioMask) Enum() *RelatioMask {
	p := new(RelatioMask)
	*p = x
	return p
}

func (x RelatioMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelatioMask) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[5].Descriptor()
}

func (RelatioMask) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[5]
}

func (x RelatioMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelatioMask.Descriptor instead.
func (RelatioMask) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{5}
}

type RelationType int32

const (
	RelationType_RELATION_NONE   RelationType = 0 //无关系
	RelationType_RELATION_FOLLOW RelationType = 1 //关注
)

// Enum value maps for RelationType.
var (
	RelationType_name = map[int32]string{
		0: "RELATION_NONE",
		1: "RELATION_FOLLOW",
	}
	RelationType_value = map[string]int32{
		"RELATION_NONE":   0,
		"RELATION_FOLLOW": 1,
	}
)

func (x RelationType) Enum() *RelationType {
	p := new(RelationType)
	*p = x
	return p
}

func (x RelationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[6].Descriptor()
}

func (RelationType) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[6]
}

func (x RelationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationType.Descriptor instead.
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{6}
}

type GameCheckType int32

const (
	GameCheckType_CheckTypeNone   GameCheckType = 0 // 无
	GameCheckType_CheckTypeCreate GameCheckType = 1 // 互动游戏创建房间
	GameCheckType_CheckTypeJoin   GameCheckType = 2 // 互动游戏加入房间
)

// Enum value maps for GameCheckType.
var (
	GameCheckType_name = map[int32]string{
		0: "CheckTypeNone",
		1: "CheckTypeCreate",
		2: "CheckTypeJoin",
	}
	GameCheckType_value = map[string]int32{
		"CheckTypeNone":   0,
		"CheckTypeCreate": 1,
		"CheckTypeJoin":   2,
	}
)

func (x GameCheckType) Enum() *GameCheckType {
	p := new(GameCheckType)
	*p = x
	return p
}

func (x GameCheckType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameCheckType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[7].Descriptor()
}

func (GameCheckType) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[7]
}

func (x GameCheckType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameCheckType.Descriptor instead.
func (GameCheckType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{7}
}

type AdultType int32

const (
	AdultType_AdultTypeUnknown AdultType = 0 // 未知
	AdultType_AdultTypeFalse   AdultType = 1 // 未成年
	AdultType_AudltTypeTrue    AdultType = 2 // 已成年
)

// Enum value maps for AdultType.
var (
	AdultType_name = map[int32]string{
		0: "AdultTypeUnknown",
		1: "AdultTypeFalse",
		2: "AudltTypeTrue",
	}
	AdultType_value = map[string]int32{
		"AdultTypeUnknown": 0,
		"AdultTypeFalse":   1,
		"AudltTypeTrue":    2,
	}
)

func (x AdultType) Enum() *AdultType {
	p := new(AdultType)
	*p = x
	return p
}

func (x AdultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdultType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_adapter_adapter_proto_enumTypes[8].Descriptor()
}

func (AdultType) Type() protoreflect.EnumType {
	return &file_pb_adapter_adapter_proto_enumTypes[8]
}

func (x AdultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdultType.Descriptor instead.
func (AdultType) EnumDescriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{8}
}

type GetBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *GetBalanceReq) Reset() {
	*x = GetBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceReq) ProtoMessage() {}

func (x *GetBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceReq.ProtoReflect.Descriptor instead.
func (*GetBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{0}
}

func (x *GetBalanceReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetBalanceReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type GetBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 余额
	Balance uint32 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
	// 赠币余额，joox有用到
	FreeCurrencyBalance uint32 `protobuf:"varint,2,opt,name=free_currency_balance,json=freeCurrencyBalance,proto3" json:"free_currency_balance,omitempty"`
}

func (x *GetBalanceRsp) Reset() {
	*x = GetBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceRsp) ProtoMessage() {}

func (x *GetBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceRsp.ProtoReflect.Descriptor instead.
func (*GetBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{1}
}

func (x *GetBalanceRsp) GetBalance() uint32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetBalanceRsp) GetFreeCurrencyBalance() uint32 {
	if x != nil {
		return x.FreeCurrencyBalance
	}
	return 0
}

type PlaceOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 扣除数量, 必须大于0
	Amount uint32 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// 增加资产列表, 调用delivery时透传
	Assets []*asset.UserAssetChange `protobuf:"bytes,4,rep,name=assets,proto3" json:"assets,omitempty"`
	// 订单配置，平台业务id，是否算收入等
	OrderConf *openpay.OrderConf `protobuf:"bytes,5,opt,name=order_conf,json=orderConf,proto3" json:"order_conf,omitempty"`
	// 场景
	Scene *openpay.Scene `protobuf:"bytes,6,opt,name=scene,proto3" json:"scene,omitempty"`
	// 设备相关
	Device *openpay.Device `protobuf:"bytes,7,opt,name=device,proto3" json:"device,omitempty"`
	// midas
	Midas *openpay.Midas `protobuf:"bytes,8,opt,name=midas,proto3" json:"midas,omitempty"`
	// mapExt
	MapExt map[string]string `protobuf:"bytes,9,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 时间 用于透传
	SysTs uint32 `protobuf:"varint,10,opt,name=sys_ts,json=sysTs,proto3" json:"sys_ts,omitempty"`
}

func (x *PlaceOrderReq) Reset() {
	*x = PlaceOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceOrderReq) ProtoMessage() {}

func (x *PlaceOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceOrderReq.ProtoReflect.Descriptor instead.
func (*PlaceOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{2}
}

func (x *PlaceOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PlaceOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PlaceOrderReq) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PlaceOrderReq) GetAssets() []*asset.UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *PlaceOrderReq) GetOrderConf() *openpay.OrderConf {
	if x != nil {
		return x.OrderConf
	}
	return nil
}

func (x *PlaceOrderReq) GetScene() *openpay.Scene {
	if x != nil {
		return x.Scene
	}
	return nil
}

func (x *PlaceOrderReq) GetDevice() *openpay.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *PlaceOrderReq) GetMidas() *openpay.Midas {
	if x != nil {
		return x.Midas
	}
	return nil
}

func (x *PlaceOrderReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *PlaceOrderReq) GetSysTs() uint32 {
	if x != nil {
		return x.SysTs
	}
	return 0
}

type PlaceOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BillNo string `protobuf:"bytes,1,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"` // 全局唯一
	// 订单签名，kg有
	Sig string `protobuf:"bytes,2,opt,name=sig,proto3" json:"sig,omitempty"`
	// 时间 用于透传
	SysTs uint32 `protobuf:"varint,3,opt,name=sys_ts,json=sysTs,proto3" json:"sys_ts,omitempty"`
}

func (x *PlaceOrderRsp) Reset() {
	*x = PlaceOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceOrderRsp) ProtoMessage() {}

func (x *PlaceOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceOrderRsp.ProtoReflect.Descriptor instead.
func (*PlaceOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{3}
}

func (x *PlaceOrderRsp) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *PlaceOrderRsp) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

func (x *PlaceOrderRsp) GetSysTs() uint32 {
	if x != nil {
		return x.SysTs
	}
	return 0
}

type PayReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 订单号
	BillNo string `protobuf:"bytes,3,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 扣除游戏币数量
	Amount uint32 `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// 备注，会写流水日志
	AppRemark string `protobuf:"bytes,5,opt,name=app_remark,json=appRemark,proto3" json:"app_remark,omitempty"`
	// 增加资产列表, 调用delivery时透传
	Assets []*asset.UserAssetChange `protobuf:"bytes,6,rep,name=assets,proto3" json:"assets,omitempty"`
	// 订单配置，平台业务id，是否算收入等
	OrderConf *openpay.OrderConf `protobuf:"bytes,7,opt,name=order_conf,json=orderConf,proto3" json:"order_conf,omitempty"`
	// 场景
	Scene *openpay.Scene `protobuf:"bytes,8,opt,name=scene,proto3" json:"scene,omitempty"`
	// 设备相关
	Device *openpay.Device `protobuf:"bytes,9,opt,name=device,proto3" json:"device,omitempty"`
	// midas
	Midas *openpay.Midas `protobuf:"bytes,10,opt,name=midas,proto3" json:"midas,omitempty"`
	// mapExt
	MapExt map[string]string `protobuf:"bytes,11,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 订单签名，kg有
	Sig string `protobuf:"bytes,12,opt,name=sig,proto3" json:"sig,omitempty"`
	// 调用Delivery接口时透传这个id
	TransactionId string `protobuf:"bytes,13,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// 游戏场次 id
	RoundId string `protobuf:"bytes,14,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
}

func (x *PayReq) Reset() {
	*x = PayReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayReq) ProtoMessage() {}

func (x *PayReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayReq.ProtoReflect.Descriptor instead.
func (*PayReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{4}
}

func (x *PayReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PayReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PayReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *PayReq) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PayReq) GetAppRemark() string {
	if x != nil {
		return x.AppRemark
	}
	return ""
}

func (x *PayReq) GetAssets() []*asset.UserAssetChange {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *PayReq) GetOrderConf() *openpay.OrderConf {
	if x != nil {
		return x.OrderConf
	}
	return nil
}

func (x *PayReq) GetScene() *openpay.Scene {
	if x != nil {
		return x.Scene
	}
	return nil
}

func (x *PayReq) GetDevice() *openpay.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *PayReq) GetMidas() *openpay.Midas {
	if x != nil {
		return x.Midas
	}
	return nil
}

func (x *PayReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *PayReq) GetSig() string {
	if x != nil {
		return x.Sig
	}
	return ""
}

func (x *PayReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *PayReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type PayRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 订单号
	BillNo string `protobuf:"bytes,1,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 余额
	Balance uint32 `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *PayRsp) Reset() {
	*x = PayRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayRsp) ProtoMessage() {}

func (x *PayRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayRsp.ProtoReflect.Descriptor instead.
func (*PayRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{5}
}

func (x *PayRsp) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *PayRsp) GetBalance() uint32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type PresentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 订单号
	BillNo string `protobuf:"bytes,3,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 奖励游戏币数量
	Amount uint32 `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// 备注，会写流水日志
	AppRemark string `protobuf:"bytes,5,opt,name=app_remark,json=appRemark,proto3" json:"app_remark,omitempty"`
	// 游戏场次 id
	RoundId string `protobuf:"bytes,6,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	// 资产
	AssetItems []*PresentReq_AssetItems `protobuf:"bytes,7,rep,name=asset_items,json=assetItems,proto3" json:"asset_items,omitempty"`
}

func (x *PresentReq) Reset() {
	*x = PresentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresentReq) ProtoMessage() {}

func (x *PresentReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresentReq.ProtoReflect.Descriptor instead.
func (*PresentReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{6}
}

func (x *PresentReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PresentReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PresentReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *PresentReq) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PresentReq) GetAppRemark() string {
	if x != nil {
		return x.AppRemark
	}
	return ""
}

func (x *PresentReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *PresentReq) GetAssetItems() []*PresentReq_AssetItems {
	if x != nil {
		return x.AssetItems
	}
	return nil
}

type PresentRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 订单号
	BillNo string `protobuf:"bytes,1,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 余额
	Balance uint32 `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *PresentRsp) Reset() {
	*x = PresentRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresentRsp) ProtoMessage() {}

func (x *PresentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresentRsp.ProtoReflect.Descriptor instead.
func (*PresentRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{7}
}

func (x *PresentRsp) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *PresentRsp) GetBalance() uint32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type Profile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 昵称
	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	// 头像
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 加密 uid
	EncryptUid string `protobuf:"bytes,3,opt,name=encrypt_uid,json=encryptUid,proto3" json:"encrypt_uid,omitempty"`
	// 财富等级
	TreasureLevel uint32 `protobuf:"varint,4,opt,name=treasure_level,json=treasureLevel,proto3" json:"treasure_level,omitempty"`
	// 头像框
	AvatarFrame string `protobuf:"bytes,5,opt,name=avatar_frame,json=avatarFrame,proto3" json:"avatar_frame,omitempty"`
	// 年龄
	Age uint32 `protobuf:"varint,6,opt,name=age,proto3" json:"age,omitempty"`
	// vip 等级
	VipLevel uint32 `protobuf:"varint,7,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	// 性别
	Gender Gender `protobuf:"varint,8,opt,name=gender,proto3,enum=component.game.Gender" json:"gender,omitempty"`
	// 城市
	City string `protobuf:"bytes,9,opt,name=city,proto3" json:"city,omitempty"`
	// 原始 uid
	RawUid uint64 `protobuf:"varint,10,opt,name=raw_uid,json=rawUid,proto3" json:"raw_uid,omitempty"`
}

func (x *Profile) Reset() {
	*x = Profile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Profile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Profile) ProtoMessage() {}

func (x *Profile) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Profile.ProtoReflect.Descriptor instead.
func (*Profile) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{8}
}

func (x *Profile) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *Profile) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Profile) GetEncryptUid() string {
	if x != nil {
		return x.EncryptUid
	}
	return ""
}

func (x *Profile) GetTreasureLevel() uint32 {
	if x != nil {
		return x.TreasureLevel
	}
	return 0
}

func (x *Profile) GetAvatarFrame() string {
	if x != nil {
		return x.AvatarFrame
	}
	return ""
}

func (x *Profile) GetAge() uint32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *Profile) GetVipLevel() uint32 {
	if x != nil {
		return x.VipLevel
	}
	return 0
}

func (x *Profile) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderUnknown
}

func (x *Profile) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Profile) GetRawUid() uint64 {
	if x != nil {
		return x.RawUid
	}
	return 0
}

type GetProfileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 头像长
	AvatarLength int32 `protobuf:"varint,3,opt,name=avatar_length,json=avatarLength,proto3" json:"avatar_length,omitempty"`
	// 头像宽
	AvatarWidth int32 `protobuf:"varint,4,opt,name=avatar_width,json=avatarWidth,proto3" json:"avatar_width,omitempty"`
}

func (x *GetProfileReq) Reset() {
	*x = GetProfileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileReq) ProtoMessage() {}

func (x *GetProfileReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileReq.ProtoReflect.Descriptor instead.
func (*GetProfileReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{9}
}

func (x *GetProfileReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetProfileReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GetProfileReq) GetAvatarLength() int32 {
	if x != nil {
		return x.AvatarLength
	}
	return 0
}

func (x *GetProfileReq) GetAvatarWidth() int32 {
	if x != nil {
		return x.AvatarWidth
	}
	return 0
}

type GetProfileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户资料
	Profile *Profile `protobuf:"bytes,1,opt,name=profile,proto3" json:"profile,omitempty"`
}

func (x *GetProfileRsp) Reset() {
	*x = GetProfileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileRsp) ProtoMessage() {}

func (x *GetProfileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileRsp.ProtoReflect.Descriptor instead.
func (*GetProfileRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{10}
}

func (x *GetProfileRsp) GetProfile() *Profile {
	if x != nil {
		return x.Profile
	}
	return nil
}

type BatchGetProfileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id 列表
	OpenIdList []string `protobuf:"bytes,2,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	// 头像长
	AvatarLength int32 `protobuf:"varint,3,opt,name=avatar_length,json=avatarLength,proto3" json:"avatar_length,omitempty"`
	// 头像宽
	AvatarWidth int32 `protobuf:"varint,4,opt,name=avatar_width,json=avatarWidth,proto3" json:"avatar_width,omitempty"`
}

func (x *BatchGetProfileReq) Reset() {
	*x = BatchGetProfileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetProfileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetProfileReq) ProtoMessage() {}

func (x *BatchGetProfileReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetProfileReq.ProtoReflect.Descriptor instead.
func (*BatchGetProfileReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{11}
}

func (x *BatchGetProfileReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGetProfileReq) GetOpenIdList() []string {
	if x != nil {
		return x.OpenIdList
	}
	return nil
}

func (x *BatchGetProfileReq) GetAvatarLength() int32 {
	if x != nil {
		return x.AvatarLength
	}
	return 0
}

func (x *BatchGetProfileReq) GetAvatarWidth() int32 {
	if x != nil {
		return x.AvatarWidth
	}
	return 0
}

type BatchGetProfileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户资料 key 为 open id
	Profiles map[string]*Profile `protobuf:"bytes,1,rep,name=profiles,proto3" json:"profiles,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetProfileRsp) Reset() {
	*x = BatchGetProfileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetProfileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetProfileRsp) ProtoMessage() {}

func (x *BatchGetProfileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetProfileRsp.ProtoReflect.Descriptor instead.
func (*BatchGetProfileRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{12}
}

func (x *BatchGetProfileRsp) GetProfiles() map[string]*Profile {
	if x != nil {
		return x.Profiles
	}
	return nil
}

type SendMailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 目标用户 open id
	ToOpenId string `protobuf:"bytes,3,opt,name=to_open_id,json=toOpenId,proto3" json:"to_open_id,omitempty"`
	// 私信内容，text 和 text_id 填其中一个
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// 私信内容id，用于多语言
	ContentId string `protobuf:"bytes,5,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	// 业务扩展字段, 微信订阅消息则传微信后台定义好的模板kv
	Attachment map[string]string `protobuf:"bytes,6,rep,name=attachment,proto3" json:"attachment,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 仅微信订阅消息需要
	WechatTemplateId string `protobuf:"bytes,7,opt,name=wechat_template_id,json=wechatTemplateId,proto3" json:"wechat_template_id,omitempty"`
	// 仅微信订阅消息需要,点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar）。该字段不填则模板无跳转。
	WechatPage string `protobuf:"bytes,8,opt,name=wechat_page,json=wechatPage,proto3" json:"wechat_page,omitempty"`
}

func (x *SendMailReq) Reset() {
	*x = SendMailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMailReq) ProtoMessage() {}

func (x *SendMailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMailReq.ProtoReflect.Descriptor instead.
func (*SendMailReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{13}
}

func (x *SendMailReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendMailReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendMailReq) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *SendMailReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendMailReq) GetContentId() string {
	if x != nil {
		return x.ContentId
	}
	return ""
}

func (x *SendMailReq) GetAttachment() map[string]string {
	if x != nil {
		return x.Attachment
	}
	return nil
}

func (x *SendMailReq) GetWechatTemplateId() string {
	if x != nil {
		return x.WechatTemplateId
	}
	return ""
}

func (x *SendMailReq) GetWechatPage() string {
	if x != nil {
		return x.WechatPage
	}
	return ""
}

type SendMailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendMailRsp) Reset() {
	*x = SendMailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMailRsp) ProtoMessage() {}

func (x *SendMailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMailRsp.ProtoReflect.Descriptor instead.
func (*SendMailRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{14}
}

type PrizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 金额
	Amount uint32 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// 订单号
	BillNo string `protobuf:"bytes,4,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
}

func (x *PrizeReq) Reset() {
	*x = PrizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrizeReq) ProtoMessage() {}

func (x *PrizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrizeReq.ProtoReflect.Descriptor instead.
func (*PrizeReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{15}
}

func (x *PrizeReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PrizeReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PrizeReq) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PrizeReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

type PrizeGift struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 礼物名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 礼物图标
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	// 礼物数量
	Num uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	// 礼物ID
	GiftId string `protobuf:"bytes,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
}

func (x *PrizeGift) Reset() {
	*x = PrizeGift{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrizeGift) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrizeGift) ProtoMessage() {}

func (x *PrizeGift) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrizeGift.ProtoReflect.Descriptor instead.
func (*PrizeGift) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{16}
}

func (x *PrizeGift) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PrizeGift) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *PrizeGift) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *PrizeGift) GetGiftId() string {
	if x != nil {
		return x.GiftId
	}
	return ""
}

type PrizeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 礼物列表
	Gifts []*PrizeGift `protobuf:"bytes,1,rep,name=gifts,proto3" json:"gifts,omitempty"`
}

func (x *PrizeRsp) Reset() {
	*x = PrizeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrizeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrizeRsp) ProtoMessage() {}

func (x *PrizeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrizeRsp.ProtoReflect.Descriptor instead.
func (*PrizeRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{17}
}

func (x *PrizeRsp) GetGifts() []*PrizeGift {
	if x != nil {
		return x.Gifts
	}
	return nil
}

type TDBankReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 表名
	Table string `protobuf:"bytes,3,opt,name=table,proto3" json:"table,omitempty"`
	// 服务名
	Program string `protobuf:"bytes,4,opt,name=program,proto3" json:"program,omitempty"`
	// 数据
	Message string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	// 业务
	BussId string `protobuf:"bytes,6,opt,name=buss_id,json=bussId,proto3" json:"buss_id,omitempty"`
}

func (x *TDBankReportReq) Reset() {
	*x = TDBankReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TDBankReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TDBankReportReq) ProtoMessage() {}

func (x *TDBankReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TDBankReportReq.ProtoReflect.Descriptor instead.
func (*TDBankReportReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{18}
}

func (x *TDBankReportReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TDBankReportReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TDBankReportReq) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *TDBankReportReq) GetProgram() string {
	if x != nil {
		return x.Program
	}
	return ""
}

func (x *TDBankReportReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TDBankReportReq) GetBussId() string {
	if x != nil {
		return x.BussId
	}
	return ""
}

type TDBankReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TDBankReportRsp) Reset() {
	*x = TDBankReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TDBankReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TDBankReportRsp) ProtoMessage() {}

func (x *TDBankReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TDBankReportRsp.ProtoReflect.Descriptor instead.
func (*TDBankReportRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{19}
}

type ConsumeFreeGiftReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 消耗数量 num
	Num uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
	// 订单号 bill_no
	BillNo string `protobuf:"bytes,4,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 来源文案 from_desc
	FromDesc string `protobuf:"bytes,5,opt,name=from_desc,json=fromDesc,proto3" json:"from_desc,omitempty"`
	// 上报 id report_id
	ReportId int64 `protobuf:"varint,6,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
}

func (x *ConsumeFreeGiftReq) Reset() {
	*x = ConsumeFreeGiftReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeFreeGiftReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeFreeGiftReq) ProtoMessage() {}

func (x *ConsumeFreeGiftReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeFreeGiftReq.ProtoReflect.Descriptor instead.
func (*ConsumeFreeGiftReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{20}
}

func (x *ConsumeFreeGiftReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ConsumeFreeGiftReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ConsumeFreeGiftReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *ConsumeFreeGiftReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *ConsumeFreeGiftReq) GetFromDesc() string {
	if x != nil {
		return x.FromDesc
	}
	return ""
}

func (x *ConsumeFreeGiftReq) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

type ConsumeFreeGiftRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConsumeFreeGiftRsp) Reset() {
	*x = ConsumeFreeGiftRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeFreeGiftRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeFreeGiftRsp) ProtoMessage() {}

func (x *ConsumeFreeGiftRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeFreeGiftRsp.ProtoReflect.Descriptor instead.
func (*ConsumeFreeGiftRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{21}
}

type RoomMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 消息文案 msg_text
	MsgText string `protobuf:"bytes,3,opt,name=msg_text,json=msgText,proto3" json:"msg_text,omitempty"`
	// 消息类型
	MsgType RoomMsgType `protobuf:"varint,4,opt,name=msg_type,json=msgType,proto3,enum=component.game.RoomMsgType" json:"msg_type,omitempty"`
	// 房间 ID
	RoomId string `protobuf:"bytes,5,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	// to open id
	ToOpenId string `protobuf:"bytes,6,opt,name=to_open_id,json=toOpenId,proto3" json:"to_open_id,omitempty"`
	// c2c 消息
	IsC2C bool `protobuf:"varint,7,opt,name=is_c2c,json=isC2c,proto3" json:"is_c2c,omitempty"`
	// 扩展数据
	ExtData map[string]string `protobuf:"bytes,8,rep,name=ext_data,json=extData,proto3" json:"ext_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin
	IdType int32 `protobuf:"varint,9,opt,name=id_type,json=idType,proto3" json:"id_type,omitempty"`
}

func (x *RoomMsgReq) Reset() {
	*x = RoomMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomMsgReq) ProtoMessage() {}

func (x *RoomMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomMsgReq.ProtoReflect.Descriptor instead.
func (*RoomMsgReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{22}
}

func (x *RoomMsgReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RoomMsgReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RoomMsgReq) GetMsgText() string {
	if x != nil {
		return x.MsgText
	}
	return ""
}

func (x *RoomMsgReq) GetMsgType() RoomMsgType {
	if x != nil {
		return x.MsgType
	}
	return RoomMsgType_RoomMsgUnknown
}

func (x *RoomMsgReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomMsgReq) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *RoomMsgReq) GetIsC2C() bool {
	if x != nil {
		return x.IsC2C
	}
	return false
}

func (x *RoomMsgReq) GetExtData() map[string]string {
	if x != nil {
		return x.ExtData
	}
	return nil
}

func (x *RoomMsgReq) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

type RoomMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RoomMsgRsp) Reset() {
	*x = RoomMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomMsgRsp) ProtoMessage() {}

func (x *RoomMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomMsgRsp.ProtoReflect.Descriptor instead.
func (*RoomMsgRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{23}
}

type SendPushReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 文案 content
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// 附加内容 attach
	Attach map[string]string `protobuf:"bytes,4,rep,name=attach,proto3" json:"attach,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// push平台配置ID
	FlowConfigId string `protobuf:"bytes,5,opt,name=flow_config_id,json=flowConfigId,proto3" json:"flow_config_id,omitempty"` // push中台配置id
	FromOpenId   string `protobuf:"bytes,6,opt,name=from_open_id,json=fromOpenId,proto3" json:"from_open_id,omitempty"`       // 发送pushId
}

func (x *SendPushReq) Reset() {
	*x = SendPushReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPushReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushReq) ProtoMessage() {}

func (x *SendPushReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushReq.ProtoReflect.Descriptor instead.
func (*SendPushReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{24}
}

func (x *SendPushReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendPushReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendPushReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendPushReq) GetAttach() map[string]string {
	if x != nil {
		return x.Attach
	}
	return nil
}

func (x *SendPushReq) GetFlowConfigId() string {
	if x != nil {
		return x.FlowConfigId
	}
	return ""
}

func (x *SendPushReq) GetFromOpenId() string {
	if x != nil {
		return x.FromOpenId
	}
	return ""
}

type SendPushRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendPushRsp) Reset() {
	*x = SendPushRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPushRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushRsp) ProtoMessage() {}

func (x *SendPushRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushRsp.ProtoReflect.Descriptor instead.
func (*SendPushRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{25}
}

type BigHornMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 消息ID msg_id
	MsgId string `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	// 文案 content
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// 附加内容 attach
	// 可选：跳转房间后打开的链接 map["afterH5URL"] = "url"
	// 可选：大喇叭样式ID，不传用默认的 map["configID"] = "配置ID"
	Attach map[string]string `protobuf:"bytes,5,rep,name=attach,proto3" json:"attach,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 打开H5链接，和跳转房间同时传时优先使用
	Open_H5_URL string `protobuf:"bytes,6,opt,name=open_H5_URL,json=openH5URL,proto3" json:"open_H5_URL,omitempty"`
	// 跳转房间ID
	RoomId string `protobuf:"bytes,7,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
}

func (x *BigHornMsgReq) Reset() {
	*x = BigHornMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BigHornMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BigHornMsgReq) ProtoMessage() {}

func (x *BigHornMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BigHornMsgReq.ProtoReflect.Descriptor instead.
func (*BigHornMsgReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{26}
}

func (x *BigHornMsgReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BigHornMsgReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BigHornMsgReq) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *BigHornMsgReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BigHornMsgReq) GetAttach() map[string]string {
	if x != nil {
		return x.Attach
	}
	return nil
}

func (x *BigHornMsgReq) GetOpen_H5_URL() string {
	if x != nil {
		return x.Open_H5_URL
	}
	return ""
}

func (x *BigHornMsgReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type BigHornMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BigHornMsgRsp) Reset() {
	*x = BigHornMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BigHornMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BigHornMsgRsp) ProtoMessage() {}

func (x *BigHornMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BigHornMsgRsp.ProtoReflect.Descriptor instead.
func (*BigHornMsgRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{27}
}

type SingleRewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// gift_id 奖品ID
	GiftId string `protobuf:"bytes,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	// gift_type 奖品Type
	GiftType uint32 `protobuf:"varint,2,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	// gift_num 发放数量
	GiftNum int64 `protobuf:"varint,3,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	// gift_reason 发放理由
	GiftReason string `protobuf:"bytes,4,opt,name=gift_reason,json=giftReason,proto3" json:"gift_reason,omitempty"`
}

func (x *SingleRewardItem) Reset() {
	*x = SingleRewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SingleRewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SingleRewardItem) ProtoMessage() {}

func (x *SingleRewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SingleRewardItem.ProtoReflect.Descriptor instead.
func (*SingleRewardItem) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{28}
}

func (x *SingleRewardItem) GetGiftId() string {
	if x != nil {
		return x.GiftId
	}
	return ""
}

func (x *SingleRewardItem) GetGiftType() uint32 {
	if x != nil {
		return x.GiftType
	}
	return 0
}

func (x *SingleRewardItem) GetGiftNum() int64 {
	if x != nil {
		return x.GiftNum
	}
	return 0
}

func (x *SingleRewardItem) GetGiftReason() string {
	if x != nil {
		return x.GiftReason
	}
	return ""
}

type SendSingleRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 奖励ID 透传用
	RewardId int64 `protobuf:"varint,3,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	// 数量 num
	Num uint32 `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
	// 订单号 bill_no
	BillNo string `protobuf:"bytes,5,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 奖品信息 reward_item
	RewardItem *SingleRewardItem `protobuf:"bytes,6,opt,name=reward_item,json=rewardItem,proto3" json:"reward_item,omitempty"`
	// 消耗的资产信息
	ConsumeItem *SendSingleRewardReq_ConsumeAssetItem `protobuf:"bytes,7,opt,name=consume_item,json=consumeItem,proto3" json:"consume_item,omitempty"`
	// 礼包发放原始的订单id
	OriginBillNo string `protobuf:"bytes,11,opt,name=origin_bill_no,json=originBillNo,proto3" json:"origin_bill_no,omitempty"`
}

func (x *SendSingleRewardReq) Reset() {
	*x = SendSingleRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSingleRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSingleRewardReq) ProtoMessage() {}

func (x *SendSingleRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSingleRewardReq.ProtoReflect.Descriptor instead.
func (*SendSingleRewardReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{29}
}

func (x *SendSingleRewardReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendSingleRewardReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendSingleRewardReq) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *SendSingleRewardReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *SendSingleRewardReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *SendSingleRewardReq) GetRewardItem() *SingleRewardItem {
	if x != nil {
		return x.RewardItem
	}
	return nil
}

func (x *SendSingleRewardReq) GetConsumeItem() *SendSingleRewardReq_ConsumeAssetItem {
	if x != nil {
		return x.ConsumeItem
	}
	return nil
}

func (x *SendSingleRewardReq) GetOriginBillNo() string {
	if x != nil {
		return x.OriginBillNo
	}
	return ""
}

type SendSingleRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendSingleRewardRsp) Reset() {
	*x = SendSingleRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSingleRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSingleRewardRsp) ProtoMessage() {}

func (x *SendSingleRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSingleRewardRsp.ProtoReflect.Descriptor instead.
func (*SendSingleRewardRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{30}
}

type RewardDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 礼物ID 非奖励ID，为奖励ID下面挂载的礼物
	GiftId string `protobuf:"bytes,3,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	// 礼物type 参考reward_sender_comm.proto GiftType
	GiftType uint32 `protobuf:"varint,4,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
}

func (x *RewardDetailReq) Reset() {
	*x = RewardDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardDetailReq) ProtoMessage() {}

func (x *RewardDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardDetailReq.ProtoReflect.Descriptor instead.
func (*RewardDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{31}
}

func (x *RewardDetailReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RewardDetailReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RewardDetailReq) GetGiftId() string {
	if x != nil {
		return x.GiftId
	}
	return ""
}

func (x *RewardDetailReq) GetGiftType() uint32 {
	if x != nil {
		return x.GiftType
	}
	return 0
}

// 子礼物信息
type SubGiftDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 礼物ID
	SubGiftId string `protobuf:"bytes,1,opt,name=sub_gift_id,json=subGiftId,proto3" json:"sub_gift_id,omitempty"`
	// 礼物type 参考reward_sender_comm.proto GiftType
	SubGiftType string `protobuf:"bytes,2,opt,name=sub_gift_type,json=subGiftType,proto3" json:"sub_gift_type,omitempty"`
	// 礼物数量
	SubGiftNum uint32 `protobuf:"varint,3,opt,name=sub_gift_num,json=subGiftNum,proto3" json:"sub_gift_num,omitempty"`
	// 礼物名称
	SubGiftName string `protobuf:"bytes,4,opt,name=sub_gift_name,json=subGiftName,proto3" json:"sub_gift_name,omitempty"`
	// 礼物logo
	SubGiftLogo string `protobuf:"bytes,5,opt,name=sub_gift_logo,json=subGiftLogo,proto3" json:"sub_gift_logo,omitempty"`
	// 礼物单价
	SubGiftUnitPrice uint32 `protobuf:"varint,6,opt,name=sub_gift_unit_price,json=subGiftUnitPrice,proto3" json:"sub_gift_unit_price,omitempty"`
	// expire_type 过期类型, 1相对过期, 2绝对过期, 0不过期
	SubGiftExpireType uint32 `protobuf:"varint,7,opt,name=sub_gift_expire_type,json=subGiftExpireType,proto3" json:"sub_gift_expire_type,omitempty"`
	// expire_sec 过期时间(s), 相对过期是过期时间(s),绝对过期是过期时间戳
	SubGiftExpireTime uint32 `protobuf:"varint,8,opt,name=sub_gift_expire_time,json=subGiftExpireTime,proto3" json:"sub_gift_expire_time,omitempty"`
}

func (x *SubGiftDetail) Reset() {
	*x = SubGiftDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubGiftDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubGiftDetail) ProtoMessage() {}

func (x *SubGiftDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubGiftDetail.ProtoReflect.Descriptor instead.
func (*SubGiftDetail) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{32}
}

func (x *SubGiftDetail) GetSubGiftId() string {
	if x != nil {
		return x.SubGiftId
	}
	return ""
}

func (x *SubGiftDetail) GetSubGiftType() string {
	if x != nil {
		return x.SubGiftType
	}
	return ""
}

func (x *SubGiftDetail) GetSubGiftNum() uint32 {
	if x != nil {
		return x.SubGiftNum
	}
	return 0
}

func (x *SubGiftDetail) GetSubGiftName() string {
	if x != nil {
		return x.SubGiftName
	}
	return ""
}

func (x *SubGiftDetail) GetSubGiftLogo() string {
	if x != nil {
		return x.SubGiftLogo
	}
	return ""
}

func (x *SubGiftDetail) GetSubGiftUnitPrice() uint32 {
	if x != nil {
		return x.SubGiftUnitPrice
	}
	return 0
}

func (x *SubGiftDetail) GetSubGiftExpireType() uint32 {
	if x != nil {
		return x.SubGiftExpireType
	}
	return 0
}

func (x *SubGiftDetail) GetSubGiftExpireTime() uint32 {
	if x != nil {
		return x.SubGiftExpireTime
	}
	return 0
}

type RewardDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SubGiftDetail 奖品ID 所指向的礼物列表
	GiftArray []*SubGiftDetail `protobuf:"bytes,1,rep,name=gift_array,json=giftArray,proto3" json:"gift_array,omitempty"`
}

func (x *RewardDetailRsp) Reset() {
	*x = RewardDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardDetailRsp) ProtoMessage() {}

func (x *RewardDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardDetailRsp.ProtoReflect.Descriptor instead.
func (*RewardDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{33}
}

func (x *RewardDetailRsp) GetGiftArray() []*SubGiftDetail {
	if x != nil {
		return x.GiftArray
	}
	return nil
}

type PropDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 道具名称
	PropName string `protobuf:"bytes,1,opt,name=prop_name,json=propName,proto3" json:"prop_name,omitempty"`
	// 道具logo
	PropLogo string `protobuf:"bytes,2,opt,name=prop_logo,json=propLogo,proto3" json:"prop_logo,omitempty"`
}

func (x *PropDetail) Reset() {
	*x = PropDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropDetail) ProtoMessage() {}

func (x *PropDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropDetail.ProtoReflect.Descriptor instead.
func (*PropDetail) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{34}
}

func (x *PropDetail) GetPropName() string {
	if x != nil {
		return x.PropName
	}
	return ""
}

func (x *PropDetail) GetPropLogo() string {
	if x != nil {
		return x.PropLogo
	}
	return ""
}

type BatchPropDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 道具ID
	PropIds []int64 `protobuf:"varint,3,rep,packed,name=prop_ids,json=propIds,proto3" json:"prop_ids,omitempty"`
	// plat 如果有plat，就不从openid和appid里面解析了
	Plat uint32 `protobuf:"varint,4,opt,name=plat,proto3" json:"plat,omitempty"`
}

func (x *BatchPropDetailReq) Reset() {
	*x = BatchPropDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchPropDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchPropDetailReq) ProtoMessage() {}

func (x *BatchPropDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchPropDetailReq.ProtoReflect.Descriptor instead.
func (*BatchPropDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{35}
}

func (x *BatchPropDetailReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchPropDetailReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchPropDetailReq) GetPropIds() []int64 {
	if x != nil {
		return x.PropIds
	}
	return nil
}

func (x *BatchPropDetailReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type BatchPropDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[int64]*PropDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchPropDetailRsp) Reset() {
	*x = BatchPropDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchPropDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchPropDetailRsp) ProtoMessage() {}

func (x *BatchPropDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchPropDetailRsp.ProtoReflect.Descriptor instead.
func (*BatchPropDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{36}
}

func (x *BatchPropDetailRsp) GetData() map[int64]*PropDetail {
	if x != nil {
		return x.Data
	}
	return nil
}

type GiftDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 礼物名称
	GiftName string `protobuf:"bytes,1,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	// 礼物logo
	GiftLogo string `protobuf:"bytes,2,opt,name=gift_logo,json=giftLogo,proto3" json:"gift_logo,omitempty"`
	// 礼物单价
	GiftPrice uint32 `protobuf:"varint,3,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
}

func (x *GiftDetail) Reset() {
	*x = GiftDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftDetail) ProtoMessage() {}

func (x *GiftDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftDetail.ProtoReflect.Descriptor instead.
func (*GiftDetail) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{37}
}

func (x *GiftDetail) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *GiftDetail) GetGiftLogo() string {
	if x != nil {
		return x.GiftLogo
	}
	return ""
}

func (x *GiftDetail) GetGiftPrice() uint32 {
	if x != nil {
		return x.GiftPrice
	}
	return 0
}

type BatchGiftDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 礼物ID
	GiftIds []int64 `protobuf:"varint,3,rep,packed,name=gift_ids,json=giftIds,proto3" json:"gift_ids,omitempty"`
	// plat 如果有plat，就不从openid和appid里面解析了
	Plat uint32 `protobuf:"varint,4,opt,name=plat,proto3" json:"plat,omitempty"`
}

func (x *BatchGiftDetailReq) Reset() {
	*x = BatchGiftDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGiftDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGiftDetailReq) ProtoMessage() {}

func (x *BatchGiftDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGiftDetailReq.ProtoReflect.Descriptor instead.
func (*BatchGiftDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{38}
}

func (x *BatchGiftDetailReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGiftDetailReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchGiftDetailReq) GetGiftIds() []int64 {
	if x != nil {
		return x.GiftIds
	}
	return nil
}

func (x *BatchGiftDetailReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type BatchGiftDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[int64]*GiftDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGiftDetailRsp) Reset() {
	*x = BatchGiftDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGiftDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGiftDetailRsp) ProtoMessage() {}

func (x *BatchGiftDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGiftDetailRsp.ProtoReflect.Descriptor instead.
func (*BatchGiftDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{39}
}

func (x *BatchGiftDetailRsp) GetData() map[int64]*GiftDetail {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetPlatBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// plat_asset_id 平台货币ID
	PlatAssetId string `protobuf:"bytes,3,opt,name=plat_asset_id,json=platAssetId,proto3" json:"plat_asset_id,omitempty"`
	// plat_asset_type 平台货币类型 参考PlatAssetType
	PlatAssetType uint32 `protobuf:"varint,4,opt,name=plat_asset_type,json=platAssetType,proto3" json:"plat_asset_type,omitempty"`
}

func (x *GetPlatBalanceReq) Reset() {
	*x = GetPlatBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlatBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatBalanceReq) ProtoMessage() {}

func (x *GetPlatBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatBalanceReq.ProtoReflect.Descriptor instead.
func (*GetPlatBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{40}
}

func (x *GetPlatBalanceReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetPlatBalanceReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GetPlatBalanceReq) GetPlatAssetId() string {
	if x != nil {
		return x.PlatAssetId
	}
	return ""
}

func (x *GetPlatBalanceReq) GetPlatAssetType() uint32 {
	if x != nil {
		return x.PlatAssetType
	}
	return 0
}

type GetPlatBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance uint64 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *GetPlatBalanceRsp) Reset() {
	*x = GetPlatBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlatBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatBalanceRsp) ProtoMessage() {}

func (x *GetPlatBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatBalanceRsp.ProtoReflect.Descriptor instead.
func (*GetPlatBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{41}
}

func (x *GetPlatBalanceRsp) GetBalance() uint64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type PrizeV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 金额
	Amount uint32 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// 订单号
	BillNo string `protobuf:"bytes,4,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 待发奖礼物 K歌：K币礼物
	Gifts []*PrizeGift `protobuf:"bytes,5,rep,name=gifts,proto3" json:"gifts,omitempty"`
}

func (x *PrizeV2Req) Reset() {
	*x = PrizeV2Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrizeV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrizeV2Req) ProtoMessage() {}

func (x *PrizeV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrizeV2Req.ProtoReflect.Descriptor instead.
func (*PrizeV2Req) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{42}
}

func (x *PrizeV2Req) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PrizeV2Req) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PrizeV2Req) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PrizeV2Req) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *PrizeV2Req) GetGifts() []*PrizeGift {
	if x != nil {
		return x.Gifts
	}
	return nil
}

type PrizeV2Rsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PrizeV2Rsp) Reset() {
	*x = PrizeV2Rsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrizeV2Rsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrizeV2Rsp) ProtoMessage() {}

func (x *PrizeV2Rsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrizeV2Rsp.ProtoReflect.Descriptor instead.
func (*PrizeV2Rsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{43}
}

type DataReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type 数据类型
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// data 数据
	Data string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	// app id
	AppId string `protobuf:"bytes,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,4,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *DataReportReq) Reset() {
	*x = DataReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReportReq) ProtoMessage() {}

func (x *DataReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReportReq.ProtoReflect.Descriptor instead.
func (*DataReportReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{44}
}

func (x *DataReportReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DataReportReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *DataReportReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DataReportReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type DataReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DataReportRsp) Reset() {
	*x = DataReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataReportRsp) ProtoMessage() {}

func (x *DataReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataReportRsp.ProtoReflect.Descriptor instead.
func (*DataReportRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{45}
}

type SafeCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 目标用户 open id
	ToOpenId string `protobuf:"bytes,3,opt,name=to_open_id,json=toOpenId,proto3" json:"to_open_id,omitempty"`
	// 安全 appid
	SafeAppid int32 `protobuf:"varint,4,opt,name=safe_appid,json=safeAppid,proto3" json:"safe_appid,omitempty"`
	// qua 设备qua
	Qua string `protobuf:"bytes,5,opt,name=qua,proto3" json:"qua,omitempty"`
	// content 待检查文案
	Content string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	// 设备号
	DeviceInfo string `protobuf:"bytes,7,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
	// ipv4
	Ipv4 string `protobuf:"bytes,8,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	// userid类型
	IdType int32 `protobuf:"varint,9,opt,name=id_type,json=idType,proto3" json:"id_type,omitempty"` //UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin
}

func (x *SafeCheckReq) Reset() {
	*x = SafeCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCheckReq) ProtoMessage() {}

func (x *SafeCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCheckReq.ProtoReflect.Descriptor instead.
func (*SafeCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{46}
}

func (x *SafeCheckReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SafeCheckReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SafeCheckReq) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *SafeCheckReq) GetSafeAppid() int32 {
	if x != nil {
		return x.SafeAppid
	}
	return 0
}

func (x *SafeCheckReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *SafeCheckReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SafeCheckReq) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

func (x *SafeCheckReq) GetIpv4() string {
	if x != nil {
		return x.Ipv4
	}
	return ""
}

func (x *SafeCheckReq) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

type SafeCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SafeType int32 `protobuf:"varint,1,opt,name=safeType,proto3" json:"safeType,omitempty"` //非0为被安全打击了
}

func (x *SafeCheckRsp) Reset() {
	*x = SafeCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCheckRsp) ProtoMessage() {}

func (x *SafeCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCheckRsp.ProtoReflect.Descriptor instead.
func (*SafeCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{47}
}

func (x *SafeCheckRsp) GetSafeType() int32 {
	if x != nil {
		return x.SafeType
	}
	return 0
}

type QzaReportReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data      map[string]string `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	NewReport bool              `protobuf:"varint,2,opt,name=new_report,json=newReport,proto3" json:"new_report,omitempty"`
}

func (x *QzaReportReq) Reset() {
	*x = QzaReportReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QzaReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QzaReportReq) ProtoMessage() {}

func (x *QzaReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QzaReportReq.ProtoReflect.Descriptor instead.
func (*QzaReportReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{48}
}

func (x *QzaReportReq) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *QzaReportReq) GetNewReport() bool {
	if x != nil {
		return x.NewReport
	}
	return false
}

type QzaReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QzaReportRsp) Reset() {
	*x = QzaReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QzaReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QzaReportRsp) ProtoMessage() {}

func (x *QzaReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QzaReportRsp.ProtoReflect.Descriptor instead.
func (*QzaReportRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{49}
}

type AnonymousItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Anonymous uint32 `protobuf:"varint,1,opt,name=Anonymous,proto3" json:"Anonymous,omitempty"` // 1匿名，0非匿名
}

func (x *AnonymousItem) Reset() {
	*x = AnonymousItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnonymousItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnonymousItem) ProtoMessage() {}

func (x *AnonymousItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnonymousItem.ProtoReflect.Descriptor instead.
func (*AnonymousItem) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{50}
}

func (x *AnonymousItem) GetAnonymous() uint32 {
	if x != nil {
		return x.Anonymous
	}
	return 0
}

type BatchGetAnonymousStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// host_open_id
	AnchorOpenId string `protobuf:"bytes,2,opt,name=anchor_open_id,json=anchorOpenId,proto3" json:"anchor_open_id,omitempty"`
	// 用户open, 单批最多49个
	AudiencesOpenIdList []string `protobuf:"bytes,3,rep,name=audiences_open_id_list,json=audiencesOpenIdList,proto3" json:"audiences_open_id_list,omitempty"`
}

func (x *BatchGetAnonymousStatusReq) Reset() {
	*x = BatchGetAnonymousStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAnonymousStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAnonymousStatusReq) ProtoMessage() {}

func (x *BatchGetAnonymousStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAnonymousStatusReq.ProtoReflect.Descriptor instead.
func (*BatchGetAnonymousStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{51}
}

func (x *BatchGetAnonymousStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGetAnonymousStatusReq) GetAnchorOpenId() string {
	if x != nil {
		return x.AnchorOpenId
	}
	return ""
}

func (x *BatchGetAnonymousStatusReq) GetAudiencesOpenIdList() []string {
	if x != nil {
		return x.AudiencesOpenIdList
	}
	return nil
}

type BatchGetAnonymousStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key为openid
	Data map[string]*AnonymousItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetAnonymousStatusRsp) Reset() {
	*x = BatchGetAnonymousStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAnonymousStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAnonymousStatusRsp) ProtoMessage() {}

func (x *BatchGetAnonymousStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAnonymousStatusRsp.ProtoReflect.Descriptor instead.
func (*BatchGetAnonymousStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{52}
}

func (x *BatchGetAnonymousStatusRsp) GetData() map[string]*AnonymousItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type HippyMsgUserSwitchEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	IsOpen bool   `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	SwType uint32 `protobuf:"varint,4,opt,name=sw_type,json=swType,proto3" json:"sw_type,omitempty"` // 调用侧指定
}

func (x *HippyMsgUserSwitchEventReq) Reset() {
	*x = HippyMsgUserSwitchEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HippyMsgUserSwitchEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HippyMsgUserSwitchEventReq) ProtoMessage() {}

func (x *HippyMsgUserSwitchEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HippyMsgUserSwitchEventReq.ProtoReflect.Descriptor instead.
func (*HippyMsgUserSwitchEventReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{53}
}

func (x *HippyMsgUserSwitchEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *HippyMsgUserSwitchEventReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *HippyMsgUserSwitchEventReq) GetIsOpen() bool {
	if x != nil {
		return x.IsOpen
	}
	return false
}

func (x *HippyMsgUserSwitchEventReq) GetSwType() uint32 {
	if x != nil {
		return x.SwType
	}
	return 0
}

type HippyMsgUserSwitchEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HippyMsgUserSwitchEventRsp) Reset() {
	*x = HippyMsgUserSwitchEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HippyMsgUserSwitchEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HippyMsgUserSwitchEventRsp) ProtoMessage() {}

func (x *HippyMsgUserSwitchEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HippyMsgUserSwitchEventRsp.ProtoReflect.Descriptor instead.
func (*HippyMsgUserSwitchEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{54}
}

type HippyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgTxt  string `protobuf:"bytes,1,opt,name=msg_txt,json=msgTxt,proto3" json:"msg_txt,omitempty"`
	BtnTxt  string `protobuf:"bytes,2,opt,name=btn_txt,json=btnTxt,proto3" json:"btn_txt,omitempty"`
	BgPic   string `protobuf:"bytes,3,opt,name=bg_pic,json=bgPic,proto3" json:"bg_pic,omitempty"`
	BtnPic  string `protobuf:"bytes,4,opt,name=btn_pic,json=btnPic,proto3" json:"btn_pic,omitempty"`
	JumpUrl string `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	RoundId string `protobuf:"bytes,6,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	ConfId  int32  `protobuf:"varint,7,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	MsgId   string `protobuf:"bytes,8,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
}

func (x *HippyInfo) Reset() {
	*x = HippyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HippyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HippyInfo) ProtoMessage() {}

func (x *HippyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HippyInfo.ProtoReflect.Descriptor instead.
func (*HippyInfo) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{55}
}

func (x *HippyInfo) GetMsgTxt() string {
	if x != nil {
		return x.MsgTxt
	}
	return ""
}

func (x *HippyInfo) GetBtnTxt() string {
	if x != nil {
		return x.BtnTxt
	}
	return ""
}

func (x *HippyInfo) GetBgPic() string {
	if x != nil {
		return x.BgPic
	}
	return ""
}

func (x *HippyInfo) GetBtnPic() string {
	if x != nil {
		return x.BtnPic
	}
	return ""
}

func (x *HippyInfo) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *HippyInfo) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *HippyInfo) GetConfId() int32 {
	if x != nil {
		return x.ConfId
	}
	return 0
}

func (x *HippyInfo) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

type SendHippyMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId          string     `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId         string     `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	RoomId         string     `protobuf:"bytes,3,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	SwitchType     int32      `protobuf:"varint,4,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	IsSwitchFilter bool       `protobuf:"varint,5,opt,name=is_switch_filter,json=isSwitchFilter,proto3" json:"is_switch_filter,omitempty"`
	HippyInfo      *HippyInfo `protobuf:"bytes,6,opt,name=hippy_info,json=hippyInfo,proto3" json:"hippy_info,omitempty"`
}

func (x *SendHippyMsgReq) Reset() {
	*x = SendHippyMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendHippyMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendHippyMsgReq) ProtoMessage() {}

func (x *SendHippyMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendHippyMsgReq.ProtoReflect.Descriptor instead.
func (*SendHippyMsgReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{56}
}

func (x *SendHippyMsgReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendHippyMsgReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendHippyMsgReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SendHippyMsgReq) GetSwitchType() int32 {
	if x != nil {
		return x.SwitchType
	}
	return 0
}

func (x *SendHippyMsgReq) GetIsSwitchFilter() bool {
	if x != nil {
		return x.IsSwitchFilter
	}
	return false
}

func (x *SendHippyMsgReq) GetHippyInfo() *HippyInfo {
	if x != nil {
		return x.HippyInfo
	}
	return nil
}

type SendHippyMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendHippyMsgRsp) Reset() {
	*x = SendHippyMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendHippyMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendHippyMsgRsp) ProtoMessage() {}

func (x *SendHippyMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendHippyMsgRsp.ProtoReflect.Descriptor instead.
func (*SendHippyMsgRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{57}
}

type UIABTestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string                    `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                           // appId
	OpenId        string                    `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`                        // openId
	BusinessId    string                    `protobuf:"bytes,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`            // 业务Id
	ChannelId     string                    `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`               // 渠道id
	ModuleId      string                    `protobuf:"bytes,5,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`                  // 模块id
	Qua           string                    `protobuf:"bytes,6,opt,name=qua,proto3" json:"qua,omitempty"`                                            // 客户端版本
	DevInfo       string                    `protobuf:"bytes,7,opt,name=dev_info,json=devInfo,proto3" json:"dev_info,omitempty"`                     // 客户端设备信息
	CallerSvrName string                    `protobuf:"bytes,8,opt,name=caller_svr_name,json=callerSvrName,proto3" json:"caller_svr_name,omitempty"` // 主调服务名
	Labels        []*abtest.AbtestLabelItem `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty"`
}

func (x *UIABTestReq) Reset() {
	*x = UIABTestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIABTestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIABTestReq) ProtoMessage() {}

func (x *UIABTestReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIABTestReq.ProtoReflect.Descriptor instead.
func (*UIABTestReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{58}
}

func (x *UIABTestReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *UIABTestReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *UIABTestReq) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *UIABTestReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *UIABTestReq) GetModuleId() string {
	if x != nil {
		return x.ModuleId
	}
	return ""
}

func (x *UIABTestReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *UIABTestReq) GetDevInfo() string {
	if x != nil {
		return x.DevInfo
	}
	return ""
}

func (x *UIABTestReq) GetCallerSvrName() string {
	if x != nil {
		return x.CallerSvrName
	}
	return ""
}

func (x *UIABTestReq) GetLabels() []*abtest.AbtestLabelItem {
	if x != nil {
		return x.Labels
	}
	return nil
}

type UIABTestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapTestInfo map[string]*abtest.UiAbtestRspItem `protobuf:"bytes,1,rep,name=map_test_info,json=mapTestInfo,proto3" json:"map_test_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //testInfo
	Interval    int32                              `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`                                                                                                                   //请求间隔
}

func (x *UIABTestRsp) Reset() {
	*x = UIABTestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UIABTestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIABTestRsp) ProtoMessage() {}

func (x *UIABTestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIABTestRsp.ProtoReflect.Descriptor instead.
func (*UIABTestRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{59}
}

func (x *UIABTestRsp) GetMapTestInfo() map[string]*abtest.UiAbtestRspItem {
	if x != nil {
		return x.MapTestInfo
	}
	return nil
}

func (x *UIABTestRsp) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

type ABTestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string                    `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                           // appId
	OpenId        string                    `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`                        // openId
	BusinessId    string                    `protobuf:"bytes,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`            // 业务Id
	ChannelId     string                    `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`               // 渠道id
	ModuleId      string                    `protobuf:"bytes,5,opt,name=module_id,json=moduleId,proto3" json:"module_id,omitempty"`                  // 模块id
	Qua           string                    `protobuf:"bytes,6,opt,name=qua,proto3" json:"qua,omitempty"`                                            // 客户端版本
	DevInfo       string                    `protobuf:"bytes,7,opt,name=dev_info,json=devInfo,proto3" json:"dev_info,omitempty"`                     // 客户端设备信息
	CallerSvrName string                    `protobuf:"bytes,8,opt,name=caller_svr_name,json=callerSvrName,proto3" json:"caller_svr_name,omitempty"` // 主调服务名
	Labels        []*abtest.AbtestLabelItem `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty"`
	ModuleKeys    []string                  `protobuf:"bytes,10,rep,name=moduleKeys,proto3" json:"moduleKeys,omitempty"` // 批量查指定moduleKey
	Version       string                    `protobuf:"bytes,11,opt,name=version,proto3" json:"version,omitempty"`       // 客户端版本
	Platform      string                    `protobuf:"bytes,12,opt,name=platform,proto3" json:"platform,omitempty"`     // 平台 andriod、ios
}

func (x *ABTestReq) Reset() {
	*x = ABTestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ABTestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABTestReq) ProtoMessage() {}

func (x *ABTestReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABTestReq.ProtoReflect.Descriptor instead.
func (*ABTestReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{60}
}

func (x *ABTestReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ABTestReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ABTestReq) GetBusinessId() string {
	if x != nil {
		return x.BusinessId
	}
	return ""
}

func (x *ABTestReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ABTestReq) GetModuleId() string {
	if x != nil {
		return x.ModuleId
	}
	return ""
}

func (x *ABTestReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *ABTestReq) GetDevInfo() string {
	if x != nil {
		return x.DevInfo
	}
	return ""
}

func (x *ABTestReq) GetCallerSvrName() string {
	if x != nil {
		return x.CallerSvrName
	}
	return ""
}

func (x *ABTestReq) GetLabels() []*abtest.AbtestLabelItem {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ABTestReq) GetModuleKeys() []string {
	if x != nil {
		return x.ModuleKeys
	}
	return nil
}

func (x *ABTestReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ABTestReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type AbtestPassback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UidBagType string `protobuf:"bytes,1,opt,name=uid_bag_type,json=uidBagType,proto3" json:"uid_bag_type,omitempty"`
	UidBagId   string `protobuf:"bytes,2,opt,name=uid_bag_id,json=uidBagId,proto3" json:"uid_bag_id,omitempty"`
}

func (x *AbtestPassback) Reset() {
	*x = AbtestPassback{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbtestPassback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbtestPassback) ProtoMessage() {}

func (x *AbtestPassback) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbtestPassback.ProtoReflect.Descriptor instead.
func (*AbtestPassback) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{61}
}

func (x *AbtestPassback) GetUidBagType() string {
	if x != nil {
		return x.UidBagType
	}
	return ""
}

func (x *AbtestPassback) GetUidBagId() string {
	if x != nil {
		return x.UidBagId
	}
	return ""
}

type AbtestRspItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TestId    string            `protobuf:"bytes,1,opt,name=test_id,json=testId,proto3" json:"test_id,omitempty"`                                                                                                  //实验组id
	ChannelId string            `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`                                                                                         //渠道id
	MapParams map[string]string `protobuf:"bytes,3,rep,name=map_params,json=mapParams,proto3" json:"map_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //实验参数
	StrAbtest string            `protobuf:"bytes,4,opt,name=str_abtest,json=strAbtest,proto3" json:"str_abtest,omitempty"`                                                                                         //数据上报用
	Passback  *AbtestPassback   `protobuf:"bytes,5,opt,name=passback,proto3" json:"passback,omitempty"`                                                                                                            // 透传abtest实验数据
}

func (x *AbtestRspItem) Reset() {
	*x = AbtestRspItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AbtestRspItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbtestRspItem) ProtoMessage() {}

func (x *AbtestRspItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbtestRspItem.ProtoReflect.Descriptor instead.
func (*AbtestRspItem) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{62}
}

func (x *AbtestRspItem) GetTestId() string {
	if x != nil {
		return x.TestId
	}
	return ""
}

func (x *AbtestRspItem) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *AbtestRspItem) GetMapParams() map[string]string {
	if x != nil {
		return x.MapParams
	}
	return nil
}

func (x *AbtestRspItem) GetStrAbtest() string {
	if x != nil {
		return x.StrAbtest
	}
	return ""
}

func (x *AbtestRspItem) GetPassback() *AbtestPassback {
	if x != nil {
		return x.Passback
	}
	return nil
}

type ABTestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapTestInfo map[string]*AbtestRspItem `protobuf:"bytes,1,rep,name=map_test_info,json=mapTestInfo,proto3" json:"map_test_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //testInfo
	Abtest      string                    `protobuf:"bytes,2,opt,name=abtest,proto3" json:"abtest,omitempty"`                                                                                                                        // 数据上报用
}

func (x *ABTestRsp) Reset() {
	*x = ABTestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ABTestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ABTestRsp) ProtoMessage() {}

func (x *ABTestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ABTestRsp.ProtoReflect.Descriptor instead.
func (*ABTestRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{63}
}

func (x *ABTestRsp) GetMapTestInfo() map[string]*AbtestRspItem {
	if x != nil {
		return x.MapTestInfo
	}
	return nil
}

func (x *ABTestRsp) GetAbtest() string {
	if x != nil {
		return x.Abtest
	}
	return ""
}

type TaskConditionBill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId      string            `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	ConditionId uint32            `protobuf:"varint,3,opt,name=condition_id,json=conditionId,proto3" json:"condition_id,omitempty"`
	Num         uint32            `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`
	Timestamp   uint32            `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ToOpenId    string            `protobuf:"bytes,6,opt,name=to_open_id,json=toOpenId,proto3" json:"to_open_id,omitempty"`
	ConsumeId   string            `protobuf:"bytes,7,opt,name=consume_id,json=consumeId,proto3" json:"consume_id,omitempty"`
	MapExt      map[string]string `protobuf:"bytes,8,rep,name=map_ext,json=mapExt,proto3" json:"map_ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	RoomId      string            `protobuf:"bytes,9,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Qua         string            `protobuf:"bytes,10,opt,name=qua,proto3" json:"qua,omitempty"`
}

func (x *TaskConditionBill) Reset() {
	*x = TaskConditionBill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskConditionBill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskConditionBill) ProtoMessage() {}

func (x *TaskConditionBill) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskConditionBill.ProtoReflect.Descriptor instead.
func (*TaskConditionBill) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{64}
}

func (x *TaskConditionBill) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TaskConditionBill) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TaskConditionBill) GetConditionId() uint32 {
	if x != nil {
		return x.ConditionId
	}
	return 0
}

func (x *TaskConditionBill) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *TaskConditionBill) GetTimestamp() uint32 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *TaskConditionBill) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *TaskConditionBill) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *TaskConditionBill) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *TaskConditionBill) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *TaskConditionBill) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

type ReportTaskConditionBillReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Bill *TaskConditionBill `protobuf:"bytes,1,opt,name=bill,proto3" json:"bill,omitempty"`
}

func (x *ReportTaskConditionBillReq) Reset() {
	*x = ReportTaskConditionBillReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportTaskConditionBillReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportTaskConditionBillReq) ProtoMessage() {}

func (x *ReportTaskConditionBillReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportTaskConditionBillReq.ProtoReflect.Descriptor instead.
func (*ReportTaskConditionBillReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{65}
}

func (x *ReportTaskConditionBillReq) GetBill() *TaskConditionBill {
	if x != nil {
		return x.Bill
	}
	return nil
}

type ReportTaskConditionBillRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportTaskConditionBillRsp) Reset() {
	*x = ReportTaskConditionBillRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportTaskConditionBillRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportTaskConditionBillRsp) ProtoMessage() {}

func (x *ReportTaskConditionBillRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportTaskConditionBillRsp.ProtoReflect.Descriptor instead.
func (*ReportTaskConditionBillRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{66}
}

type FollowOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open_id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// open id 列表
	OpenIdList []string `protobuf:"bytes,3,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	// qua 设备qua
	Qua string `protobuf:"bytes,4,opt,name=qua,proto3" json:"qua,omitempty"`
	// deviceInfo
	DeviceInfo string `protobuf:"bytes,5,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
	// optType
	Type FollowOptType `protobuf:"varint,6,opt,name=type,proto3,enum=component.game.FollowOptType" json:"type,omitempty"`
}

func (x *FollowOptReq) Reset() {
	*x = FollowOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowOptReq) ProtoMessage() {}

func (x *FollowOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowOptReq.ProtoReflect.Descriptor instead.
func (*FollowOptReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{67}
}

func (x *FollowOptReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *FollowOptReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *FollowOptReq) GetOpenIdList() []string {
	if x != nil {
		return x.OpenIdList
	}
	return nil
}

func (x *FollowOptReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *FollowOptReq) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

func (x *FollowOptReq) GetType() FollowOptType {
	if x != nil {
		return x.Type
	}
	return FollowOptType_FOLLOW_TYPE_NONE
}

type FollowInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result FollowResult `protobuf:"varint,1,opt,name=result,proto3,enum=component.game.FollowResult" json:"result,omitempty"`
	OpenId string       `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *FollowInfo) Reset() {
	*x = FollowInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowInfo) ProtoMessage() {}

func (x *FollowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowInfo.ProtoReflect.Descriptor instead.
func (*FollowInfo) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{68}
}

func (x *FollowInfo) GetResult() FollowResult {
	if x != nil {
		return x.Result
	}
	return FollowResult_FOLLOW_TYPE_SUCC
}

func (x *FollowInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type FollowOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//key:openid
	Results map[string]*FollowInfo `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FollowOptRsp) Reset() {
	*x = FollowOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowOptRsp) ProtoMessage() {}

func (x *FollowOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowOptRsp.ProtoReflect.Descriptor instead.
func (*FollowOptRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{69}
}

func (x *FollowOptRsp) GetResults() map[string]*FollowInfo {
	if x != nil {
		return x.Results
	}
	return nil
}

type QueryRelationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open_id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// open id 列表
	OpenIdList []string `protobuf:"bytes,3,rep,name=open_id_list,json=openIdList,proto3" json:"open_id_list,omitempty"`
	// mask
	Mask RelatioMask `protobuf:"varint,4,opt,name=mask,proto3,enum=component.game.RelatioMask" json:"mask,omitempty"`
}

func (x *QueryRelationReq) Reset() {
	*x = QueryRelationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRelationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRelationReq) ProtoMessage() {}

func (x *QueryRelationReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRelationReq.ProtoReflect.Descriptor instead.
func (*QueryRelationReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{70}
}

func (x *QueryRelationReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryRelationReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryRelationReq) GetOpenIdList() []string {
	if x != nil {
		return x.OpenIdList
	}
	return nil
}

func (x *QueryRelationReq) GetMask() RelatioMask {
	if x != nil {
		return x.Mask
	}
	return RelatioMask_RELATION_MASK_ALL
}

type RalationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   RelationType `protobuf:"varint,1,opt,name=type,proto3,enum=component.game.RelationType" json:"type,omitempty"`
	OpenId string       `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *RalationInfo) Reset() {
	*x = RalationInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RalationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RalationInfo) ProtoMessage() {}

func (x *RalationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RalationInfo.ProtoReflect.Descriptor instead.
func (*RalationInfo) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{71}
}

func (x *RalationInfo) GetType() RelationType {
	if x != nil {
		return x.Type
	}
	return RelationType_RELATION_NONE
}

func (x *RalationInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QueryRelationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	//key:openid
	Ralations map[string]*RalationInfo `protobuf:"bytes,1,rep,name=ralations,proto3" json:"ralations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *QueryRelationRsp) Reset() {
	*x = QueryRelationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRelationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRelationRsp) ProtoMessage() {}

func (x *QueryRelationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRelationRsp.ProtoReflect.Descriptor instead.
func (*QueryRelationRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{72}
}

func (x *QueryRelationRsp) GetRalations() map[string]*RalationInfo {
	if x != nil {
		return x.Ralations
	}
	return nil
}

type QueryCertInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 登录态信息
	Cookie map[string]string `protobuf:"bytes,3,rep,name=cookie,proto3" json:"cookie,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 客户端 ip
	ClientIp string `protobuf:"bytes,4,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	// 校验类型
	CheckType GameCheckType `protobuf:"varint,5,opt,name=check_type,json=checkType,proto3,enum=component.game.GameCheckType" json:"check_type,omitempty"`
	// 房间 id
	RoomId string `protobuf:"bytes,6,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
}

func (x *QueryCertInfoReq) Reset() {
	*x = QueryCertInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCertInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCertInfoReq) ProtoMessage() {}

func (x *QueryCertInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCertInfoReq.ProtoReflect.Descriptor instead.
func (*QueryCertInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{73}
}

func (x *QueryCertInfoReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryCertInfoReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryCertInfoReq) GetCookie() map[string]string {
	if x != nil {
		return x.Cookie
	}
	return nil
}

func (x *QueryCertInfoReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *QueryCertInfoReq) GetCheckType() GameCheckType {
	if x != nil {
		return x.CheckType
	}
	return GameCheckType_CheckTypeNone
}

func (x *QueryCertInfoReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type QueryCertInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 校验码
	AuthCode int32 `protobuf:"varint,1,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
	// 校验信息
	AuthMessage string `protobuf:"bytes,2,opt,name=auth_message,json=authMessage,proto3" json:"auth_message,omitempty"`
	// 是否实名
	IsRealName bool `protobuf:"varint,3,opt,name=is_real_name,json=isRealName,proto3" json:"is_real_name,omitempty"`
	// 是否成年
	AdultType AdultType `protobuf:"varint,4,opt,name=adult_type,json=adultType,proto3,enum=component.game.AdultType" json:"adult_type,omitempty"`
	// 认证链接
	AuthUrl string `protobuf:"bytes,5,opt,name=auth_url,json=authUrl,proto3" json:"auth_url,omitempty"`
}

func (x *QueryCertInfoRsp) Reset() {
	*x = QueryCertInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCertInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCertInfoRsp) ProtoMessage() {}

func (x *QueryCertInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCertInfoRsp.ProtoReflect.Descriptor instead.
func (*QueryCertInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{74}
}

func (x *QueryCertInfoRsp) GetAuthCode() int32 {
	if x != nil {
		return x.AuthCode
	}
	return 0
}

func (x *QueryCertInfoRsp) GetAuthMessage() string {
	if x != nil {
		return x.AuthMessage
	}
	return ""
}

func (x *QueryCertInfoRsp) GetIsRealName() bool {
	if x != nil {
		return x.IsRealName
	}
	return false
}

func (x *QueryCertInfoRsp) GetAdultType() AdultType {
	if x != nil {
		return x.AdultType
	}
	return AdultType_AdultTypeUnknown
}

func (x *QueryCertInfoRsp) GetAuthUrl() string {
	if x != nil {
		return x.AuthUrl
	}
	return ""
}

type ReportEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string            `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`              // app id
	OpenId    string            `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`           // 单个用户事件的时候需要填写
	EventType uint32            `protobuf:"varint,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"` // 事件类型 event.TmeEventType
	EventId   string            `protobuf:"bytes,4,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`        // 事件ID
	Room      *event.RoomBase   `protobuf:"bytes,5,opt,name=room,proto3" json:"room,omitempty"`                             // 房间基础信息
	Ts        uint32            `protobuf:"varint,6,opt,name=ts,proto3" json:"ts,omitempty"`                                // 时间时间戳
	ToOpenId  string            `protobuf:"bytes,7,opt,name=to_open_id,json=toOpenId,proto3" json:"to_open_id,omitempty"`   // 动作朝向 open_id
	EventInfo string            `protobuf:"bytes,10,opt,name=event_info,json=eventInfo,proto3" json:"event_info,omitempty"`
	MapExt    map[string]string `protobuf:"bytes,11,rep,name=map_ext,json=mapExt,proto3" json:"map_ext,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 过滤信息 && 额外设置信息
}

func (x *ReportEventReq) Reset() {
	*x = ReportEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportEventReq) ProtoMessage() {}

func (x *ReportEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportEventReq.ProtoReflect.Descriptor instead.
func (*ReportEventReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{75}
}

func (x *ReportEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportEventReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReportEventReq) GetEventType() uint32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *ReportEventReq) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *ReportEventReq) GetRoom() *event.RoomBase {
	if x != nil {
		return x.Room
	}
	return nil
}

func (x *ReportEventReq) GetTs() uint32 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *ReportEventReq) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *ReportEventReq) GetEventInfo() string {
	if x != nil {
		return x.EventInfo
	}
	return ""
}

func (x *ReportEventReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type ReportEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportEventRsp) Reset() {
	*x = ReportEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportEventRsp) ProtoMessage() {}

func (x *ReportEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportEventRsp.ProtoReflect.Descriptor instead.
func (*ReportEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{76}
}

type QueryFriendsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    int64  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                     // uid
	AppId  string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`     // 小游戏AppId
	Mask   uint32 `protobuf:"varint,3,opt,name=mask,proto3" json:"mask,omitempty"`                   // 0x1表示双向关注 0x2表示绑定好友
	PlatId int32  `protobuf:"varint,4,opt,name=plat_id,json=platId,proto3" json:"plat_id,omitempty"` // platid
}

func (x *QueryFriendsReq) Reset() {
	*x = QueryFriendsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryFriendsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryFriendsReq) ProtoMessage() {}

func (x *QueryFriendsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryFriendsReq.ProtoReflect.Descriptor instead.
func (*QueryFriendsReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{77}
}

func (x *QueryFriendsReq) GetUid() int64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *QueryFriendsReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryFriendsReq) GetMask() uint32 {
	if x != nil {
		return x.Mask
	}
	return 0
}

func (x *QueryFriendsReq) GetPlatId() int32 {
	if x != nil {
		return x.PlatId
	}
	return 0
}

type QueryFriendsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UidList []int64 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
}

func (x *QueryFriendsRsp) Reset() {
	*x = QueryFriendsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryFriendsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryFriendsRsp) ProtoMessage() {}

func (x *QueryFriendsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryFriendsRsp.ProtoReflect.Descriptor instead.
func (*QueryFriendsRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{78}
}

func (x *QueryFriendsRsp) GetUidList() []int64 {
	if x != nil {
		return x.UidList
	}
	return nil
}

type QueryBlacklistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vuid     int64   `protobuf:"varint,1,opt,name=vuid,proto3" json:"vuid,omitempty"`                                // uid
	AppId    string  `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                  // 小游戏AppId
	HuidList []int64 `protobuf:"varint,3,rep,packed,name=huid_list,json=huidList,proto3" json:"huid_list,omitempty"` // 待验证列表
	// op=0: 验证vuid是否在huidlist的黑名单当中，blacklist为在其黑名单当中的uid列表
	// op=1: 验证huidlist是否在vuid的黑名单当中，blacklist为在其黑名单当中的uid列表
	Op     int32 `protobuf:"varint,4,opt,name=op,proto3" json:"op,omitempty"`
	PlatId int32 `protobuf:"varint,5,opt,name=plat_id,json=platId,proto3" json:"plat_id,omitempty"` // platid
}

func (x *QueryBlacklistReq) Reset() {
	*x = QueryBlacklistReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryBlacklistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryBlacklistReq) ProtoMessage() {}

func (x *QueryBlacklistReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryBlacklistReq.ProtoReflect.Descriptor instead.
func (*QueryBlacklistReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{79}
}

func (x *QueryBlacklistReq) GetVuid() int64 {
	if x != nil {
		return x.Vuid
	}
	return 0
}

func (x *QueryBlacklistReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryBlacklistReq) GetHuidList() []int64 {
	if x != nil {
		return x.HuidList
	}
	return nil
}

func (x *QueryBlacklistReq) GetOp() int32 {
	if x != nil {
		return x.Op
	}
	return 0
}

func (x *QueryBlacklistReq) GetPlatId() int32 {
	if x != nil {
		return x.PlatId
	}
	return 0
}

type QueryBlacklistRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UidList []int64 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
}

func (x *QueryBlacklistRsp) Reset() {
	*x = QueryBlacklistRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryBlacklistRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryBlacklistRsp) ProtoMessage() {}

func (x *QueryBlacklistRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryBlacklistRsp.ProtoReflect.Descriptor instead.
func (*QueryBlacklistRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{80}
}

func (x *QueryBlacklistRsp) GetUidList() []int64 {
	if x != nil {
		return x.UidList
	}
	return nil
}

type PresentReq_AssetItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	Amount  uint32 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *PresentReq_AssetItems) Reset() {
	*x = PresentReq_AssetItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresentReq_AssetItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresentReq_AssetItems) ProtoMessage() {}

func (x *PresentReq_AssetItems) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresentReq_AssetItems.ProtoReflect.Descriptor instead.
func (*PresentReq_AssetItems) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{6, 0}
}

func (x *PresentReq_AssetItems) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *PresentReq_AssetItems) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type SendSingleRewardReq_ConsumeAssetItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	Amount  uint32 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *SendSingleRewardReq_ConsumeAssetItem) Reset() {
	*x = SendSingleRewardReq_ConsumeAssetItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_adapter_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSingleRewardReq_ConsumeAssetItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSingleRewardReq_ConsumeAssetItem) ProtoMessage() {}

func (x *SendSingleRewardReq_ConsumeAssetItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_adapter_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSingleRewardReq_ConsumeAssetItem.ProtoReflect.Descriptor instead.
func (*SendSingleRewardReq_ConsumeAssetItem) Descriptor() ([]byte, []int) {
	return file_pb_adapter_adapter_proto_rawDescGZIP(), []int{29, 0}
}

func (x *SendSingleRewardReq_ConsumeAssetItem) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *SendSingleRewardReq_ConsumeAssetItem) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

var File_pb_adapter_adapter_proto protoreflect.FileDescriptor

var file_pb_adapter_adapter_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x61,
	0x70, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x16, 0x70, 0x62, 0x2f, 0x61,
	0x62, 0x74, 0x65, 0x73, 0x74, 0x2f, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18,
	0x70, 0x62, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x70,
	0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x62, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x13, 0x66, 0x72, 0x65, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xe9, 0x03, 0x0a, 0x0d, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x0a, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x52, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x12,
	0x41, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x2e, 0x4d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x5f, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x73, 0x79, 0x73, 0x54, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x51, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x73, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x67,
	0x12, 0x15, 0x0a, 0x06, 0x73, 0x79, 0x73, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x73, 0x79, 0x73, 0x54, 0x73, 0x22, 0xd0, 0x04, 0x0a, 0x06, 0x50, 0x61, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x52, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x0a, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x2b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4d, 0x69, 0x64, 0x61, 0x73, 0x52, 0x05, 0x6d, 0x69, 0x64, 0x61, 0x73, 0x12,
	0x3a, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x69, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x69, 0x67, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x1a,
	0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3b, 0x0a, 0x06, 0x50, 0x61,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xb0, 0x02, 0x0a, 0x0a, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70,
	0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x46, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x0a, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x3f, 0x0a, 0x0a, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3f, 0x0a, 0x0a, 0x50, 0x72,
	0x65, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c,
	0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xb4, 0x02, 0x0a, 0x07,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x65,
	0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x55, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x70, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x76, 0x69, 0x70,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x61, 0x77,
	0x5f, 0x75, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x72, 0x61, 0x77, 0x55,
	0x69, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x4c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x22, 0x42, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x31, 0x0a,
	0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x22, 0x95, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x6c, 0x65, 0x6e, 0x67, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x57, 0x69, 0x64, 0x74, 0x68, 0x22, 0xb8, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x4c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x1a, 0x54, 0x0a,
	0x0d, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xef, 0x02, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x2e, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x77, 0x65, 0x63, 0x68, 0x61,
	0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x63, 0x68,
	0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x0d, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69,
	0x6c, 0x52, 0x73, 0x70, 0x22, 0x6b, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c,
	0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e,
	0x6f, 0x22, 0x5e, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x47, 0x69, 0x66, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49,
	0x64, 0x22, 0x3b, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a,
	0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x22, 0xa4,
	0x01, 0x0a, 0x0f, 0x54, 0x44, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x62, 0x75, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x75, 0x73, 0x73, 0x49, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x54, 0x44, 0x42, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0xa9, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x46, 0x72, 0x65, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75,
	0x6d, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x72, 0x6f, 0x6d, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x46,
	0x72, 0x65, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x73, 0x70, 0x22, 0xf6, 0x02, 0x0a, 0x0a, 0x52,
	0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x73, 0x67,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x73, 0x67,
	0x54, 0x65, 0x78, 0x74, 0x12, 0x36, 0x0a, 0x08, 0x6d, 0x73, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x63, 0x32, 0x63, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x43, 0x32, 0x63, 0x12, 0x42, 0x0a, 0x08, 0x65, 0x78,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x2e, 0x45, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x78, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17,
	0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x3a, 0x0a, 0x0c, 0x45, 0x78, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x0c, 0x0a, 0x0a, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x73,
	0x70, 0x22, 0x9b, 0x02, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x06, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e,
	0x64, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x12, 0x24, 0x0a, 0x0e,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x0d, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x52, 0x73, 0x70, 0x22, 0xa7,
	0x02, 0x0a, 0x0d, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x41, 0x0a, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71,
	0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x48, 0x35, 0x5f,
	0x55, 0x52, 0x4c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x48,
	0x35, 0x55, 0x52, 0x4c, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x1a, 0x39, 0x0a,
	0x0b, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x0f, 0x0a, 0x0d, 0x42, 0x69, 0x67, 0x48,
	0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x22, 0x84, 0x01, 0x0a, 0x10, 0x53, 0x69,
	0x6e, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17,
	0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x12,
	0x1f, 0x0a, 0x0b, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0x96, 0x03, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f,
	0x6e, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f,
	0x12, 0x41, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x57, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53,
	0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x24, 0x0a, 0x0e,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x69, 0x6c, 0x6c,
	0x4e, 0x6f, 0x1a, 0x45, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x15, 0x0a, 0x13, 0x53, 0x65, 0x6e,
	0x64, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70,
	0x22, 0x77, 0x0a, 0x0f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x69, 0x66, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x67, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xce, 0x02, 0x0a, 0x0d, 0x53, 0x75,
	0x62, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x0a, 0x0b, 0x73,
	0x75, 0x62, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x73,
	0x75, 0x62, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74, 0x4e, 0x75,
	0x6d, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x67, 0x69, 0x66,
	0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75,
	0x62, 0x47, 0x69, 0x66, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x2d, 0x0a, 0x13, 0x73, 0x75, 0x62,
	0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74, 0x55,
	0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x73, 0x75, 0x62, 0x5f,
	0x67, 0x69, 0x66, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x73, 0x75, 0x62,
	0x5f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x73, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x4f, 0x0a, 0x0f, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x3c, 0x0a,
	0x0a, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x61, 0x72, 0x72, 0x61, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x41, 0x72, 0x72, 0x61, 0x79, 0x22, 0x46, 0x0a, 0x0a, 0x50,
	0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x70, 0x5f, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x4c,
	0x6f, 0x67, 0x6f, 0x22, 0x73, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x70, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12,
	0x40, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73,
	0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x1a, 0x53, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x65, 0x0a, 0x0a, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x73, 0x0a,
	0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c,
	0x61, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x69, 0x66, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x69,
	0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x53, 0x0a, 0x09, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x8f, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x6c, 0x61, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c,
	0x61, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x2d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x22, 0x9e, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x56, 0x32, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c,
	0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e,
	0x6f, 0x12, 0x2f, 0x0a, 0x05, 0x67, 0x69, 0x66, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x05, 0x67, 0x69, 0x66,
	0x74, 0x73, 0x22, 0x0c, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x56, 0x32, 0x52, 0x73, 0x70,
	0x22, 0x67, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0xf4, 0x01, 0x0a, 0x0c, 0x53,
	0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x0a, 0x74,
	0x6f, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x61, 0x66,
	0x65, 0x5f, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73,
	0x61, 0x66, 0x65, 0x41, 0x70, 0x70, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x76, 0x34, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x76, 0x34, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x2a, 0x0a, 0x0c, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73,
	0x70, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x61, 0x66, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x61, 0x66, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa2, 0x01,
	0x0a, 0x0c, 0x51, 0x7a, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x3a,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x7a,
	0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65,
	0x77, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x6e, 0x65, 0x77, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x0e, 0x0a, 0x0c, 0x51, 0x7a, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x73, 0x70, 0x22, 0x2d, 0x0a, 0x0d, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x22, 0x8e, 0x01, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x6e, 0x63, 0x68, 0x6f,
	0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x16, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xbe, 0x01, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41,
	0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x48, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x56, 0x0a, 0x09, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x6e, 0x6f, 0x6e, 0x79,
	0x6d, 0x6f, 0x75, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x7e, 0x0a, 0x1a, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73, 0x67, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x77,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x1c, 0x0a, 0x1a, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73, 0x67, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73,
	0x70, 0x22, 0xd3, 0x01, 0x0a, 0x09, 0x48, 0x69, 0x70, 0x70, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x73, 0x67, 0x5f, 0x74, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x73, 0x67, 0x54, 0x78, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x74, 0x6e, 0x5f,
	0x74, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x74, 0x6e, 0x54, 0x78,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x62, 0x67, 0x5f, 0x70, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x62, 0x67, 0x50, 0x69, 0x63, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x74, 0x6e, 0x5f,
	0x70, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x74, 0x6e, 0x50, 0x69,
	0x63, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x22, 0xdf, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64,
	0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72,
	0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f,
	0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x69, 0x73, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x38, 0x0a, 0x0a, 0x68, 0x69, 0x70, 0x70, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x69, 0x70, 0x70, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x68, 0x69, 0x70, 0x70, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x11, 0x0a, 0x0f, 0x53, 0x65, 0x6e,
	0x64, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x22, 0xa0, 0x02, 0x0a,
	0x0b, 0x55, 0x49, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x64,
	0x65, 0x76, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64,
	0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72,
	0x5f, 0x73, 0x76, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x53, 0x76, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x62, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x22,
	0xd4, 0x01, 0x0a, 0x0b, 0x55, 0x49, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x50, 0x0a, 0x0d, 0x6d, 0x61, 0x70, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x49, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x54, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x54, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0x57, 0x0a,
	0x10, 0x4d, 0x61, 0x70, 0x54, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x69, 0x41, 0x62,
	0x74, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf4, 0x02, 0x0a, 0x09, 0x41, 0x42, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x71, 0x75, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x76, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x73, 0x76, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x53,
	0x76, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x41, 0x62, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x50, 0x0a,
	0x0e, 0x41, 0x62, 0x74, 0x65, 0x73, 0x74, 0x50, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x20, 0x0a, 0x0c, 0x75, 0x69, 0x64, 0x5f, 0x62, 0x61, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x69, 0x64, 0x42, 0x61, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x75, 0x69, 0x64, 0x5f, 0x62, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x69, 0x64, 0x42, 0x61, 0x67, 0x49, 0x64, 0x22,
	0xad, 0x02, 0x0a, 0x0d, 0x41, 0x62, 0x74, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x6d, 0x61, 0x70,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41,
	0x62, 0x74, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x2e, 0x4d, 0x61, 0x70,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x70,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x5f, 0x61, 0x62,
	0x74, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x41,
	0x62, 0x74, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x62, 0x74, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x1a, 0x3c, 0x0a, 0x0e, 0x4d, 0x61, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xd2, 0x01, 0x0a, 0x09, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a,
	0x0d, 0x6d, 0x61, 0x70, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e,
	0x4d, 0x61, 0x70, 0x54, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6d, 0x61, 0x70, 0x54, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x62, 0x74, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x62, 0x74, 0x65, 0x73, 0x74, 0x1a, 0x5d, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x54, 0x65, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x62, 0x74, 0x65,
	0x73, 0x74, 0x52, 0x73, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x81, 0x03, 0x0a, 0x11, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a,
	0x0a, 0x74, 0x6f, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x07, 0x6d, 0x61,
	0x70, 0x5f, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x2e, 0x4d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71,
	0x75, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61, 0x1a, 0x39, 0x0a,
	0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x53, 0x0a, 0x1a, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x04, 0x62, 0x69, 0x6c, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x04, 0x62, 0x69, 0x6c, 0x6c, 0x22, 0x1c, 0x0a,
	0x1a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x73, 0x70, 0x22, 0xc5, 0x01, 0x0a, 0x0c,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75, 0x61,
	0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x31, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x5b, 0x0a, 0x0a, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x34, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x22, 0xab, 0x01, 0x0a, 0x0c, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x43, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x56, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x95,
	0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x4d, 0x61, 0x73, 0x6b,
	0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x22, 0x59, 0x0a, 0x0c, 0x52, 0x61, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x22, 0xbd, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x09, 0x72, 0x61, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x61, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x72, 0x61, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x5a, 0x0a, 0x0e, 0x52, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x61, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xb7, 0x02, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x06, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x65, 0x72,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x12, 0x3c, 0x0a, 0x0a, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x1a, 0x39, 0x0a, 0x0b, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc9, 0x01, 0x0a, 0x10,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0a, 0x61, 0x64, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x61, 0x64, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x75, 0x74, 0x68, 0x55, 0x72, 0x6c, 0x22, 0xec, 0x02, 0x0a, 0x0e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x0a, 0x74, 0x6f, 0x5f,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x5f, 0x65, 0x78,
	0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x10, 0x0a, 0x0e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x67, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x49,
	0x64, 0x22, 0x2c, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x75, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x84, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x76, 0x75, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x68, 0x75, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x08, 0x68, 0x75, 0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x6f, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x70, 0x6c, 0x61, 0x74, 0x49, 0x64, 0x22, 0x2e, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42,
	0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x75,
	0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x75,
	0x69, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x2a, 0x3b, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x11, 0x0a, 0x0d, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4d, 0x61, 0x6e,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x57, 0x6f, 0x6d, 0x61,
	0x6e, 0x10, 0x02, 0x2a, 0x32, 0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73,
	0x67, 0x54, 0x65, 0x78, 0x74, 0x10, 0x01, 0x2a, 0x30, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x74, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x4c, 0x41, 0x54,
	0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4b, 0x47,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x10, 0x01, 0x2a, 0x4f, 0x0a, 0x0d, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x4f,
	0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x10, 0x02, 0x2a, 0x3a, 0x0a, 0x0c, 0x46, 0x6f,
	0x6c, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x4f,
	0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x2a, 0x3e, 0x0a, 0x0b, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4f,
	0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x2a, 0x36, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x4c,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x2a, 0x4a,
	0x0a, 0x0d, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x11, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x02, 0x2a, 0x48, 0x0a, 0x09, 0x41, 0x64,
	0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x64, 0x75, 0x6c, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0e, 0x41, 0x64, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x75, 0x64, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x54, 0x72,
	0x75, 0x65, 0x10, 0x02, 0x32, 0xc2, 0x15, 0x0a, 0x07, 0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x12, 0x4a, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x1d,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0a,
	0x50, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x03, 0x50, 0x61, 0x79, 0x12,
	0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x79, 0x52, 0x73, 0x70, 0x12,
	0x41, 0x0a, 0x07, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x44,
	0x0a, 0x08, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69,
	0x6c, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x05, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50,
	0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x50, 0x0a, 0x0c, 0x54, 0x44, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x54, 0x44, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x54, 0x44, 0x42, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x59,
	0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x46, 0x72, 0x65, 0x65, 0x47, 0x69, 0x66,
	0x74, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x46, 0x72, 0x65, 0x65, 0x47, 0x69,
	0x66, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x46, 0x72,
	0x65, 0x65, 0x47, 0x69, 0x66, 0x74, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x07, 0x52, 0x6f, 0x6f,
	0x6d, 0x4d, 0x73, 0x67, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x08,
	0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75,
	0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x52,
	0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0a, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67,
	0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x5c,
	0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x0c,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1f, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x59,
	0x0a, 0x0f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x0f, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x22, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x07,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x56, 0x32, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x56, 0x32, 0x52, 0x73, 0x70, 0x12,
	0x4a, 0x0a, 0x0a, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x09, 0x53,
	0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x09, 0x51, 0x7a, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x51, 0x7a, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x7a, 0x61, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x71, 0x0a,
	0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41, 0x6e,
	0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x71, 0x0a, 0x17, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x69, 0x70,
	0x70, 0x79, 0x4d, 0x73, 0x67, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73,
	0x67, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x69, 0x70, 0x70, 0x79,
	0x4d, 0x73, 0x67, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d, 0x73,
	0x67, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x69, 0x70, 0x70, 0x79, 0x4d,
	0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x08, 0x55, 0x49, 0x41, 0x42, 0x54, 0x65, 0x73,
	0x74, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x55, 0x49, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x55, 0x49, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x06, 0x41,
	0x42, 0x54, 0x65, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x41, 0x42, 0x54, 0x65, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x71, 0x0a, 0x17, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x69, 0x6c, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x47,
	0x0a, 0x09, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x12, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x0d,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x12, 0x4d, 0x0a, 0x0b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x4f, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x12,
	0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x56, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x6c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x6c, 0x61,
	0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x42, 0x3f, 0x5a, 0x3d, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_adapter_adapter_proto_rawDescOnce sync.Once
	file_pb_adapter_adapter_proto_rawDescData = file_pb_adapter_adapter_proto_rawDesc
)

func file_pb_adapter_adapter_proto_rawDescGZIP() []byte {
	file_pb_adapter_adapter_proto_rawDescOnce.Do(func() {
		file_pb_adapter_adapter_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_adapter_proto_rawDescData)
	})
	return file_pb_adapter_adapter_proto_rawDescData
}

var file_pb_adapter_adapter_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_pb_adapter_adapter_proto_msgTypes = make([]protoimpl.MessageInfo, 102)
var file_pb_adapter_adapter_proto_goTypes = []interface{}{
	(Gender)(0),                                  // 0: component.game.Gender
	(RoomMsgType)(0),                             // 1: component.game.RoomMsgType
	(PlatAssetType)(0),                           // 2: component.game.PlatAssetType
	(FollowOptType)(0),                           // 3: component.game.FollowOptType
	(FollowResult)(0),                            // 4: component.game.FollowResult
	(RelatioMask)(0),                             // 5: component.game.RelatioMask
	(RelationType)(0),                            // 6: component.game.RelationType
	(GameCheckType)(0),                           // 7: component.game.GameCheckType
	(AdultType)(0),                               // 8: component.game.AdultType
	(*GetBalanceReq)(nil),                        // 9: component.game.GetBalanceReq
	(*GetBalanceRsp)(nil),                        // 10: component.game.GetBalanceRsp
	(*PlaceOrderReq)(nil),                        // 11: component.game.PlaceOrderReq
	(*PlaceOrderRsp)(nil),                        // 12: component.game.PlaceOrderRsp
	(*PayReq)(nil),                               // 13: component.game.PayReq
	(*PayRsp)(nil),                               // 14: component.game.PayRsp
	(*PresentReq)(nil),                           // 15: component.game.PresentReq
	(*PresentRsp)(nil),                           // 16: component.game.PresentRsp
	(*Profile)(nil),                              // 17: component.game.Profile
	(*GetProfileReq)(nil),                        // 18: component.game.GetProfileReq
	(*GetProfileRsp)(nil),                        // 19: component.game.GetProfileRsp
	(*BatchGetProfileReq)(nil),                   // 20: component.game.BatchGetProfileReq
	(*BatchGetProfileRsp)(nil),                   // 21: component.game.BatchGetProfileRsp
	(*SendMailReq)(nil),                          // 22: component.game.SendMailReq
	(*SendMailRsp)(nil),                          // 23: component.game.SendMailRsp
	(*PrizeReq)(nil),                             // 24: component.game.PrizeReq
	(*PrizeGift)(nil),                            // 25: component.game.PrizeGift
	(*PrizeRsp)(nil),                             // 26: component.game.PrizeRsp
	(*TDBankReportReq)(nil),                      // 27: component.game.TDBankReportReq
	(*TDBankReportRsp)(nil),                      // 28: component.game.TDBankReportRsp
	(*ConsumeFreeGiftReq)(nil),                   // 29: component.game.ConsumeFreeGiftReq
	(*ConsumeFreeGiftRsp)(nil),                   // 30: component.game.ConsumeFreeGiftRsp
	(*RoomMsgReq)(nil),                           // 31: component.game.RoomMsgReq
	(*RoomMsgRsp)(nil),                           // 32: component.game.RoomMsgRsp
	(*SendPushReq)(nil),                          // 33: component.game.SendPushReq
	(*SendPushRsp)(nil),                          // 34: component.game.SendPushRsp
	(*BigHornMsgReq)(nil),                        // 35: component.game.BigHornMsgReq
	(*BigHornMsgRsp)(nil),                        // 36: component.game.BigHornMsgRsp
	(*SingleRewardItem)(nil),                     // 37: component.game.SingleRewardItem
	(*SendSingleRewardReq)(nil),                  // 38: component.game.SendSingleRewardReq
	(*SendSingleRewardRsp)(nil),                  // 39: component.game.SendSingleRewardRsp
	(*RewardDetailReq)(nil),                      // 40: component.game.RewardDetailReq
	(*SubGiftDetail)(nil),                        // 41: component.game.SubGiftDetail
	(*RewardDetailRsp)(nil),                      // 42: component.game.RewardDetailRsp
	(*PropDetail)(nil),                           // 43: component.game.PropDetail
	(*BatchPropDetailReq)(nil),                   // 44: component.game.BatchPropDetailReq
	(*BatchPropDetailRsp)(nil),                   // 45: component.game.BatchPropDetailRsp
	(*GiftDetail)(nil),                           // 46: component.game.GiftDetail
	(*BatchGiftDetailReq)(nil),                   // 47: component.game.BatchGiftDetailReq
	(*BatchGiftDetailRsp)(nil),                   // 48: component.game.BatchGiftDetailRsp
	(*GetPlatBalanceReq)(nil),                    // 49: component.game.GetPlatBalanceReq
	(*GetPlatBalanceRsp)(nil),                    // 50: component.game.GetPlatBalanceRsp
	(*PrizeV2Req)(nil),                           // 51: component.game.PrizeV2Req
	(*PrizeV2Rsp)(nil),                           // 52: component.game.PrizeV2Rsp
	(*DataReportReq)(nil),                        // 53: component.game.DataReportReq
	(*DataReportRsp)(nil),                        // 54: component.game.DataReportRsp
	(*SafeCheckReq)(nil),                         // 55: component.game.SafeCheckReq
	(*SafeCheckRsp)(nil),                         // 56: component.game.SafeCheckRsp
	(*QzaReportReq)(nil),                         // 57: component.game.QzaReportReq
	(*QzaReportRsp)(nil),                         // 58: component.game.QzaReportRsp
	(*AnonymousItem)(nil),                        // 59: component.game.AnonymousItem
	(*BatchGetAnonymousStatusReq)(nil),           // 60: component.game.BatchGetAnonymousStatusReq
	(*BatchGetAnonymousStatusRsp)(nil),           // 61: component.game.BatchGetAnonymousStatusRsp
	(*HippyMsgUserSwitchEventReq)(nil),           // 62: component.game.HippyMsgUserSwitchEventReq
	(*HippyMsgUserSwitchEventRsp)(nil),           // 63: component.game.HippyMsgUserSwitchEventRsp
	(*HippyInfo)(nil),                            // 64: component.game.HippyInfo
	(*SendHippyMsgReq)(nil),                      // 65: component.game.SendHippyMsgReq
	(*SendHippyMsgRsp)(nil),                      // 66: component.game.SendHippyMsgRsp
	(*UIABTestReq)(nil),                          // 67: component.game.UIABTestReq
	(*UIABTestRsp)(nil),                          // 68: component.game.UIABTestRsp
	(*ABTestReq)(nil),                            // 69: component.game.ABTestReq
	(*AbtestPassback)(nil),                       // 70: component.game.AbtestPassback
	(*AbtestRspItem)(nil),                        // 71: component.game.AbtestRspItem
	(*ABTestRsp)(nil),                            // 72: component.game.ABTestRsp
	(*TaskConditionBill)(nil),                    // 73: component.game.TaskConditionBill
	(*ReportTaskConditionBillReq)(nil),           // 74: component.game.ReportTaskConditionBillReq
	(*ReportTaskConditionBillRsp)(nil),           // 75: component.game.ReportTaskConditionBillRsp
	(*FollowOptReq)(nil),                         // 76: component.game.FollowOptReq
	(*FollowInfo)(nil),                           // 77: component.game.FollowInfo
	(*FollowOptRsp)(nil),                         // 78: component.game.FollowOptRsp
	(*QueryRelationReq)(nil),                     // 79: component.game.QueryRelationReq
	(*RalationInfo)(nil),                         // 80: component.game.RalationInfo
	(*QueryRelationRsp)(nil),                     // 81: component.game.QueryRelationRsp
	(*QueryCertInfoReq)(nil),                     // 82: component.game.QueryCertInfoReq
	(*QueryCertInfoRsp)(nil),                     // 83: component.game.QueryCertInfoRsp
	(*ReportEventReq)(nil),                       // 84: component.game.ReportEventReq
	(*ReportEventRsp)(nil),                       // 85: component.game.ReportEventRsp
	(*QueryFriendsReq)(nil),                      // 86: component.game.QueryFriendsReq
	(*QueryFriendsRsp)(nil),                      // 87: component.game.QueryFriendsRsp
	(*QueryBlacklistReq)(nil),                    // 88: component.game.QueryBlacklistReq
	(*QueryBlacklistRsp)(nil),                    // 89: component.game.QueryBlacklistRsp
	nil,                                          // 90: component.game.PlaceOrderReq.MapExtEntry
	nil,                                          // 91: component.game.PayReq.MapExtEntry
	(*PresentReq_AssetItems)(nil),                // 92: component.game.PresentReq.AssetItems
	nil,                                          // 93: component.game.BatchGetProfileRsp.ProfilesEntry
	nil,                                          // 94: component.game.SendMailReq.AttachmentEntry
	nil,                                          // 95: component.game.RoomMsgReq.ExtDataEntry
	nil,                                          // 96: component.game.SendPushReq.AttachEntry
	nil,                                          // 97: component.game.BigHornMsgReq.AttachEntry
	(*SendSingleRewardReq_ConsumeAssetItem)(nil), // 98: component.game.SendSingleRewardReq.ConsumeAssetItem
	nil,                            // 99: component.game.BatchPropDetailRsp.DataEntry
	nil,                            // 100: component.game.BatchGiftDetailRsp.DataEntry
	nil,                            // 101: component.game.QzaReportReq.DataEntry
	nil,                            // 102: component.game.BatchGetAnonymousStatusRsp.DataEntry
	nil,                            // 103: component.game.UIABTestRsp.MapTestInfoEntry
	nil,                            // 104: component.game.AbtestRspItem.MapParamsEntry
	nil,                            // 105: component.game.ABTestRsp.MapTestInfoEntry
	nil,                            // 106: component.game.TaskConditionBill.MapExtEntry
	nil,                            // 107: component.game.FollowOptRsp.ResultsEntry
	nil,                            // 108: component.game.QueryRelationRsp.RalationsEntry
	nil,                            // 109: component.game.QueryCertInfoReq.CookieEntry
	nil,                            // 110: component.game.ReportEventReq.MapExtEntry
	(*asset.UserAssetChange)(nil),  // 111: component.game.UserAssetChange
	(*openpay.OrderConf)(nil),      // 112: component.game.OrderConf
	(*openpay.Scene)(nil),          // 113: component.game.Scene
	(*openpay.Device)(nil),         // 114: component.game.Device
	(*openpay.Midas)(nil),          // 115: component.game.Midas
	(*abtest.AbtestLabelItem)(nil), // 116: abtest.AbtestLabelItem
	(*event.RoomBase)(nil),         // 117: event.RoomBase
	(*abtest.UiAbtestRspItem)(nil), // 118: abtest.UiAbtestRspItem
}
var file_pb_adapter_adapter_proto_depIdxs = []int32{
	111, // 0: component.game.PlaceOrderReq.assets:type_name -> component.game.UserAssetChange
	112, // 1: component.game.PlaceOrderReq.order_conf:type_name -> component.game.OrderConf
	113, // 2: component.game.PlaceOrderReq.scene:type_name -> component.game.Scene
	114, // 3: component.game.PlaceOrderReq.device:type_name -> component.game.Device
	115, // 4: component.game.PlaceOrderReq.midas:type_name -> component.game.Midas
	90,  // 5: component.game.PlaceOrderReq.mapExt:type_name -> component.game.PlaceOrderReq.MapExtEntry
	111, // 6: component.game.PayReq.assets:type_name -> component.game.UserAssetChange
	112, // 7: component.game.PayReq.order_conf:type_name -> component.game.OrderConf
	113, // 8: component.game.PayReq.scene:type_name -> component.game.Scene
	114, // 9: component.game.PayReq.device:type_name -> component.game.Device
	115, // 10: component.game.PayReq.midas:type_name -> component.game.Midas
	91,  // 11: component.game.PayReq.mapExt:type_name -> component.game.PayReq.MapExtEntry
	92,  // 12: component.game.PresentReq.asset_items:type_name -> component.game.PresentReq.AssetItems
	0,   // 13: component.game.Profile.gender:type_name -> component.game.Gender
	17,  // 14: component.game.GetProfileRsp.profile:type_name -> component.game.Profile
	93,  // 15: component.game.BatchGetProfileRsp.profiles:type_name -> component.game.BatchGetProfileRsp.ProfilesEntry
	94,  // 16: component.game.SendMailReq.attachment:type_name -> component.game.SendMailReq.AttachmentEntry
	25,  // 17: component.game.PrizeRsp.gifts:type_name -> component.game.PrizeGift
	1,   // 18: component.game.RoomMsgReq.msg_type:type_name -> component.game.RoomMsgType
	95,  // 19: component.game.RoomMsgReq.ext_data:type_name -> component.game.RoomMsgReq.ExtDataEntry
	96,  // 20: component.game.SendPushReq.attach:type_name -> component.game.SendPushReq.AttachEntry
	97,  // 21: component.game.BigHornMsgReq.attach:type_name -> component.game.BigHornMsgReq.AttachEntry
	37,  // 22: component.game.SendSingleRewardReq.reward_item:type_name -> component.game.SingleRewardItem
	98,  // 23: component.game.SendSingleRewardReq.consume_item:type_name -> component.game.SendSingleRewardReq.ConsumeAssetItem
	41,  // 24: component.game.RewardDetailRsp.gift_array:type_name -> component.game.SubGiftDetail
	99,  // 25: component.game.BatchPropDetailRsp.data:type_name -> component.game.BatchPropDetailRsp.DataEntry
	100, // 26: component.game.BatchGiftDetailRsp.data:type_name -> component.game.BatchGiftDetailRsp.DataEntry
	25,  // 27: component.game.PrizeV2Req.gifts:type_name -> component.game.PrizeGift
	101, // 28: component.game.QzaReportReq.data:type_name -> component.game.QzaReportReq.DataEntry
	102, // 29: component.game.BatchGetAnonymousStatusRsp.data:type_name -> component.game.BatchGetAnonymousStatusRsp.DataEntry
	64,  // 30: component.game.SendHippyMsgReq.hippy_info:type_name -> component.game.HippyInfo
	116, // 31: component.game.UIABTestReq.labels:type_name -> abtest.AbtestLabelItem
	103, // 32: component.game.UIABTestRsp.map_test_info:type_name -> component.game.UIABTestRsp.MapTestInfoEntry
	116, // 33: component.game.ABTestReq.labels:type_name -> abtest.AbtestLabelItem
	104, // 34: component.game.AbtestRspItem.map_params:type_name -> component.game.AbtestRspItem.MapParamsEntry
	70,  // 35: component.game.AbtestRspItem.passback:type_name -> component.game.AbtestPassback
	105, // 36: component.game.ABTestRsp.map_test_info:type_name -> component.game.ABTestRsp.MapTestInfoEntry
	106, // 37: component.game.TaskConditionBill.map_ext:type_name -> component.game.TaskConditionBill.MapExtEntry
	73,  // 38: component.game.ReportTaskConditionBillReq.bill:type_name -> component.game.TaskConditionBill
	3,   // 39: component.game.FollowOptReq.type:type_name -> component.game.FollowOptType
	4,   // 40: component.game.FollowInfo.result:type_name -> component.game.FollowResult
	107, // 41: component.game.FollowOptRsp.results:type_name -> component.game.FollowOptRsp.ResultsEntry
	5,   // 42: component.game.QueryRelationReq.mask:type_name -> component.game.RelatioMask
	6,   // 43: component.game.RalationInfo.type:type_name -> component.game.RelationType
	108, // 44: component.game.QueryRelationRsp.ralations:type_name -> component.game.QueryRelationRsp.RalationsEntry
	109, // 45: component.game.QueryCertInfoReq.cookie:type_name -> component.game.QueryCertInfoReq.CookieEntry
	7,   // 46: component.game.QueryCertInfoReq.check_type:type_name -> component.game.GameCheckType
	8,   // 47: component.game.QueryCertInfoRsp.adult_type:type_name -> component.game.AdultType
	117, // 48: component.game.ReportEventReq.room:type_name -> event.RoomBase
	110, // 49: component.game.ReportEventReq.map_ext:type_name -> component.game.ReportEventReq.MapExtEntry
	17,  // 50: component.game.BatchGetProfileRsp.ProfilesEntry.value:type_name -> component.game.Profile
	43,  // 51: component.game.BatchPropDetailRsp.DataEntry.value:type_name -> component.game.PropDetail
	46,  // 52: component.game.BatchGiftDetailRsp.DataEntry.value:type_name -> component.game.GiftDetail
	59,  // 53: component.game.BatchGetAnonymousStatusRsp.DataEntry.value:type_name -> component.game.AnonymousItem
	118, // 54: component.game.UIABTestRsp.MapTestInfoEntry.value:type_name -> abtest.UiAbtestRspItem
	71,  // 55: component.game.ABTestRsp.MapTestInfoEntry.value:type_name -> component.game.AbtestRspItem
	77,  // 56: component.game.FollowOptRsp.ResultsEntry.value:type_name -> component.game.FollowInfo
	80,  // 57: component.game.QueryRelationRsp.RalationsEntry.value:type_name -> component.game.RalationInfo
	9,   // 58: component.game.Adapter.GetBalance:input_type -> component.game.GetBalanceReq
	11,  // 59: component.game.Adapter.PlaceOrder:input_type -> component.game.PlaceOrderReq
	13,  // 60: component.game.Adapter.Pay:input_type -> component.game.PayReq
	15,  // 61: component.game.Adapter.Present:input_type -> component.game.PresentReq
	18,  // 62: component.game.Adapter.GetProfile:input_type -> component.game.GetProfileReq
	22,  // 63: component.game.Adapter.SendMail:input_type -> component.game.SendMailReq
	24,  // 64: component.game.Adapter.Prize:input_type -> component.game.PrizeReq
	27,  // 65: component.game.Adapter.TDBankReport:input_type -> component.game.TDBankReportReq
	20,  // 66: component.game.Adapter.BatchGetProfile:input_type -> component.game.BatchGetProfileReq
	29,  // 67: component.game.Adapter.ConsumeFreeGift:input_type -> component.game.ConsumeFreeGiftReq
	31,  // 68: component.game.Adapter.RoomMsg:input_type -> component.game.RoomMsgReq
	33,  // 69: component.game.Adapter.SendPush:input_type -> component.game.SendPushReq
	35,  // 70: component.game.Adapter.BigHornMsg:input_type -> component.game.BigHornMsgReq
	38,  // 71: component.game.Adapter.SendSingleReward:input_type -> component.game.SendSingleRewardReq
	40,  // 72: component.game.Adapter.RewardDetail:input_type -> component.game.RewardDetailReq
	44,  // 73: component.game.Adapter.BatchPropDetail:input_type -> component.game.BatchPropDetailReq
	47,  // 74: component.game.Adapter.BatchGiftDetail:input_type -> component.game.BatchGiftDetailReq
	49,  // 75: component.game.Adapter.GetPlatBalance:input_type -> component.game.GetPlatBalanceReq
	51,  // 76: component.game.Adapter.PrizeV2:input_type -> component.game.PrizeV2Req
	53,  // 77: component.game.Adapter.DataReport:input_type -> component.game.DataReportReq
	55,  // 78: component.game.Adapter.SafeCheck:input_type -> component.game.SafeCheckReq
	57,  // 79: component.game.Adapter.QzaReport:input_type -> component.game.QzaReportReq
	60,  // 80: component.game.Adapter.BatchGetAnonymousStatus:input_type -> component.game.BatchGetAnonymousStatusReq
	62,  // 81: component.game.Adapter.HippyMsgUserSwitchEvent:input_type -> component.game.HippyMsgUserSwitchEventReq
	65,  // 82: component.game.Adapter.SendHippyMsg:input_type -> component.game.SendHippyMsgReq
	67,  // 83: component.game.Adapter.UIABTest:input_type -> component.game.UIABTestReq
	69,  // 84: component.game.Adapter.ABTest:input_type -> component.game.ABTestReq
	74,  // 85: component.game.Adapter.ReportTaskConditionBill:input_type -> component.game.ReportTaskConditionBillReq
	76,  // 86: component.game.Adapter.FollowOpt:input_type -> component.game.FollowOptReq
	79,  // 87: component.game.Adapter.QueryRelation:input_type -> component.game.QueryRelationReq
	82,  // 88: component.game.Adapter.QueryCertInfo:input_type -> component.game.QueryCertInfoReq
	84,  // 89: component.game.Adapter.ReportEvent:input_type -> component.game.ReportEventReq
	86,  // 90: component.game.Adapter.QueryFriend:input_type -> component.game.QueryFriendsReq
	88,  // 91: component.game.Adapter.QueryBlacklist:input_type -> component.game.QueryBlacklistReq
	10,  // 92: component.game.Adapter.GetBalance:output_type -> component.game.GetBalanceRsp
	12,  // 93: component.game.Adapter.PlaceOrder:output_type -> component.game.PlaceOrderRsp
	14,  // 94: component.game.Adapter.Pay:output_type -> component.game.PayRsp
	16,  // 95: component.game.Adapter.Present:output_type -> component.game.PresentRsp
	19,  // 96: component.game.Adapter.GetProfile:output_type -> component.game.GetProfileRsp
	23,  // 97: component.game.Adapter.SendMail:output_type -> component.game.SendMailRsp
	26,  // 98: component.game.Adapter.Prize:output_type -> component.game.PrizeRsp
	28,  // 99: component.game.Adapter.TDBankReport:output_type -> component.game.TDBankReportRsp
	21,  // 100: component.game.Adapter.BatchGetProfile:output_type -> component.game.BatchGetProfileRsp
	30,  // 101: component.game.Adapter.ConsumeFreeGift:output_type -> component.game.ConsumeFreeGiftRsp
	32,  // 102: component.game.Adapter.RoomMsg:output_type -> component.game.RoomMsgRsp
	34,  // 103: component.game.Adapter.SendPush:output_type -> component.game.SendPushRsp
	36,  // 104: component.game.Adapter.BigHornMsg:output_type -> component.game.BigHornMsgRsp
	39,  // 105: component.game.Adapter.SendSingleReward:output_type -> component.game.SendSingleRewardRsp
	42,  // 106: component.game.Adapter.RewardDetail:output_type -> component.game.RewardDetailRsp
	45,  // 107: component.game.Adapter.BatchPropDetail:output_type -> component.game.BatchPropDetailRsp
	48,  // 108: component.game.Adapter.BatchGiftDetail:output_type -> component.game.BatchGiftDetailRsp
	50,  // 109: component.game.Adapter.GetPlatBalance:output_type -> component.game.GetPlatBalanceRsp
	52,  // 110: component.game.Adapter.PrizeV2:output_type -> component.game.PrizeV2Rsp
	54,  // 111: component.game.Adapter.DataReport:output_type -> component.game.DataReportRsp
	56,  // 112: component.game.Adapter.SafeCheck:output_type -> component.game.SafeCheckRsp
	58,  // 113: component.game.Adapter.QzaReport:output_type -> component.game.QzaReportRsp
	61,  // 114: component.game.Adapter.BatchGetAnonymousStatus:output_type -> component.game.BatchGetAnonymousStatusRsp
	63,  // 115: component.game.Adapter.HippyMsgUserSwitchEvent:output_type -> component.game.HippyMsgUserSwitchEventRsp
	66,  // 116: component.game.Adapter.SendHippyMsg:output_type -> component.game.SendHippyMsgRsp
	68,  // 117: component.game.Adapter.UIABTest:output_type -> component.game.UIABTestRsp
	72,  // 118: component.game.Adapter.ABTest:output_type -> component.game.ABTestRsp
	75,  // 119: component.game.Adapter.ReportTaskConditionBill:output_type -> component.game.ReportTaskConditionBillRsp
	78,  // 120: component.game.Adapter.FollowOpt:output_type -> component.game.FollowOptRsp
	81,  // 121: component.game.Adapter.QueryRelation:output_type -> component.game.QueryRelationRsp
	83,  // 122: component.game.Adapter.QueryCertInfo:output_type -> component.game.QueryCertInfoRsp
	85,  // 123: component.game.Adapter.ReportEvent:output_type -> component.game.ReportEventRsp
	87,  // 124: component.game.Adapter.QueryFriend:output_type -> component.game.QueryFriendsRsp
	89,  // 125: component.game.Adapter.QueryBlacklist:output_type -> component.game.QueryBlacklistRsp
	92,  // [92:126] is the sub-list for method output_type
	58,  // [58:92] is the sub-list for method input_type
	58,  // [58:58] is the sub-list for extension type_name
	58,  // [58:58] is the sub-list for extension extendee
	0,   // [0:58] is the sub-list for field type_name
}

func init() { file_pb_adapter_adapter_proto_init() }
func file_pb_adapter_adapter_proto_init() {
	if File_pb_adapter_adapter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_adapter_adapter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresentRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Profile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetProfileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetProfileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrizeGift); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrizeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TDBankReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TDBankReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeFreeGiftReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeFreeGiftRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPushReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPushRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BigHornMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BigHornMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SingleRewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSingleRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSingleRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubGiftDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchPropDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchPropDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGiftDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGiftDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlatBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlatBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrizeV2Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrizeV2Rsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QzaReportReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QzaReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnonymousItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAnonymousStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAnonymousStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HippyMsgUserSwitchEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HippyMsgUserSwitchEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HippyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendHippyMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendHippyMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIABTestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UIABTestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ABTestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbtestPassback); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AbtestRspItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ABTestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskConditionBill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportTaskConditionBillReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportTaskConditionBillRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRelationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RalationInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRelationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCertInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCertInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryFriendsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryFriendsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryBlacklistReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryBlacklistRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresentReq_AssetItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_adapter_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSingleRewardReq_ConsumeAssetItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_adapter_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   102,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_adapter_adapter_proto_goTypes,
		DependencyIndexes: file_pb_adapter_adapter_proto_depIdxs,
		EnumInfos:         file_pb_adapter_adapter_proto_enumTypes,
		MessageInfos:      file_pb_adapter_adapter_proto_msgTypes,
	}.Build()
	File_pb_adapter_adapter_proto = out.File
	file_pb_adapter_adapter_proto_rawDesc = nil
	file_pb_adapter_adapter_proto_goTypes = nil
	file_pb_adapter_adapter_proto_depIdxs = nil
}
