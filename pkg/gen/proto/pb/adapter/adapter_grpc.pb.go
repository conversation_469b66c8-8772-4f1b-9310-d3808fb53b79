// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/adapter/adapter.proto

package adapter

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Adapter_GetBalance_FullMethodName              = "/component.game.Adapter/GetBalance"
	Adapter_PlaceOrder_FullMethodName              = "/component.game.Adapter/PlaceOrder"
	Adapter_Pay_FullMethodName                     = "/component.game.Adapter/Pay"
	Adapter_Present_FullMethodName                 = "/component.game.Adapter/Present"
	Adapter_GetProfile_FullMethodName              = "/component.game.Adapter/GetProfile"
	Adapter_SendMail_FullMethodName                = "/component.game.Adapter/SendMail"
	Adapter_Prize_FullMethodName                   = "/component.game.Adapter/Prize"
	Adapter_TDBankReport_FullMethodName            = "/component.game.Adapter/TDBankReport"
	Adapter_BatchGetProfile_FullMethodName         = "/component.game.Adapter/BatchGetProfile"
	Adapter_ConsumeFreeGift_FullMethodName         = "/component.game.Adapter/ConsumeFreeGift"
	Adapter_RoomMsg_FullMethodName                 = "/component.game.Adapter/RoomMsg"
	Adapter_SendPush_FullMethodName                = "/component.game.Adapter/SendPush"
	Adapter_BigHornMsg_FullMethodName              = "/component.game.Adapter/BigHornMsg"
	Adapter_SendSingleReward_FullMethodName        = "/component.game.Adapter/SendSingleReward"
	Adapter_RewardDetail_FullMethodName            = "/component.game.Adapter/RewardDetail"
	Adapter_BatchPropDetail_FullMethodName         = "/component.game.Adapter/BatchPropDetail"
	Adapter_BatchGiftDetail_FullMethodName         = "/component.game.Adapter/BatchGiftDetail"
	Adapter_GetPlatBalance_FullMethodName          = "/component.game.Adapter/GetPlatBalance"
	Adapter_PrizeV2_FullMethodName                 = "/component.game.Adapter/PrizeV2"
	Adapter_DataReport_FullMethodName              = "/component.game.Adapter/DataReport"
	Adapter_SafeCheck_FullMethodName               = "/component.game.Adapter/SafeCheck"
	Adapter_QzaReport_FullMethodName               = "/component.game.Adapter/QzaReport"
	Adapter_BatchGetAnonymousStatus_FullMethodName = "/component.game.Adapter/BatchGetAnonymousStatus"
	Adapter_HippyMsgUserSwitchEvent_FullMethodName = "/component.game.Adapter/HippyMsgUserSwitchEvent"
	Adapter_SendHippyMsg_FullMethodName            = "/component.game.Adapter/SendHippyMsg"
	Adapter_UIABTest_FullMethodName                = "/component.game.Adapter/UIABTest"
	Adapter_ABTest_FullMethodName                  = "/component.game.Adapter/ABTest"
	Adapter_ReportTaskConditionBill_FullMethodName = "/component.game.Adapter/ReportTaskConditionBill"
	Adapter_FollowOpt_FullMethodName               = "/component.game.Adapter/FollowOpt"
	Adapter_QueryRelation_FullMethodName           = "/component.game.Adapter/QueryRelation"
	Adapter_QueryCertInfo_FullMethodName           = "/component.game.Adapter/QueryCertInfo"
	Adapter_ReportEvent_FullMethodName             = "/component.game.Adapter/ReportEvent"
	Adapter_QueryFriend_FullMethodName             = "/component.game.Adapter/QueryFriend"
	Adapter_QueryBlacklist_FullMethodName          = "/component.game.Adapter/QueryBlacklist"
)

// AdapterClient is the client API for Adapter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterClient interface {
	// 查询游戏币 [接口已迁移至adapter_revenue]
	GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceRsp, error)
	// 下单 [接口已迁移至adapter_revenue]
	PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderRsp, error)
	// 扣除游戏币 [接口已迁移至adapter_revenue]
	Pay(ctx context.Context, in *PayReq, opts ...grpc.CallOption) (*PayRsp, error)
	// 奖励游戏币 [接口已迁移至adapter_revenue]
	Present(ctx context.Context, in *PresentReq, opts ...grpc.CallOption) (*PresentRsp, error)
	// 获取用户资料 [接口已迁移至adapter_user]
	GetProfile(ctx context.Context, in *GetProfileReq, opts ...grpc.CallOption) (*GetProfileRsp, error)
	// 发送私信
	SendMail(ctx context.Context, in *SendMailReq, opts ...grpc.CallOption) (*SendMailRsp, error)
	// 根据指定金额发奖 [接口已迁移至adapter_revenue]
	Prize(ctx context.Context, in *PrizeReq, opts ...grpc.CallOption) (*PrizeRsp, error)
	// 数据上报 [接口已迁移至adapter_data]
	TDBankReport(ctx context.Context, in *TDBankReportReq, opts ...grpc.CallOption) (*TDBankReportRsp, error)
	// 批量获取用户资料 [接口已迁移至adapter_user]
	BatchGetProfile(ctx context.Context, in *BatchGetProfileReq, opts ...grpc.CallOption) (*BatchGetProfileRsp, error)
	// 消耗免费礼物 [接口已迁移至adapter_revenue]
	ConsumeFreeGift(ctx context.Context, in *ConsumeFreeGiftReq, opts ...grpc.CallOption) (*ConsumeFreeGiftRsp, error)
	// 房间消息
	RoomMsg(ctx context.Context, in *RoomMsgReq, opts ...grpc.CallOption) (*RoomMsgRsp, error)
	// 发送push
	SendPush(ctx context.Context, in *SendPushReq, opts ...grpc.CallOption) (*SendPushRsp, error)
	// 发送大喇叭 [接口已迁移至adapter_room]
	BigHornMsg(ctx context.Context, in *BigHornMsgReq, opts ...grpc.CallOption) (*BigHornMsgRsp, error)
	// 发送平台资产 [接口已迁移至adapter_revenue]
	SendSingleReward(ctx context.Context, in *SendSingleRewardReq, opts ...grpc.CallOption) (*SendSingleRewardRsp, error)
	// 礼物信息 [接口已迁移至adapter_revenue]
	RewardDetail(ctx context.Context, in *RewardDetailReq, opts ...grpc.CallOption) (*RewardDetailRsp, error)
	// 批量获取道具信息 [接口已迁移至adapter_revenue]
	BatchPropDetail(ctx context.Context, in *BatchPropDetailReq, opts ...grpc.CallOption) (*BatchPropDetailRsp, error)
	// 批量获取礼物信息 [接口已迁移至adapter_revenue]
	BatchGiftDetail(ctx context.Context, in *BatchGiftDetailReq, opts ...grpc.CallOption) (*BatchGiftDetailRsp, error)
	// 查询平台货币数量 [接口已迁移至adapter_revenue]
	GetPlatBalance(ctx context.Context, in *GetPlatBalanceReq, opts ...grpc.CallOption) (*GetPlatBalanceRsp, error)
	// 发送平台资产 TODO   [接口已迁移至adapter_revenue/废弃]
	PrizeV2(ctx context.Context, in *PrizeV2Req, opts ...grpc.CallOption) (*PrizeV2Rsp, error)
	// 数据上报 [接口已迁移至adapter_data]
	DataReport(ctx context.Context, in *DataReportReq, opts ...grpc.CallOption) (*DataReportRsp, error)
	// 安全串联
	SafeCheck(ctx context.Context, in *SafeCheckReq, opts ...grpc.CallOption) (*SafeCheckRsp, error)
	// kb 写上报 [接口已迁移至adapter_data]
	QzaReport(ctx context.Context, in *QzaReportReq, opts ...grpc.CallOption) (*QzaReportRsp, error)
	// 匿名关系查询 [接口已迁移至adapter_user]
	BatchGetAnonymousStatus(ctx context.Context, in *BatchGetAnonymousStatusReq, opts ...grpc.CallOption) (*BatchGetAnonymousStatusRsp, error)
	// hippy消息开关同步
	HippyMsgUserSwitchEvent(ctx context.Context, in *HippyMsgUserSwitchEventReq, opts ...grpc.CallOption) (*HippyMsgUserSwitchEventRsp, error)
	// 发送hippy弹框
	SendHippyMsg(ctx context.Context, in *SendHippyMsgReq, opts ...grpc.CallOption) (*SendHippyMsgRsp, error)
	// 查询UIABTest 前端调用
	UIABTest(ctx context.Context, in *UIABTestReq, opts ...grpc.CallOption) (*UIABTestRsp, error)
	// 查询ABTest  后台调用
	ABTest(ctx context.Context, in *ABTestReq, opts ...grpc.CallOption) (*ABTestRsp, error)
	// 上报任务完成状态
	ReportTaskConditionBill(ctx context.Context, in *ReportTaskConditionBillReq, opts ...grpc.CallOption) (*ReportTaskConditionBillRsp, error)
	// 关注 [接口已迁移至adapter_user]
	FollowOpt(ctx context.Context, in *FollowOptReq, opts ...grpc.CallOption) (*FollowOptRsp, error)
	// 查询关系 [接口已迁移至adapter_user]
	QueryRelation(ctx context.Context, in *QueryRelationReq, opts ...grpc.CallOption) (*QueryRelationRsp, error)
	// 查询实名成年状态 [接口已迁移至adapter_user]
	QueryCertInfo(ctx context.Context, in *QueryCertInfoReq, opts ...grpc.CallOption) (*QueryCertInfoRsp, error)
	// 上报事件 [接口已迁移至adapter_data]
	ReportEvent(ctx context.Context, in *ReportEventReq, opts ...grpc.CallOption) (*ReportEventRsp, error)
	// 查询好友 [接口已迁移至adapter_user]
	QueryFriend(ctx context.Context, in *QueryFriendsReq, opts ...grpc.CallOption) (*QueryFriendsRsp, error)
	// 查询黑名单 [接口已迁移至adapter_user]
	QueryBlacklist(ctx context.Context, in *QueryBlacklistReq, opts ...grpc.CallOption) (*QueryBlacklistRsp, error)
}

type adapterClient struct {
	cc grpc.ClientConnInterface
}

func NewAdapterClient(cc grpc.ClientConnInterface) AdapterClient {
	return &adapterClient{cc}
}

func (c *adapterClient) GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceRsp)
	err := c.cc.Invoke(ctx, Adapter_GetBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) PlaceOrder(ctx context.Context, in *PlaceOrderReq, opts ...grpc.CallOption) (*PlaceOrderRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlaceOrderRsp)
	err := c.cc.Invoke(ctx, Adapter_PlaceOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) Pay(ctx context.Context, in *PayReq, opts ...grpc.CallOption) (*PayRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayRsp)
	err := c.cc.Invoke(ctx, Adapter_Pay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) Present(ctx context.Context, in *PresentReq, opts ...grpc.CallOption) (*PresentRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PresentRsp)
	err := c.cc.Invoke(ctx, Adapter_Present_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) GetProfile(ctx context.Context, in *GetProfileReq, opts ...grpc.CallOption) (*GetProfileRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProfileRsp)
	err := c.cc.Invoke(ctx, Adapter_GetProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) SendMail(ctx context.Context, in *SendMailReq, opts ...grpc.CallOption) (*SendMailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendMailRsp)
	err := c.cc.Invoke(ctx, Adapter_SendMail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) Prize(ctx context.Context, in *PrizeReq, opts ...grpc.CallOption) (*PrizeRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrizeRsp)
	err := c.cc.Invoke(ctx, Adapter_Prize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) TDBankReport(ctx context.Context, in *TDBankReportReq, opts ...grpc.CallOption) (*TDBankReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TDBankReportRsp)
	err := c.cc.Invoke(ctx, Adapter_TDBankReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) BatchGetProfile(ctx context.Context, in *BatchGetProfileReq, opts ...grpc.CallOption) (*BatchGetProfileRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetProfileRsp)
	err := c.cc.Invoke(ctx, Adapter_BatchGetProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) ConsumeFreeGift(ctx context.Context, in *ConsumeFreeGiftReq, opts ...grpc.CallOption) (*ConsumeFreeGiftRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConsumeFreeGiftRsp)
	err := c.cc.Invoke(ctx, Adapter_ConsumeFreeGift_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) RoomMsg(ctx context.Context, in *RoomMsgReq, opts ...grpc.CallOption) (*RoomMsgRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RoomMsgRsp)
	err := c.cc.Invoke(ctx, Adapter_RoomMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) SendPush(ctx context.Context, in *SendPushReq, opts ...grpc.CallOption) (*SendPushRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendPushRsp)
	err := c.cc.Invoke(ctx, Adapter_SendPush_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) BigHornMsg(ctx context.Context, in *BigHornMsgReq, opts ...grpc.CallOption) (*BigHornMsgRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BigHornMsgRsp)
	err := c.cc.Invoke(ctx, Adapter_BigHornMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) SendSingleReward(ctx context.Context, in *SendSingleRewardReq, opts ...grpc.CallOption) (*SendSingleRewardRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendSingleRewardRsp)
	err := c.cc.Invoke(ctx, Adapter_SendSingleReward_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) RewardDetail(ctx context.Context, in *RewardDetailReq, opts ...grpc.CallOption) (*RewardDetailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RewardDetailRsp)
	err := c.cc.Invoke(ctx, Adapter_RewardDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) BatchPropDetail(ctx context.Context, in *BatchPropDetailReq, opts ...grpc.CallOption) (*BatchPropDetailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchPropDetailRsp)
	err := c.cc.Invoke(ctx, Adapter_BatchPropDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) BatchGiftDetail(ctx context.Context, in *BatchGiftDetailReq, opts ...grpc.CallOption) (*BatchGiftDetailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGiftDetailRsp)
	err := c.cc.Invoke(ctx, Adapter_BatchGiftDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) GetPlatBalance(ctx context.Context, in *GetPlatBalanceReq, opts ...grpc.CallOption) (*GetPlatBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlatBalanceRsp)
	err := c.cc.Invoke(ctx, Adapter_GetPlatBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) PrizeV2(ctx context.Context, in *PrizeV2Req, opts ...grpc.CallOption) (*PrizeV2Rsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrizeV2Rsp)
	err := c.cc.Invoke(ctx, Adapter_PrizeV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) DataReport(ctx context.Context, in *DataReportReq, opts ...grpc.CallOption) (*DataReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DataReportRsp)
	err := c.cc.Invoke(ctx, Adapter_DataReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) SafeCheck(ctx context.Context, in *SafeCheckReq, opts ...grpc.CallOption) (*SafeCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SafeCheckRsp)
	err := c.cc.Invoke(ctx, Adapter_SafeCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) QzaReport(ctx context.Context, in *QzaReportReq, opts ...grpc.CallOption) (*QzaReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QzaReportRsp)
	err := c.cc.Invoke(ctx, Adapter_QzaReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) BatchGetAnonymousStatus(ctx context.Context, in *BatchGetAnonymousStatusReq, opts ...grpc.CallOption) (*BatchGetAnonymousStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetAnonymousStatusRsp)
	err := c.cc.Invoke(ctx, Adapter_BatchGetAnonymousStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) HippyMsgUserSwitchEvent(ctx context.Context, in *HippyMsgUserSwitchEventReq, opts ...grpc.CallOption) (*HippyMsgUserSwitchEventRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HippyMsgUserSwitchEventRsp)
	err := c.cc.Invoke(ctx, Adapter_HippyMsgUserSwitchEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) SendHippyMsg(ctx context.Context, in *SendHippyMsgReq, opts ...grpc.CallOption) (*SendHippyMsgRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendHippyMsgRsp)
	err := c.cc.Invoke(ctx, Adapter_SendHippyMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) UIABTest(ctx context.Context, in *UIABTestReq, opts ...grpc.CallOption) (*UIABTestRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UIABTestRsp)
	err := c.cc.Invoke(ctx, Adapter_UIABTest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) ABTest(ctx context.Context, in *ABTestReq, opts ...grpc.CallOption) (*ABTestRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ABTestRsp)
	err := c.cc.Invoke(ctx, Adapter_ABTest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) ReportTaskConditionBill(ctx context.Context, in *ReportTaskConditionBillReq, opts ...grpc.CallOption) (*ReportTaskConditionBillRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReportTaskConditionBillRsp)
	err := c.cc.Invoke(ctx, Adapter_ReportTaskConditionBill_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) FollowOpt(ctx context.Context, in *FollowOptReq, opts ...grpc.CallOption) (*FollowOptRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FollowOptRsp)
	err := c.cc.Invoke(ctx, Adapter_FollowOpt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) QueryRelation(ctx context.Context, in *QueryRelationReq, opts ...grpc.CallOption) (*QueryRelationRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRelationRsp)
	err := c.cc.Invoke(ctx, Adapter_QueryRelation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) QueryCertInfo(ctx context.Context, in *QueryCertInfoReq, opts ...grpc.CallOption) (*QueryCertInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryCertInfoRsp)
	err := c.cc.Invoke(ctx, Adapter_QueryCertInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) ReportEvent(ctx context.Context, in *ReportEventReq, opts ...grpc.CallOption) (*ReportEventRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReportEventRsp)
	err := c.cc.Invoke(ctx, Adapter_ReportEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) QueryFriend(ctx context.Context, in *QueryFriendsReq, opts ...grpc.CallOption) (*QueryFriendsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryFriendsRsp)
	err := c.cc.Invoke(ctx, Adapter_QueryFriend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterClient) QueryBlacklist(ctx context.Context, in *QueryBlacklistReq, opts ...grpc.CallOption) (*QueryBlacklistRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryBlacklistRsp)
	err := c.cc.Invoke(ctx, Adapter_QueryBlacklist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterServer is the server API for Adapter service.
// All implementations should embed UnimplementedAdapterServer
// for forward compatibility
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterServer interface {
	// 查询游戏币 [接口已迁移至adapter_revenue]
	GetBalance(context.Context, *GetBalanceReq) (*GetBalanceRsp, error)
	// 下单 [接口已迁移至adapter_revenue]
	PlaceOrder(context.Context, *PlaceOrderReq) (*PlaceOrderRsp, error)
	// 扣除游戏币 [接口已迁移至adapter_revenue]
	Pay(context.Context, *PayReq) (*PayRsp, error)
	// 奖励游戏币 [接口已迁移至adapter_revenue]
	Present(context.Context, *PresentReq) (*PresentRsp, error)
	// 获取用户资料 [接口已迁移至adapter_user]
	GetProfile(context.Context, *GetProfileReq) (*GetProfileRsp, error)
	// 发送私信
	SendMail(context.Context, *SendMailReq) (*SendMailRsp, error)
	// 根据指定金额发奖 [接口已迁移至adapter_revenue]
	Prize(context.Context, *PrizeReq) (*PrizeRsp, error)
	// 数据上报 [接口已迁移至adapter_data]
	TDBankReport(context.Context, *TDBankReportReq) (*TDBankReportRsp, error)
	// 批量获取用户资料 [接口已迁移至adapter_user]
	BatchGetProfile(context.Context, *BatchGetProfileReq) (*BatchGetProfileRsp, error)
	// 消耗免费礼物 [接口已迁移至adapter_revenue]
	ConsumeFreeGift(context.Context, *ConsumeFreeGiftReq) (*ConsumeFreeGiftRsp, error)
	// 房间消息
	RoomMsg(context.Context, *RoomMsgReq) (*RoomMsgRsp, error)
	// 发送push
	SendPush(context.Context, *SendPushReq) (*SendPushRsp, error)
	// 发送大喇叭 [接口已迁移至adapter_room]
	BigHornMsg(context.Context, *BigHornMsgReq) (*BigHornMsgRsp, error)
	// 发送平台资产 [接口已迁移至adapter_revenue]
	SendSingleReward(context.Context, *SendSingleRewardReq) (*SendSingleRewardRsp, error)
	// 礼物信息 [接口已迁移至adapter_revenue]
	RewardDetail(context.Context, *RewardDetailReq) (*RewardDetailRsp, error)
	// 批量获取道具信息 [接口已迁移至adapter_revenue]
	BatchPropDetail(context.Context, *BatchPropDetailReq) (*BatchPropDetailRsp, error)
	// 批量获取礼物信息 [接口已迁移至adapter_revenue]
	BatchGiftDetail(context.Context, *BatchGiftDetailReq) (*BatchGiftDetailRsp, error)
	// 查询平台货币数量 [接口已迁移至adapter_revenue]
	GetPlatBalance(context.Context, *GetPlatBalanceReq) (*GetPlatBalanceRsp, error)
	// 发送平台资产 TODO   [接口已迁移至adapter_revenue/废弃]
	PrizeV2(context.Context, *PrizeV2Req) (*PrizeV2Rsp, error)
	// 数据上报 [接口已迁移至adapter_data]
	DataReport(context.Context, *DataReportReq) (*DataReportRsp, error)
	// 安全串联
	SafeCheck(context.Context, *SafeCheckReq) (*SafeCheckRsp, error)
	// kb 写上报 [接口已迁移至adapter_data]
	QzaReport(context.Context, *QzaReportReq) (*QzaReportRsp, error)
	// 匿名关系查询 [接口已迁移至adapter_user]
	BatchGetAnonymousStatus(context.Context, *BatchGetAnonymousStatusReq) (*BatchGetAnonymousStatusRsp, error)
	// hippy消息开关同步
	HippyMsgUserSwitchEvent(context.Context, *HippyMsgUserSwitchEventReq) (*HippyMsgUserSwitchEventRsp, error)
	// 发送hippy弹框
	SendHippyMsg(context.Context, *SendHippyMsgReq) (*SendHippyMsgRsp, error)
	// 查询UIABTest 前端调用
	UIABTest(context.Context, *UIABTestReq) (*UIABTestRsp, error)
	// 查询ABTest  后台调用
	ABTest(context.Context, *ABTestReq) (*ABTestRsp, error)
	// 上报任务完成状态
	ReportTaskConditionBill(context.Context, *ReportTaskConditionBillReq) (*ReportTaskConditionBillRsp, error)
	// 关注 [接口已迁移至adapter_user]
	FollowOpt(context.Context, *FollowOptReq) (*FollowOptRsp, error)
	// 查询关系 [接口已迁移至adapter_user]
	QueryRelation(context.Context, *QueryRelationReq) (*QueryRelationRsp, error)
	// 查询实名成年状态 [接口已迁移至adapter_user]
	QueryCertInfo(context.Context, *QueryCertInfoReq) (*QueryCertInfoRsp, error)
	// 上报事件 [接口已迁移至adapter_data]
	ReportEvent(context.Context, *ReportEventReq) (*ReportEventRsp, error)
	// 查询好友 [接口已迁移至adapter_user]
	QueryFriend(context.Context, *QueryFriendsReq) (*QueryFriendsRsp, error)
	// 查询黑名单 [接口已迁移至adapter_user]
	QueryBlacklist(context.Context, *QueryBlacklistReq) (*QueryBlacklistRsp, error)
}

// UnimplementedAdapterServer should be embedded to have forward compatible implementations.
type UnimplementedAdapterServer struct {
}

func (UnimplementedAdapterServer) GetBalance(context.Context, *GetBalanceReq) (*GetBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalance not implemented")
}
func (UnimplementedAdapterServer) PlaceOrder(context.Context, *PlaceOrderReq) (*PlaceOrderRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaceOrder not implemented")
}
func (UnimplementedAdapterServer) Pay(context.Context, *PayReq) (*PayRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Pay not implemented")
}
func (UnimplementedAdapterServer) Present(context.Context, *PresentReq) (*PresentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Present not implemented")
}
func (UnimplementedAdapterServer) GetProfile(context.Context, *GetProfileReq) (*GetProfileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfile not implemented")
}
func (UnimplementedAdapterServer) SendMail(context.Context, *SendMailReq) (*SendMailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMail not implemented")
}
func (UnimplementedAdapterServer) Prize(context.Context, *PrizeReq) (*PrizeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Prize not implemented")
}
func (UnimplementedAdapterServer) TDBankReport(context.Context, *TDBankReportReq) (*TDBankReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TDBankReport not implemented")
}
func (UnimplementedAdapterServer) BatchGetProfile(context.Context, *BatchGetProfileReq) (*BatchGetProfileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetProfile not implemented")
}
func (UnimplementedAdapterServer) ConsumeFreeGift(context.Context, *ConsumeFreeGiftReq) (*ConsumeFreeGiftRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeFreeGift not implemented")
}
func (UnimplementedAdapterServer) RoomMsg(context.Context, *RoomMsgReq) (*RoomMsgRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RoomMsg not implemented")
}
func (UnimplementedAdapterServer) SendPush(context.Context, *SendPushReq) (*SendPushRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPush not implemented")
}
func (UnimplementedAdapterServer) BigHornMsg(context.Context, *BigHornMsgReq) (*BigHornMsgRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BigHornMsg not implemented")
}
func (UnimplementedAdapterServer) SendSingleReward(context.Context, *SendSingleRewardReq) (*SendSingleRewardRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSingleReward not implemented")
}
func (UnimplementedAdapterServer) RewardDetail(context.Context, *RewardDetailReq) (*RewardDetailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RewardDetail not implemented")
}
func (UnimplementedAdapterServer) BatchPropDetail(context.Context, *BatchPropDetailReq) (*BatchPropDetailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchPropDetail not implemented")
}
func (UnimplementedAdapterServer) BatchGiftDetail(context.Context, *BatchGiftDetailReq) (*BatchGiftDetailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGiftDetail not implemented")
}
func (UnimplementedAdapterServer) GetPlatBalance(context.Context, *GetPlatBalanceReq) (*GetPlatBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatBalance not implemented")
}
func (UnimplementedAdapterServer) PrizeV2(context.Context, *PrizeV2Req) (*PrizeV2Rsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PrizeV2 not implemented")
}
func (UnimplementedAdapterServer) DataReport(context.Context, *DataReportReq) (*DataReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataReport not implemented")
}
func (UnimplementedAdapterServer) SafeCheck(context.Context, *SafeCheckReq) (*SafeCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SafeCheck not implemented")
}
func (UnimplementedAdapterServer) QzaReport(context.Context, *QzaReportReq) (*QzaReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QzaReport not implemented")
}
func (UnimplementedAdapterServer) BatchGetAnonymousStatus(context.Context, *BatchGetAnonymousStatusReq) (*BatchGetAnonymousStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetAnonymousStatus not implemented")
}
func (UnimplementedAdapterServer) HippyMsgUserSwitchEvent(context.Context, *HippyMsgUserSwitchEventReq) (*HippyMsgUserSwitchEventRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HippyMsgUserSwitchEvent not implemented")
}
func (UnimplementedAdapterServer) SendHippyMsg(context.Context, *SendHippyMsgReq) (*SendHippyMsgRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendHippyMsg not implemented")
}
func (UnimplementedAdapterServer) UIABTest(context.Context, *UIABTestReq) (*UIABTestRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UIABTest not implemented")
}
func (UnimplementedAdapterServer) ABTest(context.Context, *ABTestReq) (*ABTestRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ABTest not implemented")
}
func (UnimplementedAdapterServer) ReportTaskConditionBill(context.Context, *ReportTaskConditionBillReq) (*ReportTaskConditionBillRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportTaskConditionBill not implemented")
}
func (UnimplementedAdapterServer) FollowOpt(context.Context, *FollowOptReq) (*FollowOptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FollowOpt not implemented")
}
func (UnimplementedAdapterServer) QueryRelation(context.Context, *QueryRelationReq) (*QueryRelationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRelation not implemented")
}
func (UnimplementedAdapterServer) QueryCertInfo(context.Context, *QueryCertInfoReq) (*QueryCertInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCertInfo not implemented")
}
func (UnimplementedAdapterServer) ReportEvent(context.Context, *ReportEventReq) (*ReportEventRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportEvent not implemented")
}
func (UnimplementedAdapterServer) QueryFriend(context.Context, *QueryFriendsReq) (*QueryFriendsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryFriend not implemented")
}
func (UnimplementedAdapterServer) QueryBlacklist(context.Context, *QueryBlacklistReq) (*QueryBlacklistRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryBlacklist not implemented")
}

// UnsafeAdapterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdapterServer will
// result in compilation errors.
type UnsafeAdapterServer interface {
	mustEmbedUnimplementedAdapterServer()
}

func RegisterAdapterServer(s grpc.ServiceRegistrar, srv AdapterServer) {
	s.RegisterService(&Adapter_ServiceDesc, srv)
}

func _Adapter_GetBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).GetBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_GetBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).GetBalance(ctx, req.(*GetBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_PlaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).PlaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_PlaceOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).PlaceOrder(ctx, req.(*PlaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_Pay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).Pay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_Pay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).Pay(ctx, req.(*PayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_Present_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).Present(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_Present_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).Present(ctx, req.(*PresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_GetProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).GetProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_GetProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).GetProfile(ctx, req.(*GetProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_SendMail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendMailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).SendMail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_SendMail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).SendMail(ctx, req.(*SendMailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_Prize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).Prize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_Prize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).Prize(ctx, req.(*PrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_TDBankReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TDBankReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).TDBankReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_TDBankReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).TDBankReport(ctx, req.(*TDBankReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_BatchGetProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).BatchGetProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_BatchGetProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).BatchGetProfile(ctx, req.(*BatchGetProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_ConsumeFreeGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeFreeGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).ConsumeFreeGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_ConsumeFreeGift_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).ConsumeFreeGift(ctx, req.(*ConsumeFreeGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_RoomMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoomMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).RoomMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_RoomMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).RoomMsg(ctx, req.(*RoomMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_SendPush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendPushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).SendPush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_SendPush_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).SendPush(ctx, req.(*SendPushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_BigHornMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BigHornMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).BigHornMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_BigHornMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).BigHornMsg(ctx, req.(*BigHornMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_SendSingleReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSingleRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).SendSingleReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_SendSingleReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).SendSingleReward(ctx, req.(*SendSingleRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_RewardDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RewardDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).RewardDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_RewardDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).RewardDetail(ctx, req.(*RewardDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_BatchPropDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchPropDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).BatchPropDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_BatchPropDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).BatchPropDetail(ctx, req.(*BatchPropDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_BatchGiftDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGiftDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).BatchGiftDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_BatchGiftDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).BatchGiftDetail(ctx, req.(*BatchGiftDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_GetPlatBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlatBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).GetPlatBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_GetPlatBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).GetPlatBalance(ctx, req.(*GetPlatBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_PrizeV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrizeV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).PrizeV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_PrizeV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).PrizeV2(ctx, req.(*PrizeV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_DataReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).DataReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_DataReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).DataReport(ctx, req.(*DataReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_SafeCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SafeCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).SafeCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_SafeCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).SafeCheck(ctx, req.(*SafeCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_QzaReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QzaReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).QzaReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_QzaReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).QzaReport(ctx, req.(*QzaReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_BatchGetAnonymousStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnonymousStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).BatchGetAnonymousStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_BatchGetAnonymousStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).BatchGetAnonymousStatus(ctx, req.(*BatchGetAnonymousStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_HippyMsgUserSwitchEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HippyMsgUserSwitchEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).HippyMsgUserSwitchEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_HippyMsgUserSwitchEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).HippyMsgUserSwitchEvent(ctx, req.(*HippyMsgUserSwitchEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_SendHippyMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendHippyMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).SendHippyMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_SendHippyMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).SendHippyMsg(ctx, req.(*SendHippyMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_UIABTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UIABTestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).UIABTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_UIABTest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).UIABTest(ctx, req.(*UIABTestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_ABTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ABTestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).ABTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_ABTest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).ABTest(ctx, req.(*ABTestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_ReportTaskConditionBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportTaskConditionBillReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).ReportTaskConditionBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_ReportTaskConditionBill_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).ReportTaskConditionBill(ctx, req.(*ReportTaskConditionBillReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_FollowOpt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FollowOptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).FollowOpt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_FollowOpt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).FollowOpt(ctx, req.(*FollowOptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_QueryRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).QueryRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_QueryRelation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).QueryRelation(ctx, req.(*QueryRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_QueryCertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCertInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).QueryCertInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_QueryCertInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).QueryCertInfo(ctx, req.(*QueryCertInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_ReportEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).ReportEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_ReportEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).ReportEvent(ctx, req.(*ReportEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_QueryFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFriendsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).QueryFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_QueryFriend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).QueryFriend(ctx, req.(*QueryFriendsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Adapter_QueryBlacklist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryBlacklistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterServer).QueryBlacklist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Adapter_QueryBlacklist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterServer).QueryBlacklist(ctx, req.(*QueryBlacklistReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Adapter_ServiceDesc is the grpc.ServiceDesc for Adapter service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Adapter_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.Adapter",
	HandlerType: (*AdapterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBalance",
			Handler:    _Adapter_GetBalance_Handler,
		},
		{
			MethodName: "PlaceOrder",
			Handler:    _Adapter_PlaceOrder_Handler,
		},
		{
			MethodName: "Pay",
			Handler:    _Adapter_Pay_Handler,
		},
		{
			MethodName: "Present",
			Handler:    _Adapter_Present_Handler,
		},
		{
			MethodName: "GetProfile",
			Handler:    _Adapter_GetProfile_Handler,
		},
		{
			MethodName: "SendMail",
			Handler:    _Adapter_SendMail_Handler,
		},
		{
			MethodName: "Prize",
			Handler:    _Adapter_Prize_Handler,
		},
		{
			MethodName: "TDBankReport",
			Handler:    _Adapter_TDBankReport_Handler,
		},
		{
			MethodName: "BatchGetProfile",
			Handler:    _Adapter_BatchGetProfile_Handler,
		},
		{
			MethodName: "ConsumeFreeGift",
			Handler:    _Adapter_ConsumeFreeGift_Handler,
		},
		{
			MethodName: "RoomMsg",
			Handler:    _Adapter_RoomMsg_Handler,
		},
		{
			MethodName: "SendPush",
			Handler:    _Adapter_SendPush_Handler,
		},
		{
			MethodName: "BigHornMsg",
			Handler:    _Adapter_BigHornMsg_Handler,
		},
		{
			MethodName: "SendSingleReward",
			Handler:    _Adapter_SendSingleReward_Handler,
		},
		{
			MethodName: "RewardDetail",
			Handler:    _Adapter_RewardDetail_Handler,
		},
		{
			MethodName: "BatchPropDetail",
			Handler:    _Adapter_BatchPropDetail_Handler,
		},
		{
			MethodName: "BatchGiftDetail",
			Handler:    _Adapter_BatchGiftDetail_Handler,
		},
		{
			MethodName: "GetPlatBalance",
			Handler:    _Adapter_GetPlatBalance_Handler,
		},
		{
			MethodName: "PrizeV2",
			Handler:    _Adapter_PrizeV2_Handler,
		},
		{
			MethodName: "DataReport",
			Handler:    _Adapter_DataReport_Handler,
		},
		{
			MethodName: "SafeCheck",
			Handler:    _Adapter_SafeCheck_Handler,
		},
		{
			MethodName: "QzaReport",
			Handler:    _Adapter_QzaReport_Handler,
		},
		{
			MethodName: "BatchGetAnonymousStatus",
			Handler:    _Adapter_BatchGetAnonymousStatus_Handler,
		},
		{
			MethodName: "HippyMsgUserSwitchEvent",
			Handler:    _Adapter_HippyMsgUserSwitchEvent_Handler,
		},
		{
			MethodName: "SendHippyMsg",
			Handler:    _Adapter_SendHippyMsg_Handler,
		},
		{
			MethodName: "UIABTest",
			Handler:    _Adapter_UIABTest_Handler,
		},
		{
			MethodName: "ABTest",
			Handler:    _Adapter_ABTest_Handler,
		},
		{
			MethodName: "ReportTaskConditionBill",
			Handler:    _Adapter_ReportTaskConditionBill_Handler,
		},
		{
			MethodName: "FollowOpt",
			Handler:    _Adapter_FollowOpt_Handler,
		},
		{
			MethodName: "QueryRelation",
			Handler:    _Adapter_QueryRelation_Handler,
		},
		{
			MethodName: "QueryCertInfo",
			Handler:    _Adapter_QueryCertInfo_Handler,
		},
		{
			MethodName: "ReportEvent",
			Handler:    _Adapter_ReportEvent_Handler,
		},
		{
			MethodName: "QueryFriend",
			Handler:    _Adapter_QueryFriend_Handler,
		},
		{
			MethodName: "QueryBlacklist",
			Handler:    _Adapter_QueryBlacklist_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/adapter/adapter.proto",
}
