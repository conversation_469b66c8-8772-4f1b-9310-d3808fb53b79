// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/prizepool/prizepool.proto

package prizepool

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timer "kugou_adapter_service/pkg/gen/proto/pb/timer"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryPoolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	RoundID  string `protobuf:"bytes,2,opt,name=roundID,proto3" json:"roundID,omitempty"`   //场次ID维度
	AnchorId string `protobuf:"bytes,3,opt,name=anchorId,proto3" json:"anchorId,omitempty"` //主播维度
}

func (x *QueryPoolReq) Reset() {
	*x = QueryPoolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPoolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPoolReq) ProtoMessage() {}

func (x *QueryPoolReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPoolReq.ProtoReflect.Descriptor instead.
func (*QueryPoolReq) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{0}
}

func (x *QueryPoolReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryPoolReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *QueryPoolReq) GetAnchorId() string {
	if x != nil {
		return x.AnchorId
	}
	return ""
}

type QueryPoolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalAmount uint64          `protobuf:"varint,1,opt,name=totalAmount,proto3" json:"totalAmount,omitempty"`
	Status      PrizePoolStatus `protobuf:"varint,2,opt,name=status,proto3,enum=game.PrizePoolStatus" json:"status,omitempty"`
}

func (x *QueryPoolRsp) Reset() {
	*x = QueryPoolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPoolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPoolRsp) ProtoMessage() {}

func (x *QueryPoolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPoolRsp.ProtoReflect.Descriptor instead.
func (*QueryPoolRsp) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{1}
}

func (x *QueryPoolRsp) GetTotalAmount() uint64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *QueryPoolRsp) GetStatus() PrizePoolStatus {
	if x != nil {
		return x.Status
	}
	return PrizePoolStatus_PRIZE_POOL_STATUS_NONE
}

type ReportPoolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId        string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId       string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	RoundID      string `protobuf:"bytes,3,opt,name=roundID,proto3" json:"roundID,omitempty"`
	RoomID       string `protobuf:"bytes,4,opt,name=roomID,proto3" json:"roomID,omitempty"`
	ConsumeID    string `protobuf:"bytes,5,opt,name=consumeID,proto3" json:"consumeID,omitempty"`
	Amount       uint64 `protobuf:"varint,6,opt,name=amount,proto3" json:"amount,omitempty"`
	AnchorOpenId string `protobuf:"bytes,7,opt,name=anchorOpenId,proto3" json:"anchorOpenId,omitempty"`
}

func (x *ReportPoolReq) Reset() {
	*x = ReportPoolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportPoolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportPoolReq) ProtoMessage() {}

func (x *ReportPoolReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportPoolReq.ProtoReflect.Descriptor instead.
func (*ReportPoolReq) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{2}
}

func (x *ReportPoolReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportPoolReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReportPoolReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *ReportPoolReq) GetRoomID() string {
	if x != nil {
		return x.RoomID
	}
	return ""
}

func (x *ReportPoolReq) GetConsumeID() string {
	if x != nil {
		return x.ConsumeID
	}
	return ""
}

func (x *ReportPoolReq) GetAmount() uint64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ReportPoolReq) GetAnchorOpenId() string {
	if x != nil {
		return x.AnchorOpenId
	}
	return ""
}

type ReportPoolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportPoolRsp) Reset() {
	*x = ReportPoolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportPoolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportPoolRsp) ProtoMessage() {}

func (x *ReportPoolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportPoolRsp.ProtoReflect.Descriptor instead.
func (*ReportPoolRsp) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{3}
}

type PoolStatusChangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string          `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	RoundID  string          `protobuf:"bytes,2,opt,name=roundID,proto3" json:"roundID,omitempty"`
	ToStatus PrizePoolStatus `protobuf:"varint,3,opt,name=toStatus,proto3,enum=game.PrizePoolStatus" json:"toStatus,omitempty"`
}

func (x *PoolStatusChangeReq) Reset() {
	*x = PoolStatusChangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PoolStatusChangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolStatusChangeReq) ProtoMessage() {}

func (x *PoolStatusChangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolStatusChangeReq.ProtoReflect.Descriptor instead.
func (*PoolStatusChangeReq) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{4}
}

func (x *PoolStatusChangeReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PoolStatusChangeReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *PoolStatusChangeReq) GetToStatus() PrizePoolStatus {
	if x != nil {
		return x.ToStatus
	}
	return PrizePoolStatus_PRIZE_POOL_STATUS_NONE
}

type PoolStatusChangeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PoolStatusChangeRsp) Reset() {
	*x = PoolStatusChangeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PoolStatusChangeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolStatusChangeRsp) ProtoMessage() {}

func (x *PoolStatusChangeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolStatusChangeRsp.ProtoReflect.Descriptor instead.
func (*PoolStatusChangeRsp) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{5}
}

type PKShareCashReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	RoundID string `protobuf:"bytes,2,opt,name=roundID,proto3" json:"roundID,omitempty"`
}

func (x *PKShareCashReq) Reset() {
	*x = PKShareCashReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PKShareCashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PKShareCashReq) ProtoMessage() {}

func (x *PKShareCashReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PKShareCashReq.ProtoReflect.Descriptor instead.
func (*PKShareCashReq) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{6}
}

func (x *PKShareCashReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PKShareCashReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

type PKShareCashRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PKShareCashRsp) Reset() {
	*x = PKShareCashRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PKShareCashRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PKShareCashRsp) ProtoMessage() {}

func (x *PKShareCashRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PKShareCashRsp.ProtoReflect.Descriptor instead.
func (*PKShareCashRsp) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{7}
}

type PoolCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string    `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	RoundID  string    `protobuf:"bytes,2,opt,name=roundID,proto3" json:"roundID,omitempty"`
	PoolData *PoolData `protobuf:"bytes,3,opt,name=poolData,proto3" json:"poolData,omitempty"` //总奖池数据
	Version  uint64    `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *PoolCallbackReq) Reset() {
	*x = PoolCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PoolCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolCallbackReq) ProtoMessage() {}

func (x *PoolCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolCallbackReq.ProtoReflect.Descriptor instead.
func (*PoolCallbackReq) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{8}
}

func (x *PoolCallbackReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PoolCallbackReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *PoolCallbackReq) GetPoolData() *PoolData {
	if x != nil {
		return x.PoolData
	}
	return nil
}

func (x *PoolCallbackReq) GetVersion() uint64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type PoolCallbackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PoolCallbackRsp) Reset() {
	*x = PoolCallbackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_prizepool_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PoolCallbackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoolCallbackRsp) ProtoMessage() {}

func (x *PoolCallbackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_prizepool_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoolCallbackRsp.ProtoReflect.Descriptor instead.
func (*PoolCallbackRsp) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_prizepool_proto_rawDescGZIP(), []int{9}
}

var File_pb_prizepool_prizepool_proto protoreflect.FileDescriptor

var file_pb_prizepool_prizepool_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x70,
	0x72, 0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04,
	0x67, 0x61, 0x6d, 0x65, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x70, 0x62, 0x2f, 0x70,
	0x72, 0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f,
	0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x49,
	0x64, 0x22, 0x5f, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xc9, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6f, 0x6f,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f,
	0x6f, 0x6d, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49,
	0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e,
	0x63, 0x68, 0x6f, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x0f,
	0x0a, 0x0d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x22,
	0x78, 0x0a, 0x13, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x31, 0x0a, 0x08, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x08, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x50, 0x6f, 0x6f,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x73, 0x70,
	0x22, 0x40, 0x0a, 0x0e, 0x50, 0x4b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x61, 0x73, 0x68, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x44, 0x22, 0x10, 0x0a, 0x0e, 0x50, 0x4b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x61, 0x73,
	0x68, 0x52, 0x73, 0x70, 0x22, 0x87, 0x01, 0x0a, 0x0f, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x6f, 0x6f, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x70, 0x6f, 0x6f, 0x6c,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x11,
	0x0a, 0x0f, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x73,
	0x70, 0x32, 0xc9, 0x02, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12,
	0x33, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x12, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6f, 0x6f,
	0x6c, 0x52, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6f,
	0x6f, 0x6c, 0x12, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x10,
	0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x0d, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x50, 0x4b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x61, 0x73,
	0x68, 0x12, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x4b, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50,
	0x4b, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x61, 0x73, 0x68, 0x52, 0x73, 0x70, 0x42, 0x41, 0x5a,
	0x3f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_prizepool_prizepool_proto_rawDescOnce sync.Once
	file_pb_prizepool_prizepool_proto_rawDescData = file_pb_prizepool_prizepool_proto_rawDesc
)

func file_pb_prizepool_prizepool_proto_rawDescGZIP() []byte {
	file_pb_prizepool_prizepool_proto_rawDescOnce.Do(func() {
		file_pb_prizepool_prizepool_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_prizepool_prizepool_proto_rawDescData)
	})
	return file_pb_prizepool_prizepool_proto_rawDescData
}

var file_pb_prizepool_prizepool_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_prizepool_prizepool_proto_goTypes = []interface{}{
	(*QueryPoolReq)(nil),                // 0: game.QueryPoolReq
	(*QueryPoolRsp)(nil),                // 1: game.QueryPoolRsp
	(*ReportPoolReq)(nil),               // 2: game.ReportPoolReq
	(*ReportPoolRsp)(nil),               // 3: game.ReportPoolRsp
	(*PoolStatusChangeReq)(nil),         // 4: game.PoolStatusChangeReq
	(*PoolStatusChangeRsp)(nil),         // 5: game.PoolStatusChangeRsp
	(*PKShareCashReq)(nil),              // 6: game.PKShareCashReq
	(*PKShareCashRsp)(nil),              // 7: game.PKShareCashRsp
	(*PoolCallbackReq)(nil),             // 8: game.PoolCallbackReq
	(*PoolCallbackRsp)(nil),             // 9: game.PoolCallbackRsp
	(PrizePoolStatus)(0),                // 10: game.PrizePoolStatus
	(*PoolData)(nil),                    // 11: game.PoolData
	(*timer.TimerCallbackRequest)(nil),  // 12: timer.TimerCallbackRequest
	(*timer.TimerCallbackResponse)(nil), // 13: timer.TimerCallbackResponse
}
var file_pb_prizepool_prizepool_proto_depIdxs = []int32{
	10, // 0: game.QueryPoolRsp.status:type_name -> game.PrizePoolStatus
	10, // 1: game.PoolStatusChangeReq.toStatus:type_name -> game.PrizePoolStatus
	11, // 2: game.PoolCallbackReq.poolData:type_name -> game.PoolData
	0,  // 3: game.PrizePool.QueryPool:input_type -> game.QueryPoolReq
	2,  // 4: game.PrizePool.ReportPool:input_type -> game.ReportPoolReq
	4,  // 5: game.PrizePool.PoolStatusChange:input_type -> game.PoolStatusChangeReq
	12, // 6: game.PrizePool.TimerCallback:input_type -> timer.TimerCallbackRequest
	6,  // 7: game.PrizePool.PKShareCash:input_type -> game.PKShareCashReq
	1,  // 8: game.PrizePool.QueryPool:output_type -> game.QueryPoolRsp
	3,  // 9: game.PrizePool.ReportPool:output_type -> game.ReportPoolRsp
	5,  // 10: game.PrizePool.PoolStatusChange:output_type -> game.PoolStatusChangeRsp
	13, // 11: game.PrizePool.TimerCallback:output_type -> timer.TimerCallbackResponse
	7,  // 12: game.PrizePool.PKShareCash:output_type -> game.PKShareCashRsp
	8,  // [8:13] is the sub-list for method output_type
	3,  // [3:8] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_pb_prizepool_prizepool_proto_init() }
func file_pb_prizepool_prizepool_proto_init() {
	if File_pb_prizepool_prizepool_proto != nil {
		return
	}
	file_pb_prizepool_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_prizepool_prizepool_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPoolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPoolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportPoolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportPoolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PoolStatusChangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PoolStatusChangeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PKShareCashReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PKShareCashRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PoolCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_prizepool_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PoolCallbackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_prizepool_prizepool_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_prizepool_prizepool_proto_goTypes,
		DependencyIndexes: file_pb_prizepool_prizepool_proto_depIdxs,
		MessageInfos:      file_pb_prizepool_prizepool_proto_msgTypes,
	}.Build()
	File_pb_prizepool_prizepool_proto = out.File
	file_pb_prizepool_prizepool_proto_rawDesc = nil
	file_pb_prizepool_prizepool_proto_goTypes = nil
	file_pb_prizepool_prizepool_proto_depIdxs = nil
}
