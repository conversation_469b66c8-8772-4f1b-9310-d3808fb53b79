// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/prizepool/api/prizepool_api.proto

package api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	PrizePoolApi_NotifyPKCash_FullMethodName = "/game.PrizePoolApi/NotifyPKCash"
)

// PrizePoolApiClient is the client API for PrizePoolApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PrizePoolApiClient interface {
	// 通知PK摇现金活动
	NotifyPKCash(ctx context.Context, in *NotifyPKCashReq, opts ...grpc.CallOption) (*NotifyPKCashRsp, error)
}

type prizePoolApiClient struct {
	cc grpc.ClientConnInterface
}

func NewPrizePoolApiClient(cc grpc.ClientConnInterface) PrizePoolApiClient {
	return &prizePoolApiClient{cc}
}

func (c *prizePoolApiClient) NotifyPKCash(ctx context.Context, in *NotifyPKCashReq, opts ...grpc.CallOption) (*NotifyPKCashRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NotifyPKCashRsp)
	err := c.cc.Invoke(ctx, PrizePoolApi_NotifyPKCash_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrizePoolApiServer is the server API for PrizePoolApi service.
// All implementations should embed UnimplementedPrizePoolApiServer
// for forward compatibility
type PrizePoolApiServer interface {
	// 通知PK摇现金活动
	NotifyPKCash(context.Context, *NotifyPKCashReq) (*NotifyPKCashRsp, error)
}

// UnimplementedPrizePoolApiServer should be embedded to have forward compatible implementations.
type UnimplementedPrizePoolApiServer struct {
}

func (UnimplementedPrizePoolApiServer) NotifyPKCash(context.Context, *NotifyPKCashReq) (*NotifyPKCashRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotifyPKCash not implemented")
}

// UnsafePrizePoolApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrizePoolApiServer will
// result in compilation errors.
type UnsafePrizePoolApiServer interface {
	mustEmbedUnimplementedPrizePoolApiServer()
}

func RegisterPrizePoolApiServer(s grpc.ServiceRegistrar, srv PrizePoolApiServer) {
	s.RegisterService(&PrizePoolApi_ServiceDesc, srv)
}

func _PrizePoolApi_NotifyPKCash_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPKCashReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrizePoolApiServer).NotifyPKCash(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PrizePoolApi_NotifyPKCash_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrizePoolApiServer).NotifyPKCash(ctx, req.(*NotifyPKCashReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PrizePoolApi_ServiceDesc is the grpc.ServiceDesc for PrizePoolApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PrizePoolApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.PrizePoolApi",
	HandlerType: (*PrizePoolApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NotifyPKCash",
			Handler:    _PrizePoolApi_NotifyPKCash_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/prizepool/api/prizepool_api.proto",
}
