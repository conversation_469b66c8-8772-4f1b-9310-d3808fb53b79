// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/prizepool/api/prizepool_api.proto

package api

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserContribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Rank  uint32 `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`   // 排名 从 1 开始
	Score int64  `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"` // 分数
}

func (x *UserContribute) Reset() {
	*x = UserContribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserContribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContribute) ProtoMessage() {}

func (x *UserContribute) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContribute.ProtoReflect.Descriptor instead.
func (*UserContribute) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_api_prizepool_api_proto_rawDescGZIP(), []int{0}
}

func (x *UserContribute) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UserContribute) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *UserContribute) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type AnchorRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnchorID    uint64            `protobuf:"varint,1,opt,name=anchorID,proto3" json:"anchorID,omitempty"`
	Rank        uint32            `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`              // 排名 从 1 开始
	Score       int64             `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`            // 分数
	Contributes []*UserContribute `protobuf:"bytes,4,rep,name=contributes,proto3" json:"contributes,omitempty"` //用户贡献
}

func (x *AnchorRank) Reset() {
	*x = AnchorRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnchorRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnchorRank) ProtoMessage() {}

func (x *AnchorRank) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnchorRank.ProtoReflect.Descriptor instead.
func (*AnchorRank) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_api_prizepool_api_proto_rawDescGZIP(), []int{1}
}

func (x *AnchorRank) GetAnchorID() uint64 {
	if x != nil {
		return x.AnchorID
	}
	return 0
}

func (x *AnchorRank) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *AnchorRank) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *AnchorRank) GetContributes() []*UserContribute {
	if x != nil {
		return x.Contributes
	}
	return nil
}

type NotifyPKCashReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string        `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`              //游戏APPID
	RoundID     string        `protobuf:"bytes,2,opt,name=roundID,proto3" json:"roundID,omitempty"`          //场次ID
	Ranks       []*AnchorRank `protobuf:"bytes,3,rep,name=ranks,proto3" json:"ranks,omitempty"`              //排行
	GameStartTs uint64        `protobuf:"varint,4,opt,name=gameStartTs,proto3" json:"gameStartTs,omitempty"` //游戏开始时间
}

func (x *NotifyPKCashReq) Reset() {
	*x = NotifyPKCashReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyPKCashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyPKCashReq) ProtoMessage() {}

func (x *NotifyPKCashReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyPKCashReq.ProtoReflect.Descriptor instead.
func (*NotifyPKCashReq) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_api_prizepool_api_proto_rawDescGZIP(), []int{2}
}

func (x *NotifyPKCashReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *NotifyPKCashReq) GetRoundID() string {
	if x != nil {
		return x.RoundID
	}
	return ""
}

func (x *NotifyPKCashReq) GetRanks() []*AnchorRank {
	if x != nil {
		return x.Ranks
	}
	return nil
}

func (x *NotifyPKCashReq) GetGameStartTs() uint64 {
	if x != nil {
		return x.GameStartTs
	}
	return 0
}

type NotifyPKCashRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *NotifyPKCashRsp) Reset() {
	*x = NotifyPKCashRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyPKCashRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyPKCashRsp) ProtoMessage() {}

func (x *NotifyPKCashRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_prizepool_api_prizepool_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyPKCashRsp.ProtoReflect.Descriptor instead.
func (*NotifyPKCashRsp) Descriptor() ([]byte, []int) {
	return file_pb_prizepool_api_prizepool_api_proto_rawDescGZIP(), []int{3}
}

func (x *NotifyPKCashRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *NotifyPKCashRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

var File_pb_prizepool_api_prizepool_api_proto protoreflect.FileDescriptor

var file_pb_prizepool_api_prizepool_api_proto_rawDesc = []byte{
	0x0a, 0x24, 0x70, 0x62, 0x2f, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4c, 0x0a, 0x0e, 0x75, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x0a, 0x41, 0x6e, 0x63,
	0x68, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x6e, 0x63, 0x68, 0x6f,
	0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x61, 0x6e, 0x63, 0x68, 0x6f,
	0x72, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x36, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x0f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x50, 0x4b, 0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x05, 0x72, 0x61, 0x6e,
	0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x6b,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x73, 0x22, 0x4f, 0x0a, 0x0f, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x4b, 0x43,
	0x61, 0x73, 0x68, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x73, 0x67, 0x32, 0x7e, 0x0a, 0x0c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x41, 0x70, 0x69, 0x12, 0x6e, 0x0a, 0x0c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x4b,
	0x43, 0x61, 0x73, 0x68, 0x12, 0x15, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x50, 0x4b, 0x43, 0x61, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x50, 0x4b, 0x43, 0x61, 0x73, 0x68, 0x52,
	0x73, 0x70, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f,
	0x6d, 0x69, 0x6e, 0x69, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x70, 0x72, 0x69, 0x7a,
	0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x70, 0x6b, 0x5f,
	0x63, 0x61, 0x73, 0x68, 0x42, 0x45, 0x5a, 0x43, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x70, 0x72,
	0x69, 0x7a, 0x65, 0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_prizepool_api_prizepool_api_proto_rawDescOnce sync.Once
	file_pb_prizepool_api_prizepool_api_proto_rawDescData = file_pb_prizepool_api_prizepool_api_proto_rawDesc
)

func file_pb_prizepool_api_prizepool_api_proto_rawDescGZIP() []byte {
	file_pb_prizepool_api_prizepool_api_proto_rawDescOnce.Do(func() {
		file_pb_prizepool_api_prizepool_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_prizepool_api_prizepool_api_proto_rawDescData)
	})
	return file_pb_prizepool_api_prizepool_api_proto_rawDescData
}

var file_pb_prizepool_api_prizepool_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_prizepool_api_prizepool_api_proto_goTypes = []interface{}{
	(*UserContribute)(nil),  // 0: game.userContribute
	(*AnchorRank)(nil),      // 1: game.AnchorRank
	(*NotifyPKCashReq)(nil), // 2: game.NotifyPKCashReq
	(*NotifyPKCashRsp)(nil), // 3: game.NotifyPKCashRsp
}
var file_pb_prizepool_api_prizepool_api_proto_depIdxs = []int32{
	0, // 0: game.AnchorRank.contributes:type_name -> game.userContribute
	1, // 1: game.NotifyPKCashReq.ranks:type_name -> game.AnchorRank
	2, // 2: game.PrizePoolApi.NotifyPKCash:input_type -> game.NotifyPKCashReq
	3, // 3: game.PrizePoolApi.NotifyPKCash:output_type -> game.NotifyPKCashRsp
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_prizepool_api_prizepool_api_proto_init() }
func file_pb_prizepool_api_prizepool_api_proto_init() {
	if File_pb_prizepool_api_prizepool_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_prizepool_api_prizepool_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserContribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_api_prizepool_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnchorRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_api_prizepool_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyPKCashReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_prizepool_api_prizepool_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyPKCashRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_prizepool_api_prizepool_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_prizepool_api_prizepool_api_proto_goTypes,
		DependencyIndexes: file_pb_prizepool_api_prizepool_api_proto_depIdxs,
		MessageInfos:      file_pb_prizepool_api_prizepool_api_proto_msgTypes,
	}.Build()
	File_pb_prizepool_api_prizepool_api_proto = out.File
	file_pb_prizepool_api_prizepool_api_proto_rawDesc = nil
	file_pb_prizepool_api_prizepool_api_proto_goTypes = nil
	file_pb_prizepool_api_prizepool_api_proto_depIdxs = nil
}
