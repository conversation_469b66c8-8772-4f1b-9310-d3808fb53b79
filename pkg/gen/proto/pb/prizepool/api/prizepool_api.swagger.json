{"swagger": "2.0", "info": {"title": "pb/prizepool/api/prizepool_api.proto", "version": "version not set"}, "tags": [{"name": "PrizePoolApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/miniprogram/prizepool_notify_pk_cash": {"post": {"summary": "通知PK摇现金活动", "operationId": "PrizePoolApi_NotifyPKCash", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameNotifyPKCashRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameNotifyPKCashReq"}}], "tags": ["PrizePoolApi"]}}}, "definitions": {"gameAnchorRank": {"type": "object", "properties": {"anchorID": {"type": "string", "format": "uint64"}, "rank": {"type": "integer", "format": "int64", "title": "排名 从 1 开始"}, "score": {"type": "string", "format": "int64", "title": "分数"}, "contributes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameuserContribute"}, "title": "用户贡献"}}}, "gameNotifyPKCashReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏APPID"}, "roundID": {"type": "string", "title": "场次ID"}, "ranks": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameAnchorRank"}, "title": "排行"}, "gameStartTs": {"type": "string", "format": "uint64", "title": "游戏开始时间"}}}, "gameNotifyPKCashRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "gameuserContribute": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "rank": {"type": "integer", "format": "int64", "title": "排名 从 1 开始"}, "score": {"type": "string", "format": "int64", "title": "分数"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}