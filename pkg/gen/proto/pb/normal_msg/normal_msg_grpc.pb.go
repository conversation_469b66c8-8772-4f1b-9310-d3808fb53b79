// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/normal_msg/normal_msg.proto

package normal_msg

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	NormalMsg_GetUserSwitch_FullMethodName     = "/normal_msg.NormalMsg/GetUserSwitch"
	NormalMsg_SetUserSwitch_FullMethodName     = "/normal_msg.NormalMsg/SetUserSwitch"
	NormalMsg_SendUserSwitchMsg_FullMethodName = "/normal_msg.NormalMsg/SendUserSwitchMsg"
)

// NormalMsgClient is the client API for NormalMsg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NormalMsgClient interface {
	// 查询开关信息
	GetUserSwitch(ctx context.Context, in *GetUserSwitchReq, opts ...grpc.CallOption) (*GetUserSwitchRsp, error)
	// 更改开关信息
	SetUserSwitch(ctx context.Context, in *SetUserSwitchReq, opts ...grpc.CallOption) (*SetUserSwitchRsp, error)
	// 发送消息
	SendUserSwitchMsg(ctx context.Context, in *SendUserSwitchMsgReq, opts ...grpc.CallOption) (*SendUserSwitchMsgRsp, error)
}

type normalMsgClient struct {
	cc grpc.ClientConnInterface
}

func NewNormalMsgClient(cc grpc.ClientConnInterface) NormalMsgClient {
	return &normalMsgClient{cc}
}

func (c *normalMsgClient) GetUserSwitch(ctx context.Context, in *GetUserSwitchReq, opts ...grpc.CallOption) (*GetUserSwitchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserSwitchRsp)
	err := c.cc.Invoke(ctx, NormalMsg_GetUserSwitch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *normalMsgClient) SetUserSwitch(ctx context.Context, in *SetUserSwitchReq, opts ...grpc.CallOption) (*SetUserSwitchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetUserSwitchRsp)
	err := c.cc.Invoke(ctx, NormalMsg_SetUserSwitch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *normalMsgClient) SendUserSwitchMsg(ctx context.Context, in *SendUserSwitchMsgReq, opts ...grpc.CallOption) (*SendUserSwitchMsgRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendUserSwitchMsgRsp)
	err := c.cc.Invoke(ctx, NormalMsg_SendUserSwitchMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NormalMsgServer is the server API for NormalMsg service.
// All implementations should embed UnimplementedNormalMsgServer
// for forward compatibility
type NormalMsgServer interface {
	// 查询开关信息
	GetUserSwitch(context.Context, *GetUserSwitchReq) (*GetUserSwitchRsp, error)
	// 更改开关信息
	SetUserSwitch(context.Context, *SetUserSwitchReq) (*SetUserSwitchRsp, error)
	// 发送消息
	SendUserSwitchMsg(context.Context, *SendUserSwitchMsgReq) (*SendUserSwitchMsgRsp, error)
}

// UnimplementedNormalMsgServer should be embedded to have forward compatible implementations.
type UnimplementedNormalMsgServer struct {
}

func (UnimplementedNormalMsgServer) GetUserSwitch(context.Context, *GetUserSwitchReq) (*GetUserSwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSwitch not implemented")
}
func (UnimplementedNormalMsgServer) SetUserSwitch(context.Context, *SetUserSwitchReq) (*SetUserSwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserSwitch not implemented")
}
func (UnimplementedNormalMsgServer) SendUserSwitchMsg(context.Context, *SendUserSwitchMsgReq) (*SendUserSwitchMsgRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendUserSwitchMsg not implemented")
}

// UnsafeNormalMsgServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NormalMsgServer will
// result in compilation errors.
type UnsafeNormalMsgServer interface {
	mustEmbedUnimplementedNormalMsgServer()
}

func RegisterNormalMsgServer(s grpc.ServiceRegistrar, srv NormalMsgServer) {
	s.RegisterService(&NormalMsg_ServiceDesc, srv)
}

func _NormalMsg_GetUserSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NormalMsgServer).GetUserSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NormalMsg_GetUserSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NormalMsgServer).GetUserSwitch(ctx, req.(*GetUserSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NormalMsg_SetUserSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NormalMsgServer).SetUserSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NormalMsg_SetUserSwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NormalMsgServer).SetUserSwitch(ctx, req.(*SetUserSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NormalMsg_SendUserSwitchMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendUserSwitchMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NormalMsgServer).SendUserSwitchMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NormalMsg_SendUserSwitchMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NormalMsgServer).SendUserSwitchMsg(ctx, req.(*SendUserSwitchMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

// NormalMsg_ServiceDesc is the grpc.ServiceDesc for NormalMsg service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NormalMsg_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "normal_msg.NormalMsg",
	HandlerType: (*NormalMsgServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserSwitch",
			Handler:    _NormalMsg_GetUserSwitch_Handler,
		},
		{
			MethodName: "SetUserSwitch",
			Handler:    _NormalMsg_SetUserSwitch_Handler,
		},
		{
			MethodName: "SendUserSwitchMsg",
			Handler:    _NormalMsg_SendUserSwitchMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/normal_msg/normal_msg.proto",
}
