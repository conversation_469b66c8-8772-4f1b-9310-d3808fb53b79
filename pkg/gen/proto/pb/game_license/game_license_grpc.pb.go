// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_license/game_license.proto

package game_license

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GamePlayLicense_QueryFreeQualification_FullMethodName  = "/kg.game_license.GamePlayLicense/QueryFreeQualification"
	GamePlayLicense_ApplyFreeQualification_FullMethodName  = "/kg.game_license.GamePlayLicense/ApplyFreeQualification"
	GamePlayLicense_RevokeFreeQualification_FullMethodName = "/kg.game_license.GamePlayLicense/RevokeFreeQualification"
)

// GamePlayLicenseClient is the client API for GamePlayLicense service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// GamePlayLicense 用户游戏免费次数
type GamePlayLicenseClient interface {
	// QueryFreeByKey 查询免费次数, 不计入使用
	QueryFreeQualification(ctx context.Context, in *QueryFreeQualificationReq, opts ...grpc.CallOption) (*QueryFreeQualificationRsp, error)
	// ConsumeFree 消耗一次免费次数
	ApplyFreeQualification(ctx context.Context, in *ApplyFreeQualificationReq, opts ...grpc.CallOption) (*ApplyFreeQualificationRsp, error)
	// RevokeFree 撤销已使用的免费次数
	RevokeFreeQualification(ctx context.Context, in *RevokeFreeQualificationReq, opts ...grpc.CallOption) (*RevokeFreeQualificationRsp, error)
}

type gamePlayLicenseClient struct {
	cc grpc.ClientConnInterface
}

func NewGamePlayLicenseClient(cc grpc.ClientConnInterface) GamePlayLicenseClient {
	return &gamePlayLicenseClient{cc}
}

func (c *gamePlayLicenseClient) QueryFreeQualification(ctx context.Context, in *QueryFreeQualificationReq, opts ...grpc.CallOption) (*QueryFreeQualificationRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryFreeQualificationRsp)
	err := c.cc.Invoke(ctx, GamePlayLicense_QueryFreeQualification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLicenseClient) ApplyFreeQualification(ctx context.Context, in *ApplyFreeQualificationReq, opts ...grpc.CallOption) (*ApplyFreeQualificationRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplyFreeQualificationRsp)
	err := c.cc.Invoke(ctx, GamePlayLicense_ApplyFreeQualification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gamePlayLicenseClient) RevokeFreeQualification(ctx context.Context, in *RevokeFreeQualificationReq, opts ...grpc.CallOption) (*RevokeFreeQualificationRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RevokeFreeQualificationRsp)
	err := c.cc.Invoke(ctx, GamePlayLicense_RevokeFreeQualification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GamePlayLicenseServer is the server API for GamePlayLicense service.
// All implementations should embed UnimplementedGamePlayLicenseServer
// for forward compatibility
//
// GamePlayLicense 用户游戏免费次数
type GamePlayLicenseServer interface {
	// QueryFreeByKey 查询免费次数, 不计入使用
	QueryFreeQualification(context.Context, *QueryFreeQualificationReq) (*QueryFreeQualificationRsp, error)
	// ConsumeFree 消耗一次免费次数
	ApplyFreeQualification(context.Context, *ApplyFreeQualificationReq) (*ApplyFreeQualificationRsp, error)
	// RevokeFree 撤销已使用的免费次数
	RevokeFreeQualification(context.Context, *RevokeFreeQualificationReq) (*RevokeFreeQualificationRsp, error)
}

// UnimplementedGamePlayLicenseServer should be embedded to have forward compatible implementations.
type UnimplementedGamePlayLicenseServer struct {
}

func (UnimplementedGamePlayLicenseServer) QueryFreeQualification(context.Context, *QueryFreeQualificationReq) (*QueryFreeQualificationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryFreeQualification not implemented")
}
func (UnimplementedGamePlayLicenseServer) ApplyFreeQualification(context.Context, *ApplyFreeQualificationReq) (*ApplyFreeQualificationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyFreeQualification not implemented")
}
func (UnimplementedGamePlayLicenseServer) RevokeFreeQualification(context.Context, *RevokeFreeQualificationReq) (*RevokeFreeQualificationRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeFreeQualification not implemented")
}

// UnsafeGamePlayLicenseServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GamePlayLicenseServer will
// result in compilation errors.
type UnsafeGamePlayLicenseServer interface {
	mustEmbedUnimplementedGamePlayLicenseServer()
}

func RegisterGamePlayLicenseServer(s grpc.ServiceRegistrar, srv GamePlayLicenseServer) {
	s.RegisterService(&GamePlayLicense_ServiceDesc, srv)
}

func _GamePlayLicense_QueryFreeQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFreeQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLicenseServer).QueryFreeQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePlayLicense_QueryFreeQualification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLicenseServer).QueryFreeQualification(ctx, req.(*QueryFreeQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLicense_ApplyFreeQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyFreeQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLicenseServer).ApplyFreeQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePlayLicense_ApplyFreeQualification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLicenseServer).ApplyFreeQualification(ctx, req.(*ApplyFreeQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GamePlayLicense_RevokeFreeQualification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeFreeQualificationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePlayLicenseServer).RevokeFreeQualification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePlayLicense_RevokeFreeQualification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePlayLicenseServer).RevokeFreeQualification(ctx, req.(*RevokeFreeQualificationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GamePlayLicense_ServiceDesc is the grpc.ServiceDesc for GamePlayLicense service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GamePlayLicense_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "kg.game_license.GamePlayLicense",
	HandlerType: (*GamePlayLicenseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryFreeQualification",
			Handler:    _GamePlayLicense_QueryFreeQualification_Handler,
		},
		{
			MethodName: "ApplyFreeQualification",
			Handler:    _GamePlayLicense_ApplyFreeQualification_Handler,
		},
		{
			MethodName: "RevokeFreeQualification",
			Handler:    _GamePlayLicense_RevokeFreeQualification_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_license/game_license.proto",
}
