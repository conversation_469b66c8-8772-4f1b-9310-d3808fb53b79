// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/game_license/game_license.proto

/*
Package game_license is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package game_license

import (
	"context"
	"io"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_GamePlayLicense_QueryFreeQualification_0(ctx context.Context, marshaler runtime.Marshaler, client GamePlayLicenseClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryFreeQualificationReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryFreeQualification(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePlayLicense_QueryFreeQualification_0(ctx context.Context, marshaler runtime.Marshaler, server GamePlayLicenseServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryFreeQualificationReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryFreeQualification(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePlayLicense_ApplyFreeQualification_0(ctx context.Context, marshaler runtime.Marshaler, client GamePlayLicenseClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq ApplyFreeQualificationReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.ApplyFreeQualification(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePlayLicense_ApplyFreeQualification_0(ctx context.Context, marshaler runtime.Marshaler, server GamePlayLicenseServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq ApplyFreeQualificationReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.ApplyFreeQualification(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePlayLicense_RevokeFreeQualification_0(ctx context.Context, marshaler runtime.Marshaler, client GamePlayLicenseClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq RevokeFreeQualificationReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.RevokeFreeQualification(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePlayLicense_RevokeFreeQualification_0(ctx context.Context, marshaler runtime.Marshaler, server GamePlayLicenseServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq RevokeFreeQualificationReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.RevokeFreeQualification(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterGamePlayLicenseHandlerServer registers the http handlers for service GamePlayLicense to "mux".
// UnaryRPC     :call GamePlayLicenseServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterGamePlayLicenseHandlerFromEndpoint instead.
func RegisterGamePlayLicenseHandlerServer(ctx context.Context, mux *runtime.ServeMux, server GamePlayLicenseServer) error {

	mux.Handle("POST", pattern_GamePlayLicense_QueryFreeQualification_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_license.GamePlayLicense/QueryFreeQualification", runtime.WithHTTPPathPattern("/kg.game_license.GamePlayLicense/QueryFreeQualification"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePlayLicense_QueryFreeQualification_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePlayLicense_QueryFreeQualification_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePlayLicense_ApplyFreeQualification_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_license.GamePlayLicense/ApplyFreeQualification", runtime.WithHTTPPathPattern("/kg.game_license.GamePlayLicense/ApplyFreeQualification"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePlayLicense_ApplyFreeQualification_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePlayLicense_ApplyFreeQualification_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePlayLicense_RevokeFreeQualification_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_license.GamePlayLicense/RevokeFreeQualification", runtime.WithHTTPPathPattern("/kg.game_license.GamePlayLicense/RevokeFreeQualification"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePlayLicense_RevokeFreeQualification_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePlayLicense_RevokeFreeQualification_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterGamePlayLicenseHandlerFromEndpoint is same as RegisterGamePlayLicenseHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterGamePlayLicenseHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterGamePlayLicenseHandler(ctx, mux, conn)
}

// RegisterGamePlayLicenseHandler registers the http handlers for service GamePlayLicense to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterGamePlayLicenseHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterGamePlayLicenseHandlerClient(ctx, mux, NewGamePlayLicenseClient(conn))
}

// RegisterGamePlayLicenseHandlerClient registers the http handlers for service GamePlayLicense
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "GamePlayLicenseClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "GamePlayLicenseClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "GamePlayLicenseClient" to call the correct interceptors.
func RegisterGamePlayLicenseHandlerClient(ctx context.Context, mux *runtime.ServeMux, client GamePlayLicenseClient) error {

	mux.Handle("POST", pattern_GamePlayLicense_QueryFreeQualification_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_license.GamePlayLicense/QueryFreeQualification", runtime.WithHTTPPathPattern("/kg.game_license.GamePlayLicense/QueryFreeQualification"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePlayLicense_QueryFreeQualification_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePlayLicense_QueryFreeQualification_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePlayLicense_ApplyFreeQualification_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_license.GamePlayLicense/ApplyFreeQualification", runtime.WithHTTPPathPattern("/kg.game_license.GamePlayLicense/ApplyFreeQualification"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePlayLicense_ApplyFreeQualification_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePlayLicense_ApplyFreeQualification_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePlayLicense_RevokeFreeQualification_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_license.GamePlayLicense/RevokeFreeQualification", runtime.WithHTTPPathPattern("/kg.game_license.GamePlayLicense/RevokeFreeQualification"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePlayLicense_RevokeFreeQualification_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePlayLicense_RevokeFreeQualification_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_GamePlayLicense_QueryFreeQualification_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_license.GamePlayLicense", "QueryFreeQualification"}, ""))

	pattern_GamePlayLicense_ApplyFreeQualification_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_license.GamePlayLicense", "ApplyFreeQualification"}, ""))

	pattern_GamePlayLicense_RevokeFreeQualification_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_license.GamePlayLicense", "RevokeFreeQualification"}, ""))
)

var (
	forward_GamePlayLicense_QueryFreeQualification_0 = runtime.ForwardResponseMessage

	forward_GamePlayLicense_ApplyFreeQualification_0 = runtime.ForwardResponseMessage

	forward_GamePlayLicense_RevokeFreeQualification_0 = runtime.ForwardResponseMessage
)
