{"swagger": "2.0", "info": {"title": "pb/game_chef_god/food_api/game_chef_god_food_api.proto", "version": "version not set"}, "tags": [{"name": "GameChefGodFoodApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_chef_god_food_api.GameChefGodFoodApi/AddFood": {"post": {"summary": "加食材(内部使用)", "operationId": "GameChefGodFoodApi_AddFood", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_food_apiAddFoodRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_food_apiAddFoodReq"}}], "tags": ["GameChefGodFoodApi"]}}, "/game_chef_god_food_api.GameChefGodFoodApi/DeductFood": {"post": {"summary": "扣食材(内部使用,菜品服务)", "operationId": "GameChefGodFoodApi_DeductFood", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_food_apiDeductFoodRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_food_apiDeductFoodReq"}}], "tags": ["GameChefGodFoodApi"]}}, "/game_chef_god_food_api.GameChefGodFoodApi/QueryFoodInfo": {"post": {"summary": "查询食材信息", "operationId": "GameChefGodFoodApi_QueryFoodInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_food_apiQueryFoodInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_food_apiQueryFoodInfoReq"}}], "tags": ["GameChefGodFoodApi"]}}, "/game_chef_god_food_api.GameChefGodFoodApi/QueryUserFoodExpireInfo": {"post": {"summary": "查询用户食材过期信息", "operationId": "GameChefGodFoodApi_QueryUserFoodExpireInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_food_apiQueryUserFoodExpireInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_food_apiQueryUserFoodExpireInfoReq"}}], "tags": ["GameChefGodFoodApi"]}}, "/game_chef_god_food_api.GameChefGodFoodApi/QueryUserFoodList": {"post": {"summary": "查询用户食材列表", "operationId": "GameChefGodFoodApi_QueryUserFoodList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_food_apiQueryUserFoodListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_food_apiQueryUserFoodListReq"}}], "tags": ["GameChefGodFoodApi"]}}}, "definitions": {"game_chef_god_backendFood": {"type": "object", "properties": {"FoodID": {"type": "string", "format": "int64", "title": "食物id"}, "Name": {"type": "string", "title": "食物名"}, "Logo": {"type": "string", "title": "图片"}, "GiftList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_backendGiftInfo"}, "title": "可以从什么礼物中开出(配置不写入)"}}, "title": "食材(表)"}, "game_chef_god_backendGiftInfo": {"type": "object", "properties": {"GiftID": {"type": "string", "format": "int64"}, "GiftName": {"type": "string"}}}, "game_chef_god_food_apiAddFoodReq": {"type": "object", "properties": {"Uid": {"type": "string", "format": "int64"}, "FoodList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_food_apiDeductFoodItem"}}, "Ts": {"type": "string", "format": "int64", "title": "时间戳,自动转换成23:59:59"}, "BillNo": {"type": "string", "title": "订单号"}}}, "game_chef_god_food_apiAddFoodRsp": {"type": "object"}, "game_chef_god_food_apiDeductFoodCode": {"type": "string", "enum": ["Unknown", "Success", "Bill<PERSON><PERSON><PERSON><PERSON>eat", "NotEnough"], "default": "Unknown", "title": "- Success: 成功\n - BillNoRepeat: 订单重复\n - NotEnough: 食材不够"}, "game_chef_god_food_apiDeductFoodItem": {"type": "object", "properties": {"FoodID": {"type": "string", "format": "int64", "title": "食材id"}, "Num": {"type": "integer", "format": "int32"}}}, "game_chef_god_food_apiDeductFoodReq": {"type": "object", "properties": {"Uid": {"type": "string", "format": "int64"}, "FoodList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_food_apiDeductFoodItem"}}, "BillNo": {"type": "string", "title": "订单号"}}}, "game_chef_god_food_apiDeductFoodRsp": {"type": "object", "properties": {"DeductFoodCode": {"$ref": "#/definitions/game_chef_god_food_apiDeductFoodCode"}}}, "game_chef_god_food_apiFood": {"type": "object", "properties": {"Food": {"$ref": "#/definitions/game_chef_god_backendFood"}, "TotalNum": {"type": "integer", "format": "int32", "title": "总数"}, "ExpireInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_food_apiFoodExpireInfo"}, "title": "过期列表"}, "ExpireTips": {"type": "string", "title": "过期提示"}}, "title": "食材"}, "game_chef_god_food_apiFoodExpireInfo": {"type": "object", "properties": {"Num": {"type": "integer", "format": "int32", "title": "数量"}, "ExpireTs": {"type": "string", "format": "int64", "title": "过期时间"}}, "title": "食材过期信息"}, "game_chef_god_food_apiQueryFoodInfoReq": {"type": "object", "properties": {"FoodIDList": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "食材id列表"}}}, "game_chef_god_food_apiQueryFoodInfoRsp": {"type": "object", "properties": {"FoodMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/game_chef_god_backendFood"}}}}, "game_chef_god_food_apiQueryUserFoodExpireInfoReq": {"type": "object", "title": "用登录态"}, "game_chef_god_food_apiQueryUserFoodExpireInfoRsp": {"type": "object", "properties": {"FoodNum": {"type": "integer", "format": "int32", "title": "即将过期的食材种数"}}}, "game_chef_god_food_apiQueryUserFoodListReq": {"type": "object", "title": "用登录态"}, "game_chef_god_food_apiQueryUserFoodListRsp": {"type": "object", "properties": {"FoodList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_food_apiFood"}, "title": "食材列表"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}