// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_chef_god/food_api/game_chef_god_food_api.proto

package food_api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GameChefGodFoodApi_QueryFoodInfo_FullMethodName           = "/game_chef_god_food_api.GameChefGodFoodApi/QueryFoodInfo"
	GameChefGodFoodApi_QueryUserFoodExpireInfo_FullMethodName = "/game_chef_god_food_api.GameChefGodFoodApi/QueryUserFoodExpireInfo"
	GameChefGodFoodApi_QueryUserFoodList_FullMethodName       = "/game_chef_god_food_api.GameChefGodFoodApi/QueryUserFoodList"
	GameChefGodFoodApi_DeductFood_FullMethodName              = "/game_chef_god_food_api.GameChefGodFoodApi/DeductFood"
	GameChefGodFoodApi_AddFood_FullMethodName                 = "/game_chef_god_food_api.GameChefGodFoodApi/AddFood"
)

// GameChefGodFoodApiClient is the client API for GameChefGodFoodApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 厨神食材api
type GameChefGodFoodApiClient interface {
	// 查询食材信息
	QueryFoodInfo(ctx context.Context, in *QueryFoodInfoReq, opts ...grpc.CallOption) (*QueryFoodInfoRsp, error)
	// 查询用户食材过期信息
	QueryUserFoodExpireInfo(ctx context.Context, in *QueryUserFoodExpireInfoReq, opts ...grpc.CallOption) (*QueryUserFoodExpireInfoRsp, error)
	// 查询用户食材列表
	QueryUserFoodList(ctx context.Context, in *QueryUserFoodListReq, opts ...grpc.CallOption) (*QueryUserFoodListRsp, error)
	// 扣食材(内部使用,菜品服务)
	DeductFood(ctx context.Context, in *DeductFoodReq, opts ...grpc.CallOption) (*DeductFoodRsp, error)
	// 加食材(内部使用)
	AddFood(ctx context.Context, in *AddFoodReq, opts ...grpc.CallOption) (*AddFoodRsp, error)
}

type gameChefGodFoodApiClient struct {
	cc grpc.ClientConnInterface
}

func NewGameChefGodFoodApiClient(cc grpc.ClientConnInterface) GameChefGodFoodApiClient {
	return &gameChefGodFoodApiClient{cc}
}

func (c *gameChefGodFoodApiClient) QueryFoodInfo(ctx context.Context, in *QueryFoodInfoReq, opts ...grpc.CallOption) (*QueryFoodInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryFoodInfoRsp)
	err := c.cc.Invoke(ctx, GameChefGodFoodApi_QueryFoodInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodFoodApiClient) QueryUserFoodExpireInfo(ctx context.Context, in *QueryUserFoodExpireInfoReq, opts ...grpc.CallOption) (*QueryUserFoodExpireInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryUserFoodExpireInfoRsp)
	err := c.cc.Invoke(ctx, GameChefGodFoodApi_QueryUserFoodExpireInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodFoodApiClient) QueryUserFoodList(ctx context.Context, in *QueryUserFoodListReq, opts ...grpc.CallOption) (*QueryUserFoodListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryUserFoodListRsp)
	err := c.cc.Invoke(ctx, GameChefGodFoodApi_QueryUserFoodList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodFoodApiClient) DeductFood(ctx context.Context, in *DeductFoodReq, opts ...grpc.CallOption) (*DeductFoodRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeductFoodRsp)
	err := c.cc.Invoke(ctx, GameChefGodFoodApi_DeductFood_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodFoodApiClient) AddFood(ctx context.Context, in *AddFoodReq, opts ...grpc.CallOption) (*AddFoodRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddFoodRsp)
	err := c.cc.Invoke(ctx, GameChefGodFoodApi_AddFood_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameChefGodFoodApiServer is the server API for GameChefGodFoodApi service.
// All implementations should embed UnimplementedGameChefGodFoodApiServer
// for forward compatibility
//
// 厨神食材api
type GameChefGodFoodApiServer interface {
	// 查询食材信息
	QueryFoodInfo(context.Context, *QueryFoodInfoReq) (*QueryFoodInfoRsp, error)
	// 查询用户食材过期信息
	QueryUserFoodExpireInfo(context.Context, *QueryUserFoodExpireInfoReq) (*QueryUserFoodExpireInfoRsp, error)
	// 查询用户食材列表
	QueryUserFoodList(context.Context, *QueryUserFoodListReq) (*QueryUserFoodListRsp, error)
	// 扣食材(内部使用,菜品服务)
	DeductFood(context.Context, *DeductFoodReq) (*DeductFoodRsp, error)
	// 加食材(内部使用)
	AddFood(context.Context, *AddFoodReq) (*AddFoodRsp, error)
}

// UnimplementedGameChefGodFoodApiServer should be embedded to have forward compatible implementations.
type UnimplementedGameChefGodFoodApiServer struct {
}

func (UnimplementedGameChefGodFoodApiServer) QueryFoodInfo(context.Context, *QueryFoodInfoReq) (*QueryFoodInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryFoodInfo not implemented")
}
func (UnimplementedGameChefGodFoodApiServer) QueryUserFoodExpireInfo(context.Context, *QueryUserFoodExpireInfoReq) (*QueryUserFoodExpireInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUserFoodExpireInfo not implemented")
}
func (UnimplementedGameChefGodFoodApiServer) QueryUserFoodList(context.Context, *QueryUserFoodListReq) (*QueryUserFoodListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUserFoodList not implemented")
}
func (UnimplementedGameChefGodFoodApiServer) DeductFood(context.Context, *DeductFoodReq) (*DeductFoodRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeductFood not implemented")
}
func (UnimplementedGameChefGodFoodApiServer) AddFood(context.Context, *AddFoodReq) (*AddFoodRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFood not implemented")
}

// UnsafeGameChefGodFoodApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameChefGodFoodApiServer will
// result in compilation errors.
type UnsafeGameChefGodFoodApiServer interface {
	mustEmbedUnimplementedGameChefGodFoodApiServer()
}

func RegisterGameChefGodFoodApiServer(s grpc.ServiceRegistrar, srv GameChefGodFoodApiServer) {
	s.RegisterService(&GameChefGodFoodApi_ServiceDesc, srv)
}

func _GameChefGodFoodApi_QueryFoodInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFoodInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodFoodApiServer).QueryFoodInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodFoodApi_QueryFoodInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodFoodApiServer).QueryFoodInfo(ctx, req.(*QueryFoodInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodFoodApi_QueryUserFoodExpireInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUserFoodExpireInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodFoodApiServer).QueryUserFoodExpireInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodFoodApi_QueryUserFoodExpireInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodFoodApiServer).QueryUserFoodExpireInfo(ctx, req.(*QueryUserFoodExpireInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodFoodApi_QueryUserFoodList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUserFoodListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodFoodApiServer).QueryUserFoodList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodFoodApi_QueryUserFoodList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodFoodApiServer).QueryUserFoodList(ctx, req.(*QueryUserFoodListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodFoodApi_DeductFood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductFoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodFoodApiServer).DeductFood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodFoodApi_DeductFood_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodFoodApiServer).DeductFood(ctx, req.(*DeductFoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodFoodApi_AddFood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodFoodApiServer).AddFood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodFoodApi_AddFood_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodFoodApiServer).AddFood(ctx, req.(*AddFoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GameChefGodFoodApi_ServiceDesc is the grpc.ServiceDesc for GameChefGodFoodApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GameChefGodFoodApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_chef_god_food_api.GameChefGodFoodApi",
	HandlerType: (*GameChefGodFoodApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryFoodInfo",
			Handler:    _GameChefGodFoodApi_QueryFoodInfo_Handler,
		},
		{
			MethodName: "QueryUserFoodExpireInfo",
			Handler:    _GameChefGodFoodApi_QueryUserFoodExpireInfo_Handler,
		},
		{
			MethodName: "QueryUserFoodList",
			Handler:    _GameChefGodFoodApi_QueryUserFoodList_Handler,
		},
		{
			MethodName: "DeductFood",
			Handler:    _GameChefGodFoodApi_DeductFood_Handler,
		},
		{
			MethodName: "AddFood",
			Handler:    _GameChefGodFoodApi_AddFood_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_chef_god/food_api/game_chef_god_food_api.proto",
}
