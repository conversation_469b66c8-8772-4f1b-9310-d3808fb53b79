// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_chef_god/backend/food.proto

package backend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FoodDropType int32

const (
	FoodDropType_Default      FoodDropType = 0 //默认类型
	FoodDropType_DayFirstFull FoodDropType = 1 //每天首次送满n个
)

// Enum value maps for FoodDropType.
var (
	FoodDropType_name = map[int32]string{
		0: "Default",
		1: "DayFirstFull",
	}
	FoodDropType_value = map[string]int32{
		"Default":      0,
		"DayFirstFull": 1,
	}
)

func (x FoodDropType) Enum() *FoodDropType {
	p := new(FoodDropType)
	*p = x
	return p
}

func (x FoodDropType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FoodDropType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_chef_god_backend_food_proto_enumTypes[0].Descriptor()
}

func (FoodDropType) Type() protoreflect.EnumType {
	return &file_pb_game_chef_god_backend_food_proto_enumTypes[0]
}

func (x FoodDropType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FoodDropType.Descriptor instead.
func (FoodDropType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_food_proto_rawDescGZIP(), []int{0}
}

type GiftInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftID   int64  `protobuf:"varint,1,opt,name=GiftID,proto3" json:"GiftID,omitempty"`
	GiftName string `protobuf:"bytes,2,opt,name=GiftName,proto3" json:"GiftName,omitempty"`
}

func (x *GiftInfo) Reset() {
	*x = GiftInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftInfo) ProtoMessage() {}

func (x *GiftInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftInfo.ProtoReflect.Descriptor instead.
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_food_proto_rawDescGZIP(), []int{0}
}

func (x *GiftInfo) GetGiftID() int64 {
	if x != nil {
		return x.GiftID
	}
	return 0
}

func (x *GiftInfo) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

// 食材(表)
type Food struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FoodID   int64       `protobuf:"varint,1,opt,name=FoodID,proto3" json:"FoodID,omitempty"`    //食物id
	Name     string      `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`         //食物名
	Logo     string      `protobuf:"bytes,3,opt,name=Logo,proto3" json:"Logo,omitempty"`         //图片
	GiftList []*GiftInfo `protobuf:"bytes,4,rep,name=GiftList,proto3" json:"GiftList,omitempty"` //可以从什么礼物中开出(配置不写入)
}

func (x *Food) Reset() {
	*x = Food{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Food) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Food) ProtoMessage() {}

func (x *Food) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Food.ProtoReflect.Descriptor instead.
func (*Food) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_food_proto_rawDescGZIP(), []int{1}
}

func (x *Food) GetFoodID() int64 {
	if x != nil {
		return x.FoodID
	}
	return 0
}

func (x *Food) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Food) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *Food) GetGiftList() []*GiftInfo {
	if x != nil {
		return x.GiftList
	}
	return nil
}

// 食材掉落概率
type FoodDropProbability struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Probability int32 `protobuf:"varint,1,opt,name=Probability,proto3" json:"Probability,omitempty"` //掉落概率
	FoodID      int64 `protobuf:"varint,2,opt,name=FoodID,proto3" json:"FoodID,omitempty"`
	MustHitNum  int32 `protobuf:"varint,3,opt,name=MustHitNum,proto3" json:"MustHitNum,omitempty"` //送n次后必中
	Num         int32 `protobuf:"varint,4,opt,name=Num,proto3" json:"Num,omitempty"`               //掉落的数量
}

func (x *FoodDropProbability) Reset() {
	*x = FoodDropProbability{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FoodDropProbability) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FoodDropProbability) ProtoMessage() {}

func (x *FoodDropProbability) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FoodDropProbability.ProtoReflect.Descriptor instead.
func (*FoodDropProbability) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_food_proto_rawDescGZIP(), []int{2}
}

func (x *FoodDropProbability) GetProbability() int32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

func (x *FoodDropProbability) GetFoodID() int64 {
	if x != nil {
		return x.FoodID
	}
	return 0
}

func (x *FoodDropProbability) GetMustHitNum() int32 {
	if x != nil {
		return x.MustHitNum
	}
	return 0
}

func (x *FoodDropProbability) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

// 食材掉落配置(表)
type FoodDropConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftID              int64                  `protobuf:"varint,1,opt,name=GiftID,proto3" json:"GiftID,omitempty"`                                                     //可获得食材的礼物id
	DropProbabilityList []*FoodDropProbability `protobuf:"bytes,2,rep,name=DropProbabilityList,proto3" json:"DropProbabilityList,omitempty"`                            //掉落概率列表
	FoodDropType        FoodDropType           `protobuf:"varint,3,opt,name=FoodDropType,proto3,enum=game_chef_god_backend.FoodDropType" json:"FoodDropType,omitempty"` //掉落类型
	SendGiftNum         int32                  `protobuf:"varint,4,opt,name=SendGiftNum,proto3" json:"SendGiftNum,omitempty"`                                           //送礼数量(每天首次送满使用)
}

func (x *FoodDropConfig) Reset() {
	*x = FoodDropConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FoodDropConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FoodDropConfig) ProtoMessage() {}

func (x *FoodDropConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_food_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FoodDropConfig.ProtoReflect.Descriptor instead.
func (*FoodDropConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_food_proto_rawDescGZIP(), []int{3}
}

func (x *FoodDropConfig) GetGiftID() int64 {
	if x != nil {
		return x.GiftID
	}
	return 0
}

func (x *FoodDropConfig) GetDropProbabilityList() []*FoodDropProbability {
	if x != nil {
		return x.DropProbabilityList
	}
	return nil
}

func (x *FoodDropConfig) GetFoodDropType() FoodDropType {
	if x != nil {
		return x.FoodDropType
	}
	return FoodDropType_Default
}

func (x *FoodDropConfig) GetSendGiftNum() int32 {
	if x != nil {
		return x.SendGiftNum
	}
	return 0
}

var File_pb_game_chef_god_backend_food_proto protoreflect.FileDescriptor

var file_pb_game_chef_god_backend_food_proto_rawDesc = []byte{
	0x0a, 0x23, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67,
	0x6f, 0x64, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x6f, 0x6f, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66,
	0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x22, 0x3e, 0x0a, 0x08,
	0x47, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x69, 0x66, 0x74,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x47, 0x69, 0x66, 0x74, 0x49, 0x44,
	0x12, 0x1a, 0x0a, 0x08, 0x47, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x47, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x83, 0x01, 0x0a,
	0x04, 0x46, 0x6f, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x46, 0x6f, 0x6f, 0x64, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x46, 0x6f, 0x6f, 0x64, 0x49, 0x44, 0x12, 0x12, 0x0a,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x3b, 0x0a, 0x08, 0x47, 0x69, 0x66, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e,
	0x47, 0x69, 0x66, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x47, 0x69, 0x66, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x81, 0x01, 0x0a, 0x13, 0x46, 0x6f, 0x6f, 0x64, 0x44, 0x72, 0x6f, 0x70, 0x50,
	0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x72,
	0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x46, 0x6f, 0x6f, 0x64, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x46, 0x6f,
	0x6f, 0x64, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x4d, 0x75, 0x73, 0x74, 0x48, 0x69, 0x74, 0x4e,
	0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x4d, 0x75, 0x73, 0x74, 0x48, 0x69,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x4e, 0x75, 0x6d, 0x22, 0xf1, 0x01, 0x0a, 0x0e, 0x46, 0x6f, 0x6f, 0x64, 0x44,
	0x72, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x69, 0x66,
	0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x47, 0x69, 0x66, 0x74, 0x49,
	0x44, 0x12, 0x5c, 0x0a, 0x13, 0x44, 0x72, 0x6f, 0x70, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x44, 0x72, 0x6f, 0x70, 0x50,
	0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x13, 0x44, 0x72, 0x6f, 0x70,
	0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x47, 0x0a, 0x0c, 0x46, 0x6f, 0x6f, 0x64, 0x44, 0x72, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65,
	0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x46, 0x6f,
	0x6f, 0x64, 0x44, 0x72, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x46, 0x6f, 0x6f, 0x64,
	0x44, 0x72, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64,
	0x47, 0x69, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53,
	0x65, 0x6e, 0x64, 0x47, 0x69, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x2a, 0x2d, 0x0a, 0x0c, 0x46, 0x6f,
	0x6f, 0x64, 0x44, 0x72, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x61, 0x79, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x46, 0x75, 0x6c, 0x6c, 0x10, 0x01, 0x42, 0x4d, 0x5a, 0x4b, 0x74, 0x63, 0x6f,
	0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_chef_god_backend_food_proto_rawDescOnce sync.Once
	file_pb_game_chef_god_backend_food_proto_rawDescData = file_pb_game_chef_god_backend_food_proto_rawDesc
)

func file_pb_game_chef_god_backend_food_proto_rawDescGZIP() []byte {
	file_pb_game_chef_god_backend_food_proto_rawDescOnce.Do(func() {
		file_pb_game_chef_god_backend_food_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_chef_god_backend_food_proto_rawDescData)
	})
	return file_pb_game_chef_god_backend_food_proto_rawDescData
}

var file_pb_game_chef_god_backend_food_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_game_chef_god_backend_food_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_game_chef_god_backend_food_proto_goTypes = []interface{}{
	(FoodDropType)(0),           // 0: game_chef_god_backend.FoodDropType
	(*GiftInfo)(nil),            // 1: game_chef_god_backend.GiftInfo
	(*Food)(nil),                // 2: game_chef_god_backend.Food
	(*FoodDropProbability)(nil), // 3: game_chef_god_backend.FoodDropProbability
	(*FoodDropConfig)(nil),      // 4: game_chef_god_backend.FoodDropConfig
}
var file_pb_game_chef_god_backend_food_proto_depIdxs = []int32{
	1, // 0: game_chef_god_backend.Food.GiftList:type_name -> game_chef_god_backend.GiftInfo
	3, // 1: game_chef_god_backend.FoodDropConfig.DropProbabilityList:type_name -> game_chef_god_backend.FoodDropProbability
	0, // 2: game_chef_god_backend.FoodDropConfig.FoodDropType:type_name -> game_chef_god_backend.FoodDropType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_game_chef_god_backend_food_proto_init() }
func file_pb_game_chef_god_backend_food_proto_init() {
	if File_pb_game_chef_god_backend_food_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_chef_god_backend_food_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_backend_food_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Food); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_backend_food_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FoodDropProbability); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_backend_food_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FoodDropConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_chef_god_backend_food_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_chef_god_backend_food_proto_goTypes,
		DependencyIndexes: file_pb_game_chef_god_backend_food_proto_depIdxs,
		EnumInfos:         file_pb_game_chef_god_backend_food_proto_enumTypes,
		MessageInfos:      file_pb_game_chef_god_backend_food_proto_msgTypes,
	}.Build()
	File_pb_game_chef_god_backend_food_proto = out.File
	file_pb_game_chef_god_backend_food_proto_rawDesc = nil
	file_pb_game_chef_god_backend_food_proto_goTypes = nil
	file_pb_game_chef_god_backend_food_proto_depIdxs = nil
}
