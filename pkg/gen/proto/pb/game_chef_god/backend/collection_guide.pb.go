// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_chef_god/backend/collection_guide.proto

package backend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AwardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Logo      string `protobuf:"bytes,1,opt,name=Logo,proto3" json:"Logo,omitempty"`
	AwardName string `protobuf:"bytes,2,opt,name=AwardName,proto3" json:"AwardName,omitempty"`
	Num       int32  `protobuf:"varint,3,opt,name=Num,proto3" json:"Num,omitempty"`
	Price     int32  `protobuf:"varint,4,opt,name=Price,proto3" json:"Price,omitempty"`
}

func (x *AwardInfo) Reset() {
	*x = AwardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AwardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AwardInfo) ProtoMessage() {}

func (x *AwardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AwardInfo.ProtoReflect.Descriptor instead.
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_collection_guide_proto_rawDescGZIP(), []int{0}
}

func (x *AwardInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *AwardInfo) GetAwardName() string {
	if x != nil {
		return x.AwardName
	}
	return ""
}

func (x *AwardInfo) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *AwardInfo) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

// CollectionAward 收集奖励
type CollectionAward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionNum int32        `protobuf:"varint,1,opt,name=CollectionNum,proto3" json:"CollectionNum,omitempty"` //收集数量
	WelfareID     int64        `protobuf:"varint,2,opt,name=WelfareID,proto3" json:"WelfareID,omitempty"`         //奖励的福利id
	AwardList     []*AwardInfo `protobuf:"bytes,3,rep,name=AwardList,proto3" json:"AwardList,omitempty"`          //奖励信息
}

func (x *CollectionAward) Reset() {
	*x = CollectionAward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionAward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionAward) ProtoMessage() {}

func (x *CollectionAward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionAward.ProtoReflect.Descriptor instead.
func (*CollectionAward) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_collection_guide_proto_rawDescGZIP(), []int{1}
}

func (x *CollectionAward) GetCollectionNum() int32 {
	if x != nil {
		return x.CollectionNum
	}
	return 0
}

func (x *CollectionAward) GetWelfareID() int64 {
	if x != nil {
		return x.WelfareID
	}
	return 0
}

func (x *CollectionAward) GetAwardList() []*AwardInfo {
	if x != nil {
		return x.AwardList
	}
	return nil
}

// 收集图鉴(表)
type CollectionGuide struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string             `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`                         //图鉴名
	LimitedTime  bool               `protobuf:"varint,2,opt,name=LimitedTime,proto3" json:"LimitedTime,omitempty"`          //是否限时
	StartTime    int64              `protobuf:"varint,3,opt,name=StartTime,proto3" json:"StartTime,omitempty"`              //开始时间(限时使用)
	EndTime      int64              `protobuf:"varint,4,opt,name=EndTime,proto3" json:"EndTime,omitempty"`                  //结束时间(限时使用)
	DishesIDList []int64            `protobuf:"varint,5,rep,packed,name=DishesIDList,proto3" json:"DishesIDList,omitempty"` //菜品列表
	AwardList    []*CollectionAward `protobuf:"bytes,6,rep,name=AwardList,proto3" json:"AwardList,omitempty"`               //收集奖励列表
	SendBigHorn  int32              `protobuf:"varint,9,opt,name=SendBigHorn,proto3" json:"SendBigHorn,omitempty"`          //是否发大喇叭;0不发,1发
	BigHornID    int64              `protobuf:"varint,10,opt,name=BigHornID,proto3" json:"BigHornID,omitempty"`             //大喇叭ID
}

func (x *CollectionGuide) Reset() {
	*x = CollectionGuide{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionGuide) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionGuide) ProtoMessage() {}

func (x *CollectionGuide) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionGuide.ProtoReflect.Descriptor instead.
func (*CollectionGuide) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_collection_guide_proto_rawDescGZIP(), []int{2}
}

func (x *CollectionGuide) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CollectionGuide) GetLimitedTime() bool {
	if x != nil {
		return x.LimitedTime
	}
	return false
}

func (x *CollectionGuide) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CollectionGuide) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CollectionGuide) GetDishesIDList() []int64 {
	if x != nil {
		return x.DishesIDList
	}
	return nil
}

func (x *CollectionGuide) GetAwardList() []*CollectionAward {
	if x != nil {
		return x.AwardList
	}
	return nil
}

func (x *CollectionGuide) GetSendBigHorn() int32 {
	if x != nil {
		return x.SendBigHorn
	}
	return 0
}

func (x *CollectionGuide) GetBigHornID() int64 {
	if x != nil {
		return x.BigHornID
	}
	return 0
}

type FinishCollectionMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           int64   `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`                            //图鉴ID
	Name         string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                         //图鉴名
	DishesIDList []int64 `protobuf:"varint,3,rep,packed,name=dishesIDList,proto3" json:"dishesIDList,omitempty"` //菜品id列表
	Uin          int64   `protobuf:"varint,5,opt,name=Uin,proto3" json:"Uin,omitempty"`                          //用户id
	OpenID       string  `protobuf:"bytes,6,opt,name=OpenID,proto3" json:"OpenID,omitempty"`                     //openid
	TimeTs       int64   `protobuf:"varint,7,opt,name=TimeTs,proto3" json:"TimeTs,omitempty"`                    //时间,秒级
	Round        int64   `protobuf:"varint,8,opt,name=Round,proto3" json:"Round,omitempty"`                      //第几轮
}

func (x *FinishCollectionMsg) Reset() {
	*x = FinishCollectionMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinishCollectionMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinishCollectionMsg) ProtoMessage() {}

func (x *FinishCollectionMsg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinishCollectionMsg.ProtoReflect.Descriptor instead.
func (*FinishCollectionMsg) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_backend_collection_guide_proto_rawDescGZIP(), []int{3}
}

func (x *FinishCollectionMsg) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *FinishCollectionMsg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FinishCollectionMsg) GetDishesIDList() []int64 {
	if x != nil {
		return x.DishesIDList
	}
	return nil
}

func (x *FinishCollectionMsg) GetUin() int64 {
	if x != nil {
		return x.Uin
	}
	return 0
}

func (x *FinishCollectionMsg) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *FinishCollectionMsg) GetTimeTs() int64 {
	if x != nil {
		return x.TimeTs
	}
	return 0
}

func (x *FinishCollectionMsg) GetRound() int64 {
	if x != nil {
		return x.Round
	}
	return 0
}

var File_pb_game_chef_god_backend_collection_guide_proto protoreflect.FileDescriptor

var file_pb_game_chef_god_backend_collection_guide_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67,
	0x6f, 0x64, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x15, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x22, 0x65, 0x0a, 0x09, 0x41, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x4e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22,
	0x95, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x57, 0x65, 0x6c,
	0x66, 0x61, 0x72, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x57, 0x65,
	0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x44, 0x12, 0x3e, 0x0a, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa9, 0x02, 0x0a, 0x0f, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x75, 0x69, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x69, 0x73,
	0x68, 0x65, 0x73, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0c, 0x44, 0x69, 0x73, 0x68, 0x65, 0x73, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44, 0x0a,
	0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x69, 0x67, 0x48, 0x6f,
	0x72, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53, 0x65, 0x6e, 0x64, 0x42, 0x69,
	0x67, 0x48, 0x6f, 0x72, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e,
	0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72,
	0x6e, 0x49, 0x44, 0x22, 0xb5, 0x01, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x73, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x68, 0x65, 0x73, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x68, 0x65, 0x73, 0x49, 0x44, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x55, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x16, 0x0a,
	0x06, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x54,
	0x69, 0x6d, 0x65, 0x54, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x42, 0x4d, 0x5a, 0x4b, 0x74,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67,
	0x6f, 0x64, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_game_chef_god_backend_collection_guide_proto_rawDescOnce sync.Once
	file_pb_game_chef_god_backend_collection_guide_proto_rawDescData = file_pb_game_chef_god_backend_collection_guide_proto_rawDesc
)

func file_pb_game_chef_god_backend_collection_guide_proto_rawDescGZIP() []byte {
	file_pb_game_chef_god_backend_collection_guide_proto_rawDescOnce.Do(func() {
		file_pb_game_chef_god_backend_collection_guide_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_chef_god_backend_collection_guide_proto_rawDescData)
	})
	return file_pb_game_chef_god_backend_collection_guide_proto_rawDescData
}

var file_pb_game_chef_god_backend_collection_guide_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pb_game_chef_god_backend_collection_guide_proto_goTypes = []interface{}{
	(*AwardInfo)(nil),           // 0: game_chef_god_backend.AwardInfo
	(*CollectionAward)(nil),     // 1: game_chef_god_backend.CollectionAward
	(*CollectionGuide)(nil),     // 2: game_chef_god_backend.CollectionGuide
	(*FinishCollectionMsg)(nil), // 3: game_chef_god_backend.FinishCollectionMsg
}
var file_pb_game_chef_god_backend_collection_guide_proto_depIdxs = []int32{
	0, // 0: game_chef_god_backend.CollectionAward.AwardList:type_name -> game_chef_god_backend.AwardInfo
	1, // 1: game_chef_god_backend.CollectionGuide.AwardList:type_name -> game_chef_god_backend.CollectionAward
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_game_chef_god_backend_collection_guide_proto_init() }
func file_pb_game_chef_god_backend_collection_guide_proto_init() {
	if File_pb_game_chef_god_backend_collection_guide_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AwardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionAward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionGuide); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_backend_collection_guide_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinishCollectionMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_chef_god_backend_collection_guide_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_chef_god_backend_collection_guide_proto_goTypes,
		DependencyIndexes: file_pb_game_chef_god_backend_collection_guide_proto_depIdxs,
		MessageInfos:      file_pb_game_chef_god_backend_collection_guide_proto_msgTypes,
	}.Build()
	File_pb_game_chef_god_backend_collection_guide_proto = out.File
	file_pb_game_chef_god_backend_collection_guide_proto_rawDesc = nil
	file_pb_game_chef_god_backend_collection_guide_proto_goTypes = nil
	file_pb_game_chef_god_backend_collection_guide_proto_depIdxs = nil
}
