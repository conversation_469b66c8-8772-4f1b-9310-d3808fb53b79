// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_chef_god/dish_api/chef_dish_api.proto

package dish_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	backend "kugou_adapter_service/pkg/gen/proto/pb/game_chef_god/backend"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AwardStatus int32

const (
	AwardStatus_StatusUnFinish AwardStatus = 0 //未完成
	AwardStatus_StatusGet      AwardStatus = 1 //可获取
	AwardStatus_StatusFinish   AwardStatus = 2 //已领取
)

// Enum value maps for AwardStatus.
var (
	AwardStatus_name = map[int32]string{
		0: "StatusUnFinish",
		1: "StatusGet",
		2: "StatusFinish",
	}
	AwardStatus_value = map[string]int32{
		"StatusUnFinish": 0,
		"StatusGet":      1,
		"StatusFinish":   2,
	}
)

func (x AwardStatus) Enum() *AwardStatus {
	p := new(AwardStatus)
	*p = x
	return p
}

func (x AwardStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AwardStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_enumTypes[0].Descriptor()
}

func (AwardStatus) Type() protoreflect.EnumType {
	return &file_pb_game_chef_god_dish_api_chef_dish_api_proto_enumTypes[0]
}

func (x AwardStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AwardStatus.Descriptor instead.
func (AwardStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{0}
}

// 扣除余额响应结果枚举
type EmSubBalanceRes int32

const (
	EmSubBalanceRes_ENUM_SUB_BALANCE_RES_SUCCESS   EmSubBalanceRes = 0   // 扣减成功
	EmSubBalanceRes_ENUM_SUB_BALANCE_RES_NO_ENOUGH EmSubBalanceRes = 100 // 余额不足
	EmSubBalanceRes_ENUM_SUB_BALANCE_RES_DUPLICATE EmSubBalanceRes = 101 // 重复请求
	EmSubBalanceRes_ENUM_SUB_BALANCE_RES_FAIL      EmSubBalanceRes = 110 // 其他不需要重试的错误
)

// Enum value maps for EmSubBalanceRes.
var (
	EmSubBalanceRes_name = map[int32]string{
		0:   "ENUM_SUB_BALANCE_RES_SUCCESS",
		100: "ENUM_SUB_BALANCE_RES_NO_ENOUGH",
		101: "ENUM_SUB_BALANCE_RES_DUPLICATE",
		110: "ENUM_SUB_BALANCE_RES_FAIL",
	}
	EmSubBalanceRes_value = map[string]int32{
		"ENUM_SUB_BALANCE_RES_SUCCESS":   0,
		"ENUM_SUB_BALANCE_RES_NO_ENOUGH": 100,
		"ENUM_SUB_BALANCE_RES_DUPLICATE": 101,
		"ENUM_SUB_BALANCE_RES_FAIL":      110,
	}
)

func (x EmSubBalanceRes) Enum() *EmSubBalanceRes {
	p := new(EmSubBalanceRes)
	*p = x
	return p
}

func (x EmSubBalanceRes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmSubBalanceRes) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_enumTypes[1].Descriptor()
}

func (EmSubBalanceRes) Type() protoreflect.EnumType {
	return &file_pb_game_chef_god_dish_api_chef_dish_api_proto_enumTypes[1]
}

func (x EmSubBalanceRes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmSubBalanceRes.Descriptor instead.
func (EmSubBalanceRes) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{1}
}

type QueryDishListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KitchenLevel int64  `protobuf:"varint,1,opt,name=KitchenLevel,proto3" json:"KitchenLevel,omitempty"` //料理坊等级(对应厨神等级);0默认查询最高等级
	StrUin       string `protobuf:"bytes,2,opt,name=StrUin,proto3" json:"StrUin,omitempty"`              //用户uid
}

func (x *QueryDishListReq) Reset() {
	*x = QueryDishListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDishListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDishListReq) ProtoMessage() {}

func (x *QueryDishListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDishListReq.ProtoReflect.Descriptor instead.
func (*QueryDishListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryDishListReq) GetKitchenLevel() int64 {
	if x != nil {
		return x.KitchenLevel
	}
	return 0
}

func (x *QueryDishListReq) GetStrUin() string {
	if x != nil {
		return x.StrUin
	}
	return ""
}

// 菜品的食材
type DishFood struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Food    *backend.Food `protobuf:"bytes,1,opt,name=Food,proto3" json:"Food,omitempty"`
	NeedNum int32         `protobuf:"varint,2,opt,name=NeedNum,proto3" json:"NeedNum,omitempty"` //需要的数量
	OwnNum  int32         `protobuf:"varint,3,opt,name=OwnNum,proto3" json:"OwnNum,omitempty"`   //拥有的数量
}

func (x *DishFood) Reset() {
	*x = DishFood{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DishFood) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DishFood) ProtoMessage() {}

func (x *DishFood) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DishFood.ProtoReflect.Descriptor instead.
func (*DishFood) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{1}
}

func (x *DishFood) GetFood() *backend.Food {
	if x != nil {
		return x.Food
	}
	return nil
}

func (x *DishFood) GetNeedNum() int32 {
	if x != nil {
		return x.NeedNum
	}
	return 0
}

func (x *DishFood) GetOwnNum() int32 {
	if x != nil {
		return x.OwnNum
	}
	return 0
}

type QueryDishListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KitchenLevel  int64               `protobuf:"varint,1,opt,name=KitchenLevel,proto3" json:"KitchenLevel,omitempty"`   //料理坊等级(对应厨神等级);0默认查询当前等级
	CookingNum    int32               `protobuf:"varint,2,opt,name=CookingNum,proto3" json:"CookingNum,omitempty"`       //烹饪的菜品经验值
	NextLevelNeed int32               `protobuf:"varint,3,opt,name=NextLevelNeed,proto3" json:"NextLevelNeed,omitempty"` //下一级需要的数值
	DishList      []*backend.DishInfo `protobuf:"bytes,4,rep,name=DishList,proto3" json:"DishList,omitempty"`            //菜品列表
	KitchenName   string              `protobuf:"bytes,5,opt,name=KitchenName,proto3" json:"KitchenName,omitempty"`      //厨房名字
	SysTs         int64               `protobuf:"varint,6,opt,name=SysTs,proto3" json:"SysTs,omitempty"`                 //系统时间,秒级
}

func (x *QueryDishListRsp) Reset() {
	*x = QueryDishListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDishListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDishListRsp) ProtoMessage() {}

func (x *QueryDishListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDishListRsp.ProtoReflect.Descriptor instead.
func (*QueryDishListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{2}
}

func (x *QueryDishListRsp) GetKitchenLevel() int64 {
	if x != nil {
		return x.KitchenLevel
	}
	return 0
}

func (x *QueryDishListRsp) GetCookingNum() int32 {
	if x != nil {
		return x.CookingNum
	}
	return 0
}

func (x *QueryDishListRsp) GetNextLevelNeed() int32 {
	if x != nil {
		return x.NextLevelNeed
	}
	return 0
}

func (x *QueryDishListRsp) GetDishList() []*backend.DishInfo {
	if x != nil {
		return x.DishList
	}
	return nil
}

func (x *QueryDishListRsp) GetKitchenName() string {
	if x != nil {
		return x.KitchenName
	}
	return ""
}

func (x *QueryDishListRsp) GetSysTs() int64 {
	if x != nil {
		return x.SysTs
	}
	return 0
}

// 烹饪记录请求
type QueryCookingRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartIndex int32 `protobuf:"varint,2,opt,name=StartIndex,proto3" json:"StartIndex,omitempty"` // 首次不传（默认为0） 后续透传后台返回的
}

func (x *QueryCookingRecordReq) Reset() {
	*x = QueryCookingRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCookingRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCookingRecordReq) ProtoMessage() {}

func (x *QueryCookingRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCookingRecordReq.ProtoReflect.Descriptor instead.
func (*QueryCookingRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{3}
}

func (x *QueryCookingRecordReq) GetStartIndex() int32 {
	if x != nil {
		return x.StartIndex
	}
	return 0
}

// 烹饪记录
type CookingRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dish      *backend.DishInfo `protobuf:"bytes,1,opt,name=Dish,proto3" json:"Dish,omitempty"`            //菜品
	CookingTs int64             `protobuf:"varint,2,opt,name=CookingTs,proto3" json:"CookingTs,omitempty"` //烹饪时间
}

func (x *CookingRecord) Reset() {
	*x = CookingRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CookingRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CookingRecord) ProtoMessage() {}

func (x *CookingRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CookingRecord.ProtoReflect.Descriptor instead.
func (*CookingRecord) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{4}
}

func (x *CookingRecord) GetDish() *backend.DishInfo {
	if x != nil {
		return x.Dish
	}
	return nil
}

func (x *CookingRecord) GetCookingTs() int64 {
	if x != nil {
		return x.CookingTs
	}
	return 0
}

// 烹饪记录返回
type QueryCookingRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasMore    bool             `protobuf:"varint,1,opt,name=HasMore,proto3" json:"HasMore,omitempty"`       //是否还有下一页
	StartIndex int32            `protobuf:"varint,2,opt,name=StartIndex,proto3" json:"StartIndex,omitempty"` // 后台透传
	RecordList []*CookingRecord `protobuf:"bytes,3,rep,name=RecordList,proto3" json:"RecordList,omitempty"`  //烹饪记录
}

func (x *QueryCookingRecordRsp) Reset() {
	*x = QueryCookingRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCookingRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCookingRecordRsp) ProtoMessage() {}

func (x *QueryCookingRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCookingRecordRsp.ProtoReflect.Descriptor instead.
func (*QueryCookingRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{5}
}

func (x *QueryCookingRecordRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *QueryCookingRecordRsp) GetStartIndex() int32 {
	if x != nil {
		return x.StartIndex
	}
	return 0
}

func (x *QueryCookingRecordRsp) GetRecordList() []*CookingRecord {
	if x != nil {
		return x.RecordList
	}
	return nil
}

type CookReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DishID int64  `protobuf:"varint,1,opt,name=DishID,proto3" json:"DishID,omitempty"` //菜品ID
	RoomID string `protobuf:"bytes,2,opt,name=RoomID,proto3" json:"RoomID,omitempty"`  //房间ID
	ShowID string `protobuf:"bytes,3,opt,name=ShowID,proto3" json:"ShowID,omitempty"`  //showID
}

func (x *CookReq) Reset() {
	*x = CookReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CookReq) ProtoMessage() {}

func (x *CookReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CookReq.ProtoReflect.Descriptor instead.
func (*CookReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{6}
}

func (x *CookReq) GetDishID() int64 {
	if x != nil {
		return x.DishID
	}
	return 0
}

func (x *CookReq) GetRoomID() string {
	if x != nil {
		return x.RoomID
	}
	return ""
}

func (x *CookReq) GetShowID() string {
	if x != nil {
		return x.ShowID
	}
	return ""
}

type CookRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CookingResult backend.CookResult `protobuf:"varint,1,opt,name=CookingResult,proto3,enum=game_chef_god_backend.CookResult" json:"CookingResult,omitempty"` //烹饪结果
	DishLogo      string             `protobuf:"bytes,2,opt,name=DishLogo,proto3" json:"DishLogo,omitempty"`                                                  //菜品
	ResultDesc    []string           `protobuf:"bytes,3,rep,name=ResultDesc,proto3" json:"ResultDesc,omitempty"`                                              //结果文案
	GiftPic       []string           `protobuf:"bytes,4,rep,name=GiftPic,proto3" json:"GiftPic,omitempty"`                                                    //成功时的奖励图片
	DishName      string             `protobuf:"bytes,5,opt,name=DishName,proto3" json:"DishName,omitempty"`                                                  //菜品名
	DishLevel     int64              `protobuf:"varint,6,opt,name=DishLevel,proto3" json:"DishLevel,omitempty"`                                               //菜品等级
	Score         int32              `protobuf:"varint,7,opt,name=Score,proto3" json:"Score,omitempty"`                                                       //获得厨力值
}

func (x *CookRsp) Reset() {
	*x = CookRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CookRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CookRsp) ProtoMessage() {}

func (x *CookRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CookRsp.ProtoReflect.Descriptor instead.
func (*CookRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{7}
}

func (x *CookRsp) GetCookingResult() backend.CookResult {
	if x != nil {
		return x.CookingResult
	}
	return backend.CookResult(0)
}

func (x *CookRsp) GetDishLogo() string {
	if x != nil {
		return x.DishLogo
	}
	return ""
}

func (x *CookRsp) GetResultDesc() []string {
	if x != nil {
		return x.ResultDesc
	}
	return nil
}

func (x *CookRsp) GetGiftPic() []string {
	if x != nil {
		return x.GiftPic
	}
	return nil
}

func (x *CookRsp) GetDishName() string {
	if x != nil {
		return x.DishName
	}
	return ""
}

func (x *CookRsp) GetDishLevel() int64 {
	if x != nil {
		return x.DishLevel
	}
	return 0
}

func (x *CookRsp) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type QueryCollectionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryCollectionListReq) Reset() {
	*x = QueryCollectionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCollectionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCollectionListReq) ProtoMessage() {}

func (x *QueryCollectionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCollectionListReq.ProtoReflect.Descriptor instead.
func (*QueryCollectionListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{8}
}

type QueryCollectionListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionIDList []int64 `protobuf:"varint,1,rep,packed,name=CollectionIDList,proto3" json:"CollectionIDList,omitempty"` //收集图鉴列表id
}

func (x *QueryCollectionListRsp) Reset() {
	*x = QueryCollectionListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCollectionListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCollectionListRsp) ProtoMessage() {}

func (x *QueryCollectionListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCollectionListRsp.ProtoReflect.Descriptor instead.
func (*QueryCollectionListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{9}
}

func (x *QueryCollectionListRsp) GetCollectionIDList() []int64 {
	if x != nil {
		return x.CollectionIDList
	}
	return nil
}

type QueryCollectionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionIDS []int64 `protobuf:"varint,1,rep,packed,name=CollectionIDS,proto3" json:"CollectionIDS,omitempty"` //收集图鉴id
	StrUin        string  `protobuf:"bytes,2,opt,name=StrUin,proto3" json:"StrUin,omitempty"`                       //用户uid
}

func (x *QueryCollectionReq) Reset() {
	*x = QueryCollectionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCollectionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCollectionReq) ProtoMessage() {}

func (x *QueryCollectionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCollectionReq.ProtoReflect.Descriptor instead.
func (*QueryCollectionReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{10}
}

func (x *QueryCollectionReq) GetCollectionIDS() []int64 {
	if x != nil {
		return x.CollectionIDS
	}
	return nil
}

func (x *QueryCollectionReq) GetStrUin() string {
	if x != nil {
		return x.StrUin
	}
	return ""
}

// 菜品图鉴收集
type DishCollection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dish          *backend.DishInfo `protobuf:"bytes,1,opt,name=Dish,proto3" json:"Dish,omitempty"`                    //菜品
	CollectionNum int32             `protobuf:"varint,2,opt,name=CollectionNum,proto3" json:"CollectionNum,omitempty"` //收集的次数
}

func (x *DishCollection) Reset() {
	*x = DishCollection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DishCollection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DishCollection) ProtoMessage() {}

func (x *DishCollection) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DishCollection.ProtoReflect.Descriptor instead.
func (*DishCollection) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{11}
}

func (x *DishCollection) GetDish() *backend.DishInfo {
	if x != nil {
		return x.Dish
	}
	return nil
}

func (x *DishCollection) GetCollectionNum() int32 {
	if x != nil {
		return x.CollectionNum
	}
	return 0
}

type CollAwardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NowNum    int32                `protobuf:"varint,1,opt,name=NowNum,proto3" json:"NowNum,omitempty"`      //当前进度数量
	TotalNum  int32                `protobuf:"varint,2,opt,name=TotalNum,proto3" json:"TotalNum,omitempty"`  //奖励总进度
	AwardList []*backend.AwardInfo `protobuf:"bytes,3,rep,name=AwardList,proto3" json:"AwardList,omitempty"` //奖励信息
	Status    int32                `protobuf:"varint,4,opt,name=Status,proto3" json:"Status,omitempty"`      //是否可领取 查看AwardStatus枚举
}

func (x *CollAwardInfo) Reset() {
	*x = CollAwardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollAwardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollAwardInfo) ProtoMessage() {}

func (x *CollAwardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollAwardInfo.ProtoReflect.Descriptor instead.
func (*CollAwardInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{12}
}

func (x *CollAwardInfo) GetNowNum() int32 {
	if x != nil {
		return x.NowNum
	}
	return 0
}

func (x *CollAwardInfo) GetTotalNum() int32 {
	if x != nil {
		return x.TotalNum
	}
	return 0
}

func (x *CollAwardInfo) GetAwardList() []*backend.AwardInfo {
	if x != nil {
		return x.AwardList
	}
	return nil
}

func (x *CollAwardInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type LevelMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int64 `protobuf:"varint,1,opt,name=Level,proto3" json:"Level,omitempty"` //菜品等级
	Nums  int64 `protobuf:"varint,2,opt,name=Nums,proto3" json:"Nums,omitempty"`   //已收集数量
}

func (x *LevelMember) Reset() {
	*x = LevelMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LevelMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelMember) ProtoMessage() {}

func (x *LevelMember) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelMember.ProtoReflect.Descriptor instead.
func (*LevelMember) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{13}
}

func (x *LevelMember) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *LevelMember) GetNums() int64 {
	if x != nil {
		return x.Nums
	}
	return 0
}

type CollectionMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                int64             `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`                               //图鉴ID
	DishCollectionNum int32             `protobuf:"varint,2,opt,name=DishCollectionNum,proto3" json:"DishCollectionNum,omitempty"` //已收集菜品数
	DishList          []*DishCollection `protobuf:"bytes,3,rep,name=DishList,proto3" json:"DishList,omitempty"`                    //菜品
	CollectionName    string            `protobuf:"bytes,4,opt,name=CollectionName,proto3" json:"CollectionName,omitempty"`        //图鉴名称
	TotalNum          int64             `protobuf:"varint,5,opt,name=TotalNum,proto3" json:"TotalNum,omitempty"`                   //菜品总量
	CollAwardInfo     *CollAwardInfo    `protobuf:"bytes,6,opt,name=CollAwardInfo,proto3" json:"CollAwardInfo,omitempty"`          //图鉴奖励
	StartTs           int64             `protobuf:"varint,7,opt,name=StartTs,proto3" json:"StartTs,omitempty"`                     //开始时间 0为长期存在
	EndTs             int64             `protobuf:"varint,8,opt,name=EndTs,proto3" json:"EndTs,omitempty"`                         //结束时间 0为长期存在
	SysTs             int64             `protobuf:"varint,9,opt,name=SysTs,proto3" json:"SysTs,omitempty"`                         //当前时间 0为长期存在
	AllReadyColl      []*LevelMember    `protobuf:"bytes,10,rep,name=AllReadyColl,proto3" json:"AllReadyColl,omitempty"`           //已收集情况
}

func (x *CollectionMeta) Reset() {
	*x = CollectionMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectionMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectionMeta) ProtoMessage() {}

func (x *CollectionMeta) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectionMeta.ProtoReflect.Descriptor instead.
func (*CollectionMeta) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{14}
}

func (x *CollectionMeta) GetID() int64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *CollectionMeta) GetDishCollectionNum() int32 {
	if x != nil {
		return x.DishCollectionNum
	}
	return 0
}

func (x *CollectionMeta) GetDishList() []*DishCollection {
	if x != nil {
		return x.DishList
	}
	return nil
}

func (x *CollectionMeta) GetCollectionName() string {
	if x != nil {
		return x.CollectionName
	}
	return ""
}

func (x *CollectionMeta) GetTotalNum() int64 {
	if x != nil {
		return x.TotalNum
	}
	return 0
}

func (x *CollectionMeta) GetCollAwardInfo() *CollAwardInfo {
	if x != nil {
		return x.CollAwardInfo
	}
	return nil
}

func (x *CollectionMeta) GetStartTs() int64 {
	if x != nil {
		return x.StartTs
	}
	return 0
}

func (x *CollectionMeta) GetEndTs() int64 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *CollectionMeta) GetSysTs() int64 {
	if x != nil {
		return x.SysTs
	}
	return 0
}

func (x *CollectionMeta) GetAllReadyColl() []*LevelMember {
	if x != nil {
		return x.AllReadyColl
	}
	return nil
}

type QueryCollectionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataList []*CollectionMeta `protobuf:"bytes,1,rep,name=DataList,proto3" json:"DataList,omitempty"`
}

func (x *QueryCollectionRsp) Reset() {
	*x = QueryCollectionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCollectionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCollectionRsp) ProtoMessage() {}

func (x *QueryCollectionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCollectionRsp.ProtoReflect.Descriptor instead.
func (*QueryCollectionRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{15}
}

func (x *QueryCollectionRsp) GetDataList() []*CollectionMeta {
	if x != nil {
		return x.DataList
	}
	return nil
}

// 请求获取余额
type GetBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LUid   int64             `protobuf:"varint,1,opt,name=lUid,proto3" json:"lUid,omitempty"`
	MapExt map[string]string `protobuf:"bytes,2,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展字段
}

func (x *GetBalanceReq) Reset() {
	*x = GetBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceReq) ProtoMessage() {}

func (x *GetBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceReq.ProtoReflect.Descriptor instead.
func (*GetBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{16}
}

func (x *GetBalanceReq) GetLUid() int64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

func (x *GetBalanceReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

// 响应获取余额
type GetBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LBalance int32  `protobuf:"varint,1,opt,name=lBalance,proto3" json:"lBalance,omitempty"`
	StrIcon  string `protobuf:"bytes,2,opt,name=strIcon,proto3" json:"strIcon,omitempty"` // 货币图标
	StrTag   string `protobuf:"bytes,3,opt,name=strTag,proto3" json:"strTag,omitempty"`
	StrName  string `protobuf:"bytes,4,opt,name=strName,proto3" json:"strName,omitempty"` // 货币名称
}

func (x *GetBalanceRsp) Reset() {
	*x = GetBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceRsp) ProtoMessage() {}

func (x *GetBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceRsp.ProtoReflect.Descriptor instead.
func (*GetBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{17}
}

func (x *GetBalanceRsp) GetLBalance() int32 {
	if x != nil {
		return x.LBalance
	}
	return 0
}

func (x *GetBalanceRsp) GetStrIcon() string {
	if x != nil {
		return x.StrIcon
	}
	return ""
}

func (x *GetBalanceRsp) GetStrTag() string {
	if x != nil {
		return x.StrTag
	}
	return ""
}

func (x *GetBalanceRsp) GetStrName() string {
	if x != nil {
		return x.StrName
	}
	return ""
}

// 扣除余额请求
type SubBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LUid        int64             `protobuf:"varint,1,opt,name=lUid,proto3" json:"lUid,omitempty"`
	LSubAmount  int64             `protobuf:"varint,2,opt,name=lSubAmount,proto3" json:"lSubAmount,omitempty"`
	StrUniqueId string            `protobuf:"bytes,3,opt,name=strUniqueId,proto3" json:"strUniqueId,omitempty"`
	LTs         int64             `protobuf:"varint,4,opt,name=lTs,proto3" json:"lTs,omitempty"`
	MapExt      map[string]string `protobuf:"bytes,5,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 扩展字段
	UExchangeID uint32            `protobuf:"varint,6,opt,name=uExchangeID,proto3" json:"uExchangeID,omitempty"`
}

func (x *SubBalanceReq) Reset() {
	*x = SubBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubBalanceReq) ProtoMessage() {}

func (x *SubBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubBalanceReq.ProtoReflect.Descriptor instead.
func (*SubBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{18}
}

func (x *SubBalanceReq) GetLUid() int64 {
	if x != nil {
		return x.LUid
	}
	return 0
}

func (x *SubBalanceReq) GetLSubAmount() int64 {
	if x != nil {
		return x.LSubAmount
	}
	return 0
}

func (x *SubBalanceReq) GetStrUniqueId() string {
	if x != nil {
		return x.StrUniqueId
	}
	return ""
}

func (x *SubBalanceReq) GetLTs() int64 {
	if x != nil {
		return x.LTs
	}
	return 0
}

func (x *SubBalanceReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *SubBalanceReq) GetUExchangeID() uint32 {
	if x != nil {
		return x.UExchangeID
	}
	return 0
}

// 扣除余额响应
type SubBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IRes int32 `protobuf:"varint,1,opt,name=iRes,proto3" json:"iRes,omitempty"`
}

func (x *SubBalanceRsp) Reset() {
	*x = SubBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubBalanceRsp) ProtoMessage() {}

func (x *SubBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubBalanceRsp.ProtoReflect.Descriptor instead.
func (*SubBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{19}
}

func (x *SubBalanceRsp) GetIRes() int32 {
	if x != nil {
		return x.IRes
	}
	return 0
}

// 检查信息
type CheckInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrOrderID   string            `protobuf:"bytes,1,opt,name=strOrderID,proto3" json:"strOrderID,omitempty"`                                                                                 // 订单ID
	StrWelfareID string            `protobuf:"bytes,2,opt,name=strWelfareID,proto3" json:"strWelfareID,omitempty"`                                                                             // 福利ID
	UNum         uint32            `protobuf:"varint,3,opt,name=uNum,proto3" json:"uNum,omitempty"`                                                                                            // 发送数量
	StrUid       string            `protobuf:"bytes,4,opt,name=strUid,proto3" json:"strUid,omitempty"`                                                                                         // 接收者
	USendTs      uint32            `protobuf:"varint,5,opt,name=uSendTs,proto3" json:"uSendTs,omitempty"`                                                                                      // 业务方发放时间秒级
	StrReason    string            `protobuf:"bytes,6,opt,name=strReason,proto3" json:"strReason,omitempty"`                                                                                   // 业务方发放原因
	StrExtID     string            `protobuf:"bytes,7,opt,name=strExtID,proto3" json:"strExtID,omitempty"`                                                                                     // 业务方带过来的traceid 用于链路跟踪，填抽奖ID、任务ID、活动ID
	MapExt       map[string]string `protobuf:"bytes,8,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 业务方带过来的额外字段信息，5个以内
	StrProgram   string            `protobuf:"bytes,9,opt,name=strProgram,proto3" json:"strProgram,omitempty"`                                                                                 // 请求发放福利的业务方程序名
}

func (x *CheckInfo) Reset() {
	*x = CheckInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInfo) ProtoMessage() {}

func (x *CheckInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInfo.ProtoReflect.Descriptor instead.
func (*CheckInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{20}
}

func (x *CheckInfo) GetStrOrderID() string {
	if x != nil {
		return x.StrOrderID
	}
	return ""
}

func (x *CheckInfo) GetStrWelfareID() string {
	if x != nil {
		return x.StrWelfareID
	}
	return ""
}

func (x *CheckInfo) GetUNum() uint32 {
	if x != nil {
		return x.UNum
	}
	return 0
}

func (x *CheckInfo) GetStrUid() string {
	if x != nil {
		return x.StrUid
	}
	return ""
}

func (x *CheckInfo) GetUSendTs() uint32 {
	if x != nil {
		return x.USendTs
	}
	return 0
}

func (x *CheckInfo) GetStrReason() string {
	if x != nil {
		return x.StrReason
	}
	return ""
}

func (x *CheckInfo) GetStrExtID() string {
	if x != nil {
		return x.StrExtID
	}
	return ""
}

func (x *CheckInfo) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *CheckInfo) GetStrProgram() string {
	if x != nil {
		return x.StrProgram
	}
	return ""
}

// 回调信息
type CallInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrCallBackCmd string `protobuf:"bytes,1,opt,name=strCallBackCmd,proto3" json:"strCallBackCmd,omitempty"`
}

func (x *CallInfo) Reset() {
	*x = CallInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallInfo) ProtoMessage() {}

func (x *CallInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallInfo.ProtoReflect.Descriptor instead.
func (*CallInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{21}
}

func (x *CallInfo) GetStrCallBackCmd() string {
	if x != nil {
		return x.StrCallBackCmd
	}
	return ""
}

// 业务检查发送请求
type BusinessCheckSendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UAppID      uint32     `protobuf:"varint,1,opt,name=uAppID,proto3" json:"uAppID,omitempty"`          // appID 参考proto_uni_sender_svr.emAPPID
	StCheckInfo *CheckInfo `protobuf:"bytes,2,opt,name=stCheckInfo,proto3" json:"stCheckInfo,omitempty"` // 检查信息
	StCallInfo  *CallInfo  `protobuf:"bytes,3,opt,name=stCallInfo,proto3" json:"stCallInfo,omitempty"`   // 回调信息 仅仅wesing有需要
}

func (x *BusinessCheckSendReq) Reset() {
	*x = BusinessCheckSendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCheckSendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCheckSendReq) ProtoMessage() {}

func (x *BusinessCheckSendReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCheckSendReq.ProtoReflect.Descriptor instead.
func (*BusinessCheckSendReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{22}
}

func (x *BusinessCheckSendReq) GetUAppID() uint32 {
	if x != nil {
		return x.UAppID
	}
	return 0
}

func (x *BusinessCheckSendReq) GetStCheckInfo() *CheckInfo {
	if x != nil {
		return x.StCheckInfo
	}
	return nil
}

func (x *BusinessCheckSendReq) GetStCallInfo() *CallInfo {
	if x != nil {
		return x.StCallInfo
	}
	return nil
}

// 业务检查发送响应
type BusinessCheckSendRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BPassp bool `protobuf:"varint,1,opt,name=bPassp,proto3" json:"bPassp,omitempty"` // 检查是否通过
}

func (x *BusinessCheckSendRsp) Reset() {
	*x = BusinessCheckSendRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCheckSendRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCheckSendRsp) ProtoMessage() {}

func (x *BusinessCheckSendRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCheckSendRsp.ProtoReflect.Descriptor instead.
func (*BusinessCheckSendRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{23}
}

func (x *BusinessCheckSendRsp) GetBPassp() bool {
	if x != nil {
		return x.BPassp
	}
	return false
}

// 查询兑换信息
type GetExchangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetExchangeReq) Reset() {
	*x = GetExchangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeReq) ProtoMessage() {}

func (x *GetExchangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeReq.ProtoReflect.Descriptor instead.
func (*GetExchangeReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{24}
}

// 查询兑换信息
type GetExchangeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance    int32                `protobuf:"varint,1,opt,name=Balance,proto3" json:"Balance,omitempty"`       //厨力值余额
	AwardList  []*backend.AwardInfo `protobuf:"bytes,2,rep,name=AwardList,proto3" json:"AwardList,omitempty"`    //奖励信息
	ExchangeID int32                `protobuf:"varint,3,opt,name=ExchangeID,proto3" json:"ExchangeID,omitempty"` //兑换页ID
}

func (x *GetExchangeRsp) Reset() {
	*x = GetExchangeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangeRsp) ProtoMessage() {}

func (x *GetExchangeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangeRsp.ProtoReflect.Descriptor instead.
func (*GetExchangeRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{25}
}

func (x *GetExchangeRsp) GetBalance() int32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetExchangeRsp) GetAwardList() []*backend.AwardInfo {
	if x != nil {
		return x.AwardList
	}
	return nil
}

func (x *GetExchangeRsp) GetExchangeID() int32 {
	if x != nil {
		return x.ExchangeID
	}
	return 0
}

// 领取奖励
type GetAwardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollID int64 `protobuf:"varint,1,opt,name=CollID,proto3" json:"CollID,omitempty"` //图鉴ID
	Level  int64 `protobuf:"varint,2,opt,name=Level,proto3" json:"Level,omitempty"`   //奖励等级
}

func (x *GetAwardReq) Reset() {
	*x = GetAwardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAwardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAwardReq) ProtoMessage() {}

func (x *GetAwardReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAwardReq.ProtoReflect.Descriptor instead.
func (*GetAwardReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{26}
}

func (x *GetAwardReq) GetCollID() int64 {
	if x != nil {
		return x.CollID
	}
	return 0
}

func (x *GetAwardReq) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

type GetAwardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AwardList []*backend.AwardInfo `protobuf:"bytes,3,rep,name=AwardList,proto3" json:"AwardList,omitempty"` //奖励信息
}

func (x *GetAwardRsp) Reset() {
	*x = GetAwardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAwardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAwardRsp) ProtoMessage() {}

func (x *GetAwardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAwardRsp.ProtoReflect.Descriptor instead.
func (*GetAwardRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{27}
}

func (x *GetAwardRsp) GetAwardList() []*backend.AwardInfo {
	if x != nil {
		return x.AwardList
	}
	return nil
}

// 稀有菜品跑马灯
type SurprisedNotice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CookMsgList []string `protobuf:"bytes,1,rep,name=CookMsgList,proto3" json:"CookMsgList,omitempty"`
}

func (x *SurprisedNotice) Reset() {
	*x = SurprisedNotice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurprisedNotice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurprisedNotice) ProtoMessage() {}

func (x *SurprisedNotice) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurprisedNotice.ProtoReflect.Descriptor instead.
func (*SurprisedNotice) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{28}
}

func (x *SurprisedNotice) GetCookMsgList() []string {
	if x != nil {
		return x.CookMsgList
	}
	return nil
}

// 活动页信息
type GetBannerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetBannerInfoReq) Reset() {
	*x = GetBannerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBannerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBannerInfoReq) ProtoMessage() {}

func (x *GetBannerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBannerInfoReq.ProtoReflect.Descriptor instead.
func (*GetBannerInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{29}
}

type GetBannerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Notices []string `protobuf:"bytes,1,rep,name=notices,proto3" json:"notices,omitempty"`
}

func (x *GetBannerInfoRsp) Reset() {
	*x = GetBannerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBannerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBannerInfoRsp) ProtoMessage() {}

func (x *GetBannerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBannerInfoRsp.ProtoReflect.Descriptor instead.
func (*GetBannerInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{30}
}

func (x *GetBannerInfoRsp) GetNotices() []string {
	if x != nil {
		return x.Notices
	}
	return nil
}

type GetUserChefInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrUin string `protobuf:"bytes,1,opt,name=StrUin,proto3" json:"StrUin,omitempty"` //用户uin
}

func (x *GetUserChefInfoReq) Reset() {
	*x = GetUserChefInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserChefInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserChefInfoReq) ProtoMessage() {}

func (x *GetUserChefInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserChefInfoReq.ProtoReflect.Descriptor instead.
func (*GetUserChefInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{31}
}

func (x *GetUserChefInfoReq) GetStrUin() string {
	if x != nil {
		return x.StrUin
	}
	return ""
}

type GetUserChefInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Logo         string         `protobuf:"bytes,1,opt,name=Logo,proto3" json:"Logo,omitempty"`                 //
	Name         string         `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`                 //
	AllReadyColl []*LevelMember `protobuf:"bytes,3,rep,name=AllReadyColl,proto3" json:"AllReadyColl,omitempty"` //已收集情况
}

func (x *GetUserChefInfoRsp) Reset() {
	*x = GetUserChefInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserChefInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserChefInfoRsp) ProtoMessage() {}

func (x *GetUserChefInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserChefInfoRsp.ProtoReflect.Descriptor instead.
func (*GetUserChefInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{32}
}

func (x *GetUserChefInfoRsp) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *GetUserChefInfoRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserChefInfoRsp) GetAllReadyColl() []*LevelMember {
	if x != nil {
		return x.AllReadyColl
	}
	return nil
}

type AssetRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAssetName string `protobuf:"bytes,1,opt,name=StrAssetName,proto3" json:"StrAssetName,omitempty"` //货币名称
	StrAssetLogo string `protobuf:"bytes,2,opt,name=StrAssetLogo,proto3" json:"StrAssetLogo,omitempty"` //logo
	StrReason    string `protobuf:"bytes,3,opt,name=StrReason,proto3" json:"StrReason,omitempty"`       //原因
	LNum         int32  `protobuf:"varint,4,opt,name=LNum,proto3" json:"LNum,omitempty"`                //数量
	UTs          int32  `protobuf:"varint,5,opt,name=UTs,proto3" json:"UTs,omitempty"`                  //时间
}

func (x *AssetRecord) Reset() {
	*x = AssetRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetRecord) ProtoMessage() {}

func (x *AssetRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetRecord.ProtoReflect.Descriptor instead.
func (*AssetRecord) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{33}
}

func (x *AssetRecord) GetStrAssetName() string {
	if x != nil {
		return x.StrAssetName
	}
	return ""
}

func (x *AssetRecord) GetStrAssetLogo() string {
	if x != nil {
		return x.StrAssetLogo
	}
	return ""
}

func (x *AssetRecord) GetStrReason() string {
	if x != nil {
		return x.StrReason
	}
	return ""
}

func (x *AssetRecord) GetLNum() int32 {
	if x != nil {
		return x.LNum
	}
	return 0
}

func (x *AssetRecord) GetUTs() int32 {
	if x != nil {
		return x.UTs
	}
	return 0
}

// 货币记录
type AssetRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LUid        string `protobuf:"bytes,1,opt,name=LUid,proto3" json:"LUid,omitempty"`
	StrPassBack string `protobuf:"bytes,2,opt,name=StrPassBack,proto3" json:"StrPassBack,omitempty"`
}

func (x *AssetRecordReq) Reset() {
	*x = AssetRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetRecordReq) ProtoMessage() {}

func (x *AssetRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetRecordReq.ProtoReflect.Descriptor instead.
func (*AssetRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{34}
}

func (x *AssetRecordReq) GetLUid() string {
	if x != nil {
		return x.LUid
	}
	return ""
}

func (x *AssetRecordReq) GetStrPassBack() string {
	if x != nil {
		return x.StrPassBack
	}
	return ""
}

type AssetRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrPassBack string         `protobuf:"bytes,1,opt,name=StrPassBack,proto3" json:"StrPassBack,omitempty"`
	BHasMore    bool           `protobuf:"varint,2,opt,name=BHasMore,proto3" json:"BHasMore,omitempty"`
	VctRecord   []*AssetRecord `protobuf:"bytes,3,rep,name=VctRecord,proto3" json:"VctRecord,omitempty"`
}

func (x *AssetRecordRsp) Reset() {
	*x = AssetRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetRecordRsp) ProtoMessage() {}

func (x *AssetRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetRecordRsp.ProtoReflect.Descriptor instead.
func (*AssetRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP(), []int{35}
}

func (x *AssetRecordRsp) GetStrPassBack() string {
	if x != nil {
		return x.StrPassBack
	}
	return ""
}

func (x *AssetRecordRsp) GetBHasMore() bool {
	if x != nil {
		return x.BHasMore
	}
	return false
}

func (x *AssetRecordRsp) GetVctRecord() []*AssetRecord {
	if x != nil {
		return x.VctRecord
	}
	return nil
}

var File_pb_game_chef_god_dish_api_chef_dish_api_proto protoreflect.FileDescriptor

var file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67,
	0x6f, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x68, 0x65, 0x66,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x16, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64,
	0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x1a, 0x23, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x66, 0x6f, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x2f, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f,
	0x67, 0x6f, 0x64, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x75, 0x69, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x4e, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x73, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0c, 0x4b, 0x69, 0x74, 0x63, 0x68, 0x65,
	0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x4b, 0x69,
	0x74, 0x63, 0x68, 0x65, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74,
	0x72, 0x55, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x72, 0x55,
	0x69, 0x6e, 0x22, 0x6d, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x68, 0x46, 0x6f, 0x6f, 0x64, 0x12, 0x2f,
	0x0a, 0x04, 0x46, 0x6f, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x46, 0x6f, 0x6f, 0x64, 0x52, 0x04, 0x46, 0x6f, 0x6f, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x4e, 0x65, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x4e, 0x65, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x77, 0x6e,
	0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x4f, 0x77, 0x6e, 0x4e, 0x75,
	0x6d, 0x22, 0xf1, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x73, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x4b, 0x69, 0x74, 0x63, 0x68, 0x65,
	0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x4b, 0x69,
	0x74, 0x63, 0x68, 0x65, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x4e, 0x65,
	0x78, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4e, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x4e, 0x65, 0x78, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4e, 0x65, 0x65, 0x64,
	0x12, 0x3b, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67,
	0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x44, 0x69, 0x73, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x44, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x4b, 0x69, 0x74, 0x63, 0x68, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x4b, 0x69, 0x74, 0x63, 0x68, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x53, 0x79, 0x73, 0x54, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x53, 0x79, 0x73, 0x54, 0x73, 0x22, 0x37, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1e,
	0x0a, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x62,
	0x0a, 0x0d, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x33, 0x0a, 0x04, 0x44, 0x69, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x44, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x44, 0x69, 0x73, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x54,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x54, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x48,
	0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x72, 0x74, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x45, 0x0a, 0x0a, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x0a, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x51, 0x0a,
	0x07, 0x43, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x44, 0x69, 0x73, 0x68,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x44, 0x69, 0x73, 0x68, 0x49, 0x44,
	0x12, 0x16, 0x0a, 0x06, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x68, 0x6f, 0x77,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x68, 0x6f, 0x77, 0x49, 0x44,
	0x22, 0xf8, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6f, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x0d,
	0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f,
	0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x43, 0x6f, 0x6f, 0x6b,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0d, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x68, 0x4c, 0x6f, 0x67,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x69, 0x73, 0x68, 0x4c, 0x6f, 0x67,
	0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x73,
	0x63, 0x12, 0x18, 0x0a, 0x07, 0x47, 0x69, 0x66, 0x74, 0x50, 0x69, 0x63, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x47, 0x69, 0x66, 0x74, 0x50, 0x69, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x44,
	0x69, 0x73, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44,
	0x69, 0x73, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x69, 0x73, 0x68, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x44, 0x69, 0x73, 0x68,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x18, 0x0a, 0x16, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x44, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x2a, 0x0a, 0x10, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x52, 0x0a, 0x12, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x53, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x53, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x72, 0x55, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x72, 0x55, 0x69, 0x6e, 0x22,
	0x6b, 0x0a, 0x0e, 0x44, 0x69, 0x73, 0x68, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x33, 0x0a, 0x04, 0x44, 0x69, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x44, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x44, 0x69, 0x73, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x22, 0x9b, 0x01, 0x0a,
	0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16,
	0x0a, 0x06, 0x4e, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x4e, 0x6f, 0x77, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4e,
	0x75, 0x6d, 0x12, 0x3e, 0x0a, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65,
	0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x37, 0x0a, 0x0b, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x4e,
	0x75, 0x6d, 0x73, 0x22, 0xb2, 0x03, 0x0a, 0x0e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x44, 0x69, 0x73, 0x68, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x11, 0x44, 0x69, 0x73, 0x68, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x42, 0x0a, 0x08, 0x44, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68,
	0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x69, 0x73, 0x68, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x44, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x4b, 0x0a, 0x0d,
	0x43, 0x6f, 0x6c, 0x6c, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f,
	0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6c,
	0x6c, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x43, 0x6f, 0x6c, 0x6c,
	0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x6e, 0x64, 0x54, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x45, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x53, 0x79, 0x73,
	0x54, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x53, 0x79, 0x73, 0x54, 0x73, 0x12,
	0x47, 0x0a, 0x0c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65,
	0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x41, 0x6c, 0x6c, 0x52,
	0x65, 0x61, 0x64, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x22, 0x58, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x42,
	0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xa9, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x49, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x2e,
	0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x77,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x6c, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x74, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74,
	0x72, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x54, 0x61, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x54, 0x61, 0x67, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x74, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9f, 0x02, 0x0a, 0x0d, 0x53, 0x75, 0x62, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x55, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x6c, 0x55, 0x69, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x6c, 0x53, 0x75, 0x62, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x6c, 0x53, 0x75, 0x62, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6c, 0x54, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6c, 0x54,
	0x73, 0x12, 0x49, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x31, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x20, 0x0a, 0x0b,
	0x75, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x75, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x44, 0x1a, 0x39,
	0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x23, 0x0a, 0x0d, 0x53, 0x75, 0x62,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x52,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x69, 0x52, 0x65, 0x73, 0x22, 0xf1,
	0x02, 0x0a, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x74, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c,
	0x73, 0x74, 0x72, 0x57, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x74, 0x72, 0x57, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x75, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x55, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x55, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x75,
	0x53, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x45, 0x78, 0x74, 0x49, 0x44,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x45, 0x78, 0x74, 0x49, 0x44,
	0x12, 0x45, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x72,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x32, 0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26,
	0x0a, 0x0e, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x43, 0x6d, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x42,
	0x61, 0x63, 0x6b, 0x43, 0x6d, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x14, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x75, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x43, 0x0a, 0x0b, 0x73, 0x74, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73,
	0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x0a,
	0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2e,
	0x0a, 0x14, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53,
	0x65, 0x6e, 0x64, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x50, 0x61, 0x73, 0x73, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62, 0x50, 0x61, 0x73, 0x73, 0x70, 0x22, 0x10,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x22, 0x8a, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3e, 0x0a,
	0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x44, 0x22, 0x3b, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x43, 0x6f, 0x6c, 0x6c, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x43, 0x6f,
	0x6c, 0x6c, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x4d, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x09, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09,
	0x41, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x33, 0x0a, 0x0f, 0x53, 0x75, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x43, 0x6f, 0x6f, 0x6b, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6f, 0x6b, 0x4d, 0x73, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x12,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x22, 0x2c, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x73,
	0x22, 0x2c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x66, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x72, 0x55, 0x69, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x72, 0x55, 0x69, 0x6e, 0x22, 0x85,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x66, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a,
	0x0c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f,
	0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x61,
	0x64, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x22, 0x99, 0x01, 0x0a, 0x0b, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x74, 0x72, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x53, 0x74,
	0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x74,
	0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x53, 0x74, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x1c,
	0x0a, 0x09, 0x53, 0x74, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x53, 0x74, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x4c, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x4c, 0x4e, 0x75, 0x6d,
	0x12, 0x10, 0x0a, 0x03, 0x55, 0x54, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x55,
	0x54, 0x73, 0x22, 0x46, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x4c, 0x55, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x4c, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x74, 0x72, 0x50,
	0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53,
	0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x91, 0x01, 0x0a, 0x0e, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x0b, 0x53, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x53, 0x74, 0x72, 0x50, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x12,
	0x1a, 0x0a, 0x08, 0x42, 0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x42, 0x48, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x56,
	0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64,
	0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x09, 0x56, 0x63, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2a, 0x42,
	0x0a, 0x0b, 0x41, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x47, 0x65, 0x74, 0x10, 0x01,
	0x12, 0x10, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x10, 0x02, 0x2a, 0x9a, 0x01, 0x0a, 0x0f, 0x65, 0x6d, 0x53, 0x75, 0x62, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x4e, 0x55, 0x4d,
	0x5f, 0x53, 0x55, 0x42, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x53,
	0x5f, 0x4e, 0x4f, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x64, 0x12, 0x22, 0x0a, 0x1e,
	0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0x65,
	0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x42, 0x41, 0x4c,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x6e, 0x32,
	0xaf, 0x0a, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x65, 0x66, 0x47, 0x6f, 0x64, 0x44,
	0x69, 0x73, 0x68, 0x41, 0x70, 0x69, 0x12, 0x63, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44,
	0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x44, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x12, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x2d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x2d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12,
	0x48, 0x0a, 0x04, 0x43, 0x6f, 0x6f, 0x6b, 0x12, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x43, 0x6f, 0x6f, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x75, 0x0a, 0x13, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x2e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x2e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x69, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f,
	0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x2a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f,
	0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x61, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64,
	0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68,
	0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x5a,
	0x0a, 0x0a, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73,
	0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f,
	0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x5a, 0x0a, 0x0a, 0x53, 0x75,
	0x62, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x75, 0x62, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f,
	0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x75, 0x62, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x41, 0x77, 0x61,
	0x72, 0x64, 0x12, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67,
	0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69,
	0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x12, 0x69, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x66,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66,
	0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x2a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64,
	0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x68, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x6f, 0x0a, 0x11,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e,
	0x64, 0x12, 0x2c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x2c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f,
	0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x73, 0x70, 0x12, 0x62, 0x0a,
	0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f,
	0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73,
	0x70, 0x42, 0x4e, 0x5a, 0x4c, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x68, 0x65, 0x66, 0x5f, 0x67, 0x6f, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x68, 0x5f, 0x61, 0x70,
	0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescOnce sync.Once
	file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescData = file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDesc
)

func file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescGZIP() []byte {
	file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescOnce.Do(func() {
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescData)
	})
	return file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDescData
}

var file_pb_game_chef_god_dish_api_chef_dish_api_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_pb_game_chef_god_dish_api_chef_dish_api_proto_goTypes = []interface{}{
	(AwardStatus)(0),               // 0: game_chef_god_dish_api.AwardStatus
	(EmSubBalanceRes)(0),           // 1: game_chef_god_dish_api.emSubBalanceRes
	(*QueryDishListReq)(nil),       // 2: game_chef_god_dish_api.QueryDishListReq
	(*DishFood)(nil),               // 3: game_chef_god_dish_api.DishFood
	(*QueryDishListRsp)(nil),       // 4: game_chef_god_dish_api.QueryDishListRsp
	(*QueryCookingRecordReq)(nil),  // 5: game_chef_god_dish_api.QueryCookingRecordReq
	(*CookingRecord)(nil),          // 6: game_chef_god_dish_api.CookingRecord
	(*QueryCookingRecordRsp)(nil),  // 7: game_chef_god_dish_api.QueryCookingRecordRsp
	(*CookReq)(nil),                // 8: game_chef_god_dish_api.CookReq
	(*CookRsp)(nil),                // 9: game_chef_god_dish_api.CookRsp
	(*QueryCollectionListReq)(nil), // 10: game_chef_god_dish_api.QueryCollectionListReq
	(*QueryCollectionListRsp)(nil), // 11: game_chef_god_dish_api.QueryCollectionListRsp
	(*QueryCollectionReq)(nil),     // 12: game_chef_god_dish_api.QueryCollectionReq
	(*DishCollection)(nil),         // 13: game_chef_god_dish_api.DishCollection
	(*CollAwardInfo)(nil),          // 14: game_chef_god_dish_api.CollAwardInfo
	(*LevelMember)(nil),            // 15: game_chef_god_dish_api.LevelMember
	(*CollectionMeta)(nil),         // 16: game_chef_god_dish_api.CollectionMeta
	(*QueryCollectionRsp)(nil),     // 17: game_chef_god_dish_api.QueryCollectionRsp
	(*GetBalanceReq)(nil),          // 18: game_chef_god_dish_api.GetBalanceReq
	(*GetBalanceRsp)(nil),          // 19: game_chef_god_dish_api.GetBalanceRsp
	(*SubBalanceReq)(nil),          // 20: game_chef_god_dish_api.SubBalanceReq
	(*SubBalanceRsp)(nil),          // 21: game_chef_god_dish_api.SubBalanceRsp
	(*CheckInfo)(nil),              // 22: game_chef_god_dish_api.CheckInfo
	(*CallInfo)(nil),               // 23: game_chef_god_dish_api.CallInfo
	(*BusinessCheckSendReq)(nil),   // 24: game_chef_god_dish_api.BusinessCheckSendReq
	(*BusinessCheckSendRsp)(nil),   // 25: game_chef_god_dish_api.BusinessCheckSendRsp
	(*GetExchangeReq)(nil),         // 26: game_chef_god_dish_api.GetExchangeReq
	(*GetExchangeRsp)(nil),         // 27: game_chef_god_dish_api.GetExchangeRsp
	(*GetAwardReq)(nil),            // 28: game_chef_god_dish_api.GetAwardReq
	(*GetAwardRsp)(nil),            // 29: game_chef_god_dish_api.GetAwardRsp
	(*SurprisedNotice)(nil),        // 30: game_chef_god_dish_api.SurprisedNotice
	(*GetBannerInfoReq)(nil),       // 31: game_chef_god_dish_api.GetBannerInfoReq
	(*GetBannerInfoRsp)(nil),       // 32: game_chef_god_dish_api.GetBannerInfoRsp
	(*GetUserChefInfoReq)(nil),     // 33: game_chef_god_dish_api.GetUserChefInfoReq
	(*GetUserChefInfoRsp)(nil),     // 34: game_chef_god_dish_api.GetUserChefInfoRsp
	(*AssetRecord)(nil),            // 35: game_chef_god_dish_api.AssetRecord
	(*AssetRecordReq)(nil),         // 36: game_chef_god_dish_api.AssetRecordReq
	(*AssetRecordRsp)(nil),         // 37: game_chef_god_dish_api.AssetRecordRsp
	nil,                            // 38: game_chef_god_dish_api.GetBalanceReq.MapExtEntry
	nil,                            // 39: game_chef_god_dish_api.SubBalanceReq.MapExtEntry
	nil,                            // 40: game_chef_god_dish_api.CheckInfo.MapExtEntry
	(*backend.Food)(nil),           // 41: game_chef_god_backend.Food
	(*backend.DishInfo)(nil),       // 42: game_chef_god_backend.DishInfo
	(backend.CookResult)(0),        // 43: game_chef_god_backend.CookResult
	(*backend.AwardInfo)(nil),      // 44: game_chef_god_backend.AwardInfo
}
var file_pb_game_chef_god_dish_api_chef_dish_api_proto_depIdxs = []int32{
	41, // 0: game_chef_god_dish_api.DishFood.Food:type_name -> game_chef_god_backend.Food
	42, // 1: game_chef_god_dish_api.QueryDishListRsp.DishList:type_name -> game_chef_god_backend.DishInfo
	42, // 2: game_chef_god_dish_api.CookingRecord.Dish:type_name -> game_chef_god_backend.DishInfo
	6,  // 3: game_chef_god_dish_api.QueryCookingRecordRsp.RecordList:type_name -> game_chef_god_dish_api.CookingRecord
	43, // 4: game_chef_god_dish_api.CookRsp.CookingResult:type_name -> game_chef_god_backend.CookResult
	42, // 5: game_chef_god_dish_api.DishCollection.Dish:type_name -> game_chef_god_backend.DishInfo
	44, // 6: game_chef_god_dish_api.CollAwardInfo.AwardList:type_name -> game_chef_god_backend.AwardInfo
	13, // 7: game_chef_god_dish_api.CollectionMeta.DishList:type_name -> game_chef_god_dish_api.DishCollection
	14, // 8: game_chef_god_dish_api.CollectionMeta.CollAwardInfo:type_name -> game_chef_god_dish_api.CollAwardInfo
	15, // 9: game_chef_god_dish_api.CollectionMeta.AllReadyColl:type_name -> game_chef_god_dish_api.LevelMember
	16, // 10: game_chef_god_dish_api.QueryCollectionRsp.DataList:type_name -> game_chef_god_dish_api.CollectionMeta
	38, // 11: game_chef_god_dish_api.GetBalanceReq.mapExt:type_name -> game_chef_god_dish_api.GetBalanceReq.MapExtEntry
	39, // 12: game_chef_god_dish_api.SubBalanceReq.mapExt:type_name -> game_chef_god_dish_api.SubBalanceReq.MapExtEntry
	40, // 13: game_chef_god_dish_api.CheckInfo.mapExt:type_name -> game_chef_god_dish_api.CheckInfo.MapExtEntry
	22, // 14: game_chef_god_dish_api.BusinessCheckSendReq.stCheckInfo:type_name -> game_chef_god_dish_api.CheckInfo
	23, // 15: game_chef_god_dish_api.BusinessCheckSendReq.stCallInfo:type_name -> game_chef_god_dish_api.CallInfo
	44, // 16: game_chef_god_dish_api.GetExchangeRsp.AwardList:type_name -> game_chef_god_backend.AwardInfo
	44, // 17: game_chef_god_dish_api.GetAwardRsp.AwardList:type_name -> game_chef_god_backend.AwardInfo
	15, // 18: game_chef_god_dish_api.GetUserChefInfoRsp.AllReadyColl:type_name -> game_chef_god_dish_api.LevelMember
	35, // 19: game_chef_god_dish_api.AssetRecordRsp.VctRecord:type_name -> game_chef_god_dish_api.AssetRecord
	2,  // 20: game_chef_god_dish_api.GameChefGodDishApi.QueryDishList:input_type -> game_chef_god_dish_api.QueryDishListReq
	5,  // 21: game_chef_god_dish_api.GameChefGodDishApi.QueryCookingRecord:input_type -> game_chef_god_dish_api.QueryCookingRecordReq
	8,  // 22: game_chef_god_dish_api.GameChefGodDishApi.Cook:input_type -> game_chef_god_dish_api.CookReq
	10, // 23: game_chef_god_dish_api.GameChefGodDishApi.QueryCollectionList:input_type -> game_chef_god_dish_api.QueryCollectionListReq
	12, // 24: game_chef_god_dish_api.GameChefGodDishApi.QueryCollection:input_type -> game_chef_god_dish_api.QueryCollectionReq
	26, // 25: game_chef_god_dish_api.GameChefGodDishApi.GetExchangeInfo:input_type -> game_chef_god_dish_api.GetExchangeReq
	18, // 26: game_chef_god_dish_api.GameChefGodDishApi.GetBalance:input_type -> game_chef_god_dish_api.GetBalanceReq
	20, // 27: game_chef_god_dish_api.GameChefGodDishApi.SubBalance:input_type -> game_chef_god_dish_api.SubBalanceReq
	28, // 28: game_chef_god_dish_api.GameChefGodDishApi.GetAward:input_type -> game_chef_god_dish_api.GetAwardReq
	31, // 29: game_chef_god_dish_api.GameChefGodDishApi.GetBannerInfo:input_type -> game_chef_god_dish_api.GetBannerInfoReq
	33, // 30: game_chef_god_dish_api.GameChefGodDishApi.GetUserChefInfo:input_type -> game_chef_god_dish_api.GetUserChefInfoReq
	24, // 31: game_chef_god_dish_api.GameChefGodDishApi.BusinessCheckSend:input_type -> game_chef_god_dish_api.BusinessCheckSendReq
	36, // 32: game_chef_god_dish_api.GameChefGodDishApi.QueryAssetRecord:input_type -> game_chef_god_dish_api.AssetRecordReq
	4,  // 33: game_chef_god_dish_api.GameChefGodDishApi.QueryDishList:output_type -> game_chef_god_dish_api.QueryDishListRsp
	7,  // 34: game_chef_god_dish_api.GameChefGodDishApi.QueryCookingRecord:output_type -> game_chef_god_dish_api.QueryCookingRecordRsp
	9,  // 35: game_chef_god_dish_api.GameChefGodDishApi.Cook:output_type -> game_chef_god_dish_api.CookRsp
	11, // 36: game_chef_god_dish_api.GameChefGodDishApi.QueryCollectionList:output_type -> game_chef_god_dish_api.QueryCollectionListRsp
	17, // 37: game_chef_god_dish_api.GameChefGodDishApi.QueryCollection:output_type -> game_chef_god_dish_api.QueryCollectionRsp
	27, // 38: game_chef_god_dish_api.GameChefGodDishApi.GetExchangeInfo:output_type -> game_chef_god_dish_api.GetExchangeRsp
	19, // 39: game_chef_god_dish_api.GameChefGodDishApi.GetBalance:output_type -> game_chef_god_dish_api.GetBalanceRsp
	21, // 40: game_chef_god_dish_api.GameChefGodDishApi.SubBalance:output_type -> game_chef_god_dish_api.SubBalanceRsp
	29, // 41: game_chef_god_dish_api.GameChefGodDishApi.GetAward:output_type -> game_chef_god_dish_api.GetAwardRsp
	32, // 42: game_chef_god_dish_api.GameChefGodDishApi.GetBannerInfo:output_type -> game_chef_god_dish_api.GetBannerInfoRsp
	34, // 43: game_chef_god_dish_api.GameChefGodDishApi.GetUserChefInfo:output_type -> game_chef_god_dish_api.GetUserChefInfoRsp
	25, // 44: game_chef_god_dish_api.GameChefGodDishApi.BusinessCheckSend:output_type -> game_chef_god_dish_api.BusinessCheckSendRsp
	37, // 45: game_chef_god_dish_api.GameChefGodDishApi.QueryAssetRecord:output_type -> game_chef_god_dish_api.AssetRecordRsp
	33, // [33:46] is the sub-list for method output_type
	20, // [20:33] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_pb_game_chef_god_dish_api_chef_dish_api_proto_init() }
func file_pb_game_chef_god_dish_api_chef_dish_api_proto_init() {
	if File_pb_game_chef_god_dish_api_chef_dish_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDishListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DishFood); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDishListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCookingRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CookingRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCookingRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CookReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CookRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCollectionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCollectionListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCollectionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DishCollection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollAwardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LevelMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectionMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCollectionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCheckSendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCheckSendRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAwardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAwardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurprisedNotice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBannerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBannerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserChefInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserChefInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_chef_god_dish_api_chef_dish_api_proto_goTypes,
		DependencyIndexes: file_pb_game_chef_god_dish_api_chef_dish_api_proto_depIdxs,
		EnumInfos:         file_pb_game_chef_god_dish_api_chef_dish_api_proto_enumTypes,
		MessageInfos:      file_pb_game_chef_god_dish_api_chef_dish_api_proto_msgTypes,
	}.Build()
	File_pb_game_chef_god_dish_api_chef_dish_api_proto = out.File
	file_pb_game_chef_god_dish_api_chef_dish_api_proto_rawDesc = nil
	file_pb_game_chef_god_dish_api_chef_dish_api_proto_goTypes = nil
	file_pb_game_chef_god_dish_api_chef_dish_api_proto_depIdxs = nil
}
