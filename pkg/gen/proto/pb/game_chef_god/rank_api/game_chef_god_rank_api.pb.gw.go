// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/game_chef_god/rank_api/game_chef_god_rank_api.proto

/*
Package rank_api is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package rank_api

import (
	"context"
	"io"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_GameChefGodRankApi_QueryRankList_0(ctx context.Context, marshaler runtime.Marshaler, client GameChefGodRankApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryRankListReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryRankList(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GameChefGodRankApi_QueryRankList_0(ctx context.Context, marshaler runtime.Marshaler, server GameChefGodRankApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryRankListReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryRankList(ctx, &protoReq)
	return msg, metadata, err

}

func request_GameChefGodRankApi_QueryUserBottomInfo_0(ctx context.Context, marshaler runtime.Marshaler, client GameChefGodRankApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryUserBottomInfoReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryUserBottomInfo(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GameChefGodRankApi_QueryUserBottomInfo_0(ctx context.Context, marshaler runtime.Marshaler, server GameChefGodRankApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryUserBottomInfoReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryUserBottomInfo(ctx, &protoReq)
	return msg, metadata, err

}

func request_GameChefGodRankApi_SetAnonymous_0(ctx context.Context, marshaler runtime.Marshaler, client GameChefGodRankApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SetAnonymousReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.SetAnonymous(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GameChefGodRankApi_SetAnonymous_0(ctx context.Context, marshaler runtime.Marshaler, server GameChefGodRankApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SetAnonymousReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.SetAnonymous(ctx, &protoReq)
	return msg, metadata, err

}

func request_GameChefGodRankApi_QueryHallOfFame_0(ctx context.Context, marshaler runtime.Marshaler, client GameChefGodRankApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryHallOfFameReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryHallOfFame(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GameChefGodRankApi_QueryHallOfFame_0(ctx context.Context, marshaler runtime.Marshaler, server GameChefGodRankApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryHallOfFameReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryHallOfFame(ctx, &protoReq)
	return msg, metadata, err

}

func request_GameChefGodRankApi_QueryDishCollectionRankList_0(ctx context.Context, marshaler runtime.Marshaler, client GameChefGodRankApiClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryDishCollectionRankListReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.QueryDishCollectionRankList(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GameChefGodRankApi_QueryDishCollectionRankList_0(ctx context.Context, marshaler runtime.Marshaler, server GameChefGodRankApiServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq QueryDishCollectionRankListReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.QueryDishCollectionRankList(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterGameChefGodRankApiHandlerServer registers the http handlers for service GameChefGodRankApi to "mux".
// UnaryRPC     :call GameChefGodRankApiServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterGameChefGodRankApiHandlerFromEndpoint instead.
func RegisterGameChefGodRankApiHandlerServer(ctx context.Context, mux *runtime.ServeMux, server GameChefGodRankApiServer) error {

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryRankList_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryRankList", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryRankList"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GameChefGodRankApi_QueryRankList_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryRankList_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryUserBottomInfo_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryUserBottomInfo", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryUserBottomInfo"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GameChefGodRankApi_QueryUserBottomInfo_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryUserBottomInfo_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_SetAnonymous_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/SetAnonymous", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/SetAnonymous"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GameChefGodRankApi_SetAnonymous_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_SetAnonymous_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryHallOfFame_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryHallOfFame", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryHallOfFame"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GameChefGodRankApi_QueryHallOfFame_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryHallOfFame_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryDishCollectionRankList_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryDishCollectionRankList", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryDishCollectionRankList"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GameChefGodRankApi_QueryDishCollectionRankList_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryDishCollectionRankList_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterGameChefGodRankApiHandlerFromEndpoint is same as RegisterGameChefGodRankApiHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterGameChefGodRankApiHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterGameChefGodRankApiHandler(ctx, mux, conn)
}

// RegisterGameChefGodRankApiHandler registers the http handlers for service GameChefGodRankApi to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterGameChefGodRankApiHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterGameChefGodRankApiHandlerClient(ctx, mux, NewGameChefGodRankApiClient(conn))
}

// RegisterGameChefGodRankApiHandlerClient registers the http handlers for service GameChefGodRankApi
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "GameChefGodRankApiClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "GameChefGodRankApiClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "GameChefGodRankApiClient" to call the correct interceptors.
func RegisterGameChefGodRankApiHandlerClient(ctx context.Context, mux *runtime.ServeMux, client GameChefGodRankApiClient) error {

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryRankList_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryRankList", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryRankList"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GameChefGodRankApi_QueryRankList_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryRankList_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryUserBottomInfo_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryUserBottomInfo", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryUserBottomInfo"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GameChefGodRankApi_QueryUserBottomInfo_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryUserBottomInfo_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_SetAnonymous_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/SetAnonymous", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/SetAnonymous"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GameChefGodRankApi_SetAnonymous_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_SetAnonymous_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryHallOfFame_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryHallOfFame", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryHallOfFame"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GameChefGodRankApi_QueryHallOfFame_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryHallOfFame_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GameChefGodRankApi_QueryDishCollectionRankList_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/game_chef_god_rank_api.GameChefGodRankApi/QueryDishCollectionRankList", runtime.WithHTTPPathPattern("/game_chef_god_rank_api.GameChefGodRankApi/QueryDishCollectionRankList"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GameChefGodRankApi_QueryDishCollectionRankList_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GameChefGodRankApi_QueryDishCollectionRankList_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_GameChefGodRankApi_QueryRankList_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"game_chef_god_rank_api.GameChefGodRankApi", "QueryRankList"}, ""))

	pattern_GameChefGodRankApi_QueryUserBottomInfo_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"game_chef_god_rank_api.GameChefGodRankApi", "QueryUserBottomInfo"}, ""))

	pattern_GameChefGodRankApi_SetAnonymous_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"game_chef_god_rank_api.GameChefGodRankApi", "SetAnonymous"}, ""))

	pattern_GameChefGodRankApi_QueryHallOfFame_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"game_chef_god_rank_api.GameChefGodRankApi", "QueryHallOfFame"}, ""))

	pattern_GameChefGodRankApi_QueryDishCollectionRankList_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"game_chef_god_rank_api.GameChefGodRankApi", "QueryDishCollectionRankList"}, ""))
)

var (
	forward_GameChefGodRankApi_QueryRankList_0 = runtime.ForwardResponseMessage

	forward_GameChefGodRankApi_QueryUserBottomInfo_0 = runtime.ForwardResponseMessage

	forward_GameChefGodRankApi_SetAnonymous_0 = runtime.ForwardResponseMessage

	forward_GameChefGodRankApi_QueryHallOfFame_0 = runtime.ForwardResponseMessage

	forward_GameChefGodRankApi_QueryDishCollectionRankList_0 = runtime.ForwardResponseMessage
)
