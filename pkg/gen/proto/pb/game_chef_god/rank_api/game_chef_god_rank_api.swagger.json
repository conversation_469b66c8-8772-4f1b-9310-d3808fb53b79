{"swagger": "2.0", "info": {"title": "pb/game_chef_god/rank_api/game_chef_god_rank_api.proto", "version": "version not set"}, "tags": [{"name": "GameChefGodRankApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_chef_god_rank_api.GameChefGodRankApi/QueryDishCollectionRankList": {"post": {"summary": "菜品收集榜 返回top20", "operationId": "GameChefGodRankApi_QueryDishCollectionRankList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryDishCollectionRankListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryDishCollectionRankListReq"}}], "tags": ["GameChefGodRankApi"]}}, "/game_chef_god_rank_api.GameChefGodRankApi/QueryHallOfFame": {"post": {"summary": "名人堂", "operationId": "GameChefGodRankApi_QueryHallOfFame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryHallOfFameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryHallOfFameReq"}}], "tags": ["GameChefGodRankApi"]}}, "/game_chef_god_rank_api.GameChefGodRankApi/QueryRankList": {"post": {"summary": "查询榜单", "operationId": "GameChefGodRankApi_QueryRankList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryRankListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryRankListReq"}}], "tags": ["GameChefGodRankApi"]}}, "/game_chef_god_rank_api.GameChefGodRankApi/QueryUserBottomInfo": {"post": {"summary": "查询用户吸底信息", "operationId": "GameChefGodRankApi_QueryUserBottomInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryUserBottomInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_rank_apiQueryUserBottomInfoReq"}}], "tags": ["GameChefGodRankApi"]}}, "/game_chef_god_rank_api.GameChefGodRankApi/SetAnonymous": {"post": {"summary": "设置匿名", "operationId": "GameChefGodRankApi_SetAnonymous", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_chef_god_rank_apiSetAnonymousRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_chef_god_rank_apiSetAnonymousReq"}}], "tags": ["GameChefGodRankApi"]}}}, "definitions": {"game_chef_god_backendAwardInfo": {"type": "object", "properties": {"Logo": {"type": "string"}, "AwardName": {"type": "string"}, "Num": {"type": "integer", "format": "int32"}, "Price": {"type": "integer", "format": "int32"}}}, "game_chef_god_backendDishInfo": {"type": "object", "properties": {"DishID": {"type": "string", "format": "int64", "title": "菜品id"}, "Name": {"type": "string", "title": "菜品名"}, "Logo": {"type": "string", "title": "图片"}, "FoodList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_backendFoodMeta"}, "title": "所需食材"}, "Level": {"type": "string", "format": "int64", "title": "等级"}, "GetTs": {"type": "string", "format": "int64", "title": "获得时间"}, "AwardDesc": {"type": "string", "title": "奖励描述"}, "CollectNum": {"type": "integer", "format": "int32", "title": "收集次数"}}, "title": "菜品"}, "game_chef_god_backendFood": {"type": "object", "properties": {"FoodID": {"type": "string", "format": "int64", "title": "食物id"}, "Name": {"type": "string", "title": "食物名"}, "Logo": {"type": "string", "title": "图片"}, "GiftList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_backendGiftInfo"}, "title": "可以从什么礼物中开出(配置不写入)"}}, "title": "食材(表)"}, "game_chef_god_backendFoodMeta": {"type": "object", "properties": {"FoodInfo": {"$ref": "#/definitions/game_chef_god_backendFood"}, "NeedNum": {"type": "integer", "format": "int32", "title": "需要数量"}}, "title": "食物信息"}, "game_chef_god_backendGiftInfo": {"type": "object", "properties": {"GiftID": {"type": "string", "format": "int64"}, "GiftName": {"type": "string"}}}, "game_chef_god_dish_apiCollAwardInfo": {"type": "object", "properties": {"NowNum": {"type": "integer", "format": "int32", "title": "当前进度数量"}, "TotalNum": {"type": "integer", "format": "int32", "title": "奖励总进度"}, "AwardList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_backendAwardInfo"}, "title": "奖励信息"}, "Status": {"type": "integer", "format": "int32", "title": "是否可领取 查看AwardStatus枚举"}}}, "game_chef_god_dish_apiCollectionMeta": {"type": "object", "properties": {"ID": {"type": "string", "format": "int64", "title": "图鉴ID"}, "DishCollectionNum": {"type": "integer", "format": "int32", "title": "已收集菜品数"}, "DishList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_dish_apiDishCollection"}, "title": "菜品"}, "CollectionName": {"type": "string", "title": "图鉴名称"}, "TotalNum": {"type": "string", "format": "int64", "title": "菜品总量"}, "CollAwardInfo": {"$ref": "#/definitions/game_chef_god_dish_apiCollAwardInfo", "title": "图鉴奖励"}, "StartTs": {"type": "string", "format": "int64", "title": "开始时间 0为长期存在"}, "EndTs": {"type": "string", "format": "int64", "title": "结束时间 0为长期存在"}, "SysTs": {"type": "string", "format": "int64", "title": "当前时间 0为长期存在"}, "AllReadyColl": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_dish_apiLevelMember"}, "title": "已收集情况"}}}, "game_chef_god_dish_apiDishCollection": {"type": "object", "properties": {"Dish": {"$ref": "#/definitions/game_chef_god_backendDishInfo", "title": "菜品"}, "CollectionNum": {"type": "integer", "format": "int32", "title": "收集的次数"}}, "title": "菜品图鉴收集"}, "game_chef_god_dish_apiLevelMember": {"type": "object", "properties": {"Level": {"type": "string", "format": "int64", "title": "菜品等级"}, "Nums": {"type": "string", "format": "int64", "title": "已收集数量"}}}, "game_chef_god_rank_apiCollectionItem": {"type": "object", "properties": {"Collection": {"$ref": "#/definitions/game_chef_god_dish_apiCollectionMeta", "title": "图鉴"}, "CollectionUserNum": {"type": "integer", "format": "int32", "title": "集齐的用户数量"}, "UserInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_rank_apiUserInfo"}, "title": "top3用户"}}}, "game_chef_god_rank_apiQueryDishCollectionRankListReq": {"type": "object", "properties": {"DishID": {"type": "string", "format": "int64", "title": "菜品id"}}}, "game_chef_god_rank_apiQueryDishCollectionRankListRsp": {"type": "object", "properties": {"UserInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_rank_apiUserInfo"}, "title": "集齐用户列表top20"}, "CollectionUserNum": {"type": "integer", "format": "int32", "title": "已集齐的用户数量"}, "Rank": {"type": "integer", "format": "int32", "title": "排名，第几个集齐，0未集齐"}}}, "game_chef_god_rank_apiQueryHallOfFameReq": {"type": "object", "properties": {"StartIndex": {"type": "integer", "format": "int32", "title": "首次不传（默认为0） 后续透传后台返回的"}}, "title": "QueryHallOfFameReq 名人堂"}, "game_chef_god_rank_apiQueryHallOfFameRsp": {"type": "object", "properties": {"HasMore": {"type": "boolean", "title": "是否还有下一页"}, "StartIndex": {"type": "integer", "format": "int32", "title": "后台透传"}, "CollectionList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_rank_apiCollectionItem"}, "title": "图鉴列表"}}}, "game_chef_god_rank_apiQueryRankListReq": {"type": "object", "properties": {"RankListType": {"$ref": "#/definitions/game_chef_god_rank_apiRankListType", "title": "榜单类型"}, "StartIndex": {"type": "integer", "format": "int32", "title": "首次不传（默认为0） 后续透传后台返回的"}, "CollectionID": {"type": "string", "format": "int64", "title": "图鉴id,名人堂使用"}}}, "game_chef_god_rank_apiQueryRankListRsp": {"type": "object", "properties": {"HasMore": {"type": "boolean", "title": "是否还有下一页"}, "StartIndex": {"type": "integer", "format": "int32", "title": "后台透传"}, "RankList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/game_chef_god_rank_apiUserInfo"}, "title": "/ 排行榜数据"}}}, "game_chef_god_rank_apiQueryUserBottomInfoReq": {"type": "object", "properties": {"RankListType": {"$ref": "#/definitions/game_chef_god_rank_apiRankListType", "title": "榜单类型"}}}, "game_chef_god_rank_apiQueryUserBottomInfoRsp": {"type": "object", "properties": {"UserInfo": {"$ref": "#/definitions/game_chef_god_rank_apiUserInfo", "title": "吸底信息"}, "Anonymous": {"type": "boolean", "title": "是否匿名"}}}, "game_chef_god_rank_apiRankListType": {"type": "string", "enum": ["RankListTypeUnknown", "RankListTypeCook", "RankListTypeReceiveGift", "RankListTypeHallOfFame"], "default": "RankListTypeUnknown", "description": "- RankListTypeCook: 制作美食榜\n - RankListTypeReceiveGift: 收礼榜\n - RankListTypeHallOfFame: 名人堂", "title": "榜单类型"}, "game_chef_god_rank_apiSetAnonymousReq": {"type": "object", "properties": {"Anonymous": {"type": "boolean", "title": "是否匿名"}}}, "game_chef_god_rank_apiSetAnonymousRsp": {"type": "object", "properties": {"Ret": {"type": "integer", "format": "int32"}}}, "game_chef_god_rank_apiUserInfo": {"type": "object", "properties": {"Uid": {"type": "string", "title": "uid，q音为加密uin"}, "Name": {"type": "string", "title": "名字"}, "Logo": {"type": "string", "title": "头像"}, "Rank": {"type": "integer", "format": "int32", "title": "排名"}, "Score": {"type": "integer", "format": "int32", "title": "分数"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}