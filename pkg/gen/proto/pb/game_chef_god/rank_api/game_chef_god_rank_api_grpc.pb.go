// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_chef_god/rank_api/game_chef_god_rank_api.proto

package rank_api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GameChefGodRankApi_QueryRankList_FullMethodName               = "/game_chef_god_rank_api.GameChefGodRankApi/QueryRankList"
	GameChefGodRankApi_QueryUserBottomInfo_FullMethodName         = "/game_chef_god_rank_api.GameChefGodRankApi/QueryUserBottomInfo"
	GameChefGodRankApi_SetAnonymous_FullMethodName                = "/game_chef_god_rank_api.GameChefGodRankApi/SetAnonymous"
	GameChefGodRankApi_QueryHallOfFame_FullMethodName             = "/game_chef_god_rank_api.GameChefGodRankApi/QueryHallOfFame"
	GameChefGodRankApi_QueryDishCollectionRankList_FullMethodName = "/game_chef_god_rank_api.GameChefGodRankApi/QueryDishCollectionRankList"
)

// GameChefGodRankApiClient is the client API for GameChefGodRankApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 排行榜api
type GameChefGodRankApiClient interface {
	// 查询榜单
	QueryRankList(ctx context.Context, in *QueryRankListReq, opts ...grpc.CallOption) (*QueryRankListRsp, error)
	// 查询用户吸底信息
	QueryUserBottomInfo(ctx context.Context, in *QueryUserBottomInfoReq, opts ...grpc.CallOption) (*QueryUserBottomInfoRsp, error)
	// 设置匿名
	SetAnonymous(ctx context.Context, in *SetAnonymousReq, opts ...grpc.CallOption) (*SetAnonymousRsp, error)
	// 名人堂
	QueryHallOfFame(ctx context.Context, in *QueryHallOfFameReq, opts ...grpc.CallOption) (*QueryHallOfFameRsp, error)
	// 菜品收集榜 返回top20
	QueryDishCollectionRankList(ctx context.Context, in *QueryDishCollectionRankListReq, opts ...grpc.CallOption) (*QueryDishCollectionRankListRsp, error)
}

type gameChefGodRankApiClient struct {
	cc grpc.ClientConnInterface
}

func NewGameChefGodRankApiClient(cc grpc.ClientConnInterface) GameChefGodRankApiClient {
	return &gameChefGodRankApiClient{cc}
}

func (c *gameChefGodRankApiClient) QueryRankList(ctx context.Context, in *QueryRankListReq, opts ...grpc.CallOption) (*QueryRankListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRankListRsp)
	err := c.cc.Invoke(ctx, GameChefGodRankApi_QueryRankList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodRankApiClient) QueryUserBottomInfo(ctx context.Context, in *QueryUserBottomInfoReq, opts ...grpc.CallOption) (*QueryUserBottomInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryUserBottomInfoRsp)
	err := c.cc.Invoke(ctx, GameChefGodRankApi_QueryUserBottomInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodRankApiClient) SetAnonymous(ctx context.Context, in *SetAnonymousReq, opts ...grpc.CallOption) (*SetAnonymousRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetAnonymousRsp)
	err := c.cc.Invoke(ctx, GameChefGodRankApi_SetAnonymous_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodRankApiClient) QueryHallOfFame(ctx context.Context, in *QueryHallOfFameReq, opts ...grpc.CallOption) (*QueryHallOfFameRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryHallOfFameRsp)
	err := c.cc.Invoke(ctx, GameChefGodRankApi_QueryHallOfFame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameChefGodRankApiClient) QueryDishCollectionRankList(ctx context.Context, in *QueryDishCollectionRankListReq, opts ...grpc.CallOption) (*QueryDishCollectionRankListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryDishCollectionRankListRsp)
	err := c.cc.Invoke(ctx, GameChefGodRankApi_QueryDishCollectionRankList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameChefGodRankApiServer is the server API for GameChefGodRankApi service.
// All implementations should embed UnimplementedGameChefGodRankApiServer
// for forward compatibility
//
// 排行榜api
type GameChefGodRankApiServer interface {
	// 查询榜单
	QueryRankList(context.Context, *QueryRankListReq) (*QueryRankListRsp, error)
	// 查询用户吸底信息
	QueryUserBottomInfo(context.Context, *QueryUserBottomInfoReq) (*QueryUserBottomInfoRsp, error)
	// 设置匿名
	SetAnonymous(context.Context, *SetAnonymousReq) (*SetAnonymousRsp, error)
	// 名人堂
	QueryHallOfFame(context.Context, *QueryHallOfFameReq) (*QueryHallOfFameRsp, error)
	// 菜品收集榜 返回top20
	QueryDishCollectionRankList(context.Context, *QueryDishCollectionRankListReq) (*QueryDishCollectionRankListRsp, error)
}

// UnimplementedGameChefGodRankApiServer should be embedded to have forward compatible implementations.
type UnimplementedGameChefGodRankApiServer struct {
}

func (UnimplementedGameChefGodRankApiServer) QueryRankList(context.Context, *QueryRankListReq) (*QueryRankListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRankList not implemented")
}
func (UnimplementedGameChefGodRankApiServer) QueryUserBottomInfo(context.Context, *QueryUserBottomInfoReq) (*QueryUserBottomInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUserBottomInfo not implemented")
}
func (UnimplementedGameChefGodRankApiServer) SetAnonymous(context.Context, *SetAnonymousReq) (*SetAnonymousRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAnonymous not implemented")
}
func (UnimplementedGameChefGodRankApiServer) QueryHallOfFame(context.Context, *QueryHallOfFameReq) (*QueryHallOfFameRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryHallOfFame not implemented")
}
func (UnimplementedGameChefGodRankApiServer) QueryDishCollectionRankList(context.Context, *QueryDishCollectionRankListReq) (*QueryDishCollectionRankListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryDishCollectionRankList not implemented")
}

// UnsafeGameChefGodRankApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameChefGodRankApiServer will
// result in compilation errors.
type UnsafeGameChefGodRankApiServer interface {
	mustEmbedUnimplementedGameChefGodRankApiServer()
}

func RegisterGameChefGodRankApiServer(s grpc.ServiceRegistrar, srv GameChefGodRankApiServer) {
	s.RegisterService(&GameChefGodRankApi_ServiceDesc, srv)
}

func _GameChefGodRankApi_QueryRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodRankApiServer).QueryRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodRankApi_QueryRankList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodRankApiServer).QueryRankList(ctx, req.(*QueryRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodRankApi_QueryUserBottomInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUserBottomInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodRankApiServer).QueryUserBottomInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodRankApi_QueryUserBottomInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodRankApiServer).QueryUserBottomInfo(ctx, req.(*QueryUserBottomInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodRankApi_SetAnonymous_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAnonymousReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodRankApiServer).SetAnonymous(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodRankApi_SetAnonymous_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodRankApiServer).SetAnonymous(ctx, req.(*SetAnonymousReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodRankApi_QueryHallOfFame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryHallOfFameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodRankApiServer).QueryHallOfFame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodRankApi_QueryHallOfFame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodRankApiServer).QueryHallOfFame(ctx, req.(*QueryHallOfFameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameChefGodRankApi_QueryDishCollectionRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryDishCollectionRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameChefGodRankApiServer).QueryDishCollectionRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameChefGodRankApi_QueryDishCollectionRankList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameChefGodRankApiServer).QueryDishCollectionRankList(ctx, req.(*QueryDishCollectionRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GameChefGodRankApi_ServiceDesc is the grpc.ServiceDesc for GameChefGodRankApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GameChefGodRankApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_chef_god_rank_api.GameChefGodRankApi",
	HandlerType: (*GameChefGodRankApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryRankList",
			Handler:    _GameChefGodRankApi_QueryRankList_Handler,
		},
		{
			MethodName: "QueryUserBottomInfo",
			Handler:    _GameChefGodRankApi_QueryUserBottomInfo_Handler,
		},
		{
			MethodName: "SetAnonymous",
			Handler:    _GameChefGodRankApi_SetAnonymous_Handler,
		},
		{
			MethodName: "QueryHallOfFame",
			Handler:    _GameChefGodRankApi_QueryHallOfFame_Handler,
		},
		{
			MethodName: "QueryDishCollectionRankList",
			Handler:    _GameChefGodRankApi_QueryDishCollectionRankList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_chef_god/rank_api/game_chef_god_rank_api.proto",
}
