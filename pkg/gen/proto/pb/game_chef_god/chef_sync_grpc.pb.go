// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_chef_god/chef_sync.proto

package game_chef_god

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ChefSyncServer_DishConfigSync_FullMethodName            = "/chef_god.ChefSyncServer/DishConfigSync"
	ChefSyncServer_FoodConfigSync_FullMethodName            = "/chef_god.ChefSyncServer/FoodConfigSync"
	ChefSyncServer_FoodDropConfigSync_FullMethodName        = "/chef_god.ChefSyncServer/FoodDropConfigSync"
	ChefSyncServer_CollectionGuideConfigSync_FullMethodName = "/chef_god.ChefSyncServer/CollectionGuideConfigSync"
	ChefSyncServer_ChefLevelSync_FullMethodName             = "/chef_god.ChefSyncServer/ChefLevelSync"
)

// ChefSyncServerClient is the client API for ChefSyncServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 厨神配置同步
type ChefSyncServerClient interface {
	// 菜品配置同步
	DishConfigSync(ctx context.Context, in *DishConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error)
	// 食材配置同步
	FoodConfigSync(ctx context.Context, in *FoodConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error)
	// 食材掉落配置同步
	FoodDropConfigSync(ctx context.Context, in *FoodDropConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error)
	// 收集图鉴配置同步
	CollectionGuideConfigSync(ctx context.Context, in *CollectionGuideConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error)
	// 厨神等级配置同步
	ChefLevelSync(ctx context.Context, in *ChefLevelSyncReq, opts ...grpc.CallOption) (*ChefLevelSyncRsp, error)
}

type chefSyncServerClient struct {
	cc grpc.ClientConnInterface
}

func NewChefSyncServerClient(cc grpc.ClientConnInterface) ChefSyncServerClient {
	return &chefSyncServerClient{cc}
}

func (c *chefSyncServerClient) DishConfigSync(ctx context.Context, in *DishConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DishConfigSyncRsp)
	err := c.cc.Invoke(ctx, ChefSyncServer_DishConfigSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chefSyncServerClient) FoodConfigSync(ctx context.Context, in *FoodConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DishConfigSyncRsp)
	err := c.cc.Invoke(ctx, ChefSyncServer_FoodConfigSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chefSyncServerClient) FoodDropConfigSync(ctx context.Context, in *FoodDropConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DishConfigSyncRsp)
	err := c.cc.Invoke(ctx, ChefSyncServer_FoodDropConfigSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chefSyncServerClient) CollectionGuideConfigSync(ctx context.Context, in *CollectionGuideConfigSyncReq, opts ...grpc.CallOption) (*DishConfigSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DishConfigSyncRsp)
	err := c.cc.Invoke(ctx, ChefSyncServer_CollectionGuideConfigSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chefSyncServerClient) ChefLevelSync(ctx context.Context, in *ChefLevelSyncReq, opts ...grpc.CallOption) (*ChefLevelSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChefLevelSyncRsp)
	err := c.cc.Invoke(ctx, ChefSyncServer_ChefLevelSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChefSyncServerServer is the server API for ChefSyncServer service.
// All implementations should embed UnimplementedChefSyncServerServer
// for forward compatibility
//
// 厨神配置同步
type ChefSyncServerServer interface {
	// 菜品配置同步
	DishConfigSync(context.Context, *DishConfigSyncReq) (*DishConfigSyncRsp, error)
	// 食材配置同步
	FoodConfigSync(context.Context, *FoodConfigSyncReq) (*DishConfigSyncRsp, error)
	// 食材掉落配置同步
	FoodDropConfigSync(context.Context, *FoodDropConfigSyncReq) (*DishConfigSyncRsp, error)
	// 收集图鉴配置同步
	CollectionGuideConfigSync(context.Context, *CollectionGuideConfigSyncReq) (*DishConfigSyncRsp, error)
	// 厨神等级配置同步
	ChefLevelSync(context.Context, *ChefLevelSyncReq) (*ChefLevelSyncRsp, error)
}

// UnimplementedChefSyncServerServer should be embedded to have forward compatible implementations.
type UnimplementedChefSyncServerServer struct {
}

func (UnimplementedChefSyncServerServer) DishConfigSync(context.Context, *DishConfigSyncReq) (*DishConfigSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DishConfigSync not implemented")
}
func (UnimplementedChefSyncServerServer) FoodConfigSync(context.Context, *FoodConfigSyncReq) (*DishConfigSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FoodConfigSync not implemented")
}
func (UnimplementedChefSyncServerServer) FoodDropConfigSync(context.Context, *FoodDropConfigSyncReq) (*DishConfigSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FoodDropConfigSync not implemented")
}
func (UnimplementedChefSyncServerServer) CollectionGuideConfigSync(context.Context, *CollectionGuideConfigSyncReq) (*DishConfigSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectionGuideConfigSync not implemented")
}
func (UnimplementedChefSyncServerServer) ChefLevelSync(context.Context, *ChefLevelSyncReq) (*ChefLevelSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChefLevelSync not implemented")
}

// UnsafeChefSyncServerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChefSyncServerServer will
// result in compilation errors.
type UnsafeChefSyncServerServer interface {
	mustEmbedUnimplementedChefSyncServerServer()
}

func RegisterChefSyncServerServer(s grpc.ServiceRegistrar, srv ChefSyncServerServer) {
	s.RegisterService(&ChefSyncServer_ServiceDesc, srv)
}

func _ChefSyncServer_DishConfigSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DishConfigSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChefSyncServerServer).DishConfigSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChefSyncServer_DishConfigSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChefSyncServerServer).DishConfigSync(ctx, req.(*DishConfigSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChefSyncServer_FoodConfigSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FoodConfigSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChefSyncServerServer).FoodConfigSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChefSyncServer_FoodConfigSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChefSyncServerServer).FoodConfigSync(ctx, req.(*FoodConfigSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChefSyncServer_FoodDropConfigSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FoodDropConfigSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChefSyncServerServer).FoodDropConfigSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChefSyncServer_FoodDropConfigSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChefSyncServerServer).FoodDropConfigSync(ctx, req.(*FoodDropConfigSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChefSyncServer_CollectionGuideConfigSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectionGuideConfigSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChefSyncServerServer).CollectionGuideConfigSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChefSyncServer_CollectionGuideConfigSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChefSyncServerServer).CollectionGuideConfigSync(ctx, req.(*CollectionGuideConfigSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChefSyncServer_ChefLevelSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChefLevelSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChefSyncServerServer).ChefLevelSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChefSyncServer_ChefLevelSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChefSyncServerServer).ChefLevelSync(ctx, req.(*ChefLevelSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ChefSyncServer_ServiceDesc is the grpc.ServiceDesc for ChefSyncServer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChefSyncServer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "chef_god.ChefSyncServer",
	HandlerType: (*ChefSyncServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DishConfigSync",
			Handler:    _ChefSyncServer_DishConfigSync_Handler,
		},
		{
			MethodName: "FoodConfigSync",
			Handler:    _ChefSyncServer_FoodConfigSync_Handler,
		},
		{
			MethodName: "FoodDropConfigSync",
			Handler:    _ChefSyncServer_FoodDropConfigSync_Handler,
		},
		{
			MethodName: "CollectionGuideConfigSync",
			Handler:    _ChefSyncServer_CollectionGuideConfigSync_Handler,
		},
		{
			MethodName: "ChefLevelSync",
			Handler:    _ChefSyncServer_ChefLevelSync_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_chef_god/chef_sync.proto",
}
