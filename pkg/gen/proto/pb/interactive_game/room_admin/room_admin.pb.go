// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/room_admin/room_admin.proto

package interactive_game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateRoomStatusReq_StatusTransition int32

const (
	UpdateRoomStatusReq_TransitionUnknown  UpdateRoomStatusReq_StatusTransition = 0 // 未知
	UpdateRoomStatusReq_TransitionReadying UpdateRoomStatusReq_StatusTransition = 1 // 准备中
	UpdateRoomStatusReq_TransitionRunning  UpdateRoomStatusReq_StatusTransition = 2 // 进行中
	UpdateRoomStatusReq_TransitionEnd      UpdateRoomStatusReq_StatusTransition = 3 // 已结束
)

// Enum value maps for UpdateRoomStatusReq_StatusTransition.
var (
	UpdateRoomStatusReq_StatusTransition_name = map[int32]string{
		0: "TransitionUnknown",
		1: "TransitionReadying",
		2: "TransitionRunning",
		3: "TransitionEnd",
	}
	UpdateRoomStatusReq_StatusTransition_value = map[string]int32{
		"TransitionUnknown":  0,
		"TransitionReadying": 1,
		"TransitionRunning":  2,
		"TransitionEnd":      3,
	}
)

func (x UpdateRoomStatusReq_StatusTransition) Enum() *UpdateRoomStatusReq_StatusTransition {
	p := new(UpdateRoomStatusReq_StatusTransition)
	*p = x
	return p
}

func (x UpdateRoomStatusReq_StatusTransition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateRoomStatusReq_StatusTransition) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_room_admin_room_admin_proto_enumTypes[0].Descriptor()
}

func (UpdateRoomStatusReq_StatusTransition) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_room_admin_room_admin_proto_enumTypes[0]
}

func (x UpdateRoomStatusReq_StatusTransition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateRoomStatusReq_StatusTransition.Descriptor instead.
func (UpdateRoomStatusReq_StatusTransition) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{10, 0}
}

type CreateRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId    string                   `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`                                            // 房间 id
	OwnerId   uint64                   `protobuf:"varint,2,opt,name=ownerId,proto3" json:"ownerId,omitempty"`                                         // 房主 uid
	GameAppId string                   `protobuf:"bytes,3,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`                                      // 游戏 appId
	RoomType  common.RoomType          `protobuf:"varint,4,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	Config    *common.CreateRoomConfig `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	// common.GameRoomConfig config = 5;
	ReportExtend map[string]string    `protobuf:"bytes,6,rep,name=reportExtend,proto3" json:"reportExtend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 上报扩展字段
	CreatorId    uint64               `protobuf:"varint,7,opt,name=creatorId,proto3" json:"creatorId,omitempty"`                                                                                              // 创建者 uid
	GameExtend   map[string]string    `protobuf:"bytes,8,rep,name=gameExtend,proto3" json:"gameExtend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`     // 游戏扩展字段
	PlatformInfo *common.PlatformInfo `protobuf:"bytes,9,opt,name=platformInfo,proto3" json:"platformInfo,omitempty"`                                                                                         // 平台信息
	// string mergeTaskId = 10;
	// bool match = 11;
	// int32 createSource = 12; // 平台来源
	CreateGameConfigs map[string]string `protobuf:"bytes,13,rep,name=createGameConfigs,proto3" json:"createGameConfigs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 创建游戏透传配置
	// repeated Player players = 14; // 默认玩家
	CreateOnlyNoExists        bool   `protobuf:"varint,15,opt,name=createOnlyNoExists,proto3" json:"createOnlyNoExists,omitempty"` // 仅不存在时创建
	RoomTitle                 string `protobuf:"bytes,16,opt,name=roomTitle,proto3" json:"roomTitle,omitempty"`
	IgnoreInsufficientBalance bool   `protobuf:"varint,17,opt,name=ignoreInsufficientBalance,proto3" json:"ignoreInsufficientBalance,omitempty"`
}

func (x *CreateRoomReq) Reset() {
	*x = CreateRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomReq) ProtoMessage() {}

func (x *CreateRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomReq.ProtoReflect.Descriptor instead.
func (*CreateRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CreateRoomReq) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *CreateRoomReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *CreateRoomReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *CreateRoomReq) GetConfig() *common.CreateRoomConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CreateRoomReq) GetReportExtend() map[string]string {
	if x != nil {
		return x.ReportExtend
	}
	return nil
}

func (x *CreateRoomReq) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *CreateRoomReq) GetGameExtend() map[string]string {
	if x != nil {
		return x.GameExtend
	}
	return nil
}

func (x *CreateRoomReq) GetPlatformInfo() *common.PlatformInfo {
	if x != nil {
		return x.PlatformInfo
	}
	return nil
}

func (x *CreateRoomReq) GetCreateGameConfigs() map[string]string {
	if x != nil {
		return x.CreateGameConfigs
	}
	return nil
}

func (x *CreateRoomReq) GetCreateOnlyNoExists() bool {
	if x != nil {
		return x.CreateOnlyNoExists
	}
	return false
}

func (x *CreateRoomReq) GetRoomTitle() string {
	if x != nil {
		return x.RoomTitle
	}
	return ""
}

func (x *CreateRoomReq) GetIgnoreInsufficientBalance() bool {
	if x != nil {
		return x.IgnoreInsufficientBalance
	}
	return false
}

type CreateRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameUrl       string `protobuf:"bytes,1,opt,name=gameUrl,proto3" json:"gameUrl,omitempty"` // 游戏链接
	GameRoomId    string `protobuf:"bytes,2,opt,name=gameRoomId,proto3" json:"gameRoomId,omitempty"`
	GameAppId     string `protobuf:"bytes,3,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Code          int32  `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Message       string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	ModeName      string `protobuf:"bytes,6,opt,name=modeName,proto3" json:"modeName,omitempty"`
	PayDifference int64  `protobuf:"varint,7,opt,name=payDifference,proto3" json:"payDifference,omitempty"`
}

func (x *CreateRoomRsp) Reset() {
	*x = CreateRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomRsp) ProtoMessage() {}

func (x *CreateRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomRsp.ProtoReflect.Descriptor instead.
func (*CreateRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CreateRoomRsp) GetGameUrl() string {
	if x != nil {
		return x.GameUrl
	}
	return ""
}

func (x *CreateRoomRsp) GetGameRoomId() string {
	if x != nil {
		return x.GameRoomId
	}
	return ""
}

func (x *CreateRoomRsp) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *CreateRoomRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateRoomRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateRoomRsp) GetModeName() string {
	if x != nil {
		return x.ModeName
	}
	return ""
}

func (x *CreateRoomRsp) GetPayDifference() int64 {
	if x != nil {
		return x.PayDifference
	}
	return 0
}

type DestroyRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Graceful bool   `protobuf:"varint,2,opt,name=graceful,proto3" json:"graceful,omitempty"`
}

func (x *DestroyRoomReq) Reset() {
	*x = DestroyRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestroyRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestroyRoomReq) ProtoMessage() {}

func (x *DestroyRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestroyRoomReq.ProtoReflect.Descriptor instead.
func (*DestroyRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{2}
}

func (x *DestroyRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *DestroyRoomReq) GetGraceful() bool {
	if x != nil {
		return x.Graceful
	}
	return false
}

type DestroyRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameRoomId string `protobuf:"bytes,1,opt,name=gameRoomId,proto3" json:"gameRoomId,omitempty"`
}

func (x *DestroyRoomRsp) Reset() {
	*x = DestroyRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DestroyRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DestroyRoomRsp) ProtoMessage() {}

func (x *DestroyRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DestroyRoomRsp.ProtoReflect.Descriptor instead.
func (*DestroyRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{3}
}

func (x *DestroyRoomRsp) GetGameRoomId() string {
	if x != nil {
		return x.GameRoomId
	}
	return ""
}

type QueryGameConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppIds []string `protobuf:"bytes,1,rep,name=gameAppIds,proto3" json:"gameAppIds,omitempty"` // 游戏 appId
	QueryAll   bool     `protobuf:"varint,2,opt,name=queryAll,proto3" json:"queryAll,omitempty"`    // 查询所有
}

func (x *QueryGameConfigReq) Reset() {
	*x = QueryGameConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameConfigReq) ProtoMessage() {}

func (x *QueryGameConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameConfigReq.ProtoReflect.Descriptor instead.
func (*QueryGameConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{4}
}

func (x *QueryGameConfigReq) GetGameAppIds() []string {
	if x != nil {
		return x.GameAppIds
	}
	return nil
}

func (x *QueryGameConfigReq) GetQueryAll() bool {
	if x != nil {
		return x.QueryAll
	}
	return false
}

type QueryGameConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modes map[string]*QueryGameConfigRsp_ModeConfigs `protobuf:"bytes,1,rep,name=modes,proto3" json:"modes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 游戏 appId -> 模式配置
	Apps  map[string]*QueryGameConfigRsp_AppConfig   `protobuf:"bytes,2,rep,name=apps,proto3" json:"apps,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`   // 游戏 appId -> 游戏配置
}

func (x *QueryGameConfigRsp) Reset() {
	*x = QueryGameConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameConfigRsp) ProtoMessage() {}

func (x *QueryGameConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameConfigRsp.ProtoReflect.Descriptor instead.
func (*QueryGameConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{5}
}

func (x *QueryGameConfigRsp) GetModes() map[string]*QueryGameConfigRsp_ModeConfigs {
	if x != nil {
		return x.Modes
	}
	return nil
}

func (x *QueryGameConfigRsp) GetApps() map[string]*QueryGameConfigRsp_AppConfig {
	if x != nil {
		return x.Apps
	}
	return nil
}

type QueryRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomIds []string `protobuf:"bytes,1,rep,name=roomIds,proto3" json:"roomIds,omitempty"` // 最多 50 个
}

func (x *QueryRoomReq) Reset() {
	*x = QueryRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomReq) ProtoMessage() {}

func (x *QueryRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomReq.ProtoReflect.Descriptor instead.
func (*QueryRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{6}
}

func (x *QueryRoomReq) GetRoomIds() []string {
	if x != nil {
		return x.RoomIds
	}
	return nil
}

type QueryRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rooms map[string]*QueryRoomRsp_Room `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // roomId -> room
}

func (x *QueryRoomRsp) Reset() {
	*x = QueryRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomRsp) ProtoMessage() {}

func (x *QueryRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomRsp.ProtoReflect.Descriptor instead.
func (*QueryRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{7}
}

func (x *QueryRoomRsp) GetRooms() map[string]*QueryRoomRsp_Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

type LeaveRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"` // 房间 id
	Uid    uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`      // uid
}

func (x *LeaveRoomReq) Reset() {
	*x = LeaveRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRoomReq) ProtoMessage() {}

func (x *LeaveRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRoomReq.ProtoReflect.Descriptor instead.
func (*LeaveRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{8}
}

func (x *LeaveRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *LeaveRoomReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type LeaveRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LeaveRoomRsp) Reset() {
	*x = LeaveRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRoomRsp) ProtoMessage() {}

func (x *LeaveRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRoomRsp.ProtoReflect.Descriptor instead.
func (*LeaveRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{9}
}

type UpdateRoomStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId string                               `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`                                                       // 游戏 appId
	RoomId    string                               `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`                                                             // 房间 id
	Status    UpdateRoomStatusReq_StatusTransition `protobuf:"varint,3,opt,name=status,proto3,enum=interactive_game.UpdateRoomStatusReq_StatusTransition" json:"status,omitempty"` // 状态变更
	Players   []*UpdateRoomStatusReq_Player        `protobuf:"bytes,4,rep,name=players,proto3" json:"players,omitempty"`                                                           // 玩家
	EventType common.EventType                     `protobuf:"varint,5,opt,name=eventType,proto3,enum=interactive_game.common.EventType" json:"eventType,omitempty"`               // 事件类型
	Uid       uint64                               `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`                                                                  // 事件触发 uid
	KickOut   bool                                 `protobuf:"varint,7,opt,name=kickOut,proto3" json:"kickOut,omitempty"`                                                          // 是否被踢 (eventType == EventLeave)
}

func (x *UpdateRoomStatusReq) Reset() {
	*x = UpdateRoomStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoomStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomStatusReq) ProtoMessage() {}

func (x *UpdateRoomStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateRoomStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateRoomStatusReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *UpdateRoomStatusReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *UpdateRoomStatusReq) GetStatus() UpdateRoomStatusReq_StatusTransition {
	if x != nil {
		return x.Status
	}
	return UpdateRoomStatusReq_TransitionUnknown
}

func (x *UpdateRoomStatusReq) GetPlayers() []*UpdateRoomStatusReq_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *UpdateRoomStatusReq) GetEventType() common.EventType {
	if x != nil {
		return x.EventType
	}
	return common.EventType(0)
}

func (x *UpdateRoomStatusReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UpdateRoomStatusReq) GetKickOut() bool {
	if x != nil {
		return x.KickOut
	}
	return false
}

type UpdateRoomStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateRoomStatusRsp) Reset() {
	*x = UpdateRoomStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoomStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomStatusRsp) ProtoMessage() {}

func (x *UpdateRoomStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomStatusRsp.ProtoReflect.Descriptor instead.
func (*UpdateRoomStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{11}
}

type CreateRoomCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId    string                   `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`                                            // 房间 id
	OwnerId   uint64                   `protobuf:"varint,2,opt,name=ownerId,proto3" json:"ownerId,omitempty"`                                         // 房主 uid
	GameAppId string                   `protobuf:"bytes,3,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`                                      // 游戏 appId
	RoomType  common.RoomType          `protobuf:"varint,4,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	Config    *common.CreateRoomConfig `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	// common.GameRoomConfig config = 5;
	CreatorId uint64 `protobuf:"varint,6,opt,name=creatorId,proto3" json:"creatorId,omitempty"` // 创建者 uid
}

func (x *CreateRoomCheckReq) Reset() {
	*x = CreateRoomCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoomCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomCheckReq) ProtoMessage() {}

func (x *CreateRoomCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomCheckReq.ProtoReflect.Descriptor instead.
func (*CreateRoomCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{12}
}

func (x *CreateRoomCheckReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CreateRoomCheckReq) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *CreateRoomCheckReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *CreateRoomCheckReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *CreateRoomCheckReq) GetConfig() *common.CreateRoomConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CreateRoomCheckReq) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

type CreateRoomCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Balance uint32 `protobuf:"varint,3,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *CreateRoomCheckRsp) Reset() {
	*x = CreateRoomCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoomCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomCheckRsp) ProtoMessage() {}

func (x *CreateRoomCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomCheckRsp.ProtoReflect.Descriptor instead.
func (*CreateRoomCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{13}
}

func (x *CreateRoomCheckRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateRoomCheckRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateRoomCheckRsp) GetBalance() uint32 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type ReloadRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *ReloadRoomReq) Reset() {
	*x = ReloadRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadRoomReq) ProtoMessage() {}

func (x *ReloadRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadRoomReq.ProtoReflect.Descriptor instead.
func (*ReloadRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{14}
}

func (x *ReloadRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type ReloadRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameUrl    string `protobuf:"bytes,1,opt,name=gameUrl,proto3" json:"gameUrl,omitempty"` // 游戏链接
	GameRoomId string `protobuf:"bytes,2,opt,name=gameRoomId,proto3" json:"gameRoomId,omitempty"`
	GameAppId  string `protobuf:"bytes,3,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Code       int32  `protobuf:"varint,4,opt,name=code,proto3" json:"code,omitempty"`
	Message    string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	ModeName   string `protobuf:"bytes,6,opt,name=modeName,proto3" json:"modeName,omitempty"`
}

func (x *ReloadRoomRsp) Reset() {
	*x = ReloadRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadRoomRsp) ProtoMessage() {}

func (x *ReloadRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadRoomRsp.ProtoReflect.Descriptor instead.
func (*ReloadRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{15}
}

func (x *ReloadRoomRsp) GetGameUrl() string {
	if x != nil {
		return x.GameUrl
	}
	return ""
}

func (x *ReloadRoomRsp) GetGameRoomId() string {
	if x != nil {
		return x.GameRoomId
	}
	return ""
}

func (x *ReloadRoomRsp) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *ReloadRoomRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ReloadRoomRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ReloadRoomRsp) GetModeName() string {
	if x != nil {
		return x.ModeName
	}
	return ""
}

type PreJoinRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Uid    uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *PreJoinRoomReq) Reset() {
	*x = PreJoinRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreJoinRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreJoinRoomReq) ProtoMessage() {}

func (x *PreJoinRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreJoinRoomReq.ProtoReflect.Descriptor instead.
func (*PreJoinRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{16}
}

func (x *PreJoinRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *PreJoinRoomReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type PreJoinRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    common.RoomStatus `protobuf:"varint,1,opt,name=status,proto3,enum=interactive_game.common.RoomStatus" json:"status,omitempty"`
	ModeId    string            `protobuf:"bytes,2,opt,name=modeId,proto3" json:"modeId,omitempty"`
	PayModeId string            `protobuf:"bytes,3,opt,name=payModeId,proto3" json:"payModeId,omitempty"`
	GameAppId string            `protobuf:"bytes,4,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
}

func (x *PreJoinRoomRsp) Reset() {
	*x = PreJoinRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreJoinRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreJoinRoomRsp) ProtoMessage() {}

func (x *PreJoinRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreJoinRoomRsp.ProtoReflect.Descriptor instead.
func (*PreJoinRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{17}
}

func (x *PreJoinRoomRsp) GetStatus() common.RoomStatus {
	if x != nil {
		return x.Status
	}
	return common.RoomStatus(0)
}

func (x *PreJoinRoomRsp) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *PreJoinRoomRsp) GetPayModeId() string {
	if x != nil {
		return x.PayModeId
	}
	return ""
}

func (x *PreJoinRoomRsp) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

type CheckFreeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid       uint64                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 房主 uid
	GameAppId string                    `protobuf:"bytes,2,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	PayModeId string                    `protobuf:"bytes,3,opt,name=payModeId,proto3" json:"payModeId,omitempty"`
	RoomType  common.RoomType           `protobuf:"varint,4,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	PayConfig *common.GameRoomPayConfig `protobuf:"bytes,5,opt,name=payConfig,proto3" json:"payConfig,omitempty"`
}

func (x *CheckFreeReq) Reset() {
	*x = CheckFreeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckFreeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFreeReq) ProtoMessage() {}

func (x *CheckFreeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFreeReq.ProtoReflect.Descriptor instead.
func (*CheckFreeReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{18}
}

func (x *CheckFreeReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CheckFreeReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *CheckFreeReq) GetPayModeId() string {
	if x != nil {
		return x.PayModeId
	}
	return ""
}

func (x *CheckFreeReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *CheckFreeReq) GetPayConfig() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConfig
	}
	return nil
}

type CheckFreeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Balance int64 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (x *CheckFreeRsp) Reset() {
	*x = CheckFreeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckFreeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckFreeRsp) ProtoMessage() {}

func (x *CheckFreeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckFreeRsp.ProtoReflect.Descriptor instead.
func (*CheckFreeRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{19}
}

func (x *CheckFreeRsp) GetBalance() int64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

type CheckBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid       uint64                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 房主 uid
	GameAppId string                    `protobuf:"bytes,2,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	RoomType  common.RoomType           `protobuf:"varint,3,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	PayConfig *common.GameRoomPayConfig `protobuf:"bytes,4,opt,name=payConfig,proto3" json:"payConfig,omitempty"`                                      // 付费配置
}

func (x *CheckBalanceReq) Reset() {
	*x = CheckBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBalanceReq) ProtoMessage() {}

func (x *CheckBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBalanceReq.ProtoReflect.Descriptor instead.
func (*CheckBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{20}
}

func (x *CheckBalanceReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CheckBalanceReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *CheckBalanceReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *CheckBalanceReq) GetPayConfig() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConfig
	}
	return nil
}

type CheckBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code          int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	PayDifference int64  `protobuf:"varint,3,opt,name=payDifference,proto3" json:"payDifference,omitempty"`
}

func (x *CheckBalanceRsp) Reset() {
	*x = CheckBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBalanceRsp) ProtoMessage() {}

func (x *CheckBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBalanceRsp.ProtoReflect.Descriptor instead.
func (*CheckBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{21}
}

func (x *CheckBalanceRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckBalanceRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckBalanceRsp) GetPayDifference() int64 {
	if x != nil {
		return x.PayDifference
	}
	return 0
}

type AddPlayersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId  string                  `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Players []*AddPlayersReq_Player `protobuf:"bytes,2,rep,name=players,proto3" json:"players,omitempty"`
}

func (x *AddPlayersReq) Reset() {
	*x = AddPlayersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPlayersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayersReq) ProtoMessage() {}

func (x *AddPlayersReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayersReq.ProtoReflect.Descriptor instead.
func (*AddPlayersReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{22}
}

func (x *AddPlayersReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *AddPlayersReq) GetPlayers() []*AddPlayersReq_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

type AddPlayersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uids []uint64 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"` // 成功加入的 uid
}

func (x *AddPlayersRsp) Reset() {
	*x = AddPlayersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPlayersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayersRsp) ProtoMessage() {}

func (x *AddPlayersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayersRsp.ProtoReflect.Descriptor instead.
func (*AddPlayersRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{23}
}

func (x *AddPlayersRsp) GetUids() []uint64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

type CheckPayConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        uint64                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameAppId  string                      `protobuf:"bytes,2,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	RoomType   common.RoomType             `protobuf:"varint,3,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	PayConfigs []*common.GameRoomPayConfig `protobuf:"bytes,4,rep,name=payConfigs,proto3" json:"payConfigs,omitempty"`
}

func (x *CheckPayConfigReq) Reset() {
	*x = CheckPayConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPayConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPayConfigReq) ProtoMessage() {}

func (x *CheckPayConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPayConfigReq.ProtoReflect.Descriptor instead.
func (*CheckPayConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{24}
}

func (x *CheckPayConfigReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CheckPayConfigReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *CheckPayConfigReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *CheckPayConfigReq) GetPayConfigs() []*common.GameRoomPayConfig {
	if x != nil {
		return x.PayConfigs
	}
	return nil
}

type CheckPayConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results map[string]*CheckPayConfigRsp_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // payModeId -> result
}

func (x *CheckPayConfigRsp) Reset() {
	*x = CheckPayConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPayConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPayConfigRsp) ProtoMessage() {}

func (x *CheckPayConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPayConfigRsp.ProtoReflect.Descriptor instead.
func (*CheckPayConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{25}
}

func (x *CheckPayConfigRsp) GetResults() map[string]*CheckPayConfigRsp_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type PendingRoomNotiyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId  string                        `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`    // 游戏 appId
	RoomId     string                        `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`          // 房间 id
	Players    []*PendingRoomNotiyReq_Player `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`        // 当前玩家信息
	CreateTime int64                         `protobuf:"varint,4,opt,name=createTime,proto3" json:"createTime,omitempty"` // 房间创建时间
}

func (x *PendingRoomNotiyReq) Reset() {
	*x = PendingRoomNotiyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PendingRoomNotiyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PendingRoomNotiyReq) ProtoMessage() {}

func (x *PendingRoomNotiyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PendingRoomNotiyReq.ProtoReflect.Descriptor instead.
func (*PendingRoomNotiyReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{26}
}

func (x *PendingRoomNotiyReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *PendingRoomNotiyReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *PendingRoomNotiyReq) GetPlayers() []*PendingRoomNotiyReq_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *PendingRoomNotiyReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

type PendingRoomNotiyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PendingRoomNotiyRsp) Reset() {
	*x = PendingRoomNotiyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PendingRoomNotiyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PendingRoomNotiyRsp) ProtoMessage() {}

func (x *PendingRoomNotiyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PendingRoomNotiyRsp.ProtoReflect.Descriptor instead.
func (*PendingRoomNotiyRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{27}
}

type CreateRoomReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *CreateRoomReq_Player) Reset() {
	*x = CreateRoomReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoomReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoomReq_Player) ProtoMessage() {}

func (x *CreateRoomReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoomReq_Player.ProtoReflect.Descriptor instead.
func (*CreateRoomReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CreateRoomReq_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type QueryGameConfigRsp_ModeConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModeId       string                       `protobuf:"bytes,1,opt,name=modeId,proto3" json:"modeId,omitempty"`
	Name         string                       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                 // 名称
	MakeUpConfig *common.GameRoomMakeUpConfig `protobuf:"bytes,3,opt,name=makeUpConfig,proto3" json:"makeUpConfig,omitempty"` // 组局配置
	AssetId      int64                        `protobuf:"varint,4,opt,name=assetId,proto3" json:"assetId,omitempty"`
	DirectIds    []uint32                     `protobuf:"varint,5,rep,packed,name=directIds,proto3" json:"directIds,omitempty"` // 定向id
}

func (x *QueryGameConfigRsp_ModeConfig) Reset() {
	*x = QueryGameConfigRsp_ModeConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameConfigRsp_ModeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameConfigRsp_ModeConfig) ProtoMessage() {}

func (x *QueryGameConfigRsp_ModeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameConfigRsp_ModeConfig.ProtoReflect.Descriptor instead.
func (*QueryGameConfigRsp_ModeConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{5, 0}
}

func (x *QueryGameConfigRsp_ModeConfig) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *QueryGameConfigRsp_ModeConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QueryGameConfigRsp_ModeConfig) GetMakeUpConfig() *common.GameRoomMakeUpConfig {
	if x != nil {
		return x.MakeUpConfig
	}
	return nil
}

func (x *QueryGameConfigRsp_ModeConfig) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *QueryGameConfigRsp_ModeConfig) GetDirectIds() []uint32 {
	if x != nil {
		return x.DirectIds
	}
	return nil
}

type QueryGameConfigRsp_ModeConfigs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Configs []*QueryGameConfigRsp_ModeConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
}

func (x *QueryGameConfigRsp_ModeConfigs) Reset() {
	*x = QueryGameConfigRsp_ModeConfigs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameConfigRsp_ModeConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameConfigRsp_ModeConfigs) ProtoMessage() {}

func (x *QueryGameConfigRsp_ModeConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameConfigRsp_ModeConfigs.ProtoReflect.Descriptor instead.
func (*QueryGameConfigRsp_ModeConfigs) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{5, 1}
}

func (x *QueryGameConfigRsp_ModeConfigs) GetConfigs() []*QueryGameConfigRsp_ModeConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type QueryGameConfigRsp_AppConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UseFreePayModeIds []string `protobuf:"bytes,1,rep,name=useFreePayModeIds,proto3" json:"useFreePayModeIds,omitempty"`
	PriorityPayModeId []string `protobuf:"bytes,2,rep,name=priorityPayModeId,proto3" json:"priorityPayModeId,omitempty"`
}

func (x *QueryGameConfigRsp_AppConfig) Reset() {
	*x = QueryGameConfigRsp_AppConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGameConfigRsp_AppConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGameConfigRsp_AppConfig) ProtoMessage() {}

func (x *QueryGameConfigRsp_AppConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGameConfigRsp_AppConfig.ProtoReflect.Descriptor instead.
func (*QueryGameConfigRsp_AppConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{5, 2}
}

func (x *QueryGameConfigRsp_AppConfig) GetUseFreePayModeIds() []string {
	if x != nil {
		return x.UseFreePayModeIds
	}
	return nil
}

func (x *QueryGameConfigRsp_AppConfig) GetPriorityPayModeId() []string {
	if x != nil {
		return x.PriorityPayModeId
	}
	return nil
}

type QueryRoomRsp_RoomConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModeId       string                       `protobuf:"bytes,1,opt,name=modeId,proto3" json:"modeId,omitempty"`
	JoinMode     common.JoinMode              `protobuf:"varint,2,opt,name=joinMode,proto3,enum=interactive_game.common.JoinMode" json:"joinMode,omitempty"`
	PayConfig    *common.GameRoomPayConfig    `protobuf:"bytes,3,opt,name=payConfig,proto3" json:"payConfig,omitempty"`
	MakeUpConfig *common.GameRoomMakeUpConfig `protobuf:"bytes,4,opt,name=makeUpConfig,proto3" json:"makeUpConfig,omitempty"`
}

func (x *QueryRoomRsp_RoomConfig) Reset() {
	*x = QueryRoomRsp_RoomConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomRsp_RoomConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomRsp_RoomConfig) ProtoMessage() {}

func (x *QueryRoomRsp_RoomConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomRsp_RoomConfig.ProtoReflect.Descriptor instead.
func (*QueryRoomRsp_RoomConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{7, 0}
}

func (x *QueryRoomRsp_RoomConfig) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *QueryRoomRsp_RoomConfig) GetJoinMode() common.JoinMode {
	if x != nil {
		return x.JoinMode
	}
	return common.JoinMode(0)
}

func (x *QueryRoomRsp_RoomConfig) GetPayConfig() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConfig
	}
	return nil
}

func (x *QueryRoomRsp_RoomConfig) GetMakeUpConfig() *common.GameRoomMakeUpConfig {
	if x != nil {
		return x.MakeUpConfig
	}
	return nil
}

type QueryRoomRsp_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` // 座位号
	Uid   uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *QueryRoomRsp_Player) Reset() {
	*x = QueryRoomRsp_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomRsp_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomRsp_Player) ProtoMessage() {}

func (x *QueryRoomRsp_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomRsp_Player.ProtoReflect.Descriptor instead.
func (*QueryRoomRsp_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{7, 1}
}

func (x *QueryRoomRsp_Player) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *QueryRoomRsp_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type QueryRoomRsp_Room struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       common.RoomStatus        `protobuf:"varint,1,opt,name=status,proto3,enum=interactive_game.common.RoomStatus" json:"status,omitempty"`
	GameAppId    string                   `protobuf:"bytes,2,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	OwnerId      uint64                   `protobuf:"varint,3,opt,name=ownerId,proto3" json:"ownerId,omitempty"`
	Players      []*QueryRoomRsp_Player   `protobuf:"bytes,4,rep,name=players,proto3" json:"players,omitempty"`
	RoundId      string                   `protobuf:"bytes,5,opt,name=roundId,proto3" json:"roundId,omitempty"`
	PlatformInfo *common.PlatformInfo     `protobuf:"bytes,6,opt,name=platformInfo,proto3" json:"platformInfo,omitempty"`
	Config       *QueryRoomRsp_RoomConfig `protobuf:"bytes,7,opt,name=config,proto3" json:"config,omitempty"`
	RoomType     common.RoomType          `protobuf:"varint,8,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"`
	UpdateTime   int64                    `protobuf:"varint,9,opt,name=updateTime,proto3" json:"updateTime,omitempty"` // 最后更新时间
	MatchTime    int64                    `protobuf:"varint,10,opt,name=matchTime,proto3" json:"matchTime,omitempty"`
}

func (x *QueryRoomRsp_Room) Reset() {
	*x = QueryRoomRsp_Room{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRoomRsp_Room) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRoomRsp_Room) ProtoMessage() {}

func (x *QueryRoomRsp_Room) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRoomRsp_Room.ProtoReflect.Descriptor instead.
func (*QueryRoomRsp_Room) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{7, 2}
}

func (x *QueryRoomRsp_Room) GetStatus() common.RoomStatus {
	if x != nil {
		return x.Status
	}
	return common.RoomStatus(0)
}

func (x *QueryRoomRsp_Room) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *QueryRoomRsp_Room) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *QueryRoomRsp_Room) GetPlayers() []*QueryRoomRsp_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *QueryRoomRsp_Room) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *QueryRoomRsp_Room) GetPlatformInfo() *common.PlatformInfo {
	if x != nil {
		return x.PlatformInfo
	}
	return nil
}

func (x *QueryRoomRsp_Room) GetConfig() *QueryRoomRsp_RoomConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *QueryRoomRsp_Room) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *QueryRoomRsp_Room) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *QueryRoomRsp_Room) GetMatchTime() int64 {
	if x != nil {
		return x.MatchTime
	}
	return 0
}

type UpdateRoomStatusReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index      uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` // 座位号
	Uid        uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Eliminated bool   `protobuf:"varint,3,opt,name=eliminated,proto3" json:"eliminated,omitempty"` // 被淘汰
}

func (x *UpdateRoomStatusReq_Player) Reset() {
	*x = UpdateRoomStatusReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoomStatusReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoomStatusReq_Player) ProtoMessage() {}

func (x *UpdateRoomStatusReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoomStatusReq_Player.ProtoReflect.Descriptor instead.
func (*UpdateRoomStatusReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{10, 0}
}

func (x *UpdateRoomStatusReq_Player) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *UpdateRoomStatusReq_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UpdateRoomStatusReq_Player) GetEliminated() bool {
	if x != nil {
		return x.Eliminated
	}
	return false
}

type AddPlayersReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid   uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Robot bool   `protobuf:"varint,3,opt,name=robot,proto3" json:"robot,omitempty"`
	Index int64  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"` // 座位号 0 自动选择
}

func (x *AddPlayersReq_Player) Reset() {
	*x = AddPlayersReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPlayersReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayersReq_Player) ProtoMessage() {}

func (x *AddPlayersReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayersReq_Player.ProtoReflect.Descriptor instead.
func (*AddPlayersReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{22, 0}
}

func (x *AddPlayersReq_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *AddPlayersReq_Player) GetRobot() bool {
	if x != nil {
		return x.Robot
	}
	return false
}

func (x *AddPlayersReq_Player) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type CheckPayConfigRsp_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Shortfall       int64 `protobuf:"varint,1,opt,name=shortfall,proto3" json:"shortfall,omitempty"`
	UseFree         bool  `protobuf:"varint,2,opt,name=useFree,proto3" json:"useFree,omitempty"`
	UseAlternate    bool  `protobuf:"varint,3,opt,name=useAlternate,proto3" json:"useAlternate,omitempty"`
	AlternateEnough bool  `protobuf:"varint,4,opt,name=alternateEnough,proto3" json:"alternateEnough,omitempty"`
	FreeTimes       int64 `protobuf:"varint,5,opt,name=freeTimes,proto3" json:"freeTimes,omitempty"`
}

func (x *CheckPayConfigRsp_Result) Reset() {
	*x = CheckPayConfigRsp_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckPayConfigRsp_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckPayConfigRsp_Result) ProtoMessage() {}

func (x *CheckPayConfigRsp_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckPayConfigRsp_Result.ProtoReflect.Descriptor instead.
func (*CheckPayConfigRsp_Result) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{25, 0}
}

func (x *CheckPayConfigRsp_Result) GetShortfall() int64 {
	if x != nil {
		return x.Shortfall
	}
	return 0
}

func (x *CheckPayConfigRsp_Result) GetUseFree() bool {
	if x != nil {
		return x.UseFree
	}
	return false
}

func (x *CheckPayConfigRsp_Result) GetUseAlternate() bool {
	if x != nil {
		return x.UseAlternate
	}
	return false
}

func (x *CheckPayConfigRsp_Result) GetAlternateEnough() bool {
	if x != nil {
		return x.AlternateEnough
	}
	return false
}

func (x *CheckPayConfigRsp_Result) GetFreeTimes() int64 {
	if x != nil {
		return x.FreeTimes
	}
	return 0
}

type PendingRoomNotiyReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index      uint32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` // 座位号
	Uid        uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Eliminated bool   `protobuf:"varint,3,opt,name=eliminated,proto3" json:"eliminated,omitempty"` // 被淘汰
	Robot      bool   `protobuf:"varint,4,opt,name=robot,proto3" json:"robot,omitempty"`
	Timestamp  int64  `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 玩家入座时间
}

func (x *PendingRoomNotiyReq_Player) Reset() {
	*x = PendingRoomNotiyReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PendingRoomNotiyReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PendingRoomNotiyReq_Player) ProtoMessage() {}

func (x *PendingRoomNotiyReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PendingRoomNotiyReq_Player.ProtoReflect.Descriptor instead.
func (*PendingRoomNotiyReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP(), []int{26, 0}
}

func (x *PendingRoomNotiyReq_Player) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *PendingRoomNotiyReq_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PendingRoomNotiyReq_Player) GetEliminated() bool {
	if x != nil {
		return x.Eliminated
	}
	return false
}

func (x *PendingRoomNotiyReq_Player) GetRobot() bool {
	if x != nil {
		return x.Robot
	}
	return false
}

func (x *PendingRoomNotiyReq_Player) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

var File_pb_interactive_game_room_admin_room_admin_proto protoreflect.FileDescriptor

var file_pb_interactive_game_room_admin_room_admin_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc6, 0x07, 0x0a,
	0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x3d,
	0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f,
	0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x55, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x67, 0x61, 0x6d, 0x65,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x64, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x4e, 0x6f, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x6c, 0x79, 0x4e,
	0x6f, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x6f, 0x6d,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x19, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x49,
	0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x49, 0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x1a, 0x1a, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x1a,
	0x3f, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3d, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x44, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd7, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x55,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x79,
	0x44, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x70, 0x61, 0x79, 0x44, 0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22,
	0x44, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72, 0x61,
	0x63, 0x65, 0x66, 0x75, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x67, 0x72, 0x61,
	0x63, 0x65, 0x66, 0x75, 0x6c, 0x22, 0x30, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79,
	0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x52,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x61, 0x6d,
	0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a,
	0x0a, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x71, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x71, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x6c, 0x22, 0xfd, 0x05, 0x0a, 0x12, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70,
	0x12, 0x45, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x1a, 0xc3, 0x01, 0x0a, 0x0a,
	0x4d, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x6d, 0x61, 0x6b, 0x65, 0x55, 0x70,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x4d,
	0x61, 0x6b, 0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x6d, 0x61, 0x6b,
	0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x49, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x73, 0x1a, 0x58, 0x0a, 0x0b, 0x4d, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x12, 0x49, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x1a, 0x67, 0x0a, 0x09, 0x41,
	0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2c, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x46,
	0x72, 0x65, 0x65, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x11, 0x75, 0x73, 0x65, 0x46, 0x72, 0x65, 0x65, 0x50, 0x61, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x11, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x50, 0x61, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x1a, 0x6a, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x46, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x67, 0x0a, 0x09, 0x41, 0x70, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x44, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x73, 0x70, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x28, 0x0a, 0x0c, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x6f, 0x6d,
	0x49, 0x64, 0x73, 0x22, 0xc7, 0x07, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f,
	0x6d, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d,
	0x52, 0x73, 0x70, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05,
	0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x1a, 0x80, 0x02, 0x0a, 0x0a, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08,
	0x6a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x48, 0x0a, 0x09, 0x70,
	0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f,
	0x6d, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x70, 0x61, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x51, 0x0a, 0x0c, 0x6d, 0x61, 0x6b, 0x65, 0x55, 0x70, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x61,
	0x6b, 0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x6d, 0x61, 0x6b, 0x65,
	0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x30, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x1a, 0xe1, 0x03, 0x0a, 0x04, 0x52,
	0x6f, 0x6f, 0x6d, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41,
	0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x5d,
	0x0a, 0x0a, 0x52, 0x6f, 0x6f, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x38, 0x0a,
	0x0c, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x22, 0x90, 0x04, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x69, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x6b, 0x69, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x1a, 0x50, 0x0a, 0x06, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x22, 0x6b, 0x0a,
	0x10, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x64, 0x79, 0x69, 0x6e, 0x67, 0x10, 0x01,
	0x12, 0x15, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x10, 0x03, 0x22, 0x15, 0x0a, 0x13, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x22, 0x84, 0x02, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72,
	0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x5c, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x27, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22,
	0xb1, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x67,
	0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x67, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x6f,
	0x6f, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22,
	0xa1, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x73, 0x70, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f,
	0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70,
	0x70, 0x49, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x72, 0x65,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x48, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x09, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x28, 0x0a, 0x0c, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x46, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xca, 0x01, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x65, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x61, 0x79, 0x44, 0x69, 0x66, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x44,
	0x69, 0x66, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x0d, 0x41, 0x64,
	0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x73, 0x1a, 0x46, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x23, 0x0a,
	0x0d, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x04, 0x75, 0x69,
	0x64, 0x73, 0x22, 0xce, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72,
	0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x22, 0xf6, 0x02, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x79,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0xac, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x66, 0x61, 0x6c, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x66, 0x61, 0x6c, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x46, 0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x46, 0x72, 0x65, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x41,
	0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x75, 0x73, 0x65, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f,
	0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x1a, 0x66, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xba, 0x02, 0x0a,
	0x13, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x6f, 0x6f, 0x6d, 0x4e, 0x6f, 0x74, 0x69,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x07, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x6f, 0x6f, 0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x79, 0x52,
	0x65, 0x71, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x1a, 0x84, 0x01, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x65, 0x6c, 0x69, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x15, 0x0a, 0x13, 0x50, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x6f, 0x6f, 0x6d, 0x4e, 0x6f, 0x74, 0x69, 0x79, 0x52, 0x73, 0x70,
	0x32, 0xf8, 0x07, 0x0a, 0x09, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x4e,
	0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1f, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x51,
	0x0a, 0x0b, 0x44, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x20, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x44, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x44, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x5d, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70,
	0x12, 0x4b, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1e, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x4b, 0x0a,
	0x09, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1e, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x0f, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x24, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6f,
	0x6d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0a, 0x52, 0x65, 0x6c,
	0x6f, 0x61, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x6f, 0x61,
	0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6c, 0x6f,
	0x61, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x0b, 0x50, 0x72, 0x65,
	0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x65, 0x4a,
	0x6f, 0x69, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72,
	0x65, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x09,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x72, 0x65, 0x65, 0x12, 0x1e, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x46, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x46, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x0c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x4e, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x5a, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50,
	0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x42, 0x48, 0x5a, 0x46, 0x74,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_room_admin_room_admin_proto_rawDescOnce sync.Once
	file_pb_interactive_game_room_admin_room_admin_proto_rawDescData = file_pb_interactive_game_room_admin_room_admin_proto_rawDesc
)

func file_pb_interactive_game_room_admin_room_admin_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_room_admin_room_admin_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_room_admin_room_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_room_admin_room_admin_proto_rawDescData)
	})
	return file_pb_interactive_game_room_admin_room_admin_proto_rawDescData
}

var file_pb_interactive_game_room_admin_room_admin_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_interactive_game_room_admin_room_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_pb_interactive_game_room_admin_room_admin_proto_goTypes = []interface{}{
	(UpdateRoomStatusReq_StatusTransition)(0), // 0: interactive_game.UpdateRoomStatusReq.StatusTransition
	(*CreateRoomReq)(nil),                     // 1: interactive_game.CreateRoomReq
	(*CreateRoomRsp)(nil),                     // 2: interactive_game.CreateRoomRsp
	(*DestroyRoomReq)(nil),                    // 3: interactive_game.DestroyRoomReq
	(*DestroyRoomRsp)(nil),                    // 4: interactive_game.DestroyRoomRsp
	(*QueryGameConfigReq)(nil),                // 5: interactive_game.QueryGameConfigReq
	(*QueryGameConfigRsp)(nil),                // 6: interactive_game.QueryGameConfigRsp
	(*QueryRoomReq)(nil),                      // 7: interactive_game.QueryRoomReq
	(*QueryRoomRsp)(nil),                      // 8: interactive_game.QueryRoomRsp
	(*LeaveRoomReq)(nil),                      // 9: interactive_game.LeaveRoomReq
	(*LeaveRoomRsp)(nil),                      // 10: interactive_game.LeaveRoomRsp
	(*UpdateRoomStatusReq)(nil),               // 11: interactive_game.UpdateRoomStatusReq
	(*UpdateRoomStatusRsp)(nil),               // 12: interactive_game.UpdateRoomStatusRsp
	(*CreateRoomCheckReq)(nil),                // 13: interactive_game.CreateRoomCheckReq
	(*CreateRoomCheckRsp)(nil),                // 14: interactive_game.CreateRoomCheckRsp
	(*ReloadRoomReq)(nil),                     // 15: interactive_game.ReloadRoomReq
	(*ReloadRoomRsp)(nil),                     // 16: interactive_game.ReloadRoomRsp
	(*PreJoinRoomReq)(nil),                    // 17: interactive_game.PreJoinRoomReq
	(*PreJoinRoomRsp)(nil),                    // 18: interactive_game.PreJoinRoomRsp
	(*CheckFreeReq)(nil),                      // 19: interactive_game.CheckFreeReq
	(*CheckFreeRsp)(nil),                      // 20: interactive_game.CheckFreeRsp
	(*CheckBalanceReq)(nil),                   // 21: interactive_game.CheckBalanceReq
	(*CheckBalanceRsp)(nil),                   // 22: interactive_game.CheckBalanceRsp
	(*AddPlayersReq)(nil),                     // 23: interactive_game.AddPlayersReq
	(*AddPlayersRsp)(nil),                     // 24: interactive_game.AddPlayersRsp
	(*CheckPayConfigReq)(nil),                 // 25: interactive_game.CheckPayConfigReq
	(*CheckPayConfigRsp)(nil),                 // 26: interactive_game.CheckPayConfigRsp
	(*PendingRoomNotiyReq)(nil),               // 27: interactive_game.PendingRoomNotiyReq
	(*PendingRoomNotiyRsp)(nil),               // 28: interactive_game.PendingRoomNotiyRsp
	(*CreateRoomReq_Player)(nil),              // 29: interactive_game.CreateRoomReq.Player
	nil,                                       // 30: interactive_game.CreateRoomReq.ReportExtendEntry
	nil,                                       // 31: interactive_game.CreateRoomReq.GameExtendEntry
	nil,                                       // 32: interactive_game.CreateRoomReq.CreateGameConfigsEntry
	(*QueryGameConfigRsp_ModeConfig)(nil),     // 33: interactive_game.QueryGameConfigRsp.ModeConfig
	(*QueryGameConfigRsp_ModeConfigs)(nil),    // 34: interactive_game.QueryGameConfigRsp.ModeConfigs
	(*QueryGameConfigRsp_AppConfig)(nil),      // 35: interactive_game.QueryGameConfigRsp.AppConfig
	nil,                                       // 36: interactive_game.QueryGameConfigRsp.ModesEntry
	nil,                                       // 37: interactive_game.QueryGameConfigRsp.AppsEntry
	(*QueryRoomRsp_RoomConfig)(nil),           // 38: interactive_game.QueryRoomRsp.RoomConfig
	(*QueryRoomRsp_Player)(nil),               // 39: interactive_game.QueryRoomRsp.Player
	(*QueryRoomRsp_Room)(nil),                 // 40: interactive_game.QueryRoomRsp.Room
	nil,                                       // 41: interactive_game.QueryRoomRsp.RoomsEntry
	(*UpdateRoomStatusReq_Player)(nil),        // 42: interactive_game.UpdateRoomStatusReq.Player
	(*AddPlayersReq_Player)(nil),              // 43: interactive_game.AddPlayersReq.Player
	(*CheckPayConfigRsp_Result)(nil),          // 44: interactive_game.CheckPayConfigRsp.Result
	nil,                                       // 45: interactive_game.CheckPayConfigRsp.ResultsEntry
	(*PendingRoomNotiyReq_Player)(nil),        // 46: interactive_game.PendingRoomNotiyReq.Player
	(common.RoomType)(0),                      // 47: interactive_game.common.RoomType
	(*common.CreateRoomConfig)(nil),           // 48: interactive_game.common.CreateRoomConfig
	(*common.PlatformInfo)(nil),               // 49: interactive_game.common.PlatformInfo
	(common.EventType)(0),                     // 50: interactive_game.common.EventType
	(common.RoomStatus)(0),                    // 51: interactive_game.common.RoomStatus
	(*common.GameRoomPayConfig)(nil),          // 52: interactive_game.common.GameRoomPayConfig
	(*common.GameRoomMakeUpConfig)(nil),       // 53: interactive_game.common.GameRoomMakeUpConfig
	(common.JoinMode)(0),                      // 54: interactive_game.common.JoinMode
}
var file_pb_interactive_game_room_admin_room_admin_proto_depIdxs = []int32{
	47, // 0: interactive_game.CreateRoomReq.roomType:type_name -> interactive_game.common.RoomType
	48, // 1: interactive_game.CreateRoomReq.config:type_name -> interactive_game.common.CreateRoomConfig
	30, // 2: interactive_game.CreateRoomReq.reportExtend:type_name -> interactive_game.CreateRoomReq.ReportExtendEntry
	31, // 3: interactive_game.CreateRoomReq.gameExtend:type_name -> interactive_game.CreateRoomReq.GameExtendEntry
	49, // 4: interactive_game.CreateRoomReq.platformInfo:type_name -> interactive_game.common.PlatformInfo
	32, // 5: interactive_game.CreateRoomReq.createGameConfigs:type_name -> interactive_game.CreateRoomReq.CreateGameConfigsEntry
	36, // 6: interactive_game.QueryGameConfigRsp.modes:type_name -> interactive_game.QueryGameConfigRsp.ModesEntry
	37, // 7: interactive_game.QueryGameConfigRsp.apps:type_name -> interactive_game.QueryGameConfigRsp.AppsEntry
	41, // 8: interactive_game.QueryRoomRsp.rooms:type_name -> interactive_game.QueryRoomRsp.RoomsEntry
	0,  // 9: interactive_game.UpdateRoomStatusReq.status:type_name -> interactive_game.UpdateRoomStatusReq.StatusTransition
	42, // 10: interactive_game.UpdateRoomStatusReq.players:type_name -> interactive_game.UpdateRoomStatusReq.Player
	50, // 11: interactive_game.UpdateRoomStatusReq.eventType:type_name -> interactive_game.common.EventType
	47, // 12: interactive_game.CreateRoomCheckReq.roomType:type_name -> interactive_game.common.RoomType
	48, // 13: interactive_game.CreateRoomCheckReq.config:type_name -> interactive_game.common.CreateRoomConfig
	51, // 14: interactive_game.PreJoinRoomRsp.status:type_name -> interactive_game.common.RoomStatus
	47, // 15: interactive_game.CheckFreeReq.roomType:type_name -> interactive_game.common.RoomType
	52, // 16: interactive_game.CheckFreeReq.payConfig:type_name -> interactive_game.common.GameRoomPayConfig
	47, // 17: interactive_game.CheckBalanceReq.roomType:type_name -> interactive_game.common.RoomType
	52, // 18: interactive_game.CheckBalanceReq.payConfig:type_name -> interactive_game.common.GameRoomPayConfig
	43, // 19: interactive_game.AddPlayersReq.players:type_name -> interactive_game.AddPlayersReq.Player
	47, // 20: interactive_game.CheckPayConfigReq.roomType:type_name -> interactive_game.common.RoomType
	52, // 21: interactive_game.CheckPayConfigReq.payConfigs:type_name -> interactive_game.common.GameRoomPayConfig
	45, // 22: interactive_game.CheckPayConfigRsp.results:type_name -> interactive_game.CheckPayConfigRsp.ResultsEntry
	46, // 23: interactive_game.PendingRoomNotiyReq.players:type_name -> interactive_game.PendingRoomNotiyReq.Player
	53, // 24: interactive_game.QueryGameConfigRsp.ModeConfig.makeUpConfig:type_name -> interactive_game.common.GameRoomMakeUpConfig
	33, // 25: interactive_game.QueryGameConfigRsp.ModeConfigs.configs:type_name -> interactive_game.QueryGameConfigRsp.ModeConfig
	34, // 26: interactive_game.QueryGameConfigRsp.ModesEntry.value:type_name -> interactive_game.QueryGameConfigRsp.ModeConfigs
	35, // 27: interactive_game.QueryGameConfigRsp.AppsEntry.value:type_name -> interactive_game.QueryGameConfigRsp.AppConfig
	54, // 28: interactive_game.QueryRoomRsp.RoomConfig.joinMode:type_name -> interactive_game.common.JoinMode
	52, // 29: interactive_game.QueryRoomRsp.RoomConfig.payConfig:type_name -> interactive_game.common.GameRoomPayConfig
	53, // 30: interactive_game.QueryRoomRsp.RoomConfig.makeUpConfig:type_name -> interactive_game.common.GameRoomMakeUpConfig
	51, // 31: interactive_game.QueryRoomRsp.Room.status:type_name -> interactive_game.common.RoomStatus
	39, // 32: interactive_game.QueryRoomRsp.Room.players:type_name -> interactive_game.QueryRoomRsp.Player
	49, // 33: interactive_game.QueryRoomRsp.Room.platformInfo:type_name -> interactive_game.common.PlatformInfo
	38, // 34: interactive_game.QueryRoomRsp.Room.config:type_name -> interactive_game.QueryRoomRsp.RoomConfig
	47, // 35: interactive_game.QueryRoomRsp.Room.roomType:type_name -> interactive_game.common.RoomType
	40, // 36: interactive_game.QueryRoomRsp.RoomsEntry.value:type_name -> interactive_game.QueryRoomRsp.Room
	44, // 37: interactive_game.CheckPayConfigRsp.ResultsEntry.value:type_name -> interactive_game.CheckPayConfigRsp.Result
	1,  // 38: interactive_game.RoomAdmin.CreateRoom:input_type -> interactive_game.CreateRoomReq
	3,  // 39: interactive_game.RoomAdmin.DestroyRoom:input_type -> interactive_game.DestroyRoomReq
	5,  // 40: interactive_game.RoomAdmin.QueryGameConfig:input_type -> interactive_game.QueryGameConfigReq
	7,  // 41: interactive_game.RoomAdmin.QueryRoom:input_type -> interactive_game.QueryRoomReq
	9,  // 42: interactive_game.RoomAdmin.LeaveRoom:input_type -> interactive_game.LeaveRoomReq
	13, // 43: interactive_game.RoomAdmin.CreateRoomCheck:input_type -> interactive_game.CreateRoomCheckReq
	15, // 44: interactive_game.RoomAdmin.ReloadRoom:input_type -> interactive_game.ReloadRoomReq
	17, // 45: interactive_game.RoomAdmin.PreJoinRoom:input_type -> interactive_game.PreJoinRoomReq
	19, // 46: interactive_game.RoomAdmin.CheckFree:input_type -> interactive_game.CheckFreeReq
	21, // 47: interactive_game.RoomAdmin.CheckBalance:input_type -> interactive_game.CheckBalanceReq
	23, // 48: interactive_game.RoomAdmin.AddPlayers:input_type -> interactive_game.AddPlayersReq
	25, // 49: interactive_game.RoomAdmin.CheckPayConfig:input_type -> interactive_game.CheckPayConfigReq
	2,  // 50: interactive_game.RoomAdmin.CreateRoom:output_type -> interactive_game.CreateRoomRsp
	4,  // 51: interactive_game.RoomAdmin.DestroyRoom:output_type -> interactive_game.DestroyRoomRsp
	6,  // 52: interactive_game.RoomAdmin.QueryGameConfig:output_type -> interactive_game.QueryGameConfigRsp
	8,  // 53: interactive_game.RoomAdmin.QueryRoom:output_type -> interactive_game.QueryRoomRsp
	10, // 54: interactive_game.RoomAdmin.LeaveRoom:output_type -> interactive_game.LeaveRoomRsp
	14, // 55: interactive_game.RoomAdmin.CreateRoomCheck:output_type -> interactive_game.CreateRoomCheckRsp
	16, // 56: interactive_game.RoomAdmin.ReloadRoom:output_type -> interactive_game.ReloadRoomRsp
	18, // 57: interactive_game.RoomAdmin.PreJoinRoom:output_type -> interactive_game.PreJoinRoomRsp
	20, // 58: interactive_game.RoomAdmin.CheckFree:output_type -> interactive_game.CheckFreeRsp
	22, // 59: interactive_game.RoomAdmin.CheckBalance:output_type -> interactive_game.CheckBalanceRsp
	24, // 60: interactive_game.RoomAdmin.AddPlayers:output_type -> interactive_game.AddPlayersRsp
	26, // 61: interactive_game.RoomAdmin.CheckPayConfig:output_type -> interactive_game.CheckPayConfigRsp
	50, // [50:62] is the sub-list for method output_type
	38, // [38:50] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_room_admin_room_admin_proto_init() }
func file_pb_interactive_game_room_admin_room_admin_proto_init() {
	if File_pb_interactive_game_room_admin_room_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DestroyRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DestroyRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoomStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoomStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoomCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoomCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreJoinRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreJoinRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckFreeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckFreeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPlayersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPlayersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPayConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPayConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PendingRoomNotiyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PendingRoomNotiyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoomReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameConfigRsp_ModeConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameConfigRsp_ModeConfigs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGameConfigRsp_AppConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomRsp_RoomConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomRsp_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRoomRsp_Room); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoomStatusReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPlayersReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckPayConfigRsp_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_admin_room_admin_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PendingRoomNotiyReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_room_admin_room_admin_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_room_admin_room_admin_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_room_admin_room_admin_proto_depIdxs,
		EnumInfos:         file_pb_interactive_game_room_admin_room_admin_proto_enumTypes,
		MessageInfos:      file_pb_interactive_game_room_admin_room_admin_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_room_admin_room_admin_proto = out.File
	file_pb_interactive_game_room_admin_room_admin_proto_rawDesc = nil
	file_pb_interactive_game_room_admin_room_admin_proto_goTypes = nil
	file_pb_interactive_game_room_admin_room_admin_proto_depIdxs = nil
}
