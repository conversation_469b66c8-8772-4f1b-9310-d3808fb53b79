{"swagger": "2.0", "info": {"title": "pb/interactive_game/room_admin/room_admin.proto", "version": "version not set"}, "tags": [{"name": "RoomAdmin"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.RoomAdmin/AddPlayers": {"post": {"summary": "增加玩家 (只支持免费模式)", "operationId": "RoomAdmin_AddPlayers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameAddPlayersRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameAddPlayersReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/CheckBalance": {"post": {"summary": "校验余额", "operationId": "RoomAdmin_CheckBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCheckBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCheckBalanceReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/CheckFree": {"post": {"summary": "校验免费次数", "operationId": "RoomAdmin_CheckFree", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCheckFreeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCheckFreeReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/CheckPayConfig": {"post": {"summary": "校验付费模式", "operationId": "RoomAdmin_CheckPayConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCheckPayConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCheckPayConfigReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/CreateRoom": {"post": {"summary": "创建房间", "operationId": "RoomAdmin_CreateRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCreateRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCreateRoomReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/CreateRoomCheck": {"post": {"summary": "创建房间校验", "operationId": "RoomAdmin_CreateRoomCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCreateRoomCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCreateRoomCheckReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/DestroyRoom": {"post": {"summary": "销毁房间", "operationId": "RoomAdmin_DestroyRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameDestroyRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameDestroyRoomReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/LeaveRoom": {"post": {"summary": "退房", "operationId": "RoomAdmin_LeaveRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameLeaveRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameLeaveRoomReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/PreJoinRoom": {"post": {"summary": "预加入房间", "operationId": "RoomAdmin_PreJoinRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePreJoinRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePreJoinRoomReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/QueryGameConfig": {"post": {"summary": "查询游戏配置", "operationId": "RoomAdmin_QueryGameConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameQueryGameConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameQueryGameConfigReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/QueryRoom": {"post": {"summary": "查询游戏房间", "operationId": "RoomAdmin_QueryRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameQueryRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameQueryRoomReq"}}], "tags": ["RoomAdmin"]}}, "/interactive_game.RoomAdmin/ReloadRoom": {"post": {"summary": "重新加载房间", "operationId": "RoomAdmin_ReloadRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameReloadRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameReloadRoomReq"}}], "tags": ["RoomAdmin"]}}}, "definitions": {"CheckPayConfigRspResult": {"type": "object", "properties": {"shortfall": {"type": "string", "format": "int64"}, "useFree": {"type": "boolean"}, "useAlternate": {"type": "boolean"}, "alternateEnough": {"type": "boolean"}, "freeTimes": {"type": "string", "format": "int64"}}}, "KgInfoLiveMikeMode": {"type": "string", "enum": ["LiveMikeModeUnknown", "LiveMikeModeVideo", "LiveMikeModeAudio", "LiveMikeModePopup"], "default": "LiveMikeModeUnknown", "title": "- LiveMikeModeVideo: 视频上麦\n - LiveMikeModeAudio: 音频上麦\n - LiveMikeModePopup: 弹窗上麦"}, "QueryGameConfigRspAppConfig": {"type": "object", "properties": {"useFreePayModeIds": {"type": "array", "items": {"type": "string"}}, "priorityPayModeId": {"type": "array", "items": {"type": "string"}}}}, "QueryGameConfigRspModeConfig": {"type": "object", "properties": {"modeId": {"type": "string"}, "name": {"type": "string", "title": "名称"}, "makeUpConfig": {"$ref": "#/definitions/commonGameRoomMakeUpConfig", "title": "组局配置"}, "assetId": {"type": "string", "format": "int64"}, "directIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "定向id"}}}, "QueryGameConfigRspModeConfigs": {"type": "object", "properties": {"configs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QueryGameConfigRspModeConfig"}}}}, "QueryRoomRspRoom": {"type": "object", "properties": {"status": {"$ref": "#/definitions/commonRoomStatus"}, "gameAppId": {"type": "string"}, "ownerId": {"type": "string", "format": "uint64"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameQueryRoomRspPlayer"}}, "roundId": {"type": "string"}, "platformInfo": {"$ref": "#/definitions/commonPlatformInfo"}, "config": {"$ref": "#/definitions/QueryRoomRspRoomConfig"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType"}, "updateTime": {"type": "string", "format": "int64", "title": "最后更新时间"}, "matchTime": {"type": "string", "format": "int64"}}}, "QueryRoomRspRoomConfig": {"type": "object", "properties": {"modeId": {"type": "string"}, "joinMode": {"$ref": "#/definitions/commonJoinMode"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig"}, "makeUpConfig": {"$ref": "#/definitions/commonGameRoomMakeUpConfig"}}}, "commonCreateRoomConfig": {"type": "object", "properties": {"modeId": {"type": "string", "title": "模式 id (关联组局配置)"}, "joinMode": {"$ref": "#/definitions/commonJoinMode", "title": "加入模式"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig", "title": "付费配置"}, "autoBegin": {"type": "boolean", "title": "人满自动开"}, "allowFirstBegin": {"type": "boolean", "title": "允许第一个加入的人开始游戏"}, "ownerJoin": {"type": "boolean", "title": "房主必须参与游戏"}, "disableOwnerAutoJoin": {"type": "boolean", "title": "关闭房主自动参与"}, "withManager": {"type": "boolean", "title": "管理员房间"}}}, "commonGameRoomMakeUpConfig": {"type": "object", "properties": {"minPlayers": {"type": "integer", "format": "int64", "title": "最少开始人数"}, "maxPlayers": {"type": "integer", "format": "int64", "title": "最大加入人数"}, "groupNum": {"type": "integer", "format": "int64", "description": "队伍人数", "title": "BeginPrivilege beginPrivilege = 3;\nbool ownerJoin = 4; // 房主必须参与游戏"}, "gameConfigs": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传配置"}}}, "commonGameRoomPayConfig": {"type": "object", "properties": {"mode": {"$ref": "#/definitions/commonPayMode"}, "assetId": {"type": "string", "format": "int64", "title": "扣费资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "扣费资产数量"}, "payModeName": {"type": "string", "title": "扣费模式名称"}, "payModeId": {"type": "string", "title": "扣费模式 id"}}}, "commonJoinMode": {"type": "string", "enum": ["JoinUnlimited", "Join<PERSON><PERSON>"], "default": "JoinUnlimited", "title": "- JoinUnlimited: 自由落座\n - JoinPicked: 主播选择"}, "commonKgInfo": {"type": "object", "properties": {"roomType": {"$ref": "#/definitions/commonKgInfoRoomType"}, "gameId": {"type": "string", "title": "string payConfig = 2;"}, "liveMikeMode": {"$ref": "#/definitions/KgInfoLiveMikeMode"}}}, "commonKgInfoRoomType": {"type": "string", "enum": ["RoomTypeUnknown", "RoomTypeLive", "RoomTypeSocialKtv", "RoomTypeSingleMikeKtv"], "default": "RoomTypeUnknown", "title": "- RoomTypeLive: 直播\n - RoomTypeSocialKtv: 欢聚\n - RoomTypeSingleMikeKtv: 单麦"}, "commonPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "commonPlatformInfo": {"type": "object", "properties": {"platId": {"type": "string", "format": "uint64", "title": "EPlatID"}, "kg": {"$ref": "#/definitions/commonKgInfo"}}}, "commonRoomStatus": {"type": "string", "enum": ["RoomNone", "RoomPending", "RoomPlaying"], "default": "RoomNone", "title": "- RoomPending: 组局中\n - RoomPlaying: 游戏中"}, "interactive_gameAddPlayersReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameAddPlayersReqPlayer"}}}}, "interactive_gameAddPlayersReqPlayer": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "robot": {"type": "boolean"}, "index": {"type": "string", "format": "int64", "title": "座位号 0 自动选择"}}}, "interactive_gameAddPlayersRsp": {"type": "object", "properties": {"uids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "成功加入的 uid"}}}, "interactive_gameCheckBalanceReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "房主 uid"}, "gameAppId": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig", "title": "付费配置"}}}, "interactive_gameCheckBalanceRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "payDifference": {"type": "string", "format": "int64"}}}, "interactive_gameCheckFreeReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "房主 uid"}, "gameAppId": {"type": "string"}, "payModeId": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig"}}}, "interactive_gameCheckFreeRsp": {"type": "object", "properties": {"balance": {"type": "string", "format": "int64"}}}, "interactive_gameCheckPayConfigReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "gameAppId": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}, "payConfigs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonGameRoomPayConfig"}}}}, "interactive_gameCheckPayConfigRsp": {"type": "object", "properties": {"results": {"type": "object", "additionalProperties": {"$ref": "#/definitions/CheckPayConfigRspResult"}, "title": "payModeId -> result"}}}, "interactive_gameCreateRoomCheckReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间 id"}, "ownerId": {"type": "string", "format": "uint64", "title": "房主 uid"}, "gameAppId": {"type": "string", "title": "游戏 appId"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}, "config": {"$ref": "#/definitions/commonCreateRoomConfig"}, "creatorId": {"type": "string", "format": "uint64", "description": "创建者 uid", "title": "common.GameRoomConfig config = 5;"}}}, "interactive_gameCreateRoomCheckRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "balance": {"type": "integer", "format": "int64"}}}, "interactive_gameCreateRoomReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间 id"}, "ownerId": {"type": "string", "format": "uint64", "title": "房主 uid"}, "gameAppId": {"type": "string", "title": "游戏 appId"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}, "config": {"$ref": "#/definitions/commonCreateRoomConfig"}, "reportExtend": {"type": "object", "additionalProperties": {"type": "string"}, "description": "上报扩展字段", "title": "common.GameRoomConfig config = 5;"}, "creatorId": {"type": "string", "format": "uint64", "title": "创建者 uid"}, "gameExtend": {"type": "object", "additionalProperties": {"type": "string"}, "title": "游戏扩展字段"}, "platformInfo": {"$ref": "#/definitions/commonPlatformInfo", "title": "平台信息"}, "createGameConfigs": {"type": "object", "additionalProperties": {"type": "string"}, "description": "创建游戏透传配置", "title": "string mergeTaskId = 10;\nbool match = 11;\nint32 createSource = 12; // 平台来源"}, "createOnlyNoExists": {"type": "boolean", "description": "仅不存在时创建", "title": "repeated Player players = 14; // 默认玩家"}, "roomTitle": {"type": "string"}, "ignoreInsufficientBalance": {"type": "boolean"}}}, "interactive_gameCreateRoomRsp": {"type": "object", "properties": {"gameUrl": {"type": "string", "title": "游戏链接"}, "gameRoomId": {"type": "string"}, "gameAppId": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "modeName": {"type": "string"}, "payDifference": {"type": "string", "format": "int64"}}}, "interactive_gameDestroyRoomReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "graceful": {"type": "boolean"}}}, "interactive_gameDestroyRoomRsp": {"type": "object", "properties": {"gameRoomId": {"type": "string"}}}, "interactive_gameLeaveRoomReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间 id"}, "uid": {"type": "string", "format": "uint64", "title": "uid"}}}, "interactive_gameLeaveRoomRsp": {"type": "object"}, "interactive_gamePreJoinRoomReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}}}, "interactive_gamePreJoinRoomRsp": {"type": "object", "properties": {"status": {"$ref": "#/definitions/commonRoomStatus"}, "modeId": {"type": "string"}, "payModeId": {"type": "string"}, "gameAppId": {"type": "string"}}}, "interactive_gameQueryGameConfigReq": {"type": "object", "properties": {"gameAppIds": {"type": "array", "items": {"type": "string"}, "title": "游戏 appId"}, "queryAll": {"type": "boolean", "title": "查询所有"}}}, "interactive_gameQueryGameConfigRsp": {"type": "object", "properties": {"modes": {"type": "object", "additionalProperties": {"$ref": "#/definitions/QueryGameConfigRspModeConfigs"}, "title": "游戏 appId -> 模式配置"}, "apps": {"type": "object", "additionalProperties": {"$ref": "#/definitions/QueryGameConfigRspAppConfig"}, "title": "游戏 appId -> 游戏配置"}}}, "interactive_gameQueryRoomReq": {"type": "object", "properties": {"roomIds": {"type": "array", "items": {"type": "string"}, "title": "最多 50 个"}}}, "interactive_gameQueryRoomRsp": {"type": "object", "properties": {"rooms": {"type": "object", "additionalProperties": {"$ref": "#/definitions/QueryRoomRspRoom"}, "title": "roomId -> room"}}}, "interactive_gameQueryRoomRspPlayer": {"type": "object", "properties": {"index": {"type": "integer", "format": "int64", "title": "座位号"}, "uid": {"type": "string", "format": "uint64"}}}, "interactive_gameReloadRoomReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameReloadRoomRsp": {"type": "object", "properties": {"gameUrl": {"type": "string", "title": "游戏链接"}, "gameRoomId": {"type": "string"}, "gameAppId": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "modeName": {"type": "string"}}}, "interactive_gamecommonRoomType": {"type": "string", "enum": ["TypeNone", "TypeKtv", "TypeTwins", "TypeLive", "TypeMatch"], "default": "TypeNone", "title": "- TypeKtv: 歌房\n - TypeTwins: 双人房\n - TypeLive: 直播\n - TypeMatch: 匹配房"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}