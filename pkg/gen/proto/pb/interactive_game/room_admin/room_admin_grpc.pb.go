// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/room_admin/room_admin.proto

package interactive_game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	RoomAdmin_CreateRoom_FullMethodName      = "/interactive_game.RoomAdmin/CreateRoom"
	RoomAdmin_DestroyRoom_FullMethodName     = "/interactive_game.RoomAdmin/DestroyRoom"
	RoomAdmin_QueryGameConfig_FullMethodName = "/interactive_game.RoomAdmin/QueryGameConfig"
	RoomAdmin_QueryRoom_FullMethodName       = "/interactive_game.RoomAdmin/QueryRoom"
	RoomAdmin_LeaveRoom_FullMethodName       = "/interactive_game.RoomAdmin/LeaveRoom"
	RoomAdmin_CreateRoomCheck_FullMethodName = "/interactive_game.RoomAdmin/CreateRoomCheck"
	RoomAdmin_ReloadRoom_FullMethodName      = "/interactive_game.RoomAdmin/ReloadRoom"
	RoomAdmin_PreJoinRoom_FullMethodName     = "/interactive_game.RoomAdmin/PreJoinRoom"
	RoomAdmin_CheckFree_FullMethodName       = "/interactive_game.RoomAdmin/CheckFree"
	RoomAdmin_CheckBalance_FullMethodName    = "/interactive_game.RoomAdmin/CheckBalance"
	RoomAdmin_AddPlayers_FullMethodName      = "/interactive_game.RoomAdmin/AddPlayers"
	RoomAdmin_CheckPayConfig_FullMethodName  = "/interactive_game.RoomAdmin/CheckPayConfig"
)

// RoomAdminClient is the client API for RoomAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoomAdminClient interface {
	// 创建房间
	CreateRoom(ctx context.Context, in *CreateRoomReq, opts ...grpc.CallOption) (*CreateRoomRsp, error)
	// 销毁房间
	DestroyRoom(ctx context.Context, in *DestroyRoomReq, opts ...grpc.CallOption) (*DestroyRoomRsp, error)
	// 查询游戏配置
	QueryGameConfig(ctx context.Context, in *QueryGameConfigReq, opts ...grpc.CallOption) (*QueryGameConfigRsp, error)
	// 查询游戏房间
	QueryRoom(ctx context.Context, in *QueryRoomReq, opts ...grpc.CallOption) (*QueryRoomRsp, error)
	// 退房
	LeaveRoom(ctx context.Context, in *LeaveRoomReq, opts ...grpc.CallOption) (*LeaveRoomRsp, error)
	// 创建房间校验
	CreateRoomCheck(ctx context.Context, in *CreateRoomCheckReq, opts ...grpc.CallOption) (*CreateRoomCheckRsp, error)
	// 重新加载房间
	ReloadRoom(ctx context.Context, in *ReloadRoomReq, opts ...grpc.CallOption) (*ReloadRoomRsp, error)
	// 预加入房间
	PreJoinRoom(ctx context.Context, in *PreJoinRoomReq, opts ...grpc.CallOption) (*PreJoinRoomRsp, error)
	// 校验免费次数
	CheckFree(ctx context.Context, in *CheckFreeReq, opts ...grpc.CallOption) (*CheckFreeRsp, error)
	// 校验余额
	CheckBalance(ctx context.Context, in *CheckBalanceReq, opts ...grpc.CallOption) (*CheckBalanceRsp, error)
	// 增加玩家 (只支持免费模式)
	AddPlayers(ctx context.Context, in *AddPlayersReq, opts ...grpc.CallOption) (*AddPlayersRsp, error)
	// 校验付费模式
	CheckPayConfig(ctx context.Context, in *CheckPayConfigReq, opts ...grpc.CallOption) (*CheckPayConfigRsp, error)
}

type roomAdminClient struct {
	cc grpc.ClientConnInterface
}

func NewRoomAdminClient(cc grpc.ClientConnInterface) RoomAdminClient {
	return &roomAdminClient{cc}
}

func (c *roomAdminClient) CreateRoom(ctx context.Context, in *CreateRoomReq, opts ...grpc.CallOption) (*CreateRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRoomRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_CreateRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) DestroyRoom(ctx context.Context, in *DestroyRoomReq, opts ...grpc.CallOption) (*DestroyRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DestroyRoomRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_DestroyRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) QueryGameConfig(ctx context.Context, in *QueryGameConfigReq, opts ...grpc.CallOption) (*QueryGameConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryGameConfigRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_QueryGameConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) QueryRoom(ctx context.Context, in *QueryRoomReq, opts ...grpc.CallOption) (*QueryRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRoomRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_QueryRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) LeaveRoom(ctx context.Context, in *LeaveRoomReq, opts ...grpc.CallOption) (*LeaveRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeaveRoomRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_LeaveRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) CreateRoomCheck(ctx context.Context, in *CreateRoomCheckReq, opts ...grpc.CallOption) (*CreateRoomCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRoomCheckRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_CreateRoomCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) ReloadRoom(ctx context.Context, in *ReloadRoomReq, opts ...grpc.CallOption) (*ReloadRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReloadRoomRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_ReloadRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) PreJoinRoom(ctx context.Context, in *PreJoinRoomReq, opts ...grpc.CallOption) (*PreJoinRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PreJoinRoomRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_PreJoinRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) CheckFree(ctx context.Context, in *CheckFreeReq, opts ...grpc.CallOption) (*CheckFreeRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckFreeRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_CheckFree_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) CheckBalance(ctx context.Context, in *CheckBalanceReq, opts ...grpc.CallOption) (*CheckBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckBalanceRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_CheckBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) AddPlayers(ctx context.Context, in *AddPlayersReq, opts ...grpc.CallOption) (*AddPlayersRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddPlayersRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_AddPlayers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomAdminClient) CheckPayConfig(ctx context.Context, in *CheckPayConfigReq, opts ...grpc.CallOption) (*CheckPayConfigRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckPayConfigRsp)
	err := c.cc.Invoke(ctx, RoomAdmin_CheckPayConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoomAdminServer is the server API for RoomAdmin service.
// All implementations should embed UnimplementedRoomAdminServer
// for forward compatibility
type RoomAdminServer interface {
	// 创建房间
	CreateRoom(context.Context, *CreateRoomReq) (*CreateRoomRsp, error)
	// 销毁房间
	DestroyRoom(context.Context, *DestroyRoomReq) (*DestroyRoomRsp, error)
	// 查询游戏配置
	QueryGameConfig(context.Context, *QueryGameConfigReq) (*QueryGameConfigRsp, error)
	// 查询游戏房间
	QueryRoom(context.Context, *QueryRoomReq) (*QueryRoomRsp, error)
	// 退房
	LeaveRoom(context.Context, *LeaveRoomReq) (*LeaveRoomRsp, error)
	// 创建房间校验
	CreateRoomCheck(context.Context, *CreateRoomCheckReq) (*CreateRoomCheckRsp, error)
	// 重新加载房间
	ReloadRoom(context.Context, *ReloadRoomReq) (*ReloadRoomRsp, error)
	// 预加入房间
	PreJoinRoom(context.Context, *PreJoinRoomReq) (*PreJoinRoomRsp, error)
	// 校验免费次数
	CheckFree(context.Context, *CheckFreeReq) (*CheckFreeRsp, error)
	// 校验余额
	CheckBalance(context.Context, *CheckBalanceReq) (*CheckBalanceRsp, error)
	// 增加玩家 (只支持免费模式)
	AddPlayers(context.Context, *AddPlayersReq) (*AddPlayersRsp, error)
	// 校验付费模式
	CheckPayConfig(context.Context, *CheckPayConfigReq) (*CheckPayConfigRsp, error)
}

// UnimplementedRoomAdminServer should be embedded to have forward compatible implementations.
type UnimplementedRoomAdminServer struct {
}

func (UnimplementedRoomAdminServer) CreateRoom(context.Context, *CreateRoomReq) (*CreateRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRoom not implemented")
}
func (UnimplementedRoomAdminServer) DestroyRoom(context.Context, *DestroyRoomReq) (*DestroyRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DestroyRoom not implemented")
}
func (UnimplementedRoomAdminServer) QueryGameConfig(context.Context, *QueryGameConfigReq) (*QueryGameConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryGameConfig not implemented")
}
func (UnimplementedRoomAdminServer) QueryRoom(context.Context, *QueryRoomReq) (*QueryRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRoom not implemented")
}
func (UnimplementedRoomAdminServer) LeaveRoom(context.Context, *LeaveRoomReq) (*LeaveRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LeaveRoom not implemented")
}
func (UnimplementedRoomAdminServer) CreateRoomCheck(context.Context, *CreateRoomCheckReq) (*CreateRoomCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRoomCheck not implemented")
}
func (UnimplementedRoomAdminServer) ReloadRoom(context.Context, *ReloadRoomReq) (*ReloadRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReloadRoom not implemented")
}
func (UnimplementedRoomAdminServer) PreJoinRoom(context.Context, *PreJoinRoomReq) (*PreJoinRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreJoinRoom not implemented")
}
func (UnimplementedRoomAdminServer) CheckFree(context.Context, *CheckFreeReq) (*CheckFreeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckFree not implemented")
}
func (UnimplementedRoomAdminServer) CheckBalance(context.Context, *CheckBalanceReq) (*CheckBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBalance not implemented")
}
func (UnimplementedRoomAdminServer) AddPlayers(context.Context, *AddPlayersReq) (*AddPlayersRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPlayers not implemented")
}
func (UnimplementedRoomAdminServer) CheckPayConfig(context.Context, *CheckPayConfigReq) (*CheckPayConfigRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPayConfig not implemented")
}

// UnsafeRoomAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoomAdminServer will
// result in compilation errors.
type UnsafeRoomAdminServer interface {
	mustEmbedUnimplementedRoomAdminServer()
}

func RegisterRoomAdminServer(s grpc.ServiceRegistrar, srv RoomAdminServer) {
	s.RegisterService(&RoomAdmin_ServiceDesc, srv)
}

func _RoomAdmin_CreateRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).CreateRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_CreateRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).CreateRoom(ctx, req.(*CreateRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_DestroyRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DestroyRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).DestroyRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_DestroyRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).DestroyRoom(ctx, req.(*DestroyRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_QueryGameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryGameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).QueryGameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_QueryGameConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).QueryGameConfig(ctx, req.(*QueryGameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_QueryRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).QueryRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_QueryRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).QueryRoom(ctx, req.(*QueryRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_LeaveRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).LeaveRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_LeaveRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).LeaveRoom(ctx, req.(*LeaveRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_CreateRoomCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoomCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).CreateRoomCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_CreateRoomCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).CreateRoomCheck(ctx, req.(*CreateRoomCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_ReloadRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReloadRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).ReloadRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_ReloadRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).ReloadRoom(ctx, req.(*ReloadRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_PreJoinRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreJoinRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).PreJoinRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_PreJoinRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).PreJoinRoom(ctx, req.(*PreJoinRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_CheckFree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFreeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).CheckFree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_CheckFree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).CheckFree(ctx, req.(*CheckFreeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_CheckBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).CheckBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_CheckBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).CheckBalance(ctx, req.(*CheckBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_AddPlayers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlayersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).AddPlayers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_AddPlayers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).AddPlayers(ctx, req.(*AddPlayersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomAdmin_CheckPayConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPayConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomAdminServer).CheckPayConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomAdmin_CheckPayConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomAdminServer).CheckPayConfig(ctx, req.(*CheckPayConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RoomAdmin_ServiceDesc is the grpc.ServiceDesc for RoomAdmin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RoomAdmin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.RoomAdmin",
	HandlerType: (*RoomAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateRoom",
			Handler:    _RoomAdmin_CreateRoom_Handler,
		},
		{
			MethodName: "DestroyRoom",
			Handler:    _RoomAdmin_DestroyRoom_Handler,
		},
		{
			MethodName: "QueryGameConfig",
			Handler:    _RoomAdmin_QueryGameConfig_Handler,
		},
		{
			MethodName: "QueryRoom",
			Handler:    _RoomAdmin_QueryRoom_Handler,
		},
		{
			MethodName: "LeaveRoom",
			Handler:    _RoomAdmin_LeaveRoom_Handler,
		},
		{
			MethodName: "CreateRoomCheck",
			Handler:    _RoomAdmin_CreateRoomCheck_Handler,
		},
		{
			MethodName: "ReloadRoom",
			Handler:    _RoomAdmin_ReloadRoom_Handler,
		},
		{
			MethodName: "PreJoinRoom",
			Handler:    _RoomAdmin_PreJoinRoom_Handler,
		},
		{
			MethodName: "CheckFree",
			Handler:    _RoomAdmin_CheckFree_Handler,
		},
		{
			MethodName: "CheckBalance",
			Handler:    _RoomAdmin_CheckBalance_Handler,
		},
		{
			MethodName: "AddPlayers",
			Handler:    _RoomAdmin_AddPlayers_Handler,
		},
		{
			MethodName: "CheckPayConfig",
			Handler:    _RoomAdmin_CheckPayConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/room_admin/room_admin.proto",
}
