// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/settlement/settlement.proto

package interactive_game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SettlementMode int32

const (
	SettlementMode_SettlementSingle SettlementMode = 0
	SettlementMode_SettlementTeam   SettlementMode = 1 // 组队
)

// Enum value maps for SettlementMode.
var (
	SettlementMode_name = map[int32]string{
		0: "SettlementSingle",
		1: "SettlementTeam",
	}
	SettlementMode_value = map[string]int32{
		"SettlementSingle": 0,
		"SettlementTeam":   1,
	}
)

func (x SettlementMode) Enum() *SettlementMode {
	p := new(SettlementMode)
	*p = x
	return p
}

func (x SettlementMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SettlementMode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_settlement_settlement_proto_enumTypes[0].Descriptor()
}

func (SettlementMode) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_settlement_settlement_proto_enumTypes[0]
}

func (x SettlementMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SettlementMode.Descriptor instead.
func (SettlementMode) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{0}
}

type SettlementReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId          string                  `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`                                           // appId
	RoomId         string                  `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`                                         // 房间 id
	RoundId        string                  `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"`                                       // 场次 id
	Results        []*SettlementReq_Result `protobuf:"bytes,4,rep,name=results,proto3" json:"results,omitempty"`                                       // 各玩家游戏结果
	PayMode        common.PayMode          `protobuf:"varint,5,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"` // 0免费场/1货币场/2鲜花场
	Pool           int64                   `protobuf:"varint,6,opt,name=pool,proto3" json:"pool,omitempty"`                                            // 总奖池
	Ts             int64                   `protobuf:"varint,7,opt,name=ts,proto3" json:"ts,omitempty"`                                                // 游戏开始时间戳
	SettlementMode SettlementMode          `protobuf:"varint,8,opt,name=settlementMode,proto3,enum=interactive_game.SettlementMode" json:"settlementMode,omitempty"`
	PlayerNum      uint32                  `protobuf:"varint,9,opt,name=playerNum,proto3" json:"playerNum,omitempty"`      // 最大玩家数
	Players        []*SettlementReq_Player `protobuf:"bytes,10,rep,name=players,proto3" json:"players,omitempty"`          // 对局玩家列表
	PayAssetNum    int64                   `protobuf:"varint,11,opt,name=payAssetNum,proto3" json:"payAssetNum,omitempty"` // 扣费资产数量，对应字段 payMode
	GameEndTime    int64                   `protobuf:"varint,12,opt,name=gameEndTime,proto3" json:"gameEndTime,omitempty"` // 游戏结束时间戳
}

func (x *SettlementReq) Reset() {
	*x = SettlementReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementReq) ProtoMessage() {}

func (x *SettlementReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementReq.ProtoReflect.Descriptor instead.
func (*SettlementReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{0}
}

func (x *SettlementReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SettlementReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SettlementReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *SettlementReq) GetResults() []*SettlementReq_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *SettlementReq) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

func (x *SettlementReq) GetPool() int64 {
	if x != nil {
		return x.Pool
	}
	return 0
}

func (x *SettlementReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *SettlementReq) GetSettlementMode() SettlementMode {
	if x != nil {
		return x.SettlementMode
	}
	return SettlementMode_SettlementSingle
}

func (x *SettlementReq) GetPlayerNum() uint32 {
	if x != nil {
		return x.PlayerNum
	}
	return 0
}

func (x *SettlementReq) GetPlayers() []*SettlementReq_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *SettlementReq) GetPayAssetNum() int64 {
	if x != nil {
		return x.PayAssetNum
	}
	return 0
}

func (x *SettlementReq) GetGameEndTime() int64 {
	if x != nil {
		return x.GameEndTime
	}
	return 0
}

type SettlementRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*SettlementRsp_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *SettlementRsp) Reset() {
	*x = SettlementRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementRsp) ProtoMessage() {}

func (x *SettlementRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementRsp.ProtoReflect.Descriptor instead.
func (*SettlementRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{1}
}

func (x *SettlementRsp) GetResults() []*SettlementRsp_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

// 用户游戏结算数据缓存
// key为 sett:res:{appid}:{roundId}
type SettlementResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId         string                     `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`                                         // 房间 id
	Results        []*SettlementResult_Result `protobuf:"bytes,2,rep,name=results,proto3" json:"results,omitempty"`                                       // 玩家游戏结果
	PayMode        common.PayMode             `protobuf:"varint,3,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"` // 0免费场/1货币场/2鲜花场
	Pool           int64                      `protobuf:"varint,4,opt,name=pool,proto3" json:"pool,omitempty"`                                            // 总奖池
	Ts             int64                      `protobuf:"varint,5,opt,name=ts,proto3" json:"ts,omitempty"`                                                // ts
	AssetIdOfPrize int32                      `protobuf:"varint,6,opt,name=assetIdOfPrize,proto3" json:"assetIdOfPrize,omitempty"`                        // 发奖的assetId
	AppId          string                     `protobuf:"bytes,7,opt,name=appId,proto3" json:"appId,omitempty"`                                           // appId
	RoundId        string                     `protobuf:"bytes,8,opt,name=roundId,proto3" json:"roundId,omitempty"`                                       // roundId
	SettlementMode SettlementMode             `protobuf:"varint,9,opt,name=settlementMode,proto3,enum=interactive_game.SettlementMode" json:"settlementMode,omitempty"`
	PlayerNum      uint32                     `protobuf:"varint,10,opt,name=playerNum,proto3" json:"playerNum,omitempty"` // 当前玩家数
}

func (x *SettlementResult) Reset() {
	*x = SettlementResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementResult) ProtoMessage() {}

func (x *SettlementResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementResult.ProtoReflect.Descriptor instead.
func (*SettlementResult) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{2}
}

func (x *SettlementResult) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SettlementResult) GetResults() []*SettlementResult_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *SettlementResult) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

func (x *SettlementResult) GetPool() int64 {
	if x != nil {
		return x.Pool
	}
	return 0
}

func (x *SettlementResult) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *SettlementResult) GetAssetIdOfPrize() int32 {
	if x != nil {
		return x.AssetIdOfPrize
	}
	return 0
}

func (x *SettlementResult) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SettlementResult) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *SettlementResult) GetSettlementMode() SettlementMode {
	if x != nil {
		return x.SettlementMode
	}
	return SettlementMode_SettlementSingle
}

func (x *SettlementResult) GetPlayerNum() uint32 {
	if x != nil {
		return x.PlayerNum
	}
	return 0
}

// 游戏结算配置的key
// key为 settlementconfig:{appid}
type SettlementConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 货币场配置
	AssetIdOfTicket  int32  `protobuf:"varint,1,opt,name=assetIdOfTicket,proto3" json:"assetIdOfTicket,omitempty"`   // 门票的assetId，对货币场使用
	AssetIdOfPrize   int32  `protobuf:"varint,2,opt,name=assetIdOfPrize,proto3" json:"assetIdOfPrize,omitempty"`     // 奖励的assetId，对货币场使用
	ParaPercentage   int32  `protobuf:"varint,3,opt,name=paraPercentage,proto3" json:"paraPercentage,omitempty"`     // 可以分奖池的人的百分比[0, 100]
	ParaQ            int32  `protobuf:"varint,4,opt,name=ParaQ,proto3" json:"ParaQ,omitempty"`                       // 等比数列的Q，不知道咋形容，详见https://tapd.woa.com/Singbar/prong/stories/view/1010088921877113059
	GameName         string `protobuf:"bytes,5,opt,name=gameName,proto3" json:"gameName,omitempty"`                  // 游戏名称
	JumpUrlOfFlower  string `protobuf:"bytes,6,opt,name=jumpUrlOfFlower,proto3" json:"jumpUrlOfFlower,omitempty"`    // 鲜花跳转链接
	JumpUrlOfAsset   string `protobuf:"bytes,7,opt,name=jumpUrlOfAsset,proto3" json:"jumpUrlOfAsset,omitempty"`      // 资产跳转链接
	RewardIdOfFlower int32  `protobuf:"varint,8,opt,name=rewardIdOfFlower,proto3" json:"rewardIdOfFlower,omitempty"` // 发鲜花的福利id
	AssetMail        string `protobuf:"bytes,9,opt,name=assetMail,proto3" json:"assetMail,omitempty"`
	FlowerMail       string `protobuf:"bytes,10,opt,name=flowerMail,proto3" json:"flowerMail,omitempty"`
	FlowerReason     string `protobuf:"bytes,11,opt,name=flowerReason,proto3" json:"flowerReason,omitempty"`
	TeamQ            int32  `protobuf:"varint,12,opt,name=teamQ,proto3" json:"teamQ,omitempty"`                   //  组队模式的Q
	TeamPercentage   int32  `protobuf:"varint,13,opt,name=teamPercentage,proto3" json:"teamPercentage,omitempty"` // 组队模式的可获奖队伍比例
}

func (x *SettlementConfig) Reset() {
	*x = SettlementConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementConfig) ProtoMessage() {}

func (x *SettlementConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementConfig.ProtoReflect.Descriptor instead.
func (*SettlementConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{3}
}

func (x *SettlementConfig) GetAssetIdOfTicket() int32 {
	if x != nil {
		return x.AssetIdOfTicket
	}
	return 0
}

func (x *SettlementConfig) GetAssetIdOfPrize() int32 {
	if x != nil {
		return x.AssetIdOfPrize
	}
	return 0
}

func (x *SettlementConfig) GetParaPercentage() int32 {
	if x != nil {
		return x.ParaPercentage
	}
	return 0
}

func (x *SettlementConfig) GetParaQ() int32 {
	if x != nil {
		return x.ParaQ
	}
	return 0
}

func (x *SettlementConfig) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

func (x *SettlementConfig) GetJumpUrlOfFlower() string {
	if x != nil {
		return x.JumpUrlOfFlower
	}
	return ""
}

func (x *SettlementConfig) GetJumpUrlOfAsset() string {
	if x != nil {
		return x.JumpUrlOfAsset
	}
	return ""
}

func (x *SettlementConfig) GetRewardIdOfFlower() int32 {
	if x != nil {
		return x.RewardIdOfFlower
	}
	return 0
}

func (x *SettlementConfig) GetAssetMail() string {
	if x != nil {
		return x.AssetMail
	}
	return ""
}

func (x *SettlementConfig) GetFlowerMail() string {
	if x != nil {
		return x.FlowerMail
	}
	return ""
}

func (x *SettlementConfig) GetFlowerReason() string {
	if x != nil {
		return x.FlowerReason
	}
	return ""
}

func (x *SettlementConfig) GetTeamQ() int32 {
	if x != nil {
		return x.TeamQ
	}
	return 0
}

func (x *SettlementConfig) GetTeamPercentage() int32 {
	if x != nil {
		return x.TeamPercentage
	}
	return 0
}

type SettlementReq_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Rank       uint32 `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`             // 排名 从 1 开始
	Experience int64  `protobuf:"varint,3,opt,name=experience,proto3" json:"experience,omitempty"` // 获得经验值
	Score      int64  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`           // 分数
	Seat       uint32 `protobuf:"varint,5,opt,name=seat,proto3" json:"seat,omitempty"`             // 座位号
	Extra      string `protobuf:"bytes,6,opt,name=extra,proto3" json:"extra,omitempty"`            // 额外透传数据
	Team       uint32 `protobuf:"varint,7,opt,name=team,proto3" json:"team,omitempty"`             // 组队模式下，相同的为队友
}

func (x *SettlementReq_Result) Reset() {
	*x = SettlementReq_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementReq_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementReq_Result) ProtoMessage() {}

func (x *SettlementReq_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementReq_Result.ProtoReflect.Descriptor instead.
func (*SettlementReq_Result) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SettlementReq_Result) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SettlementReq_Result) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *SettlementReq_Result) GetExperience() int64 {
	if x != nil {
		return x.Experience
	}
	return 0
}

func (x *SettlementReq_Result) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SettlementReq_Result) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *SettlementReq_Result) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *SettlementReq_Result) GetTeam() uint32 {
	if x != nil {
		return x.Team
	}
	return 0
}

type SettlementReq_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Team   uint32 `protobuf:"varint,2,opt,name=team,proto3" json:"team,omitempty"` // 组队模式下，相同的为队友
}

func (x *SettlementReq_Player) Reset() {
	*x = SettlementReq_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementReq_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementReq_Player) ProtoMessage() {}

func (x *SettlementReq_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementReq_Player.ProtoReflect.Descriptor instead.
func (*SettlementReq_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{0, 1}
}

func (x *SettlementReq_Player) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SettlementReq_Player) GetTeam() uint32 {
	if x != nil {
		return x.Team
	}
	return 0
}

type SettlementRsp_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Win    int64  `protobuf:"varint,2,opt,name=win,proto3" json:"win,omitempty"`
}

func (x *SettlementRsp_Result) Reset() {
	*x = SettlementRsp_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementRsp_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementRsp_Result) ProtoMessage() {}

func (x *SettlementRsp_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementRsp_Result.ProtoReflect.Descriptor instead.
func (*SettlementRsp_Result) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SettlementRsp_Result) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SettlementRsp_Result) GetWin() int64 {
	if x != nil {
		return x.Win
	}
	return 0
}

// 段位结算信息
type SettlementResult_SegmentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score       int64  `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`            // 当前获得积分(有正负数情况)
	TScore      int64  `protobuf:"varint,2,opt,name=tScore,proto3" json:"tScore,omitempty"`          // 总积分
	SegName     string `protobuf:"bytes,3,opt,name=segName,proto3" json:"segName,omitempty"`         // 段位名(例如:白银II)
	HasEvent    bool   `protobuf:"varint,4,opt,name=hasEvent,proto3" json:"hasEvent,omitempty"`      // 是否有段位事件
	SegIcon     string `protobuf:"bytes,5,opt,name=segIcon,proto3" json:"segIcon,omitempty"`         // 段位Icon
	AvatarFrame string `protobuf:"bytes,6,opt,name=avatarFrame,proto3" json:"avatarFrame,omitempty"` // 段位头像框
}

func (x *SettlementResult_SegmentResult) Reset() {
	*x = SettlementResult_SegmentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementResult_SegmentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementResult_SegmentResult) ProtoMessage() {}

func (x *SettlementResult_SegmentResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementResult_SegmentResult.ProtoReflect.Descriptor instead.
func (*SettlementResult_SegmentResult) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{2, 0}
}

func (x *SettlementResult_SegmentResult) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SettlementResult_SegmentResult) GetTScore() int64 {
	if x != nil {
		return x.TScore
	}
	return 0
}

func (x *SettlementResult_SegmentResult) GetSegName() string {
	if x != nil {
		return x.SegName
	}
	return ""
}

func (x *SettlementResult_SegmentResult) GetHasEvent() bool {
	if x != nil {
		return x.HasEvent
	}
	return false
}

func (x *SettlementResult_SegmentResult) GetSegIcon() string {
	if x != nil {
		return x.SegIcon
	}
	return ""
}

func (x *SettlementResult_SegmentResult) GetAvatarFrame() string {
	if x != nil {
		return x.AvatarFrame
	}
	return ""
}

type SettlementResult_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string                          `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Rank       uint32                          `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	Experience int64                           `protobuf:"varint,3,opt,name=experience,proto3" json:"experience,omitempty"`
	Score      int64                           `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"` // 得分
	Win        int64                           `protobuf:"varint,5,opt,name=win,proto3" json:"win,omitempty"`     // 赢得的货币/鲜花 存下来已经算好了
	Seat       uint32                          `protobuf:"varint,6,opt,name=seat,proto3" json:"seat,omitempty"`   // 座位号
	Extra      string                          `protobuf:"bytes,7,opt,name=extra,proto3" json:"extra,omitempty"`  // 额外透传数据
	Team       uint32                          `protobuf:"varint,8,opt,name=team,proto3" json:"team,omitempty"`
	Seg        *SettlementResult_SegmentResult `protobuf:"bytes,9,opt,name=seg,proto3" json:"seg,omitempty"` // 段位结算信息
}

func (x *SettlementResult_Result) Reset() {
	*x = SettlementResult_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettlementResult_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettlementResult_Result) ProtoMessage() {}

func (x *SettlementResult_Result) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_settlement_settlement_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettlementResult_Result.ProtoReflect.Descriptor instead.
func (*SettlementResult_Result) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP(), []int{2, 1}
}

func (x *SettlementResult_Result) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SettlementResult_Result) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *SettlementResult_Result) GetExperience() int64 {
	if x != nil {
		return x.Experience
	}
	return 0
}

func (x *SettlementResult_Result) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SettlementResult_Result) GetWin() int64 {
	if x != nil {
		return x.Win
	}
	return 0
}

func (x *SettlementResult_Result) GetSeat() uint32 {
	if x != nil {
		return x.Seat
	}
	return 0
}

func (x *SettlementResult_Result) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *SettlementResult_Result) GetTeam() uint32 {
	if x != nil {
		return x.Team
	}
	return 0
}

func (x *SettlementResult_Result) GetSeg() *SettlementResult_SegmentResult {
	if x != nil {
		return x.Seg
	}
	return nil
}

var File_pb_interactive_game_settlement_settlement_proto protoreflect.FileDescriptor

var file_pb_interactive_game_settlement_settlement_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc8, 0x05, 0x0a,
	0x0d, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x6f, 0x6f, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x73, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d,
	0x12, 0x40, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x61, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0xa8, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1e, 0x0a,
	0x0a, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x65, 0x61,
	0x6d, 0x1a, 0x34, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x22, 0x85, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x74,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65,
	0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0x32, 0x0a, 0x06, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x77, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x77, 0x69, 0x6e, 0x22,
	0xc2, 0x06, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x12, 0x3a, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6f, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x6f, 0x6f,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x4f, 0x66, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x4f, 0x66, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x0e, 0x73, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75,
	0x6d, 0x1a, 0xaf, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x61, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68,
	0x61, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x49, 0x63,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x67, 0x49, 0x63, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x46, 0x72,
	0x61, 0x6d, 0x65, 0x1a, 0xfe, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x77,
	0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x73, 0x65, 0x61, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x65, 0x61, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d,
	0x12, 0x42, 0x0a, 0x03, 0x73, 0x65, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x2e, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x73, 0x65, 0x67, 0x22, 0xdc, 0x03, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x4f, 0x66, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x4f, 0x66, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x4f, 0x66,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x4f, 0x66, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x70,
	0x61, 0x72, 0x61, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x61, 0x72, 0x61, 0x51, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x50, 0x61, 0x72, 0x61, 0x51, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c,
	0x4f, 0x66, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x4f, 0x66, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x12,
	0x26, 0x0a, 0x0e, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x4f, 0x66, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c,
	0x4f, 0x66, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x4f, 0x66, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x4f, 0x66, 0x46, 0x6c, 0x6f,
	0x77, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6c,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x69,
	0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4d, 0x61, 0x69, 0x6c, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4d, 0x61, 0x69,
	0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x51, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x65, 0x61, 0x6d, 0x51, 0x12, 0x26, 0x0a, 0x0e, 0x74,
	0x65, 0x61, 0x6d, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x61, 0x6d, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x2a, 0x3a, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x10, 0x01, 0x32,
	0x5c, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a,
	0x0a, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x48, 0x5a,
	0x46, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_settlement_settlement_proto_rawDescOnce sync.Once
	file_pb_interactive_game_settlement_settlement_proto_rawDescData = file_pb_interactive_game_settlement_settlement_proto_rawDesc
)

func file_pb_interactive_game_settlement_settlement_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_settlement_settlement_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_settlement_settlement_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_settlement_settlement_proto_rawDescData)
	})
	return file_pb_interactive_game_settlement_settlement_proto_rawDescData
}

var file_pb_interactive_game_settlement_settlement_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_interactive_game_settlement_settlement_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_pb_interactive_game_settlement_settlement_proto_goTypes = []interface{}{
	(SettlementMode)(0),                    // 0: interactive_game.SettlementMode
	(*SettlementReq)(nil),                  // 1: interactive_game.SettlementReq
	(*SettlementRsp)(nil),                  // 2: interactive_game.SettlementRsp
	(*SettlementResult)(nil),               // 3: interactive_game.SettlementResult
	(*SettlementConfig)(nil),               // 4: interactive_game.SettlementConfig
	(*SettlementReq_Result)(nil),           // 5: interactive_game.SettlementReq.Result
	(*SettlementReq_Player)(nil),           // 6: interactive_game.SettlementReq.Player
	(*SettlementRsp_Result)(nil),           // 7: interactive_game.SettlementRsp.Result
	(*SettlementResult_SegmentResult)(nil), // 8: interactive_game.SettlementResult.SegmentResult
	(*SettlementResult_Result)(nil),        // 9: interactive_game.SettlementResult.Result
	(common.PayMode)(0),                    // 10: interactive_game.common.PayMode
}
var file_pb_interactive_game_settlement_settlement_proto_depIdxs = []int32{
	5,  // 0: interactive_game.SettlementReq.results:type_name -> interactive_game.SettlementReq.Result
	10, // 1: interactive_game.SettlementReq.payMode:type_name -> interactive_game.common.PayMode
	0,  // 2: interactive_game.SettlementReq.settlementMode:type_name -> interactive_game.SettlementMode
	6,  // 3: interactive_game.SettlementReq.players:type_name -> interactive_game.SettlementReq.Player
	7,  // 4: interactive_game.SettlementRsp.results:type_name -> interactive_game.SettlementRsp.Result
	9,  // 5: interactive_game.SettlementResult.results:type_name -> interactive_game.SettlementResult.Result
	10, // 6: interactive_game.SettlementResult.payMode:type_name -> interactive_game.common.PayMode
	0,  // 7: interactive_game.SettlementResult.settlementMode:type_name -> interactive_game.SettlementMode
	8,  // 8: interactive_game.SettlementResult.Result.seg:type_name -> interactive_game.SettlementResult.SegmentResult
	1,  // 9: interactive_game.Settlement.Settlement:input_type -> interactive_game.SettlementReq
	2,  // 10: interactive_game.Settlement.Settlement:output_type -> interactive_game.SettlementRsp
	10, // [10:11] is the sub-list for method output_type
	9,  // [9:10] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_settlement_settlement_proto_init() }
func file_pb_interactive_game_settlement_settlement_proto_init() {
	if File_pb_interactive_game_settlement_settlement_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementReq_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementReq_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementRsp_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementResult_SegmentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_settlement_settlement_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettlementResult_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_settlement_settlement_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_settlement_settlement_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_settlement_settlement_proto_depIdxs,
		EnumInfos:         file_pb_interactive_game_settlement_settlement_proto_enumTypes,
		MessageInfos:      file_pb_interactive_game_settlement_settlement_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_settlement_settlement_proto = out.File
	file_pb_interactive_game_settlement_settlement_proto_rawDesc = nil
	file_pb_interactive_game_settlement_settlement_proto_goTypes = nil
	file_pb_interactive_game_settlement_settlement_proto_depIdxs = nil
}
