{"swagger": "2.0", "info": {"title": "pb/interactive_game/settlement/settlement.proto", "version": "version not set"}, "tags": [{"name": "Settlement"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.Settlement/Settlement": {"post": {"summary": "结算请求", "operationId": "Settlement_Settlement", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameSettlementRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameSettlementReq"}}], "tags": ["Settlement"]}}}, "definitions": {"SettlementReqPlayer": {"type": "object", "properties": {"openId": {"type": "string"}, "team": {"type": "integer", "format": "int64", "title": "组队模式下，相同的为队友"}}}, "commonPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "interactive_gameSettlementMode": {"type": "string", "enum": ["SettlementSingle", "SettlementTeam"], "default": "SettlementSingle", "title": "- SettlementTeam: 组队"}, "interactive_gameSettlementReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "roomId": {"type": "string", "title": "房间 id"}, "roundId": {"type": "string", "title": "场次 id"}, "results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameSettlementReqResult"}, "title": "各玩家游戏结果"}, "payMode": {"$ref": "#/definitions/commonPayMode", "title": "0免费场/1货币场/2鲜花场"}, "pool": {"type": "string", "format": "int64", "title": "总奖池"}, "ts": {"type": "string", "format": "int64", "title": "游戏开始时间戳"}, "settlementMode": {"$ref": "#/definitions/interactive_gameSettlementMode"}, "playerNum": {"type": "integer", "format": "int64", "title": "最大玩家数"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/SettlementReqPlayer"}, "title": "对局玩家列表"}, "payAssetNum": {"type": "string", "format": "int64", "title": "扣费资产数量，对应字段 payMode"}, "gameEndTime": {"type": "string", "format": "int64", "title": "游戏结束时间戳"}}}, "interactive_gameSettlementReqResult": {"type": "object", "properties": {"openId": {"type": "string"}, "rank": {"type": "integer", "format": "int64", "title": "排名 从 1 开始"}, "experience": {"type": "string", "format": "int64", "title": "获得经验值"}, "score": {"type": "string", "format": "int64", "title": "分数"}, "seat": {"type": "integer", "format": "int64", "title": "座位号"}, "extra": {"type": "string", "title": "额外透传数据"}, "team": {"type": "integer", "format": "int64", "title": "组队模式下，相同的为队友"}}}, "interactive_gameSettlementRsp": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameSettlementRspResult"}}}}, "interactive_gameSettlementRspResult": {"type": "object", "properties": {"openId": {"type": "string"}, "win": {"type": "string", "format": "int64"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}