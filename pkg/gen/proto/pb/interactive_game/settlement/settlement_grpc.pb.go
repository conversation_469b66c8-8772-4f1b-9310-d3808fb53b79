// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/settlement/settlement.proto

package interactive_game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Settlement_Settlement_FullMethodName = "/interactive_game.Settlement/Settlement"
)

// SettlementClient is the client API for Settlement service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SettlementClient interface {
	// 结算请求
	Settlement(ctx context.Context, in *SettlementReq, opts ...grpc.CallOption) (*SettlementRsp, error)
}

type settlementClient struct {
	cc grpc.ClientConnInterface
}

func NewSettlementClient(cc grpc.ClientConnInterface) SettlementClient {
	return &settlementClient{cc}
}

func (c *settlementClient) Settlement(ctx context.Context, in *SettlementReq, opts ...grpc.CallOption) (*SettlementRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SettlementRsp)
	err := c.cc.Invoke(ctx, Settlement_Settlement_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SettlementServer is the server API for Settlement service.
// All implementations should embed UnimplementedSettlementServer
// for forward compatibility
type SettlementServer interface {
	// 结算请求
	Settlement(context.Context, *SettlementReq) (*SettlementRsp, error)
}

// UnimplementedSettlementServer should be embedded to have forward compatible implementations.
type UnimplementedSettlementServer struct {
}

func (UnimplementedSettlementServer) Settlement(context.Context, *SettlementReq) (*SettlementRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Settlement not implemented")
}

// UnsafeSettlementServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SettlementServer will
// result in compilation errors.
type UnsafeSettlementServer interface {
	mustEmbedUnimplementedSettlementServer()
}

func RegisterSettlementServer(s grpc.ServiceRegistrar, srv SettlementServer) {
	s.RegisterService(&Settlement_ServiceDesc, srv)
}

func _Settlement_Settlement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettlementReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementServer).Settlement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Settlement_Settlement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementServer).Settlement(ctx, req.(*SettlementReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Settlement_ServiceDesc is the grpc.ServiceDesc for Settlement service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Settlement_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.Settlement",
	HandlerType: (*SettlementServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Settlement",
			Handler:    _Settlement_Settlement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/settlement/settlement.proto",
}
