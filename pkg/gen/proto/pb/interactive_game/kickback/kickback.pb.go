// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/kickback/kickback.proto

package kickback

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransferInReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string         `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	PayMode       common.PayMode `protobuf:"varint,2,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"`
	Value         int64          `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	TransactionId string         `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
}

func (x *TransferInReq) Reset() {
	*x = TransferInReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_kickback_kickback_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferInReq) ProtoMessage() {}

func (x *TransferInReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_kickback_kickback_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferInReq.ProtoReflect.Descriptor instead.
func (*TransferInReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_kickback_kickback_proto_rawDescGZIP(), []int{0}
}

func (x *TransferInReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TransferInReq) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

func (x *TransferInReq) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *TransferInReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

type TransferInRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TransferInRsp) Reset() {
	*x = TransferInRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_kickback_kickback_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransferInRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferInRsp) ProtoMessage() {}

func (x *TransferInRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_kickback_kickback_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferInRsp.ProtoReflect.Descriptor instead.
func (*TransferInRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_kickback_kickback_proto_rawDescGZIP(), []int{1}
}

var File_pb_interactive_game_kickback_kickback_proto protoreflect.FileDescriptor

var file_pb_interactive_game_kickback_kickback_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x2f, 0x6b,
	0x69, 0x63, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x63, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x9d, 0x01, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x07, 0x70, 0x61, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x52,
	0x73, 0x70, 0x32, 0x6c, 0x0a, 0x08, 0x4b, 0x69, 0x63, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x60,
	0x0a, 0x0a, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x12, 0x28, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x6b, 0x69, 0x63, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65,
	0x72, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x69, 0x63, 0x6b, 0x62, 0x61,
	0x63, 0x6b, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x52, 0x73, 0x70,
	0x42, 0x51, 0x5a, 0x4f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6b, 0x69, 0x63, 0x6b, 0x62,
	0x61, 0x63, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_kickback_kickback_proto_rawDescOnce sync.Once
	file_pb_interactive_game_kickback_kickback_proto_rawDescData = file_pb_interactive_game_kickback_kickback_proto_rawDesc
)

func file_pb_interactive_game_kickback_kickback_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_kickback_kickback_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_kickback_kickback_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_kickback_kickback_proto_rawDescData)
	})
	return file_pb_interactive_game_kickback_kickback_proto_rawDescData
}

var file_pb_interactive_game_kickback_kickback_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_interactive_game_kickback_kickback_proto_goTypes = []interface{}{
	(*TransferInReq)(nil), // 0: interactive_game_kickback.TransferInReq
	(*TransferInRsp)(nil), // 1: interactive_game_kickback.TransferInRsp
	(common.PayMode)(0),   // 2: interactive_game.common.PayMode
}
var file_pb_interactive_game_kickback_kickback_proto_depIdxs = []int32{
	2, // 0: interactive_game_kickback.TransferInReq.payMode:type_name -> interactive_game.common.PayMode
	0, // 1: interactive_game_kickback.Kickback.TransferIn:input_type -> interactive_game_kickback.TransferInReq
	1, // 2: interactive_game_kickback.Kickback.TransferIn:output_type -> interactive_game_kickback.TransferInRsp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_kickback_kickback_proto_init() }
func file_pb_interactive_game_kickback_kickback_proto_init() {
	if File_pb_interactive_game_kickback_kickback_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_kickback_kickback_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferInReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_kickback_kickback_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransferInRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_kickback_kickback_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_kickback_kickback_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_kickback_kickback_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_kickback_kickback_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_kickback_kickback_proto = out.File
	file_pb_interactive_game_kickback_kickback_proto_rawDesc = nil
	file_pb_interactive_game_kickback_kickback_proto_goTypes = nil
	file_pb_interactive_game_kickback_kickback_proto_depIdxs = nil
}
