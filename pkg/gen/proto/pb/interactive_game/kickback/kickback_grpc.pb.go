// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/kickback/kickback.proto

package kickback

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Kickback_TransferIn_FullMethodName = "/interactive_game_kickback.Kickback/TransferIn"
)

// KickbackClient is the client API for Kickback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KickbackClient interface {
	// 转入
	TransferIn(ctx context.Context, in *TransferInReq, opts ...grpc.CallOption) (*TransferInRsp, error)
}

type kickbackClient struct {
	cc grpc.ClientConnInterface
}

func NewKickbackClient(cc grpc.ClientConnInterface) KickbackClient {
	return &kickbackClient{cc}
}

func (c *kickbackClient) TransferIn(ctx context.Context, in *TransferInReq, opts ...grpc.CallOption) (*TransferInRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransferInRsp)
	err := c.cc.Invoke(ctx, Kickback_TransferIn_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KickbackServer is the server API for Kickback service.
// All implementations should embed UnimplementedKickbackServer
// for forward compatibility
type KickbackServer interface {
	// 转入
	TransferIn(context.Context, *TransferInReq) (*TransferInRsp, error)
}

// UnimplementedKickbackServer should be embedded to have forward compatible implementations.
type UnimplementedKickbackServer struct {
}

func (UnimplementedKickbackServer) TransferIn(context.Context, *TransferInReq) (*TransferInRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferIn not implemented")
}

// UnsafeKickbackServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KickbackServer will
// result in compilation errors.
type UnsafeKickbackServer interface {
	mustEmbedUnimplementedKickbackServer()
}

func RegisterKickbackServer(s grpc.ServiceRegistrar, srv KickbackServer) {
	s.RegisterService(&Kickback_ServiceDesc, srv)
}

func _Kickback_TransferIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KickbackServer).TransferIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kickback_TransferIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KickbackServer).TransferIn(ctx, req.(*TransferInReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Kickback_ServiceDesc is the grpc.ServiceDesc for Kickback service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Kickback_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game_kickback.Kickback",
	HandlerType: (*KickbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TransferIn",
			Handler:    _Kickback_TransferIn_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/kickback/kickback.proto",
}
