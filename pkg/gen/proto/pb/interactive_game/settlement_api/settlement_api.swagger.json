{"swagger": "2.0", "info": {"title": "pb/interactive_game/settlement_api/settlement_api.proto", "version": "version not set"}, "tags": [{"name": "SettlementApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.SettlementApi/SettlementResult": {"post": {"summary": "查询结算结果", "operationId": "SettlementApi_SettlementResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameSettlementResultRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameSettlementResultReq"}}], "tags": ["SettlementApi"]}}}, "definitions": {"SettlementResultRspResult": {"type": "object", "properties": {"openId": {"type": "string"}, "rank": {"type": "integer", "format": "int64"}, "experience": {"type": "string", "format": "int64"}, "score": {"type": "string", "format": "int64", "title": "得分"}, "win": {"type": "string", "format": "int64", "title": "赢得的货币/鲜花"}, "avatar": {"type": "string", "title": "头像"}, "nick": {"type": "string", "title": "昵称"}, "followed": {"type": "integer", "format": "int64", "title": "关注1，没关注0"}, "seat": {"type": "integer", "format": "int64", "title": "座位号"}, "extra": {"type": "string", "title": "额外透传，预留"}, "team": {"type": "integer", "format": "int64", "title": "组队号"}, "seg": {"$ref": "#/definitions/SettlementResultRspSegmentResult", "title": "段位信息"}}}, "SettlementResultRspSegmentResult": {"type": "object", "properties": {"score": {"type": "string", "format": "int64", "title": "当前获得积分(有正负数情况)"}, "tScore": {"type": "string", "format": "int64", "title": "总积分"}, "segName": {"type": "string", "title": "段位名(例如:白银II)"}, "hasEvent": {"type": "boolean", "title": "是否有段位事件"}, "segIcon": {"type": "string", "title": "段位Icon"}, "avatarFrame": {"type": "string", "title": "段位头像框"}}, "title": "段位结算信息"}, "interactive_gameSettlementResultReq": {"type": "object", "properties": {"roundId": {"type": "string", "title": "场次 id"}, "passback": {"type": "string", "title": "分页用"}}}, "interactive_gameSettlementResultRsp": {"type": "object", "properties": {"payMode": {"type": "integer", "format": "int32", "title": "0免费场/1货币场/2鲜花场"}, "prizeIcon": {"type": "string", "title": "获奖奖励的图标（如果是鲜花则是鲜花的图标）"}, "results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/SettlementResultRspResult"}, "title": "各玩家游戏结果"}, "hostResult": {"$ref": "#/definitions/SettlementResultRspResult", "title": "用户自己的获奖结果"}, "passback": {"type": "string", "title": "分页用"}, "hasMore": {"type": "boolean", "title": "分页用"}, "prizeName": {"type": "string", "title": "奖励名称"}, "settlementMode": {"type": "integer", "format": "int32", "title": "0单人/1组队"}, "playerNum": {"type": "integer", "format": "int64", "title": "最大玩家数"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}