{"swagger": "2.0", "info": {"title": "pb/interactive_game/game_history/game_history.proto", "version": "version not set"}, "tags": [{"name": "GameHistory"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.GameHistory/PayInWhichGame": {"post": {"summary": "查询订单发生在哪局游戏", "operationId": "GameHistory_PayInWhichGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePayInWhichGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePayInWhichGameReq"}}], "tags": ["GameHistory"]}}, "/interactive_game.GameHistory/RoomGameHistory": {"post": {"summary": "查询房间历史游戏", "operationId": "GameHistory_RoomGameHistory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameRoomGameHistoryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameRoomGameHistoryReq"}}], "tags": ["GameHistory"]}}}, "definitions": {"commonPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "interactive_gameGameHistoryItem": {"type": "object", "properties": {"roomId": {"type": "string"}, "roundId": {"type": "string"}, "gameBeginTs": {"type": "string", "format": "uint64"}, "gameEndTs": {"type": "string", "format": "uint64"}, "roomCreateTs": {"type": "string", "format": "uint64"}, "roomDestroyTs": {"type": "string", "format": "uint64"}, "ext": {"$ref": "#/definitions/interactive_gameGameHistoryItemExt"}}}, "interactive_gameGameHistoryItemExt": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "common.RoomType"}, "payMode": {"$ref": "#/definitions/commonPayMode", "title": "common.PayMode"}}}, "interactive_gamePayInWhichGameReq": {"type": "object", "properties": {"PayUid": {"type": "integer", "format": "int64"}, "PayTime": {"type": "integer", "format": "int64"}, "RoomId": {"type": "string"}, "BillNo": {"type": "string"}, "LPayUid": {"type": "string", "format": "int64"}}}, "interactive_gamePayInWhichGameRsp": {"type": "object", "properties": {"appId": {"type": "string"}, "roundId": {"type": "string"}}}, "interactive_gameRoomGameHistoryReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间 id"}, "pageSize": {"type": "integer", "format": "int64", "title": "分页大小"}, "passback": {"type": "string"}}}, "interactive_gameRoomGameHistoryRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameGameHistoryItem"}}, "passback": {"type": "string"}}}, "interactive_gamecommonRoomType": {"type": "string", "enum": ["TypeNone", "TypeKtv", "TypeTwins", "TypeLive", "TypeMatch"], "default": "TypeNone", "title": "- TypeKtv: 歌房\n - TypeTwins: 双人房\n - TypeLive: 直播\n - TypeMatch: 匹配房"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}