// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/game_interface/game_interface.proto

package interactive_game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GameInterface_GameOver_FullMethodName           = "/interactive_game.GameInterface/GameOver"
	GameInterface_CreateGame_FullMethodName         = "/interactive_game.GameInterface/CreateGame"
	GameInterface_BatchGetRoomStatus_FullMethodName = "/interactive_game.GameInterface/BatchGetRoomStatus"
	GameInterface_EventReport_FullMethodName        = "/interactive_game.GameInterface/EventReport"
	GameInterface_PlayerOut_FullMethodName          = "/interactive_game.GameInterface/PlayerOut"
	GameInterface_GetGameRoundStatus_FullMethodName = "/interactive_game.GameInterface/GetGameRoundStatus"
)

// GameInterfaceClient is the client API for GameInterface service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GameInterfaceClient interface {
	// 结束游戏
	GameOver(ctx context.Context, in *GameOverReq, opts ...grpc.CallOption) (*GameOverRsp, error)
	// 创建游戏 测试回调
	CreateGame(ctx context.Context, in *CreateGameReq, opts ...grpc.CallOption) (*CreateGameRsp, error)
	// 批量获取房间状态
	BatchGetRoomStatus(ctx context.Context, in *BatchGetRoomStatusReq, opts ...grpc.CallOption) (*BatchGetRoomStatusRsp, error)
	// 事件上报
	EventReport(ctx context.Context, in *EventReportReq, opts ...grpc.CallOption) (*EventReportRsp, error)
	// 淘汰
	PlayerOut(ctx context.Context, in *PlayerOutReq, opts ...grpc.CallOption) (*PlayerOutRsp, error)
	// 获取游戏场次状态 (游戏实现)
	GetGameRoundStatus(ctx context.Context, in *GetGameRoundStatusReq, opts ...grpc.CallOption) (*GetGameRoundStatusRsp, error)
}

type gameInterfaceClient struct {
	cc grpc.ClientConnInterface
}

func NewGameInterfaceClient(cc grpc.ClientConnInterface) GameInterfaceClient {
	return &gameInterfaceClient{cc}
}

func (c *gameInterfaceClient) GameOver(ctx context.Context, in *GameOverReq, opts ...grpc.CallOption) (*GameOverRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameOverRsp)
	err := c.cc.Invoke(ctx, GameInterface_GameOver_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameInterfaceClient) CreateGame(ctx context.Context, in *CreateGameReq, opts ...grpc.CallOption) (*CreateGameRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateGameRsp)
	err := c.cc.Invoke(ctx, GameInterface_CreateGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameInterfaceClient) BatchGetRoomStatus(ctx context.Context, in *BatchGetRoomStatusReq, opts ...grpc.CallOption) (*BatchGetRoomStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetRoomStatusRsp)
	err := c.cc.Invoke(ctx, GameInterface_BatchGetRoomStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameInterfaceClient) EventReport(ctx context.Context, in *EventReportReq, opts ...grpc.CallOption) (*EventReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EventReportRsp)
	err := c.cc.Invoke(ctx, GameInterface_EventReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameInterfaceClient) PlayerOut(ctx context.Context, in *PlayerOutReq, opts ...grpc.CallOption) (*PlayerOutRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlayerOutRsp)
	err := c.cc.Invoke(ctx, GameInterface_PlayerOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameInterfaceClient) GetGameRoundStatus(ctx context.Context, in *GetGameRoundStatusReq, opts ...grpc.CallOption) (*GetGameRoundStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGameRoundStatusRsp)
	err := c.cc.Invoke(ctx, GameInterface_GetGameRoundStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameInterfaceServer is the server API for GameInterface service.
// All implementations should embed UnimplementedGameInterfaceServer
// for forward compatibility
type GameInterfaceServer interface {
	// 结束游戏
	GameOver(context.Context, *GameOverReq) (*GameOverRsp, error)
	// 创建游戏 测试回调
	CreateGame(context.Context, *CreateGameReq) (*CreateGameRsp, error)
	// 批量获取房间状态
	BatchGetRoomStatus(context.Context, *BatchGetRoomStatusReq) (*BatchGetRoomStatusRsp, error)
	// 事件上报
	EventReport(context.Context, *EventReportReq) (*EventReportRsp, error)
	// 淘汰
	PlayerOut(context.Context, *PlayerOutReq) (*PlayerOutRsp, error)
	// 获取游戏场次状态 (游戏实现)
	GetGameRoundStatus(context.Context, *GetGameRoundStatusReq) (*GetGameRoundStatusRsp, error)
}

// UnimplementedGameInterfaceServer should be embedded to have forward compatible implementations.
type UnimplementedGameInterfaceServer struct {
}

func (UnimplementedGameInterfaceServer) GameOver(context.Context, *GameOverReq) (*GameOverRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameOver not implemented")
}
func (UnimplementedGameInterfaceServer) CreateGame(context.Context, *CreateGameReq) (*CreateGameRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGame not implemented")
}
func (UnimplementedGameInterfaceServer) BatchGetRoomStatus(context.Context, *BatchGetRoomStatusReq) (*BatchGetRoomStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetRoomStatus not implemented")
}
func (UnimplementedGameInterfaceServer) EventReport(context.Context, *EventReportReq) (*EventReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EventReport not implemented")
}
func (UnimplementedGameInterfaceServer) PlayerOut(context.Context, *PlayerOutReq) (*PlayerOutRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlayerOut not implemented")
}
func (UnimplementedGameInterfaceServer) GetGameRoundStatus(context.Context, *GetGameRoundStatusReq) (*GetGameRoundStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGameRoundStatus not implemented")
}

// UnsafeGameInterfaceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameInterfaceServer will
// result in compilation errors.
type UnsafeGameInterfaceServer interface {
	mustEmbedUnimplementedGameInterfaceServer()
}

func RegisterGameInterfaceServer(s grpc.ServiceRegistrar, srv GameInterfaceServer) {
	s.RegisterService(&GameInterface_ServiceDesc, srv)
}

func _GameInterface_GameOver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameOverReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameInterfaceServer).GameOver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameInterface_GameOver_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameInterfaceServer).GameOver(ctx, req.(*GameOverReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameInterface_CreateGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameInterfaceServer).CreateGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameInterface_CreateGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameInterfaceServer).CreateGame(ctx, req.(*CreateGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameInterface_BatchGetRoomStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetRoomStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameInterfaceServer).BatchGetRoomStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameInterface_BatchGetRoomStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameInterfaceServer).BatchGetRoomStatus(ctx, req.(*BatchGetRoomStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameInterface_EventReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EventReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameInterfaceServer).EventReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameInterface_EventReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameInterfaceServer).EventReport(ctx, req.(*EventReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameInterface_PlayerOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerOutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameInterfaceServer).PlayerOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameInterface_PlayerOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameInterfaceServer).PlayerOut(ctx, req.(*PlayerOutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameInterface_GetGameRoundStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameRoundStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameInterfaceServer).GetGameRoundStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameInterface_GetGameRoundStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameInterfaceServer).GetGameRoundStatus(ctx, req.(*GetGameRoundStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GameInterface_ServiceDesc is the grpc.ServiceDesc for GameInterface service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GameInterface_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.GameInterface",
	HandlerType: (*GameInterfaceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GameOver",
			Handler:    _GameInterface_GameOver_Handler,
		},
		{
			MethodName: "CreateGame",
			Handler:    _GameInterface_CreateGame_Handler,
		},
		{
			MethodName: "BatchGetRoomStatus",
			Handler:    _GameInterface_BatchGetRoomStatus_Handler,
		},
		{
			MethodName: "EventReport",
			Handler:    _GameInterface_EventReport_Handler,
		},
		{
			MethodName: "PlayerOut",
			Handler:    _GameInterface_PlayerOut_Handler,
		},
		{
			MethodName: "GetGameRoundStatus",
			Handler:    _GameInterface_GetGameRoundStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/game_interface/game_interface.proto",
}
