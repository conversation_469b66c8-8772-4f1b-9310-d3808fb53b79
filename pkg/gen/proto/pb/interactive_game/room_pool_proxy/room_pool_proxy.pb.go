// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/room_pool_proxy/room_pool_proxy.proto

package room_pool_proxy

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlayerMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid              uint64                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppId            string                           `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Intentions       []*common.ConcreteMatchIntention `protobuf:"bytes,3,rep,name=intentions,proto3" json:"intentions,omitempty"`
	SaveIntention    bool                             `protobuf:"varint,4,opt,name=saveIntention,proto3" json:"saveIntention,omitempty"`
	ReplaceIntention bool                             `protobuf:"varint,5,opt,name=replaceIntention,proto3" json:"replaceIntention,omitempty"`
}

func (x *PlayerMatchReq) Reset() {
	*x = PlayerMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMatchReq) ProtoMessage() {}

func (x *PlayerMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMatchReq.ProtoReflect.Descriptor instead.
func (*PlayerMatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{0}
}

func (x *PlayerMatchReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PlayerMatchReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PlayerMatchReq) GetIntentions() []*common.ConcreteMatchIntention {
	if x != nil {
		return x.Intentions
	}
	return nil
}

func (x *PlayerMatchReq) GetSaveIntention() bool {
	if x != nil {
		return x.SaveIntention
	}
	return false
}

func (x *PlayerMatchReq) GetReplaceIntention() bool {
	if x != nil {
		return x.ReplaceIntention
	}
	return false
}

type PlayerMatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback string `protobuf:"bytes,1,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *PlayerMatchRsp) Reset() {
	*x = PlayerMatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMatchRsp) ProtoMessage() {}

func (x *PlayerMatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMatchRsp.ProtoReflect.Descriptor instead.
func (*PlayerMatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{1}
}

func (x *PlayerMatchRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type PlayerMatchPollReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *PlayerMatchPollReq) Reset() {
	*x = PlayerMatchPollReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMatchPollReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMatchPollReq) ProtoMessage() {}

func (x *PlayerMatchPollReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMatchPollReq.ProtoReflect.Descriptor instead.
func (*PlayerMatchPollReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{2}
}

func (x *PlayerMatchPollReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PlayerMatchPollReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type PlayerMatchPollRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback    string            `protobuf:"bytes,1,opt,name=passback,proto3" json:"passback,omitempty"`
	RoomId      string            `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Backup      bool              `protobuf:"varint,3,opt,name=backup,proto3" json:"backup,omitempty"`
	ModeId      string            `protobuf:"bytes,4,opt,name=modeId,proto3" json:"modeId,omitempty"`
	PayModeId   string            `protobuf:"bytes,5,opt,name=payModeId,proto3" json:"payModeId,omitempty"`
	AppId       string            `protobuf:"bytes,6,opt,name=appId,proto3" json:"appId,omitempty"`
	MatchSource int32             `protobuf:"varint,7,opt,name=matchSource,proto3" json:"matchSource,omitempty"`
	Extend      map[string]string `protobuf:"bytes,8,rep,name=extend,proto3" json:"extend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PlayerMatchPollRsp) Reset() {
	*x = PlayerMatchPollRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMatchPollRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMatchPollRsp) ProtoMessage() {}

func (x *PlayerMatchPollRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMatchPollRsp.ProtoReflect.Descriptor instead.
func (*PlayerMatchPollRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{3}
}

func (x *PlayerMatchPollRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *PlayerMatchPollRsp) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *PlayerMatchPollRsp) GetBackup() bool {
	if x != nil {
		return x.Backup
	}
	return false
}

func (x *PlayerMatchPollRsp) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *PlayerMatchPollRsp) GetPayModeId() string {
	if x != nil {
		return x.PayModeId
	}
	return ""
}

func (x *PlayerMatchPollRsp) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PlayerMatchPollRsp) GetMatchSource() int32 {
	if x != nil {
		return x.MatchSource
	}
	return 0
}

func (x *PlayerMatchPollRsp) GetExtend() map[string]string {
	if x != nil {
		return x.Extend
	}
	return nil
}

type MatchSuccessReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*MatchSuccessReq_MatchResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *MatchSuccessReq) Reset() {
	*x = MatchSuccessReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchSuccessReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchSuccessReq) ProtoMessage() {}

func (x *MatchSuccessReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchSuccessReq.ProtoReflect.Descriptor instead.
func (*MatchSuccessReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{4}
}

func (x *MatchSuccessReq) GetResults() []*MatchSuccessReq_MatchResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type MatchSuccessRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MatchSuccessRsp) Reset() {
	*x = MatchSuccessRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchSuccessRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchSuccessRsp) ProtoMessage() {}

func (x *MatchSuccessRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchSuccessRsp.ProtoReflect.Descriptor instead.
func (*MatchSuccessRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{5}
}

type ListMatchRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string              `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	MatchType common.AppMatchType `protobuf:"varint,2,opt,name=matchType,proto3,enum=interactive_game.common.AppMatchType" json:"matchType,omitempty"`
}

func (x *ListMatchRoomReq) Reset() {
	*x = ListMatchRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMatchRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMatchRoomReq) ProtoMessage() {}

func (x *ListMatchRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMatchRoomReq.ProtoReflect.Descriptor instead.
func (*ListMatchRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{6}
}

func (x *ListMatchRoomReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ListMatchRoomReq) GetMatchType() common.AppMatchType {
	if x != nil {
		return x.MatchType
	}
	return common.AppMatchType(0)
}

type ListMatchRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rooms []*ListMatchRoomRsp_MatchRoom `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
}

func (x *ListMatchRoomRsp) Reset() {
	*x = ListMatchRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMatchRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMatchRoomRsp) ProtoMessage() {}

func (x *ListMatchRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMatchRoomRsp.ProtoReflect.Descriptor instead.
func (*ListMatchRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{7}
}

func (x *ListMatchRoomRsp) GetRooms() []*ListMatchRoomRsp_MatchRoom {
	if x != nil {
		return x.Rooms
	}
	return nil
}

type PlayerCancelMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *PlayerCancelMatchReq) Reset() {
	*x = PlayerCancelMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerCancelMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerCancelMatchReq) ProtoMessage() {}

func (x *PlayerCancelMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerCancelMatchReq.ProtoReflect.Descriptor instead.
func (*PlayerCancelMatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{8}
}

func (x *PlayerCancelMatchReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PlayerCancelMatchReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type PlayerCancelMatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlayerCancelMatchRsp) Reset() {
	*x = PlayerCancelMatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerCancelMatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerCancelMatchRsp) ProtoMessage() {}

func (x *PlayerCancelMatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerCancelMatchRsp.ProtoReflect.Descriptor instead.
func (*PlayerCancelMatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{9}
}

type ListRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId string `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Passback  string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
	ModeId    string `protobuf:"bytes,3,opt,name=modeId,proto3" json:"modeId,omitempty"`
}

func (x *ListRoomReq) Reset() {
	*x = ListRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomReq) ProtoMessage() {}

func (x *ListRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomReq.ProtoReflect.Descriptor instead.
func (*ListRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{10}
}

func (x *ListRoomReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *ListRoomReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *ListRoomReq) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

type ListRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rooms    []*ListRoomRsp_Room `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	HasMore  bool                `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`
	Passback string              `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *ListRoomRsp) Reset() {
	*x = ListRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomRsp) ProtoMessage() {}

func (x *ListRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomRsp.ProtoReflect.Descriptor instead.
func (*ListRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{11}
}

func (x *ListRoomRsp) GetRooms() []*ListRoomRsp_Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *ListRoomRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *ListRoomRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type PlayerMatchIntentionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId string `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Uid       uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *PlayerMatchIntentionReq) Reset() {
	*x = PlayerMatchIntentionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMatchIntentionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMatchIntentionReq) ProtoMessage() {}

func (x *PlayerMatchIntentionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMatchIntentionReq.ProtoReflect.Descriptor instead.
func (*PlayerMatchIntentionReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{12}
}

func (x *PlayerMatchIntentionReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *PlayerMatchIntentionReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type PlayerMatchIntentionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intentions []*common.MatchIntention `protobuf:"bytes,1,rep,name=intentions,proto3" json:"intentions,omitempty"`
}

func (x *PlayerMatchIntentionRsp) Reset() {
	*x = PlayerMatchIntentionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMatchIntentionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMatchIntentionRsp) ProtoMessage() {}

func (x *PlayerMatchIntentionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMatchIntentionRsp.ProtoReflect.Descriptor instead.
func (*PlayerMatchIntentionRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{13}
}

func (x *PlayerMatchIntentionRsp) GetIntentions() []*common.MatchIntention {
	if x != nil {
		return x.Intentions
	}
	return nil
}

type PlayerPauseMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid      uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *PlayerPauseMatchReq) Reset() {
	*x = PlayerPauseMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerPauseMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerPauseMatchReq) ProtoMessage() {}

func (x *PlayerPauseMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerPauseMatchReq.ProtoReflect.Descriptor instead.
func (*PlayerPauseMatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{14}
}

func (x *PlayerPauseMatchReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PlayerPauseMatchReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type PlayerPauseMatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlayerPauseMatchRsp) Reset() {
	*x = PlayerPauseMatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerPauseMatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerPauseMatchRsp) ProtoMessage() {}

func (x *PlayerPauseMatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerPauseMatchRsp.ProtoReflect.Descriptor instead.
func (*PlayerPauseMatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{15}
}

type PlayerResetMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid        uint64                           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Passback   string                           `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
	Intentions []*common.ConcreteMatchIntention `protobuf:"bytes,3,rep,name=intentions,proto3" json:"intentions,omitempty"`
}

func (x *PlayerResetMatchReq) Reset() {
	*x = PlayerResetMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerResetMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerResetMatchReq) ProtoMessage() {}

func (x *PlayerResetMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerResetMatchReq.ProtoReflect.Descriptor instead.
func (*PlayerResetMatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{16}
}

func (x *PlayerResetMatchReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *PlayerResetMatchReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *PlayerResetMatchReq) GetIntentions() []*common.ConcreteMatchIntention {
	if x != nil {
		return x.Intentions
	}
	return nil
}

type PlayerResetMatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlayerResetMatchRsp) Reset() {
	*x = PlayerResetMatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerResetMatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerResetMatchRsp) ProtoMessage() {}

func (x *PlayerResetMatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerResetMatchRsp.ProtoReflect.Descriptor instead.
func (*PlayerResetMatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{17}
}

type MaxMatchingModeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId string `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
}

func (x *MaxMatchingModeReq) Reset() {
	*x = MaxMatchingModeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaxMatchingModeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaxMatchingModeReq) ProtoMessage() {}

func (x *MaxMatchingModeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaxMatchingModeReq.ProtoReflect.Descriptor instead.
func (*MaxMatchingModeReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{18}
}

func (x *MaxMatchingModeReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

type MaxMatchingModeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModeId string `protobuf:"bytes,1,opt,name=modeId,proto3" json:"modeId,omitempty"`
}

func (x *MaxMatchingModeRsp) Reset() {
	*x = MaxMatchingModeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaxMatchingModeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaxMatchingModeRsp) ProtoMessage() {}

func (x *MaxMatchingModeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaxMatchingModeRsp.ProtoReflect.Descriptor instead.
func (*MaxMatchingModeRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{19}
}

func (x *MaxMatchingModeRsp) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

type OptimalIntentionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId  string                           `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Intentions []*common.ConcreteMatchIntention `protobuf:"bytes,2,rep,name=intentions,proto3" json:"intentions,omitempty"`
}

func (x *OptimalIntentionReq) Reset() {
	*x = OptimalIntentionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptimalIntentionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptimalIntentionReq) ProtoMessage() {}

func (x *OptimalIntentionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptimalIntentionReq.ProtoReflect.Descriptor instead.
func (*OptimalIntentionReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{20}
}

func (x *OptimalIntentionReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *OptimalIntentionReq) GetIntentions() []*common.ConcreteMatchIntention {
	if x != nil {
		return x.Intentions
	}
	return nil
}

type OptimalIntentionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intention *common.ConcreteMatchIntention `protobuf:"bytes,1,opt,name=intention,proto3" json:"intention,omitempty"`
}

func (x *OptimalIntentionRsp) Reset() {
	*x = OptimalIntentionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptimalIntentionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptimalIntentionRsp) ProtoMessage() {}

func (x *OptimalIntentionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptimalIntentionRsp.ProtoReflect.Descriptor instead.
func (*OptimalIntentionRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{21}
}

func (x *OptimalIntentionRsp) GetIntention() *common.ConcreteMatchIntention {
	if x != nil {
		return x.Intention
	}
	return nil
}

type MatchSuccessReq_MatchResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId    string            `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	RoomType  int32             `protobuf:"varint,2,opt,name=roomType,proto3" json:"roomType,omitempty"`
	Uids      []uint64          `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	AppId     string            `protobuf:"bytes,4,opt,name=appId,proto3" json:"appId,omitempty"`
	ModeId    string            `protobuf:"bytes,5,opt,name=modeId,proto3" json:"modeId,omitempty"`
	PayModeId string            `protobuf:"bytes,6,opt,name=payModeId,proto3" json:"payModeId,omitempty"`
	Extend    map[string]string `protobuf:"bytes,7,rep,name=extend,proto3" json:"extend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MatchSuccessReq_MatchResult) Reset() {
	*x = MatchSuccessReq_MatchResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchSuccessReq_MatchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchSuccessReq_MatchResult) ProtoMessage() {}

func (x *MatchSuccessReq_MatchResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchSuccessReq_MatchResult.ProtoReflect.Descriptor instead.
func (*MatchSuccessReq_MatchResult) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{4, 0}
}

func (x *MatchSuccessReq_MatchResult) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *MatchSuccessReq_MatchResult) GetRoomType() int32 {
	if x != nil {
		return x.RoomType
	}
	return 0
}

func (x *MatchSuccessReq_MatchResult) GetUids() []uint64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *MatchSuccessReq_MatchResult) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *MatchSuccessReq_MatchResult) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *MatchSuccessReq_MatchResult) GetPayModeId() string {
	if x != nil {
		return x.PayModeId
	}
	return ""
}

func (x *MatchSuccessReq_MatchResult) GetExtend() map[string]string {
	if x != nil {
		return x.Extend
	}
	return nil
}

type ListMatchRoomRsp_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *ListMatchRoomRsp_Player) Reset() {
	*x = ListMatchRoomRsp_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMatchRoomRsp_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMatchRoomRsp_Player) ProtoMessage() {}

func (x *ListMatchRoomRsp_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMatchRoomRsp_Player.ProtoReflect.Descriptor instead.
func (*ListMatchRoomRsp_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ListMatchRoomRsp_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type ListMatchRoomRsp_RoomConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModeId       string                       `protobuf:"bytes,1,opt,name=modeId,proto3" json:"modeId,omitempty"`
	PayConfig    *common.GameRoomPayConfig    `protobuf:"bytes,2,opt,name=payConfig,proto3" json:"payConfig,omitempty"`
	MakeUpConfig *common.GameRoomMakeUpConfig `protobuf:"bytes,3,opt,name=makeUpConfig,proto3" json:"makeUpConfig,omitempty"`
}

func (x *ListMatchRoomRsp_RoomConfig) Reset() {
	*x = ListMatchRoomRsp_RoomConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMatchRoomRsp_RoomConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMatchRoomRsp_RoomConfig) ProtoMessage() {}

func (x *ListMatchRoomRsp_RoomConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMatchRoomRsp_RoomConfig.ProtoReflect.Descriptor instead.
func (*ListMatchRoomRsp_RoomConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{7, 1}
}

func (x *ListMatchRoomRsp_RoomConfig) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *ListMatchRoomRsp_RoomConfig) GetPayConfig() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConfig
	}
	return nil
}

func (x *ListMatchRoomRsp_RoomConfig) GetMakeUpConfig() *common.GameRoomMakeUpConfig {
	if x != nil {
		return x.MakeUpConfig
	}
	return nil
}

type ListMatchRoomRsp_MatchRoom struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     common.RoomStatus              `protobuf:"varint,1,opt,name=status,proto3,enum=interactive_game.common.RoomStatus" json:"status,omitempty"`
	Owner      uint64                         `protobuf:"varint,2,opt,name=owner,proto3" json:"owner,omitempty"`
	GameAppId  string                         `protobuf:"bytes,3,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Config     *ListMatchRoomRsp_RoomConfig   `protobuf:"bytes,4,opt,name=config,proto3" json:"config,omitempty"`
	RoomType   common.RoomType                `protobuf:"varint,5,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"`
	UpdateTime int64                          `protobuf:"varint,6,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	MatchTime  int64                          `protobuf:"varint,7,opt,name=matchTime,proto3" json:"matchTime,omitempty"`
	RoomId     string                         `protobuf:"bytes,8,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Players    []*ListMatchRoomRsp_Player     `protobuf:"bytes,9,rep,name=players,proto3" json:"players,omitempty"`
	Intentions []*ListMatchRoomRsp_RoomConfig `protobuf:"bytes,10,rep,name=intentions,proto3" json:"intentions,omitempty"`
}

func (x *ListMatchRoomRsp_MatchRoom) Reset() {
	*x = ListMatchRoomRsp_MatchRoom{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMatchRoomRsp_MatchRoom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMatchRoomRsp_MatchRoom) ProtoMessage() {}

func (x *ListMatchRoomRsp_MatchRoom) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMatchRoomRsp_MatchRoom.ProtoReflect.Descriptor instead.
func (*ListMatchRoomRsp_MatchRoom) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{7, 2}
}

func (x *ListMatchRoomRsp_MatchRoom) GetStatus() common.RoomStatus {
	if x != nil {
		return x.Status
	}
	return common.RoomStatus(0)
}

func (x *ListMatchRoomRsp_MatchRoom) GetOwner() uint64 {
	if x != nil {
		return x.Owner
	}
	return 0
}

func (x *ListMatchRoomRsp_MatchRoom) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *ListMatchRoomRsp_MatchRoom) GetConfig() *ListMatchRoomRsp_RoomConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ListMatchRoomRsp_MatchRoom) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *ListMatchRoomRsp_MatchRoom) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ListMatchRoomRsp_MatchRoom) GetMatchTime() int64 {
	if x != nil {
		return x.MatchTime
	}
	return 0
}

func (x *ListMatchRoomRsp_MatchRoom) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *ListMatchRoomRsp_MatchRoom) GetPlayers() []*ListMatchRoomRsp_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *ListMatchRoomRsp_MatchRoom) GetIntentions() []*ListMatchRoomRsp_RoomConfig {
	if x != nil {
		return x.Intentions
	}
	return nil
}

type ListRoomRsp_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *ListRoomRsp_Player) Reset() {
	*x = ListRoomRsp_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRoomRsp_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomRsp_Player) ProtoMessage() {}

func (x *ListRoomRsp_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomRsp_Player.ProtoReflect.Descriptor instead.
func (*ListRoomRsp_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{11, 0}
}

func (x *ListRoomRsp_Player) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

type ListRoomRsp_Room struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId       string                       `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Players      []*ListRoomRsp_Player        `protobuf:"bytes,2,rep,name=players,proto3" json:"players,omitempty"`
	Status       common.RoomStatus            `protobuf:"varint,3,opt,name=status,proto3,enum=interactive_game.common.RoomStatus" json:"status,omitempty"`
	MakeUpConfig *common.GameRoomMakeUpConfig `protobuf:"bytes,4,opt,name=makeUpConfig,proto3" json:"makeUpConfig,omitempty"`
	PayConfig    *common.GameRoomPayConfig    `protobuf:"bytes,5,opt,name=payConfig,proto3" json:"payConfig,omitempty"`
	RoomType     common.RoomType              `protobuf:"varint,6,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"`
}

func (x *ListRoomRsp_Room) Reset() {
	*x = ListRoomRsp_Room{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRoomRsp_Room) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRoomRsp_Room) ProtoMessage() {}

func (x *ListRoomRsp_Room) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRoomRsp_Room.ProtoReflect.Descriptor instead.
func (*ListRoomRsp_Room) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP(), []int{11, 1}
}

func (x *ListRoomRsp_Room) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *ListRoomRsp_Room) GetPlayers() []*ListRoomRsp_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *ListRoomRsp_Room) GetStatus() common.RoomStatus {
	if x != nil {
		return x.Status
	}
	return common.RoomStatus(0)
}

func (x *ListRoomRsp_Room) GetMakeUpConfig() *common.GameRoomMakeUpConfig {
	if x != nil {
		return x.MakeUpConfig
	}
	return nil
}

func (x *ListRoomRsp_Room) GetPayConfig() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConfig
	}
	return nil
}

func (x *ListRoomRsp_Room) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

var File_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto protoreflect.FileDescriptor

var file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDesc = []byte{
	0x0a, 0x39, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x78, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70,
	0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdb, 0x01, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x4f, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x61, 0x76, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x61, 0x76, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x72, 0x65, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2c, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x22, 0x42, 0x0a, 0x12, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xd3, 0x02, 0x0a, 0x12, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x73, 0x70, 0x2e, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8c, 0x03, 0x0a,
	0x0f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x47, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0xaf, 0x02, 0x0a, 0x0b, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x04, 0x75, 0x69, 0x64,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x51, 0x0a,
	0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x1a, 0x39, 0x0a, 0x0b, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x11, 0x0a, 0x0f, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x22, 0x6d,
	0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa5, 0x06,
	0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x73, 0x70, 0x12, 0x42, 0x0a, 0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f,
	0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x1a, 0x1a, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x1a, 0xc1, 0x01, 0x0a, 0x0a, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x09, 0x70, 0x61, 0x79,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50,
	0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x51, 0x0a, 0x0c, 0x6d, 0x61, 0x6b, 0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x61, 0x6b, 0x65,
	0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x6d, 0x61, 0x6b, 0x65, 0x55, 0x70,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0xec, 0x03, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x08,
	0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x43, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x4d, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x44, 0x0a, 0x14, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x16, 0x0a, 0x14, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x73, 0x70, 0x22, 0x5f, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x22, 0x93, 0x04, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6f,
	0x6d, 0x52, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x73, 0x70, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x1a, 0x1a, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x1a, 0xf7, 0x02, 0x0a, 0x04, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x3e, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73,
	0x70, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x73, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x51,
	0x0a, 0x0c, 0x6d, 0x61, 0x6b, 0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x61, 0x6b, 0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0c, 0x6d, 0x61, 0x6b, 0x65, 0x55, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x48, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x09, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x08, 0x72,
	0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x49, 0x0a, 0x17, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x62, 0x0a, 0x17, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x47, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x43, 0x0a, 0x13, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x50, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x15,
	0x0a, 0x13, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x73, 0x70, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x4f, 0x0a, 0x0a, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65,
	0x74, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x15, 0x0a, 0x13,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x73, 0x70, 0x22, 0x32, 0x0a, 0x12, 0x4d, 0x61, 0x78, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x69,
	0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x22, 0x2c, 0x0a, 0x12, 0x4d, 0x61, 0x78, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x13, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x61,
	0x6c, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0a, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65,
	0x74, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x64, 0x0a, 0x13,
	0x4f, 0x70, 0x74, 0x69, 0x6d, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x43, 0x6f, 0x6e, 0x63, 0x72, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x92, 0x08, 0x0a, 0x0d, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x6f, 0x6f, 0x6c, 0x50,
	0x72, 0x6f, 0x78, 0x79, 0x12, 0x51, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x12, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x6c, 0x6c, 0x12, 0x24, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50,
	0x6f, 0x6c, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x0d,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x22, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f,
	0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x26, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x26, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x08, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f,
	0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6f,
	0x6d, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x14, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x60, 0x0a, 0x10, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x61, 0x75, 0x73,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x50, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x61, 0x75, 0x73, 0x65, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x73, 0x70, 0x12, 0x60, 0x0a, 0x10, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a,
	0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x73, 0x65, 0x74, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x0f, 0x4d, 0x61, 0x78, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x78,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x78, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x73, 0x70, 0x12, 0x60, 0x0a, 0x10, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x61, 0x6c,
	0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x25, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x42, 0x58, 0x5a, 0x56, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescOnce sync.Once
	file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescData = file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDesc
)

func file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescData)
	})
	return file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDescData
}

var file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_goTypes = []interface{}{
	(*PlayerMatchReq)(nil),                // 0: interactive_game.PlayerMatchReq
	(*PlayerMatchRsp)(nil),                // 1: interactive_game.PlayerMatchRsp
	(*PlayerMatchPollReq)(nil),            // 2: interactive_game.PlayerMatchPollReq
	(*PlayerMatchPollRsp)(nil),            // 3: interactive_game.PlayerMatchPollRsp
	(*MatchSuccessReq)(nil),               // 4: interactive_game.MatchSuccessReq
	(*MatchSuccessRsp)(nil),               // 5: interactive_game.MatchSuccessRsp
	(*ListMatchRoomReq)(nil),              // 6: interactive_game.ListMatchRoomReq
	(*ListMatchRoomRsp)(nil),              // 7: interactive_game.ListMatchRoomRsp
	(*PlayerCancelMatchReq)(nil),          // 8: interactive_game.PlayerCancelMatchReq
	(*PlayerCancelMatchRsp)(nil),          // 9: interactive_game.PlayerCancelMatchRsp
	(*ListRoomReq)(nil),                   // 10: interactive_game.ListRoomReq
	(*ListRoomRsp)(nil),                   // 11: interactive_game.ListRoomRsp
	(*PlayerMatchIntentionReq)(nil),       // 12: interactive_game.PlayerMatchIntentionReq
	(*PlayerMatchIntentionRsp)(nil),       // 13: interactive_game.PlayerMatchIntentionRsp
	(*PlayerPauseMatchReq)(nil),           // 14: interactive_game.PlayerPauseMatchReq
	(*PlayerPauseMatchRsp)(nil),           // 15: interactive_game.PlayerPauseMatchRsp
	(*PlayerResetMatchReq)(nil),           // 16: interactive_game.PlayerResetMatchReq
	(*PlayerResetMatchRsp)(nil),           // 17: interactive_game.PlayerResetMatchRsp
	(*MaxMatchingModeReq)(nil),            // 18: interactive_game.MaxMatchingModeReq
	(*MaxMatchingModeRsp)(nil),            // 19: interactive_game.MaxMatchingModeRsp
	(*OptimalIntentionReq)(nil),           // 20: interactive_game.OptimalIntentionReq
	(*OptimalIntentionRsp)(nil),           // 21: interactive_game.OptimalIntentionRsp
	nil,                                   // 22: interactive_game.PlayerMatchPollRsp.ExtendEntry
	(*MatchSuccessReq_MatchResult)(nil),   // 23: interactive_game.MatchSuccessReq.MatchResult
	nil,                                   // 24: interactive_game.MatchSuccessReq.MatchResult.ExtendEntry
	(*ListMatchRoomRsp_Player)(nil),       // 25: interactive_game.ListMatchRoomRsp.Player
	(*ListMatchRoomRsp_RoomConfig)(nil),   // 26: interactive_game.ListMatchRoomRsp.RoomConfig
	(*ListMatchRoomRsp_MatchRoom)(nil),    // 27: interactive_game.ListMatchRoomRsp.MatchRoom
	(*ListRoomRsp_Player)(nil),            // 28: interactive_game.ListRoomRsp.Player
	(*ListRoomRsp_Room)(nil),              // 29: interactive_game.ListRoomRsp.Room
	(*common.ConcreteMatchIntention)(nil), // 30: interactive_game.common.ConcreteMatchIntention
	(common.AppMatchType)(0),              // 31: interactive_game.common.AppMatchType
	(*common.MatchIntention)(nil),         // 32: interactive_game.common.MatchIntention
	(*common.GameRoomPayConfig)(nil),      // 33: interactive_game.common.GameRoomPayConfig
	(*common.GameRoomMakeUpConfig)(nil),   // 34: interactive_game.common.GameRoomMakeUpConfig
	(common.RoomStatus)(0),                // 35: interactive_game.common.RoomStatus
	(common.RoomType)(0),                  // 36: interactive_game.common.RoomType
}
var file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_depIdxs = []int32{
	30, // 0: interactive_game.PlayerMatchReq.intentions:type_name -> interactive_game.common.ConcreteMatchIntention
	22, // 1: interactive_game.PlayerMatchPollRsp.extend:type_name -> interactive_game.PlayerMatchPollRsp.ExtendEntry
	23, // 2: interactive_game.MatchSuccessReq.results:type_name -> interactive_game.MatchSuccessReq.MatchResult
	31, // 3: interactive_game.ListMatchRoomReq.matchType:type_name -> interactive_game.common.AppMatchType
	27, // 4: interactive_game.ListMatchRoomRsp.rooms:type_name -> interactive_game.ListMatchRoomRsp.MatchRoom
	29, // 5: interactive_game.ListRoomRsp.rooms:type_name -> interactive_game.ListRoomRsp.Room
	32, // 6: interactive_game.PlayerMatchIntentionRsp.intentions:type_name -> interactive_game.common.MatchIntention
	30, // 7: interactive_game.PlayerResetMatchReq.intentions:type_name -> interactive_game.common.ConcreteMatchIntention
	30, // 8: interactive_game.OptimalIntentionReq.intentions:type_name -> interactive_game.common.ConcreteMatchIntention
	30, // 9: interactive_game.OptimalIntentionRsp.intention:type_name -> interactive_game.common.ConcreteMatchIntention
	24, // 10: interactive_game.MatchSuccessReq.MatchResult.extend:type_name -> interactive_game.MatchSuccessReq.MatchResult.ExtendEntry
	33, // 11: interactive_game.ListMatchRoomRsp.RoomConfig.payConfig:type_name -> interactive_game.common.GameRoomPayConfig
	34, // 12: interactive_game.ListMatchRoomRsp.RoomConfig.makeUpConfig:type_name -> interactive_game.common.GameRoomMakeUpConfig
	35, // 13: interactive_game.ListMatchRoomRsp.MatchRoom.status:type_name -> interactive_game.common.RoomStatus
	26, // 14: interactive_game.ListMatchRoomRsp.MatchRoom.config:type_name -> interactive_game.ListMatchRoomRsp.RoomConfig
	36, // 15: interactive_game.ListMatchRoomRsp.MatchRoom.roomType:type_name -> interactive_game.common.RoomType
	25, // 16: interactive_game.ListMatchRoomRsp.MatchRoom.players:type_name -> interactive_game.ListMatchRoomRsp.Player
	26, // 17: interactive_game.ListMatchRoomRsp.MatchRoom.intentions:type_name -> interactive_game.ListMatchRoomRsp.RoomConfig
	28, // 18: interactive_game.ListRoomRsp.Room.players:type_name -> interactive_game.ListRoomRsp.Player
	35, // 19: interactive_game.ListRoomRsp.Room.status:type_name -> interactive_game.common.RoomStatus
	34, // 20: interactive_game.ListRoomRsp.Room.makeUpConfig:type_name -> interactive_game.common.GameRoomMakeUpConfig
	33, // 21: interactive_game.ListRoomRsp.Room.payConfig:type_name -> interactive_game.common.GameRoomPayConfig
	36, // 22: interactive_game.ListRoomRsp.Room.roomType:type_name -> interactive_game.common.RoomType
	0,  // 23: interactive_game.RoomPoolProxy.PlayerMatch:input_type -> interactive_game.PlayerMatchReq
	2,  // 24: interactive_game.RoomPoolProxy.PlayerMatchPoll:input_type -> interactive_game.PlayerMatchPollReq
	4,  // 25: interactive_game.RoomPoolProxy.MatchSuccess:input_type -> interactive_game.MatchSuccessReq
	6,  // 26: interactive_game.RoomPoolProxy.ListMatchRoom:input_type -> interactive_game.ListMatchRoomReq
	8,  // 27: interactive_game.RoomPoolProxy.PlayerCancelMatch:input_type -> interactive_game.PlayerCancelMatchReq
	10, // 28: interactive_game.RoomPoolProxy.ListRoom:input_type -> interactive_game.ListRoomReq
	12, // 29: interactive_game.RoomPoolProxy.PlayerMatchIntention:input_type -> interactive_game.PlayerMatchIntentionReq
	14, // 30: interactive_game.RoomPoolProxy.PlayerPauseMatch:input_type -> interactive_game.PlayerPauseMatchReq
	16, // 31: interactive_game.RoomPoolProxy.PlayerResetMatch:input_type -> interactive_game.PlayerResetMatchReq
	18, // 32: interactive_game.RoomPoolProxy.MaxMatchingMode:input_type -> interactive_game.MaxMatchingModeReq
	20, // 33: interactive_game.RoomPoolProxy.OptimalIntention:input_type -> interactive_game.OptimalIntentionReq
	1,  // 34: interactive_game.RoomPoolProxy.PlayerMatch:output_type -> interactive_game.PlayerMatchRsp
	3,  // 35: interactive_game.RoomPoolProxy.PlayerMatchPoll:output_type -> interactive_game.PlayerMatchPollRsp
	5,  // 36: interactive_game.RoomPoolProxy.MatchSuccess:output_type -> interactive_game.MatchSuccessRsp
	7,  // 37: interactive_game.RoomPoolProxy.ListMatchRoom:output_type -> interactive_game.ListMatchRoomRsp
	9,  // 38: interactive_game.RoomPoolProxy.PlayerCancelMatch:output_type -> interactive_game.PlayerCancelMatchRsp
	11, // 39: interactive_game.RoomPoolProxy.ListRoom:output_type -> interactive_game.ListRoomRsp
	13, // 40: interactive_game.RoomPoolProxy.PlayerMatchIntention:output_type -> interactive_game.PlayerMatchIntentionRsp
	15, // 41: interactive_game.RoomPoolProxy.PlayerPauseMatch:output_type -> interactive_game.PlayerPauseMatchRsp
	17, // 42: interactive_game.RoomPoolProxy.PlayerResetMatch:output_type -> interactive_game.PlayerResetMatchRsp
	19, // 43: interactive_game.RoomPoolProxy.MaxMatchingMode:output_type -> interactive_game.MaxMatchingModeRsp
	21, // 44: interactive_game.RoomPoolProxy.OptimalIntention:output_type -> interactive_game.OptimalIntentionRsp
	34, // [34:45] is the sub-list for method output_type
	23, // [23:34] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_init() }
func file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_init() {
	if File_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMatchPollReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMatchPollRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchSuccessReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchSuccessRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMatchRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMatchRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerCancelMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerCancelMatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMatchIntentionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMatchIntentionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerPauseMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerPauseMatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerResetMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerResetMatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaxMatchingModeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaxMatchingModeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptimalIntentionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptimalIntentionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchSuccessReq_MatchResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMatchRoomRsp_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMatchRoomRsp_RoomConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMatchRoomRsp_MatchRoom); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRoomRsp_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRoomRsp_Room); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto = out.File
	file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_rawDesc = nil
	file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_goTypes = nil
	file_pb_interactive_game_room_pool_proxy_room_pool_proxy_proto_depIdxs = nil
}
