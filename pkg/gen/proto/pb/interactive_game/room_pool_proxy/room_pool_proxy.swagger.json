{"swagger": "2.0", "info": {"title": "pb/interactive_game/room_pool_proxy/room_pool_proxy.proto", "version": "version not set"}, "tags": [{"name": "RoomPoolProxy"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.RoomPoolProxy/ListMatchRoom": {"post": {"summary": "匹配玩家池", "operationId": "RoomPoolProxy_ListMatchRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameListMatchRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameListMatchRoomReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/ListRoom": {"post": {"summary": "房间列表", "operationId": "RoomPoolProxy_ListRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameListRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameListRoomReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/MatchSuccess": {"post": {"summary": "匹配成功", "operationId": "RoomPoolProxy_MatchSuccess", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameMatchSuccessRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameMatchSuccessReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/MaxMatchingMode": {"post": {"summary": "最大匹配模式", "operationId": "RoomPoolProxy_MaxMatchingMode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameMaxMatchingModeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameMaxMatchingModeReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/OptimalIntention": {"post": {"summary": "最优模式", "operationId": "RoomPoolProxy_OptimalIntention", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameOptimalIntentionRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameOptimalIntentionReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/PlayerCancelMatch": {"post": {"summary": "玩家取消匹配", "operationId": "RoomPoolProxy_PlayerCancelMatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayerCancelMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayerCancelMatchReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/PlayerMatch": {"post": {"summary": "玩家匹配", "operationId": "RoomPoolProxy_PlayerMatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayerMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayerMatchReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/PlayerMatchIntention": {"post": {"summary": "匹配历史", "operationId": "RoomPoolProxy_PlayerMatchIntention", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayerMatchIntentionRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayerMatchIntentionReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/PlayerMatchPoll": {"post": {"summary": "玩家匹配轮询", "operationId": "RoomPoolProxy_PlayerMatchPoll", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayerMatchPollRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayerMatchPollReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/PlayerPauseMatch": {"post": {"summary": "玩家暂停匹配", "operationId": "RoomPoolProxy_PlayerPauseMatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayerPauseMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayerPauseMatchReq"}}], "tags": ["RoomPoolProxy"]}}, "/interactive_game.RoomPoolProxy/PlayerResetMatch": {"post": {"summary": "玩家重新设置匹配", "operationId": "RoomPoolProxy_PlayerResetMatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayerResetMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayerResetMatchReq"}}], "tags": ["RoomPoolProxy"]}}}, "definitions": {"ListMatchRoomRspMatchRoom": {"type": "object", "properties": {"status": {"$ref": "#/definitions/commonRoomStatus"}, "owner": {"type": "string", "format": "uint64"}, "gameAppId": {"type": "string"}, "config": {"$ref": "#/definitions/ListMatchRoomRspRoomConfig"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType"}, "updateTime": {"type": "string", "format": "int64"}, "matchTime": {"type": "string", "format": "int64"}, "roomId": {"type": "string"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameListMatchRoomRspPlayer"}}, "intentions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ListMatchRoomRspRoomConfig"}}}}, "ListMatchRoomRspRoomConfig": {"type": "object", "properties": {"modeId": {"type": "string"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig"}, "makeUpConfig": {"$ref": "#/definitions/commonGameRoomMakeUpConfig"}}}, "ListRoomRspRoom": {"type": "object", "properties": {"roomId": {"type": "string"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameListRoomRspPlayer"}}, "status": {"$ref": "#/definitions/commonRoomStatus"}, "makeUpConfig": {"$ref": "#/definitions/commonGameRoomMakeUpConfig"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType"}}}, "MatchSuccessReqMatchResult": {"type": "object", "properties": {"roomId": {"type": "string"}, "roomType": {"type": "integer", "format": "int32"}, "uids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "appId": {"type": "string"}, "modeId": {"type": "string"}, "payModeId": {"type": "string"}, "extend": {"type": "object", "additionalProperties": {"type": "string"}}}}, "commonAppMatchType": {"type": "string", "enum": ["AppMatchTypeNone", "AppMatchTypeSimple", "AppMatchTypeRecommend"], "default": "AppMatchTypeNone", "title": "- AppMatchTypeNone: 无\n - AppMatchTypeSimple: 业务\n - AppMatchTypeRecommend: 推荐"}, "commonConcreteMatchIntention": {"type": "object", "properties": {"modeId": {"type": "string"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig"}, "disable": {"type": "boolean"}}}, "commonGameRoomMakeUpConfig": {"type": "object", "properties": {"minPlayers": {"type": "integer", "format": "int64", "title": "最少开始人数"}, "maxPlayers": {"type": "integer", "format": "int64", "title": "最大加入人数"}, "groupNum": {"type": "integer", "format": "int64", "description": "队伍人数", "title": "BeginPrivilege beginPrivilege = 3;\nbool ownerJoin = 4; // 房主必须参与游戏"}, "gameConfigs": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传配置"}}}, "commonGameRoomPayConfig": {"type": "object", "properties": {"mode": {"$ref": "#/definitions/commonPayMode"}, "assetId": {"type": "string", "format": "int64", "title": "扣费资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "扣费资产数量"}, "payModeName": {"type": "string", "title": "扣费模式名称"}, "payModeId": {"type": "string", "title": "扣费模式 id"}}}, "commonMatchIntention": {"type": "object", "properties": {"modeId": {"type": "string"}, "payModeId": {"type": "string"}}}, "commonPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "commonRoomStatus": {"type": "string", "enum": ["RoomNone", "RoomPending", "RoomPlaying"], "default": "RoomNone", "title": "- RoomPending: 组局中\n - RoomPlaying: 游戏中"}, "interactive_gameListMatchRoomReq": {"type": "object", "properties": {"appId": {"type": "string"}, "matchType": {"$ref": "#/definitions/commonAppMatchType"}}}, "interactive_gameListMatchRoomRsp": {"type": "object", "properties": {"rooms": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ListMatchRoomRspMatchRoom"}}}}, "interactive_gameListMatchRoomRspPlayer": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}}}, "interactive_gameListRoomReq": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "passback": {"type": "string"}, "modeId": {"type": "string"}}}, "interactive_gameListRoomRsp": {"type": "object", "properties": {"rooms": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/ListRoomRspRoom"}}, "hasMore": {"type": "boolean"}, "passback": {"type": "string"}}}, "interactive_gameListRoomRspPlayer": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}}}, "interactive_gameMatchSuccessReq": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/MatchSuccessReqMatchResult"}}}}, "interactive_gameMatchSuccessRsp": {"type": "object"}, "interactive_gameMaxMatchingModeReq": {"type": "object", "properties": {"gameAppId": {"type": "string"}}}, "interactive_gameMaxMatchingModeRsp": {"type": "object", "properties": {"modeId": {"type": "string"}}}, "interactive_gameOptimalIntentionReq": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "intentions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonConcreteMatchIntention"}}}}, "interactive_gameOptimalIntentionRsp": {"type": "object", "properties": {"intention": {"$ref": "#/definitions/commonConcreteMatchIntention"}}}, "interactive_gamePlayerCancelMatchReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "passback": {"type": "string"}}}, "interactive_gamePlayerCancelMatchRsp": {"type": "object"}, "interactive_gamePlayerMatchIntentionReq": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}}}, "interactive_gamePlayerMatchIntentionRsp": {"type": "object", "properties": {"intentions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonMatchIntention"}}}}, "interactive_gamePlayerMatchPollReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "passback": {"type": "string"}}}, "interactive_gamePlayerMatchPollRsp": {"type": "object", "properties": {"passback": {"type": "string"}, "roomId": {"type": "string"}, "backup": {"type": "boolean"}, "modeId": {"type": "string"}, "payModeId": {"type": "string"}, "appId": {"type": "string"}, "matchSource": {"type": "integer", "format": "int32"}, "extend": {"type": "object", "additionalProperties": {"type": "string"}}}}, "interactive_gamePlayerMatchReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "appId": {"type": "string"}, "intentions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonConcreteMatchIntention"}}, "saveIntention": {"type": "boolean"}, "replaceIntention": {"type": "boolean"}}}, "interactive_gamePlayerMatchRsp": {"type": "object", "properties": {"passback": {"type": "string"}}}, "interactive_gamePlayerPauseMatchReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "passback": {"type": "string"}}}, "interactive_gamePlayerPauseMatchRsp": {"type": "object"}, "interactive_gamePlayerResetMatchReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "passback": {"type": "string"}, "intentions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonConcreteMatchIntention"}}}}, "interactive_gamePlayerResetMatchRsp": {"type": "object"}, "interactive_gamecommonRoomType": {"type": "string", "enum": ["TypeNone", "TypeKtv", "TypeTwins", "TypeLive", "TypeMatch"], "default": "TypeNone", "title": "- TypeKtv: 歌房\n - TypeTwins: 双人房\n - TypeLive: 直播\n - TypeMatch: 匹配房"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}