// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/app_rank_admin/app_rank_admin.proto

package app_rank_admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AppRankAdmin_AppRank_FullMethodName      = "/interactive_game.AppRankAdmin/AppRank"
	AppRankAdmin_AppRankItems_FullMethodName = "/interactive_game.AppRankAdmin/AppRankItems"
)

// AppRankAdminClient is the client API for AppRankAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppRankAdminClient interface {
	// 查询榜单
	AppRank(ctx context.Context, in *AppRankReq, opts ...grpc.CallOption) (*AppRankRsp, error)
	// 查询榜单项
	AppRankItems(ctx context.Context, in *AppRankItemsReq, opts ...grpc.CallOption) (*AppRankItemsRsp, error)
}

type appRankAdminClient struct {
	cc grpc.ClientConnInterface
}

func NewAppRankAdminClient(cc grpc.ClientConnInterface) AppRankAdminClient {
	return &appRankAdminClient{cc}
}

func (c *appRankAdminClient) AppRank(ctx context.Context, in *AppRankReq, opts ...grpc.CallOption) (*AppRankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AppRankRsp)
	err := c.cc.Invoke(ctx, AppRankAdmin_AppRank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appRankAdminClient) AppRankItems(ctx context.Context, in *AppRankItemsReq, opts ...grpc.CallOption) (*AppRankItemsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AppRankItemsRsp)
	err := c.cc.Invoke(ctx, AppRankAdmin_AppRankItems_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppRankAdminServer is the server API for AppRankAdmin service.
// All implementations should embed UnimplementedAppRankAdminServer
// for forward compatibility
type AppRankAdminServer interface {
	// 查询榜单
	AppRank(context.Context, *AppRankReq) (*AppRankRsp, error)
	// 查询榜单项
	AppRankItems(context.Context, *AppRankItemsReq) (*AppRankItemsRsp, error)
}

// UnimplementedAppRankAdminServer should be embedded to have forward compatible implementations.
type UnimplementedAppRankAdminServer struct {
}

func (UnimplementedAppRankAdminServer) AppRank(context.Context, *AppRankReq) (*AppRankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AppRank not implemented")
}
func (UnimplementedAppRankAdminServer) AppRankItems(context.Context, *AppRankItemsReq) (*AppRankItemsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AppRankItems not implemented")
}

// UnsafeAppRankAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppRankAdminServer will
// result in compilation errors.
type UnsafeAppRankAdminServer interface {
	mustEmbedUnimplementedAppRankAdminServer()
}

func RegisterAppRankAdminServer(s grpc.ServiceRegistrar, srv AppRankAdminServer) {
	s.RegisterService(&AppRankAdmin_ServiceDesc, srv)
}

func _AppRankAdmin_AppRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppRankAdminServer).AppRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppRankAdmin_AppRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppRankAdminServer).AppRank(ctx, req.(*AppRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppRankAdmin_AppRankItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AppRankItemsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppRankAdminServer).AppRankItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppRankAdmin_AppRankItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppRankAdminServer).AppRankItems(ctx, req.(*AppRankItemsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AppRankAdmin_ServiceDesc is the grpc.ServiceDesc for AppRankAdmin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppRankAdmin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.AppRankAdmin",
	HandlerType: (*AppRankAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AppRank",
			Handler:    _AppRankAdmin_AppRank_Handler,
		},
		{
			MethodName: "AppRankItems",
			Handler:    _AppRankAdmin_AppRankItems_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/app_rank_admin/app_rank_admin.proto",
}
