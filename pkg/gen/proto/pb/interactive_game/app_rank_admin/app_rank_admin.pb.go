// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/app_rank_admin/app_rank_admin.proto

package app_rank_admin

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AppRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid          uint64                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"` // 主人态 uid
	AppId        string                  `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Timestamp    int64                   `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"` // 时间周期 传 0 查当前周期
	PeriodicType common.RankPeriodicType `protobuf:"varint,4,opt,name=periodicType,proto3,enum=interactive_game.common.RankPeriodicType" json:"periodicType,omitempty"`
	PayMode      common.PayMode          `protobuf:"varint,5,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"`
	Passback     string                  `protobuf:"bytes,6,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *AppRankReq) Reset() {
	*x = AppRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRankReq) ProtoMessage() {}

func (x *AppRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRankReq.ProtoReflect.Descriptor instead.
func (*AppRankReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP(), []int{0}
}

func (x *AppRankReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *AppRankReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppRankReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AppRankReq) GetPeriodicType() common.RankPeriodicType {
	if x != nil {
		return x.PeriodicType
	}
	return common.RankPeriodicType(0)
}

func (x *AppRankReq) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

func (x *AppRankReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type AppRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items       []*AppRankRsp_Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	RankSize    int64              `protobuf:"varint,2,opt,name=rankSize,proto3" json:"rankSize,omitempty"`   // 排行榜人数
	RankBonus   int64              `protobuf:"varint,3,opt,name=rankBonus,proto3" json:"rankBonus,omitempty"` // 排行榜分成
	Current     *AppRankRsp_Item   `protobuf:"bytes,4,opt,name=current,proto3" json:"current,omitempty"`
	HasMore     bool               `protobuf:"varint,5,opt,name=hasMore,proto3" json:"hasMore,omitempty"`
	Passback    string             `protobuf:"bytes,6,opt,name=passback,proto3" json:"passback,omitempty"`
	BeginTime   int64              `protobuf:"varint,7,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime     int64              `protobuf:"varint,8,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Predecessor *AppRankRsp_Item   `protobuf:"bytes,9,opt,name=predecessor,proto3" json:"predecessor,omitempty"`
	Freeze      bool               `protobuf:"varint,10,opt,name=freeze,proto3" json:"freeze,omitempty"`
	AppName     string             `protobuf:"bytes,11,opt,name=appName,proto3" json:"appName,omitempty"`
}

func (x *AppRankRsp) Reset() {
	*x = AppRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRankRsp) ProtoMessage() {}

func (x *AppRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRankRsp.ProtoReflect.Descriptor instead.
func (*AppRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP(), []int{1}
}

func (x *AppRankRsp) GetItems() []*AppRankRsp_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *AppRankRsp) GetRankSize() int64 {
	if x != nil {
		return x.RankSize
	}
	return 0
}

func (x *AppRankRsp) GetRankBonus() int64 {
	if x != nil {
		return x.RankBonus
	}
	return 0
}

func (x *AppRankRsp) GetCurrent() *AppRankRsp_Item {
	if x != nil {
		return x.Current
	}
	return nil
}

func (x *AppRankRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *AppRankRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *AppRankRsp) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *AppRankRsp) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *AppRankRsp) GetPredecessor() *AppRankRsp_Item {
	if x != nil {
		return x.Predecessor
	}
	return nil
}

func (x *AppRankRsp) GetFreeze() bool {
	if x != nil {
		return x.Freeze
	}
	return false
}

func (x *AppRankRsp) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

type AppRankItemsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid           uint64         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AppId         string         `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`
	Timestamp     int64          `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	FilterPayMode bool           `protobuf:"varint,4,opt,name=filterPayMode,proto3" json:"filterPayMode,omitempty"`
	PayMode       common.PayMode `protobuf:"varint,5,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"`
}

func (x *AppRankItemsReq) Reset() {
	*x = AppRankItemsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRankItemsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRankItemsReq) ProtoMessage() {}

func (x *AppRankItemsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRankItemsReq.ProtoReflect.Descriptor instead.
func (*AppRankItemsReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP(), []int{2}
}

func (x *AppRankItemsReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *AppRankItemsReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AppRankItemsReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *AppRankItemsReq) GetFilterPayMode() bool {
	if x != nil {
		return x.FilterPayMode
	}
	return false
}

func (x *AppRankItemsReq) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

type AppRankItemsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*AppRankItemsRsp_RankItem `protobuf:"bytes,1,rep,name=Items,proto3" json:"Items,omitempty"`
}

func (x *AppRankItemsRsp) Reset() {
	*x = AppRankItemsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRankItemsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRankItemsRsp) ProtoMessage() {}

func (x *AppRankItemsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRankItemsRsp.ProtoReflect.Descriptor instead.
func (*AppRankItemsRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP(), []int{3}
}

func (x *AppRankItemsRsp) GetItems() []*AppRankItemsRsp_RankItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type AppRankRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank  int64  `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"` // 排名
	Uid   uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Score int64  `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
}

func (x *AppRankRsp_Item) Reset() {
	*x = AppRankRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRankRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRankRsp_Item) ProtoMessage() {}

func (x *AppRankRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRankRsp_Item.ProtoReflect.Descriptor instead.
func (*AppRankRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP(), []int{1, 0}
}

func (x *AppRankRsp_Item) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *AppRankRsp_Item) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *AppRankRsp_Item) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type AppRankItemsRsp_RankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserRank     int64                   `protobuf:"varint,1,opt,name=userRank,proto3" json:"userRank,omitempty"`
	UserScore    int64                   `protobuf:"varint,2,opt,name=userScore,proto3" json:"userScore,omitempty"`
	Bonus        int64                   `protobuf:"varint,3,opt,name=bonus,proto3" json:"bonus,omitempty"`
	Cycle        int64                   `protobuf:"varint,4,opt,name=cycle,proto3" json:"cycle,omitempty"`
	PeriodicType common.RankPeriodicType `protobuf:"varint,5,opt,name=periodicType,proto3,enum=interactive_game.common.RankPeriodicType" json:"periodicType,omitempty"`
	PayMode      common.PayMode          `protobuf:"varint,6,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"`
}

func (x *AppRankItemsRsp_RankItem) Reset() {
	*x = AppRankItemsRsp_RankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppRankItemsRsp_RankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppRankItemsRsp_RankItem) ProtoMessage() {}

func (x *AppRankItemsRsp_RankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppRankItemsRsp_RankItem.ProtoReflect.Descriptor instead.
func (*AppRankItemsRsp_RankItem) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AppRankItemsRsp_RankItem) GetUserRank() int64 {
	if x != nil {
		return x.UserRank
	}
	return 0
}

func (x *AppRankItemsRsp_RankItem) GetUserScore() int64 {
	if x != nil {
		return x.UserScore
	}
	return 0
}

func (x *AppRankItemsRsp_RankItem) GetBonus() int64 {
	if x != nil {
		return x.Bonus
	}
	return 0
}

func (x *AppRankItemsRsp_RankItem) GetCycle() int64 {
	if x != nil {
		return x.Cycle
	}
	return 0
}

func (x *AppRankItemsRsp_RankItem) GetPeriodicType() common.RankPeriodicType {
	if x != nil {
		return x.PeriodicType
	}
	return common.RankPeriodicType(0)
}

func (x *AppRankItemsRsp_RankItem) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

var File_pb_interactive_game_app_rank_admin_app_rank_admin_proto protoreflect.FileDescriptor

var file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDesc = []byte{
	0x0a, 0x37, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf9, 0x01, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b,
	0x22, 0xe5, 0x03, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e, 0x6b,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x42, 0x6f, 0x6e, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x42, 0x6f, 0x6e,
	0x75, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73,
	0x70, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x43, 0x0a,
	0x0b, 0x70, 0x72, 0x65, 0x64, 0x65, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x64, 0x65, 0x63, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x66, 0x72, 0x65, 0x65, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x70,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x42, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xb9, 0x01, 0x0a, 0x0f, 0x41, 0x70, 0x70,
	0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x50, 0x61, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x22, 0xd1, 0x02, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61,
	0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0xfb, 0x01, 0x0a, 0x08, 0x52,
	0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x6f, 0x6e, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x62, 0x6f, 0x6e, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x4d, 0x0a,
	0x0c, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61,
	0x6e, 0x6b, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07,
	0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x32, 0xab, 0x01, 0x0a, 0x0c, 0x41, 0x70, 0x70,
	0x52, 0x61, 0x6e, 0x6b, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x45, 0x0a, 0x07, 0x41, 0x70, 0x70,
	0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70,
	0x12, 0x54, 0x0a, 0x0c, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x52, 0x73, 0x70, 0x42, 0x57, 0x5a, 0x55, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x61, 0x70, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescOnce sync.Once
	file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescData = file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDesc
)

func file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescData)
	})
	return file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDescData
}

var file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_goTypes = []interface{}{
	(*AppRankReq)(nil),               // 0: interactive_game.AppRankReq
	(*AppRankRsp)(nil),               // 1: interactive_game.AppRankRsp
	(*AppRankItemsReq)(nil),          // 2: interactive_game.AppRankItemsReq
	(*AppRankItemsRsp)(nil),          // 3: interactive_game.AppRankItemsRsp
	(*AppRankRsp_Item)(nil),          // 4: interactive_game.AppRankRsp.Item
	(*AppRankItemsRsp_RankItem)(nil), // 5: interactive_game.AppRankItemsRsp.RankItem
	(common.RankPeriodicType)(0),     // 6: interactive_game.common.RankPeriodicType
	(common.PayMode)(0),              // 7: interactive_game.common.PayMode
}
var file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_depIdxs = []int32{
	6,  // 0: interactive_game.AppRankReq.periodicType:type_name -> interactive_game.common.RankPeriodicType
	7,  // 1: interactive_game.AppRankReq.payMode:type_name -> interactive_game.common.PayMode
	4,  // 2: interactive_game.AppRankRsp.items:type_name -> interactive_game.AppRankRsp.Item
	4,  // 3: interactive_game.AppRankRsp.current:type_name -> interactive_game.AppRankRsp.Item
	4,  // 4: interactive_game.AppRankRsp.predecessor:type_name -> interactive_game.AppRankRsp.Item
	7,  // 5: interactive_game.AppRankItemsReq.payMode:type_name -> interactive_game.common.PayMode
	5,  // 6: interactive_game.AppRankItemsRsp.Items:type_name -> interactive_game.AppRankItemsRsp.RankItem
	6,  // 7: interactive_game.AppRankItemsRsp.RankItem.periodicType:type_name -> interactive_game.common.RankPeriodicType
	7,  // 8: interactive_game.AppRankItemsRsp.RankItem.payMode:type_name -> interactive_game.common.PayMode
	0,  // 9: interactive_game.AppRankAdmin.AppRank:input_type -> interactive_game.AppRankReq
	2,  // 10: interactive_game.AppRankAdmin.AppRankItems:input_type -> interactive_game.AppRankItemsReq
	1,  // 11: interactive_game.AppRankAdmin.AppRank:output_type -> interactive_game.AppRankRsp
	3,  // 12: interactive_game.AppRankAdmin.AppRankItems:output_type -> interactive_game.AppRankItemsRsp
	11, // [11:13] is the sub-list for method output_type
	9,  // [9:11] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_init() }
func file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_init() {
	if File_pb_interactive_game_app_rank_admin_app_rank_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRankItemsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRankItemsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRankRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppRankItemsRsp_RankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_app_rank_admin_app_rank_admin_proto = out.File
	file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_rawDesc = nil
	file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_goTypes = nil
	file_pb_interactive_game_app_rank_admin_app_rank_admin_proto_depIdxs = nil
}
