{"swagger": "2.0", "info": {"title": "pb/interactive_game/rank/rank.proto", "version": "version not set"}, "tags": [{"name": "Rank"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.Rank/BatchRankInfo": {"post": {"summary": "榜单信息", "operationId": "Rank_BatchRankInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameBatchRankInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameBatchRankInfoReq"}}], "tags": ["Rank"]}}, "/interactive_game.Rank/QueryRank": {"post": {"summary": "查询榜单", "operationId": "Rank_QueryRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameQueryRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameQueryRankReq"}}], "tags": ["Rank"]}}, "/interactive_game.Rank/QueryRankItems": {"post": {"summary": "查询榜单项", "operationId": "Rank_QueryRankItems", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameQueryRankItemsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameQueryRankItemsReq"}}], "tags": ["Rank"]}}}, "definitions": {"BatchRankInfoRspRank": {"type": "object", "properties": {"inProgress": {"type": "boolean"}, "cycle": {"type": "string", "format": "int64"}, "beginTime": {"type": "string", "format": "int64"}, "endTime": {"type": "string", "format": "int64"}, "visible": {"type": "boolean", "title": "可见"}}}, "QueryRankItemsRspRankItem": {"type": "object", "properties": {"userRank": {"type": "string", "format": "int64"}, "userScore": {"type": "string", "format": "int64"}, "cycle": {"type": "string", "format": "int64"}, "fields": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}}}}, "QueryRankRspItem": {"type": "object", "properties": {"rank": {"type": "string", "format": "int64", "title": "排名"}, "openId": {"type": "string"}, "score": {"type": "string", "format": "int64"}}}, "interactive_gameBatchRankInfoReq": {"type": "object", "properties": {"configIds": {"type": "array", "items": {"type": "string", "format": "int64"}}, "timestamp": {"type": "string", "format": "int64"}, "roomId": {"type": "string"}}}, "interactive_gameBatchRankInfoRsp": {"type": "object", "properties": {"ranks": {"type": "object", "additionalProperties": {"$ref": "#/definitions/BatchRankInfoRspRank"}}}}, "interactive_gameQueryRankItemsReq": {"type": "object", "properties": {"openId": {"type": "string"}, "appId": {"type": "string"}, "timestamp": {"type": "string", "format": "int64"}, "configIds": {"type": "array", "items": {"type": "string", "format": "int64"}}, "fields": {"type": "array", "items": {"type": "string"}}, "roomId": {"type": "string"}}}, "interactive_gameQueryRankItemsRsp": {"type": "object", "properties": {"items": {"type": "object", "additionalProperties": {"$ref": "#/definitions/QueryRankItemsRspRankItem"}}}}, "interactive_gameQueryRankReq": {"type": "object", "properties": {"openId": {"type": "string", "title": "主人态 uid"}, "appId": {"type": "string"}, "timestamp": {"type": "string", "format": "int64", "title": "时间周期 传 0 查当前周期"}, "configId": {"type": "string", "format": "int64"}, "fields": {"type": "array", "items": {"type": "string"}}, "passback": {"type": "string"}}}, "interactive_gameQueryRankRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QueryRankRspItem"}}, "rankSize": {"type": "string", "format": "int64", "title": "排行榜人数"}, "current": {"$ref": "#/definitions/QueryRankRspItem"}, "hasMore": {"type": "boolean"}, "passback": {"type": "string"}, "beginTime": {"type": "string", "format": "int64"}, "endTime": {"type": "string", "format": "int64"}, "predecessor": {"$ref": "#/definitions/QueryRankRspItem", "title": "不一定会返回"}, "freeze": {"type": "boolean"}, "fields": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}