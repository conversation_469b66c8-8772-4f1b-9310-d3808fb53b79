// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/rank/rank.proto

package rank

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Rank_QueryRank_FullMethodName      = "/interactive_game.Rank/QueryRank"
	Rank_QueryRankItems_FullMethodName = "/interactive_game.Rank/QueryRankItems"
	Rank_BatchRankInfo_FullMethodName  = "/interactive_game.Rank/BatchRankInfo"
)

// RankClient is the client API for Rank service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankClient interface {
	// 查询榜单
	QueryRank(ctx context.Context, in *QueryRankReq, opts ...grpc.CallOption) (*QueryRankRsp, error)
	// 查询榜单项
	QueryRankItems(ctx context.Context, in *QueryRankItemsReq, opts ...grpc.CallOption) (*QueryRankItemsRsp, error)
	// 榜单信息
	BatchRankInfo(ctx context.Context, in *BatchRankInfoReq, opts ...grpc.CallOption) (*BatchRankInfoRsp, error)
}

type rankClient struct {
	cc grpc.ClientConnInterface
}

func NewRankClient(cc grpc.ClientConnInterface) RankClient {
	return &rankClient{cc}
}

func (c *rankClient) QueryRank(ctx context.Context, in *QueryRankReq, opts ...grpc.CallOption) (*QueryRankRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRankRsp)
	err := c.cc.Invoke(ctx, Rank_QueryRank_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankClient) QueryRankItems(ctx context.Context, in *QueryRankItemsReq, opts ...grpc.CallOption) (*QueryRankItemsRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryRankItemsRsp)
	err := c.cc.Invoke(ctx, Rank_QueryRankItems_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankClient) BatchRankInfo(ctx context.Context, in *BatchRankInfoReq, opts ...grpc.CallOption) (*BatchRankInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchRankInfoRsp)
	err := c.cc.Invoke(ctx, Rank_BatchRankInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankServer is the server API for Rank service.
// All implementations should embed UnimplementedRankServer
// for forward compatibility
type RankServer interface {
	// 查询榜单
	QueryRank(context.Context, *QueryRankReq) (*QueryRankRsp, error)
	// 查询榜单项
	QueryRankItems(context.Context, *QueryRankItemsReq) (*QueryRankItemsRsp, error)
	// 榜单信息
	BatchRankInfo(context.Context, *BatchRankInfoReq) (*BatchRankInfoRsp, error)
}

// UnimplementedRankServer should be embedded to have forward compatible implementations.
type UnimplementedRankServer struct {
}

func (UnimplementedRankServer) QueryRank(context.Context, *QueryRankReq) (*QueryRankRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRank not implemented")
}
func (UnimplementedRankServer) QueryRankItems(context.Context, *QueryRankItemsReq) (*QueryRankItemsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryRankItems not implemented")
}
func (UnimplementedRankServer) BatchRankInfo(context.Context, *BatchRankInfoReq) (*BatchRankInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchRankInfo not implemented")
}

// UnsafeRankServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankServer will
// result in compilation errors.
type UnsafeRankServer interface {
	mustEmbedUnimplementedRankServer()
}

func RegisterRankServer(s grpc.ServiceRegistrar, srv RankServer) {
	s.RegisterService(&Rank_ServiceDesc, srv)
}

func _Rank_QueryRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServer).QueryRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rank_QueryRank_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServer).QueryRank(ctx, req.(*QueryRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rank_QueryRankItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRankItemsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServer).QueryRankItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rank_QueryRankItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServer).QueryRankItems(ctx, req.(*QueryRankItemsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rank_BatchRankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRankInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServer).BatchRankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rank_BatchRankInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServer).BatchRankInfo(ctx, req.(*BatchRankInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Rank_ServiceDesc is the grpc.ServiceDesc for Rank service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Rank_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.Rank",
	HandlerType: (*RankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryRank",
			Handler:    _Rank_QueryRank_Handler,
		},
		{
			MethodName: "QueryRankItems",
			Handler:    _Rank_QueryRankItems_Handler,
		},
		{
			MethodName: "BatchRankInfo",
			Handler:    _Rank_BatchRankInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/rank/rank.proto",
}
