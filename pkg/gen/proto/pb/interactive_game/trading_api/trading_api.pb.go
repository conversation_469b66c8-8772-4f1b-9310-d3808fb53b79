// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/trading_api/trading_api.proto

package trading_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QueryAlternateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  int64 `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`   // 功能卡/门票 资产 id
	AssetNum int64 `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"` // 需要的资产数量
}

func (x *QueryAlternateReq) Reset() {
	*x = QueryAlternateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAlternateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlternateReq) ProtoMessage() {}

func (x *QueryAlternateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlternateReq.ProtoReflect.Descriptor instead.
func (*QueryAlternateReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryAlternateReq) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *QueryAlternateReq) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

type QueryAlternateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetIcon       string                      `protobuf:"bytes,1,opt,name=assetIcon,proto3" json:"assetIcon,omitempty"`              // 资产图标
	AssetName       string                      `protobuf:"bytes,2,opt,name=assetName,proto3" json:"assetName,omitempty"`              // 资产名称
	AssetNum        int64                       `protobuf:"varint,3,opt,name=assetNum,proto3" json:"assetNum,omitempty"`               // 资产数量
	AlternateName   string                      `protobuf:"bytes,4,opt,name=alternateName,proto3" json:"alternateName,omitempty"`      // 代替品名称
	AlternateNum    int64                       `protobuf:"varint,5,opt,name=alternateNum,proto3" json:"alternateNum,omitempty"`       // 替代品数量
	Ticket          string                      `protobuf:"bytes,6,opt,name=ticket,proto3" json:"ticket,omitempty"`                    // 兑换回传
	Rewards         []*QueryAlternateRsp_Reward `protobuf:"bytes,7,rep,name=rewards,proto3" json:"rewards,omitempty"`                  // 加赠列表
	ExchangeRate    string                      `protobuf:"bytes,8,opt,name=exchangeRate,proto3" json:"exchangeRate,omitempty"`        // 兑换比例
	EnableAlternate bool                        `protobuf:"varint,9,opt,name=enableAlternate,proto3" json:"enableAlternate,omitempty"` // 是否勾选
}

func (x *QueryAlternateRsp) Reset() {
	*x = QueryAlternateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAlternateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlternateRsp) ProtoMessage() {}

func (x *QueryAlternateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlternateRsp.ProtoReflect.Descriptor instead.
func (*QueryAlternateRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryAlternateRsp) GetAssetIcon() string {
	if x != nil {
		return x.AssetIcon
	}
	return ""
}

func (x *QueryAlternateRsp) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *QueryAlternateRsp) GetAssetNum() int64 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *QueryAlternateRsp) GetAlternateName() string {
	if x != nil {
		return x.AlternateName
	}
	return ""
}

func (x *QueryAlternateRsp) GetAlternateNum() int64 {
	if x != nil {
		return x.AlternateNum
	}
	return 0
}

func (x *QueryAlternateRsp) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *QueryAlternateRsp) GetRewards() []*QueryAlternateRsp_Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *QueryAlternateRsp) GetExchangeRate() string {
	if x != nil {
		return x.ExchangeRate
	}
	return ""
}

func (x *QueryAlternateRsp) GetEnableAlternate() bool {
	if x != nil {
		return x.EnableAlternate
	}
	return false
}

type QueryPreferenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryPreferenceReq) Reset() {
	*x = QueryPreferenceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPreferenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPreferenceReq) ProtoMessage() {}

func (x *QueryPreferenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPreferenceReq.ProtoReflect.Descriptor instead.
func (*QueryPreferenceReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{2}
}

type QueryPreferenceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key: "alternate_222_224" value: "0", "1" // K 豆兑换游乐券
	// key: "alternate_263_224" value: "0", "1" // K 豆兑换功能卡
	Preference map[string]string `protobuf:"bytes,1,rep,name=preference,proto3" json:"preference,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *QueryPreferenceRsp) Reset() {
	*x = QueryPreferenceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPreferenceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPreferenceRsp) ProtoMessage() {}

func (x *QueryPreferenceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPreferenceRsp.ProtoReflect.Descriptor instead.
func (*QueryPreferenceRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{3}
}

func (x *QueryPreferenceRsp) GetPreference() map[string]string {
	if x != nil {
		return x.Preference
	}
	return nil
}

type UpdatePreferenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *UpdatePreferenceReq) Reset() {
	*x = UpdatePreferenceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePreferenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePreferenceReq) ProtoMessage() {}

func (x *UpdatePreferenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePreferenceReq.ProtoReflect.Descriptor instead.
func (*UpdatePreferenceReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdatePreferenceReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *UpdatePreferenceReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type UpdatePreferenceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePreferenceRsp) Reset() {
	*x = UpdatePreferenceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePreferenceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePreferenceRsp) ProtoMessage() {}

func (x *UpdatePreferenceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePreferenceRsp.ProtoReflect.Descriptor instead.
func (*UpdatePreferenceRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{5}
}

type ExchangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ticket        string `protobuf:"bytes,1,opt,name=ticket,proto3" json:"ticket,omitempty"`
	SetPreference bool   `protobuf:"varint,2,opt,name=setPreference,proto3" json:"setPreference,omitempty"`
}

func (x *ExchangeReq) Reset() {
	*x = ExchangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeReq) ProtoMessage() {}

func (x *ExchangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeReq.ProtoReflect.Descriptor instead.
func (*ExchangeReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{6}
}

func (x *ExchangeReq) GetTicket() string {
	if x != nil {
		return x.Ticket
	}
	return ""
}

func (x *ExchangeReq) GetSetPreference() bool {
	if x != nil {
		return x.SetPreference
	}
	return false
}

type ExchangeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExchangeRsp) Reset() {
	*x = ExchangeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeRsp) ProtoMessage() {}

func (x *ExchangeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeRsp.ProtoReflect.Descriptor instead.
func (*ExchangeRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{7}
}

type QueryAlternateRsp_Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardIcon string `protobuf:"bytes,1,opt,name=rewardIcon,proto3" json:"rewardIcon,omitempty"` // 奖励图标
	RewardName string `protobuf:"bytes,2,opt,name=rewardName,proto3" json:"rewardName,omitempty"` // 奖励名称
	RewardNum  int64  `protobuf:"varint,3,opt,name=rewardNum,proto3" json:"rewardNum,omitempty"`  // 奖励数量
}

func (x *QueryAlternateRsp_Reward) Reset() {
	*x = QueryAlternateRsp_Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryAlternateRsp_Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryAlternateRsp_Reward) ProtoMessage() {}

func (x *QueryAlternateRsp_Reward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryAlternateRsp_Reward.ProtoReflect.Descriptor instead.
func (*QueryAlternateRsp_Reward) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *QueryAlternateRsp_Reward) GetRewardIcon() string {
	if x != nil {
		return x.RewardIcon
	}
	return ""
}

func (x *QueryAlternateRsp_Reward) GetRewardName() string {
	if x != nil {
		return x.RewardName
	}
	return ""
}

func (x *QueryAlternateRsp_Reward) GetRewardNum() int64 {
	if x != nil {
		return x.RewardNum
	}
	return 0
}

var File_pb_interactive_game_trading_api_trading_api_proto protoreflect.FileDescriptor

var file_pb_interactive_game_trading_api_trading_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70,
	0x69, 0x22, 0x49, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0xd5, 0x03, 0x0a,
	0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x63, 0x6f, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x6c,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74,
	0x65, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x50, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x22,
	0x0a, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x6c, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x1a, 0x66, 0x0a, 0x06,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4e, 0x75, 0x6d, 0x22, 0x14, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x22, 0xb5, 0x01, 0x0a, 0x12, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x60, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x1a, 0x3d, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x3d, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x15, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x4b, 0x0a, 0x0b, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x65, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x0d, 0x0a, 0x0b, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x73, 0x70, 0x32, 0xd3, 0x03, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x70, 0x69, 0x12, 0x72, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x6c, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x75, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x30, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x30, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x78,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x31, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x60, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x73, 0x70, 0x42, 0x54, 0x5a, 0x52, 0x74, 0x63,
	0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x70, 0x69,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_trading_api_trading_api_proto_rawDescOnce sync.Once
	file_pb_interactive_game_trading_api_trading_api_proto_rawDescData = file_pb_interactive_game_trading_api_trading_api_proto_rawDesc
)

func file_pb_interactive_game_trading_api_trading_api_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_trading_api_trading_api_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_trading_api_trading_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_trading_api_trading_api_proto_rawDescData)
	})
	return file_pb_interactive_game_trading_api_trading_api_proto_rawDescData
}

var file_pb_interactive_game_trading_api_trading_api_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_interactive_game_trading_api_trading_api_proto_goTypes = []interface{}{
	(*QueryAlternateReq)(nil),        // 0: interactive_game_trading_api.QueryAlternateReq
	(*QueryAlternateRsp)(nil),        // 1: interactive_game_trading_api.QueryAlternateRsp
	(*QueryPreferenceReq)(nil),       // 2: interactive_game_trading_api.QueryPreferenceReq
	(*QueryPreferenceRsp)(nil),       // 3: interactive_game_trading_api.QueryPreferenceRsp
	(*UpdatePreferenceReq)(nil),      // 4: interactive_game_trading_api.UpdatePreferenceReq
	(*UpdatePreferenceRsp)(nil),      // 5: interactive_game_trading_api.UpdatePreferenceRsp
	(*ExchangeReq)(nil),              // 6: interactive_game_trading_api.ExchangeReq
	(*ExchangeRsp)(nil),              // 7: interactive_game_trading_api.ExchangeRsp
	(*QueryAlternateRsp_Reward)(nil), // 8: interactive_game_trading_api.QueryAlternateRsp.Reward
	nil,                              // 9: interactive_game_trading_api.QueryPreferenceRsp.PreferenceEntry
}
var file_pb_interactive_game_trading_api_trading_api_proto_depIdxs = []int32{
	8, // 0: interactive_game_trading_api.QueryAlternateRsp.rewards:type_name -> interactive_game_trading_api.QueryAlternateRsp.Reward
	9, // 1: interactive_game_trading_api.QueryPreferenceRsp.preference:type_name -> interactive_game_trading_api.QueryPreferenceRsp.PreferenceEntry
	0, // 2: interactive_game_trading_api.TradingApi.QueryAlternate:input_type -> interactive_game_trading_api.QueryAlternateReq
	2, // 3: interactive_game_trading_api.TradingApi.QueryPreference:input_type -> interactive_game_trading_api.QueryPreferenceReq
	4, // 4: interactive_game_trading_api.TradingApi.UpdatePreference:input_type -> interactive_game_trading_api.UpdatePreferenceReq
	6, // 5: interactive_game_trading_api.TradingApi.Exchange:input_type -> interactive_game_trading_api.ExchangeReq
	1, // 6: interactive_game_trading_api.TradingApi.QueryAlternate:output_type -> interactive_game_trading_api.QueryAlternateRsp
	3, // 7: interactive_game_trading_api.TradingApi.QueryPreference:output_type -> interactive_game_trading_api.QueryPreferenceRsp
	5, // 8: interactive_game_trading_api.TradingApi.UpdatePreference:output_type -> interactive_game_trading_api.UpdatePreferenceRsp
	7, // 9: interactive_game_trading_api.TradingApi.Exchange:output_type -> interactive_game_trading_api.ExchangeRsp
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_trading_api_trading_api_proto_init() }
func file_pb_interactive_game_trading_api_trading_api_proto_init() {
	if File_pb_interactive_game_trading_api_trading_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAlternateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAlternateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPreferenceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPreferenceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePreferenceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePreferenceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_trading_api_trading_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryAlternateRsp_Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_trading_api_trading_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_trading_api_trading_api_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_trading_api_trading_api_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_trading_api_trading_api_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_trading_api_trading_api_proto = out.File
	file_pb_interactive_game_trading_api_trading_api_proto_rawDesc = nil
	file_pb_interactive_game_trading_api_trading_api_proto_goTypes = nil
	file_pb_interactive_game_trading_api_trading_api_proto_depIdxs = nil
}
