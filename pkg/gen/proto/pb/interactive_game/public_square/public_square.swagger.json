{"swagger": "2.0", "info": {"title": "pb/interactive_game/public_square/public_square.proto", "version": "version not set"}, "tags": [{"name": "PublicSquare"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.PublicSquare/QuickMatch": {"post": {"summary": "快速匹配", "operationId": "PublicSquare_QuickMatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameQuickMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameQuickMatchReq"}}], "tags": ["PublicSquare"]}}, "/interactive_game.PublicSquare/RoomList": {"post": {"summary": "房间列表", "operationId": "PublicSquare_RoomList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameRoomListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameRoomListReq"}}], "tags": ["PublicSquare"]}}}, "definitions": {"RoomListRspPlayer": {"type": "object", "properties": {"openId": {"type": "string"}}}, "RoomListRspRoom": {"type": "object", "properties": {"status": {"$ref": "#/definitions/commonRoomStatus"}, "modeId": {"type": "string"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RoomListRspPlayer"}}, "maxPlayers": {"type": "integer", "format": "int64"}, "joinMode": {"$ref": "#/definitions/commonJoinMode"}, "payConf": {"$ref": "#/definitions/commonGameRoomPayConfig"}, "gameAppId": {"type": "string"}, "roomId": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType"}}}, "commonGameRoomPayConfig": {"type": "object", "properties": {"mode": {"$ref": "#/definitions/commonPayMode"}, "assetId": {"type": "string", "format": "int64", "title": "扣费资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "扣费资产数量"}, "payModeName": {"type": "string", "title": "扣费模式名称"}, "payModeId": {"type": "string", "title": "扣费模式 id"}}}, "commonJoinMode": {"type": "string", "enum": ["JoinUnlimited", "Join<PERSON><PERSON>"], "default": "JoinUnlimited", "title": "- JoinUnlimited: 自由落座\n - JoinPicked: 主播选择"}, "commonPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "commonRoomStatus": {"type": "string", "enum": ["RoomNone", "RoomPending", "RoomPlaying"], "default": "RoomNone", "title": "- RoomPending: 组局中\n - RoomPlaying: 游戏中"}, "interactive_gameQuickMatchReq": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "modeId": {"type": "string"}, "payConf": {"$ref": "#/definitions/commonGameRoomPayConfig", "title": "付费参数"}, "openId": {"type": "string"}}}, "interactive_gameQuickMatchRsp": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameRoomListReq": {"type": "object", "properties": {"gameAppId": {"type": "string"}, "passback": {"type": "string"}, "modeId": {"type": "string", "title": "根据游戏模式筛选"}, "payConf": {"$ref": "#/definitions/commonGameRoomPayConfig", "title": "付费参数"}, "openId": {"type": "string"}}}, "interactive_gameRoomListRsp": {"type": "object", "properties": {"rooms": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/RoomListRspRoom"}}, "hasMore": {"type": "boolean"}, "passback": {"type": "string"}}}, "interactive_gamecommonRoomType": {"type": "string", "enum": ["TypeNone", "TypeKtv", "TypeTwins", "TypeLive", "TypeMatch"], "default": "TypeNone", "title": "- TypeKtv: 歌房\n - TypeTwins: 双人房\n - TypeLive: 直播\n - TypeMatch: 匹配房"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}