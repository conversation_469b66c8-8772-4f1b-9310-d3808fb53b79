// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/public_square/public_square.proto

package interactive_game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RoomListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId string                    `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Passback  string                    `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
	ModeId    string                    `protobuf:"bytes,3,opt,name=modeId,proto3" json:"modeId,omitempty"`   // 根据游戏模式筛选
	PayConf   *common.GameRoomPayConfig `protobuf:"bytes,4,opt,name=payConf,proto3" json:"payConf,omitempty"` // 付费参数
	OpenId    string                    `protobuf:"bytes,5,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *RoomListReq) Reset() {
	*x = RoomListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomListReq) ProtoMessage() {}

func (x *RoomListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomListReq.ProtoReflect.Descriptor instead.
func (*RoomListReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP(), []int{0}
}

func (x *RoomListReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *RoomListReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *RoomListReq) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *RoomListReq) GetPayConf() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConf
	}
	return nil
}

func (x *RoomListReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type RoomListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rooms    []*RoomListRsp_Room `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	HasMore  bool                `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`
	Passback string              `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *RoomListRsp) Reset() {
	*x = RoomListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomListRsp) ProtoMessage() {}

func (x *RoomListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomListRsp.ProtoReflect.Descriptor instead.
func (*RoomListRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP(), []int{1}
}

func (x *RoomListRsp) GetRooms() []*RoomListRsp_Room {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *RoomListRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *RoomListRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type QuickMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameAppId string                    `protobuf:"bytes,1,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	ModeId    string                    `protobuf:"bytes,2,opt,name=modeId,proto3" json:"modeId,omitempty"`
	PayConf   *common.GameRoomPayConfig `protobuf:"bytes,3,opt,name=payConf,proto3" json:"payConf,omitempty"` // 付费参数
	OpenId    string                    `protobuf:"bytes,4,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *QuickMatchReq) Reset() {
	*x = QuickMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuickMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickMatchReq) ProtoMessage() {}

func (x *QuickMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickMatchReq.ProtoReflect.Descriptor instead.
func (*QuickMatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP(), []int{2}
}

func (x *QuickMatchReq) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *QuickMatchReq) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *QuickMatchReq) GetPayConf() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConf
	}
	return nil
}

func (x *QuickMatchReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QuickMatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *QuickMatchRsp) Reset() {
	*x = QuickMatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuickMatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickMatchRsp) ProtoMessage() {}

func (x *QuickMatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickMatchRsp.ProtoReflect.Descriptor instead.
func (*QuickMatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP(), []int{3}
}

func (x *QuickMatchRsp) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type RoomListRsp_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *RoomListRsp_Player) Reset() {
	*x = RoomListRsp_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomListRsp_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomListRsp_Player) ProtoMessage() {}

func (x *RoomListRsp_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomListRsp_Player.ProtoReflect.Descriptor instead.
func (*RoomListRsp_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RoomListRsp_Player) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type RoomListRsp_Room struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     common.RoomStatus         `protobuf:"varint,1,opt,name=status,proto3,enum=interactive_game.common.RoomStatus" json:"status,omitempty"`
	ModeId     string                    `protobuf:"bytes,2,opt,name=modeId,proto3" json:"modeId,omitempty"`
	Players    []*RoomListRsp_Player     `protobuf:"bytes,3,rep,name=players,proto3" json:"players,omitempty"`
	MaxPlayers uint32                    `protobuf:"varint,4,opt,name=maxPlayers,proto3" json:"maxPlayers,omitempty"`
	JoinMode   common.JoinMode           `protobuf:"varint,5,opt,name=joinMode,proto3,enum=interactive_game.common.JoinMode" json:"joinMode,omitempty"`
	PayConf    *common.GameRoomPayConfig `protobuf:"bytes,6,opt,name=payConf,proto3" json:"payConf,omitempty"`
	GameAppId  string                    `protobuf:"bytes,7,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	RoomId     string                    `protobuf:"bytes,8,opt,name=roomId,proto3" json:"roomId,omitempty"`
	RoomType   common.RoomType           `protobuf:"varint,9,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"`
}

func (x *RoomListRsp_Room) Reset() {
	*x = RoomListRsp_Room{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomListRsp_Room) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomListRsp_Room) ProtoMessage() {}

func (x *RoomListRsp_Room) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_public_square_public_square_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomListRsp_Room.ProtoReflect.Descriptor instead.
func (*RoomListRsp_Room) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP(), []int{1, 1}
}

func (x *RoomListRsp_Room) GetStatus() common.RoomStatus {
	if x != nil {
		return x.Status
	}
	return common.RoomStatus(0)
}

func (x *RoomListRsp_Room) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *RoomListRsp_Room) GetPlayers() []*RoomListRsp_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *RoomListRsp_Room) GetMaxPlayers() uint32 {
	if x != nil {
		return x.MaxPlayers
	}
	return 0
}

func (x *RoomListRsp_Room) GetJoinMode() common.JoinMode {
	if x != nil {
		return x.JoinMode
	}
	return common.JoinMode(0)
}

func (x *RoomListRsp_Room) GetPayConf() *common.GameRoomPayConfig {
	if x != nil {
		return x.PayConf
	}
	return nil
}

func (x *RoomListRsp_Room) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *RoomListRsp_Room) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomListRsp_Room) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

var File_pb_interactive_game_public_square_public_square_proto protoreflect.FileDescriptor

var file_pb_interactive_game_public_square_public_square_proto_rawDesc = []byte{
	0x0a, 0x35, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x73, 0x71, 0x75,
	0x61, 0x72, 0x65, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x73, 0x71, 0x75, 0x61, 0x72,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xbd, 0x01, 0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x07, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x22, 0xd7, 0x04, 0x0a, 0x0b, 0x52, 0x6f, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x38, 0x0a, 0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x1a, 0x20, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x1a, 0xb5, 0x03, 0x0a, 0x04, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x3b, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x3e, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x61, 0x78, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x73, 0x12, 0x3d, 0x0a, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4a,
	0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d,
	0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07,
	0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x3d, 0x0a,
	0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa3, 0x01, 0x0a,
	0x0d, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x1c,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x07, 0x70, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x22, 0x27, 0x0a, 0x0d, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x32, 0xa8, 0x01, 0x0a, 0x0c,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x53, 0x71, 0x75, 0x61, 0x72, 0x65, 0x12, 0x48, 0x0a, 0x08,
	0x52, 0x6f, 0x6f, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0a, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x1f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x42, 0x48, 0x5a, 0x46, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_public_square_public_square_proto_rawDescOnce sync.Once
	file_pb_interactive_game_public_square_public_square_proto_rawDescData = file_pb_interactive_game_public_square_public_square_proto_rawDesc
)

func file_pb_interactive_game_public_square_public_square_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_public_square_public_square_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_public_square_public_square_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_public_square_public_square_proto_rawDescData)
	})
	return file_pb_interactive_game_public_square_public_square_proto_rawDescData
}

var file_pb_interactive_game_public_square_public_square_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_interactive_game_public_square_public_square_proto_goTypes = []interface{}{
	(*RoomListReq)(nil),              // 0: interactive_game.RoomListReq
	(*RoomListRsp)(nil),              // 1: interactive_game.RoomListRsp
	(*QuickMatchReq)(nil),            // 2: interactive_game.QuickMatchReq
	(*QuickMatchRsp)(nil),            // 3: interactive_game.QuickMatchRsp
	(*RoomListRsp_Player)(nil),       // 4: interactive_game.RoomListRsp.Player
	(*RoomListRsp_Room)(nil),         // 5: interactive_game.RoomListRsp.Room
	(*common.GameRoomPayConfig)(nil), // 6: interactive_game.common.GameRoomPayConfig
	(common.RoomStatus)(0),           // 7: interactive_game.common.RoomStatus
	(common.JoinMode)(0),             // 8: interactive_game.common.JoinMode
	(common.RoomType)(0),             // 9: interactive_game.common.RoomType
}
var file_pb_interactive_game_public_square_public_square_proto_depIdxs = []int32{
	6,  // 0: interactive_game.RoomListReq.payConf:type_name -> interactive_game.common.GameRoomPayConfig
	5,  // 1: interactive_game.RoomListRsp.rooms:type_name -> interactive_game.RoomListRsp.Room
	6,  // 2: interactive_game.QuickMatchReq.payConf:type_name -> interactive_game.common.GameRoomPayConfig
	7,  // 3: interactive_game.RoomListRsp.Room.status:type_name -> interactive_game.common.RoomStatus
	4,  // 4: interactive_game.RoomListRsp.Room.players:type_name -> interactive_game.RoomListRsp.Player
	8,  // 5: interactive_game.RoomListRsp.Room.joinMode:type_name -> interactive_game.common.JoinMode
	6,  // 6: interactive_game.RoomListRsp.Room.payConf:type_name -> interactive_game.common.GameRoomPayConfig
	9,  // 7: interactive_game.RoomListRsp.Room.roomType:type_name -> interactive_game.common.RoomType
	0,  // 8: interactive_game.PublicSquare.RoomList:input_type -> interactive_game.RoomListReq
	2,  // 9: interactive_game.PublicSquare.QuickMatch:input_type -> interactive_game.QuickMatchReq
	1,  // 10: interactive_game.PublicSquare.RoomList:output_type -> interactive_game.RoomListRsp
	3,  // 11: interactive_game.PublicSquare.QuickMatch:output_type -> interactive_game.QuickMatchRsp
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_public_square_public_square_proto_init() }
func file_pb_interactive_game_public_square_public_square_proto_init() {
	if File_pb_interactive_game_public_square_public_square_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_public_square_public_square_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_public_square_public_square_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_public_square_public_square_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuickMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_public_square_public_square_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuickMatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_public_square_public_square_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomListRsp_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_public_square_public_square_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomListRsp_Room); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_public_square_public_square_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_public_square_public_square_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_public_square_public_square_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_public_square_public_square_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_public_square_public_square_proto = out.File
	file_pb_interactive_game_public_square_public_square_proto_rawDesc = nil
	file_pb_interactive_game_public_square_public_square_proto_goTypes = nil
	file_pb_interactive_game_public_square_public_square_proto_depIdxs = nil
}
