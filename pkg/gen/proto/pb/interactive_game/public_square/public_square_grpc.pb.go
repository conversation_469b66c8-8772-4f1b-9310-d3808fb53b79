// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/public_square/public_square.proto

package interactive_game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	PublicSquare_RoomList_FullMethodName   = "/interactive_game.PublicSquare/RoomList"
	PublicSquare_QuickMatch_FullMethodName = "/interactive_game.PublicSquare/QuickMatch"
)

// PublicSquareClient is the client API for PublicSquare service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PublicSquareClient interface {
	// 房间列表
	RoomList(ctx context.Context, in *RoomListReq, opts ...grpc.CallOption) (*RoomListRsp, error)
	// 快速匹配
	QuickMatch(ctx context.Context, in *QuickMatchReq, opts ...grpc.CallOption) (*QuickMatchRsp, error)
}

type publicSquareClient struct {
	cc grpc.ClientConnInterface
}

func NewPublicSquareClient(cc grpc.ClientConnInterface) PublicSquareClient {
	return &publicSquareClient{cc}
}

func (c *publicSquareClient) RoomList(ctx context.Context, in *RoomListReq, opts ...grpc.CallOption) (*RoomListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RoomListRsp)
	err := c.cc.Invoke(ctx, PublicSquare_RoomList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicSquareClient) QuickMatch(ctx context.Context, in *QuickMatchReq, opts ...grpc.CallOption) (*QuickMatchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuickMatchRsp)
	err := c.cc.Invoke(ctx, PublicSquare_QuickMatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PublicSquareServer is the server API for PublicSquare service.
// All implementations should embed UnimplementedPublicSquareServer
// for forward compatibility
type PublicSquareServer interface {
	// 房间列表
	RoomList(context.Context, *RoomListReq) (*RoomListRsp, error)
	// 快速匹配
	QuickMatch(context.Context, *QuickMatchReq) (*QuickMatchRsp, error)
}

// UnimplementedPublicSquareServer should be embedded to have forward compatible implementations.
type UnimplementedPublicSquareServer struct {
}

func (UnimplementedPublicSquareServer) RoomList(context.Context, *RoomListReq) (*RoomListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RoomList not implemented")
}
func (UnimplementedPublicSquareServer) QuickMatch(context.Context, *QuickMatchReq) (*QuickMatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuickMatch not implemented")
}

// UnsafePublicSquareServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PublicSquareServer will
// result in compilation errors.
type UnsafePublicSquareServer interface {
	mustEmbedUnimplementedPublicSquareServer()
}

func RegisterPublicSquareServer(s grpc.ServiceRegistrar, srv PublicSquareServer) {
	s.RegisterService(&PublicSquare_ServiceDesc, srv)
}

func _PublicSquare_RoomList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoomListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareServer).RoomList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquare_RoomList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareServer).RoomList(ctx, req.(*RoomListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicSquare_QuickMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuickMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareServer).QuickMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquare_QuickMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareServer).QuickMatch(ctx, req.(*QuickMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PublicSquare_ServiceDesc is the grpc.ServiceDesc for PublicSquare service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PublicSquare_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.PublicSquare",
	HandlerType: (*PublicSquareServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RoomList",
			Handler:    _PublicSquare_RoomList_Handler,
		},
		{
			MethodName: "QuickMatch",
			Handler:    _PublicSquare_QuickMatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/public_square/public_square.proto",
}
