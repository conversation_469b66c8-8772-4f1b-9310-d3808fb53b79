{"swagger": "2.0", "info": {"title": "pb/interactive_game/trading/trading.proto", "version": "version not set"}, "tags": [{"name": "Trading"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game_trading.Trading/MixedPaymentExchange": {"post": {"summary": "兑换", "operationId": "Trading_MixedPaymentExchange", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_tradingMixedPaymentExchangeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_tradingMixedPaymentExchangeReq"}}], "tags": ["Trading"]}}, "/interactive_game_trading.Trading/MixedPaymentQuery": {"post": {"summary": "查询", "operationId": "Trading_MixedPaymentQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_tradingMixedPaymentQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_tradingMixedPaymentQueryReq"}}], "tags": ["Trading"]}}, "/interactive_game_trading.Trading/Pay": {"post": {"summary": "支付", "operationId": "Trading_Pay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_tradingPayRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_tradingPayReq"}}], "tags": ["Trading"]}}, "/interactive_game_trading.Trading/QueryBalance": {"post": {"summary": "校验余额", "operationId": "Trading_QueryBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_tradingQueryBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_tradingQueryBalanceReq"}}], "tags": ["Trading"]}}, "/interactive_game_trading.Trading/Refund": {"post": {"summary": "退款", "operationId": "Trading_Refund", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_game_tradingRefundRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_game_tradingRefundReq"}}], "tags": ["Trading"]}}}, "definitions": {"MixedPaymentQueryRspReward": {"type": "object", "properties": {"icon": {"type": "string", "title": "奖励图标"}, "name": {"type": "string", "title": "奖励名称"}, "num": {"type": "string", "format": "int64", "title": "奖励数量"}}}, "interactive_game_tradingMixedPaymentExchangeReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "ticket": {"type": "string"}}}, "interactive_game_tradingMixedPaymentExchangeRsp": {"type": "object", "properties": {"targetAssetId": {"type": "string", "format": "int64"}, "targetAssetNum": {"type": "string", "format": "int64"}, "alternateAssetId": {"type": "string", "format": "int64"}, "alternateAssetNum": {"type": "string", "format": "int64"}}}, "interactive_game_tradingMixedPaymentQueryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetId": {"type": "string", "format": "int64"}, "assetNum": {"type": "string", "format": "int64"}, "requireRewards": {"type": "boolean"}}}, "interactive_game_tradingMixedPaymentQueryRsp": {"type": "object", "properties": {"targetAssetNum": {"type": "string", "format": "int64", "title": "目标数量"}, "alternateAssetId": {"type": "string", "format": "int64", "title": "替代品资产 id"}, "alternateAssetNum": {"type": "string", "format": "int64", "title": "替代品数量"}, "exchangeRate": {"type": "string", "title": "兑换比例"}, "ticket": {"type": "string", "title": "兑换回传"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/MixedPaymentQueryRspReward"}, "title": "加赠列表"}}}, "interactive_game_tradingPayReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetId": {"type": "string", "format": "int64"}, "assetNum": {"type": "string", "format": "int64"}, "transactionId": {"type": "string"}, "timestamp": {"type": "string", "format": "int64"}, "reason": {"type": "string"}}}, "interactive_game_tradingPayRsp": {"type": "object"}, "interactive_game_tradingQueryBalanceReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "assetId": {"type": "string", "format": "int64"}}}, "interactive_game_tradingQueryBalanceRsp": {"type": "object", "properties": {"assetNum": {"type": "string", "format": "int64"}, "AssetNumFromAlternate": {"type": "string", "format": "int64"}, "enableAlternate": {"type": "boolean"}}}, "interactive_game_tradingRefundReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "transactionId": {"type": "string"}}}, "interactive_game_tradingRefundRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}