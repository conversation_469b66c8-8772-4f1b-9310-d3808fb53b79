// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/trading/trading.proto

package trading

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Trading_QueryBalance_FullMethodName         = "/interactive_game_trading.Trading/QueryBalance"
	Trading_Pay_FullMethodName                  = "/interactive_game_trading.Trading/Pay"
	Trading_Refund_FullMethodName               = "/interactive_game_trading.Trading/Refund"
	Trading_MixedPaymentExchange_FullMethodName = "/interactive_game_trading.Trading/MixedPaymentExchange"
	Trading_MixedPaymentQuery_FullMethodName    = "/interactive_game_trading.Trading/MixedPaymentQuery"
)

// TradingClient is the client API for Trading service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TradingClient interface {
	// 校验余额
	QueryBalance(ctx context.Context, in *QueryBalanceReq, opts ...grpc.CallOption) (*QueryBalanceRsp, error)
	// 支付
	Pay(ctx context.Context, in *PayReq, opts ...grpc.CallOption) (*PayRsp, error)
	// 退款
	Refund(ctx context.Context, in *RefundReq, opts ...grpc.CallOption) (*RefundRsp, error)
	// 兑换
	MixedPaymentExchange(ctx context.Context, in *MixedPaymentExchangeReq, opts ...grpc.CallOption) (*MixedPaymentExchangeRsp, error)
	// 查询
	MixedPaymentQuery(ctx context.Context, in *MixedPaymentQueryReq, opts ...grpc.CallOption) (*MixedPaymentQueryRsp, error)
}

type tradingClient struct {
	cc grpc.ClientConnInterface
}

func NewTradingClient(cc grpc.ClientConnInterface) TradingClient {
	return &tradingClient{cc}
}

func (c *tradingClient) QueryBalance(ctx context.Context, in *QueryBalanceReq, opts ...grpc.CallOption) (*QueryBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryBalanceRsp)
	err := c.cc.Invoke(ctx, Trading_QueryBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tradingClient) Pay(ctx context.Context, in *PayReq, opts ...grpc.CallOption) (*PayRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PayRsp)
	err := c.cc.Invoke(ctx, Trading_Pay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tradingClient) Refund(ctx context.Context, in *RefundReq, opts ...grpc.CallOption) (*RefundRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundRsp)
	err := c.cc.Invoke(ctx, Trading_Refund_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tradingClient) MixedPaymentExchange(ctx context.Context, in *MixedPaymentExchangeReq, opts ...grpc.CallOption) (*MixedPaymentExchangeRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MixedPaymentExchangeRsp)
	err := c.cc.Invoke(ctx, Trading_MixedPaymentExchange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tradingClient) MixedPaymentQuery(ctx context.Context, in *MixedPaymentQueryReq, opts ...grpc.CallOption) (*MixedPaymentQueryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MixedPaymentQueryRsp)
	err := c.cc.Invoke(ctx, Trading_MixedPaymentQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TradingServer is the server API for Trading service.
// All implementations should embed UnimplementedTradingServer
// for forward compatibility
type TradingServer interface {
	// 校验余额
	QueryBalance(context.Context, *QueryBalanceReq) (*QueryBalanceRsp, error)
	// 支付
	Pay(context.Context, *PayReq) (*PayRsp, error)
	// 退款
	Refund(context.Context, *RefundReq) (*RefundRsp, error)
	// 兑换
	MixedPaymentExchange(context.Context, *MixedPaymentExchangeReq) (*MixedPaymentExchangeRsp, error)
	// 查询
	MixedPaymentQuery(context.Context, *MixedPaymentQueryReq) (*MixedPaymentQueryRsp, error)
}

// UnimplementedTradingServer should be embedded to have forward compatible implementations.
type UnimplementedTradingServer struct {
}

func (UnimplementedTradingServer) QueryBalance(context.Context, *QueryBalanceReq) (*QueryBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryBalance not implemented")
}
func (UnimplementedTradingServer) Pay(context.Context, *PayReq) (*PayRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Pay not implemented")
}
func (UnimplementedTradingServer) Refund(context.Context, *RefundReq) (*RefundRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedTradingServer) MixedPaymentExchange(context.Context, *MixedPaymentExchangeReq) (*MixedPaymentExchangeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MixedPaymentExchange not implemented")
}
func (UnimplementedTradingServer) MixedPaymentQuery(context.Context, *MixedPaymentQueryReq) (*MixedPaymentQueryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MixedPaymentQuery not implemented")
}

// UnsafeTradingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TradingServer will
// result in compilation errors.
type UnsafeTradingServer interface {
	mustEmbedUnimplementedTradingServer()
}

func RegisterTradingServer(s grpc.ServiceRegistrar, srv TradingServer) {
	s.RegisterService(&Trading_ServiceDesc, srv)
}

func _Trading_QueryBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TradingServer).QueryBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Trading_QueryBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TradingServer).QueryBalance(ctx, req.(*QueryBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Trading_Pay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TradingServer).Pay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Trading_Pay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TradingServer).Pay(ctx, req.(*PayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Trading_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TradingServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Trading_Refund_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TradingServer).Refund(ctx, req.(*RefundReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Trading_MixedPaymentExchange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MixedPaymentExchangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TradingServer).MixedPaymentExchange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Trading_MixedPaymentExchange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TradingServer).MixedPaymentExchange(ctx, req.(*MixedPaymentExchangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Trading_MixedPaymentQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MixedPaymentQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TradingServer).MixedPaymentQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Trading_MixedPaymentQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TradingServer).MixedPaymentQuery(ctx, req.(*MixedPaymentQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Trading_ServiceDesc is the grpc.ServiceDesc for Trading service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Trading_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game_trading.Trading",
	HandlerType: (*TradingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryBalance",
			Handler:    _Trading_QueryBalance_Handler,
		},
		{
			MethodName: "Pay",
			Handler:    _Trading_Pay_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _Trading_Refund_Handler,
		},
		{
			MethodName: "MixedPaymentExchange",
			Handler:    _Trading_MixedPaymentExchange_Handler,
		},
		{
			MethodName: "MixedPaymentQuery",
			Handler:    _Trading_MixedPaymentQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/trading/trading.proto",
}
