// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/public_square_internal/public_square_internal.proto

package interactive_game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	PublicSquareInternal_LoadAppIDList_FullMethodName = "/interactive_game.PublicSquareInternal/LoadAppIDList"
	PublicSquareInternal_LoadRoomList_FullMethodName  = "/interactive_game.PublicSquareInternal/LoadRoomList"
	PublicSquareInternal_KickRoomList_FullMethodName  = "/interactive_game.PublicSquareInternal/KickRoomList"
	PublicSquareInternal_BatchRoom_FullMethodName     = "/interactive_game.PublicSquareInternal/BatchRoom"
	PublicSquareInternal_RoomList_FullMethodName      = "/interactive_game.PublicSquareInternal/RoomList"
	PublicSquareInternal_QuickMatch_FullMethodName    = "/interactive_game.PublicSquareInternal/QuickMatch"
)

// PublicSquareInternalClient is the client API for PublicSquareInternal service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PublicSquareInternalClient interface {
	// 加载游戏appid列表
	LoadAppIDList(ctx context.Context, in *LoadAppIDListReq, opts ...grpc.CallOption) (*LoadAppIDListRsp, error)
	// 加载房间列表
	LoadRoomList(ctx context.Context, in *LoadRoomListReq, opts ...grpc.CallOption) (*LoadRoomListRsp, error)
	// 增加剔除房间
	KickRoomList(ctx context.Context, in *KickRoomListReq, opts ...grpc.CallOption) (*KickRoomListRsp, error)
	// 批量查询房间
	BatchRoom(ctx context.Context, in *BatchRoomReq, opts ...grpc.CallOption) (*BatchRoomRsp, error)
	// 房间列表
	RoomList(ctx context.Context, in *RoomListInternalReq, opts ...grpc.CallOption) (*RoomListInternalRsp, error)
	// 快速匹配
	QuickMatch(ctx context.Context, in *QuickMatchInternalReq, opts ...grpc.CallOption) (*QuickMatchInternalRsp, error)
}

type publicSquareInternalClient struct {
	cc grpc.ClientConnInterface
}

func NewPublicSquareInternalClient(cc grpc.ClientConnInterface) PublicSquareInternalClient {
	return &publicSquareInternalClient{cc}
}

func (c *publicSquareInternalClient) LoadAppIDList(ctx context.Context, in *LoadAppIDListReq, opts ...grpc.CallOption) (*LoadAppIDListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadAppIDListRsp)
	err := c.cc.Invoke(ctx, PublicSquareInternal_LoadAppIDList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicSquareInternalClient) LoadRoomList(ctx context.Context, in *LoadRoomListReq, opts ...grpc.CallOption) (*LoadRoomListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoadRoomListRsp)
	err := c.cc.Invoke(ctx, PublicSquareInternal_LoadRoomList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicSquareInternalClient) KickRoomList(ctx context.Context, in *KickRoomListReq, opts ...grpc.CallOption) (*KickRoomListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KickRoomListRsp)
	err := c.cc.Invoke(ctx, PublicSquareInternal_KickRoomList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicSquareInternalClient) BatchRoom(ctx context.Context, in *BatchRoomReq, opts ...grpc.CallOption) (*BatchRoomRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchRoomRsp)
	err := c.cc.Invoke(ctx, PublicSquareInternal_BatchRoom_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicSquareInternalClient) RoomList(ctx context.Context, in *RoomListInternalReq, opts ...grpc.CallOption) (*RoomListInternalRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RoomListInternalRsp)
	err := c.cc.Invoke(ctx, PublicSquareInternal_RoomList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *publicSquareInternalClient) QuickMatch(ctx context.Context, in *QuickMatchInternalReq, opts ...grpc.CallOption) (*QuickMatchInternalRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuickMatchInternalRsp)
	err := c.cc.Invoke(ctx, PublicSquareInternal_QuickMatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PublicSquareInternalServer is the server API for PublicSquareInternal service.
// All implementations should embed UnimplementedPublicSquareInternalServer
// for forward compatibility
type PublicSquareInternalServer interface {
	// 加载游戏appid列表
	LoadAppIDList(context.Context, *LoadAppIDListReq) (*LoadAppIDListRsp, error)
	// 加载房间列表
	LoadRoomList(context.Context, *LoadRoomListReq) (*LoadRoomListRsp, error)
	// 增加剔除房间
	KickRoomList(context.Context, *KickRoomListReq) (*KickRoomListRsp, error)
	// 批量查询房间
	BatchRoom(context.Context, *BatchRoomReq) (*BatchRoomRsp, error)
	// 房间列表
	RoomList(context.Context, *RoomListInternalReq) (*RoomListInternalRsp, error)
	// 快速匹配
	QuickMatch(context.Context, *QuickMatchInternalReq) (*QuickMatchInternalRsp, error)
}

// UnimplementedPublicSquareInternalServer should be embedded to have forward compatible implementations.
type UnimplementedPublicSquareInternalServer struct {
}

func (UnimplementedPublicSquareInternalServer) LoadAppIDList(context.Context, *LoadAppIDListReq) (*LoadAppIDListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadAppIDList not implemented")
}
func (UnimplementedPublicSquareInternalServer) LoadRoomList(context.Context, *LoadRoomListReq) (*LoadRoomListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadRoomList not implemented")
}
func (UnimplementedPublicSquareInternalServer) KickRoomList(context.Context, *KickRoomListReq) (*KickRoomListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickRoomList not implemented")
}
func (UnimplementedPublicSquareInternalServer) BatchRoom(context.Context, *BatchRoomReq) (*BatchRoomRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchRoom not implemented")
}
func (UnimplementedPublicSquareInternalServer) RoomList(context.Context, *RoomListInternalReq) (*RoomListInternalRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RoomList not implemented")
}
func (UnimplementedPublicSquareInternalServer) QuickMatch(context.Context, *QuickMatchInternalReq) (*QuickMatchInternalRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuickMatch not implemented")
}

// UnsafePublicSquareInternalServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PublicSquareInternalServer will
// result in compilation errors.
type UnsafePublicSquareInternalServer interface {
	mustEmbedUnimplementedPublicSquareInternalServer()
}

func RegisterPublicSquareInternalServer(s grpc.ServiceRegistrar, srv PublicSquareInternalServer) {
	s.RegisterService(&PublicSquareInternal_ServiceDesc, srv)
}

func _PublicSquareInternal_LoadAppIDList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadAppIDListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareInternalServer).LoadAppIDList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquareInternal_LoadAppIDList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareInternalServer).LoadAppIDList(ctx, req.(*LoadAppIDListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicSquareInternal_LoadRoomList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadRoomListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareInternalServer).LoadRoomList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquareInternal_LoadRoomList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareInternalServer).LoadRoomList(ctx, req.(*LoadRoomListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicSquareInternal_KickRoomList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickRoomListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareInternalServer).KickRoomList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquareInternal_KickRoomList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareInternalServer).KickRoomList(ctx, req.(*KickRoomListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicSquareInternal_BatchRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareInternalServer).BatchRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquareInternal_BatchRoom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareInternalServer).BatchRoom(ctx, req.(*BatchRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicSquareInternal_RoomList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoomListInternalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareInternalServer).RoomList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquareInternal_RoomList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareInternalServer).RoomList(ctx, req.(*RoomListInternalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PublicSquareInternal_QuickMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuickMatchInternalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicSquareInternalServer).QuickMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PublicSquareInternal_QuickMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicSquareInternalServer).QuickMatch(ctx, req.(*QuickMatchInternalReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PublicSquareInternal_ServiceDesc is the grpc.ServiceDesc for PublicSquareInternal service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PublicSquareInternal_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.PublicSquareInternal",
	HandlerType: (*PublicSquareInternalServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LoadAppIDList",
			Handler:    _PublicSquareInternal_LoadAppIDList_Handler,
		},
		{
			MethodName: "LoadRoomList",
			Handler:    _PublicSquareInternal_LoadRoomList_Handler,
		},
		{
			MethodName: "KickRoomList",
			Handler:    _PublicSquareInternal_KickRoomList_Handler,
		},
		{
			MethodName: "BatchRoom",
			Handler:    _PublicSquareInternal_BatchRoom_Handler,
		},
		{
			MethodName: "RoomList",
			Handler:    _PublicSquareInternal_RoomList_Handler,
		},
		{
			MethodName: "QuickMatch",
			Handler:    _PublicSquareInternal_QuickMatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/public_square_internal/public_square_internal.proto",
}
