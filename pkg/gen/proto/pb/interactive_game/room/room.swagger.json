{"swagger": "2.0", "info": {"title": "pb/interactive_game/room/room.proto", "version": "version not set"}, "tags": [{"name": "Room"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.Room/BeginGame": {"post": {"summary": "开始游戏", "operationId": "Room_BeginGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameBeginGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameBeginGameReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/CancelMatch": {"post": {"summary": "取消匹配", "operationId": "Room_CancelMatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCancelMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCancelMatchReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/Dequeue": {"post": {"summary": "用户取消申请", "operationId": "Room_Dequeue", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameDequeueRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameDequeueReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/Enqueue": {"post": {"summary": "用户加入", "operationId": "Room_Enqueue", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameEnqueueRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameEnqueueReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/KickOut": {"post": {"summary": "踢人", "operationId": "Room_KickOut", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameKickOutRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameKickOutReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/Leave": {"post": {"summary": "用户离开", "operationId": "Room_Leave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameLeaveRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameLeaveReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/Match": {"post": {"summary": "匹配路人", "operationId": "Room_Match", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameMatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameMatchReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/OperationalPosition": {"post": {"summary": "运营位", "operationId": "Room_OperationalPosition", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameOperationalPositionRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameOperationalPositionReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/ParticipantList": {"post": {"summary": "参与者列表", "operationId": "Room_ParticipantList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameParticipantListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameParticipantListReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/PickPlayer": {"post": {"summary": "房主选择", "operationId": "Room_PickPlayer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePickPlayerRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePickPlayerReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/PlayAgain": {"post": {"summary": "再来一局", "operationId": "Room_PlayAgain", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gamePlayAgainRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gamePlayAgainReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/QueryStatus": {"post": {"summary": "查询状态", "operationId": "Room_QueryStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameQueryStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameQueryStatusReq"}}], "tags": ["Room"]}}, "/interactive_game.Room/VerifyBalance": {"post": {"summary": "校验余额", "operationId": "Room_VerifyBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameVerifyBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameVerifyBalanceReq"}}], "tags": ["Room"]}}}, "definitions": {"KgInfoLiveMikeMode": {"type": "string", "enum": ["LiveMikeModeUnknown", "LiveMikeModeVideo", "LiveMikeModeAudio", "LiveMikeModePopup"], "default": "LiveMikeModeUnknown", "title": "- LiveMikeModeVideo: 视频上麦\n - LiveMikeModeAudio: 音频上麦\n - LiveMikeModePopup: 弹窗上麦"}, "OperationalPositionRspImage": {"type": "object", "properties": {"url": {"type": "string", "title": "图片链接"}, "hyperlink": {"type": "string", "title": "跳转链接"}}}, "OperationalPositionRspPosition": {"type": "object", "properties": {"image": {"$ref": "#/definitions/OperationalPositionRspImage", "title": "common.OperationalPositionType type = 1;"}, "video": {"$ref": "#/definitions/OperationalPositionRspVideo"}, "task": {"$ref": "#/definitions/OperationalPositionRspTask"}, "id": {"type": "string"}}}, "OperationalPositionRspTask": {"type": "object", "properties": {"type": {"$ref": "#/definitions/commonOperationalPositionTaskType", "title": "任务类型"}, "taskId": {"type": "integer", "format": "int32", "title": "任务ID"}, "state": {"$ref": "#/definitions/commonOperationalPositionTaskState", "title": "任务状态"}, "title": {"type": "string", "title": "任务标题"}, "progress": {"type": "integer", "format": "int64", "title": "当前进度"}, "maxProgress": {"type": "integer", "format": "int64", "title": "最大进度"}, "rewardId": {"type": "string", "title": "奖励 id 领取任务需要回传"}, "nodes": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/OperationalPositionRspTaskNode"}, "title": "任务节点"}, "description": {"type": "string", "title": "任务描述"}, "buttonAction": {"$ref": "#/definitions/commonOperationalPositionTaskButtonAction"}, "buttonUrl": {"type": "string"}}}, "OperationalPositionRspTaskNode": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/OperationalPositionRspTaskNodeReward"}, "title": "奖励信息"}, "description": {"type": "string", "title": "节点描述"}, "progress": {"type": "integer", "format": "int64", "title": "节点进度"}, "state": {"$ref": "#/definitions/commonOperationalPositionTaskState", "title": "节点状态"}}}, "OperationalPositionRspTaskNodeReward": {"type": "object", "properties": {"icon": {"type": "string", "title": "奖励 icon"}, "description": {"type": "string", "title": "奖励描述"}}}, "OperationalPositionRspVideo": {"type": "object", "properties": {"url": {"type": "string", "title": "视频链接"}, "poster": {"type": "string", "title": "封面图"}}}, "QueryStatusRspPlayer": {"type": "object", "properties": {"index": {"type": "integer", "format": "int64", "title": "座位号"}, "user": {"$ref": "#/definitions/interactive_gameUser"}, "role": {"$ref": "#/definitions/commonRole"}, "ready": {"type": "boolean", "title": "是否准备 -- 废弃"}, "eliminated": {"type": "boolean", "title": "被淘汰"}}}, "QueryStatusRspRankStatus": {"type": "object", "properties": {"show": {"type": "boolean", "title": "是否展示"}, "rank": {"type": "string", "format": "int64", "title": "排名"}, "description": {"type": "string", "title": "描述文案"}, "payMode": {"$ref": "#/definitions/commonPayMode"}, "periodicType": {"$ref": "#/definitions/commonRankPeriodicType"}}}, "commonGameRoomConfig": {"type": "object", "properties": {"modeId": {"type": "string", "title": "模式 id (关联组局配置)"}, "joinMode": {"$ref": "#/definitions/commonJoinMode", "title": "加入模式"}, "payConfig": {"$ref": "#/definitions/commonGameRoomPayConfig", "title": "付费配置"}, "autoBegin": {"type": "boolean", "title": "人满自动开"}, "makeUpConfig": {"$ref": "#/definitions/commonGameRoomMakeUpConfig", "title": "模式 id 对应的组局配置"}, "allowFirstBegin": {"type": "boolean", "title": "允许第一个加入的人开始游戏"}, "ownerJoin": {"type": "boolean", "title": "房主必须参与游戏"}, "disableOwnerAutoJoin": {"type": "boolean", "description": "关闭房主自动参与", "title": "bool requireMike = 8; // 需要上麦\nstring preparePageUrl = 9; // 准备页链接\nstring gamePageUrl = 10; // 游戏页链接"}, "backgroundMusic": {"type": "string", "title": "背景音乐"}, "mikeMode": {"$ref": "#/definitions/commonMikeMode", "title": "连麦模式"}, "withManager": {"type": "boolean", "title": "管理员房间"}, "gameName": {"type": "string", "title": "游戏名称"}, "isVoiceGame": {"type": "boolean", "title": "语音游戏"}, "gameType": {"$ref": "#/definitions/commonGameType", "title": "游戏类型"}}}, "commonGameRoomMakeUpConfig": {"type": "object", "properties": {"minPlayers": {"type": "integer", "format": "int64", "title": "最少开始人数"}, "maxPlayers": {"type": "integer", "format": "int64", "title": "最大加入人数"}, "groupNum": {"type": "integer", "format": "int64", "description": "队伍人数", "title": "BeginPrivilege beginPrivilege = 3;\nbool ownerJoin = 4; // 房主必须参与游戏"}, "gameConfigs": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传配置"}}}, "commonGameRoomPayConfig": {"type": "object", "properties": {"mode": {"$ref": "#/definitions/commonPayMode"}, "assetId": {"type": "string", "format": "int64", "title": "扣费资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "扣费资产数量"}, "payModeName": {"type": "string", "title": "扣费模式名称"}, "payModeId": {"type": "string", "title": "扣费模式 id"}}}, "commonGameType": {"type": "string", "enum": ["GameTypeDefault", "GameTypeSud"], "default": "GameTypeDefault", "title": "- GameTypeSud: 即构"}, "commonGender": {"type": "string", "enum": ["Unknown", "Man", "Woman"], "default": "Unknown"}, "commonJoinMode": {"type": "string", "enum": ["JoinUnlimited", "Join<PERSON><PERSON>"], "default": "JoinUnlimited", "title": "- JoinUnlimited: 自由落座\n - JoinPicked: 主播选择"}, "commonKgInfo": {"type": "object", "properties": {"roomType": {"$ref": "#/definitions/commonKgInfoRoomType"}, "gameId": {"type": "string", "title": "string payConfig = 2;"}, "liveMikeMode": {"$ref": "#/definitions/KgInfoLiveMikeMode"}}}, "commonKgInfoRoomType": {"type": "string", "enum": ["RoomTypeUnknown", "RoomTypeLive", "RoomTypeSocialKtv", "RoomTypeSingleMikeKtv"], "default": "RoomTypeUnknown", "title": "- RoomTypeLive: 直播\n - RoomTypeSocialKtv: 欢聚\n - RoomTypeSingleMikeKtv: 单麦"}, "commonMatchAuthorityType": {"type": "string", "enum": ["MatchAuthorityTypeDefault", "MatchAuthorityTypeAll"], "default": "MatchAuthorityTypeDefault", "title": "- MatchAuthorityTypeDefault: 只有公开房间可匹配\n - MatchAuthorityTypeAll: 私密房也可以匹配"}, "commonMatchStatus": {"type": "string", "enum": ["MatchStatusNone", "MatchStatusEnable", "MatchStatusMatching", "MatchStatusSuccess"], "default": "MatchStatusNone", "title": "- MatchStatusNone: 关闭匹配功能\n - MatchStatusEnable: 开启匹配功能\n - MatchStatusMatching: 匹配中\n - MatchStatusSuccess: 匹配成功"}, "commonMatchType": {"type": "string", "enum": ["MatchTypeNone", "MatchTypeMatch", "MatchTypeBegin"], "default": "MatchTypeNone", "title": "- MatchTypeNone: 无\n - MatchTypeMatch: 匹配路人\n - MatchTypeBegin: 立即开始"}, "commonMessage": {"type": "object", "properties": {"seq": {"type": "string", "format": "int64", "title": "消息序号"}, "messageType": {"$ref": "#/definitions/commonMessageType", "title": "消息类型"}, "timestamp": {"type": "string", "format": "int64", "title": "消息时间戳"}, "messageText": {"type": "string", "title": "消息文案"}}}, "commonMessageType": {"type": "string", "enum": ["MessageNone", "MessageToast", "MessageKickout"], "default": "MessageNone", "title": "- MessageNone: 无效消息\n - MessageToast: 提示 toast\n - MessageKickout: 被踢"}, "commonMikeMode": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Mike<PERSON><PERSON><PERSON><PERSON>", "MikeAutoClose", "<PERSON><PERSON><PERSON><PERSON>"], "default": "<PERSON><PERSON><PERSON>", "title": "- MikeNone: 不上麦\n - MikeAutoOpen: 上麦自动开\n - MikeAutoClose: 上麦自动关\n - MikeForce: 上麦自动关"}, "commonOperationalPositionTaskButtonAction": {"type": "string", "enum": ["OperationalPositionTaskButtonActionNone", "OperationalPositionTaskButtonActionEnqueue", "OperationalPositionTaskButtonActionMatch"], "default": "OperationalPositionTaskButtonActionNone", "title": "- OperationalPositionTaskButtonActionEnqueue: 入座\n - OperationalPositionTaskButtonActionMatch: 匹配"}, "commonOperationalPositionTaskState": {"type": "string", "enum": ["OperationalPositionTaskStateNone", "OperationalPositionTaskStateDoing", "OperationalPositionTaskStateAvailable", "OperationalPositionTaskStateComplete"], "default": "OperationalPositionTaskStateNone", "title": "- OperationalPositionTaskStateDoing: 进行中\n - OperationalPositionTaskStateAvailable: 已完成 可领取\n - OperationalPositionTaskStateComplete: 已完成 已领取"}, "commonOperationalPositionTaskType": {"type": "string", "enum": ["OperationalPositionTaskTypeNone", "OperationalPositionTaskTypeNormal", "OperationalPositionTaskTypeStep"], "default": "OperationalPositionTaskTypeNone", "title": "- OperationalPositionTaskTypeNormal: 普通任务\n - OperationalPositionTaskTypeStep: 阶梯任务"}, "commonPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "commonPlatformInfo": {"type": "object", "properties": {"platId": {"type": "string", "format": "uint64", "title": "EPlatID"}, "kg": {"$ref": "#/definitions/commonKgInfo"}}}, "commonRankPeriodicType": {"type": "string", "enum": ["RankPeriodicDay", "RankPeriodicWeek"], "default": "RankPeriodicDay", "title": "- RankPeriodicDay: 每日\n - RankPeriodicWeek: 每周"}, "commonRole": {"type": "string", "enum": ["RoleNone", "<PERSON><PERSON><PERSON>er", "RoleCaptain", "RoleManager"], "default": "RoleNone", "title": "- RoleOwner: 房主 (开始游戏 + 选人 + 踢人)\n - RoleCaptain: 队长 (开始游戏)\n - RoleManager: 管理员 (开始游戏 + 选人 + 踢人)"}, "commonRoomStatus": {"type": "string", "enum": ["RoomNone", "RoomPending", "RoomPlaying"], "default": "RoomNone", "title": "- RoomPending: 组局中\n - RoomPlaying: 游戏中"}, "interactive_gameBeginGameReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameBeginGameRsp": {"type": "object"}, "interactive_gameCancelMatchReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameCancelMatchRsp": {"type": "object"}, "interactive_gameDequeueReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameDequeueRsp": {"type": "object"}, "interactive_gameEnqueueReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "index": {"type": "integer", "format": "int64", "title": "座位号 传 0 自动选"}}}, "interactive_gameEnqueueRsp": {"type": "object"}, "interactive_gameKickOutReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "openId": {"type": "string"}}}, "interactive_gameKickOutRsp": {"type": "object"}, "interactive_gameLeaveReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameLeaveRsp": {"type": "object"}, "interactive_gameMatchOption": {"type": "object", "properties": {"disableRoomCache": {"type": "boolean", "title": "实时回源平台查房间信息"}, "autoTurn2Public": {"type": "boolean", "title": "自动将房间转为公开,只对房主有效"}}}, "interactive_gameMatchReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "option": {"$ref": "#/definitions/interactive_gameMatchOption", "title": "匹配选项"}}}, "interactive_gameMatchRsp": {"type": "object", "title": "房间私密信息弹窗,只有对房主才有效,请判断错误码code.proto下code.InteractiveGamePrivate后弹窗提示"}, "interactive_gameOperationalPositionReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameOperationalPositionRsp": {"type": "object", "properties": {"positions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/OperationalPositionRspPosition"}}, "slideSeconds": {"type": "integer", "format": "int64", "title": "轮播间隔"}}}, "interactive_gameParticipant": {"type": "object", "properties": {"user": {"$ref": "#/definitions/interactive_gameUser"}}}, "interactive_gameParticipantListReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "passback": {"type": "string"}}}, "interactive_gameParticipantListRsp": {"type": "object", "properties": {"participants": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interactive_gameParticipant"}}, "hasMore": {"type": "boolean"}, "passback": {"type": "string"}, "gameName": {"type": "string"}, "gameIcon": {"type": "string"}, "participantNum": {"type": "integer", "format": "int64", "title": "排队人数"}}}, "interactive_gamePickPlayerReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "openId": {"type": "string"}, "index": {"type": "integer", "format": "int64"}}}, "interactive_gamePickPlayerRsp": {"type": "object"}, "interactive_gamePlayAgainReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gamePlayAgainRsp": {"type": "object"}, "interactive_gameQueryStatusReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "passback": {"type": "string", "title": "回传数据 (可选)"}, "matchType": {"$ref": "#/definitions/commonMatchAuthorityType", "title": "新旧版组局页需要区分"}}}, "interactive_gameQueryStatusRsp": {"type": "object", "properties": {"status": {"$ref": "#/definitions/commonRoomStatus"}, "gameAppId": {"type": "string"}, "owner": {"$ref": "#/definitions/interactive_gameUser"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QueryStatusRspPlayer"}}, "participantNum": {"type": "integer", "format": "int64", "title": "排队人数"}, "isQueued": {"type": "boolean", "title": "是否排队"}, "passback": {"type": "string", "title": "回传数据"}, "config": {"$ref": "#/definitions/commonGameRoomConfig", "title": "配置"}, "showBeginGame": {"type": "boolean", "title": "是否有权限开启游戏"}, "queueTimestamp": {"type": "string", "format": "int64", "title": "最新申请队列时间戳"}, "reportExtend": {"type": "object", "additionalProperties": {"type": "string"}, "title": "上报扩展字段"}, "role": {"$ref": "#/definitions/commonRole"}, "roundId": {"type": "string"}, "messages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonMessage"}, "description": "新消息", "title": "uint64 rawUID = 14; // 主人态 uid"}, "gameExtend": {"type": "object", "additionalProperties": {"type": "string"}, "title": "游戏扩展字段"}, "rankStatus": {"$ref": "#/definitions/QueryStatusRspRankStatus", "title": "废弃"}, "settlementRoundId": {"type": "string", "title": "结算 roundId 游戏中返回当前 组局中返回上一局"}, "matchStatus": {"$ref": "#/definitions/commonMatchStatus", "title": "匹配状态"}, "matchedRoomId": {"type": "string", "title": "匹配成功跳转的 roomId -- 废弃"}, "joinFromMatch": {"type": "boolean", "title": "来自匹配自动上座 -- 废弃"}, "platformInfo": {"$ref": "#/definitions/commonPlatformInfo"}, "matchedRoomType": {"type": "integer", "format": "int32", "title": "匹配上的房间类型 -- 废弃"}, "gameModeName": {"type": "string", "title": "游戏模式名称"}, "gameIcon": {"type": "string", "title": "游戏图标"}, "matchType": {"$ref": "#/definitions/commonMatchType", "title": "匹配类型 -- 废弃"}, "freeTimes": {"type": "string", "format": "int64", "title": "免费次数"}, "autoJoin": {"type": "boolean", "title": "需要自动落座"}, "matchSource": {"type": "string", "title": "匹配来源 -- 废弃"}, "joinUseFree": {"type": "boolean", "title": "使用免费次数入座"}, "rankIcon": {"type": "string", "title": "段位图标"}}}, "interactive_gameUser": {"type": "object", "properties": {"openId": {"type": "string"}, "avatar": {"type": "string"}, "nick": {"type": "string"}, "gender": {"$ref": "#/definitions/commonGender"}, "age": {"type": "integer", "format": "int64"}, "treasureLevel": {"type": "integer", "format": "int64"}, "vipLevel": {"type": "integer", "format": "int64"}, "avatarFrame": {"type": "string", "title": "头像框"}, "city": {"type": "string", "title": "城市"}, "encryptUID": {"type": "string", "title": "加密 uid"}}}, "interactive_gameVerifyBalanceReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameVerifyBalanceRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}