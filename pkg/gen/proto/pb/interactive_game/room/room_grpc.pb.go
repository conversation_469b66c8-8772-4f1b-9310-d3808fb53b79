// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/room/room.proto

package interactive_game

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Room_QueryStatus_FullMethodName         = "/interactive_game.Room/QueryStatus"
	Room_Enqueue_FullMethodName             = "/interactive_game.Room/Enqueue"
	Room_Dequeue_FullMethodName             = "/interactive_game.Room/Dequeue"
	Room_Leave_FullMethodName               = "/interactive_game.Room/Leave"
	Room_ParticipantList_FullMethodName     = "/interactive_game.Room/ParticipantList"
	Room_PickPlayer_FullMethodName          = "/interactive_game.Room/PickPlayer"
	Room_KickOut_FullMethodName             = "/interactive_game.Room/KickOut"
	Room_BeginGame_FullMethodName           = "/interactive_game.Room/BeginGame"
	Room_PlayAgain_FullMethodName           = "/interactive_game.Room/PlayAgain"
	Room_Match_FullMethodName               = "/interactive_game.Room/Match"
	Room_CancelMatch_FullMethodName         = "/interactive_game.Room/CancelMatch"
	Room_OperationalPosition_FullMethodName = "/interactive_game.Room/OperationalPosition"
	Room_VerifyBalance_FullMethodName       = "/interactive_game.Room/VerifyBalance"
)

// RoomClient is the client API for Room service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoomClient interface {
	// 查询状态
	QueryStatus(ctx context.Context, in *QueryStatusReq, opts ...grpc.CallOption) (*QueryStatusRsp, error)
	// 用户加入
	Enqueue(ctx context.Context, in *EnqueueReq, opts ...grpc.CallOption) (*EnqueueRsp, error)
	// 用户取消申请
	Dequeue(ctx context.Context, in *DequeueReq, opts ...grpc.CallOption) (*DequeueRsp, error)
	// 用户离开
	Leave(ctx context.Context, in *LeaveReq, opts ...grpc.CallOption) (*LeaveRsp, error)
	// 参与者列表
	ParticipantList(ctx context.Context, in *ParticipantListReq, opts ...grpc.CallOption) (*ParticipantListRsp, error)
	// 房主选择
	PickPlayer(ctx context.Context, in *PickPlayerReq, opts ...grpc.CallOption) (*PickPlayerRsp, error)
	// 踢人
	KickOut(ctx context.Context, in *KickOutReq, opts ...grpc.CallOption) (*KickOutRsp, error)
	// 开始游戏
	BeginGame(ctx context.Context, in *BeginGameReq, opts ...grpc.CallOption) (*BeginGameRsp, error)
	// 再来一局
	PlayAgain(ctx context.Context, in *PlayAgainReq, opts ...grpc.CallOption) (*PlayAgainRsp, error)
	// 匹配路人
	Match(ctx context.Context, in *MatchReq, opts ...grpc.CallOption) (*MatchRsp, error)
	// 取消匹配
	CancelMatch(ctx context.Context, in *CancelMatchReq, opts ...grpc.CallOption) (*CancelMatchRsp, error)
	// 运营位
	OperationalPosition(ctx context.Context, in *OperationalPositionReq, opts ...grpc.CallOption) (*OperationalPositionRsp, error)
	// 校验余额
	VerifyBalance(ctx context.Context, in *VerifyBalanceReq, opts ...grpc.CallOption) (*VerifyBalanceRsp, error)
}

type roomClient struct {
	cc grpc.ClientConnInterface
}

func NewRoomClient(cc grpc.ClientConnInterface) RoomClient {
	return &roomClient{cc}
}

func (c *roomClient) QueryStatus(ctx context.Context, in *QueryStatusReq, opts ...grpc.CallOption) (*QueryStatusRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryStatusRsp)
	err := c.cc.Invoke(ctx, Room_QueryStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) Enqueue(ctx context.Context, in *EnqueueReq, opts ...grpc.CallOption) (*EnqueueRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EnqueueRsp)
	err := c.cc.Invoke(ctx, Room_Enqueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) Dequeue(ctx context.Context, in *DequeueReq, opts ...grpc.CallOption) (*DequeueRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DequeueRsp)
	err := c.cc.Invoke(ctx, Room_Dequeue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) Leave(ctx context.Context, in *LeaveReq, opts ...grpc.CallOption) (*LeaveRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LeaveRsp)
	err := c.cc.Invoke(ctx, Room_Leave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) ParticipantList(ctx context.Context, in *ParticipantListReq, opts ...grpc.CallOption) (*ParticipantListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ParticipantListRsp)
	err := c.cc.Invoke(ctx, Room_ParticipantList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) PickPlayer(ctx context.Context, in *PickPlayerReq, opts ...grpc.CallOption) (*PickPlayerRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PickPlayerRsp)
	err := c.cc.Invoke(ctx, Room_PickPlayer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) KickOut(ctx context.Context, in *KickOutReq, opts ...grpc.CallOption) (*KickOutRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KickOutRsp)
	err := c.cc.Invoke(ctx, Room_KickOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) BeginGame(ctx context.Context, in *BeginGameReq, opts ...grpc.CallOption) (*BeginGameRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BeginGameRsp)
	err := c.cc.Invoke(ctx, Room_BeginGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) PlayAgain(ctx context.Context, in *PlayAgainReq, opts ...grpc.CallOption) (*PlayAgainRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlayAgainRsp)
	err := c.cc.Invoke(ctx, Room_PlayAgain_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) Match(ctx context.Context, in *MatchReq, opts ...grpc.CallOption) (*MatchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MatchRsp)
	err := c.cc.Invoke(ctx, Room_Match_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) CancelMatch(ctx context.Context, in *CancelMatchReq, opts ...grpc.CallOption) (*CancelMatchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelMatchRsp)
	err := c.cc.Invoke(ctx, Room_CancelMatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) OperationalPosition(ctx context.Context, in *OperationalPositionReq, opts ...grpc.CallOption) (*OperationalPositionRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OperationalPositionRsp)
	err := c.cc.Invoke(ctx, Room_OperationalPosition_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomClient) VerifyBalance(ctx context.Context, in *VerifyBalanceReq, opts ...grpc.CallOption) (*VerifyBalanceRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyBalanceRsp)
	err := c.cc.Invoke(ctx, Room_VerifyBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoomServer is the server API for Room service.
// All implementations should embed UnimplementedRoomServer
// for forward compatibility
type RoomServer interface {
	// 查询状态
	QueryStatus(context.Context, *QueryStatusReq) (*QueryStatusRsp, error)
	// 用户加入
	Enqueue(context.Context, *EnqueueReq) (*EnqueueRsp, error)
	// 用户取消申请
	Dequeue(context.Context, *DequeueReq) (*DequeueRsp, error)
	// 用户离开
	Leave(context.Context, *LeaveReq) (*LeaveRsp, error)
	// 参与者列表
	ParticipantList(context.Context, *ParticipantListReq) (*ParticipantListRsp, error)
	// 房主选择
	PickPlayer(context.Context, *PickPlayerReq) (*PickPlayerRsp, error)
	// 踢人
	KickOut(context.Context, *KickOutReq) (*KickOutRsp, error)
	// 开始游戏
	BeginGame(context.Context, *BeginGameReq) (*BeginGameRsp, error)
	// 再来一局
	PlayAgain(context.Context, *PlayAgainReq) (*PlayAgainRsp, error)
	// 匹配路人
	Match(context.Context, *MatchReq) (*MatchRsp, error)
	// 取消匹配
	CancelMatch(context.Context, *CancelMatchReq) (*CancelMatchRsp, error)
	// 运营位
	OperationalPosition(context.Context, *OperationalPositionReq) (*OperationalPositionRsp, error)
	// 校验余额
	VerifyBalance(context.Context, *VerifyBalanceReq) (*VerifyBalanceRsp, error)
}

// UnimplementedRoomServer should be embedded to have forward compatible implementations.
type UnimplementedRoomServer struct {
}

func (UnimplementedRoomServer) QueryStatus(context.Context, *QueryStatusReq) (*QueryStatusRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryStatus not implemented")
}
func (UnimplementedRoomServer) Enqueue(context.Context, *EnqueueReq) (*EnqueueRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Enqueue not implemented")
}
func (UnimplementedRoomServer) Dequeue(context.Context, *DequeueReq) (*DequeueRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Dequeue not implemented")
}
func (UnimplementedRoomServer) Leave(context.Context, *LeaveReq) (*LeaveRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Leave not implemented")
}
func (UnimplementedRoomServer) ParticipantList(context.Context, *ParticipantListReq) (*ParticipantListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParticipantList not implemented")
}
func (UnimplementedRoomServer) PickPlayer(context.Context, *PickPlayerReq) (*PickPlayerRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PickPlayer not implemented")
}
func (UnimplementedRoomServer) KickOut(context.Context, *KickOutReq) (*KickOutRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KickOut not implemented")
}
func (UnimplementedRoomServer) BeginGame(context.Context, *BeginGameReq) (*BeginGameRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BeginGame not implemented")
}
func (UnimplementedRoomServer) PlayAgain(context.Context, *PlayAgainReq) (*PlayAgainRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlayAgain not implemented")
}
func (UnimplementedRoomServer) Match(context.Context, *MatchReq) (*MatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Match not implemented")
}
func (UnimplementedRoomServer) CancelMatch(context.Context, *CancelMatchReq) (*CancelMatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelMatch not implemented")
}
func (UnimplementedRoomServer) OperationalPosition(context.Context, *OperationalPositionReq) (*OperationalPositionRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperationalPosition not implemented")
}
func (UnimplementedRoomServer) VerifyBalance(context.Context, *VerifyBalanceReq) (*VerifyBalanceRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyBalance not implemented")
}

// UnsafeRoomServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoomServer will
// result in compilation errors.
type UnsafeRoomServer interface {
	mustEmbedUnimplementedRoomServer()
}

func RegisterRoomServer(s grpc.ServiceRegistrar, srv RoomServer) {
	s.RegisterService(&Room_ServiceDesc, srv)
}

func _Room_QueryStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).QueryStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_QueryStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).QueryStatus(ctx, req.(*QueryStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_Enqueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnqueueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).Enqueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_Enqueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).Enqueue(ctx, req.(*EnqueueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_Dequeue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DequeueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).Dequeue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_Dequeue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).Dequeue(ctx, req.(*DequeueReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_Leave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LeaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).Leave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_Leave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).Leave(ctx, req.(*LeaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_ParticipantList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParticipantListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).ParticipantList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_ParticipantList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).ParticipantList(ctx, req.(*ParticipantListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_PickPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PickPlayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).PickPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_PickPlayer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).PickPlayer(ctx, req.(*PickPlayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_KickOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickOutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).KickOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_KickOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).KickOut(ctx, req.(*KickOutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_BeginGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BeginGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).BeginGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_BeginGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).BeginGame(ctx, req.(*BeginGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_PlayAgain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayAgainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).PlayAgain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_PlayAgain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).PlayAgain(ctx, req.(*PlayAgainReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_Match_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).Match(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_Match_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).Match(ctx, req.(*MatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_CancelMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).CancelMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_CancelMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).CancelMatch(ctx, req.(*CancelMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_OperationalPosition_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OperationalPositionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).OperationalPosition(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_OperationalPosition_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).OperationalPosition(ctx, req.(*OperationalPositionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Room_VerifyBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomServer).VerifyBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Room_VerifyBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomServer).VerifyBalance(ctx, req.(*VerifyBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Room_ServiceDesc is the grpc.ServiceDesc for Room service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Room_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.Room",
	HandlerType: (*RoomServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryStatus",
			Handler:    _Room_QueryStatus_Handler,
		},
		{
			MethodName: "Enqueue",
			Handler:    _Room_Enqueue_Handler,
		},
		{
			MethodName: "Dequeue",
			Handler:    _Room_Dequeue_Handler,
		},
		{
			MethodName: "Leave",
			Handler:    _Room_Leave_Handler,
		},
		{
			MethodName: "ParticipantList",
			Handler:    _Room_ParticipantList_Handler,
		},
		{
			MethodName: "PickPlayer",
			Handler:    _Room_PickPlayer_Handler,
		},
		{
			MethodName: "KickOut",
			Handler:    _Room_KickOut_Handler,
		},
		{
			MethodName: "BeginGame",
			Handler:    _Room_BeginGame_Handler,
		},
		{
			MethodName: "PlayAgain",
			Handler:    _Room_PlayAgain_Handler,
		},
		{
			MethodName: "Match",
			Handler:    _Room_Match_Handler,
		},
		{
			MethodName: "CancelMatch",
			Handler:    _Room_CancelMatch_Handler,
		},
		{
			MethodName: "OperationalPosition",
			Handler:    _Room_OperationalPosition_Handler,
		},
		{
			MethodName: "VerifyBalance",
			Handler:    _Room_VerifyBalance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/room/room.proto",
}
