// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/room/room.proto

package interactive_game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId        string        `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Avatar        string        `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Nick          string        `protobuf:"bytes,3,opt,name=nick,proto3" json:"nick,omitempty"`
	Gender        common.Gender `protobuf:"varint,4,opt,name=gender,proto3,enum=interactive_game.common.Gender" json:"gender,omitempty"`
	Age           uint32        `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	TreasureLevel uint32        `protobuf:"varint,6,opt,name=treasureLevel,proto3" json:"treasureLevel,omitempty"`
	VipLevel      uint32        `protobuf:"varint,7,opt,name=vipLevel,proto3" json:"vipLevel,omitempty"`
	AvatarFrame   string        `protobuf:"bytes,8,opt,name=avatarFrame,proto3" json:"avatarFrame,omitempty"` // 头像框
	City          string        `protobuf:"bytes,9,opt,name=city,proto3" json:"city,omitempty"`               // 城市
	EncryptUID    string        `protobuf:"bytes,10,opt,name=encryptUID,proto3" json:"encryptUID,omitempty"`  // 加密 uid
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *User) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *User) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *User) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *User) GetAge() uint32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *User) GetTreasureLevel() uint32 {
	if x != nil {
		return x.TreasureLevel
	}
	return 0
}

func (x *User) GetVipLevel() uint32 {
	if x != nil {
		return x.VipLevel
	}
	return 0
}

func (x *User) GetAvatarFrame() string {
	if x != nil {
		return x.AvatarFrame
	}
	return ""
}

func (x *User) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *User) GetEncryptUID() string {
	if x != nil {
		return x.EncryptUID
	}
	return ""
}

type QueryStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId    string                    `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Passback  string                    `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`                                                    // 回传数据 (可选)
	MatchType common.MatchAuthorityType `protobuf:"varint,3,opt,name=matchType,proto3,enum=interactive_game.common.MatchAuthorityType" json:"matchType,omitempty"` // 新旧版组局页需要区分
}

func (x *QueryStatusReq) Reset() {
	*x = QueryStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusReq) ProtoMessage() {}

func (x *QueryStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusReq.ProtoReflect.Descriptor instead.
func (*QueryStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{1}
}

func (x *QueryStatusReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *QueryStatusReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *QueryStatusReq) GetMatchType() common.MatchAuthorityType {
	if x != nil {
		return x.MatchType
	}
	return common.MatchAuthorityType(0)
}

type QueryStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         common.RoomStatus        `protobuf:"varint,1,opt,name=status,proto3,enum=interactive_game.common.RoomStatus" json:"status,omitempty"`
	GameAppId      string                   `protobuf:"bytes,2,opt,name=gameAppId,proto3" json:"gameAppId,omitempty"`
	Owner          *User                    `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner,omitempty"`
	Players        []*QueryStatusRsp_Player `protobuf:"bytes,4,rep,name=players,proto3" json:"players,omitempty"`
	ParticipantNum uint32                   `protobuf:"varint,5,opt,name=participantNum,proto3" json:"participantNum,omitempty"`                                                                                     // 排队人数
	IsQueued       bool                     `protobuf:"varint,6,opt,name=isQueued,proto3" json:"isQueued,omitempty"`                                                                                                 // 是否排队
	Passback       string                   `protobuf:"bytes,7,opt,name=passback,proto3" json:"passback,omitempty"`                                                                                                  // 回传数据
	Config         *common.GameRoomConfig   `protobuf:"bytes,8,opt,name=config,proto3" json:"config,omitempty"`                                                                                                      // 配置
	ShowBeginGame  bool                     `protobuf:"varint,9,opt,name=showBeginGame,proto3" json:"showBeginGame,omitempty"`                                                                                       // 是否有权限开启游戏
	QueueTimestamp int64                    `protobuf:"varint,10,opt,name=queueTimestamp,proto3" json:"queueTimestamp,omitempty"`                                                                                    // 最新申请队列时间戳
	ReportExtend   map[string]string        `protobuf:"bytes,11,rep,name=reportExtend,proto3" json:"reportExtend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 上报扩展字段
	Role           common.Role              `protobuf:"varint,12,opt,name=role,proto3,enum=interactive_game.common.Role" json:"role,omitempty"`
	RoundId        string                   `protobuf:"bytes,13,opt,name=roundId,proto3" json:"roundId,omitempty"`
	// uint64 rawUID = 14; // 主人态 uid
	Messages          []*common.Message          `protobuf:"bytes,15,rep,name=messages,proto3" json:"messages,omitempty"`                                                                                             // 新消息
	GameExtend        map[string]string          `protobuf:"bytes,16,rep,name=gameExtend,proto3" json:"gameExtend,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 游戏扩展字段
	RankStatus        *QueryStatusRsp_RankStatus `protobuf:"bytes,17,opt,name=rankStatus,proto3" json:"rankStatus,omitempty"`                                                                                         // 废弃
	SettlementRoundId string                     `protobuf:"bytes,18,opt,name=settlementRoundId,proto3" json:"settlementRoundId,omitempty"`                                                                           // 结算 roundId 游戏中返回当前 组局中返回上一局
	MatchStatus       common.MatchStatus         `protobuf:"varint,19,opt,name=matchStatus,proto3,enum=interactive_game.common.MatchStatus" json:"matchStatus,omitempty"`                                             // 匹配状态
	MatchedRoomId     string                     `protobuf:"bytes,20,opt,name=matchedRoomId,proto3" json:"matchedRoomId,omitempty"`                                                                                   // 匹配成功跳转的 roomId -- 废弃
	JoinFromMatch     bool                       `protobuf:"varint,21,opt,name=joinFromMatch,proto3" json:"joinFromMatch,omitempty"`                                                                                  // 来自匹配自动上座 -- 废弃
	PlatformInfo      *common.PlatformInfo       `protobuf:"bytes,22,opt,name=platformInfo,proto3" json:"platformInfo,omitempty"`
	MatchedRoomType   int32                      `protobuf:"varint,23,opt,name=matchedRoomType,proto3" json:"matchedRoomType,omitempty"`                            // 匹配上的房间类型 -- 废弃
	GameModeName      string                     `protobuf:"bytes,24,opt,name=gameModeName,proto3" json:"gameModeName,omitempty"`                                   // 游戏模式名称
	GameIcon          string                     `protobuf:"bytes,25,opt,name=gameIcon,proto3" json:"gameIcon,omitempty"`                                           // 游戏图标
	MatchType         common.MatchType           `protobuf:"varint,26,opt,name=matchType,proto3,enum=interactive_game.common.MatchType" json:"matchType,omitempty"` // 匹配类型 -- 废弃
	FreeTimes         int64                      `protobuf:"varint,27,opt,name=freeTimes,proto3" json:"freeTimes,omitempty"`                                        // 免费次数
	AutoJoin          bool                       `protobuf:"varint,28,opt,name=autoJoin,proto3" json:"autoJoin,omitempty"`                                          // 需要自动落座
	MatchSource       string                     `protobuf:"bytes,29,opt,name=matchSource,proto3" json:"matchSource,omitempty"`                                     // 匹配来源 -- 废弃
	JoinUseFree       bool                       `protobuf:"varint,30,opt,name=joinUseFree,proto3" json:"joinUseFree,omitempty"`                                    // 使用免费次数入座
	RankIcon          string                     `protobuf:"bytes,31,opt,name=rankIcon,proto3" json:"rankIcon,omitempty"`                                           // 段位图标
}

func (x *QueryStatusRsp) Reset() {
	*x = QueryStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRsp) ProtoMessage() {}

func (x *QueryStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRsp.ProtoReflect.Descriptor instead.
func (*QueryStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{2}
}

func (x *QueryStatusRsp) GetStatus() common.RoomStatus {
	if x != nil {
		return x.Status
	}
	return common.RoomStatus(0)
}

func (x *QueryStatusRsp) GetGameAppId() string {
	if x != nil {
		return x.GameAppId
	}
	return ""
}

func (x *QueryStatusRsp) GetOwner() *User {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *QueryStatusRsp) GetPlayers() []*QueryStatusRsp_Player {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *QueryStatusRsp) GetParticipantNum() uint32 {
	if x != nil {
		return x.ParticipantNum
	}
	return 0
}

func (x *QueryStatusRsp) GetIsQueued() bool {
	if x != nil {
		return x.IsQueued
	}
	return false
}

func (x *QueryStatusRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *QueryStatusRsp) GetConfig() *common.GameRoomConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *QueryStatusRsp) GetShowBeginGame() bool {
	if x != nil {
		return x.ShowBeginGame
	}
	return false
}

func (x *QueryStatusRsp) GetQueueTimestamp() int64 {
	if x != nil {
		return x.QueueTimestamp
	}
	return 0
}

func (x *QueryStatusRsp) GetReportExtend() map[string]string {
	if x != nil {
		return x.ReportExtend
	}
	return nil
}

func (x *QueryStatusRsp) GetRole() common.Role {
	if x != nil {
		return x.Role
	}
	return common.Role(0)
}

func (x *QueryStatusRsp) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *QueryStatusRsp) GetMessages() []*common.Message {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *QueryStatusRsp) GetGameExtend() map[string]string {
	if x != nil {
		return x.GameExtend
	}
	return nil
}

func (x *QueryStatusRsp) GetRankStatus() *QueryStatusRsp_RankStatus {
	if x != nil {
		return x.RankStatus
	}
	return nil
}

func (x *QueryStatusRsp) GetSettlementRoundId() string {
	if x != nil {
		return x.SettlementRoundId
	}
	return ""
}

func (x *QueryStatusRsp) GetMatchStatus() common.MatchStatus {
	if x != nil {
		return x.MatchStatus
	}
	return common.MatchStatus(0)
}

func (x *QueryStatusRsp) GetMatchedRoomId() string {
	if x != nil {
		return x.MatchedRoomId
	}
	return ""
}

func (x *QueryStatusRsp) GetJoinFromMatch() bool {
	if x != nil {
		return x.JoinFromMatch
	}
	return false
}

func (x *QueryStatusRsp) GetPlatformInfo() *common.PlatformInfo {
	if x != nil {
		return x.PlatformInfo
	}
	return nil
}

func (x *QueryStatusRsp) GetMatchedRoomType() int32 {
	if x != nil {
		return x.MatchedRoomType
	}
	return 0
}

func (x *QueryStatusRsp) GetGameModeName() string {
	if x != nil {
		return x.GameModeName
	}
	return ""
}

func (x *QueryStatusRsp) GetGameIcon() string {
	if x != nil {
		return x.GameIcon
	}
	return ""
}

func (x *QueryStatusRsp) GetMatchType() common.MatchType {
	if x != nil {
		return x.MatchType
	}
	return common.MatchType(0)
}

func (x *QueryStatusRsp) GetFreeTimes() int64 {
	if x != nil {
		return x.FreeTimes
	}
	return 0
}

func (x *QueryStatusRsp) GetAutoJoin() bool {
	if x != nil {
		return x.AutoJoin
	}
	return false
}

func (x *QueryStatusRsp) GetMatchSource() string {
	if x != nil {
		return x.MatchSource
	}
	return ""
}

func (x *QueryStatusRsp) GetJoinUseFree() bool {
	if x != nil {
		return x.JoinUseFree
	}
	return false
}

func (x *QueryStatusRsp) GetRankIcon() string {
	if x != nil {
		return x.RankIcon
	}
	return ""
}

type EnqueueReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Index  uint32 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"` // 座位号 传 0 自动选
}

func (x *EnqueueReq) Reset() {
	*x = EnqueueReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnqueueReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnqueueReq) ProtoMessage() {}

func (x *EnqueueReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnqueueReq.ProtoReflect.Descriptor instead.
func (*EnqueueReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{3}
}

func (x *EnqueueReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *EnqueueReq) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type EnqueueRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnqueueRsp) Reset() {
	*x = EnqueueRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnqueueRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnqueueRsp) ProtoMessage() {}

func (x *EnqueueRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnqueueRsp.ProtoReflect.Descriptor instead.
func (*EnqueueRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{4}
}

type DequeueReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *DequeueReq) Reset() {
	*x = DequeueReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DequeueReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DequeueReq) ProtoMessage() {}

func (x *DequeueReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DequeueReq.ProtoReflect.Descriptor instead.
func (*DequeueReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{5}
}

func (x *DequeueReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type DequeueRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DequeueRsp) Reset() {
	*x = DequeueRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DequeueRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DequeueRsp) ProtoMessage() {}

func (x *DequeueRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DequeueRsp.ProtoReflect.Descriptor instead.
func (*DequeueRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{6}
}

type LeaveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *LeaveReq) Reset() {
	*x = LeaveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveReq) ProtoMessage() {}

func (x *LeaveReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveReq.ProtoReflect.Descriptor instead.
func (*LeaveReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{7}
}

func (x *LeaveReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type LeaveRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LeaveRsp) Reset() {
	*x = LeaveRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LeaveRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveRsp) ProtoMessage() {}

func (x *LeaveRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveRsp.ProtoReflect.Descriptor instead.
func (*LeaveRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{8}
}

type Participant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *Participant) Reset() {
	*x = Participant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Participant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Participant) ProtoMessage() {}

func (x *Participant) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Participant.ProtoReflect.Descriptor instead.
func (*Participant) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{9}
}

func (x *Participant) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type ParticipantListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Passback string `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *ParticipantListReq) Reset() {
	*x = ParticipantListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParticipantListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantListReq) ProtoMessage() {}

func (x *ParticipantListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantListReq.ProtoReflect.Descriptor instead.
func (*ParticipantListReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{10}
}

func (x *ParticipantListReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *ParticipantListReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type ParticipantListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Participants   []*Participant `protobuf:"bytes,1,rep,name=participants,proto3" json:"participants,omitempty"`
	HasMore        bool           `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`
	Passback       string         `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"`
	GameName       string         `protobuf:"bytes,4,opt,name=gameName,proto3" json:"gameName,omitempty"`
	GameIcon       string         `protobuf:"bytes,5,opt,name=gameIcon,proto3" json:"gameIcon,omitempty"`
	ParticipantNum uint32         `protobuf:"varint,6,opt,name=participantNum,proto3" json:"participantNum,omitempty"` // 排队人数
}

func (x *ParticipantListRsp) Reset() {
	*x = ParticipantListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParticipantListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParticipantListRsp) ProtoMessage() {}

func (x *ParticipantListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParticipantListRsp.ProtoReflect.Descriptor instead.
func (*ParticipantListRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{11}
}

func (x *ParticipantListRsp) GetParticipants() []*Participant {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *ParticipantListRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *ParticipantListRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *ParticipantListRsp) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

func (x *ParticipantListRsp) GetGameIcon() string {
	if x != nil {
		return x.GameIcon
	}
	return ""
}

func (x *ParticipantListRsp) GetParticipantNum() uint32 {
	if x != nil {
		return x.ParticipantNum
	}
	return 0
}

type PickPlayerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Index  uint32 `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *PickPlayerReq) Reset() {
	*x = PickPlayerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PickPlayerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PickPlayerReq) ProtoMessage() {}

func (x *PickPlayerReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PickPlayerReq.ProtoReflect.Descriptor instead.
func (*PickPlayerReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{12}
}

func (x *PickPlayerReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *PickPlayerReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *PickPlayerReq) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type PickPlayerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PickPlayerRsp) Reset() {
	*x = PickPlayerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PickPlayerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PickPlayerRsp) ProtoMessage() {}

func (x *PickPlayerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PickPlayerRsp.ProtoReflect.Descriptor instead.
func (*PickPlayerRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{13}
}

type KickOutReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *KickOutReq) Reset() {
	*x = KickOutReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KickOutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickOutReq) ProtoMessage() {}

func (x *KickOutReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickOutReq.ProtoReflect.Descriptor instead.
func (*KickOutReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{14}
}

func (x *KickOutReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *KickOutReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type KickOutRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *KickOutRsp) Reset() {
	*x = KickOutRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KickOutRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KickOutRsp) ProtoMessage() {}

func (x *KickOutRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KickOutRsp.ProtoReflect.Descriptor instead.
func (*KickOutRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{15}
}

type BeginGameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *BeginGameReq) Reset() {
	*x = BeginGameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginGameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginGameReq) ProtoMessage() {}

func (x *BeginGameReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginGameReq.ProtoReflect.Descriptor instead.
func (*BeginGameReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{16}
}

func (x *BeginGameReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type BeginGameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BeginGameRsp) Reset() {
	*x = BeginGameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BeginGameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeginGameRsp) ProtoMessage() {}

func (x *BeginGameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeginGameRsp.ProtoReflect.Descriptor instead.
func (*BeginGameRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{17}
}

type PlayAgainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *PlayAgainReq) Reset() {
	*x = PlayAgainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayAgainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayAgainReq) ProtoMessage() {}

func (x *PlayAgainReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayAgainReq.ProtoReflect.Descriptor instead.
func (*PlayAgainReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{18}
}

func (x *PlayAgainReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type PlayAgainRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlayAgainRsp) Reset() {
	*x = PlayAgainRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayAgainRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayAgainRsp) ProtoMessage() {}

func (x *PlayAgainRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayAgainRsp.ProtoReflect.Descriptor instead.
func (*PlayAgainRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{19}
}

type MatchOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisableRoomCache bool `protobuf:"varint,1,opt,name=disableRoomCache,proto3" json:"disableRoomCache,omitempty"` // 实时回源平台查房间信息
	AutoTurn2Public  bool `protobuf:"varint,2,opt,name=autoTurn2Public,proto3" json:"autoTurn2Public,omitempty"`   // 自动将房间转为公开,只对房主有效
}

func (x *MatchOption) Reset() {
	*x = MatchOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchOption) ProtoMessage() {}

func (x *MatchOption) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchOption.ProtoReflect.Descriptor instead.
func (*MatchOption) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{20}
}

func (x *MatchOption) GetDisableRoomCache() bool {
	if x != nil {
		return x.DisableRoomCache
	}
	return false
}

func (x *MatchOption) GetAutoTurn2Public() bool {
	if x != nil {
		return x.AutoTurn2Public
	}
	return false
}

type MatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string       `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	Option *MatchOption `protobuf:"bytes,2,opt,name=option,proto3" json:"option,omitempty"` // 匹配选项
}

func (x *MatchReq) Reset() {
	*x = MatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchReq) ProtoMessage() {}

func (x *MatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchReq.ProtoReflect.Descriptor instead.
func (*MatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{21}
}

func (x *MatchReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *MatchReq) GetOption() *MatchOption {
	if x != nil {
		return x.Option
	}
	return nil
}

// 房间私密信息弹窗,只有对房主才有效,请判断错误码code.proto下code.InteractiveGamePrivate后弹窗提示
type MatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MatchRsp) Reset() {
	*x = MatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchRsp) ProtoMessage() {}

func (x *MatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchRsp.ProtoReflect.Descriptor instead.
func (*MatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{22}
}

type CancelMatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *CancelMatchReq) Reset() {
	*x = CancelMatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelMatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelMatchReq) ProtoMessage() {}

func (x *CancelMatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelMatchReq.ProtoReflect.Descriptor instead.
func (*CancelMatchReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{23}
}

func (x *CancelMatchReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type CancelMatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelMatchRsp) Reset() {
	*x = CancelMatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelMatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelMatchRsp) ProtoMessage() {}

func (x *CancelMatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelMatchRsp.ProtoReflect.Descriptor instead.
func (*CancelMatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{24}
}

type OperationalPositionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *OperationalPositionReq) Reset() {
	*x = OperationalPositionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionReq) ProtoMessage() {}

func (x *OperationalPositionReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionReq.ProtoReflect.Descriptor instead.
func (*OperationalPositionReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{25}
}

func (x *OperationalPositionReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type OperationalPositionRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Positions    []*OperationalPositionRsp_Position `protobuf:"bytes,1,rep,name=positions,proto3" json:"positions,omitempty"`
	SlideSeconds uint32                             `protobuf:"varint,2,opt,name=slideSeconds,proto3" json:"slideSeconds,omitempty"` // 轮播间隔
}

func (x *OperationalPositionRsp) Reset() {
	*x = OperationalPositionRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp) ProtoMessage() {}

func (x *OperationalPositionRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26}
}

func (x *OperationalPositionRsp) GetPositions() []*OperationalPositionRsp_Position {
	if x != nil {
		return x.Positions
	}
	return nil
}

func (x *OperationalPositionRsp) GetSlideSeconds() uint32 {
	if x != nil {
		return x.SlideSeconds
	}
	return 0
}

type VerifyBalanceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *VerifyBalanceReq) Reset() {
	*x = VerifyBalanceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBalanceReq) ProtoMessage() {}

func (x *VerifyBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBalanceReq.ProtoReflect.Descriptor instead.
func (*VerifyBalanceReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{27}
}

func (x *VerifyBalanceReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type VerifyBalanceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *VerifyBalanceRsp) Reset() {
	*x = VerifyBalanceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyBalanceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyBalanceRsp) ProtoMessage() {}

func (x *VerifyBalanceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyBalanceRsp.ProtoReflect.Descriptor instead.
func (*VerifyBalanceRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{28}
}

func (x *VerifyBalanceRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type QueryStatusRsp_Player struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index      uint32      `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` // 座位号
	User       *User       `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Role       common.Role `protobuf:"varint,3,opt,name=role,proto3,enum=interactive_game.common.Role" json:"role,omitempty"`
	Ready      bool        `protobuf:"varint,4,opt,name=ready,proto3" json:"ready,omitempty"`           // 是否准备 -- 废弃
	Eliminated bool        `protobuf:"varint,5,opt,name=eliminated,proto3" json:"eliminated,omitempty"` // 被淘汰
}

func (x *QueryStatusRsp_Player) Reset() {
	*x = QueryStatusRsp_Player{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRsp_Player) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRsp_Player) ProtoMessage() {}

func (x *QueryStatusRsp_Player) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRsp_Player.ProtoReflect.Descriptor instead.
func (*QueryStatusRsp_Player) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{2, 0}
}

func (x *QueryStatusRsp_Player) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *QueryStatusRsp_Player) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *QueryStatusRsp_Player) GetRole() common.Role {
	if x != nil {
		return x.Role
	}
	return common.Role(0)
}

func (x *QueryStatusRsp_Player) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *QueryStatusRsp_Player) GetEliminated() bool {
	if x != nil {
		return x.Eliminated
	}
	return false
}

type QueryStatusRsp_RankStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Show         bool                    `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`              // 是否展示
	Rank         int64                   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`              // 排名
	Description  string                  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 描述文案
	PayMode      common.PayMode          `protobuf:"varint,4,opt,name=payMode,proto3,enum=interactive_game.common.PayMode" json:"payMode,omitempty"`
	PeriodicType common.RankPeriodicType `protobuf:"varint,5,opt,name=periodicType,proto3,enum=interactive_game.common.RankPeriodicType" json:"periodicType,omitempty"`
}

func (x *QueryStatusRsp_RankStatus) Reset() {
	*x = QueryStatusRsp_RankStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRsp_RankStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRsp_RankStatus) ProtoMessage() {}

func (x *QueryStatusRsp_RankStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRsp_RankStatus.ProtoReflect.Descriptor instead.
func (*QueryStatusRsp_RankStatus) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{2, 1}
}

func (x *QueryStatusRsp_RankStatus) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *QueryStatusRsp_RankStatus) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *QueryStatusRsp_RankStatus) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *QueryStatusRsp_RankStatus) GetPayMode() common.PayMode {
	if x != nil {
		return x.PayMode
	}
	return common.PayMode(0)
}

func (x *QueryStatusRsp_RankStatus) GetPeriodicType() common.RankPeriodicType {
	if x != nil {
		return x.PeriodicType
	}
	return common.RankPeriodicType(0)
}

type OperationalPositionRsp_Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url       string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`             // 图片链接
	Hyperlink string `protobuf:"bytes,2,opt,name=hyperlink,proto3" json:"hyperlink,omitempty"` // 跳转链接
}

func (x *OperationalPositionRsp_Image) Reset() {
	*x = OperationalPositionRsp_Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp_Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp_Image) ProtoMessage() {}

func (x *OperationalPositionRsp_Image) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp_Image.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp_Image) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26, 0}
}

func (x *OperationalPositionRsp_Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *OperationalPositionRsp_Image) GetHyperlink() string {
	if x != nil {
		return x.Hyperlink
	}
	return ""
}

type OperationalPositionRsp_Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`       // 视频链接
	Poster string `protobuf:"bytes,2,opt,name=poster,proto3" json:"poster,omitempty"` // 封面图
}

func (x *OperationalPositionRsp_Video) Reset() {
	*x = OperationalPositionRsp_Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp_Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp_Video) ProtoMessage() {}

func (x *OperationalPositionRsp_Video) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp_Video.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp_Video) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26, 1}
}

func (x *OperationalPositionRsp_Video) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *OperationalPositionRsp_Video) GetPoster() string {
	if x != nil {
		return x.Poster
	}
	return ""
}

type OperationalPositionRsp_TaskNodeReward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon        string `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`               // 奖励 icon
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"` // 奖励描述
}

func (x *OperationalPositionRsp_TaskNodeReward) Reset() {
	*x = OperationalPositionRsp_TaskNodeReward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp_TaskNodeReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp_TaskNodeReward) ProtoMessage() {}

func (x *OperationalPositionRsp_TaskNodeReward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp_TaskNodeReward.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp_TaskNodeReward) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26, 2}
}

func (x *OperationalPositionRsp_TaskNodeReward) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *OperationalPositionRsp_TaskNodeReward) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type OperationalPositionRsp_TaskNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards     []*OperationalPositionRsp_TaskNodeReward `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"`                                                        // 奖励信息
	Description string                                   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                                                // 节点描述
	Progress    uint32                                   `protobuf:"varint,3,opt,name=progress,proto3" json:"progress,omitempty"`                                                     // 节点进度
	State       common.OperationalPositionTaskState      `protobuf:"varint,4,opt,name=state,proto3,enum=interactive_game.common.OperationalPositionTaskState" json:"state,omitempty"` // 节点状态
}

func (x *OperationalPositionRsp_TaskNode) Reset() {
	*x = OperationalPositionRsp_TaskNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp_TaskNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp_TaskNode) ProtoMessage() {}

func (x *OperationalPositionRsp_TaskNode) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp_TaskNode.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp_TaskNode) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26, 3}
}

func (x *OperationalPositionRsp_TaskNode) GetRewards() []*OperationalPositionRsp_TaskNodeReward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *OperationalPositionRsp_TaskNode) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OperationalPositionRsp_TaskNode) GetProgress() uint32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *OperationalPositionRsp_TaskNode) GetState() common.OperationalPositionTaskState {
	if x != nil {
		return x.State
	}
	return common.OperationalPositionTaskState(0)
}

type OperationalPositionRsp_Task struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type         common.OperationalPositionTaskType         `protobuf:"varint,1,opt,name=type,proto3,enum=interactive_game.common.OperationalPositionTaskType" json:"type,omitempty"`    // 任务类型
	TaskId       int32                                      `protobuf:"varint,2,opt,name=taskId,proto3" json:"taskId,omitempty"`                                                         // 任务ID
	State        common.OperationalPositionTaskState        `protobuf:"varint,3,opt,name=state,proto3,enum=interactive_game.common.OperationalPositionTaskState" json:"state,omitempty"` // 任务状态
	Title        string                                     `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`                                                            // 任务标题
	Progress     uint32                                     `protobuf:"varint,5,opt,name=progress,proto3" json:"progress,omitempty"`                                                     // 当前进度
	MaxProgress  uint32                                     `protobuf:"varint,6,opt,name=maxProgress,proto3" json:"maxProgress,omitempty"`                                               // 最大进度
	RewardId     string                                     `protobuf:"bytes,7,opt,name=rewardId,proto3" json:"rewardId,omitempty"`                                                      // 奖励 id 领取任务需要回传
	Nodes        []*OperationalPositionRsp_TaskNode         `protobuf:"bytes,8,rep,name=nodes,proto3" json:"nodes,omitempty"`                                                            // 任务节点
	Description  string                                     `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`                                                // 任务描述
	ButtonAction common.OperationalPositionTaskButtonAction `protobuf:"varint,10,opt,name=buttonAction,proto3,enum=interactive_game.common.OperationalPositionTaskButtonAction" json:"buttonAction,omitempty"`
	ButtonUrl    string                                     `protobuf:"bytes,11,opt,name=buttonUrl,proto3" json:"buttonUrl,omitempty"`
}

func (x *OperationalPositionRsp_Task) Reset() {
	*x = OperationalPositionRsp_Task{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp_Task) ProtoMessage() {}

func (x *OperationalPositionRsp_Task) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp_Task.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp_Task) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26, 4}
}

func (x *OperationalPositionRsp_Task) GetType() common.OperationalPositionTaskType {
	if x != nil {
		return x.Type
	}
	return common.OperationalPositionTaskType(0)
}

func (x *OperationalPositionRsp_Task) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *OperationalPositionRsp_Task) GetState() common.OperationalPositionTaskState {
	if x != nil {
		return x.State
	}
	return common.OperationalPositionTaskState(0)
}

func (x *OperationalPositionRsp_Task) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *OperationalPositionRsp_Task) GetProgress() uint32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *OperationalPositionRsp_Task) GetMaxProgress() uint32 {
	if x != nil {
		return x.MaxProgress
	}
	return 0
}

func (x *OperationalPositionRsp_Task) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *OperationalPositionRsp_Task) GetNodes() []*OperationalPositionRsp_TaskNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *OperationalPositionRsp_Task) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *OperationalPositionRsp_Task) GetButtonAction() common.OperationalPositionTaskButtonAction {
	if x != nil {
		return x.ButtonAction
	}
	return common.OperationalPositionTaskButtonAction(0)
}

func (x *OperationalPositionRsp_Task) GetButtonUrl() string {
	if x != nil {
		return x.ButtonUrl
	}
	return ""
}

type OperationalPositionRsp_Position struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common.OperationalPositionType type = 1;
	Image *OperationalPositionRsp_Image `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Video *OperationalPositionRsp_Video `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty"`
	Task  *OperationalPositionRsp_Task  `protobuf:"bytes,4,opt,name=task,proto3" json:"task,omitempty"`
	Id    string                        `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *OperationalPositionRsp_Position) Reset() {
	*x = OperationalPositionRsp_Position{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_room_room_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperationalPositionRsp_Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperationalPositionRsp_Position) ProtoMessage() {}

func (x *OperationalPositionRsp_Position) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_room_room_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperationalPositionRsp_Position.ProtoReflect.Descriptor instead.
func (*OperationalPositionRsp_Position) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_room_room_proto_rawDescGZIP(), []int{26, 5}
}

func (x *OperationalPositionRsp_Position) GetImage() *OperationalPositionRsp_Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *OperationalPositionRsp_Position) GetVideo() *OperationalPositionRsp_Video {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *OperationalPositionRsp_Position) GetTask() *OperationalPositionRsp_Task {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *OperationalPositionRsp_Position) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_pb_interactive_game_room_room_proto protoreflect.FileDescriptor

var file_pb_interactive_game_room_room_proto_rawDesc = []byte{
	0x0a, 0x23, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x2f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xad, 0x02, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x69, 0x63,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x12, 0x37, 0x0a,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x55, 0x49, 0x44, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x55, 0x49, 0x44,
	0x22, 0x8f, 0x01, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x49, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xba, 0x0f, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x2c, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x41,
	0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73,
	0x70, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x12, 0x3f, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47,
	0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x42,
	0x65, 0x67, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x56, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x4b, 0x0a, 0x0a, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e, 0x52, 0x61, 0x6e, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x46, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x6a, 0x6f, 0x69, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6a, 0x6f, 0x69, 0x6e, 0x46, 0x72, 0x6f, 0x6d, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x49, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x28, 0x0a, 0x0f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x61, 0x6d,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x67, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x09, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66,
	0x72, 0x65, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x66, 0x72, 0x65, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74,
	0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x75, 0x74,
	0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6a, 0x6f, 0x69, 0x6e, 0x55,
	0x73, 0x65, 0x46, 0x72, 0x65, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6a, 0x6f,
	0x69, 0x6e, 0x55, 0x73, 0x65, 0x46, 0x72, 0x65, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x61, 0x6e,
	0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x61, 0x6e,
	0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x1a, 0xb3, 0x01, 0x0a, 0x06, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x65,
	0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x1a, 0xe1, 0x01, 0x0a, 0x0a,
	0x52, 0x61, 0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68,
	0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x68, 0x6f, 0x77, 0x12, 0x12,
	0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x72, 0x61,
	0x6e, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x1a,
	0x3f, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3d, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x3a, 0x0a, 0x0a, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x0c, 0x0a, 0x0a, 0x45,
	0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x73, 0x70, 0x22, 0x24, 0x0a, 0x0a, 0x44, 0x65, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22,
	0x0c, 0x0a, 0x0a, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x73, 0x70, 0x22, 0x22, 0x0a,
	0x08, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x22, 0x0a, 0x0a, 0x08, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x73, 0x70, 0x22, 0x39, 0x0a,
	0x0b, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x12, 0x50, 0x61, 0x72, 0x74,
	0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x22, 0xed, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x52, 0x0c,
	0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61,
	0x63, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x4e,
	0x75, 0x6d, 0x22, 0x55, 0x0a, 0x0d, 0x50, 0x69, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x0f, 0x0a, 0x0d, 0x50, 0x69, 0x63,
	0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0x3c, 0x0a, 0x0a, 0x4b, 0x69,
	0x63, 0x6b, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x0c, 0x0a, 0x0a, 0x4b, 0x69, 0x63, 0x6b,
	0x4f, 0x75, 0x74, 0x52, 0x73, 0x70, 0x22, 0x26, 0x0a, 0x0c, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x0e,
	0x0a, 0x0c, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x22, 0x26,
	0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x79, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x50, 0x6c, 0x61, 0x79, 0x41, 0x67,
	0x61, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x63, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x54, 0x75, 0x72, 0x6e, 0x32, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f,
	0x54, 0x75, 0x72, 0x6e, 0x32, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x22, 0x59, 0x0a, 0x08, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12,
	0x35, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x0a, 0x0a, 0x08, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x73, 0x70, 0x22, 0x28, 0x0a, 0x0e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x22, 0x30,
	0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64,
	0x22, 0xab, 0x0a, 0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x09, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c,
	0x73, 0x6c, 0x69, 0x64, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x1a, 0x37, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x68,
	0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x68, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x1a, 0x31, 0x0a, 0x05, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x1a, 0x46, 0x0a, 0x0e,
	0x54, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xe8, 0x01, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x64,
	0x65, 0x12, 0x51, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x35, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x1a,
	0x90, 0x04, 0x0a, 0x04, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x48, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x61, 0x78,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x6d, 0x61, 0x78, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x60, 0x0a, 0x0c, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x1a, 0xe9, 0x01, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x44, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x2e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x52, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x12, 0x41, 0x0a, 0x04, 0x74,
	0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x73, 0x70, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2a,
	0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x26, 0x0a, 0x10, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x73, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x32, 0x90, 0x08, 0x0a, 0x04, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x51, 0x0a, 0x0b, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x45,
	0x0a, 0x07, 0x45, 0x6e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x12, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x6e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x52, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x07, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x12, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x44, 0x65, 0x71, 0x75, 0x65, 0x75, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x05,
	0x4c, 0x65, 0x61, 0x76, 0x65, 0x12, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x73, 0x70, 0x12, 0x5d, 0x0a,
	0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0a,
	0x50, 0x69, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x1f, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x69,
	0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50,
	0x69, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x07,
	0x4b, 0x69, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x12, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4b, 0x69, 0x63, 0x6b, 0x4f,
	0x75, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4b, 0x69, 0x63, 0x6b, 0x4f, 0x75, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x09, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x4b, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x12, 0x1e, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a,
	0x05, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x51,
	0x0a, 0x0b, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x20, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73,
	0x70, 0x12, 0x69, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x28, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x0d,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x22, 0x2e,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x73, 0x70, 0x42, 0x48, 0x5a, 0x46, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_room_room_proto_rawDescOnce sync.Once
	file_pb_interactive_game_room_room_proto_rawDescData = file_pb_interactive_game_room_room_proto_rawDesc
)

func file_pb_interactive_game_room_room_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_room_room_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_room_room_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_room_room_proto_rawDescData)
	})
	return file_pb_interactive_game_room_room_proto_rawDescData
}

var file_pb_interactive_game_room_room_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_pb_interactive_game_room_room_proto_goTypes = []interface{}{
	(*User)(nil),                                    // 0: interactive_game.User
	(*QueryStatusReq)(nil),                          // 1: interactive_game.QueryStatusReq
	(*QueryStatusRsp)(nil),                          // 2: interactive_game.QueryStatusRsp
	(*EnqueueReq)(nil),                              // 3: interactive_game.EnqueueReq
	(*EnqueueRsp)(nil),                              // 4: interactive_game.EnqueueRsp
	(*DequeueReq)(nil),                              // 5: interactive_game.DequeueReq
	(*DequeueRsp)(nil),                              // 6: interactive_game.DequeueRsp
	(*LeaveReq)(nil),                                // 7: interactive_game.LeaveReq
	(*LeaveRsp)(nil),                                // 8: interactive_game.LeaveRsp
	(*Participant)(nil),                             // 9: interactive_game.Participant
	(*ParticipantListReq)(nil),                      // 10: interactive_game.ParticipantListReq
	(*ParticipantListRsp)(nil),                      // 11: interactive_game.ParticipantListRsp
	(*PickPlayerReq)(nil),                           // 12: interactive_game.PickPlayerReq
	(*PickPlayerRsp)(nil),                           // 13: interactive_game.PickPlayerRsp
	(*KickOutReq)(nil),                              // 14: interactive_game.KickOutReq
	(*KickOutRsp)(nil),                              // 15: interactive_game.KickOutRsp
	(*BeginGameReq)(nil),                            // 16: interactive_game.BeginGameReq
	(*BeginGameRsp)(nil),                            // 17: interactive_game.BeginGameRsp
	(*PlayAgainReq)(nil),                            // 18: interactive_game.PlayAgainReq
	(*PlayAgainRsp)(nil),                            // 19: interactive_game.PlayAgainRsp
	(*MatchOption)(nil),                             // 20: interactive_game.MatchOption
	(*MatchReq)(nil),                                // 21: interactive_game.MatchReq
	(*MatchRsp)(nil),                                // 22: interactive_game.MatchRsp
	(*CancelMatchReq)(nil),                          // 23: interactive_game.CancelMatchReq
	(*CancelMatchRsp)(nil),                          // 24: interactive_game.CancelMatchRsp
	(*OperationalPositionReq)(nil),                  // 25: interactive_game.OperationalPositionReq
	(*OperationalPositionRsp)(nil),                  // 26: interactive_game.OperationalPositionRsp
	(*VerifyBalanceReq)(nil),                        // 27: interactive_game.VerifyBalanceReq
	(*VerifyBalanceRsp)(nil),                        // 28: interactive_game.VerifyBalanceRsp
	(*QueryStatusRsp_Player)(nil),                   // 29: interactive_game.QueryStatusRsp.Player
	(*QueryStatusRsp_RankStatus)(nil),               // 30: interactive_game.QueryStatusRsp.RankStatus
	nil,                                             // 31: interactive_game.QueryStatusRsp.ReportExtendEntry
	nil,                                             // 32: interactive_game.QueryStatusRsp.GameExtendEntry
	(*OperationalPositionRsp_Image)(nil),            // 33: interactive_game.OperationalPositionRsp.Image
	(*OperationalPositionRsp_Video)(nil),            // 34: interactive_game.OperationalPositionRsp.Video
	(*OperationalPositionRsp_TaskNodeReward)(nil),   // 35: interactive_game.OperationalPositionRsp.TaskNodeReward
	(*OperationalPositionRsp_TaskNode)(nil),         // 36: interactive_game.OperationalPositionRsp.TaskNode
	(*OperationalPositionRsp_Task)(nil),             // 37: interactive_game.OperationalPositionRsp.Task
	(*OperationalPositionRsp_Position)(nil),         // 38: interactive_game.OperationalPositionRsp.Position
	(common.Gender)(0),                              // 39: interactive_game.common.Gender
	(common.MatchAuthorityType)(0),                  // 40: interactive_game.common.MatchAuthorityType
	(common.RoomStatus)(0),                          // 41: interactive_game.common.RoomStatus
	(*common.GameRoomConfig)(nil),                   // 42: interactive_game.common.GameRoomConfig
	(common.Role)(0),                                // 43: interactive_game.common.Role
	(*common.Message)(nil),                          // 44: interactive_game.common.Message
	(common.MatchStatus)(0),                         // 45: interactive_game.common.MatchStatus
	(*common.PlatformInfo)(nil),                     // 46: interactive_game.common.PlatformInfo
	(common.MatchType)(0),                           // 47: interactive_game.common.MatchType
	(common.PayMode)(0),                             // 48: interactive_game.common.PayMode
	(common.RankPeriodicType)(0),                    // 49: interactive_game.common.RankPeriodicType
	(common.OperationalPositionTaskState)(0),        // 50: interactive_game.common.OperationalPositionTaskState
	(common.OperationalPositionTaskType)(0),         // 51: interactive_game.common.OperationalPositionTaskType
	(common.OperationalPositionTaskButtonAction)(0), // 52: interactive_game.common.OperationalPositionTaskButtonAction
}
var file_pb_interactive_game_room_room_proto_depIdxs = []int32{
	39, // 0: interactive_game.User.gender:type_name -> interactive_game.common.Gender
	40, // 1: interactive_game.QueryStatusReq.matchType:type_name -> interactive_game.common.MatchAuthorityType
	41, // 2: interactive_game.QueryStatusRsp.status:type_name -> interactive_game.common.RoomStatus
	0,  // 3: interactive_game.QueryStatusRsp.owner:type_name -> interactive_game.User
	29, // 4: interactive_game.QueryStatusRsp.players:type_name -> interactive_game.QueryStatusRsp.Player
	42, // 5: interactive_game.QueryStatusRsp.config:type_name -> interactive_game.common.GameRoomConfig
	31, // 6: interactive_game.QueryStatusRsp.reportExtend:type_name -> interactive_game.QueryStatusRsp.ReportExtendEntry
	43, // 7: interactive_game.QueryStatusRsp.role:type_name -> interactive_game.common.Role
	44, // 8: interactive_game.QueryStatusRsp.messages:type_name -> interactive_game.common.Message
	32, // 9: interactive_game.QueryStatusRsp.gameExtend:type_name -> interactive_game.QueryStatusRsp.GameExtendEntry
	30, // 10: interactive_game.QueryStatusRsp.rankStatus:type_name -> interactive_game.QueryStatusRsp.RankStatus
	45, // 11: interactive_game.QueryStatusRsp.matchStatus:type_name -> interactive_game.common.MatchStatus
	46, // 12: interactive_game.QueryStatusRsp.platformInfo:type_name -> interactive_game.common.PlatformInfo
	47, // 13: interactive_game.QueryStatusRsp.matchType:type_name -> interactive_game.common.MatchType
	0,  // 14: interactive_game.Participant.user:type_name -> interactive_game.User
	9,  // 15: interactive_game.ParticipantListRsp.participants:type_name -> interactive_game.Participant
	20, // 16: interactive_game.MatchReq.option:type_name -> interactive_game.MatchOption
	38, // 17: interactive_game.OperationalPositionRsp.positions:type_name -> interactive_game.OperationalPositionRsp.Position
	0,  // 18: interactive_game.QueryStatusRsp.Player.user:type_name -> interactive_game.User
	43, // 19: interactive_game.QueryStatusRsp.Player.role:type_name -> interactive_game.common.Role
	48, // 20: interactive_game.QueryStatusRsp.RankStatus.payMode:type_name -> interactive_game.common.PayMode
	49, // 21: interactive_game.QueryStatusRsp.RankStatus.periodicType:type_name -> interactive_game.common.RankPeriodicType
	35, // 22: interactive_game.OperationalPositionRsp.TaskNode.rewards:type_name -> interactive_game.OperationalPositionRsp.TaskNodeReward
	50, // 23: interactive_game.OperationalPositionRsp.TaskNode.state:type_name -> interactive_game.common.OperationalPositionTaskState
	51, // 24: interactive_game.OperationalPositionRsp.Task.type:type_name -> interactive_game.common.OperationalPositionTaskType
	50, // 25: interactive_game.OperationalPositionRsp.Task.state:type_name -> interactive_game.common.OperationalPositionTaskState
	36, // 26: interactive_game.OperationalPositionRsp.Task.nodes:type_name -> interactive_game.OperationalPositionRsp.TaskNode
	52, // 27: interactive_game.OperationalPositionRsp.Task.buttonAction:type_name -> interactive_game.common.OperationalPositionTaskButtonAction
	33, // 28: interactive_game.OperationalPositionRsp.Position.image:type_name -> interactive_game.OperationalPositionRsp.Image
	34, // 29: interactive_game.OperationalPositionRsp.Position.video:type_name -> interactive_game.OperationalPositionRsp.Video
	37, // 30: interactive_game.OperationalPositionRsp.Position.task:type_name -> interactive_game.OperationalPositionRsp.Task
	1,  // 31: interactive_game.Room.QueryStatus:input_type -> interactive_game.QueryStatusReq
	3,  // 32: interactive_game.Room.Enqueue:input_type -> interactive_game.EnqueueReq
	5,  // 33: interactive_game.Room.Dequeue:input_type -> interactive_game.DequeueReq
	7,  // 34: interactive_game.Room.Leave:input_type -> interactive_game.LeaveReq
	10, // 35: interactive_game.Room.ParticipantList:input_type -> interactive_game.ParticipantListReq
	12, // 36: interactive_game.Room.PickPlayer:input_type -> interactive_game.PickPlayerReq
	14, // 37: interactive_game.Room.KickOut:input_type -> interactive_game.KickOutReq
	16, // 38: interactive_game.Room.BeginGame:input_type -> interactive_game.BeginGameReq
	18, // 39: interactive_game.Room.PlayAgain:input_type -> interactive_game.PlayAgainReq
	21, // 40: interactive_game.Room.Match:input_type -> interactive_game.MatchReq
	23, // 41: interactive_game.Room.CancelMatch:input_type -> interactive_game.CancelMatchReq
	25, // 42: interactive_game.Room.OperationalPosition:input_type -> interactive_game.OperationalPositionReq
	27, // 43: interactive_game.Room.VerifyBalance:input_type -> interactive_game.VerifyBalanceReq
	2,  // 44: interactive_game.Room.QueryStatus:output_type -> interactive_game.QueryStatusRsp
	4,  // 45: interactive_game.Room.Enqueue:output_type -> interactive_game.EnqueueRsp
	6,  // 46: interactive_game.Room.Dequeue:output_type -> interactive_game.DequeueRsp
	8,  // 47: interactive_game.Room.Leave:output_type -> interactive_game.LeaveRsp
	11, // 48: interactive_game.Room.ParticipantList:output_type -> interactive_game.ParticipantListRsp
	13, // 49: interactive_game.Room.PickPlayer:output_type -> interactive_game.PickPlayerRsp
	15, // 50: interactive_game.Room.KickOut:output_type -> interactive_game.KickOutRsp
	17, // 51: interactive_game.Room.BeginGame:output_type -> interactive_game.BeginGameRsp
	19, // 52: interactive_game.Room.PlayAgain:output_type -> interactive_game.PlayAgainRsp
	22, // 53: interactive_game.Room.Match:output_type -> interactive_game.MatchRsp
	24, // 54: interactive_game.Room.CancelMatch:output_type -> interactive_game.CancelMatchRsp
	26, // 55: interactive_game.Room.OperationalPosition:output_type -> interactive_game.OperationalPositionRsp
	28, // 56: interactive_game.Room.VerifyBalance:output_type -> interactive_game.VerifyBalanceRsp
	44, // [44:57] is the sub-list for method output_type
	31, // [31:44] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_room_room_proto_init() }
func file_pb_interactive_game_room_room_proto_init() {
	if File_pb_interactive_game_room_room_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_room_room_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnqueueReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnqueueRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DequeueReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DequeueRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LeaveRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Participant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParticipantListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParticipantListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PickPlayerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PickPlayerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KickOutReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KickOutRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginGameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BeginGameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayAgainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayAgainRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelMatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelMatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBalanceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyBalanceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusRsp_Player); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusRsp_RankStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp_Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp_Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp_TaskNodeReward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp_TaskNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp_Task); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_room_room_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperationalPositionRsp_Position); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_room_room_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_room_room_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_room_room_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_room_room_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_room_room_proto = out.File
	file_pb_interactive_game_room_room_proto_rawDesc = nil
	file_pb_interactive_game_room_room_proto_goTypes = nil
	file_pb_interactive_game_room_room_proto_depIdxs = nil
}
