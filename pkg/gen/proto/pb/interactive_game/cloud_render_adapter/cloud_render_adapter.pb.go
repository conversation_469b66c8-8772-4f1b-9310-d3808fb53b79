// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game/cloud_render_adapter/cloud_render_adapter.proto

package interactive_game

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/interactive_game/common"
	timer "kugou_adapter_service/pkg/gen/proto/pb/timer"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OpenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId      string          `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	AppId       string          `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`                                              // 游戏appid
	OwnerOpenId string          `protobuf:"bytes,3,opt,name=ownerOpenId,proto3" json:"ownerOpenId,omitempty"`                                  // 房主 openId
	RoomType    common.RoomType `protobuf:"varint,4,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
	RoundId     string          `protobuf:"bytes,5,opt,name=roundId,proto3" json:"roundId,omitempty"`                                          // 场次id
	GameUrl     string          `protobuf:"bytes,6,opt,name=gameUrl,proto3" json:"gameUrl,omitempty"`                                          // 上游直接拼好的游戏URL
}

func (x *OpenReq) Reset() {
	*x = OpenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenReq) ProtoMessage() {}

func (x *OpenReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenReq.ProtoReflect.Descriptor instead.
func (*OpenReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{0}
}

func (x *OpenReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *OpenReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *OpenReq) GetOwnerOpenId() string {
	if x != nil {
		return x.OwnerOpenId
	}
	return ""
}

func (x *OpenReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

func (x *OpenReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *OpenReq) GetGameUrl() string {
	if x != nil {
		return x.GameUrl
	}
	return ""
}

type OpenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RenderID string `protobuf:"bytes,1,opt,name=renderID,proto3" json:"renderID,omitempty"` // close和heartbeat要用，得存下来
	CdnUrl   string `protobuf:"bytes,2,opt,name=cdnUrl,proto3" json:"cdnUrl,omitempty"`     // cdn地址，实时返回3-5s后有效
}

func (x *OpenRsp) Reset() {
	*x = OpenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRsp) ProtoMessage() {}

func (x *OpenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRsp.ProtoReflect.Descriptor instead.
func (*OpenRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{1}
}

func (x *OpenRsp) GetRenderID() string {
	if x != nil {
		return x.RenderID
	}
	return ""
}

func (x *OpenRsp) GetCdnUrl() string {
	if x != nil {
		return x.CdnUrl
	}
	return ""
}

type CloseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string          `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"` // roomid，Adapter内解决roomid到renderid的映射
	AppId    string          `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`   // 游戏appid
	RoundId  string          `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"`
	RoomType common.RoomType `protobuf:"varint,4,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"` // 房间类型
}

func (x *CloseReq) Reset() {
	*x = CloseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseReq) ProtoMessage() {}

func (x *CloseReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseReq.ProtoReflect.Descriptor instead.
func (*CloseReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{2}
}

func (x *CloseReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CloseReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CloseReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *CloseReq) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

type CloseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloseRsp) Reset() {
	*x = CloseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseRsp) ProtoMessage() {}

func (x *CloseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseRsp.ProtoReflect.Descriptor instead.
func (*CloseRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{3}
}

type BatchStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomids []string `protobuf:"bytes,1,rep,name=roomids,proto3" json:"roomids,omitempty"` // roomids
	Appid   string   `protobuf:"bytes,2,opt,name=appid,proto3" json:"appid,omitempty"`     // 游戏appid
}

func (x *BatchStateReq) Reset() {
	*x = BatchStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchStateReq) ProtoMessage() {}

func (x *BatchStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchStateReq.ProtoReflect.Descriptor instead.
func (*BatchStateReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{4}
}

func (x *BatchStateReq) GetRoomids() []string {
	if x != nil {
		return x.Roomids
	}
	return nil
}

func (x *BatchStateReq) GetAppid() string {
	if x != nil {
		return x.Appid
	}
	return ""
}

type BatchStateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapStates map[string]*RenderInfo `protobuf:"bytes,1,rep,name=mapStates,proto3" json:"mapStates,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchStateRsp) Reset() {
	*x = BatchStateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchStateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchStateRsp) ProtoMessage() {}

func (x *BatchStateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchStateRsp.ProtoReflect.Descriptor instead.
func (*BatchStateRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{5}
}

func (x *BatchStateRsp) GetMapStates() map[string]*RenderInfo {
	if x != nil {
		return x.MapStates
	}
	return nil
}

type HeartbeatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomid       string `protobuf:"bytes,1,opt,name=roomid,proto3" json:"roomid,omitempty"`              // roomid，Adapter内解决roomid到renderid的映射
	AppId        string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`                // 游戏appid
	HeartbeatTs  int64  `protobuf:"varint,3,opt,name=heartbeatTs,proto3" json:"heartbeatTs,omitempty"`   // 心跳时间戳，秒
	NextInterval int64  `protobuf:"varint,4,opt,name=nextInterval,proto3" json:"nextInterval,omitempty"` // 下一次间隔，秒
	RoundId      string `protobuf:"bytes,5,opt,name=roundId,proto3" json:"roundId,omitempty"`
}

func (x *HeartbeatReq) Reset() {
	*x = HeartbeatReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatReq) ProtoMessage() {}

func (x *HeartbeatReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatReq.ProtoReflect.Descriptor instead.
func (*HeartbeatReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{6}
}

func (x *HeartbeatReq) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *HeartbeatReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *HeartbeatReq) GetHeartbeatTs() int64 {
	if x != nil {
		return x.HeartbeatTs
	}
	return 0
}

func (x *HeartbeatReq) GetNextInterval() int64 {
	if x != nil {
		return x.NextInterval
	}
	return 0
}

func (x *HeartbeatReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type HeartbeatRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HeartbeatRsp) Reset() {
	*x = HeartbeatRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartbeatRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatRsp) ProtoMessage() {}

func (x *HeartbeatRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatRsp.ProtoReflect.Descriptor instead.
func (*HeartbeatRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{7}
}

// renderInfo 存储，key为 render:{roomId}
type RenderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RenderID string          `protobuf:"bytes,1,opt,name=renderID,proto3" json:"renderID,omitempty"`  // renderID，Close的时候有用
	CdnUrl   string          `protobuf:"bytes,2,opt,name=cdnUrl,proto3" json:"cdnUrl,omitempty"`      // cdn
	UpdateTs int64           `protobuf:"varint,3,opt,name=updateTs,proto3" json:"updateTs,omitempty"` // 更新时间戳，秒
	CloseTs  int64           `protobuf:"varint,4,opt,name=closeTs,proto3" json:"closeTs,omitempty"`   // cancel时间戳，秒
	RoundId  string          `protobuf:"bytes,5,opt,name=roundId,proto3" json:"roundId,omitempty"`
	CdnUrl2  string          `protobuf:"bytes,6,opt,name=cdnUrl2,proto3" json:"cdnUrl2,omitempty"`
	RoomType common.RoomType `protobuf:"varint,7,opt,name=roomType,proto3,enum=interactive_game.common.RoomType" json:"roomType,omitempty"`
}

func (x *RenderInfo) Reset() {
	*x = RenderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenderInfo) ProtoMessage() {}

func (x *RenderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenderInfo.ProtoReflect.Descriptor instead.
func (*RenderInfo) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{8}
}

func (x *RenderInfo) GetRenderID() string {
	if x != nil {
		return x.RenderID
	}
	return ""
}

func (x *RenderInfo) GetCdnUrl() string {
	if x != nil {
		return x.CdnUrl
	}
	return ""
}

func (x *RenderInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *RenderInfo) GetCloseTs() int64 {
	if x != nil {
		return x.CloseTs
	}
	return 0
}

func (x *RenderInfo) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *RenderInfo) GetCdnUrl2() string {
	if x != nil {
		return x.CdnUrl2
	}
	return ""
}

func (x *RenderInfo) GetRoomType() common.RoomType {
	if x != nil {
		return x.RoomType
	}
	return common.RoomType(0)
}

type CancelTimerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RenderId string `protobuf:"bytes,1,opt,name=renderId,proto3" json:"renderId,omitempty"`
	RoomId   string `protobuf:"bytes,2,opt,name=roomId,proto3" json:"roomId,omitempty"`
	AppId    string `protobuf:"bytes,3,opt,name=appId,proto3" json:"appId,omitempty"`
	RoundId  string `protobuf:"bytes,4,opt,name=roundId,proto3" json:"roundId,omitempty"`
}

func (x *CancelTimerInfo) Reset() {
	*x = CancelTimerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelTimerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelTimerInfo) ProtoMessage() {}

func (x *CancelTimerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelTimerInfo.ProtoReflect.Descriptor instead.
func (*CancelTimerInfo) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{9}
}

func (x *CancelTimerInfo) GetRenderId() string {
	if x != nil {
		return x.RenderId
	}
	return ""
}

func (x *CancelTimerInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *CancelTimerInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CancelTimerInfo) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type IsWorthRenderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomid   string `protobuf:"bytes,1,opt,name=roomid,proto3" json:"roomid,omitempty"`      // 直播的话是showId，歌房的话是roomId
	AppId    string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`        // 游戏appid
	RoundId  string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"`    // 场次id
	RoomType uint32 `protobuf:"varint,4,opt,name=roomType,proto3" json:"roomType,omitempty"` // 房间类型 1 歌房 2双人房 3直播
}

func (x *IsWorthRenderReq) Reset() {
	*x = IsWorthRenderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsWorthRenderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsWorthRenderReq) ProtoMessage() {}

func (x *IsWorthRenderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsWorthRenderReq.ProtoReflect.Descriptor instead.
func (*IsWorthRenderReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{10}
}

func (x *IsWorthRenderReq) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *IsWorthRenderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *IsWorthRenderReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *IsWorthRenderReq) GetRoomType() uint32 {
	if x != nil {
		return x.RoomType
	}
	return 0
}

type IsWorthRenderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	ErrorCode int32 `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 错误信息
	ErrorMsg string `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
	Worth    bool   `protobuf:"varint,3,opt,name=worth,proto3" json:"worth,omitempty"`    // 是否要录这个主播
	Width    uint32 `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`    // 录制宽度
	Height   uint32 `protobuf:"varint,5,opt,name=height,proto3" json:"height,omitempty"`  // 录制高度
	GameUrl  string `protobuf:"bytes,6,opt,name=gameUrl,proto3" json:"gameUrl,omitempty"` // 游戏链接（除了鉴权参数外的链接）
}

func (x *IsWorthRenderRsp) Reset() {
	*x = IsWorthRenderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsWorthRenderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsWorthRenderRsp) ProtoMessage() {}

func (x *IsWorthRenderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsWorthRenderRsp.ProtoReflect.Descriptor instead.
func (*IsWorthRenderRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{11}
}

func (x *IsWorthRenderRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *IsWorthRenderRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *IsWorthRenderRsp) GetWorth() bool {
	if x != nil {
		return x.Worth
	}
	return false
}

func (x *IsWorthRenderRsp) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *IsWorthRenderRsp) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *IsWorthRenderRsp) GetGameUrl() string {
	if x != nil {
		return x.GameUrl
	}
	return ""
}

type OpenRenderNotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomid   string `protobuf:"bytes,1,opt,name=roomid,proto3" json:"roomid,omitempty"`   // 直播的话是showId，歌房的话是roomId
	AppId    string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`     // 游戏appid
	RoundId  string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次id
	CdnUrl   string `protobuf:"bytes,4,opt,name=cdnUrl,proto3" json:"cdnUrl,omitempty"`
	RenderId string `protobuf:"bytes,5,opt,name=renderId,proto3" json:"renderId,omitempty"`
	StartTs  uint32 `protobuf:"varint,6,opt,name=startTs,proto3" json:"startTs,omitempty"`
	EndTs    uint32 `protobuf:"varint,7,opt,name=endTs,proto3" json:"endTs,omitempty"`
	RoomType uint32 `protobuf:"varint,8,opt,name=roomType,proto3" json:"roomType,omitempty"` // 房间类型 1 歌房 2双人房 3直播
	CdnUrl2  string `protobuf:"bytes,9,opt,name=cdnUrl2,proto3" json:"cdnUrl2,omitempty"`
}

func (x *OpenRenderNotifyReq) Reset() {
	*x = OpenRenderNotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenRenderNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRenderNotifyReq) ProtoMessage() {}

func (x *OpenRenderNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRenderNotifyReq.ProtoReflect.Descriptor instead.
func (*OpenRenderNotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{12}
}

func (x *OpenRenderNotifyReq) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *OpenRenderNotifyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *OpenRenderNotifyReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *OpenRenderNotifyReq) GetCdnUrl() string {
	if x != nil {
		return x.CdnUrl
	}
	return ""
}

func (x *OpenRenderNotifyReq) GetRenderId() string {
	if x != nil {
		return x.RenderId
	}
	return ""
}

func (x *OpenRenderNotifyReq) GetStartTs() uint32 {
	if x != nil {
		return x.StartTs
	}
	return 0
}

func (x *OpenRenderNotifyReq) GetEndTs() uint32 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *OpenRenderNotifyReq) GetRoomType() uint32 {
	if x != nil {
		return x.RoomType
	}
	return 0
}

func (x *OpenRenderNotifyReq) GetCdnUrl2() string {
	if x != nil {
		return x.CdnUrl2
	}
	return ""
}

type OpenRenderNotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	ErrorCode int32 `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 错误信息
	ErrorMsg string `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
}

func (x *OpenRenderNotifyRsp) Reset() {
	*x = OpenRenderNotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenRenderNotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRenderNotifyRsp) ProtoMessage() {}

func (x *OpenRenderNotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRenderNotifyRsp.ProtoReflect.Descriptor instead.
func (*OpenRenderNotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{13}
}

func (x *OpenRenderNotifyRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *OpenRenderNotifyRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type CloseRenderNotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Roomid   string `protobuf:"bytes,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	AppId    string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`     // 游戏appid
	RoundId  string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次id
	RenderId string `protobuf:"bytes,4,opt,name=renderId,proto3" json:"renderId,omitempty"`
	EndTs    uint32 `protobuf:"varint,5,opt,name=endTs,proto3" json:"endTs,omitempty"`
	RoomType uint32 `protobuf:"varint,6,opt,name=roomType,proto3" json:"roomType,omitempty"` // 房间类型 1 歌房 2双人房 3直播
}

func (x *CloseRenderNotifyReq) Reset() {
	*x = CloseRenderNotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseRenderNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseRenderNotifyReq) ProtoMessage() {}

func (x *CloseRenderNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseRenderNotifyReq.ProtoReflect.Descriptor instead.
func (*CloseRenderNotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{14}
}

func (x *CloseRenderNotifyReq) GetRoomid() string {
	if x != nil {
		return x.Roomid
	}
	return ""
}

func (x *CloseRenderNotifyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CloseRenderNotifyReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *CloseRenderNotifyReq) GetRenderId() string {
	if x != nil {
		return x.RenderId
	}
	return ""
}

func (x *CloseRenderNotifyReq) GetEndTs() uint32 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *CloseRenderNotifyReq) GetRoomType() uint32 {
	if x != nil {
		return x.RoomType
	}
	return 0
}

type CloseRenderNotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	ErrorCode int32 `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 错误信息
	ErrorMsg string `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
}

func (x *CloseRenderNotifyRsp) Reset() {
	*x = CloseRenderNotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseRenderNotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseRenderNotifyRsp) ProtoMessage() {}

func (x *CloseRenderNotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseRenderNotifyRsp.ProtoReflect.Descriptor instead.
func (*CloseRenderNotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{15}
}

func (x *CloseRenderNotifyRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *CloseRenderNotifyRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type RegistSlowCloseTimerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId  string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`   // 直播的话是showId，歌房的话是roomId
	AppId   string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`     // 游戏appid
	RoundId string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 场次id
	Ts      int64  `protobuf:"varint,4,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *RegistSlowCloseTimerReq) Reset() {
	*x = RegistSlowCloseTimerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegistSlowCloseTimerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegistSlowCloseTimerReq) ProtoMessage() {}

func (x *RegistSlowCloseTimerReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegistSlowCloseTimerReq.ProtoReflect.Descriptor instead.
func (*RegistSlowCloseTimerReq) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{16}
}

func (x *RegistSlowCloseTimerReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RegistSlowCloseTimerReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RegistSlowCloseTimerReq) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

func (x *RegistSlowCloseTimerReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type SlowCloseTimerBiz struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId  string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`   // 直播的话是showId，歌房的话是roomId
	AppId   string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`     // 游戏appid
	RoundId string `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 轮次ID
}

func (x *SlowCloseTimerBiz) Reset() {
	*x = SlowCloseTimerBiz{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlowCloseTimerBiz) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlowCloseTimerBiz) ProtoMessage() {}

func (x *SlowCloseTimerBiz) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlowCloseTimerBiz.ProtoReflect.Descriptor instead.
func (*SlowCloseTimerBiz) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{17}
}

func (x *SlowCloseTimerBiz) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SlowCloseTimerBiz) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SlowCloseTimerBiz) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type RegistSlowCloseTimerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 错误码
	ErrorCode int32 `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 错误信息
	ErrorMsg string `protobuf:"bytes,2,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`
}

func (x *RegistSlowCloseTimerRsp) Reset() {
	*x = RegistSlowCloseTimerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegistSlowCloseTimerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegistSlowCloseTimerRsp) ProtoMessage() {}

func (x *RegistSlowCloseTimerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegistSlowCloseTimerRsp.ProtoReflect.Descriptor instead.
func (*RegistSlowCloseTimerRsp) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP(), []int{18}
}

func (x *RegistSlowCloseTimerRsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *RegistSlowCloseTimerRsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

var File_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto protoreflect.FileDescriptor

var file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDesc = []byte{
	0x0a, 0x43, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x72, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x5f, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x27, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x01, 0x0a, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67,
	0x61, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61,
	0x6d, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x3d, 0x0a, 0x07, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x64, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x64,
	0x6e, 0x55, 0x72, 0x6c, 0x22, 0x91, 0x01, 0x0a, 0x08, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x08, 0x72, 0x6f, 0x6f,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x0a, 0x0a, 0x08, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x52, 0x73, 0x70, 0x22, 0x3f, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x69, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x09, 0x6d, 0x61, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x73, 0x1a, 0x5a, 0x0a, 0x0e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x9c, 0x01, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x54, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74,
	0x54, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x22, 0x0e, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x73, 0x70,
	0x22, 0xe9, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x64, 0x6e, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x64, 0x6e,
	0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x64, 0x6e, 0x55, 0x72, 0x6c, 0x32, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x64, 0x6e, 0x55, 0x72, 0x6c, 0x32, 0x12, 0x3d, 0x0a,
	0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x21, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x75, 0x0a, 0x0f,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f,
	0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x10, 0x49, 0x73, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x10,
	0x49, 0x73, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70,
	0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x22, 0xf7, 0x01, 0x0a, 0x13, 0x4f,
	0x70, 0x65, 0x6e, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x64,
	0x6e, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x64, 0x6e, 0x55,
	0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x64, 0x54,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x64,
	0x6e, 0x55, 0x72, 0x6c, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x64, 0x6e,
	0x55, 0x72, 0x6c, 0x32, 0x22, 0x51, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0xac, 0x01, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x73,
	0x65, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x6f,
	0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x6f,
	0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22, 0x52, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x52,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x71, 0x0a, 0x17, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x77, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0x5b, 0x0a,
	0x11, 0x53, 0x6c, 0x6f, 0x77, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x42,
	0x69, 0x7a, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x22, 0x55, 0x0a, 0x17, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x77, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73,
	0x67, 0x32, 0xb3, 0x04, 0x0a, 0x12, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x04, 0x4f, 0x70, 0x65, 0x6e,
	0x12, 0x19, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x4f,
	0x70, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x05, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x12,
	0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x0a, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x06, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x12, 0x1b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x09,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12, 0x1e, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x65, 0x61,
	0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x14, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x77, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x72, 0x12, 0x29, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x77, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x6f, 0x77, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x11, 0x53, 0x6c, 0x6f, 0x77, 0x43,
	0x6c, 0x6f, 0x73, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x12, 0x1b, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x48, 0x5a, 0x46, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescOnce sync.Once
	file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescData = file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDesc
)

func file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescData)
	})
	return file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDescData
}

var file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_goTypes = []interface{}{
	(*OpenReq)(nil),                     // 0: interactive_game.OpenReq
	(*OpenRsp)(nil),                     // 1: interactive_game.OpenRsp
	(*CloseReq)(nil),                    // 2: interactive_game.CloseReq
	(*CloseRsp)(nil),                    // 3: interactive_game.CloseRsp
	(*BatchStateReq)(nil),               // 4: interactive_game.BatchStateReq
	(*BatchStateRsp)(nil),               // 5: interactive_game.BatchStateRsp
	(*HeartbeatReq)(nil),                // 6: interactive_game.HeartbeatReq
	(*HeartbeatRsp)(nil),                // 7: interactive_game.HeartbeatRsp
	(*RenderInfo)(nil),                  // 8: interactive_game.RenderInfo
	(*CancelTimerInfo)(nil),             // 9: interactive_game.CancelTimerInfo
	(*IsWorthRenderReq)(nil),            // 10: interactive_game.IsWorthRenderReq
	(*IsWorthRenderRsp)(nil),            // 11: interactive_game.IsWorthRenderRsp
	(*OpenRenderNotifyReq)(nil),         // 12: interactive_game.OpenRenderNotifyReq
	(*OpenRenderNotifyRsp)(nil),         // 13: interactive_game.OpenRenderNotifyRsp
	(*CloseRenderNotifyReq)(nil),        // 14: interactive_game.CloseRenderNotifyReq
	(*CloseRenderNotifyRsp)(nil),        // 15: interactive_game.CloseRenderNotifyRsp
	(*RegistSlowCloseTimerReq)(nil),     // 16: interactive_game.RegistSlowCloseTimerReq
	(*SlowCloseTimerBiz)(nil),           // 17: interactive_game.SlowCloseTimerBiz
	(*RegistSlowCloseTimerRsp)(nil),     // 18: interactive_game.RegistSlowCloseTimerRsp
	nil,                                 // 19: interactive_game.BatchStateRsp.MapStatesEntry
	(common.RoomType)(0),                // 20: interactive_game.common.RoomType
	(*timer.TimerCallbackRequest)(nil),  // 21: timer.TimerCallbackRequest
	(*timer.TimerCallbackResponse)(nil), // 22: timer.TimerCallbackResponse
}
var file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_depIdxs = []int32{
	20, // 0: interactive_game.OpenReq.roomType:type_name -> interactive_game.common.RoomType
	20, // 1: interactive_game.CloseReq.roomType:type_name -> interactive_game.common.RoomType
	19, // 2: interactive_game.BatchStateRsp.mapStates:type_name -> interactive_game.BatchStateRsp.MapStatesEntry
	20, // 3: interactive_game.RenderInfo.roomType:type_name -> interactive_game.common.RoomType
	8,  // 4: interactive_game.BatchStateRsp.MapStatesEntry.value:type_name -> interactive_game.RenderInfo
	0,  // 5: interactive_game.CloudRenderAdapter.Open:input_type -> interactive_game.OpenReq
	2,  // 6: interactive_game.CloudRenderAdapter.Close:input_type -> interactive_game.CloseReq
	4,  // 7: interactive_game.CloudRenderAdapter.BatchState:input_type -> interactive_game.BatchStateReq
	21, // 8: interactive_game.CloudRenderAdapter.Cancel:input_type -> timer.TimerCallbackRequest
	6,  // 9: interactive_game.CloudRenderAdapter.Heartbeat:input_type -> interactive_game.HeartbeatReq
	16, // 10: interactive_game.CloudRenderAdapter.RegistSlowCloseTimer:input_type -> interactive_game.RegistSlowCloseTimerReq
	21, // 11: interactive_game.CloudRenderAdapter.SlowCloseCallBack:input_type -> timer.TimerCallbackRequest
	1,  // 12: interactive_game.CloudRenderAdapter.Open:output_type -> interactive_game.OpenRsp
	3,  // 13: interactive_game.CloudRenderAdapter.Close:output_type -> interactive_game.CloseRsp
	5,  // 14: interactive_game.CloudRenderAdapter.BatchState:output_type -> interactive_game.BatchStateRsp
	22, // 15: interactive_game.CloudRenderAdapter.Cancel:output_type -> timer.TimerCallbackResponse
	7,  // 16: interactive_game.CloudRenderAdapter.Heartbeat:output_type -> interactive_game.HeartbeatRsp
	18, // 17: interactive_game.CloudRenderAdapter.RegistSlowCloseTimer:output_type -> interactive_game.RegistSlowCloseTimerRsp
	22, // 18: interactive_game.CloudRenderAdapter.SlowCloseCallBack:output_type -> timer.TimerCallbackResponse
	12, // [12:19] is the sub-list for method output_type
	5,  // [5:12] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_init() }
func file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_init() {
	if File_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchStateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartbeatRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelTimerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsWorthRenderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsWorthRenderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenRenderNotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenRenderNotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseRenderNotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseRenderNotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegistSlowCloseTimerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlowCloseTimerBiz); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegistSlowCloseTimerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto = out.File
	file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_rawDesc = nil
	file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_goTypes = nil
	file_pb_interactive_game_cloud_render_adapter_cloud_render_adapter_proto_depIdxs = nil
}
