{"swagger": "2.0", "info": {"title": "pb/interactive_game/cloud_render_adapter/cloud_render_adapter.proto", "version": "version not set"}, "tags": [{"name": "CloudRenderAdapter"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.CloudRenderAdapter/BatchState": {"post": {"summary": "查看当前状态", "operationId": "CloudRenderAdapter_BatchState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameBatchStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameBatchStateReq"}}], "tags": ["CloudRenderAdapter"]}}, "/interactive_game.CloudRenderAdapter/Cancel": {"post": {"summary": "超时cancel", "operationId": "CloudRenderAdapter_Cancel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/timerTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/timerTimerCallbackRequest"}}], "tags": ["CloudRenderAdapter"]}}, "/interactive_game.CloudRenderAdapter/Close": {"post": {"summary": "结束渲染", "operationId": "CloudRenderAdapter_Close", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameCloseRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameCloseReq"}}], "tags": ["CloudRenderAdapter"]}}, "/interactive_game.CloudRenderAdapter/Heartbeat": {"post": {"summary": "心跳，刷新定时器", "operationId": "CloudRenderAdapter_Heartbeat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameHeartbeatRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameHeartbeatReq"}}], "tags": ["CloudRenderAdapter"]}}, "/interactive_game.CloudRenderAdapter/Open": {"post": {"summary": "开始渲染", "operationId": "CloudRenderAdapter_Open", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameOpenRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameOpenReq"}}], "tags": ["CloudRenderAdapter"]}}, "/interactive_game.CloudRenderAdapter/RegistSlowCloseTimer": {"post": {"summary": "注册慢关闭定时器", "operationId": "CloudRenderAdapter_RegistSlowCloseTimer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameRegistSlowCloseTimerRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameRegistSlowCloseTimerReq"}}], "tags": ["CloudRenderAdapter"]}}, "/interactive_game.CloudRenderAdapter/SlowCloseCallBack": {"post": {"summary": "慢关闭定时器回调", "operationId": "CloudRenderAdapter_SlowCloseCallBack", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/timerTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/timerTimerCallbackRequest"}}], "tags": ["CloudRenderAdapter"]}}}, "definitions": {"interactive_gameBatchStateReq": {"type": "object", "properties": {"roomids": {"type": "array", "items": {"type": "string"}, "title": "roomids"}, "appid": {"type": "string", "title": "游戏appid"}}}, "interactive_gameBatchStateRsp": {"type": "object", "properties": {"mapStates": {"type": "object", "additionalProperties": {"$ref": "#/definitions/interactive_gameRenderInfo"}}}}, "interactive_gameCloseReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "roomid，Adapter内解决roomid到renderid的映射"}, "appId": {"type": "string", "title": "游戏appid"}, "roundId": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}}}, "interactive_gameCloseRsp": {"type": "object"}, "interactive_gameHeartbeatReq": {"type": "object", "properties": {"roomid": {"type": "string", "title": "roomid，Adapter内解决roomid到renderid的映射"}, "appId": {"type": "string", "title": "游戏appid"}, "heartbeatTs": {"type": "string", "format": "int64", "title": "心跳时间戳，秒"}, "nextInterval": {"type": "string", "format": "int64", "title": "下一次间隔，秒"}, "roundId": {"type": "string"}}}, "interactive_gameHeartbeatRsp": {"type": "object"}, "interactive_gameOpenReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "appId": {"type": "string", "title": "游戏appid"}, "ownerOpenId": {"type": "string", "title": "房主 openId"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType", "title": "房间类型"}, "roundId": {"type": "string", "title": "场次id"}, "gameUrl": {"type": "string", "title": "上游直接拼好的游戏URL"}}}, "interactive_gameOpenRsp": {"type": "object", "properties": {"renderID": {"type": "string", "title": "close和heartbeat要用，得存下来"}, "cdnUrl": {"type": "string", "title": "cdn地址，实时返回3-5s后有效"}}}, "interactive_gameRegistSlowCloseTimerReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "直播的话是showId，歌房的话是roomId"}, "appId": {"type": "string", "title": "游戏appid"}, "roundId": {"type": "string", "title": "场次id"}, "ts": {"type": "string", "format": "int64"}}}, "interactive_gameRegistSlowCloseTimerRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}}}, "interactive_gameRenderInfo": {"type": "object", "properties": {"renderID": {"type": "string", "title": "renderID，Close的时候有用"}, "cdnUrl": {"type": "string", "title": "cdn"}, "updateTs": {"type": "string", "format": "int64", "title": "更新时间戳，秒"}, "closeTs": {"type": "string", "format": "int64", "title": "cancel时间戳，秒"}, "roundId": {"type": "string"}, "cdnUrl2": {"type": "string"}, "roomType": {"$ref": "#/definitions/interactive_gamecommonRoomType"}}, "title": "renderInfo 存储，key为 render:{roomId}"}, "interactive_gamecommonRoomType": {"type": "string", "enum": ["TypeNone", "TypeKtv", "TypeTwins", "TypeLive", "TypeMatch"], "default": "TypeNone", "title": "- TypeKtv: 歌房\n - TypeTwins: 双人房\n - TypeLive: 直播\n - TypeMatch: 匹配房"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "timerTimerCallbackRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "timerId": {"type": "string"}, "fireTime": {"type": "string", "format": "int64", "title": "触发时间 毫秒"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据"}}}, "timerTimerCallbackResponse": {"type": "object", "properties": {"cancel": {"type": "boolean", "title": "用于周期定时器取消"}, "nextFireTime": {"type": "string", "format": "int64", "title": "用于一次性定时器指定下次触发时间"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据 不为 nil 则覆盖"}}}}}