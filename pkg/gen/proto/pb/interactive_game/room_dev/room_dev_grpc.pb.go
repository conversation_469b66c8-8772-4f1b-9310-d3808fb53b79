// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/interactive_game/room_dev/room_dev.proto

package room_dev

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	RoomDev_DevCreateGame_FullMethodName = "/interactive_game.RoomDev/DevCreateGame"
	RoomDev_DevGameOver_FullMethodName   = "/interactive_game.RoomDev/DevGameOver"
	RoomDev_DevPlayerOut_FullMethodName  = "/interactive_game.RoomDev/DevPlayerOut"
)

// RoomDevClient is the client API for RoomDev service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RoomDevClient interface {
	// 创建游戏 (空实现)
	DevCreateGame(ctx context.Context, in *DevCreateGameReq, opts ...grpc.CallOption) (*DevCreateGameRsp, error)
	// 结束游戏
	DevGameOver(ctx context.Context, in *DevGameOverReq, opts ...grpc.CallOption) (*DevGameOverRsp, error)
	// 中途淘汰
	DevPlayerOut(ctx context.Context, in *DevPlayerOutReq, opts ...grpc.CallOption) (*DevPlayerOutRsp, error)
}

type roomDevClient struct {
	cc grpc.ClientConnInterface
}

func NewRoomDevClient(cc grpc.ClientConnInterface) RoomDevClient {
	return &roomDevClient{cc}
}

func (c *roomDevClient) DevCreateGame(ctx context.Context, in *DevCreateGameReq, opts ...grpc.CallOption) (*DevCreateGameRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DevCreateGameRsp)
	err := c.cc.Invoke(ctx, RoomDev_DevCreateGame_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomDevClient) DevGameOver(ctx context.Context, in *DevGameOverReq, opts ...grpc.CallOption) (*DevGameOverRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DevGameOverRsp)
	err := c.cc.Invoke(ctx, RoomDev_DevGameOver_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *roomDevClient) DevPlayerOut(ctx context.Context, in *DevPlayerOutReq, opts ...grpc.CallOption) (*DevPlayerOutRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DevPlayerOutRsp)
	err := c.cc.Invoke(ctx, RoomDev_DevPlayerOut_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RoomDevServer is the server API for RoomDev service.
// All implementations should embed UnimplementedRoomDevServer
// for forward compatibility
type RoomDevServer interface {
	// 创建游戏 (空实现)
	DevCreateGame(context.Context, *DevCreateGameReq) (*DevCreateGameRsp, error)
	// 结束游戏
	DevGameOver(context.Context, *DevGameOverReq) (*DevGameOverRsp, error)
	// 中途淘汰
	DevPlayerOut(context.Context, *DevPlayerOutReq) (*DevPlayerOutRsp, error)
}

// UnimplementedRoomDevServer should be embedded to have forward compatible implementations.
type UnimplementedRoomDevServer struct {
}

func (UnimplementedRoomDevServer) DevCreateGame(context.Context, *DevCreateGameReq) (*DevCreateGameRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevCreateGame not implemented")
}
func (UnimplementedRoomDevServer) DevGameOver(context.Context, *DevGameOverReq) (*DevGameOverRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevGameOver not implemented")
}
func (UnimplementedRoomDevServer) DevPlayerOut(context.Context, *DevPlayerOutReq) (*DevPlayerOutRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevPlayerOut not implemented")
}

// UnsafeRoomDevServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RoomDevServer will
// result in compilation errors.
type UnsafeRoomDevServer interface {
	mustEmbedUnimplementedRoomDevServer()
}

func RegisterRoomDevServer(s grpc.ServiceRegistrar, srv RoomDevServer) {
	s.RegisterService(&RoomDev_ServiceDesc, srv)
}

func _RoomDev_DevCreateGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevCreateGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomDevServer).DevCreateGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomDev_DevCreateGame_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomDevServer).DevCreateGame(ctx, req.(*DevCreateGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomDev_DevGameOver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevGameOverReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomDevServer).DevGameOver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomDev_DevGameOver_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomDevServer).DevGameOver(ctx, req.(*DevGameOverReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RoomDev_DevPlayerOut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevPlayerOutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RoomDevServer).DevPlayerOut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RoomDev_DevPlayerOut_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RoomDevServer).DevPlayerOut(ctx, req.(*DevPlayerOutReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RoomDev_ServiceDesc is the grpc.ServiceDesc for RoomDev service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RoomDev_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "interactive_game.RoomDev",
	HandlerType: (*RoomDevServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DevCreateGame",
			Handler:    _RoomDev_DevCreateGame_Handler,
		},
		{
			MethodName: "DevGameOver",
			Handler:    _RoomDev_DevGameOver_Handler,
		},
		{
			MethodName: "DevPlayerOut",
			Handler:    _RoomDev_DevPlayerOut_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/interactive_game/room_dev/room_dev.proto",
}
