{"swagger": "2.0", "info": {"title": "pb/interactive_game/room_dev/room_dev.proto", "version": "version not set"}, "tags": [{"name": "<PERSON><PERSON><PERSON>"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/interactive_game.RoomDev/DevCreateGame": {"post": {"summary": "创建游戏 (空实现)", "operationId": "RoomDev_DevCreateGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameDevCreateGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameDevCreateGameReq"}}], "tags": ["<PERSON><PERSON><PERSON>"]}}, "/interactive_game.RoomDev/DevGameOver": {"post": {"summary": "结束游戏", "operationId": "RoomDev_DevGameOver", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameDevGameOverRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameDevGameOverReq"}}], "tags": ["<PERSON><PERSON><PERSON>"]}}, "/interactive_game.RoomDev/DevPlayerOut": {"post": {"summary": "中途淘汰", "operationId": "RoomDev_DevPlayerOut", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/interactive_gameDevPlayerOutRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/interactive_gameDevPlayerOutReq"}}], "tags": ["<PERSON><PERSON><PERSON>"]}}}, "definitions": {"interactive_gameDevCreateGameReq": {"type": "object"}, "interactive_gameDevCreateGameRsp": {"type": "object"}, "interactive_gameDevGameOverReq": {"type": "object", "properties": {"roomId": {"type": "string"}}}, "interactive_gameDevGameOverRsp": {"type": "object"}, "interactive_gameDevPlayerOutReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "openId": {"type": "string"}}}, "interactive_gameDevPlayerOutRsp": {"type": "object"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}