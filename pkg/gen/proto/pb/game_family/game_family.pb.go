// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_family/game_family.proto

package game_family

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GroupStatus int32

const (
	GroupStatus_GROUP_STATUS_NORMAL      GroupStatus = 0 //默认正常状态
	GroupStatus_GROUP_STATUS_DESTORY     GroupStatus = 1 //解散状态
	GroupStatus_GROUP_STATUS_NO_EXIST    GroupStatus = 2 //请求的家族id不存在
	GroupStatus_GROUP_STATUS_FROZEN      GroupStatus = 3 //因请求家族id的族长家族vip会员过期，导致该家族处于被冻结状态
	GroupStatus_GROUP_STATUS_NEED_MODIFY GroupStatus = 4 //家族信息需要修改，导致家族处于冻结状态
	GroupStatus_GROUP_STATUS_NO_ACTIVE   GroupStatus = 5 //家族不活跃冻结，家族惩罚工具
)

// Enum value maps for GroupStatus.
var (
	GroupStatus_name = map[int32]string{
		0: "GROUP_STATUS_NORMAL",
		1: "GROUP_STATUS_DESTORY",
		2: "GROUP_STATUS_NO_EXIST",
		3: "GROUP_STATUS_FROZEN",
		4: "GROUP_STATUS_NEED_MODIFY",
		5: "GROUP_STATUS_NO_ACTIVE",
	}
	GroupStatus_value = map[string]int32{
		"GROUP_STATUS_NORMAL":      0,
		"GROUP_STATUS_DESTORY":     1,
		"GROUP_STATUS_NO_EXIST":    2,
		"GROUP_STATUS_FROZEN":      3,
		"GROUP_STATUS_NEED_MODIFY": 4,
		"GROUP_STATUS_NO_ACTIVE":   5,
	}
)

func (x GroupStatus) Enum() *GroupStatus {
	p := new(GroupStatus)
	*p = x
	return p
}

func (x GroupStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_family_game_family_proto_enumTypes[0].Descriptor()
}

func (GroupStatus) Type() protoreflect.EnumType {
	return &file_pb_game_family_game_family_proto_enumTypes[0]
}

func (x GroupStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupStatus.Descriptor instead.
func (GroupStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{0}
}

type GroupNotifyEventType int32

const (
	GroupNotifyEventType_GROUP_NOTIFY_EVENT_TYPE_NONE   GroupNotifyEventType = 0
	GroupNotifyEventType_GROUP_NOTIFY_EVENT_TYPE_JOIN   GroupNotifyEventType = 7 // 加入
	GroupNotifyEventType_GROUP_NOTIFY_EVENT_TYPE_LEAVE  GroupNotifyEventType = 8 // 离开
	GroupNotifyEventType_GROUP_NOTIFY_EVENT_TYPE_DELETE GroupNotifyEventType = 9 // 被踢
)

// Enum value maps for GroupNotifyEventType.
var (
	GroupNotifyEventType_name = map[int32]string{
		0: "GROUP_NOTIFY_EVENT_TYPE_NONE",
		7: "GROUP_NOTIFY_EVENT_TYPE_JOIN",
		8: "GROUP_NOTIFY_EVENT_TYPE_LEAVE",
		9: "GROUP_NOTIFY_EVENT_TYPE_DELETE",
	}
	GroupNotifyEventType_value = map[string]int32{
		"GROUP_NOTIFY_EVENT_TYPE_NONE":   0,
		"GROUP_NOTIFY_EVENT_TYPE_JOIN":   7,
		"GROUP_NOTIFY_EVENT_TYPE_LEAVE":  8,
		"GROUP_NOTIFY_EVENT_TYPE_DELETE": 9,
	}
)

func (x GroupNotifyEventType) Enum() *GroupNotifyEventType {
	p := new(GroupNotifyEventType)
	*p = x
	return p
}

func (x GroupNotifyEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupNotifyEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_family_game_family_proto_enumTypes[1].Descriptor()
}

func (GroupNotifyEventType) Type() protoreflect.EnumType {
	return &file_pb_game_family_game_family_proto_enumTypes[1]
}

func (x GroupNotifyEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupNotifyEventType.Descriptor instead.
func (GroupNotifyEventType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{1}
}

type GroupScoreEventType int32

const (
	GroupScoreEventType_GROUP_EVENT_SCORE_TYPE_TOTAL         GroupScoreEventType = 0 // 所有
	GroupScoreEventType_GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE GroupScoreEventType = 1 // 任务完成
	GroupScoreEventType_GROUP_SCORE_EVENT_TYPE_CONSUME_KB    GroupScoreEventType = 2 // 送礼
	GroupScoreEventType_GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE GroupScoreEventType = 3 // 游戏获取
)

// Enum value maps for GroupScoreEventType.
var (
	GroupScoreEventType_name = map[int32]string{
		0: "GROUP_EVENT_SCORE_TYPE_TOTAL",
		1: "GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE",
		2: "GROUP_SCORE_EVENT_TYPE_CONSUME_KB",
		3: "GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE",
	}
	GroupScoreEventType_value = map[string]int32{
		"GROUP_EVENT_SCORE_TYPE_TOTAL":         0,
		"GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE": 1,
		"GROUP_SCORE_EVENT_TYPE_CONSUME_KB":    2,
		"GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE": 3,
	}
)

func (x GroupScoreEventType) Enum() *GroupScoreEventType {
	p := new(GroupScoreEventType)
	*p = x
	return p
}

func (x GroupScoreEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupScoreEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_family_game_family_proto_enumTypes[2].Descriptor()
}

func (GroupScoreEventType) Type() protoreflect.EnumType {
	return &file_pb_game_family_game_family_proto_enumTypes[2]
}

func (x GroupScoreEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupScoreEventType.Descriptor instead.
func (GroupScoreEventType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{2}
}

type BatchGetByFidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId   string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	GroupIds []uint64 `protobuf:"varint,3,rep,packed,name=groupIds,proto3" json:"groupIds,omitempty"` // 家族id列表
}

func (x *BatchGetByFidReq) Reset() {
	*x = BatchGetByFidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetByFidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetByFidReq) ProtoMessage() {}

func (x *BatchGetByFidReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetByFidReq.ProtoReflect.Descriptor instead.
func (*BatchGetByFidReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{0}
}

func (x *BatchGetByFidReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGetByFidReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchGetByFidReq) GetGroupIds() []uint64 {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

type BatchGetByFidRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapInfo map[uint64]*Group `protobuf:"bytes,1,rep,name=mapInfo,proto3" json:"mapInfo,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetByFidRsp) Reset() {
	*x = BatchGetByFidRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetByFidRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetByFidRsp) ProtoMessage() {}

func (x *BatchGetByFidRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetByFidRsp.ProtoReflect.Descriptor instead.
func (*BatchGetByFidRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{1}
}

func (x *BatchGetByFidRsp) GetMapInfo() map[uint64]*Group {
	if x != nil {
		return x.MapInfo
	}
	return nil
}

type BatchGetByOpenIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenIds []string `protobuf:"bytes,2,rep,name=openIds,proto3" json:"openIds,omitempty"`
}

func (x *BatchGetByOpenIdReq) Reset() {
	*x = BatchGetByOpenIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetByOpenIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetByOpenIdReq) ProtoMessage() {}

func (x *BatchGetByOpenIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetByOpenIdReq.ProtoReflect.Descriptor instead.
func (*BatchGetByOpenIdReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetByOpenIdReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGetByOpenIdReq) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type BatchGetByOpenIdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapInfo map[string]*Group `protobuf:"bytes,1,rep,name=mapInfo,proto3" json:"mapInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetByOpenIdRsp) Reset() {
	*x = BatchGetByOpenIdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetByOpenIdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetByOpenIdRsp) ProtoMessage() {}

func (x *BatchGetByOpenIdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetByOpenIdRsp.ProtoReflect.Descriptor instead.
func (*BatchGetByOpenIdRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{3}
}

func (x *BatchGetByOpenIdRsp) GetMapInfo() map[string]*Group {
	if x != nil {
		return x.MapInfo
	}
	return nil
}

type ConveneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`      // appId
	OpenId  string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`    // openId
	GroupId uint64 `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"` // 家族id
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`  // 召集文案
	JumpUrl string `protobuf:"bytes,5,opt,name=jumpUrl,proto3" json:"jumpUrl,omitempty"`  // 跳转链接
	Icon    string `protobuf:"bytes,6,opt,name=icon,proto3" json:"icon,omitempty"`        // 展示图片
	Title   string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`      // 标题
}

func (x *ConveneReq) Reset() {
	*x = ConveneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConveneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConveneReq) ProtoMessage() {}

func (x *ConveneReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConveneReq.ProtoReflect.Descriptor instead.
func (*ConveneReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{4}
}

func (x *ConveneReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ConveneReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ConveneReq) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ConveneReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ConveneReq) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *ConveneReq) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ConveneReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ConveneRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConveneRsp) Reset() {
	*x = ConveneRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConveneRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConveneRsp) ProtoMessage() {}

func (x *ConveneRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConveneRsp.ProtoReflect.Descriptor instead.
func (*ConveneRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{5}
}

type RecommandListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`   // appId
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"` // openId
	Num    uint64 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`      // 拉取的数量
}

func (x *RecommandListReq) Reset() {
	*x = RecommandListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommandListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommandListReq) ProtoMessage() {}

func (x *RecommandListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommandListReq.ProtoReflect.Descriptor instead.
func (*RecommandListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{6}
}

func (x *RecommandListReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RecommandListReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RecommandListReq) GetNum() uint64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type RecommandListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*Group `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"` // 推荐列表
}

func (x *RecommandListRsp) Reset() {
	*x = RecommandListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommandListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommandListRsp) ProtoMessage() {}

func (x *RecommandListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommandListRsp.ProtoReflect.Descriptor instead.
func (*RecommandListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{7}
}

func (x *RecommandListRsp) GetGroups() []*Group {
	if x != nil {
		return x.Groups
	}
	return nil
}

type JoinReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`      // appId
	OpenId  string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`    // openId
	GroupId uint64 `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"` // 家族id
	Reason  string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`    // 加入理由
}

func (x *JoinReq) Reset() {
	*x = JoinReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinReq) ProtoMessage() {}

func (x *JoinReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinReq.ProtoReflect.Descriptor instead.
func (*JoinReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{8}
}

func (x *JoinReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *JoinReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *JoinReq) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *JoinReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 根据grpc code = 0 判断是否申请成功
type JoinRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FreeEnter uint32 `protobuf:"varint,1,opt,name=freeEnter,proto3" json:"freeEnter,omitempty"` // 如果grpc code == 0且freeEnter == 1, 表示自动加入成功, 否则等审核, 审核通过后通过JoinCallbackReq回调业务
}

func (x *JoinRsp) Reset() {
	*x = JoinRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinRsp) ProtoMessage() {}

func (x *JoinRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinRsp.ProtoReflect.Descriptor instead.
func (*JoinRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{9}
}

func (x *JoinRsp) GetFreeEnter() uint32 {
	if x != nil {
		return x.FreeEnter
	}
	return 0
}

type Group struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Base *GroupBaseInfo `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"` // 基础信息
}

func (x *Group) Reset() {
	*x = Group{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Group) ProtoMessage() {}

func (x *Group) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Group.ProtoReflect.Descriptor instead.
func (*Group) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{10}
}

func (x *Group) GetBase() *GroupBaseInfo {
	if x != nil {
		return x.Base
	}
	return nil
}

type GroupBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                       uint64      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                              // 家族id
	Name                     string      `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                           // 家族名称
	Avatar                   string      `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`                                       // 家族头像
	AdminOpenId              string      `protobuf:"bytes,4,opt,name=adminOpenId,proto3" json:"adminOpenId,omitempty"`                             // 族长openId
	Status                   GroupStatus `protobuf:"varint,5,opt,name=status,proto3,enum=game.GroupStatus" json:"status,omitempty"`                // 家族状态
	CreateTime               uint64      `protobuf:"varint,6,opt,name=createTime,proto3" json:"createTime,omitempty"`                              // 创建时间
	FreeEnter                uint32      `protobuf:"varint,7,opt,name=freeEnter,proto3" json:"freeEnter,omitempty"`                                // 是否是免审加入家族，0不是免审家族，1是免审家族or自动通过
	MemberCount              uint64      `protobuf:"varint,8,opt,name=memberCount,proto3" json:"memberCount,omitempty"`                            // 当前家族人数
	Level                    uint64      `protobuf:"varint,9,opt,name=level,proto3" json:"level,omitempty"`                                        // 家族等级
	UserLevelLimit           uint32      `protobuf:"varint,10,opt,name=userLevelLimit,proto3" json:"userLevelLimit,omitempty"`                     // 加入用户等级限制
	UserJoinWealthLevelLimit uint32      `protobuf:"varint,11,opt,name=userJoinWealthLevelLimit,proto3" json:"userJoinWealthLevelLimit,omitempty"` // 用户加入财富等级限制
}

func (x *GroupBaseInfo) Reset() {
	*x = GroupBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupBaseInfo) ProtoMessage() {}

func (x *GroupBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupBaseInfo.ProtoReflect.Descriptor instead.
func (*GroupBaseInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{11}
}

func (x *GroupBaseInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupBaseInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GroupBaseInfo) GetAdminOpenId() string {
	if x != nil {
		return x.AdminOpenId
	}
	return ""
}

func (x *GroupBaseInfo) GetStatus() GroupStatus {
	if x != nil {
		return x.Status
	}
	return GroupStatus_GROUP_STATUS_NORMAL
}

func (x *GroupBaseInfo) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *GroupBaseInfo) GetFreeEnter() uint32 {
	if x != nil {
		return x.FreeEnter
	}
	return 0
}

func (x *GroupBaseInfo) GetMemberCount() uint64 {
	if x != nil {
		return x.MemberCount
	}
	return 0
}

func (x *GroupBaseInfo) GetLevel() uint64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GroupBaseInfo) GetUserLevelLimit() uint32 {
	if x != nil {
		return x.UserLevelLimit
	}
	return 0
}

func (x *GroupBaseInfo) GetUserJoinWealthLevelLimit() uint32 {
	if x != nil {
		return x.UserJoinWealthLevelLimit
	}
	return 0
}

// 回调通知接口, 全民旅行后台实现接口(JoinEventNotifyReq,JoinEventNotifyRsp)
type NotifyEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`          // appId
	OpenIds   []string `protobuf:"bytes,2,rep,name=openIds,proto3" json:"openIds,omitempty"`      // openIds
	GroupId   uint64   `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`     // 家族id
	EventType uint32   `protobuf:"varint,4,opt,name=eventType,proto3" json:"eventType,omitempty"` // GroupNotifyEventType
}

func (x *NotifyEventReq) Reset() {
	*x = NotifyEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyEventReq) ProtoMessage() {}

func (x *NotifyEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyEventReq.ProtoReflect.Descriptor instead.
func (*NotifyEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{12}
}

func (x *NotifyEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *NotifyEventReq) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

func (x *NotifyEventReq) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *NotifyEventReq) GetEventType() uint32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

type NotifyEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NotifyEventRsp) Reset() {
	*x = NotifyEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyEventRsp) ProtoMessage() {}

func (x *NotifyEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyEventRsp.ProtoReflect.Descriptor instead.
func (*NotifyEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{13}
}

type ConsumeNotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`          // appId
	Uids      []uint64 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`    // uids
	GroupId   uint64   `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`     // 家族id
	EventType uint32   `protobuf:"varint,4,opt,name=eventType,proto3" json:"eventType,omitempty"` // GroupNotifyEventType
}

func (x *ConsumeNotifyReq) Reset() {
	*x = ConsumeNotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeNotifyReq) ProtoMessage() {}

func (x *ConsumeNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeNotifyReq.ProtoReflect.Descriptor instead.
func (*ConsumeNotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{14}
}

func (x *ConsumeNotifyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ConsumeNotifyReq) GetUids() []uint64 {
	if x != nil {
		return x.Uids
	}
	return nil
}

func (x *ConsumeNotifyReq) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ConsumeNotifyReq) GetEventType() uint32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

type ConsumeNotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ConsumeNotifyRsp) Reset() {
	*x = ConsumeNotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeNotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeNotifyRsp) ProtoMessage() {}

func (x *ConsumeNotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeNotifyRsp.ProtoReflect.Descriptor instead.
func (*ConsumeNotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{15}
}

type ReportScoreEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string              `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`                                        // appId
	OpenId    string              `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`                                      // openId
	GroupId   uint64              `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`                                   // 家族id
	Score     uint64              `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                                       // 积分
	ScoreType GroupScoreEventType `protobuf:"varint,5,opt,name=scoreType,proto3,enum=game.GroupScoreEventType" json:"scoreType,omitempty"` // 积分类型
	ConsumeId string              `protobuf:"bytes,6,opt,name=consumeId,proto3" json:"consumeId,omitempty"`                                // 幂等id
	MapExt    map[string]string   `protobuf:"bytes,7,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ReportScoreEventReq) Reset() {
	*x = ReportScoreEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportScoreEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportScoreEventReq) ProtoMessage() {}

func (x *ReportScoreEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportScoreEventReq.ProtoReflect.Descriptor instead.
func (*ReportScoreEventReq) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{16}
}

func (x *ReportScoreEventReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportScoreEventReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReportScoreEventReq) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ReportScoreEventReq) GetScore() uint64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ReportScoreEventReq) GetScoreType() GroupScoreEventType {
	if x != nil {
		return x.ScoreType
	}
	return GroupScoreEventType_GROUP_EVENT_SCORE_TYPE_TOTAL
}

func (x *ReportScoreEventReq) GetConsumeId() string {
	if x != nil {
		return x.ConsumeId
	}
	return ""
}

func (x *ReportScoreEventReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

// 通过grpc code判断
type ReportScoreEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportScoreEventRsp) Reset() {
	*x = ReportScoreEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_family_game_family_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportScoreEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportScoreEventRsp) ProtoMessage() {}

func (x *ReportScoreEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_family_game_family_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportScoreEventRsp.ProtoReflect.Descriptor instead.
func (*ReportScoreEventRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_family_game_family_proto_rawDescGZIP(), []int{17}
}

var File_pb_game_family_game_family_proto protoreflect.FileDescriptor

var file_pb_game_family_game_family_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x5c, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x46, 0x69, 0x64, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x22, 0x9a, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x46, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x07, 0x6d,
	0x61, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x46, 0x69,
	0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x47, 0x0a, 0x0c, 0x4d, 0x61,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x45, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42,
	0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x13, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x40, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x73, 0x70, 0x2e, 0x4d,
	0x61, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x47, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb2, 0x01,
	0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x22, 0x0c, 0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x73, 0x70,
	0x22, 0x52, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x22, 0x37, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x69, 0x0a,
	0x07, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x27, 0x0a, 0x07, 0x4a, 0x6f, 0x69, 0x6e,
	0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x22, 0x30, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x62, 0x61,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x62,
	0x61, 0x73, 0x65, 0x22, 0xf2, 0x02, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x66, 0x72, 0x65, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x66, 0x72, 0x65, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x75, 0x73,
	0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x18,
	0x75, 0x73, 0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x18,
	0x75, 0x73, 0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x78, 0x0a, 0x0e, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x52, 0x73, 0x70, 0x22, 0x74, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x04, 0x75, 0x69,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x12, 0x0a, 0x10, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x22, 0xc4,
	0x02, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x09, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x06, 0x6d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61,
	0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x15, 0x0a, 0x13, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x2a, 0xae, 0x01, 0x0a,
	0x0b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x13,
	0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x52,
	0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4e, 0x4f, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45,
	0x4e, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4e, 0x45, 0x45, 0x44, 0x5f, 0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x10,
	0x04, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x05, 0x2a, 0xa1, 0x01,
	0x0a, 0x14, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x52, 0x4f, 0x55,
	0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4a, 0x4f, 0x49, 0x4e, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x47, 0x52,
	0x4f, 0x55, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x56, 0x45, 0x10, 0x08, 0x12, 0x22, 0x0a,
	0x1e, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x45, 0x56,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10,
	0x09, 0x2a, 0xb2, 0x01, 0x0a, 0x13, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x47,
	0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c,
	0x45, 0x54, 0x45, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53,
	0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x43, 0x4f, 0x4e, 0x53, 0x55, 0x4d, 0x45, 0x5f, 0x4b, 0x42, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24,
	0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45, 0x56, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x45, 0x10, 0x03, 0x32, 0xb5, 0x03, 0x0a, 0x0c, 0x46, 0x61, 0x6d, 0x69, 0x6c,
	0x79, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x46, 0x69, 0x64, 0x12, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x46, 0x69, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x42, 0x79, 0x46, 0x69, 0x64, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x4f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x2d, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x12, 0x10, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x3f, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x24, 0x0a, 0x04, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x0d, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x0d, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x0b, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x42, 0x43,
	0x5a, 0x41, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x61, 0x6d,
	0x69, 0x6c, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_family_game_family_proto_rawDescOnce sync.Once
	file_pb_game_family_game_family_proto_rawDescData = file_pb_game_family_game_family_proto_rawDesc
)

func file_pb_game_family_game_family_proto_rawDescGZIP() []byte {
	file_pb_game_family_game_family_proto_rawDescOnce.Do(func() {
		file_pb_game_family_game_family_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_family_game_family_proto_rawDescData)
	})
	return file_pb_game_family_game_family_proto_rawDescData
}

var file_pb_game_family_game_family_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pb_game_family_game_family_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_pb_game_family_game_family_proto_goTypes = []interface{}{
	(GroupStatus)(0),            // 0: game.GroupStatus
	(GroupNotifyEventType)(0),   // 1: game.GroupNotifyEventType
	(GroupScoreEventType)(0),    // 2: game.GroupScoreEventType
	(*BatchGetByFidReq)(nil),    // 3: game.BatchGetByFidReq
	(*BatchGetByFidRsp)(nil),    // 4: game.BatchGetByFidRsp
	(*BatchGetByOpenIdReq)(nil), // 5: game.BatchGetByOpenIdReq
	(*BatchGetByOpenIdRsp)(nil), // 6: game.BatchGetByOpenIdRsp
	(*ConveneReq)(nil),          // 7: game.ConveneReq
	(*ConveneRsp)(nil),          // 8: game.ConveneRsp
	(*RecommandListReq)(nil),    // 9: game.RecommandListReq
	(*RecommandListRsp)(nil),    // 10: game.RecommandListRsp
	(*JoinReq)(nil),             // 11: game.JoinReq
	(*JoinRsp)(nil),             // 12: game.JoinRsp
	(*Group)(nil),               // 13: game.Group
	(*GroupBaseInfo)(nil),       // 14: game.GroupBaseInfo
	(*NotifyEventReq)(nil),      // 15: game.NotifyEventReq
	(*NotifyEventRsp)(nil),      // 16: game.NotifyEventRsp
	(*ConsumeNotifyReq)(nil),    // 17: game.ConsumeNotifyReq
	(*ConsumeNotifyRsp)(nil),    // 18: game.ConsumeNotifyRsp
	(*ReportScoreEventReq)(nil), // 19: game.ReportScoreEventReq
	(*ReportScoreEventRsp)(nil), // 20: game.ReportScoreEventRsp
	nil,                         // 21: game.BatchGetByFidRsp.MapInfoEntry
	nil,                         // 22: game.BatchGetByOpenIdRsp.MapInfoEntry
	nil,                         // 23: game.ReportScoreEventReq.MapExtEntry
}
var file_pb_game_family_game_family_proto_depIdxs = []int32{
	21, // 0: game.BatchGetByFidRsp.mapInfo:type_name -> game.BatchGetByFidRsp.MapInfoEntry
	22, // 1: game.BatchGetByOpenIdRsp.mapInfo:type_name -> game.BatchGetByOpenIdRsp.MapInfoEntry
	13, // 2: game.RecommandListRsp.groups:type_name -> game.Group
	14, // 3: game.Group.base:type_name -> game.GroupBaseInfo
	0,  // 4: game.GroupBaseInfo.status:type_name -> game.GroupStatus
	2,  // 5: game.ReportScoreEventReq.scoreType:type_name -> game.GroupScoreEventType
	23, // 6: game.ReportScoreEventReq.mapExt:type_name -> game.ReportScoreEventReq.MapExtEntry
	13, // 7: game.BatchGetByFidRsp.MapInfoEntry.value:type_name -> game.Group
	13, // 8: game.BatchGetByOpenIdRsp.MapInfoEntry.value:type_name -> game.Group
	3,  // 9: game.FamilyServer.BatchGetByFid:input_type -> game.BatchGetByFidReq
	5,  // 10: game.FamilyServer.BatchGetByOpenId:input_type -> game.BatchGetByOpenIdReq
	7,  // 11: game.FamilyServer.Convene:input_type -> game.ConveneReq
	9,  // 12: game.FamilyServer.RecommandList:input_type -> game.RecommandListReq
	11, // 13: game.FamilyServer.Join:input_type -> game.JoinReq
	17, // 14: game.FamilyServer.ConsumeNotify:input_type -> game.ConsumeNotifyReq
	19, // 15: game.FamilyServer.ReportScore:input_type -> game.ReportScoreEventReq
	4,  // 16: game.FamilyServer.BatchGetByFid:output_type -> game.BatchGetByFidRsp
	6,  // 17: game.FamilyServer.BatchGetByOpenId:output_type -> game.BatchGetByOpenIdRsp
	8,  // 18: game.FamilyServer.Convene:output_type -> game.ConveneRsp
	10, // 19: game.FamilyServer.RecommandList:output_type -> game.RecommandListRsp
	12, // 20: game.FamilyServer.Join:output_type -> game.JoinRsp
	18, // 21: game.FamilyServer.ConsumeNotify:output_type -> game.ConsumeNotifyRsp
	20, // 22: game.FamilyServer.ReportScore:output_type -> game.ReportScoreEventRsp
	16, // [16:23] is the sub-list for method output_type
	9,  // [9:16] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_game_family_game_family_proto_init() }
func file_pb_game_family_game_family_proto_init() {
	if File_pb_game_family_game_family_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_family_game_family_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetByFidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetByFidRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetByOpenIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetByOpenIdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConveneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConveneRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommandListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommandListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Group); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeNotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeNotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportScoreEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_family_game_family_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportScoreEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_family_game_family_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_family_game_family_proto_goTypes,
		DependencyIndexes: file_pb_game_family_game_family_proto_depIdxs,
		EnumInfos:         file_pb_game_family_game_family_proto_enumTypes,
		MessageInfos:      file_pb_game_family_game_family_proto_msgTypes,
	}.Build()
	File_pb_game_family_game_family_proto = out.File
	file_pb_game_family_game_family_proto_rawDesc = nil
	file_pb_game_family_game_family_proto_goTypes = nil
	file_pb_game_family_game_family_proto_depIdxs = nil
}
