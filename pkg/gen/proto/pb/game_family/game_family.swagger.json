{"swagger": "2.0", "info": {"title": "pb/game_family/game_family.proto", "version": "version not set"}, "tags": [{"name": "FamilyServer"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game.FamilyServer/BatchGetByFid": {"post": {"summary": "根据家族Id查询指定家族信息", "operationId": "FamilyServer_BatchGetByFid", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBatchGetByFidRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBatchGetByFidReq"}}], "tags": ["FamilyServer"]}}, "/game.FamilyServer/BatchGetByOpenId": {"post": {"summary": "查询用户所在的家族信息", "operationId": "FamilyServer_BatchGetByOpenId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBatchGetByOpenIdRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBatchGetByOpenIdReq"}}], "tags": ["FamilyServer"]}}, "/game.FamilyServer/ConsumeNotify": {"post": {"summary": "接收事件通知, 回调给游戏实现的接口(NotifyEventReq,NotifyEventRsp)", "operationId": "FamilyServer_ConsumeNotify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameConsumeNotifyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameConsumeNotifyReq"}}], "tags": ["FamilyServer"]}}, "/game.FamilyServer/Convene": {"post": {"summary": "私信召集", "operationId": "FamilyServer_Convene", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameConveneRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameConveneReq"}}], "tags": ["FamilyServer"]}}, "/game.FamilyServer/Join": {"post": {"summary": "加入家族", "operationId": "FamilyServer_Join", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameJoinRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameJoinReq"}}], "tags": ["FamilyServer"]}}, "/game.FamilyServer/RecommandList": {"post": {"summary": "查询推荐家族列表", "operationId": "FamilyServer_RecommandList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameRecommandListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameRecommandListReq"}}], "tags": ["FamilyServer"]}}, "/game.FamilyServer/ReportScore": {"post": {"summary": "给家族加积分", "operationId": "FamilyServer_ReportScore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameReportScoreEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameReportScoreEventReq"}}], "tags": ["FamilyServer"]}}}, "definitions": {"gameBatchGetByFidReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "groupIds": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "家族id列表"}}}, "gameBatchGetByFidRsp": {"type": "object", "properties": {"mapInfo": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameGroup"}}}}, "gameBatchGetByOpenIdReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openIds": {"type": "array", "items": {"type": "string"}}}}, "gameBatchGetByOpenIdRsp": {"type": "object", "properties": {"mapInfo": {"type": "object", "additionalProperties": {"$ref": "#/definitions/gameGroup"}}}}, "gameConsumeNotifyReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "uids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "uids"}, "groupId": {"type": "string", "format": "uint64", "title": "家族id"}, "eventType": {"type": "integer", "format": "int64", "title": "GroupNotifyEventType"}}}, "gameConsumeNotifyRsp": {"type": "object"}, "gameConveneReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "openId": {"type": "string", "title": "openId"}, "groupId": {"type": "string", "format": "uint64", "title": "家族id"}, "content": {"type": "string", "title": "召集文案"}, "jumpUrl": {"type": "string", "title": "跳转链接"}, "icon": {"type": "string", "title": "展示图片"}, "title": {"type": "string", "title": "标题"}}}, "gameConveneRsp": {"type": "object"}, "gameGroup": {"type": "object", "properties": {"base": {"$ref": "#/definitions/gameGroupBaseInfo", "title": "基础信息"}}}, "gameGroupBaseInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "家族id"}, "name": {"type": "string", "title": "家族名称"}, "avatar": {"type": "string", "title": "家族头像"}, "adminOpenId": {"type": "string", "title": "族长openId"}, "status": {"$ref": "#/definitions/gameGroupStatus", "title": "家族状态"}, "createTime": {"type": "string", "format": "uint64", "title": "创建时间"}, "freeEnter": {"type": "integer", "format": "int64", "title": "是否是免审加入家族，0不是免审家族，1是免审家族or自动通过"}, "memberCount": {"type": "string", "format": "uint64", "title": "当前家族人数"}, "level": {"type": "string", "format": "uint64", "title": "家族等级"}, "userLevelLimit": {"type": "integer", "format": "int64", "title": "加入用户等级限制"}, "userJoinWealthLevelLimit": {"type": "integer", "format": "int64", "title": "用户加入财富等级限制"}}}, "gameGroupScoreEventType": {"type": "string", "enum": ["GROUP_EVENT_SCORE_TYPE_TOTAL", "GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE", "GROUP_SCORE_EVENT_TYPE_CONSUME_KB", "GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE"], "default": "GROUP_EVENT_SCORE_TYPE_TOTAL", "title": "- GROUP_EVENT_SCORE_TYPE_TOTAL: 所有\n - GROUP_SCORE_EVENT_TYPE_TASK_COMPLETE: 任务完成\n - GROUP_SCORE_EVENT_TYPE_CONSUME_KB: 送礼\n - GROUP_SCORE_EVENT_TYPE_GAME_COMPLETE: 游戏获取"}, "gameGroupStatus": {"type": "string", "enum": ["GROUP_STATUS_NORMAL", "GROUP_STATUS_DESTORY", "GROUP_STATUS_NO_EXIST", "GROUP_STATUS_FROZEN", "GROUP_STATUS_NEED_MODIFY", "GROUP_STATUS_NO_ACTIVE"], "default": "GROUP_STATUS_NORMAL", "title": "- GROUP_STATUS_NORMAL: 默认正常状态\n - GROUP_STATUS_DESTORY: 解散状态\n - GROUP_STATUS_NO_EXIST: 请求的家族id不存在\n - GROUP_STATUS_FROZEN: 因请求家族id的族长家族vip会员过期，导致该家族处于被冻结状态\n - GROUP_STATUS_NEED_MODIFY: 家族信息需要修改，导致家族处于冻结状态\n - GROUP_STATUS_NO_ACTIVE: 家族不活跃冻结，家族惩罚工具"}, "gameJoinReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "openId": {"type": "string", "title": "openId"}, "groupId": {"type": "string", "format": "uint64", "title": "家族id"}, "reason": {"type": "string", "title": "加入理由"}}}, "gameJoinRsp": {"type": "object", "properties": {"freeEnter": {"type": "integer", "format": "int64", "title": "如果grpc code == 0且freeEnter == 1, 表示自动加入成功, 否则等审核, 审核通过后通过JoinCallbackReq回调业务"}}, "title": "根据grpc code = 0 判断是否申请成功"}, "gameRecommandListReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "openId": {"type": "string", "title": "openId"}, "num": {"type": "string", "format": "uint64", "title": "拉取的数量"}}}, "gameRecommandListRsp": {"type": "object", "properties": {"groups": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGroup"}, "title": "推荐列表"}}}, "gameReportScoreEventReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "openId": {"type": "string", "title": "openId"}, "groupId": {"type": "string", "format": "uint64", "title": "家族id"}, "score": {"type": "string", "format": "uint64", "title": "积分"}, "scoreType": {"$ref": "#/definitions/gameGroupScoreEventType", "title": "积分类型"}, "consumeId": {"type": "string", "title": "幂等id"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "gameReportScoreEventRsp": {"type": "object", "title": "通过grpc code判断"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}