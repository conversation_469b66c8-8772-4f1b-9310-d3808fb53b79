// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/timer/storage.proto

package timer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TimerNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId     string  `protobuf:"bytes,1,opt,name=nodeId,proto3" json:"nodeId,omitempty"`
	Partitions []int64 `protobuf:"varint,2,rep,packed,name=partitions,proto3" json:"partitions,omitempty"`
}

func (x *TimerNode) Reset() {
	*x = TimerNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerNode) ProtoMessage() {}

func (x *TimerNode) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerNode.ProtoReflect.Descriptor instead.
func (*TimerNode) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{0}
}

func (x *TimerNode) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *TimerNode) GetPartitions() []int64 {
	if x != nil {
		return x.Partitions
	}
	return nil
}

type TimerMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PartitionNum   int64        `protobuf:"varint,1,opt,name=partitionNum,proto3" json:"partitionNum,omitempty"`
	Nodes          []*TimerNode `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`                    // 正常节点
	Backups        []*TimerNode `protobuf:"bytes,3,rep,name=backups,proto3" json:"backups,omitempty"`                // 备份节点
	BackupJoinTime int64        `protobuf:"varint,4,opt,name=backupJoinTime,proto3" json:"backupJoinTime,omitempty"` // 最后一次加入时间
}

func (x *TimerMeta) Reset() {
	*x = TimerMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerMeta) ProtoMessage() {}

func (x *TimerMeta) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerMeta.ProtoReflect.Descriptor instead.
func (*TimerMeta) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{1}
}

func (x *TimerMeta) GetPartitionNum() int64 {
	if x != nil {
		return x.PartitionNum
	}
	return 0
}

func (x *TimerMeta) GetNodes() []*TimerNode {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *TimerMeta) GetBackups() []*TimerNode {
	if x != nil {
		return x.Backups
	}
	return nil
}

func (x *TimerMeta) GetBackupJoinTime() int64 {
	if x != nil {
		return x.BackupJoinTime
	}
	return 0
}

type TimerNodeHeartbeat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HeartbeatTime int64 `protobuf:"varint,1,opt,name=heartbeatTime,proto3" json:"heartbeatTime,omitempty"` // 毫秒
}

func (x *TimerNodeHeartbeat) Reset() {
	*x = TimerNodeHeartbeat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerNodeHeartbeat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerNodeHeartbeat) ProtoMessage() {}

func (x *TimerNodeHeartbeat) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerNodeHeartbeat.ProtoReflect.Descriptor instead.
func (*TimerNodeHeartbeat) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{2}
}

func (x *TimerNodeHeartbeat) GetHeartbeatTime() int64 {
	if x != nil {
		return x.HeartbeatTime
	}
	return 0
}

type TimerTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Partition    int64        `protobuf:"varint,1,opt,name=partition,proto3" json:"partition,omitempty"` // 分区
	Status       TimerStatus  `protobuf:"varint,2,opt,name=status,proto3,enum=timer.TimerStatus" json:"status,omitempty"`
	BizData      []byte       `protobuf:"bytes,3,opt,name=bizData,proto3" json:"bizData,omitempty"`            // 业务数据
	Config       *TimerConfig `protobuf:"bytes,4,opt,name=config,proto3" json:"config,omitempty"`              // 触发配置
	CreatedTime  int64        `protobuf:"varint,5,opt,name=createdTime,proto3" json:"createdTime,omitempty"`   // 创建时间 毫秒
	ModifiedTime int64        `protobuf:"varint,6,opt,name=modifiedTime,proto3" json:"modifiedTime,omitempty"` // 修改时间 毫秒
	FiredTime    int64        `protobuf:"varint,7,opt,name=firedTime,proto3" json:"firedTime,omitempty"`       // 触发时间 毫秒
	SuccessTimes int64        `protobuf:"varint,8,opt,name=successTimes,proto3" json:"successTimes,omitempty"` // 成功调用次数
	FailureTimes int64        `protobuf:"varint,9,opt,name=failureTimes,proto3" json:"failureTimes,omitempty"` // 失败调用次数
}

func (x *TimerTask) Reset() {
	*x = TimerTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerTask) ProtoMessage() {}

func (x *TimerTask) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerTask.ProtoReflect.Descriptor instead.
func (*TimerTask) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{3}
}

func (x *TimerTask) GetPartition() int64 {
	if x != nil {
		return x.Partition
	}
	return 0
}

func (x *TimerTask) GetStatus() TimerStatus {
	if x != nil {
		return x.Status
	}
	return TimerStatus_Invalid
}

func (x *TimerTask) GetBizData() []byte {
	if x != nil {
		return x.BizData
	}
	return nil
}

func (x *TimerTask) GetConfig() *TimerConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *TimerTask) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *TimerTask) GetModifiedTime() int64 {
	if x != nil {
		return x.ModifiedTime
	}
	return 0
}

func (x *TimerTask) GetFiredTime() int64 {
	if x != nil {
		return x.FiredTime
	}
	return 0
}

func (x *TimerTask) GetSuccessTimes() int64 {
	if x != nil {
		return x.SuccessTimes
	}
	return 0
}

func (x *TimerTask) GetFailureTimes() int64 {
	if x != nil {
		return x.FailureTimes
	}
	return 0
}

// 定时器配置
// key: app_configs
type AppConfigs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apps map[string]*AppConfigs_App `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *AppConfigs) Reset() {
	*x = AppConfigs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppConfigs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppConfigs) ProtoMessage() {}

func (x *AppConfigs) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppConfigs.ProtoReflect.Descriptor instead.
func (*AppConfigs) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{4}
}

func (x *AppConfigs) GetApps() map[string]*AppConfigs_App {
	if x != nil {
		return x.Apps
	}
	return nil
}

type AppConfigs_Ckv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mod        int32  `protobuf:"varint,1,opt,name=mod,proto3" json:"mod,omitempty"`
	Cmd        int32  `protobuf:"varint,2,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Password   string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	ServerName string `protobuf:"bytes,4,opt,name=serverName,proto3" json:"serverName,omitempty"`
}

func (x *AppConfigs_Ckv) Reset() {
	*x = AppConfigs_Ckv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppConfigs_Ckv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppConfigs_Ckv) ProtoMessage() {}

func (x *AppConfigs_Ckv) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppConfigs_Ckv.ProtoReflect.Descriptor instead.
func (*AppConfigs_Ckv) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{4, 0}
}

func (x *AppConfigs_Ckv) GetMod() int32 {
	if x != nil {
		return x.Mod
	}
	return 0
}

func (x *AppConfigs_Ckv) GetCmd() int32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *AppConfigs_Ckv) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AppConfigs_Ckv) GetServerName() string {
	if x != nil {
		return x.ServerName
	}
	return ""
}

type AppConfigs_App struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ckv             *AppConfigs_Ckv `protobuf:"bytes,1,opt,name=ckv,proto3" json:"ckv,omitempty"`
	Partition       int64           `protobuf:"varint,2,opt,name=partition,proto3" json:"partition,omitempty"`
	MaxDelay        int64           `protobuf:"varint,3,opt,name=maxDelay,proto3" json:"maxDelay,omitempty"` // ms
	Address         string          `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	CheckInterval   int64           `protobuf:"varint,5,opt,name=checkInterval,proto3" json:"checkInterval,omitempty"`     // ms
	MonitorInterval int64           `protobuf:"varint,6,opt,name=monitorInterval,proto3" json:"monitorInterval,omitempty"` // ms
}

func (x *AppConfigs_App) Reset() {
	*x = AppConfigs_App{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_storage_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppConfigs_App) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppConfigs_App) ProtoMessage() {}

func (x *AppConfigs_App) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_storage_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppConfigs_App.ProtoReflect.Descriptor instead.
func (*AppConfigs_App) Descriptor() ([]byte, []int) {
	return file_pb_timer_storage_proto_rawDescGZIP(), []int{4, 1}
}

func (x *AppConfigs_App) GetCkv() *AppConfigs_Ckv {
	if x != nil {
		return x.Ckv
	}
	return nil
}

func (x *AppConfigs_App) GetPartition() int64 {
	if x != nil {
		return x.Partition
	}
	return 0
}

func (x *AppConfigs_App) GetMaxDelay() int64 {
	if x != nil {
		return x.MaxDelay
	}
	return 0
}

func (x *AppConfigs_App) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AppConfigs_App) GetCheckInterval() int64 {
	if x != nil {
		return x.CheckInterval
	}
	return 0
}

func (x *AppConfigs_App) GetMonitorInterval() int64 {
	if x != nil {
		return x.MonitorInterval
	}
	return 0
}

var File_pb_timer_storage_proto protoreflect.FileDescriptor

var file_pb_timer_storage_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x1a,
	0x15, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x43, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x4e,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xab, 0x01, 0x0a, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x4a, 0x6f, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x62, 0x61, 0x63, 0x6b, 0x75,
	0x70, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x12, 0x54, 0x69, 0x6d,
	0x65, 0x72, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc7, 0x02, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x62, 0x69, 0x7a, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x62, 0x69, 0x7a, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2a, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x69, 0x72,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69,
	0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22,
	0xc9, 0x03, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x2f,
	0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e,
	0x41, 0x70, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x61, 0x70, 0x70, 0x73, 0x1a,
	0x65, 0x0a, 0x03, 0x43, 0x6b, 0x76, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x6f, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x6f, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0xd2, 0x01, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x27,
	0x0a, 0x03, 0x63, 0x6b, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x43,
	0x6b, 0x76, 0x52, 0x03, 0x63, 0x6b, 0x76, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x44, 0x65, 0x6c, 0x61,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x44, 0x65, 0x6c, 0x61,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0x4e, 0x0a, 0x09, 0x41,
	0x70, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x72, 0x2e, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2e, 0x41, 0x70, 0x70,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x3d, 0x5a, 0x3b, 0x74,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pb_timer_storage_proto_rawDescOnce sync.Once
	file_pb_timer_storage_proto_rawDescData = file_pb_timer_storage_proto_rawDesc
)

func file_pb_timer_storage_proto_rawDescGZIP() []byte {
	file_pb_timer_storage_proto_rawDescOnce.Do(func() {
		file_pb_timer_storage_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_timer_storage_proto_rawDescData)
	})
	return file_pb_timer_storage_proto_rawDescData
}

var file_pb_timer_storage_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_timer_storage_proto_goTypes = []interface{}{
	(*TimerNode)(nil),          // 0: timer.TimerNode
	(*TimerMeta)(nil),          // 1: timer.TimerMeta
	(*TimerNodeHeartbeat)(nil), // 2: timer.TimerNodeHeartbeat
	(*TimerTask)(nil),          // 3: timer.TimerTask
	(*AppConfigs)(nil),         // 4: timer.AppConfigs
	(*AppConfigs_Ckv)(nil),     // 5: timer.AppConfigs.Ckv
	(*AppConfigs_App)(nil),     // 6: timer.AppConfigs.App
	nil,                        // 7: timer.AppConfigs.AppsEntry
	(TimerStatus)(0),           // 8: timer.TimerStatus
	(*TimerConfig)(nil),        // 9: timer.TimerConfig
}
var file_pb_timer_storage_proto_depIdxs = []int32{
	0, // 0: timer.TimerMeta.nodes:type_name -> timer.TimerNode
	0, // 1: timer.TimerMeta.backups:type_name -> timer.TimerNode
	8, // 2: timer.TimerTask.status:type_name -> timer.TimerStatus
	9, // 3: timer.TimerTask.config:type_name -> timer.TimerConfig
	7, // 4: timer.AppConfigs.apps:type_name -> timer.AppConfigs.AppsEntry
	5, // 5: timer.AppConfigs.App.ckv:type_name -> timer.AppConfigs.Ckv
	6, // 6: timer.AppConfigs.AppsEntry.value:type_name -> timer.AppConfigs.App
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_pb_timer_storage_proto_init() }
func file_pb_timer_storage_proto_init() {
	if File_pb_timer_storage_proto != nil {
		return
	}
	file_pb_timer_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_timer_storage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_storage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_storage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerNodeHeartbeat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_storage_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_storage_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppConfigs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_storage_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppConfigs_Ckv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_storage_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppConfigs_App); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_timer_storage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_timer_storage_proto_goTypes,
		DependencyIndexes: file_pb_timer_storage_proto_depIdxs,
		MessageInfos:      file_pb_timer_storage_proto_msgTypes,
	}.Build()
	File_pb_timer_storage_proto = out.File
	file_pb_timer_storage_proto_rawDesc = nil
	file_pb_timer_storage_proto_goTypes = nil
	file_pb_timer_storage_proto_depIdxs = nil
}
