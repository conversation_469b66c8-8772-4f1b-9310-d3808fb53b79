// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/timer/timer.proto

package timer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Timer_Query_FullMethodName    = "/timer.Timer/Query"
	Timer_Register_FullMethodName = "/timer.Timer/Register"
	Timer_Cancel_FullMethodName   = "/timer.Timer/Cancel"
)

// TimerClient is the client API for Timer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TimerClient interface {
	// 查询计时器
	Query(ctx context.Context, in *TimerQueryRequest, opts ...grpc.CallOption) (*TimerQueryResponse, error)
	// 注册定时器
	Register(ctx context.Context, in *TimerRegisterRequest, opts ...grpc.CallOption) (*TimerRegisterResponse, error)
	// 取消定时器
	Cancel(ctx context.Context, in *TimerCancelRequest, opts ...grpc.CallOption) (*TimerCancelResponse, error)
}

type timerClient struct {
	cc grpc.ClientConnInterface
}

func NewTimerClient(cc grpc.ClientConnInterface) TimerClient {
	return &timerClient{cc}
}

func (c *timerClient) Query(ctx context.Context, in *TimerQueryRequest, opts ...grpc.CallOption) (*TimerQueryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerQueryResponse)
	err := c.cc.Invoke(ctx, Timer_Query_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timerClient) Register(ctx context.Context, in *TimerRegisterRequest, opts ...grpc.CallOption) (*TimerRegisterResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerRegisterResponse)
	err := c.cc.Invoke(ctx, Timer_Register_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timerClient) Cancel(ctx context.Context, in *TimerCancelRequest, opts ...grpc.CallOption) (*TimerCancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerCancelResponse)
	err := c.cc.Invoke(ctx, Timer_Cancel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TimerServer is the server API for Timer service.
// All implementations should embed UnimplementedTimerServer
// for forward compatibility
type TimerServer interface {
	// 查询计时器
	Query(context.Context, *TimerQueryRequest) (*TimerQueryResponse, error)
	// 注册定时器
	Register(context.Context, *TimerRegisterRequest) (*TimerRegisterResponse, error)
	// 取消定时器
	Cancel(context.Context, *TimerCancelRequest) (*TimerCancelResponse, error)
}

// UnimplementedTimerServer should be embedded to have forward compatible implementations.
type UnimplementedTimerServer struct {
}

func (UnimplementedTimerServer) Query(context.Context, *TimerQueryRequest) (*TimerQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (UnimplementedTimerServer) Register(context.Context, *TimerRegisterRequest) (*TimerRegisterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Register not implemented")
}
func (UnimplementedTimerServer) Cancel(context.Context, *TimerCancelRequest) (*TimerCancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Cancel not implemented")
}

// UnsafeTimerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TimerServer will
// result in compilation errors.
type UnsafeTimerServer interface {
	mustEmbedUnimplementedTimerServer()
}

func RegisterTimerServer(s grpc.ServiceRegistrar, srv TimerServer) {
	s.RegisterService(&Timer_ServiceDesc, srv)
}

func _Timer_Query_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimerServer).Query(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Timer_Query_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimerServer).Query(ctx, req.(*TimerQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Timer_Register_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerRegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimerServer).Register(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Timer_Register_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimerServer).Register(ctx, req.(*TimerRegisterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Timer_Cancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerCancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimerServer).Cancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Timer_Cancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimerServer).Cancel(ctx, req.(*TimerCancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Timer_ServiceDesc is the grpc.ServiceDesc for Timer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Timer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "timer.Timer",
	HandlerType: (*TimerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Query",
			Handler:    _Timer_Query_Handler,
		},
		{
			MethodName: "Register",
			Handler:    _Timer_Register_Handler,
		},
		{
			MethodName: "Cancel",
			Handler:    _Timer_Cancel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/timer/timer.proto",
}
