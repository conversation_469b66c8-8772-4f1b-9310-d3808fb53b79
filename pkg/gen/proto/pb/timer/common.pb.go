// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/timer/common.proto

package timer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TimerTriggerType int32

const (
	TimerTriggerType_Unknow TimerTriggerType = 0 // 未知
	TimerTriggerType_Once   TimerTriggerType = 1 // 一次性
	TimerTriggerType_Period TimerTriggerType = 2 // 周期
)

// Enum value maps for TimerTriggerType.
var (
	TimerTriggerType_name = map[int32]string{
		0: "Unknow",
		1: "Once",
		2: "Period",
	}
	TimerTriggerType_value = map[string]int32{
		"Unknow": 0,
		"Once":   1,
		"Period": 2,
	}
)

func (x TimerTriggerType) Enum() *TimerTriggerType {
	p := new(TimerTriggerType)
	*p = x
	return p
}

func (x TimerTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimerTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_timer_common_proto_enumTypes[0].Descriptor()
}

func (TimerTriggerType) Type() protoreflect.EnumType {
	return &file_pb_timer_common_proto_enumTypes[0]
}

func (x TimerTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimerTriggerType.Descriptor instead.
func (TimerTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{0}
}

type TimerStatus int32

const (
	TimerStatus_Invalid   TimerStatus = 0 // 无效
	TimerStatus_Pending   TimerStatus = 1
	TimerStatus_Normal    TimerStatus = 2 // 正常
	TimerStatus_Completed TimerStatus = 3 // 已完成
	TimerStatus_Canceled  TimerStatus = 4 // 已取消
	TimerStatus_Abandoned TimerStatus = 5
)

// Enum value maps for TimerStatus.
var (
	TimerStatus_name = map[int32]string{
		0: "Invalid",
		1: "Pending",
		2: "Normal",
		3: "Completed",
		4: "Canceled",
		5: "Abandoned",
	}
	TimerStatus_value = map[string]int32{
		"Invalid":   0,
		"Pending":   1,
		"Normal":    2,
		"Completed": 3,
		"Canceled":  4,
		"Abandoned": 5,
	}
)

func (x TimerStatus) Enum() *TimerStatus {
	p := new(TimerStatus)
	*p = x
	return p
}

func (x TimerStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimerStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_timer_common_proto_enumTypes[1].Descriptor()
}

func (TimerStatus) Type() protoreflect.EnumType {
	return &file_pb_timer_common_proto_enumTypes[1]
}

func (x TimerStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimerStatus.Descriptor instead.
func (TimerStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{1}
}

type TimerTriggerOnce struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FireTime int64 `protobuf:"varint,1,opt,name=fireTime,proto3" json:"fireTime,omitempty"` // 触发时间戳 毫秒
}

func (x *TimerTriggerOnce) Reset() {
	*x = TimerTriggerOnce{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerTriggerOnce) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerTriggerOnce) ProtoMessage() {}

func (x *TimerTriggerOnce) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerTriggerOnce.ProtoReflect.Descriptor instead.
func (*TimerTriggerOnce) Descriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{0}
}

func (x *TimerTriggerOnce) GetFireTime() int64 {
	if x != nil {
		return x.FireTime
	}
	return 0
}

type TimerTriggerPeriod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeginTime int64 `protobuf:"varint,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"` // 开始时间戳 毫秒
	Interval  int64 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`   // 间隔 毫秒
}

func (x *TimerTriggerPeriod) Reset() {
	*x = TimerTriggerPeriod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerTriggerPeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerTriggerPeriod) ProtoMessage() {}

func (x *TimerTriggerPeriod) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerTriggerPeriod.ProtoReflect.Descriptor instead.
func (*TimerTriggerPeriod) Descriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{1}
}

func (x *TimerTriggerPeriod) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *TimerTriggerPeriod) GetInterval() int64 {
	if x != nil {
		return x.Interval
	}
	return 0
}

type TimerTrigger struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   TimerTriggerType    `protobuf:"varint,1,opt,name=type,proto3,enum=timer.TimerTriggerType" json:"type,omitempty"`
	Once   *TimerTriggerOnce   `protobuf:"bytes,2,opt,name=once,proto3" json:"once,omitempty"`     // 一次性触发
	Period *TimerTriggerPeriod `protobuf:"bytes,3,opt,name=period,proto3" json:"period,omitempty"` // 周期触发
}

func (x *TimerTrigger) Reset() {
	*x = TimerTrigger{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerTrigger) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerTrigger) ProtoMessage() {}

func (x *TimerTrigger) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerTrigger.ProtoReflect.Descriptor instead.
func (*TimerTrigger) Descriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{2}
}

func (x *TimerTrigger) GetType() TimerTriggerType {
	if x != nil {
		return x.Type
	}
	return TimerTriggerType_Unknow
}

func (x *TimerTrigger) GetOnce() *TimerTriggerOnce {
	if x != nil {
		return x.Once
	}
	return nil
}

func (x *TimerTrigger) GetPeriod() *TimerTriggerPeriod {
	if x != nil {
		return x.Period
	}
	return nil
}

type TimerRetry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxTimes int64 `protobuf:"varint,1,opt,name=maxTimes,proto3" json:"maxTimes,omitempty"` // 最大重试次数 默认 0 不重试
	Interval int64 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"` // 重试间隔 毫秒 默认 1000ms
}

func (x *TimerRetry) Reset() {
	*x = TimerRetry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerRetry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerRetry) ProtoMessage() {}

func (x *TimerRetry) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerRetry.ProtoReflect.Descriptor instead.
func (*TimerRetry) Descriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{3}
}

func (x *TimerRetry) GetMaxTimes() int64 {
	if x != nil {
		return x.MaxTimes
	}
	return 0
}

func (x *TimerRetry) GetInterval() int64 {
	if x != nil {
		return x.Interval
	}
	return 0
}

type TimerCallback struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=serviceName,proto3" json:"serviceName,omitempty"` // 服务名
	Timeout     int64  `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"`        // 超时时间 毫秒 范围 (1, 5000] 默认 1000ms 周期类型的超时时间如果比触发间隔时间长 超时会导致中间的周期不触发
}

func (x *TimerCallback) Reset() {
	*x = TimerCallback{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerCallback) ProtoMessage() {}

func (x *TimerCallback) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerCallback.ProtoReflect.Descriptor instead.
func (*TimerCallback) Descriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{4}
}

func (x *TimerCallback) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *TimerCallback) GetTimeout() int64 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

type TimerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trigger  *TimerTrigger  `protobuf:"bytes,1,opt,name=trigger,proto3" json:"trigger,omitempty"`   // 触发配置
	Callback *TimerCallback `protobuf:"bytes,2,opt,name=callback,proto3" json:"callback,omitempty"` // 回调配置
	Retry    *TimerRetry    `protobuf:"bytes,3,opt,name=retry,proto3" json:"retry,omitempty"`       // 重试配置
}

func (x *TimerConfig) Reset() {
	*x = TimerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_timer_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerConfig) ProtoMessage() {}

func (x *TimerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_timer_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerConfig.ProtoReflect.Descriptor instead.
func (*TimerConfig) Descriptor() ([]byte, []int) {
	return file_pb_timer_common_proto_rawDescGZIP(), []int{5}
}

func (x *TimerConfig) GetTrigger() *TimerTrigger {
	if x != nil {
		return x.Trigger
	}
	return nil
}

func (x *TimerConfig) GetCallback() *TimerCallback {
	if x != nil {
		return x.Callback
	}
	return nil
}

func (x *TimerConfig) GetRetry() *TimerRetry {
	if x != nil {
		return x.Retry
	}
	return nil
}

var File_pb_timer_common_proto protoreflect.FileDescriptor

var file_pb_timer_common_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x22, 0x2e,
	0x0a, 0x10, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f, 0x6e,
	0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x4e,
	0x0a, 0x12, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22, 0x9b,
	0x01, 0x0a, 0x0c, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12,
	0x2b, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e,
	0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x04,
	0x6f, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4f,
	0x6e, 0x63, 0x65, 0x52, 0x04, 0x6f, 0x6e, 0x63, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x52, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0x44, 0x0a, 0x0a,
	0x54, 0x69, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x61,
	0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x61,
	0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x22, 0x4b, 0x0a, 0x0d, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22,
	0x97, 0x01, 0x0a, 0x0b, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x2d, 0x0a, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x54, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x07, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x30,
	0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x12, 0x27, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x2a, 0x34, 0x0a, 0x10, 0x54, 0x69, 0x6d,
	0x65, 0x72, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a,
	0x06, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4f, 0x6e, 0x63,
	0x65, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x10, 0x02, 0x2a,
	0x5f, 0x0a, 0x0b, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b,
	0x0a, 0x07, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10,
	0x04, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x62, 0x61, 0x6e, 0x64, 0x6f, 0x6e, 0x65, 0x64, 0x10, 0x05,
	0x42, 0x3d, 0x5a, 0x3b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_timer_common_proto_rawDescOnce sync.Once
	file_pb_timer_common_proto_rawDescData = file_pb_timer_common_proto_rawDesc
)

func file_pb_timer_common_proto_rawDescGZIP() []byte {
	file_pb_timer_common_proto_rawDescOnce.Do(func() {
		file_pb_timer_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_timer_common_proto_rawDescData)
	})
	return file_pb_timer_common_proto_rawDescData
}

var file_pb_timer_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_timer_common_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_timer_common_proto_goTypes = []interface{}{
	(TimerTriggerType)(0),      // 0: timer.TimerTriggerType
	(TimerStatus)(0),           // 1: timer.TimerStatus
	(*TimerTriggerOnce)(nil),   // 2: timer.TimerTriggerOnce
	(*TimerTriggerPeriod)(nil), // 3: timer.TimerTriggerPeriod
	(*TimerTrigger)(nil),       // 4: timer.TimerTrigger
	(*TimerRetry)(nil),         // 5: timer.TimerRetry
	(*TimerCallback)(nil),      // 6: timer.TimerCallback
	(*TimerConfig)(nil),        // 7: timer.TimerConfig
}
var file_pb_timer_common_proto_depIdxs = []int32{
	0, // 0: timer.TimerTrigger.type:type_name -> timer.TimerTriggerType
	2, // 1: timer.TimerTrigger.once:type_name -> timer.TimerTriggerOnce
	3, // 2: timer.TimerTrigger.period:type_name -> timer.TimerTriggerPeriod
	4, // 3: timer.TimerConfig.trigger:type_name -> timer.TimerTrigger
	6, // 4: timer.TimerConfig.callback:type_name -> timer.TimerCallback
	5, // 5: timer.TimerConfig.retry:type_name -> timer.TimerRetry
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_pb_timer_common_proto_init() }
func file_pb_timer_common_proto_init() {
	if File_pb_timer_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_timer_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerTriggerOnce); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerTriggerPeriod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerTrigger); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerRetry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerCallback); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_timer_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_timer_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_timer_common_proto_goTypes,
		DependencyIndexes: file_pb_timer_common_proto_depIdxs,
		EnumInfos:         file_pb_timer_common_proto_enumTypes,
		MessageInfos:      file_pb_timer_common_proto_msgTypes,
	}.Build()
	File_pb_timer_common_proto = out.File
	file_pb_timer_common_proto_rawDesc = nil
	file_pb_timer_common_proto_goTypes = nil
	file_pb_timer_common_proto_depIdxs = nil
}
