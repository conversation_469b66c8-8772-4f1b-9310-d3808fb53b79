// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/ringbuf/ringbuf.proto

package ringbuf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StringBuffer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UPos uint32 `protobuf:"varint,1,opt,name=uPos,proto3" json:"uPos,omitempty"` // 将所有的consumeid看成一个环,uPos为当前的位置
	Keys []byte `protobuf:"bytes,2,opt,name=Keys,proto3" json:"Keys,omitempty"`  // 存储的md5(Keys)列表
}

func (x *StringBuffer) Reset() {
	*x = StringBuffer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_ringbuf_ringbuf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringBuffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringBuffer) ProtoMessage() {}

func (x *StringBuffer) ProtoReflect() protoreflect.Message {
	mi := &file_pb_ringbuf_ringbuf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringBuffer.ProtoReflect.Descriptor instead.
func (*StringBuffer) Descriptor() ([]byte, []int) {
	return file_pb_ringbuf_ringbuf_proto_rawDescGZIP(), []int{0}
}

func (x *StringBuffer) GetUPos() uint32 {
	if x != nil {
		return x.UPos
	}
	return 0
}

func (x *StringBuffer) GetKeys() []byte {
	if x != nil {
		return x.Keys
	}
	return nil
}

var File_pb_ringbuf_ringbuf_proto protoreflect.FileDescriptor

var file_pb_ringbuf_ringbuf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x72, 0x69, 0x6e, 0x67, 0x62, 0x75, 0x66, 0x2f, 0x72, 0x69, 0x6e,
	0x67, 0x62, 0x75, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x0c, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x50,
	0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x75, 0x50, 0x6f, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x4b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x4b, 0x65,
	0x79, 0x73, 0x42, 0x3f, 0x5a, 0x3d, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x72, 0x69, 0x6e, 0x67,
	0x62, 0x75, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_ringbuf_ringbuf_proto_rawDescOnce sync.Once
	file_pb_ringbuf_ringbuf_proto_rawDescData = file_pb_ringbuf_ringbuf_proto_rawDesc
)

func file_pb_ringbuf_ringbuf_proto_rawDescGZIP() []byte {
	file_pb_ringbuf_ringbuf_proto_rawDescOnce.Do(func() {
		file_pb_ringbuf_ringbuf_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_ringbuf_ringbuf_proto_rawDescData)
	})
	return file_pb_ringbuf_ringbuf_proto_rawDescData
}

var file_pb_ringbuf_ringbuf_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pb_ringbuf_ringbuf_proto_goTypes = []interface{}{
	(*StringBuffer)(nil), // 0: component.game.StringBuffer
}
var file_pb_ringbuf_ringbuf_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_ringbuf_ringbuf_proto_init() }
func file_pb_ringbuf_ringbuf_proto_init() {
	if File_pb_ringbuf_ringbuf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_ringbuf_ringbuf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringBuffer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_ringbuf_ringbuf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_ringbuf_ringbuf_proto_goTypes,
		DependencyIndexes: file_pb_ringbuf_ringbuf_proto_depIdxs,
		MessageInfos:      file_pb_ringbuf_ringbuf_proto_msgTypes,
	}.Build()
	File_pb_ringbuf_ringbuf_proto = out.File
	file_pb_ringbuf_ringbuf_proto_rawDesc = nil
	file_pb_ringbuf_ringbuf_proto_goTypes = nil
	file_pb_ringbuf_ringbuf_proto_depIdxs = nil
}
