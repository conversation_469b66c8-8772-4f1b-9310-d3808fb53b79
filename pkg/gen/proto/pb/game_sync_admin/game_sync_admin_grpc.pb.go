// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_sync_admin/game_sync_admin.proto

package game_sync_admin

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GameSyncAdmin_ExchangeCurrencyCopy_FullMethodName   = "/component.game.game_sync_admin/ExchangeCurrencyCopy"
	GameSyncAdmin_ExchangeCurrencyList_FullMethodName   = "/component.game.game_sync_admin/ExchangeCurrencyList"
	GameSyncAdmin_ExchangeCurrencyUpdate_FullMethodName = "/component.game.game_sync_admin/ExchangeCurrencyUpdate"
	GameSyncAdmin_GameCopy_FullMethodName               = "/component.game.game_sync_admin/GameCopy"
	GameSyncAdmin_GameCurrencyCopy_FullMethodName       = "/component.game.game_sync_admin/GameCurrencyCopy"
	GameSyncAdmin_GameCurrencyList_FullMethodName       = "/component.game.game_sync_admin/GameCurrencyList"
	GameSyncAdmin_GameCurrencyUpdate_FullMethodName     = "/component.game.game_sync_admin/GameCurrencyUpdate"
	GameSyncAdmin_GameList_FullMethodName               = "/component.game.game_sync_admin/GameList"
	GameSyncAdmin_GameSync_FullMethodName               = "/component.game.game_sync_admin/GameSync"
	GameSyncAdmin_GameUpdate_FullMethodName             = "/component.game.game_sync_admin/GameUpdate"
	GameSyncAdmin_PropDetail_FullMethodName             = "/component.game.game_sync_admin/PropDetail"
	GameSyncAdmin_GiftDetail_FullMethodName             = "/component.game.game_sync_admin/GiftDetail"
	GameSyncAdmin_GameAssetCopy_FullMethodName          = "/component.game.game_sync_admin/GameAssetCopy"
	GameSyncAdmin_GameAssetList_FullMethodName          = "/component.game.game_sync_admin/GameAssetList"
	GameSyncAdmin_GameAssetUpdate_FullMethodName        = "/component.game.game_sync_admin/GameAssetUpdate"
	GameSyncAdmin_GamePackageCopy_FullMethodName        = "/component.game.game_sync_admin/GamePackageCopy"
	GameSyncAdmin_GamePackageList_FullMethodName        = "/component.game.game_sync_admin/GamePackageList"
	GameSyncAdmin_GamePackageUpdate_FullMethodName      = "/component.game.game_sync_admin/GamePackageUpdate"
)

// GameSyncAdminClient is the client API for GameSyncAdmin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GameSyncAdminClient interface {
	// Copy到外网--兑换货币
	ExchangeCurrencyCopy(ctx context.Context, in *ExchangeCurrencyCopyReq, opts ...grpc.CallOption) (*ExchangeCurrencyCopyRsp, error)
	// 兑换币列表
	ExchangeCurrencyList(ctx context.Context, in *ExchangeCurrencyListReq, opts ...grpc.CallOption) (*ExchangeCurrencyListRsp, error)
	// 兑换币配置更新
	ExchangeCurrencyUpdate(ctx context.Context, in *ExchangeCurrencyUpdateReq, opts ...grpc.CallOption) (*ExchangeCurrencyUpdateRsp, error)
	// Copy到外网--游戏
	GameCopy(ctx context.Context, in *GameCopyReq, opts ...grpc.CallOption) (*GameCopyRsp, error)
	// Copy到外网--游戏货币
	GameCurrencyCopy(ctx context.Context, in *GameCurrencyCopyReq, opts ...grpc.CallOption) (*GameCurrencyCopyRsp, error)
	// 游戏货币列表
	GameCurrencyList(ctx context.Context, in *GameCurrencyListReq, opts ...grpc.CallOption) (*GameCurrencyListRsp, error)
	// 游戏货币更新
	GameCurrencyUpdate(ctx context.Context, in *GameCurrencyUpdateReq, opts ...grpc.CallOption) (*GameCurrencyUpdateRsp, error)
	// 游戏列表
	GameList(ctx context.Context, in *GameListReq, opts ...grpc.CallOption) (*GameListRsp, error)
	// 游戏同步
	GameSync(ctx context.Context, in *GameSyncReq, opts ...grpc.CallOption) (*GameSyncRsp, error)
	// 游戏更新
	GameUpdate(ctx context.Context, in *GameUpdateReq, opts ...grpc.CallOption) (*GameUpdateRsp, error)
	// 道具信息查询
	PropDetail(ctx context.Context, in *PropDetailReq, opts ...grpc.CallOption) (*PropDetailRsp, error)
	// 道具信息查询
	GiftDetail(ctx context.Context, in *GiftDetailReq, opts ...grpc.CallOption) (*GiftDetailRsp, error)
	// Copy到外网--游戏资产
	GameAssetCopy(ctx context.Context, in *GameAssetCopyReq, opts ...grpc.CallOption) (*GameAssetCopyRsp, error)
	// 游戏资产列表
	GameAssetList(ctx context.Context, in *GameAssetListReq, opts ...grpc.CallOption) (*GameAssetListRsp, error)
	// 游戏资产更新
	GameAssetUpdate(ctx context.Context, in *GameAssetUpdateReq, opts ...grpc.CallOption) (*GameAssetUpdateRsp, error)
	// Copy到外网--游戏礼包
	GamePackageCopy(ctx context.Context, in *GamePackageCopyReq, opts ...grpc.CallOption) (*GamePackageCopyRsp, error)
	// 游戏礼包列表
	GamePackageList(ctx context.Context, in *GamePackageListReq, opts ...grpc.CallOption) (*GamePackageListRsp, error)
	// 游戏礼包更新
	GamePackageUpdate(ctx context.Context, in *GamePackageUpdateReq, opts ...grpc.CallOption) (*GamePackageUpdateRsp, error)
}

type gameSyncAdminClient struct {
	cc grpc.ClientConnInterface
}

func NewGameSyncAdminClient(cc grpc.ClientConnInterface) GameSyncAdminClient {
	return &gameSyncAdminClient{cc}
}

func (c *gameSyncAdminClient) ExchangeCurrencyCopy(ctx context.Context, in *ExchangeCurrencyCopyReq, opts ...grpc.CallOption) (*ExchangeCurrencyCopyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExchangeCurrencyCopyRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_ExchangeCurrencyCopy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) ExchangeCurrencyList(ctx context.Context, in *ExchangeCurrencyListReq, opts ...grpc.CallOption) (*ExchangeCurrencyListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExchangeCurrencyListRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_ExchangeCurrencyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) ExchangeCurrencyUpdate(ctx context.Context, in *ExchangeCurrencyUpdateReq, opts ...grpc.CallOption) (*ExchangeCurrencyUpdateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExchangeCurrencyUpdateRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_ExchangeCurrencyUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameCopy(ctx context.Context, in *GameCopyReq, opts ...grpc.CallOption) (*GameCopyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameCopyRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameCopy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameCurrencyCopy(ctx context.Context, in *GameCurrencyCopyReq, opts ...grpc.CallOption) (*GameCurrencyCopyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameCurrencyCopyRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameCurrencyCopy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameCurrencyList(ctx context.Context, in *GameCurrencyListReq, opts ...grpc.CallOption) (*GameCurrencyListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameCurrencyListRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameCurrencyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameCurrencyUpdate(ctx context.Context, in *GameCurrencyUpdateReq, opts ...grpc.CallOption) (*GameCurrencyUpdateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameCurrencyUpdateRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameCurrencyUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameList(ctx context.Context, in *GameListReq, opts ...grpc.CallOption) (*GameListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameListRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameSync(ctx context.Context, in *GameSyncReq, opts ...grpc.CallOption) (*GameSyncRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameSyncRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameSync_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameUpdate(ctx context.Context, in *GameUpdateReq, opts ...grpc.CallOption) (*GameUpdateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameUpdateRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) PropDetail(ctx context.Context, in *PropDetailReq, opts ...grpc.CallOption) (*PropDetailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PropDetailRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_PropDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GiftDetail(ctx context.Context, in *GiftDetailReq, opts ...grpc.CallOption) (*GiftDetailRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GiftDetailRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GiftDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameAssetCopy(ctx context.Context, in *GameAssetCopyReq, opts ...grpc.CallOption) (*GameAssetCopyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameAssetCopyRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameAssetCopy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameAssetList(ctx context.Context, in *GameAssetListReq, opts ...grpc.CallOption) (*GameAssetListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameAssetListRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameAssetList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GameAssetUpdate(ctx context.Context, in *GameAssetUpdateReq, opts ...grpc.CallOption) (*GameAssetUpdateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GameAssetUpdateRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GameAssetUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GamePackageCopy(ctx context.Context, in *GamePackageCopyReq, opts ...grpc.CallOption) (*GamePackageCopyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GamePackageCopyRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GamePackageCopy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GamePackageList(ctx context.Context, in *GamePackageListReq, opts ...grpc.CallOption) (*GamePackageListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GamePackageListRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GamePackageList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gameSyncAdminClient) GamePackageUpdate(ctx context.Context, in *GamePackageUpdateReq, opts ...grpc.CallOption) (*GamePackageUpdateRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GamePackageUpdateRsp)
	err := c.cc.Invoke(ctx, GameSyncAdmin_GamePackageUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GameSyncAdminServer is the server API for GameSyncAdmin service.
// All implementations should embed UnimplementedGameSyncAdminServer
// for forward compatibility
type GameSyncAdminServer interface {
	// Copy到外网--兑换货币
	ExchangeCurrencyCopy(context.Context, *ExchangeCurrencyCopyReq) (*ExchangeCurrencyCopyRsp, error)
	// 兑换币列表
	ExchangeCurrencyList(context.Context, *ExchangeCurrencyListReq) (*ExchangeCurrencyListRsp, error)
	// 兑换币配置更新
	ExchangeCurrencyUpdate(context.Context, *ExchangeCurrencyUpdateReq) (*ExchangeCurrencyUpdateRsp, error)
	// Copy到外网--游戏
	GameCopy(context.Context, *GameCopyReq) (*GameCopyRsp, error)
	// Copy到外网--游戏货币
	GameCurrencyCopy(context.Context, *GameCurrencyCopyReq) (*GameCurrencyCopyRsp, error)
	// 游戏货币列表
	GameCurrencyList(context.Context, *GameCurrencyListReq) (*GameCurrencyListRsp, error)
	// 游戏货币更新
	GameCurrencyUpdate(context.Context, *GameCurrencyUpdateReq) (*GameCurrencyUpdateRsp, error)
	// 游戏列表
	GameList(context.Context, *GameListReq) (*GameListRsp, error)
	// 游戏同步
	GameSync(context.Context, *GameSyncReq) (*GameSyncRsp, error)
	// 游戏更新
	GameUpdate(context.Context, *GameUpdateReq) (*GameUpdateRsp, error)
	// 道具信息查询
	PropDetail(context.Context, *PropDetailReq) (*PropDetailRsp, error)
	// 道具信息查询
	GiftDetail(context.Context, *GiftDetailReq) (*GiftDetailRsp, error)
	// Copy到外网--游戏资产
	GameAssetCopy(context.Context, *GameAssetCopyReq) (*GameAssetCopyRsp, error)
	// 游戏资产列表
	GameAssetList(context.Context, *GameAssetListReq) (*GameAssetListRsp, error)
	// 游戏资产更新
	GameAssetUpdate(context.Context, *GameAssetUpdateReq) (*GameAssetUpdateRsp, error)
	// Copy到外网--游戏礼包
	GamePackageCopy(context.Context, *GamePackageCopyReq) (*GamePackageCopyRsp, error)
	// 游戏礼包列表
	GamePackageList(context.Context, *GamePackageListReq) (*GamePackageListRsp, error)
	// 游戏礼包更新
	GamePackageUpdate(context.Context, *GamePackageUpdateReq) (*GamePackageUpdateRsp, error)
}

// UnimplementedGameSyncAdminServer should be embedded to have forward compatible implementations.
type UnimplementedGameSyncAdminServer struct {
}

func (UnimplementedGameSyncAdminServer) ExchangeCurrencyCopy(context.Context, *ExchangeCurrencyCopyReq) (*ExchangeCurrencyCopyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExchangeCurrencyCopy not implemented")
}
func (UnimplementedGameSyncAdminServer) ExchangeCurrencyList(context.Context, *ExchangeCurrencyListReq) (*ExchangeCurrencyListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExchangeCurrencyList not implemented")
}
func (UnimplementedGameSyncAdminServer) ExchangeCurrencyUpdate(context.Context, *ExchangeCurrencyUpdateReq) (*ExchangeCurrencyUpdateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExchangeCurrencyUpdate not implemented")
}
func (UnimplementedGameSyncAdminServer) GameCopy(context.Context, *GameCopyReq) (*GameCopyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameCopy not implemented")
}
func (UnimplementedGameSyncAdminServer) GameCurrencyCopy(context.Context, *GameCurrencyCopyReq) (*GameCurrencyCopyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameCurrencyCopy not implemented")
}
func (UnimplementedGameSyncAdminServer) GameCurrencyList(context.Context, *GameCurrencyListReq) (*GameCurrencyListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameCurrencyList not implemented")
}
func (UnimplementedGameSyncAdminServer) GameCurrencyUpdate(context.Context, *GameCurrencyUpdateReq) (*GameCurrencyUpdateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameCurrencyUpdate not implemented")
}
func (UnimplementedGameSyncAdminServer) GameList(context.Context, *GameListReq) (*GameListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameList not implemented")
}
func (UnimplementedGameSyncAdminServer) GameSync(context.Context, *GameSyncReq) (*GameSyncRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameSync not implemented")
}
func (UnimplementedGameSyncAdminServer) GameUpdate(context.Context, *GameUpdateReq) (*GameUpdateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameUpdate not implemented")
}
func (UnimplementedGameSyncAdminServer) PropDetail(context.Context, *PropDetailReq) (*PropDetailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PropDetail not implemented")
}
func (UnimplementedGameSyncAdminServer) GiftDetail(context.Context, *GiftDetailReq) (*GiftDetailRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GiftDetail not implemented")
}
func (UnimplementedGameSyncAdminServer) GameAssetCopy(context.Context, *GameAssetCopyReq) (*GameAssetCopyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameAssetCopy not implemented")
}
func (UnimplementedGameSyncAdminServer) GameAssetList(context.Context, *GameAssetListReq) (*GameAssetListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameAssetList not implemented")
}
func (UnimplementedGameSyncAdminServer) GameAssetUpdate(context.Context, *GameAssetUpdateReq) (*GameAssetUpdateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GameAssetUpdate not implemented")
}
func (UnimplementedGameSyncAdminServer) GamePackageCopy(context.Context, *GamePackageCopyReq) (*GamePackageCopyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GamePackageCopy not implemented")
}
func (UnimplementedGameSyncAdminServer) GamePackageList(context.Context, *GamePackageListReq) (*GamePackageListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GamePackageList not implemented")
}
func (UnimplementedGameSyncAdminServer) GamePackageUpdate(context.Context, *GamePackageUpdateReq) (*GamePackageUpdateRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GamePackageUpdate not implemented")
}

// UnsafeGameSyncAdminServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GameSyncAdminServer will
// result in compilation errors.
type UnsafeGameSyncAdminServer interface {
	mustEmbedUnimplementedGameSyncAdminServer()
}

func RegisterGameSyncAdminServer(s grpc.ServiceRegistrar, srv GameSyncAdminServer) {
	s.RegisterService(&GameSyncAdmin_ServiceDesc, srv)
}

func _GameSyncAdmin_ExchangeCurrencyCopy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeCurrencyCopyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).ExchangeCurrencyCopy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_ExchangeCurrencyCopy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).ExchangeCurrencyCopy(ctx, req.(*ExchangeCurrencyCopyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_ExchangeCurrencyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeCurrencyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).ExchangeCurrencyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_ExchangeCurrencyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).ExchangeCurrencyList(ctx, req.(*ExchangeCurrencyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_ExchangeCurrencyUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeCurrencyUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).ExchangeCurrencyUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_ExchangeCurrencyUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).ExchangeCurrencyUpdate(ctx, req.(*ExchangeCurrencyUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameCopy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameCopyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameCopy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameCopy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameCopy(ctx, req.(*GameCopyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameCurrencyCopy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameCurrencyCopyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameCurrencyCopy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameCurrencyCopy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameCurrencyCopy(ctx, req.(*GameCurrencyCopyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameCurrencyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameCurrencyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameCurrencyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameCurrencyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameCurrencyList(ctx, req.(*GameCurrencyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameCurrencyUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameCurrencyUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameCurrencyUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameCurrencyUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameCurrencyUpdate(ctx, req.(*GameCurrencyUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameList(ctx, req.(*GameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameSync_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameSync(ctx, req.(*GameSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameUpdate(ctx, req.(*GameUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_PropDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PropDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).PropDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_PropDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).PropDetail(ctx, req.(*PropDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GiftDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GiftDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GiftDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GiftDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GiftDetail(ctx, req.(*GiftDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameAssetCopy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameAssetCopyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameAssetCopy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameAssetCopy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameAssetCopy(ctx, req.(*GameAssetCopyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameAssetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameAssetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameAssetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameAssetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameAssetList(ctx, req.(*GameAssetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GameAssetUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GameAssetUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GameAssetUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GameAssetUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GameAssetUpdate(ctx, req.(*GameAssetUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GamePackageCopy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GamePackageCopyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GamePackageCopy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GamePackageCopy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GamePackageCopy(ctx, req.(*GamePackageCopyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GamePackageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GamePackageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GamePackageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GamePackageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GamePackageList(ctx, req.(*GamePackageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GameSyncAdmin_GamePackageUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GamePackageUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GameSyncAdminServer).GamePackageUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GameSyncAdmin_GamePackageUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GameSyncAdminServer).GamePackageUpdate(ctx, req.(*GamePackageUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GameSyncAdmin_ServiceDesc is the grpc.ServiceDesc for GameSyncAdmin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GameSyncAdmin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.game_sync_admin",
	HandlerType: (*GameSyncAdminServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExchangeCurrencyCopy",
			Handler:    _GameSyncAdmin_ExchangeCurrencyCopy_Handler,
		},
		{
			MethodName: "ExchangeCurrencyList",
			Handler:    _GameSyncAdmin_ExchangeCurrencyList_Handler,
		},
		{
			MethodName: "ExchangeCurrencyUpdate",
			Handler:    _GameSyncAdmin_ExchangeCurrencyUpdate_Handler,
		},
		{
			MethodName: "GameCopy",
			Handler:    _GameSyncAdmin_GameCopy_Handler,
		},
		{
			MethodName: "GameCurrencyCopy",
			Handler:    _GameSyncAdmin_GameCurrencyCopy_Handler,
		},
		{
			MethodName: "GameCurrencyList",
			Handler:    _GameSyncAdmin_GameCurrencyList_Handler,
		},
		{
			MethodName: "GameCurrencyUpdate",
			Handler:    _GameSyncAdmin_GameCurrencyUpdate_Handler,
		},
		{
			MethodName: "GameList",
			Handler:    _GameSyncAdmin_GameList_Handler,
		},
		{
			MethodName: "GameSync",
			Handler:    _GameSyncAdmin_GameSync_Handler,
		},
		{
			MethodName: "GameUpdate",
			Handler:    _GameSyncAdmin_GameUpdate_Handler,
		},
		{
			MethodName: "PropDetail",
			Handler:    _GameSyncAdmin_PropDetail_Handler,
		},
		{
			MethodName: "GiftDetail",
			Handler:    _GameSyncAdmin_GiftDetail_Handler,
		},
		{
			MethodName: "GameAssetCopy",
			Handler:    _GameSyncAdmin_GameAssetCopy_Handler,
		},
		{
			MethodName: "GameAssetList",
			Handler:    _GameSyncAdmin_GameAssetList_Handler,
		},
		{
			MethodName: "GameAssetUpdate",
			Handler:    _GameSyncAdmin_GameAssetUpdate_Handler,
		},
		{
			MethodName: "GamePackageCopy",
			Handler:    _GameSyncAdmin_GamePackageCopy_Handler,
		},
		{
			MethodName: "GamePackageList",
			Handler:    _GameSyncAdmin_GamePackageList_Handler,
		},
		{
			MethodName: "GamePackageUpdate",
			Handler:    _GameSyncAdmin_GamePackageUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_sync_admin/game_sync_admin.proto",
}
