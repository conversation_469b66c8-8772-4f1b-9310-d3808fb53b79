{"swagger": "2.0", "info": {"title": "pb/game_sync_admin/game_sync_admin.proto", "version": "version not set"}, "tags": [{"name": "game_sync_admin"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_sync_admin/exchange_currency/copy": {"post": {"summary": "Copy到外网--兑换货币", "operationId": "game_sync_admin_ExchangeCurrencyCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeCurrencyCopyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeCurrencyCopyReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/exchange_currency/list": {"post": {"summary": "兑换币列表", "operationId": "game_sync_admin_ExchangeCurrencyList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeCurrencyListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeCurrencyListReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/exchange_currency/update": {"post": {"summary": "兑换币配置更新", "operationId": "game_sync_admin_ExchangeCurrencyUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameExchangeCurrencyUpdateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameExchangeCurrencyUpdateReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game/copy": {"post": {"summary": "Copy到外网--游戏", "operationId": "game_sync_admin_GameCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameCopyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameCopyReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game/list": {"post": {"summary": "游戏列表", "operationId": "game_sync_admin_GameList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameListReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game/sync": {"post": {"summary": "游戏同步", "operationId": "game_sync_admin_GameSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameSyncRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameSyncReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game/update": {"post": {"summary": "游戏更新", "operationId": "game_sync_admin_GameUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameUpdateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameUpdateReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_asset/copy": {"post": {"summary": "Copy到外网--游戏资产", "operationId": "game_sync_admin_GameAssetCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameAssetCopyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameAssetCopyReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_asset/list": {"post": {"summary": "游戏资产列表", "operationId": "game_sync_admin_GameAssetList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameAssetListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameAssetListReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_asset/update": {"post": {"summary": "游戏资产更新", "operationId": "game_sync_admin_GameAssetUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameAssetUpdateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameAssetUpdateReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_currency/copy": {"post": {"summary": "Copy到外网--游戏货币", "operationId": "game_sync_admin_GameCurrencyCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameCurrencyCopyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameCurrencyCopyReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_currency/list": {"post": {"summary": "游戏货币列表", "operationId": "game_sync_admin_GameCurrencyList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameCurrencyListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameCurrencyListReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_currency/update": {"post": {"summary": "游戏货币更新", "operationId": "game_sync_admin_GameCurrencyUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGameCurrencyUpdateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGameCurrencyUpdateReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_package/copy": {"post": {"summary": "Copy到外网--游戏礼包", "operationId": "game_sync_admin_GamePackageCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGamePackageCopyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGamePackageCopyReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_package/list": {"post": {"summary": "游戏礼包列表", "operationId": "game_sync_admin_GamePackageList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGamePackageListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGamePackageListReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/game_package/update": {"post": {"summary": "游戏礼包更新", "operationId": "game_sync_admin_GamePackageUpdate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGamePackageUpdateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGamePackageUpdateReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/gift/detail": {"post": {"summary": "道具信息查询", "operationId": "game_sync_admin_GiftDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameGiftDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameGiftDetailReq"}}], "tags": ["game_sync_admin"]}}, "/game_sync_admin/prop/detail": {"post": {"summary": "道具信息查询", "operationId": "game_sync_admin_PropDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gamePropDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gamePropDetailReq"}}], "tags": ["game_sync_admin"]}}}, "definitions": {"gameCommonRsp": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "gameExchangeCurrency": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string", "title": "兑换券名称"}, "icon": {"type": "string", "title": "兑换券icon"}, "status": {"type": "integer", "format": "int64", "title": "同步状态"}, "expireType": {"type": "integer", "format": "int64", "title": "过期类型"}, "expireTime": {"type": "integer", "format": "int64", "title": "过期时间"}, "associatedId": {"type": "integer", "format": "int64", "title": "关联ID"}, "comment": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "tag": {"type": "string"}, "uniAssetId": {"type": "integer", "format": "int64"}, "expireUrl": {"type": "string", "title": "过期链接"}}}, "gameExchangeCurrencyCopyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "operator": {"type": "string"}}}, "gameExchangeCurrencyCopyRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}}}, "gameExchangeCurrencyListReq": {"type": "object", "properties": {"appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}}}, "gameExchangeCurrencyListRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "currencies": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameExchangeCurrency"}, "title": "这里返回的兑换货币已经过滤了对应平台的数据"}}}, "gameExchangeCurrencyUpdateReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "icon": {"type": "string"}, "expireType": {"type": "integer", "format": "int64"}, "expireTime": {"type": "integer", "format": "int64"}, "comment": {"type": "string"}, "operator": {"type": "string"}, "tag": {"type": "string"}, "expireUrl": {"type": "string", "title": "过期链接"}, "uniAssetId": {"type": "integer", "format": "int64", "title": "关联的统一货币"}}}, "gameExchangeCurrencyUpdateRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "id": {"type": "integer", "format": "int64"}}}, "gameGame": {"type": "object", "properties": {"appId": {"type": "string"}, "name": {"type": "string"}, "exchangeActivityId": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "prizeRewardIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "associatedAppId": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "syncedBy": {"type": "string"}, "createTime": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}, "syncTime": {"type": "integer", "format": "int64"}, "uniAppId": {"type": "string"}}}, "gameGameAssetConfig": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string", "title": "资产名称"}, "icon": {"type": "string", "title": "资产icon"}, "status": {"type": "integer", "format": "int64", "title": "同步状态"}, "tag": {"type": "string", "title": "关联标签"}, "associatedId": {"type": "integer", "format": "int64", "title": "关联ID"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "uniAssetId": {"type": "integer", "format": "int64"}, "expireType": {"type": "integer", "format": "int64", "title": "过期类型, 参见 asset_admin.ExpireTypeExtend"}, "expireTime": {"type": "integer", "format": "int64", "title": "过期时间"}, "expireUrl": {"type": "string", "title": "过期链接"}}}, "gameGameAssetCopyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "operator": {"type": "string"}}}, "gameGameAssetCopyRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}}}, "gameGameAssetListReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGameAssetListRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGameAssetConfig"}}}}, "gameGameAssetUpdateReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "operator": {"type": "string"}, "tag": {"type": "string"}, "expireType": {"type": "integer", "format": "int64", "title": "过期类型, 参见 asset_admin.ExpireTypeExtend"}, "expireTime": {"type": "integer", "format": "int64", "title": "过期时间"}, "expireUrl": {"type": "string", "title": "过期链接"}, "plat": {"type": "integer", "format": "int64", "title": "platId"}}}, "gameGameAssetUpdateRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "id": {"type": "integer", "format": "int64"}}}, "gameGameCopyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "operator": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGameCopyRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}}}, "gameGameCurrency": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "status": {"type": "integer", "format": "int64"}, "payType": {"type": "integer", "format": "int64"}, "payPropsUnitPrize": {"type": "integer", "format": "int64"}, "payPropsId": {"type": "integer", "format": "int64"}, "exchangeRate": {"type": "integer", "format": "int64"}, "platBusinessId": {"type": "integer", "format": "int64", "title": "平台BusinessId"}, "revenueType": {"type": "integer", "format": "int64"}, "expireType": {"type": "integer", "format": "int64"}, "expireTime": {"type": "integer", "format": "int64"}, "associatedId": {"type": "integer", "format": "int64"}, "comment": {"type": "string"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "tag": {"type": "string"}, "uniAssetId": {"type": "integer", "format": "int64"}, "payDesc": {"type": "string", "description": "购买窗口提示文案", "title": "购买配置"}, "payGear": {"type": "string", "title": "购买档次"}, "expireUrl": {"type": "string", "title": "过期链接"}, "modalType": {"type": "integer", "format": "int64", "title": "弹窗类型 1 旧版 2 低风险 3 高风险"}, "assetRule": {"type": "string", "title": "道具文案"}, "sendToUgc": {"type": "boolean"}, "extensionId": {"type": "string", "title": "扩展服务"}}}, "gameGameCurrencyCopyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "operator": {"type": "string"}}}, "gameGameCurrencyCopyRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}}}, "gameGameCurrencyListReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGameCurrencyListRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "currencies": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGameCurrency"}}}}, "gameGameCurrencyUpdateReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "icon": {"type": "string"}, "payType": {"type": "integer", "format": "int64"}, "payPropsUnitPrize": {"type": "integer", "format": "int64"}, "payPropsId": {"type": "integer", "format": "int64"}, "exchangeRate": {"type": "integer", "format": "int64"}, "revenueType": {"type": "integer", "format": "int64"}, "platBusinessId": {"type": "integer", "format": "int64", "title": "平台BusinessId"}, "expireType": {"type": "integer", "format": "int64"}, "expireTime": {"type": "integer", "format": "int64"}, "comment": {"type": "string"}, "operator": {"type": "string"}, "tag": {"type": "string"}, "uniAssetId": {"type": "integer", "format": "int64", "title": "关联的统一货币"}, "payDesc": {"type": "string", "description": "购买窗口提示文案", "title": "购买配置"}, "payGear": {"type": "string", "title": "购买档次"}, "expireUrl": {"type": "string", "title": "过期链接"}, "modalType": {"type": "integer", "format": "int64", "title": "弹窗类型 1 旧版 2 低风险 3 高风险"}, "assetRule": {"type": "string", "title": "道具文案"}, "sendToUgc": {"type": "boolean"}, "extensionId": {"type": "string", "title": "扩展服务"}}}, "gameGameCurrencyUpdateRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "id": {"type": "integer", "format": "int64"}}}, "gameGameListReq": {"type": "object", "properties": {"appIds": {"type": "array", "items": {"type": "string"}}, "name": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGameListRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "games": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGame"}}}}, "gameGamePackage": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "name": {"type": "string"}, "icon": {"type": "string"}, "status": {"type": "integer", "format": "int64"}, "payType": {"type": "integer", "format": "int64"}, "payPropsUnitPrize": {"type": "integer", "format": "int64"}, "payPropsId": {"type": "integer", "format": "int64"}, "exchangeRate": {"type": "integer", "format": "int64"}, "platBusinessId": {"type": "integer", "format": "int64", "title": "平台BusinessId"}, "revenueType": {"type": "integer", "format": "int64"}, "rewardId": {"type": "integer", "format": "int64"}, "createdBy": {"type": "string"}, "updatedBy": {"type": "string"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "uniId": {"type": "integer", "format": "int64"}, "modalType": {"type": "integer", "format": "int64", "title": "弹窗类型 1 旧版 2 低风险 3 高风险"}, "description": {"type": "string"}, "sendToUgc": {"type": "boolean"}}}, "gameGamePackageCopyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "operator": {"type": "string"}}}, "gameGamePackageCopyRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}}}, "gameGamePackageListReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGamePackageListRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "packages": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/gameGamePackage"}}}}, "gameGamePackageUpdateReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "icon": {"type": "string"}, "payType": {"type": "integer", "format": "int64"}, "payPropsUnitPrize": {"type": "integer", "format": "int64"}, "payPropsId": {"type": "integer", "format": "int64"}, "exchangeRate": {"type": "integer", "format": "int64"}, "revenueType": {"type": "integer", "format": "int64"}, "platBusinessId": {"type": "integer", "format": "int64", "title": "平台BusinessId"}, "rewardId": {"type": "integer", "format": "int64"}, "operator": {"type": "string"}, "uniId": {"type": "integer", "format": "int64"}, "modalType": {"type": "integer", "format": "int64", "title": "弹窗类型 1 旧版 2 低风险 3 高风险"}, "description": {"type": "string"}, "sendToUgc": {"type": "boolean"}}}, "gameGamePackageUpdateRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "id": {"type": "integer", "format": "int64"}}}, "gameGameSyncReq": {"type": "object", "properties": {"appId": {"type": "string"}, "operator": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGameSyncRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}}}, "gameGameUpdateReq": {"type": "object", "properties": {"appId": {"type": "string"}, "name": {"type": "string"}, "prizeRewardIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "operator": {"type": "string"}, "plat": {"type": "integer", "format": "int64"}}}, "gameGameUpdateRsp": {"type": "object", "properties": {"commonRsp": {"$ref": "#/definitions/gameCommonRsp"}, "appId": {"type": "string"}}}, "gameGiftDetailReq": {"type": "object", "properties": {"plat": {"type": "integer", "format": "int64"}, "giftId": {"type": "integer", "format": "int64"}}}, "gameGiftDetailRsp": {"type": "object", "properties": {"giftName": {"type": "string"}, "giftIcon": {"type": "string"}, "giftPrice": {"type": "integer", "format": "int64"}}}, "gamePropDetailReq": {"type": "object", "properties": {"plat": {"type": "integer", "format": "int64"}, "propId": {"type": "integer", "format": "int64"}}}, "gamePropDetailRsp": {"type": "object", "properties": {"propName": {"type": "string"}, "propIcon": {"type": "string"}, "propPrice": {"type": "integer", "format": "int64"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}