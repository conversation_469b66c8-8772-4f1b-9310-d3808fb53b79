// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_sync_admin/game_sync_admin.proto

package game_sync_admin

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CommonRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    uint32 `protobuf:"varint,1,opt,name=Code,json=code,proto3" json:"Code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=Message,json=message,proto3" json:"Message,omitempty"`
}

func (x *CommonRsp) Reset() {
	*x = CommonRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonRsp) ProtoMessage() {}

func (x *CommonRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonRsp.ProtoReflect.Descriptor instead.
func (*CommonRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{0}
}

func (x *CommonRsp) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CommonRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ExchangeCurrencyCopyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat     uint32 `protobuf:"varint,2,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	ID       uint32 `protobuf:"varint,3,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Operator string `protobuf:"bytes,4,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
}

func (x *ExchangeCurrencyCopyReq) Reset() {
	*x = ExchangeCurrencyCopyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyCopyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyCopyReq) ProtoMessage() {}

func (x *ExchangeCurrencyCopyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyCopyReq.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyCopyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{1}
}

func (x *ExchangeCurrencyCopyReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *ExchangeCurrencyCopyReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *ExchangeCurrencyCopyReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *ExchangeCurrencyCopyReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type ExchangeCurrencyCopyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
}

func (x *ExchangeCurrencyCopyRsp) Reset() {
	*x = ExchangeCurrencyCopyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyCopyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyCopyRsp) ProtoMessage() {}

func (x *ExchangeCurrencyCopyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyCopyRsp.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyCopyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{2}
}

func (x *ExchangeCurrencyCopyRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

type ExchangeCurrencyListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat  uint32 `protobuf:"varint,2,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	ID    uint32 `protobuf:"varint,3,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
}

func (x *ExchangeCurrencyListReq) Reset() {
	*x = ExchangeCurrencyListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyListReq) ProtoMessage() {}

func (x *ExchangeCurrencyListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyListReq.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{3}
}

func (x *ExchangeCurrencyListReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *ExchangeCurrencyListReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *ExchangeCurrencyListReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type ExchangeCurrency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID        string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name         string `protobuf:"bytes,3,opt,name=Name,json=name,proto3" json:"Name,omitempty"`                          // 兑换券名称
	Icon         string `protobuf:"bytes,4,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`                          // 兑换券icon
	Status       uint32 `protobuf:"varint,5,opt,name=Status,json=status,proto3" json:"Status,omitempty"`                   // 同步状态
	ExpireType   uint32 `protobuf:"varint,6,opt,name=ExpireType,json=expireType,proto3" json:"ExpireType,omitempty"`       // 过期类型
	ExpireTime   uint32 `protobuf:"varint,7,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime,omitempty"`       // 过期时间
	AssociatedID uint32 `protobuf:"varint,8,opt,name=AssociatedID,json=associatedId,proto3" json:"AssociatedID,omitempty"` // 关联ID
	Comment      string `protobuf:"bytes,9,opt,name=Comment,json=comment,proto3" json:"Comment,omitempty"`
	CreatedBy    string `protobuf:"bytes,10,opt,name=CreatedBy,json=createdBy,proto3" json:"CreatedBy,omitempty"`
	UpdatedBy    string `protobuf:"bytes,11,opt,name=UpdatedBy,json=updatedBy,proto3" json:"UpdatedBy,omitempty"`
	CreateTime   uint32 `protobuf:"varint,12,opt,name=CreateTime,json=createTime,proto3" json:"CreateTime,omitempty"`
	UpdateTime   uint32 `protobuf:"varint,13,opt,name=UpdateTime,json=updateTime,proto3" json:"UpdateTime,omitempty"`
	Tag          string `protobuf:"bytes,14,opt,name=Tag,json=tag,proto3" json:"Tag,omitempty"`
	UniAssetId   uint32 `protobuf:"varint,15,opt,name=UniAssetId,json=uniAssetId,proto3" json:"UniAssetId,omitempty"`
	ExpireUrl    string `protobuf:"bytes,16,opt,name=ExpireUrl,json=expireUrl,proto3" json:"ExpireUrl,omitempty"` // 过期链接
}

func (x *ExchangeCurrency) Reset() {
	*x = ExchangeCurrency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrency) ProtoMessage() {}

func (x *ExchangeCurrency) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrency.ProtoReflect.Descriptor instead.
func (*ExchangeCurrency) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{4}
}

func (x *ExchangeCurrency) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *ExchangeCurrency) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *ExchangeCurrency) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExchangeCurrency) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ExchangeCurrency) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ExchangeCurrency) GetExpireType() uint32 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *ExchangeCurrency) GetExpireTime() uint32 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *ExchangeCurrency) GetAssociatedID() uint32 {
	if x != nil {
		return x.AssociatedID
	}
	return 0
}

func (x *ExchangeCurrency) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ExchangeCurrency) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *ExchangeCurrency) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *ExchangeCurrency) GetCreateTime() uint32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ExchangeCurrency) GetUpdateTime() uint32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ExchangeCurrency) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ExchangeCurrency) GetUniAssetId() uint32 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

func (x *ExchangeCurrency) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

type ExchangeCurrencyListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	// 这里返回的兑换货币已经过滤了对应平台的数据
	Currencies []*ExchangeCurrency `protobuf:"bytes,2,rep,name=Currencies,json=currencies,proto3" json:"Currencies,omitempty"`
}

func (x *ExchangeCurrencyListRsp) Reset() {
	*x = ExchangeCurrencyListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyListRsp) ProtoMessage() {}

func (x *ExchangeCurrencyListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyListRsp.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{5}
}

func (x *ExchangeCurrencyListRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *ExchangeCurrencyListRsp) GetCurrencies() []*ExchangeCurrency {
	if x != nil {
		return x.Currencies
	}
	return nil
}

type ExchangeCurrencyUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID         uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Status     uint32 `protobuf:"varint,2,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	AppID      string `protobuf:"bytes,3,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat       uint32 `protobuf:"varint,4,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	Name       string `protobuf:"bytes,5,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Icon       string `protobuf:"bytes,6,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	ExpireType uint32 `protobuf:"varint,7,opt,name=ExpireType,json=expireType,proto3" json:"ExpireType,omitempty"`
	ExpireTime uint32 `protobuf:"varint,8,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime,omitempty"`
	Comment    string `protobuf:"bytes,9,opt,name=Comment,json=comment,proto3" json:"Comment,omitempty"`
	Operator   string `protobuf:"bytes,10,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	Tag        string `protobuf:"bytes,11,opt,name=Tag,json=tag,proto3" json:"Tag,omitempty"`
	ExpireUrl  string `protobuf:"bytes,12,opt,name=ExpireUrl,json=expireUrl,proto3" json:"ExpireUrl,omitempty"`     // 过期链接
	UniAssetId uint32 `protobuf:"varint,13,opt,name=UniAssetId,json=uniAssetId,proto3" json:"UniAssetId,omitempty"` // 关联的统一货币
}

func (x *ExchangeCurrencyUpdateReq) Reset() {
	*x = ExchangeCurrencyUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyUpdateReq) ProtoMessage() {}

func (x *ExchangeCurrencyUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyUpdateReq.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyUpdateReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{6}
}

func (x *ExchangeCurrencyUpdateReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *ExchangeCurrencyUpdateReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ExchangeCurrencyUpdateReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *ExchangeCurrencyUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetExpireType() uint32 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *ExchangeCurrencyUpdateReq) GetExpireTime() uint32 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *ExchangeCurrencyUpdateReq) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

func (x *ExchangeCurrencyUpdateReq) GetUniAssetId() uint32 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

type ExchangeCurrencyUpdateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	ID        uint32     `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
}

func (x *ExchangeCurrencyUpdateRsp) Reset() {
	*x = ExchangeCurrencyUpdateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangeCurrencyUpdateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangeCurrencyUpdateRsp) ProtoMessage() {}

func (x *ExchangeCurrencyUpdateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangeCurrencyUpdateRsp.ProtoReflect.Descriptor instead.
func (*ExchangeCurrencyUpdateRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{7}
}

func (x *ExchangeCurrencyUpdateRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *ExchangeCurrencyUpdateRsp) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type GameCopyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Operator string `protobuf:"bytes,2,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	Plat     uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GameCopyReq) Reset() {
	*x = GameCopyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCopyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCopyReq) ProtoMessage() {}

func (x *GameCopyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCopyReq.ProtoReflect.Descriptor instead.
func (*GameCopyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{8}
}

func (x *GameCopyReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameCopyReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GameCopyReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameCopyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
}

func (x *GameCopyRsp) Reset() {
	*x = GameCopyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCopyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCopyRsp) ProtoMessage() {}

func (x *GameCopyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCopyRsp.ProtoReflect.Descriptor instead.
func (*GameCopyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{9}
}

func (x *GameCopyRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

type GameCurrencyCopyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat     uint32 `protobuf:"varint,2,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	ID       uint32 `protobuf:"varint,3,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Operator string `protobuf:"bytes,4,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
}

func (x *GameCurrencyCopyReq) Reset() {
	*x = GameCurrencyCopyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrencyCopyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrencyCopyReq) ProtoMessage() {}

func (x *GameCurrencyCopyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrencyCopyReq.ProtoReflect.Descriptor instead.
func (*GameCurrencyCopyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{10}
}

func (x *GameCurrencyCopyReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameCurrencyCopyReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *GameCurrencyCopyReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameCurrencyCopyReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type GameCurrencyCopyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
}

func (x *GameCurrencyCopyRsp) Reset() {
	*x = GameCurrencyCopyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrencyCopyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrencyCopyRsp) ProtoMessage() {}

func (x *GameCurrencyCopyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrencyCopyRsp.ProtoReflect.Descriptor instead.
func (*GameCurrencyCopyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{11}
}

func (x *GameCurrencyCopyRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

type GameCurrencyListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID    uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat  uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GameCurrencyListReq) Reset() {
	*x = GameCurrencyListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrencyListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrencyListReq) ProtoMessage() {}

func (x *GameCurrencyListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrencyListReq.ProtoReflect.Descriptor instead.
func (*GameCurrencyListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{12}
}

func (x *GameCurrencyListReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameCurrencyListReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameCurrencyListReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameCurrencyListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp  *CommonRsp      `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	Currencies []*GameCurrency `protobuf:"bytes,2,rep,name=Currencies,json=currencies,proto3" json:"Currencies,omitempty"`
}

func (x *GameCurrencyListRsp) Reset() {
	*x = GameCurrencyListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrencyListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrencyListRsp) ProtoMessage() {}

func (x *GameCurrencyListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrencyListRsp.ProtoReflect.Descriptor instead.
func (*GameCurrencyListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{13}
}

func (x *GameCurrencyListRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GameCurrencyListRsp) GetCurrencies() []*GameCurrency {
	if x != nil {
		return x.Currencies
	}
	return nil
}

type GameCurrency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID             string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name              string `protobuf:"bytes,3,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Icon              string `protobuf:"bytes,4,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Status            uint32 `protobuf:"varint,5,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	PayType           uint32 `protobuf:"varint,6,opt,name=PayType,json=payType,proto3" json:"PayType,omitempty"`
	PayPropsUnitPrize uint32 `protobuf:"varint,7,opt,name=PayPropsUnitPrize,json=payPropsUnitPrize,proto3" json:"PayPropsUnitPrize,omitempty"`
	PayPropsID        uint32 `protobuf:"varint,8,opt,name=PayPropsID,json=payPropsId,proto3" json:"PayPropsID,omitempty"`
	ExchangeRate      uint32 `protobuf:"varint,9,opt,name=ExchangeRate,json=exchangeRate,proto3" json:"ExchangeRate,omitempty"`
	// 平台BusinessId
	PlatBusinessID uint32 `protobuf:"varint,10,opt,name=PlatBusinessID,json=platBusinessId,proto3" json:"PlatBusinessID,omitempty"`
	RevenueType    uint32 `protobuf:"varint,11,opt,name=RevenueType,json=revenueType,proto3" json:"RevenueType,omitempty"`
	ExpireType     uint32 `protobuf:"varint,12,opt,name=ExpireType,json=expireType,proto3" json:"ExpireType,omitempty"`
	ExpireTime     uint32 `protobuf:"varint,13,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime,omitempty"`
	AssociatedID   uint32 `protobuf:"varint,14,opt,name=AssociatedID,json=associatedId,proto3" json:"AssociatedID,omitempty"`
	Comment        string `protobuf:"bytes,15,opt,name=Comment,json=comment,proto3" json:"Comment,omitempty"`
	CreatedBy      string `protobuf:"bytes,16,opt,name=CreatedBy,json=createdBy,proto3" json:"CreatedBy,omitempty"`
	UpdatedBy      string `protobuf:"bytes,17,opt,name=UpdatedBy,json=updatedBy,proto3" json:"UpdatedBy,omitempty"`
	CreateTime     uint32 `protobuf:"varint,18,opt,name=CreateTime,json=createTime,proto3" json:"CreateTime,omitempty"`
	UpdateTime     uint32 `protobuf:"varint,19,opt,name=UpdateTime,json=updateTime,proto3" json:"UpdateTime,omitempty"`
	Tag            string `protobuf:"bytes,20,opt,name=Tag,json=tag,proto3" json:"Tag,omitempty"`
	UniAssetId     uint32 `protobuf:"varint,21,opt,name=UniAssetId,json=uniAssetId,proto3" json:"UniAssetId,omitempty"`
	// 购买配置
	PayDesc     string `protobuf:"bytes,22,opt,name=PayDesc,json=payDesc,proto3" json:"PayDesc,omitempty"`        // 购买窗口提示文案
	PayGear     string `protobuf:"bytes,23,opt,name=PayGear,json=payGear,proto3" json:"PayGear,omitempty"`        // 购买档次
	ExpireUrl   string `protobuf:"bytes,24,opt,name=ExpireUrl,json=expireUrl,proto3" json:"ExpireUrl,omitempty"`  // 过期链接
	ModalType   uint32 `protobuf:"varint,25,opt,name=ModalType,json=modalType,proto3" json:"ModalType,omitempty"` // 弹窗类型 1 旧版 2 低风险 3 高风险
	AssetRule   string `protobuf:"bytes,26,opt,name=AssetRule,json=assetRule,proto3" json:"AssetRule,omitempty"`  // 道具文案
	SendToUgc   bool   `protobuf:"varint,27,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
	ExtensionId string `protobuf:"bytes,28,opt,name=extensionId,proto3" json:"extensionId,omitempty"` // 扩展服务
}

func (x *GameCurrency) Reset() {
	*x = GameCurrency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrency) ProtoMessage() {}

func (x *GameCurrency) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrency.ProtoReflect.Descriptor instead.
func (*GameCurrency) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{14}
}

func (x *GameCurrency) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameCurrency) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameCurrency) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameCurrency) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GameCurrency) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GameCurrency) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *GameCurrency) GetPayPropsUnitPrize() uint32 {
	if x != nil {
		return x.PayPropsUnitPrize
	}
	return 0
}

func (x *GameCurrency) GetPayPropsID() uint32 {
	if x != nil {
		return x.PayPropsID
	}
	return 0
}

func (x *GameCurrency) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *GameCurrency) GetPlatBusinessID() uint32 {
	if x != nil {
		return x.PlatBusinessID
	}
	return 0
}

func (x *GameCurrency) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *GameCurrency) GetExpireType() uint32 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *GameCurrency) GetExpireTime() uint32 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *GameCurrency) GetAssociatedID() uint32 {
	if x != nil {
		return x.AssociatedID
	}
	return 0
}

func (x *GameCurrency) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GameCurrency) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *GameCurrency) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *GameCurrency) GetCreateTime() uint32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *GameCurrency) GetUpdateTime() uint32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *GameCurrency) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GameCurrency) GetUniAssetId() uint32 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

func (x *GameCurrency) GetPayDesc() string {
	if x != nil {
		return x.PayDesc
	}
	return ""
}

func (x *GameCurrency) GetPayGear() string {
	if x != nil {
		return x.PayGear
	}
	return ""
}

func (x *GameCurrency) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

func (x *GameCurrency) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *GameCurrency) GetAssetRule() string {
	if x != nil {
		return x.AssetRule
	}
	return ""
}

func (x *GameCurrency) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

func (x *GameCurrency) GetExtensionId() string {
	if x != nil {
		return x.ExtensionId
	}
	return ""
}

type GameCurrencyUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID             string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat              uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	Name              string `protobuf:"bytes,4,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Icon              string `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	PayType           uint32 `protobuf:"varint,6,opt,name=PayType,json=payType,proto3" json:"PayType,omitempty"`
	PayPropsUnitPrize uint32 `protobuf:"varint,7,opt,name=PayPropsUnitPrize,json=payPropsUnitPrize,proto3" json:"PayPropsUnitPrize,omitempty"`
	PayPropsID        uint32 `protobuf:"varint,8,opt,name=PayPropsID,json=payPropsId,proto3" json:"PayPropsID,omitempty"`
	ExchangeRate      uint32 `protobuf:"varint,9,opt,name=ExchangeRate,json=exchangeRate,proto3" json:"ExchangeRate,omitempty"`
	RevenueType       uint32 `protobuf:"varint,10,opt,name=RevenueType,json=revenueType,proto3" json:"RevenueType,omitempty"`
	// 平台BusinessId
	PlatBusinessID uint32 `protobuf:"varint,11,opt,name=PlatBusinessID,json=platBusinessId,proto3" json:"PlatBusinessID,omitempty"`
	ExpireType     uint32 `protobuf:"varint,12,opt,name=ExpireType,json=expireType,proto3" json:"ExpireType,omitempty"`
	ExpireTime     uint32 `protobuf:"varint,13,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime,omitempty"`
	Comment        string `protobuf:"bytes,14,opt,name=Comment,json=comment,proto3" json:"Comment,omitempty"`
	Operator       string `protobuf:"bytes,15,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	Tag            string `protobuf:"bytes,16,opt,name=Tag,json=tag,proto3" json:"Tag,omitempty"`
	UniAssetId     uint32 `protobuf:"varint,17,opt,name=UniAssetId,json=uniAssetId,proto3" json:"UniAssetId,omitempty"` // 关联的统一货币
	// 购买配置
	PayDesc     string `protobuf:"bytes,22,opt,name=PayDesc,json=payDesc,proto3" json:"PayDesc,omitempty"`        // 购买窗口提示文案
	PayGear     string `protobuf:"bytes,23,opt,name=PayGear,json=payGear,proto3" json:"PayGear,omitempty"`        // 购买档次
	ExpireUrl   string `protobuf:"bytes,24,opt,name=ExpireUrl,json=expireUrl,proto3" json:"ExpireUrl,omitempty"`  // 过期链接
	ModalType   uint32 `protobuf:"varint,25,opt,name=ModalType,json=modalType,proto3" json:"ModalType,omitempty"` // 弹窗类型 1 旧版 2 低风险 3 高风险
	AssetRule   string `protobuf:"bytes,26,opt,name=AssetRule,json=assetRule,proto3" json:"AssetRule,omitempty"`  // 道具文案
	SendToUgc   bool   `protobuf:"varint,27,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
	ExtensionId string `protobuf:"bytes,28,opt,name=extensionId,proto3" json:"extensionId,omitempty"` // 扩展服务
}

func (x *GameCurrencyUpdateReq) Reset() {
	*x = GameCurrencyUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrencyUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrencyUpdateReq) ProtoMessage() {}

func (x *GameCurrencyUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrencyUpdateReq.ProtoReflect.Descriptor instead.
func (*GameCurrencyUpdateReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{15}
}

func (x *GameCurrencyUpdateReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetPayPropsUnitPrize() uint32 {
	if x != nil {
		return x.PayPropsUnitPrize
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetPayPropsID() uint32 {
	if x != nil {
		return x.PayPropsID
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetPlatBusinessID() uint32 {
	if x != nil {
		return x.PlatBusinessID
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetExpireType() uint32 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetExpireTime() uint32 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetUniAssetId() uint32 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetPayDesc() string {
	if x != nil {
		return x.PayDesc
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetPayGear() string {
	if x != nil {
		return x.PayGear
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *GameCurrencyUpdateReq) GetAssetRule() string {
	if x != nil {
		return x.AssetRule
	}
	return ""
}

func (x *GameCurrencyUpdateReq) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

func (x *GameCurrencyUpdateReq) GetExtensionId() string {
	if x != nil {
		return x.ExtensionId
	}
	return ""
}

type GameCurrencyUpdateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	ID        uint32     `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
}

func (x *GameCurrencyUpdateRsp) Reset() {
	*x = GameCurrencyUpdateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameCurrencyUpdateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameCurrencyUpdateRsp) ProtoMessage() {}

func (x *GameCurrencyUpdateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameCurrencyUpdateRsp.ProtoReflect.Descriptor instead.
func (*GameCurrencyUpdateRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{16}
}

func (x *GameCurrencyUpdateRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GameCurrencyUpdateRsp) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type GameAssetCopyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	ID       uint32 `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Operator string `protobuf:"bytes,3,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
}

func (x *GameAssetCopyReq) Reset() {
	*x = GameAssetCopyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetCopyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetCopyReq) ProtoMessage() {}

func (x *GameAssetCopyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetCopyReq.ProtoReflect.Descriptor instead.
func (*GameAssetCopyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{17}
}

func (x *GameAssetCopyReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameAssetCopyReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameAssetCopyReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type GameAssetCopyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
}

func (x *GameAssetCopyRsp) Reset() {
	*x = GameAssetCopyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetCopyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetCopyRsp) ProtoMessage() {}

func (x *GameAssetCopyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetCopyRsp.ProtoReflect.Descriptor instead.
func (*GameAssetCopyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{18}
}

func (x *GameAssetCopyRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

type GameAssetListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID    uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat  uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GameAssetListReq) Reset() {
	*x = GameAssetListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetListReq) ProtoMessage() {}

func (x *GameAssetListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetListReq.ProtoReflect.Descriptor instead.
func (*GameAssetListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{19}
}

func (x *GameAssetListReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameAssetListReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameAssetListReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameAssetListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp         `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	Assets    []*GameAssetConfig `protobuf:"bytes,2,rep,name=Assets,json=assets,proto3" json:"Assets,omitempty"`
}

func (x *GameAssetListRsp) Reset() {
	*x = GameAssetListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetListRsp) ProtoMessage() {}

func (x *GameAssetListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetListRsp.ProtoReflect.Descriptor instead.
func (*GameAssetListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{20}
}

func (x *GameAssetListRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GameAssetListRsp) GetAssets() []*GameAssetConfig {
	if x != nil {
		return x.Assets
	}
	return nil
}

type GameAssetConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID           uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID        string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name         string `protobuf:"bytes,3,opt,name=Name,json=name,proto3" json:"Name,omitempty"`                          // 资产名称
	Icon         string `protobuf:"bytes,4,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`                          // 资产icon
	Status       uint32 `protobuf:"varint,5,opt,name=Status,json=status,proto3" json:"Status,omitempty"`                   // 同步状态
	Tag          string `protobuf:"bytes,6,opt,name=Tag,json=tag,proto3" json:"Tag,omitempty"`                             // 关联标签
	AssociatedID uint32 `protobuf:"varint,7,opt,name=AssociatedID,json=associatedId,proto3" json:"AssociatedID,omitempty"` // 关联ID
	CreatedBy    string `protobuf:"bytes,8,opt,name=CreatedBy,json=createdBy,proto3" json:"CreatedBy,omitempty"`
	UpdatedBy    string `protobuf:"bytes,9,opt,name=UpdatedBy,json=updatedBy,proto3" json:"UpdatedBy,omitempty"`
	CreateTime   uint32 `protobuf:"varint,10,opt,name=CreateTime,json=createTime,proto3" json:"CreateTime,omitempty"`
	UpdateTime   uint32 `protobuf:"varint,11,opt,name=UpdateTime,json=updateTime,proto3" json:"UpdateTime,omitempty"`
	UniAssetId   uint32 `protobuf:"varint,12,opt,name=UniAssetId,json=uniAssetId,proto3" json:"UniAssetId,omitempty"`
	ExpireType   uint32 `protobuf:"varint,13,opt,name=ExpireType,json=expireType,proto3" json:"ExpireType,omitempty"` // 过期类型, 参见 asset_admin.ExpireTypeExtend
	ExpireTime   uint32 `protobuf:"varint,14,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime,omitempty"` // 过期时间
	ExpireUrl    string `protobuf:"bytes,15,opt,name=ExpireUrl,json=expireUrl,proto3" json:"ExpireUrl,omitempty"`     // 过期链接
}

func (x *GameAssetConfig) Reset() {
	*x = GameAssetConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetConfig) ProtoMessage() {}

func (x *GameAssetConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetConfig.ProtoReflect.Descriptor instead.
func (*GameAssetConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{21}
}

func (x *GameAssetConfig) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameAssetConfig) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameAssetConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameAssetConfig) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GameAssetConfig) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GameAssetConfig) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GameAssetConfig) GetAssociatedID() uint32 {
	if x != nil {
		return x.AssociatedID
	}
	return 0
}

func (x *GameAssetConfig) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *GameAssetConfig) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *GameAssetConfig) GetCreateTime() uint32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *GameAssetConfig) GetUpdateTime() uint32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *GameAssetConfig) GetUniAssetId() uint32 {
	if x != nil {
		return x.UniAssetId
	}
	return 0
}

func (x *GameAssetConfig) GetExpireType() uint32 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *GameAssetConfig) GetExpireTime() uint32 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *GameAssetConfig) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

type GameAssetUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID         uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Status     uint32 `protobuf:"varint,2,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	AppID      string `protobuf:"bytes,3,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name       string `protobuf:"bytes,4,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Icon       string `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Operator   string `protobuf:"bytes,6,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	Tag        string `protobuf:"bytes,7,opt,name=Tag,json=tag,proto3" json:"Tag,omitempty"`
	ExpireType uint32 `protobuf:"varint,8,opt,name=ExpireType,json=expireType,proto3" json:"ExpireType,omitempty"` // 过期类型, 参见 asset_admin.ExpireTypeExtend
	ExpireTime uint32 `protobuf:"varint,9,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime,omitempty"` // 过期时间
	ExpireUrl  string `protobuf:"bytes,10,opt,name=ExpireUrl,json=expireUrl,proto3" json:"ExpireUrl,omitempty"`    // 过期链接
	Plat       uint32 `protobuf:"varint,11,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`                  // platId
}

func (x *GameAssetUpdateReq) Reset() {
	*x = GameAssetUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetUpdateReq) ProtoMessage() {}

func (x *GameAssetUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetUpdateReq.ProtoReflect.Descriptor instead.
func (*GameAssetUpdateReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{22}
}

func (x *GameAssetUpdateReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GameAssetUpdateReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GameAssetUpdateReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameAssetUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameAssetUpdateReq) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GameAssetUpdateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GameAssetUpdateReq) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *GameAssetUpdateReq) GetExpireType() uint32 {
	if x != nil {
		return x.ExpireType
	}
	return 0
}

func (x *GameAssetUpdateReq) GetExpireTime() uint32 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *GameAssetUpdateReq) GetExpireUrl() string {
	if x != nil {
		return x.ExpireUrl
	}
	return ""
}

func (x *GameAssetUpdateReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameAssetUpdateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	ID        uint32     `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
}

func (x *GameAssetUpdateRsp) Reset() {
	*x = GameAssetUpdateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameAssetUpdateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameAssetUpdateRsp) ProtoMessage() {}

func (x *GameAssetUpdateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameAssetUpdateRsp.ProtoReflect.Descriptor instead.
func (*GameAssetUpdateRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{23}
}

func (x *GameAssetUpdateRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GameAssetUpdateRsp) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

type GameListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppIDs []string `protobuf:"bytes,1,rep,name=AppIDs,json=appIds,proto3" json:"AppIDs,omitempty"`
	Name   string   `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Plat   uint32   `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GameListReq) Reset() {
	*x = GameListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListReq) ProtoMessage() {}

func (x *GameListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListReq.ProtoReflect.Descriptor instead.
func (*GameListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{24}
}

func (x *GameListReq) GetAppIDs() []string {
	if x != nil {
		return x.AppIDs
	}
	return nil
}

func (x *GameListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameListReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	Games     []*Game    `protobuf:"bytes,2,rep,name=Games,json=games,proto3" json:"Games,omitempty"`
}

func (x *GameListRsp) Reset() {
	*x = GameListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameListRsp) ProtoMessage() {}

func (x *GameListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameListRsp.ProtoReflect.Descriptor instead.
func (*GameListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{25}
}

func (x *GameListRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GameListRsp) GetGames() []*Game {
	if x != nil {
		return x.Games
	}
	return nil
}

type Game struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID              string   `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name               string   `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	ExchangeActivityID []uint32 `protobuf:"varint,3,rep,packed,name=ExchangeActivityID,json=exchangeActivityId,proto3" json:"ExchangeActivityID,omitempty"`
	PrizeRewardIds     []uint32 `protobuf:"varint,4,rep,packed,name=PrizeRewardIds,json=prizeRewardIds,proto3" json:"PrizeRewardIds,omitempty"`
	AssociatedAppID    string   `protobuf:"bytes,5,opt,name=AssociatedAppID,json=associatedAppId,proto3" json:"AssociatedAppID,omitempty"`
	CreatedBy          string   `protobuf:"bytes,6,opt,name=CreatedBy,json=createdBy,proto3" json:"CreatedBy,omitempty"`
	UpdatedBy          string   `protobuf:"bytes,7,opt,name=UpdatedBy,json=updatedBy,proto3" json:"UpdatedBy,omitempty"`
	SyncedBy           string   `protobuf:"bytes,8,opt,name=SyncedBy,json=syncedBy,proto3" json:"SyncedBy,omitempty"`
	CreateTime         string   `protobuf:"bytes,9,opt,name=CreateTime,json=createTime,proto3" json:"CreateTime,omitempty"`
	UpdateTime         uint32   `protobuf:"varint,10,opt,name=UpdateTime,json=updateTime,proto3" json:"UpdateTime,omitempty"`
	SyncTime           uint32   `protobuf:"varint,11,opt,name=SyncTime,json=syncTime,proto3" json:"SyncTime,omitempty"`
	UniAppId           string   `protobuf:"bytes,12,opt,name=UniAppId,json=uniAppId,proto3" json:"UniAppId,omitempty"`
}

func (x *Game) Reset() {
	*x = Game{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Game) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Game) ProtoMessage() {}

func (x *Game) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Game.ProtoReflect.Descriptor instead.
func (*Game) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{26}
}

func (x *Game) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *Game) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Game) GetExchangeActivityID() []uint32 {
	if x != nil {
		return x.ExchangeActivityID
	}
	return nil
}

func (x *Game) GetPrizeRewardIds() []uint32 {
	if x != nil {
		return x.PrizeRewardIds
	}
	return nil
}

func (x *Game) GetAssociatedAppID() string {
	if x != nil {
		return x.AssociatedAppID
	}
	return ""
}

func (x *Game) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Game) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *Game) GetSyncedBy() string {
	if x != nil {
		return x.SyncedBy
	}
	return ""
}

func (x *Game) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Game) GetUpdateTime() uint32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Game) GetSyncTime() uint32 {
	if x != nil {
		return x.SyncTime
	}
	return 0
}

func (x *Game) GetUniAppId() string {
	if x != nil {
		return x.UniAppId
	}
	return ""
}

type GameSyncReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Operator string `protobuf:"bytes,2,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	Plat     uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GameSyncReq) Reset() {
	*x = GameSyncReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameSyncReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameSyncReq) ProtoMessage() {}

func (x *GameSyncReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameSyncReq.ProtoReflect.Descriptor instead.
func (*GameSyncReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{27}
}

func (x *GameSyncReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameSyncReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GameSyncReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameSyncRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
}

func (x *GameSyncRsp) Reset() {
	*x = GameSyncRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameSyncRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameSyncRsp) ProtoMessage() {}

func (x *GameSyncRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameSyncRsp.ProtoReflect.Descriptor instead.
func (*GameSyncRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{28}
}

func (x *GameSyncRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

type GameUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID          string   `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name           string   `protobuf:"bytes,2,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	PrizeRewardIds []uint32 `protobuf:"varint,3,rep,packed,name=PrizeRewardIds,json=prizeRewardIds,proto3" json:"PrizeRewardIds,omitempty"`
	Operator       string   `protobuf:"bytes,4,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	Plat           uint32   `protobuf:"varint,5,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GameUpdateReq) Reset() {
	*x = GameUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameUpdateReq) ProtoMessage() {}

func (x *GameUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameUpdateReq.ProtoReflect.Descriptor instead.
func (*GameUpdateReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{29}
}

func (x *GameUpdateReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GameUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GameUpdateReq) GetPrizeRewardIds() []uint32 {
	if x != nil {
		return x.PrizeRewardIds
	}
	return nil
}

func (x *GameUpdateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GameUpdateReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GameUpdateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	AppID     string     `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
}

func (x *GameUpdateRsp) Reset() {
	*x = GameUpdateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameUpdateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameUpdateRsp) ProtoMessage() {}

func (x *GameUpdateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameUpdateRsp.ProtoReflect.Descriptor instead.
func (*GameUpdateRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{30}
}

func (x *GameUpdateRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GameUpdateRsp) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

type PropDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plat   uint32 `protobuf:"varint,1,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	PropId uint32 `protobuf:"varint,2,opt,name=PropId,json=propId,proto3" json:"PropId,omitempty"`
}

func (x *PropDetailReq) Reset() {
	*x = PropDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropDetailReq) ProtoMessage() {}

func (x *PropDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropDetailReq.ProtoReflect.Descriptor instead.
func (*PropDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{31}
}

func (x *PropDetailReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *PropDetailReq) GetPropId() uint32 {
	if x != nil {
		return x.PropId
	}
	return 0
}

type PropDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PropName  string `protobuf:"bytes,1,opt,name=PropName,json=propName,proto3" json:"PropName,omitempty"`
	PropIcon  string `protobuf:"bytes,2,opt,name=PropIcon,json=propIcon,proto3" json:"PropIcon,omitempty"`
	PropPrice uint32 `protobuf:"varint,3,opt,name=PropPrice,json=propPrice,proto3" json:"PropPrice,omitempty"`
}

func (x *PropDetailRsp) Reset() {
	*x = PropDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PropDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PropDetailRsp) ProtoMessage() {}

func (x *PropDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PropDetailRsp.ProtoReflect.Descriptor instead.
func (*PropDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{32}
}

func (x *PropDetailRsp) GetPropName() string {
	if x != nil {
		return x.PropName
	}
	return ""
}

func (x *PropDetailRsp) GetPropIcon() string {
	if x != nil {
		return x.PropIcon
	}
	return ""
}

func (x *PropDetailRsp) GetPropPrice() uint32 {
	if x != nil {
		return x.PropPrice
	}
	return 0
}

type GiftDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Plat   uint32 `protobuf:"varint,1,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	GiftId uint32 `protobuf:"varint,2,opt,name=GiftId,json=giftId,proto3" json:"GiftId,omitempty"`
}

func (x *GiftDetailReq) Reset() {
	*x = GiftDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftDetailReq) ProtoMessage() {}

func (x *GiftDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftDetailReq.ProtoReflect.Descriptor instead.
func (*GiftDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{33}
}

func (x *GiftDetailReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *GiftDetailReq) GetGiftId() uint32 {
	if x != nil {
		return x.GiftId
	}
	return 0
}

type GiftDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GiftName  string `protobuf:"bytes,1,opt,name=GiftName,json=giftName,proto3" json:"GiftName,omitempty"`
	GiftIcon  string `protobuf:"bytes,2,opt,name=GiftIcon,json=giftIcon,proto3" json:"GiftIcon,omitempty"`
	GiftPrice uint32 `protobuf:"varint,3,opt,name=GiftPrice,json=giftPrice,proto3" json:"GiftPrice,omitempty"`
}

func (x *GiftDetailRsp) Reset() {
	*x = GiftDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftDetailRsp) ProtoMessage() {}

func (x *GiftDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftDetailRsp.ProtoReflect.Descriptor instead.
func (*GiftDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{34}
}

func (x *GiftDetailRsp) GetGiftName() string {
	if x != nil {
		return x.GiftName
	}
	return ""
}

func (x *GiftDetailRsp) GetGiftIcon() string {
	if x != nil {
		return x.GiftIcon
	}
	return ""
}

func (x *GiftDetailRsp) GetGiftPrice() uint32 {
	if x != nil {
		return x.GiftPrice
	}
	return 0
}

type GamePackageCopyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string `protobuf:"bytes,1,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat     uint32 `protobuf:"varint,2,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	ID       uint32 `protobuf:"varint,3,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	Operator string `protobuf:"bytes,4,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
}

func (x *GamePackageCopyReq) Reset() {
	*x = GamePackageCopyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackageCopyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackageCopyReq) ProtoMessage() {}

func (x *GamePackageCopyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackageCopyReq.ProtoReflect.Descriptor instead.
func (*GamePackageCopyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{35}
}

func (x *GamePackageCopyReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GamePackageCopyReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *GamePackageCopyReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GamePackageCopyReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type GamePackageCopyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
}

func (x *GamePackageCopyRsp) Reset() {
	*x = GamePackageCopyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackageCopyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackageCopyRsp) ProtoMessage() {}

func (x *GamePackageCopyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackageCopyRsp.ProtoReflect.Descriptor instead.
func (*GamePackageCopyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{36}
}

func (x *GamePackageCopyRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

type GamePackageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID    uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat  uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
}

func (x *GamePackageListReq) Reset() {
	*x = GamePackageListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackageListReq) ProtoMessage() {}

func (x *GamePackageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackageListReq.ProtoReflect.Descriptor instead.
func (*GamePackageListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{37}
}

func (x *GamePackageListReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GamePackageListReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GamePackageListReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

type GamePackageListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp     `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	Packages  []*GamePackage `protobuf:"bytes,2,rep,name=packages,proto3" json:"packages,omitempty"`
}

func (x *GamePackageListRsp) Reset() {
	*x = GamePackageListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackageListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackageListRsp) ProtoMessage() {}

func (x *GamePackageListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackageListRsp.ProtoReflect.Descriptor instead.
func (*GamePackageListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{38}
}

func (x *GamePackageListRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GamePackageListRsp) GetPackages() []*GamePackage {
	if x != nil {
		return x.Packages
	}
	return nil
}

type GamePackage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID             string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Name              string `protobuf:"bytes,3,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Icon              string `protobuf:"bytes,4,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	Status            uint32 `protobuf:"varint,5,opt,name=Status,json=status,proto3" json:"Status,omitempty"`
	PayType           uint32 `protobuf:"varint,6,opt,name=PayType,json=payType,proto3" json:"PayType,omitempty"`
	PayPropsUnitPrize uint32 `protobuf:"varint,7,opt,name=PayPropsUnitPrize,json=payPropsUnitPrize,proto3" json:"PayPropsUnitPrize,omitempty"`
	PayPropsID        uint32 `protobuf:"varint,8,opt,name=PayPropsID,json=payPropsId,proto3" json:"PayPropsID,omitempty"`
	ExchangeRate      uint32 `protobuf:"varint,9,opt,name=ExchangeRate,json=exchangeRate,proto3" json:"ExchangeRate,omitempty"`
	// 平台BusinessId
	PlatBusinessID uint32 `protobuf:"varint,10,opt,name=PlatBusinessID,json=platBusinessId,proto3" json:"PlatBusinessID,omitempty"`
	RevenueType    uint32 `protobuf:"varint,11,opt,name=RevenueType,json=revenueType,proto3" json:"RevenueType,omitempty"`
	RewardID       uint32 `protobuf:"varint,12,opt,name=RewardID,json=rewardId,proto3" json:"RewardID,omitempty"`
	CreatedBy      string `protobuf:"bytes,13,opt,name=CreatedBy,json=createdBy,proto3" json:"CreatedBy,omitempty"`
	UpdatedBy      string `protobuf:"bytes,14,opt,name=UpdatedBy,json=updatedBy,proto3" json:"UpdatedBy,omitempty"`
	CreateTime     uint32 `protobuf:"varint,15,opt,name=CreateTime,json=createTime,proto3" json:"CreateTime,omitempty"`
	UpdateTime     uint32 `protobuf:"varint,16,opt,name=UpdateTime,json=updateTime,proto3" json:"UpdateTime,omitempty"`
	UniID          uint32 `protobuf:"varint,17,opt,name=UniID,json=uniId,proto3" json:"UniID,omitempty"`
	ModalType      uint32 `protobuf:"varint,18,opt,name=ModalType,json=modalType,proto3" json:"ModalType,omitempty"` // 弹窗类型 1 旧版 2 低风险 3 高风险
	Description    string `protobuf:"bytes,19,opt,name=Description,json=description,proto3" json:"Description,omitempty"`
	SendToUgc      bool   `protobuf:"varint,20,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
}

func (x *GamePackage) Reset() {
	*x = GamePackage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackage) ProtoMessage() {}

func (x *GamePackage) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackage.ProtoReflect.Descriptor instead.
func (*GamePackage) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{39}
}

func (x *GamePackage) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GamePackage) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GamePackage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GamePackage) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GamePackage) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GamePackage) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *GamePackage) GetPayPropsUnitPrize() uint32 {
	if x != nil {
		return x.PayPropsUnitPrize
	}
	return 0
}

func (x *GamePackage) GetPayPropsID() uint32 {
	if x != nil {
		return x.PayPropsID
	}
	return 0
}

func (x *GamePackage) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *GamePackage) GetPlatBusinessID() uint32 {
	if x != nil {
		return x.PlatBusinessID
	}
	return 0
}

func (x *GamePackage) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *GamePackage) GetRewardID() uint32 {
	if x != nil {
		return x.RewardID
	}
	return 0
}

func (x *GamePackage) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *GamePackage) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *GamePackage) GetCreateTime() uint32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *GamePackage) GetUpdateTime() uint32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *GamePackage) GetUniID() uint32 {
	if x != nil {
		return x.UniID
	}
	return 0
}

func (x *GamePackage) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *GamePackage) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GamePackage) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

type GamePackageUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                uint32 `protobuf:"varint,1,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
	AppID             string `protobuf:"bytes,2,opt,name=AppID,json=appId,proto3" json:"AppID,omitempty"`
	Plat              uint32 `protobuf:"varint,3,opt,name=Plat,json=plat,proto3" json:"Plat,omitempty"`
	Name              string `protobuf:"bytes,4,opt,name=Name,json=name,proto3" json:"Name,omitempty"`
	Icon              string `protobuf:"bytes,5,opt,name=Icon,json=icon,proto3" json:"Icon,omitempty"`
	PayType           uint32 `protobuf:"varint,6,opt,name=PayType,json=payType,proto3" json:"PayType,omitempty"`
	PayPropsUnitPrize uint32 `protobuf:"varint,7,opt,name=PayPropsUnitPrize,json=payPropsUnitPrize,proto3" json:"PayPropsUnitPrize,omitempty"`
	PayPropsID        uint32 `protobuf:"varint,8,opt,name=PayPropsID,json=payPropsId,proto3" json:"PayPropsID,omitempty"`
	ExchangeRate      uint32 `protobuf:"varint,9,opt,name=ExchangeRate,json=exchangeRate,proto3" json:"ExchangeRate,omitempty"`
	RevenueType       uint32 `protobuf:"varint,10,opt,name=RevenueType,json=revenueType,proto3" json:"RevenueType,omitempty"`
	// 平台BusinessId
	PlatBusinessID uint32 `protobuf:"varint,11,opt,name=PlatBusinessID,json=platBusinessId,proto3" json:"PlatBusinessID,omitempty"`
	RewardID       uint32 `protobuf:"varint,12,opt,name=RewardID,json=rewardId,proto3" json:"RewardID,omitempty"`
	Operator       string `protobuf:"bytes,13,opt,name=Operator,json=operator,proto3" json:"Operator,omitempty"`
	UniID          uint32 `protobuf:"varint,14,opt,name=UniID,json=uniId,proto3" json:"UniID,omitempty"`
	ModalType      uint32 `protobuf:"varint,15,opt,name=ModalType,json=modalType,proto3" json:"ModalType,omitempty"` // 弹窗类型 1 旧版 2 低风险 3 高风险
	Description    string `protobuf:"bytes,16,opt,name=Description,json=description,proto3" json:"Description,omitempty"`
	SendToUgc      bool   `protobuf:"varint,17,opt,name=sendToUgc,proto3" json:"sendToUgc,omitempty"`
}

func (x *GamePackageUpdateReq) Reset() {
	*x = GamePackageUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackageUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackageUpdateReq) ProtoMessage() {}

func (x *GamePackageUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackageUpdateReq.ProtoReflect.Descriptor instead.
func (*GamePackageUpdateReq) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{40}
}

func (x *GamePackageUpdateReq) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *GamePackageUpdateReq) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *GamePackageUpdateReq) GetPlat() uint32 {
	if x != nil {
		return x.Plat
	}
	return 0
}

func (x *GamePackageUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GamePackageUpdateReq) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GamePackageUpdateReq) GetPayType() uint32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

func (x *GamePackageUpdateReq) GetPayPropsUnitPrize() uint32 {
	if x != nil {
		return x.PayPropsUnitPrize
	}
	return 0
}

func (x *GamePackageUpdateReq) GetPayPropsID() uint32 {
	if x != nil {
		return x.PayPropsID
	}
	return 0
}

func (x *GamePackageUpdateReq) GetExchangeRate() uint32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *GamePackageUpdateReq) GetRevenueType() uint32 {
	if x != nil {
		return x.RevenueType
	}
	return 0
}

func (x *GamePackageUpdateReq) GetPlatBusinessID() uint32 {
	if x != nil {
		return x.PlatBusinessID
	}
	return 0
}

func (x *GamePackageUpdateReq) GetRewardID() uint32 {
	if x != nil {
		return x.RewardID
	}
	return 0
}

func (x *GamePackageUpdateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *GamePackageUpdateReq) GetUniID() uint32 {
	if x != nil {
		return x.UniID
	}
	return 0
}

func (x *GamePackageUpdateReq) GetModalType() uint32 {
	if x != nil {
		return x.ModalType
	}
	return 0
}

func (x *GamePackageUpdateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GamePackageUpdateReq) GetSendToUgc() bool {
	if x != nil {
		return x.SendToUgc
	}
	return false
}

type GamePackageUpdateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommonRsp *CommonRsp `protobuf:"bytes,1,opt,name=commonRsp,proto3" json:"commonRsp,omitempty"`
	ID        uint32     `protobuf:"varint,2,opt,name=ID,json=id,proto3" json:"ID,omitempty"`
}

func (x *GamePackageUpdateRsp) Reset() {
	*x = GamePackageUpdateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GamePackageUpdateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GamePackageUpdateRsp) ProtoMessage() {}

func (x *GamePackageUpdateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GamePackageUpdateRsp.ProtoReflect.Descriptor instead.
func (*GamePackageUpdateRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP(), []int{41}
}

func (x *GamePackageUpdateRsp) GetCommonRsp() *CommonRsp {
	if x != nil {
		return x.CommonRsp
	}
	return nil
}

func (x *GamePackageUpdateRsp) GetID() uint32 {
	if x != nil {
		return x.ID
	}
	return 0
}

var File_pb_game_sync_admin_game_sync_admin_proto protoreflect.FileDescriptor

var file_pb_game_sync_admin_game_sync_admin_proto_rawDesc = []byte{
	0x0a, 0x28, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x39, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x6f, 0x0a, 0x17, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x22, 0x52, 0x0a, 0x17, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73, 0x70, 0x12,
	0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x53, 0x0a, 0x17, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0xc2, 0x03,
	0x0a, 0x10, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x42, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x74, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72,
	0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55,
	0x72, 0x6c, 0x22, 0x94, 0x01, 0x0a, 0x17, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0a, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0a, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x22, 0xdb, 0x02, 0x0a, 0x19, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x6e, 0x69, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x6e, 0x69,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x19, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x53, 0x0a,
	0x0b, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c,
	0x61, 0x74, 0x22, 0x46, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73,
	0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x6b, 0x0a, 0x13, 0x47, 0x61,
	0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x4e, 0x0a, 0x13, 0x47, 0x61, 0x6d, 0x65, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x4f, 0x0a, 0x13, 0x47, 0x61, 0x6d, 0x65, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x13, 0x47, 0x61, 0x6d,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0a, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x61, 0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0a, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x22, 0xc4, 0x06, 0x0a, 0x0c, 0x47, 0x61, 0x6d, 0x65,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x50,
	0x72, 0x6f, 0x70, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x11, 0x70, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x55, 0x6e, 0x69,
	0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x79, 0x50, 0x72, 0x6f,
	0x70, 0x73, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x50,
	0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x50, 0x6c,
	0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x44, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1e,
	0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67,
	0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x44, 0x65, 0x73, 0x63, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x44, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61,
	0x79, 0x47, 0x65, 0x61, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x47, 0x65, 0x61, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72,
	0x6c, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55,
	0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x12, 0x20, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xc5,
	0x05, 0x0a, 0x15, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c,
	0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73,
	0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x11, 0x70, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69,
	0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x49, 0x44,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61,
	0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x76,
	0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x74,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x6e, 0x69, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x6e,
	0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x44,
	0x65, 0x73, 0x63, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x44, 0x65,
	0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x47, 0x65, 0x61, 0x72, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x47, 0x65, 0x61, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f,
	0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f,
	0x55, 0x67, 0x63, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54,
	0x6f, 0x55, 0x67, 0x63, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x60, 0x0a, 0x15, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x54, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x4b,
	0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x52,
	0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x4c, 0x0a, 0x10, 0x47,
	0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x10, 0x47, 0x61,
	0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x06, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x22, 0xa7, 0x03, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x61,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1e, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x55, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x75, 0x6e, 0x69, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x9a, 0x02, 0x0a, 0x12, 0x47,
	0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x55,
	0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x22, 0x5d, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a,
	0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4d, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x70, 0x70, 0x49, 0x44, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x70, 0x6c, 0x61, 0x74, 0x22, 0x72, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x2a, 0x0a,
	0x05, 0x47, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x52, 0x05, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x82, 0x03, 0x0a, 0x04, 0x47, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x12, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1c, 0x0a, 0x09,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x79,
	0x6e, 0x63, 0x65, 0x64, 0x42, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x79,
	0x6e, 0x63, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x41, 0x70, 0x70, 0x49, 0x64, 0x22, 0x53,
	0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70,
	0x6c, 0x61, 0x74, 0x22, 0x46, 0x0a, 0x0b, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x22, 0x91, 0x01, 0x0a, 0x0d,
	0x47, 0x61, 0x6d, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x50, 0x72, 0x69, 0x7a, 0x65,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x0e, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x50,
	0x6c, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x22,
	0x5e, 0x0a, 0x0d, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22,
	0x3b, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x6c, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x70, 0x49, 0x64, 0x22, 0x65, 0x0a, 0x0d,
	0x50, 0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x72, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f,
	0x70, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x70, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x70, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x70, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x22, 0x3b, 0x0a, 0x0d, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x69, 0x66, 0x74,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64,
	0x22, 0x65, 0x0a, 0x0d, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73,
	0x70, 0x12, 0x1a, 0x0a, 0x08, 0x47, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x69, 0x66, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x47, 0x69, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x69, 0x66, 0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x69, 0x66,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x67, 0x69,
	0x66, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x6a, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x22, 0x4d, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x22, 0x4e, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6c,
	0x61, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x37, 0x0a, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x22, 0xd5, 0x04, 0x0a, 0x0b,
	0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x41,
	0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x50,
	0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x70, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73,
	0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x79,
	0x50, 0x72, 0x6f, 0x70, 0x73, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70,
	0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x50, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x72, 0x65, 0x76, 0x65,
	0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x55, 0x6e, 0x69, 0x49, 0x44, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x75, 0x6e, 0x69, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55,
	0x67, 0x63, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f,
	0x55, 0x67, 0x63, 0x22, 0xfa, 0x03, 0x0a, 0x14, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x70, 0x6c, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x50, 0x61, 0x79, 0x50,
	0x72, 0x6f, 0x70, 0x73, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x11, 0x70, 0x61, 0x79, 0x50, 0x72, 0x6f, 0x70, 0x73, 0x55, 0x6e, 0x69,
	0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x61, 0x79, 0x50, 0x72, 0x6f,
	0x70, 0x73, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x79, 0x50,
	0x72, 0x6f, 0x70, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x50, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x6c, 0x61, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x44,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x55, 0x6e, 0x69, 0x49, 0x44, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x75, 0x6e, 0x69,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x55, 0x67, 0x63,
	0x22, 0x5f, 0x0a, 0x14, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x52, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x73,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x32, 0x8b, 0x13, 0x0a, 0x0f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x9c, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x27,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73, 0x70,
	0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x2f,
	0x63, 0x6f, 0x70, 0x79, 0x12, 0x9c, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22,
	0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0xa4, 0x01, 0x0a, 0x16, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x29,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x73, 0x70, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22,
	0x29, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x63, 0x79, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x08, 0x47, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x70, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73, 0x70,
	0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x63, 0x6f, 0x70, 0x79, 0x12, 0x8c, 0x01, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x23, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65,
	0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43,
	0x6f, 0x70, 0x79, 0x52, 0x73, 0x70, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x3a, 0x01,
	0x2a, 0x22, 0x23, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x2f, 0x63, 0x6f, 0x70, 0x79, 0x12, 0x8c, 0x01, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d,
	0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x3a, 0x01, 0x2a,
	0x22, 0x23, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x25, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e,
	0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x6b, 0x0a, 0x08,
	0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x6b, 0x0a, 0x08, 0x47, 0x61, 0x6d,
	0x65, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x73, 0x70, 0x22,
	0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x73, 0x0a, 0x0a, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x73, 0x0a, 0x0a, 0x50,
	0x72, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a,
	0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x73, 0x0a, 0x0a, 0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x69, 0x66, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x22, 0x27, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x69, 0x66, 0x74, 0x2f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x80, 0x01, 0x0a, 0x0d, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x73, 0x70, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x70, 0x79, 0x12, 0x80, 0x01, 0x0a, 0x0d, 0x47, 0x61, 0x6d,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61,
	0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x2b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x0f,
	0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a,
	0x01, 0x2a, 0x22, 0x22, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x2f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x52,
	0x73, 0x70, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x63, 0x6f, 0x70,
	0x79, 0x12, 0x88, 0x01, 0x0a, 0x0f, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x22, 0x2d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x90, 0x01, 0x0a,
	0x11, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x2f,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x47, 0x5a, 0x45, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_sync_admin_game_sync_admin_proto_rawDescOnce sync.Once
	file_pb_game_sync_admin_game_sync_admin_proto_rawDescData = file_pb_game_sync_admin_game_sync_admin_proto_rawDesc
)

func file_pb_game_sync_admin_game_sync_admin_proto_rawDescGZIP() []byte {
	file_pb_game_sync_admin_game_sync_admin_proto_rawDescOnce.Do(func() {
		file_pb_game_sync_admin_game_sync_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_sync_admin_game_sync_admin_proto_rawDescData)
	})
	return file_pb_game_sync_admin_game_sync_admin_proto_rawDescData
}

var file_pb_game_sync_admin_game_sync_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_pb_game_sync_admin_game_sync_admin_proto_goTypes = []interface{}{
	(*CommonRsp)(nil),                 // 0: component.game.CommonRsp
	(*ExchangeCurrencyCopyReq)(nil),   // 1: component.game.ExchangeCurrencyCopyReq
	(*ExchangeCurrencyCopyRsp)(nil),   // 2: component.game.ExchangeCurrencyCopyRsp
	(*ExchangeCurrencyListReq)(nil),   // 3: component.game.ExchangeCurrencyListReq
	(*ExchangeCurrency)(nil),          // 4: component.game.ExchangeCurrency
	(*ExchangeCurrencyListRsp)(nil),   // 5: component.game.ExchangeCurrencyListRsp
	(*ExchangeCurrencyUpdateReq)(nil), // 6: component.game.ExchangeCurrencyUpdateReq
	(*ExchangeCurrencyUpdateRsp)(nil), // 7: component.game.ExchangeCurrencyUpdateRsp
	(*GameCopyReq)(nil),               // 8: component.game.GameCopyReq
	(*GameCopyRsp)(nil),               // 9: component.game.GameCopyRsp
	(*GameCurrencyCopyReq)(nil),       // 10: component.game.GameCurrencyCopyReq
	(*GameCurrencyCopyRsp)(nil),       // 11: component.game.GameCurrencyCopyRsp
	(*GameCurrencyListReq)(nil),       // 12: component.game.GameCurrencyListReq
	(*GameCurrencyListRsp)(nil),       // 13: component.game.GameCurrencyListRsp
	(*GameCurrency)(nil),              // 14: component.game.GameCurrency
	(*GameCurrencyUpdateReq)(nil),     // 15: component.game.GameCurrencyUpdateReq
	(*GameCurrencyUpdateRsp)(nil),     // 16: component.game.GameCurrencyUpdateRsp
	(*GameAssetCopyReq)(nil),          // 17: component.game.GameAssetCopyReq
	(*GameAssetCopyRsp)(nil),          // 18: component.game.GameAssetCopyRsp
	(*GameAssetListReq)(nil),          // 19: component.game.GameAssetListReq
	(*GameAssetListRsp)(nil),          // 20: component.game.GameAssetListRsp
	(*GameAssetConfig)(nil),           // 21: component.game.GameAssetConfig
	(*GameAssetUpdateReq)(nil),        // 22: component.game.GameAssetUpdateReq
	(*GameAssetUpdateRsp)(nil),        // 23: component.game.GameAssetUpdateRsp
	(*GameListReq)(nil),               // 24: component.game.GameListReq
	(*GameListRsp)(nil),               // 25: component.game.GameListRsp
	(*Game)(nil),                      // 26: component.game.Game
	(*GameSyncReq)(nil),               // 27: component.game.GameSyncReq
	(*GameSyncRsp)(nil),               // 28: component.game.GameSyncRsp
	(*GameUpdateReq)(nil),             // 29: component.game.GameUpdateReq
	(*GameUpdateRsp)(nil),             // 30: component.game.GameUpdateRsp
	(*PropDetailReq)(nil),             // 31: component.game.PropDetailReq
	(*PropDetailRsp)(nil),             // 32: component.game.PropDetailRsp
	(*GiftDetailReq)(nil),             // 33: component.game.GiftDetailReq
	(*GiftDetailRsp)(nil),             // 34: component.game.GiftDetailRsp
	(*GamePackageCopyReq)(nil),        // 35: component.game.GamePackageCopyReq
	(*GamePackageCopyRsp)(nil),        // 36: component.game.GamePackageCopyRsp
	(*GamePackageListReq)(nil),        // 37: component.game.GamePackageListReq
	(*GamePackageListRsp)(nil),        // 38: component.game.GamePackageListRsp
	(*GamePackage)(nil),               // 39: component.game.GamePackage
	(*GamePackageUpdateReq)(nil),      // 40: component.game.GamePackageUpdateReq
	(*GamePackageUpdateRsp)(nil),      // 41: component.game.GamePackageUpdateRsp
}
var file_pb_game_sync_admin_game_sync_admin_proto_depIdxs = []int32{
	0,  // 0: component.game.ExchangeCurrencyCopyRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 1: component.game.ExchangeCurrencyListRsp.commonRsp:type_name -> component.game.CommonRsp
	4,  // 2: component.game.ExchangeCurrencyListRsp.Currencies:type_name -> component.game.ExchangeCurrency
	0,  // 3: component.game.ExchangeCurrencyUpdateRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 4: component.game.GameCopyRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 5: component.game.GameCurrencyCopyRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 6: component.game.GameCurrencyListRsp.commonRsp:type_name -> component.game.CommonRsp
	14, // 7: component.game.GameCurrencyListRsp.Currencies:type_name -> component.game.GameCurrency
	0,  // 8: component.game.GameCurrencyUpdateRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 9: component.game.GameAssetCopyRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 10: component.game.GameAssetListRsp.commonRsp:type_name -> component.game.CommonRsp
	21, // 11: component.game.GameAssetListRsp.Assets:type_name -> component.game.GameAssetConfig
	0,  // 12: component.game.GameAssetUpdateRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 13: component.game.GameListRsp.commonRsp:type_name -> component.game.CommonRsp
	26, // 14: component.game.GameListRsp.Games:type_name -> component.game.Game
	0,  // 15: component.game.GameSyncRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 16: component.game.GameUpdateRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 17: component.game.GamePackageCopyRsp.commonRsp:type_name -> component.game.CommonRsp
	0,  // 18: component.game.GamePackageListRsp.commonRsp:type_name -> component.game.CommonRsp
	39, // 19: component.game.GamePackageListRsp.packages:type_name -> component.game.GamePackage
	0,  // 20: component.game.GamePackageUpdateRsp.commonRsp:type_name -> component.game.CommonRsp
	1,  // 21: component.game.game_sync_admin.ExchangeCurrencyCopy:input_type -> component.game.ExchangeCurrencyCopyReq
	3,  // 22: component.game.game_sync_admin.ExchangeCurrencyList:input_type -> component.game.ExchangeCurrencyListReq
	6,  // 23: component.game.game_sync_admin.ExchangeCurrencyUpdate:input_type -> component.game.ExchangeCurrencyUpdateReq
	8,  // 24: component.game.game_sync_admin.GameCopy:input_type -> component.game.GameCopyReq
	10, // 25: component.game.game_sync_admin.GameCurrencyCopy:input_type -> component.game.GameCurrencyCopyReq
	12, // 26: component.game.game_sync_admin.GameCurrencyList:input_type -> component.game.GameCurrencyListReq
	15, // 27: component.game.game_sync_admin.GameCurrencyUpdate:input_type -> component.game.GameCurrencyUpdateReq
	24, // 28: component.game.game_sync_admin.GameList:input_type -> component.game.GameListReq
	27, // 29: component.game.game_sync_admin.GameSync:input_type -> component.game.GameSyncReq
	29, // 30: component.game.game_sync_admin.GameUpdate:input_type -> component.game.GameUpdateReq
	31, // 31: component.game.game_sync_admin.PropDetail:input_type -> component.game.PropDetailReq
	33, // 32: component.game.game_sync_admin.GiftDetail:input_type -> component.game.GiftDetailReq
	17, // 33: component.game.game_sync_admin.GameAssetCopy:input_type -> component.game.GameAssetCopyReq
	19, // 34: component.game.game_sync_admin.GameAssetList:input_type -> component.game.GameAssetListReq
	22, // 35: component.game.game_sync_admin.GameAssetUpdate:input_type -> component.game.GameAssetUpdateReq
	35, // 36: component.game.game_sync_admin.GamePackageCopy:input_type -> component.game.GamePackageCopyReq
	37, // 37: component.game.game_sync_admin.GamePackageList:input_type -> component.game.GamePackageListReq
	40, // 38: component.game.game_sync_admin.GamePackageUpdate:input_type -> component.game.GamePackageUpdateReq
	2,  // 39: component.game.game_sync_admin.ExchangeCurrencyCopy:output_type -> component.game.ExchangeCurrencyCopyRsp
	5,  // 40: component.game.game_sync_admin.ExchangeCurrencyList:output_type -> component.game.ExchangeCurrencyListRsp
	7,  // 41: component.game.game_sync_admin.ExchangeCurrencyUpdate:output_type -> component.game.ExchangeCurrencyUpdateRsp
	9,  // 42: component.game.game_sync_admin.GameCopy:output_type -> component.game.GameCopyRsp
	11, // 43: component.game.game_sync_admin.GameCurrencyCopy:output_type -> component.game.GameCurrencyCopyRsp
	13, // 44: component.game.game_sync_admin.GameCurrencyList:output_type -> component.game.GameCurrencyListRsp
	16, // 45: component.game.game_sync_admin.GameCurrencyUpdate:output_type -> component.game.GameCurrencyUpdateRsp
	25, // 46: component.game.game_sync_admin.GameList:output_type -> component.game.GameListRsp
	28, // 47: component.game.game_sync_admin.GameSync:output_type -> component.game.GameSyncRsp
	30, // 48: component.game.game_sync_admin.GameUpdate:output_type -> component.game.GameUpdateRsp
	32, // 49: component.game.game_sync_admin.PropDetail:output_type -> component.game.PropDetailRsp
	34, // 50: component.game.game_sync_admin.GiftDetail:output_type -> component.game.GiftDetailRsp
	18, // 51: component.game.game_sync_admin.GameAssetCopy:output_type -> component.game.GameAssetCopyRsp
	20, // 52: component.game.game_sync_admin.GameAssetList:output_type -> component.game.GameAssetListRsp
	23, // 53: component.game.game_sync_admin.GameAssetUpdate:output_type -> component.game.GameAssetUpdateRsp
	36, // 54: component.game.game_sync_admin.GamePackageCopy:output_type -> component.game.GamePackageCopyRsp
	38, // 55: component.game.game_sync_admin.GamePackageList:output_type -> component.game.GamePackageListRsp
	41, // 56: component.game.game_sync_admin.GamePackageUpdate:output_type -> component.game.GamePackageUpdateRsp
	39, // [39:57] is the sub-list for method output_type
	21, // [21:39] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_pb_game_sync_admin_game_sync_admin_proto_init() }
func file_pb_game_sync_admin_game_sync_admin_proto_init() {
	if File_pb_game_sync_admin_game_sync_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyCopyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyCopyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangeCurrencyUpdateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCopyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCopyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrencyCopyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrencyCopyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrencyListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrencyListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrencyUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameCurrencyUpdateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetCopyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetCopyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameAssetUpdateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Game); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameSyncReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameSyncRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameUpdateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PropDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackageCopyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackageCopyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackageListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackageListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackageUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_sync_admin_game_sync_admin_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GamePackageUpdateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_sync_admin_game_sync_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   42,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_sync_admin_game_sync_admin_proto_goTypes,
		DependencyIndexes: file_pb_game_sync_admin_game_sync_admin_proto_depIdxs,
		MessageInfos:      file_pb_game_sync_admin_game_sync_admin_proto_msgTypes,
	}.Build()
	File_pb_game_sync_admin_game_sync_admin_proto = out.File
	file_pb_game_sync_admin_game_sync_admin_proto_rawDesc = nil
	file_pb_game_sync_admin_game_sync_admin_proto_goTypes = nil
	file_pb_game_sync_admin_game_sync_admin_proto_depIdxs = nil
}
