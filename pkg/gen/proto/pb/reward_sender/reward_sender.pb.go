// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/reward_sender/reward_sender.proto

package reward_sender

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetRewardInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reward_ids 奖励ID
	RewardIds []int64 `protobuf:"varint,1,rep,packed,name=reward_ids,json=rewardIds,proto3" json:"reward_ids,omitempty"`
}

func (x *GetRewardInfoReq) Reset() {
	*x = GetRewardInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardInfoReq) ProtoMessage() {}

func (x *GetRewardInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardInfoReq.ProtoReflect.Descriptor instead.
func (*GetRewardInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{0}
}

func (x *GetRewardInfoReq) GetRewardIds() []int64 {
	if x != nil {
		return x.RewardIds
	}
	return nil
}

type GetRewardInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardInfo map[int64]*RewardConfig `protobuf:"bytes,1,rep,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetRewardInfoRsp) Reset() {
	*x = GetRewardInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardInfoRsp) ProtoMessage() {}

func (x *GetRewardInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRewardInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{1}
}

func (x *GetRewardInfoRsp) GetRewardInfo() map[int64]*RewardConfig {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 奖励ID
	RewardId int64 `protobuf:"varint,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	// 发送数量
	Num int64 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{2}
}

func (x *Reward) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *Reward) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type Roi struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 轮次ID 业务方有轮次ID的需要透传
	RoundId string `protobuf:"bytes,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
}

func (x *Roi) Reset() {
	*x = Roi{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Roi) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Roi) ProtoMessage() {}

func (x *Roi) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Roi.ProtoReflect.Descriptor instead.
func (*Roi) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{3}
}

func (x *Roi) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

type ConsumeAssetItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	Amount  uint32 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *ConsumeAssetItem) Reset() {
	*x = ConsumeAssetItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumeAssetItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeAssetItem) ProtoMessage() {}

func (x *ConsumeAssetItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeAssetItem.ProtoReflect.Descriptor instead.
func (*ConsumeAssetItem) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{4}
}

func (x *ConsumeAssetItem) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *ConsumeAssetItem) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type SendRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` //根据idType解析是openid或puid(端内uid)
	// 奖励, 不超过10个
	Rewards []*Reward `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`
	// 订单ID
	BillNo string `protobuf:"bytes,4,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 聚合私信
	AggrMail bool `protobuf:"varint,5,opt,name=aggr_mail,json=aggrMail,proto3" json:"aggr_mail,omitempty"`
	// 调用方名称
	ProgramName string `protobuf:"bytes,6,opt,name=program_name,json=programName,proto3" json:"program_name,omitempty"`
	// 发奖时间 秒（注意不是当前时间，重试不能变化）
	SendTs int64 `protobuf:"varint,7,opt,name=send_ts,json=sendTs,proto3" json:"send_ts,omitempty"`
	// roi roi监控需要的数据
	Roi *Roi `protobuf:"bytes,8,opt,name=roi,proto3" json:"roi,omitempty"`
	// 消耗的资产信息(消耗了consume_item, 发放了rewards)
	ConsumeItem *ConsumeAssetItem `protobuf:"bytes,9,opt,name=consume_item,json=consumeItem,proto3" json:"consume_item,omitempty"`
	// userid类型
	IdType int32 `protobuf:"varint,10,opt,name=id_type,json=idType,proto3" json:"id_type,omitempty"` //UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin
	// 通用透传字段
	MapExt map[string]string `protobuf:"bytes,11,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SendRewardReq) Reset() {
	*x = SendRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRewardReq) ProtoMessage() {}

func (x *SendRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRewardReq.ProtoReflect.Descriptor instead.
func (*SendRewardReq) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{5}
}

func (x *SendRewardReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendRewardReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendRewardReq) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *SendRewardReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *SendRewardReq) GetAggrMail() bool {
	if x != nil {
		return x.AggrMail
	}
	return false
}

func (x *SendRewardReq) GetProgramName() string {
	if x != nil {
		return x.ProgramName
	}
	return ""
}

func (x *SendRewardReq) GetSendTs() int64 {
	if x != nil {
		return x.SendTs
	}
	return 0
}

func (x *SendRewardReq) GetRoi() *Roi {
	if x != nil {
		return x.Roi
	}
	return nil
}

func (x *SendRewardReq) GetConsumeItem() *ConsumeAssetItem {
	if x != nil {
		return x.ConsumeItem
	}
	return nil
}

func (x *SendRewardReq) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

func (x *SendRewardReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type SendRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SendRewardRsp) Reset() {
	*x = SendRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRewardRsp) ProtoMessage() {}

func (x *SendRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRewardRsp.ProtoReflect.Descriptor instead.
func (*SendRewardRsp) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{6}
}

type RewardDealIndex struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 同个奖励ID 已经处理奖品项
	RewardItemIndex uint32 `protobuf:"varint,1,opt,name=reward_item_index,json=rewardItemIndex,proto3" json:"reward_item_index,omitempty"`
	// 已经处理奖励ID项
	RewardIdIndex uint32 `protobuf:"varint,2,opt,name=reward_id_index,json=rewardIdIndex,proto3" json:"reward_id_index,omitempty"`
}

func (x *RewardDealIndex) Reset() {
	*x = RewardDealIndex{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardDealIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardDealIndex) ProtoMessage() {}

func (x *RewardDealIndex) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardDealIndex.ProtoReflect.Descriptor instead.
func (*RewardDealIndex) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{7}
}

func (x *RewardDealIndex) GetRewardItemIndex() uint32 {
	if x != nil {
		return x.RewardItemIndex
	}
	return 0
}

func (x *RewardDealIndex) GetRewardIdIndex() uint32 {
	if x != nil {
		return x.RewardIdIndex
	}
	return 0
}

type RewardExtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 奖励ID
	RewardId int64 `protobuf:"varint,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	// 奖励价值
	RewardPrice int64 `protobuf:"varint,2,opt,name=reward_price,json=rewardPrice,proto3" json:"reward_price,omitempty"`
}

func (x *RewardExtInfo) Reset() {
	*x = RewardExtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardExtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardExtInfo) ProtoMessage() {}

func (x *RewardExtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardExtInfo.ProtoReflect.Descriptor instead.
func (*RewardExtInfo) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{8}
}

func (x *RewardExtInfo) GetRewardId() int64 {
	if x != nil {
		return x.RewardId
	}
	return 0
}

func (x *RewardExtInfo) GetRewardPrice() int64 {
	if x != nil {
		return x.RewardPrice
	}
	return 0
}

type SendRewardBill struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 奖励ID
	Rewards []*Reward `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`
	// 订单ID
	BillNo string `protobuf:"bytes,4,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// 时间戳
	Timestamp int64 `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 处理index
	Index *RewardDealIndex `protobuf:"bytes,6,opt,name=index,proto3" json:"index,omitempty"`
	// 聚合私信
	AggrMail bool `protobuf:"varint,7,opt,name=aggr_mail,json=aggrMail,proto3" json:"aggr_mail,omitempty"`
	// 调用方名称
	ProgramName string `protobuf:"bytes,8,opt,name=program_name,json=programName,proto3" json:"program_name,omitempty"`
	// 平台ID
	PlatId uint64 `protobuf:"varint,9,opt,name=plat_id,json=platId,proto3" json:"plat_id,omitempty"`
	// 发奖时间
	SendTs int64 `protobuf:"varint,10,opt,name=send_ts,json=sendTs,proto3" json:"send_ts,omitempty"`
	// roi信息
	Roi *Roi `protobuf:"bytes,11,opt,name=roi,proto3" json:"roi,omitempty"`
	// 奖励额外信息
	RewardExt []*RewardExtInfo `protobuf:"bytes,12,rep,name=reward_ext,json=rewardExt,proto3" json:"reward_ext,omitempty"`
	// 平台UID
	PlatUID uint64 `protobuf:"varint,13,opt,name=PlatUID,proto3" json:"PlatUID,omitempty"`
	// 流水发出时间
	PublishMs uint64 `protobuf:"varint,14,opt,name=PublishMs,proto3" json:"PublishMs,omitempty"`
	// 消耗的资产信息(即消耗了consume_item,发放了rewards)
	ConsumeItem *ConsumeAssetItem `protobuf:"bytes,15,opt,name=consume_item,json=consumeItem,proto3" json:"consume_item,omitempty"`
	// 通用透传字段
	MapExt map[string]string `protobuf:"bytes,16,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SendRewardBill) Reset() {
	*x = SendRewardBill{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendRewardBill) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendRewardBill) ProtoMessage() {}

func (x *SendRewardBill) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendRewardBill.ProtoReflect.Descriptor instead.
func (*SendRewardBill) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{9}
}

func (x *SendRewardBill) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendRewardBill) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendRewardBill) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *SendRewardBill) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *SendRewardBill) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SendRewardBill) GetIndex() *RewardDealIndex {
	if x != nil {
		return x.Index
	}
	return nil
}

func (x *SendRewardBill) GetAggrMail() bool {
	if x != nil {
		return x.AggrMail
	}
	return false
}

func (x *SendRewardBill) GetProgramName() string {
	if x != nil {
		return x.ProgramName
	}
	return ""
}

func (x *SendRewardBill) GetPlatId() uint64 {
	if x != nil {
		return x.PlatId
	}
	return 0
}

func (x *SendRewardBill) GetSendTs() int64 {
	if x != nil {
		return x.SendTs
	}
	return 0
}

func (x *SendRewardBill) GetRoi() *Roi {
	if x != nil {
		return x.Roi
	}
	return nil
}

func (x *SendRewardBill) GetRewardExt() []*RewardExtInfo {
	if x != nil {
		return x.RewardExt
	}
	return nil
}

func (x *SendRewardBill) GetPlatUID() uint64 {
	if x != nil {
		return x.PlatUID
	}
	return 0
}

func (x *SendRewardBill) GetPublishMs() uint64 {
	if x != nil {
		return x.PublishMs
	}
	return 0
}

func (x *SendRewardBill) GetConsumeItem() *ConsumeAssetItem {
	if x != nil {
		return x.ConsumeItem
	}
	return nil
}

func (x *SendRewardBill) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type RewardPrizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// app id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// open id
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	// 金额
	Amount uint32 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// 订单号
	BillNo string `protobuf:"bytes,4,opt,name=bill_no,json=billNo,proto3" json:"bill_no,omitempty"`
	// userid类型
	IdType int32 `protobuf:"varint,5,opt,name=id_type,json=idType,proto3" json:"id_type,omitempty"` //UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin
}

func (x *RewardPrizeReq) Reset() {
	*x = RewardPrizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardPrizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardPrizeReq) ProtoMessage() {}

func (x *RewardPrizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardPrizeReq.ProtoReflect.Descriptor instead.
func (*RewardPrizeReq) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{10}
}

func (x *RewardPrizeReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RewardPrizeReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RewardPrizeReq) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *RewardPrizeReq) GetBillNo() string {
	if x != nil {
		return x.BillNo
	}
	return ""
}

func (x *RewardPrizeReq) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

type RewardPrizeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prizes []*SubRewardItem `protobuf:"bytes,1,rep,name=prizes,proto3" json:"prizes,omitempty"`
}

func (x *RewardPrizeRsp) Reset() {
	*x = RewardPrizeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardPrizeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardPrizeRsp) ProtoMessage() {}

func (x *RewardPrizeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_reward_sender_reward_sender_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardPrizeRsp.ProtoReflect.Descriptor instead.
func (*RewardPrizeRsp) Descriptor() ([]byte, []int) {
	return file_pb_reward_sender_reward_sender_proto_rawDescGZIP(), []int{11}
}

func (x *RewardPrizeRsp) GetPrizes() []*SubRewardItem {
	if x != nil {
		return x.Prizes
	}
	return nil
}

var File_pb_reward_sender_reward_sender_proto protoreflect.FileDescriptor

var file_pb_reward_sender_reward_sender_proto_rawDesc = []byte{
	0x0a, 0x24, 0x70, 0x62, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x1a, 0x29, 0x70, 0x62, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x31, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x64, 0x73, 0x22, 0xc2, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x5b, 0x0a, 0x0f,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x37, 0x0a, 0x06, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e,
	0x75, 0x6d, 0x22, 0x20, 0x0a, 0x03, 0x52, 0x6f, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe6, 0x03, 0x0a, 0x0d,
	0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x30, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x67, 0x72,
	0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x67, 0x67,
	0x72, 0x4d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x54,
	0x73, 0x12, 0x25, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x52, 0x6f, 0x69, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x43, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x0f, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x73, 0x70, 0x22, 0x65, 0x0a, 0x0f, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x4f, 0x0a, 0x0d,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0xb3, 0x05,
	0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x30, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x65, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x67, 0x72, 0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x67, 0x67, 0x72, 0x4d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x73, 0x12, 0x25, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x52, 0x6f, 0x69, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x3c, 0x0a, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x45, 0x78, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x6c, 0x61, 0x74, 0x55,
	0x49, 0x44, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x50, 0x6c, 0x61, 0x74, 0x55, 0x49,
	0x44, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4d, 0x73, 0x12,
	0x43, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x42, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x10,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x42, 0x69, 0x6c, 0x6c, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x8a, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x47, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x35, 0x0a, 0x06, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x06, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x32, 0xfe, 0x01, 0x0a, 0x0c, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12,
	0x4a, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53,
	0x65, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x65,
	0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x0b, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x73, 0x70, 0x42, 0x45, 0x5a, 0x43, 0x74, 0x63,
	0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x2f, 0x70, 0x62, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_reward_sender_reward_sender_proto_rawDescOnce sync.Once
	file_pb_reward_sender_reward_sender_proto_rawDescData = file_pb_reward_sender_reward_sender_proto_rawDesc
)

func file_pb_reward_sender_reward_sender_proto_rawDescGZIP() []byte {
	file_pb_reward_sender_reward_sender_proto_rawDescOnce.Do(func() {
		file_pb_reward_sender_reward_sender_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_reward_sender_reward_sender_proto_rawDescData)
	})
	return file_pb_reward_sender_reward_sender_proto_rawDescData
}

var file_pb_reward_sender_reward_sender_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pb_reward_sender_reward_sender_proto_goTypes = []interface{}{
	(*GetRewardInfoReq)(nil), // 0: component.game.GetRewardInfoReq
	(*GetRewardInfoRsp)(nil), // 1: component.game.GetRewardInfoRsp
	(*Reward)(nil),           // 2: component.game.Reward
	(*Roi)(nil),              // 3: component.game.Roi
	(*ConsumeAssetItem)(nil), // 4: component.game.ConsumeAssetItem
	(*SendRewardReq)(nil),    // 5: component.game.SendRewardReq
	(*SendRewardRsp)(nil),    // 6: component.game.SendRewardRsp
	(*RewardDealIndex)(nil),  // 7: component.game.RewardDealIndex
	(*RewardExtInfo)(nil),    // 8: component.game.RewardExtInfo
	(*SendRewardBill)(nil),   // 9: component.game.SendRewardBill
	(*RewardPrizeReq)(nil),   // 10: component.game.RewardPrizeReq
	(*RewardPrizeRsp)(nil),   // 11: component.game.RewardPrizeRsp
	nil,                      // 12: component.game.GetRewardInfoRsp.RewardInfoEntry
	nil,                      // 13: component.game.SendRewardReq.MapExtEntry
	nil,                      // 14: component.game.SendRewardBill.MapExtEntry
	(*SubRewardItem)(nil),    // 15: component.game.subRewardItem
	(*RewardConfig)(nil),     // 16: component.game.RewardConfig
}
var file_pb_reward_sender_reward_sender_proto_depIdxs = []int32{
	12, // 0: component.game.GetRewardInfoRsp.reward_info:type_name -> component.game.GetRewardInfoRsp.RewardInfoEntry
	2,  // 1: component.game.SendRewardReq.rewards:type_name -> component.game.Reward
	3,  // 2: component.game.SendRewardReq.roi:type_name -> component.game.Roi
	4,  // 3: component.game.SendRewardReq.consume_item:type_name -> component.game.ConsumeAssetItem
	13, // 4: component.game.SendRewardReq.mapExt:type_name -> component.game.SendRewardReq.MapExtEntry
	2,  // 5: component.game.SendRewardBill.rewards:type_name -> component.game.Reward
	7,  // 6: component.game.SendRewardBill.index:type_name -> component.game.RewardDealIndex
	3,  // 7: component.game.SendRewardBill.roi:type_name -> component.game.Roi
	8,  // 8: component.game.SendRewardBill.reward_ext:type_name -> component.game.RewardExtInfo
	4,  // 9: component.game.SendRewardBill.consume_item:type_name -> component.game.ConsumeAssetItem
	14, // 10: component.game.SendRewardBill.mapExt:type_name -> component.game.SendRewardBill.MapExtEntry
	15, // 11: component.game.RewardPrizeRsp.prizes:type_name -> component.game.subRewardItem
	16, // 12: component.game.GetRewardInfoRsp.RewardInfoEntry.value:type_name -> component.game.RewardConfig
	0,  // 13: component.game.RewardSender.GetRewardInfo:input_type -> component.game.GetRewardInfoReq
	5,  // 14: component.game.RewardSender.SendReward:input_type -> component.game.SendRewardReq
	10, // 15: component.game.RewardSender.RewardPrize:input_type -> component.game.RewardPrizeReq
	1,  // 16: component.game.RewardSender.GetRewardInfo:output_type -> component.game.GetRewardInfoRsp
	6,  // 17: component.game.RewardSender.SendReward:output_type -> component.game.SendRewardRsp
	11, // 18: component.game.RewardSender.RewardPrize:output_type -> component.game.RewardPrizeRsp
	16, // [16:19] is the sub-list for method output_type
	13, // [13:16] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_pb_reward_sender_reward_sender_proto_init() }
func file_pb_reward_sender_reward_sender_proto_init() {
	if File_pb_reward_sender_reward_sender_proto != nil {
		return
	}
	file_pb_reward_sender_reward_sender_comm_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_reward_sender_reward_sender_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Roi); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumeAssetItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardDealIndex); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardExtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendRewardBill); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardPrizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_reward_sender_reward_sender_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardPrizeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_reward_sender_reward_sender_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_reward_sender_reward_sender_proto_goTypes,
		DependencyIndexes: file_pb_reward_sender_reward_sender_proto_depIdxs,
		MessageInfos:      file_pb_reward_sender_reward_sender_proto_msgTypes,
	}.Build()
	File_pb_reward_sender_reward_sender_proto = out.File
	file_pb_reward_sender_reward_sender_proto_rawDesc = nil
	file_pb_reward_sender_reward_sender_proto_goTypes = nil
	file_pb_reward_sender_reward_sender_proto_depIdxs = nil
}
