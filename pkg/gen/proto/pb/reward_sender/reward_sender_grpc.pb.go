// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/reward_sender/reward_sender.proto

package reward_sender

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	RewardSender_GetRewardInfo_FullMethodName = "/component.game.RewardSender/GetRewardInfo"
	RewardSender_SendReward_FullMethodName    = "/component.game.RewardSender/SendReward"
	RewardSender_RewardPrize_FullMethodName   = "/component.game.RewardSender/RewardPrize"
)

// RewardSenderClient is the client API for RewardSender service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RewardSenderClient interface {
	// 获取奖励ID信息
	GetRewardInfo(ctx context.Context, in *GetRewardInfoReq, opts ...grpc.CallOption) (*GetRewardInfoRsp, error)
	// 发送奖励
	SendReward(ctx context.Context, in *SendRewardReq, opts ...grpc.CallOption) (*SendRewardRsp, error)
	// 根据奖励金额发放奖励 TODO
	RewardPrize(ctx context.Context, in *RewardPrizeReq, opts ...grpc.CallOption) (*RewardPrizeRsp, error)
}

type rewardSenderClient struct {
	cc grpc.ClientConnInterface
}

func NewRewardSenderClient(cc grpc.ClientConnInterface) RewardSenderClient {
	return &rewardSenderClient{cc}
}

func (c *rewardSenderClient) GetRewardInfo(ctx context.Context, in *GetRewardInfoReq, opts ...grpc.CallOption) (*GetRewardInfoRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRewardInfoRsp)
	err := c.cc.Invoke(ctx, RewardSender_GetRewardInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardSenderClient) SendReward(ctx context.Context, in *SendRewardReq, opts ...grpc.CallOption) (*SendRewardRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendRewardRsp)
	err := c.cc.Invoke(ctx, RewardSender_SendReward_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rewardSenderClient) RewardPrize(ctx context.Context, in *RewardPrizeReq, opts ...grpc.CallOption) (*RewardPrizeRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RewardPrizeRsp)
	err := c.cc.Invoke(ctx, RewardSender_RewardPrize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RewardSenderServer is the server API for RewardSender service.
// All implementations should embed UnimplementedRewardSenderServer
// for forward compatibility
type RewardSenderServer interface {
	// 获取奖励ID信息
	GetRewardInfo(context.Context, *GetRewardInfoReq) (*GetRewardInfoRsp, error)
	// 发送奖励
	SendReward(context.Context, *SendRewardReq) (*SendRewardRsp, error)
	// 根据奖励金额发放奖励 TODO
	RewardPrize(context.Context, *RewardPrizeReq) (*RewardPrizeRsp, error)
}

// UnimplementedRewardSenderServer should be embedded to have forward compatible implementations.
type UnimplementedRewardSenderServer struct {
}

func (UnimplementedRewardSenderServer) GetRewardInfo(context.Context, *GetRewardInfoReq) (*GetRewardInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewardInfo not implemented")
}
func (UnimplementedRewardSenderServer) SendReward(context.Context, *SendRewardReq) (*SendRewardRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReward not implemented")
}
func (UnimplementedRewardSenderServer) RewardPrize(context.Context, *RewardPrizeReq) (*RewardPrizeRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RewardPrize not implemented")
}

// UnsafeRewardSenderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RewardSenderServer will
// result in compilation errors.
type UnsafeRewardSenderServer interface {
	mustEmbedUnimplementedRewardSenderServer()
}

func RegisterRewardSenderServer(s grpc.ServiceRegistrar, srv RewardSenderServer) {
	s.RegisterService(&RewardSender_ServiceDesc, srv)
}

func _RewardSender_GetRewardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardSenderServer).GetRewardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RewardSender_GetRewardInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardSenderServer).GetRewardInfo(ctx, req.(*GetRewardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RewardSender_SendReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardSenderServer).SendReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RewardSender_SendReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardSenderServer).SendReward(ctx, req.(*SendRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RewardSender_RewardPrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RewardPrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RewardSenderServer).RewardPrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RewardSender_RewardPrize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RewardSenderServer).RewardPrize(ctx, req.(*RewardPrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RewardSender_ServiceDesc is the grpc.ServiceDesc for RewardSender service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RewardSender_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.game.RewardSender",
	HandlerType: (*RewardSenderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRewardInfo",
			Handler:    _RewardSender_GetRewardInfo_Handler,
		},
		{
			MethodName: "SendReward",
			Handler:    _RewardSender_SendReward_Handler,
		},
		{
			MethodName: "RewardPrize",
			Handler:    _RewardSender_RewardPrize_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/reward_sender/reward_sender.proto",
}
