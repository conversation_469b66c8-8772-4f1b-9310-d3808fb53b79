{"swagger": "2.0", "info": {"title": "pb/adapter_lucky_draw/adapter_lucky_draw.proto", "version": "version not set"}, "tags": [{"name": "AdapterLuckyDraw"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/adapter_lucky_draw.AdapterLuckyDraw/AddTicket": {"post": {"summary": "发放抽奖券", "operationId": "AdapterLuckyDraw_AddTicket", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_lucky_drawAddTicketRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_lucky_drawAddTicketReq"}}], "tags": ["AdapterLuckyDraw"]}}, "/adapter_lucky_draw.AdapterLuckyDraw/DoLottery": {"post": {"summary": "抽奖", "operationId": "AdapterLuckyDraw_DoLottery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_lucky_drawLotteryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_lucky_drawLotteryReq"}}], "tags": ["AdapterLuckyDraw"]}}, "/adapter_lucky_draw.AdapterLuckyDraw/SubTicket": {"post": {"summary": "扣减抽奖券【抽奖中台调用】", "operationId": "AdapterLuckyDraw_SubTicket", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_lucky_drawSubTicketRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_lucky_drawSubTicketReq"}}], "tags": ["AdapterLuckyDraw"]}}}, "definitions": {"adapter_lucky_drawAddTicketReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "cnt": {"type": "integer", "format": "int64", "title": "增加数量"}, "activityID": {"type": "string", "title": "活动id"}, "seqID": {"type": "string", "title": "去重id"}}}, "adapter_lucky_drawAddTicketRsp": {"type": "object", "properties": {"ticketCnt": {"type": "integer", "format": "int64", "title": "用户拥有的券数量"}}}, "adapter_lucky_drawAwardInfo": {"type": "object", "properties": {"id": {"type": "string", "title": "奖项id"}, "remarks": {"type": "string", "title": "备注信息"}}, "title": "奖项信息，其他字段后续自行补充"}, "adapter_lucky_drawLotteryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "activityID": {"type": "string"}, "consumeID": {"type": "string"}, "mapParams": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传扩展参数"}}}, "adapter_lucky_drawLotteryRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "业务错误码，参考 pb/code.proto LuckyDrawXX"}, "errorMsg": {"type": "string"}, "activityID": {"type": "string", "title": "活动id"}, "awardInfo": {"$ref": "#/definitions/adapter_lucky_drawAwardInfo", "title": "中奖奖项信息"}, "safetyParams": {"$ref": "#/definitions/adapter_lucky_drawSafetyParams", "title": "安全相关参数"}, "ticketCnt": {"type": "integer", "format": "int64", "title": "用户拥有的券数量"}}}, "adapter_lucky_drawSafetyParams": {"type": "object", "properties": {"validateURL": {"type": "string", "title": "验证码url，error_code=LuckyDrawNeedSafeVerify时使用"}}}, "adapter_lucky_drawSubTicketReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string", "format": "int64"}, "cnt": {"type": "integer", "format": "int64", "title": "扣除数量"}, "activityID": {"type": "string", "title": "活动id"}, "seqID": {"type": "string", "title": "去重id"}, "mapParams": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传扩展参数，同LotteryReq.MapParams"}, "bizId": {"type": "string", "title": "业务幂等ID"}, "batchCnt": {"type": "integer", "format": "int64", "title": "批量抽奖-批量次数"}}}, "adapter_lucky_drawSubTicketRsp": {"type": "object", "properties": {"ret": {"type": "integer", "format": "int32", "title": "业务错误码，参考 pb/code.proto LuckyDrawXX"}, "cnt": {"type": "integer", "format": "int64", "title": "用户拥有的抽奖券数量"}, "errMsg": {"type": "string"}, "subCnt": {"type": "integer", "format": "int64", "title": "实际扣除数量"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}