// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/adapter_lucky_draw/adapter_lucky_draw.proto

package adapter_lucky_draw

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	AdapterLuckyDraw_DoLottery_FullMethodName = "/adapter_lucky_draw.AdapterLuckyDraw/DoLottery"
	AdapterLuckyDraw_AddTicket_FullMethodName = "/adapter_lucky_draw.AdapterLuckyDraw/AddTicket"
	AdapterLuckyDraw_SubTicket_FullMethodName = "/adapter_lucky_draw.AdapterLuckyDraw/SubTicket"
)

// AdapterLuckyDrawClient is the client API for AdapterLuckyDraw service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterLuckyDrawClient interface {
	// 抽奖
	DoLottery(ctx context.Context, in *LotteryReq, opts ...grpc.CallOption) (*LotteryRsp, error)
	// 发放抽奖券
	AddTicket(ctx context.Context, in *AddTicketReq, opts ...grpc.CallOption) (*AddTicketRsp, error)
	// 扣减抽奖券【抽奖中台调用】
	SubTicket(ctx context.Context, in *SubTicketReq, opts ...grpc.CallOption) (*SubTicketRsp, error)
}

type adapterLuckyDrawClient struct {
	cc grpc.ClientConnInterface
}

func NewAdapterLuckyDrawClient(cc grpc.ClientConnInterface) AdapterLuckyDrawClient {
	return &adapterLuckyDrawClient{cc}
}

func (c *adapterLuckyDrawClient) DoLottery(ctx context.Context, in *LotteryReq, opts ...grpc.CallOption) (*LotteryRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LotteryRsp)
	err := c.cc.Invoke(ctx, AdapterLuckyDraw_DoLottery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterLuckyDrawClient) AddTicket(ctx context.Context, in *AddTicketReq, opts ...grpc.CallOption) (*AddTicketRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddTicketRsp)
	err := c.cc.Invoke(ctx, AdapterLuckyDraw_AddTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterLuckyDrawClient) SubTicket(ctx context.Context, in *SubTicketReq, opts ...grpc.CallOption) (*SubTicketRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SubTicketRsp)
	err := c.cc.Invoke(ctx, AdapterLuckyDraw_SubTicket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterLuckyDrawServer is the server API for AdapterLuckyDraw service.
// All implementations should embed UnimplementedAdapterLuckyDrawServer
// for forward compatibility
//
// 请求 adapter 服务时，头部必须携带 game_appid 和用户 open id
type AdapterLuckyDrawServer interface {
	// 抽奖
	DoLottery(context.Context, *LotteryReq) (*LotteryRsp, error)
	// 发放抽奖券
	AddTicket(context.Context, *AddTicketReq) (*AddTicketRsp, error)
	// 扣减抽奖券【抽奖中台调用】
	SubTicket(context.Context, *SubTicketReq) (*SubTicketRsp, error)
}

// UnimplementedAdapterLuckyDrawServer should be embedded to have forward compatible implementations.
type UnimplementedAdapterLuckyDrawServer struct {
}

func (UnimplementedAdapterLuckyDrawServer) DoLottery(context.Context, *LotteryReq) (*LotteryRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoLottery not implemented")
}
func (UnimplementedAdapterLuckyDrawServer) AddTicket(context.Context, *AddTicketReq) (*AddTicketRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTicket not implemented")
}
func (UnimplementedAdapterLuckyDrawServer) SubTicket(context.Context, *SubTicketReq) (*SubTicketRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubTicket not implemented")
}

// UnsafeAdapterLuckyDrawServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdapterLuckyDrawServer will
// result in compilation errors.
type UnsafeAdapterLuckyDrawServer interface {
	mustEmbedUnimplementedAdapterLuckyDrawServer()
}

func RegisterAdapterLuckyDrawServer(s grpc.ServiceRegistrar, srv AdapterLuckyDrawServer) {
	s.RegisterService(&AdapterLuckyDraw_ServiceDesc, srv)
}

func _AdapterLuckyDraw_DoLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterLuckyDrawServer).DoLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterLuckyDraw_DoLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterLuckyDrawServer).DoLottery(ctx, req.(*LotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterLuckyDraw_AddTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterLuckyDrawServer).AddTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterLuckyDraw_AddTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterLuckyDrawServer).AddTicket(ctx, req.(*AddTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterLuckyDraw_SubTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubTicketReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterLuckyDrawServer).SubTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdapterLuckyDraw_SubTicket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterLuckyDrawServer).SubTicket(ctx, req.(*SubTicketReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AdapterLuckyDraw_ServiceDesc is the grpc.ServiceDesc for AdapterLuckyDraw service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdapterLuckyDraw_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "adapter_lucky_draw.AdapterLuckyDraw",
	HandlerType: (*AdapterLuckyDrawServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoLottery",
			Handler:    _AdapterLuckyDraw_DoLottery_Handler,
		},
		{
			MethodName: "AddTicket",
			Handler:    _AdapterLuckyDraw_AddTicket_Handler,
		},
		{
			MethodName: "SubTicket",
			Handler:    _AdapterLuckyDraw_SubTicket_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/adapter_lucky_draw/adapter_lucky_draw.proto",
}
