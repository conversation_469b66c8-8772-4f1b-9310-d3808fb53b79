// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/business_intelligence_reportor/business_intelligence_reportor.proto

package business_intelligence

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	TmeBusinessIntelligenceReportor_ReportFreeGift_FullMethodName = "/kg.game.TmeBusinessIntelligenceReportor/ReportFreeGift"
)

// TmeBusinessIntelligenceReportorClient is the client API for TmeBusinessIntelligenceReportor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TmeBusinessIntelligenceReportorClient interface {
	ReportFreeGift(ctx context.Context, in *ReportFreeGiftReq, opts ...grpc.CallOption) (*ReportFreeGiftRsp, error)
}

type tmeBusinessIntelligenceReportorClient struct {
	cc grpc.ClientConnInterface
}

func NewTmeBusinessIntelligenceReportorClient(cc grpc.ClientConnInterface) TmeBusinessIntelligenceReportorClient {
	return &tmeBusinessIntelligenceReportorClient{cc}
}

func (c *tmeBusinessIntelligenceReportorClient) ReportFreeGift(ctx context.Context, in *ReportFreeGiftReq, opts ...grpc.CallOption) (*ReportFreeGiftRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReportFreeGiftRsp)
	err := c.cc.Invoke(ctx, TmeBusinessIntelligenceReportor_ReportFreeGift_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TmeBusinessIntelligenceReportorServer is the server API for TmeBusinessIntelligenceReportor service.
// All implementations should embed UnimplementedTmeBusinessIntelligenceReportorServer
// for forward compatibility
type TmeBusinessIntelligenceReportorServer interface {
	ReportFreeGift(context.Context, *ReportFreeGiftReq) (*ReportFreeGiftRsp, error)
}

// UnimplementedTmeBusinessIntelligenceReportorServer should be embedded to have forward compatible implementations.
type UnimplementedTmeBusinessIntelligenceReportorServer struct {
}

func (UnimplementedTmeBusinessIntelligenceReportorServer) ReportFreeGift(context.Context, *ReportFreeGiftReq) (*ReportFreeGiftRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportFreeGift not implemented")
}

// UnsafeTmeBusinessIntelligenceReportorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TmeBusinessIntelligenceReportorServer will
// result in compilation errors.
type UnsafeTmeBusinessIntelligenceReportorServer interface {
	mustEmbedUnimplementedTmeBusinessIntelligenceReportorServer()
}

func RegisterTmeBusinessIntelligenceReportorServer(s grpc.ServiceRegistrar, srv TmeBusinessIntelligenceReportorServer) {
	s.RegisterService(&TmeBusinessIntelligenceReportor_ServiceDesc, srv)
}

func _TmeBusinessIntelligenceReportor_ReportFreeGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportFreeGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TmeBusinessIntelligenceReportorServer).ReportFreeGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TmeBusinessIntelligenceReportor_ReportFreeGift_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TmeBusinessIntelligenceReportorServer).ReportFreeGift(ctx, req.(*ReportFreeGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TmeBusinessIntelligenceReportor_ServiceDesc is the grpc.ServiceDesc for TmeBusinessIntelligenceReportor service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TmeBusinessIntelligenceReportor_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "kg.game.TmeBusinessIntelligenceReportor",
	HandlerType: (*TmeBusinessIntelligenceReportorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReportFreeGift",
			Handler:    _TmeBusinessIntelligenceReportor_ReportFreeGift_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/business_intelligence_reportor/business_intelligence_reportor.proto",
}
