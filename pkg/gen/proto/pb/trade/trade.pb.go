// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/trade/trade.proto

package trade

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OperatingSystem int32

const (
	OperatingSystem_OperatingSystemUnknown OperatingSystem = 0
	OperatingSystem_OperatingSystemAndroid OperatingSystem = 1
	OperatingSystem_OperatingSystemIOS     OperatingSystem = 2
)

// Enum value maps for OperatingSystem.
var (
	OperatingSystem_name = map[int32]string{
		0: "OperatingSystemUnknown",
		1: "OperatingSystemAndroid",
		2: "OperatingSystemIOS",
	}
	OperatingSystem_value = map[string]int32{
		"OperatingSystemUnknown": 0,
		"OperatingSystemAndroid": 1,
		"OperatingSystemIOS":     2,
	}
)

func (x OperatingSystem) Enum() *OperatingSystem {
	p := new(OperatingSystem)
	*p = x
	return p
}

func (x OperatingSystem) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperatingSystem) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_trade_trade_proto_enumTypes[0].Descriptor()
}

func (OperatingSystem) Type() protoreflect.EnumType {
	return &file_pb_trade_trade_proto_enumTypes[0]
}

func (x OperatingSystem) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperatingSystem.Descriptor instead.
func (OperatingSystem) EnumDescriptor() ([]byte, []int) {
	return file_pb_trade_trade_proto_rawDescGZIP(), []int{0}
}

type CheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessId int64           `protobuf:"varint,1,opt,name=businessId,proto3" json:"businessId,omitempty"`
	Uid        uint64          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ProductId  string          `protobuf:"bytes,3,opt,name=productId,proto3" json:"productId,omitempty"`               // 商品 id
	Price      int64           `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                      // 价格
	Os         OperatingSystem `protobuf:"varint,5,opt,name=os,proto3,enum=trade.OperatingSystem" json:"os,omitempty"` // 系统
	// greenId
	// kugouSkuId/wxSkuId
	MapExt map[string]string `protobuf:"bytes,6,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	OpenId string            `protobuf:"bytes,7,opt,name=openId,proto3" json:"openId,omitempty"` // 兼容微信小游戏 默认传openId
	AppId  string            `protobuf:"bytes,8,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *CheckOrderReq) Reset() {
	*x = CheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_trade_trade_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderReq) ProtoMessage() {}

func (x *CheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_trade_trade_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderReq.ProtoReflect.Descriptor instead.
func (*CheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_trade_trade_proto_rawDescGZIP(), []int{0}
}

func (x *CheckOrderReq) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CheckOrderReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *CheckOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CheckOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CheckOrderReq) GetOs() OperatingSystem {
	if x != nil {
		return x.Os
	}
	return OperatingSystem_OperatingSystemUnknown
}

func (x *CheckOrderReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *CheckOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type CheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckOrderRsp) Reset() {
	*x = CheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_trade_trade_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderRsp) ProtoMessage() {}

func (x *CheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_trade_trade_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderRsp.ProtoReflect.Descriptor instead.
func (*CheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_trade_trade_proto_rawDescGZIP(), []int{1}
}

type DeliveryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessId    int64           `protobuf:"varint,1,opt,name=businessId,proto3" json:"businessId,omitempty"`
	Uid           uint64          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ProductId     string          `protobuf:"bytes,3,opt,name=productId,proto3" json:"productId,omitempty"` // 商品 id
	Price         int64           `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`        // 价格
	TransactionId string          `protobuf:"bytes,5,opt,name=transactionId,proto3" json:"transactionId,omitempty"`
	Timestamp     int64           `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Os            OperatingSystem `protobuf:"varint,7,opt,name=os,proto3,enum=trade.OperatingSystem" json:"os,omitempty"` // 系统
	// greenId
	// kugouSkuId/wxSkuId
	MapExt map[string]string `protobuf:"bytes,8,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	OpenId string            `protobuf:"bytes,9,opt,name=openId,proto3" json:"openId,omitempty"` // 兼容微信小游戏 默认传openId
	AppId  string            `protobuf:"bytes,10,opt,name=appId,proto3" json:"appId,omitempty"`
}

func (x *DeliveryReq) Reset() {
	*x = DeliveryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_trade_trade_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryReq) ProtoMessage() {}

func (x *DeliveryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_trade_trade_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryReq.ProtoReflect.Descriptor instead.
func (*DeliveryReq) Descriptor() ([]byte, []int) {
	return file_pb_trade_trade_proto_rawDescGZIP(), []int{2}
}

func (x *DeliveryReq) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *DeliveryReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *DeliveryReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *DeliveryReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *DeliveryReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *DeliveryReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DeliveryReq) GetOs() OperatingSystem {
	if x != nil {
		return x.Os
	}
	return OperatingSystem_OperatingSystemUnknown
}

func (x *DeliveryReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *DeliveryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DeliveryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type DeliveryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeliveryRsp) Reset() {
	*x = DeliveryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_trade_trade_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryRsp) ProtoMessage() {}

func (x *DeliveryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_trade_trade_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryRsp.ProtoReflect.Descriptor instead.
func (*DeliveryRsp) Descriptor() ([]byte, []int) {
	return file_pb_trade_trade_proto_rawDescGZIP(), []int{3}
}

var File_pb_trade_trade_proto protoreflect.FileDescriptor

var file_pb_trade_trade_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x72, 0x61, 0x64, 0x65, 0x22, 0xc0, 0x02,
	0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x38, 0x0a,
	0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73,
	0x70, 0x22, 0x80, 0x03, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x26, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52,
	0x02, 0x6f, 0x73, 0x12, 0x36, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x0d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x52, 0x73, 0x70, 0x2a, 0x61, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x32, 0x75, 0x0a, 0x05, 0x54, 0x72, 0x61, 0x64, 0x65, 0x12,
	0x38, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x14, 0x2e,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x08, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x12, 0x2e, 0x74, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x42, 0x3d, 0x5a,
	0x3b, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_trade_trade_proto_rawDescOnce sync.Once
	file_pb_trade_trade_proto_rawDescData = file_pb_trade_trade_proto_rawDesc
)

func file_pb_trade_trade_proto_rawDescGZIP() []byte {
	file_pb_trade_trade_proto_rawDescOnce.Do(func() {
		file_pb_trade_trade_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_trade_trade_proto_rawDescData)
	})
	return file_pb_trade_trade_proto_rawDescData
}

var file_pb_trade_trade_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_trade_trade_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_trade_trade_proto_goTypes = []interface{}{
	(OperatingSystem)(0),  // 0: trade.OperatingSystem
	(*CheckOrderReq)(nil), // 1: trade.CheckOrderReq
	(*CheckOrderRsp)(nil), // 2: trade.CheckOrderRsp
	(*DeliveryReq)(nil),   // 3: trade.DeliveryReq
	(*DeliveryRsp)(nil),   // 4: trade.DeliveryRsp
	nil,                   // 5: trade.CheckOrderReq.MapExtEntry
	nil,                   // 6: trade.DeliveryReq.MapExtEntry
}
var file_pb_trade_trade_proto_depIdxs = []int32{
	0, // 0: trade.CheckOrderReq.os:type_name -> trade.OperatingSystem
	5, // 1: trade.CheckOrderReq.mapExt:type_name -> trade.CheckOrderReq.MapExtEntry
	0, // 2: trade.DeliveryReq.os:type_name -> trade.OperatingSystem
	6, // 3: trade.DeliveryReq.mapExt:type_name -> trade.DeliveryReq.MapExtEntry
	1, // 4: trade.Trade.CheckOrder:input_type -> trade.CheckOrderReq
	3, // 5: trade.Trade.Delivery:input_type -> trade.DeliveryReq
	2, // 6: trade.Trade.CheckOrder:output_type -> trade.CheckOrderRsp
	4, // 7: trade.Trade.Delivery:output_type -> trade.DeliveryRsp
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pb_trade_trade_proto_init() }
func file_pb_trade_trade_proto_init() {
	if File_pb_trade_trade_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_trade_trade_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_trade_trade_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_trade_trade_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_trade_trade_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_trade_trade_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_trade_trade_proto_goTypes,
		DependencyIndexes: file_pb_trade_trade_proto_depIdxs,
		EnumInfos:         file_pb_trade_trade_proto_enumTypes,
		MessageInfos:      file_pb_trade_trade_proto_msgTypes,
	}.Build()
	File_pb_trade_trade_proto = out.File
	file_pb_trade_trade_proto_rawDesc = nil
	file_pb_trade_trade_proto_goTypes = nil
	file_pb_trade_trade_proto_depIdxs = nil
}
