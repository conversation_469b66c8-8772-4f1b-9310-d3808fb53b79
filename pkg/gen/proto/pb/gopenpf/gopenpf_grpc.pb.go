// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/gopenpf/gopenpf.proto

package gopenpf

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GOpenPfService_GetProfile_FullMethodName    = "/component.gopenpf.GOpenPfService/GetProfile"
	GOpenPfService_UpdateProfile_FullMethodName = "/component.gopenpf.GOpenPfService/UpdateProfile"
)

// GOpenPfServiceClient is the client API for GOpenPfService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// GOpenPfService 游戏开放平台-资料托管服务
type GOpenPfServiceClient interface {
	// 资料获取
	GetProfile(ctx context.Context, in *GetProfileReq, opts ...grpc.CallOption) (*GetProfileRsp, error)
	// 资料更新
	UpdateProfile(ctx context.Context, in *UpdateProfileReq, opts ...grpc.CallOption) (*UpdateProfileRsp, error)
}

type gOpenPfServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGOpenPfServiceClient(cc grpc.ClientConnInterface) GOpenPfServiceClient {
	return &gOpenPfServiceClient{cc}
}

func (c *gOpenPfServiceClient) GetProfile(ctx context.Context, in *GetProfileReq, opts ...grpc.CallOption) (*GetProfileRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProfileRsp)
	err := c.cc.Invoke(ctx, GOpenPfService_GetProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gOpenPfServiceClient) UpdateProfile(ctx context.Context, in *UpdateProfileReq, opts ...grpc.CallOption) (*UpdateProfileRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateProfileRsp)
	err := c.cc.Invoke(ctx, GOpenPfService_UpdateProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GOpenPfServiceServer is the server API for GOpenPfService service.
// All implementations should embed UnimplementedGOpenPfServiceServer
// for forward compatibility
//
// GOpenPfService 游戏开放平台-资料托管服务
type GOpenPfServiceServer interface {
	// 资料获取
	GetProfile(context.Context, *GetProfileReq) (*GetProfileRsp, error)
	// 资料更新
	UpdateProfile(context.Context, *UpdateProfileReq) (*UpdateProfileRsp, error)
}

// UnimplementedGOpenPfServiceServer should be embedded to have forward compatible implementations.
type UnimplementedGOpenPfServiceServer struct {
}

func (UnimplementedGOpenPfServiceServer) GetProfile(context.Context, *GetProfileReq) (*GetProfileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfile not implemented")
}
func (UnimplementedGOpenPfServiceServer) UpdateProfile(context.Context, *UpdateProfileReq) (*UpdateProfileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProfile not implemented")
}

// UnsafeGOpenPfServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GOpenPfServiceServer will
// result in compilation errors.
type UnsafeGOpenPfServiceServer interface {
	mustEmbedUnimplementedGOpenPfServiceServer()
}

func RegisterGOpenPfServiceServer(s grpc.ServiceRegistrar, srv GOpenPfServiceServer) {
	s.RegisterService(&GOpenPfService_ServiceDesc, srv)
}

func _GOpenPfService_GetProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GOpenPfServiceServer).GetProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GOpenPfService_GetProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GOpenPfServiceServer).GetProfile(ctx, req.(*GetProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GOpenPfService_UpdateProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GOpenPfServiceServer).UpdateProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GOpenPfService_UpdateProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GOpenPfServiceServer).UpdateProfile(ctx, req.(*UpdateProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GOpenPfService_ServiceDesc is the grpc.ServiceDesc for GOpenPfService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GOpenPfService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "component.gopenpf.GOpenPfService",
	HandlerType: (*GOpenPfServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProfile",
			Handler:    _GOpenPfService_GetProfile_Handler,
		},
		{
			MethodName: "UpdateProfile",
			Handler:    _GOpenPfService_UpdateProfile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/gopenpf/gopenpf.proto",
}
