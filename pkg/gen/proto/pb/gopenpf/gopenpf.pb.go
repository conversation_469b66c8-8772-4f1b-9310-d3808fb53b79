// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/gopenpf/gopenpf.proto

package gopenpf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EMask int32

const (
	EMask_EMask_None   EMask = 0
	EMask_EMask_Nick   EMask = 1 // 用户昵称
	EMask_EMask_Avatar EMask = 2 // 用户头像
	EMask_EMask_Uid    EMask = 4 // 用户uid
)

// Enum value maps for EMask.
var (
	EMask_name = map[int32]string{
		0: "EMask_None",
		1: "EMask_Nick",
		2: "EMask_Avatar",
		4: "EMask_Uid",
	}
	EMask_value = map[string]int32{
		"EMask_None":   0,
		"EMask_Nick":   1,
		"EMask_Avatar": 2,
		"EMask_Uid":    4,
	}
)

func (x EMask) Enum() *EMask {
	p := new(EMask)
	*p = x
	return p
}

func (x EMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMask) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_gopenpf_gopenpf_proto_enumTypes[0].Descriptor()
}

func (EMask) Type() protoreflect.EnumType {
	return &file_pb_gopenpf_gopenpf_proto_enumTypes[0]
}

func (x EMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EMask.Descriptor instead.
func (EMask) EnumDescriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{0}
}

// EField 用于资料存储字段
type EField int32

const (
	EField_EField_None   EField = 0
	EField_EField_Nick   EField = 1 // 用户昵称
	EField_EField_Avatar EField = 2 // 用户头像
	EField_EField_Uid    EField = 3 // 用户uid
)

// Enum value maps for EField.
var (
	EField_name = map[int32]string{
		0: "EField_None",
		1: "EField_Nick",
		2: "EField_Avatar",
		3: "EField_Uid",
	}
	EField_value = map[string]int32{
		"EField_None":   0,
		"EField_Nick":   1,
		"EField_Avatar": 2,
		"EField_Uid":    3,
	}
)

func (x EField) Enum() *EField {
	p := new(EField)
	*p = x
	return p
}

func (x EField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EField) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_gopenpf_gopenpf_proto_enumTypes[1].Descriptor()
}

func (EField) Type() protoreflect.EnumType {
	return &file_pb_gopenpf_gopenpf_proto_enumTypes[1]
}

func (x EField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EField.Descriptor instead.
func (EField) EnumDescriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{1}
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrNick   string `protobuf:"bytes,1,opt,name=strNick,proto3" json:"strNick,omitempty"`     // 用户昵称
	StrAvatar string `protobuf:"bytes,2,opt,name=strAvatar,proto3" json:"strAvatar,omitempty"` // 用户头像
	StrUid    string `protobuf:"bytes,3,opt,name=strUid,proto3" json:"strUid,omitempty"`       // 用户uid
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetStrNick() string {
	if x != nil {
		return x.StrNick
	}
	return ""
}

func (x *UserInfo) GetStrAvatar() string {
	if x != nil {
		return x.StrAvatar
	}
	return ""
}

func (x *UserInfo) GetStrUid() string {
	if x != nil {
		return x.StrUid
	}
	return ""
}

// GetProfileReq 获取资料请求
type GetProfileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID   string   `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`     // 应用ID
	LPlatID    uint64   `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`      // 平台id
	LMask      uint64   `protobuf:"varint,3,opt,name=lMask,proto3" json:"lMask,omitempty"`          // 资料掩码
	VecOpenids []string `protobuf:"bytes,4,rep,name=vecOpenids,proto3" json:"vecOpenids,omitempty"` // 用户openid  最大批量100个
}

func (x *GetProfileReq) Reset() {
	*x = GetProfileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileReq) ProtoMessage() {}

func (x *GetProfileReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileReq.ProtoReflect.Descriptor instead.
func (*GetProfileReq) Descriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{1}
}

func (x *GetProfileReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *GetProfileReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *GetProfileReq) GetLMask() uint64 {
	if x != nil {
		return x.LMask
	}
	return 0
}

func (x *GetProfileReq) GetVecOpenids() []string {
	if x != nil {
		return x.VecOpenids
	}
	return nil
}

type GetProfileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MapFail map[string]int32               `protobuf:"bytes,1,rep,name=mapFail,proto3" json:"mapFail,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 转换失败, key: openid  value: error code
	MapSucc map[string]*GetProfileRsp_Item `protobuf:"bytes,2,rep,name=mapSucc,proto3" json:"mapSucc,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`  // 转换成功, key: openid  value: Item
}

func (x *GetProfileRsp) Reset() {
	*x = GetProfileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileRsp) ProtoMessage() {}

func (x *GetProfileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileRsp.ProtoReflect.Descriptor instead.
func (*GetProfileRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{2}
}

func (x *GetProfileRsp) GetMapFail() map[string]int32 {
	if x != nil {
		return x.MapFail
	}
	return nil
}

func (x *GetProfileRsp) GetMapSucc() map[string]*GetProfileRsp_Item {
	if x != nil {
		return x.MapSucc
	}
	return nil
}

// UpdateProfileReq 更新资料请求
type UpdateProfileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAppID   string    `protobuf:"bytes,1,opt,name=strAppID,proto3" json:"strAppID,omitempty"`     // 应用ID
	LPlatID    uint64    `protobuf:"varint,2,opt,name=lPlatID,proto3" json:"lPlatID,omitempty"`      // 平台id
	LMask      uint64    `protobuf:"varint,3,opt,name=lMask,proto3" json:"lMask,omitempty"`          // 资料掩码
	StrOpenid  string    `protobuf:"bytes,4,opt,name=strOpenid,proto3" json:"strOpenid,omitempty"`   // 用户openid
	StUserInfo *UserInfo `protobuf:"bytes,5,opt,name=stUserInfo,proto3" json:"stUserInfo,omitempty"` // 用户资料
}

func (x *UpdateProfileReq) Reset() {
	*x = UpdateProfileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProfileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProfileReq) ProtoMessage() {}

func (x *UpdateProfileReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProfileReq.ProtoReflect.Descriptor instead.
func (*UpdateProfileReq) Descriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateProfileReq) GetStrAppID() string {
	if x != nil {
		return x.StrAppID
	}
	return ""
}

func (x *UpdateProfileReq) GetLPlatID() uint64 {
	if x != nil {
		return x.LPlatID
	}
	return 0
}

func (x *UpdateProfileReq) GetLMask() uint64 {
	if x != nil {
		return x.LMask
	}
	return 0
}

func (x *UpdateProfileReq) GetStrOpenid() string {
	if x != nil {
		return x.StrOpenid
	}
	return ""
}

func (x *UpdateProfileReq) GetStUserInfo() *UserInfo {
	if x != nil {
		return x.StUserInfo
	}
	return nil
}

type UpdateProfileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateProfileRsp) Reset() {
	*x = UpdateProfileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateProfileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProfileRsp) ProtoMessage() {}

func (x *UpdateProfileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProfileRsp.ProtoReflect.Descriptor instead.
func (*UpdateProfileRsp) Descriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{4}
}

type GetProfileRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StUserInfo *UserInfo `protobuf:"bytes,1,opt,name=stUserInfo,proto3" json:"stUserInfo,omitempty"` // 用户资料
}

func (x *GetProfileRsp_Item) Reset() {
	*x = GetProfileRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProfileRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProfileRsp_Item) ProtoMessage() {}

func (x *GetProfileRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_gopenpf_gopenpf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProfileRsp_Item.ProtoReflect.Descriptor instead.
func (*GetProfileRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_gopenpf_gopenpf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetProfileRsp_Item) GetStUserInfo() *UserInfo {
	if x != nil {
		return x.StUserInfo
	}
	return nil
}

var File_pb_gopenpf_gopenpf_proto protoreflect.FileDescriptor

var file_pb_gopenpf_gopenpf_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2f, 0x67, 0x6f, 0x70,
	0x65, 0x6e, 0x70, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x22, 0x5a, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x72,
	0x4e, 0x69, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x4e,
	0x69, 0x63, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x55, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x72, 0x55, 0x69, 0x64, 0x22, 0x7b, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74,
	0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x05, 0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x65, 0x63, 0x4f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x63, 0x4f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x73, 0x22, 0x85, 0x03, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x47, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x46,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x46,
	0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x46, 0x61, 0x69,
	0x6c, 0x12, 0x47, 0x0a, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x1a, 0x43, 0x0a, 0x04, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x3b, 0x0a, 0x0a, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a,
	0x3a, 0x0a, 0x0c, 0x4d, 0x61, 0x70, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x61, 0x0a, 0x0c, 0x4d,
	0x61, 0x70, 0x53, 0x75, 0x63, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3b, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb9,
	0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12,
	0x18, 0x0a, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x6c, 0x50, 0x6c, 0x61, 0x74, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x4d, 0x61,
	0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x4d, 0x61, 0x73, 0x6b, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x3b, 0x0a,
	0x0a, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f,
	0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x12, 0x0a, 0x10, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x2a, 0x48,
	0x0a, 0x05, 0x45, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x4d, 0x61, 0x73, 0x6b,
	0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x4d, 0x61, 0x73, 0x6b,
	0x5f, 0x4e, 0x69, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x4d, 0x61, 0x73, 0x6b,
	0x5f, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x4d, 0x61,
	0x73, 0x6b, 0x5f, 0x55, 0x69, 0x64, 0x10, 0x04, 0x2a, 0x4d, 0x0a, 0x06, 0x45, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x4e, 0x6f, 0x6e,
	0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x4e, 0x69,
	0x63, 0x6b, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x55, 0x69, 0x64, 0x10, 0x03, 0x32, 0xbd, 0x01, 0x0a, 0x0e, 0x47, 0x4f, 0x70, 0x65,
	0x6e, 0x50, 0x66, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x0d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x23, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70,
	0x66, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x42, 0x47, 0x5a, 0x45, 0x74, 0x63, 0x6f, 0x64, 0x65,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62,
	0x2f, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66, 0x3b, 0x67, 0x6f, 0x70, 0x65, 0x6e, 0x70, 0x66,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_gopenpf_gopenpf_proto_rawDescOnce sync.Once
	file_pb_gopenpf_gopenpf_proto_rawDescData = file_pb_gopenpf_gopenpf_proto_rawDesc
)

func file_pb_gopenpf_gopenpf_proto_rawDescGZIP() []byte {
	file_pb_gopenpf_gopenpf_proto_rawDescOnce.Do(func() {
		file_pb_gopenpf_gopenpf_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_gopenpf_gopenpf_proto_rawDescData)
	})
	return file_pb_gopenpf_gopenpf_proto_rawDescData
}

var file_pb_gopenpf_gopenpf_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_gopenpf_gopenpf_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_gopenpf_gopenpf_proto_goTypes = []interface{}{
	(EMask)(0),                 // 0: component.gopenpf.EMask
	(EField)(0),                // 1: component.gopenpf.EField
	(*UserInfo)(nil),           // 2: component.gopenpf.UserInfo
	(*GetProfileReq)(nil),      // 3: component.gopenpf.GetProfileReq
	(*GetProfileRsp)(nil),      // 4: component.gopenpf.GetProfileRsp
	(*UpdateProfileReq)(nil),   // 5: component.gopenpf.UpdateProfileReq
	(*UpdateProfileRsp)(nil),   // 6: component.gopenpf.UpdateProfileRsp
	(*GetProfileRsp_Item)(nil), // 7: component.gopenpf.GetProfileRsp.Item
	nil,                        // 8: component.gopenpf.GetProfileRsp.MapFailEntry
	nil,                        // 9: component.gopenpf.GetProfileRsp.MapSuccEntry
}
var file_pb_gopenpf_gopenpf_proto_depIdxs = []int32{
	8, // 0: component.gopenpf.GetProfileRsp.mapFail:type_name -> component.gopenpf.GetProfileRsp.MapFailEntry
	9, // 1: component.gopenpf.GetProfileRsp.mapSucc:type_name -> component.gopenpf.GetProfileRsp.MapSuccEntry
	2, // 2: component.gopenpf.UpdateProfileReq.stUserInfo:type_name -> component.gopenpf.UserInfo
	2, // 3: component.gopenpf.GetProfileRsp.Item.stUserInfo:type_name -> component.gopenpf.UserInfo
	7, // 4: component.gopenpf.GetProfileRsp.MapSuccEntry.value:type_name -> component.gopenpf.GetProfileRsp.Item
	3, // 5: component.gopenpf.GOpenPfService.GetProfile:input_type -> component.gopenpf.GetProfileReq
	5, // 6: component.gopenpf.GOpenPfService.UpdateProfile:input_type -> component.gopenpf.UpdateProfileReq
	4, // 7: component.gopenpf.GOpenPfService.GetProfile:output_type -> component.gopenpf.GetProfileRsp
	6, // 8: component.gopenpf.GOpenPfService.UpdateProfile:output_type -> component.gopenpf.UpdateProfileRsp
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pb_gopenpf_gopenpf_proto_init() }
func file_pb_gopenpf_gopenpf_proto_init() {
	if File_pb_gopenpf_gopenpf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_gopenpf_gopenpf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopenpf_gopenpf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopenpf_gopenpf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopenpf_gopenpf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProfileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopenpf_gopenpf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateProfileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_gopenpf_gopenpf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProfileRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_gopenpf_gopenpf_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_gopenpf_gopenpf_proto_goTypes,
		DependencyIndexes: file_pb_gopenpf_gopenpf_proto_depIdxs,
		EnumInfos:         file_pb_gopenpf_gopenpf_proto_enumTypes,
		MessageInfos:      file_pb_gopenpf_gopenpf_proto_msgTypes,
	}.Build()
	File_pb_gopenpf_gopenpf_proto = out.File
	file_pb_gopenpf_gopenpf_proto_rawDesc = nil
	file_pb_gopenpf_gopenpf_proto_goTypes = nil
	file_pb_gopenpf_gopenpf_proto_depIdxs = nil
}
