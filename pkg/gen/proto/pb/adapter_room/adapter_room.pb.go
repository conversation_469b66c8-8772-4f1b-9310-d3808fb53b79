// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/adapter_room/adapter_room.proto

package adapter_room

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	adapter "kugou_adapter_service/pkg/gen/proto/pb/adapter"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetRoomInfo 查询房间信息
type GetRoomInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` //房间id
}

func (x *GetRoomInfoReq) Reset() {
	*x = GetRoomInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomInfoReq) ProtoMessage() {}

func (x *GetRoomInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomInfoReq.ProtoReflect.Descriptor instead.
func (*GetRoomInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{0}
}

func (x *GetRoomInfoReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetRoomInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info *RoomInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"` //房间信息
}

func (x *GetRoomInfoRsp) Reset() {
	*x = GetRoomInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomInfoRsp) ProtoMessage() {}

func (x *GetRoomInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRoomInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{1}
}

func (x *GetRoomInfoRsp) GetInfo() *RoomInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// BatchGetRoomInfo 批量查询房间信息
type BatchGetRoomInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomIdList []string `protobuf:"bytes,1,rep,name=room_id_list,json=roomIdList,proto3" json:"room_id_list,omitempty"` //房间id列表
}

func (x *BatchGetRoomInfoReq) Reset() {
	*x = BatchGetRoomInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetRoomInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetRoomInfoReq) ProtoMessage() {}

func (x *BatchGetRoomInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetRoomInfoReq.ProtoReflect.Descriptor instead.
func (*BatchGetRoomInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetRoomInfoReq) GetRoomIdList() []string {
	if x != nil {
		return x.RoomIdList
	}
	return nil
}

type BatchGetRoomInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info_Map map[string]*RoomInfo `protobuf:"bytes,1,rep,name=info_Map,json=infoMap,proto3" json:"info_Map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //房间信息
}

func (x *BatchGetRoomInfoRsp) Reset() {
	*x = BatchGetRoomInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetRoomInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetRoomInfoRsp) ProtoMessage() {}

func (x *BatchGetRoomInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetRoomInfoRsp.ProtoReflect.Descriptor instead.
func (*BatchGetRoomInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{3}
}

func (x *BatchGetRoomInfoRsp) GetInfo_Map() map[string]*RoomInfo {
	if x != nil {
		return x.Info_Map
	}
	return nil
}

// GetRecommendRoom 推荐列表
type GetRecommendRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId  string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`    //用户id
	IdType  int32  `protobuf:"varint,2,opt,name=id_type,json=idType,proto3" json:"id_type,omitempty"`   //UserIDType 用户id类型;默认0为openid;1为端内登录态uid;2为加密uid
	FromTag string `protobuf:"bytes,3,opt,name=from_tag,json=fromTag,proto3" json:"from_tag,omitempty"` //推荐标识
	AppId   string `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`       //小游戏AppId
}

func (x *GetRecommendRoomReq) Reset() {
	*x = GetRecommendRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendRoomReq) ProtoMessage() {}

func (x *GetRecommendRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendRoomReq.ProtoReflect.Descriptor instead.
func (*GetRecommendRoomReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{4}
}

func (x *GetRecommendRoomReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetRecommendRoomReq) GetIdType() int32 {
	if x != nil {
		return x.IdType
	}
	return 0
}

func (x *GetRecommendRoomReq) GetFromTag() string {
	if x != nil {
		return x.FromTag
	}
	return ""
}

func (x *GetRecommendRoomReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

type GetRecommendRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info_Map map[string]*RoomInfo `protobuf:"bytes,1,rep,name=info_Map,json=infoMap,proto3" json:"info_Map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //房间信息
}

func (x *GetRecommendRoomRsp) Reset() {
	*x = GetRecommendRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendRoomRsp) ProtoMessage() {}

func (x *GetRecommendRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendRoomRsp.ProtoReflect.Descriptor instead.
func (*GetRecommendRoomRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{5}
}

func (x *GetRecommendRoomRsp) GetInfo_Map() map[string]*RoomInfo {
	if x != nil {
		return x.Info_Map
	}
	return nil
}

// BatchGetRoomInfoWithUserID 用户id查询房间信息
type BatchGetRoomInfoWithUserIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIdList []string `protobuf:"bytes,1,rep,name=user_id_list,json=userIdList,proto3" json:"user_id_list,omitempty"` //用户id列表(q音为加密uid)
}

func (x *BatchGetRoomInfoWithUserIDReq) Reset() {
	*x = BatchGetRoomInfoWithUserIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetRoomInfoWithUserIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetRoomInfoWithUserIDReq) ProtoMessage() {}

func (x *BatchGetRoomInfoWithUserIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetRoomInfoWithUserIDReq.ProtoReflect.Descriptor instead.
func (*BatchGetRoomInfoWithUserIDReq) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{6}
}

func (x *BatchGetRoomInfoWithUserIDReq) GetUserIdList() []string {
	if x != nil {
		return x.UserIdList
	}
	return nil
}

type BatchGetRoomInfoWithUserIDRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info_Map   map[string]*RoomInfo `protobuf:"bytes,1,rep,name=info_Map,json=infoMap,proto3" json:"info_Map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`            //直播房间信息
	KtvInfoMap map[string]*RoomInfo `protobuf:"bytes,2,rep,name=ktv_info_map,json=ktvInfoMap,proto3" json:"ktv_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //歌房房间信息，K歌平台适用，K歌直播和歌房可以同时存在
}

func (x *BatchGetRoomInfoWithUserIDRsp) Reset() {
	*x = BatchGetRoomInfoWithUserIDRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetRoomInfoWithUserIDRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetRoomInfoWithUserIDRsp) ProtoMessage() {}

func (x *BatchGetRoomInfoWithUserIDRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_adapter_room_adapter_room_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetRoomInfoWithUserIDRsp.ProtoReflect.Descriptor instead.
func (*BatchGetRoomInfoWithUserIDRsp) Descriptor() ([]byte, []int) {
	return file_pb_adapter_room_adapter_room_proto_rawDescGZIP(), []int{7}
}

func (x *BatchGetRoomInfoWithUserIDRsp) GetInfo_Map() map[string]*RoomInfo {
	if x != nil {
		return x.Info_Map
	}
	return nil
}

func (x *BatchGetRoomInfoWithUserIDRsp) GetKtvInfoMap() map[string]*RoomInfo {
	if x != nil {
		return x.KtvInfoMap
	}
	return nil
}

var File_pb_adapter_room_adapter_room_proto protoreflect.FileDescriptor

var file_pb_adapter_room_adapter_room_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f,
	0x6d, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f,
	0x6f, 0x6d, 0x1a, 0x1c, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72,
	0x6f, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x18, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x61,
	0x70, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x29, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07,
	0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x2a, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f,
	0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x22, 0x37, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x72, 0x6f,
	0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb4, 0x01, 0x0a,
	0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x4d, 0x61, 0x70,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x69, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x1a,
	0x52, 0x0a, 0x0c, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e,
	0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x79, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x66, 0x72, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0xb4,
	0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52,
	0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x4d,
	0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74,
	0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x69, 0x6e, 0x66, 0x6f, 0x4d, 0x61,
	0x70, 0x1a, 0x52, 0x0a, 0x0c, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f,
	0x6d, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x41, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x20, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xfe, 0x02, 0x0a, 0x1d, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a, 0x08, 0x69, 0x6e,
	0x66, 0x6f, 0x5f, 0x4d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61,
	0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x69, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x12,
	0x5d, 0x0a, 0x0c, 0x6b, 0x74, 0x76, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f,
	0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52,
	0x73, 0x70, 0x2e, 0x4b, 0x74, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0a, 0x6b, 0x74, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x1a, 0x52,
	0x0a, 0x0c, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x55, 0x0a, 0x0f, 0x4b, 0x74, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x93, 0x04, 0x0a, 0x0b, 0x41, 0x64,
	0x61, 0x70, 0x74, 0x65, 0x72, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x49, 0x0a, 0x0b, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74,
	0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74,
	0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x64,
	0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x58,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x6f,
	0x6f, 0x6d, 0x12, 0x21, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6f,
	0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x6f,
	0x6f, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f,
	0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x76, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x2b, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x6f,
	0x6f, 0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x73, 0x70,
	0x12, 0x4a, 0x0a, 0x0a, 0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x12, 0x1d,
	0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x42, 0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x42,
	0x69, 0x67, 0x48, 0x6f, 0x72, 0x6e, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x07,
	0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67,
	0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x73, 0x70, 0x42,
	0x44, 0x5a, 0x42, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x6f, 0x6f, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_adapter_room_adapter_room_proto_rawDescOnce sync.Once
	file_pb_adapter_room_adapter_room_proto_rawDescData = file_pb_adapter_room_adapter_room_proto_rawDesc
)

func file_pb_adapter_room_adapter_room_proto_rawDescGZIP() []byte {
	file_pb_adapter_room_adapter_room_proto_rawDescOnce.Do(func() {
		file_pb_adapter_room_adapter_room_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_adapter_room_adapter_room_proto_rawDescData)
	})
	return file_pb_adapter_room_adapter_room_proto_rawDescData
}

var file_pb_adapter_room_adapter_room_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_pb_adapter_room_adapter_room_proto_goTypes = []interface{}{
	(*GetRoomInfoReq)(nil),                // 0: adapter_room.GetRoomInfoReq
	(*GetRoomInfoRsp)(nil),                // 1: adapter_room.GetRoomInfoRsp
	(*BatchGetRoomInfoReq)(nil),           // 2: adapter_room.BatchGetRoomInfoReq
	(*BatchGetRoomInfoRsp)(nil),           // 3: adapter_room.BatchGetRoomInfoRsp
	(*GetRecommendRoomReq)(nil),           // 4: adapter_room.GetRecommendRoomReq
	(*GetRecommendRoomRsp)(nil),           // 5: adapter_room.GetRecommendRoomRsp
	(*BatchGetRoomInfoWithUserIDReq)(nil), // 6: adapter_room.BatchGetRoomInfoWithUserIDReq
	(*BatchGetRoomInfoWithUserIDRsp)(nil), // 7: adapter_room.BatchGetRoomInfoWithUserIDRsp
	nil,                                   // 8: adapter_room.BatchGetRoomInfoRsp.InfoMapEntry
	nil,                                   // 9: adapter_room.GetRecommendRoomRsp.InfoMapEntry
	nil,                                   // 10: adapter_room.BatchGetRoomInfoWithUserIDRsp.InfoMapEntry
	nil,                                   // 11: adapter_room.BatchGetRoomInfoWithUserIDRsp.KtvInfoMapEntry
	(*RoomInfo)(nil),                      // 12: adapter_room.RoomInfo
	(*adapter.BigHornMsgReq)(nil),         // 13: component.game.BigHornMsgReq
	(*adapter.RoomMsgReq)(nil),            // 14: component.game.RoomMsgReq
	(*adapter.BigHornMsgRsp)(nil),         // 15: component.game.BigHornMsgRsp
	(*adapter.RoomMsgRsp)(nil),            // 16: component.game.RoomMsgRsp
}
var file_pb_adapter_room_adapter_room_proto_depIdxs = []int32{
	12, // 0: adapter_room.GetRoomInfoRsp.info:type_name -> adapter_room.RoomInfo
	8,  // 1: adapter_room.BatchGetRoomInfoRsp.info_Map:type_name -> adapter_room.BatchGetRoomInfoRsp.InfoMapEntry
	9,  // 2: adapter_room.GetRecommendRoomRsp.info_Map:type_name -> adapter_room.GetRecommendRoomRsp.InfoMapEntry
	10, // 3: adapter_room.BatchGetRoomInfoWithUserIDRsp.info_Map:type_name -> adapter_room.BatchGetRoomInfoWithUserIDRsp.InfoMapEntry
	11, // 4: adapter_room.BatchGetRoomInfoWithUserIDRsp.ktv_info_map:type_name -> adapter_room.BatchGetRoomInfoWithUserIDRsp.KtvInfoMapEntry
	12, // 5: adapter_room.BatchGetRoomInfoRsp.InfoMapEntry.value:type_name -> adapter_room.RoomInfo
	12, // 6: adapter_room.GetRecommendRoomRsp.InfoMapEntry.value:type_name -> adapter_room.RoomInfo
	12, // 7: adapter_room.BatchGetRoomInfoWithUserIDRsp.InfoMapEntry.value:type_name -> adapter_room.RoomInfo
	12, // 8: adapter_room.BatchGetRoomInfoWithUserIDRsp.KtvInfoMapEntry.value:type_name -> adapter_room.RoomInfo
	0,  // 9: adapter_room.AdapterRoom.GetRoomInfo:input_type -> adapter_room.GetRoomInfoReq
	2,  // 10: adapter_room.AdapterRoom.BatchGetRoomInfo:input_type -> adapter_room.BatchGetRoomInfoReq
	4,  // 11: adapter_room.AdapterRoom.GetRecommendRoom:input_type -> adapter_room.GetRecommendRoomReq
	6,  // 12: adapter_room.AdapterRoom.BatchGetRoomInfoWithUserID:input_type -> adapter_room.BatchGetRoomInfoWithUserIDReq
	13, // 13: adapter_room.AdapterRoom.BigHornMsg:input_type -> component.game.BigHornMsgReq
	14, // 14: adapter_room.AdapterRoom.RoomMsg:input_type -> component.game.RoomMsgReq
	1,  // 15: adapter_room.AdapterRoom.GetRoomInfo:output_type -> adapter_room.GetRoomInfoRsp
	3,  // 16: adapter_room.AdapterRoom.BatchGetRoomInfo:output_type -> adapter_room.BatchGetRoomInfoRsp
	5,  // 17: adapter_room.AdapterRoom.GetRecommendRoom:output_type -> adapter_room.GetRecommendRoomRsp
	7,  // 18: adapter_room.AdapterRoom.BatchGetRoomInfoWithUserID:output_type -> adapter_room.BatchGetRoomInfoWithUserIDRsp
	15, // 19: adapter_room.AdapterRoom.BigHornMsg:output_type -> component.game.BigHornMsgRsp
	16, // 20: adapter_room.AdapterRoom.RoomMsg:output_type -> component.game.RoomMsgRsp
	15, // [15:21] is the sub-list for method output_type
	9,  // [9:15] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_adapter_room_adapter_room_proto_init() }
func file_pb_adapter_room_adapter_room_proto_init() {
	if File_pb_adapter_room_adapter_room_proto != nil {
		return
	}
	file_pb_adapter_room_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_adapter_room_adapter_room_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetRoomInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetRoomInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetRoomInfoWithUserIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_adapter_room_adapter_room_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetRoomInfoWithUserIDRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_adapter_room_adapter_room_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_adapter_room_adapter_room_proto_goTypes,
		DependencyIndexes: file_pb_adapter_room_adapter_room_proto_depIdxs,
		MessageInfos:      file_pb_adapter_room_adapter_room_proto_msgTypes,
	}.Build()
	File_pb_adapter_room_adapter_room_proto = out.File
	file_pb_adapter_room_adapter_room_proto_rawDesc = nil
	file_pb_adapter_room_adapter_room_proto_goTypes = nil
	file_pb_adapter_room_adapter_room_proto_depIdxs = nil
}
