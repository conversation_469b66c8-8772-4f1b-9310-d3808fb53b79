{"swagger": "2.0", "info": {"title": "pb/adapter_room/adapter_room.proto", "version": "version not set"}, "tags": [{"name": "AdapterRoom"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/adapter_room.AdapterRoom/BatchGetRoomInfo": {"post": {"summary": "批量查询房间信息", "operationId": "AdapterRoom_BatchGetRoomInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_roomBatchGetRoomInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_roomBatchGetRoomInfoReq"}}], "tags": ["AdapterRoom"]}}, "/adapter_room.AdapterRoom/BatchGetRoomInfoWithUserID": {"post": {"summary": "用户ID查询房间信息", "operationId": "AdapterRoom_BatchGetRoomInfoWithUserID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_roomBatchGetRoomInfoWithUserIDRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_roomBatchGetRoomInfoWithUserIDReq"}}], "tags": ["AdapterRoom"]}}, "/adapter_room.AdapterRoom/BigHornMsg": {"post": {"summary": "发送大喇叭", "operationId": "AdapterRoom_BigHornMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameBigHornMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameBigHornMsgReq"}}], "tags": ["AdapterRoom"]}}, "/adapter_room.AdapterRoom/GetRecommendRoom": {"post": {"summary": "推荐列表", "operationId": "AdapterRoom_GetRecommendRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_roomGetRecommendRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_roomGetRecommendRoomReq"}}], "tags": ["AdapterRoom"]}}, "/adapter_room.AdapterRoom/GetRoomInfo": {"post": {"summary": "查询房间信息", "operationId": "AdapterRoom_GetRoomInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/adapter_roomGetRoomInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/adapter_roomGetRoomInfoReq"}}], "tags": ["AdapterRoom"]}}, "/adapter_room.AdapterRoom/RoomMsg": {"post": {"summary": "发送房间消息", "operationId": "AdapterRoom_RoomMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameRoomMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameRoomMsgReq"}}], "tags": ["AdapterRoom"]}}}, "definitions": {"adapter_roomBatchGetRoomInfoReq": {"type": "object", "properties": {"roomIdList": {"type": "array", "items": {"type": "string"}, "title": "房间id列表"}}, "title": "BatchGetRoomInfo 批量查询房间信息"}, "adapter_roomBatchGetRoomInfoRsp": {"type": "object", "properties": {"infoMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_roomRoomInfo"}, "title": "房间信息"}}}, "adapter_roomBatchGetRoomInfoWithUserIDReq": {"type": "object", "properties": {"userIdList": {"type": "array", "items": {"type": "string"}, "title": "用户id列表(q音为加密uid)"}}, "title": "BatchGetRoomInfoWithUserID 用户id查询房间信息"}, "adapter_roomBatchGetRoomInfoWithUserIDRsp": {"type": "object", "properties": {"infoMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_roomRoomInfo"}, "title": "直播房间信息"}, "ktvInfoMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_roomRoomInfo"}, "title": "歌房房间信息，K歌平台适用，K歌直播和歌房可以同时存在"}}}, "adapter_roomGetRecommendRoomReq": {"type": "object", "properties": {"userId": {"type": "string", "title": "用户id"}, "idType": {"type": "integer", "format": "int32", "title": "UserIDType 用户id类型;默认0为openid;1为端内登录态uid;2为加密uid"}, "fromTag": {"type": "string", "title": "推荐标识"}, "appId": {"type": "string", "title": "小游戏AppId"}}, "title": "GetRecommendRoom 推荐列表"}, "adapter_roomGetRecommendRoomRsp": {"type": "object", "properties": {"infoMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/adapter_roomRoomInfo"}, "title": "房间信息"}}}, "adapter_roomGetRoomInfoReq": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间id"}}, "title": "GetRoomInfo 查询房间信息"}, "adapter_roomGetRoomInfoRsp": {"type": "object", "properties": {"info": {"$ref": "#/definitions/adapter_roomRoomInfo", "title": "房间信息"}}}, "adapter_roomRoomInfo": {"type": "object", "properties": {"roomId": {"type": "string", "title": "房间id"}, "showId": {"type": "string", "title": "showid 场次id"}, "anchorId": {"type": "string", "title": "房主id"}, "roomName": {"type": "string", "title": "房间名称"}, "roomLogo": {"type": "string", "title": "房间图标"}, "roomStatus": {"type": "integer", "format": "int32", "title": "房间状态 0未知 1=running 2=end"}, "roomStart": {"type": "integer", "format": "int32", "title": "开始时间"}, "roomEnd": {"type": "integer", "format": "int32", "title": "结束时间"}, "roomType": {"type": "integer", "format": "int32", "title": "房间类型"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展信息"}, "anchorEncodeId": {"type": "string", "title": "主播加密uid"}}}, "gameBigHornMsgReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "open id"}, "msgId": {"type": "string", "title": "消息ID msg_id"}, "content": {"type": "string", "title": "文案 content"}, "attach": {"type": "object", "additionalProperties": {"type": "string"}, "title": "附加内容 attach\n可选：跳转房间后打开的链接 map[\"afterH5URL\"] = \"url\"\n可选：大喇叭样式ID，不传用默认的 map[\"configID\"] = \"配置ID\""}, "openH5URL": {"type": "string", "title": "打开H5链接，和跳转房间同时传时优先使用"}, "roomId": {"type": "string", "title": "跳转房间ID"}, "idType": {"type": "integer", "format": "int32", "description": "UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin", "title": "userid类型"}, "anchorOpenId": {"type": "string", "description": "一般在大喇叭的前端展示主播头像", "title": "主播 open id"}}}, "gameBigHornMsgRsp": {"type": "object"}, "gameRoomMsgReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "openId": {"type": "string", "title": "open id"}, "msgText": {"type": "string", "title": "消息文案 msg_text"}, "msgType": {"$ref": "#/definitions/gameRoomMsgType", "title": "消息类型"}, "roomId": {"type": "string", "title": "房间 ID"}, "toOpenId": {"type": "string", "title": "to open id"}, "isC2c": {"type": "boolean", "title": "c2c 消息"}, "extData": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展数据\n可选：跳转文案 map[\"jumpText\"]\n可选：跳转链接 map[\"jumpURL\"]\n可选：发RoomMsgTex时，是否广播消息 map[\"uIsGlobalMsg\"]  1:是 0:否"}, "idType": {"type": "integer", "format": "int32", "title": "UserIDType 用户id类型;0为open_id=openid;1为open_id=端内登录态uin;2为加密uin"}}}, "gameRoomMsgRsp": {"type": "object"}, "gameRoomMsgType": {"type": "string", "enum": ["RoomMsgUnknown", "RoomMsgText", "RoomMsgAt"], "default": "RoomMsgUnknown", "title": "- RoomMsgText: 文本消息\n - RoomMsgAt: at消息，此时不需要传room_id、is_c2c"}, "googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}}}