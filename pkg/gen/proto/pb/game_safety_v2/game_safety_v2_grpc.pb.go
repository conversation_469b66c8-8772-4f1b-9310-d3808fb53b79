// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_safety_v2/game_safety_v2.proto

package game_safety_v2

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	SafetyV2_SyncSafeCheck_FullMethodName = "/game.SafetyV2/SyncSafeCheck"
)

// SafetyV2Client is the client API for SafetyV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 安全上报接口
type SafetyV2Client interface {
	// 图文同步审查
	SyncSafeCheck(ctx context.Context, in *SyncSafeCheckReq, opts ...grpc.CallOption) (*SyncSafeCheckRsp, error)
}

type safetyV2Client struct {
	cc grpc.ClientConnInterface
}

func NewSafetyV2Client(cc grpc.ClientConnInterface) SafetyV2Client {
	return &safetyV2Client{cc}
}

func (c *safetyV2Client) SyncSafeCheck(ctx context.Context, in *SyncSafeCheckReq, opts ...grpc.CallOption) (*SyncSafeCheckRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncSafeCheckRsp)
	err := c.cc.Invoke(ctx, SafetyV2_SyncSafeCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SafetyV2Server is the server API for SafetyV2 service.
// All implementations should embed UnimplementedSafetyV2Server
// for forward compatibility
//
// 安全上报接口
type SafetyV2Server interface {
	// 图文同步审查
	SyncSafeCheck(context.Context, *SyncSafeCheckReq) (*SyncSafeCheckRsp, error)
}

// UnimplementedSafetyV2Server should be embedded to have forward compatible implementations.
type UnimplementedSafetyV2Server struct {
}

func (UnimplementedSafetyV2Server) SyncSafeCheck(context.Context, *SyncSafeCheckReq) (*SyncSafeCheckRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncSafeCheck not implemented")
}

// UnsafeSafetyV2Server may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SafetyV2Server will
// result in compilation errors.
type UnsafeSafetyV2Server interface {
	mustEmbedUnimplementedSafetyV2Server()
}

func RegisterSafetyV2Server(s grpc.ServiceRegistrar, srv SafetyV2Server) {
	s.RegisterService(&SafetyV2_ServiceDesc, srv)
}

func _SafetyV2_SyncSafeCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncSafeCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SafetyV2Server).SyncSafeCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SafetyV2_SyncSafeCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SafetyV2Server).SyncSafeCheck(ctx, req.(*SyncSafeCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SafetyV2_ServiceDesc is the grpc.ServiceDesc for SafetyV2 service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SafetyV2_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game.SafetyV2",
	HandlerType: (*SafetyV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncSafeCheck",
			Handler:    _SafetyV2_SyncSafeCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_safety_v2/game_safety_v2.proto",
}
