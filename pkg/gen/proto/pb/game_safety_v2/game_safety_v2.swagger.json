{"swagger": "2.0", "info": {"title": "pb/game_safety_v2/game_safety_v2.proto", "version": "version not set"}, "tags": [{"name": "SafetyV2"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game.SafetyV2/SyncSafeCheck": {"post": {"summary": "图文同步审查", "description": "在请求时SyncSafeCheck将实现的异步回调接口地址填入CallbackMsg\n 异步策略人审结果通知回调, 接收安全侧异步回调, 接入业务方自己实现\n 定义见 open_game_open_api.proto\n 实现rpc(SafeCheckCallbackV2Req,SafeCheckCallbackV2Rsp)", "operationId": "SafetyV2_SyncSafeCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameSyncSafeCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameSyncSafeCheckReq"}}], "tags": ["SafetyV2"]}}}, "definitions": {"gameBasicInfo": {"type": "object", "properties": {"apiVersion": {"type": "string", "title": "填v1.0"}, "appId": {"type": "string", "title": "安全侧分配"}, "category": {"type": "string", "title": "安全侧分配"}, "platform": {"type": "string", "title": "平台(kg/music)"}, "sendTimeMillisecond": {"type": "string", "title": "时间戳字符串"}, "dataId": {"type": "string", "title": "该字段用于返回检测对象对应请求参数中的DataId，与输入的DataId字段中的内容对应。注意：此字段可能返回 null，表示取不到有效值。"}, "callback": {"type": "string", "title": "回传参数, 解析CallbackMsg的json字符串"}}}, "gameCheckInfo": {"type": "object", "properties": {"textContent": {"type": "string"}, "title": {"type": "string"}, "atInfo": {"type": "string", "title": "用于保存 @内容;"}, "comment": {"type": "string", "title": "分享或转载理由"}, "imageUrl": {"type": "array", "items": {"type": "string"}, "title": "图片"}, "audioUrl": {"type": "string", "title": "音频"}, "videoUrl": {"type": "string", "title": "视频"}}}, "gameCheckResultDetail": {"type": "object", "properties": {"hitType": {"type": "integer", "format": "int32", "title": "打击方式 0 合法 1 拦截"}, "strPrompt": {"type": "string", "title": "提示语"}, "hitReason": {"type": "string", "format": "uint64", "title": "打击原因枚举 0 安全 108 文本机审 109 图片同步审查 101 频控 除了0其他都不正常"}, "strReason": {"type": "string", "title": "打击原因"}, "strategyConclusion": {"type": "string", "title": "结论"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "其他信息"}}}, "gameDeviceInfo": {"type": "object", "properties": {"ip": {"type": "string"}, "mac": {"type": "string"}, "imei": {"type": "string"}, "idfa": {"type": "string"}, "idfv": {"type": "string"}, "mobileFlag": {"type": "integer", "format": "int64", "title": "是否来自手机"}, "mobleQUA": {"type": "string", "title": "qua"}, "uuid": {"type": "string"}, "udid": {"type": "string"}, "qimei36": {"type": "string"}, "deviceInfo": {"type": "string"}}}, "gameSyncSafeCheckReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "device": {"$ref": "#/definitions/gameDeviceInfo", "title": "设备信息, 有的尽量填"}, "basic": {"$ref": "#/definitions/gameBasicInfo", "title": "基础信息, 包含业务透传信息"}, "check": {"$ref": "#/definitions/gameCheckInfo", "title": "送审信息"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传信息"}}}, "gameSyncSafeCheckRsp": {"type": "object", "properties": {"suggestion": {"type": "string", "title": "Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过"}, "detail": {"$ref": "#/definitions/gameCheckResultDetail", "title": "审查详情"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}