// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_safety_v2/game_safety_v2.proto

package game_safety_v2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncSafeCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string            `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Device *DeviceInfo       `protobuf:"bytes,3,opt,name=device,proto3" json:"device,omitempty"`                                                                                         // 设备信息, 有的尽量填
	Basic  *BasicInfo        `protobuf:"bytes,4,opt,name=basic,proto3" json:"basic,omitempty"`                                                                                           // 基础信息, 包含业务透传信息
	Check  *CheckInfo        `protobuf:"bytes,5,opt,name=check,proto3" json:"check,omitempty"`                                                                                           // 送审信息
	MapExt map[string]string `protobuf:"bytes,6,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传信息
}

func (x *SyncSafeCheckReq) Reset() {
	*x = SyncSafeCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSafeCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSafeCheckReq) ProtoMessage() {}

func (x *SyncSafeCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSafeCheckReq.ProtoReflect.Descriptor instead.
func (*SyncSafeCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{0}
}

func (x *SyncSafeCheckReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SyncSafeCheckReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SyncSafeCheckReq) GetDevice() *DeviceInfo {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *SyncSafeCheckReq) GetBasic() *BasicInfo {
	if x != nil {
		return x.Basic
	}
	return nil
}

func (x *SyncSafeCheckReq) GetCheck() *CheckInfo {
	if x != nil {
		return x.Check
	}
	return nil
}

func (x *SyncSafeCheckReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type SyncSafeCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Suggestion string             `protobuf:"bytes,1,opt,name=suggestion,proto3" json:"suggestion,omitempty"` // Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过
	Detail     *CheckResultDetail `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`         // 审查详情
}

func (x *SyncSafeCheckRsp) Reset() {
	*x = SyncSafeCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSafeCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSafeCheckRsp) ProtoMessage() {}

func (x *SyncSafeCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSafeCheckRsp.ProtoReflect.Descriptor instead.
func (*SyncSafeCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{1}
}

func (x *SyncSafeCheckRsp) GetSuggestion() string {
	if x != nil {
		return x.Suggestion
	}
	return ""
}

func (x *SyncSafeCheckRsp) GetDetail() *CheckResultDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type CheckResultDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitType            int32             `protobuf:"varint,1,opt,name=hitType,proto3" json:"hitType,omitempty"`                                                                                      // 打击方式 0 合法 1 拦截
	StrPrompt          string            `protobuf:"bytes,2,opt,name=strPrompt,proto3" json:"strPrompt,omitempty"`                                                                                   // 提示语
	HitReason          uint64            `protobuf:"varint,3,opt,name=hitReason,proto3" json:"hitReason,omitempty"`                                                                                  // 打击原因枚举 0 安全 108 文本机审 109 图片同步审查 101 频控 除了0其他都不正常
	StrReason          string            `protobuf:"bytes,4,opt,name=strReason,proto3" json:"strReason,omitempty"`                                                                                   // 打击原因
	StrategyConclusion string            `protobuf:"bytes,5,opt,name=strategyConclusion,proto3" json:"strategyConclusion,omitempty"`                                                                 // 结论
	MapExt             map[string]string `protobuf:"bytes,6,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 其他信息
}

func (x *CheckResultDetail) Reset() {
	*x = CheckResultDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckResultDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckResultDetail) ProtoMessage() {}

func (x *CheckResultDetail) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckResultDetail.ProtoReflect.Descriptor instead.
func (*CheckResultDetail) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{2}
}

func (x *CheckResultDetail) GetHitType() int32 {
	if x != nil {
		return x.HitType
	}
	return 0
}

func (x *CheckResultDetail) GetStrPrompt() string {
	if x != nil {
		return x.StrPrompt
	}
	return ""
}

func (x *CheckResultDetail) GetHitReason() uint64 {
	if x != nil {
		return x.HitReason
	}
	return 0
}

func (x *CheckResultDetail) GetStrReason() string {
	if x != nil {
		return x.StrReason
	}
	return ""
}

func (x *CheckResultDetail) GetStrategyConclusion() string {
	if x != nil {
		return x.StrategyConclusion
	}
	return ""
}

func (x *CheckResultDetail) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type CheckInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TextContent string   `protobuf:"bytes,1,opt,name=textContent,proto3" json:"textContent,omitempty"`
	Title       string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	AtInfo      string   `protobuf:"bytes,3,opt,name=atInfo,proto3" json:"atInfo,omitempty"`     // 用于保存 @内容;
	Comment     string   `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`   // 分享或转载理由
	ImageUrl    []string `protobuf:"bytes,5,rep,name=imageUrl,proto3" json:"imageUrl,omitempty"` // 图片
	AudioUrl    string   `protobuf:"bytes,6,opt,name=audioUrl,proto3" json:"audioUrl,omitempty"` // 音频
	VideoUrl    string   `protobuf:"bytes,7,opt,name=videoUrl,proto3" json:"videoUrl,omitempty"` // 视频
}

func (x *CheckInfo) Reset() {
	*x = CheckInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInfo) ProtoMessage() {}

func (x *CheckInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInfo.ProtoReflect.Descriptor instead.
func (*CheckInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{3}
}

func (x *CheckInfo) GetTextContent() string {
	if x != nil {
		return x.TextContent
	}
	return ""
}

func (x *CheckInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CheckInfo) GetAtInfo() string {
	if x != nil {
		return x.AtInfo
	}
	return ""
}

func (x *CheckInfo) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *CheckInfo) GetImageUrl() []string {
	if x != nil {
		return x.ImageUrl
	}
	return nil
}

func (x *CheckInfo) GetAudioUrl() string {
	if x != nil {
		return x.AudioUrl
	}
	return ""
}

func (x *CheckInfo) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

type CallbackMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallbackService string `protobuf:"bytes,1,opt,name=callbackService,proto3" json:"callbackService,omitempty"` // 业务自定义回调路由, eg: kg.game.DemoServer/callback
	CustomData      []byte `protobuf:"bytes,2,opt,name=customData,proto3" json:"customData,omitempty"`           // 业务自定义回调透传数据
}

func (x *CallbackMsg) Reset() {
	*x = CallbackMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackMsg) ProtoMessage() {}

func (x *CallbackMsg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackMsg.ProtoReflect.Descriptor instead.
func (*CallbackMsg) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{4}
}

func (x *CallbackMsg) GetCallbackService() string {
	if x != nil {
		return x.CallbackService
	}
	return ""
}

func (x *CallbackMsg) GetCustomData() []byte {
	if x != nil {
		return x.CustomData
	}
	return nil
}

type BasicInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiVersion          string `protobuf:"bytes,1,opt,name=apiVersion,proto3" json:"apiVersion,omitempty"`                   // 填v1.0
	AppId               string `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`                             // 安全侧分配
	Category            string `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`                       // 安全侧分配
	Platform            string `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`                       // 平台(kg/music)
	SendTimeMillisecond string `protobuf:"bytes,5,opt,name=sendTimeMillisecond,proto3" json:"sendTimeMillisecond,omitempty"` // 时间戳字符串
	DataId              string `protobuf:"bytes,6,opt,name=dataId,proto3" json:"dataId,omitempty"`                           // 该字段用于返回检测对象对应请求参数中的DataId，与输入的DataId字段中的内容对应。注意：此字段可能返回 null，表示取不到有效值。
	Callback            string `protobuf:"bytes,7,opt,name=callback,proto3" json:"callback,omitempty"`                       // 回传参数, 解析CallbackMsg的json字符串
}

func (x *BasicInfo) Reset() {
	*x = BasicInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicInfo) ProtoMessage() {}

func (x *BasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicInfo.ProtoReflect.Descriptor instead.
func (*BasicInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{5}
}

func (x *BasicInfo) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *BasicInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BasicInfo) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *BasicInfo) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *BasicInfo) GetSendTimeMillisecond() string {
	if x != nil {
		return x.SendTimeMillisecond
	}
	return ""
}

func (x *BasicInfo) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *BasicInfo) GetCallback() string {
	if x != nil {
		return x.Callback
	}
	return ""
}

type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip         string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Mac        string `protobuf:"bytes,2,opt,name=mac,proto3" json:"mac,omitempty"`
	Imei       string `protobuf:"bytes,3,opt,name=imei,proto3" json:"imei,omitempty"`
	Idfa       string `protobuf:"bytes,4,opt,name=idfa,proto3" json:"idfa,omitempty"`
	Idfv       string `protobuf:"bytes,5,opt,name=idfv,proto3" json:"idfv,omitempty"`
	MobileFlag uint32 `protobuf:"varint,6,opt,name=mobileFlag,proto3" json:"mobileFlag,omitempty"` // 是否来自手机
	MobleQUA   string `protobuf:"bytes,7,opt,name=mobleQUA,proto3" json:"mobleQUA,omitempty"`      // qua
	Uuid       string `protobuf:"bytes,8,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Udid       string `protobuf:"bytes,9,opt,name=udid,proto3" json:"udid,omitempty"`
	Qimei36    string `protobuf:"bytes,10,opt,name=qimei36,proto3" json:"qimei36,omitempty"`
	DeviceInfo string `protobuf:"bytes,11,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP(), []int{6}
}

func (x *DeviceInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeviceInfo) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *DeviceInfo) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *DeviceInfo) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *DeviceInfo) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *DeviceInfo) GetMobileFlag() uint32 {
	if x != nil {
		return x.MobileFlag
	}
	return 0
}

func (x *DeviceInfo) GetMobleQUA() string {
	if x != nil {
		return x.MobleQUA
	}
	return ""
}

func (x *DeviceInfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DeviceInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *DeviceInfo) GetQimei36() string {
	if x != nil {
		return x.Qimei36
	}
	return ""
}

func (x *DeviceInfo) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

var File_pb_game_safety_v2_game_safety_v2_proto protoreflect.FileDescriptor

var file_pb_game_safety_v2_game_safety_v2_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79,
	0x5f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f,
	0x76, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65, 0x22, 0xaf,
	0x02, 0x0a, 0x10, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x62,
	0x61, 0x73, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x62, 0x61, 0x73,
	0x69, 0x63, 0x12, 0x25, 0x0a, 0x05, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x3a, 0x0a, 0x06, 0x6d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x63, 0x0a, 0x10, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xaf, 0x02, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x68,
	0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x68, 0x69,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x50, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x50, 0x72, 0x6f,
	0x6d, 0x70, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x68, 0x69, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x2e, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x43, 0x6f, 0x6e, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x3b, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39, 0x0a, 0x0b,
	0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc9, 0x01, 0x0a, 0x09, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x75, 0x64, 0x69, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x75, 0x64, 0x69, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x55, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x55, 0x72, 0x6c, 0x22, 0x57, 0x0a, 0x0b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d,
	0x73, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x22, 0xdf, 0x01, 0x0a,
	0x09, 0x42, 0x61, 0x73, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70,
	0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x30, 0x0a, 0x13, 0x73, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x69, 0x6c, 0x6c, 0x69, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61,
	0x74, 0x61, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x88,
	0x02, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x6d, 0x65, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x76, 0x12, 0x1e, 0x0a, 0x0a, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6d,
	0x6f, 0x62, 0x6c, 0x65, 0x51, 0x55, 0x41, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d,
	0x6f, 0x62, 0x6c, 0x65, 0x51, 0x55, 0x41, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x64, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x32, 0x4b, 0x0a, 0x08, 0x53, 0x61, 0x66,
	0x65, 0x74, 0x79, 0x56, 0x32, 0x12, 0x3f, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x61, 0x66,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x42, 0x46, 0x5a, 0x44, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x61, 0x66, 0x65, 0x74, 0x79, 0x5f, 0x76, 0x32, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_safety_v2_game_safety_v2_proto_rawDescOnce sync.Once
	file_pb_game_safety_v2_game_safety_v2_proto_rawDescData = file_pb_game_safety_v2_game_safety_v2_proto_rawDesc
)

func file_pb_game_safety_v2_game_safety_v2_proto_rawDescGZIP() []byte {
	file_pb_game_safety_v2_game_safety_v2_proto_rawDescOnce.Do(func() {
		file_pb_game_safety_v2_game_safety_v2_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_safety_v2_game_safety_v2_proto_rawDescData)
	})
	return file_pb_game_safety_v2_game_safety_v2_proto_rawDescData
}

var file_pb_game_safety_v2_game_safety_v2_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_pb_game_safety_v2_game_safety_v2_proto_goTypes = []interface{}{
	(*SyncSafeCheckReq)(nil),  // 0: game.SyncSafeCheckReq
	(*SyncSafeCheckRsp)(nil),  // 1: game.SyncSafeCheckRsp
	(*CheckResultDetail)(nil), // 2: game.CheckResultDetail
	(*CheckInfo)(nil),         // 3: game.CheckInfo
	(*CallbackMsg)(nil),       // 4: game.CallbackMsg
	(*BasicInfo)(nil),         // 5: game.BasicInfo
	(*DeviceInfo)(nil),        // 6: game.DeviceInfo
	nil,                       // 7: game.SyncSafeCheckReq.MapExtEntry
	nil,                       // 8: game.CheckResultDetail.MapExtEntry
}
var file_pb_game_safety_v2_game_safety_v2_proto_depIdxs = []int32{
	6, // 0: game.SyncSafeCheckReq.device:type_name -> game.DeviceInfo
	5, // 1: game.SyncSafeCheckReq.basic:type_name -> game.BasicInfo
	3, // 2: game.SyncSafeCheckReq.check:type_name -> game.CheckInfo
	7, // 3: game.SyncSafeCheckReq.mapExt:type_name -> game.SyncSafeCheckReq.MapExtEntry
	2, // 4: game.SyncSafeCheckRsp.detail:type_name -> game.CheckResultDetail
	8, // 5: game.CheckResultDetail.mapExt:type_name -> game.CheckResultDetail.MapExtEntry
	0, // 6: game.SafetyV2.SyncSafeCheck:input_type -> game.SyncSafeCheckReq
	1, // 7: game.SafetyV2.SyncSafeCheck:output_type -> game.SyncSafeCheckRsp
	7, // [7:8] is the sub-list for method output_type
	6, // [6:7] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_pb_game_safety_v2_game_safety_v2_proto_init() }
func file_pb_game_safety_v2_game_safety_v2_proto_init() {
	if File_pb_game_safety_v2_game_safety_v2_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSafeCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSafeCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckResultDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BasicInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_safety_v2_game_safety_v2_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_safety_v2_game_safety_v2_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_safety_v2_game_safety_v2_proto_goTypes,
		DependencyIndexes: file_pb_game_safety_v2_game_safety_v2_proto_depIdxs,
		MessageInfos:      file_pb_game_safety_v2_game_safety_v2_proto_msgTypes,
	}.Build()
	File_pb_game_safety_v2_game_safety_v2_proto = out.File
	file_pb_game_safety_v2_game_safety_v2_proto_rawDesc = nil
	file_pb_game_safety_v2_game_safety_v2_proto_goTypes = nil
	file_pb_game_safety_v2_game_safety_v2_proto_depIdxs = nil
}
