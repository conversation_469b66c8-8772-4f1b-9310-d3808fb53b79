// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/asyncwork/asyncwork.proto

package asyncwork

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkStatus int32

const (
	WorkStatus_WorkStatusInvalid     WorkStatus = 0
	WorkStatus_WorkStatusSuccess     WorkStatus = 1
	WorkStatus_WorkStatusFailure     WorkStatus = 2
	WorkStatus_WorkStatusSystemError WorkStatus = 3
)

// Enum value maps for WorkStatus.
var (
	WorkStatus_name = map[int32]string{
		0: "WorkStatusInvalid",
		1: "WorkStatusSuccess",
		2: "WorkStatusFailure",
		3: "WorkStatusSystemError",
	}
	WorkStatus_value = map[string]int32{
		"WorkStatusInvalid":     0,
		"WorkStatusSuccess":     1,
		"WorkStatusFailure":     2,
		"WorkStatusSystemError": 3,
	}
)

func (x WorkStatus) Enum() *WorkStatus {
	p := new(WorkStatus)
	*p = x
	return p
}

func (x WorkStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_asyncwork_asyncwork_proto_enumTypes[0].Descriptor()
}

func (WorkStatus) Type() protoreflect.EnumType {
	return &file_pb_asyncwork_asyncwork_proto_enumTypes[0]
}

func (x WorkStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkStatus.Descriptor instead.
func (WorkStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_asyncwork_asyncwork_proto_rawDescGZIP(), []int{0}
}

type WorkResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status WorkStatus `protobuf:"varint,1,opt,name=status,proto3,enum=asyncwork.WorkStatus" json:"status,omitempty"`
	Data   []byte     `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *WorkResult) Reset() {
	*x = WorkResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_asyncwork_asyncwork_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkResult) ProtoMessage() {}

func (x *WorkResult) ProtoReflect() protoreflect.Message {
	mi := &file_pb_asyncwork_asyncwork_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkResult.ProtoReflect.Descriptor instead.
func (*WorkResult) Descriptor() ([]byte, []int) {
	return file_pb_asyncwork_asyncwork_proto_rawDescGZIP(), []int{0}
}

func (x *WorkResult) GetStatus() WorkStatus {
	if x != nil {
		return x.Status
	}
	return WorkStatus_WorkStatusInvalid
}

func (x *WorkResult) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_pb_asyncwork_asyncwork_proto protoreflect.FileDescriptor

var file_pb_asyncwork_asyncwork_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x61,
	0x73, 0x79, 0x6e, 0x63, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09,
	0x61, 0x73, 0x79, 0x6e, 0x63, 0x77, 0x6f, 0x72, 0x6b, 0x22, 0x4f, 0x0a, 0x0a, 0x57, 0x6f, 0x72,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x2a, 0x6c, 0x0a, 0x0a, 0x57, 0x6f,
	0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x15, 0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12,
	0x15, 0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x10, 0x02, 0x12, 0x19, 0x0a,
	0x15, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x03, 0x42, 0x41, 0x5a, 0x3f, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x77, 0x6f, 0x72, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_asyncwork_asyncwork_proto_rawDescOnce sync.Once
	file_pb_asyncwork_asyncwork_proto_rawDescData = file_pb_asyncwork_asyncwork_proto_rawDesc
)

func file_pb_asyncwork_asyncwork_proto_rawDescGZIP() []byte {
	file_pb_asyncwork_asyncwork_proto_rawDescOnce.Do(func() {
		file_pb_asyncwork_asyncwork_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_asyncwork_asyncwork_proto_rawDescData)
	})
	return file_pb_asyncwork_asyncwork_proto_rawDescData
}

var file_pb_asyncwork_asyncwork_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_asyncwork_asyncwork_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pb_asyncwork_asyncwork_proto_goTypes = []interface{}{
	(WorkStatus)(0),    // 0: asyncwork.WorkStatus
	(*WorkResult)(nil), // 1: asyncwork.WorkResult
}
var file_pb_asyncwork_asyncwork_proto_depIdxs = []int32{
	0, // 0: asyncwork.WorkResult.status:type_name -> asyncwork.WorkStatus
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_asyncwork_asyncwork_proto_init() }
func file_pb_asyncwork_asyncwork_proto_init() {
	if File_pb_asyncwork_asyncwork_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_asyncwork_asyncwork_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_asyncwork_asyncwork_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_asyncwork_asyncwork_proto_goTypes,
		DependencyIndexes: file_pb_asyncwork_asyncwork_proto_depIdxs,
		EnumInfos:         file_pb_asyncwork_asyncwork_proto_enumTypes,
		MessageInfos:      file_pb_asyncwork_asyncwork_proto_msgTypes,
	}.Build()
	File_pb_asyncwork_asyncwork_proto = out.File
	file_pb_asyncwork_asyncwork_proto_rawDesc = nil
	file_pb_asyncwork_asyncwork_proto_goTypes = nil
	file_pb_asyncwork_asyncwork_proto_depIdxs = nil
}
