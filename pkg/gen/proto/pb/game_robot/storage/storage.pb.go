// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_robot/storage/storage.proto

package storage

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RobotGender int32

const (
	RobotGender_RobotGenderNone   RobotGender = 0 // 未知
	RobotGender_RobotGenderMale   RobotGender = 1 // 男
	RobotGender_RobotGenderFemale RobotGender = 2 // 女
)

// Enum value maps for RobotGender.
var (
	RobotGender_name = map[int32]string{
		0: "RobotGenderNone",
		1: "RobotGenderMale",
		2: "RobotGenderFemale",
	}
	RobotGender_value = map[string]int32{
		"RobotGenderNone":   0,
		"RobotGenderMale":   1,
		"RobotGenderFemale": 2,
	}
)

func (x RobotGender) Enum() *RobotGender {
	p := new(RobotGender)
	*p = x
	return p
}

func (x RobotGender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RobotGender) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_robot_storage_storage_proto_enumTypes[0].Descriptor()
}

func (RobotGender) Type() protoreflect.EnumType {
	return &file_pb_game_robot_storage_storage_proto_enumTypes[0]
}

func (x RobotGender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RobotGender.Descriptor instead.
func (RobotGender) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_robot_storage_storage_proto_rawDescGZIP(), []int{0}
}

type RobotRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         // id
	Nick   string `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick,omitempty"`      // 昵称
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`  // 头像
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"` // 性别
}

func (x *RobotRecord) Reset() {
	*x = RobotRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_storage_storage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotRecord) ProtoMessage() {}

func (x *RobotRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_storage_storage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotRecord.ProtoReflect.Descriptor instead.
func (*RobotRecord) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_storage_storage_proto_rawDescGZIP(), []int{0}
}

func (x *RobotRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RobotRecord) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *RobotRecord) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RobotRecord) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type RobotCursor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cursor int64 `protobuf:"varint,1,opt,name=cursor,proto3" json:"cursor,omitempty"`
	Max    int64 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *RobotCursor) Reset() {
	*x = RobotCursor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_storage_storage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotCursor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotCursor) ProtoMessage() {}

func (x *RobotCursor) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_storage_storage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotCursor.ProtoReflect.Descriptor instead.
func (*RobotCursor) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_storage_storage_proto_rawDescGZIP(), []int{1}
}

func (x *RobotCursor) GetCursor() int64 {
	if x != nil {
		return x.Cursor
	}
	return 0
}

func (x *RobotCursor) GetMax() int64 {
	if x != nil {
		return x.Max
	}
	return 0
}

var File_pb_game_robot_storage_storage_proto protoreflect.FileDescriptor

var file_pb_game_robot_storage_storage_proto_rawDesc = []byte{
	0x0a, 0x23, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2f,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x22, 0x61, 0x0a, 0x0b, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x69, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x22, 0x37, 0x0a, 0x0b, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x75, 0x72,
	0x73, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x2a, 0x4e, 0x0a,
	0x0b, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x13, 0x0a, 0x0f,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x6e, 0x65, 0x10,
	0x00, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x4d, 0x61, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x47,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x46, 0x65, 0x6d, 0x61, 0x6c, 0x65, 0x10, 0x02, 0x42, 0x4a, 0x5a,
	0x48, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pb_game_robot_storage_storage_proto_rawDescOnce sync.Once
	file_pb_game_robot_storage_storage_proto_rawDescData = file_pb_game_robot_storage_storage_proto_rawDesc
)

func file_pb_game_robot_storage_storage_proto_rawDescGZIP() []byte {
	file_pb_game_robot_storage_storage_proto_rawDescOnce.Do(func() {
		file_pb_game_robot_storage_storage_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_robot_storage_storage_proto_rawDescData)
	})
	return file_pb_game_robot_storage_storage_proto_rawDescData
}

var file_pb_game_robot_storage_storage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_game_robot_storage_storage_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_game_robot_storage_storage_proto_goTypes = []interface{}{
	(RobotGender)(0),    // 0: game_robot.RobotGender
	(*RobotRecord)(nil), // 1: game_robot.RobotRecord
	(*RobotCursor)(nil), // 2: game_robot.RobotCursor
}
var file_pb_game_robot_storage_storage_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_game_robot_storage_storage_proto_init() }
func file_pb_game_robot_storage_storage_proto_init() {
	if File_pb_game_robot_storage_storage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_robot_storage_storage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_robot_storage_storage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotCursor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_robot_storage_storage_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_robot_storage_storage_proto_goTypes,
		DependencyIndexes: file_pb_game_robot_storage_storage_proto_depIdxs,
		EnumInfos:         file_pb_game_robot_storage_storage_proto_enumTypes,
		MessageInfos:      file_pb_game_robot_storage_storage_proto_msgTypes,
	}.Build()
	File_pb_game_robot_storage_storage_proto = out.File
	file_pb_game_robot_storage_storage_proto_rawDesc = nil
	file_pb_game_robot_storage_storage_proto_goTypes = nil
	file_pb_game_robot_storage_storage_proto_depIdxs = nil
}
