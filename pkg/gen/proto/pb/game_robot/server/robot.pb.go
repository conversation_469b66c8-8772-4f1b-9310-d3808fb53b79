// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_robot/server/robot.proto

package game_robot

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GrantReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	SceneId string `protobuf:"bytes,2,opt,name=sceneId,proto3" json:"sceneId,omitempty"`
	Num     uint32 `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *GrantReq) Reset() {
	*x = GrantReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_server_robot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantReq) ProtoMessage() {}

func (x *GrantReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_server_robot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantReq.ProtoReflect.Descriptor instead.
func (*GrantReq) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_server_robot_proto_rawDescGZIP(), []int{0}
}

func (x *GrantReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GrantReq) GetSceneId() string {
	if x != nil {
		return x.SceneId
	}
	return ""
}

func (x *GrantReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type GrantRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenIds []string `protobuf:"bytes,1,rep,name=openIds,proto3" json:"openIds,omitempty"`
}

func (x *GrantRsp) Reset() {
	*x = GrantRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_server_robot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantRsp) ProtoMessage() {}

func (x *GrantRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_server_robot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantRsp.ProtoReflect.Descriptor instead.
func (*GrantRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_server_robot_proto_rawDescGZIP(), []int{1}
}

func (x *GrantRsp) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type QueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	SceneId string   `protobuf:"bytes,2,opt,name=sceneId,proto3" json:"sceneId,omitempty"`
	OpenIds []string `protobuf:"bytes,3,rep,name=openIds,proto3" json:"openIds,omitempty"`
}

func (x *QueryReq) Reset() {
	*x = QueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_server_robot_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReq) ProtoMessage() {}

func (x *QueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_server_robot_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReq.ProtoReflect.Descriptor instead.
func (*QueryReq) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_server_robot_proto_rawDescGZIP(), []int{2}
}

func (x *QueryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryReq) GetSceneId() string {
	if x != nil {
		return x.SceneId
	}
	return ""
}

func (x *QueryReq) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type Robot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`  // id
	Nick   string `protobuf:"bytes,2,opt,name=nick,proto3" json:"nick,omitempty"`      // 昵称
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`  // 头像
	Gender uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"` // 性别
}

func (x *Robot) Reset() {
	*x = Robot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_server_robot_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Robot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Robot) ProtoMessage() {}

func (x *Robot) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_server_robot_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Robot.ProtoReflect.Descriptor instead.
func (*Robot) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_server_robot_proto_rawDescGZIP(), []int{3}
}

func (x *Robot) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *Robot) GetNick() string {
	if x != nil {
		return x.Nick
	}
	return ""
}

func (x *Robot) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Robot) GetGender() uint32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

type QueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result map[string]*Robot `protobuf:"bytes,1,rep,name=result,proto3" json:"result,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *QueryRsp) Reset() {
	*x = QueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_robot_server_robot_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRsp) ProtoMessage() {}

func (x *QueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_robot_server_robot_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRsp.ProtoReflect.Descriptor instead.
func (*QueryRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_robot_server_robot_proto_rawDescGZIP(), []int{4}
}

func (x *QueryRsp) GetResult() map[string]*Robot {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_pb_game_robot_server_robot_proto protoreflect.FileDescriptor

var file_pb_game_robot_server_robot_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x22, 0x4c,
	0x0a, 0x08, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x24, 0x0a, 0x08,
	0x47, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x73, 0x22, 0x54, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x63, 0x0a, 0x05, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x69, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x69, 0x63, 0x6b, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x92, 0x01,
	0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x1a, 0x4c, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x32, 0x77, 0x0a, 0x0b, 0x4c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x12, 0x33, 0x0a, 0x05, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x12, 0x14, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x47, 0x72,
	0x61, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x42, 0x42, 0x5a, 0x40, 0x74,
	0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_robot_server_robot_proto_rawDescOnce sync.Once
	file_pb_game_robot_server_robot_proto_rawDescData = file_pb_game_robot_server_robot_proto_rawDesc
)

func file_pb_game_robot_server_robot_proto_rawDescGZIP() []byte {
	file_pb_game_robot_server_robot_proto_rawDescOnce.Do(func() {
		file_pb_game_robot_server_robot_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_robot_server_robot_proto_rawDescData)
	})
	return file_pb_game_robot_server_robot_proto_rawDescData
}

var file_pb_game_robot_server_robot_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_game_robot_server_robot_proto_goTypes = []interface{}{
	(*GrantReq)(nil), // 0: game_robot.GrantReq
	(*GrantRsp)(nil), // 1: game_robot.GrantRsp
	(*QueryReq)(nil), // 2: game_robot.QueryReq
	(*Robot)(nil),    // 3: game_robot.Robot
	(*QueryRsp)(nil), // 4: game_robot.QueryRsp
	nil,              // 5: game_robot.QueryRsp.ResultEntry
}
var file_pb_game_robot_server_robot_proto_depIdxs = []int32{
	5, // 0: game_robot.QueryRsp.result:type_name -> game_robot.QueryRsp.ResultEntry
	3, // 1: game_robot.QueryRsp.ResultEntry.value:type_name -> game_robot.Robot
	0, // 2: game_robot.LeaseServer.Grant:input_type -> game_robot.GrantReq
	2, // 3: game_robot.LeaseServer.Query:input_type -> game_robot.QueryReq
	1, // 4: game_robot.LeaseServer.Grant:output_type -> game_robot.GrantRsp
	4, // 5: game_robot.LeaseServer.Query:output_type -> game_robot.QueryRsp
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_game_robot_server_robot_proto_init() }
func file_pb_game_robot_server_robot_proto_init() {
	if File_pb_game_robot_server_robot_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_robot_server_robot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_robot_server_robot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_robot_server_robot_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_robot_server_robot_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Robot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_robot_server_robot_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_robot_server_robot_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_robot_server_robot_proto_goTypes,
		DependencyIndexes: file_pb_game_robot_server_robot_proto_depIdxs,
		MessageInfos:      file_pb_game_robot_server_robot_proto_msgTypes,
	}.Build()
	File_pb_game_robot_server_robot_proto = out.File
	file_pb_game_robot_server_robot_proto_rawDesc = nil
	file_pb_game_robot_server_robot_proto_goTypes = nil
	file_pb_game_robot_server_robot_proto_depIdxs = nil
}
