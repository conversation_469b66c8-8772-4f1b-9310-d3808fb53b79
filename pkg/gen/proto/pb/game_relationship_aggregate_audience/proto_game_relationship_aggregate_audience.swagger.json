{"swagger": "2.0", "info": {"title": "pb/game_relationship_aggregate_audience/proto_game_relationship_aggregate_audience.proto", "version": "version not set"}, "tags": [{"name": "RelationshipAggregateAudience"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/component.game.RelationshipAggregateAudience/ReportRoomAudience": {"post": {"summary": "心跳上报", "operationId": "RelationshipAggregateAudience_ReportRoomAudience", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/gameHeartbeatReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/gameHeartbeatReportReq"}}], "tags": ["RelationshipAggregateAudience"]}}}, "definitions": {"gameHeartType": {"type": "string", "enum": ["ReportTypeUnknown", "ReportTypeEnter", "ReportTypeHeartbeat", "ReportTypeLeave"], "default": "ReportTypeUnknown", "title": "- ReportTypeEnter: 进入\n - ReportTypeHeartbeat: 心跳\n - ReportTypeLeave: 离开"}, "gameHeartbeatReportReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "int64"}, "heartbeatType": {"$ref": "#/definitions/gameHeartType"}, "roomType": {"$ref": "#/definitions/gameRoomType"}, "roomId": {"type": "string"}, "showId": {"type": "string"}, "beginTime": {"type": "string", "format": "int64"}, "reportTime": {"type": "string", "format": "int64"}}}, "gameHeartbeatReportRsp": {"type": "object", "properties": {"msg": {"type": "string"}}}, "gameRoomType": {"type": "string", "enum": ["RoomTypeUnknown", "RoomTypeLive", "RoomTypeKTV"], "default": "RoomTypeUnknown"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}