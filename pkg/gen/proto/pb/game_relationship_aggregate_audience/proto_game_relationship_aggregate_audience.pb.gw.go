// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/game_relationship_aggregate_audience/proto_game_relationship_aggregate_audience.proto

/*
Package game_relationship_aggregate_audience is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package game_relationship_aggregate_audience

import (
	"context"
	"io"
	"kugou_adapter_service/pkg/gen/proto/pb/game_relationship_aggregate"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_RelationshipAggregateAudience_ReportRoomAudience_0(ctx context.Context, marshaler runtime.Marshaler, client RelationshipAggregateAudienceClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq game_relationship_aggregate.HeartbeatReportReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.ReportRoomAudience(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_RelationshipAggregateAudience_ReportRoomAudience_0(ctx context.Context, marshaler runtime.Marshaler, server RelationshipAggregateAudienceServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq game_relationship_aggregate.HeartbeatReportReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.ReportRoomAudience(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterRelationshipAggregateAudienceHandlerServer registers the http handlers for service RelationshipAggregateAudience to "mux".
// UnaryRPC     :call RelationshipAggregateAudienceServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterRelationshipAggregateAudienceHandlerFromEndpoint instead.
func RegisterRelationshipAggregateAudienceHandlerServer(ctx context.Context, mux *runtime.ServeMux, server RelationshipAggregateAudienceServer) error {

	mux.Handle("POST", pattern_RelationshipAggregateAudience_ReportRoomAudience_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/component.game.RelationshipAggregateAudience/ReportRoomAudience", runtime.WithHTTPPathPattern("/component.game.RelationshipAggregateAudience/ReportRoomAudience"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_RelationshipAggregateAudience_ReportRoomAudience_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_RelationshipAggregateAudience_ReportRoomAudience_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterRelationshipAggregateAudienceHandlerFromEndpoint is same as RegisterRelationshipAggregateAudienceHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterRelationshipAggregateAudienceHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterRelationshipAggregateAudienceHandler(ctx, mux, conn)
}

// RegisterRelationshipAggregateAudienceHandler registers the http handlers for service RelationshipAggregateAudience to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterRelationshipAggregateAudienceHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterRelationshipAggregateAudienceHandlerClient(ctx, mux, NewRelationshipAggregateAudienceClient(conn))
}

// RegisterRelationshipAggregateAudienceHandlerClient registers the http handlers for service RelationshipAggregateAudience
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "RelationshipAggregateAudienceClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "RelationshipAggregateAudienceClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "RelationshipAggregateAudienceClient" to call the correct interceptors.
func RegisterRelationshipAggregateAudienceHandlerClient(ctx context.Context, mux *runtime.ServeMux, client RelationshipAggregateAudienceClient) error {

	mux.Handle("POST", pattern_RelationshipAggregateAudience_ReportRoomAudience_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/component.game.RelationshipAggregateAudience/ReportRoomAudience", runtime.WithHTTPPathPattern("/component.game.RelationshipAggregateAudience/ReportRoomAudience"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_RelationshipAggregateAudience_ReportRoomAudience_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_RelationshipAggregateAudience_ReportRoomAudience_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_RelationshipAggregateAudience_ReportRoomAudience_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"component.game.RelationshipAggregateAudience", "ReportRoomAudience"}, ""))
)

var (
	forward_RelationshipAggregateAudience_ReportRoomAudience_0 = runtime.ForwardResponseMessage
)
