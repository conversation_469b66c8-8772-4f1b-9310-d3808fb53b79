// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/im/im_write_wrap.proto

package im

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmBusinessMsgType int32

const (
	EmBusinessMsgType_BUSINESS_MSG_TYPE_DEFAULT  EmBusinessMsgType = 0 //默认消息
	EmBusinessMsgType_BUSINESS_MSG_TYPE_SQUADRON EmBusinessMsgType = 1 //群消息
)

// Enum value maps for EmBusinessMsgType.
var (
	EmBusinessMsgType_name = map[int32]string{
		0: "BUSINESS_MSG_TYPE_DEFAULT",
		1: "BUSINESS_MSG_TYPE_SQUADRON",
	}
	EmBusinessMsgType_value = map[string]int32{
		"BUSINESS_MSG_TYPE_DEFAULT":  0,
		"BUSINESS_MSG_TYPE_SQUADRON": 1,
	}
)

func (x EmBusinessMsgType) Enum() *EmBusinessMsgType {
	p := new(EmBusinessMsgType)
	*p = x
	return p
}

func (x EmBusinessMsgType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmBusinessMsgType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_im_im_write_wrap_proto_enumTypes[0].Descriptor()
}

func (EmBusinessMsgType) Type() protoreflect.EnumType {
	return &file_pb_im_im_write_wrap_proto_enumTypes[0]
}

func (x EmBusinessMsgType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmBusinessMsgType.Descriptor instead.
func (EmBusinessMsgType) EnumDescriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{0}
}

// BUSINESS_MSG_TYPE_SQUADRON
type EmSquadronMsgSubType int32

const (
	EmSquadronMsgSubType_IM_MSG_SQUADRON_SUBTYPE_NORMAL EmSquadronMsgSubType = 0 //普通消息
)

// Enum value maps for EmSquadronMsgSubType.
var (
	EmSquadronMsgSubType_name = map[int32]string{
		0: "IM_MSG_SQUADRON_SUBTYPE_NORMAL",
	}
	EmSquadronMsgSubType_value = map[string]int32{
		"IM_MSG_SQUADRON_SUBTYPE_NORMAL": 0,
	}
)

func (x EmSquadronMsgSubType) Enum() *EmSquadronMsgSubType {
	p := new(EmSquadronMsgSubType)
	*p = x
	return p
}

func (x EmSquadronMsgSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmSquadronMsgSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_im_im_write_wrap_proto_enumTypes[1].Descriptor()
}

func (EmSquadronMsgSubType) Type() protoreflect.EnumType {
	return &file_pb_im_im_write_wrap_proto_enumTypes[1]
}

func (x EmSquadronMsgSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmSquadronMsgSubType.Descriptor instead.
func (EmSquadronMsgSubType) EnumDescriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{1}
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type RoomMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId      string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`            //房间id
	MsgId       string `protobuf:"bytes,2,opt,name=msgId,proto3" json:"msgId,omitempty"`              //消息id，保持唯一(业务侧可写可不写，不写的话im侧自行生成)
	MsgType     int32  `protobuf:"varint,3,opt,name=msgType,proto3" json:"msgType,omitempty"`         //消息主类型emBusinessMsgType
	MsgSubType  int32  `protobuf:"varint,4,opt,name=msgSubType,proto3" json:"msgSubType,omitempty"`   //消息子类型
	Content     string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`          //消息内容
	Sender      *User  `protobuf:"bytes,6,opt,name=sender,proto3" json:"sender,omitempty"`            //发送人
	Receiver    *User  `protobuf:"bytes,7,opt,name=receiver,proto3" json:"receiver,omitempty"`        //接受人
	Microsecond int64  `protobuf:"varint,8,opt,name=microsecond,proto3" json:"microsecond,omitempty"` //消息发送的微秒时间戳
	Seq         int64  `protobuf:"varint,9,opt,name=seq,proto3" json:"seq,omitempty"`                 //消息序列号,roomid纬度递增,业务侧不写
}

func (x *RoomMsg) Reset() {
	*x = RoomMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomMsg) ProtoMessage() {}

func (x *RoomMsg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomMsg.ProtoReflect.Descriptor instead.
func (*RoomMsg) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{1}
}

func (x *RoomMsg) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomMsg) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *RoomMsg) GetMsgType() int32 {
	if x != nil {
		return x.MsgType
	}
	return 0
}

func (x *RoomMsg) GetMsgSubType() int32 {
	if x != nil {
		return x.MsgSubType
	}
	return 0
}

func (x *RoomMsg) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *RoomMsg) GetSender() *User {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *RoomMsg) GetReceiver() *User {
	if x != nil {
		return x.Receiver
	}
	return nil
}

func (x *RoomMsg) GetMicrosecond() int64 {
	if x != nil {
		return x.Microsecond
	}
	return 0
}

func (x *RoomMsg) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

type SendMsgWrapReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg      *RoomMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	SendType int32    `protobuf:"varint,2,opt,name=sendType,proto3" json:"sendType,omitempty"` //发送类型，0默认房间组播，1单播c2c
}

func (x *SendMsgWrapReq) Reset() {
	*x = SendMsgWrapReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgWrapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgWrapReq) ProtoMessage() {}

func (x *SendMsgWrapReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgWrapReq.ProtoReflect.Descriptor instead.
func (*SendMsgWrapReq) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{2}
}

func (x *SendMsgWrapReq) GetMsg() *RoomMsg {
	if x != nil {
		return x.Msg
	}
	return nil
}

func (x *SendMsgWrapReq) GetSendType() int32 {
	if x != nil {
		return x.SendType
	}
	return 0
}

type SendMsgWrapRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
}

func (x *SendMsgWrapRsp) Reset() {
	*x = SendMsgWrapRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMsgWrapRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMsgWrapRsp) ProtoMessage() {}

func (x *SendMsgWrapRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMsgWrapRsp.ProtoReflect.Descriptor instead.
func (*SendMsgWrapRsp) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{3}
}

func (x *SendMsgWrapRsp) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

type GetCommMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	OpenId   string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`     //用户openid
	Passback string `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"` //首次不填，后面透传后台返回的passback字段
}

func (x *GetCommMsgReq) Reset() {
	*x = GetCommMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommMsgReq) ProtoMessage() {}

func (x *GetCommMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommMsgReq.ProtoReflect.Descriptor instead.
func (*GetCommMsgReq) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{4}
}

func (x *GetCommMsgReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GetCommMsgReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GetCommMsgReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type GetCommMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VecMsg     []*RoomMsg `protobuf:"bytes,1,rep,name=vecMsg,proto3" json:"vecMsg,omitempty"`                            //消息数组
	IntervalMs int32      `protobuf:"varint,2,opt,name=interval_ms,json=intervalMs,proto3" json:"interval_ms,omitempty"` //客户端从收到消息到下一次拉取消息的间隔，单位ms。如果这个值是0，那就是长轮询，后台一回包，客户端就重新拉取。如果这个值非0，那就是定时轮询。
	TimeoutMs  int32      `protobuf:"varint,3,opt,name=timeout_ms,json=timeoutMs,proto3" json:"timeout_ms,omitempty"`    //客户端下一次拉取消息的超时时间，单位ms。由后台来灵活控制超时时间，避免客户端因为超时太长而导致出现卡住现象。
	Passback   string     `protobuf:"bytes,4,opt,name=passback,proto3" json:"passback,omitempty"`                        //首次不填，后面透传后台返回的passback字段
}

func (x *GetCommMsgRsp) Reset() {
	*x = GetCommMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommMsgRsp) ProtoMessage() {}

func (x *GetCommMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommMsgRsp.ProtoReflect.Descriptor instead.
func (*GetCommMsgRsp) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{5}
}

func (x *GetCommMsgRsp) GetVecMsg() []*RoomMsg {
	if x != nil {
		return x.VecMsg
	}
	return nil
}

func (x *GetCommMsgRsp) GetIntervalMs() int32 {
	if x != nil {
		return x.IntervalMs
	}
	return 0
}

func (x *GetCommMsgRsp) GetTimeoutMs() int32 {
	if x != nil {
		return x.TimeoutMs
	}
	return 0
}

func (x *GetCommMsgRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

type GetHistoryMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`
	OpenId   string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`     //用户openid
	Passback string `protobuf:"bytes,3,opt,name=passback,proto3" json:"passback,omitempty"` //首次不填，后面透传后台返回的passback字段
	Msgid    string `protobuf:"bytes,4,opt,name=msgid,proto3" json:"msgid,omitempty"`       //如果是拉老消息，为本地最老的一条消息msgid/如果拉新消息，为本地最新一条消息的msgid,如果本地没消息，穿空
	OldMsg   int32  `protobuf:"varint,5,opt,name=oldMsg,proto3" json:"oldMsg,omitempty"`    //0拉新消息，1拉老消息
	Count    int32  `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`      //拉取消息条数
}

func (x *GetHistoryMsgReq) Reset() {
	*x = GetHistoryMsgReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHistoryMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHistoryMsgReq) ProtoMessage() {}

func (x *GetHistoryMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHistoryMsgReq.ProtoReflect.Descriptor instead.
func (*GetHistoryMsgReq) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{6}
}

func (x *GetHistoryMsgReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GetHistoryMsgReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GetHistoryMsgReq) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

func (x *GetHistoryMsgReq) GetMsgid() string {
	if x != nil {
		return x.Msgid
	}
	return ""
}

func (x *GetHistoryMsgReq) GetOldMsg() int32 {
	if x != nil {
		return x.OldMsg
	}
	return 0
}

func (x *GetHistoryMsgReq) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetHistoryMsgRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VecMsg   []*RoomMsg `protobuf:"bytes,1,rep,name=vecMsg,proto3" json:"vecMsg,omitempty"` //消息数组
	Passback string     `protobuf:"bytes,2,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *GetHistoryMsgRsp) Reset() {
	*x = GetHistoryMsgRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_im_im_write_wrap_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetHistoryMsgRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHistoryMsgRsp) ProtoMessage() {}

func (x *GetHistoryMsgRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_im_im_write_wrap_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHistoryMsgRsp.ProtoReflect.Descriptor instead.
func (*GetHistoryMsgRsp) Descriptor() ([]byte, []int) {
	return file_pb_im_im_write_wrap_proto_rawDescGZIP(), []int{7}
}

func (x *GetHistoryMsgRsp) GetVecMsg() []*RoomMsg {
	if x != nil {
		return x.VecMsg
	}
	return nil
}

func (x *GetHistoryMsgRsp) GetPassback() string {
	if x != nil {
		return x.Passback
	}
	return ""
}

var File_pb_im_im_write_wrap_proto protoreflect.FileDescriptor

var file_pb_im_im_write_wrap_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x62, 0x2f, 0x69, 0x6d, 0x2f, 0x69, 0x6d, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x5f, 0x77, 0x72, 0x61, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6d, 0x22, 0x1e, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x22, 0x91, 0x02, 0x0a, 0x07, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73, 0x67, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x73, 0x67, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6d, 0x73,
	0x67, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6d, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x08, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x69, 0x63, 0x72, 0x6f,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x22, 0x50, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x73, 0x67, 0x57, 0x72, 0x61, 0x70, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6d, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0x28, 0x0a, 0x0e, 0x53, 0x65,
	0x6e, 0x64, 0x4d, 0x73, 0x67, 0x57, 0x72, 0x61, 0x70, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f,
	0x6f, 0x6d, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x4d,
	0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63,
	0x6b, 0x22, 0x95, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x4d, 0x73, 0x67,
	0x52, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x06, 0x76, 0x65, 0x63, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6d, 0x2e, 0x52, 0x6f,
	0x6f, 0x6d, 0x4d, 0x73, 0x67, 0x52, 0x06, 0x76, 0x65, 0x63, 0x4d, 0x73, 0x67, 0x12, 0x1f, 0x0a,
	0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x4d, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xa2, 0x01, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73,
	0x67, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6c, 0x64, 0x4d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6f, 0x6c, 0x64, 0x4d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x58,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4d, 0x73, 0x67, 0x52,
	0x73, 0x70, 0x12, 0x28, 0x0a, 0x06, 0x76, 0x65, 0x63, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6d, 0x2e, 0x52, 0x6f, 0x6f,
	0x6d, 0x4d, 0x73, 0x67, 0x52, 0x06, 0x76, 0x65, 0x63, 0x4d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x2a, 0x52, 0x0a, 0x11, 0x65, 0x6d, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x19, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x51, 0x55, 0x41, 0x44, 0x52, 0x4f, 0x4e, 0x10, 0x01, 0x2a, 0x3a, 0x0a, 0x14,
	0x65, 0x6d, 0x53, 0x71, 0x75, 0x61, 0x64, 0x72, 0x6f, 0x6e, 0x4d, 0x73, 0x67, 0x53, 0x75, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4d, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x53,
	0x51, 0x55, 0x41, 0x44, 0x52, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x42, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x42, 0x3a, 0x5a, 0x38, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x69, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_im_im_write_wrap_proto_rawDescOnce sync.Once
	file_pb_im_im_write_wrap_proto_rawDescData = file_pb_im_im_write_wrap_proto_rawDesc
)

func file_pb_im_im_write_wrap_proto_rawDescGZIP() []byte {
	file_pb_im_im_write_wrap_proto_rawDescOnce.Do(func() {
		file_pb_im_im_write_wrap_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_im_im_write_wrap_proto_rawDescData)
	})
	return file_pb_im_im_write_wrap_proto_rawDescData
}

var file_pb_im_im_write_wrap_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_im_im_write_wrap_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_im_im_write_wrap_proto_goTypes = []interface{}{
	(EmBusinessMsgType)(0),    // 0: game_im.emBusinessMsgType
	(EmSquadronMsgSubType)(0), // 1: game_im.emSquadronMsgSubType
	(*User)(nil),              // 2: game_im.User
	(*RoomMsg)(nil),           // 3: game_im.RoomMsg
	(*SendMsgWrapReq)(nil),    // 4: game_im.SendMsgWrapReq
	(*SendMsgWrapRsp)(nil),    // 5: game_im.SendMsgWrapRsp
	(*GetCommMsgReq)(nil),     // 6: game_im.GetCommMsgReq
	(*GetCommMsgRsp)(nil),     // 7: game_im.GetCommMsgRsp
	(*GetHistoryMsgReq)(nil),  // 8: game_im.GetHistoryMsgReq
	(*GetHistoryMsgRsp)(nil),  // 9: game_im.GetHistoryMsgRsp
}
var file_pb_im_im_write_wrap_proto_depIdxs = []int32{
	2, // 0: game_im.RoomMsg.sender:type_name -> game_im.User
	2, // 1: game_im.RoomMsg.receiver:type_name -> game_im.User
	3, // 2: game_im.SendMsgWrapReq.msg:type_name -> game_im.RoomMsg
	3, // 3: game_im.GetCommMsgRsp.vecMsg:type_name -> game_im.RoomMsg
	3, // 4: game_im.GetHistoryMsgRsp.vecMsg:type_name -> game_im.RoomMsg
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pb_im_im_write_wrap_proto_init() }
func file_pb_im_im_write_wrap_proto_init() {
	if File_pb_im_im_write_wrap_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_im_im_write_wrap_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgWrapReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMsgWrapRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHistoryMsgReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_im_im_write_wrap_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetHistoryMsgRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_im_im_write_wrap_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_im_im_write_wrap_proto_goTypes,
		DependencyIndexes: file_pb_im_im_write_wrap_proto_depIdxs,
		EnumInfos:         file_pb_im_im_write_wrap_proto_enumTypes,
		MessageInfos:      file_pb_im_im_write_wrap_proto_msgTypes,
	}.Build()
	File_pb_im_im_write_wrap_proto = out.File
	file_pb_im_im_write_wrap_proto_rawDesc = nil
	file_pb_im_im_write_wrap_proto_goTypes = nil
	file_pb_im_im_write_wrap_proto_depIdxs = nil
}
