{"swagger": "2.0", "info": {"title": "pb/open_game_open_api/open_game_open_api.proto", "version": "version not set"}, "tags": [{"name": "OpenGameOpenApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/tme_game_quick_gift/server/game_quick_gift_config": {"post": {"summary": "获取游戏快捷送礼的信息，区分快捷送礼是送礼or回礼", "operationId": "OpenGameOpenApi_GetGameQuickGiftConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGameQuickGiftConfiglRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGameQuickGiftConfiglReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/abTest": {"post": {"operationId": "OpenGameOpenApi_ABTest", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiABTestRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiABTestReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/addAsset": {"post": {"operationId": "OpenGameOpenApi_AddAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiAddAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiAddAssetReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/batchGetAnonymousStatus": {"post": {"operationId": "OpenGameOpenApi_BatchGetAnonymousStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBatchGetAnonymousStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBatchGetAnonymousStatusReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/batchGetProfile": {"post": {"summary": "批量获取用户资料", "operationId": "OpenGameOpenApi_BatchGetProfile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBatchGetProfileRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBatchGetProfileReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/batchGetUserFeature": {"post": {"operationId": "OpenGameOpenApi_BatchGetUserFeature", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBatchGetUserFeatureRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBatchGetUserFeatureReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/bigHornMsg": {"post": {"summary": "发送大喇叭", "operationId": "OpenGameOpenApi_BigHornMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBigHornMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBigHornMsgReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/consumeFreeGift": {"post": {"summary": "消耗免费礼物", "operationId": "OpenGameOpenApi_ConsumeFreeGift", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiConsumeFreeGiftRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiConsumeFreeGiftReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/dcReport": {"post": {"summary": "上报 IDC DC表; 上报公有云DC不可用该接口", "operationId": "OpenGameOpenApi_DCReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiDCReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiDCReportReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/followOpt": {"post": {"operationId": "OpenGameOpenApi_FollowOpt", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiFollowOptRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiFollowOptReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/getBalance": {"post": {"summary": "获取游戏币余额", "operationId": "OpenGameOpenApi_GetBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetBalanceReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/getPlatBalance": {"post": {"summary": "平台货币余额", "operationId": "OpenGameOpenApi_GetPlatBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetPlatBalanceRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetPlatBalanceReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/getProfile": {"post": {"summary": "获取用户资料", "operationId": "OpenGameOpenApi_GetProfile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetProfileRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetProfileReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/getSendGiftOrder": {"post": {"operationId": "OpenGameOpenApi_GetSendGiftOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetSendGiftOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetSendGiftOrderReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/getUserSwitch": {"post": {"summary": "[通用消息] 查询开关信息", "operationId": "OpenGameOpenApi_GetUserSwitch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetUserSwitchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetUserSwitchReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/gift/batchDetail": {"post": {"summary": "批量获取礼物信息", "operationId": "OpenGameOpenApi_BatchGiftDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBatchGiftDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBatchGiftDetailReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/hippyMsg": {"post": {"operationId": "OpenGameOpenApi_SendHippyMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSendHippyMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSendHippyMsgReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/hippyMsgUserSwitchEvent": {"post": {"operationId": "OpenGameOpenApi_HippyMsgUserSwitchEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiHippyMsgUserSwitchEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiHippyMsgUserSwitchEventReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/pay": {"post": {"summary": "扣除游戏币", "operationId": "OpenGameOpenApi_Pay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiPayRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiPayReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/placeOrder": {"post": {"summary": "下单", "operationId": "OpenGameOpenApi_PlaceOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiPlaceOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiPlaceOrderReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/present": {"post": {"summary": "奖励游戏币", "operationId": "OpenGameOpenApi_Present", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiPresentRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiPresentReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/prize": {"post": {"summary": "根据指定金额发奖", "operationId": "OpenGameOpenApi_Prize", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiPrizeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiPrizeReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/prizeV2": {"post": {"summary": "根据指定金额发奖V2 TODO", "operationId": "OpenGameOpenApi_PrizeV2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiPrizeV2Rsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiPrizeV2Req"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/prop/batchDetail": {"post": {"summary": "批量获取道具信息", "operationId": "OpenGameOpenApi_BatchPropDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBatchPropDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBatchPropDetailReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/queryAsset": {"post": {"operationId": "OpenGameOpenApi_QueryAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQueryAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQueryAssetReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/queryCertInfo": {"post": {"operationId": "OpenGameOpenApi_QueryCertInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQueryCertInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQueryCertInfoReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/queryRelation": {"post": {"operationId": "OpenGameOpenApi_QueryRelation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQueryRelationRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQueryRelationReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/qzaReport": {"post": {"operationId": "OpenGameOpenApi_QzaReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQzaReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQzaReportReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/refund": {"post": {"summary": "退款游戏币", "operationId": "OpenGameOpenApi_Refund", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiRefundRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiRefundReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/reportTaskConditionBill": {"post": {"operationId": "OpenGameOpenApi_ReportTaskConditionBill", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiReportTaskConditionBillRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiReportTaskConditionBillReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/rewardDetail": {"post": {"summary": "礼物信息", "operationId": "OpenGameOpenApi_RewardDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiRewardDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiRewardDetailReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/rewardDetailBatch": {"post": {"summary": "礼物信息 批量", "operationId": "OpenGameOpenApi_RewardDetailBatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiRewardDetailBatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiRewardDetailBatchReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/roomMsg": {"post": {"summary": "房间消息", "operationId": "OpenGameOpenApi_RoomMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiRoomMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiRoomMsgReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/safeCheck": {"post": {"operationId": "OpenGameOpenApi_SafeCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/safeCheckPic": {"post": {"operationId": "OpenGameOpenApi_SafeCheckPic", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckPicRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckPicReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/sendMail": {"post": {"summary": "私信", "operationId": "OpenGameOpenApi_SendMail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSendMailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSendMailReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/sendPush": {"post": {"summary": "发送push", "operationId": "OpenGameOpenApi_SendPush", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSendPushRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSendPushReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/sendSingleReward": {"post": {"summary": "发送平台资产", "operationId": "OpenGameOpenApi_SendSingleReward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSendSingleRewardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSendSingleRewardReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/sendUserSwitchMsg": {"post": {"summary": "[通用消息]发送消息", "operationId": "OpenGameOpenApi_SendUserSwitchMsg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSendUserSwitchMsgRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSendUserSwitchMsgReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/setUserSwitch": {"post": {"summary": "[通用消息] 更改开关信息", "operationId": "OpenGameOpenApi_SetUserSwitch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSetUserSwitchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSetUserSwitchReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/subAsset": {"post": {"operationId": "OpenGameOpenApi_SubAsset", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSubAssetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSubAssetReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/tdbankReport": {"post": {"summary": "数据上报", "operationId": "OpenGameOpenApi_TDBankReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiTDBankReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiTDBankReportReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/tmeEvent": {"post": {"operationId": "OpenGameOpenApi_TmeEvent", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiTmeEventRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiTmeEventReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/uiABTest": {"post": {"operationId": "OpenGameOpenApi_UIABTest", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiUIABTestRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiUIABTestReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/batchUserDirect": {"post": {"summary": "定向", "operationId": "OpenGameOpenApi_BatchUserDirect", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiBatchUserDirectRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiBatchUserDirectReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/getCwGameRankInfo": {"post": {"summary": "获取cw游戏信息", "operationId": "OpenGameOpenApi_GetCwGameRankInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetCwGameRankInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetCwGameRankInfoReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/getIntimacy": {"post": {"operationId": "OpenGameOpenApi_GetUserIntimacy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetUserIntimacyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetUserIntimacyReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/getRiskLevel": {"post": {"summary": "查询风险等级", "operationId": "OpenGameOpenApi_GetRiskLevel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetRiskLevelRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetRiskLevelReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/judgeOnline": {"post": {"operationId": "OpenGameOpenApi_JudgeOnline", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiJudgeOnlineRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiJudgeOnlineReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/noteRank": {"post": {"operationId": "OpenGameOpenApi_NoteRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiNoteRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiNoteRankReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/noteWeekRank": {"post": {"operationId": "OpenGameOpenApi_NoteWeekRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiNoteWeekRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiNoteWeekRankReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/queryBlacklist": {"post": {"summary": "查询黑名单", "operationId": "OpenGameOpenApi_QueryBlacklist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQueryBlacklistRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQueryBlacklistReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/queryFriend": {"post": {"summary": "查询好友", "operationId": "OpenGameOpenApi_QueryFriends", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQueryFriendsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQueryFriendsReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/verifyFollow": {"post": {"summary": "验证黑名单", "operationId": "OpenGameOpenApi_VerifyFollow", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiVerifyFollowRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiVerifyFollowReq"}}], "tags": ["OpenGameOpenApi"]}}, "/minigame/openapi/user/writeGameFeed": {"post": {"operationId": "OpenGameOpenApi_WriteGameFeed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiWriteGameFeedRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiWriteGameFeedReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/interactiveGame/switchRoom": {"post": {"summary": "创建房间", "operationId": "OpenGameOpenApi_InteractiveGameSwitchRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiInteractiveGameSwitchRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiInteractiveGameSwitchRoomReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/addTicket": {"post": {"summary": "赠送抽奖券", "operationId": "OpenGameOpenApi_AddTicket", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiAddTicketRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiAddTicketReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/advertCheck": {"post": {"summary": "广告校验", "operationId": "OpenGameOpenApi_AdvertCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiAdvertCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiAdvertCheckReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/advertInfo": {"post": {"summary": "广告信息", "operationId": "OpenGameOpenApi_AdvertInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiAdvertInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiAdvertInfoReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/advertReceiveReward": {"post": {"summary": "领取广告奖励", "operationId": "OpenGameOpenApi_AdvertReceiveReward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiAdvertReceiveRewardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiAdvertReceiveRewardReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/alarmProxy": {"post": {"summary": "告警", "operationId": "OpenGameOpenApi_AlarmProxy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiAlarmProxyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiAlarmProxyReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/data/CwsxGetSuggestion": {"post": {"summary": "三消动态难度", "operationId": "OpenGameOpenApi_CwsxGetSuggestion", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiCwsxGetSuggestionRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiCwsxGetSuggestionReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/data/CwsxQueryOrders": {"post": {"summary": "三消对账 查询平台订单", "operationId": "OpenGameOpenApi_CwsxQueryOrders", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiCwsxQueryOrdersRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiCwsxQueryOrdersReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/data/cwsxAdSpotTimes": {"post": {"summary": "cwsx广告点位", "operationId": "OpenGameOpenApi_CwsxAdSpotTimes", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiCwsxAdSpotTimesRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiCwsxAdSpotTimesReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/data/zhiYanReport": {"post": {"summary": "智研上报", "operationId": "OpenGameOpenApi_ZhiYanReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiZhiYanReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiZhiYanReportReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/dataReport": {"post": {"summary": "数据上报", "operationId": "OpenGameOpenApi_DataReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiDataReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiDataReportReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/gamePackapi": {"post": {"summary": "游戏请求", "operationId": "OpenGameOpenApi_GamePackapi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGamePackapiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGamePackapiReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/geo/query": {"post": {"summary": "Geo", "operationId": "OpenGameOpenApi_QueryGeo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiQueryGeoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiQueryGeoReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/lottery": {"post": {"summary": "抽奖", "operationId": "OpenGameOpenApi_Lottery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiLotteryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiLotteryReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/revenue/createTrade": {"post": {"summary": "支付中台 下单", "operationId": "OpenGameOpenApi_CreateTrade", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiCreateTradeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiCreateTradeReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/openapi/user/getInvitateFriend": {"post": {"summary": "查询待邀请好友", "operationId": "OpenGameOpenApi_GetInvitateFriend", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiGetInvitateFriendRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiGetInvitateFriendReq"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/safev2/async_check_callback": {"post": {"summary": "图文同步安全审查", "operationId": "OpenGameOpenApi_ASyncSafeCheckV2Callback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckCallbackV2Req"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckCallbackV2Req"}}], "tags": ["OpenGameOpenApi"]}}, "/miniprogram/safev2/sync_check": {"post": {"summary": "图文同步安全审查", "operationId": "OpenGameOpenApi_SyncSafeCheckV2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2Rsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2Req"}}], "tags": ["OpenGameOpenApi"]}}}, "definitions": {"CreateTradeReqGoods": {"type": "object", "properties": {"goodsId": {"type": "string"}, "goodsNum": {"type": "string", "format": "int64"}, "goodsAmount": {"type": "string", "format": "int64"}}}, "CwsxGetSuggestionReqAppId": {"type": "string", "enum": ["AppIdKg", "AppIdQMusic", "AppIdKugou", "AppIdWechat"], "default": "AppIdKg"}, "CwsxGetSuggestionReqBeforeItems": {"type": "object", "properties": {"itemId": {"type": "string"}, "itemCnt": {"type": "string", "format": "int64"}}}, "CwsxGetSuggestionReqDeviceInfo": {"type": "object", "properties": {"osType": {"$ref": "#/definitions/CwsxGetSuggestionReqOsType"}}}, "CwsxGetSuggestionReqGameContext": {"type": "object", "properties": {"deviceInfo": {"$ref": "#/definitions/CwsxGetSuggestionReqDeviceInfo", "title": "设备信息"}, "curLevel": {"type": "string", "format": "int64", "title": "当前闯关的关卡id"}, "totalCoins": {"type": "string", "format": "int64", "title": "当前金币数"}, "totalHealthpoints": {"type": "string", "format": "int64", "title": "当前体力值"}, "beforeItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CwsxGetSuggestionReqBeforeItems"}}, "extMap": {"type": "object", "additionalProperties": {"type": "string"}}}}, "CwsxGetSuggestionReqOsType": {"type": "string", "enum": ["Android", "Ios"], "default": "Android"}, "CwsxGetSuggestionRspDifficulutyType": {"type": "string", "enum": ["DifficulutyTypeEasy", "DifficulutyTypeNormal", "DifficulutyTypeHard"], "default": "DifficulutyTypeEasy"}, "CwsxGetSuggestionRspOperation": {"type": "object", "properties": {"operationType": {"$ref": "#/definitions/CwsxGetSuggestionRspOperationType"}, "operationValue": {"$ref": "#/definitions/CwsxGetSuggestionRspOperationValue"}}}, "CwsxGetSuggestionRspOperationType": {"type": "string", "enum": ["OperationTypeModifyDifficulty"], "default": "OperationTypeModifyDifficulty"}, "CwsxGetSuggestionRspOperationValue": {"type": "object", "properties": {"difficulty": {"$ref": "#/definitions/CwsxGetSuggestionRspDifficulutyType"}, "difficultyValue": {"type": "number", "format": "float"}}}, "PresentReqAssetItems": {"type": "object", "properties": {"assetId": {"type": "string"}, "amount": {"type": "integer", "format": "int64"}}}, "SendSingleRewardReqConsumeAssetItem": {"type": "object", "properties": {"assetId": {"type": "string"}, "amount": {"type": "integer", "format": "int64"}}}, "VerifyFollowReqVerifyFollowType": {"type": "string", "enum": ["Forward", "Reverse"], "default": "Forward", "title": "- Forward: 验证uid是否关注了vecUid\n - Reverse: 验证vecUid是否关注了uid"}, "interfaceopen_game_open_apiAdvertInfo": {"type": "object", "properties": {"incentiveType": {"$ref": "#/definitions/open_game_open_apiIncentiveType", "title": "广告奖励数值类型"}, "rewardNum": {"type": "string", "format": "uint64", "title": "后台计算出的金币值"}, "showAdvert": {"type": "boolean", "title": "是否展示广告"}, "leftAdvert": {"type": "integer", "format": "int64", "title": "剩余广告次数(仅用于前端展示次数，是否能看广告用showAdvert字段判断)"}}}, "interfaceopen_game_open_apiDevice": {"type": "object", "properties": {"qua": {"type": "string"}, "deviceInfo": {"type": "string"}}}, "open_game_open_apiABTestReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "businessId": {"type": "string", "title": "业务Id"}, "channelId": {"type": "string", "title": "渠道id"}, "moduleId": {"type": "string", "title": "模块id"}, "qua": {"type": "string", "title": "客户端版本"}, "devInfo": {"type": "string", "title": "客户端设备信息"}, "callerSvrName": {"type": "string", "title": "主调服务名"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiAbtestLabelItem"}}, "hashKey": {"type": "string", "title": "分流key. hashKey空时,按strId（uid）分流"}, "moduleKeys": {"type": "array", "items": {"type": "string"}, "title": "批量请求"}, "version": {"type": "string", "title": "客户端版本"}, "platform": {"type": "string", "title": "平台 andriod、ios"}}}, "open_game_open_apiABTestRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "mapTestInfo": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiAbtestRspItem"}, "title": "testInfo"}, "modidToModkey": {"type": "object", "additionalProperties": {"type": "string"}}, "abtest": {"type": "string", "title": "数据上报用"}}}, "open_game_open_apiAbtestLabelItem": {"type": "object", "properties": {"labelKey": {"type": "string"}, "labelValue": {"type": "string"}}}, "open_game_open_apiAbtestPassback": {"type": "object", "properties": {"uidBagType": {"type": "string"}, "uidBagId": {"type": "string"}}}, "open_game_open_apiAbtestRspItem": {"type": "object", "properties": {"testId": {"type": "string", "title": "实验组id"}, "channelId": {"type": "string", "title": "渠道id"}, "mapParams": {"type": "object", "additionalProperties": {"type": "string"}, "title": "实验参数"}, "strAbtest": {"type": "string", "title": "数据上报用"}, "passback": {"$ref": "#/definitions/open_game_open_apiAbtestPassback", "title": "透传abtest实验数据"}}}, "open_game_open_apiAdCheckMask": {"type": "string", "enum": ["AdCheckMaskNone", "AdCheckMaskDownloadActive"], "default": "AdCheckMaskNone", "title": "- AdCheckMaskNone: 只需校验主广告曝光\n - AdCheckMaskDownloadActive: 需要校验广告点击激活是否通过"}, "open_game_open_apiAddAssetReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiPlatAssetChange"}, "title": "资产列表"}, "billNo": {"type": "string", "title": "订单ID"}, "source": {"$ref": "#/definitions/open_game_open_apiSourceChannel", "title": "来源配置"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位毫秒"}, "assetType": {"$ref": "#/definitions/open_game_open_apiPlatAssetType", "title": "资产类型"}, "scene": {"type": "integer", "format": "int32", "title": "场景，透传上报用"}}}, "open_game_open_apiAddAssetRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}, "billNo": {"type": "string", "title": "订单ID"}, "mapResult": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiPlatUserAsset"}, "title": "结果资产列表"}}}, "open_game_open_apiAddTicketReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo"}, "cnt": {"type": "integer", "format": "int64", "title": "增加数量"}, "activityID": {"type": "string", "title": "活动id"}, "seqID": {"type": "string", "title": "去重id"}}}, "open_game_open_apiAddTicketRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "ticketCnt": {"type": "integer", "format": "int64", "title": "用户拥有的券数量"}}}, "open_game_open_apiAdultType": {"type": "string", "enum": ["AdultTypeUnknown", "AdultTypeFalse", "AudltTypeTrue"], "default": "AdultTypeUnknown", "title": "- AdultTypeUnknown: 未知\n - AdultTypeFalse: 未成年\n - AudltTypeTrue: 已成年"}, "open_game_open_apiAdvertCheckReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "adToken": {"type": "string", "title": "广告曝光token"}, "adPosId": {"type": "string", "title": "广告位id"}, "qimei36": {"type": "string", "title": "qimei36设备号"}, "sceneId": {"type": "string", "title": "场景id(可选)"}, "adCheckMask": {"$ref": "#/definitions/open_game_open_apiAdCheckMask", "title": "广告校验mask"}}}, "open_game_open_apiAdvertCheckRsp": {"type": "object", "properties": {"traceId": {"type": "string", "title": "广告曝光唯一id, 类似订单号的逻辑,也可以用token代替"}, "result": {"type": "integer", "format": "int32", "title": "曝光校验结果，0是校验通过 1是校验不通过"}, "rewardNum": {"type": "string", "format": "uint64", "title": "广告ecpm数值奖励数量"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "EncodedEcpm": {"type": "string", "title": "ecpm加密值"}, "ecpmCoin": {"type": "integer", "format": "int64", "title": "ecpm换算成金币数"}}}, "open_game_open_apiAdvertInfoReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "advertSceneList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiAdvertScene"}, "title": "广告场景"}}}, "open_game_open_apiAdvertInfoRsp": {"type": "object", "properties": {"advertInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/interfaceopen_game_open_apiAdvertInfo"}, "title": "广告信息"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiAdvertReceiveRewardReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "adToken": {"type": "string", "title": "广告曝光token"}, "adPosId": {"type": "string", "title": "广告位id"}, "qimei36": {"type": "string"}, "sceneId": {"type": "string", "description": "场景id(可选)", "title": "场景id(可选)"}}}, "open_game_open_apiAdvertReceiveRewardRsp": {"type": "object", "properties": {"traceId": {"type": "string", "title": "广告曝光唯一id"}, "result": {"type": "integer", "format": "int32", "title": "领取结果，0是成功"}, "rewardNum": {"type": "string", "format": "uint64", "title": "广告ecpm数值奖励数量"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiAdvertScene": {"type": "object", "properties": {"adPosId": {"type": "string", "title": "广告位id"}, "sceneId": {"type": "string", "title": "场景id(可选)"}}}, "open_game_open_apiAlarmProxyReq": {"type": "object", "properties": {"alarmType": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}}, "open_game_open_apiAlarmProxyRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiAnonymousData": {"type": "object", "properties": {"mapStatus": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiAnonymousItem"}}}}, "open_game_open_apiAnonymousItem": {"type": "object", "properties": {"Anonymous": {"type": "integer", "format": "int64", "title": "1表示匿名，0表示非匿名"}}}, "open_game_open_apiAwardInfo": {"type": "object", "properties": {"id": {"type": "string", "title": "奖项id"}, "remarks": {"type": "string", "title": "备注信息"}}, "title": "奖项信息，其他字段后续自行补充"}, "open_game_open_apiBalanceData": {"type": "object", "properties": {"balance": {"type": "integer", "format": "int64"}, "freeCurrencyBalance": {"type": "integer", "format": "int64", "title": "赠币余额，joox有用到"}}}, "open_game_open_apiBatchGetAnonymousStatusReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "audiencesList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "观众列表"}}}, "open_game_open_apiBatchGetAnonymousStatusRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiAnonymousData"}}}, "open_game_open_apiBatchGetProfileReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "uidList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "uid 列表"}, "avatarLength": {"type": "integer", "format": "int32", "title": "头像长, wesing 长宽一致, 0: 原图"}, "avatarWidth": {"type": "integer", "format": "int32", "title": "头像宽"}, "encryptUidList": {"type": "array", "items": {"type": "string"}, "title": "酷狗用这个"}}}, "open_game_open_apiBatchGetProfileRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiProfileData"}}}}, "open_game_open_apiBatchGetUserFeatureReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "uidList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "目标用户uid"}}}, "open_game_open_apiBatchGetUserFeatureRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiUserFeatureData"}}}, "open_game_open_apiBatchGiftDetailReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "giftIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "礼物ID"}}}, "open_game_open_apiBatchGiftDetailRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiGiftDetail"}}}}, "open_game_open_apiBatchPropDetailReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "propIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "道具ID"}}}, "open_game_open_apiBatchPropDetailRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiPropDetail"}}}}, "open_game_open_apiBatchUserDirectReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "openType": {"type": "string", "title": "open type"}, "openKey": {"type": "string", "title": "open key"}, "openId": {"type": "string", "title": "open id (cookie里的openid)"}, "targetids": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "定向id"}, "qua": {"type": "string", "title": "qua"}, "device": {"type": "string", "title": "设备信息"}}}, "open_game_open_apiBatchUserDirectRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "res": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiDirectRes"}}}}, "open_game_open_apiBigHornMsgReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "msgId": {"type": "string", "title": "消息ID msg_id"}, "content": {"type": "string", "title": "文案 content"}, "attach": {"type": "object", "additionalProperties": {"type": "string"}, "title": "附加内容 attach\n可选：跳转房间后打开的链接 map[\"afterH5URL\"] = \"url\"\n可选：大喇叭样式ID，不传用默认的 map[\"configID\"] = \"配置ID\""}, "openH5URL": {"type": "string", "title": "打开H5链接，和跳转房间同时传时优先使用"}, "roomId": {"type": "string", "title": "跳转房间ID"}}}, "open_game_open_apiBigHornMsgRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiConsumeFreeGiftReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "num": {"type": "integer", "format": "int64", "title": "消耗数量 num"}, "billNo": {"type": "string", "title": "订单号 bill_no"}, "fromDesc": {"type": "string", "title": "来源文案 from_desc"}, "reportId": {"type": "string", "format": "int64", "title": "上报 id"}}}, "open_game_open_apiConsumeFreeGiftRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiCreateTradeReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo"}, "offerId": {"type": "string", "title": "购买场景 id"}, "amount": {"type": "string", "format": "int64", "title": "订单总金额"}, "description": {"type": "string", "title": "订单描述（会展示在微信支付页面）"}, "businessData": {"type": "string", "title": "业务透传数据（发货时会回传）"}, "goodsList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CreateTradeReqGoods"}, "title": "物品列表"}, "expireTime": {"type": "string", "format": "int64", "title": "订单过期时间 单位秒 不设置会用默认的"}, "userIp": {"type": "string", "title": "用户 ip"}, "device": {"type": "string", "title": "用户设备号"}}}, "open_game_open_apiCreateTradeRsp": {"type": "object", "properties": {"tradeId": {"type": "string", "title": "订单号"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiCustomRewardItem": {"type": "object", "properties": {"StrRewardName": {"type": "string", "title": "名称"}, "StrData": {"type": "string", "title": "数据"}, "URewardType": {"type": "integer", "format": "int64", "title": "类型"}, "StrLogo": {"type": "string", "title": "图片"}, "URewardNum": {"type": "integer", "format": "int64", "title": "数量"}, "LCustomValue": {"type": "string", "format": "int64", "title": "价值"}}, "title": "自定义类型"}, "open_game_open_apiCwGameRankInfo": {"type": "object", "properties": {"friendPetId": {"type": "string", "format": "int64", "title": "好友的宠物id"}, "friendPetCover": {"type": "string", "title": "宠物头像"}, "friendPetStatus": {"$ref": "#/definitions/open_game_open_apiPetStatus", "title": "宠物状态"}, "friendPetLiveStatus": {"$ref": "#/definitions/open_game_open_apiPetLiveStatus", "title": "宠物饥饿状态"}, "petInteractiveStatus": {"$ref": "#/definitions/open_game_open_apiPetInteractiveStatus", "title": "宠物互动操作状态"}, "concerned": {"type": "integer", "format": "int32", "title": "是否关注 1:展示关注icon 2:展示微信 3:展示qq"}}}, "open_game_open_apiCwsxAdSpotTimesReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uid": {"type": "string"}}}, "open_game_open_apiCwsxAdSpotTimesRsp": {"type": "object", "properties": {"spotTimes": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiCwsxGetSuggestionReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "appId": {"$ref": "#/definitions/CwsxGetSuggestionReqAppId"}, "context": {"$ref": "#/definitions/CwsxGetSuggestionReqGameContext"}}}, "open_game_open_apiCwsxGetSuggestionRsp": {"type": "object", "properties": {"operationSuggestions": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CwsxGetSuggestionRspOperation"}}, "algorithmInfo": {"type": "string"}, "traceId": {"type": "string"}}}, "open_game_open_apiCwsxQueryOrdersReq": {"type": "object", "properties": {"start_time": {"type": "string", "format": "int64"}, "end_time": {"type": "string", "format": "int64"}, "passback": {"type": "string", "format": "int64"}}}, "open_game_open_apiCwsxQueryOrdersRsp": {"type": "object", "properties": {"data": {"$ref": "#/definitions/open_game_open_apiCwsxQueryOrdersRspData"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiCwsxQueryOrdersRspData": {"type": "object", "properties": {"data": {"type": "string"}, "passback": {"type": "string", "format": "int64"}, "has_more": {"type": "boolean"}}}, "open_game_open_apiDCReportReq": {"type": "object", "properties": {"tableName": {"type": "string"}, "data": {"type": "array", "items": {"type": "string"}}}}, "open_game_open_apiDCReportRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}}}, "open_game_open_apiDataReportReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "type": {"type": "string", "title": "type 数据类型"}, "data": {"type": "string", "title": "data 数据"}, "platId": {"type": "string", "format": "uint64", "title": "平台id"}}}, "open_game_open_apiDataReportRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiDirectRes": {"type": "object", "properties": {"targetId": {"type": "string", "format": "int64"}, "res": {"type": "integer", "format": "int32", "title": "结果(1:命中,0:未命中)"}}}, "open_game_open_apiFeature": {"type": "object", "properties": {"age": {"type": "integer", "format": "int32", "title": "年龄"}, "city": {"type": "integer", "format": "int32", "title": "城市code"}, "gender": {"type": "integer", "format": "int32", "title": "1男2女"}, "level": {"type": "integer", "format": "int32", "title": "用户等级"}, "intimacyList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiIntimacy"}, "title": "亲密度列表，最多50个"}}}, "open_game_open_apiFollowInfo": {"type": "object", "properties": {"result": {"$ref": "#/definitions/open_game_open_apiFollowResult"}, "uid": {"type": "string", "format": "uint64"}}}, "open_game_open_apiFollowOptReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "uidList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "uid 列表"}, "qua": {"type": "string", "title": "qua 设备qua"}, "deviceInfo": {"type": "string", "title": "deviceInfo"}, "type": {"$ref": "#/definitions/open_game_open_apiFollowOptType", "title": "FollowOptType"}, "source": {"type": "string", "format": "int64", "title": "souce 业务来源id"}}}, "open_game_open_apiFollowOptRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "results": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiFollowInfo"}, "title": "key:uid"}}}, "open_game_open_apiFollowOptType": {"type": "string", "enum": ["FOLLOW_TYPE_NONE", "FOLLOW_TYPE_ADD", "FOLLOW_TYPE_SUB"], "default": "FOLLOW_TYPE_NONE"}, "open_game_open_apiFollowResult": {"type": "string", "enum": ["FOLLOW_TYPE_SUCC", "FOLLOW_TYPE_FAIL"], "default": "FOLLOW_TYPE_SUCC"}, "open_game_open_apiGameCheckType": {"type": "string", "enum": ["CheckTypeNone", "CheckTypeCreate", "CheckTypeJoin"], "default": "CheckTypeNone", "title": "- CheckTypeNone: 无\n - CheckTypeCreate: 互动游戏创建房间\n - CheckTypeJoin: 互动游戏加入房间"}, "open_game_open_apiGameFeedBase": {"type": "object", "properties": {"params": {"type": "object", "additionalProperties": {"type": "string"}}, "jumpUrl": {"type": "string", "title": "底部按钮跳转链接"}}}, "open_game_open_apiGameMiddleInfo": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "uid"}, "gameAppid": {"type": "string", "title": "game app id"}, "encryptUid": {"type": "string", "title": "encrypt_uid"}, "ipv4": {"type": "integer", "format": "int64", "title": "客户端ipv4"}, "ipv6": {"type": "string", "title": "客户端ipv6"}, "strUid": {"type": "string", "title": "strUid 支持str类型的平台uid"}, "deviceInfo": {"type": "string", "title": "设备信息"}}, "title": "游戏中台数据"}, "open_game_open_apiGamePackapiReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "toUidId": {"type": "string", "format": "uint64", "title": "动作朝向 uid"}, "cmdId": {"type": "integer", "format": "int64", "title": "cmd类型 游戏自己定义"}, "msgId": {"type": "string", "title": "消息唯一ID"}, "ts": {"type": "integer", "format": "int64", "title": "时间时间戳"}, "payload": {"type": "string", "title": "app_id + cmd_id对接的结构体 json.Marshal 之后的结果"}}}, "open_game_open_apiGamePackapiRsp": {"type": "object", "properties": {"msgRtn": {"type": "string", "title": "回包"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiGameQuickGiftConfiglReq": {"type": "object", "properties": {"gameAppid": {"type": "string", "title": "游戏appid"}}}, "open_game_open_apiGameQuickGiftConfiglRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "sendData": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiGiftDetail"}, "title": "送礼列表"}, "backData": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiGiftDetail"}, "title": "回礼列表"}}}, "open_game_open_apiGender": {"type": "string", "enum": ["GenderUnknown", "Gender<PERSON>an", "GenderWoman"], "default": "GenderUnknown", "title": "- GenderMan: 男\n - GenderWoman: 女"}, "open_game_open_apiGeoInfo": {"type": "object", "properties": {"countryCode": {"type": "string"}, "countryName": {"type": "string"}, "provinceCode": {"type": "string"}, "provinceName": {"type": "string"}, "cityCode": {"type": "string"}, "cityName": {"type": "string"}}}, "open_game_open_apiGetBalanceReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}}}, "open_game_open_apiGetBalanceRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32", "title": "余额"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiBalanceData"}}}, "open_game_open_apiGetCwGameRankInfoReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "uid": {"type": "string", "format": "uint64", "title": "主人态 uid"}, "uidList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "榜单 uid 列表,支持查自己"}}}, "open_game_open_apiGetCwGameRankInfoRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32"}, "errorMsg": {"type": "string"}, "cwGameRankInfos": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiCwGameRankInfo"}, "title": "用户资料 key 为 uid"}}}, "open_game_open_apiGetInvitateFriendReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo"}, "limit": {"type": "integer", "format": "int64", "title": "限制长度 最多100"}}}, "open_game_open_apiGetInvitateFriendRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "uids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "好友列表"}}}, "open_game_open_apiGetPlatBalanceReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "platAssetId": {"type": "string", "title": "plat_asset_id 平台货币ID"}, "platAssetType": {"type": "integer", "format": "int64", "title": "plat_asset_type 平台货币类型 参考PlatAssetType"}}}, "open_game_open_apiGetPlatBalanceRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "balance": {"type": "string", "format": "uint64"}}}, "open_game_open_apiGetProfileReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "avatarLength": {"type": "integer", "format": "int32", "title": "头像长, wesing 长宽一致, 0: 原图"}, "avatarWidth": {"type": "integer", "format": "int32", "title": "头像宽"}}}, "open_game_open_apiGetProfileRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiProfileData"}}}, "open_game_open_apiGetRiskLevelReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "uidList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "uid 列表"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展参数"}}}, "open_game_open_apiGetRiskLevelRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "mapRiskLevel": {"type": "object", "additionalProperties": {"type": "string", "format": "int64"}, "title": "uid <-> 风险等级"}, "mapErrCodes": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "title": "uid <-> <-> err code"}}}, "open_game_open_apiGetSendGiftOrderReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "orderId": {"type": "string", "title": "每次请求访问的唯一标识"}, "orderTime": {"type": "string", "title": "送礼订单下单的时间戳，精确到毫秒(酷狗用到)"}, "userId": {"type": "string", "title": "送礼者用户id"}}}, "open_game_open_apiGetSendGiftOrderRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"type": "string"}}}, "open_game_open_apiGetUserIntimacyReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}}}, "open_game_open_apiGetUserIntimacyRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "intimacy": {"type": "object", "additionalProperties": {"type": "number", "format": "float"}}}}, "open_game_open_apiGetUserSwitchReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}}}, "open_game_open_apiGetUserSwitchRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}, "swInfo": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiSwitchInfo"}}}}, "open_game_open_apiGiftDetail": {"type": "object", "properties": {"giftName": {"type": "string", "title": "礼物名称"}, "giftLogo": {"type": "string", "title": "礼物logo"}, "giftPrice": {"type": "integer", "format": "int64", "title": "礼物单价"}}}, "open_game_open_apiHippyInfo": {"type": "object", "properties": {"msgTxt": {"type": "string"}, "btnTxt": {"type": "string"}, "bgPic": {"type": "string"}, "btnPic": {"type": "string"}, "jumpUrl": {"type": "string"}, "roundId": {"type": "string"}, "confId": {"type": "integer", "format": "int32"}, "msgId": {"type": "string"}}}, "open_game_open_apiHippyMsgUserSwitchEventReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "isOpen": {"type": "boolean"}, "swType": {"type": "integer", "format": "int64", "title": "调用侧指定"}}}, "open_game_open_apiHippyMsgUserSwitchEventRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiIncentiveType": {"type": "string", "enum": ["IncentiveTypeNone", "IncentiveTypeBI", "IncentiveTypeECPM"], "default": "IncentiveTypeNone", "title": "- IncentiveTypeNone: 非法类型\n - IncentiveTypeBI: 使用后台下发的激励广告奖励数值\n - IncentiveTypeECPM: 使用商广返回的激励广告奖励数值（基于ecpm）"}, "open_game_open_apiInteractiveGameSwitchRoomReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "app id"}, "uid": {"type": "string", "format": "uint64", "title": "主人态 uid"}, "modeId": {"type": "string"}, "payModeId": {"type": "string"}, "ext": {"type": "object", "additionalProperties": {"type": "string"}}}}, "open_game_open_apiInteractiveGameSwitchRoomRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "roomId": {"type": "string"}}}, "open_game_open_apiIntimacy": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "intimacyScore": {"type": "integer", "format": "int64", "title": "亲密度Score*10000"}}}, "open_game_open_apiJudgeOnlineReq": {"type": "object", "properties": {"appId": {"type": "string"}, "uids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "open_game_open_apiJudgeOnlineRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "onlineInfo": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiOnlineInfo"}}}}, "open_game_open_apiLotteryReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo"}, "activityID": {"type": "string"}, "consumeID": {"type": "string"}, "mapParams": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传扩展参数"}}}, "open_game_open_apiLotteryRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32", "title": "业务错误码，参考 pb/code.proto LuckyDrawXX"}, "error_msg": {"type": "string"}, "activityID": {"type": "string", "title": "活动id"}, "awardInfo": {"$ref": "#/definitions/open_game_open_apiAwardInfo", "title": "中奖奖项信息"}, "safetyParams": {"$ref": "#/definitions/open_game_open_apiSafetyParams", "title": "安全相关参数"}, "ticketCnt": {"type": "integer", "format": "int64", "title": "用户拥有的券数量"}}}, "open_game_open_apiMidas": {"type": "object", "properties": {"pf": {"type": "string"}, "pfKey": {"type": "string"}, "sessionId": {"type": "string"}, "sessionType": {"type": "string"}, "payToken": {"type": "string"}}}, "open_game_open_apiNoteRankItem": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "用户id"}, "notes": {"type": "string", "format": "uint64", "title": "音符"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "cwGameRankInfo": {"$ref": "#/definitions/open_game_open_apiCwGameRankInfo", "title": "cw游戏排行榜信息"}, "extraNote": {"type": "integer", "format": "int32", "title": "是否有额外音符可收"}}}, "open_game_open_apiNoteRankReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "用户id"}, "passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}}}, "open_game_open_apiNoteRankRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32"}, "errorMsg": {"type": "string"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiNoteRankItem"}, "title": "排行榜列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "再次翻页的时候需要把这个东西传过来"}, "friendApiAuth": {"type": "integer", "format": "int32", "title": "好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆"}, "bingStatus": {"type": "integer", "format": "int32", "title": "绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定"}, "selfRankItem": {"$ref": "#/definitions/open_game_open_apiSelfRankItem", "title": "自己的榜单信息"}}}, "open_game_open_apiNoteWeekRankReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64", "title": "用户id"}, "passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}}}, "open_game_open_apiNoteWeekRankRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32"}, "errorMsg": {"type": "string"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiNoteRankItem"}, "title": "排行榜列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "passback": {"type": "integer", "format": "int32", "title": "再次翻页的时候需要把这个东西传过来"}, "friendApiAuth": {"type": "integer", "format": "int32", "title": "好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆"}, "bingStatus": {"type": "integer", "format": "int32", "title": "绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定"}, "selfRankItem": {"$ref": "#/definitions/open_game_open_apiSelfRankItem", "title": "自己的榜单信息"}}}, "open_game_open_apiOnlineInfo": {"type": "object", "properties": {"status": {"type": "integer", "format": "int64"}, "lastHbTime": {"type": "integer", "format": "int64", "title": "上次心跳时间"}}}, "open_game_open_apiOrderConf": {"type": "object", "properties": {"revenueType": {"type": "integer", "format": "int64", "title": "收入类型 1/计收入 2/不计收入"}, "payType": {"type": "integer", "format": "int64", "description": "例如，类型为1：赠送礼物获得，则需送出pay_gift_num个id为pay_gift_id的礼物，赠送目标为Scene中的场景和tarUid（可以将礼物价值和amount进行校验）\n          类型为3：赠送道具获得，则需送出pay_prop_num个id为pay_prop_id的道具，赠送目标为Scene中的场景和tarUid\n          类型为2：不送出道具和礼物，直接扣费", "title": "购买模式 1/赠送礼物获得 2/直接获得 3/赠送道具获得"}, "platBusinessId": {"type": "integer", "format": "int64", "title": "平台业务ID"}, "payGiftId": {"type": "integer", "format": "int64", "title": "送出礼物id"}, "payGiftNum": {"type": "integer", "format": "int64", "title": "送出礼物个数（建议这里的礼物id+礼物个数算出来的饭票数，和amount校验下是否相等）"}}}, "open_game_open_apiPayData": {"type": "object", "properties": {"bill_no": {"type": "string", "title": "订单号"}, "balance": {"type": "integer", "format": "int64", "title": "扣除后余额"}}}, "open_game_open_apiPayReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "billNo": {"type": "string", "title": "订单号, 全局唯一"}, "amount": {"type": "integer", "format": "int64", "title": "扣除数量, 必须大于0"}, "appRemark": {"type": "string", "title": "备注, 会写流水"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiUserAssetChange"}, "title": "增加资产列表, 调用delivery时透传"}, "orderConf": {"$ref": "#/definitions/open_game_open_apiOrderConf", "title": "订单配置，平台业务id，是否算收入等"}, "scene": {"$ref": "#/definitions/open_game_open_apiScene", "title": "场景"}, "device": {"$ref": "#/definitions/interfaceopen_game_open_apiDevice", "title": "设备相关"}, "midas": {"$ref": "#/definitions/open_game_open_apiMidas", "title": "midas"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "mapExt"}, "sig": {"type": "string", "title": "订单签名，没有忽略"}, "transactionId": {"type": "string", "title": "调用Delivery接口时透传这个id"}, "roundId": {"type": "string", "title": "游戏场次 id"}}}, "open_game_open_apiPayRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiPayData"}}}, "open_game_open_apiPetInteractiveStatus": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "PetReceivingNote", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "PetUnHappy", "PetSick"], "default": "<PERSON><PERSON><PERSON><PERSON>", "title": "- PetDefault: 无\n - PetReceivingNote: 收音符\n - PetHunger: 饿了\n - PetDirty: 脏了\n - PetUnHappy: 不开心\n - PetSick: 生病"}, "open_game_open_apiPetLiveStatus": {"type": "string", "enum": ["PetLSNormal", "PetLSHungry"], "default": "PetLSNormal", "title": "- PetLSNormal: 正常状态\n - PetLSHungry: 饥饿状态"}, "open_game_open_apiPetStatus": {"type": "string", "enum": ["PetNotAdopt", "Pet<PERSON><PERSON><PERSON>", "PetHatched", "PetIdle", "PetOutWaiting", "PetOutting", "PetBack", "PetDying", "Pet<PERSON><PERSON>ing"], "default": "PetNotAdopt", "title": "- PetNotAdopt: 尚未被领取, 所有宠物的初始状态\n - PetAdopted: 已领取孵化中, 用户点击收下宠物蛋后转这个状态\n - PetHatched: 已孵化成功, 成功孵化后转这个状态\n - PetIdle: 在小窝, 用户点击领养宠物/出门回家被查看后转这个状态\n - PetOutWaiting: 点击出门之后, 宠物处于出门等待中状态\n - PetOutting: 出门中, 宠物匹配到后转这个状态\n - PetBack: 宠物刚刚回家\n - PetDying: 濒临死亡状态\n - PetRescuing: 听歌抢救状态"}, "open_game_open_apiPlaceOrderReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "amount": {"type": "integer", "format": "int64", "title": "扣除数量, 必须大于0"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiUserAssetChange"}, "title": "增加资产列表, 调用delivery时透传"}, "orderConf": {"$ref": "#/definitions/open_game_open_apiOrderConf", "title": "订单配置，平台业务id，是否算收入等"}, "scene": {"$ref": "#/definitions/open_game_open_apiScene", "title": "场景"}, "device": {"$ref": "#/definitions/interfaceopen_game_open_apiDevice", "title": "设备相关"}, "midas": {"$ref": "#/definitions/open_game_open_apiMidas", "title": "midas"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "mapExt"}, "sysTs": {"type": "integer", "format": "int64", "title": "时间 用于透传"}}}, "open_game_open_apiPlaceOrderRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "billNo": {"type": "string", "title": "全局唯一"}, "sig": {"type": "string", "title": "订单签名，没有忽略"}, "sysTs": {"type": "integer", "format": "int64", "title": "时间 用于透传"}}}, "open_game_open_apiPlatAssetChange": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64"}, "assetNum": {"type": "string", "format": "int64"}}}, "open_game_open_apiPlatAssetType": {"type": "string", "enum": ["PlatAssetTypeDefault", "PlatAssetTypeDiamond", "PlatAssetTypeDiamondGift", "PlatAssetTypeSilverCoin", "PlatAssetTypeCarrierDogecoin", "PlatAssetTypeKgWZCoin", "PlatAssetTypeLrTaskCoin"], "default": "PlatAssetTypeDefault", "title": "- PlatAssetTypeDefault: 默认\n - PlatAssetTypeDiamond: K歌钻石\n - PlatAssetTypeDiamondGift: K歌钻石礼物\n - PlatAssetTypeSilverCoin: wesing银币\n - PlatAssetTypeCarrierDogecoin: 酷狗网赚金币\n - PlatAssetTypeKgWZCoin: K歌网赚金币\n - PlatAssetTypeLrTaskCoin: 懒人网赚金币"}, "open_game_open_apiPlatUserAsset": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64"}, "assetNum": {"type": "string", "format": "int64"}}}, "open_game_open_apiPresentData": {"type": "object", "properties": {"bill_no": {"type": "string", "title": "订单号"}, "balance": {"type": "integer", "format": "int64", "title": "奖励后余额"}}}, "open_game_open_apiPresentReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "billNo": {"type": "string", "title": "订单号"}, "amount": {"type": "integer", "format": "int64", "title": "奖励个数"}, "appRemark": {"type": "string", "title": "备注, 会写流水"}, "roundId": {"type": "string", "title": "游戏场次 id"}, "assetItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PresentReqAssetItems"}, "title": "资产"}}}, "open_game_open_apiPresentRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiPresentData"}}}, "open_game_open_apiPrizeGift": {"type": "object", "properties": {"name": {"type": "string", "title": "礼物名称"}, "icon": {"type": "string", "title": "礼物图标"}, "num": {"type": "integer", "format": "int64", "title": "礼物数量"}, "giftId": {"type": "string", "title": "礼物ID"}}}, "open_game_open_apiPrizeReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "amount": {"type": "integer", "format": "int64", "title": "金额"}, "billNo": {"type": "string", "title": "订单号"}}}, "open_game_open_apiPrizeRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiPrizeGift"}, "title": "礼物列表"}}}, "open_game_open_apiPrizeV2Req": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "amount": {"type": "integer", "format": "int64", "title": "金额"}, "billNo": {"type": "string", "title": "订单号"}, "gifts": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiPrizeGift"}, "title": "待发奖礼物"}}}, "open_game_open_apiPrizeV2Rsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiProfileData": {"type": "object", "properties": {"nickname": {"type": "string", "title": "昵称"}, "avatar": {"type": "string", "title": "头像"}, "encryptUid": {"type": "string", "title": "加密 uid"}, "treasureLevel": {"type": "integer", "format": "int64", "title": "财富等级"}, "avatarFrame": {"type": "string", "title": "头像框"}, "age": {"type": "integer", "format": "int64", "title": "年龄"}, "vipLevel": {"type": "integer", "format": "int64", "title": "vip 等级"}, "gender": {"$ref": "#/definitions/open_game_open_apiGender", "title": "性别"}, "city": {"type": "string", "title": "城市"}, "extra": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Q音vip_level>0时，map[\"vipType\"] = \"1(绿钻)、2(豪华绿钻)、3(超级会员)\"\n                 map[\"vipEndTime\"] = \"(unix时间戳)\""}}}, "open_game_open_apiPropDetail": {"type": "object", "properties": {"propName": {"type": "string", "title": "道具名称"}, "propLogo": {"type": "string", "title": "道具logo"}}}, "open_game_open_apiQueryAssetReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "assetIds": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "资产ID"}, "source": {"$ref": "#/definitions/open_game_open_apiSourceChannel", "title": "来源配置"}, "assetType": {"$ref": "#/definitions/open_game_open_apiPlatAssetType", "title": "资产类型"}}}, "open_game_open_apiQueryAssetRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}, "mapResult": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiPlatUserAsset"}, "title": "结果资产列表"}}}, "open_game_open_apiQueryBlacklistReq": {"type": "object", "properties": {"vUid": {"type": "string", "format": "int64"}, "huidList": {"type": "array", "items": {"type": "string", "format": "int64"}}, "op": {"type": "string", "format": "int64", "title": "op=0:\n验证vuid是否在huidlist的黑名单当中，blacklist为在其黑名单当中的uid列表\nop=1:\n验证huidlist是否在vuid的黑名单当中，blacklist为在其黑名单当中的uid列表"}}}, "open_game_open_apiQueryBlacklistRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "Data": {"$ref": "#/definitions/open_game_open_apiQueryBlacklistRspData"}}}, "open_game_open_apiQueryBlacklistRspData": {"type": "object", "properties": {"uidList": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "open_game_open_apiQueryCertInfoReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "cookie": {"type": "object", "additionalProperties": {"type": "string"}, "title": "登录态信息"}, "clientIp": {"type": "string", "title": "客户端 ip"}, "checkType": {"$ref": "#/definitions/open_game_open_apiGameCheckType", "title": "校验类型"}, "roomId": {"type": "string", "title": "房间 id"}}}, "open_game_open_apiQueryCertInfoRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}, "isRealName": {"type": "boolean", "title": "是否实名"}, "adultType": {"$ref": "#/definitions/open_game_open_apiAdultType", "title": "是否成年"}, "authUrl": {"type": "string", "title": "认证链接"}}}, "open_game_open_apiQueryFriendsReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "int64"}, "appId": {"type": "string", "title": "小游戏AppId"}, "mask": {"type": "integer", "format": "int64", "title": "0x1 双向关注 0x2 绑定好友 0x4 单向关注"}, "cookie": {"type": "object", "additionalProperties": {"type": "string"}, "title": "登录态信息"}}}, "open_game_open_apiQueryFriendsRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apiQueryFriendsRspData"}}}, "open_game_open_apiQueryFriendsRspData": {"type": "object", "properties": {"uidList": {"type": "array", "items": {"type": "string", "format": "int64"}}, "friendApiAuth": {"type": "integer", "format": "int32", "title": "好友关系授权信息，0：授权；1:qq或者微信没有授权；2:qq音乐端内隐私没有开启；3:票据过期需要重新登陆"}, "bingStatus": {"type": "integer", "format": "int32", "title": "绑定账号状态：0：已经绑定已授权；1：已绑定无授权：2：未绑定"}}}, "open_game_open_apiQueryGeoReq": {"type": "object", "properties": {"ip": {"type": "string"}}}, "open_game_open_apiQueryGeoRsp": {"type": "object", "properties": {"geo": {"$ref": "#/definitions/open_game_open_apiGeoInfo"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiQueryRelationReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "uidList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "uid 列表"}, "mask": {"$ref": "#/definitions/open_game_open_apiRelatioMask", "title": "mask"}}}, "open_game_open_apiQueryRelationRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "ralations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiRalationInfo"}, "title": "key:uid"}}}, "open_game_open_apiQzaReportReq": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "string"}}, "newReport": {"type": "boolean"}, "new_report_qmusic": {"type": "boolean"}}}, "open_game_open_apiQzaReportRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiRalationInfo": {"type": "object", "properties": {"type": {"$ref": "#/definitions/open_game_open_apiRelationType"}, "uid": {"type": "string", "format": "uint64"}}}, "open_game_open_apiRefundReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "billNo": {"type": "string", "title": "订单号"}, "appRemark": {"type": "string", "title": "备注，会写流水日志"}}}, "open_game_open_apiRefundRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiRelatioMask": {"type": "string", "enum": ["RELATION_MASK_ALL", "RELATION_MASK_FOLLOW"], "default": "RELATION_MASK_ALL", "title": "- RELATION_MASK_ALL: 所有关系\n - RELATION_MASK_FOLLOW: 拉取关注"}, "open_game_open_apiRelationType": {"type": "string", "enum": ["RELATION_NONE", "RELATION_FOLLOW"], "default": "RELATION_NONE", "title": "- RELATION_NONE: 无关系\n - RELATION_FOLLOW: 关注"}, "open_game_open_apiReportTaskConditionBillReq": {"type": "object", "properties": {"bill": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiTaskConditionBill"}}}}, "open_game_open_apiReportTaskConditionBillRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiRewardDetailBatchReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "giftIds": {"type": "array", "items": {"type": "string"}, "title": "平台礼物ID"}, "giftType": {"type": "integer", "format": "int64", "title": "平台礼物type 参考reward_sender_comm.proto GiftType"}}}, "open_game_open_apiRewardDetailBatchRsp": {"type": "object", "properties": {"subGiftDetails": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiRewardDetailBatchRspRewardDetail"}}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiRewardDetailBatchRspRewardDetail": {"type": "object", "properties": {"giftArray": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiSubGiftDetail"}, "title": "SubGiftDetail 奖品ID 所指向的礼物列表"}, "customRewardItem": {"$ref": "#/definitions/open_game_open_apiCustomRewardItem", "title": "自定义类型"}}}, "open_game_open_apiRewardDetailReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "giftId": {"type": "string", "title": "平台礼物ID"}, "giftType": {"type": "integer", "format": "int64", "title": "平台礼物type 参考reward_sender_comm.proto GiftType"}}}, "open_game_open_apiRewardDetailRsp": {"type": "object", "properties": {"giftArray": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiSubGiftDetail"}, "title": "SubGiftDetail 平台礼物ID 所指向的礼物列表"}, "error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "customRewardItem": {"$ref": "#/definitions/open_game_open_apiCustomRewardItem", "title": "自定义奖励类型"}}}, "open_game_open_apiRoomMsgReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "msgText": {"type": "string", "title": "消息文案 msg_text"}, "msgType": {"type": "integer", "format": "int64", "title": "消息类型"}, "roomId": {"type": "string", "title": "房间 ID"}, "toUid": {"type": "string", "format": "uint64", "title": "to uid"}, "isC2c": {"type": "boolean", "title": "c2c 消息"}, "extData": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展数据"}}}, "open_game_open_apiRoomMsgRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiSafeCheckCallbackV2Req": {"type": "object", "properties": {"requestId": {"type": "string"}, "taskId": {"type": "string"}, "dataId": {"type": "string"}, "callback": {"type": "string", "title": "解析CallbackMsg的json字符串"}, "suggestion": {"type": "string"}, "strategyConclusion": {"type": "string"}, "resultType": {"type": "integer", "format": "int64", "title": "结果方式 1:机审结果，2：人审结果, 3：命中审核结果缓存,把人审结果缓存下来，作为下次审核的结果自动返回block或pass。"}}}, "open_game_open_apiSafeCheckPicReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "toUid": {"type": "string", "format": "uint64", "title": "目标用户uid"}, "safeAppid": {"type": "integer", "format": "int32", "title": "安全 appid"}, "qua": {"type": "string", "title": "qua 设备qua"}, "picUrl": {"type": "string", "title": "pic 上报图片url"}, "roomId": {"type": "string", "title": "房间id"}, "params": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展参数"}}}, "open_game_open_apiSafeCheckPicRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apisafeData"}}}, "open_game_open_apiSafeCheckReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "toUid": {"type": "string", "format": "uint64", "title": "目标用户uid"}, "safeAppid": {"type": "integer", "format": "int32", "title": "安全 appid"}, "qua": {"type": "string", "title": "qua 设备qua"}, "content": {"type": "string", "title": "content 待检查文案"}}}, "open_game_open_apiSafeCheckRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "data": {"$ref": "#/definitions/open_game_open_apisafeData"}}}, "open_game_open_apiSafeCheckV2BasicInfo": {"type": "object", "properties": {"apiVersion": {"type": "string", "title": "填v1.0"}, "appId": {"type": "string", "title": "安全侧分配"}, "category": {"type": "string", "title": "安全侧分配"}, "platform": {"type": "string", "title": "平台(kg/music)"}, "sendTimeMillisecond": {"type": "string", "title": "时间戳字符串"}, "accountType": {"type": "integer", "format": "int64", "title": "该字段表示业务用户ID对应的账号类型，取值：1-微信uin，2-QQ号，3-微信群uin，4-qq群号，5-微信openid，6-QQopenid，7-其它string。该字段与账号ID参数（UserId）配合使用可确定唯一账号。"}, "postUin": {"type": "string", "title": "该字段可传入微信openid、QQopenid、字符串等账号信息，与账号类别参数（accountType）配合使用可确定唯一账号"}, "requestId": {"type": "string", "title": "请求id"}, "dataId": {"type": "string", "title": "该字段用于返回检测对象对应请求参数中的DataId，与输入的DataId字段中的内容对应。注意：此字段可能返回 null，表示取不到有效值。"}, "callback": {"type": "string", "title": "回传参数, 解析CallbackMsg的json字符串"}, "callbackUrl": {"type": "string", "title": "回调地址, 不用填, 中台会自己填充"}}}, "open_game_open_apiSafeCheckV2CheckInfo": {"type": "object", "properties": {"textContent": {"type": "string"}, "title": {"type": "string"}, "atInfo": {"type": "string", "title": "用于保存 @内容;"}, "comment": {"type": "string", "title": "分享或转载理由"}, "imageUrl": {"type": "array", "items": {"type": "string"}, "title": "图片"}, "audioUrl": {"type": "string", "title": "音频"}, "videoUrl": {"type": "string", "title": "视频"}}}, "open_game_open_apiSafeCheckV2DeviceInfo": {"type": "object", "properties": {"ip": {"type": "string"}, "mac": {"type": "string"}, "imei": {"type": "string"}, "idfa": {"type": "string"}, "idfv": {"type": "string"}, "mobileFlag": {"type": "integer", "format": "int64", "title": "是否来自手机"}, "mobleQUA": {"type": "string", "title": "qua"}, "uuid": {"type": "string"}, "udid": {"type": "string"}, "qimei36": {"type": "string"}, "deviceInfo": {"type": "string"}}}, "open_game_open_apiSafeCheckV2Req": {"type": "object", "properties": {"device": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2DeviceInfo", "title": "设备信息, 有的尽量填"}, "user": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2UserInfo", "title": "可选, 用途: 数据分析会用到可能会作为标签生产的依据, 有的尽量填"}, "basic": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2BasicInfo", "title": "基础信息, 包含业务透传信息"}, "check": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2CheckInfo", "title": "送审信息"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "透传信息"}, "platform": {"type": "string", "title": "平台"}, "gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}}}, "open_game_open_apiSafeCheckV2ResultDetail": {"type": "object", "properties": {"hitType": {"type": "integer", "format": "int32", "title": "//打击方式"}, "strPrompt": {"type": "string", "title": "提示语"}, "hitReason": {"type": "integer", "format": "int32"}, "strReason": {"type": "string", "title": "打击原因"}, "strategyConclusion": {"type": "string", "title": "结论"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "其他信息"}}}, "open_game_open_apiSafeCheckV2Rsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "suggestion": {"type": "string", "title": "Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过"}, "basic": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2BasicInfo", "title": "请求时携带的basic信息"}, "detail": {"$ref": "#/definitions/open_game_open_apiSafeCheckV2ResultDetail", "title": "审查详情"}}}, "open_game_open_apiSafeCheckV2UserInfo": {"type": "object", "properties": {"nickname": {"type": "string"}, "gender": {"type": "integer", "format": "int64", "title": "0 未知 1男 2女"}, "age": {"type": "integer", "format": "int64"}, "level": {"type": "integer", "format": "int64", "title": "等级，0（默认值，代表等级未知）、1（等级较低）、2（等级中等）、3（等级较高），目前暂不支持自定义等级"}, "phone": {"type": "string"}, "headUrl": {"type": "string"}, "signature": {"type": "string"}, "userRole": {"type": "string", "title": "用户类型角色，角色与安全中心约定即可"}, "isPremium": {"type": "integer", "format": "int64", "title": "是否有签约付费属性，0为默认值，1为有"}, "friendNum": {"type": "integer", "format": "int64", "title": "好友数量"}, "fansNum": {"type": "integer", "format": "int64", "title": "粉丝数量"}, "hostUserRole": {"type": "string", "title": "被动方用户类型角色, 如歌房送礼 接收方的角色"}}, "title": "Optional 可选"}, "open_game_open_apiSafetyParams": {"type": "object", "properties": {"validateURL": {"type": "string", "title": "验证码url，error_code=LuckyDrawNeedSafeVerify时使用"}}}, "open_game_open_apiScene": {"type": "object", "properties": {"sceneType": {"type": "integer", "format": "int64", "title": "参考 SceneType"}, "roomId": {"type": "string"}, "showId": {"type": "string"}, "ugcId": {"type": "string"}, "tarUid": {"type": "string", "title": "例如ugc作者，ktv房主，LIVE主播"}}}, "open_game_open_apiSelfRankItem": {"type": "object", "properties": {"rank": {"type": "integer", "format": "int32", "title": "用户排名"}, "notes": {"type": "string", "format": "uint64", "title": "音符"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "friendPetCover": {"type": "string", "title": "宠物头像"}}}, "open_game_open_apiSendHippyMsgReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "roomId": {"type": "string"}, "switchType": {"type": "integer", "format": "int32"}, "isSwitchFilter": {"type": "boolean"}, "hippyInfo": {"$ref": "#/definitions/open_game_open_apiHippyInfo"}}}, "open_game_open_apiSendHippyMsgRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiSendMailReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "toUid": {"type": "string", "format": "uint64", "title": "to uid"}, "toOpenid": {"type": "string", "title": "openid"}, "content": {"type": "string", "title": "content, 私信内容, content和content_id填一个"}, "contentId": {"type": "string", "title": "content_id 私信内容id, 用于多语言"}, "attachment": {"type": "object", "additionalProperties": {"type": "string"}, "title": "附加信息"}}}, "open_game_open_apiSendMailRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiSendPushReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "content": {"type": "string", "title": "文案 content"}, "attach": {"type": "object", "additionalProperties": {"type": "string"}, "title": "附加内容 attach"}, "fromUid": {"type": "string", "format": "uint64"}, "flowConfigId": {"type": "string"}}}, "open_game_open_apiSendPushRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiSendSingleRewardReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "rewardId": {"type": "string", "format": "int64", "title": "奖励ID 透传用"}, "num": {"type": "integer", "format": "int64", "title": "数量 num"}, "billNo": {"type": "string", "title": "订单号 bill_no"}, "rewardItem": {"$ref": "#/definitions/open_game_open_apiSingleRewardItem", "title": "奖品信息 reward_item"}, "consumeItem": {"$ref": "#/definitions/SendSingleRewardReqConsumeAssetItem", "title": "消耗的资产信息"}, "device": {"$ref": "#/definitions/open_game_open_apiSendSingleRewardReqDevice", "title": "设备信息"}, "MapExt": {"type": "object", "additionalProperties": {"type": "string"}}, "originBillNo": {"type": "string", "title": "礼包发放原始订单号 bill_no, 仅用于订单回调校验"}}}, "open_game_open_apiSendSingleRewardReqDevice": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台信息 kugou、qqmusic、qmkege、kuwo、lanren"}, "version": {"type": "string", "title": "客户端版本 1.2.3"}, "os": {"type": "string", "title": "系统 android、ios"}}}, "open_game_open_apiSendSingleRewardRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiSendUserSwitchMsgReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "roomId": {"type": "string", "title": "如果制定则只发特定房间消息"}, "roomType": {"type": "integer", "format": "int32", "title": "房间类型， 0:全部 1:直播间 2:歌房"}, "swType": {"type": "integer", "format": "int32", "title": "调用侧指定"}, "msgType": {"type": "integer", "format": "int32", "title": "消息类型"}, "switchMsgInfo": {"type": "string", "title": "消息类型"}, "msgId": {"type": "string", "title": "消息ID"}}}, "open_game_open_apiSendUserSwitchMsgRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}}}, "open_game_open_apiSetUserSwitchReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "swInfo": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiSwitchInfo"}}}}, "open_game_open_apiSetUserSwitchRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}}}, "open_game_open_apiSingleRewardItem": {"type": "object", "properties": {"giftId": {"type": "string", "title": "gift_id 奖品ID"}, "giftType": {"type": "integer", "format": "int64", "title": "gift_type 奖品Type"}, "giftNum": {"type": "string", "format": "int64", "title": "gift_num 发放数量"}, "giftReason": {"type": "string", "title": "gift_reason 发放理由"}}}, "open_game_open_apiSourceChannel": {"type": "object", "properties": {"sourceId": {"type": "integer", "format": "int64", "title": "来源ID, 后台分配"}, "reason": {"type": "string"}, "appName": {"type": "string", "title": "游戏名称"}}, "title": "来源渠道"}, "open_game_open_apiSubAssetReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiPlatAssetChange"}, "title": "资产列表"}, "billNo": {"type": "string", "title": "订单ID"}, "source": {"$ref": "#/definitions/open_game_open_apiSourceChannel", "title": "来源配置"}, "timestamp": {"type": "string", "format": "int64", "title": "操作时间戳 单位毫秒"}, "assetType": {"$ref": "#/definitions/open_game_open_apiPlatAssetType", "title": "资产类型"}, "scene": {"type": "integer", "format": "int32", "title": "场景，透传上报用"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "扩展参数"}}}, "open_game_open_apiSubAssetRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码 资产数量不足返回 12001"}, "errorMsg": {"type": "string", "title": "错误信息"}, "billNo": {"type": "string", "title": "订单ID"}, "mapResult": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiPlatUserAsset"}, "title": "结果资产列表"}}}, "open_game_open_apiSubGiftDetail": {"type": "object", "properties": {"subGiftId": {"type": "string", "title": "礼物ID"}, "subGiftType": {"type": "string", "title": "礼物type 参考reward_sender_comm.proto GiftType"}, "subGiftNum": {"type": "integer", "format": "int64", "title": "礼物数量"}, "subGiftName": {"type": "string", "title": "礼物名称"}, "subGiftLogo": {"type": "string", "title": "礼物logo"}, "subGiftUnitPrice": {"type": "integer", "format": "int64", "title": "礼物单价"}, "subGiftExpireType": {"type": "integer", "format": "int64", "title": "expire_type 过期类型, 1相对过期, 2绝对过期"}, "subGiftExpireTime": {"type": "integer", "format": "int64", "title": "expire_sec 过期时间(s), 相对过期"}}, "title": "子礼物信息"}, "open_game_open_apiSwitchInfo": {"type": "object", "properties": {"swType": {"type": "integer", "format": "int32"}, "isOpen": {"type": "integer", "format": "int32", "title": "0: 关闭, 1:打开"}}}, "open_game_open_apiTDBankReportReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "table": {"type": "string", "title": "表名"}, "program": {"type": "string", "title": "服务名"}, "message": {"type": "string", "title": "数据"}, "bussId": {"type": "string", "title": "业务"}}}, "open_game_open_apiTDBankReportRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiTaskConditionBill": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo"}, "toUid": {"type": "integer", "format": "int64"}, "conditionId": {"type": "integer", "format": "int64"}, "num": {"type": "integer", "format": "int64"}, "timestamp": {"type": "integer", "format": "int64"}, "consumeId": {"type": "string"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}, "roomId": {"type": "string"}, "qua": {"type": "string"}}}, "open_game_open_apiTmeEventReq": {"type": "object", "properties": {"event": {"type": "string"}}}, "open_game_open_apiTmeEventRsp": {"type": "object", "properties": {"errorCode": {"type": "integer", "format": "int32", "title": "错误码"}, "errorMsg": {"type": "string", "title": "错误信息"}}}, "open_game_open_apiUIABTestReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "businessId": {"type": "string", "title": "业务Id"}, "channelId": {"type": "string", "title": "渠道id"}, "moduleId": {"type": "string", "title": "模块id"}, "qua": {"type": "string", "title": "客户端版本"}, "devInfo": {"type": "string", "title": "客户端设备信息"}, "callerSvrName": {"type": "string", "title": "主调服务名"}, "labels": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/open_game_open_apiAbtestLabelItem"}}}}, "open_game_open_apiUIABTestRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "mapTestInfo": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiUiAbtestRspItem"}, "title": "testInfo"}, "interval": {"type": "integer", "format": "int32", "title": "请求间隔"}}}, "open_game_open_apiUiAbtestRspItem": {"type": "object", "properties": {"testId": {"type": "string"}, "mapParams": {"type": "object", "additionalProperties": {"type": "string"}}}}, "open_game_open_apiUserAssetChange": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量"}}}, "open_game_open_apiUserFeatureData": {"type": "object", "properties": {"mapFeature": {"type": "object", "additionalProperties": {"$ref": "#/definitions/open_game_open_apiFeature"}}}}, "open_game_open_apiVerifyFollowReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "int64"}, "vecUid": {"type": "array", "items": {"type": "string", "format": "int64"}}, "followType": {"$ref": "#/definitions/VerifyFollowReqVerifyFollowType"}}}, "open_game_open_apiVerifyFollowRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}, "MapUid": {"type": "object", "additionalProperties": {"type": "boolean"}}}}, "open_game_open_apiWriteGameFeedReq": {"type": "object", "properties": {"gameMiddleInfo": {"$ref": "#/definitions/open_game_open_apiGameMiddleInfo", "title": "游戏中台数据"}, "seqId": {"type": "string", "title": "唯一id"}, "type": {"type": "integer", "format": "int64", "title": "动态类型, see EnumGameFeedType"}, "expireSec": {"type": "integer", "format": "int32", "title": "有效时长,单位:秒"}, "base": {"$ref": "#/definitions/open_game_open_apiGameFeedBase", "title": "基础样式"}, "roomid": {"type": "string", "title": "房间roomid"}}}, "open_game_open_apiWriteGameFeedRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apiZhiYanReportReq": {"type": "object", "properties": {"appMark": {"type": "string"}, "metricGroup": {"type": "string"}, "reportIndex": {"type": "object", "additionalProperties": {"type": "number", "format": "double"}}, "reportTag": {"type": "object", "additionalProperties": {"type": "string"}}, "reportTs": {"type": "string", "format": "int64"}}}, "open_game_open_apiZhiYanReportRsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "open_game_open_apisafeData": {"type": "object", "properties": {"safeType": {"type": "integer", "format": "int32", "title": "非0为被安全打击了"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}