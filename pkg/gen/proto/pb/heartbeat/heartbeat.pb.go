// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/heartbeat/heartbeat.proto

package heartbeat

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HeartBeatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string            `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string            `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	RoomID string            `protobuf:"bytes,3,opt,name=roomID,proto3" json:"roomID,omitempty"`
	MapExt map[string]string `protobuf:"bytes,4,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //业务自定义数据
	// ---------------赛艇---------------------
	// key: boatID   value:船ID
	// key: roundID  value:场次ID
	Rtt uint32 `protobuf:"varint,5,opt,name=rtt,proto3" json:"rtt,omitempty"` //请求往返时间ms级 （请求耗时-bizUseMs）
}

func (x *HeartBeatReq) Reset() {
	*x = HeartBeatReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_heartbeat_heartbeat_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartBeatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartBeatReq) ProtoMessage() {}

func (x *HeartBeatReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_heartbeat_heartbeat_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartBeatReq.ProtoReflect.Descriptor instead.
func (*HeartBeatReq) Descriptor() ([]byte, []int) {
	return file_pb_heartbeat_heartbeat_proto_rawDescGZIP(), []int{0}
}

func (x *HeartBeatReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *HeartBeatReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *HeartBeatReq) GetRoomID() string {
	if x != nil {
		return x.RoomID
	}
	return ""
}

func (x *HeartBeatReq) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

func (x *HeartBeatReq) GetRtt() uint32 {
	if x != nil {
		return x.Rtt
	}
	return 0
}

type HeartBeatRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IntervalTs uint64 `protobuf:"varint,1,opt,name=intervalTs,proto3" json:"intervalTs,omitempty"` //下次心跳间隔时间 s级
	BizUseMs   uint64 `protobuf:"varint,2,opt,name=bizUseMs,proto3" json:"bizUseMs,omitempty"`     //业务逻辑处理时间 ms级
}

func (x *HeartBeatRsp) Reset() {
	*x = HeartBeatRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_heartbeat_heartbeat_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeartBeatRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartBeatRsp) ProtoMessage() {}

func (x *HeartBeatRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_heartbeat_heartbeat_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartBeatRsp.ProtoReflect.Descriptor instead.
func (*HeartBeatRsp) Descriptor() ([]byte, []int) {
	return file_pb_heartbeat_heartbeat_proto_rawDescGZIP(), []int{1}
}

func (x *HeartBeatRsp) GetIntervalTs() uint64 {
	if x != nil {
		return x.IntervalTs
	}
	return 0
}

func (x *HeartBeatRsp) GetBizUseMs() uint64 {
	if x != nil {
		return x.BizUseMs
	}
	return 0
}

var File_pb_heartbeat_heartbeat_proto protoreflect.FileDescriptor

var file_pb_heartbeat_heartbeat_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x2f, 0x68,
	0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04,
	0x67, 0x61, 0x6d, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65,
	0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x44, 0x12, 0x36, 0x0a, 0x06, 0x6d,
	0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x2e,
	0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x74, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x03, 0x72, 0x74, 0x74, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x4a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x54, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x55, 0x73, 0x65, 0x4d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x62, 0x69, 0x7a, 0x55, 0x73, 0x65, 0x4d, 0x73, 0x32, 0x43, 0x0a, 0x0c,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x53, 0x76, 0x72, 0x12, 0x33, 0x0a, 0x09,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x12, 0x12, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x48, 0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x52, 0x73,
	0x70, 0x42, 0x41, 0x5a, 0x3f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x68, 0x65, 0x61, 0x72, 0x74,
	0x62, 0x65, 0x61, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_heartbeat_heartbeat_proto_rawDescOnce sync.Once
	file_pb_heartbeat_heartbeat_proto_rawDescData = file_pb_heartbeat_heartbeat_proto_rawDesc
)

func file_pb_heartbeat_heartbeat_proto_rawDescGZIP() []byte {
	file_pb_heartbeat_heartbeat_proto_rawDescOnce.Do(func() {
		file_pb_heartbeat_heartbeat_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_heartbeat_heartbeat_proto_rawDescData)
	})
	return file_pb_heartbeat_heartbeat_proto_rawDescData
}

var file_pb_heartbeat_heartbeat_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pb_heartbeat_heartbeat_proto_goTypes = []interface{}{
	(*HeartBeatReq)(nil), // 0: game.HeartBeatReq
	(*HeartBeatRsp)(nil), // 1: game.HeartBeatRsp
	nil,                  // 2: game.HeartBeatReq.MapExtEntry
}
var file_pb_heartbeat_heartbeat_proto_depIdxs = []int32{
	2, // 0: game.HeartBeatReq.mapExt:type_name -> game.HeartBeatReq.MapExtEntry
	0, // 1: game.HeartBeatSvr.HeartBeat:input_type -> game.HeartBeatReq
	1, // 2: game.HeartBeatSvr.HeartBeat:output_type -> game.HeartBeatRsp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_heartbeat_heartbeat_proto_init() }
func file_pb_heartbeat_heartbeat_proto_init() {
	if File_pb_heartbeat_heartbeat_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_heartbeat_heartbeat_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartBeatReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_heartbeat_heartbeat_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeartBeatRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_heartbeat_heartbeat_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_heartbeat_heartbeat_proto_goTypes,
		DependencyIndexes: file_pb_heartbeat_heartbeat_proto_depIdxs,
		MessageInfos:      file_pb_heartbeat_heartbeat_proto_msgTypes,
	}.Build()
	File_pb_heartbeat_heartbeat_proto = out.File
	file_pb_heartbeat_heartbeat_proto_rawDesc = nil
	file_pb_heartbeat_heartbeat_proto_goTypes = nil
	file_pb_heartbeat_heartbeat_proto_depIdxs = nil
}
