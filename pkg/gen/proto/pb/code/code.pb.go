// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/code/code.proto

package code

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Code int32

const (
	// 标准错误码
	Code_OK                 Code = 0
	Code_Canceled           Code = 1
	Code_Unknown            Code = 2
	Code_InvalidArgument    Code = 3
	Code_DeadlineExceeded   Code = 4
	Code_NotFound           Code = 5
	Code_AlreadyExists      Code = 6
	Code_PermissionDenied   Code = 7
	Code_ResourceExhausted  Code = 8
	Code_FailedPrecondition Code = 9
	Code_Aborted            Code = 10
	Code_OutOfRange         Code = 11
	Code_Unimplemented      Code = 12
	Code_Internal           Code = 13
	Code_Unavailable        Code = 14
	Code_DataLoss           Code = 15
	Code_Unauthenticated    Code = 16
	// 推币机相关，从国际版仓库拷过来的
	Code_CoinDozerNotOnline                        Code = 11000 // 推币机不在线
	Code_CoinDozerHeartBeatNotExist                Code = 11001 // 推币机心跳不存在
	Code_CoinDozerNotPlaying                       Code = 11002 // 推币机不在游戏中
	Code_CoinDozerRoundHadEnd                      Code = 11003 // 推币机轮次已结束
	Code_AssetNotEnough                            Code = 12001 // 资产数量不足
	Code_AssetDuplicateTransaction                 Code = 12002 // 事务重复
	Code_BalanceNotEnough                          Code = 12003 // 余额不足, K币不足，饭票不足
	Code_NeedToRelogin                             Code = 12004 // 需要触发重新登录
	Code_ChildConsumeLimit                         Code = 12005 // 触发未成年人消费限制
	Code_AdultConsumeLimit                         Code = 12006 // 触发成年人消费限制
	Code_RealNameVerificationRequired              Code = 12007 // 需要实名验证--强阻断
	Code_AssetUpdateConflict                       Code = 12008 // 更新冲突
	Code_AdultConsumeDayRemind                     Code = 12009 // 成年人日消费提醒（需手动确认）
	Code_AdultConsumeMonthRemind                   Code = 12010 // 成年人月消费提醒（需手动确认）
	Code_AssetNotExists                            Code = 12011 // 资产ID不存在
	Code_RealNameVerificationWeekRequired          Code = 12012 // 需要实名验证--弱阻断
	Code_AntiIndulgedConsumeLimit                  Code = 12013 //防沉迷消费限额
	Code_AntiIndulgedWealfareLimit                 Code = 12014 //防沉迷财富等级限制
	Code_ShipmentNeedsDelay                        Code = 12015 // 发货需要推迟，需要重试
	Code_OrderAlreadyExists                        Code = 13000 // 订单已存在
	Code_OrderHasNotBeenPaid                       Code = 13001 // 订单未支付
	Code_OrderNotExist                             Code = 13002 // 订单不存在
	Code_TimerAlreadyExists                        Code = 14000 // 定时器已存在
	Code_TimerUpdateConflict                       Code = 14001 // 定时器更新冲突
	Code_BillDuplicate                             Code = 15000 //订单重复
	Code_TxmsgNotExists                            Code = 16000 // 消息不存在
	Code_TxmsgNeedRetry                            Code = 16001 // 需要重试
	Code_TxmsgUncommitted                          Code = 16002 // 未提交
	Code_TxmsgInvokeError                          Code = 16003 // 调用错误
	Code_InteractiveGameRoomExists                 Code = 17000 // 房间已存在
	Code_InteractiveGameAssetNotEnough             Code = 17001 // 余额不足
	Code_InteractiveGameNeedAuth                   Code = 17002 // 需要认证
	Code_InteractiveGameFlowerNoEnough             Code = 17003 // 鲜花不足
	Code_InteractiveGameCertInfoInvalid            Code = 17004 // 认证信息无效
	Code_InteractiveGameRoomRoundCreated           Code = 17005 // 房间场次已创建
	Code_InteractiveGameRoomRoundConflict          Code = 17006 // 房间场次冲突
	Code_InteractiveGameRoomPlaying                Code = 17007 // 游戏进行中
	Code_InteractiveGameSettlementNotExist         Code = 17008 // 未找到结算数据
	Code_InteractiveGameUpdateConflict             Code = 17009 // 更新冲突
	Code_InteractiveGameMaxNumberOfPlayers         Code = 17010 // 满员
	Code_InteractiveGameCanceled                   Code = 17011 // 上游取消
	Code_InteractiveGameSwitchRoomDeny             Code = 17012 // 拒绝创建房间
	Code_InteractiveGamePlayerExists               Code = 17013 // 玩家已存在
	Code_InteractiveGameIndexLocked                Code = 17014 // 座位占用中
	Code_InteractiveGameForbidden                  Code = 17015 // 阻断
	Code_InteractiveGameAlternateAssetEnough       Code = 17016
	Code_InteractiveGameTradingInsufficientBalance Code = 17017 // 余额不足
	Code_InteractiveGameTradingMixedPayment        Code = 17018 // 余额不足可兑换
	Code_InteractiveGamePrivate                    Code = 17019 // 房间私密, 弹窗提示
	Code_InteractiveGamePrivate2PublicFail         Code = 17020 // 私密转公开失败
	Code_InteractiveGameRoomStatusNoPending        Code = 17021 // 非组局状态
	Code_InteractiveGameGetRoomError               Code = 17022 // 获取房间信息失败
	Code_InteractiveGameAppMismatch                Code = 17023 // appId 不一致
	Code_InteractiveGameEmptyRoomId                Code = 17024 // roomId 为空
	Code_InteractiveGameOwnerMustJoin              Code = 17025 // 房主不能离开
	Code_AdapterSendMailLimit                      Code = 18000 // 私信 触发频控限制
	Code_AdapterSendMailTurnedOff                  Code = 18001 // 私信 功能关闭
	Code_PackageBegin                              Code = 19000 // 礼包
	Code_PackageExclusiveMusicSVIP                 Code = 19001 // 仅music svip购买
	Code_PackageEnd                                Code = 19100 // 礼包
	Code_OpenBegin                                 Code = 21000 // 开放平台错误码定义
	Code_OpenInvalidAuthCode                       Code = 21001 // 授权码无效
	Code_OpenExpired                               Code = 21002 // 过期
	Code_OpenSignErr                               Code = 21003 // 签名错误
	Code_OpenNoSign                                Code = 21004 // 未带签名
	Code_OpenUnknownScope                          Code = 21005 // 未知授权范围
	Code_OpenUnknownGrantType                      Code = 21006 // 未知授权类型
	Code_OpenAppNotFound                           Code = 21007 // 应用id不存在
	Code_OpenReplicateApp                          Code = 21008 // 致命错误，应用重复，一般不会发生
	Code_OpenSQLQueryFailed                        Code = 21009 // 存储获取失败
	Code_OpenNoToken                               Code = 21010 // token不存在
	Code_OpenUnsupported                           Code = 21011 // 暂不支持的操作
	Code_OpenSvrBusy                               Code = 21012 // busy
	Code_OpenInvalidTime                           Code = 21013 // 时间校验不通过
	Code_OpenInvalidPlat                           Code = 21014 // 平台id无效
	Code_OpenEnd                                   Code = 21500 // 开放平台错误码定义over
	Code_PetPkBegin                                Code = 22000 // 宠物PK-开始
	Code_PetPkAddHpStageShifted                    Code = 22001 // 排期/阶段变化, 导致不能加血
	Code_PetPkEnd                                  Code = 22100 // 宠物PK-结束
	Code_LuckyDrawBegin                            Code = 23000 // 抽奖-开始
	Code_LuckyDrawNeedSafeVerify                   Code = 23001 // 需要安全验证，读取rsp中url
	Code_LuckyDrawTicketNotEnough                  Code = 23002 // 抽奖券不足
	Code_LuckyDrawAsyncLottery                     Code = 23003 // 抽奖进行中，稍后留意抽奖结果
	Code_LuckyDrawNotFound                         Code = 23004 // 活动不存在
	Code_LuckyDrawEnd                              Code = 23100 // 抽奖-结束
	// 业务公共错误码
	Code_COMMBegin                    Code = 30000
	Code_COMMServerErr                Code = 30001 // 服务错误
	Code_COMMParamInvalid             Code = 30002 // 参数错误
	Code_COMMNotFound                 Code = 30003 // 未发现
	Code_COMMNotEnough                Code = 30004 // 余额不足
	Code_COMMRepeatedRequest          Code = 30005 // 重复请求(已成功)
	Code_COMMConfigErr                Code = 30006 // 配置错误
	Code_COMMAlreadyFinished          Code = 30007 // 已经完成
	Code_COMMVerifyErr                Code = 30008 // 校验失败
	Code_COMMTicketNotEnough          Code = 30009 // 次数不足
	Code_COMMUserIsDarkIndustryBanned Code = 30010 // 用户是黑产
	Code_COMMAdNotExposed             Code = 30011 // 广告未曝光
	Code_COMMAdNotAchieved            Code = 30012 // 广告未达成
	Code_COMMEnd                      Code = 31000
)

// Enum value maps for Code.
var (
	Code_name = map[int32]string{
		0:     "OK",
		1:     "Canceled",
		2:     "Unknown",
		3:     "InvalidArgument",
		4:     "DeadlineExceeded",
		5:     "NotFound",
		6:     "AlreadyExists",
		7:     "PermissionDenied",
		8:     "ResourceExhausted",
		9:     "FailedPrecondition",
		10:    "Aborted",
		11:    "OutOfRange",
		12:    "Unimplemented",
		13:    "Internal",
		14:    "Unavailable",
		15:    "DataLoss",
		16:    "Unauthenticated",
		11000: "CoinDozerNotOnline",
		11001: "CoinDozerHeartBeatNotExist",
		11002: "CoinDozerNotPlaying",
		11003: "CoinDozerRoundHadEnd",
		12001: "AssetNotEnough",
		12002: "AssetDuplicateTransaction",
		12003: "BalanceNotEnough",
		12004: "NeedToRelogin",
		12005: "ChildConsumeLimit",
		12006: "AdultConsumeLimit",
		12007: "RealNameVerificationRequired",
		12008: "AssetUpdateConflict",
		12009: "AdultConsumeDayRemind",
		12010: "AdultConsumeMonthRemind",
		12011: "AssetNotExists",
		12012: "RealNameVerificationWeekRequired",
		12013: "AntiIndulgedConsumeLimit",
		12014: "AntiIndulgedWealfareLimit",
		12015: "ShipmentNeedsDelay",
		13000: "OrderAlreadyExists",
		13001: "OrderHasNotBeenPaid",
		13002: "OrderNotExist",
		14000: "TimerAlreadyExists",
		14001: "TimerUpdateConflict",
		15000: "BillDuplicate",
		16000: "TxmsgNotExists",
		16001: "TxmsgNeedRetry",
		16002: "TxmsgUncommitted",
		16003: "TxmsgInvokeError",
		17000: "InteractiveGameRoomExists",
		17001: "InteractiveGameAssetNotEnough",
		17002: "InteractiveGameNeedAuth",
		17003: "InteractiveGameFlowerNoEnough",
		17004: "InteractiveGameCertInfoInvalid",
		17005: "InteractiveGameRoomRoundCreated",
		17006: "InteractiveGameRoomRoundConflict",
		17007: "InteractiveGameRoomPlaying",
		17008: "InteractiveGameSettlementNotExist",
		17009: "InteractiveGameUpdateConflict",
		17010: "InteractiveGameMaxNumberOfPlayers",
		17011: "InteractiveGameCanceled",
		17012: "InteractiveGameSwitchRoomDeny",
		17013: "InteractiveGamePlayerExists",
		17014: "InteractiveGameIndexLocked",
		17015: "InteractiveGameForbidden",
		17016: "InteractiveGameAlternateAssetEnough",
		17017: "InteractiveGameTradingInsufficientBalance",
		17018: "InteractiveGameTradingMixedPayment",
		17019: "InteractiveGamePrivate",
		17020: "InteractiveGamePrivate2PublicFail",
		17021: "InteractiveGameRoomStatusNoPending",
		17022: "InteractiveGameGetRoomError",
		17023: "InteractiveGameAppMismatch",
		17024: "InteractiveGameEmptyRoomId",
		17025: "InteractiveGameOwnerMustJoin",
		18000: "AdapterSendMailLimit",
		18001: "AdapterSendMailTurnedOff",
		19000: "PackageBegin",
		19001: "PackageExclusiveMusicSVIP",
		19100: "PackageEnd",
		21000: "OpenBegin",
		21001: "OpenInvalidAuthCode",
		21002: "OpenExpired",
		21003: "OpenSignErr",
		21004: "OpenNoSign",
		21005: "OpenUnknownScope",
		21006: "OpenUnknownGrantType",
		21007: "OpenAppNotFound",
		21008: "OpenReplicateApp",
		21009: "OpenSQLQueryFailed",
		21010: "OpenNoToken",
		21011: "OpenUnsupported",
		21012: "OpenSvrBusy",
		21013: "OpenInvalidTime",
		21014: "OpenInvalidPlat",
		21500: "OpenEnd",
		22000: "PetPkBegin",
		22001: "PetPkAddHpStageShifted",
		22100: "PetPkEnd",
		23000: "LuckyDrawBegin",
		23001: "LuckyDrawNeedSafeVerify",
		23002: "LuckyDrawTicketNotEnough",
		23003: "LuckyDrawAsyncLottery",
		23004: "LuckyDrawNotFound",
		23100: "LuckyDrawEnd",
		30000: "COMMBegin",
		30001: "COMMServerErr",
		30002: "COMMParamInvalid",
		30003: "COMMNotFound",
		30004: "COMMNotEnough",
		30005: "COMMRepeatedRequest",
		30006: "COMMConfigErr",
		30007: "COMMAlreadyFinished",
		30008: "COMMVerifyErr",
		30009: "COMMTicketNotEnough",
		30010: "COMMUserIsDarkIndustryBanned",
		30011: "COMMAdNotExposed",
		30012: "COMMAdNotAchieved",
		31000: "COMMEnd",
	}
	Code_value = map[string]int32{
		"OK":                                        0,
		"Canceled":                                  1,
		"Unknown":                                   2,
		"InvalidArgument":                           3,
		"DeadlineExceeded":                          4,
		"NotFound":                                  5,
		"AlreadyExists":                             6,
		"PermissionDenied":                          7,
		"ResourceExhausted":                         8,
		"FailedPrecondition":                        9,
		"Aborted":                                   10,
		"OutOfRange":                                11,
		"Unimplemented":                             12,
		"Internal":                                  13,
		"Unavailable":                               14,
		"DataLoss":                                  15,
		"Unauthenticated":                           16,
		"CoinDozerNotOnline":                        11000,
		"CoinDozerHeartBeatNotExist":                11001,
		"CoinDozerNotPlaying":                       11002,
		"CoinDozerRoundHadEnd":                      11003,
		"AssetNotEnough":                            12001,
		"AssetDuplicateTransaction":                 12002,
		"BalanceNotEnough":                          12003,
		"NeedToRelogin":                             12004,
		"ChildConsumeLimit":                         12005,
		"AdultConsumeLimit":                         12006,
		"RealNameVerificationRequired":              12007,
		"AssetUpdateConflict":                       12008,
		"AdultConsumeDayRemind":                     12009,
		"AdultConsumeMonthRemind":                   12010,
		"AssetNotExists":                            12011,
		"RealNameVerificationWeekRequired":          12012,
		"AntiIndulgedConsumeLimit":                  12013,
		"AntiIndulgedWealfareLimit":                 12014,
		"ShipmentNeedsDelay":                        12015,
		"OrderAlreadyExists":                        13000,
		"OrderHasNotBeenPaid":                       13001,
		"OrderNotExist":                             13002,
		"TimerAlreadyExists":                        14000,
		"TimerUpdateConflict":                       14001,
		"BillDuplicate":                             15000,
		"TxmsgNotExists":                            16000,
		"TxmsgNeedRetry":                            16001,
		"TxmsgUncommitted":                          16002,
		"TxmsgInvokeError":                          16003,
		"InteractiveGameRoomExists":                 17000,
		"InteractiveGameAssetNotEnough":             17001,
		"InteractiveGameNeedAuth":                   17002,
		"InteractiveGameFlowerNoEnough":             17003,
		"InteractiveGameCertInfoInvalid":            17004,
		"InteractiveGameRoomRoundCreated":           17005,
		"InteractiveGameRoomRoundConflict":          17006,
		"InteractiveGameRoomPlaying":                17007,
		"InteractiveGameSettlementNotExist":         17008,
		"InteractiveGameUpdateConflict":             17009,
		"InteractiveGameMaxNumberOfPlayers":         17010,
		"InteractiveGameCanceled":                   17011,
		"InteractiveGameSwitchRoomDeny":             17012,
		"InteractiveGamePlayerExists":               17013,
		"InteractiveGameIndexLocked":                17014,
		"InteractiveGameForbidden":                  17015,
		"InteractiveGameAlternateAssetEnough":       17016,
		"InteractiveGameTradingInsufficientBalance": 17017,
		"InteractiveGameTradingMixedPayment":        17018,
		"InteractiveGamePrivate":                    17019,
		"InteractiveGamePrivate2PublicFail":         17020,
		"InteractiveGameRoomStatusNoPending":        17021,
		"InteractiveGameGetRoomError":               17022,
		"InteractiveGameAppMismatch":                17023,
		"InteractiveGameEmptyRoomId":                17024,
		"InteractiveGameOwnerMustJoin":              17025,
		"AdapterSendMailLimit":                      18000,
		"AdapterSendMailTurnedOff":                  18001,
		"PackageBegin":                              19000,
		"PackageExclusiveMusicSVIP":                 19001,
		"PackageEnd":                                19100,
		"OpenBegin":                                 21000,
		"OpenInvalidAuthCode":                       21001,
		"OpenExpired":                               21002,
		"OpenSignErr":                               21003,
		"OpenNoSign":                                21004,
		"OpenUnknownScope":                          21005,
		"OpenUnknownGrantType":                      21006,
		"OpenAppNotFound":                           21007,
		"OpenReplicateApp":                          21008,
		"OpenSQLQueryFailed":                        21009,
		"OpenNoToken":                               21010,
		"OpenUnsupported":                           21011,
		"OpenSvrBusy":                               21012,
		"OpenInvalidTime":                           21013,
		"OpenInvalidPlat":                           21014,
		"OpenEnd":                                   21500,
		"PetPkBegin":                                22000,
		"PetPkAddHpStageShifted":                    22001,
		"PetPkEnd":                                  22100,
		"LuckyDrawBegin":                            23000,
		"LuckyDrawNeedSafeVerify":                   23001,
		"LuckyDrawTicketNotEnough":                  23002,
		"LuckyDrawAsyncLottery":                     23003,
		"LuckyDrawNotFound":                         23004,
		"LuckyDrawEnd":                              23100,
		"COMMBegin":                                 30000,
		"COMMServerErr":                             30001,
		"COMMParamInvalid":                          30002,
		"COMMNotFound":                              30003,
		"COMMNotEnough":                             30004,
		"COMMRepeatedRequest":                       30005,
		"COMMConfigErr":                             30006,
		"COMMAlreadyFinished":                       30007,
		"COMMVerifyErr":                             30008,
		"COMMTicketNotEnough":                       30009,
		"COMMUserIsDarkIndustryBanned":              30010,
		"COMMAdNotExposed":                          30011,
		"COMMAdNotAchieved":                         30012,
		"COMMEnd":                                   31000,
	}
)

func (x Code) Enum() *Code {
	p := new(Code)
	*p = x
	return p
}

func (x Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Code) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_code_code_proto_enumTypes[0].Descriptor()
}

func (Code) Type() protoreflect.EnumType {
	return &file_pb_code_code_proto_enumTypes[0]
}

func (x Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Code.Descriptor instead.
func (Code) EnumDescriptor() ([]byte, []int) {
	return file_pb_code_code_proto_rawDescGZIP(), []int{0}
}

var File_pb_code_code_proto protoreflect.FileDescriptor

var file_pb_code_code_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x64, 0x65, 0x2f, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x2a, 0xe2, 0x17, 0x0a, 0x04, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x41, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x44,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x78, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x10,
	0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x05, 0x12,
	0x11, 0x0a, 0x0d, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73,
	0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x6e, 0x69, 0x65, 0x64, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x45, 0x78, 0x68, 0x61, 0x75, 0x73, 0x74, 0x65, 0x64, 0x10, 0x08, 0x12,
	0x16, 0x0a, 0x12, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x50, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x62, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x10, 0x0a, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x75, 0x74, 0x4f, 0x66, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x6e, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x65, 0x64, 0x10, 0x0c, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x6c, 0x65, 0x10, 0x0e, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f,
	0x73, 0x73, 0x10, 0x0f, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x6e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e,
	0x74, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x10, 0x10, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x6f, 0x69,
	0x6e, 0x44, 0x6f, 0x7a, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x10,
	0xf8, 0x55, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x6f, 0x69, 0x6e, 0x44, 0x6f, 0x7a, 0x65, 0x72, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x42, 0x65, 0x61, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x10, 0xf9, 0x55, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x6f, 0x69, 0x6e, 0x44, 0x6f, 0x7a, 0x65, 0x72,
	0x4e, 0x6f, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x10, 0xfa, 0x55, 0x12, 0x19, 0x0a,
	0x14, 0x43, 0x6f, 0x69, 0x6e, 0x44, 0x6f, 0x7a, 0x65, 0x72, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x48,
	0x61, 0x64, 0x45, 0x6e, 0x64, 0x10, 0xfb, 0x55, 0x12, 0x13, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xe1, 0x5d, 0x12, 0x1e, 0x0a,
	0x19, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0xe2, 0x5d, 0x12, 0x15, 0x0a,
	0x10, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67,
	0x68, 0x10, 0xe3, 0x5d, 0x12, 0x12, 0x0a, 0x0d, 0x4e, 0x65, 0x65, 0x64, 0x54, 0x6f, 0x52, 0x65,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0xe4, 0x5d, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x68, 0x69, 0x6c,
	0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xe5, 0x5d,
	0x12, 0x16, 0x0a, 0x11, 0x41, 0x64, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xe6, 0x5d, 0x12, 0x21, 0x0a, 0x1c, 0x52, 0x65, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0xe7, 0x5d, 0x12, 0x18, 0x0a, 0x13, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69,
	0x63, 0x74, 0x10, 0xe8, 0x5d, 0x12, 0x1a, 0x0a, 0x15, 0x41, 0x64, 0x75, 0x6c, 0x74, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x44, 0x61, 0x79, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x10, 0xe9,
	0x5d, 0x12, 0x1c, 0x0a, 0x17, 0x41, 0x64, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x10, 0xea, 0x5d, 0x12,
	0x13, 0x0a, 0x0e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x73, 0x10, 0xeb, 0x5d, 0x12, 0x25, 0x0a, 0x20, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x65, 0x65, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0xec, 0x5d, 0x12, 0x1d, 0x0a, 0x18, 0x41,
	0x6e, 0x74, 0x69, 0x49, 0x6e, 0x64, 0x75, 0x6c, 0x67, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xed, 0x5d, 0x12, 0x1e, 0x0a, 0x19, 0x41, 0x6e,
	0x74, 0x69, 0x49, 0x6e, 0x64, 0x75, 0x6c, 0x67, 0x65, 0x64, 0x57, 0x65, 0x61, 0x6c, 0x66, 0x61,
	0x72, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0xee, 0x5d, 0x12, 0x17, 0x0a, 0x12, 0x53, 0x68,
	0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x65, 0x65, 0x64, 0x73, 0x44, 0x65, 0x6c, 0x61, 0x79,
	0x10, 0xef, 0x5d, 0x12, 0x17, 0x0a, 0x12, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6c, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0xc8, 0x65, 0x12, 0x18, 0x0a, 0x13,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x48, 0x61, 0x73, 0x4e, 0x6f, 0x74, 0x42, 0x65, 0x65, 0x6e, 0x50,
	0x61, 0x69, 0x64, 0x10, 0xc9, 0x65, 0x12, 0x12, 0x0a, 0x0d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4e,
	0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0xca, 0x65, 0x12, 0x17, 0x0a, 0x12, 0x54, 0x69,
	0x6d, 0x65, 0x72, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73,
	0x10, 0xb0, 0x6d, 0x12, 0x18, 0x0a, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x10, 0xb1, 0x6d, 0x12, 0x12, 0x0a,
	0x0d, 0x42, 0x69, 0x6c, 0x6c, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x10, 0x98,
	0x75, 0x12, 0x13, 0x0a, 0x0e, 0x54, 0x78, 0x6d, 0x73, 0x67, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69,
	0x73, 0x74, 0x73, 0x10, 0x80, 0x7d, 0x12, 0x13, 0x0a, 0x0e, 0x54, 0x78, 0x6d, 0x73, 0x67, 0x4e,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x74, 0x72, 0x79, 0x10, 0x81, 0x7d, 0x12, 0x15, 0x0a, 0x10, 0x54,
	0x78, 0x6d, 0x73, 0x67, 0x55, 0x6e, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x10,
	0x82, 0x7d, 0x12, 0x15, 0x0a, 0x10, 0x54, 0x78, 0x6d, 0x73, 0x67, 0x49, 0x6e, 0x76, 0x6f, 0x6b,
	0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x83, 0x7d, 0x12, 0x1f, 0x0a, 0x19, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x10, 0xe8, 0x84, 0x01, 0x12, 0x23, 0x0a, 0x1d, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xe9, 0x84, 0x01, 0x12,
	0x1d, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61,
	0x6d, 0x65, 0x4e, 0x65, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x10, 0xea, 0x84, 0x01, 0x12, 0x23,
	0x0a, 0x1d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x46, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x4e, 0x6f, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10,
	0xeb, 0x84, 0x01, 0x12, 0x24, 0x0a, 0x1e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0xec, 0x84, 0x01, 0x12, 0x25, 0x0a, 0x1f, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x10, 0xed, 0x84, 0x01,
	0x12, 0x26, 0x0a, 0x20, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x6c, 0x69, 0x63, 0x74, 0x10, 0xee, 0x84, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x50,
	0x6c, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x10, 0xef, 0x84, 0x01, 0x12, 0x27, 0x0a, 0x21, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10,
	0xf0, 0x84, 0x01, 0x12, 0x23, 0x0a, 0x1d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x6c, 0x69, 0x63, 0x74, 0x10, 0xf1, 0x84, 0x01, 0x12, 0x27, 0x0a, 0x21, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x78, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x10, 0xf2, 0x84,
	0x01, 0x12, 0x1d, 0x0a, 0x17, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0xf3, 0x84, 0x01,
	0x12, 0x23, 0x0a, 0x1d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x6f, 0x6d, 0x44, 0x65, 0x6e,
	0x79, 0x10, 0xf4, 0x84, 0x01, 0x12, 0x21, 0x0a, 0x1b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x73, 0x10, 0xf5, 0x84, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x10, 0xf6, 0x84, 0x01, 0x12, 0x1e, 0x0a, 0x18, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x46, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0xf7, 0x84, 0x01, 0x12, 0x29, 0x0a, 0x23, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x6c, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67,
	0x68, 0x10, 0xf8, 0x84, 0x01, 0x12, 0x2f, 0x0a, 0x29, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x10, 0xf9, 0x84, 0x01, 0x12, 0x28, 0x0a, 0x22, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x54, 0x72, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x4d, 0x69, 0x78, 0x65, 0x64, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x10, 0xfa, 0x84, 0x01,
	0x12, 0x1c, 0x0a, 0x16, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47,
	0x61, 0x6d, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x10, 0xfb, 0x84, 0x01, 0x12, 0x27,
	0x0a, 0x21, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d,
	0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x32, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x46,
	0x61, 0x69, 0x6c, 0x10, 0xfc, 0x84, 0x01, 0x12, 0x28, 0x0a, 0x22, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x4e, 0x6f, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0xfd, 0x84,
	0x01, 0x12, 0x21, 0x0a, 0x1b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x47, 0x61, 0x6d, 0x65, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x10, 0xfe, 0x84, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x70, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x10, 0xff, 0x84, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x6f,
	0x6f, 0x6d, 0x49, 0x64, 0x10, 0x80, 0x85, 0x01, 0x12, 0x22, 0x0a, 0x1c, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x4d, 0x75, 0x73, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x81, 0x85, 0x01, 0x12, 0x1a, 0x0a, 0x14,
	0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x10, 0xd0, 0x8c, 0x01, 0x12, 0x1e, 0x0a, 0x18, 0x41, 0x64, 0x61, 0x70,
	0x74, 0x65, 0x72, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x69, 0x6c, 0x54, 0x75, 0x72, 0x6e, 0x65,
	0x64, 0x4f, 0x66, 0x66, 0x10, 0xd1, 0x8c, 0x01, 0x12, 0x12, 0x0a, 0x0c, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x10, 0xb8, 0x94, 0x01, 0x12, 0x1f, 0x0a, 0x19,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x76, 0x65,
	0x4d, 0x75, 0x73, 0x69, 0x63, 0x53, 0x56, 0x49, 0x50, 0x10, 0xb9, 0x94, 0x01, 0x12, 0x10, 0x0a,
	0x0a, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x64, 0x10, 0x9c, 0x95, 0x01, 0x12,
	0x0f, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x6e, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x10, 0x88, 0xa4, 0x01,
	0x12, 0x19, 0x0a, 0x13, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x41,
	0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x10, 0x89, 0xa4, 0x01, 0x12, 0x11, 0x0a, 0x0b, 0x4f,
	0x70, 0x65, 0x6e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10, 0x8a, 0xa4, 0x01, 0x12, 0x11,
	0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x45, 0x72, 0x72, 0x10, 0x8b, 0xa4,
	0x01, 0x12, 0x10, 0x0a, 0x0a, 0x4f, 0x70, 0x65, 0x6e, 0x4e, 0x6f, 0x53, 0x69, 0x67, 0x6e, 0x10,
	0x8c, 0xa4, 0x01, 0x12, 0x16, 0x0a, 0x10, 0x4f, 0x70, 0x65, 0x6e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x10, 0x8d, 0xa4, 0x01, 0x12, 0x1a, 0x0a, 0x14, 0x4f,
	0x70, 0x65, 0x6e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x10, 0x8e, 0xa4, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x41,
	0x70, 0x70, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0x8f, 0xa4, 0x01, 0x12, 0x16,
	0x0a, 0x10, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x10, 0x90, 0xa4, 0x01, 0x12, 0x18, 0x0a, 0x12, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x51,
	0x4c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x91, 0xa4, 0x01,
	0x12, 0x11, 0x0a, 0x0b, 0x4f, 0x70, 0x65, 0x6e, 0x4e, 0x6f, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x10,
	0x92, 0xa4, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x55, 0x6e, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x10, 0x93, 0xa4, 0x01, 0x12, 0x11, 0x0a, 0x0b, 0x4f, 0x70,
	0x65, 0x6e, 0x53, 0x76, 0x72, 0x42, 0x75, 0x73, 0x79, 0x10, 0x94, 0xa4, 0x01, 0x12, 0x15, 0x0a,
	0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x10, 0x95, 0xa4, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x50, 0x6c, 0x61, 0x74, 0x10, 0x96, 0xa4, 0x01, 0x12, 0x0d, 0x0a, 0x07, 0x4f,
	0x70, 0x65, 0x6e, 0x45, 0x6e, 0x64, 0x10, 0xfc, 0xa7, 0x01, 0x12, 0x10, 0x0a, 0x0a, 0x50, 0x65,
	0x74, 0x50, 0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x10, 0xf0, 0xab, 0x01, 0x12, 0x1c, 0x0a, 0x16,
	0x50, 0x65, 0x74, 0x50, 0x6b, 0x41, 0x64, 0x64, 0x48, 0x70, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53,
	0x68, 0x69, 0x66, 0x74, 0x65, 0x64, 0x10, 0xf1, 0xab, 0x01, 0x12, 0x0e, 0x0a, 0x08, 0x50, 0x65,
	0x74, 0x50, 0x6b, 0x45, 0x6e, 0x64, 0x10, 0xd4, 0xac, 0x01, 0x12, 0x14, 0x0a, 0x0e, 0x4c, 0x75,
	0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x10, 0xd8, 0xb3, 0x01,
	0x12, 0x1d, 0x0a, 0x17, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x4e, 0x65, 0x65,
	0x64, 0x53, 0x61, 0x66, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x10, 0xd9, 0xb3, 0x01, 0x12,
	0x1e, 0x0a, 0x18, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xda, 0xb3, 0x01, 0x12,
	0x1b, 0x0a, 0x15, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x41, 0x73, 0x79, 0x6e,
	0x63, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x10, 0xdb, 0xb3, 0x01, 0x12, 0x17, 0x0a, 0x11,
	0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e,
	0x64, 0x10, 0xdc, 0xb3, 0x01, 0x12, 0x12, 0x0a, 0x0c, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72,
	0x61, 0x77, 0x45, 0x6e, 0x64, 0x10, 0xbc, 0xb4, 0x01, 0x12, 0x0f, 0x0a, 0x09, 0x43, 0x4f, 0x4d,
	0x4d, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x10, 0xb0, 0xea, 0x01, 0x12, 0x13, 0x0a, 0x0d, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x45, 0x72, 0x72, 0x10, 0xb1, 0xea, 0x01, 0x12,
	0x16, 0x0a, 0x10, 0x43, 0x4f, 0x4d, 0x4d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x10, 0xb2, 0xea, 0x01, 0x12, 0x12, 0x0a, 0x0c, 0x43, 0x4f, 0x4d, 0x4d, 0x4e,
	0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xb3, 0xea, 0x01, 0x12, 0x13, 0x0a, 0x0d, 0x43,
	0x4f, 0x4d, 0x4d, 0x4e, 0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xb4, 0xea, 0x01,
	0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x4d, 0x4d, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x10, 0xb5, 0xea, 0x01, 0x12, 0x13, 0x0a, 0x0d, 0x43,
	0x4f, 0x4d, 0x4d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x72, 0x72, 0x10, 0xb6, 0xea, 0x01,
	0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0xb7, 0xea, 0x01, 0x12, 0x13, 0x0a, 0x0d, 0x43,
	0x4f, 0x4d, 0x4d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x45, 0x72, 0x72, 0x10, 0xb8, 0xea, 0x01,
	0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x4d, 0x4d, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x6f,
	0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x10, 0xb9, 0xea, 0x01, 0x12, 0x22, 0x0a, 0x1c, 0x43,
	0x4f, 0x4d, 0x4d, 0x55, 0x73, 0x65, 0x72, 0x49, 0x73, 0x44, 0x61, 0x72, 0x6b, 0x49, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x64, 0x10, 0xba, 0xea, 0x01, 0x12,
	0x16, 0x0a, 0x10, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x64, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x70, 0x6f,
	0x73, 0x65, 0x64, 0x10, 0xbb, 0xea, 0x01, 0x12, 0x17, 0x0a, 0x11, 0x43, 0x4f, 0x4d, 0x4d, 0x41,
	0x64, 0x4e, 0x6f, 0x74, 0x41, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x64, 0x10, 0xbc, 0xea, 0x01,
	0x12, 0x0d, 0x0a, 0x07, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x6e, 0x64, 0x10, 0x98, 0xf2, 0x01, 0x42,
	0x3c, 0x5a, 0x3a, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x6f, 0x64, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_code_code_proto_rawDescOnce sync.Once
	file_pb_code_code_proto_rawDescData = file_pb_code_code_proto_rawDesc
)

func file_pb_code_code_proto_rawDescGZIP() []byte {
	file_pb_code_code_proto_rawDescOnce.Do(func() {
		file_pb_code_code_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_code_code_proto_rawDescData)
	})
	return file_pb_code_code_proto_rawDescData
}

var file_pb_code_code_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_code_code_proto_goTypes = []interface{}{
	(Code)(0), // 0: code.Code
}
var file_pb_code_code_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_code_code_proto_init() }
func file_pb_code_code_proto_init() {
	if File_pb_code_code_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_code_code_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_code_code_proto_goTypes,
		DependencyIndexes: file_pb_code_code_proto_depIdxs,
		EnumInfos:         file_pb_code_code_proto_enumTypes,
	}.Build()
	File_pb_code_code_proto = out.File
	file_pb_code_code_proto_rawDesc = nil
	file_pb_code_code_proto_goTypes = nil
	file_pb_code_code_proto_depIdxs = nil
}
