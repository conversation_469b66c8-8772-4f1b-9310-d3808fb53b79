// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_breakout_activity/webapi/webapi.proto

package webapi

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	entry "kugou_adapter_service/pkg/gen/proto/pb/cwsx_activity/entry"
	device "kugou_adapter_service/pkg/gen/proto/pb/device"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
	common "kugou_adapter_service/pkg/gen/proto/pb/game_breakout_activity/common"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReadActivityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ActivityId int32  `protobuf:"varint,3,opt,name=activityId,proto3" json:"activityId,omitempty"`
}

func (x *ReadActivityReq) Reset() {
	*x = ReadActivityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadActivityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadActivityReq) ProtoMessage() {}

func (x *ReadActivityReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadActivityReq.ProtoReflect.Descriptor instead.
func (*ReadActivityReq) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{0}
}

func (x *ReadActivityReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReadActivityReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReadActivityReq) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

type ReadActivityRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReadActivityRsp) Reset() {
	*x = ReadActivityRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadActivityRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadActivityRsp) ProtoMessage() {}

func (x *ReadActivityRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadActivityRsp.ProtoReflect.Descriptor instead.
func (*ReadActivityRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{1}
}

// LadderStatus 阶梯状态
type LadderStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LadderId     int32                   `protobuf:"varint,1,opt,name=ladderId,proto3" json:"ladderId,omitempty"`                                                      // 梯度id
	AchieveState common.UserAchieveState `protobuf:"varint,2,opt,name=achieveState,proto3,enum=game_breakout_activity.UserAchieveState" json:"achieveState,omitempty"` // 达成状态
	ClaimState   common.UserClaimState   `protobuf:"varint,3,opt,name=claimState,proto3,enum=game_breakout_activity.UserClaimState" json:"claimState,omitempty"`       // 领取状态
	AchieveTime  uint32                  `protobuf:"varint,4,opt,name=achieveTime,proto3" json:"achieveTime,omitempty"`                                                // 达成时间
	ClaimTime    uint32                  `protobuf:"varint,5,opt,name=claimTime,proto3" json:"claimTime,omitempty"`                                                    // 领取时间
}

func (x *LadderStatus) Reset() {
	*x = LadderStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LadderStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LadderStatus) ProtoMessage() {}

func (x *LadderStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LadderStatus.ProtoReflect.Descriptor instead.
func (*LadderStatus) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{2}
}

func (x *LadderStatus) GetLadderId() int32 {
	if x != nil {
		return x.LadderId
	}
	return 0
}

func (x *LadderStatus) GetAchieveState() common.UserAchieveState {
	if x != nil {
		return x.AchieveState
	}
	return common.UserAchieveState(0)
}

func (x *LadderStatus) GetClaimState() common.UserClaimState {
	if x != nil {
		return x.ClaimState
	}
	return common.UserClaimState(0)
}

func (x *LadderStatus) GetAchieveTime() uint32 {
	if x != nil {
		return x.AchieveTime
	}
	return 0
}

func (x *LadderStatus) GetClaimTime() uint32 {
	if x != nil {
		return x.ClaimTime
	}
	return 0
}

// Progress 进度
type Progress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurProgress   int32 `protobuf:"varint,1,opt,name=curProgress,proto3" json:"curProgress,omitempty"`     // 当前进度
	TotalProgress int32 `protobuf:"varint,2,opt,name=totalProgress,proto3" json:"totalProgress,omitempty"` // 总进度
}

func (x *Progress) Reset() {
	*x = Progress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Progress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Progress) ProtoMessage() {}

func (x *Progress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Progress.ProtoReflect.Descriptor instead.
func (*Progress) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{3}
}

func (x *Progress) GetCurProgress() int32 {
	if x != nil {
		return x.CurProgress
	}
	return 0
}

func (x *Progress) GetTotalProgress() int32 {
	if x != nil {
		return x.TotalProgress
	}
	return 0
}

// ActivityStatus 活动参与状态
type ActivityStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId     int32                    `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`                                       // 活动id
	StartTime      uint32                   `protobuf:"varint,2,opt,name=startTime,proto3" json:"startTime,omitempty"`                                         // 参与时间
	RemainSec      uint32                   `protobuf:"varint,3,opt,name=remainSec,proto3" json:"remainSec,omitempty"`                                         // 剩余时间
	ActProgress    *Progress                `protobuf:"bytes,4,opt,name=actProgress,proto3" json:"actProgress,omitempty"`                                      // 总活动进度
	LadderProgress *Progress                `protobuf:"bytes,5,opt,name=ladderProgress,proto3" json:"ladderProgress,omitempty"`                                // 当前阶梯进度
	CanClaimNum    int32                    `protobuf:"varint,6,opt,name=canClaimNum,proto3" json:"canClaimNum,omitempty"`                                     // 待领取的奖励数量
	Status         common.UserActivityState `protobuf:"varint,7,opt,name=status,proto3,enum=game_breakout_activity.UserActivityState" json:"status,omitempty"` // 参与状态
	LadderStatus   []*LadderStatus          `protobuf:"bytes,8,rep,name=ladderStatus,proto3" json:"ladderStatus,omitempty"`                                    // 阶梯状态
	HasRead        uint32                   `protobuf:"varint,9,opt,name=hasRead,proto3" json:"hasRead,omitempty"`                                             // 是否已读 0 未读 1 已读
	DoneTime       uint32                   `protobuf:"varint,10,opt,name=doneTime,proto3" json:"doneTime,omitempty"`                                          // 完成的时间
}

func (x *ActivityStatus) Reset() {
	*x = ActivityStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityStatus) ProtoMessage() {}

func (x *ActivityStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityStatus.ProtoReflect.Descriptor instead.
func (*ActivityStatus) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{4}
}

func (x *ActivityStatus) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *ActivityStatus) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ActivityStatus) GetRemainSec() uint32 {
	if x != nil {
		return x.RemainSec
	}
	return 0
}

func (x *ActivityStatus) GetActProgress() *Progress {
	if x != nil {
		return x.ActProgress
	}
	return nil
}

func (x *ActivityStatus) GetLadderProgress() *Progress {
	if x != nil {
		return x.LadderProgress
	}
	return nil
}

func (x *ActivityStatus) GetCanClaimNum() int32 {
	if x != nil {
		return x.CanClaimNum
	}
	return 0
}

func (x *ActivityStatus) GetStatus() common.UserActivityState {
	if x != nil {
		return x.Status
	}
	return common.UserActivityState(0)
}

func (x *ActivityStatus) GetLadderStatus() []*LadderStatus {
	if x != nil {
		return x.LadderStatus
	}
	return nil
}

func (x *ActivityStatus) GetHasRead() uint32 {
	if x != nil {
		return x.HasRead
	}
	return 0
}

func (x *ActivityStatus) GetDoneTime() uint32 {
	if x != nil {
		return x.DoneTime
	}
	return 0
}

// UserAchieveItem 用户达成状态
type UserAchieveItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityStatus *ActivityStatus `protobuf:"bytes,1,opt,name=activityStatus,proto3" json:"activityStatus,omitempty"` // 整体活动状态
	LadderStatus   []*LadderStatus `protobuf:"bytes,2,rep,name=ladderStatus,proto3" json:"ladderStatus,omitempty"`     // 用户梯度状态
}

func (x *UserAchieveItem) Reset() {
	*x = UserAchieveItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAchieveItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAchieveItem) ProtoMessage() {}

func (x *UserAchieveItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAchieveItem.ProtoReflect.Descriptor instead.
func (*UserAchieveItem) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{5}
}

func (x *UserAchieveItem) GetActivityStatus() *ActivityStatus {
	if x != nil {
		return x.ActivityStatus
	}
	return nil
}

func (x *UserAchieveItem) GetLadderStatus() []*LadderStatus {
	if x != nil {
		return x.LadderStatus
	}
	return nil
}

type Activity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *ActivityStatus          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`   // 用户状态
	Config  *Activity_ActivityConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`   // 配置
	RoundId string                   `protobuf:"bytes,3,opt,name=roundId,proto3" json:"roundId,omitempty"` // 轮次Id
}

func (x *Activity) Reset() {
	*x = Activity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Activity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity) ProtoMessage() {}

func (x *Activity) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity.ProtoReflect.Descriptor instead.
func (*Activity) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{6}
}

func (x *Activity) GetStatus() *ActivityStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *Activity) GetConfig() *Activity_ActivityConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *Activity) GetRoundId() string {
	if x != nil {
		return x.RoundId
	}
	return ""
}

// QueryListReq 查询活动列表
type QueryListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId  string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	StageId uint32 `protobuf:"varint,3,opt,name=stageId,proto3" json:"stageId,omitempty"` // 用户当前关卡Id
}

func (x *QueryListReq) Reset() {
	*x = QueryListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryListReq) ProtoMessage() {}

func (x *QueryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryListReq.ProtoReflect.Descriptor instead.
func (*QueryListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{7}
}

func (x *QueryListReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryListReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *QueryListReq) GetStageId() uint32 {
	if x != nil {
		return x.StageId
	}
	return 0
}

// QueryListRsp 查询活动列表
type QueryListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Activities []*Activity `protobuf:"bytes,1,rep,name=activities,proto3" json:"activities,omitempty"`
}

func (x *QueryListRsp) Reset() {
	*x = QueryListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryListRsp) ProtoMessage() {}

func (x *QueryListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryListRsp.ProtoReflect.Descriptor instead.
func (*QueryListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{8}
}

func (x *QueryListRsp) GetActivities() []*Activity {
	if x != nil {
		return x.Activities
	}
	return nil
}

// ClaimAchieveReq 领取奖励
type ClaimAchieveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string         `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string         `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ActivityId int32          `protobuf:"varint,3,opt,name=activityId,proto3" json:"activityId,omitempty"`
	LadderId   int32          `protobuf:"varint,4,opt,name=ladderId,proto3" json:"ladderId,omitempty"` // 填-1则默认领取所有可领取的
	Device     *device.Device `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *ClaimAchieveReq) Reset() {
	*x = ClaimAchieveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimAchieveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimAchieveReq) ProtoMessage() {}

func (x *ClaimAchieveReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimAchieveReq.ProtoReflect.Descriptor instead.
func (*ClaimAchieveReq) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{9}
}

func (x *ClaimAchieveReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ClaimAchieveReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ClaimAchieveReq) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *ClaimAchieveReq) GetLadderId() int32 {
	if x != nil {
		return x.LadderId
	}
	return 0
}

func (x *ClaimAchieveReq) GetDevice() *device.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

// ClaimAchieveRsp 根据gRPC status code判断结果
type ClaimAchieveRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LadderIds []int32                  `protobuf:"varint,1,rep,packed,name=ladderIds,proto3" json:"ladderIds,omitempty"` // 已领取的ladder Id列表
	Cards     []*game_api.TreasureCard `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"`                 // 宝藏卡信息, 如果产生宝藏卡领取, 则放在这里
	Rewards   []*game_api.RewardItem   `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`             // 奖励信息
}

func (x *ClaimAchieveRsp) Reset() {
	*x = ClaimAchieveRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimAchieveRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimAchieveRsp) ProtoMessage() {}

func (x *ClaimAchieveRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimAchieveRsp.ProtoReflect.Descriptor instead.
func (*ClaimAchieveRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{10}
}

func (x *ClaimAchieveRsp) GetLadderIds() []int32 {
	if x != nil {
		return x.LadderIds
	}
	return nil
}

func (x *ClaimAchieveRsp) GetCards() []*game_api.TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

func (x *ClaimAchieveRsp) GetRewards() []*game_api.RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type EntryExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Activity      *Activity                 `protobuf:"bytes,1,opt,name=activity,proto3" json:"activity,omitempty"`
	StageProgress []*EntryExt_StageProgress `protobuf:"bytes,2,rep,name=stageProgress,proto3" json:"stageProgress,omitempty"` // 关卡类型-->完成关卡能得到的进度
}

func (x *EntryExt) Reset() {
	*x = EntryExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntryExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntryExt) ProtoMessage() {}

func (x *EntryExt) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntryExt.ProtoReflect.Descriptor instead.
func (*EntryExt) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{11}
}

func (x *EntryExt) GetActivity() *Activity {
	if x != nil {
		return x.Activity
	}
	return nil
}

func (x *EntryExt) GetStageProgress() []*EntryExt_StageProgress {
	if x != nil {
		return x.StageProgress
	}
	return nil
}

// LadderConfig 阶梯配置
type Activity_LadderConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int32                                      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`        // 梯度Id
	Len    int32                                      `protobuf:"varint,2,opt,name=len,proto3" json:"len,omitempty"`      // 长度
	Reward *Activity_LadderConfig_LadderRewardPackage `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"` // 礼包配置
}

func (x *Activity_LadderConfig) Reset() {
	*x = Activity_LadderConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Activity_LadderConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity_LadderConfig) ProtoMessage() {}

func (x *Activity_LadderConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity_LadderConfig.ProtoReflect.Descriptor instead.
func (*Activity_LadderConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{6, 0}
}

func (x *Activity_LadderConfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Activity_LadderConfig) GetLen() int32 {
	if x != nil {
		return x.Len
	}
	return 0
}

func (x *Activity_LadderConfig) GetReward() *Activity_LadderConfig_LadderRewardPackage {
	if x != nil {
		return x.Reward
	}
	return nil
}

// ActivityConfig 活动配置信息
type Activity_ActivityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId int32                    `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"` // 活动id
	Ladders    []*Activity_LadderConfig `protobuf:"bytes,2,rep,name=ladders,proto3" json:"ladders,omitempty"`        // 梯度信息
}

func (x *Activity_ActivityConfig) Reset() {
	*x = Activity_ActivityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Activity_ActivityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity_ActivityConfig) ProtoMessage() {}

func (x *Activity_ActivityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity_ActivityConfig.ProtoReflect.Descriptor instead.
func (*Activity_ActivityConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{6, 1}
}

func (x *Activity_ActivityConfig) GetActivityId() int32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *Activity_ActivityConfig) GetLadders() []*Activity_LadderConfig {
	if x != nil {
		return x.Ladders
	}
	return nil
}

// LadderRewardItem 梯度奖励
type Activity_LadderConfig_LadderRewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *Activity_LadderConfig_LadderRewardItem) Reset() {
	*x = Activity_LadderConfig_LadderRewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Activity_LadderConfig_LadderRewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity_LadderConfig_LadderRewardItem) ProtoMessage() {}

func (x *Activity_LadderConfig_LadderRewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity_LadderConfig_LadderRewardItem.ProtoReflect.Descriptor instead.
func (*Activity_LadderConfig_LadderRewardItem) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{6, 0, 0}
}

func (x *Activity_LadderConfig_LadderRewardItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Activity_LadderConfig_LadderRewardItem) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

// LadderReward 阶梯奖励
type Activity_LadderConfig_LadderRewardPackage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindId int32                                     `protobuf:"varint,1,opt,name=bindId,proto3" json:"bindId,omitempty"`
	Items  []*Activity_LadderConfig_LadderRewardItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *Activity_LadderConfig_LadderRewardPackage) Reset() {
	*x = Activity_LadderConfig_LadderRewardPackage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Activity_LadderConfig_LadderRewardPackage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity_LadderConfig_LadderRewardPackage) ProtoMessage() {}

func (x *Activity_LadderConfig_LadderRewardPackage) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity_LadderConfig_LadderRewardPackage.ProtoReflect.Descriptor instead.
func (*Activity_LadderConfig_LadderRewardPackage) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{6, 0, 1}
}

func (x *Activity_LadderConfig_LadderRewardPackage) GetBindId() int32 {
	if x != nil {
		return x.BindId
	}
	return 0
}

func (x *Activity_LadderConfig_LadderRewardPackage) GetItems() []*Activity_LadderConfig_LadderRewardItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type EntryExt_StageProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StageType     int32 `protobuf:"varint,1,opt,name=stageType,proto3" json:"stageType,omitempty"`         // 参见pb/event/event.proto#L260 CWSXStageType
	StageProgress int32 `protobuf:"varint,2,opt,name=stageProgress,proto3" json:"stageProgress,omitempty"` // 完成关卡用户能获得的进度
}

func (x *EntryExt_StageProgress) Reset() {
	*x = EntryExt_StageProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntryExt_StageProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntryExt_StageProgress) ProtoMessage() {}

func (x *EntryExt_StageProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntryExt_StageProgress.ProtoReflect.Descriptor instead.
func (*EntryExt_StageProgress) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP(), []int{11, 0}
}

func (x *EntryExt_StageProgress) GetStageType() int32 {
	if x != nil {
		return x.StageType
	}
	return 0
}

func (x *EntryExt_StageProgress) GetStageProgress() int32 {
	if x != nil {
		return x.StageProgress
	}
	return 0
}

var File_pb_game_breakout_activity_webapi_webapi_proto protoreflect.FileDescriptor

var file_pb_game_breakout_activity_webapi_webapi_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f,
	0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x77, 0x65, 0x62, 0x61,
	0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x16, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x2b, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x2f,
	0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x62,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x64, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x64,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x22, 0x80, 0x02, 0x0a, 0x0c,
	0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x61, 0x63, 0x68, 0x69,
	0x65, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x68, 0x69,
	0x65, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x52,
	0x0a, 0x08, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x75,
	0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x63, 0x75, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x22, 0xdf, 0x03, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65,
	0x63, 0x12, 0x42, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72,
	0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x0e, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x20, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4e, 0x75, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x61, 0x6e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x4e, 0x75,
	0x6d, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0c, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x68, 0x61, 0x73, 0x52, 0x65, 0x61, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x68, 0x61, 0x73, 0x52, 0x65, 0x61, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x6f, 0x6e, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64, 0x6f, 0x6e, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0xab, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x63, 0x68,
	0x69, 0x65, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x4e, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x6c, 0x61, 0x64, 0x64,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xf2, 0x04, 0x0a, 0x08, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12,
	0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x47, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x64, 0x1a, 0xc7, 0x02, 0x0a, 0x0c, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6c, 0x65, 0x6e, 0x12, 0x59, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x1a, 0x34, 0x0a, 0x10, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x1a, 0x83, 0x01, 0x0a, 0x13, 0x4c, 0x61, 0x64, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x62, 0x69, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x62, 0x69, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x79, 0x0a, 0x0e,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e,
	0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x47,
	0x0a, 0x07, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x07,
	0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x73, 0x22, 0x56, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22,
	0x50, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x40, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x22, 0xa3, 0x01, 0x0a, 0x0f, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x41, 0x63, 0x68, 0x69, 0x65,
	0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x41, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x61, 0x64, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09,
	0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x63, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xf3, 0x01, 0x0a, 0x08, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x45, 0x78, 0x74, 0x12, 0x3c, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72,
	0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x12, 0x54, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x53, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x32, 0xac, 0x03,
	0x0a, 0x06, 0x57, 0x65, 0x62, 0x61, 0x70, 0x69, 0x12, 0x57, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73,
	0x70, 0x12, 0x60, 0x0a, 0x0c, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x41, 0x63, 0x68, 0x69, 0x65, 0x76,
	0x65, 0x12, 0x27, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x41, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x41, 0x63, 0x68, 0x69, 0x65, 0x76, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x04, 0x52, 0x65, 0x61, 0x64, 0x12, 0x27, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x52, 0x65,
	0x61, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x4a, 0x0a,
	0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1d, 0x2e, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79,
	0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x69, 0x6e, 0x6c,
	0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42, 0x55, 0x5a, 0x53,
	0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b,
	0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x77, 0x65, 0x62,
	0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_breakout_activity_webapi_webapi_proto_rawDescOnce sync.Once
	file_pb_game_breakout_activity_webapi_webapi_proto_rawDescData = file_pb_game_breakout_activity_webapi_webapi_proto_rawDesc
)

func file_pb_game_breakout_activity_webapi_webapi_proto_rawDescGZIP() []byte {
	file_pb_game_breakout_activity_webapi_webapi_proto_rawDescOnce.Do(func() {
		file_pb_game_breakout_activity_webapi_webapi_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_breakout_activity_webapi_webapi_proto_rawDescData)
	})
	return file_pb_game_breakout_activity_webapi_webapi_proto_rawDescData
}

var file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_pb_game_breakout_activity_webapi_webapi_proto_goTypes = []interface{}{
	(*ReadActivityReq)(nil),                           // 0: game_breakout_activity.ReadActivityReq
	(*ReadActivityRsp)(nil),                           // 1: game_breakout_activity.ReadActivityRsp
	(*LadderStatus)(nil),                              // 2: game_breakout_activity.LadderStatus
	(*Progress)(nil),                                  // 3: game_breakout_activity.Progress
	(*ActivityStatus)(nil),                            // 4: game_breakout_activity.ActivityStatus
	(*UserAchieveItem)(nil),                           // 5: game_breakout_activity.UserAchieveItem
	(*Activity)(nil),                                  // 6: game_breakout_activity.Activity
	(*QueryListReq)(nil),                              // 7: game_breakout_activity.QueryListReq
	(*QueryListRsp)(nil),                              // 8: game_breakout_activity.QueryListRsp
	(*ClaimAchieveReq)(nil),                           // 9: game_breakout_activity.ClaimAchieveReq
	(*ClaimAchieveRsp)(nil),                           // 10: game_breakout_activity.ClaimAchieveRsp
	(*EntryExt)(nil),                                  // 11: game_breakout_activity.EntryExt
	(*Activity_LadderConfig)(nil),                     // 12: game_breakout_activity.Activity.LadderConfig
	(*Activity_ActivityConfig)(nil),                   // 13: game_breakout_activity.Activity.ActivityConfig
	(*Activity_LadderConfig_LadderRewardItem)(nil),    // 14: game_breakout_activity.Activity.LadderConfig.LadderRewardItem
	(*Activity_LadderConfig_LadderRewardPackage)(nil), // 15: game_breakout_activity.Activity.LadderConfig.LadderRewardPackage
	(*EntryExt_StageProgress)(nil),                    // 16: game_breakout_activity.EntryExt.StageProgress
	(common.UserAchieveState)(0),                      // 17: game_breakout_activity.UserAchieveState
	(common.UserClaimState)(0),                        // 18: game_breakout_activity.UserClaimState
	(common.UserActivityState)(0),                     // 19: game_breakout_activity.UserActivityState
	(*device.Device)(nil),                             // 20: device.Device
	(*game_api.TreasureCard)(nil),                     // 21: game_api.TreasureCard
	(*game_api.RewardItem)(nil),                       // 22: game_api.RewardItem
	(*entry.EntryReq)(nil),                            // 23: cwsx_activity_entry.EntryReq
	(*inlet.ActivityStateReq)(nil),                    // 24: inlet.ActivityStateReq
	(*entry.EntryRsp)(nil),                            // 25: cwsx_activity_entry.EntryRsp
	(*inlet.ActivityStateRsp)(nil),                    // 26: inlet.ActivityStateRsp
}
var file_pb_game_breakout_activity_webapi_webapi_proto_depIdxs = []int32{
	17, // 0: game_breakout_activity.LadderStatus.achieveState:type_name -> game_breakout_activity.UserAchieveState
	18, // 1: game_breakout_activity.LadderStatus.claimState:type_name -> game_breakout_activity.UserClaimState
	3,  // 2: game_breakout_activity.ActivityStatus.actProgress:type_name -> game_breakout_activity.Progress
	3,  // 3: game_breakout_activity.ActivityStatus.ladderProgress:type_name -> game_breakout_activity.Progress
	19, // 4: game_breakout_activity.ActivityStatus.status:type_name -> game_breakout_activity.UserActivityState
	2,  // 5: game_breakout_activity.ActivityStatus.ladderStatus:type_name -> game_breakout_activity.LadderStatus
	4,  // 6: game_breakout_activity.UserAchieveItem.activityStatus:type_name -> game_breakout_activity.ActivityStatus
	2,  // 7: game_breakout_activity.UserAchieveItem.ladderStatus:type_name -> game_breakout_activity.LadderStatus
	4,  // 8: game_breakout_activity.Activity.status:type_name -> game_breakout_activity.ActivityStatus
	13, // 9: game_breakout_activity.Activity.config:type_name -> game_breakout_activity.Activity.ActivityConfig
	6,  // 10: game_breakout_activity.QueryListRsp.activities:type_name -> game_breakout_activity.Activity
	20, // 11: game_breakout_activity.ClaimAchieveReq.device:type_name -> device.Device
	21, // 12: game_breakout_activity.ClaimAchieveRsp.cards:type_name -> game_api.TreasureCard
	22, // 13: game_breakout_activity.ClaimAchieveRsp.rewards:type_name -> game_api.RewardItem
	6,  // 14: game_breakout_activity.EntryExt.activity:type_name -> game_breakout_activity.Activity
	16, // 15: game_breakout_activity.EntryExt.stageProgress:type_name -> game_breakout_activity.EntryExt.StageProgress
	15, // 16: game_breakout_activity.Activity.LadderConfig.reward:type_name -> game_breakout_activity.Activity.LadderConfig.LadderRewardPackage
	12, // 17: game_breakout_activity.Activity.ActivityConfig.ladders:type_name -> game_breakout_activity.Activity.LadderConfig
	14, // 18: game_breakout_activity.Activity.LadderConfig.LadderRewardPackage.items:type_name -> game_breakout_activity.Activity.LadderConfig.LadderRewardItem
	7,  // 19: game_breakout_activity.Webapi.QueryList:input_type -> game_breakout_activity.QueryListReq
	9,  // 20: game_breakout_activity.Webapi.ClaimAchieve:input_type -> game_breakout_activity.ClaimAchieveReq
	0,  // 21: game_breakout_activity.Webapi.Read:input_type -> game_breakout_activity.ReadActivityReq
	23, // 22: game_breakout_activity.Webapi.QueryEntry:input_type -> cwsx_activity_entry.EntryReq
	24, // 23: game_breakout_activity.Webapi.ActivityState:input_type -> inlet.ActivityStateReq
	8,  // 24: game_breakout_activity.Webapi.QueryList:output_type -> game_breakout_activity.QueryListRsp
	10, // 25: game_breakout_activity.Webapi.ClaimAchieve:output_type -> game_breakout_activity.ClaimAchieveRsp
	1,  // 26: game_breakout_activity.Webapi.Read:output_type -> game_breakout_activity.ReadActivityRsp
	25, // 27: game_breakout_activity.Webapi.QueryEntry:output_type -> cwsx_activity_entry.EntryRsp
	26, // 28: game_breakout_activity.Webapi.ActivityState:output_type -> inlet.ActivityStateRsp
	24, // [24:29] is the sub-list for method output_type
	19, // [19:24] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_pb_game_breakout_activity_webapi_webapi_proto_init() }
func file_pb_game_breakout_activity_webapi_webapi_proto_init() {
	if File_pb_game_breakout_activity_webapi_webapi_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadActivityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadActivityRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LadderStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Progress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAchieveItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Activity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimAchieveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimAchieveRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntryExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Activity_LadderConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Activity_ActivityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Activity_LadderConfig_LadderRewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Activity_LadderConfig_LadderRewardPackage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EntryExt_StageProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_breakout_activity_webapi_webapi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_breakout_activity_webapi_webapi_proto_goTypes,
		DependencyIndexes: file_pb_game_breakout_activity_webapi_webapi_proto_depIdxs,
		MessageInfos:      file_pb_game_breakout_activity_webapi_webapi_proto_msgTypes,
	}.Build()
	File_pb_game_breakout_activity_webapi_webapi_proto = out.File
	file_pb_game_breakout_activity_webapi_webapi_proto_rawDesc = nil
	file_pb_game_breakout_activity_webapi_webapi_proto_goTypes = nil
	file_pb_game_breakout_activity_webapi_webapi_proto_depIdxs = nil
}
