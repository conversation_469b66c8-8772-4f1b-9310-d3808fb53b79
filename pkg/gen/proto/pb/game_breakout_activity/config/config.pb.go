// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_breakout_activity/config/config.proto

package config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LadderRewardItem 梯度奖励
type LadderRewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Num int32 `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *LadderRewardItem) Reset() {
	*x = LadderRewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LadderRewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LadderRewardItem) ProtoMessage() {}

func (x *LadderRewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LadderRewardItem.ProtoReflect.Descriptor instead.
func (*LadderRewardItem) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_config_config_proto_rawDescGZIP(), []int{0}
}

func (x *LadderRewardItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LadderRewardItem) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

// LadderReward 阶梯奖励
type LadderRewardPackage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindId int32               `protobuf:"varint,1,opt,name=bindId,proto3" json:"bindId,omitempty"`
	Items  []*LadderRewardItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *LadderRewardPackage) Reset() {
	*x = LadderRewardPackage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LadderRewardPackage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LadderRewardPackage) ProtoMessage() {}

func (x *LadderRewardPackage) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LadderRewardPackage.ProtoReflect.Descriptor instead.
func (*LadderRewardPackage) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_config_config_proto_rawDescGZIP(), []int{1}
}

func (x *LadderRewardPackage) GetBindId() int32 {
	if x != nil {
		return x.BindId
	}
	return 0
}

func (x *LadderRewardPackage) GetItems() []*LadderRewardItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// Ladder 阶梯
type LadderConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Length          int32  `protobuf:"varint,2,opt,name=length,proto3" json:"length,omitempty"`
	BindRewardPkgId int32  `protobuf:"varint,3,opt,name=bindRewardPkgId,proto3" json:"bindRewardPkgId,omitempty"` // 礼包Id
	BindReportId    string `protobuf:"bytes,4,opt,name=bindReportId,proto3" json:"bindReportId,omitempty"`        // 达成进度后key上报用
}

func (x *LadderConfig) Reset() {
	*x = LadderConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LadderConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LadderConfig) ProtoMessage() {}

func (x *LadderConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LadderConfig.ProtoReflect.Descriptor instead.
func (*LadderConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_config_config_proto_rawDescGZIP(), []int{2}
}

func (x *LadderConfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LadderConfig) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *LadderConfig) GetBindRewardPkgId() int32 {
	if x != nil {
		return x.BindRewardPkgId
	}
	return 0
}

func (x *LadderConfig) GetBindReportId() string {
	if x != nil {
		return x.BindReportId
	}
	return ""
}

// LevelConfig 关卡配置
type LevelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LevelType   int32 `protobuf:"varint,1,opt,name=levelType,proto3" json:"levelType,omitempty"`     // 关卡类型
	ProgressNum int32 `protobuf:"varint,2,opt,name=progressNum,proto3" json:"progressNum,omitempty"` // 累计进度
}

func (x *LevelConfig) Reset() {
	*x = LevelConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LevelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LevelConfig) ProtoMessage() {}

func (x *LevelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LevelConfig.ProtoReflect.Descriptor instead.
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_config_config_proto_rawDescGZIP(), []int{3}
}

func (x *LevelConfig) GetLevelType() int32 {
	if x != nil {
		return x.LevelType
	}
	return 0
}

func (x *LevelConfig) GetProgressNum() int32 {
	if x != nil {
		return x.ProgressNum
	}
	return 0
}

type OpenRoundConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundId int32 `protobuf:"varint,1,opt,name=roundId,proto3" json:"roundId,omitempty"` // 轮次id
	Begin   int32 `protobuf:"varint,2,opt,name=begin,proto3" json:"begin,omitempty"`     // 开始时间(0-6)
	End     int32 `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`         // 结束时间(0-6)
}

func (x *OpenRoundConfig) Reset() {
	*x = OpenRoundConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OpenRoundConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRoundConfig) ProtoMessage() {}

func (x *OpenRoundConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRoundConfig.ProtoReflect.Descriptor instead.
func (*OpenRoundConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_config_config_proto_rawDescGZIP(), []int{4}
}

func (x *OpenRoundConfig) GetRoundId() int32 {
	if x != nil {
		return x.RoundId
	}
	return 0
}

func (x *OpenRoundConfig) GetBegin() int32 {
	if x != nil {
		return x.Begin
	}
	return 0
}

func (x *OpenRoundConfig) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

// Activity 活动配置
type ActivityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                   // 活动Id
	AppId               string             `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`                              // appId
	Name                string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                // 活动名称
	UserRemainSec       int32              `protobuf:"varint,4,opt,name=userRemainSec,proto3" json:"userRemainSec,omitempty"`             // 用户参与后倒计时
	OpenPeriod          int32              `protobuf:"varint,5,opt,name=openPeriod,proto3" json:"openPeriod,omitempty"`                   // 开放日范围
	LadderConfigs       []*LadderConfig    `protobuf:"bytes,6,rep,name=ladderConfigs,proto3" json:"ladderConfigs,omitempty"`              // 梯度设置
	LevelConfig         []*LevelConfig     `protobuf:"bytes,7,rep,name=levelConfig,proto3" json:"levelConfig,omitempty"`                  // 关卡配置
	BeginTime           int64              `protobuf:"varint,8,opt,name=beginTime,proto3" json:"beginTime,omitempty"`                     // 开始时间
	EndTime             int64              `protobuf:"varint,9,opt,name=endTime,proto3" json:"endTime,omitempty"`                         // 结束时间
	Enable              bool               `protobuf:"varint,10,opt,name=enable,proto3" json:"enable,omitempty"`                          // 是否开启
	RewardDeliveryRoute string             `protobuf:"bytes,11,opt,name=rewardDeliveryRoute,proto3" json:"rewardDeliveryRoute,omitempty"` // 游戏发奖链接
	RewardPkgQueryRoute string             `protobuf:"bytes,12,opt,name=rewardPkgQueryRoute,proto3" json:"rewardPkgQueryRoute,omitempty"` // 游戏礼包查询接口
	JoinMode            int32              `protobuf:"varint,13,opt,name=joinMode,proto3" json:"joinMode,omitempty"`                      // 用户参与模式(0上线后玩第一局开始算起, 1上线后曝光开始算起)
	Whitelist           []string           `protobuf:"bytes,14,rep,name=whitelist,proto3" json:"whitelist,omitempty"`                     // 白名单列表
	EnableWhitelist     bool               `protobuf:"varint,15,opt,name=enableWhitelist,proto3" json:"enableWhitelist,omitempty"`        // 是否开启白名单
	OpenRoundConfigs    []*OpenRoundConfig `protobuf:"bytes,16,rep,name=openRoundConfigs,proto3" json:"openRoundConfigs,omitempty"`       // 每周开放轮次信息
	MinLimitStateId     int32              `protobuf:"varint,17,opt,name=minLimitStateId,proto3" json:"minLimitStateId,omitempty"`        // 限制开放的用户
}

func (x *ActivityConfig) Reset() {
	*x = ActivityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityConfig) ProtoMessage() {}

func (x *ActivityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_config_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityConfig.ProtoReflect.Descriptor instead.
func (*ActivityConfig) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_config_config_proto_rawDescGZIP(), []int{5}
}

func (x *ActivityConfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivityConfig) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ActivityConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ActivityConfig) GetUserRemainSec() int32 {
	if x != nil {
		return x.UserRemainSec
	}
	return 0
}

func (x *ActivityConfig) GetOpenPeriod() int32 {
	if x != nil {
		return x.OpenPeriod
	}
	return 0
}

func (x *ActivityConfig) GetLadderConfigs() []*LadderConfig {
	if x != nil {
		return x.LadderConfigs
	}
	return nil
}

func (x *ActivityConfig) GetLevelConfig() []*LevelConfig {
	if x != nil {
		return x.LevelConfig
	}
	return nil
}

func (x *ActivityConfig) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *ActivityConfig) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ActivityConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ActivityConfig) GetRewardDeliveryRoute() string {
	if x != nil {
		return x.RewardDeliveryRoute
	}
	return ""
}

func (x *ActivityConfig) GetRewardPkgQueryRoute() string {
	if x != nil {
		return x.RewardPkgQueryRoute
	}
	return ""
}

func (x *ActivityConfig) GetJoinMode() int32 {
	if x != nil {
		return x.JoinMode
	}
	return 0
}

func (x *ActivityConfig) GetWhitelist() []string {
	if x != nil {
		return x.Whitelist
	}
	return nil
}

func (x *ActivityConfig) GetEnableWhitelist() bool {
	if x != nil {
		return x.EnableWhitelist
	}
	return false
}

func (x *ActivityConfig) GetOpenRoundConfigs() []*OpenRoundConfig {
	if x != nil {
		return x.OpenRoundConfigs
	}
	return nil
}

func (x *ActivityConfig) GetMinLimitStateId() int32 {
	if x != nil {
		return x.MinLimitStateId
	}
	return 0
}

var File_pb_game_breakout_activity_config_config_proto protoreflect.FileDescriptor

var file_pb_game_breakout_activity_config_config_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f,
	0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x16, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x22, 0x34, 0x0a, 0x10, 0x4c, 0x61, 0x64, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e,
	0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x6d, 0x0a,
	0x13, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x69, 0x6e, 0x64, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x62, 0x69, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x84, 0x01, 0x0a,
	0x0c, 0x4c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x6b, 0x67, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x6b, 0x67, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x0b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e,
	0x75, 0x6d, 0x22, 0x53, 0x0a, 0x0f, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0xba, 0x05, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70,
	0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x70,
	0x65, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x6f, 0x70, 0x65, 0x6e, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x4a, 0x0a, 0x0d, 0x6c, 0x61,
	0x64, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x61, 0x64, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x6c, 0x61, 0x64, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a,
	0x13, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12,
	0x30, 0x0a, 0x13, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x6b, 0x67, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x50, 0x6b, 0x67, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x52, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x52, 0x6f,
	0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x69,
	0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x42, 0x55, 0x5a, 0x53, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_game_breakout_activity_config_config_proto_rawDescOnce sync.Once
	file_pb_game_breakout_activity_config_config_proto_rawDescData = file_pb_game_breakout_activity_config_config_proto_rawDesc
)

func file_pb_game_breakout_activity_config_config_proto_rawDescGZIP() []byte {
	file_pb_game_breakout_activity_config_config_proto_rawDescOnce.Do(func() {
		file_pb_game_breakout_activity_config_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_breakout_activity_config_config_proto_rawDescData)
	})
	return file_pb_game_breakout_activity_config_config_proto_rawDescData
}

var file_pb_game_breakout_activity_config_config_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_game_breakout_activity_config_config_proto_goTypes = []interface{}{
	(*LadderRewardItem)(nil),    // 0: game_breakout_activity.LadderRewardItem
	(*LadderRewardPackage)(nil), // 1: game_breakout_activity.LadderRewardPackage
	(*LadderConfig)(nil),        // 2: game_breakout_activity.LadderConfig
	(*LevelConfig)(nil),         // 3: game_breakout_activity.LevelConfig
	(*OpenRoundConfig)(nil),     // 4: game_breakout_activity.OpenRoundConfig
	(*ActivityConfig)(nil),      // 5: game_breakout_activity.ActivityConfig
}
var file_pb_game_breakout_activity_config_config_proto_depIdxs = []int32{
	0, // 0: game_breakout_activity.LadderRewardPackage.items:type_name -> game_breakout_activity.LadderRewardItem
	2, // 1: game_breakout_activity.ActivityConfig.ladderConfigs:type_name -> game_breakout_activity.LadderConfig
	3, // 2: game_breakout_activity.ActivityConfig.levelConfig:type_name -> game_breakout_activity.LevelConfig
	4, // 3: game_breakout_activity.ActivityConfig.openRoundConfigs:type_name -> game_breakout_activity.OpenRoundConfig
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pb_game_breakout_activity_config_config_proto_init() }
func file_pb_game_breakout_activity_config_config_proto_init() {
	if File_pb_game_breakout_activity_config_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_breakout_activity_config_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LadderRewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_config_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LadderRewardPackage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_config_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LadderConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_config_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LevelConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_config_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OpenRoundConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_config_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_breakout_activity_config_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_breakout_activity_config_config_proto_goTypes,
		DependencyIndexes: file_pb_game_breakout_activity_config_config_proto_depIdxs,
		MessageInfos:      file_pb_game_breakout_activity_config_config_proto_msgTypes,
	}.Build()
	File_pb_game_breakout_activity_config_config_proto = out.File
	file_pb_game_breakout_activity_config_config_proto_rawDesc = nil
	file_pb_game_breakout_activity_config_config_proto_goTypes = nil
	file_pb_game_breakout_activity_config_config_proto_depIdxs = nil
}
