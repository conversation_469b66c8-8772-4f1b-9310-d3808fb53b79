// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_breakout_activity/consumer/event_consumer.proto

package consumer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlaceHolderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlaceHolderReq) Reset() {
	*x = PlaceHolderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceHolderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceHolderReq) ProtoMessage() {}

func (x *PlaceHolderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceHolderReq.ProtoReflect.Descriptor instead.
func (*PlaceHolderReq) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescGZIP(), []int{0}
}

type PlaceHolderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlaceHolderRsp) Reset() {
	*x = PlaceHolderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlaceHolderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceHolderRsp) ProtoMessage() {}

func (x *PlaceHolderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceHolderRsp.ProtoReflect.Descriptor instead.
func (*PlaceHolderRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescGZIP(), []int{1}
}

var File_pb_game_breakout_activity_consumer_event_consumer_proto protoreflect.FileDescriptor

var file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDesc = []byte{
	0x0a, 0x37, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f,
	0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x22, 0x10, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x22, 0x10, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x52, 0x73, 0x70, 0x32, 0x76, 0x0a, 0x15, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x5d,
	0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12, 0x26, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65,
	0x61, 0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x50,
	0x6c, 0x61, 0x63, 0x65, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x42, 0x57, 0x5a,
	0x55, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x61,
	0x6b, 0x6f, 0x75, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescOnce sync.Once
	file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescData = file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDesc
)

func file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescGZIP() []byte {
	file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescOnce.Do(func() {
		file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescData)
	})
	return file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDescData
}

var file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_game_breakout_activity_consumer_event_consumer_proto_goTypes = []interface{}{
	(*PlaceHolderReq)(nil), // 0: game_breakout_activity.PlaceHolderReq
	(*PlaceHolderRsp)(nil), // 1: game_breakout_activity.PlaceHolderRsp
}
var file_pb_game_breakout_activity_consumer_event_consumer_proto_depIdxs = []int32{
	0, // 0: game_breakout_activity.ActivityEventConsumer.PlaceHolder:input_type -> game_breakout_activity.PlaceHolderReq
	1, // 1: game_breakout_activity.ActivityEventConsumer.PlaceHolder:output_type -> game_breakout_activity.PlaceHolderRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_game_breakout_activity_consumer_event_consumer_proto_init() }
func file_pb_game_breakout_activity_consumer_event_consumer_proto_init() {
	if File_pb_game_breakout_activity_consumer_event_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceHolderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlaceHolderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_breakout_activity_consumer_event_consumer_proto_goTypes,
		DependencyIndexes: file_pb_game_breakout_activity_consumer_event_consumer_proto_depIdxs,
		MessageInfos:      file_pb_game_breakout_activity_consumer_event_consumer_proto_msgTypes,
	}.Build()
	File_pb_game_breakout_activity_consumer_event_consumer_proto = out.File
	file_pb_game_breakout_activity_consumer_event_consumer_proto_rawDesc = nil
	file_pb_game_breakout_activity_consumer_event_consumer_proto_goTypes = nil
	file_pb_game_breakout_activity_consumer_event_consumer_proto_depIdxs = nil
}
