// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_breakout_activity/builder/builder.proto

package builder

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	game_api "kugou_adapter_service/pkg/gen/proto/pb/game_api"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Builder_Read_FullMethodName           = "/game_breakout_activity.Builder/Read"
	Builder_Join_FullMethodName           = "/game_breakout_activity.Builder/Join"
	Builder_Claim_FullMethodName          = "/game_breakout_activity.Builder/Claim"
	Builder_AddProgress_FullMethodName    = "/game_breakout_activity.Builder/AddProgress"
	Builder_Clean_FullMethodName          = "/game_breakout_activity.Builder/Clean"
	Builder_RetentionPopup_FullMethodName = "/game_breakout_activity.Builder/RetentionPopup"
)

// BuilderClient is the client API for Builder service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BuilderClient interface {
	Read(ctx context.Context, in *ReadReq, opts ...grpc.CallOption) (*ReadRsp, error)
	Join(ctx context.Context, in *JoinReq, opts ...grpc.CallOption) (*JoinRsp, error)
	Claim(ctx context.Context, in *ClaimReq, opts ...grpc.CallOption) (*ClaimRsp, error)
	AddProgress(ctx context.Context, in *AddProgressReq, opts ...grpc.CallOption) (*AddProgressRsp, error)
	Clean(ctx context.Context, in *CleanReq, opts ...grpc.CallOption) (*CleanRsp, error)
	RetentionPopup(ctx context.Context, in *game_api.RetentionPopupReq, opts ...grpc.CallOption) (*game_api.RetentionPopupRsp, error)
}

type builderClient struct {
	cc grpc.ClientConnInterface
}

func NewBuilderClient(cc grpc.ClientConnInterface) BuilderClient {
	return &builderClient{cc}
}

func (c *builderClient) Read(ctx context.Context, in *ReadReq, opts ...grpc.CallOption) (*ReadRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReadRsp)
	err := c.cc.Invoke(ctx, Builder_Read_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *builderClient) Join(ctx context.Context, in *JoinReq, opts ...grpc.CallOption) (*JoinRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JoinRsp)
	err := c.cc.Invoke(ctx, Builder_Join_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *builderClient) Claim(ctx context.Context, in *ClaimReq, opts ...grpc.CallOption) (*ClaimRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClaimRsp)
	err := c.cc.Invoke(ctx, Builder_Claim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *builderClient) AddProgress(ctx context.Context, in *AddProgressReq, opts ...grpc.CallOption) (*AddProgressRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddProgressRsp)
	err := c.cc.Invoke(ctx, Builder_AddProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *builderClient) Clean(ctx context.Context, in *CleanReq, opts ...grpc.CallOption) (*CleanRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CleanRsp)
	err := c.cc.Invoke(ctx, Builder_Clean_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *builderClient) RetentionPopup(ctx context.Context, in *game_api.RetentionPopupReq, opts ...grpc.CallOption) (*game_api.RetentionPopupRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game_api.RetentionPopupRsp)
	err := c.cc.Invoke(ctx, Builder_RetentionPopup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BuilderServer is the server API for Builder service.
// All implementations should embed UnimplementedBuilderServer
// for forward compatibility
type BuilderServer interface {
	Read(context.Context, *ReadReq) (*ReadRsp, error)
	Join(context.Context, *JoinReq) (*JoinRsp, error)
	Claim(context.Context, *ClaimReq) (*ClaimRsp, error)
	AddProgress(context.Context, *AddProgressReq) (*AddProgressRsp, error)
	Clean(context.Context, *CleanReq) (*CleanRsp, error)
	RetentionPopup(context.Context, *game_api.RetentionPopupReq) (*game_api.RetentionPopupRsp, error)
}

// UnimplementedBuilderServer should be embedded to have forward compatible implementations.
type UnimplementedBuilderServer struct {
}

func (UnimplementedBuilderServer) Read(context.Context, *ReadReq) (*ReadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Read not implemented")
}
func (UnimplementedBuilderServer) Join(context.Context, *JoinReq) (*JoinRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Join not implemented")
}
func (UnimplementedBuilderServer) Claim(context.Context, *ClaimReq) (*ClaimRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Claim not implemented")
}
func (UnimplementedBuilderServer) AddProgress(context.Context, *AddProgressReq) (*AddProgressRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddProgress not implemented")
}
func (UnimplementedBuilderServer) Clean(context.Context, *CleanReq) (*CleanRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Clean not implemented")
}
func (UnimplementedBuilderServer) RetentionPopup(context.Context, *game_api.RetentionPopupReq) (*game_api.RetentionPopupRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetentionPopup not implemented")
}

// UnsafeBuilderServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BuilderServer will
// result in compilation errors.
type UnsafeBuilderServer interface {
	mustEmbedUnimplementedBuilderServer()
}

func RegisterBuilderServer(s grpc.ServiceRegistrar, srv BuilderServer) {
	s.RegisterService(&Builder_ServiceDesc, srv)
}

func _Builder_Read_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BuilderServer).Read(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Builder_Read_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BuilderServer).Read(ctx, req.(*ReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Builder_Join_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BuilderServer).Join(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Builder_Join_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BuilderServer).Join(ctx, req.(*JoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Builder_Claim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClaimReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BuilderServer).Claim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Builder_Claim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BuilderServer).Claim(ctx, req.(*ClaimReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Builder_AddProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BuilderServer).AddProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Builder_AddProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BuilderServer).AddProgress(ctx, req.(*AddProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Builder_Clean_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BuilderServer).Clean(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Builder_Clean_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BuilderServer).Clean(ctx, req.(*CleanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Builder_RetentionPopup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game_api.RetentionPopupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BuilderServer).RetentionPopup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Builder_RetentionPopup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BuilderServer).RetentionPopup(ctx, req.(*game_api.RetentionPopupReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Builder_ServiceDesc is the grpc.ServiceDesc for Builder service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Builder_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_breakout_activity.Builder",
	HandlerType: (*BuilderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Read",
			Handler:    _Builder_Read_Handler,
		},
		{
			MethodName: "Join",
			Handler:    _Builder_Join_Handler,
		},
		{
			MethodName: "Claim",
			Handler:    _Builder_Claim_Handler,
		},
		{
			MethodName: "AddProgress",
			Handler:    _Builder_AddProgress_Handler,
		},
		{
			MethodName: "Clean",
			Handler:    _Builder_Clean_Handler,
		},
		{
			MethodName: "RetentionPopup",
			Handler:    _Builder_RetentionPopup_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_breakout_activity/builder/builder.proto",
}
