// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_api/game_api.proto

package game_api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	event "kugou_adapter_service/pkg/gen/proto/pb/event"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RewardItemType int32

const (
	RewardItemType_Reward_From_GameNormal      RewardItemType = 0 // 游戏物品-默认
	RewardItemType_Reward_From_Platform        RewardItemType = 1 // 平台物品
	RewardItemType_Reward_From_GameLimitedTime RewardItemType = 2 // 游戏物品-限时
)

// Enum value maps for RewardItemType.
var (
	RewardItemType_name = map[int32]string{
		0: "Reward_From_GameNormal",
		1: "Reward_From_Platform",
		2: "Reward_From_GameLimitedTime",
	}
	RewardItemType_value = map[string]int32{
		"Reward_From_GameNormal":      0,
		"Reward_From_Platform":        1,
		"Reward_From_GameLimitedTime": 2,
	}
)

func (x RewardItemType) Enum() *RewardItemType {
	p := new(RewardItemType)
	*p = x
	return p
}

func (x RewardItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_api_game_api_proto_enumTypes[0].Descriptor()
}

func (RewardItemType) Type() protoreflect.EnumType {
	return &file_pb_game_api_game_api_proto_enumTypes[0]
}

func (x RewardItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardItemType.Descriptor instead.
func (RewardItemType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{0}
}

type GameDeliveryType int32

const (
	GameDeliveryType_GameDeliveryTypeRedStone     GameDeliveryType = 0 // 游戏红石 https://gm.tmeoa.com/?type=kg_cwsx_activity_gift_prod
	GameDeliveryType_GameDeliveryTypeMiddleConfig GameDeliveryType = 1 // 中台配置 https://game-config.tmeoa.com/sanxiao/asset/props
)

// Enum value maps for GameDeliveryType.
var (
	GameDeliveryType_name = map[int32]string{
		0: "GameDeliveryTypeRedStone",
		1: "GameDeliveryTypeMiddleConfig",
	}
	GameDeliveryType_value = map[string]int32{
		"GameDeliveryTypeRedStone":     0,
		"GameDeliveryTypeMiddleConfig": 1,
	}
)

func (x GameDeliveryType) Enum() *GameDeliveryType {
	p := new(GameDeliveryType)
	*p = x
	return p
}

func (x GameDeliveryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameDeliveryType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_api_game_api_proto_enumTypes[1].Descriptor()
}

func (GameDeliveryType) Type() protoreflect.EnumType {
	return &file_pb_game_api_game_api_proto_enumTypes[1]
}

func (x GameDeliveryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameDeliveryType.Descriptor instead.
func (GameDeliveryType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{1}
}

// 挽留弹窗协议
type GameActivityType int32

const (
	GameActivityType_Unknow   GameActivityType = 0 //  未知
	GameActivityType_Schedule GameActivityType = 1 // 进度
	GameActivityType_Collect  GameActivityType = 2 // 收集
)

// Enum value maps for GameActivityType.
var (
	GameActivityType_name = map[int32]string{
		0: "Unknow",
		1: "Schedule",
		2: "Collect",
	}
	GameActivityType_value = map[string]int32{
		"Unknow":   0,
		"Schedule": 1,
		"Collect":  2,
	}
)

func (x GameActivityType) Enum() *GameActivityType {
	p := new(GameActivityType)
	*p = x
	return p
}

func (x GameActivityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameActivityType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_api_game_api_proto_enumTypes[2].Descriptor()
}

func (GameActivityType) Type() protoreflect.EnumType {
	return &file_pb_game_api_game_api_proto_enumTypes[2]
}

func (x GameActivityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameActivityType.Descriptor instead.
func (GameActivityType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{2}
}

type GameActivityStatus int32

const (
	GameActivityStatus_Default   GameActivityStatus = 0 // 默认 未参加
	GameActivityStatus_Ongoing   GameActivityStatus = 1 // 参加进行中
	GameActivityStatus_UnClaimed GameActivityStatus = 2 // 待领取
)

// Enum value maps for GameActivityStatus.
var (
	GameActivityStatus_name = map[int32]string{
		0: "Default",
		1: "Ongoing",
		2: "UnClaimed",
	}
	GameActivityStatus_value = map[string]int32{
		"Default":   0,
		"Ongoing":   1,
		"UnClaimed": 2,
	}
)

func (x GameActivityStatus) Enum() *GameActivityStatus {
	p := new(GameActivityStatus)
	*p = x
	return p
}

func (x GameActivityStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameActivityStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_api_game_api_proto_enumTypes[3].Descriptor()
}

func (GameActivityStatus) Type() protoreflect.EnumType {
	return &file_pb_game_api_game_api_proto_enumTypes[3]
}

func (x GameActivityStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameActivityStatus.Descriptor instead.
func (GameActivityStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{3}
}

type SendHeartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FriendId string `protobuf:"bytes,1,opt,name=friend_id,json=friendId,proto3" json:"friend_id,omitempty"` // 接收方openid
	OwnerId  string `protobuf:"bytes,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`    // 赠送方openid
}

func (x *SendHeartReq) Reset() {
	*x = SendHeartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendHeartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendHeartReq) ProtoMessage() {}

func (x *SendHeartReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendHeartReq.ProtoReflect.Descriptor instead.
func (*SendHeartReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{0}
}

func (x *SendHeartReq) GetFriendId() string {
	if x != nil {
		return x.FriendId
	}
	return ""
}

func (x *SendHeartReq) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

type SendHeartRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hearts uint32 `protobuf:"varint,1,opt,name=hearts,proto3" json:"hearts,omitempty"` // 本次赠送的体力数量
}

func (x *SendHeartRsp) Reset() {
	*x = SendHeartRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendHeartRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendHeartRsp) ProtoMessage() {}

func (x *SendHeartRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendHeartRsp.ProtoReflect.Descriptor instead.
func (*SendHeartRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{1}
}

func (x *SendHeartRsp) GetHearts() uint32 {
	if x != nil {
		return x.Hearts
	}
	return 0
}

type QueryStageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *QueryStageReq) Reset() {
	*x = QueryStageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStageReq) ProtoMessage() {}

func (x *QueryStageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStageReq.ProtoReflect.Descriptor instead.
func (*QueryStageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{2}
}

func (x *QueryStageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryStageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QueryStageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurStage       uint32 `protobuf:"varint,1,opt,name=cur_stage,json=curStage,proto3" json:"cur_stage,omitempty"`                     // 累计成功闯关数：包含巅峰赛
	MaxNormalFloor uint32 `protobuf:"varint,2,opt,name=max_normal_floor,json=maxNormalFloor,proto3" json:"max_normal_floor,omitempty"` // 闯关进度：对应页面上显示的进度
}

func (x *QueryStageRsp) Reset() {
	*x = QueryStageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStageRsp) ProtoMessage() {}

func (x *QueryStageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStageRsp.ProtoReflect.Descriptor instead.
func (*QueryStageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{3}
}

func (x *QueryStageRsp) GetCurStage() uint32 {
	if x != nil {
		return x.CurStage
	}
	return 0
}

func (x *QueryStageRsp) GetMaxNormalFloor() uint32 {
	if x != nil {
		return x.MaxNormalFloor
	}
	return 0
}

type RewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                  // 奖励id
	Num  uint32         `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`                                // 奖励数量 限时道具表示分钟数
	Name string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                               // 奖励名称
	Type RewardItemType `protobuf:"varint,4,opt,name=type,proto3,enum=game_api.RewardItemType" json:"type,omitempty"` // 资产类型
	Img  string         `protobuf:"bytes,5,opt,name=img,proto3" json:"img,omitempty"`                                 // 图片
	Desc string         `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`                               // 描述问题呢
}

func (x *RewardItem) Reset() {
	*x = RewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardItem) ProtoMessage() {}

func (x *RewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardItem.ProtoReflect.Descriptor instead.
func (*RewardItem) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{4}
}

func (x *RewardItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RewardItem) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *RewardItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RewardItem) GetType() RewardItemType {
	if x != nil {
		return x.Type
	}
	return RewardItemType_Reward_From_GameNormal
}

func (x *RewardItem) GetImg() string {
	if x != nil {
		return x.Img
	}
	return ""
}

func (x *RewardItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type Package struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId   string        `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`       // 礼包id
	PackageName string        `protobuf:"bytes,2,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` // 礼包名称
	Rewards     []*RewardItem `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`                            // 奖励
	Price       int64         `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                               // 礼包价格
}

func (x *Package) Reset() {
	*x = Package{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{5}
}

func (x *Package) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *Package) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Package) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *Package) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type BatchQueryPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId     string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageIds []string `protobuf:"bytes,3,rep,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"` // 礼包ids
}

func (x *BatchQueryPackageReq) Reset() {
	*x = BatchQueryPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQueryPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryPackageReq) ProtoMessage() {}

func (x *BatchQueryPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryPackageReq.ProtoReflect.Descriptor instead.
func (*BatchQueryPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{6}
}

func (x *BatchQueryPackageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchQueryPackageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchQueryPackageReq) GetPackageIds() []string {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

type BatchQueryPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Packages map[string]*Package `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 礼包
}

func (x *BatchQueryPackageRsp) Reset() {
	*x = BatchQueryPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQueryPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryPackageRsp) ProtoMessage() {}

func (x *BatchQueryPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryPackageRsp.ProtoReflect.Descriptor instead.
func (*BatchQueryPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{7}
}

func (x *BatchQueryPackageRsp) GetPackages() map[string]*Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

type GameDeliveryPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string           `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId        string           `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageId     string           `protobuf:"bytes,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`             // 礼包 id
	TransactionId string           `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // 唯一订单 id
	Timestamp     int64            `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 发货时间戳
	Num           uint32           `protobuf:"varint,6,opt,name=num,proto3" json:"num,omitempty"`                                         // 发几个礼包,默认一个
	Type          GameDeliveryType `protobuf:"varint,7,opt,name=type,proto3,enum=game_api.GameDeliveryType" json:"type,omitempty"`
}

func (x *GameDeliveryPackageReq) Reset() {
	*x = GameDeliveryPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageReq) ProtoMessage() {}

func (x *GameDeliveryPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageReq.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{8}
}

func (x *GameDeliveryPackageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GameDeliveryPackageReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *GameDeliveryPackageReq) GetType() GameDeliveryType {
	if x != nil {
		return x.Type
	}
	return GameDeliveryType_GameDeliveryTypeRedStone
}

type GameDeliveryPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*RewardItem   `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"` // 奖励
	Cards   []*TreasureCard `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"`     // 如果发奖产生宝藏奖励, 这个字段不为空
}

func (x *GameDeliveryPackageRsp) Reset() {
	*x = GameDeliveryPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageRsp) ProtoMessage() {}

func (x *GameDeliveryPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageRsp.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{9}
}

func (x *GameDeliveryPackageRsp) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *GameDeliveryPackageRsp) GetCards() []*TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

type BatchGameDeliveryPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId        string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageIds    []string `protobuf:"bytes,3,rep,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"`          // 礼包 id
	TransactionId string   `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // 唯一订单 id
	Timestamp     int64    `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 发货时间戳
}

func (x *BatchGameDeliveryPackageReq) Reset() {
	*x = BatchGameDeliveryPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageReq) ProtoMessage() {}

func (x *BatchGameDeliveryPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageReq.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{10}
}

func (x *BatchGameDeliveryPackageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGameDeliveryPackageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchGameDeliveryPackageReq) GetPackageIds() []string {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

func (x *BatchGameDeliveryPackageReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BatchGameDeliveryPackageReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type BatchGameDeliveryPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*RewardItem   `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"` // 奖励
	Cards   []*TreasureCard `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"`     // 如果发奖产生宝藏奖励, 这个字段不为空
}

func (x *BatchGameDeliveryPackageRsp) Reset() {
	*x = BatchGameDeliveryPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageRsp) ProtoMessage() {}

func (x *BatchGameDeliveryPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageRsp.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{11}
}

func (x *BatchGameDeliveryPackageRsp) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *BatchGameDeliveryPackageRsp) GetCards() []*TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

// 宝藏卡结构
type TreasureCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureCardId uint32 `protobuf:"varint,1,opt,name=treasure_card_id,json=treasureCardId,proto3" json:"treasure_card_id,omitempty"` // 宝藏卡id
	IsDecompose    bool   `protobuf:"varint,2,opt,name=is_decompose,json=isDecompose,proto3" json:"is_decompose,omitempty"`            // 是否被分
	DecomposeNum   uint32 `protobuf:"varint,3,opt,name=decompose_num,json=decomposeNum,proto3" json:"decompose_num,omitempty"`         // 分解出多少碎片
}

func (x *TreasureCard) Reset() {
	*x = TreasureCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreasureCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreasureCard) ProtoMessage() {}

func (x *TreasureCard) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreasureCard.ProtoReflect.Descriptor instead.
func (*TreasureCard) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{12}
}

func (x *TreasureCard) GetTreasureCardId() uint32 {
	if x != nil {
		return x.TreasureCardId
	}
	return 0
}

func (x *TreasureCard) GetIsDecompose() bool {
	if x != nil {
		return x.IsDecompose
	}
	return false
}

func (x *TreasureCard) GetDecomposeNum() uint32 {
	if x != nil {
		return x.DecomposeNum
	}
	return 0
}

type BatchGameDeliveryPackageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId        string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageIds    []string `protobuf:"bytes,3,rep,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"`          // 礼包 id
	TransactionId string   `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // 唯一订单 id
	Timestamp     int64    `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 发货时间戳
}

func (x *BatchGameDeliveryPackageListReq) Reset() {
	*x = BatchGameDeliveryPackageListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageListReq) ProtoMessage() {}

func (x *BatchGameDeliveryPackageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageListReq.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{13}
}

func (x *BatchGameDeliveryPackageListReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGameDeliveryPackageListReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchGameDeliveryPackageListReq) GetPackageIds() []string {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

func (x *BatchGameDeliveryPackageListReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BatchGameDeliveryPackageListReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type GameDeliveryPackageRewards struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId string          `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"` // 礼包id
	Rewards   []*RewardItem   `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`                      // 奖励
	Cards     []*TreasureCard `protobuf:"bytes,3,rep,name=cards,proto3" json:"cards,omitempty"`                          // 如果发奖产生宝藏奖励这个字段不为空
}

func (x *GameDeliveryPackageRewards) Reset() {
	*x = GameDeliveryPackageRewards{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageRewards) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageRewards) ProtoMessage() {}

func (x *GameDeliveryPackageRewards) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageRewards.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageRewards) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{14}
}

func (x *GameDeliveryPackageRewards) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *GameDeliveryPackageRewards) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *GameDeliveryPackageRewards) GetCards() []*TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

type BatchGameDeliveryPackageListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*GameDeliveryPackageRewards `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"` // 按请求顺序返回
}

func (x *BatchGameDeliveryPackageListRsp) Reset() {
	*x = BatchGameDeliveryPackageListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageListRsp) ProtoMessage() {}

func (x *BatchGameDeliveryPackageListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageListRsp.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{15}
}

func (x *BatchGameDeliveryPackageListRsp) GetRewards() []*GameDeliveryPackageRewards {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type RetentionPopupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string              `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string              `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	StageType  event.CWSXStageType `protobuf:"varint,3,opt,name=stageType,proto3,enum=event.CWSXStageType" json:"stageType,omitempty"` // 关卡类型
	DoubleBuff bool                `protobuf:"varint,4,opt,name=doubleBuff,proto3" json:"doubleBuff,omitempty"`                        // 双倍buff
}

func (x *RetentionPopupReq) Reset() {
	*x = RetentionPopupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetentionPopupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetentionPopupReq) ProtoMessage() {}

func (x *RetentionPopupReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetentionPopupReq.ProtoReflect.Descriptor instead.
func (*RetentionPopupReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{16}
}

func (x *RetentionPopupReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RetentionPopupReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *RetentionPopupReq) GetStageType() event.CWSXStageType {
	if x != nil {
		return x.StageType
	}
	return event.CWSXStageType(0)
}

func (x *RetentionPopupReq) GetDoubleBuff() bool {
	if x != nil {
		return x.DoubleBuff
	}
	return false
}

type RetentionPopupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityType     GameActivityType   `protobuf:"varint,1,opt,name=activityType,proto3,enum=game_api.GameActivityType" json:"activityType,omitempty"`       // GameActivityType 活动类型
	ActivityStatus   GameActivityStatus `protobuf:"varint,2,opt,name=activityStatus,proto3,enum=game_api.GameActivityStatus" json:"activityStatus,omitempty"` // GameActivityStatus 活动状态
	CurrentSchedule  uint32             `protobuf:"varint,3,opt,name=currentSchedule,proto3" json:"currentSchedule,omitempty"`                                // 当前进度值
	NextSchedule     uint32             `protobuf:"varint,4,opt,name=nextSchedule,proto3" json:"nextSchedule,omitempty"`                                      // 下一阶段值
	LostGoodNums     uint32             `protobuf:"varint,5,opt,name=lostGoodNums,proto3" json:"lostGoodNums,omitempty"`                                      // 损失物品数量
	NextStageRewards []*RewardItem      `protobuf:"bytes,6,rep,name=NextStageRewards,proto3" json:"NextStageRewards,omitempty"`                               // 下阶段可获得奖励
	MapExt           map[string]string  `protobuf:"bytes,15,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RetentionPopupRsp) Reset() {
	*x = RetentionPopupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetentionPopupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetentionPopupRsp) ProtoMessage() {}

func (x *RetentionPopupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetentionPopupRsp.ProtoReflect.Descriptor instead.
func (*RetentionPopupRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{17}
}

func (x *RetentionPopupRsp) GetActivityType() GameActivityType {
	if x != nil {
		return x.ActivityType
	}
	return GameActivityType_Unknow
}

func (x *RetentionPopupRsp) GetActivityStatus() GameActivityStatus {
	if x != nil {
		return x.ActivityStatus
	}
	return GameActivityStatus_Default
}

func (x *RetentionPopupRsp) GetCurrentSchedule() uint32 {
	if x != nil {
		return x.CurrentSchedule
	}
	return 0
}

func (x *RetentionPopupRsp) GetNextSchedule() uint32 {
	if x != nil {
		return x.NextSchedule
	}
	return 0
}

func (x *RetentionPopupRsp) GetLostGoodNums() uint32 {
	if x != nil {
		return x.LostGoodNums
	}
	return 0
}

func (x *RetentionPopupRsp) GetNextStageRewards() []*RewardItem {
	if x != nil {
		return x.NextStageRewards
	}
	return nil
}

func (x *RetentionPopupRsp) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type CallbackStepBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId   string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	BuyRound uint32 `protobuf:"varint,3,opt,name=buyRound,proto3" json:"buyRound,omitempty"` // 本次闯关第几次购买
	BillId   string `protobuf:"bytes,4,opt,name=billId,proto3" json:"billId,omitempty"`      // 购买唯一id
	Step     int64  `protobuf:"varint,5,opt,name=step,proto3" json:"step,omitempty"`         // 免费步数
}

func (x *CallbackStepBuyReq) Reset() {
	*x = CallbackStepBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackStepBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackStepBuyReq) ProtoMessage() {}

func (x *CallbackStepBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackStepBuyReq.ProtoReflect.Descriptor instead.
func (*CallbackStepBuyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{18}
}

func (x *CallbackStepBuyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CallbackStepBuyReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CallbackStepBuyReq) GetBuyRound() uint32 {
	if x != nil {
		return x.BuyRound
	}
	return 0
}

func (x *CallbackStepBuyReq) GetBillId() string {
	if x != nil {
		return x.BillId
	}
	return ""
}

func (x *CallbackStepBuyReq) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

type CallbackStepBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CallbackStepBuyRsp) Reset() {
	*x = CallbackStepBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_api_game_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackStepBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackStepBuyRsp) ProtoMessage() {}

func (x *CallbackStepBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_api_game_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackStepBuyRsp.ProtoReflect.Descriptor instead.
func (*CallbackStepBuyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_api_game_api_proto_rawDescGZIP(), []int{19}
}

var File_pb_game_api_game_api_proto protoreflect.FileDescriptor

var file_pb_game_api_game_api_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x46, 0x0a, 0x0c,
	0x53, 0x65, 0x6e, 0x64, 0x48, 0x65, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x26, 0x0a, 0x0c, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x65, 0x61, 0x72,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x72, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x61, 0x72, 0x74, 0x73, 0x22, 0x3f, 0x0a, 0x0d,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x56, 0x0a,
	0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x75, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x63, 0x75, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6d,
	0x61, 0x78, 0x5f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x22, 0x96, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x91,
	0x01, 0x0a, 0x07, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x22, 0x67, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x22, 0xb0, 0x01, 0x0a, 0x14,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x4e,
	0x0a, 0x0d, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x27, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xee,
	0x01, 0x0a, 0x16, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12,
	0x2e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0x76, 0x0a, 0x16, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x63, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x7b, 0x0a,
	0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x05,
	0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x0c, 0x54,
	0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74,
	0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44,
	0x65, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x64, 0x65, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x22, 0xb7, 0x01,
	0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x99, 0x01, 0x0a, 0x1a, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61,
	0x72, 0x64, 0x73, 0x22, 0x61, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52, 0x07, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x57, 0x53, 0x58, 0x53, 0x74, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66, 0x22, 0xc9,
	0x03, 0x0a, 0x11, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75,
	0x70, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x65, 0x78, 0x74,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x73, 0x74,
	0x47, 0x6f, 0x6f, 0x64, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x6c, 0x6f, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x4e, 0x75, 0x6d, 0x73, 0x12, 0x40, 0x0a, 0x10,
	0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x10, 0x4e, 0x65,
	0x78, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x3f,
	0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x45,
	0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a,
	0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8a, 0x01, 0x0a, 0x12, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x65, 0x70, 0x42, 0x75, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x62, 0x75, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x62, 0x75, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62,
	0x69, 0x6c, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x53, 0x74, 0x65, 0x70, 0x42, 0x75, 0x79, 0x52, 0x73, 0x70, 0x2a, 0x67, 0x0a,
	0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x16, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x47,
	0x61, 0x6d, 0x65, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x10, 0x02, 0x2a, 0x52, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x61,
	0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x64, 0x53, 0x74, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x69, 0x64, 0x64,
	0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x10, 0x01, 0x2a, 0x39, 0x0a, 0x10, 0x47, 0x61,
	0x6d, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a,
	0x0a, 0x06, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x10, 0x02, 0x2a, 0x3d, 0x0a, 0x12, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x6e, 0x67, 0x6f,
	0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x6e, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x65, 0x64, 0x10, 0x02, 0x32, 0x96, 0x04, 0x0a, 0x07, 0x47, 0x61, 0x6d, 0x65, 0x41, 0x70, 0x69,
	0x12, 0x3b, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x65, 0x61, 0x72, 0x74, 0x12, 0x16, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x65, 0x61,
	0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x48, 0x65, 0x61, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a,
	0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x17, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x53, 0x0a,
	0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x12, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x59, 0x0a, 0x13, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x68, 0x0a,
	0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x74, 0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x29, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61,
	0x70, 0x69, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x29, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x42, 0x40, 0x5a,
	0x3e, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_api_game_api_proto_rawDescOnce sync.Once
	file_pb_game_api_game_api_proto_rawDescData = file_pb_game_api_game_api_proto_rawDesc
)

func file_pb_game_api_game_api_proto_rawDescGZIP() []byte {
	file_pb_game_api_game_api_proto_rawDescOnce.Do(func() {
		file_pb_game_api_game_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_api_game_api_proto_rawDescData)
	})
	return file_pb_game_api_game_api_proto_rawDescData
}

var file_pb_game_api_game_api_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pb_game_api_game_api_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_pb_game_api_game_api_proto_goTypes = []interface{}{
	(RewardItemType)(0),                     // 0: game_api.RewardItemType
	(GameDeliveryType)(0),                   // 1: game_api.GameDeliveryType
	(GameActivityType)(0),                   // 2: game_api.GameActivityType
	(GameActivityStatus)(0),                 // 3: game_api.GameActivityStatus
	(*SendHeartReq)(nil),                    // 4: game_api.SendHeartReq
	(*SendHeartRsp)(nil),                    // 5: game_api.SendHeartRsp
	(*QueryStageReq)(nil),                   // 6: game_api.QueryStageReq
	(*QueryStageRsp)(nil),                   // 7: game_api.QueryStageRsp
	(*RewardItem)(nil),                      // 8: game_api.RewardItem
	(*Package)(nil),                         // 9: game_api.Package
	(*BatchQueryPackageReq)(nil),            // 10: game_api.BatchQueryPackageReq
	(*BatchQueryPackageRsp)(nil),            // 11: game_api.BatchQueryPackageRsp
	(*GameDeliveryPackageReq)(nil),          // 12: game_api.GameDeliveryPackageReq
	(*GameDeliveryPackageRsp)(nil),          // 13: game_api.GameDeliveryPackageRsp
	(*BatchGameDeliveryPackageReq)(nil),     // 14: game_api.BatchGameDeliveryPackageReq
	(*BatchGameDeliveryPackageRsp)(nil),     // 15: game_api.BatchGameDeliveryPackageRsp
	(*TreasureCard)(nil),                    // 16: game_api.TreasureCard
	(*BatchGameDeliveryPackageListReq)(nil), // 17: game_api.BatchGameDeliveryPackageListReq
	(*GameDeliveryPackageRewards)(nil),      // 18: game_api.GameDeliveryPackageRewards
	(*BatchGameDeliveryPackageListRsp)(nil), // 19: game_api.BatchGameDeliveryPackageListRsp
	(*RetentionPopupReq)(nil),               // 20: game_api.RetentionPopupReq
	(*RetentionPopupRsp)(nil),               // 21: game_api.RetentionPopupRsp
	(*CallbackStepBuyReq)(nil),              // 22: game_api.CallbackStepBuyReq
	(*CallbackStepBuyRsp)(nil),              // 23: game_api.CallbackStepBuyRsp
	nil,                                     // 24: game_api.BatchQueryPackageRsp.PackagesEntry
	nil,                                     // 25: game_api.RetentionPopupRsp.MapExtEntry
	(event.CWSXStageType)(0),                // 26: event.CWSXStageType
}
var file_pb_game_api_game_api_proto_depIdxs = []int32{
	0,  // 0: game_api.RewardItem.type:type_name -> game_api.RewardItemType
	8,  // 1: game_api.Package.rewards:type_name -> game_api.RewardItem
	24, // 2: game_api.BatchQueryPackageRsp.packages:type_name -> game_api.BatchQueryPackageRsp.PackagesEntry
	1,  // 3: game_api.GameDeliveryPackageReq.type:type_name -> game_api.GameDeliveryType
	8,  // 4: game_api.GameDeliveryPackageRsp.rewards:type_name -> game_api.RewardItem
	16, // 5: game_api.GameDeliveryPackageRsp.cards:type_name -> game_api.TreasureCard
	8,  // 6: game_api.BatchGameDeliveryPackageRsp.rewards:type_name -> game_api.RewardItem
	16, // 7: game_api.BatchGameDeliveryPackageRsp.cards:type_name -> game_api.TreasureCard
	8,  // 8: game_api.GameDeliveryPackageRewards.rewards:type_name -> game_api.RewardItem
	16, // 9: game_api.GameDeliveryPackageRewards.cards:type_name -> game_api.TreasureCard
	18, // 10: game_api.BatchGameDeliveryPackageListRsp.rewards:type_name -> game_api.GameDeliveryPackageRewards
	26, // 11: game_api.RetentionPopupReq.stageType:type_name -> event.CWSXStageType
	2,  // 12: game_api.RetentionPopupRsp.activityType:type_name -> game_api.GameActivityType
	3,  // 13: game_api.RetentionPopupRsp.activityStatus:type_name -> game_api.GameActivityStatus
	8,  // 14: game_api.RetentionPopupRsp.NextStageRewards:type_name -> game_api.RewardItem
	25, // 15: game_api.RetentionPopupRsp.mapExt:type_name -> game_api.RetentionPopupRsp.MapExtEntry
	9,  // 16: game_api.BatchQueryPackageRsp.PackagesEntry.value:type_name -> game_api.Package
	4,  // 17: game_api.GameApi.SendHeart:input_type -> game_api.SendHeartReq
	6,  // 18: game_api.GameApi.QueryStage:input_type -> game_api.QueryStageReq
	10, // 19: game_api.GameApi.BatchQueryPackage:input_type -> game_api.BatchQueryPackageReq
	12, // 20: game_api.GameApi.GameDeliveryPackage:input_type -> game_api.GameDeliveryPackageReq
	14, // 21: game_api.GameApi.BatchGameDeliveryPackage:input_type -> game_api.BatchGameDeliveryPackageReq
	17, // 22: game_api.GameApi.BatchGameDeliveryPackageList:input_type -> game_api.BatchGameDeliveryPackageListReq
	5,  // 23: game_api.GameApi.SendHeart:output_type -> game_api.SendHeartRsp
	7,  // 24: game_api.GameApi.QueryStage:output_type -> game_api.QueryStageRsp
	11, // 25: game_api.GameApi.BatchQueryPackage:output_type -> game_api.BatchQueryPackageRsp
	13, // 26: game_api.GameApi.GameDeliveryPackage:output_type -> game_api.GameDeliveryPackageRsp
	15, // 27: game_api.GameApi.BatchGameDeliveryPackage:output_type -> game_api.BatchGameDeliveryPackageRsp
	19, // 28: game_api.GameApi.BatchGameDeliveryPackageList:output_type -> game_api.BatchGameDeliveryPackageListRsp
	23, // [23:29] is the sub-list for method output_type
	17, // [17:23] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_pb_game_api_game_api_proto_init() }
func file_pb_game_api_game_api_proto_init() {
	if File_pb_game_api_game_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_api_game_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendHeartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendHeartRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQueryPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQueryPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreasureCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageRewards); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetentionPopupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetentionPopupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackStepBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_api_game_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackStepBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_api_game_api_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_api_game_api_proto_goTypes,
		DependencyIndexes: file_pb_game_api_game_api_proto_depIdxs,
		EnumInfos:         file_pb_game_api_game_api_proto_enumTypes,
		MessageInfos:      file_pb_game_api_game_api_proto_msgTypes,
	}.Build()
	File_pb_game_api_game_api_proto = out.File
	file_pb_game_api_game_api_proto_rawDesc = nil
	file_pb_game_api_game_api_proto_goTypes = nil
	file_pb_game_api_game_api_proto_depIdxs = nil
}
