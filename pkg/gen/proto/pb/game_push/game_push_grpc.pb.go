// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_push/game_push.proto

package game_push

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	GamePush_SendGameRelationNotify_FullMethodName = "/game_push.GamePush/SendGameRelationNotify"
)

// GamePushClient is the client API for GamePush service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GamePushClient interface {
	// https://tapd.woa.com/Singbar_android/prong/stories/view/1010088931882595869
	// 发送push 和 im
	SendGameRelationNotify(ctx context.Context, in *SendGameRelationNotifyReq, opts ...grpc.CallOption) (*SendGameRelationNotifyRsp, error)
}

type gamePushClient struct {
	cc grpc.ClientConnInterface
}

func NewGamePushClient(cc grpc.ClientConnInterface) GamePushClient {
	return &gamePushClient{cc}
}

func (c *gamePushClient) SendGameRelationNotify(ctx context.Context, in *SendGameRelationNotifyReq, opts ...grpc.CallOption) (*SendGameRelationNotifyRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendGameRelationNotifyRsp)
	err := c.cc.Invoke(ctx, GamePush_SendGameRelationNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GamePushServer is the server API for GamePush service.
// All implementations should embed UnimplementedGamePushServer
// for forward compatibility
type GamePushServer interface {
	// https://tapd.woa.com/Singbar_android/prong/stories/view/1010088931882595869
	// 发送push 和 im
	SendGameRelationNotify(context.Context, *SendGameRelationNotifyReq) (*SendGameRelationNotifyRsp, error)
}

// UnimplementedGamePushServer should be embedded to have forward compatible implementations.
type UnimplementedGamePushServer struct {
}

func (UnimplementedGamePushServer) SendGameRelationNotify(context.Context, *SendGameRelationNotifyReq) (*SendGameRelationNotifyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendGameRelationNotify not implemented")
}

// UnsafeGamePushServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GamePushServer will
// result in compilation errors.
type UnsafeGamePushServer interface {
	mustEmbedUnimplementedGamePushServer()
}

func RegisterGamePushServer(s grpc.ServiceRegistrar, srv GamePushServer) {
	s.RegisterService(&GamePush_ServiceDesc, srv)
}

func _GamePush_SendGameRelationNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendGameRelationNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GamePushServer).SendGameRelationNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GamePush_SendGameRelationNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GamePushServer).SendGameRelationNotify(ctx, req.(*SendGameRelationNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GamePush_ServiceDesc is the grpc.ServiceDesc for GamePush service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GamePush_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_push.GamePush",
	HandlerType: (*GamePushServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendGameRelationNotify",
			Handler:    _GamePush_SendGameRelationNotify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_push/game_push.proto",
}
