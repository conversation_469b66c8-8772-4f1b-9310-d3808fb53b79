{"swagger": "2.0", "info": {"title": "pb/game_push/game_push.proto", "version": "version not set"}, "tags": [{"name": "GamePush"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_push.GamePush/SendGameRelationNotify": {"post": {"summary": "https://tapd.woa.com/Singbar_android/prong/stories/view/1010088931882595869\n发送push 和 im", "operationId": "GamePush_SendGameRelationNotify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_pushSendGameRelationNotifyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_pushSendGameRelationNotifyReq"}}], "tags": ["GamePush"]}}}, "definitions": {"game_pushMailInfo": {"type": "object", "properties": {"content": {"type": "string"}, "attachment": {"type": "object", "additionalProperties": {"type": "string"}}}, "title": "私信"}, "game_pushPushInfo": {"type": "object", "properties": {"attach": {"type": "object", "additionalProperties": {"type": "string"}, "title": "附加内容 attach"}, "flowConfigId": {"type": "string", "title": "push中台配置id"}}, "title": "push消息"}, "game_pushSendGameRelationNotifyReq": {"type": "object", "properties": {"openId": {"type": "string", "title": "用户ID"}, "appId": {"type": "string", "title": "小游戏AppId"}, "push": {"$ref": "#/definitions/game_pushPushInfo"}, "mail": {"$ref": "#/definitions/game_pushMailInfo"}, "fromOpenId": {"type": "string"}}}, "game_pushSendGameRelationNotifyRsp": {"type": "object", "properties": {"iRet": {"type": "string", "format": "int64"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}