// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_push/game_push_comm.proto

package game_push

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// game_push:limit:%{uid}
type PushLimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MpApp   map[string]int32 `protobuf:"bytes,1,rep,name=mpApp,proto3" json:"mpApp,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`    // 每人每天最多收到一个游戏id X条通知，X后台可配置
	MpUser  map[int64]int32  `protobuf:"bytes,2,rep,name=mpUser,proto3" json:"mpUser,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 每人每天最多收到同一个uid通知 Y次，Y后台可配置
	StrDate string           `protobuf:"bytes,3,opt,name=strDate,proto3" json:"strDate,omitempty"`                                                                                         // 日期
	UTs     int64            `protobuf:"varint,4,opt,name=uTs,proto3" json:"uTs,omitempty"`                                                                                                // 上一次发送消息的时间间隔
}

func (x *PushLimit) Reset() {
	*x = PushLimit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushLimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLimit) ProtoMessage() {}

func (x *PushLimit) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLimit.ProtoReflect.Descriptor instead.
func (*PushLimit) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_comm_proto_rawDescGZIP(), []int{0}
}

func (x *PushLimit) GetMpApp() map[string]int32 {
	if x != nil {
		return x.MpApp
	}
	return nil
}

func (x *PushLimit) GetMpUser() map[int64]int32 {
	if x != nil {
		return x.MpUser
	}
	return nil
}

func (x *PushLimit) GetStrDate() string {
	if x != nil {
		return x.StrDate
	}
	return ""
}

func (x *PushLimit) GetUTs() int64 {
	if x != nil {
		return x.UTs
	}
	return 0
}

type LimitInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MaxNum uint32 `protobuf:"varint,2,opt,name=max_num,json=maxNum,proto3" json:"max_num,omitempty"` // 每人每天最多收到游戏id x条通知
}

func (x *LimitInfo) Reset() {
	*x = LimitInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitInfo) ProtoMessage() {}

func (x *LimitInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitInfo.ProtoReflect.Descriptor instead.
func (*LimitInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_comm_proto_rawDescGZIP(), []int{1}
}

func (x *LimitInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *LimitInfo) GetMaxNum() uint32 {
	if x != nil {
		return x.MaxNum
	}
	return 0
}

// game_push:limit_info
type LimitInfoCkv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VecLimit []*LimitInfo `protobuf:"bytes,1,rep,name=vecLimit,proto3" json:"vecLimit,omitempty"`
	UTs      int32        `protobuf:"varint,2,opt,name=uTs,proto3" json:"uTs,omitempty"`
}

func (x *LimitInfoCkv) Reset() {
	*x = LimitInfoCkv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_comm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitInfoCkv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitInfoCkv) ProtoMessage() {}

func (x *LimitInfoCkv) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_comm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitInfoCkv.ProtoReflect.Descriptor instead.
func (*LimitInfoCkv) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_comm_proto_rawDescGZIP(), []int{2}
}

func (x *LimitInfoCkv) GetVecLimit() []*LimitInfo {
	if x != nil {
		return x.VecLimit
	}
	return nil
}

func (x *LimitInfoCkv) GetUTs() int32 {
	if x != nil {
		return x.UTs
	}
	return 0
}

var File_pb_game_push_game_push_comm_proto protoreflect.FileDescriptor

var file_pb_game_push_game_push_comm_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x22, 0x9d,
	0x02, 0x0a, 0x09, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x35, 0x0a, 0x05,
	0x6d, 0x70, 0x41, 0x70, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x2e, 0x4d, 0x70, 0x41, 0x70, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x6d, 0x70,
	0x41, 0x70, 0x70, 0x12, 0x38, 0x0a, 0x06, 0x6d, 0x70, 0x55, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x2e, 0x4d, 0x70, 0x55, 0x73, 0x65, 0x72,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x70, 0x55, 0x73, 0x65, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x54, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x75, 0x54, 0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x4d, 0x70, 0x41,
	0x70, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x70, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3b,
	0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x22, 0x52, 0x0a, 0x0c, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6b, 0x76, 0x12, 0x30, 0x0a, 0x08, 0x76,
	0x65, 0x63, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x65, 0x63, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x54, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x75, 0x54, 0x73, 0x42,
	0x41, 0x5a, 0x3f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_push_game_push_comm_proto_rawDescOnce sync.Once
	file_pb_game_push_game_push_comm_proto_rawDescData = file_pb_game_push_game_push_comm_proto_rawDesc
)

func file_pb_game_push_game_push_comm_proto_rawDescGZIP() []byte {
	file_pb_game_push_game_push_comm_proto_rawDescOnce.Do(func() {
		file_pb_game_push_game_push_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_push_game_push_comm_proto_rawDescData)
	})
	return file_pb_game_push_game_push_comm_proto_rawDescData
}

var file_pb_game_push_game_push_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_pb_game_push_game_push_comm_proto_goTypes = []interface{}{
	(*PushLimit)(nil),    // 0: game_push.PushLimit
	(*LimitInfo)(nil),    // 1: game_push.LimitInfo
	(*LimitInfoCkv)(nil), // 2: game_push.LimitInfoCkv
	nil,                  // 3: game_push.PushLimit.MpAppEntry
	nil,                  // 4: game_push.PushLimit.MpUserEntry
}
var file_pb_game_push_game_push_comm_proto_depIdxs = []int32{
	3, // 0: game_push.PushLimit.mpApp:type_name -> game_push.PushLimit.MpAppEntry
	4, // 1: game_push.PushLimit.mpUser:type_name -> game_push.PushLimit.MpUserEntry
	1, // 2: game_push.LimitInfoCkv.vecLimit:type_name -> game_push.LimitInfo
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_game_push_game_push_comm_proto_init() }
func file_pb_game_push_game_push_comm_proto_init() {
	if File_pb_game_push_game_push_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_push_game_push_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushLimit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_push_game_push_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_push_game_push_comm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitInfoCkv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_push_game_push_comm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_push_game_push_comm_proto_goTypes,
		DependencyIndexes: file_pb_game_push_game_push_comm_proto_depIdxs,
		MessageInfos:      file_pb_game_push_game_push_comm_proto_msgTypes,
	}.Build()
	File_pb_game_push_game_push_comm_proto = out.File
	file_pb_game_push_game_push_comm_proto_rawDesc = nil
	file_pb_game_push_game_push_comm_proto_goTypes = nil
	file_pb_game_push_game_push_comm_proto_depIdxs = nil
}
