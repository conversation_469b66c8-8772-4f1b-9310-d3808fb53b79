// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_push/game_push.proto

package game_push

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EmRtnCode int32

const (
	EmRtnCode_RT_SUCC  EmRtnCode = 0
	EmRtnCode_RT_LIMIT EmRtnCode = 1
)

// Enum value maps for EmRtnCode.
var (
	EmRtnCode_name = map[int32]string{
		0: "RT_SUCC",
		1: "RT_LIMIT",
	}
	EmRtnCode_value = map[string]int32{
		"RT_SUCC":  0,
		"RT_LIMIT": 1,
	}
)

func (x EmRtnCode) Enum() *EmRtnCode {
	p := new(EmRtnCode)
	*p = x
	return p
}

func (x EmRtnCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EmRtnCode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_push_game_push_proto_enumTypes[0].Descriptor()
}

func (EmRtnCode) Type() protoreflect.EnumType {
	return &file_pb_game_push_game_push_proto_enumTypes[0]
}

func (x EmRtnCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EmRtnCode.Descriptor instead.
func (EmRtnCode) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_proto_rawDescGZIP(), []int{0}
}

// push消息
type PushInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 附加内容 attach
	Attach       map[string]string `protobuf:"bytes,1,rep,name=attach,proto3" json:"attach,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FlowConfigId string            `protobuf:"bytes,2,opt,name=flowConfigId,proto3" json:"flowConfigId,omitempty"` // push中台配置id
}

func (x *PushInfo) Reset() {
	*x = PushInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushInfo) ProtoMessage() {}

func (x *PushInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushInfo.ProtoReflect.Descriptor instead.
func (*PushInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_proto_rawDescGZIP(), []int{0}
}

func (x *PushInfo) GetAttach() map[string]string {
	if x != nil {
		return x.Attach
	}
	return nil
}

func (x *PushInfo) GetFlowConfigId() string {
	if x != nil {
		return x.FlowConfigId
	}
	return ""
}

// 私信
type MailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content    string            `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Attachment map[string]string `protobuf:"bytes,2,rep,name=attachment,proto3" json:"attachment,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MailInfo) Reset() {
	*x = MailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailInfo) ProtoMessage() {}

func (x *MailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailInfo.ProtoReflect.Descriptor instead.
func (*MailInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_proto_rawDescGZIP(), []int{1}
}

func (x *MailInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MailInfo) GetAttachment() map[string]string {
	if x != nil {
		return x.Attachment
	}
	return nil
}

type SendGameRelationNotifyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId     string    `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"` // 用户ID
	AppId      string    `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`   // 小游戏AppId
	Push       *PushInfo `protobuf:"bytes,3,opt,name=push,proto3" json:"push,omitempty"`
	Mail       *MailInfo `protobuf:"bytes,4,opt,name=mail,proto3" json:"mail,omitempty"`
	FromOpenId string    `protobuf:"bytes,5,opt,name=fromOpenId,proto3" json:"fromOpenId,omitempty"`
}

func (x *SendGameRelationNotifyReq) Reset() {
	*x = SendGameRelationNotifyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGameRelationNotifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGameRelationNotifyReq) ProtoMessage() {}

func (x *SendGameRelationNotifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGameRelationNotifyReq.ProtoReflect.Descriptor instead.
func (*SendGameRelationNotifyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_proto_rawDescGZIP(), []int{2}
}

func (x *SendGameRelationNotifyReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SendGameRelationNotifyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SendGameRelationNotifyReq) GetPush() *PushInfo {
	if x != nil {
		return x.Push
	}
	return nil
}

func (x *SendGameRelationNotifyReq) GetMail() *MailInfo {
	if x != nil {
		return x.Mail
	}
	return nil
}

func (x *SendGameRelationNotifyReq) GetFromOpenId() string {
	if x != nil {
		return x.FromOpenId
	}
	return ""
}

type SendGameRelationNotifyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IRet int64 `protobuf:"varint,1,opt,name=iRet,proto3" json:"iRet,omitempty"`
}

func (x *SendGameRelationNotifyRsp) Reset() {
	*x = SendGameRelationNotifyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_push_game_push_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendGameRelationNotifyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendGameRelationNotifyRsp) ProtoMessage() {}

func (x *SendGameRelationNotifyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_push_game_push_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendGameRelationNotifyRsp.ProtoReflect.Descriptor instead.
func (*SendGameRelationNotifyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_push_game_push_proto_rawDescGZIP(), []int{3}
}

func (x *SendGameRelationNotifyRsp) GetIRet() int64 {
	if x != nil {
		return x.IRet
	}
	return 0
}

var File_pb_game_push_game_push_proto protoreflect.FileDescriptor

var file_pb_game_push_game_push_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x22, 0xa2, 0x01, 0x0a, 0x08, 0x50, 0x75,
	0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x12,
	0x22, 0x0a, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa8,
	0x01, 0x0a, 0x08, 0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbb, 0x01, 0x0a, 0x19, 0x53, 0x65,
	0x6e, 0x64, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x75, 0x73, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x75, 0x73, 0x68, 0x12, 0x27,
	0x0a, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f,
	0x6d, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x2f, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x47,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x52, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x69, 0x52, 0x65, 0x74, 0x2a, 0x26, 0x0a, 0x09, 0x65, 0x6d, 0x52, 0x74,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x01,
	0x32, 0x70, 0x0a, 0x08, 0x47, 0x61, 0x6d, 0x65, 0x50, 0x75, 0x73, 0x68, 0x12, 0x64, 0x0a, 0x16,
	0x53, 0x65, 0x6e, 0x64, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x24, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x47, 0x61, 0x6d,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52,
	0x73, 0x70, 0x42, 0x41, 0x5a, 0x3f, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_push_game_push_proto_rawDescOnce sync.Once
	file_pb_game_push_game_push_proto_rawDescData = file_pb_game_push_game_push_proto_rawDesc
)

func file_pb_game_push_game_push_proto_rawDescGZIP() []byte {
	file_pb_game_push_game_push_proto_rawDescOnce.Do(func() {
		file_pb_game_push_game_push_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_push_game_push_proto_rawDescData)
	})
	return file_pb_game_push_game_push_proto_rawDescData
}

var file_pb_game_push_game_push_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_game_push_game_push_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_game_push_game_push_proto_goTypes = []interface{}{
	(EmRtnCode)(0),                    // 0: game_push.emRtnCode
	(*PushInfo)(nil),                  // 1: game_push.PushInfo
	(*MailInfo)(nil),                  // 2: game_push.MailInfo
	(*SendGameRelationNotifyReq)(nil), // 3: game_push.SendGameRelationNotifyReq
	(*SendGameRelationNotifyRsp)(nil), // 4: game_push.SendGameRelationNotifyRsp
	nil,                               // 5: game_push.PushInfo.AttachEntry
	nil,                               // 6: game_push.MailInfo.AttachmentEntry
}
var file_pb_game_push_game_push_proto_depIdxs = []int32{
	5, // 0: game_push.PushInfo.attach:type_name -> game_push.PushInfo.AttachEntry
	6, // 1: game_push.MailInfo.attachment:type_name -> game_push.MailInfo.AttachmentEntry
	1, // 2: game_push.SendGameRelationNotifyReq.push:type_name -> game_push.PushInfo
	2, // 3: game_push.SendGameRelationNotifyReq.mail:type_name -> game_push.MailInfo
	3, // 4: game_push.GamePush.SendGameRelationNotify:input_type -> game_push.SendGameRelationNotifyReq
	4, // 5: game_push.GamePush.SendGameRelationNotify:output_type -> game_push.SendGameRelationNotifyRsp
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pb_game_push_game_push_proto_init() }
func file_pb_game_push_game_push_proto_init() {
	if File_pb_game_push_game_push_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_push_game_push_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_push_game_push_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_push_game_push_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGameRelationNotifyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_push_game_push_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendGameRelationNotifyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_push_game_push_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_push_game_push_proto_goTypes,
		DependencyIndexes: file_pb_game_push_game_push_proto_depIdxs,
		EnumInfos:         file_pb_game_push_game_push_proto_enumTypes,
		MessageInfos:      file_pb_game_push_game_push_proto_msgTypes,
	}.Build()
	File_pb_game_push_game_push_proto = out.File
	file_pb_game_push_game_push_proto_rawDesc = nil
	file_pb_game_push_game_push_proto_goTypes = nil
	file_pb_game_push_game_push_proto_depIdxs = nil
}
