// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_invitation/server/server.proto

package server

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/cwsx_game_invitation/common"
	storage "kugou_adapter_service/pkg/gen/proto/pb/cwsx_game_invitation/storage"
	device "kugou_adapter_service/pkg/gen/proto/pb/device"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 邀请列表
type InvitateListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId   string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Passback uint32 `protobuf:"varint,3,opt,name=passback,proto3" json:"passback,omitempty"` // 分页参数
}

func (x *InvitateListReq) Reset() {
	*x = InvitateListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateListReq) ProtoMessage() {}

func (x *InvitateListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateListReq.ProtoReflect.Descriptor instead.
func (*InvitateListReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{0}
}

func (x *InvitateListReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *InvitateListReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *InvitateListReq) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

type InvitateListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lists    []*InvitateListRsp_Item `protobuf:"bytes,1,rep,name=lists,proto3" json:"lists,omitempty"`        // 邀请列表
	Passback uint32                  `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"` // 分页参数
	HasNext  bool                    `protobuf:"varint,3,opt,name=hasNext,proto3" json:"hasNext,omitempty"`   // 是否有下一页
}

func (x *InvitateListRsp) Reset() {
	*x = InvitateListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateListRsp) ProtoMessage() {}

func (x *InvitateListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateListRsp.ProtoReflect.Descriptor instead.
func (*InvitateListRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{1}
}

func (x *InvitateListRsp) GetLists() []*InvitateListRsp_Item {
	if x != nil {
		return x.Lists
	}
	return nil
}

func (x *InvitateListRsp) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *InvitateListRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

// 助力领奖
type HelpingClaimReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string         `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string         `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	InviteeOpenId string         `protobuf:"bytes,3,opt,name=inviteeOpenId,proto3" json:"inviteeOpenId,omitempty"` // 被邀请人openId
	Device        *device.Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *HelpingClaimReq) Reset() {
	*x = HelpingClaimReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingClaimReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingClaimReq) ProtoMessage() {}

func (x *HelpingClaimReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingClaimReq.ProtoReflect.Descriptor instead.
func (*HelpingClaimReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{2}
}

func (x *HelpingClaimReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *HelpingClaimReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *HelpingClaimReq) GetInviteeOpenId() string {
	if x != nil {
		return x.InviteeOpenId
	}
	return ""
}

func (x *HelpingClaimReq) GetDevice() *device.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type HelpingClaimRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleId int64 `protobuf:"varint,1,opt,name=bundleId,proto3" json:"bundleId,omitempty"` // 礼包id
}

func (x *HelpingClaimRsp) Reset() {
	*x = HelpingClaimRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingClaimRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingClaimRsp) ProtoMessage() {}

func (x *HelpingClaimRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingClaimRsp.ProtoReflect.Descriptor instead.
func (*HelpingClaimRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{3}
}

func (x *HelpingClaimRsp) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

// 邀请记录
type InvitateRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ActivityId uint32 `protobuf:"varint,3,opt,name=activityId,proto3" json:"activityId,omitempty"` // 活动id
	Passback   uint32 `protobuf:"varint,4,opt,name=passback,proto3" json:"passback,omitempty"`     // 分页参数
}

func (x *InvitateRecordReq) Reset() {
	*x = InvitateRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRecordReq) ProtoMessage() {}

func (x *InvitateRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRecordReq.ProtoReflect.Descriptor instead.
func (*InvitateRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{4}
}

func (x *InvitateRecordReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *InvitateRecordReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *InvitateRecordReq) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *InvitateRecordReq) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

type InvitateRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordList []*storage.InvitateRecord `protobuf:"bytes,1,rep,name=recordList,proto3" json:"recordList,omitempty"` // 记录列表-默认最新100条
	Passback   uint32                    `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"`    // 分页参数
	HasNext    bool                      `protobuf:"varint,3,opt,name=hasNext,proto3" json:"hasNext,omitempty"`      // 是否有下一页
}

func (x *InvitateRecordRsp) Reset() {
	*x = InvitateRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRecordRsp) ProtoMessage() {}

func (x *InvitateRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRecordRsp.ProtoReflect.Descriptor instead.
func (*InvitateRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{5}
}

func (x *InvitateRecordRsp) GetRecordList() []*storage.InvitateRecord {
	if x != nil {
		return x.RecordList
	}
	return nil
}

func (x *InvitateRecordRsp) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *InvitateRecordRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

type InviteCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ActivityId uint32 `protobuf:"varint,3,opt,name=activityId,proto3" json:"activityId,omitempty"` // 活动id
}

func (x *InviteCountReq) Reset() {
	*x = InviteCountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteCountReq) ProtoMessage() {}

func (x *InviteCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteCountReq.ProtoReflect.Descriptor instead.
func (*InviteCountReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{6}
}

func (x *InviteCountReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *InviteCountReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *InviteCountReq) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

type InviteCountRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvitationClaimed uint32 `protobuf:"varint,1,opt,name=invitationClaimed,proto3" json:"invitationClaimed,omitempty"` // 已领取
	InvitationUnclaim uint32 `protobuf:"varint,2,opt,name=invitationUnclaim,proto3" json:"invitationUnclaim,omitempty"` // 未领取
}

func (x *InviteCountRsp) Reset() {
	*x = InviteCountRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviteCountRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteCountRsp) ProtoMessage() {}

func (x *InviteCountRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteCountRsp.ProtoReflect.Descriptor instead.
func (*InviteCountRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{7}
}

func (x *InviteCountRsp) GetInvitationClaimed() uint32 {
	if x != nil {
		return x.InvitationClaimed
	}
	return 0
}

func (x *InviteCountRsp) GetInvitationUnclaim() uint32 {
	if x != nil {
		return x.InvitationUnclaim
	}
	return 0
}

// 助力成功
type HelpingSuccReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *HelpingSuccReq) Reset() {
	*x = HelpingSuccReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingSuccReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingSuccReq) ProtoMessage() {}

func (x *HelpingSuccReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingSuccReq.ProtoReflect.Descriptor instead.
func (*HelpingSuccReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{8}
}

func (x *HelpingSuccReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *HelpingSuccReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type HelpingSuccRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterOpenId string `protobuf:"bytes,1,opt,name=inviterOpenId,proto3" json:"inviterOpenId,omitempty"` // 邀请人openId
	BundleId      int64  `protobuf:"varint,2,opt,name=bundleId,proto3" json:"bundleId,omitempty"`          // 礼包id
}

func (x *HelpingSuccRsp) Reset() {
	*x = HelpingSuccRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingSuccRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingSuccRsp) ProtoMessage() {}

func (x *HelpingSuccRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingSuccRsp.ProtoReflect.Descriptor instead.
func (*HelpingSuccRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{9}
}

func (x *HelpingSuccRsp) GetInviterOpenId() string {
	if x != nil {
		return x.InviterOpenId
	}
	return ""
}

func (x *HelpingSuccRsp) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

type StageSuccReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *StageSuccReq) Reset() {
	*x = StageSuccReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageSuccReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageSuccReq) ProtoMessage() {}

func (x *StageSuccReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageSuccReq.ProtoReflect.Descriptor instead.
func (*StageSuccReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{10}
}

func (x *StageSuccReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *StageSuccReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type StageSuccRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StageSuccRsp) Reset() {
	*x = StageSuccRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageSuccRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageSuccRsp) ProtoMessage() {}

func (x *StageSuccRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageSuccRsp.ProtoReflect.Descriptor instead.
func (*StageSuccRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{11}
}

type InvitateListRsp_Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Invitee string                `protobuf:"bytes,1,opt,name=invitee,proto3" json:"invitee,omitempty"`
	Status  common.InvitateStatus `protobuf:"varint,2,opt,name=status,proto3,enum=cwsx_game_invitation.common.InvitateStatus" json:"status,omitempty"`
}

func (x *InvitateListRsp_Item) Reset() {
	*x = InvitateListRsp_Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateListRsp_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateListRsp_Item) ProtoMessage() {}

func (x *InvitateListRsp_Item) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateListRsp_Item.ProtoReflect.Descriptor instead.
func (*InvitateListRsp_Item) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{1, 0}
}

func (x *InvitateListRsp_Item) GetInvitee() string {
	if x != nil {
		return x.Invitee
	}
	return ""
}

func (x *InvitateListRsp_Item) GetStatus() common.InvitateStatus {
	if x != nil {
		return x.Status
	}
	return common.InvitateStatus(0)
}

type InvitateRecordRsp_Record struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviteeOpenId string `protobuf:"bytes,1,opt,name=inviteeOpenId,proto3" json:"inviteeOpenId,omitempty"` // 被邀请人openId
	Status        uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`              // 邀请状态 见 RecordStatus
	BundleId      int64  `protobuf:"varint,3,opt,name=bundleId,proto3" json:"bundleId,omitempty"`          // 礼包id
}

func (x *InvitateRecordRsp_Record) Reset() {
	*x = InvitateRecordRsp_Record{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRecordRsp_Record) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRecordRsp_Record) ProtoMessage() {}

func (x *InvitateRecordRsp_Record) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_server_server_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRecordRsp_Record.ProtoReflect.Descriptor instead.
func (*InvitateRecordRsp_Record) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP(), []int{5, 0}
}

func (x *InvitateRecordRsp_Record) GetInviteeOpenId() string {
	if x != nil {
		return x.InviteeOpenId
	}
	return ""
}

func (x *InvitateRecordRsp_Record) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InvitateRecordRsp_Record) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

var File_pb_cwsx_game_invitation_server_server_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_invitation_server_server_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x2b, 0x70, 0x62, 0x2f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x62, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c,
	0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5b,
	0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xf7, 0x01, 0x0a, 0x0f,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x47, 0x0a, 0x05, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x1a, 0x65,
	0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65,
	0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2b, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e,
	0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x06, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x2d, 0x0a, 0x0f, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62,
	0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62,
	0x61, 0x63, 0x6b, 0x22, 0xfb, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0a, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62,
	0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x1a, 0x62, 0x0a,
	0x06, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0x5e, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x22, 0x6c, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x65,
	0x64, 0x12, 0x2c, 0x0a, 0x11, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x22,
	0x3e, 0x0a, 0x0e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22,
	0x52, 0x0a, 0x0e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x52, 0x73,
	0x70, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x63, 0x63,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x63, 0x63, 0x52, 0x73,
	0x70, 0x32, 0xd9, 0x05, 0x0a, 0x15, 0x43, 0x77, 0x73, 0x78, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x76, 0x72, 0x12, 0x6a, 0x0a, 0x0c, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x6a, 0x0a, 0x0c, 0x48, 0x65, 0x6c, 0x70, 0x69,
	0x6e, 0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x2c, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x52, 0x73, 0x70, 0x12, 0x70, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x2e, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x67, 0x0a, 0x0b, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x2b, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x67,
	0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x12, 0x2b, 0x2e,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x65, 0x6c, 0x70,
	0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x2b, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67,
	0x53, 0x75, 0x63, 0x63, 0x52, 0x73, 0x70, 0x12, 0x61, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x53, 0x75, 0x63, 0x63, 0x12, 0x29, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x63, 0x63, 0x52, 0x65, 0x71, 0x1a,
	0x29, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x53, 0x75, 0x63, 0x63, 0x52, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0d, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x69, 0x6e,
	0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x42, 0x53, 0x5a,
	0x51, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_invitation_server_server_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_invitation_server_server_proto_rawDescData = file_pb_cwsx_game_invitation_server_server_proto_rawDesc
)

func file_pb_cwsx_game_invitation_server_server_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_invitation_server_server_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_invitation_server_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_invitation_server_server_proto_rawDescData)
	})
	return file_pb_cwsx_game_invitation_server_server_proto_rawDescData
}

var file_pb_cwsx_game_invitation_server_server_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_pb_cwsx_game_invitation_server_server_proto_goTypes = []interface{}{
	(*InvitateListReq)(nil),          // 0: cwsx_game_invitation_server.InvitateListReq
	(*InvitateListRsp)(nil),          // 1: cwsx_game_invitation_server.InvitateListRsp
	(*HelpingClaimReq)(nil),          // 2: cwsx_game_invitation_server.HelpingClaimReq
	(*HelpingClaimRsp)(nil),          // 3: cwsx_game_invitation_server.HelpingClaimRsp
	(*InvitateRecordReq)(nil),        // 4: cwsx_game_invitation_server.InvitateRecordReq
	(*InvitateRecordRsp)(nil),        // 5: cwsx_game_invitation_server.InvitateRecordRsp
	(*InviteCountReq)(nil),           // 6: cwsx_game_invitation_server.InviteCountReq
	(*InviteCountRsp)(nil),           // 7: cwsx_game_invitation_server.InviteCountRsp
	(*HelpingSuccReq)(nil),           // 8: cwsx_game_invitation_server.HelpingSuccReq
	(*HelpingSuccRsp)(nil),           // 9: cwsx_game_invitation_server.HelpingSuccRsp
	(*StageSuccReq)(nil),             // 10: cwsx_game_invitation_server.StageSuccReq
	(*StageSuccRsp)(nil),             // 11: cwsx_game_invitation_server.StageSuccRsp
	(*InvitateListRsp_Item)(nil),     // 12: cwsx_game_invitation_server.InvitateListRsp.Item
	(*InvitateRecordRsp_Record)(nil), // 13: cwsx_game_invitation_server.InvitateRecordRsp.Record
	(*device.Device)(nil),            // 14: device.Device
	(*storage.InvitateRecord)(nil),   // 15: cwsx_game_invitation.storage.InvitateRecord
	(common.InvitateStatus)(0),       // 16: cwsx_game_invitation.common.InvitateStatus
	(*inlet.ActivityStateReq)(nil),   // 17: inlet.ActivityStateReq
	(*inlet.ActivityStateRsp)(nil),   // 18: inlet.ActivityStateRsp
}
var file_pb_cwsx_game_invitation_server_server_proto_depIdxs = []int32{
	12, // 0: cwsx_game_invitation_server.InvitateListRsp.lists:type_name -> cwsx_game_invitation_server.InvitateListRsp.Item
	14, // 1: cwsx_game_invitation_server.HelpingClaimReq.device:type_name -> device.Device
	15, // 2: cwsx_game_invitation_server.InvitateRecordRsp.recordList:type_name -> cwsx_game_invitation.storage.InvitateRecord
	16, // 3: cwsx_game_invitation_server.InvitateListRsp.Item.status:type_name -> cwsx_game_invitation.common.InvitateStatus
	0,  // 4: cwsx_game_invitation_server.CwsxGameInvitationSvr.InvitateList:input_type -> cwsx_game_invitation_server.InvitateListReq
	2,  // 5: cwsx_game_invitation_server.CwsxGameInvitationSvr.HelpingClaim:input_type -> cwsx_game_invitation_server.HelpingClaimReq
	4,  // 6: cwsx_game_invitation_server.CwsxGameInvitationSvr.InvitateRecord:input_type -> cwsx_game_invitation_server.InvitateRecordReq
	6,  // 7: cwsx_game_invitation_server.CwsxGameInvitationSvr.InviteCount:input_type -> cwsx_game_invitation_server.InviteCountReq
	8,  // 8: cwsx_game_invitation_server.CwsxGameInvitationSvr.HelpingSucc:input_type -> cwsx_game_invitation_server.HelpingSuccReq
	10, // 9: cwsx_game_invitation_server.CwsxGameInvitationSvr.StageSucc:input_type -> cwsx_game_invitation_server.StageSuccReq
	17, // 10: cwsx_game_invitation_server.CwsxGameInvitationSvr.ActivityState:input_type -> inlet.ActivityStateReq
	1,  // 11: cwsx_game_invitation_server.CwsxGameInvitationSvr.InvitateList:output_type -> cwsx_game_invitation_server.InvitateListRsp
	3,  // 12: cwsx_game_invitation_server.CwsxGameInvitationSvr.HelpingClaim:output_type -> cwsx_game_invitation_server.HelpingClaimRsp
	5,  // 13: cwsx_game_invitation_server.CwsxGameInvitationSvr.InvitateRecord:output_type -> cwsx_game_invitation_server.InvitateRecordRsp
	7,  // 14: cwsx_game_invitation_server.CwsxGameInvitationSvr.InviteCount:output_type -> cwsx_game_invitation_server.InviteCountRsp
	9,  // 15: cwsx_game_invitation_server.CwsxGameInvitationSvr.HelpingSucc:output_type -> cwsx_game_invitation_server.HelpingSuccRsp
	11, // 16: cwsx_game_invitation_server.CwsxGameInvitationSvr.StageSucc:output_type -> cwsx_game_invitation_server.StageSuccRsp
	18, // 17: cwsx_game_invitation_server.CwsxGameInvitationSvr.ActivityState:output_type -> inlet.ActivityStateRsp
	11, // [11:18] is the sub-list for method output_type
	4,  // [4:11] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_invitation_server_server_proto_init() }
func file_pb_cwsx_game_invitation_server_server_proto_init() {
	if File_pb_cwsx_game_invitation_server_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingClaimReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingClaimRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InviteCountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InviteCountRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingSuccReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingSuccRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageSuccReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageSuccRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateListRsp_Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_server_server_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRecordRsp_Record); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_invitation_server_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_cwsx_game_invitation_server_server_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_invitation_server_server_proto_depIdxs,
		MessageInfos:      file_pb_cwsx_game_invitation_server_server_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_invitation_server_server_proto = out.File
	file_pb_cwsx_game_invitation_server_server_proto_rawDesc = nil
	file_pb_cwsx_game_invitation_server_server_proto_goTypes = nil
	file_pb_cwsx_game_invitation_server_server_proto_depIdxs = nil
}
