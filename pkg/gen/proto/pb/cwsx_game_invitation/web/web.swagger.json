{"swagger": "2.0", "info": {"title": "pb/cwsx_game_invitation/web/web.proto", "version": "version not set"}, "tags": [{"name": "CwsxGameInvitationApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/cwsx_game_invitation.CwsxGameInvitationApi/HelpingClaim": {"post": {"summary": "助力领奖-单条邀请", "operationId": "CwsxGameInvitationApi_HelpingClaim", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationHelpingClaimRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationHelpingClaimReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/HelpingSucc": {"post": {"summary": "助力成功", "operationId": "CwsxGameInvitationApi_HelpingSucc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationHelpingSuccRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationHelpingSuccReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/Invitate": {"post": {"summary": "发起邀请", "operationId": "CwsxGameInvitationApi_Invitate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/InvitateClaim": {"post": {"summary": "邀请领奖-限时活动", "operationId": "CwsxGameInvitationApi_InvitateClaim", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateClaimRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateClaimReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/InvitateList": {"post": {"summary": "邀请列表", "operationId": "CwsxGameInvitationApi_InvitateList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateListReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/InvitateRecord": {"post": {"summary": "邀请记录", "operationId": "CwsxGameInvitationApi_InvitateRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateRecordRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationInvitateRecordReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/QueryDetail": {"post": {"summary": "活动详情", "operationId": "CwsxGameInvitationApi_QueryDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationQueryDetailRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationQueryDetailReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/QueryStatus": {"post": {"summary": "活动入口", "operationId": "CwsxGameInvitationApi_QueryStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationQueryStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationQueryStatusReq"}}], "tags": ["CwsxGameInvitationApi"]}}, "/cwsx_game_invitation.CwsxGameInvitationApi/StartHelping": {"post": {"summary": "开始助力", "operationId": "CwsxGameInvitationApi_StartHelping", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_game_invitationStartHelpingRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_game_invitationStartHelpingReq"}}], "tags": ["CwsxGameInvitationApi"]}}}, "definitions": {"InvitateClaimReqClaimInfo": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int64", "title": "限时活动id"}, "threshold": {"type": "integer", "format": "int64", "title": "阶梯值"}}}, "QueryDetailRspClaimProgress": {"type": "object", "properties": {"status": {"type": "integer", "format": "int64", "title": "阶段状态 见 ClaimStatus"}, "threshold": {"type": "integer", "format": "int64", "title": "阶梯值"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonItem"}, "title": "奖励"}}}, "QueryStatusRspUnclaim": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int64", "title": "限时活动id"}, "threshold": {"type": "integer", "format": "int64", "title": "阶梯值"}}}, "commonInvitateUser": {"type": "object", "properties": {"openId": {"type": "string", "title": "待邀请人 openId"}, "name": {"type": "string", "title": "姓名"}, "avatar": {"type": "string", "title": "头像"}, "status": {"type": "integer", "format": "int64", "title": "邀请状态 见 InvitateStatus"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonItem"}, "title": "奖励"}, "uid": {"type": "integer", "format": "int64", "title": "web上报用"}}}, "commonItem": {"type": "object", "properties": {"itemId": {"type": "integer", "format": "int64"}, "num": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int64", "title": "礼物类型 见 PackageItemType"}}}, "cwsx_game_invitationHelpingClaimReq": {"type": "object", "properties": {"inviteeOpenId": {"type": "string", "title": "被邀请人openId"}, "device": {"$ref": "#/definitions/deviceDevice"}}, "title": "日常助力领奖"}, "cwsx_game_invitationHelpingClaimRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonItem"}, "title": "奖励"}}}, "cwsx_game_invitationHelpingSuccReq": {"type": "object", "title": "助力成功"}, "cwsx_game_invitationHelpingSuccRsp": {"type": "object", "properties": {"inviterOpenId": {"type": "string", "title": "邀请人openId"}, "name": {"type": "string", "title": "姓名"}, "avatar": {"type": "string", "title": "头像"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonItem"}, "title": "奖励"}, "uid": {"type": "string", "format": "uint64", "title": "web上报用"}}}, "cwsx_game_invitationInvitateClaimReq": {"type": "object", "properties": {"infos": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/InvitateClaimReqClaimInfo"}, "title": "领取奖励"}, "device": {"$ref": "#/definitions/deviceDevice"}}, "title": "限时活动领奖"}, "cwsx_game_invitationInvitateClaimRsp": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonItem"}, "title": "奖励"}}}, "cwsx_game_invitationInvitateListReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int64", "title": "分页参数"}}, "title": "邀请列表"}, "cwsx_game_invitationInvitateListRsp": {"type": "object", "properties": {"invitateList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonInvitateUser"}, "title": "邀请列表"}, "passback": {"type": "integer", "format": "int64", "title": "分页参数"}, "hasNext": {"type": "boolean", "title": "是否有下一页"}}}, "cwsx_game_invitationInvitateRecordReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int64", "title": "分页参数"}, "activityId": {"type": "integer", "format": "int64", "title": "限时活动id"}}, "title": "邀请记录"}, "cwsx_game_invitationInvitateRecordRsp": {"type": "object", "properties": {"recordList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/cwsx_game_invitationcommonInvitateRecord"}, "title": "记录列表"}, "passback": {"type": "integer", "format": "int64", "title": "分页参数"}, "hasNext": {"type": "boolean", "title": "是否有下一页"}}}, "cwsx_game_invitationInvitateReq": {"type": "object", "properties": {"inviteeOpenId": {"type": "string", "title": "被邀请人openId"}, "invitateId": {"type": "string", "title": "邀请id 标识表示助力的对象"}}, "title": "发起邀请 需要校验关系"}, "cwsx_game_invitationInvitateRsp": {"type": "object"}, "cwsx_game_invitationQueryDetailReq": {"type": "object", "title": "活动详情"}, "cwsx_game_invitationQueryDetailRsp": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int64", "title": "限时活动id"}, "status": {"type": "integer", "format": "int64", "title": "活动状态 见 ActivityStatus"}, "leftTime": {"type": "integer", "format": "int64", "title": "剩余时间"}, "ruleImgUrl": {"type": "string", "title": "活动规则图片"}, "title": {"type": "string", "title": "主标题"}, "detailDesc": {"type": "string", "title": "详细说明"}, "progress": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QueryDetailRspClaimProgress"}, "title": "领取进度"}, "succCount": {"type": "integer", "format": "int64", "title": "成功邀请个数"}, "unclaimCount": {"type": "integer", "format": "int64", "title": "未领取个数"}, "invitateId": {"type": "string", "title": "邀请id inviterOpenId"}}}, "cwsx_game_invitationQueryStatusReq": {"type": "object", "title": "活动状态"}, "cwsx_game_invitationQueryStatusRsp": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int64", "title": "限时活动id"}, "status": {"type": "integer", "format": "int64", "title": "活动状态 见 ActivityStatus"}, "leftTime": {"type": "integer", "format": "int64", "title": "剩余时间"}, "unclaims": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/QueryStatusRspUnclaim"}, "title": "限时未领取奖励"}}}, "cwsx_game_invitationStartHelpingReq": {"type": "object", "properties": {"invitateId": {"type": "string", "title": "邀请id 标识表示助力的对象"}}, "title": "开始助力"}, "cwsx_game_invitationStartHelpingRsp": {"type": "object", "properties": {"message": {"type": "string", "title": "开始助力文案"}}}, "cwsx_game_invitationcommonInvitateRecord": {"type": "object", "properties": {"inviteeOpenId": {"type": "string", "title": "被邀请人openId"}, "name": {"type": "string", "title": "姓名"}, "avatar": {"type": "string", "title": "头像"}, "status": {"type": "integer", "format": "int64", "title": "邀请状态 见 RecordStatus"}, "items": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonItem"}, "title": "奖励"}, "uid": {"type": "integer", "format": "int64", "title": "web上报用"}}}, "deviceDevice": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台信息 kugou、qqmusic、qmkege、kuwo、lanren"}, "version": {"type": "string", "title": "客户端版本 1.2.3"}, "os": {"type": "string", "title": "系统 android、ios"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}