// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_invitation/web/web.proto

package web

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	common "kugou_adapter_service/pkg/gen/proto/pb/cwsx_game_invitation/common"
	device "kugou_adapter_service/pkg/gen/proto/pb/device"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 活动状态
type QueryStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryStatusReq) Reset() {
	*x = QueryStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusReq) ProtoMessage() {}

func (x *QueryStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusReq.ProtoReflect.Descriptor instead.
func (*QueryStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{0}
}

type QueryStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId uint32                    `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"` // 限时活动id
	Status     uint32                    `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`         // 活动状态 见 ActivityStatus
	LeftTime   uint32                    `protobuf:"varint,3,opt,name=leftTime,proto3" json:"leftTime,omitempty"`     // 剩余时间
	Unclaims   []*QueryStatusRsp_Unclaim `protobuf:"bytes,4,rep,name=unclaims,proto3" json:"unclaims,omitempty"`      // 限时未领取奖励
}

func (x *QueryStatusRsp) Reset() {
	*x = QueryStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRsp) ProtoMessage() {}

func (x *QueryStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRsp.ProtoReflect.Descriptor instead.
func (*QueryStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{1}
}

func (x *QueryStatusRsp) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *QueryStatusRsp) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *QueryStatusRsp) GetLeftTime() uint32 {
	if x != nil {
		return x.LeftTime
	}
	return 0
}

func (x *QueryStatusRsp) GetUnclaims() []*QueryStatusRsp_Unclaim {
	if x != nil {
		return x.Unclaims
	}
	return nil
}

// 活动详情
type QueryDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QueryDetailReq) Reset() {
	*x = QueryDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDetailReq) ProtoMessage() {}

func (x *QueryDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDetailReq.ProtoReflect.Descriptor instead.
func (*QueryDetailReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{2}
}

type QueryDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId   uint32                          `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`     // 限时活动id
	Status       uint32                          `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`             // 活动状态 见 ActivityStatus
	LeftTime     uint32                          `protobuf:"varint,3,opt,name=leftTime,proto3" json:"leftTime,omitempty"`         // 剩余时间
	RuleImgUrl   string                          `protobuf:"bytes,4,opt,name=ruleImgUrl,proto3" json:"ruleImgUrl,omitempty"`      // 活动规则图片
	Title        string                          `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`                // 主标题
	DetailDesc   string                          `protobuf:"bytes,6,opt,name=detailDesc,proto3" json:"detailDesc,omitempty"`      // 详细说明
	Progress     []*QueryDetailRsp_ClaimProgress `protobuf:"bytes,7,rep,name=progress,proto3" json:"progress,omitempty"`          // 领取进度
	SuccCount    uint32                          `protobuf:"varint,8,opt,name=succCount,proto3" json:"succCount,omitempty"`       // 成功邀请个数
	UnclaimCount uint32                          `protobuf:"varint,9,opt,name=unclaimCount,proto3" json:"unclaimCount,omitempty"` // 未领取个数
	InvitateId   string                          `protobuf:"bytes,10,opt,name=invitateId,proto3" json:"invitateId,omitempty"`     // 邀请id inviterOpenId
}

func (x *QueryDetailRsp) Reset() {
	*x = QueryDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDetailRsp) ProtoMessage() {}

func (x *QueryDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDetailRsp.ProtoReflect.Descriptor instead.
func (*QueryDetailRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{3}
}

func (x *QueryDetailRsp) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *QueryDetailRsp) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *QueryDetailRsp) GetLeftTime() uint32 {
	if x != nil {
		return x.LeftTime
	}
	return 0
}

func (x *QueryDetailRsp) GetRuleImgUrl() string {
	if x != nil {
		return x.RuleImgUrl
	}
	return ""
}

func (x *QueryDetailRsp) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *QueryDetailRsp) GetDetailDesc() string {
	if x != nil {
		return x.DetailDesc
	}
	return ""
}

func (x *QueryDetailRsp) GetProgress() []*QueryDetailRsp_ClaimProgress {
	if x != nil {
		return x.Progress
	}
	return nil
}

func (x *QueryDetailRsp) GetSuccCount() uint32 {
	if x != nil {
		return x.SuccCount
	}
	return 0
}

func (x *QueryDetailRsp) GetUnclaimCount() uint32 {
	if x != nil {
		return x.UnclaimCount
	}
	return 0
}

func (x *QueryDetailRsp) GetInvitateId() string {
	if x != nil {
		return x.InvitateId
	}
	return ""
}

// 邀请列表
type InvitateListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback uint32 `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 分页参数
}

func (x *InvitateListReq) Reset() {
	*x = InvitateListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateListReq) ProtoMessage() {}

func (x *InvitateListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateListReq.ProtoReflect.Descriptor instead.
func (*InvitateListReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{4}
}

func (x *InvitateListReq) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

type InvitateListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvitateList []*common.InvitateUser `protobuf:"bytes,1,rep,name=invitateList,proto3" json:"invitateList,omitempty"` // 邀请列表
	Passback     uint32                 `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"`        // 分页参数
	HasNext      bool                   `protobuf:"varint,3,opt,name=hasNext,proto3" json:"hasNext,omitempty"`          // 是否有下一页
}

func (x *InvitateListRsp) Reset() {
	*x = InvitateListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateListRsp) ProtoMessage() {}

func (x *InvitateListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateListRsp.ProtoReflect.Descriptor instead.
func (*InvitateListRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{5}
}

func (x *InvitateListRsp) GetInvitateList() []*common.InvitateUser {
	if x != nil {
		return x.InvitateList
	}
	return nil
}

func (x *InvitateListRsp) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *InvitateListRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

// 发起邀请 需要校验关系
type InvitateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviteeOpenId string `protobuf:"bytes,1,opt,name=inviteeOpenId,proto3" json:"inviteeOpenId,omitempty"` // 被邀请人openId
	InvitateId    string `protobuf:"bytes,2,opt,name=invitateId,proto3" json:"invitateId,omitempty"`       // 邀请id 标识表示助力的对象
}

func (x *InvitateReq) Reset() {
	*x = InvitateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateReq) ProtoMessage() {}

func (x *InvitateReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateReq.ProtoReflect.Descriptor instead.
func (*InvitateReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{6}
}

func (x *InvitateReq) GetInviteeOpenId() string {
	if x != nil {
		return x.InviteeOpenId
	}
	return ""
}

func (x *InvitateReq) GetInvitateId() string {
	if x != nil {
		return x.InvitateId
	}
	return ""
}

type InvitateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InvitateRsp) Reset() {
	*x = InvitateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRsp) ProtoMessage() {}

func (x *InvitateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRsp.ProtoReflect.Descriptor instead.
func (*InvitateRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{7}
}

// 邀请记录
type InvitateRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback   uint32 `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"`     // 分页参数
	ActivityId uint32 `protobuf:"varint,2,opt,name=activityId,proto3" json:"activityId,omitempty"` // 限时活动id
}

func (x *InvitateRecordReq) Reset() {
	*x = InvitateRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRecordReq) ProtoMessage() {}

func (x *InvitateRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRecordReq.ProtoReflect.Descriptor instead.
func (*InvitateRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{8}
}

func (x *InvitateRecordReq) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *InvitateRecordReq) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

type InvitateRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordList []*common.InvitateRecord `protobuf:"bytes,1,rep,name=recordList,proto3" json:"recordList,omitempty"` // 记录列表
	Passback   uint32                   `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"`    // 分页参数
	HasNext    bool                     `protobuf:"varint,3,opt,name=hasNext,proto3" json:"hasNext,omitempty"`      // 是否有下一页
}

func (x *InvitateRecordRsp) Reset() {
	*x = InvitateRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRecordRsp) ProtoMessage() {}

func (x *InvitateRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRecordRsp.ProtoReflect.Descriptor instead.
func (*InvitateRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{9}
}

func (x *InvitateRecordRsp) GetRecordList() []*common.InvitateRecord {
	if x != nil {
		return x.RecordList
	}
	return nil
}

func (x *InvitateRecordRsp) GetPassback() uint32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *InvitateRecordRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

// 开始助力
type StartHelpingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvitateId string `protobuf:"bytes,1,opt,name=invitateId,proto3" json:"invitateId,omitempty"` // 邀请id 标识表示助力的对象
}

func (x *StartHelpingReq) Reset() {
	*x = StartHelpingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartHelpingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartHelpingReq) ProtoMessage() {}

func (x *StartHelpingReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartHelpingReq.ProtoReflect.Descriptor instead.
func (*StartHelpingReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{10}
}

func (x *StartHelpingReq) GetInvitateId() string {
	if x != nil {
		return x.InvitateId
	}
	return ""
}

type StartHelpingRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"` // 开始助力文案
}

func (x *StartHelpingRsp) Reset() {
	*x = StartHelpingRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartHelpingRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartHelpingRsp) ProtoMessage() {}

func (x *StartHelpingRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartHelpingRsp.ProtoReflect.Descriptor instead.
func (*StartHelpingRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{11}
}

func (x *StartHelpingRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 助力成功
type HelpingSuccReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HelpingSuccReq) Reset() {
	*x = HelpingSuccReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingSuccReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingSuccReq) ProtoMessage() {}

func (x *HelpingSuccReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingSuccReq.ProtoReflect.Descriptor instead.
func (*HelpingSuccReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{12}
}

type HelpingSuccRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterOpenId string         `protobuf:"bytes,1,opt,name=inviterOpenId,proto3" json:"inviterOpenId,omitempty"` // 邀请人openId
	Name          string         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                   // 姓名
	Avatar        string         `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 头像
	Items         []*common.Item `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`                 // 奖励
	Uid           uint64         `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`                    // web上报用
}

func (x *HelpingSuccRsp) Reset() {
	*x = HelpingSuccRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingSuccRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingSuccRsp) ProtoMessage() {}

func (x *HelpingSuccRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingSuccRsp.ProtoReflect.Descriptor instead.
func (*HelpingSuccRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{13}
}

func (x *HelpingSuccRsp) GetInviterOpenId() string {
	if x != nil {
		return x.InviterOpenId
	}
	return ""
}

func (x *HelpingSuccRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HelpingSuccRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *HelpingSuccRsp) GetItems() []*common.Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *HelpingSuccRsp) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

// 日常助力领奖
type HelpingClaimReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviteeOpenId string         `protobuf:"bytes,1,opt,name=inviteeOpenId,proto3" json:"inviteeOpenId,omitempty"` // 被邀请人openId
	Device        *device.Device `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *HelpingClaimReq) Reset() {
	*x = HelpingClaimReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingClaimReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingClaimReq) ProtoMessage() {}

func (x *HelpingClaimReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingClaimReq.ProtoReflect.Descriptor instead.
func (*HelpingClaimReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{14}
}

func (x *HelpingClaimReq) GetInviteeOpenId() string {
	if x != nil {
		return x.InviteeOpenId
	}
	return ""
}

func (x *HelpingClaimReq) GetDevice() *device.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type HelpingClaimRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*common.Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"` // 奖励
}

func (x *HelpingClaimRsp) Reset() {
	*x = HelpingClaimRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpingClaimRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpingClaimRsp) ProtoMessage() {}

func (x *HelpingClaimRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpingClaimRsp.ProtoReflect.Descriptor instead.
func (*HelpingClaimRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{15}
}

func (x *HelpingClaimRsp) GetItems() []*common.Item {
	if x != nil {
		return x.Items
	}
	return nil
}

// 限时活动领奖
type InvitateClaimReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos  []*InvitateClaimReq_ClaimInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"` // 领取奖励
	Device *device.Device                `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`
}

func (x *InvitateClaimReq) Reset() {
	*x = InvitateClaimReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateClaimReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateClaimReq) ProtoMessage() {}

func (x *InvitateClaimReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateClaimReq.ProtoReflect.Descriptor instead.
func (*InvitateClaimReq) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{16}
}

func (x *InvitateClaimReq) GetInfos() []*InvitateClaimReq_ClaimInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

func (x *InvitateClaimReq) GetDevice() *device.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

type InvitateClaimRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*common.Item `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"` // 奖励
}

func (x *InvitateClaimRsp) Reset() {
	*x = InvitateClaimRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateClaimRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateClaimRsp) ProtoMessage() {}

func (x *InvitateClaimRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateClaimRsp.ProtoReflect.Descriptor instead.
func (*InvitateClaimRsp) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{17}
}

func (x *InvitateClaimRsp) GetItems() []*common.Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type QueryStatusRsp_Unclaim struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId uint32 `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"` // 限时活动id
	Threshold  uint32 `protobuf:"varint,2,opt,name=threshold,proto3" json:"threshold,omitempty"`   // 阶梯值
}

func (x *QueryStatusRsp_Unclaim) Reset() {
	*x = QueryStatusRsp_Unclaim{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStatusRsp_Unclaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStatusRsp_Unclaim) ProtoMessage() {}

func (x *QueryStatusRsp_Unclaim) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStatusRsp_Unclaim.ProtoReflect.Descriptor instead.
func (*QueryStatusRsp_Unclaim) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{1, 0}
}

func (x *QueryStatusRsp_Unclaim) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *QueryStatusRsp_Unclaim) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

type QueryDetailRsp_ClaimProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    uint32         `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`       // 阶段状态 见 ClaimStatus
	Threshold uint32         `protobuf:"varint,2,opt,name=threshold,proto3" json:"threshold,omitempty"` // 阶梯值
	Items     []*common.Item `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`          // 奖励
}

func (x *QueryDetailRsp_ClaimProgress) Reset() {
	*x = QueryDetailRsp_ClaimProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDetailRsp_ClaimProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDetailRsp_ClaimProgress) ProtoMessage() {}

func (x *QueryDetailRsp_ClaimProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDetailRsp_ClaimProgress.ProtoReflect.Descriptor instead.
func (*QueryDetailRsp_ClaimProgress) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{3, 0}
}

func (x *QueryDetailRsp_ClaimProgress) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *QueryDetailRsp_ClaimProgress) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *QueryDetailRsp_ClaimProgress) GetItems() []*common.Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type InvitateClaimReq_ClaimInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId uint32 `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"` // 限时活动id
	Threshold  uint32 `protobuf:"varint,2,opt,name=threshold,proto3" json:"threshold,omitempty"`   // 阶梯值
}

func (x *InvitateClaimReq_ClaimInfo) Reset() {
	*x = InvitateClaimReq_ClaimInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateClaimReq_ClaimInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateClaimReq_ClaimInfo) ProtoMessage() {}

func (x *InvitateClaimReq_ClaimInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_web_web_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateClaimReq_ClaimInfo.ProtoReflect.Descriptor instead.
func (*InvitateClaimReq_ClaimInfo) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP(), []int{16, 0}
}

func (x *InvitateClaimReq_ClaimInfo) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *InvitateClaimReq_ClaimInfo) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

var File_pb_cwsx_game_invitation_web_web_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_invitation_web_web_proto_rawDesc = []byte{
	0x0a, 0x25, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x77, 0x65, 0x62, 0x2f, 0x77, 0x65,
	0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x2b, 0x70,
	0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70, 0x62, 0x2f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x10, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x22, 0xf7, 0x01, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6c, 0x65, 0x66, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x75,
	0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x73, 0x70, 0x2e, 0x55, 0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x08, 0x75, 0x6e, 0x63,
	0x6c, 0x61, 0x69, 0x6d, 0x73, 0x1a, 0x47, 0x0a, 0x07, 0x55, 0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x10,
	0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x22, 0xec, 0x03, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x73, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x65, 0x66, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6c,
	0x65, 0x66, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x49,
	0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x75, 0x6c,
	0x65, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x4e, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x73, 0x70, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x75, 0x63, 0x63, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x73, 0x75, 0x63, 0x63, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x75,
	0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x75, 0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x1a,
	0x7e, 0x0a, 0x0d, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22,
	0x2d, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x96,
	0x01, 0x0a, 0x0f, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x4d, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a,
	0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x22, 0x53, 0x0a, 0x0b, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0x0d, 0x0a, 0x0b,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x22, 0x4f, 0x0a, 0x11, 0x49,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x96, 0x01, 0x0a,
	0x11, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x4b, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68,
	0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61,
	0x73, 0x4e, 0x65, 0x78, 0x74, 0x22, 0x31, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x48, 0x65,
	0x6c, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67,
	0x53, 0x75, 0x63, 0x63, 0x52, 0x65, 0x71, 0x22, 0xad, 0x01, 0x0a, 0x0e, 0x48, 0x65, 0x6c, 0x70,
	0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x52, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x37, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0x5f, 0x0a, 0x0f, 0x48, 0x65, 0x6c, 0x70, 0x69,
	0x6e, 0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x4a, 0x0a, 0x0f, 0x48, 0x65, 0x6c, 0x70,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0xcd, 0x01, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x46, 0x0a, 0x05, 0x69, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71,
	0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x12, 0x26, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x49, 0x0a, 0x09, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x22, 0x4b, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x32, 0xd9, 0x06, 0x0a, 0x15, 0x43, 0x77, 0x73, 0x78, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x12, 0x59, 0x0a, 0x0b, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x2e, 0x63, 0x77, 0x73,
	0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x24, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x24, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x73,
	0x70, 0x12, 0x5c, 0x0a, 0x0c, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x25, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x50, 0x0a, 0x08, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x62, 0x0a, 0x0e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x27, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x5c, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x48, 0x65,
	0x6c, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x73, 0x70, 0x12, 0x59, 0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x75,
	0x63, 0x63, 0x12, 0x24, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e,
	0x67, 0x53, 0x75, 0x63, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x63, 0x63, 0x52, 0x73, 0x70, 0x12, 0x5c,
	0x0a, 0x0c, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x25,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x6c, 0x70, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x6c,
	0x70, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x5f, 0x0a, 0x0d,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x26, 0x2e,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x73, 0x70, 0x42, 0x50, 0x5a,
	0x4e, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x77, 0x65, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_invitation_web_web_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_invitation_web_web_proto_rawDescData = file_pb_cwsx_game_invitation_web_web_proto_rawDesc
)

func file_pb_cwsx_game_invitation_web_web_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_invitation_web_web_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_invitation_web_web_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_invitation_web_web_proto_rawDescData)
	})
	return file_pb_cwsx_game_invitation_web_web_proto_rawDescData
}

var file_pb_cwsx_game_invitation_web_web_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_pb_cwsx_game_invitation_web_web_proto_goTypes = []interface{}{
	(*QueryStatusReq)(nil),               // 0: cwsx_game_invitation.QueryStatusReq
	(*QueryStatusRsp)(nil),               // 1: cwsx_game_invitation.QueryStatusRsp
	(*QueryDetailReq)(nil),               // 2: cwsx_game_invitation.QueryDetailReq
	(*QueryDetailRsp)(nil),               // 3: cwsx_game_invitation.QueryDetailRsp
	(*InvitateListReq)(nil),              // 4: cwsx_game_invitation.InvitateListReq
	(*InvitateListRsp)(nil),              // 5: cwsx_game_invitation.InvitateListRsp
	(*InvitateReq)(nil),                  // 6: cwsx_game_invitation.InvitateReq
	(*InvitateRsp)(nil),                  // 7: cwsx_game_invitation.InvitateRsp
	(*InvitateRecordReq)(nil),            // 8: cwsx_game_invitation.InvitateRecordReq
	(*InvitateRecordRsp)(nil),            // 9: cwsx_game_invitation.InvitateRecordRsp
	(*StartHelpingReq)(nil),              // 10: cwsx_game_invitation.StartHelpingReq
	(*StartHelpingRsp)(nil),              // 11: cwsx_game_invitation.StartHelpingRsp
	(*HelpingSuccReq)(nil),               // 12: cwsx_game_invitation.HelpingSuccReq
	(*HelpingSuccRsp)(nil),               // 13: cwsx_game_invitation.HelpingSuccRsp
	(*HelpingClaimReq)(nil),              // 14: cwsx_game_invitation.HelpingClaimReq
	(*HelpingClaimRsp)(nil),              // 15: cwsx_game_invitation.HelpingClaimRsp
	(*InvitateClaimReq)(nil),             // 16: cwsx_game_invitation.InvitateClaimReq
	(*InvitateClaimRsp)(nil),             // 17: cwsx_game_invitation.InvitateClaimRsp
	(*QueryStatusRsp_Unclaim)(nil),       // 18: cwsx_game_invitation.QueryStatusRsp.Unclaim
	(*QueryDetailRsp_ClaimProgress)(nil), // 19: cwsx_game_invitation.QueryDetailRsp.ClaimProgress
	(*InvitateClaimReq_ClaimInfo)(nil),   // 20: cwsx_game_invitation.InvitateClaimReq.ClaimInfo
	(*common.InvitateUser)(nil),          // 21: cwsx_game_invitation.common.InvitateUser
	(*common.InvitateRecord)(nil),        // 22: cwsx_game_invitation.common.InvitateRecord
	(*common.Item)(nil),                  // 23: cwsx_game_invitation.common.Item
	(*device.Device)(nil),                // 24: device.Device
}
var file_pb_cwsx_game_invitation_web_web_proto_depIdxs = []int32{
	18, // 0: cwsx_game_invitation.QueryStatusRsp.unclaims:type_name -> cwsx_game_invitation.QueryStatusRsp.Unclaim
	19, // 1: cwsx_game_invitation.QueryDetailRsp.progress:type_name -> cwsx_game_invitation.QueryDetailRsp.ClaimProgress
	21, // 2: cwsx_game_invitation.InvitateListRsp.invitateList:type_name -> cwsx_game_invitation.common.InvitateUser
	22, // 3: cwsx_game_invitation.InvitateRecordRsp.recordList:type_name -> cwsx_game_invitation.common.InvitateRecord
	23, // 4: cwsx_game_invitation.HelpingSuccRsp.items:type_name -> cwsx_game_invitation.common.Item
	24, // 5: cwsx_game_invitation.HelpingClaimReq.device:type_name -> device.Device
	23, // 6: cwsx_game_invitation.HelpingClaimRsp.items:type_name -> cwsx_game_invitation.common.Item
	20, // 7: cwsx_game_invitation.InvitateClaimReq.infos:type_name -> cwsx_game_invitation.InvitateClaimReq.ClaimInfo
	24, // 8: cwsx_game_invitation.InvitateClaimReq.device:type_name -> device.Device
	23, // 9: cwsx_game_invitation.InvitateClaimRsp.items:type_name -> cwsx_game_invitation.common.Item
	23, // 10: cwsx_game_invitation.QueryDetailRsp.ClaimProgress.items:type_name -> cwsx_game_invitation.common.Item
	0,  // 11: cwsx_game_invitation.CwsxGameInvitationApi.QueryStatus:input_type -> cwsx_game_invitation.QueryStatusReq
	2,  // 12: cwsx_game_invitation.CwsxGameInvitationApi.QueryDetail:input_type -> cwsx_game_invitation.QueryDetailReq
	4,  // 13: cwsx_game_invitation.CwsxGameInvitationApi.InvitateList:input_type -> cwsx_game_invitation.InvitateListReq
	6,  // 14: cwsx_game_invitation.CwsxGameInvitationApi.Invitate:input_type -> cwsx_game_invitation.InvitateReq
	8,  // 15: cwsx_game_invitation.CwsxGameInvitationApi.InvitateRecord:input_type -> cwsx_game_invitation.InvitateRecordReq
	10, // 16: cwsx_game_invitation.CwsxGameInvitationApi.StartHelping:input_type -> cwsx_game_invitation.StartHelpingReq
	12, // 17: cwsx_game_invitation.CwsxGameInvitationApi.HelpingSucc:input_type -> cwsx_game_invitation.HelpingSuccReq
	14, // 18: cwsx_game_invitation.CwsxGameInvitationApi.HelpingClaim:input_type -> cwsx_game_invitation.HelpingClaimReq
	16, // 19: cwsx_game_invitation.CwsxGameInvitationApi.InvitateClaim:input_type -> cwsx_game_invitation.InvitateClaimReq
	1,  // 20: cwsx_game_invitation.CwsxGameInvitationApi.QueryStatus:output_type -> cwsx_game_invitation.QueryStatusRsp
	3,  // 21: cwsx_game_invitation.CwsxGameInvitationApi.QueryDetail:output_type -> cwsx_game_invitation.QueryDetailRsp
	5,  // 22: cwsx_game_invitation.CwsxGameInvitationApi.InvitateList:output_type -> cwsx_game_invitation.InvitateListRsp
	7,  // 23: cwsx_game_invitation.CwsxGameInvitationApi.Invitate:output_type -> cwsx_game_invitation.InvitateRsp
	9,  // 24: cwsx_game_invitation.CwsxGameInvitationApi.InvitateRecord:output_type -> cwsx_game_invitation.InvitateRecordRsp
	11, // 25: cwsx_game_invitation.CwsxGameInvitationApi.StartHelping:output_type -> cwsx_game_invitation.StartHelpingRsp
	13, // 26: cwsx_game_invitation.CwsxGameInvitationApi.HelpingSucc:output_type -> cwsx_game_invitation.HelpingSuccRsp
	15, // 27: cwsx_game_invitation.CwsxGameInvitationApi.HelpingClaim:output_type -> cwsx_game_invitation.HelpingClaimRsp
	17, // 28: cwsx_game_invitation.CwsxGameInvitationApi.InvitateClaim:output_type -> cwsx_game_invitation.InvitateClaimRsp
	20, // [20:29] is the sub-list for method output_type
	11, // [11:20] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_invitation_web_web_proto_init() }
func file_pb_cwsx_game_invitation_web_web_proto_init() {
	if File_pb_cwsx_game_invitation_web_web_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartHelpingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartHelpingRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingSuccReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingSuccRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingClaimReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpingClaimRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateClaimReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateClaimRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStatusRsp_Unclaim); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDetailRsp_ClaimProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_web_web_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateClaimReq_ClaimInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_invitation_web_web_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_cwsx_game_invitation_web_web_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_invitation_web_web_proto_depIdxs,
		MessageInfos:      file_pb_cwsx_game_invitation_web_web_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_invitation_web_web_proto = out.File
	file_pb_cwsx_game_invitation_web_web_proto_rawDesc = nil
	file_pb_cwsx_game_invitation_web_web_proto_goTypes = nil
	file_pb_cwsx_game_invitation_web_web_proto_depIdxs = nil
}
