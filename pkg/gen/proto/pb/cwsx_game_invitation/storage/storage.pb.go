// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_invitation/storage/storage.proto

package storage

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserProgress 限时活动 领取进度存储
type UserProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MInfos map[uint32]*UserProgress_ClaimInfo `protobuf:"bytes,1,rep,name=mInfos,proto3" json:"mInfos,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 领取信息
}

func (x *UserProgress) Reset() {
	*x = UserProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProgress) ProtoMessage() {}

func (x *UserProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProgress.ProtoReflect.Descriptor instead.
func (*UserProgress) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_storage_storage_proto_rawDescGZIP(), []int{0}
}

func (x *UserProgress) GetMInfos() map[uint32]*UserProgress_ClaimInfo {
	if x != nil {
		return x.MInfos
	}
	return nil
}

// InvitateRecord 邀请记录
type InvitateRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviteeOpenId string `protobuf:"bytes,1,opt,name=inviteeOpenId,proto3" json:"inviteeOpenId,omitempty"` // 被邀请人openId
	Status        uint32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`              // 邀请状态 见 RecordStatus
	BundleId      int64  `protobuf:"varint,3,opt,name=bundleId,proto3" json:"bundleId,omitempty"`          // 礼包id
}

func (x *InvitateRecord) Reset() {
	*x = InvitateRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvitateRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvitateRecord) ProtoMessage() {}

func (x *InvitateRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvitateRecord.ProtoReflect.Descriptor instead.
func (*InvitateRecord) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_storage_storage_proto_rawDescGZIP(), []int{1}
}

func (x *InvitateRecord) GetInviteeOpenId() string {
	if x != nil {
		return x.InviteeOpenId
	}
	return ""
}

func (x *InvitateRecord) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InvitateRecord) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

type UserProgress_ClaimInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MClaimStatus map[uint32]bool `protobuf:"bytes,1,rep,name=mClaimStatus,proto3" json:"mClaimStatus,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 领取
}

func (x *UserProgress_ClaimInfo) Reset() {
	*x = UserProgress_ClaimInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserProgress_ClaimInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserProgress_ClaimInfo) ProtoMessage() {}

func (x *UserProgress_ClaimInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserProgress_ClaimInfo.ProtoReflect.Descriptor instead.
func (*UserProgress_ClaimInfo) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_storage_storage_proto_rawDescGZIP(), []int{0, 0}
}

func (x *UserProgress_ClaimInfo) GetMClaimStatus() map[uint32]bool {
	if x != nil {
		return x.MClaimStatus
	}
	return nil
}

var File_pb_cwsx_game_invitation_storage_storage_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_invitation_storage_storage_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1c, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0x8a, 0x03,
	0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x4e,
	0x0a, 0x06, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x4d, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0xb8,
	0x01, 0x0a, 0x09, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x6a, 0x0a, 0x0c,
	0x6d, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x46, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x6d, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x3f, 0x0a, 0x11, 0x4d, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6f, 0x0a, 0x0b, 0x4d, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4a, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6a, 0x0a, 0x0e, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x0d,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x4f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x42, 0x54, 0x5a, 0x52, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_invitation_storage_storage_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_invitation_storage_storage_proto_rawDescData = file_pb_cwsx_game_invitation_storage_storage_proto_rawDesc
)

func file_pb_cwsx_game_invitation_storage_storage_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_invitation_storage_storage_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_invitation_storage_storage_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_invitation_storage_storage_proto_rawDescData)
	})
	return file_pb_cwsx_game_invitation_storage_storage_proto_rawDescData
}

var file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_pb_cwsx_game_invitation_storage_storage_proto_goTypes = []interface{}{
	(*UserProgress)(nil),           // 0: cwsx_game_invitation.storage.UserProgress
	(*InvitateRecord)(nil),         // 1: cwsx_game_invitation.storage.InvitateRecord
	(*UserProgress_ClaimInfo)(nil), // 2: cwsx_game_invitation.storage.UserProgress.ClaimInfo
	nil,                            // 3: cwsx_game_invitation.storage.UserProgress.MInfosEntry
	nil,                            // 4: cwsx_game_invitation.storage.UserProgress.ClaimInfo.MClaimStatusEntry
}
var file_pb_cwsx_game_invitation_storage_storage_proto_depIdxs = []int32{
	3, // 0: cwsx_game_invitation.storage.UserProgress.mInfos:type_name -> cwsx_game_invitation.storage.UserProgress.MInfosEntry
	4, // 1: cwsx_game_invitation.storage.UserProgress.ClaimInfo.mClaimStatus:type_name -> cwsx_game_invitation.storage.UserProgress.ClaimInfo.MClaimStatusEntry
	2, // 2: cwsx_game_invitation.storage.UserProgress.MInfosEntry.value:type_name -> cwsx_game_invitation.storage.UserProgress.ClaimInfo
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_invitation_storage_storage_proto_init() }
func file_pb_cwsx_game_invitation_storage_storage_proto_init() {
	if File_pb_cwsx_game_invitation_storage_storage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvitateRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserProgress_ClaimInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_invitation_storage_storage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_cwsx_game_invitation_storage_storage_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_invitation_storage_storage_proto_depIdxs,
		MessageInfos:      file_pb_cwsx_game_invitation_storage_storage_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_invitation_storage_storage_proto = out.File
	file_pb_cwsx_game_invitation_storage_storage_proto_rawDesc = nil
	file_pb_cwsx_game_invitation_storage_storage_proto_goTypes = nil
	file_pb_cwsx_game_invitation_storage_storage_proto_depIdxs = nil
}
