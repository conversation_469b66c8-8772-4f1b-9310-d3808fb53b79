// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/cwsx_game_invitation/config/config.proto

package config

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DailyActivityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId       uint32  `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`                    // 活动id
	Title            string  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                               // 主标题
	DetailDesc       string  `protobuf:"bytes,3,opt,name=detailDesc,proto3" json:"detailDesc,omitempty"`                     // 详细说明
	RuleImgUrl       string  `protobuf:"bytes,4,opt,name=ruleImgUrl,proto3" json:"ruleImgUrl,omitempty"`                     // 活动规则图片
	InvitateInterval uint32  `protobuf:"varint,5,opt,name=invitateInterval,proto3" json:"invitateInterval,omitempty"`        // 邀请间隔
	InviterBundleIds []int64 `protobuf:"varint,6,rep,packed,name=inviterBundleIds,proto3" json:"inviterBundleIds,omitempty"` // 邀请人助力奖励
	InviteeBundleIds []int64 `protobuf:"varint,7,rep,packed,name=inviteeBundleIds,proto3" json:"inviteeBundleIds,omitempty"` // 被邀请人助力奖励
	Content          string  `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`                           // 私信标题
	Desc             string  `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc,omitempty"`                                 // 私信外显文案
	Copy             string  `protobuf:"bytes,10,opt,name=copy,proto3" json:"copy,omitempty"`                                // 私信主文案
	JumpUrl          string  `protobuf:"bytes,11,opt,name=jumpUrl,proto3" json:"jumpUrl,omitempty"`                          // 跳转链接
	JumpIcon         string  `protobuf:"bytes,12,opt,name=jumpIcon,proto3" json:"jumpIcon,omitempty"`                        // 跳转Icon
	JumpCopy         string  `protobuf:"bytes,13,opt,name=jumpCopy,proto3" json:"jumpCopy,omitempty"`                        // 跳转文案
	AllowStage       uint32  `protobuf:"varint,14,opt,name=allowStage,proto3" json:"allowStage,omitempty"`                   // 允许展示关卡
}

func (x *DailyActivityConfig) Reset() {
	*x = DailyActivityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyActivityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyActivityConfig) ProtoMessage() {}

func (x *DailyActivityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyActivityConfig.ProtoReflect.Descriptor instead.
func (*DailyActivityConfig) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_config_config_proto_rawDescGZIP(), []int{0}
}

func (x *DailyActivityConfig) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *DailyActivityConfig) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DailyActivityConfig) GetDetailDesc() string {
	if x != nil {
		return x.DetailDesc
	}
	return ""
}

func (x *DailyActivityConfig) GetRuleImgUrl() string {
	if x != nil {
		return x.RuleImgUrl
	}
	return ""
}

func (x *DailyActivityConfig) GetInvitateInterval() uint32 {
	if x != nil {
		return x.InvitateInterval
	}
	return 0
}

func (x *DailyActivityConfig) GetInviterBundleIds() []int64 {
	if x != nil {
		return x.InviterBundleIds
	}
	return nil
}

func (x *DailyActivityConfig) GetInviteeBundleIds() []int64 {
	if x != nil {
		return x.InviteeBundleIds
	}
	return nil
}

func (x *DailyActivityConfig) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *DailyActivityConfig) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DailyActivityConfig) GetCopy() string {
	if x != nil {
		return x.Copy
	}
	return ""
}

func (x *DailyActivityConfig) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *DailyActivityConfig) GetJumpIcon() string {
	if x != nil {
		return x.JumpIcon
	}
	return ""
}

func (x *DailyActivityConfig) GetJumpCopy() string {
	if x != nil {
		return x.JumpCopy
	}
	return ""
}

func (x *DailyActivityConfig) GetAllowStage() uint32 {
	if x != nil {
		return x.AllowStage
	}
	return 0
}

type LimitedActivity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId uint32                           `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"` // 活动id
	Title      string                           `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`            // 主标题
	DetailDesc string                           `protobuf:"bytes,3,opt,name=detailDesc,proto3" json:"detailDesc,omitempty"`  // 详细说明
	RuleImgUrl string                           `protobuf:"bytes,4,opt,name=ruleImgUrl,proto3" json:"ruleImgUrl,omitempty"`  // 活动规则图片
	StartTime  uint32                           `protobuf:"varint,5,opt,name=startTime,proto3" json:"startTime,omitempty"`   // 开始时间
	EndTime    uint32                           `protobuf:"varint,6,opt,name=endTime,proto3" json:"endTime,omitempty"`       // 开始时间
	Progress   []*LimitedActivity_ClaimProgress `protobuf:"bytes,7,rep,name=progress,proto3" json:"progress,omitempty"`      // 领取进度
}

func (x *LimitedActivity) Reset() {
	*x = LimitedActivity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitedActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitedActivity) ProtoMessage() {}

func (x *LimitedActivity) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitedActivity.ProtoReflect.Descriptor instead.
func (*LimitedActivity) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_config_config_proto_rawDescGZIP(), []int{1}
}

func (x *LimitedActivity) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *LimitedActivity) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *LimitedActivity) GetDetailDesc() string {
	if x != nil {
		return x.DetailDesc
	}
	return ""
}

func (x *LimitedActivity) GetRuleImgUrl() string {
	if x != nil {
		return x.RuleImgUrl
	}
	return ""
}

func (x *LimitedActivity) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *LimitedActivity) GetEndTime() uint32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *LimitedActivity) GetProgress() []*LimitedActivity_ClaimProgress {
	if x != nil {
		return x.Progress
	}
	return nil
}

type LimitedActivityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*LimitedActivity `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` // 限时活动 根据id降序
}

func (x *LimitedActivityConfig) Reset() {
	*x = LimitedActivityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitedActivityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitedActivityConfig) ProtoMessage() {}

func (x *LimitedActivityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitedActivityConfig.ProtoReflect.Descriptor instead.
func (*LimitedActivityConfig) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_config_config_proto_rawDescGZIP(), []int{2}
}

func (x *LimitedActivityConfig) GetList() []*LimitedActivity {
	if x != nil {
		return x.List
	}
	return nil
}

type ActivityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DailyConfig   *DailyActivityConfig   `protobuf:"bytes,1,opt,name=dailyConfig,proto3" json:"dailyConfig,omitempty"`
	LimitedConfig *LimitedActivityConfig `protobuf:"bytes,2,opt,name=limitedConfig,proto3" json:"limitedConfig,omitempty"`
}

func (x *ActivityConfig) Reset() {
	*x = ActivityConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityConfig) ProtoMessage() {}

func (x *ActivityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityConfig.ProtoReflect.Descriptor instead.
func (*ActivityConfig) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_config_config_proto_rawDescGZIP(), []int{3}
}

func (x *ActivityConfig) GetDailyConfig() *DailyActivityConfig {
	if x != nil {
		return x.DailyConfig
	}
	return nil
}

func (x *ActivityConfig) GetLimitedConfig() *LimitedActivityConfig {
	if x != nil {
		return x.LimitedConfig
	}
	return nil
}

type LimitedActivity_ClaimProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Threshold uint32 `protobuf:"varint,1,opt,name=threshold,proto3" json:"threshold,omitempty"` // 阶梯值
	BundleId  int64  `protobuf:"varint,2,opt,name=bundleId,proto3" json:"bundleId,omitempty"`   // 奖励礼包
}

func (x *LimitedActivity_ClaimProgress) Reset() {
	*x = LimitedActivity_ClaimProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitedActivity_ClaimProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitedActivity_ClaimProgress) ProtoMessage() {}

func (x *LimitedActivity_ClaimProgress) ProtoReflect() protoreflect.Message {
	mi := &file_pb_cwsx_game_invitation_config_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitedActivity_ClaimProgress.ProtoReflect.Descriptor instead.
func (*LimitedActivity_ClaimProgress) Descriptor() ([]byte, []int) {
	return file_pb_cwsx_game_invitation_config_config_proto_rawDescGZIP(), []int{1, 0}
}

func (x *LimitedActivity_ClaimProgress) GetThreshold() uint32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *LimitedActivity_ClaimProgress) GetBundleId() int64 {
	if x != nil {
		return x.BundleId
	}
	return 0
}

var File_pb_cwsx_game_invitation_config_config_proto protoreflect.FileDescriptor

var file_pb_cwsx_game_invitation_config_config_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0xc3, 0x03, 0x0a, 0x13, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65,
	0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x75,
	0x6c, 0x65, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x42,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x73,
	0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x49, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x65, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x70, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x70, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70,
	0x49, 0x63, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70,
	0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x70, 0x79,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x70, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x22, 0xe2, 0x02, 0x0a, 0x0f, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x75,
	0x6c, 0x65, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x75, 0x6c, 0x65, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x56, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x49, 0x0a, 0x0d, 0x43, 0x6c,
	0x61, 0x69, 0x6d, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x59, 0x0a, 0x15, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x40,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0xbe, 0x01, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x52, 0x0a, 0x0b, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x64, 0x61, 0x69, 0x6c,
	0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x58, 0x0a, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x65, 0x64, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x53, 0x5a, 0x51, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x63, 0x77, 0x73, 0x78, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_cwsx_game_invitation_config_config_proto_rawDescOnce sync.Once
	file_pb_cwsx_game_invitation_config_config_proto_rawDescData = file_pb_cwsx_game_invitation_config_config_proto_rawDesc
)

func file_pb_cwsx_game_invitation_config_config_proto_rawDescGZIP() []byte {
	file_pb_cwsx_game_invitation_config_config_proto_rawDescOnce.Do(func() {
		file_pb_cwsx_game_invitation_config_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_cwsx_game_invitation_config_config_proto_rawDescData)
	})
	return file_pb_cwsx_game_invitation_config_config_proto_rawDescData
}

var file_pb_cwsx_game_invitation_config_config_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_pb_cwsx_game_invitation_config_config_proto_goTypes = []interface{}{
	(*DailyActivityConfig)(nil),           // 0: cwsx_game_invitation.config.DailyActivityConfig
	(*LimitedActivity)(nil),               // 1: cwsx_game_invitation.config.LimitedActivity
	(*LimitedActivityConfig)(nil),         // 2: cwsx_game_invitation.config.LimitedActivityConfig
	(*ActivityConfig)(nil),                // 3: cwsx_game_invitation.config.ActivityConfig
	(*LimitedActivity_ClaimProgress)(nil), // 4: cwsx_game_invitation.config.LimitedActivity.ClaimProgress
}
var file_pb_cwsx_game_invitation_config_config_proto_depIdxs = []int32{
	4, // 0: cwsx_game_invitation.config.LimitedActivity.progress:type_name -> cwsx_game_invitation.config.LimitedActivity.ClaimProgress
	1, // 1: cwsx_game_invitation.config.LimitedActivityConfig.list:type_name -> cwsx_game_invitation.config.LimitedActivity
	0, // 2: cwsx_game_invitation.config.ActivityConfig.dailyConfig:type_name -> cwsx_game_invitation.config.DailyActivityConfig
	2, // 3: cwsx_game_invitation.config.ActivityConfig.limitedConfig:type_name -> cwsx_game_invitation.config.LimitedActivityConfig
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pb_cwsx_game_invitation_config_config_proto_init() }
func file_pb_cwsx_game_invitation_config_config_proto_init() {
	if File_pb_cwsx_game_invitation_config_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_cwsx_game_invitation_config_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyActivityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_config_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitedActivity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_config_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitedActivityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_config_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_cwsx_game_invitation_config_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitedActivity_ClaimProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_cwsx_game_invitation_config_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_cwsx_game_invitation_config_config_proto_goTypes,
		DependencyIndexes: file_pb_cwsx_game_invitation_config_config_proto_depIdxs,
		MessageInfos:      file_pb_cwsx_game_invitation_config_config_proto_msgTypes,
	}.Build()
	File_pb_cwsx_game_invitation_config_config_proto = out.File
	file_pb_cwsx_game_invitation_config_config_proto_rawDesc = nil
	file_pb_cwsx_game_invitation_config_config_proto_goTypes = nil
	file_pb_cwsx_game_invitation_config_config_proto_depIdxs = nil
}
