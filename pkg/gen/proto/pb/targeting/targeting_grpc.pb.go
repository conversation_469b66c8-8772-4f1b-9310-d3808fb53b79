// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/targeting/targeting.proto

package targeting

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Targeting_IsMatch_FullMethodName = "/targeting.Targeting/IsMatch"
)

// TargetingClient is the client API for Targeting service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TargetingClient interface {
	// 检测是否命中
	IsMatch(ctx context.Context, in *IsMatchReq, opts ...grpc.CallOption) (*IsMatchRsp, error)
}

type targetingClient struct {
	cc grpc.ClientConnInterface
}

func NewTargetingClient(cc grpc.ClientConnInterface) TargetingClient {
	return &targetingClient{cc}
}

func (c *targetingClient) IsMatch(ctx context.Context, in *IsMatchReq, opts ...grpc.CallOption) (*IsMatchRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IsMatchRsp)
	err := c.cc.Invoke(ctx, Targeting_IsMatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TargetingServer is the server API for Targeting service.
// All implementations should embed UnimplementedTargetingServer
// for forward compatibility
type TargetingServer interface {
	// 检测是否命中
	IsMatch(context.Context, *IsMatchReq) (*IsMatchRsp, error)
}

// UnimplementedTargetingServer should be embedded to have forward compatible implementations.
type UnimplementedTargetingServer struct {
}

func (UnimplementedTargetingServer) IsMatch(context.Context, *IsMatchReq) (*IsMatchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsMatch not implemented")
}

// UnsafeTargetingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TargetingServer will
// result in compilation errors.
type UnsafeTargetingServer interface {
	mustEmbedUnimplementedTargetingServer()
}

func RegisterTargetingServer(s grpc.ServiceRegistrar, srv TargetingServer) {
	s.RegisterService(&Targeting_ServiceDesc, srv)
}

func _Targeting_IsMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TargetingServer).IsMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Targeting_IsMatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TargetingServer).IsMatch(ctx, req.(*IsMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Targeting_ServiceDesc is the grpc.ServiceDesc for Targeting service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Targeting_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "targeting.Targeting",
	HandlerType: (*TargetingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IsMatch",
			Handler:    _Targeting_IsMatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/targeting/targeting.proto",
}
