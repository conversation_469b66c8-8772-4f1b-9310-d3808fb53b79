// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: pb/game_private/game_private.proto

/*
Package game_private is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package game_private

import (
	"context"
	"io"
	"net/http"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/v2/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = metadata.Join

func request_GamePrivate_LuckyFarmLuckyMoment_0(ctx context.Context, marshaler runtime.Marshaler, client GamePrivateClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmLuckyMomentReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.LuckyFarmLuckyMoment(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePrivate_LuckyFarmLuckyMoment_0(ctx context.Context, marshaler runtime.Marshaler, server GamePrivateServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmLuckyMomentReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.LuckyFarmLuckyMoment(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePrivate_LuckyFarmAnimalEvent_0(ctx context.Context, marshaler runtime.Marshaler, client GamePrivateClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmAnimalEventReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.LuckyFarmAnimalEvent(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePrivate_LuckyFarmAnimalEvent_0(ctx context.Context, marshaler runtime.Marshaler, server GamePrivateServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmAnimalEventReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.LuckyFarmAnimalEvent(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePrivate_LuckyFarmUserSwitchEvent_0(ctx context.Context, marshaler runtime.Marshaler, client GamePrivateClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmUserSwitchEventReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.LuckyFarmUserSwitchEvent(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePrivate_LuckyFarmUserSwitchEvent_0(ctx context.Context, marshaler runtime.Marshaler, server GamePrivateServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmUserSwitchEventReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.LuckyFarmUserSwitchEvent(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePrivate_LuckyFarmNoAnimalEvent_0(ctx context.Context, marshaler runtime.Marshaler, client GamePrivateClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmNoAnimalEventReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.LuckyFarmNoAnimalEvent(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePrivate_LuckyFarmNoAnimalEvent_0(ctx context.Context, marshaler runtime.Marshaler, server GamePrivateServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq LuckyFarmNoAnimalEventReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.LuckyFarmNoAnimalEvent(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePrivate_SurpriseDancingGetRoomData_0(ctx context.Context, marshaler runtime.Marshaler, client GamePrivateClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SurpriseDancingGetRoomDataReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.SurpriseDancingGetRoomData(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePrivate_SurpriseDancingGetRoomData_0(ctx context.Context, marshaler runtime.Marshaler, server GamePrivateServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SurpriseDancingGetRoomDataReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.SurpriseDancingGetRoomData(ctx, &protoReq)
	return msg, metadata, err

}

func request_GamePrivate_SurpriseDancingGetLiveStatus_0(ctx context.Context, marshaler runtime.Marshaler, client GamePrivateClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SurpriseDancingGetLiveStatusReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.SurpriseDancingGetLiveStatus(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_GamePrivate_SurpriseDancingGetLiveStatus_0(ctx context.Context, marshaler runtime.Marshaler, server GamePrivateServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq SurpriseDancingGetLiveStatusReq
	var metadata runtime.ServerMetadata

	if err := marshaler.NewDecoder(req.Body).Decode(&protoReq); err != nil && err != io.EOF {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.SurpriseDancingGetLiveStatus(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterGamePrivateHandlerServer registers the http handlers for service GamePrivate to "mux".
// UnaryRPC     :call GamePrivateServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterGamePrivateHandlerFromEndpoint instead.
func RegisterGamePrivateHandlerServer(ctx context.Context, mux *runtime.ServeMux, server GamePrivateServer) error {

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmLuckyMoment_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmLuckyMoment", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmLuckyMoment"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePrivate_LuckyFarmLuckyMoment_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmLuckyMoment_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmAnimalEvent_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmAnimalEvent", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmAnimalEvent"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePrivate_LuckyFarmAnimalEvent_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmAnimalEvent_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmUserSwitchEvent_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmUserSwitchEvent", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmUserSwitchEvent"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePrivate_LuckyFarmUserSwitchEvent_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmUserSwitchEvent_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmNoAnimalEvent_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmNoAnimalEvent", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmNoAnimalEvent"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePrivate_LuckyFarmNoAnimalEvent_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmNoAnimalEvent_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_SurpriseDancingGetRoomData_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_private.GamePrivate/SurpriseDancingGetRoomData", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/SurpriseDancingGetRoomData"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePrivate_SurpriseDancingGetRoomData_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_SurpriseDancingGetRoomData_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_SurpriseDancingGetLiveStatus_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateIncomingContext(ctx, mux, req, "/kg.game_private.GamePrivate/SurpriseDancingGetLiveStatus", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/SurpriseDancingGetLiveStatus"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_GamePrivate_SurpriseDancingGetLiveStatus_0(annotatedContext, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_SurpriseDancingGetLiveStatus_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterGamePrivateHandlerFromEndpoint is same as RegisterGamePrivateHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterGamePrivateHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Errorf("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterGamePrivateHandler(ctx, mux, conn)
}

// RegisterGamePrivateHandler registers the http handlers for service GamePrivate to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterGamePrivateHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterGamePrivateHandlerClient(ctx, mux, NewGamePrivateClient(conn))
}

// RegisterGamePrivateHandlerClient registers the http handlers for service GamePrivate
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "GamePrivateClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "GamePrivateClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "GamePrivateClient" to call the correct interceptors.
func RegisterGamePrivateHandlerClient(ctx context.Context, mux *runtime.ServeMux, client GamePrivateClient) error {

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmLuckyMoment_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmLuckyMoment", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmLuckyMoment"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePrivate_LuckyFarmLuckyMoment_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmLuckyMoment_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmAnimalEvent_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmAnimalEvent", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmAnimalEvent"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePrivate_LuckyFarmAnimalEvent_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmAnimalEvent_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmUserSwitchEvent_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmUserSwitchEvent", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmUserSwitchEvent"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePrivate_LuckyFarmUserSwitchEvent_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmUserSwitchEvent_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_LuckyFarmNoAnimalEvent_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_private.GamePrivate/LuckyFarmNoAnimalEvent", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/LuckyFarmNoAnimalEvent"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePrivate_LuckyFarmNoAnimalEvent_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_LuckyFarmNoAnimalEvent_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_SurpriseDancingGetRoomData_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_private.GamePrivate/SurpriseDancingGetRoomData", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/SurpriseDancingGetRoomData"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePrivate_SurpriseDancingGetRoomData_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_SurpriseDancingGetRoomData_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	mux.Handle("POST", pattern_GamePrivate_SurpriseDancingGetLiveStatus_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		var err error
		var annotatedContext context.Context
		annotatedContext, err = runtime.AnnotateContext(ctx, mux, req, "/kg.game_private.GamePrivate/SurpriseDancingGetLiveStatus", runtime.WithHTTPPathPattern("/kg.game_private.GamePrivate/SurpriseDancingGetLiveStatus"))
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_GamePrivate_SurpriseDancingGetLiveStatus_0(annotatedContext, inboundMarshaler, client, req, pathParams)
		annotatedContext = runtime.NewServerMetadataContext(annotatedContext, md)
		if err != nil {
			runtime.HTTPError(annotatedContext, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_GamePrivate_SurpriseDancingGetLiveStatus_0(annotatedContext, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_GamePrivate_LuckyFarmLuckyMoment_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_private.GamePrivate", "LuckyFarmLuckyMoment"}, ""))

	pattern_GamePrivate_LuckyFarmAnimalEvent_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_private.GamePrivate", "LuckyFarmAnimalEvent"}, ""))

	pattern_GamePrivate_LuckyFarmUserSwitchEvent_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_private.GamePrivate", "LuckyFarmUserSwitchEvent"}, ""))

	pattern_GamePrivate_LuckyFarmNoAnimalEvent_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_private.GamePrivate", "LuckyFarmNoAnimalEvent"}, ""))

	pattern_GamePrivate_SurpriseDancingGetRoomData_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_private.GamePrivate", "SurpriseDancingGetRoomData"}, ""))

	pattern_GamePrivate_SurpriseDancingGetLiveStatus_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"kg.game_private.GamePrivate", "SurpriseDancingGetLiveStatus"}, ""))
)

var (
	forward_GamePrivate_LuckyFarmLuckyMoment_0 = runtime.ForwardResponseMessage

	forward_GamePrivate_LuckyFarmAnimalEvent_0 = runtime.ForwardResponseMessage

	forward_GamePrivate_LuckyFarmUserSwitchEvent_0 = runtime.ForwardResponseMessage

	forward_GamePrivate_LuckyFarmNoAnimalEvent_0 = runtime.ForwardResponseMessage

	forward_GamePrivate_SurpriseDancingGetRoomData_0 = runtime.ForwardResponseMessage

	forward_GamePrivate_SurpriseDancingGetLiveStatus_0 = runtime.ForwardResponseMessage
)
