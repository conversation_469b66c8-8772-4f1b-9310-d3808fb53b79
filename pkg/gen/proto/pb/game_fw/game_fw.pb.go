// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_fw/game_fw.proto

package game_fw

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFloatWindowsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Qua    string    `protobuf:"bytes,1,opt,name=qua,proto3" json:"qua,omitempty"`                          // k歌qua
	Device string    `protobuf:"bytes,2,opt,name=device,proto3" json:"device,omitempty"`                    // 设备信息
	Scene  SceneType `protobuf:"varint,3,opt,name=scene,proto3,enum=game.SceneType" json:"scene,omitempty"` // 场景
}

func (x *GetFloatWindowsReq) Reset() {
	*x = GetFloatWindowsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_fw_game_fw_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFloatWindowsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFloatWindowsReq) ProtoMessage() {}

func (x *GetFloatWindowsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_fw_game_fw_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFloatWindowsReq.ProtoReflect.Descriptor instead.
func (*GetFloatWindowsReq) Descriptor() ([]byte, []int) {
	return file_pb_game_fw_game_fw_proto_rawDescGZIP(), []int{0}
}

func (x *GetFloatWindowsReq) GetQua() string {
	if x != nil {
		return x.Qua
	}
	return ""
}

func (x *GetFloatWindowsReq) GetDevice() string {
	if x != nil {
		return x.Device
	}
	return ""
}

func (x *GetFloatWindowsReq) GetScene() SceneType {
	if x != nil {
		return x.Scene
	}
	return SceneType_SCENE_TYPE_NONE
}

type GetFloatWindowsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Style   StyleType       `protobuf:"varint,1,opt,name=style,proto3,enum=game.StyleType" json:"style,omitempty"` // 样式
	Default *DefaultWindows `protobuf:"bytes,2,opt,name=default,proto3" json:"default,omitempty"`                  // 默认样式
}

func (x *GetFloatWindowsRsp) Reset() {
	*x = GetFloatWindowsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_fw_game_fw_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFloatWindowsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFloatWindowsRsp) ProtoMessage() {}

func (x *GetFloatWindowsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_fw_game_fw_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFloatWindowsRsp.ProtoReflect.Descriptor instead.
func (*GetFloatWindowsRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_fw_game_fw_proto_rawDescGZIP(), []int{1}
}

func (x *GetFloatWindowsRsp) GetStyle() StyleType {
	if x != nil {
		return x.Style
	}
	return StyleType_STYLE_TYPE_DEFAULT
}

func (x *GetFloatWindowsRsp) GetDefault() *DefaultWindows {
	if x != nil {
		return x.Default
	}
	return nil
}

var File_pb_game_fw_game_fw_proto protoreflect.FileDescriptor

var file_pb_game_fw_game_fw_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x77, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x66, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x67, 0x61, 0x6d, 0x65,
	0x1a, 0x17, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x66, 0x77, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x65, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x71, 0x75,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65,
	0x22, 0x6b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x57, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x73, 0x52, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x53, 0x74, 0x79,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x2e, 0x0a,
	0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x57, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x73, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x32, 0x55, 0x0a,
	0x0c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x12, 0x45, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73,
	0x12, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x73, 0x52, 0x73, 0x70, 0x42, 0x3f, 0x5a, 0x3d, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d,
	0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x66, 0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_fw_game_fw_proto_rawDescOnce sync.Once
	file_pb_game_fw_game_fw_proto_rawDescData = file_pb_game_fw_game_fw_proto_rawDesc
)

func file_pb_game_fw_game_fw_proto_rawDescGZIP() []byte {
	file_pb_game_fw_game_fw_proto_rawDescOnce.Do(func() {
		file_pb_game_fw_game_fw_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_fw_game_fw_proto_rawDescData)
	})
	return file_pb_game_fw_game_fw_proto_rawDescData
}

var file_pb_game_fw_game_fw_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_game_fw_game_fw_proto_goTypes = []interface{}{
	(*GetFloatWindowsReq)(nil), // 0: game.GetFloatWindowsReq
	(*GetFloatWindowsRsp)(nil), // 1: game.GetFloatWindowsRsp
	(SceneType)(0),             // 2: game.SceneType
	(StyleType)(0),             // 3: game.StyleType
	(*DefaultWindows)(nil),     // 4: game.DefaultWindows
}
var file_pb_game_fw_game_fw_proto_depIdxs = []int32{
	2, // 0: game.GetFloatWindowsReq.scene:type_name -> game.SceneType
	3, // 1: game.GetFloatWindowsRsp.style:type_name -> game.StyleType
	4, // 2: game.GetFloatWindowsRsp.default:type_name -> game.DefaultWindows
	0, // 3: game.FloatWindows.GetFloatWindows:input_type -> game.GetFloatWindowsReq
	1, // 4: game.FloatWindows.GetFloatWindows:output_type -> game.GetFloatWindowsRsp
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_game_fw_game_fw_proto_init() }
func file_pb_game_fw_game_fw_proto_init() {
	if File_pb_game_fw_game_fw_proto != nil {
		return
	}
	file_pb_game_fw_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_game_fw_game_fw_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFloatWindowsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_fw_game_fw_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFloatWindowsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_fw_game_fw_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_fw_game_fw_proto_goTypes,
		DependencyIndexes: file_pb_game_fw_game_fw_proto_depIdxs,
		MessageInfos:      file_pb_game_fw_game_fw_proto_msgTypes,
	}.Build()
	File_pb_game_fw_game_fw_proto = out.File
	file_pb_game_fw_game_fw_proto_rawDesc = nil
	file_pb_game_fw_game_fw_proto_goTypes = nil
	file_pb_game_fw_game_fw_proto_depIdxs = nil
}
