{"swagger": "2.0", "info": {"title": "pb/stateful_router_monitor/monitor.proto", "version": "version not set"}, "tags": [{"name": "StatefulRouterMonitor"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/stateful_router.StatefulRouterMonitor/Query": {"post": {"operationId": "StatefulRouterMonitor_Query", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stateful_routerQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stateful_routerQueryReq"}}], "tags": ["StatefulRouterMonitor"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "stateful_routerQueryReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appid"}}}, "stateful_routerQueryRsp": {"type": "object", "properties": {"msg": {"type": "string"}}}}}