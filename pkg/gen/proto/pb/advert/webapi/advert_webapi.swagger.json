{"swagger": "2.0", "info": {"title": "pb/advert/webapi/advert_webapi.proto", "version": "version not set"}, "tags": [{"name": "AdvertWebapi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/advert_webapi.AdvertWebapi/AdvertInfo": {"post": {"summary": "获取广告信息", "operationId": "AdvertWebapi_AdvertInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/advert_webapiAdvertInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/advert_webapiAdvertInfoReq"}}], "tags": ["AdvertWebapi"]}}, "/advert_webapi.AdvertWebapi/AdvertReceiveReward": {"post": {"summary": "领取广告奖励", "operationId": "AdvertWebapi_AdvertReceiveReward", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/advert_webapiAdvertReceiveRewardRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/advert_webapiAdvertReceiveRewardReq"}}], "tags": ["AdvertWebapi"]}}}, "definitions": {"advert_webapiAdvertInfo": {"type": "object", "properties": {"incentiveType": {"$ref": "#/definitions/advert_webapiIncentiveType", "title": "广告奖励数值类型"}, "rewardNum": {"type": "string", "format": "uint64", "title": "后台计算出的金币值"}, "showAdvert": {"type": "boolean", "title": "是否展示广告"}, "leftAdvert": {"type": "integer", "format": "int64", "title": "剩余广告次数(仅用于前端展示次数，是否能看广告用showAdvert字段判断)"}}}, "advert_webapiAdvertInfoReq": {"type": "object", "properties": {"advertSceneList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/advert_webapiAdvertScene"}, "title": "广告场景"}}}, "advert_webapiAdvertInfoRsp": {"type": "object", "properties": {"advertInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/advert_webapiAdvertInfo"}, "title": "广告信息"}}}, "advert_webapiAdvertReceiveRewardReq": {"type": "object", "properties": {"adToken": {"type": "string", "title": "广告曝光token"}, "adPosId": {"type": "string", "title": "广告位id"}, "qimei36": {"type": "string"}, "sceneId": {"type": "string", "title": "场景id(可选)"}}}, "advert_webapiAdvertReceiveRewardRsp": {"type": "object", "properties": {"traceId": {"type": "string", "title": "广告曝光唯一id"}, "result": {"type": "integer", "format": "int32", "title": "领取结果，0是成功"}, "rewardNum": {"type": "string", "format": "uint64", "title": "广告ecpm数值奖励数量"}}}, "advert_webapiAdvertScene": {"type": "object", "properties": {"adPosId": {"type": "string", "title": "广告位id"}, "sceneId": {"type": "string", "title": "场景id(可选)"}}}, "advert_webapiIncentiveType": {"type": "string", "enum": ["IncentiveTypeNone", "IncentiveTypeBI", "IncentiveTypeECPM"], "default": "IncentiveTypeNone", "title": "- IncentiveTypeNone: 非法类型\n - IncentiveTypeBI: 使用后台下发的激励广告奖励数值\n - IncentiveTypeECPM: 使用商广返回的激励广告奖励数值（基于ecpm）"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}