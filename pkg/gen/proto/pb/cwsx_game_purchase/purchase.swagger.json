{"swagger": "2.0", "info": {"title": "pb/cwsx_game_purchase/purchase.proto", "version": "version not set"}, "tags": [{"name": "purchase"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/cwsx_game.purchase/NotifyCallback": {"get": {"summary": "发货", "operationId": "purchase_NotifyCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gameNotifyCallbackRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "appKey", "description": "app key", "in": "query", "required": false, "type": "string"}, {"name": "appOrderId", "description": "cp方订单号", "in": "query", "required": false, "type": "string"}, {"name": "appRoleId", "description": "游戏的角色id", "in": "query", "required": false, "type": "string"}, {"name": "orderId", "description": "sdk方订单号", "in": "query", "required": false, "type": "string"}, {"name": "payResult", "description": "是否支付 1表示已支付", "in": "query", "required": false, "type": "string"}, {"name": "productId", "description": "订单中道具id", "in": "query", "required": false, "type": "string"}, {"name": "serverId", "description": "游戏中区服id", "in": "query", "required": false, "type": "string"}, {"name": "totalFee", "description": "订单中的金额", "in": "query", "required": false, "type": "string"}, {"name": "userId", "description": "游戏中的用户id", "in": "query", "required": false, "type": "string"}, {"name": "time", "description": "时间戳", "in": "query", "required": false, "type": "string"}, {"name": "sign", "description": "签名字段", "in": "query", "required": false, "type": "string"}, {"name": "signReturn", "description": "sdk方使用的订单校验签名 cp方可以忽略", "in": "query", "required": false, "type": "string"}, {"name": "isSandbox", "description": "以下为v2版本参数\nhttps://d9pqpqvtzu.feishu.cn/wiki/JNXlwaTHGiTrfOkZa0bchDEUnpc\n\n是否为测试订单, 1是测试订单, 2是正常订单", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "channel", "description": "玩心发行平台ID,提供对接物料时应包含此项", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "xxGameId", "description": "玩心发行游戏ID", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "tp", "description": "客户端下单传入的透传参数，若存在回调时将原值返回,为空择不返回此参数", "in": "query", "required": false, "type": "string"}], "tags": ["purchase"]}, "post": {"summary": "发货", "operationId": "purchase_NotifyCallback2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gameNotifyCallbackRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_gameNotifyCallbackReq"}}], "tags": ["purchase"]}}, "/cwsx_game.purchase/PlaceOrder": {"post": {"summary": "下单", "operationId": "purchase_PlaceOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/cwsx_gamePlaceOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/cwsx_gamePlaceOrderReq"}}], "tags": ["purchase"]}}}, "definitions": {"cwsx_gameNotifyCallbackReq": {"type": "object", "properties": {"appKey": {"type": "string", "title": "app key"}, "appOrderId": {"type": "string", "title": "cp方订单号"}, "appRoleId": {"type": "string", "title": "游戏的角色id"}, "orderId": {"type": "string", "title": "sdk方订单号"}, "payResult": {"type": "string", "title": "是否支付 1表示已支付"}, "productId": {"type": "string", "title": "订单中道具id"}, "serverId": {"type": "string", "title": "游戏中区服id"}, "totalFee": {"type": "string", "title": "订单中的金额"}, "userId": {"type": "string", "title": "游戏中的用户id"}, "time": {"type": "string", "title": "时间戳"}, "sign": {"type": "string", "title": "签名字段"}, "signReturn": {"type": "string", "title": "sdk方使用的订单校验签名 cp方可以忽略"}, "isSandbox": {"type": "string", "format": "int64", "description": "是否为测试订单, 1是测试订单, 2是正常订单", "title": "以下为v2版本参数\nhttps://d9pqpqvtzu.feishu.cn/wiki/JNXlwaTHGiTrfOkZa0bchDEUnpc"}, "channel": {"type": "string", "format": "int64", "title": "玩心发行平台ID,提供对接物料时应包含此项"}, "xxGameId": {"type": "string", "format": "int64", "title": "玩心发行游戏ID"}, "tp": {"type": "string", "title": "客户端下单传入的透传参数，若存在回调时将原值返回,为空择不返回此参数"}}}, "cwsx_gameNotifyCallbackRsp": {"type": "object", "properties": {"ret": {"type": "integer", "format": "int64", "title": "1表示成功 非1表示失败"}, "msg": {"type": "string", "title": "信息"}}}, "cwsx_gameOperatingSystem": {"type": "string", "enum": ["OperatingSystemUnknown", "OperatingSystemAndroid", "OperatingSystemIOS"], "default": "OperatingSystemUnknown"}, "cwsx_gamePlaceOrderReq": {"type": "object", "properties": {"businessId": {"type": "string", "format": "int64"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品id"}, "price": {"type": "string", "format": "int64", "title": "价格"}, "os": {"$ref": "#/definitions/cwsx_gameOperatingSystem", "title": "系统"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}, "title": "greenId/wxSkuId\nwxEp 0=小程序 1=app"}}}, "cwsx_gamePlaceOrderRsp": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单id"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}