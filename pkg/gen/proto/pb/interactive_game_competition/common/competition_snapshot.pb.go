// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_competition/common/competition_snapshot.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SnapshotRunningMatchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigId    int64       `protobuf:"varint,1,opt,name=configId,proto3" json:"configId,omitempty"`
	MatchId     string      `protobuf:"bytes,2,opt,name=matchId,proto3" json:"matchId,omitempty"`
	MatchIndex  int64       `protobuf:"varint,3,opt,name=matchIndex,proto3" json:"matchIndex,omitempty"`
	MatchStatus MatchStatus `protobuf:"varint,4,opt,name=matchStatus,proto3,enum=game.common.MatchStatus" json:"matchStatus,omitempty"`
	MinTs       int64       `protobuf:"varint,5,opt,name=minTs,proto3" json:"minTs,omitempty"`
	MaxTs       int64       `protobuf:"varint,6,opt,name=maxTs,proto3" json:"maxTs,omitempty"`
}

func (x *SnapshotRunningMatchInfo) Reset() {
	*x = SnapshotRunningMatchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotRunningMatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotRunningMatchInfo) ProtoMessage() {}

func (x *SnapshotRunningMatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotRunningMatchInfo.ProtoReflect.Descriptor instead.
func (*SnapshotRunningMatchInfo) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescGZIP(), []int{0}
}

func (x *SnapshotRunningMatchInfo) GetConfigId() int64 {
	if x != nil {
		return x.ConfigId
	}
	return 0
}

func (x *SnapshotRunningMatchInfo) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *SnapshotRunningMatchInfo) GetMatchIndex() int64 {
	if x != nil {
		return x.MatchIndex
	}
	return 0
}

func (x *SnapshotRunningMatchInfo) GetMatchStatus() MatchStatus {
	if x != nil {
		return x.MatchStatus
	}
	return MatchStatus_MatchStatusNone
}

func (x *SnapshotRunningMatchInfo) GetMinTs() int64 {
	if x != nil {
		return x.MinTs
	}
	return 0
}

func (x *SnapshotRunningMatchInfo) GetMaxTs() int64 {
	if x != nil {
		return x.MaxTs
	}
	return 0
}

type SnapshotRunningMatchList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SnapshotRunningMatchInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Ts   int64                       `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (x *SnapshotRunningMatchList) Reset() {
	*x = SnapshotRunningMatchList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnapshotRunningMatchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotRunningMatchList) ProtoMessage() {}

func (x *SnapshotRunningMatchList) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotRunningMatchList.ProtoReflect.Descriptor instead.
func (*SnapshotRunningMatchList) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescGZIP(), []int{1}
}

func (x *SnapshotRunningMatchList) GetList() []*SnapshotRunningMatchInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SnapshotRunningMatchList) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

var File_pb_interactive_game_competition_common_competition_snapshot_proto protoreflect.FileDescriptor

var file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDesc = []byte{
	0x0a, 0x41, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x1a, 0x3e, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xd8, 0x01, 0x0a, 0x18, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x75, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x3a, 0x0a, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x54, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x6d, 0x69, 0x6e, 0x54, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x78, 0x54, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x61, 0x78, 0x54, 0x73, 0x22, 0x65, 0x0a, 0x18, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x74, 0x73, 0x42, 0x5b, 0x5a, 0x59, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f,
	0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74,
	0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescOnce sync.Once
	file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescData = file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDesc
)

func file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescData)
	})
	return file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDescData
}

var file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_interactive_game_competition_common_competition_snapshot_proto_goTypes = []interface{}{
	(*SnapshotRunningMatchInfo)(nil), // 0: game.common.SnapshotRunningMatchInfo
	(*SnapshotRunningMatchList)(nil), // 1: game.common.SnapshotRunningMatchList
	(MatchStatus)(0),                 // 2: game.common.MatchStatus
}
var file_pb_interactive_game_competition_common_competition_snapshot_proto_depIdxs = []int32{
	2, // 0: game.common.SnapshotRunningMatchInfo.matchStatus:type_name -> game.common.MatchStatus
	0, // 1: game.common.SnapshotRunningMatchList.list:type_name -> game.common.SnapshotRunningMatchInfo
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_competition_common_competition_snapshot_proto_init() }
func file_pb_interactive_game_competition_common_competition_snapshot_proto_init() {
	if File_pb_interactive_game_competition_common_competition_snapshot_proto != nil {
		return
	}
	file_pb_interactive_game_competition_common_competition_match_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotRunningMatchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnapshotRunningMatchList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_interactive_game_competition_common_competition_snapshot_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_competition_common_competition_snapshot_proto_depIdxs,
		MessageInfos:      file_pb_interactive_game_competition_common_competition_snapshot_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_competition_common_competition_snapshot_proto = out.File
	file_pb_interactive_game_competition_common_competition_snapshot_proto_rawDesc = nil
	file_pb_interactive_game_competition_common_competition_snapshot_proto_goTypes = nil
	file_pb_interactive_game_competition_common_competition_snapshot_proto_depIdxs = nil
}
