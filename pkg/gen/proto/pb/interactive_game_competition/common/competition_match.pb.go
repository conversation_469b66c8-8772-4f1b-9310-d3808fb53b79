// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_competition/common/competition_match.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ParticipantType 玩家类型
type ParticipantType int32

const (
	ParticipantType_ParticipantTypeUser  ParticipantType = 0 // 用户
	ParticipantType_ParticipantTypeRobot ParticipantType = 1 // 机器人
)

// Enum value maps for ParticipantType.
var (
	ParticipantType_name = map[int32]string{
		0: "ParticipantTypeUser",
		1: "ParticipantTypeRobot",
	}
	ParticipantType_value = map[string]int32{
		"ParticipantTypeUser":  0,
		"ParticipantTypeRobot": 1,
	}
)

func (x ParticipantType) Enum() *ParticipantType {
	p := new(ParticipantType)
	*p = x
	return p
}

func (x ParticipantType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[0].Descriptor()
}

func (ParticipantType) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[0]
}

func (x ParticipantType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantType.Descriptor instead.
func (ParticipantType) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{0}
}

// 用户全局状态
type ParticipantStatus int32

const (
	ParticipantStatus_ParticipantStatusNone       ParticipantStatus = 0 // 无
	ParticipantStatus_ParticipantStatusSignUp     ParticipantStatus = 1 // 已报名
	ParticipantStatus_ParticipantStatusInprogress ParticipantStatus = 2 // 比赛中
	ParticipantStatus_ParticipantStatusCanceled   ParticipantStatus = 3 // 超时退赛
	ParticipantStatus_ParticipantStatusWaitResult ParticipantStatus = 4 // 等待结算
	ParticipantStatus_ParticipantStatusEliminated ParticipantStatus = 5 // 已淘汰
	ParticipantStatus_ParticipantStatusFinished   ParticipantStatus = 6 // 已结束
)

// Enum value maps for ParticipantStatus.
var (
	ParticipantStatus_name = map[int32]string{
		0: "ParticipantStatusNone",
		1: "ParticipantStatusSignUp",
		2: "ParticipantStatusInprogress",
		3: "ParticipantStatusCanceled",
		4: "ParticipantStatusWaitResult",
		5: "ParticipantStatusEliminated",
		6: "ParticipantStatusFinished",
	}
	ParticipantStatus_value = map[string]int32{
		"ParticipantStatusNone":       0,
		"ParticipantStatusSignUp":     1,
		"ParticipantStatusInprogress": 2,
		"ParticipantStatusCanceled":   3,
		"ParticipantStatusWaitResult": 4,
		"ParticipantStatusEliminated": 5,
		"ParticipantStatusFinished":   6,
	}
)

func (x ParticipantStatus) Enum() *ParticipantStatus {
	p := new(ParticipantStatus)
	*p = x
	return p
}

func (x ParticipantStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[1].Descriptor()
}

func (ParticipantStatus) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[1]
}

func (x ParticipantStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantStatus.Descriptor instead.
func (ParticipantStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{1}
}

// 用户回合内状态
type ParticipantStageStatus int32

const (
	ParticipantStageStatus_ParticipantStageStatusWaitting   ParticipantStageStatus = 0 // 等待中
	ParticipantStageStatus_ParticipantStageStatusInprogress ParticipantStageStatus = 1 // 比赛中
	ParticipantStageStatus_ParticipantStageStatusPromoted   ParticipantStageStatus = 2 // 已晋级
	ParticipantStageStatus_ParticipantStageStatusEliminated ParticipantStageStatus = 3 // 已淘汰
)

// Enum value maps for ParticipantStageStatus.
var (
	ParticipantStageStatus_name = map[int32]string{
		0: "ParticipantStageStatusWaitting",
		1: "ParticipantStageStatusInprogress",
		2: "ParticipantStageStatusPromoted",
		3: "ParticipantStageStatusEliminated",
	}
	ParticipantStageStatus_value = map[string]int32{
		"ParticipantStageStatusWaitting":   0,
		"ParticipantStageStatusInprogress": 1,
		"ParticipantStageStatusPromoted":   2,
		"ParticipantStageStatusEliminated": 3,
	}
)

func (x ParticipantStageStatus) Enum() *ParticipantStageStatus {
	p := new(ParticipantStageStatus)
	*p = x
	return p
}

func (x ParticipantStageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParticipantStageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[2].Descriptor()
}

func (ParticipantStageStatus) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[2]
}

func (x ParticipantStageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParticipantStageStatus.Descriptor instead.
func (ParticipantStageStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{2}
}

// MatchStatus 轮次状态(第x轮)
type MatchStatus int32

const (
	MatchStatus_MatchStatusNone       MatchStatus = 0 // 无
	MatchStatus_MatchStatusCreated    MatchStatus = 1 // 已创建
	MatchStatus_MatchStatusSignUp     MatchStatus = 2 // 报名中(提前报名、准备中)
	MatchStatus_MatchStatusInProgress MatchStatus = 3 // 进行中
	MatchStatus_MatchStatusFinished   MatchStatus = 4 // 已结束
	MatchStatus_MatchStatusCanceled   MatchStatus = 5 // 已取消
)

// Enum value maps for MatchStatus.
var (
	MatchStatus_name = map[int32]string{
		0: "MatchStatusNone",
		1: "MatchStatusCreated",
		2: "MatchStatusSignUp",
		3: "MatchStatusInProgress",
		4: "MatchStatusFinished",
		5: "MatchStatusCanceled",
	}
	MatchStatus_value = map[string]int32{
		"MatchStatusNone":       0,
		"MatchStatusCreated":    1,
		"MatchStatusSignUp":     2,
		"MatchStatusInProgress": 3,
		"MatchStatusFinished":   4,
		"MatchStatusCanceled":   5,
	}
)

func (x MatchStatus) Enum() *MatchStatus {
	p := new(MatchStatus)
	*p = x
	return p
}

func (x MatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[3].Descriptor()
}

func (MatchStatus) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[3]
}

func (x MatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchStatus.Descriptor instead.
func (MatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{3}
}

// StageStatus 阶段状态(第x轮第n局)
type StageStatus int32

const (
	StageStatus_StageStatusNone       StageStatus = 0 // 无
	StageStatus_StageStatusWaitting   StageStatus = 1 // 等待中
	StageStatus_StageStatusInProgress StageStatus = 2 // 比赛中
	StageStatus_StageStatusFinished   StageStatus = 3 // 已结束
)

// Enum value maps for StageStatus.
var (
	StageStatus_name = map[int32]string{
		0: "StageStatusNone",
		1: "StageStatusWaitting",
		2: "StageStatusInProgress",
		3: "StageStatusFinished",
	}
	StageStatus_value = map[string]int32{
		"StageStatusNone":       0,
		"StageStatusWaitting":   1,
		"StageStatusInProgress": 2,
		"StageStatusFinished":   3,
	}
)

func (x StageStatus) Enum() *StageStatus {
	p := new(StageStatus)
	*p = x
	return p
}

func (x StageStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StageStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[4].Descriptor()
}

func (StageStatus) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_competition_common_competition_match_proto_enumTypes[4]
}

func (x StageStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StageStatus.Descriptor instead.
func (StageStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{4}
}

// Participant 参赛人员信息
type Participant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId        string            `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`                                     // 用户ID
	Status        ParticipantStatus `protobuf:"varint,2,opt,name=status,proto3,enum=game.common.ParticipantStatus" json:"status,omitempty"` // 玩家状态
	Typ           ParticipantType   `protobuf:"varint,3,opt,name=typ,proto3,enum=game.common.ParticipantType" json:"typ,omitempty"`         // 真人/机器人
	CurStageIndex int32             `protobuf:"varint,4,opt,name=curStageIndex,proto3" json:"curStageIndex,omitempty"`                      // 当前所处阶段
	TotalScore    int64             `protobuf:"varint,5,opt,name=totalScore,proto3" json:"totalScore,omitempty"`                            // 本场总积分
	Rank          int64             `protobuf:"varint,6,opt,name=rank,proto3" json:"rank,omitempty"`                                        // 本场积分排名
	Reward        *UserReward       `protobuf:"bytes,7,opt,name=reward,proto3" json:"reward,omitempty"`                                     // 奖品信息
}

func (x *Participant) Reset() {
	*x = Participant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Participant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Participant) ProtoMessage() {}

func (x *Participant) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Participant.ProtoReflect.Descriptor instead.
func (*Participant) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{0}
}

func (x *Participant) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *Participant) GetStatus() ParticipantStatus {
	if x != nil {
		return x.Status
	}
	return ParticipantStatus_ParticipantStatusNone
}

func (x *Participant) GetTyp() ParticipantType {
	if x != nil {
		return x.Typ
	}
	return ParticipantType_ParticipantTypeUser
}

func (x *Participant) GetCurStageIndex() int32 {
	if x != nil {
		return x.CurStageIndex
	}
	return 0
}

func (x *Participant) GetTotalScore() int64 {
	if x != nil {
		return x.TotalScore
	}
	return 0
}

func (x *Participant) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *Participant) GetReward() *UserReward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// Match 轮次信息
type Match struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                       // 轮次ID
	ConfId        int64          `protobuf:"varint,2,opt,name=confId,proto3" json:"confId,omitempty"`                              // 赛事配置ID
	MatchIndex    int64          `protobuf:"varint,3,opt,name=matchIndex,proto3" json:"matchIndex,omitempty"`                      // 当前轮次
	StageIndex    int64          `protobuf:"varint,4,opt,name=stageIndex,proto3" json:"stageIndex,omitempty"`                      // 当前所处阶段
	MatchDesc     string         `protobuf:"bytes,5,opt,name=matchDesc,proto3" json:"matchDesc,omitempty"`                         // 7点-9点场
	Stages        []*Stage       `protobuf:"bytes,6,rep,name=stages,proto3" json:"stages,omitempty"`                               // 阶段信息
	Participants  []*Participant `protobuf:"bytes,7,rep,name=participants,proto3" json:"participants,omitempty"`                   // 参赛列表
	Status        MatchStatus    `protobuf:"varint,8,opt,name=status,proto3,enum=game.common.MatchStatus" json:"status,omitempty"` // 轮次状态
	CreateTs      int64          `protobuf:"varint,9,opt,name=createTs,proto3" json:"createTs,omitempty"`                          // 创建时间
	EndTs         int64          `protobuf:"varint,10,opt,name=endTs,proto3" json:"endTs,omitempty"`                               // 结束时间
	FirstSignUpTs int64          `protobuf:"varint,11,opt,name=firstSignUpTs,proto3" json:"firstSignUpTs,omitempty"`               //第一个开始报名的时间
}

func (x *Match) Reset() {
	*x = Match{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Match) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Match) ProtoMessage() {}

func (x *Match) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Match.ProtoReflect.Descriptor instead.
func (*Match) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{1}
}

func (x *Match) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Match) GetConfId() int64 {
	if x != nil {
		return x.ConfId
	}
	return 0
}

func (x *Match) GetMatchIndex() int64 {
	if x != nil {
		return x.MatchIndex
	}
	return 0
}

func (x *Match) GetStageIndex() int64 {
	if x != nil {
		return x.StageIndex
	}
	return 0
}

func (x *Match) GetMatchDesc() string {
	if x != nil {
		return x.MatchDesc
	}
	return ""
}

func (x *Match) GetStages() []*Stage {
	if x != nil {
		return x.Stages
	}
	return nil
}

func (x *Match) GetParticipants() []*Participant {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *Match) GetStatus() MatchStatus {
	if x != nil {
		return x.Status
	}
	return MatchStatus_MatchStatusNone
}

func (x *Match) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Match) GetEndTs() int64 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *Match) GetFirstSignUpTs() int64 {
	if x != nil {
		return x.FirstSignUpTs
	}
	return 0
}

// Stage 轮次参赛阶段信息
type Stage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StageIndex    int32        `protobuf:"varint,1,opt,name=stageIndex,proto3" json:"stageIndex,omitempty"`                      // 阶段
	Rooms         []*StageRoom `protobuf:"bytes,2,rep,name=rooms,proto3" json:"rooms,omitempty"`                                 // 房间信息
	Status        StageStatus  `protobuf:"varint,3,opt,name=status,proto3,enum=game.common.StageStatus" json:"status,omitempty"` // 阶段状态
	Rank          []*StageRank `protobuf:"bytes,4,rep,name=rank,proto3" json:"rank,omitempty"`                                   // 排名信息
	CreateTs      int64        `protobuf:"varint,6,opt,name=createTs,proto3" json:"createTs,omitempty"`                          // 创建时间
	EndTs         int64        `protobuf:"varint,7,opt,name=endTs,proto3" json:"endTs,omitempty"`                                // 结束时间
	RankedRoomNum int64        `protobuf:"varint,5,opt,name=rankedRoomNum,proto3" json:"rankedRoomNum,omitempty"`                // 已经结算的房间数量
}

func (x *Stage) Reset() {
	*x = Stage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Stage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stage) ProtoMessage() {}

func (x *Stage) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stage.ProtoReflect.Descriptor instead.
func (*Stage) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{2}
}

func (x *Stage) GetStageIndex() int32 {
	if x != nil {
		return x.StageIndex
	}
	return 0
}

func (x *Stage) GetRooms() []*StageRoom {
	if x != nil {
		return x.Rooms
	}
	return nil
}

func (x *Stage) GetStatus() StageStatus {
	if x != nil {
		return x.Status
	}
	return StageStatus_StageStatusNone
}

func (x *Stage) GetRank() []*StageRank {
	if x != nil {
		return x.Rank
	}
	return nil
}

func (x *Stage) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Stage) GetEndTs() int64 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *Stage) GetRankedRoomNum() int64 {
	if x != nil {
		return x.RankedRoomNum
	}
	return 0
}

// StageUser 用户
type StageUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string                 `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Status ParticipantStageStatus `protobuf:"varint,2,opt,name=status,proto3,enum=game.common.ParticipantStageStatus" json:"status,omitempty"`
}

func (x *StageUser) Reset() {
	*x = StageUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageUser) ProtoMessage() {}

func (x *StageUser) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageUser.ProtoReflect.Descriptor instead.
func (*StageUser) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{3}
}

func (x *StageUser) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *StageUser) GetStatus() ParticipantStageStatus {
	if x != nil {
		return x.Status
	}
	return ParticipantStageStatus_ParticipantStageStatusWaitting
}

// StageRoom 分组信息
type StageRoom struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string       `protobuf:"bytes,1,opt,name=roomId,proto3" json:"roomId,omitempty"`      // 分配的房间Id
	Users    []*StageUser `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`        // 房间参赛人员
	CreateTs int64        `protobuf:"varint,3,opt,name=createTs,proto3" json:"createTs,omitempty"` // 创建时间
	Ranked   bool         `protobuf:"varint,4,opt,name=ranked,proto3" json:"ranked,omitempty"`     // 是否已经结算
}

func (x *StageRoom) Reset() {
	*x = StageRoom{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageRoom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageRoom) ProtoMessage() {}

func (x *StageRoom) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageRoom.ProtoReflect.Descriptor instead.
func (*StageRoom) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{4}
}

func (x *StageRoom) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *StageRoom) GetUsers() []*StageUser {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *StageRoom) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *StageRoom) GetRanked() bool {
	if x != nil {
		return x.Ranked
	}
	return false
}

// StageRank
type StageRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId    string `protobuf:"bytes,1,opt,name=openId,proto3" json:"openId,omitempty"`
	Score     int64  `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`         // 积分
	RankIndex int64  `protobuf:"varint,3,opt,name=rankIndex,proto3" json:"rankIndex,omitempty"` // 排名
	Promoted  bool   `protobuf:"varint,4,opt,name=promoted,proto3" json:"promoted,omitempty"`   // 是否晋级
}

func (x *StageRank) Reset() {
	*x = StageRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageRank) ProtoMessage() {}

func (x *StageRank) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageRank.ProtoReflect.Descriptor instead.
func (*StageRank) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP(), []int{5}
}

func (x *StageRank) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *StageRank) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *StageRank) GetRankIndex() int64 {
	if x != nil {
		return x.RankIndex
	}
	return 0
}

func (x *StageRank) GetPromoted() bool {
	if x != nil {
		return x.Promoted
	}
	return false
}

var File_pb_interactive_game_competition_common_competition_match_proto protoreflect.FileDescriptor

var file_pb_interactive_game_competition_common_competition_match_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x40, 0x70,
	0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x98, 0x02, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2e, 0x0a, 0x03, 0x74, 0x79, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x03, 0x74, 0x79, 0x70, 0x12,
	0x24, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x81, 0x03, 0x0a, 0x05, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x73, 0x63, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x6e, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x54, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x66, 0x69, 0x72, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x54, 0x73, 0x22, 0x8b,
	0x02, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x6f, 0x6f, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x52,
	0x05, 0x72, 0x6f, 0x6f, 0x6d, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x04,
	0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x61, 0x6e, 0x6b, 0x65, 0x64,
	0x52, 0x6f, 0x6f, 0x6d, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72,
	0x61, 0x6e, 0x6b, 0x65, 0x64, 0x52, 0x6f, 0x6f, 0x6d, 0x4e, 0x75, 0x6d, 0x22, 0x60, 0x0a, 0x09,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x85,
	0x01, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x6f, 0x6f, 0x6d, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f,
	0x6f, 0x6d, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x61, 0x6e, 0x6b, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x72, 0x61, 0x6e, 0x6b, 0x65, 0x64, 0x22, 0x73, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52,
	0x61, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x64, 0x2a, 0x44, 0x0a, 0x0f, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17,
	0x0a, 0x13, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x10,
	0x01, 0x2a, 0xec, 0x01, 0x0a, 0x11, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x10, 0x01, 0x12,
	0x1f, 0x0a, 0x1b, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x10, 0x02,
	0x12, 0x1d, 0x0a, 0x19, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0x03, 0x12,
	0x1f, 0x0a, 0x1b, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x57, 0x61, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x10, 0x04,
	0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x10,
	0x05, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x06,
	0x2a, 0xac, 0x01, 0x0a, 0x16, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x61, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12,
	0x24, 0x0a, 0x20, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50,
	0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x65, 0x64, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x6c, 0x69, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x10, 0x03, 0x2a,
	0x9e, 0x01, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x13, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x6f,
	0x6e, 0x65, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x55,
	0x70, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x10, 0x03, 0x12, 0x17,
	0x0a, 0x13, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x65, 0x64, 0x10, 0x05,
	0x2a, 0x6f, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x13, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x6f,
	0x6e, 0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x57, 0x61, 0x69, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x67,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x10,
	0x03, 0x42, 0x5b, 0x5a, 0x59, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d,
	0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x65, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_competition_common_competition_match_proto_rawDescOnce sync.Once
	file_pb_interactive_game_competition_common_competition_match_proto_rawDescData = file_pb_interactive_game_competition_common_competition_match_proto_rawDesc
)

func file_pb_interactive_game_competition_common_competition_match_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_competition_common_competition_match_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_competition_common_competition_match_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_competition_common_competition_match_proto_rawDescData)
	})
	return file_pb_interactive_game_competition_common_competition_match_proto_rawDescData
}

var file_pb_interactive_game_competition_common_competition_match_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_pb_interactive_game_competition_common_competition_match_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pb_interactive_game_competition_common_competition_match_proto_goTypes = []interface{}{
	(ParticipantType)(0),        // 0: game.common.ParticipantType
	(ParticipantStatus)(0),      // 1: game.common.ParticipantStatus
	(ParticipantStageStatus)(0), // 2: game.common.ParticipantStageStatus
	(MatchStatus)(0),            // 3: game.common.MatchStatus
	(StageStatus)(0),            // 4: game.common.StageStatus
	(*Participant)(nil),         // 5: game.common.Participant
	(*Match)(nil),               // 6: game.common.Match
	(*Stage)(nil),               // 7: game.common.Stage
	(*StageUser)(nil),           // 8: game.common.StageUser
	(*StageRoom)(nil),           // 9: game.common.StageRoom
	(*StageRank)(nil),           // 10: game.common.StageRank
	(*UserReward)(nil),          // 11: game.common.UserReward
}
var file_pb_interactive_game_competition_common_competition_match_proto_depIdxs = []int32{
	1,  // 0: game.common.Participant.status:type_name -> game.common.ParticipantStatus
	0,  // 1: game.common.Participant.typ:type_name -> game.common.ParticipantType
	11, // 2: game.common.Participant.reward:type_name -> game.common.UserReward
	7,  // 3: game.common.Match.stages:type_name -> game.common.Stage
	5,  // 4: game.common.Match.participants:type_name -> game.common.Participant
	3,  // 5: game.common.Match.status:type_name -> game.common.MatchStatus
	9,  // 6: game.common.Stage.rooms:type_name -> game.common.StageRoom
	4,  // 7: game.common.Stage.status:type_name -> game.common.StageStatus
	10, // 8: game.common.Stage.rank:type_name -> game.common.StageRank
	2,  // 9: game.common.StageUser.status:type_name -> game.common.ParticipantStageStatus
	8,  // 10: game.common.StageRoom.users:type_name -> game.common.StageUser
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_competition_common_competition_match_proto_init() }
func file_pb_interactive_game_competition_common_competition_match_proto_init() {
	if File_pb_interactive_game_competition_common_competition_match_proto != nil {
		return
	}
	file_pb_interactive_game_competition_common_competition_history_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Participant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Match); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Stage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageRoom); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_match_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_competition_common_competition_match_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_interactive_game_competition_common_competition_match_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_competition_common_competition_match_proto_depIdxs,
		EnumInfos:         file_pb_interactive_game_competition_common_competition_match_proto_enumTypes,
		MessageInfos:      file_pb_interactive_game_competition_common_competition_match_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_competition_common_competition_match_proto = out.File
	file_pb_interactive_game_competition_common_competition_match_proto_rawDesc = nil
	file_pb_interactive_game_competition_common_competition_match_proto_goTypes = nil
	file_pb_interactive_game_competition_common_competition_match_proto_depIdxs = nil
}
