// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/interactive_game_competition/common/competition_config.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PayMode 付费模式
type PayMode int32

const (
	PayMode_PayFree     PayMode = 0
	PayMode_PayRequired PayMode = 1 // 付费场
	PayMode_PayFlower   PayMode = 2 // 鲜花礼物道具场
)

// Enum value maps for PayMode.
var (
	PayMode_name = map[int32]string{
		0: "PayFree",
		1: "PayRequired",
		2: "PayFlower",
	}
	PayMode_value = map[string]int32{
		"PayFree":     0,
		"PayRequired": 1,
		"PayFlower":   2,
	}
)

func (x PayMode) Enum() *PayMode {
	p := new(PayMode)
	*p = x
	return p
}

func (x PayMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayMode) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_interactive_game_competition_common_competition_config_proto_enumTypes[0].Descriptor()
}

func (PayMode) Type() protoreflect.EnumType {
	return &file_pb_interactive_game_competition_common_competition_config_proto_enumTypes[0]
}

func (x PayMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayMode.Descriptor instead.
func (PayMode) EnumDescriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{0}
}

// StageConfig 赛事阶段信息
type StageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index         int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`                 // 阶段序号
	InitPlayerNum int32 `protobuf:"varint,2,opt,name=initPlayerNum,proto3" json:"initPlayerNum,omitempty"` // 初始人数
	PromotionNum  int32 `protobuf:"varint,3,opt,name=promotionNum,proto3" json:"promotionNum,omitempty"`   // 晋级人数
}

func (x *StageConfig) Reset() {
	*x = StageConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StageConfig) ProtoMessage() {}

func (x *StageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StageConfig.ProtoReflect.Descriptor instead.
func (*StageConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{0}
}

func (x *StageConfig) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *StageConfig) GetInitPlayerNum() int32 {
	if x != nil {
		return x.InitPlayerNum
	}
	return 0
}

func (x *StageConfig) GetPromotionNum() int32 {
	if x != nil {
		return x.PromotionNum
	}
	return 0
}

// PayConfig 报名付费模式
type PayConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  int32   `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`                          // 资产ID
	AssetNum int32   `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"`                        // 报名费
	ModeId   string  `protobuf:"bytes,3,opt,name=modeId,proto3" json:"modeId,omitempty"`                             // 模式
	ModeName string  `protobuf:"bytes,4,opt,name=modeName,proto3" json:"modeName,omitempty"`                         // 模式名称
	PayMode  PayMode `protobuf:"varint,5,opt,name=payMode,proto3,enum=game.common.PayMode" json:"payMode,omitempty"` // 付费模式
}

func (x *PayConfig) Reset() {
	*x = PayConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayConfig) ProtoMessage() {}

func (x *PayConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayConfig.ProtoReflect.Descriptor instead.
func (*PayConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{1}
}

func (x *PayConfig) GetAssetId() int32 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *PayConfig) GetAssetNum() int32 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

func (x *PayConfig) GetModeId() string {
	if x != nil {
		return x.ModeId
	}
	return ""
}

func (x *PayConfig) GetModeName() string {
	if x != nil {
		return x.ModeName
	}
	return ""
}

func (x *PayConfig) GetPayMode() PayMode {
	if x != nil {
		return x.PayMode
	}
	return PayMode_PayFree
}

// RobotConfig 机器人配置
type RobotConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"` // 是否允许机器人加入
	Mode   string `protobuf:"bytes,2,opt,name=mode,proto3" json:"mode,omitempty"`      // 算法模式(default)
}

func (x *RobotConfig) Reset() {
	*x = RobotConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotConfig) ProtoMessage() {}

func (x *RobotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotConfig.ProtoReflect.Descriptor instead.
func (*RobotConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{2}
}

func (x *RobotConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *RobotConfig) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

// OperateConfig 运营配置
type OperateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeginDateTime      int32             `protobuf:"varint,1,opt,name=beginDateTime,proto3" json:"beginDateTime,omitempty"`                                                                            // 赛事开始时间
	EndDateTime        int32             `protobuf:"varint,2,opt,name=endDateTime,proto3" json:"endDateTime,omitempty"`                                                                                // 赛事结束时间
	BeginDayMinute     int32             `protobuf:"varint,3,opt,name=beginDayMinute,proto3" json:"beginDayMinute,omitempty"`                                                                          // 每日开始时间
	EndDayMinute       int32             `protobuf:"varint,4,opt,name=endDayMinute,proto3" json:"endDayMinute,omitempty"`                                                                              // 每日结束时间
	ShowAdvanceSeconds int32             `protobuf:"varint,5,opt,name=showAdvanceSeconds,proto3" json:"showAdvanceSeconds,omitempty"`                                                                  // 开始前x秒可参与报名
	ExtInfo            map[string]string `protobuf:"bytes,6,rep,name=extInfo,proto3" json:"extInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 其他运营配置
}

func (x *OperateConfig) Reset() {
	*x = OperateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OperateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperateConfig) ProtoMessage() {}

func (x *OperateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperateConfig.ProtoReflect.Descriptor instead.
func (*OperateConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{3}
}

func (x *OperateConfig) GetBeginDateTime() int32 {
	if x != nil {
		return x.BeginDateTime
	}
	return 0
}

func (x *OperateConfig) GetEndDateTime() int32 {
	if x != nil {
		return x.EndDateTime
	}
	return 0
}

func (x *OperateConfig) GetBeginDayMinute() int32 {
	if x != nil {
		return x.BeginDayMinute
	}
	return 0
}

func (x *OperateConfig) GetEndDayMinute() int32 {
	if x != nil {
		return x.EndDayMinute
	}
	return 0
}

func (x *OperateConfig) GetShowAdvanceSeconds() int32 {
	if x != nil {
		return x.ShowAdvanceSeconds
	}
	return 0
}

func (x *OperateConfig) GetExtInfo() map[string]string {
	if x != nil {
		return x.ExtInfo
	}
	return nil
}

type RewardRankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index    int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`       // 名次
	AssetNum int32 `protobuf:"varint,2,opt,name=assetNum,proto3" json:"assetNum,omitempty"` // 可发的资产数量
}

func (x *RewardRankItem) Reset() {
	*x = RewardRankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardRankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardRankItem) ProtoMessage() {}

func (x *RewardRankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardRankItem.ProtoReflect.Descriptor instead.
func (*RewardRankItem) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{4}
}

func (x *RewardRankItem) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *RewardRankItem) GetAssetNum() int32 {
	if x != nil {
		return x.AssetNum
	}
	return 0
}

// RewardConfig 发奖配置
type RewardConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId     int64             `protobuf:"varint,1,opt,name=assetId,proto3" json:"assetId,omitempty"`        // 奖品资产ID
	RankRewards []*RewardRankItem `protobuf:"bytes,2,rep,name=rankRewards,proto3" json:"rankRewards,omitempty"` // 每个名次的奖品
	Total       int64             `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`            // 总奖池信息
}

func (x *RewardConfig) Reset() {
	*x = RewardConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardConfig) ProtoMessage() {}

func (x *RewardConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardConfig.ProtoReflect.Descriptor instead.
func (*RewardConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{5}
}

func (x *RewardConfig) GetAssetId() int64 {
	if x != nil {
		return x.AssetId
	}
	return 0
}

func (x *RewardConfig) GetRankRewards() []*RewardRankItem {
	if x != nil {
		return x.RankRewards
	}
	return nil
}

func (x *RewardConfig) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// TransparentConfig 透传配置
type TransparentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransConfig map[string]string `protobuf:"bytes,1,rep,name=transConfig,proto3" json:"transConfig,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 透传配置
}

func (x *TransparentConfig) Reset() {
	*x = TransparentConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransparentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransparentConfig) ProtoMessage() {}

func (x *TransparentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransparentConfig.ProtoReflect.Descriptor instead.
func (*TransparentConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{6}
}

func (x *TransparentConfig) GetTransConfig() map[string]string {
	if x != nil {
		return x.TransConfig
	}
	return nil
}

// CompetitionConfig 赛制配置
type CompetitionConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    int64              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                       // 配置ID
	AppId                 string             `protobuf:"bytes,2,opt,name=appId,proto3" json:"appId,omitempty"`                                  // 小游戏APPID
	InitParticipantsNum   int64              `protobuf:"varint,4,opt,name=initParticipantsNum,proto3" json:"initParticipantsNum,omitempty"`     // 初始参赛人数
	InitSignupWaitSeconds int32              `protobuf:"varint,5,opt,name=initSignupWaitSeconds,proto3" json:"initSignupWaitSeconds,omitempty"` // 成局等待时长
	StageNum              int32              `protobuf:"varint,6,opt,name=stageNum,proto3" json:"stageNum,omitempty"`                           // 比赛局数
	StageInfo             []*StageConfig     `protobuf:"bytes,7,rep,name=stageInfo,proto3" json:"stageInfo,omitempty"`                          // 赛事阶段配置
	PayInfo               []*PayConfig       `protobuf:"bytes,8,rep,name=payInfo,proto3" json:"payInfo,omitempty"`                              // 付费配置
	RobotInfo             *RobotConfig       `protobuf:"bytes,9,opt,name=robotInfo,proto3" json:"robotInfo,omitempty"`                          // 机器人配置
	OperateInfo           *OperateConfig     `protobuf:"bytes,10,opt,name=operateInfo,proto3" json:"operateInfo,omitempty"`                     // 运营配置
	TransInfo             *TransparentConfig `protobuf:"bytes,11,opt,name=transInfo,proto3" json:"transInfo,omitempty"`                         // 透传配置
	RewardInfo            []*RewardConfig    `protobuf:"bytes,12,rep,name=rewardInfo,proto3" json:"rewardInfo,omitempty"`                       // 发奖配置
	Enable                bool               `protobuf:"varint,13,opt,name=enable,proto3" json:"enable,omitempty"`                              // 是否上架
	RoomSize              int32              `protobuf:"varint,14,opt,name=roomSize,proto3" json:"roomSize,omitempty"`                          // 单房间参赛人数
}

func (x *CompetitionConfig) Reset() {
	*x = CompetitionConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompetitionConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompetitionConfig) ProtoMessage() {}

func (x *CompetitionConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompetitionConfig.ProtoReflect.Descriptor instead.
func (*CompetitionConfig) Descriptor() ([]byte, []int) {
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP(), []int{7}
}

func (x *CompetitionConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CompetitionConfig) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CompetitionConfig) GetInitParticipantsNum() int64 {
	if x != nil {
		return x.InitParticipantsNum
	}
	return 0
}

func (x *CompetitionConfig) GetInitSignupWaitSeconds() int32 {
	if x != nil {
		return x.InitSignupWaitSeconds
	}
	return 0
}

func (x *CompetitionConfig) GetStageNum() int32 {
	if x != nil {
		return x.StageNum
	}
	return 0
}

func (x *CompetitionConfig) GetStageInfo() []*StageConfig {
	if x != nil {
		return x.StageInfo
	}
	return nil
}

func (x *CompetitionConfig) GetPayInfo() []*PayConfig {
	if x != nil {
		return x.PayInfo
	}
	return nil
}

func (x *CompetitionConfig) GetRobotInfo() *RobotConfig {
	if x != nil {
		return x.RobotInfo
	}
	return nil
}

func (x *CompetitionConfig) GetOperateInfo() *OperateConfig {
	if x != nil {
		return x.OperateInfo
	}
	return nil
}

func (x *CompetitionConfig) GetTransInfo() *TransparentConfig {
	if x != nil {
		return x.TransInfo
	}
	return nil
}

func (x *CompetitionConfig) GetRewardInfo() []*RewardConfig {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *CompetitionConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *CompetitionConfig) GetRoomSize() int32 {
	if x != nil {
		return x.RoomSize
	}
	return 0
}

var File_pb_interactive_game_competition_common_competition_config_proto protoreflect.FileDescriptor

var file_pb_interactive_game_competition_common_competition_config_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x70, 0x62, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0b, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x6d,
	0x0a, 0x0b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x69, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x69, 0x6e, 0x69, 0x74,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x22, 0xa5, 0x01,
	0x0a, 0x09, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x64,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x39, 0x0a, 0x0b, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65,
	0x22, 0xd2, 0x02, 0x0a, 0x0d, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x65, 0x67, 0x69, 0x6e,
	0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x44,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x65,
	0x67, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x4d, 0x69, 0x6e, 0x75,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x79, 0x4d, 0x69, 0x6e, 0x75,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x79,
	0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x68, 0x6f, 0x77, 0x41, 0x64,
	0x76, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x73, 0x68, 0x6f, 0x77, 0x41, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x07, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x3a, 0x0a, 0x0c, 0x45, 0x78, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x42, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52,
	0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0x7d, 0x0a, 0x0c, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x61, 0x6e,
	0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x72, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa6, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x51,
	0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x1a, 0x3e, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xca, 0x04, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x30, 0x0a,
	0x13, 0x69, 0x6e, 0x69, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74,
	0x73, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x69, 0x6e, 0x69, 0x74,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x12,
	0x34, 0x0a, 0x15, 0x69, 0x6e, 0x69, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x57, 0x61, 0x69,
	0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x69, 0x6e, 0x69, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x57, 0x61, 0x69, 0x74, 0x53, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x75,
	0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x75,
	0x6d, 0x12, 0x36, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x07, 0x70, 0x61, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x07, 0x70, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x36, 0x0a, 0x09, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x3c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x39, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x53, 0x69, 0x7a, 0x65, 0x2a, 0x36,
	0x0a, 0x07, 0x50, 0x61, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x61, 0x79,
	0x46, 0x72, 0x65, 0x65, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x61, 0x79, 0x46, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x10, 0x02, 0x42, 0x5b, 0x5a, 0x59, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e,
	0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61,
	0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_interactive_game_competition_common_competition_config_proto_rawDescOnce sync.Once
	file_pb_interactive_game_competition_common_competition_config_proto_rawDescData = file_pb_interactive_game_competition_common_competition_config_proto_rawDesc
)

func file_pb_interactive_game_competition_common_competition_config_proto_rawDescGZIP() []byte {
	file_pb_interactive_game_competition_common_competition_config_proto_rawDescOnce.Do(func() {
		file_pb_interactive_game_competition_common_competition_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_interactive_game_competition_common_competition_config_proto_rawDescData)
	})
	return file_pb_interactive_game_competition_common_competition_config_proto_rawDescData
}

var file_pb_interactive_game_competition_common_competition_config_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_interactive_game_competition_common_competition_config_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_pb_interactive_game_competition_common_competition_config_proto_goTypes = []interface{}{
	(PayMode)(0),              // 0: game.common.PayMode
	(*StageConfig)(nil),       // 1: game.common.StageConfig
	(*PayConfig)(nil),         // 2: game.common.PayConfig
	(*RobotConfig)(nil),       // 3: game.common.RobotConfig
	(*OperateConfig)(nil),     // 4: game.common.OperateConfig
	(*RewardRankItem)(nil),    // 5: game.common.RewardRankItem
	(*RewardConfig)(nil),      // 6: game.common.RewardConfig
	(*TransparentConfig)(nil), // 7: game.common.TransparentConfig
	(*CompetitionConfig)(nil), // 8: game.common.CompetitionConfig
	nil,                       // 9: game.common.OperateConfig.ExtInfoEntry
	nil,                       // 10: game.common.TransparentConfig.TransConfigEntry
}
var file_pb_interactive_game_competition_common_competition_config_proto_depIdxs = []int32{
	0,  // 0: game.common.PayConfig.payMode:type_name -> game.common.PayMode
	9,  // 1: game.common.OperateConfig.extInfo:type_name -> game.common.OperateConfig.ExtInfoEntry
	5,  // 2: game.common.RewardConfig.rankRewards:type_name -> game.common.RewardRankItem
	10, // 3: game.common.TransparentConfig.transConfig:type_name -> game.common.TransparentConfig.TransConfigEntry
	1,  // 4: game.common.CompetitionConfig.stageInfo:type_name -> game.common.StageConfig
	2,  // 5: game.common.CompetitionConfig.payInfo:type_name -> game.common.PayConfig
	3,  // 6: game.common.CompetitionConfig.robotInfo:type_name -> game.common.RobotConfig
	4,  // 7: game.common.CompetitionConfig.operateInfo:type_name -> game.common.OperateConfig
	7,  // 8: game.common.CompetitionConfig.transInfo:type_name -> game.common.TransparentConfig
	6,  // 9: game.common.CompetitionConfig.rewardInfo:type_name -> game.common.RewardConfig
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_pb_interactive_game_competition_common_competition_config_proto_init() }
func file_pb_interactive_game_competition_common_competition_config_proto_init() {
	if File_pb_interactive_game_competition_common_competition_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StageConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OperateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardRankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransparentConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_interactive_game_competition_common_competition_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompetitionConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_interactive_game_competition_common_competition_config_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_interactive_game_competition_common_competition_config_proto_goTypes,
		DependencyIndexes: file_pb_interactive_game_competition_common_competition_config_proto_depIdxs,
		EnumInfos:         file_pb_interactive_game_competition_common_competition_config_proto_enumTypes,
		MessageInfos:      file_pb_interactive_game_competition_common_competition_config_proto_msgTypes,
	}.Build()
	File_pb_interactive_game_competition_common_competition_config_proto = out.File
	file_pb_interactive_game_competition_common_competition_config_proto_rawDesc = nil
	file_pb_interactive_game_competition_common_competition_config_proto_goTypes = nil
	file_pb_interactive_game_competition_common_competition_config_proto_depIdxs = nil
}
