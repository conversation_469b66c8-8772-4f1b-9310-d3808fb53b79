{"swagger": "2.0", "info": {"title": "pb/mg/mg.proto", "version": "version not set"}, "tags": [{"name": "MgSvr"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/mg.MgSvr/ClearRoom": {"post": {"summary": "------ <PERSON><PERSON> 测试 ------ //", "operationId": "MgSvr_ClearRoom", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgClearRoomRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgClearRoomReq"}}], "tags": ["MgSvr"]}}, "/mg.MgSvr/CreateGame": {"post": {"summary": "------ <PERSON>er 调用 ------- //\n同步游戏开局 至即构", "operationId": "MgSvr_CreateGame", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgCreateGameRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgCreateGameReq"}}], "tags": ["MgSvr"]}}, "/mg.MgSvr/GetCode": {"post": {"summary": "------ client 调用 ------ //\n获取Code", "operationId": "MgSvr_GetCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgGetCodeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgGetCodeReq"}}], "tags": ["MgSvr"]}}, "/mg.MgSvr/GetGameRoundStatus": {"post": {"operationId": "MgSvr_GetGameRoundStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mgGetGameRoundStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mgGetGameRoundStatusReq"}}], "tags": ["MgSvr"]}}}, "definitions": {"CreateGameReqPay": {"type": "object", "properties": {"mode": {"$ref": "#/definitions/mgPayMode", "title": "付费模式"}, "num": {"type": "string", "format": "int64", "title": "门票价格"}, "modeId": {"type": "string", "title": "模式 id"}}}, "CreateGameReqPlayer": {"type": "object", "properties": {"index": {"type": "integer", "format": "int64", "title": "座位号"}, "openId": {"type": "string"}}}, "commonGameRoundStatus": {"type": "string", "enum": ["GameRoundStatusNone", "GameRoundStatusPlaying", "GameRoundStatusOver"], "default": "GameRoundStatusNone", "title": "- GameRoundStatusNone: 场次不存在\n - GameRoundStatusPlaying: 场次游戏中\n - GameRoundStatusOver: 场次结束"}, "commonPlayerResult": {"type": "object", "properties": {"openId": {"type": "string"}, "win": {"type": "boolean", "title": "是否获胜"}, "rank": {"type": "integer", "format": "int64", "title": "排名 从 1 开始"}, "experience": {"type": "string", "format": "int64", "title": "获得经验值"}, "score": {"type": "string", "format": "int64", "title": "分数"}, "settlementExtra": {"type": "string", "title": "结算展示文案"}}}, "commonRoundStatus": {"type": "string", "enum": ["RoundStatusNormal", "RoundStatusDraw", "RoundStatusAbnormal"], "default": "RoundStatusNormal", "title": "- RoundStatusNormal: 正常结束\n - RoundStatusDraw: 平局结束\n - RoundStatusAbnormal: 异常结束 (系统错误等)"}, "mgClearRoomReq": {"type": "object", "properties": {"roomId": {"type": "string"}, "mgId": {"type": "string", "format": "int64"}}}, "mgClearRoomRsp": {"type": "object"}, "mgCreateGameReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "游戏 id"}, "roomId": {"type": "string", "title": "房间 id (小于 128 字节)"}, "roomType": {"$ref": "#/definitions/mgRoomType", "title": "房间类型"}, "roundId": {"type": "string", "title": "场次 id (小于 64 字节)"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/CreateGameReqPlayer"}, "title": "玩家列表"}, "configs": {"type": "object", "additionalProperties": {"type": "string"}, "description": "自定义配置", "title": "20000034: difficulty 游戏难度 (normal 普通 challenge 挑战)"}, "ownerId": {"type": "string", "title": "房主 openId"}, "pay": {"$ref": "#/definitions/CreateGameReqPay", "title": "付费信息"}, "paidTotal": {"type": "string", "format": "int64", "title": "支付总门票数"}, "groupNum": {"type": "integer", "format": "int64", "title": "队伍人数"}, "platformInfo": {"$ref": "#/definitions/mgPlatformInfo"}, "createFromMatch": {"type": "boolean", "title": "通过匹配创建"}}, "title": "需要保证接口可重入 错误码见 code/code.proto\n1. 如果 roomId 在游戏中且 roundId 相同则返回成功或 code.InteractiveGameRoomRoundCreated = 17005\n2. 如果 roomId 在游戏中且 roundId 不同则返回 code.InteractiveGameRoomRoundConflict = 17006"}, "mgCreateGameRsp": {"type": "object"}, "mgGetCodeReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "mgGetCodeRsp": {"type": "object", "properties": {"code": {"type": "string"}, "expireDate": {"type": "string", "format": "int64", "title": "过期时间戳"}, "mgId": {"type": "string", "format": "int64", "title": "游戏id"}}}, "mgGetGameRoundStatusReq": {"type": "object", "properties": {"appId": {"type": "string", "title": "appId"}, "roomId": {"type": "string", "title": "房间 id"}, "roundId": {"type": "string", "title": "场次 id"}}}, "mgGetGameRoundStatusRsp": {"type": "object", "properties": {"gameRoundStatus": {"$ref": "#/definitions/commonGameRoundStatus", "title": "游戏场次状态"}, "results": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/commonPlayerResult"}, "title": "各玩家游戏结果"}, "roundStatus": {"$ref": "#/definitions/commonRoundStatus", "title": "场次状态"}, "requireSettlement": {"type": "boolean", "title": "需要结算"}, "settlementPool": {"type": "string", "format": "int64", "title": "结算奖池 用于游戏自定义抽成 需要 <= CreateGame 的门票总数 不传默认为门票总数"}}}, "mgKgInfo": {"type": "object", "properties": {"roomType": {"$ref": "#/definitions/mgKgInfoRoomType"}, "gameId": {"type": "string", "title": "string payConfig = 2;"}, "liveMikeMode": {"$ref": "#/definitions/mgKgInfoLiveMikeMode"}}}, "mgKgInfoLiveMikeMode": {"type": "string", "enum": ["LiveMikeModeUnknown", "LiveMikeModeVideo", "LiveMikeModeAudio", "LiveMikeModePopup"], "default": "LiveMikeModeUnknown", "title": "- LiveMikeModeVideo: 视频上麦\n - LiveMikeModeAudio: 音频上麦\n - LiveMikeModePopup: 弹窗上麦"}, "mgKgInfoRoomType": {"type": "string", "enum": ["RoomTypeUnknown", "RoomTypeLive", "RoomTypeSocialKtv", "RoomTypeSingleMikeKtv"], "default": "RoomTypeUnknown", "title": "- RoomTypeLive: 直播\n - RoomTypeSocialKtv: 欢聚\n - RoomTypeSingleMikeKtv: 单麦"}, "mgPayMode": {"type": "string", "enum": ["PayFree", "PayRequired", "PayFlower"], "default": "PayFree", "title": "- PayRequired: 付费场\n - PayFlower: 鲜花礼物道具场"}, "mgPlatformInfo": {"type": "object", "properties": {"platId": {"type": "string", "format": "uint64", "title": "EPlatID"}, "kg": {"$ref": "#/definitions/mgKgInfo"}}}, "mgRoomType": {"type": "string", "enum": ["TypeNone", "TypeKtv", "TypeTwins", "TypeLive", "TypeMatch"], "default": "TypeNone", "title": "- TypeKtv: 歌房\n - TypeTwins: 双人房\n - TypeLive: 直播\n - TypeMatch: 匹配房"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}