// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/mg/mg_comm.proto

package mg

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MgInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	MgId       string `protobuf:"bytes,2,opt,name=mg_id,json=mgId,proto3" json:"mg_id,omitempty"`
	Desc       string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ScoreField string `protobuf:"bytes,4,opt,name=score_field,json=scoreField,proto3" json:"score_field,omitempty"` // 统计分数字段
}

func (x *MgInfo) Reset() {
	*x = MgInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_mg_mg_comm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MgInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MgInfo) ProtoMessage() {}

func (x *MgInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_mg_mg_comm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MgInfo.ProtoReflect.Descriptor instead.
func (*MgInfo) Descriptor() ([]byte, []int) {
	return file_pb_mg_mg_comm_proto_rawDescGZIP(), []int{0}
}

func (x *MgInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *MgInfo) GetMgId() string {
	if x != nil {
		return x.MgId
	}
	return ""
}

func (x *MgInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *MgInfo) GetScoreField() string {
	if x != nil {
		return x.ScoreField
	}
	return ""
}

// mg:mg_info
type MgInfoCkv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VecMg []*MgInfo `protobuf:"bytes,1,rep,name=vecMg,proto3" json:"vecMg,omitempty"`
	UTs   int32     `protobuf:"varint,2,opt,name=uTs,proto3" json:"uTs,omitempty"`
}

func (x *MgInfoCkv) Reset() {
	*x = MgInfoCkv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_mg_mg_comm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MgInfoCkv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MgInfoCkv) ProtoMessage() {}

func (x *MgInfoCkv) ProtoReflect() protoreflect.Message {
	mi := &file_pb_mg_mg_comm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MgInfoCkv.ProtoReflect.Descriptor instead.
func (*MgInfoCkv) Descriptor() ([]byte, []int) {
	return file_pb_mg_mg_comm_proto_rawDescGZIP(), []int{1}
}

func (x *MgInfoCkv) GetVecMg() []*MgInfo {
	if x != nil {
		return x.VecMg
	}
	return nil
}

func (x *MgInfoCkv) GetUTs() int32 {
	if x != nil {
		return x.UTs
	}
	return 0
}

var File_pb_mg_mg_comm_proto protoreflect.FileDescriptor

var file_pb_mg_mg_comm_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x2f, 0x6d, 0x67, 0x2f, 0x6d, 0x67, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x6d, 0x67, 0x22, 0x69, 0x0a, 0x06, 0x4d, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x6d, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x67, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x22, 0x3f, 0x0a, 0x09, 0x4d, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6b,
	0x76, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x65, 0x63, 0x4d, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x6d, 0x67, 0x2e, 0x4d, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x65,
	0x63, 0x4d, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x54, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x75, 0x54, 0x73, 0x42, 0x3a, 0x5a, 0x38, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x6d,
	0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_mg_mg_comm_proto_rawDescOnce sync.Once
	file_pb_mg_mg_comm_proto_rawDescData = file_pb_mg_mg_comm_proto_rawDesc
)

func file_pb_mg_mg_comm_proto_rawDescGZIP() []byte {
	file_pb_mg_mg_comm_proto_rawDescOnce.Do(func() {
		file_pb_mg_mg_comm_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_mg_mg_comm_proto_rawDescData)
	})
	return file_pb_mg_mg_comm_proto_rawDescData
}

var file_pb_mg_mg_comm_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pb_mg_mg_comm_proto_goTypes = []interface{}{
	(*MgInfo)(nil),    // 0: mg.MgInfo
	(*MgInfoCkv)(nil), // 1: mg.MgInfoCkv
}
var file_pb_mg_mg_comm_proto_depIdxs = []int32{
	0, // 0: mg.MgInfoCkv.vecMg:type_name -> mg.MgInfo
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pb_mg_mg_comm_proto_init() }
func file_pb_mg_mg_comm_proto_init() {
	if File_pb_mg_mg_comm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_mg_mg_comm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MgInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_mg_mg_comm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MgInfoCkv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_mg_mg_comm_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_mg_mg_comm_proto_goTypes,
		DependencyIndexes: file_pb_mg_mg_comm_proto_depIdxs,
		MessageInfos:      file_pb_mg_mg_comm_proto_msgTypes,
	}.Build()
	File_pb_mg_mg_comm_proto = out.File
	file_pb_mg_mg_comm_proto_rawDesc = nil
	file_pb_mg_mg_comm_proto_goTypes = nil
	file_pb_mg_mg_comm_proto_depIdxs = nil
}
