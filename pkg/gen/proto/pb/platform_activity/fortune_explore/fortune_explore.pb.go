// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/platform_activity/fortune_explore/fortune_explore.proto

package fortune_explore

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	adapter_unified_assets "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/adapter_unified_assets"
	callback "kugou_adapter_service/pkg/gen/proto/pb/adapter_unified_assets/callback"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GiftLevel int32

const (
	GiftLevel_Normal GiftLevel = 0
	GiftLevel_R      GiftLevel = 1
	GiftLevel_SR     GiftLevel = 2
	GiftLevel_SSR    GiftLevel = 3
)

// Enum value maps for GiftLevel.
var (
	GiftLevel_name = map[int32]string{
		0: "Normal",
		1: "R",
		2: "SR",
		3: "SSR",
	}
	GiftLevel_value = map[string]int32{
		"Normal": 0,
		"R":      1,
		"SR":     2,
		"SSR":    3,
	}
)

func (x GiftLevel) Enum() *GiftLevel {
	p := new(GiftLevel)
	*p = x
	return p
}

func (x GiftLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GiftLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_enumTypes[0].Descriptor()
}

func (GiftLevel) Type() protoreflect.EnumType {
	return &file_pb_platform_activity_fortune_explore_fortune_explore_proto_enumTypes[0]
}

func (x GiftLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GiftLevel.Descriptor instead.
func (GiftLevel) EnumDescriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{0}
}

type RecordRewardType int32

const (
	RecordRewardType_Gourd    RecordRewardType = 0
	RecordRewardType_JadeRing RecordRewardType = 1
)

// Enum value maps for RecordRewardType.
var (
	RecordRewardType_name = map[int32]string{
		0: "Gourd",
		1: "JadeRing",
	}
	RecordRewardType_value = map[string]int32{
		"Gourd":    0,
		"JadeRing": 1,
	}
)

func (x RecordRewardType) Enum() *RecordRewardType {
	p := new(RecordRewardType)
	*p = x
	return p
}

func (x RecordRewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecordRewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_enumTypes[1].Descriptor()
}

func (RecordRewardType) Type() protoreflect.EnumType {
	return &file_pb_platform_activity_fortune_explore_fortune_explore_proto_enumTypes[1]
}

func (x RecordRewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecordRewardType.Descriptor instead.
func (RecordRewardType) EnumDescriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{1}
}

type GetGourdListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId uint32 `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`
	Round      int32  `protobuf:"varint,2,opt,name=round,proto3" json:"round,omitempty"`
	Uid        string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (x *GetGourdListReq) Reset() {
	*x = GetGourdListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGourdListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGourdListReq) ProtoMessage() {}

func (x *GetGourdListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGourdListReq.ProtoReflect.Descriptor instead.
func (*GetGourdListReq) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{0}
}

func (x *GetGourdListReq) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *GetGourdListReq) GetRound() int32 {
	if x != nil {
		return x.Round
	}
	return 0
}

func (x *GetGourdListReq) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetGourdListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayInfo *UserExploreShowData `protobuf:"bytes,1,opt,name=displayInfo,proto3" json:"displayInfo,omitempty"`
	UnitPrice   uint32               `protobuf:"varint,2,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"` //单个抽取价格
	StartTime   uint32               `protobuf:"varint,3,opt,name=startTime,proto3" json:"startTime,omitempty"` //开始时间
	EndTime     uint32               `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`     //结束时间
	HasNext     bool                 `protobuf:"varint,5,opt,name=hasNext,proto3" json:"hasNext,omitempty"`     //是否解锁下一轮
	IsRebate    bool                 `protobuf:"varint,8,opt,name=isRebate,proto3" json:"isRebate,omitempty"`   //是否返利，true时有返利，使用临时背包
}

func (x *GetGourdListRsp) Reset() {
	*x = GetGourdListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGourdListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGourdListRsp) ProtoMessage() {}

func (x *GetGourdListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGourdListRsp.ProtoReflect.Descriptor instead.
func (*GetGourdListRsp) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{1}
}

func (x *GetGourdListRsp) GetDisplayInfo() *UserExploreShowData {
	if x != nil {
		return x.DisplayInfo
	}
	return nil
}

func (x *GetGourdListRsp) GetUnitPrice() uint32 {
	if x != nil {
		return x.UnitPrice
	}
	return 0
}

func (x *GetGourdListRsp) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GetGourdListRsp) GetEndTime() uint32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GetGourdListRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *GetGourdListRsp) GetIsRebate() bool {
	if x != nil {
		return x.IsRebate
	}
	return false
}

type UserExploreShowData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NowRound     uint32          `protobuf:"varint,1,opt,name=nowRound,proto3" json:"nowRound,omitempty"`
	ExploreInfo  *ExploreRound   `protobuf:"bytes,2,opt,name=exploreInfo,proto3" json:"exploreInfo,omitempty"`
	JadeRingInfo *JadeRingInfo   `protobuf:"bytes,3,opt,name=jadeRingInfo,proto3" json:"jadeRingInfo,omitempty"`
	UserStatus   *UserStatusInfo `protobuf:"bytes,4,opt,name=userStatus,proto3" json:"userStatus,omitempty"`
}

func (x *UserExploreShowData) Reset() {
	*x = UserExploreShowData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExploreShowData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExploreShowData) ProtoMessage() {}

func (x *UserExploreShowData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExploreShowData.ProtoReflect.Descriptor instead.
func (*UserExploreShowData) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{2}
}

func (x *UserExploreShowData) GetNowRound() uint32 {
	if x != nil {
		return x.NowRound
	}
	return 0
}

func (x *UserExploreShowData) GetExploreInfo() *ExploreRound {
	if x != nil {
		return x.ExploreInfo
	}
	return nil
}

func (x *UserExploreShowData) GetJadeRingInfo() *JadeRingInfo {
	if x != nil {
		return x.JadeRingInfo
	}
	return nil
}

func (x *UserExploreShowData) GetUserStatus() *UserStatusInfo {
	if x != nil {
		return x.UserStatus
	}
	return nil
}

type UserStatusInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenNextRound     bool   `protobuf:"varint,1,opt,name=openNextRound,proto3" json:"openNextRound,omitempty"`
	GetJadeRingReward bool   `protobuf:"varint,2,opt,name=getJadeRingReward,proto3" json:"getJadeRingReward,omitempty"`
	OpenJadeRingNum   uint32 `protobuf:"varint,3,opt,name=openJadeRingNum,proto3" json:"openJadeRingNum,omitempty"`
}

func (x *UserStatusInfo) Reset() {
	*x = UserStatusInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatusInfo) ProtoMessage() {}

func (x *UserStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatusInfo.ProtoReflect.Descriptor instead.
func (*UserStatusInfo) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{3}
}

func (x *UserStatusInfo) GetOpenNextRound() bool {
	if x != nil {
		return x.OpenNextRound
	}
	return false
}

func (x *UserStatusInfo) GetGetJadeRingReward() bool {
	if x != nil {
		return x.GetJadeRingReward
	}
	return false
}

func (x *UserStatusInfo) GetOpenJadeRingNum() uint32 {
	if x != nil {
		return x.OpenJadeRingNum
	}
	return 0
}

type UserExploreData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NowRound     uint32          `protobuf:"varint,1,opt,name=nowRound,proto3" json:"nowRound,omitempty"`
	ExploreList  []*ExploreRound `protobuf:"bytes,2,rep,name=exploreList,proto3" json:"exploreList,omitempty"`
	JadeRingInfo *JadeRingInfo   `protobuf:"bytes,3,opt,name=jadeRingInfo,proto3" json:"jadeRingInfo,omitempty"`
	RecordInfo   []*RecordItem   `protobuf:"bytes,4,rep,name=recordInfo,proto3" json:"recordInfo,omitempty"`
}

func (x *UserExploreData) Reset() {
	*x = UserExploreData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExploreData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExploreData) ProtoMessage() {}

func (x *UserExploreData) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExploreData.ProtoReflect.Descriptor instead.
func (*UserExploreData) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{4}
}

func (x *UserExploreData) GetNowRound() uint32 {
	if x != nil {
		return x.NowRound
	}
	return 0
}

func (x *UserExploreData) GetExploreList() []*ExploreRound {
	if x != nil {
		return x.ExploreList
	}
	return nil
}

func (x *UserExploreData) GetJadeRingInfo() *JadeRingInfo {
	if x != nil {
		return x.JadeRingInfo
	}
	return nil
}

func (x *UserExploreData) GetRecordInfo() []*RecordItem {
	if x != nil {
		return x.RecordInfo
	}
	return nil
}

type JadeRingInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectNum       uint32      `protobuf:"varint,1,opt,name=collectNum,proto3" json:"collectNum,omitempty"`
	TargetNum        uint32      `protobuf:"varint,2,opt,name=targetNum,proto3" json:"targetNum,omitempty"`
	ProbabilityUp    uint32      `protobuf:"varint,3,opt,name=probabilityUp,proto3" json:"probabilityUp,omitempty"`
	RewardInfo       *RewardInfo `protobuf:"bytes,4,opt,name=rewardInfo,proto3" json:"rewardInfo,omitempty"`
	UserDrawInterval uint32      `protobuf:"varint,5,opt,name=userDrawInterval,proto3" json:"userDrawInterval,omitempty"`
	WelfareId        uint32      `protobuf:"varint,6,opt,name=welfareId,proto3" json:"welfareId,omitempty"`
}

func (x *JadeRingInfo) Reset() {
	*x = JadeRingInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JadeRingInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JadeRingInfo) ProtoMessage() {}

func (x *JadeRingInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JadeRingInfo.ProtoReflect.Descriptor instead.
func (*JadeRingInfo) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{5}
}

func (x *JadeRingInfo) GetCollectNum() uint32 {
	if x != nil {
		return x.CollectNum
	}
	return 0
}

func (x *JadeRingInfo) GetTargetNum() uint32 {
	if x != nil {
		return x.TargetNum
	}
	return 0
}

func (x *JadeRingInfo) GetProbabilityUp() uint32 {
	if x != nil {
		return x.ProbabilityUp
	}
	return 0
}

func (x *JadeRingInfo) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *JadeRingInfo) GetUserDrawInterval() uint32 {
	if x != nil {
		return x.UserDrawInterval
	}
	return 0
}

func (x *JadeRingInfo) GetWelfareId() uint32 {
	if x != nil {
		return x.WelfareId
	}
	return 0
}

type RewardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pic       string `protobuf:"bytes,1,opt,name=pic,proto3" json:"pic,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Price     uint32 `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	Count     uint32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	GiftLevel int32  `protobuf:"varint,5,opt,name=giftLevel,proto3" json:"giftLevel,omitempty"` // 枚举 GiftLevel
}

func (x *RewardInfo) Reset() {
	*x = RewardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardInfo) ProtoMessage() {}

func (x *RewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardInfo.ProtoReflect.Descriptor instead.
func (*RewardInfo) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{6}
}

func (x *RewardInfo) GetPic() string {
	if x != nil {
		return x.Pic
	}
	return ""
}

func (x *RewardInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RewardInfo) GetPrice() uint32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *RewardInfo) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *RewardInfo) GetGiftLevel() int32 {
	if x != nil {
		return x.GiftLevel
	}
	return 0
}

type ExploreRound struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GourdList []*GourdInfo `protobuf:"bytes,1,rep,name=gourdList,proto3" json:"gourdList,omitempty"`
}

func (x *ExploreRound) Reset() {
	*x = ExploreRound{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExploreRound) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExploreRound) ProtoMessage() {}

func (x *ExploreRound) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExploreRound.ProtoReflect.Descriptor instead.
func (*ExploreRound) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{7}
}

func (x *ExploreRound) GetGourdList() []*GourdInfo {
	if x != nil {
		return x.GourdList
	}
	return nil
}

type GourdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsOpen     bool        `protobuf:"varint,1,opt,name=isOpen,proto3" json:"isOpen,omitempty"`
	WelfareId  uint32      `protobuf:"varint,2,opt,name=welfareId,proto3" json:"welfareId,omitempty"`
	RewardInfo *RewardInfo `protobuf:"bytes,3,opt,name=rewardInfo,proto3" json:"rewardInfo,omitempty"`
	RewardTime uint32      `protobuf:"varint,4,opt,name=rewardTime,proto3" json:"rewardTime,omitempty"`
}

func (x *GourdInfo) Reset() {
	*x = GourdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GourdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GourdInfo) ProtoMessage() {}

func (x *GourdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GourdInfo.ProtoReflect.Descriptor instead.
func (*GourdInfo) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{8}
}

func (x *GourdInfo) GetIsOpen() bool {
	if x != nil {
		return x.IsOpen
	}
	return false
}

func (x *GourdInfo) GetWelfareId() uint32 {
	if x != nil {
		return x.WelfareId
	}
	return 0
}

func (x *GourdInfo) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *GourdInfo) GetRewardTime() uint32 {
	if x != nil {
		return x.RewardTime
	}
	return 0
}

type PayGourdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId        string                               `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	ActivityId   uint32                               `protobuf:"varint,2,opt,name=activityId,proto3" json:"activityId,omitempty"`                           // 本活动的标识ID
	PayApp       *adapter_unified_assets.PayApp       `protobuf:"bytes,3,opt,name=payApp,proto3" json:"payApp,omitempty"`                                    // 支付app
	Round        int32                                `protobuf:"varint,4,opt,name=round,proto3" json:"round,omitempty"`                                     // 付费轮次
	PayIndex     []uint32                             `protobuf:"varint,5,rep,packed,name=payIndex,proto3" json:"payIndex,omitempty"`                        // 购买葫芦索引
	PayUserID    string                               `protobuf:"bytes,6,opt,name=payUserID,proto3" json:"payUserID,omitempty"`                              //付费用户
	PayAmount    uint32                               `protobuf:"varint,7,opt,name=payAmount,proto3" json:"payAmount,omitempty"`                             //总付费数
	RecvUserID   string                               `protobuf:"bytes,8,opt,name=recvUserID,proto3" json:"recvUserID,omitempty"`                            //被打赏者
	OrderID      string                               `protobuf:"bytes,9,opt,name=orderID,proto3" json:"orderID,omitempty"`                                  //订单id，幂等使用
	PaySceneInfo *adapter_unified_assets.PaySceneInfo `protobuf:"bytes,10,opt,name=pay_scene_info,json=paySceneInfo,proto3" json:"pay_scene_info,omitempty"` // 扣费场景信息
}

func (x *PayGourdReq) Reset() {
	*x = PayGourdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayGourdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayGourdReq) ProtoMessage() {}

func (x *PayGourdReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayGourdReq.ProtoReflect.Descriptor instead.
func (*PayGourdReq) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{9}
}

func (x *PayGourdReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *PayGourdReq) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *PayGourdReq) GetPayApp() *adapter_unified_assets.PayApp {
	if x != nil {
		return x.PayApp
	}
	return nil
}

func (x *PayGourdReq) GetRound() int32 {
	if x != nil {
		return x.Round
	}
	return 0
}

func (x *PayGourdReq) GetPayIndex() []uint32 {
	if x != nil {
		return x.PayIndex
	}
	return nil
}

func (x *PayGourdReq) GetPayUserID() string {
	if x != nil {
		return x.PayUserID
	}
	return ""
}

func (x *PayGourdReq) GetPayAmount() uint32 {
	if x != nil {
		return x.PayAmount
	}
	return 0
}

func (x *PayGourdReq) GetRecvUserID() string {
	if x != nil {
		return x.RecvUserID
	}
	return ""
}

func (x *PayGourdReq) GetOrderID() string {
	if x != nil {
		return x.OrderID
	}
	return ""
}

func (x *PayGourdReq) GetPaySceneInfo() *adapter_unified_assets.PaySceneInfo {
	if x != nil {
		return x.PaySceneInfo
	}
	return nil
}

type PayGourdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrMsg      string               `protobuf:"bytes,1,opt,name=errMsg,proto3" json:"errMsg,omitempty"`
	DisplayInfo *UserExploreShowData `protobuf:"bytes,2,opt,name=displayInfo,proto3" json:"displayInfo,omitempty"`
}

func (x *PayGourdRsp) Reset() {
	*x = PayGourdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayGourdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayGourdRsp) ProtoMessage() {}

func (x *PayGourdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayGourdRsp.ProtoReflect.Descriptor instead.
func (*PayGourdRsp) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{10}
}

func (x *PayGourdRsp) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *PayGourdRsp) GetDisplayInfo() *UserExploreShowData {
	if x != nil {
		return x.DisplayInfo
	}
	return nil
}

type QueryRewardRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId uint32 `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`
	UserID     string `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	PageSize   uint32 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"` //页大小 默认10
	PassBack   uint32 `protobuf:"varint,4,opt,name=passBack,proto3" json:"passBack,omitempty"`
}

func (x *QueryRewardRecordReq) Reset() {
	*x = QueryRewardRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRewardRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRewardRecordReq) ProtoMessage() {}

func (x *QueryRewardRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRewardRecordReq.ProtoReflect.Descriptor instead.
func (*QueryRewardRecordReq) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{11}
}

func (x *QueryRewardRecordReq) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *QueryRewardRecordReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *QueryRewardRecordReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QueryRewardRecordReq) GetPassBack() uint32 {
	if x != nil {
		return x.PassBack
	}
	return 0
}

type QueryRewardRecordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info     []*RecordItem `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	HasMore  bool          `protobuf:"varint,2,opt,name=hasMore,proto3" json:"hasMore,omitempty"`
	PassBack uint32        `protobuf:"varint,3,opt,name=passBack,proto3" json:"passBack,omitempty"`
}

func (x *QueryRewardRecordRsp) Reset() {
	*x = QueryRewardRecordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRewardRecordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRewardRecordRsp) ProtoMessage() {}

func (x *QueryRewardRecordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRewardRecordRsp.ProtoReflect.Descriptor instead.
func (*QueryRewardRecordRsp) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{12}
}

func (x *QueryRewardRecordRsp) GetInfo() []*RecordItem {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *QueryRewardRecordRsp) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *QueryRewardRecordRsp) GetPassBack() uint32 {
	if x != nil {
		return x.PassBack
	}
	return 0
}

type RecordItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardInfo *RewardInfo `protobuf:"bytes,1,opt,name=rewardInfo,proto3" json:"rewardInfo,omitempty"`
	WelfareId  uint32      `protobuf:"varint,2,opt,name=welfareId,proto3" json:"welfareId,omitempty"`
	RewardTime uint32      `protobuf:"varint,3,opt,name=rewardTime,proto3" json:"rewardTime,omitempty"`
	RewardType int32       `protobuf:"varint,4,opt,name=rewardType,proto3" json:"rewardType,omitempty"` // 枚举 RecordRewardType
}

func (x *RecordItem) Reset() {
	*x = RecordItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordItem) ProtoMessage() {}

func (x *RecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordItem.ProtoReflect.Descriptor instead.
func (*RecordItem) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{13}
}

func (x *RecordItem) GetRewardInfo() *RewardInfo {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *RecordItem) GetWelfareId() uint32 {
	if x != nil {
		return x.WelfareId
	}
	return 0
}

func (x *RecordItem) GetRewardTime() uint32 {
	if x != nil {
		return x.RewardTime
	}
	return 0
}

func (x *RecordItem) GetRewardType() int32 {
	if x != nil {
		return x.RewardType
	}
	return 0
}

type ConfigSyncReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Price        uint32 `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`              //抽取价格
	GourdList    string `protobuf:"bytes,3,opt,name=gourdList,proto3" json:"gourdList,omitempty"`       //葫芦配置
	JadeRingList string `protobuf:"bytes,4,opt,name=jadeRingList,proto3" json:"jadeRingList,omitempty"` //玉环配置
	StartTime    string `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime,omitempty"`       //开始时间
	EndTime      string `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime,omitempty"`           //结束时间
	Status       string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`             //上架状态
	IsRebate     string `protobuf:"bytes,8,opt,name=isRebate,proto3" json:"isRebate,omitempty"`         //是否返利 0否 1是
}

func (x *ConfigSyncReq) Reset() {
	*x = ConfigSyncReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigSyncReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigSyncReq) ProtoMessage() {}

func (x *ConfigSyncReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigSyncReq.ProtoReflect.Descriptor instead.
func (*ConfigSyncReq) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{14}
}

func (x *ConfigSyncReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConfigSyncReq) GetPrice() uint32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ConfigSyncReq) GetGourdList() string {
	if x != nil {
		return x.GourdList
	}
	return ""
}

func (x *ConfigSyncReq) GetJadeRingList() string {
	if x != nil {
		return x.JadeRingList
	}
	return ""
}

func (x *ConfigSyncReq) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ConfigSyncReq) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ConfigSyncReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ConfigSyncReq) GetIsRebate() string {
	if x != nil {
		return x.IsRebate
	}
	return ""
}

type FortuneExploreConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Price        uint32        `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`              //抽取价格
	GourdList    []*GourdConf  `protobuf:"bytes,3,rep,name=gourdList,proto3" json:"gourdList,omitempty"`       //葫芦配置
	JadeRingConf *JadeRingConf `protobuf:"bytes,4,opt,name=JadeRingConf,proto3" json:"JadeRingConf,omitempty"` //玉环配置
	StartTime    uint32        `protobuf:"varint,5,opt,name=startTime,proto3" json:"startTime,omitempty"`      //开始时间
	EndTime      uint32        `protobuf:"varint,6,opt,name=endTime,proto3" json:"endTime,omitempty"`          //结束时间
	Status       bool          `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`            //上架状态
	IsRebate     bool          `protobuf:"varint,8,opt,name=isRebate,proto3" json:"isRebate,omitempty"`        //是否返利
}

func (x *FortuneExploreConf) Reset() {
	*x = FortuneExploreConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FortuneExploreConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FortuneExploreConf) ProtoMessage() {}

func (x *FortuneExploreConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FortuneExploreConf.ProtoReflect.Descriptor instead.
func (*FortuneExploreConf) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{15}
}

func (x *FortuneExploreConf) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FortuneExploreConf) GetPrice() uint32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *FortuneExploreConf) GetGourdList() []*GourdConf {
	if x != nil {
		return x.GourdList
	}
	return nil
}

func (x *FortuneExploreConf) GetJadeRingConf() *JadeRingConf {
	if x != nil {
		return x.JadeRingConf
	}
	return nil
}

func (x *FortuneExploreConf) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *FortuneExploreConf) GetEndTime() uint32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *FortuneExploreConf) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *FortuneExploreConf) GetIsRebate() bool {
	if x != nil {
		return x.IsRebate
	}
	return false
}

type GourdConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardList []*GourdRewardConf `protobuf:"bytes,1,rep,name=rewardList,proto3" json:"rewardList,omitempty"`
	GourdNum   uint32             `protobuf:"varint,2,opt,name=gourdNum,proto3" json:"gourdNum,omitempty"`
}

func (x *GourdConf) Reset() {
	*x = GourdConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GourdConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GourdConf) ProtoMessage() {}

func (x *GourdConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GourdConf.ProtoReflect.Descriptor instead.
func (*GourdConf) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{16}
}

func (x *GourdConf) GetRewardList() []*GourdRewardConf {
	if x != nil {
		return x.RewardList
	}
	return nil
}

func (x *GourdConf) GetGourdNum() uint32 {
	if x != nil {
		return x.GourdNum
	}
	return 0
}

type GourdRewardConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WelfareID   uint32 `protobuf:"varint,1,opt,name=welfareID,proto3" json:"welfareID,omitempty"`
	Probability uint32 `protobuf:"varint,2,opt,name=probability,proto3" json:"probability,omitempty"` //抽取概率
	GiftLevel   uint32 `protobuf:"varint,3,opt,name=giftLevel,proto3" json:"giftLevel,omitempty"`
}

func (x *GourdRewardConf) Reset() {
	*x = GourdRewardConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GourdRewardConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GourdRewardConf) ProtoMessage() {}

func (x *GourdRewardConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GourdRewardConf.ProtoReflect.Descriptor instead.
func (*GourdRewardConf) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{17}
}

func (x *GourdRewardConf) GetWelfareID() uint32 {
	if x != nil {
		return x.WelfareID
	}
	return 0
}

func (x *GourdRewardConf) GetProbability() uint32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

func (x *GourdRewardConf) GetGiftLevel() uint32 {
	if x != nil {
		return x.GiftLevel
	}
	return 0
}

type JadeRingConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardList   []*JadeRingRewardConf `protobuf:"bytes,1,rep,name=rewardList,proto3" json:"rewardList,omitempty"`
	DrawInterval uint32                `protobuf:"varint,2,opt,name=drawInterval,proto3" json:"drawInterval,omitempty"`
	TargetNum    uint32                `protobuf:"varint,3,opt,name=targetNum,proto3" json:"targetNum,omitempty"`
}

func (x *JadeRingConf) Reset() {
	*x = JadeRingConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JadeRingConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JadeRingConf) ProtoMessage() {}

func (x *JadeRingConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JadeRingConf.ProtoReflect.Descriptor instead.
func (*JadeRingConf) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{18}
}

func (x *JadeRingConf) GetRewardList() []*JadeRingRewardConf {
	if x != nil {
		return x.RewardList
	}
	return nil
}

func (x *JadeRingConf) GetDrawInterval() uint32 {
	if x != nil {
		return x.DrawInterval
	}
	return 0
}

func (x *JadeRingConf) GetTargetNum() uint32 {
	if x != nil {
		return x.TargetNum
	}
	return 0
}

type JadeRingRewardConf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BeginStage  uint32 `protobuf:"varint,1,opt,name=beginStage,proto3" json:"beginStage,omitempty"`
	EndStage    uint32 `protobuf:"varint,2,opt,name=endStage,proto3" json:"endStage,omitempty"`
	WelfareID   uint32 `protobuf:"varint,3,opt,name=welfareID,proto3" json:"welfareID,omitempty"`
	Probability uint32 `protobuf:"varint,4,opt,name=probability,proto3" json:"probability,omitempty"` //抽取概率
}

func (x *JadeRingRewardConf) Reset() {
	*x = JadeRingRewardConf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JadeRingRewardConf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JadeRingRewardConf) ProtoMessage() {}

func (x *JadeRingRewardConf) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JadeRingRewardConf.ProtoReflect.Descriptor instead.
func (*JadeRingRewardConf) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{19}
}

func (x *JadeRingRewardConf) GetBeginStage() uint32 {
	if x != nil {
		return x.BeginStage
	}
	return 0
}

func (x *JadeRingRewardConf) GetEndStage() uint32 {
	if x != nil {
		return x.EndStage
	}
	return 0
}

func (x *JadeRingRewardConf) GetWelfareID() uint32 {
	if x != nil {
		return x.WelfareID
	}
	return 0
}

func (x *JadeRingRewardConf) GetProbability() uint32 {
	if x != nil {
		return x.Probability
	}
	return 0
}

type ConfigSyncRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RetCode uint32 `protobuf:"varint,1,opt,name=retCode,proto3" json:"retCode,omitempty"` // 错误码
	RetMsg  string `protobuf:"bytes,2,opt,name=retMsg,proto3" json:"retMsg,omitempty"`    // 错误提示语
}

func (x *ConfigSyncRsp) Reset() {
	*x = ConfigSyncRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigSyncRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigSyncRsp) ProtoMessage() {}

func (x *ConfigSyncRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigSyncRsp.ProtoReflect.Descriptor instead.
func (*ConfigSyncRsp) Descriptor() ([]byte, []int) {
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP(), []int{20}
}

func (x *ConfigSyncRsp) GetRetCode() uint32 {
	if x != nil {
		return x.RetCode
	}
	return 0
}

func (x *ConfigSyncRsp) GetRetMsg() string {
	if x != nil {
		return x.RetMsg
	}
	return ""
}

var File_pb_platform_activity_fortune_explore_fortune_explore_proto protoreflect.FileDescriptor

var file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x70, 0x62, 0x2f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65,
	0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2f, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65,
	0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x66, 0x6f,
	0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x1a, 0x31, 0x70,
	0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x3d, 0x70, 0x62, 0x2f, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x61, 0x64, 0x61, 0x70,
	0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x59, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x22, 0xe5, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x46,
	0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78,
	0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72,
	0x65, 0x53, 0x68, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x52, 0x65, 0x62, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x62, 0x61,
	0x74, 0x65, 0x22, 0xf6, 0x01, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x6f,
	0x72, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f,
	0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6e, 0x6f,
	0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x3f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6f,
	0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x45, 0x78,
	0x70, 0x6c, 0x6f, 0x72, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x0c, 0x6a, 0x61, 0x64, 0x65, 0x52,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e,
	0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6a, 0x61,
	0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x0a, 0x75, 0x73,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x75, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x0e,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24,
	0x0a, 0x0d, 0x6f, 0x70, 0x65, 0x6e, 0x4e, 0x65, 0x78, 0x74, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x6e, 0x4e, 0x65, 0x78, 0x74, 0x52,
	0x6f, 0x75, 0x6e, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x67, 0x65, 0x74, 0x4a, 0x61, 0x64, 0x65, 0x52,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x11, 0x67, 0x65, 0x74, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x70, 0x65, 0x6e, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69,
	0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x6f, 0x70, 0x65,
	0x6e, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x22, 0xee, 0x01, 0x0a,
	0x0f, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x6e, 0x6f, 0x77, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x3f, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64,
	0x52, 0x0b, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x41, 0x0a,
	0x0c, 0x6a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78,
	0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0c, 0x6a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x3b, 0x0a, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65,
	0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf9, 0x01,
	0x0a, 0x0c, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d,
	0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x55, 0x70, 0x12, 0x3b, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65,
	0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x2a, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x44, 0x72, 0x61, 0x77, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x75, 0x73, 0x65, 0x72, 0x44,
	0x72, 0x61, 0x77, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x77,
	0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x0a, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x63, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x69, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x66,
	0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x69,
	0x66, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x48, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6c, 0x6f,
	0x72, 0x65, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x67, 0x6f, 0x75, 0x72, 0x64,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x6f, 0x72,
	0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x6f, 0x75,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x67, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x9e, 0x01, 0x0a, 0x09, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x16, 0x0a, 0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x6c, 0x66, 0x61,
	0x72, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x65, 0x6c, 0x66,
	0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x6f, 0x72, 0x74,
	0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xef, 0x02, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x70, 0x61, 0x79, 0x41,
	0x70, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74,
	0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x2e, 0x50, 0x61, 0x79, 0x41, 0x70, 0x70, 0x52, 0x06, 0x70, 0x61, 0x79, 0x41, 0x70, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x79, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x72, 0x65, 0x63, 0x76, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x76, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x12, 0x4a, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x5f,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x61, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x50, 0x61, 0x79, 0x53, 0x63, 0x65,
	0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x70, 0x61, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6d, 0x0a, 0x0b, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x75, 0x72, 0x64,
	0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x46, 0x0a, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f,
	0x72, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x53, 0x68,
	0x6f, 0x77, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x22, 0x7d, 0x0a, 0x14,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x72, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x42, 0x61, 0x63, 0x6b, 0x22, 0xa7, 0x01, 0x0a, 0x0a,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x3b, 0x0a, 0x0a, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x6c, 0x66, 0x61,
	0x72, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x65, 0x6c, 0x66,
	0x61, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x67, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x6a,
	0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x73, 0x52, 0x65, 0x62, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x62, 0x61, 0x74, 0x65, 0x22, 0xa3, 0x02, 0x0a, 0x12,
	0x46, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x67, 0x6f, 0x75, 0x72,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x6f,
	0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x6f,
	0x75, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x09, 0x67, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x41, 0x0a, 0x0c, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x43, 0x6f,
	0x6e, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75,
	0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x4a, 0x61, 0x64, 0x65, 0x52,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x0c, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x52, 0x65, 0x62, 0x61, 0x74,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x62, 0x61, 0x74,
	0x65, 0x22, 0x69, 0x0a, 0x09, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x40,
	0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x67, 0x6f, 0x75, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x67, 0x6f, 0x75, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x22, 0x6f, 0x0a, 0x0f,
	0x47, 0x6f, 0x75, 0x72, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x12,
	0x1c, 0x0a, 0x09, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x49, 0x44, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x67, 0x69, 0x66, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x67, 0x69, 0x66, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x95, 0x01,
	0x0a, 0x0c, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x43,
	0x0a, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70,
	0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x72, 0x61, 0x77, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x64, 0x72, 0x61, 0x77, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x4e, 0x75, 0x6d, 0x22, 0x90, 0x01, 0x0a, 0x12, 0x4a, 0x61, 0x64, 0x65, 0x52, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x1e, 0x0a, 0x0a,
	0x62, 0x65, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x6c, 0x66,
	0x61, 0x72, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x65, 0x6c,
	0x66, 0x61, 0x72, 0x65, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x41, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x2a, 0x2f, 0x0a, 0x09, 0x47,
	0x69, 0x66, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x10, 0x00, 0x12, 0x05, 0x0a, 0x01, 0x52, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x53,
	0x52, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x53, 0x52, 0x10, 0x03, 0x2a, 0x2b, 0x0a, 0x10,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x09, 0x0a, 0x05, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4a,
	0x61, 0x64, 0x65, 0x52, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x32, 0xac, 0x03, 0x0a, 0x11, 0x46, 0x6f,
	0x72, 0x74, 0x75, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x41, 0x70, 0x69, 0x12,
	0x52, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x20, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c,
	0x6f, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x08, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x12,
	0x1c, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72,
	0x65, 0x2e, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e,
	0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e,
	0x50, 0x61, 0x79, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x61, 0x0a, 0x11, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x25, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f,
	0x72, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e,
	0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x4a,
	0x0a, 0x10, 0x50, 0x61, 0x79, 0x47, 0x6f, 0x75, 0x72, 0x64, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x12, 0x1a, 0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x68, 0x69, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0a, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x1e, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75,
	0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x66, 0x6f, 0x72, 0x74, 0x75,
	0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x73, 0x70, 0x42, 0x47, 0x5a, 0x45, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f,
	0x67, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70,
	0x62, 0x2f, 0x66, 0x6f, 0x72, 0x74, 0x75, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x6c, 0x6f, 0x72,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescOnce sync.Once
	file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescData = file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDesc
)

func file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescGZIP() []byte {
	file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescOnce.Do(func() {
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescData)
	})
	return file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDescData
}

var file_pb_platform_activity_fortune_explore_fortune_explore_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_pb_platform_activity_fortune_explore_fortune_explore_proto_goTypes = []interface{}{
	(GiftLevel)(0),                              // 0: fortune_explore.GiftLevel
	(RecordRewardType)(0),                       // 1: fortune_explore.RecordRewardType
	(*GetGourdListReq)(nil),                     // 2: fortune_explore.GetGourdListReq
	(*GetGourdListRsp)(nil),                     // 3: fortune_explore.GetGourdListRsp
	(*UserExploreShowData)(nil),                 // 4: fortune_explore.UserExploreShowData
	(*UserStatusInfo)(nil),                      // 5: fortune_explore.UserStatusInfo
	(*UserExploreData)(nil),                     // 6: fortune_explore.UserExploreData
	(*JadeRingInfo)(nil),                        // 7: fortune_explore.JadeRingInfo
	(*RewardInfo)(nil),                          // 8: fortune_explore.RewardInfo
	(*ExploreRound)(nil),                        // 9: fortune_explore.ExploreRound
	(*GourdInfo)(nil),                           // 10: fortune_explore.GourdInfo
	(*PayGourdReq)(nil),                         // 11: fortune_explore.PayGourdReq
	(*PayGourdRsp)(nil),                         // 12: fortune_explore.PayGourdRsp
	(*QueryRewardRecordReq)(nil),                // 13: fortune_explore.QueryRewardRecordReq
	(*QueryRewardRecordRsp)(nil),                // 14: fortune_explore.QueryRewardRecordRsp
	(*RecordItem)(nil),                          // 15: fortune_explore.RecordItem
	(*ConfigSyncReq)(nil),                       // 16: fortune_explore.ConfigSyncReq
	(*FortuneExploreConf)(nil),                  // 17: fortune_explore.FortuneExploreConf
	(*GourdConf)(nil),                           // 18: fortune_explore.GourdConf
	(*GourdRewardConf)(nil),                     // 19: fortune_explore.GourdRewardConf
	(*JadeRingConf)(nil),                        // 20: fortune_explore.JadeRingConf
	(*JadeRingRewardConf)(nil),                  // 21: fortune_explore.JadeRingRewardConf
	(*ConfigSyncRsp)(nil),                       // 22: fortune_explore.ConfigSyncRsp
	(*adapter_unified_assets.PayApp)(nil),       // 23: adapter_unified_assets.PayApp
	(*adapter_unified_assets.PaySceneInfo)(nil), // 24: adapter_unified_assets.PaySceneInfo
	(*callback.OrderShipmentReq)(nil),           // 25: callback.OrderShipmentReq
	(*callback.OrderShipmentRsp)(nil),           // 26: callback.OrderShipmentRsp
}
var file_pb_platform_activity_fortune_explore_fortune_explore_proto_depIdxs = []int32{
	4,  // 0: fortune_explore.GetGourdListRsp.displayInfo:type_name -> fortune_explore.UserExploreShowData
	9,  // 1: fortune_explore.UserExploreShowData.exploreInfo:type_name -> fortune_explore.ExploreRound
	7,  // 2: fortune_explore.UserExploreShowData.jadeRingInfo:type_name -> fortune_explore.JadeRingInfo
	5,  // 3: fortune_explore.UserExploreShowData.userStatus:type_name -> fortune_explore.UserStatusInfo
	9,  // 4: fortune_explore.UserExploreData.exploreList:type_name -> fortune_explore.ExploreRound
	7,  // 5: fortune_explore.UserExploreData.jadeRingInfo:type_name -> fortune_explore.JadeRingInfo
	15, // 6: fortune_explore.UserExploreData.recordInfo:type_name -> fortune_explore.RecordItem
	8,  // 7: fortune_explore.JadeRingInfo.rewardInfo:type_name -> fortune_explore.RewardInfo
	10, // 8: fortune_explore.ExploreRound.gourdList:type_name -> fortune_explore.GourdInfo
	8,  // 9: fortune_explore.GourdInfo.rewardInfo:type_name -> fortune_explore.RewardInfo
	23, // 10: fortune_explore.PayGourdReq.payApp:type_name -> adapter_unified_assets.PayApp
	24, // 11: fortune_explore.PayGourdReq.pay_scene_info:type_name -> adapter_unified_assets.PaySceneInfo
	4,  // 12: fortune_explore.PayGourdRsp.displayInfo:type_name -> fortune_explore.UserExploreShowData
	15, // 13: fortune_explore.QueryRewardRecordRsp.info:type_name -> fortune_explore.RecordItem
	8,  // 14: fortune_explore.RecordItem.rewardInfo:type_name -> fortune_explore.RewardInfo
	18, // 15: fortune_explore.FortuneExploreConf.gourdList:type_name -> fortune_explore.GourdConf
	20, // 16: fortune_explore.FortuneExploreConf.JadeRingConf:type_name -> fortune_explore.JadeRingConf
	19, // 17: fortune_explore.GourdConf.rewardList:type_name -> fortune_explore.GourdRewardConf
	21, // 18: fortune_explore.JadeRingConf.rewardList:type_name -> fortune_explore.JadeRingRewardConf
	2,  // 19: fortune_explore.FortuneExploreApi.GetGourdList:input_type -> fortune_explore.GetGourdListReq
	11, // 20: fortune_explore.FortuneExploreApi.PayGourd:input_type -> fortune_explore.PayGourdReq
	13, // 21: fortune_explore.FortuneExploreApi.QueryRewardRecord:input_type -> fortune_explore.QueryRewardRecordReq
	25, // 22: fortune_explore.FortuneExploreApi.PayGourdCallback:input_type -> callback.OrderShipmentReq
	16, // 23: fortune_explore.FortuneExploreApi.ConfigSync:input_type -> fortune_explore.ConfigSyncReq
	3,  // 24: fortune_explore.FortuneExploreApi.GetGourdList:output_type -> fortune_explore.GetGourdListRsp
	12, // 25: fortune_explore.FortuneExploreApi.PayGourd:output_type -> fortune_explore.PayGourdRsp
	14, // 26: fortune_explore.FortuneExploreApi.QueryRewardRecord:output_type -> fortune_explore.QueryRewardRecordRsp
	26, // 27: fortune_explore.FortuneExploreApi.PayGourdCallback:output_type -> callback.OrderShipmentRsp
	22, // 28: fortune_explore.FortuneExploreApi.ConfigSync:output_type -> fortune_explore.ConfigSyncRsp
	24, // [24:29] is the sub-list for method output_type
	19, // [19:24] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_pb_platform_activity_fortune_explore_fortune_explore_proto_init() }
func file_pb_platform_activity_fortune_explore_fortune_explore_proto_init() {
	if File_pb_platform_activity_fortune_explore_fortune_explore_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGourdListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGourdListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExploreShowData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserStatusInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExploreData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JadeRingInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExploreRound); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GourdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayGourdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayGourdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRewardRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRewardRecordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigSyncReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FortuneExploreConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GourdConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GourdRewardConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JadeRingConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JadeRingRewardConf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigSyncRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_platform_activity_fortune_explore_fortune_explore_proto_goTypes,
		DependencyIndexes: file_pb_platform_activity_fortune_explore_fortune_explore_proto_depIdxs,
		EnumInfos:         file_pb_platform_activity_fortune_explore_fortune_explore_proto_enumTypes,
		MessageInfos:      file_pb_platform_activity_fortune_explore_fortune_explore_proto_msgTypes,
	}.Build()
	File_pb_platform_activity_fortune_explore_fortune_explore_proto = out.File
	file_pb_platform_activity_fortune_explore_fortune_explore_proto_rawDesc = nil
	file_pb_platform_activity_fortune_explore_fortune_explore_proto_goTypes = nil
	file_pb_platform_activity_fortune_explore_fortune_explore_proto_depIdxs = nil
}
