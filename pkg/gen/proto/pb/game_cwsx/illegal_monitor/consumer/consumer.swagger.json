{"swagger": "2.0", "info": {"title": "pb/game_cwsx/illegal_monitor/consumer/consumer.proto", "version": "version not set"}, "tags": [{"name": "IllegalMonitor"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/game_cwsx.IllegalMonitor/HandleException": {"post": {"operationId": "IllegalMonitor_HandleException", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsxHandleExceptionRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsxHandleExceptionReq"}}], "tags": ["IllegalMonitor"]}}, "/game_cwsx.IllegalMonitor/ManualInvoke": {"post": {"operationId": "IllegalMonitor_ManualInvoke", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/game_cwsxManualInvokeRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/game_cwsxManualInvokeReq"}}], "tags": ["IllegalMonitor"]}}}, "definitions": {"game_cwsxHandleExceptionReq": {"type": "object", "properties": {"action": {"type": "string"}, "openId": {"type": "string"}, "appId": {"type": "string"}, "handler": {"type": "string"}, "reason": {"type": "string"}, "ts": {"type": "string", "format": "int64"}}}, "game_cwsxHandleExceptionRsp": {"type": "object"}, "game_cwsxManualInvokeReq": {"type": "object", "properties": {"stageId": {"type": "integer", "format": "int64"}, "appId": {"type": "string"}, "action": {"type": "integer", "format": "int64"}, "date": {"type": "string"}, "beginDate": {"type": "string"}, "endDate": {"type": "string"}, "stageAbGroup": {"type": "integer", "format": "int64"}, "stageTag": {"type": "string"}}}, "game_cwsxManualInvokeRsp": {"type": "object", "properties": {"result": {"type": "string"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}