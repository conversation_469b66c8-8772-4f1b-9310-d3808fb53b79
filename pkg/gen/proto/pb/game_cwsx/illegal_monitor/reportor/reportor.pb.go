// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/illegal_monitor/reportor/reportor.proto

package reportor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	event "kugou_adapter_service/pkg/gen/proto/pb/event"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlayDetailReportRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PlayDetailReportRsp) Reset() {
	*x = PlayDetailReportRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayDetailReportRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayDetailReportRsp) ProtoMessage() {}

func (x *PlayDetailReportRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayDetailReportRsp.ProtoReflect.Descriptor instead.
func (*PlayDetailReportRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescGZIP(), []int{0}
}

var File_pb_game_cwsx_illegal_monitor_reportor_reportor_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDesc = []byte{
	0x0a, 0x34, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69,
	0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x1a, 0x14, 0x70, 0x62, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x15, 0x0a, 0x13, 0x50, 0x6c, 0x61, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x73, 0x70, 0x32, 0x55,
	0x0a, 0x08, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x6f, 0x72, 0x12, 0x49, 0x0a, 0x10, 0x50, 0x6c,
	0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x15,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x57, 0x53, 0x58, 0x50, 0x6c, 0x61, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x1e, 0x2e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x73, 0x70, 0x42, 0x5a, 0x5a, 0x58, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x2f, 0x74, 0x6d, 0x65, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c,
	0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x6f,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescData = file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDesc
)

func file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescData)
	})
	return file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDescData
}

var file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_goTypes = []interface{}{
	(*PlayDetailReportRsp)(nil),  // 0: game_cwsx.PlayDetailReportRsp
	(*event.CWSXPlayDetail)(nil), // 1: event.CWSXPlayDetail
}
var file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_depIdxs = []int32{
	1, // 0: game_cwsx.Reportor.PlayDetailReport:input_type -> event.CWSXPlayDetail
	0, // 1: game_cwsx.Reportor.PlayDetailReport:output_type -> game_cwsx.PlayDetailReportRsp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_init() }
func file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_init() {
	if File_pb_game_cwsx_illegal_monitor_reportor_reportor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayDetailReportRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_illegal_monitor_reportor_reportor_proto = out.File
	file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_rawDesc = nil
	file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_goTypes = nil
	file_pb_game_cwsx_illegal_monitor_reportor_reportor_proto_depIdxs = nil
}
