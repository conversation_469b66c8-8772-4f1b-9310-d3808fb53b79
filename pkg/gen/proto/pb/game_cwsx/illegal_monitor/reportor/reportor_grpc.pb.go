// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/illegal_monitor/reportor/reportor.proto

package reportor

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	event "kugou_adapter_service/pkg/gen/proto/pb/event"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Reportor_PlayDetailReport_FullMethodName = "/game_cwsx.Reportor/PlayDetailReport"
)

// ReportorClient is the client API for Reportor service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReportorClient interface {
	// 宠物消除闯关明细数据上报接口
	PlayDetailReport(ctx context.Context, in *event.CWSXPlayDetail, opts ...grpc.CallOption) (*PlayDetailReportRsp, error)
}

type reportorClient struct {
	cc grpc.ClientConnInterface
}

func NewReportorClient(cc grpc.ClientConnInterface) ReportorClient {
	return &reportorClient{cc}
}

func (c *reportorClient) PlayDetailReport(ctx context.Context, in *event.CWSXPlayDetail, opts ...grpc.CallOption) (*PlayDetailReportRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlayDetailReportRsp)
	err := c.cc.Invoke(ctx, Reportor_PlayDetailReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReportorServer is the server API for Reportor service.
// All implementations should embed UnimplementedReportorServer
// for forward compatibility
type ReportorServer interface {
	// 宠物消除闯关明细数据上报接口
	PlayDetailReport(context.Context, *event.CWSXPlayDetail) (*PlayDetailReportRsp, error)
}

// UnimplementedReportorServer should be embedded to have forward compatible implementations.
type UnimplementedReportorServer struct {
}

func (UnimplementedReportorServer) PlayDetailReport(context.Context, *event.CWSXPlayDetail) (*PlayDetailReportRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlayDetailReport not implemented")
}

// UnsafeReportorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReportorServer will
// result in compilation errors.
type UnsafeReportorServer interface {
	mustEmbedUnimplementedReportorServer()
}

func RegisterReportorServer(s grpc.ServiceRegistrar, srv ReportorServer) {
	s.RegisterService(&Reportor_ServiceDesc, srv)
}

func _Reportor_PlayDetailReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(event.CWSXPlayDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReportorServer).PlayDetailReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Reportor_PlayDetailReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReportorServer).PlayDetailReport(ctx, req.(*event.CWSXPlayDetail))
	}
	return interceptor(ctx, in, info, handler)
}

// Reportor_ServiceDesc is the grpc.ServiceDesc for Reportor service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Reportor_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "game_cwsx.Reportor",
	HandlerType: (*ReportorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlayDetailReport",
			Handler:    _Reportor_PlayDetailReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/illegal_monitor/reportor/reportor.proto",
}
