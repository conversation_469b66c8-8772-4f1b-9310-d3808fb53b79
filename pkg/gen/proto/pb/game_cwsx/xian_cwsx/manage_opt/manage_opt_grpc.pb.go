// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/xian_cwsx/manage_opt/manage_opt.proto

package manage_opt

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	ManageOpt_ActivityOpt_FullMethodName = "/xian_cwsx.ManageOpt/ActivityOpt"
)

// ManageOptClient is the client API for ManageOpt service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ManageOptClient interface {
	// 活动单独配置
	ActivityOpt(ctx context.Context, in *ActivityOptReq, opts ...grpc.CallOption) (*ActivityOptRsp, error)
}

type manageOptClient struct {
	cc grpc.ClientConnInterface
}

func NewManageOptClient(cc grpc.ClientConnInterface) ManageOptClient {
	return &manageOptClient{cc}
}

func (c *manageOptClient) ActivityOpt(ctx context.Context, in *ActivityOptReq, opts ...grpc.CallOption) (*ActivityOptRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivityOptRsp)
	err := c.cc.Invoke(ctx, ManageOpt_ActivityOpt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ManageOptServer is the server API for ManageOpt service.
// All implementations should embed UnimplementedManageOptServer
// for forward compatibility
type ManageOptServer interface {
	// 活动单独配置
	ActivityOpt(context.Context, *ActivityOptReq) (*ActivityOptRsp, error)
}

// UnimplementedManageOptServer should be embedded to have forward compatible implementations.
type UnimplementedManageOptServer struct {
}

func (UnimplementedManageOptServer) ActivityOpt(context.Context, *ActivityOptReq) (*ActivityOptRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityOpt not implemented")
}

// UnsafeManageOptServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ManageOptServer will
// result in compilation errors.
type UnsafeManageOptServer interface {
	mustEmbedUnimplementedManageOptServer()
}

func RegisterManageOptServer(s grpc.ServiceRegistrar, srv ManageOptServer) {
	s.RegisterService(&ManageOpt_ServiceDesc, srv)
}

func _ManageOpt_ActivityOpt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityOptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ManageOptServer).ActivityOpt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ManageOpt_ActivityOpt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ManageOptServer).ActivityOpt(ctx, req.(*ActivityOptReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ManageOpt_ServiceDesc is the grpc.ServiceDesc for ManageOpt service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ManageOpt_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "xian_cwsx.ManageOpt",
	HandlerType: (*ManageOptServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ActivityOpt",
			Handler:    _ManageOpt_ActivityOpt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/xian_cwsx/manage_opt/manage_opt.proto",
}
