// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/xian_cwsx/push/push.proto

package push

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	Push_CallbackActivity_FullMethodName = "/xian_cwsx.Push/CallbackActivity"
)

// PushClient is the client API for Push service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushClient interface {
	// 活动上报回调
	CallbackActivity(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error)
}

type pushClient struct {
	cc grpc.ClientConnInterface
}

func NewPushClient(cc grpc.ClientConnInterface) PushClient {
	return &pushClient{cc}
}

func (c *pushClient) CallbackActivity(ctx context.Context, in *TimerCallbackRequest, opts ...grpc.CallOption) (*TimerCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimerCallbackResponse)
	err := c.cc.Invoke(ctx, Push_CallbackActivity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushServer is the server API for Push service.
// All implementations should embed UnimplementedPushServer
// for forward compatibility
type PushServer interface {
	// 活动上报回调
	CallbackActivity(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error)
}

// UnimplementedPushServer should be embedded to have forward compatible implementations.
type UnimplementedPushServer struct {
}

func (UnimplementedPushServer) CallbackActivity(context.Context, *TimerCallbackRequest) (*TimerCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CallbackActivity not implemented")
}

// UnsafePushServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushServer will
// result in compilation errors.
type UnsafePushServer interface {
	mustEmbedUnimplementedPushServer()
}

func RegisterPushServer(s grpc.ServiceRegistrar, srv PushServer) {
	s.RegisterService(&Push_ServiceDesc, srv)
}

func _Push_CallbackActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimerCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushServer).CallbackActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Push_CallbackActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushServer).CallbackActivity(ctx, req.(*TimerCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Push_ServiceDesc is the grpc.ServiceDesc for Push service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Push_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "xian_cwsx.Push",
	HandlerType: (*PushServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CallbackActivity",
			Handler:    _Push_CallbackActivity_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/xian_cwsx/push/push.proto",
}
