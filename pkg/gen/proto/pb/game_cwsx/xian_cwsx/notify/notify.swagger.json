{"swagger": "2.0", "info": {"title": "pb/game_cwsx/xian_cwsx/notify/notify.proto", "version": "version not set"}, "tags": [{"name": "Notify"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xian_cwsx.Notify/AchiPlant": {"post": {"operationId": "Notify_AchiPlant", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxAchiPlantRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxAchiPlantReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/ActivityCheck": {"post": {"summary": "活动状态查询回调", "operationId": "Notify_ActivityCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/inletInletStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/inletInletStatusReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/BatchGameDeliveryPackage": {"post": {"summary": "批量兑换礼包", "operationId": "Notify_BatchGameDeliveryPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxBatchGameDeliveryPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxBatchGameDeliveryPackageReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/BatchGameDeliveryPackageList": {"post": {"summary": "批量兑换礼包--单个id奖励区分", "operationId": "Notify_BatchGameDeliveryPackageList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxBatchGameDeliveryPackageListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxBatchGameDeliveryPackageListReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/BatchGetDecorate": {"post": {"summary": "批量查询玩家装饰绑定信息", "operationId": "Notify_BatchGetDecorate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/privilegeBatchGetDecorateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/privilegeBatchGetDecorateReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/BatchGetUserTeam": {"post": {"summary": "批量查询用户战队", "operationId": "Notify_BatchGetUserTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxBatchGetUserTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxBatchGetUserTeamReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/BatchQueryCwsxPackage": {"post": {"summary": "查询礼包", "operationId": "Notify_BatchQueryCwsxPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxBatchQueryCwsxPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxBatchQueryCwsxPackageReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/Callback": {"post": {"summary": "定时器回调", "operationId": "Notify_Callback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTimerCallbackRequest"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/CallbackStepBuy": {"post": {"summary": "回调步数购买", "operationId": "Notify_CallbackStepBuy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxCallbackStepBuyRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxCallbackStepBuyReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/ChangeRegionRank": {"post": {"summary": "更换地区，地区总榜变动", "operationId": "Notify_ChangeRegionRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxChangeRegionRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxChangeRegionRankReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/CheckGM": {"post": {"summary": "是否是gm, 是否在白名单", "operationId": "Notify_CheckGM", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxCheckGMRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxCheckGMReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/CheckOrder": {"post": {"summary": "校验订单", "operationId": "Notify_CheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxCheckOrderReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/CreateTeam": {"post": {"summary": "Todo 仅供中台调用 -- 中台审核完毕调用", "operationId": "Notify_CreateTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxCreateTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxCreateTeamReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/DeliverGoodsCallback": {"post": {"summary": "游戏圈礼包发货回调", "operationId": "Notify_DeliverGoodsCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxDeliverGoodsImplementRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxDeliverGoodsImplementReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/Delivery": {"post": {"summary": "发货", "operationId": "Notify_Delivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxDeliveryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxDeliveryReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/DragonBoatAchi": {"post": {"summary": "更新龙舟赛成就", "operationId": "Notify_DragonBoatAchi", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxDragonBoatAchiRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxDragonBoatAchiReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/GameDeliveryPackage": {"post": {"summary": "兑换礼包", "operationId": "Notify_GameDeliveryPackage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxGameDeliveryPackageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxGameDeliveryPackageReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/HeartFullCallback": {"post": {"summary": "玩家体力恢复满事件回调", "operationId": "Notify_HeartFullCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTimerCallbackRequest"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/InletSync": {"post": {"summary": "负责Game服务里的所有活动状态同步", "operationId": "Notify_InletSync", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/inletActivityStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/inletActivityStateReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/InlineDecorateCheck": {"post": {"summary": "当前装扮校准", "operationId": "Notify_InlineDecorateCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxInlineDecorateCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxInlineDecorateCheckReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/LabaUserInfo": {"post": {"summary": "拉霸要获取的用户信息", "operationId": "Notify_LabaUserInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxLabaUserInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxLabaUserInfoReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/LoadLightweightAssets": {"post": {"summary": "资产查询回调", "operationId": "Notify_LoadLightweightAssets", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxLoadLightweightAssetsRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxLoadLightweightAssetsReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/QueryStage": {"post": {"summary": "查询闯关", "description": "查询闯关", "operationId": "Notify_QueryStage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxQueryStageRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxQueryStageReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/QueryTeamMembers": {"post": {"summary": "玩家战队队员查询", "operationId": "Notify_QueryTeamMembers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxQueryTeamMembersRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxQueryTeamMembersReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/QueryUserTeam": {"post": {"summary": "玩家战队查询", "operationId": "Notify_QueryUserTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxQueryUserTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxQueryUserTeamReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/ReportPlantCoin": {"post": {"summary": "庄园关金币上报回调", "operationId": "Notify_ReportPlantCoin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxReportPlantCoinRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxReportPlantCoinReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/SafeCallback": {"post": {"summary": "--- 弹窗相关---\n弹窗优先级更新\n rpc UpdatePopUpRegister(UpdatePopUpRegisterReq) returns (UpdatePopUpRegisterRsp);\n安全审查回调 改头像", "operationId": "Notify_SafeCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSafeCheckCallbackV2Rsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSafeCheckCallbackV2Req"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/SafeCallbackDescribe": {"post": {"summary": "安全审查，描述置空", "operationId": "Notify_SafeCallbackDescribe", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSafeCheckCallbackV2Rsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSafeCheckCallbackV2Req"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/SafeCallbackOp": {"post": {"summary": "安全 重置/解散", "operationId": "Notify_SafeCallbackOp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSafeCallbackOpRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSafeCallbackOpReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/SafeCallbackPin": {"post": {"summary": "安全审查，频控", "operationId": "Notify_SafeCallbackPin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSafeCheckCallbackV2Rsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSafeCheckCallbackV2Req"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/SafeCallbackQuery": {"post": {"summary": "安全 查询", "operationId": "Notify_SafeCallbackQuery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSafeCallbackQueryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSafeCallbackQueryReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/SuperBollRetentionPopup": {"post": {"summary": "超级彩球挽回", "operationId": "Notify_SuperBollRetentionPopup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSuperBollRetentionPopupRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSuperBollRetentionPopupReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/TeamWeekCallback": {"post": {"summary": "战队周榜发奖回调", "operationId": "Notify_TeamWeekCallback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTimerCallbackResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTimerCallbackRequest"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/TeamsBatch": {"post": {"summary": "查询team的信息", "operationId": "Notify_TeamsBatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamsBatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamsBatchReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/UserGuidesSubmit": {"post": {"summary": "查询玩家的引导\n rpc UserGuides(UserGuidesReq) returns (UserGuidesRsp);\n提交玩家的引导", "operationId": "Notify_UserGuidesSubmit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxUserGuidesSubmitRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxUserGuidesSubmitReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/VIPCheck": {"post": {"operationId": "Notify_VIPCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/inletActivityStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/inletActivityStateReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/WarOrderCheckOrder": {"post": {"summary": "------战令相关------\n战令校验激活订单", "operationId": "Notify_WarOrderCheckOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxWarOrderCheckOrderRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxWarOrderCheckOrderReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/WarOrderDelivery": {"post": {"summary": "战令激活发货", "operationId": "Notify_WarOrderDelivery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxWarOrderDeliveryRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxWarOrderDeliveryReq"}}], "tags": ["Notify"]}}, "/xian_cwsx.Notify/WarOrderRetentionPopup": {"post": {"summary": "战令挽回查询", "operationId": "Notify_WarOrderRetentionPopup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxWarOrderRetentionPopupRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxWarOrderRetentionPopupReq"}}], "tags": ["Notify"]}}}, "definitions": {"DeliverGoodsImplementReqGood": {"type": "object", "properties": {"id": {"type": "string", "title": "游戏道具id标识"}, "num": {"type": "string", "format": "int64", "title": "发送的道具数量"}}}, "inletActivityStateReq": {"type": "object", "properties": {"number": {"$ref": "#/definitions/inletInletNumber", "title": "入口活动编号=>InletNumber"}, "openId": {"type": "string", "title": "用户的open_id"}, "os": {"$ref": "#/definitions/inletPlatformType", "title": "设备类型"}, "sceneId": {"type": "integer", "format": "int32", "title": "场景ID"}, "userAgent": {"type": "string", "title": "ua"}, "appId": {"type": "string", "title": "appid"}, "needData": {"$ref": "#/definitions/inletNeedDataType", "title": "需要具体数据"}, "floor": {"type": "string", "format": "int64", "title": "玩家当前关卡"}, "cacheBindKey": {"type": "string", "title": "卢学彦 【荣耀奖牌赛】"}, "teamId": {"type": "string", "title": "战队ID"}}, "title": "ActivityStateReq 查询活动状态请求"}, "inletActivityStateRsp": {"type": "object", "properties": {"inletStatus": {"$ref": "#/definitions/inletInletStatusType", "title": "入口状态 【1.0版本弃用】"}, "nextReqTime": {"type": "string", "format": "int64", "title": "下次请求时间  -1永久保持历史状态"}, "startTime": {"type": "string", "format": "int64", "title": "活动开始时间"}, "endTime": {"type": "string", "format": "int64", "title": "活动结束时间"}, "data": {"type": "string", "format": "byte", "title": "当need_data=1时，返回具体活动的数据。"}, "round": {"type": "string", "title": "轮次 刷新记录使用"}, "buyStatus": {"$ref": "#/definitions/inletBuyType", "title": "购买状态"}, "showType": {"$ref": "#/definitions/inletManageShowType", "description": "入口展示状态【决定是否入口展示】:在nextReqTime过期后重新加载", "title": "-- 入口展示条件，下面的值拥有后，忽略上面的【 InletStatusType inlet_status = 1; // 入口状态】的状态"}, "popupType": {"$ref": "#/definitions/inletPopupType", "title": "弹窗的状态【决定弹窗的类型，在showType开启的时候才会有效】:在nextReqTime过期后重新加载"}}, "title": "ActivityStateRsp 查询活动状态响应 下次请求仅在 在need_data=1的时候不判断next_req_time。入口服务也不记载data"}, "inletBuyType": {"type": "string", "enum": ["BuyType_None", "BuyType_Yes"], "default": "BuyType_None", "description": "- BuyType_None: 未购买\n - BuyType_Yes: 已购买", "title": "BuyType 需要具体数据"}, "inletInletNumber": {"type": "string", "enum": ["InletNumber_None", "InletNumber_HonorMedalCompetition", "InletNumber_AirplaneRace", "InletNumber_SuperColorfulLights", "InletNumber_TeamRudder", "InletNumber_TeamCompetition", "InletNumber_BattlePass", "InletNumber_Fishing", "InletNumber_CuteRabbitParadise", "InletNumber_EndlessTreasures", "InletNumber_CheckIn", "InletNumber_LightingRush", "InletNumber_SpecialDiscountPackage", "InletNumber_NoviceChallengeEvent", "InletNumber_DragonsTreasure", "InletNumber_InviteFriends", "InletNumber_ShareFriends", "InletNumber_Favorite", "InletNumber_Recharge", "InletNumber_EveryDayReceive", "InletNumber_DragonBoat", "InletNumber_DailyTask", "InletNumber_Laba", "InletNumber_Vip", "InletNumber_CwsxShop", "InletNumber_GameHub", "InletNumber_Announces", "InletNumber_RescuePlants", "InletNumber_Dan", "InletNumber_Block", "InletNumber_DecPop", "InletNumber_WeekRank", "InletNumber_StarActivity", "InletNumber_PeakRaceBigRound", "InletNumber_CollectTasks", "InletNumber_PeakRaceSmallRound", "InletNumber_Announce"], "default": "InletNumber_None", "description": "- InletNumber_None: 无\n - InletNumber_HonorMedalCompetition: 荣耀奖牌赛 @卢学彦\n - InletNumber_AirplaneRace: 飞机竞赛 @作废\n - InletNumber_SuperColorfulLights: 超级彩灯 @上官冲\n - InletNumber_TeamRudder: 战队淘金 @裴晓晨\n - InletNumber_TeamCompetition: 战队竞赛  @作废\n - InletNumber_BattlePass: 战令 @上官冲\n - InletNumber_Fishing: 小猫钓鱼 @裴晓晨\n - InletNumber_CuteRabbitParadise: 萌兔乐园 @卢学彦\n - InletNumber_EndlessTreasures: 无尽宝藏 @陈航\n - InletNumber_CheckIn: 签到 @上官冲\n - InletNumber_LightingRush: 彩虹竞速 @裴晓晨\n - InletNumber_SpecialDiscountPackage: 特惠礼包 @陈航\n - InletNumber_NoviceChallengeEvent: 新手闯关活动 @陈航\n - InletNumber_DragonsTreasure: 巨龙宝藏 @陈航\n - InletNumber_InviteFriends: 邀请好友 @白龙斐\n - InletNumber_ShareFriends: 分享好友 @上官冲\n - InletNumber_Favorite: 收藏 @白龙斐\n - InletNumber_Recharge: 宠物三消充值活动 @王国栋\n - InletNumber_EveryDayReceive: 分天领取礼包 @王国栋\n - InletNumber_DragonBoat: 龙舟竞赛 @曾润良\n - InletNumber_DailyTask: 任务中心每日消除金币任务 @卢学彦\n - InletNumber_Laba: 拉霸活动@车照\n - InletNumber_Vip: VIP专有客服 @曾润良\n - InletNumber_CwsxShop: 宠物三消商城入口 @裴晓晨\n - InletNumber_GameHub: 微信游戏圈 @裴晓晨\n - InletNumber_Announces: 公告 @白龙斐\n - InletNumber_RescuePlants: 营救植物 @上官冲\n - InletNumber_Dan: 段位赛 @车照\n - InletNumber_Block: 俄罗斯方块 @裴晓晨\n - InletNumber_DecPop: ------ 游戏中的单机小游戏 从200开始-------\n\n解密消除 @白龙斐\n - InletNumber_WeekRank: -------------\n下面是仅有弹窗无的枚举 Begin\n-------------\n非入口的活动 增加枚举值， 1000 开头\n\n周排行榜 @上官冲\n - InletNumber_StarActivity: 明星活动 @裴晓晨\n - InletNumber_PeakRaceBigRound: 巅峰赛大轮次 @上官冲\n - InletNumber_CollectTasks: 收集任务 @上官冲\n - InletNumber_PeakRaceSmallRound: 巅峰赛小轮次 @上官冲\n - InletNumber_Announce: @废弃 公告由 InletNumber_Announces(26号)代替", "title": "InletNumber 增加枚举值"}, "inletInletStatusReq": {"type": "object", "properties": {"number": {"type": "array", "items": {"$ref": "#/definitions/inletInletNumber"}, "title": "入口活动编号=>InletNumber"}, "openId": {"type": "string", "title": "用户的open_id"}, "os": {"$ref": "#/definitions/inletPlatformType", "title": "设备类型"}, "sceneId": {"type": "integer", "format": "int32", "title": "场景ID"}, "userAgent": {"type": "string", "title": "透传ua"}, "appId": {"type": "string", "title": "appId"}}, "title": "InletStatusReq 查询活动状态"}, "inletInletStatusRsp": {"type": "object", "properties": {"inletStatus": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "入口活动编号[InletNumber]=>入口状态[true展示 false关闭]"}}, "title": "InletSetRsp 查询活动状态"}, "inletInletStatusType": {"type": "string", "enum": ["InletStatusType_None", "InletStatusType_Open", "InletStatusType_OpenAndJoin", "InletStatusType_EndHaveAward", "InletStatusType_EndReceivedAward", "InletStatusType_EndNoAward", "InletStatusType_EndNoJoin", "InletStatusType_AbClose", "InletStatusType_OpenAndJoin_HasAward", "InletStatusType_Notice"], "default": "InletStatusType_None", "description": "- InletStatusType_None: 无定义，继续保持老状态\n - InletStatusType_Open: 活动开启中但未参与\n - InletStatusType_OpenAndJoin: 活动开启，玩家参与中\n - InletStatusType_EndHaveAward: 活动结束，但有奖可领：有待领取的奖品\n - InletStatusType_EndReceivedAward: 活动结束，但有奖可领：全部奖品领取完成\n - InletStatusType_EndNoAward: 活动结束，但无奖可领：参与后未达到领奖条件\n - InletStatusType_EndNoJoin: 活动结束，且一直未参与\n - InletStatusType_AbClose: Ab无法参与\n - InletStatusType_OpenAndJoin_HasAward: 活动开启，玩家参与中：有待领取的奖品[特殊活动使用]\n - InletStatusType_Notice: 预告期", "title": "InletStatusType 入口状态枚举 （活动入口加载的状态值、弹窗所用的状态值）"}, "inletManageShowType": {"type": "string", "enum": ["ManageShowType_None", "ManageShowType_Open", "ManageShowType_Close"], "default": "ManageShowType_None", "description": "- ManageShowType_None: 无定义，继续保持老状态\n - ManageShowType_Open: 入口展示\n - ManageShowType_Close: 入口关闭", "title": "ManageShowType 查询活动状态响应"}, "inletNeedDataType": {"type": "string", "enum": ["NeedDataType_None", "NeedDataType_Yes"], "default": "NeedDataType_None", "description": "- NeedDataType_None: 不需要活动具体数据，仅返回状态即可\n - NeedDataType_Yes: 需要具体数据", "title": "NeedDataType 需要具体数据"}, "inletPlatformType": {"type": "string", "enum": ["PlatformUnknown", "PlatformAndroid", "PlatformIOS"], "default": "PlatformUnknown", "title": "PlatformType 设备类型"}, "inletPopupType": {"type": "string", "enum": ["PopupType_None", "PopupType_EndNoAward", "PopupType_EndHaveAward", "PopupType_Begin", "PopupType_Buy"], "default": "PopupType_None", "description": "- PopupType_None: 无\n - PopupType_EndNoAward: 结算期间：无领奖状态\n - PopupType_EndHaveAward: 结算期间：有领奖状态\n - PopupType_Begin: 活动开启弹窗\n - PopupType_Buy: 购买类型:展示时间", "title": "弹窗的类型 弹窗的类型状态 弹窗顺序由编号升序决策"}, "privilegeBatchGetDecorateReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "array", "items": {"type": "string"}}}}, "privilegeBatchGetDecorateRsp": {"type": "object", "properties": {"mapExt": {"type": "object", "additionalProperties": {"$ref": "#/definitions/privilegeUserPriviledges"}}}}, "privilegeUserPriviledges": {"type": "object", "properties": {"status": {"type": "object", "additionalProperties": {"type": "string"}, "description": "用户正在穿戴的特权列表", "title": "key以约定好的cwsx_plg_作为前缀"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "xian_cwsxAchiPlantReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "plantCnt": {"type": "integer", "format": "int32", "title": "收获次数"}, "plantCntT": {"type": "string", "format": "int64", "title": "收获次数时间"}}}, "xian_cwsxAchiPlantRsp": {"type": "object"}, "xian_cwsxBatchGameDeliveryPackageListReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "packageIds": {"type": "array", "items": {"type": "string"}, "title": "礼包 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}}}, "xian_cwsxBatchGameDeliveryPackageListRsp": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxGameDeliveryPackageRewards"}}}}, "xian_cwsxBatchGameDeliveryPackageReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "packageIds": {"type": "array", "items": {"type": "string"}, "title": "礼包 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}}}, "xian_cwsxBatchGameDeliveryPackageRsp": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxRewardItem"}, "title": "奖励"}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTreasureCard"}, "title": "如果发奖产生宝藏奖励这个字段不为空"}}}, "xian_cwsxBatchGetUserTeamReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openIds": {"type": "array", "items": {"type": "string"}}}}, "xian_cwsxBatchGetUserTeamRsp": {"type": "object", "properties": {"utMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/xian_cwsxBatchGetUserTeamRspTeamInfo"}}}}, "xian_cwsxBatchGetUserTeamRspTeamInfo": {"type": "object", "properties": {"name": {"type": "string", "title": "战队名称"}}}, "xian_cwsxBatchQueryCwsxPackageReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "packageIds": {"type": "array", "items": {"type": "string"}, "title": "礼包ids"}}}, "xian_cwsxBatchQueryCwsxPackageRsp": {"type": "object", "properties": {"packages": {"type": "object", "additionalProperties": {"$ref": "#/definitions/xian_cwsxPackage"}, "title": "礼包"}}}, "xian_cwsxCallbackStepBuyReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "buyRound": {"type": "integer", "format": "int64", "title": "本次闯关第几次购买"}, "billId": {"type": "string", "title": "购买唯一id"}, "step": {"type": "string", "format": "int64", "title": "获得步数"}}}, "xian_cwsxCallbackStepBuyRsp": {"type": "object"}, "xian_cwsxChangeRegionRankReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "oldCityCode": {"type": "string"}, "oldCityName": {"type": "string"}, "newCityCode": {"type": "string"}, "newCityName": {"type": "string"}}}, "xian_cwsxChangeRegionRankRsp": {"type": "object"}, "xian_cwsxCheckGMReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openid": {"type": "string"}}}, "xian_cwsxCheckGMRsp": {"type": "object", "properties": {"res": {"type": "boolean"}}}, "xian_cwsxCheckOrderReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "price": {"type": "string", "format": "int64", "title": "价格"}, "greenId": {"type": "string", "format": "int64", "title": "绿钻 id"}, "os": {"$ref": "#/definitions/xian_cwsxOperatingSystem", "title": "系统"}}}, "xian_cwsxCheckOrderRsp": {"type": "object"}, "xian_cwsxCreateTeamReq": {"type": "object", "properties": {"predictAvatar": {"type": "integer", "format": "int32", "title": "选择了预制头像id -- 读红石"}, "setAvatar": {"type": "string", "title": "玩家自己设置图片的url 设置了2则不判断1"}, "name": {"type": "string", "title": "战队名字"}, "describe": {"type": "string", "title": "战队描述"}, "isAuditFloor": {"type": "boolean", "title": "加入是否需要审核关卡过关情况"}, "unlockFloor": {"type": "integer", "format": "int32", "title": "加入关卡必须打过的关卡"}}}, "xian_cwsxCreateTeamRsp": {"type": "object"}, "xian_cwsxDeliverGoodsImplementReq": {"type": "object", "properties": {"orderId": {"type": "string", "title": "订单唯一id。 开发者服务器可用于消息去重"}, "isPreview": {"type": "string", "format": "int64", "title": "是否预览调试"}, "toOpenId": {"type": "string", "title": "接收道具的玩家openid"}, "zone": {"type": "string", "format": "int64", "title": "分区。1001: iOS, 2001: Android、PC"}, "giftTypeId": {"type": "string", "format": "int64", "title": "发货礼包类型：1- 每日签到礼包 2- 周福利礼包 3-运营活动礼包 6-每日登录礼包 8 - 游戏圈活动礼包"}, "giftId": {"type": "string", "title": "礼包 ID（可在 MP 配置好礼包后，提前获取）"}, "sendTime": {"type": "string", "format": "int64", "title": "玩家接收道具的时间"}, "goods": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/DeliverGoodsImplementReqGood"}, "title": "发货列表"}, "createTime": {"type": "string", "format": "int64", "title": "消息发送时间"}}, "title": "业务实现接口"}, "xian_cwsxDeliverGoodsImplementRsp": {"type": "object", "title": "业务实现接口"}, "xian_cwsxDeliveryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}}}, "xian_cwsxDeliveryRsp": {"type": "object", "properties": {"products": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxDeliveryRspProduct"}}}}, "xian_cwsxDeliveryRspProduct": {"type": "object", "properties": {"name": {"type": "string"}, "num": {"type": "string", "format": "int64"}}}, "xian_cwsxDragonBoatAchiReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "rank": {"type": "integer", "format": "int32", "title": "龙舟赛名次"}, "rankT": {"type": "string", "format": "int64", "title": "龙舟赛时间"}}}, "xian_cwsxDragonBoatAchiRsp": {"type": "object"}, "xian_cwsxGameDeliveryPackageReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "packageId": {"type": "string", "title": "礼包 id"}, "transactionId": {"type": "string", "title": "唯一订单 id 发中台资产"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}, "num": {"type": "integer", "format": "int64", "title": "发几个礼包,默认一个"}, "type": {"$ref": "#/definitions/xian_cwsxGameDeliveryType", "title": "发奖类型"}, "reason": {"type": "string", "title": "发奖场景"}}}, "xian_cwsxGameDeliveryPackageRewards": {"type": "object", "properties": {"packageId": {"type": "string", "title": "礼包id"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxRewardItem"}, "title": "奖励"}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTreasureCard"}, "title": "如果发奖产生宝藏奖励这个字段不为空"}}}, "xian_cwsxGameDeliveryPackageRsp": {"type": "object", "properties": {"rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxRewardItem"}, "title": "奖励"}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTreasureCard"}, "title": "如果发奖产生宝藏奖励这个字段不为空"}}}, "xian_cwsxGameDeliveryType": {"type": "string", "enum": ["GameDeliveryTypeRedStone", "GameDeliveryTypeMiddleConfig"], "default": "GameDeliveryTypeRedStone", "title": "- GameDeliveryTypeRedStone: 游戏红石 https://gm.tmeoa.com/?type=kg_cwsx_activity_gift_prod\n - GameDeliveryTypeMiddleConfig: 中台配置 https://game-config.tmeoa.com/sanxiao/asset/props"}, "xian_cwsxGameRewardItemType": {"type": "string", "enum": ["GameReward_From_GameNormal", "GameReward_From_Platform", "GameReward_From_GameLimitedTime"], "default": "GameReward_From_GameNormal", "title": "- GameReward_From_GameNormal: 游戏物品-默认\n - GameReward_From_Platform: 平台物品\n - GameReward_From_GameLimitedTime: 游戏物品-限时"}, "xian_cwsxInfiniteItem": {"type": "object", "properties": {"assetId": {"type": "integer", "format": "int64"}, "expireTime": {"type": "integer", "format": "int64", "title": "无限道具到期时间"}}}, "xian_cwsxInlineDecorateCheckReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsxInlineDecorateCheckRsp": {"type": "object"}, "xian_cwsxLabaUserInfoReq": {"type": "object", "properties": {"openid": {"type": "string", "title": "玩家uid"}}}, "xian_cwsxLabaUserInfoRsp": {"type": "object", "properties": {"guideList": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "玩家完成的引导的list"}, "maxFloor": {"type": "integer", "format": "int64", "title": "最大关卡数"}}}, "xian_cwsxLoadLightweightAssetsReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsxLoadLightweightAssetsRsp": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxUserAsset"}, "title": "游戏相关资产"}, "infiniteItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxInfiniteItem"}, "title": "无限战前道具及过期时间"}}}, "xian_cwsxOperatingSystem": {"type": "string", "enum": ["OperatingSystemUnknown", "OperatingSystemAndroid", "OperatingSystemIOS"], "default": "OperatingSystemUnknown"}, "xian_cwsxPackage": {"type": "object", "properties": {"packageId": {"type": "string", "title": "礼包id"}, "packageName": {"type": "string", "title": "礼包名称"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxRewardItem"}, "title": "奖励"}, "price": {"type": "string", "format": "int64", "title": "礼包价格"}}}, "xian_cwsxPopRewardItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "奖励id"}, "num": {"type": "integer", "format": "int64", "title": "奖励数量 限时道具表示分钟数"}, "name": {"type": "string", "title": "奖励名称"}, "type": {"$ref": "#/definitions/xian_cwsxGameRewardItemType", "title": "资产类型"}}}, "xian_cwsxQueryStageReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string", "title": "请求用户"}}}, "xian_cwsxQueryStageRsp": {"type": "object", "properties": {"curStage": {"type": "integer", "format": "int64", "title": "玩家当前成功闯关数"}, "maxNormalFloor": {"type": "integer", "format": "int64", "title": "通过的最大普通关卡"}}}, "xian_cwsxQueryTeamMembersReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsxQueryTeamMembersRsp": {"type": "object", "properties": {"friendList": {"type": "array", "items": {"type": "string"}}}}, "xian_cwsxQueryUserTeamReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsxQueryUserTeamRsp": {"type": "object", "properties": {"teamId": {"type": "string", "title": "战队id,无战队为\"\""}}}, "xian_cwsxReportPlantCoinReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "uid": {"type": "string"}, "seed": {"type": "string"}, "stageId": {"type": "string", "format": "int64"}, "entryTime": {"type": "string", "format": "int64"}, "useStep": {"type": "integer", "format": "int64"}, "submitTime": {"type": "string", "format": "int64"}, "coin": {"type": "string", "format": "int64"}}}, "xian_cwsxReportPlantCoinRsp": {"type": "object"}, "xian_cwsxRewardItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "奖励id"}, "num": {"type": "integer", "format": "int64", "title": "奖励数量"}, "name": {"type": "string", "title": "奖励名称"}, "type": {"$ref": "#/definitions/xian_cwsxRewardItemType", "title": "资产类型"}}}, "xian_cwsxRewardItemType": {"type": "string", "enum": ["Reward_From_GameNormal", "Reward_From_Platform", "Reward_From_GameLimitedTime"], "default": "Reward_From_GameNormal", "title": "- Reward_From_GameNormal: 游戏物品-默认\n - Reward_From_Platform: 平台物品\n - Reward_From_GameLimitedTime: 游戏物品-限时"}, "xian_cwsxSafeCallbackOpReq": {"type": "object", "properties": {"op": {"$ref": "#/definitions/xian_cwsxSafeOp"}, "team": {"$ref": "#/definitions/xian_cwsxTeamChange", "title": "操作信息"}}}, "xian_cwsxSafeCallbackOpRsp": {"type": "object"}, "xian_cwsxSafeCallbackQueryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsxSafeCallbackQueryRsp": {"type": "object", "properties": {"name": {"type": "string", "title": "战队名"}, "describe": {"type": "string", "title": "战队简介"}, "position": {"type": "integer", "format": "int64", "title": "职位 0 队长 其他队员"}, "createAt": {"type": "string", "format": "int64", "title": "创建时间"}, "memberNum": {"type": "integer", "format": "int64", "title": "成员人数-包含队长"}, "teamId": {"type": "string", "title": "战队id"}}}, "xian_cwsxSafeCheckCallbackV2Req": {"type": "object", "properties": {"requestId": {"type": "string"}, "taskId": {"type": "string"}, "dataId": {"type": "string"}, "callback": {"type": "string", "title": "解析CallbackMsg的json字符串"}, "suggestion": {"type": "string"}, "strategyConclusion": {"type": "string"}, "resultType": {"type": "integer", "format": "int64", "title": "结果方式 1:机审结果，2：人审结果, 3：命中审核结果缓存,把人审结果缓存下来，作为下次审核的结果自动返回block或pass。"}}}, "xian_cwsxSafeCheckCallbackV2Rsp": {"type": "object", "properties": {"error_code": {"type": "integer", "format": "int32"}, "error_msg": {"type": "string"}}}, "xian_cwsxSafeOp": {"type": "string", "enum": ["SafeOp_Unknown", "SafeOp_Reset", "SafeOp_Dissolve"], "default": "SafeOp_Unknown", "title": "- SafeOp_Unknown: 未知\n - SafeOp_Reset: 重置\n - SafeOp_Dissolve: 解散"}, "xian_cwsxSuperBollRetentionPopupReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "stageType": {"type": "integer", "format": "int64", "title": "关卡类型"}, "doubleBuff": {"type": "boolean", "title": "双倍buff"}}}, "xian_cwsxSuperBollRetentionPopupRsp": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int64", "title": "GameActivity 活动id"}, "activityType": {"type": "integer", "format": "int64", "title": "GameActivityType 活动类型"}, "activityStatus": {"type": "integer", "format": "int64", "title": "GameActivityStatus 活动状态"}, "currentSchedule": {"type": "integer", "format": "int64", "title": "当前进度值"}, "nextSchedule": {"type": "integer", "format": "int64", "title": "下一阶段值"}, "lostGoodNums": {"type": "integer", "format": "int64", "title": "损失物品数量"}, "NextStageRewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxPopRewardItem"}, "title": "可获得奖励"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}, "xian_cwsxTeamChange": {"type": "object", "properties": {"teamId": {"type": "string", "title": "哪个战队"}, "name": {"type": "string", "title": "名字改啥"}, "avatar": {"type": "string", "title": "头像url改啥"}, "describe": {"type": "string", "title": "描述改啥"}}}, "xian_cwsxTeamInfo": {"type": "object", "properties": {"teamId": {"type": "string", "title": "战队唯一id"}, "setAvatar": {"type": "string", "title": "玩家自己设置图片的url 预制id也是url填入"}, "name": {"type": "string", "title": "战队名字"}, "membersNumber": {"type": "integer", "format": "int64", "title": "人员数量"}, "infiniteItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxInfiniteItem"}, "title": "无限道具及过期时间"}, "activeLevel": {"type": "integer", "format": "int64", "title": "活跃等级"}, "score": {"type": "integer", "format": "int64"}, "describe": {"type": "string"}, "isApply": {"type": "boolean", "title": "是否已申请"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "lowestLevel": {"type": "integer", "format": "int64", "title": "进关条件"}}}, "xian_cwsxTeamsBatchReq": {"type": "object", "properties": {"teamIds": {"type": "array", "items": {"type": "string"}, "title": "team_id的列表"}}}, "xian_cwsxTeamsBatchRsp": {"type": "object", "properties": {"teamsMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/xian_cwsxTeamInfo"}}}}, "xian_cwsxTimerCallbackRequest": {"type": "object", "properties": {"appId": {"type": "string"}, "timerId": {"type": "string"}, "fireTime": {"type": "string", "format": "int64", "title": "触发时间 毫秒"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据"}}}, "xian_cwsxTimerCallbackResponse": {"type": "object", "properties": {"cancel": {"type": "boolean", "title": "用于周期定时器取消"}, "nextFireTime": {"type": "string", "format": "int64", "title": "用于一次性定时器指定下次触发时间"}, "bizData": {"type": "string", "format": "byte", "title": "业务数据 不为 nil 则覆盖"}}}, "xian_cwsxTreasureCard": {"type": "object", "properties": {"treasureCardId": {"type": "integer", "format": "int64", "title": "宝藏卡id"}, "isDecompose": {"type": "boolean", "title": "是否被分"}, "decomposeNum": {"type": "integer", "format": "int64", "title": "分解出多少碎片"}}, "title": "宝藏卡结构"}, "xian_cwsxUserAsset": {"type": "object", "properties": {"assetId": {"type": "string", "format": "int64", "title": "资产 id"}, "assetNum": {"type": "string", "format": "int64", "title": "资产数量"}, "name": {"type": "string", "title": "资产名称"}, "icon": {"type": "string", "title": "资产图标"}}}, "xian_cwsxUserGuidesSubmitReq": {"type": "object", "properties": {"openid": {"type": "string"}, "guideIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "delete": {"type": "boolean", "title": "清空引导记录"}}}, "xian_cwsxUserGuidesSubmitRsp": {"type": "object"}, "xian_cwsxWarOrderCheckOrderReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "price": {"type": "string", "format": "int64", "title": "价格"}, "os": {"$ref": "#/definitions/xian_cwsxOperatingSystem", "title": "系统"}}}, "xian_cwsxWarOrderCheckOrderRsp": {"type": "object"}, "xian_cwsxWarOrderDeliveryReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "productId": {"type": "string", "title": "商品 id"}, "transactionId": {"type": "string", "title": "唯一订单 id"}, "timestamp": {"type": "string", "format": "int64", "title": "发货时间戳"}, "os": {"$ref": "#/definitions/xian_cwsxOperatingSystem", "title": "系统"}}}, "xian_cwsxWarOrderDeliveryRsp": {"type": "object", "properties": {"products": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxWarOrderDeliveryRspProduct"}}}}, "xian_cwsxWarOrderDeliveryRspProduct": {"type": "object", "properties": {"name": {"type": "string"}, "num": {"type": "string", "format": "int64"}}}, "xian_cwsxWarOrderRetentionPopupReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "stageType": {"type": "integer", "format": "int64", "title": "关卡类型"}, "doubleBuff": {"type": "boolean", "title": "双倍buff"}}}, "xian_cwsxWarOrderRetentionPopupRsp": {"type": "object", "properties": {"activityId": {"type": "integer", "format": "int64", "title": "GameActivity 活动id"}, "activityType": {"type": "integer", "format": "int64", "title": "GameActivityType 活动类型"}, "activityStatus": {"type": "integer", "format": "int64", "title": "GameActivityStatus 活动状态"}, "currentSchedule": {"type": "integer", "format": "int64", "title": "当前进度值"}, "nextSchedule": {"type": "integer", "format": "int64", "title": "下一阶段值"}, "lostGoodNums": {"type": "integer", "format": "int64", "title": "损失物品数量"}, "NextStageRewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxPopRewardItem"}, "title": "可获得奖励"}, "mapExt": {"type": "object", "additionalProperties": {"type": "string"}}}}}}