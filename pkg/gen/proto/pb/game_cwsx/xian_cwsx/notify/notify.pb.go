// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/xian_cwsx/notify/notify.proto

package notify

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	inlet "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/inlet"
	privilege "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/privilege"
	game "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/xian_cwsx/game"
	team "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/xian_cwsx/team"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GameDeliveryType int32

const (
	GameDeliveryType_GameDeliveryTypeRedStone     GameDeliveryType = 0 // 游戏红石 https://gm.tmeoa.com/?type=kg_cwsx_activity_gift_prod
	GameDeliveryType_GameDeliveryTypeMiddleConfig GameDeliveryType = 1 // 中台配置 https://game-config.tmeoa.com/sanxiao/asset/props
)

// Enum value maps for GameDeliveryType.
var (
	GameDeliveryType_name = map[int32]string{
		0: "GameDeliveryTypeRedStone",
		1: "GameDeliveryTypeMiddleConfig",
	}
	GameDeliveryType_value = map[string]int32{
		"GameDeliveryTypeRedStone":     0,
		"GameDeliveryTypeMiddleConfig": 1,
	}
)

func (x GameDeliveryType) Enum() *GameDeliveryType {
	p := new(GameDeliveryType)
	*p = x
	return p
}

func (x GameDeliveryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GameDeliveryType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes[0].Descriptor()
}

func (GameDeliveryType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes[0]
}

func (x GameDeliveryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GameDeliveryType.Descriptor instead.
func (GameDeliveryType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{0}
}

type RewardItemType int32

const (
	RewardItemType_Reward_From_GameNormal      RewardItemType = 0 // 游戏物品-默认
	RewardItemType_Reward_From_Platform        RewardItemType = 1 // 平台物品
	RewardItemType_Reward_From_GameLimitedTime RewardItemType = 2 // 游戏物品-限时
)

// Enum value maps for RewardItemType.
var (
	RewardItemType_name = map[int32]string{
		0: "Reward_From_GameNormal",
		1: "Reward_From_Platform",
		2: "Reward_From_GameLimitedTime",
	}
	RewardItemType_value = map[string]int32{
		"Reward_From_GameNormal":      0,
		"Reward_From_Platform":        1,
		"Reward_From_GameLimitedTime": 2,
	}
)

func (x RewardItemType) Enum() *RewardItemType {
	p := new(RewardItemType)
	*p = x
	return p
}

func (x RewardItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes[1].Descriptor()
}

func (RewardItemType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes[1]
}

func (x RewardItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardItemType.Descriptor instead.
func (RewardItemType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{1}
}

type SafeOp int32

const (
	SafeOp_SafeOp_Unknown  SafeOp = 0 // 未知
	SafeOp_SafeOp_Reset    SafeOp = 1 // 重置
	SafeOp_SafeOp_Dissolve SafeOp = 2 // 解散
)

// Enum value maps for SafeOp.
var (
	SafeOp_name = map[int32]string{
		0: "SafeOp_Unknown",
		1: "SafeOp_Reset",
		2: "SafeOp_Dissolve",
	}
	SafeOp_value = map[string]int32{
		"SafeOp_Unknown":  0,
		"SafeOp_Reset":    1,
		"SafeOp_Dissolve": 2,
	}
)

func (x SafeOp) Enum() *SafeOp {
	p := new(SafeOp)
	*p = x
	return p
}

func (x SafeOp) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SafeOp) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes[2].Descriptor()
}

func (SafeOp) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes[2]
}

func (x SafeOp) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SafeOp.Descriptor instead.
func (SafeOp) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{2}
}

type TimerCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	TimerId  string `protobuf:"bytes,2,opt,name=timerId,proto3" json:"timerId,omitempty"`
	FireTime int64  `protobuf:"varint,3,opt,name=fireTime,proto3" json:"fireTime,omitempty"` // 触发时间 毫秒
	BizData  []byte `protobuf:"bytes,4,opt,name=bizData,proto3" json:"bizData,omitempty"`    // 业务数据
}

func (x *TimerCallbackRequest) Reset() {
	*x = TimerCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerCallbackRequest) ProtoMessage() {}

func (x *TimerCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerCallbackRequest.ProtoReflect.Descriptor instead.
func (*TimerCallbackRequest) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{0}
}

func (x *TimerCallbackRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *TimerCallbackRequest) GetTimerId() string {
	if x != nil {
		return x.TimerId
	}
	return ""
}

func (x *TimerCallbackRequest) GetFireTime() int64 {
	if x != nil {
		return x.FireTime
	}
	return 0
}

func (x *TimerCallbackRequest) GetBizData() []byte {
	if x != nil {
		return x.BizData
	}
	return nil
}

type TimerCallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cancel       bool   `protobuf:"varint,1,opt,name=cancel,proto3" json:"cancel,omitempty"`             // 用于周期定时器取消
	NextFireTime int64  `protobuf:"varint,2,opt,name=nextFireTime,proto3" json:"nextFireTime,omitempty"` // 用于一次性定时器指定下次触发时间
	BizData      []byte `protobuf:"bytes,3,opt,name=bizData,proto3" json:"bizData,omitempty"`            // 业务数据 不为 nil 则覆盖
}

func (x *TimerCallbackResponse) Reset() {
	*x = TimerCallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimerCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimerCallbackResponse) ProtoMessage() {}

func (x *TimerCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimerCallbackResponse.ProtoReflect.Descriptor instead.
func (*TimerCallbackResponse) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{1}
}

func (x *TimerCallbackResponse) GetCancel() bool {
	if x != nil {
		return x.Cancel
	}
	return false
}

func (x *TimerCallbackResponse) GetNextFireTime() int64 {
	if x != nil {
		return x.NextFireTime
	}
	return 0
}

func (x *TimerCallbackResponse) GetBizData() []byte {
	if x != nil {
		return x.BizData
	}
	return nil
}

type TeamsBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamIds []string `protobuf:"bytes,1,rep,name=team_ids,json=teamIds,proto3" json:"team_ids,omitempty"` // team_id的列表
}

func (x *TeamsBatchReq) Reset() {
	*x = TeamsBatchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamsBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamsBatchReq) ProtoMessage() {}

func (x *TeamsBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamsBatchReq.ProtoReflect.Descriptor instead.
func (*TeamsBatchReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{2}
}

func (x *TeamsBatchReq) GetTeamIds() []string {
	if x != nil {
		return x.TeamIds
	}
	return nil
}

type TeamsBatchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamsMap map[string]*team.TeamInfo `protobuf:"bytes,1,rep,name=teams_map,json=teamsMap,proto3" json:"teams_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TeamsBatchRsp) Reset() {
	*x = TeamsBatchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamsBatchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamsBatchRsp) ProtoMessage() {}

func (x *TeamsBatchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamsBatchRsp.ProtoReflect.Descriptor instead.
func (*TeamsBatchRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{3}
}

func (x *TeamsBatchRsp) GetTeamsMap() map[string]*team.TeamInfo {
	if x != nil {
		return x.TeamsMap
	}
	return nil
}

type CheckGMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Openid string `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
}

func (x *CheckGMReq) Reset() {
	*x = CheckGMReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckGMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGMReq) ProtoMessage() {}

func (x *CheckGMReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGMReq.ProtoReflect.Descriptor instead.
func (*CheckGMReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{4}
}

func (x *CheckGMReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckGMReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

type CheckGMRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Res bool `protobuf:"varint,1,opt,name=res,proto3" json:"res,omitempty"`
}

func (x *CheckGMRsp) Reset() {
	*x = CheckGMRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckGMRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckGMRsp) ProtoMessage() {}

func (x *CheckGMRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckGMRsp.ProtoReflect.Descriptor instead.
func (*CheckGMRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{5}
}

func (x *CheckGMRsp) GetRes() bool {
	if x != nil {
		return x.Res
	}
	return false
}

type LabaUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid string `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"` // 玩家uid
}

func (x *LabaUserInfoReq) Reset() {
	*x = LabaUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabaUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabaUserInfoReq) ProtoMessage() {}

func (x *LabaUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabaUserInfoReq.ProtoReflect.Descriptor instead.
func (*LabaUserInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{6}
}

func (x *LabaUserInfoReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

type LabaUserInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuideList []uint32 `protobuf:"varint,1,rep,packed,name=guide_list,json=guideList,proto3" json:"guide_list,omitempty"` // 玩家完成的引导的list
	MaxFloor  uint32   `protobuf:"varint,2,opt,name=max_floor,json=maxFloor,proto3" json:"max_floor,omitempty"`           // 最大关卡数
}

func (x *LabaUserInfoRsp) Reset() {
	*x = LabaUserInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabaUserInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabaUserInfoRsp) ProtoMessage() {}

func (x *LabaUserInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabaUserInfoRsp.ProtoReflect.Descriptor instead.
func (*LabaUserInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{7}
}

func (x *LabaUserInfoRsp) GetGuideList() []uint32 {
	if x != nil {
		return x.GuideList
	}
	return nil
}

func (x *LabaUserInfoRsp) GetMaxFloor() uint32 {
	if x != nil {
		return x.MaxFloor
	}
	return 0
}

type UserGuidesSubmitReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid   string   `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	GuideIds []uint32 `protobuf:"varint,2,rep,packed,name=guide_ids,json=guideIds,proto3" json:"guide_ids,omitempty"`
	Delete   bool     `protobuf:"varint,3,opt,name=delete,proto3" json:"delete,omitempty"` //清空引导记录
}

func (x *UserGuidesSubmitReq) Reset() {
	*x = UserGuidesSubmitReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserGuidesSubmitReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuidesSubmitReq) ProtoMessage() {}

func (x *UserGuidesSubmitReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuidesSubmitReq.ProtoReflect.Descriptor instead.
func (*UserGuidesSubmitReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{8}
}

func (x *UserGuidesSubmitReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *UserGuidesSubmitReq) GetGuideIds() []uint32 {
	if x != nil {
		return x.GuideIds
	}
	return nil
}

func (x *UserGuidesSubmitReq) GetDelete() bool {
	if x != nil {
		return x.Delete
	}
	return false
}

type UserGuidesSubmitRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UserGuidesSubmitRsp) Reset() {
	*x = UserGuidesSubmitRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserGuidesSubmitRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuidesSubmitRsp) ProtoMessage() {}

func (x *UserGuidesSubmitRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuidesSubmitRsp.ProtoReflect.Descriptor instead.
func (*UserGuidesSubmitRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{9}
}

type CreateTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PredictAvatar int32  `protobuf:"varint,1,opt,name=predict_avatar,json=predictAvatar,proto3" json:"predict_avatar,omitempty"` // 选择了预制头像id -- 读红石
	SetAvatar     string `protobuf:"bytes,2,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`              // 玩家自己设置图片的url 设置了2则不判断1
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                         // 战队名字
	Describe      string `protobuf:"bytes,4,opt,name=describe,proto3" json:"describe,omitempty"`                                 // 战队描述
	IsAuditFloor  bool   `protobuf:"varint,5,opt,name=is_audit_floor,json=isAuditFloor,proto3" json:"is_audit_floor,omitempty"`  // 加入是否需要审核关卡过关情况
	UnlockFloor   int32  `protobuf:"varint,6,opt,name=unlock_floor,json=unlockFloor,proto3" json:"unlock_floor,omitempty"`       // 加入关卡必须打过的关卡
}

func (x *CreateTeamReq) Reset() {
	*x = CreateTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTeamReq) ProtoMessage() {}

func (x *CreateTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTeamReq.ProtoReflect.Descriptor instead.
func (*CreateTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{10}
}

func (x *CreateTeamReq) GetPredictAvatar() int32 {
	if x != nil {
		return x.PredictAvatar
	}
	return 0
}

func (x *CreateTeamReq) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *CreateTeamReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTeamReq) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *CreateTeamReq) GetIsAuditFloor() bool {
	if x != nil {
		return x.IsAuditFloor
	}
	return false
}

func (x *CreateTeamReq) GetUnlockFloor() int32 {
	if x != nil {
		return x.UnlockFloor
	}
	return 0
}

type CreateTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateTeamRsp) Reset() {
	*x = CreateTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTeamRsp) ProtoMessage() {}

func (x *CreateTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTeamRsp.ProtoReflect.Descriptor instead.
func (*CreateTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{11}
}

type CheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string               `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId    string               `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId string               `protobuf:"bytes,3,opt,name=productId,proto3" json:"productId,omitempty"`                   // 商品 id
	Price     int64                `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                          // 价格
	GreenId   int64                `protobuf:"varint,5,opt,name=greenId,proto3" json:"greenId,omitempty"`                      // 绿钻 id
	Os        game.OperatingSystem `protobuf:"varint,6,opt,name=os,proto3,enum=xian_cwsx.OperatingSystem" json:"os,omitempty"` // 系统
}

func (x *CheckOrderReq) Reset() {
	*x = CheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderReq) ProtoMessage() {}

func (x *CheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderReq.ProtoReflect.Descriptor instead.
func (*CheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{12}
}

func (x *CheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CheckOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CheckOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CheckOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CheckOrderReq) GetGreenId() int64 {
	if x != nil {
		return x.GreenId
	}
	return 0
}

func (x *CheckOrderReq) GetOs() game.OperatingSystem {
	if x != nil {
		return x.Os
	}
	return game.OperatingSystem(0)
}

type CheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CheckOrderRsp) Reset() {
	*x = CheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOrderRsp) ProtoMessage() {}

func (x *CheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOrderRsp.ProtoReflect.Descriptor instead.
func (*CheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{13}
}

type DeliveryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId     string `protobuf:"bytes,3,opt,name=productId,proto3" json:"productId,omitempty"`         // 商品 id
	TransactionId string `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"` // 唯一订单 id
	Timestamp     int64  `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`        // 发货时间戳
}

func (x *DeliveryReq) Reset() {
	*x = DeliveryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryReq) ProtoMessage() {}

func (x *DeliveryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryReq.ProtoReflect.Descriptor instead.
func (*DeliveryReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{14}
}

func (x *DeliveryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DeliveryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DeliveryReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *DeliveryReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *DeliveryReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type DeliveryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Products []*DeliveryRsp_Product `protobuf:"bytes,1,rep,name=products,proto3" json:"products,omitempty"`
}

func (x *DeliveryRsp) Reset() {
	*x = DeliveryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryRsp) ProtoMessage() {}

func (x *DeliveryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryRsp.ProtoReflect.Descriptor instead.
func (*DeliveryRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{15}
}

func (x *DeliveryRsp) GetProducts() []*DeliveryRsp_Product {
	if x != nil {
		return x.Products
	}
	return nil
}

type Package struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId   string        `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`       // 礼包id
	PackageName string        `protobuf:"bytes,2,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"` // 礼包名称
	Rewards     []*RewardItem `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`                            // 奖励
	Price       int64         `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                               // 礼包价格
}

func (x *Package) Reset() {
	*x = Package{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Package) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Package) ProtoMessage() {}

func (x *Package) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Package.ProtoReflect.Descriptor instead.
func (*Package) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{16}
}

func (x *Package) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *Package) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *Package) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *Package) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type BatchQueryCwsxPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	PackageIds []string `protobuf:"bytes,3,rep,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"` // 礼包ids
}

func (x *BatchQueryCwsxPackageReq) Reset() {
	*x = BatchQueryCwsxPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQueryCwsxPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryCwsxPackageReq) ProtoMessage() {}

func (x *BatchQueryCwsxPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryCwsxPackageReq.ProtoReflect.Descriptor instead.
func (*BatchQueryCwsxPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{17}
}

func (x *BatchQueryCwsxPackageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchQueryCwsxPackageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchQueryCwsxPackageReq) GetPackageIds() []string {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

type BatchQueryCwsxPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Packages map[string]*Package `protobuf:"bytes,1,rep,name=packages,proto3" json:"packages,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 礼包
}

func (x *BatchQueryCwsxPackageRsp) Reset() {
	*x = BatchQueryCwsxPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQueryCwsxPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryCwsxPackageRsp) ProtoMessage() {}

func (x *BatchQueryCwsxPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryCwsxPackageRsp.ProtoReflect.Descriptor instead.
func (*BatchQueryCwsxPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{18}
}

func (x *BatchQueryCwsxPackageRsp) GetPackages() map[string]*Package {
	if x != nil {
		return x.Packages
	}
	return nil
}

type QueryStageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // 请求用户
}

func (x *QueryStageReq) Reset() {
	*x = QueryStageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStageReq) ProtoMessage() {}

func (x *QueryStageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStageReq.ProtoReflect.Descriptor instead.
func (*QueryStageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{19}
}

func (x *QueryStageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryStageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QueryStageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurStage       uint32 `protobuf:"varint,1,opt,name=cur_stage,json=curStage,proto3" json:"cur_stage,omitempty"`                     // 玩家当前成功闯关数
	MaxNormalFloor uint32 `protobuf:"varint,2,opt,name=max_normal_floor,json=maxNormalFloor,proto3" json:"max_normal_floor,omitempty"` // 通过的最大普通关卡
}

func (x *QueryStageRsp) Reset() {
	*x = QueryStageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryStageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStageRsp) ProtoMessage() {}

func (x *QueryStageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStageRsp.ProtoReflect.Descriptor instead.
func (*QueryStageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{20}
}

func (x *QueryStageRsp) GetCurStage() uint32 {
	if x != nil {
		return x.CurStage
	}
	return 0
}

func (x *QueryStageRsp) GetMaxNormalFloor() uint32 {
	if x != nil {
		return x.MaxNormalFloor
	}
	return 0
}

type GameDeliveryPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string           `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId        string           `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageId     string           `protobuf:"bytes,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`             // 礼包 id
	TransactionId string           `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // 唯一订单 id 发中台资产
	Timestamp     int64            `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 发货时间戳
	Num           uint32           `protobuf:"varint,6,opt,name=num,proto3" json:"num,omitempty"`                                         // 发几个礼包,默认一个
	Type          GameDeliveryType `protobuf:"varint,7,opt,name=type,proto3,enum=xian_cwsx.GameDeliveryType" json:"type,omitempty"`       // 发奖类型
	Reason        string           `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`                                    //发奖场景
}

func (x *GameDeliveryPackageReq) Reset() {
	*x = GameDeliveryPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageReq) ProtoMessage() {}

func (x *GameDeliveryPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageReq.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{21}
}

func (x *GameDeliveryPackageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *GameDeliveryPackageReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GameDeliveryPackageReq) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *GameDeliveryPackageReq) GetType() GameDeliveryType {
	if x != nil {
		return x.Type
	}
	return GameDeliveryType_GameDeliveryTypeRedStone
}

func (x *GameDeliveryPackageReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type RewardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint32         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                   // 奖励id
	Num  uint32         `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`                                 // 奖励数量
	Name string         `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                // 奖励名称
	Type RewardItemType `protobuf:"varint,4,opt,name=type,proto3,enum=xian_cwsx.RewardItemType" json:"type,omitempty"` // 资产类型
}

func (x *RewardItem) Reset() {
	*x = RewardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardItem) ProtoMessage() {}

func (x *RewardItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardItem.ProtoReflect.Descriptor instead.
func (*RewardItem) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{22}
}

func (x *RewardItem) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RewardItem) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *RewardItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RewardItem) GetType() RewardItemType {
	if x != nil {
		return x.Type
	}
	return RewardItemType_Reward_From_GameNormal
}

type GameDeliveryPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*RewardItem        `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"` // 奖励
	Cards   []*game.TreasureCard `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"`     // 如果发奖产生宝藏奖励这个字段不为空
}

func (x *GameDeliveryPackageRsp) Reset() {
	*x = GameDeliveryPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageRsp) ProtoMessage() {}

func (x *GameDeliveryPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageRsp.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{23}
}

func (x *GameDeliveryPackageRsp) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *GameDeliveryPackageRsp) GetCards() []*game.TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

type BatchGameDeliveryPackageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId        string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageIds    []string `protobuf:"bytes,3,rep,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"`          // 礼包 id
	TransactionId string   `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // 唯一订单 id
	Timestamp     int64    `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 发货时间戳
}

func (x *BatchGameDeliveryPackageReq) Reset() {
	*x = BatchGameDeliveryPackageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageReq) ProtoMessage() {}

func (x *BatchGameDeliveryPackageReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageReq.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{24}
}

func (x *BatchGameDeliveryPackageReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGameDeliveryPackageReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchGameDeliveryPackageReq) GetPackageIds() []string {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

func (x *BatchGameDeliveryPackageReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BatchGameDeliveryPackageReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type BatchGameDeliveryPackageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*RewardItem        `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"` // 奖励
	Cards   []*game.TreasureCard `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"`     // 如果发奖产生宝藏奖励这个字段不为空
}

func (x *BatchGameDeliveryPackageRsp) Reset() {
	*x = BatchGameDeliveryPackageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageRsp) ProtoMessage() {}

func (x *BatchGameDeliveryPackageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageRsp.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{25}
}

func (x *BatchGameDeliveryPackageRsp) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *BatchGameDeliveryPackageRsp) GetCards() []*game.TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

type BatchGameDeliveryPackageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId        string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	PackageIds    []string `protobuf:"bytes,3,rep,name=package_ids,json=packageIds,proto3" json:"package_ids,omitempty"`          // 礼包 id
	TransactionId string   `protobuf:"bytes,4,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"` // 唯一订单 id
	Timestamp     int64    `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 发货时间戳
}

func (x *BatchGameDeliveryPackageListReq) Reset() {
	*x = BatchGameDeliveryPackageListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageListReq) ProtoMessage() {}

func (x *BatchGameDeliveryPackageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageListReq.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{26}
}

func (x *BatchGameDeliveryPackageListReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGameDeliveryPackageListReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *BatchGameDeliveryPackageListReq) GetPackageIds() []string {
	if x != nil {
		return x.PackageIds
	}
	return nil
}

func (x *BatchGameDeliveryPackageListReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *BatchGameDeliveryPackageListReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type GameDeliveryPackageRewards struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId string               `protobuf:"bytes,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"` // 礼包id
	Rewards   []*RewardItem        `protobuf:"bytes,2,rep,name=rewards,proto3" json:"rewards,omitempty"`                      // 奖励
	Cards     []*game.TreasureCard `protobuf:"bytes,3,rep,name=cards,proto3" json:"cards,omitempty"`                          // 如果发奖产生宝藏奖励这个字段不为空
}

func (x *GameDeliveryPackageRewards) Reset() {
	*x = GameDeliveryPackageRewards{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameDeliveryPackageRewards) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameDeliveryPackageRewards) ProtoMessage() {}

func (x *GameDeliveryPackageRewards) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameDeliveryPackageRewards.ProtoReflect.Descriptor instead.
func (*GameDeliveryPackageRewards) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{27}
}

func (x *GameDeliveryPackageRewards) GetPackageId() string {
	if x != nil {
		return x.PackageId
	}
	return ""
}

func (x *GameDeliveryPackageRewards) GetRewards() []*RewardItem {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *GameDeliveryPackageRewards) GetCards() []*game.TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

type BatchGameDeliveryPackageListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*GameDeliveryPackageRewards `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"`
}

func (x *BatchGameDeliveryPackageListRsp) Reset() {
	*x = BatchGameDeliveryPackageListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGameDeliveryPackageListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGameDeliveryPackageListRsp) ProtoMessage() {}

func (x *BatchGameDeliveryPackageListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGameDeliveryPackageListRsp.ProtoReflect.Descriptor instead.
func (*BatchGameDeliveryPackageListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{28}
}

func (x *BatchGameDeliveryPackageListRsp) GetRewards() []*GameDeliveryPackageRewards {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type WarOrderRetentionPopupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	StageType  uint32 `protobuf:"varint,3,opt,name=stageType,proto3" json:"stageType,omitempty"`   // 关卡类型
	DoubleBuff bool   `protobuf:"varint,4,opt,name=doubleBuff,proto3" json:"doubleBuff,omitempty"` // 双倍buff
}

func (x *WarOrderRetentionPopupReq) Reset() {
	*x = WarOrderRetentionPopupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderRetentionPopupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderRetentionPopupReq) ProtoMessage() {}

func (x *WarOrderRetentionPopupReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderRetentionPopupReq.ProtoReflect.Descriptor instead.
func (*WarOrderRetentionPopupReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{29}
}

func (x *WarOrderRetentionPopupReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *WarOrderRetentionPopupReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WarOrderRetentionPopupReq) GetStageType() uint32 {
	if x != nil {
		return x.StageType
	}
	return 0
}

func (x *WarOrderRetentionPopupReq) GetDoubleBuff() bool {
	if x != nil {
		return x.DoubleBuff
	}
	return false
}

type WarOrderRetentionPopupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId       uint32                `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`            // GameActivity 活动id
	ActivityType     uint32                `protobuf:"varint,2,opt,name=activityType,proto3" json:"activityType,omitempty"`        // GameActivityType 活动类型
	ActivityStatus   uint32                `protobuf:"varint,3,opt,name=activityStatus,proto3" json:"activityStatus,omitempty"`    // GameActivityStatus 活动状态
	CurrentSchedule  uint32                `protobuf:"varint,4,opt,name=currentSchedule,proto3" json:"currentSchedule,omitempty"`  // 当前进度值
	NextSchedule     uint32                `protobuf:"varint,5,opt,name=nextSchedule,proto3" json:"nextSchedule,omitempty"`        // 下一阶段值
	LostGoodNums     uint32                `protobuf:"varint,6,opt,name=lostGoodNums,proto3" json:"lostGoodNums,omitempty"`        // 损失物品数量
	NextStageRewards []*game.PopRewardItem `protobuf:"bytes,7,rep,name=NextStageRewards,proto3" json:"NextStageRewards,omitempty"` // 可获得奖励
	MapExt           map[string]string     `protobuf:"bytes,15,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WarOrderRetentionPopupRsp) Reset() {
	*x = WarOrderRetentionPopupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderRetentionPopupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderRetentionPopupRsp) ProtoMessage() {}

func (x *WarOrderRetentionPopupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderRetentionPopupRsp.ProtoReflect.Descriptor instead.
func (*WarOrderRetentionPopupRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{30}
}

func (x *WarOrderRetentionPopupRsp) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *WarOrderRetentionPopupRsp) GetActivityType() uint32 {
	if x != nil {
		return x.ActivityType
	}
	return 0
}

func (x *WarOrderRetentionPopupRsp) GetActivityStatus() uint32 {
	if x != nil {
		return x.ActivityStatus
	}
	return 0
}

func (x *WarOrderRetentionPopupRsp) GetCurrentSchedule() uint32 {
	if x != nil {
		return x.CurrentSchedule
	}
	return 0
}

func (x *WarOrderRetentionPopupRsp) GetNextSchedule() uint32 {
	if x != nil {
		return x.NextSchedule
	}
	return 0
}

func (x *WarOrderRetentionPopupRsp) GetLostGoodNums() uint32 {
	if x != nil {
		return x.LostGoodNums
	}
	return 0
}

func (x *WarOrderRetentionPopupRsp) GetNextStageRewards() []*game.PopRewardItem {
	if x != nil {
		return x.NextStageRewards
	}
	return nil
}

func (x *WarOrderRetentionPopupRsp) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type WarOrderCheckOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string               `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId    string               `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId string               `protobuf:"bytes,3,opt,name=productId,proto3" json:"productId,omitempty"`                   // 商品 id
	Price     int64                `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`                          // 价格
	Os        game.OperatingSystem `protobuf:"varint,6,opt,name=os,proto3,enum=xian_cwsx.OperatingSystem" json:"os,omitempty"` // 系统
}

func (x *WarOrderCheckOrderReq) Reset() {
	*x = WarOrderCheckOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderCheckOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderCheckOrderReq) ProtoMessage() {}

func (x *WarOrderCheckOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderCheckOrderReq.ProtoReflect.Descriptor instead.
func (*WarOrderCheckOrderReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{31}
}

func (x *WarOrderCheckOrderReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *WarOrderCheckOrderReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WarOrderCheckOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *WarOrderCheckOrderReq) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *WarOrderCheckOrderReq) GetOs() game.OperatingSystem {
	if x != nil {
		return x.Os
	}
	return game.OperatingSystem(0)
}

type WarOrderCheckOrderRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WarOrderCheckOrderRsp) Reset() {
	*x = WarOrderCheckOrderRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderCheckOrderRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderCheckOrderRsp) ProtoMessage() {}

func (x *WarOrderCheckOrderRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderCheckOrderRsp.ProtoReflect.Descriptor instead.
func (*WarOrderCheckOrderRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{32}
}

type WarOrderDeliveryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId         string               `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId        string               `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	ProductId     string               `protobuf:"bytes,3,opt,name=productId,proto3" json:"productId,omitempty"`                   // 商品 id
	TransactionId string               `protobuf:"bytes,4,opt,name=transactionId,proto3" json:"transactionId,omitempty"`           // 唯一订单 id
	Timestamp     int64                `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                  // 发货时间戳
	Os            game.OperatingSystem `protobuf:"varint,6,opt,name=os,proto3,enum=xian_cwsx.OperatingSystem" json:"os,omitempty"` // 系统
}

func (x *WarOrderDeliveryReq) Reset() {
	*x = WarOrderDeliveryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderDeliveryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderDeliveryReq) ProtoMessage() {}

func (x *WarOrderDeliveryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderDeliveryReq.ProtoReflect.Descriptor instead.
func (*WarOrderDeliveryReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{33}
}

func (x *WarOrderDeliveryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *WarOrderDeliveryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *WarOrderDeliveryReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *WarOrderDeliveryReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *WarOrderDeliveryReq) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *WarOrderDeliveryReq) GetOs() game.OperatingSystem {
	if x != nil {
		return x.Os
	}
	return game.OperatingSystem(0)
}

type WarOrderDeliveryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Products []*WarOrderDeliveryRsp_Product `protobuf:"bytes,1,rep,name=products,proto3" json:"products,omitempty"`
}

func (x *WarOrderDeliveryRsp) Reset() {
	*x = WarOrderDeliveryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderDeliveryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderDeliveryRsp) ProtoMessage() {}

func (x *WarOrderDeliveryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderDeliveryRsp.ProtoReflect.Descriptor instead.
func (*WarOrderDeliveryRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{34}
}

func (x *WarOrderDeliveryRsp) GetProducts() []*WarOrderDeliveryRsp_Product {
	if x != nil {
		return x.Products
	}
	return nil
}

type SafeCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomData []byte `protobuf:"bytes,1,opt,name=customData,proto3" json:"customData,omitempty"` // 业务自定义回调透传数据
}

func (x *SafeCallbackReq) Reset() {
	*x = SafeCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCallbackReq) ProtoMessage() {}

func (x *SafeCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCallbackReq.ProtoReflect.Descriptor instead.
func (*SafeCallbackReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{35}
}

func (x *SafeCallbackReq) GetCustomData() []byte {
	if x != nil {
		return x.CustomData
	}
	return nil
}

type SafeCallbackRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SafeCallbackRsp) Reset() {
	*x = SafeCallbackRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCallbackRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCallbackRsp) ProtoMessage() {}

func (x *SafeCallbackRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCallbackRsp.ProtoReflect.Descriptor instead.
func (*SafeCallbackRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{36}
}

type CallbackInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	TeamId string `protobuf:"bytes,3,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 即使玩家不在这个战队了，也得改
}

func (x *CallbackInfo) Reset() {
	*x = CallbackInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackInfo) ProtoMessage() {}

func (x *CallbackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackInfo.ProtoReflect.Descriptor instead.
func (*CallbackInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{37}
}

func (x *CallbackInfo) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CallbackInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CallbackInfo) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

type CallbackMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CallbackService string `protobuf:"bytes,1,opt,name=callbackService,proto3" json:"callbackService,omitempty"` // 业务自定义回调路由, eg: kg.game.DemoServer/callback
	CustomData      []byte `protobuf:"bytes,2,opt,name=customData,proto3" json:"customData,omitempty"`           // 业务自定义回调透传数据
}

func (x *CallbackMsg) Reset() {
	*x = CallbackMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackMsg) ProtoMessage() {}

func (x *CallbackMsg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackMsg.ProtoReflect.Descriptor instead.
func (*CallbackMsg) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{38}
}

func (x *CallbackMsg) GetCallbackService() string {
	if x != nil {
		return x.CallbackService
	}
	return ""
}

func (x *CallbackMsg) GetCustomData() []byte {
	if x != nil {
		return x.CustomData
	}
	return nil
}

type SafeCheckCallbackV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId          string `protobuf:"bytes,1,opt,name=requestId,proto3" json:"requestId,omitempty"`
	TaskId             string `protobuf:"bytes,2,opt,name=taskId,proto3" json:"taskId,omitempty"`
	DataId             string `protobuf:"bytes,3,opt,name=dataId,proto3" json:"dataId,omitempty"`
	Callback           string `protobuf:"bytes,4,opt,name=callback,proto3" json:"callback,omitempty"` // 解析CallbackMsg的json字符串
	Suggestion         string `protobuf:"bytes,5,opt,name=suggestion,proto3" json:"suggestion,omitempty"`
	StrategyConclusion string `protobuf:"bytes,6,opt,name=strategyConclusion,proto3" json:"strategyConclusion,omitempty"`
	ResultType         uint32 `protobuf:"varint,7,opt,name=resultType,proto3" json:"resultType,omitempty"` // 结果方式 1:机审结果，2：人审结果, 3：命中审核结果缓存,把人审结果缓存下来，作为下次审核的结果自动返回block或pass。
}

func (x *SafeCheckCallbackV2Req) Reset() {
	*x = SafeCheckCallbackV2Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCheckCallbackV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCheckCallbackV2Req) ProtoMessage() {}

func (x *SafeCheckCallbackV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCheckCallbackV2Req.ProtoReflect.Descriptor instead.
func (*SafeCheckCallbackV2Req) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{39}
}

func (x *SafeCheckCallbackV2Req) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *SafeCheckCallbackV2Req) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SafeCheckCallbackV2Req) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *SafeCheckCallbackV2Req) GetCallback() string {
	if x != nil {
		return x.Callback
	}
	return ""
}

func (x *SafeCheckCallbackV2Req) GetSuggestion() string {
	if x != nil {
		return x.Suggestion
	}
	return ""
}

func (x *SafeCheckCallbackV2Req) GetStrategyConclusion() string {
	if x != nil {
		return x.StrategyConclusion
	}
	return ""
}

func (x *SafeCheckCallbackV2Req) GetResultType() uint32 {
	if x != nil {
		return x.ResultType
	}
	return 0
}

type SafeCheckCallbackV2Rsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode int32  `protobuf:"varint,1,opt,name=error_code,proto3" json:"error_code,omitempty"`
	ErrorMsg  string `protobuf:"bytes,2,opt,name=error_msg,proto3" json:"error_msg,omitempty"`
}

func (x *SafeCheckCallbackV2Rsp) Reset() {
	*x = SafeCheckCallbackV2Rsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCheckCallbackV2Rsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCheckCallbackV2Rsp) ProtoMessage() {}

func (x *SafeCheckCallbackV2Rsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCheckCallbackV2Rsp.ProtoReflect.Descriptor instead.
func (*SafeCheckCallbackV2Rsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{40}
}

func (x *SafeCheckCallbackV2Rsp) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *SafeCheckCallbackV2Rsp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type TeamChange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId   string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 哪个战队
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                   // 名字改啥
	Avatar   string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 头像url改啥
	Describe string `protobuf:"bytes,4,opt,name=describe,proto3" json:"describe,omitempty"`           // 描述改啥
}

func (x *TeamChange) Reset() {
	*x = TeamChange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamChange) ProtoMessage() {}

func (x *TeamChange) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamChange.ProtoReflect.Descriptor instead.
func (*TeamChange) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{41}
}

func (x *TeamChange) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *TeamChange) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamChange) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamChange) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

type SafeCallbackOpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Op   SafeOp      `protobuf:"varint,1,opt,name=op,proto3,enum=xian_cwsx.SafeOp" json:"op,omitempty"`
	Team *TeamChange `protobuf:"bytes,2,opt,name=team,proto3" json:"team,omitempty"` // 操作信息
}

func (x *SafeCallbackOpReq) Reset() {
	*x = SafeCallbackOpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCallbackOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCallbackOpReq) ProtoMessage() {}

func (x *SafeCallbackOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCallbackOpReq.ProtoReflect.Descriptor instead.
func (*SafeCallbackOpReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{42}
}

func (x *SafeCallbackOpReq) GetOp() SafeOp {
	if x != nil {
		return x.Op
	}
	return SafeOp_SafeOp_Unknown
}

func (x *SafeCallbackOpReq) GetTeam() *TeamChange {
	if x != nil {
		return x.Team
	}
	return nil
}

type SafeCallbackOpRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SafeCallbackOpRsp) Reset() {
	*x = SafeCallbackOpRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCallbackOpRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCallbackOpRsp) ProtoMessage() {}

func (x *SafeCallbackOpRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCallbackOpRsp.ProtoReflect.Descriptor instead.
func (*SafeCallbackOpRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{43}
}

type SafeCallbackQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
}

func (x *SafeCallbackQueryReq) Reset() {
	*x = SafeCallbackQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCallbackQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCallbackQueryReq) ProtoMessage() {}

func (x *SafeCallbackQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCallbackQueryReq.ProtoReflect.Descriptor instead.
func (*SafeCallbackQueryReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{44}
}

func (x *SafeCallbackQueryReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SafeCallbackQueryReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type SafeCallbackQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                             // 战队名
	Describe  string `protobuf:"bytes,2,opt,name=describe,proto3" json:"describe,omitempty"`                     // 战队简介
	Position  uint32 `protobuf:"varint,3,opt,name=position,proto3" json:"position,omitempty"`                    // 职位 0 队长 其他队员
	CreateAt  int64  `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`    // 创建时间
	MemberNum uint32 `protobuf:"varint,5,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"` // 成员人数-包含队长
	TeamId    string `protobuf:"bytes,6,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`           // 战队id
}

func (x *SafeCallbackQueryRsp) Reset() {
	*x = SafeCallbackQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SafeCallbackQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SafeCallbackQueryRsp) ProtoMessage() {}

func (x *SafeCallbackQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SafeCallbackQueryRsp.ProtoReflect.Descriptor instead.
func (*SafeCallbackQueryRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{45}
}

func (x *SafeCallbackQueryRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SafeCallbackQueryRsp) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *SafeCallbackQueryRsp) GetPosition() uint32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *SafeCallbackQueryRsp) GetCreateAt() int64 {
	if x != nil {
		return x.CreateAt
	}
	return 0
}

func (x *SafeCallbackQueryRsp) GetMemberNum() uint32 {
	if x != nil {
		return x.MemberNum
	}
	return 0
}

func (x *SafeCallbackQueryRsp) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

type SuperBollRetentionPopupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	StageType  uint32 `protobuf:"varint,3,opt,name=stageType,proto3" json:"stageType,omitempty"`   // 关卡类型
	DoubleBuff bool   `protobuf:"varint,4,opt,name=doubleBuff,proto3" json:"doubleBuff,omitempty"` // 双倍buff
}

func (x *SuperBollRetentionPopupReq) Reset() {
	*x = SuperBollRetentionPopupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuperBollRetentionPopupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuperBollRetentionPopupReq) ProtoMessage() {}

func (x *SuperBollRetentionPopupReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuperBollRetentionPopupReq.ProtoReflect.Descriptor instead.
func (*SuperBollRetentionPopupReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{46}
}

func (x *SuperBollRetentionPopupReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *SuperBollRetentionPopupReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *SuperBollRetentionPopupReq) GetStageType() uint32 {
	if x != nil {
		return x.StageType
	}
	return 0
}

func (x *SuperBollRetentionPopupReq) GetDoubleBuff() bool {
	if x != nil {
		return x.DoubleBuff
	}
	return false
}

type SuperBollRetentionPopupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId       uint32                `protobuf:"varint,1,opt,name=activityId,proto3" json:"activityId,omitempty"`            // GameActivity 活动id
	ActivityType     uint32                `protobuf:"varint,2,opt,name=activityType,proto3" json:"activityType,omitempty"`        // GameActivityType 活动类型
	ActivityStatus   uint32                `protobuf:"varint,3,opt,name=activityStatus,proto3" json:"activityStatus,omitempty"`    // GameActivityStatus 活动状态
	CurrentSchedule  uint32                `protobuf:"varint,4,opt,name=currentSchedule,proto3" json:"currentSchedule,omitempty"`  // 当前进度值
	NextSchedule     uint32                `protobuf:"varint,5,opt,name=nextSchedule,proto3" json:"nextSchedule,omitempty"`        // 下一阶段值
	LostGoodNums     uint32                `protobuf:"varint,6,opt,name=lostGoodNums,proto3" json:"lostGoodNums,omitempty"`        // 损失物品数量
	NextStageRewards []*game.PopRewardItem `protobuf:"bytes,7,rep,name=NextStageRewards,proto3" json:"NextStageRewards,omitempty"` // 可获得奖励
	MapExt           map[string]string     `protobuf:"bytes,15,rep,name=mapExt,proto3" json:"mapExt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SuperBollRetentionPopupRsp) Reset() {
	*x = SuperBollRetentionPopupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuperBollRetentionPopupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuperBollRetentionPopupRsp) ProtoMessage() {}

func (x *SuperBollRetentionPopupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuperBollRetentionPopupRsp.ProtoReflect.Descriptor instead.
func (*SuperBollRetentionPopupRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{47}
}

func (x *SuperBollRetentionPopupRsp) GetActivityId() uint32 {
	if x != nil {
		return x.ActivityId
	}
	return 0
}

func (x *SuperBollRetentionPopupRsp) GetActivityType() uint32 {
	if x != nil {
		return x.ActivityType
	}
	return 0
}

func (x *SuperBollRetentionPopupRsp) GetActivityStatus() uint32 {
	if x != nil {
		return x.ActivityStatus
	}
	return 0
}

func (x *SuperBollRetentionPopupRsp) GetCurrentSchedule() uint32 {
	if x != nil {
		return x.CurrentSchedule
	}
	return 0
}

func (x *SuperBollRetentionPopupRsp) GetNextSchedule() uint32 {
	if x != nil {
		return x.NextSchedule
	}
	return 0
}

func (x *SuperBollRetentionPopupRsp) GetLostGoodNums() uint32 {
	if x != nil {
		return x.LostGoodNums
	}
	return 0
}

func (x *SuperBollRetentionPopupRsp) GetNextStageRewards() []*game.PopRewardItem {
	if x != nil {
		return x.NextStageRewards
	}
	return nil
}

func (x *SuperBollRetentionPopupRsp) GetMapExt() map[string]string {
	if x != nil {
		return x.MapExt
	}
	return nil
}

type CallbackStepBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId    string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId   string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	BuyRound uint32 `protobuf:"varint,3,opt,name=buyRound,proto3" json:"buyRound,omitempty"` // 本次闯关第几次购买
	BillId   string `protobuf:"bytes,4,opt,name=billId,proto3" json:"billId,omitempty"`      // 购买唯一id
	Step     int64  `protobuf:"varint,5,opt,name=step,proto3" json:"step,omitempty"`         // 获得步数
}

func (x *CallbackStepBuyReq) Reset() {
	*x = CallbackStepBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackStepBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackStepBuyReq) ProtoMessage() {}

func (x *CallbackStepBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackStepBuyReq.ProtoReflect.Descriptor instead.
func (*CallbackStepBuyReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{48}
}

func (x *CallbackStepBuyReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *CallbackStepBuyReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *CallbackStepBuyReq) GetBuyRound() uint32 {
	if x != nil {
		return x.BuyRound
	}
	return 0
}

func (x *CallbackStepBuyReq) GetBillId() string {
	if x != nil {
		return x.BillId
	}
	return ""
}

func (x *CallbackStepBuyReq) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

type CallbackStepBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CallbackStepBuyRsp) Reset() {
	*x = CallbackStepBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackStepBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackStepBuyRsp) ProtoMessage() {}

func (x *CallbackStepBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackStepBuyRsp.ProtoReflect.Descriptor instead.
func (*CallbackStepBuyRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{49}
}

// 业务实现接口
type DeliverGoodsImplementReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId    string                           `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`        // 订单唯一id。 开发者服务器可用于消息去重
	IsPreview  int64                            `protobuf:"varint,2,opt,name=isPreview,proto3" json:"isPreview,omitempty"`   // 是否预览调试
	ToOpenId   string                           `protobuf:"bytes,3,opt,name=toOpenId,proto3" json:"toOpenId,omitempty"`      // 接收道具的玩家openid
	Zone       int64                            `protobuf:"varint,4,opt,name=zone,proto3" json:"zone,omitempty"`             // 分区。1001: iOS, 2001: Android、PC
	GiftTypeId int64                            `protobuf:"varint,5,opt,name=giftTypeId,proto3" json:"giftTypeId,omitempty"` // 发货礼包类型：1- 每日签到礼包 2- 周福利礼包 3-运营活动礼包 6-每日登录礼包 8 - 游戏圈活动礼包
	GiftId     string                           `protobuf:"bytes,6,opt,name=giftId,proto3" json:"giftId,omitempty"`          // 礼包 ID（可在 MP 配置好礼包后，提前获取）
	SendTime   int64                            `protobuf:"varint,7,opt,name=sendTime,proto3" json:"sendTime,omitempty"`     // 玩家接收道具的时间
	Goods      []*DeliverGoodsImplementReq_Good `protobuf:"bytes,8,rep,name=goods,proto3" json:"goods,omitempty"`            // 发货列表
	CreateTime int64                            `protobuf:"varint,9,opt,name=createTime,proto3" json:"createTime,omitempty"` //消息发送时间
}

func (x *DeliverGoodsImplementReq) Reset() {
	*x = DeliverGoodsImplementReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverGoodsImplementReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverGoodsImplementReq) ProtoMessage() {}

func (x *DeliverGoodsImplementReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverGoodsImplementReq.ProtoReflect.Descriptor instead.
func (*DeliverGoodsImplementReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{50}
}

func (x *DeliverGoodsImplementReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *DeliverGoodsImplementReq) GetIsPreview() int64 {
	if x != nil {
		return x.IsPreview
	}
	return 0
}

func (x *DeliverGoodsImplementReq) GetToOpenId() string {
	if x != nil {
		return x.ToOpenId
	}
	return ""
}

func (x *DeliverGoodsImplementReq) GetZone() int64 {
	if x != nil {
		return x.Zone
	}
	return 0
}

func (x *DeliverGoodsImplementReq) GetGiftTypeId() int64 {
	if x != nil {
		return x.GiftTypeId
	}
	return 0
}

func (x *DeliverGoodsImplementReq) GetGiftId() string {
	if x != nil {
		return x.GiftId
	}
	return ""
}

func (x *DeliverGoodsImplementReq) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

func (x *DeliverGoodsImplementReq) GetGoods() []*DeliverGoodsImplementReq_Good {
	if x != nil {
		return x.Goods
	}
	return nil
}

func (x *DeliverGoodsImplementReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// 业务实现接口
type DeliverGoodsImplementRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeliverGoodsImplementRsp) Reset() {
	*x = DeliverGoodsImplementRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverGoodsImplementRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverGoodsImplementRsp) ProtoMessage() {}

func (x *DeliverGoodsImplementRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverGoodsImplementRsp.ProtoReflect.Descriptor instead.
func (*DeliverGoodsImplementRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{51}
}

type DragonBoatAchiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Rank   int32  `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`   // 龙舟赛名次
	RankT  int64  `protobuf:"varint,4,opt,name=rankT,proto3" json:"rankT,omitempty"` // 龙舟赛时间
}

func (x *DragonBoatAchiReq) Reset() {
	*x = DragonBoatAchiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DragonBoatAchiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DragonBoatAchiReq) ProtoMessage() {}

func (x *DragonBoatAchiReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DragonBoatAchiReq.ProtoReflect.Descriptor instead.
func (*DragonBoatAchiReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{52}
}

func (x *DragonBoatAchiReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *DragonBoatAchiReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DragonBoatAchiReq) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *DragonBoatAchiReq) GetRankT() int64 {
	if x != nil {
		return x.RankT
	}
	return 0
}

type DragonBoatAchiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DragonBoatAchiRsp) Reset() {
	*x = DragonBoatAchiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DragonBoatAchiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DragonBoatAchiRsp) ProtoMessage() {}

func (x *DragonBoatAchiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DragonBoatAchiRsp.ProtoReflect.Descriptor instead.
func (*DragonBoatAchiRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{53}
}

type AchiPlantReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId     string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId    string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	PlantCnt  int32  `protobuf:"varint,3,opt,name=plant_cnt,json=plantCnt,proto3" json:"plant_cnt,omitempty"`      // 收获次数
	PlantCntT int64  `protobuf:"varint,4,opt,name=plant_cnt_t,json=plantCntT,proto3" json:"plant_cnt_t,omitempty"` // 收获次数时间
}

func (x *AchiPlantReq) Reset() {
	*x = AchiPlantReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AchiPlantReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AchiPlantReq) ProtoMessage() {}

func (x *AchiPlantReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AchiPlantReq.ProtoReflect.Descriptor instead.
func (*AchiPlantReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{54}
}

func (x *AchiPlantReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AchiPlantReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *AchiPlantReq) GetPlantCnt() int32 {
	if x != nil {
		return x.PlantCnt
	}
	return 0
}

func (x *AchiPlantReq) GetPlantCntT() int64 {
	if x != nil {
		return x.PlantCntT
	}
	return 0
}

type AchiPlantRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AchiPlantRsp) Reset() {
	*x = AchiPlantRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AchiPlantRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AchiPlantRsp) ProtoMessage() {}

func (x *AchiPlantRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AchiPlantRsp.ProtoReflect.Descriptor instead.
func (*AchiPlantRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{55}
}

type QueryUserTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *QueryUserTeamReq) Reset() {
	*x = QueryUserTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryUserTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUserTeamReq) ProtoMessage() {}

func (x *QueryUserTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUserTeamReq.ProtoReflect.Descriptor instead.
func (*QueryUserTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{56}
}

func (x *QueryUserTeamReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryUserTeamReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QueryUserTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 战队id,无战队为""
}

func (x *QueryUserTeamRsp) Reset() {
	*x = QueryUserTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryUserTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUserTeamRsp) ProtoMessage() {}

func (x *QueryUserTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUserTeamRsp.ProtoReflect.Descriptor instead.
func (*QueryUserTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{57}
}

func (x *QueryUserTeamRsp) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

type InlineDecorateCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *InlineDecorateCheckReq) Reset() {
	*x = InlineDecorateCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InlineDecorateCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InlineDecorateCheckReq) ProtoMessage() {}

func (x *InlineDecorateCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InlineDecorateCheckReq.ProtoReflect.Descriptor instead.
func (*InlineDecorateCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{58}
}

func (x *InlineDecorateCheckReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *InlineDecorateCheckReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type InlineDecorateCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InlineDecorateCheckRsp) Reset() {
	*x = InlineDecorateCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InlineDecorateCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InlineDecorateCheckRsp) ProtoMessage() {}

func (x *InlineDecorateCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InlineDecorateCheckRsp.ProtoReflect.Descriptor instead.
func (*InlineDecorateCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{59}
}

type LoadLightweightAssetsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *LoadLightweightAssetsReq) Reset() {
	*x = LoadLightweightAssetsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadLightweightAssetsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadLightweightAssetsReq) ProtoMessage() {}

func (x *LoadLightweightAssetsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadLightweightAssetsReq.ProtoReflect.Descriptor instead.
func (*LoadLightweightAssetsReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{60}
}

func (x *LoadLightweightAssetsReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *LoadLightweightAssetsReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type LoadLightweightAssetsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets        []*game.UserAsset    `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`                                    // 游戏相关资产
	InfiniteItems []*game.InfiniteItem `protobuf:"bytes,2,rep,name=infinite_items,json=infiniteItems,proto3" json:"infinite_items,omitempty"` // 无限战前道具及过期时间
}

func (x *LoadLightweightAssetsRsp) Reset() {
	*x = LoadLightweightAssetsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadLightweightAssetsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadLightweightAssetsRsp) ProtoMessage() {}

func (x *LoadLightweightAssetsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadLightweightAssetsRsp.ProtoReflect.Descriptor instead.
func (*LoadLightweightAssetsRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{61}
}

func (x *LoadLightweightAssetsRsp) GetAssets() []*game.UserAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *LoadLightweightAssetsRsp) GetInfiniteItems() []*game.InfiniteItem {
	if x != nil {
		return x.InfiniteItems
	}
	return nil
}

type ReportPlantCoinReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId     string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	Uid        string `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Seed       string `protobuf:"bytes,4,opt,name=seed,proto3" json:"seed,omitempty"`
	StageId    int64  `protobuf:"varint,5,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	EntryTime  int64  `protobuf:"varint,6,opt,name=entry_time,json=entryTime,proto3" json:"entry_time,omitempty"`
	UseStep    uint32 `protobuf:"varint,7,opt,name=use_step,json=useStep,proto3" json:"use_step,omitempty"`
	SubmitTime int64  `protobuf:"varint,8,opt,name=submit_time,json=submitTime,proto3" json:"submit_time,omitempty"`
	Coin       int64  `protobuf:"varint,9,opt,name=coin,proto3" json:"coin,omitempty"`
}

func (x *ReportPlantCoinReq) Reset() {
	*x = ReportPlantCoinReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportPlantCoinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportPlantCoinReq) ProtoMessage() {}

func (x *ReportPlantCoinReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportPlantCoinReq.ProtoReflect.Descriptor instead.
func (*ReportPlantCoinReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{62}
}

func (x *ReportPlantCoinReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ReportPlantCoinReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ReportPlantCoinReq) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ReportPlantCoinReq) GetSeed() string {
	if x != nil {
		return x.Seed
	}
	return ""
}

func (x *ReportPlantCoinReq) GetStageId() int64 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *ReportPlantCoinReq) GetEntryTime() int64 {
	if x != nil {
		return x.EntryTime
	}
	return 0
}

func (x *ReportPlantCoinReq) GetUseStep() uint32 {
	if x != nil {
		return x.UseStep
	}
	return 0
}

func (x *ReportPlantCoinReq) GetSubmitTime() int64 {
	if x != nil {
		return x.SubmitTime
	}
	return 0
}

func (x *ReportPlantCoinReq) GetCoin() int64 {
	if x != nil {
		return x.Coin
	}
	return 0
}

type ReportPlantCoinRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportPlantCoinRsp) Reset() {
	*x = ReportPlantCoinRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportPlantCoinRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportPlantCoinRsp) ProtoMessage() {}

func (x *ReportPlantCoinRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportPlantCoinRsp.ProtoReflect.Descriptor instead.
func (*ReportPlantCoinRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{63}
}

type ChangeRegionRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId      string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	OldCityCode string `protobuf:"bytes,3,opt,name=oldCityCode,proto3" json:"oldCityCode,omitempty"`
	OldCityName string `protobuf:"bytes,4,opt,name=oldCityName,proto3" json:"oldCityName,omitempty"`
	NewCityCode string `protobuf:"bytes,5,opt,name=newCityCode,proto3" json:"newCityCode,omitempty"`
	NewCityName string `protobuf:"bytes,6,opt,name=newCityName,proto3" json:"newCityName,omitempty"`
}

func (x *ChangeRegionRankReq) Reset() {
	*x = ChangeRegionRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeRegionRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeRegionRankReq) ProtoMessage() {}

func (x *ChangeRegionRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeRegionRankReq.ProtoReflect.Descriptor instead.
func (*ChangeRegionRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{64}
}

func (x *ChangeRegionRankReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ChangeRegionRankReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ChangeRegionRankReq) GetOldCityCode() string {
	if x != nil {
		return x.OldCityCode
	}
	return ""
}

func (x *ChangeRegionRankReq) GetOldCityName() string {
	if x != nil {
		return x.OldCityName
	}
	return ""
}

func (x *ChangeRegionRankReq) GetNewCityCode() string {
	if x != nil {
		return x.NewCityCode
	}
	return ""
}

func (x *ChangeRegionRankReq) GetNewCityName() string {
	if x != nil {
		return x.NewCityName
	}
	return ""
}

type ChangeRegionRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChangeRegionRankRsp) Reset() {
	*x = ChangeRegionRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeRegionRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeRegionRankRsp) ProtoMessage() {}

func (x *ChangeRegionRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeRegionRankRsp.ProtoReflect.Descriptor instead.
func (*ChangeRegionRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{65}
}

type QueryTeamMembersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId  string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OpenId string `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
}

func (x *QueryTeamMembersReq) Reset() {
	*x = QueryTeamMembersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTeamMembersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTeamMembersReq) ProtoMessage() {}

func (x *QueryTeamMembersReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTeamMembersReq.ProtoReflect.Descriptor instead.
func (*QueryTeamMembersReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{66}
}

func (x *QueryTeamMembersReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *QueryTeamMembersReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type QueryTeamMembersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FriendList []string `protobuf:"bytes,1,rep,name=friend_list,json=friendList,proto3" json:"friend_list,omitempty"`
}

func (x *QueryTeamMembersRsp) Reset() {
	*x = QueryTeamMembersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTeamMembersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTeamMembersRsp) ProtoMessage() {}

func (x *QueryTeamMembersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTeamMembersRsp.ProtoReflect.Descriptor instead.
func (*QueryTeamMembersRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{67}
}

func (x *QueryTeamMembersRsp) GetFriendList() []string {
	if x != nil {
		return x.FriendList
	}
	return nil
}

type BatchGetUserTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId   string   `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	OpenIds []string `protobuf:"bytes,2,rep,name=open_ids,json=openIds,proto3" json:"open_ids,omitempty"`
}

func (x *BatchGetUserTeamReq) Reset() {
	*x = BatchGetUserTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetUserTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetUserTeamReq) ProtoMessage() {}

func (x *BatchGetUserTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetUserTeamReq.ProtoReflect.Descriptor instead.
func (*BatchGetUserTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{68}
}

func (x *BatchGetUserTeamReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *BatchGetUserTeamReq) GetOpenIds() []string {
	if x != nil {
		return x.OpenIds
	}
	return nil
}

type BatchGetUserTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UtMap map[string]*BatchGetUserTeamRsp_TeamInfo `protobuf:"bytes,1,rep,name=ut_map,json=utMap,proto3" json:"ut_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BatchGetUserTeamRsp) Reset() {
	*x = BatchGetUserTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetUserTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetUserTeamRsp) ProtoMessage() {}

func (x *BatchGetUserTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetUserTeamRsp.ProtoReflect.Descriptor instead.
func (*BatchGetUserTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{69}
}

func (x *BatchGetUserTeamRsp) GetUtMap() map[string]*BatchGetUserTeamRsp_TeamInfo {
	if x != nil {
		return x.UtMap
	}
	return nil
}

type DeliveryRsp_Product struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Num  int64  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *DeliveryRsp_Product) Reset() {
	*x = DeliveryRsp_Product{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryRsp_Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryRsp_Product) ProtoMessage() {}

func (x *DeliveryRsp_Product) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryRsp_Product.ProtoReflect.Descriptor instead.
func (*DeliveryRsp_Product) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{15, 0}
}

func (x *DeliveryRsp_Product) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DeliveryRsp_Product) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type WarOrderDeliveryRsp_Product struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Num  int64  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
}

func (x *WarOrderDeliveryRsp_Product) Reset() {
	*x = WarOrderDeliveryRsp_Product{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WarOrderDeliveryRsp_Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarOrderDeliveryRsp_Product) ProtoMessage() {}

func (x *WarOrderDeliveryRsp_Product) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarOrderDeliveryRsp_Product.ProtoReflect.Descriptor instead.
func (*WarOrderDeliveryRsp_Product) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{34, 0}
}

func (x *WarOrderDeliveryRsp_Product) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WarOrderDeliveryRsp_Product) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type DeliverGoodsImplementReq_Good struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`    // 游戏道具id标识
	Num int64  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"` // 发送的道具数量
}

func (x *DeliverGoodsImplementReq_Good) Reset() {
	*x = DeliverGoodsImplementReq_Good{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliverGoodsImplementReq_Good) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliverGoodsImplementReq_Good) ProtoMessage() {}

func (x *DeliverGoodsImplementReq_Good) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliverGoodsImplementReq_Good.ProtoReflect.Descriptor instead.
func (*DeliverGoodsImplementReq_Good) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{50, 0}
}

func (x *DeliverGoodsImplementReq_Good) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DeliverGoodsImplementReq_Good) GetNum() int64 {
	if x != nil {
		return x.Num
	}
	return 0
}

type BatchGetUserTeamRsp_TeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 战队名称
}

func (x *BatchGetUserTeamRsp_TeamInfo) Reset() {
	*x = BatchGetUserTeamRsp_TeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetUserTeamRsp_TeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetUserTeamRsp_TeamInfo) ProtoMessage() {}

func (x *BatchGetUserTeamRsp_TeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetUserTeamRsp_TeamInfo.ProtoReflect.Descriptor instead.
func (*BatchGetUserTeamRsp_TeamInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP(), []int{69, 0}
}

func (x *BatchGetUserTeamRsp_TeamInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_pb_game_cwsx_xian_cwsx_notify_notify_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2f,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x1a, 0x1e, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2f, 0x69, 0x6e, 0x6c, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x2f,
	0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f,
	0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x7c, 0x0a, 0x14, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x72, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x7a, 0x44, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x62, 0x69, 0x7a, 0x44, 0x61, 0x74, 0x61, 0x22, 0x6d, 0x0a,
	0x15, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x12, 0x22,
	0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x46, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x46, 0x69, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x69, 0x7a, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x07, 0x62, 0x69, 0x7a, 0x44, 0x61, 0x74, 0x61, 0x22, 0x2a, 0x0a, 0x0d,
	0x54, 0x65, 0x61, 0x6d, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a,
	0x08, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x0d, 0x54, 0x65, 0x61,
	0x6d, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x09, 0x74, 0x65,
	0x61, 0x6d, 0x73, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x4d, 0x61, 0x70, 0x1a,
	0x50, 0x0a, 0x0d, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x3a, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x4d, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x22, 0x1e, 0x0a,
	0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x4d, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x72,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x72, 0x65, 0x73, 0x22, 0x29, 0x0a,
	0x0f, 0x4c, 0x61, 0x62, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x22, 0x4d, 0x0a, 0x0f, 0x4c, 0x61, 0x62, 0x61,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x67,
	0x75, 0x69, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x09, 0x67, 0x75, 0x69, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61,
	0x78, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d,
	0x61, 0x78, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x22, 0x62, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x47,
	0x75, 0x69, 0x64, 0x65, 0x73, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x75, 0x69, 0x64, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x08, 0x67, 0x75, 0x69, 0x64, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x55,
	0x73, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x65, 0x73, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52,
	0x73, 0x70, 0x22, 0xce, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x65, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73,
	0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x46, 0x6c, 0x6f, 0x6f, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x6c,
	0x6f, 0x6f, 0x72, 0x22, 0x0f, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61,
	0x6d, 0x52, 0x73, 0x70, 0x22, 0xb7, 0x01, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x65, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x65, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73, 0x22, 0x0f,
	0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22,
	0x9d, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0x7a, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x3a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x1a, 0x2f, 0x0a, 0x07, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x92, 0x01, 0x0a, 0x07,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x22, 0x69, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x77,
	0x73, 0x78, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x18,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x77, 0x73, 0x78, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x08, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x77, 0x73, 0x78, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x2e,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x73, 0x1a, 0x4f, 0x0a, 0x0d, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3e, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x6e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x6d, 0x61, 0x78, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x6f, 0x72,
	0x22, 0x87, 0x02, 0x0a, 0x16, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75,
	0x6d, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x71, 0x0a, 0x0a, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a,
	0x16, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x7d, 0x0a,
	0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2d, 0x0a,
	0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72,
	0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x22, 0xb7, 0x01, 0x0a,
	0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x9b, 0x01, 0x0a, 0x1a, 0x47, 0x61, 0x6d, 0x65, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63,
	0x61, 0x72, 0x64, 0x73, 0x22, 0x62, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d,
	0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x52,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x19, 0x57, 0x61, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x75,
	0x66, 0x66, 0x22, 0xc4, 0x03, 0x0a, 0x19, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x73, 0x70,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e, 0x65,
	0x78, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f,
	0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x6c, 0x6f, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x4e, 0x75, 0x6d, 0x73, 0x12, 0x44,
	0x0a, 0x10, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x50, 0x6f, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x10, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70, 0x45, 0x78,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x1a, 0x39,
	0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x57, 0x61,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x6f,
	0x73, 0x22, 0x17, 0x0a, 0x15, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0xd1, 0x01, 0x0a, 0x13, 0x57,
	0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x2a, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x6f, 0x73, 0x22, 0x8a,
	0x01, 0x0a, 0x13, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x1a, 0x2f, 0x0a, 0x07, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x31, 0x0a, 0x0f, 0x53,
	0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x22, 0x11,
	0x0a, 0x0f, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x73,
	0x70, 0x22, 0x55, 0x0a, 0x0c, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x0b, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x44, 0x61, 0x74,
	0x61, 0x22, 0xf2, 0x01, 0x0a, 0x16, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65,
	0x67, 0x79, 0x43, 0x6f, 0x6e, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x43, 0x6f, 0x6e, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x56, 0x0a, 0x16, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32, 0x52, 0x73, 0x70,
	0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x22, 0x6d,
	0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x22, 0x61, 0x0a,
	0x11, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4f, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x21, 0x0a, 0x02, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x4f,
	0x70, 0x52, 0x02, 0x6f, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x65, 0x61, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x54, 0x65, 0x61, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x04, 0x74, 0x65, 0x61, 0x6d,
	0x22, 0x13, 0x0a, 0x11, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x4f, 0x70, 0x52, 0x73, 0x70, 0x22, 0x46, 0x0a, 0x14, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0xb7, 0x01,
	0x0a, 0x14, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x1a, 0x53, 0x75, 0x70, 0x65,
	0x72, 0x42, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x75, 0x66, 0x66,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x42, 0x75,
	0x66, 0x66, 0x22, 0xc6, 0x03, 0x0a, 0x1a, 0x53, 0x75, 0x70, 0x65, 0x72, 0x42, 0x6f, 0x6c, 0x6c,
	0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x73,
	0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49,
	0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6e, 0x65, 0x78, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6e,
	0x65, 0x78, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c,
	0x6f, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x4e, 0x75, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x6c, 0x6f, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x4e, 0x75, 0x6d, 0x73, 0x12,
	0x44, 0x0a, 0x10, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x50, 0x6f, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x10, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x49, 0x0a, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x53, 0x75, 0x70, 0x65, 0x72, 0x42, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x73, 0x70, 0x2e, 0x4d, 0x61, 0x70,
	0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6d, 0x61, 0x70, 0x45, 0x78, 0x74,
	0x1a, 0x39, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x45, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8a, 0x01, 0x0a, 0x12,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x65, 0x70, 0x42, 0x75, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x62, 0x75, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x62, 0x75, 0x79, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69,
	0x6c, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x65, 0x70, 0x42, 0x75, 0x79, 0x52, 0x73, 0x70, 0x22, 0xe0,
	0x02, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49,
	0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x49,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x69, 0x66, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49,
	0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x47, 0x6f, 0x6f, 0x64,
	0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x28, 0x0a, 0x04, 0x47, 0x6f, 0x6f, 0x64, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6e, 0x75,
	0x6d, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x6b, 0x0a,
	0x11, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x42, 0x6f, 0x61, 0x74, 0x41, 0x63, 0x68, 0x69, 0x52,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x72, 0x61, 0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x22, 0x13, 0x0a, 0x11, 0x44, 0x72,
	0x61, 0x67, 0x6f, 0x6e, 0x42, 0x6f, 0x61, 0x74, 0x41, 0x63, 0x68, 0x69, 0x52, 0x73, 0x70, 0x22,
	0x79, 0x0a, 0x0c, 0x41, 0x63, 0x68, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x6c,
	0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x5f, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x70, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x6e, 0x74, 0x54, 0x22, 0x0e, 0x0a, 0x0c, 0x41, 0x63,
	0x68, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x40, 0x0a, 0x10, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x10,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x16, 0x49, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x22, 0x18, 0x0a, 0x16, 0x49, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x63, 0x6f, 0x72,
	0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x22, 0x48, 0x0a, 0x18, 0x4c,
	0x6f, 0x61, 0x64, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x64, 0x4c, 0x69,
	0x67, 0x68, 0x74, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x12, 0x3e, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0d, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0xf2, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74,
	0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x65, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x65,
	0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x75, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x63, 0x6f, 0x69, 0x6e, 0x22, 0x14, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x6c, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x22, 0xcb, 0x01, 0x0a, 0x13,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x43, 0x69, 0x74,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69, 0x74, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x43,
	0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x43, 0x69,
	0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65,
	0x77, 0x43, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70,
	0x22, 0x43, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x47, 0x0a,
	0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x40,
	0x0a, 0x06, 0x75, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x2e, 0x55,
	0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x75, 0x74, 0x4d, 0x61, 0x70,
	0x1a, 0x1e, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x1a, 0x61, 0x0a, 0x0a, 0x55, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x2e,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x2a, 0x52, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x47, 0x61, 0x6d, 0x65, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x64, 0x53, 0x74,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x10, 0x01, 0x2a, 0x67, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x47, 0x61, 0x6d, 0x65, 0x4e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x10, 0x01, 0x12,
	0x1f, 0x0a, 0x1b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x46, 0x72, 0x6f, 0x6d, 0x5f, 0x47,
	0x61, 0x6d, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x10, 0x02,
	0x2a, 0x43, 0x0a, 0x06, 0x53, 0x61, 0x66, 0x65, 0x4f, 0x70, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x61,
	0x66, 0x65, 0x4f, 0x70, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x53, 0x61, 0x66, 0x65, 0x4f, 0x70, 0x5f, 0x52, 0x65, 0x73, 0x65, 0x74, 0x10, 0x01,
	0x12, 0x13, 0x0a, 0x0f, 0x53, 0x61, 0x66, 0x65, 0x4f, 0x70, 0x5f, 0x44, 0x69, 0x73, 0x73, 0x6f,
	0x6c, 0x76, 0x65, 0x10, 0x02, 0x32, 0x9a, 0x19, 0x0a, 0x06, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x12, 0x4d, 0x0a, 0x08, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1f, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x18, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x40, 0x0a, 0x0a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x12,
	0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12,
	0x40, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x18, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x61, 0x0a, 0x15, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43,
	0x77, 0x73, 0x78, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x23, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x77, 0x73, 0x78, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x23, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x77, 0x73, 0x78, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x13, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x21, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x61, 0x6d, 0x65, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x6a, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x12, 0x26, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x73, 0x70, 0x12, 0x76, 0x0a,
	0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x61, 0x6d, 0x65, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x2a, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x61, 0x6d, 0x65, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x64, 0x0a, 0x16, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12,
	0x24, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x57, 0x61, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70,
	0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x12, 0x57,
	0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x20, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x57, 0x61,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x57, 0x61, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x0c, 0x53, 0x61, 0x66,
	0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32, 0x52, 0x73, 0x70, 0x12,
	0x5c, 0x0a, 0x14, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32, 0x52, 0x73, 0x70, 0x12, 0x57, 0x0a,
	0x0f, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x69, 0x6e,
	0x12, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x53, 0x61, 0x66, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x56, 0x32, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4f, 0x70, 0x12, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x4f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4f,
	0x70, 0x52, 0x73, 0x70, 0x12, 0x55, 0x0a, 0x11, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1f, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x61, 0x66, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x67, 0x0a, 0x17, 0x53,
	0x75, 0x70, 0x65, 0x72, 0x42, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x12, 0x25, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x53, 0x75, 0x70, 0x65, 0x72, 0x42, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x75, 0x70, 0x65, 0x72, 0x42,
	0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75,
	0x70, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x11, 0x48, 0x65, 0x61, 0x72, 0x74, 0x46, 0x75, 0x6c,
	0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1f, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0d,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x15, 0x2e,
	0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x49, 0x6e, 0x6c,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x55, 0x0a, 0x10, 0x54,
	0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x1f, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74,
	0x65, 0x70, 0x42, 0x75, 0x79, 0x12, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x65, 0x70, 0x42, 0x75,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x65, 0x70, 0x42, 0x75, 0x79,
	0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x79, 0x6e, 0x63,
	0x12, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65,
	0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65,
	0x67, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x44, 0x65, 0x63, 0x6f, 0x72,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65,
	0x67, 0x65, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x44, 0x65, 0x63, 0x6f, 0x72,
	0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x60, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x23,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6d, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6d, 0x70, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0e, 0x44, 0x72, 0x61, 0x67,
	0x6f, 0x6e, 0x42, 0x6f, 0x61, 0x74, 0x41, 0x63, 0x68, 0x69, 0x12, 0x1c, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x42, 0x6f, 0x61,
	0x74, 0x41, 0x63, 0x68, 0x69, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x42, 0x6f, 0x61, 0x74, 0x41,
	0x63, 0x68, 0x69, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09, 0x41, 0x63, 0x68, 0x69, 0x50, 0x6c,
	0x61, 0x6e, 0x74, 0x12, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x41, 0x63, 0x68, 0x69, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x41, 0x63, 0x68, 0x69, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x08, 0x56, 0x49, 0x50, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x12, 0x17, 0x2e, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x6e, 0x6c,
	0x65, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x65,
	0x73, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x12, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x65, 0x73, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x65, 0x73, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x0c, 0x4c, 0x61, 0x62, 0x61, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x4c, 0x61, 0x62, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x4c, 0x61, 0x62, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12,
	0x49, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d,
	0x12, 0x1b, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x13, 0x49, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x12, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x49, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x63, 0x6f, 0x72, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x07, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x47, 0x4d, 0x12, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x47, 0x4d, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x47, 0x4d, 0x52, 0x73, 0x70,
	0x12, 0x61, 0x0a, 0x15, 0x4c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x23, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x4c,
	0x69, 0x67, 0x68, 0x74, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61,
	0x6e, 0x74, 0x43, 0x6f, 0x69, 0x6e, 0x12, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x6f,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x69,
	0x6e, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x1e, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a, 0x10,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d,
	0x12, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70,
	0x12, 0x40, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x18,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x73,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x73, 0x70, 0x42, 0x5b, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65,
	0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescData = file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDesc
)

func file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescData)
	})
	return file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDescData
}

var file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes = make([]protoimpl.MessageInfo, 79)
var file_pb_game_cwsx_xian_cwsx_notify_notify_proto_goTypes = []interface{}{
	(GameDeliveryType)(0),                   // 0: xian_cwsx.GameDeliveryType
	(RewardItemType)(0),                     // 1: xian_cwsx.RewardItemType
	(SafeOp)(0),                             // 2: xian_cwsx.SafeOp
	(*TimerCallbackRequest)(nil),            // 3: xian_cwsx.TimerCallbackRequest
	(*TimerCallbackResponse)(nil),           // 4: xian_cwsx.TimerCallbackResponse
	(*TeamsBatchReq)(nil),                   // 5: xian_cwsx.TeamsBatchReq
	(*TeamsBatchRsp)(nil),                   // 6: xian_cwsx.TeamsBatchRsp
	(*CheckGMReq)(nil),                      // 7: xian_cwsx.CheckGMReq
	(*CheckGMRsp)(nil),                      // 8: xian_cwsx.CheckGMRsp
	(*LabaUserInfoReq)(nil),                 // 9: xian_cwsx.LabaUserInfoReq
	(*LabaUserInfoRsp)(nil),                 // 10: xian_cwsx.LabaUserInfoRsp
	(*UserGuidesSubmitReq)(nil),             // 11: xian_cwsx.UserGuidesSubmitReq
	(*UserGuidesSubmitRsp)(nil),             // 12: xian_cwsx.UserGuidesSubmitRsp
	(*CreateTeamReq)(nil),                   // 13: xian_cwsx.CreateTeamReq
	(*CreateTeamRsp)(nil),                   // 14: xian_cwsx.CreateTeamRsp
	(*CheckOrderReq)(nil),                   // 15: xian_cwsx.CheckOrderReq
	(*CheckOrderRsp)(nil),                   // 16: xian_cwsx.CheckOrderRsp
	(*DeliveryReq)(nil),                     // 17: xian_cwsx.DeliveryReq
	(*DeliveryRsp)(nil),                     // 18: xian_cwsx.DeliveryRsp
	(*Package)(nil),                         // 19: xian_cwsx.Package
	(*BatchQueryCwsxPackageReq)(nil),        // 20: xian_cwsx.BatchQueryCwsxPackageReq
	(*BatchQueryCwsxPackageRsp)(nil),        // 21: xian_cwsx.BatchQueryCwsxPackageRsp
	(*QueryStageReq)(nil),                   // 22: xian_cwsx.QueryStageReq
	(*QueryStageRsp)(nil),                   // 23: xian_cwsx.QueryStageRsp
	(*GameDeliveryPackageReq)(nil),          // 24: xian_cwsx.GameDeliveryPackageReq
	(*RewardItem)(nil),                      // 25: xian_cwsx.RewardItem
	(*GameDeliveryPackageRsp)(nil),          // 26: xian_cwsx.GameDeliveryPackageRsp
	(*BatchGameDeliveryPackageReq)(nil),     // 27: xian_cwsx.BatchGameDeliveryPackageReq
	(*BatchGameDeliveryPackageRsp)(nil),     // 28: xian_cwsx.BatchGameDeliveryPackageRsp
	(*BatchGameDeliveryPackageListReq)(nil), // 29: xian_cwsx.BatchGameDeliveryPackageListReq
	(*GameDeliveryPackageRewards)(nil),      // 30: xian_cwsx.GameDeliveryPackageRewards
	(*BatchGameDeliveryPackageListRsp)(nil), // 31: xian_cwsx.BatchGameDeliveryPackageListRsp
	(*WarOrderRetentionPopupReq)(nil),       // 32: xian_cwsx.WarOrderRetentionPopupReq
	(*WarOrderRetentionPopupRsp)(nil),       // 33: xian_cwsx.WarOrderRetentionPopupRsp
	(*WarOrderCheckOrderReq)(nil),           // 34: xian_cwsx.WarOrderCheckOrderReq
	(*WarOrderCheckOrderRsp)(nil),           // 35: xian_cwsx.WarOrderCheckOrderRsp
	(*WarOrderDeliveryReq)(nil),             // 36: xian_cwsx.WarOrderDeliveryReq
	(*WarOrderDeliveryRsp)(nil),             // 37: xian_cwsx.WarOrderDeliveryRsp
	(*SafeCallbackReq)(nil),                 // 38: xian_cwsx.SafeCallbackReq
	(*SafeCallbackRsp)(nil),                 // 39: xian_cwsx.SafeCallbackRsp
	(*CallbackInfo)(nil),                    // 40: xian_cwsx.CallbackInfo
	(*CallbackMsg)(nil),                     // 41: xian_cwsx.CallbackMsg
	(*SafeCheckCallbackV2Req)(nil),          // 42: xian_cwsx.SafeCheckCallbackV2Req
	(*SafeCheckCallbackV2Rsp)(nil),          // 43: xian_cwsx.SafeCheckCallbackV2Rsp
	(*TeamChange)(nil),                      // 44: xian_cwsx.TeamChange
	(*SafeCallbackOpReq)(nil),               // 45: xian_cwsx.SafeCallbackOpReq
	(*SafeCallbackOpRsp)(nil),               // 46: xian_cwsx.SafeCallbackOpRsp
	(*SafeCallbackQueryReq)(nil),            // 47: xian_cwsx.SafeCallbackQueryReq
	(*SafeCallbackQueryRsp)(nil),            // 48: xian_cwsx.SafeCallbackQueryRsp
	(*SuperBollRetentionPopupReq)(nil),      // 49: xian_cwsx.SuperBollRetentionPopupReq
	(*SuperBollRetentionPopupRsp)(nil),      // 50: xian_cwsx.SuperBollRetentionPopupRsp
	(*CallbackStepBuyReq)(nil),              // 51: xian_cwsx.CallbackStepBuyReq
	(*CallbackStepBuyRsp)(nil),              // 52: xian_cwsx.CallbackStepBuyRsp
	(*DeliverGoodsImplementReq)(nil),        // 53: xian_cwsx.DeliverGoodsImplementReq
	(*DeliverGoodsImplementRsp)(nil),        // 54: xian_cwsx.DeliverGoodsImplementRsp
	(*DragonBoatAchiReq)(nil),               // 55: xian_cwsx.DragonBoatAchiReq
	(*DragonBoatAchiRsp)(nil),               // 56: xian_cwsx.DragonBoatAchiRsp
	(*AchiPlantReq)(nil),                    // 57: xian_cwsx.AchiPlantReq
	(*AchiPlantRsp)(nil),                    // 58: xian_cwsx.AchiPlantRsp
	(*QueryUserTeamReq)(nil),                // 59: xian_cwsx.QueryUserTeamReq
	(*QueryUserTeamRsp)(nil),                // 60: xian_cwsx.QueryUserTeamRsp
	(*InlineDecorateCheckReq)(nil),          // 61: xian_cwsx.InlineDecorateCheckReq
	(*InlineDecorateCheckRsp)(nil),          // 62: xian_cwsx.InlineDecorateCheckRsp
	(*LoadLightweightAssetsReq)(nil),        // 63: xian_cwsx.LoadLightweightAssetsReq
	(*LoadLightweightAssetsRsp)(nil),        // 64: xian_cwsx.LoadLightweightAssetsRsp
	(*ReportPlantCoinReq)(nil),              // 65: xian_cwsx.ReportPlantCoinReq
	(*ReportPlantCoinRsp)(nil),              // 66: xian_cwsx.ReportPlantCoinRsp
	(*ChangeRegionRankReq)(nil),             // 67: xian_cwsx.ChangeRegionRankReq
	(*ChangeRegionRankRsp)(nil),             // 68: xian_cwsx.ChangeRegionRankRsp
	(*QueryTeamMembersReq)(nil),             // 69: xian_cwsx.QueryTeamMembersReq
	(*QueryTeamMembersRsp)(nil),             // 70: xian_cwsx.QueryTeamMembersRsp
	(*BatchGetUserTeamReq)(nil),             // 71: xian_cwsx.BatchGetUserTeamReq
	(*BatchGetUserTeamRsp)(nil),             // 72: xian_cwsx.BatchGetUserTeamRsp
	nil,                                     // 73: xian_cwsx.TeamsBatchRsp.TeamsMapEntry
	(*DeliveryRsp_Product)(nil),             // 74: xian_cwsx.DeliveryRsp.Product
	nil,                                     // 75: xian_cwsx.BatchQueryCwsxPackageRsp.PackagesEntry
	nil,                                     // 76: xian_cwsx.WarOrderRetentionPopupRsp.MapExtEntry
	(*WarOrderDeliveryRsp_Product)(nil),     // 77: xian_cwsx.WarOrderDeliveryRsp.Product
	nil,                                     // 78: xian_cwsx.SuperBollRetentionPopupRsp.MapExtEntry
	(*DeliverGoodsImplementReq_Good)(nil),   // 79: xian_cwsx.DeliverGoodsImplementReq.Good
	(*BatchGetUserTeamRsp_TeamInfo)(nil),    // 80: xian_cwsx.BatchGetUserTeamRsp.TeamInfo
	nil,                                     // 81: xian_cwsx.BatchGetUserTeamRsp.UtMapEntry
	(game.OperatingSystem)(0),               // 82: xian_cwsx.OperatingSystem
	(*game.TreasureCard)(nil),               // 83: xian_cwsx.TreasureCard
	(*game.PopRewardItem)(nil),              // 84: xian_cwsx.PopRewardItem
	(*game.UserAsset)(nil),                  // 85: xian_cwsx.UserAsset
	(*game.InfiniteItem)(nil),               // 86: xian_cwsx.InfiniteItem
	(*team.TeamInfo)(nil),                   // 87: xian_cwsx.TeamInfo
	(*inlet.InletStatusReq)(nil),            // 88: inlet.InletStatusReq
	(*inlet.ActivityStateReq)(nil),          // 89: inlet.ActivityStateReq
	(*privilege.BatchGetDecorateReq)(nil),   // 90: privilege.BatchGetDecorateReq
	(*inlet.InletStatusRsp)(nil),            // 91: inlet.InletStatusRsp
	(*inlet.ActivityStateRsp)(nil),          // 92: inlet.ActivityStateRsp
	(*privilege.BatchGetDecorateRsp)(nil),   // 93: privilege.BatchGetDecorateRsp
}
var file_pb_game_cwsx_xian_cwsx_notify_notify_proto_depIdxs = []int32{
	73, // 0: xian_cwsx.TeamsBatchRsp.teams_map:type_name -> xian_cwsx.TeamsBatchRsp.TeamsMapEntry
	82, // 1: xian_cwsx.CheckOrderReq.os:type_name -> xian_cwsx.OperatingSystem
	74, // 2: xian_cwsx.DeliveryRsp.products:type_name -> xian_cwsx.DeliveryRsp.Product
	25, // 3: xian_cwsx.Package.rewards:type_name -> xian_cwsx.RewardItem
	75, // 4: xian_cwsx.BatchQueryCwsxPackageRsp.packages:type_name -> xian_cwsx.BatchQueryCwsxPackageRsp.PackagesEntry
	0,  // 5: xian_cwsx.GameDeliveryPackageReq.type:type_name -> xian_cwsx.GameDeliveryType
	1,  // 6: xian_cwsx.RewardItem.type:type_name -> xian_cwsx.RewardItemType
	25, // 7: xian_cwsx.GameDeliveryPackageRsp.rewards:type_name -> xian_cwsx.RewardItem
	83, // 8: xian_cwsx.GameDeliveryPackageRsp.cards:type_name -> xian_cwsx.TreasureCard
	25, // 9: xian_cwsx.BatchGameDeliveryPackageRsp.rewards:type_name -> xian_cwsx.RewardItem
	83, // 10: xian_cwsx.BatchGameDeliveryPackageRsp.cards:type_name -> xian_cwsx.TreasureCard
	25, // 11: xian_cwsx.GameDeliveryPackageRewards.rewards:type_name -> xian_cwsx.RewardItem
	83, // 12: xian_cwsx.GameDeliveryPackageRewards.cards:type_name -> xian_cwsx.TreasureCard
	30, // 13: xian_cwsx.BatchGameDeliveryPackageListRsp.rewards:type_name -> xian_cwsx.GameDeliveryPackageRewards
	84, // 14: xian_cwsx.WarOrderRetentionPopupRsp.NextStageRewards:type_name -> xian_cwsx.PopRewardItem
	76, // 15: xian_cwsx.WarOrderRetentionPopupRsp.mapExt:type_name -> xian_cwsx.WarOrderRetentionPopupRsp.MapExtEntry
	82, // 16: xian_cwsx.WarOrderCheckOrderReq.os:type_name -> xian_cwsx.OperatingSystem
	82, // 17: xian_cwsx.WarOrderDeliveryReq.os:type_name -> xian_cwsx.OperatingSystem
	77, // 18: xian_cwsx.WarOrderDeliveryRsp.products:type_name -> xian_cwsx.WarOrderDeliveryRsp.Product
	2,  // 19: xian_cwsx.SafeCallbackOpReq.op:type_name -> xian_cwsx.SafeOp
	44, // 20: xian_cwsx.SafeCallbackOpReq.team:type_name -> xian_cwsx.TeamChange
	84, // 21: xian_cwsx.SuperBollRetentionPopupRsp.NextStageRewards:type_name -> xian_cwsx.PopRewardItem
	78, // 22: xian_cwsx.SuperBollRetentionPopupRsp.mapExt:type_name -> xian_cwsx.SuperBollRetentionPopupRsp.MapExtEntry
	79, // 23: xian_cwsx.DeliverGoodsImplementReq.goods:type_name -> xian_cwsx.DeliverGoodsImplementReq.Good
	85, // 24: xian_cwsx.LoadLightweightAssetsRsp.assets:type_name -> xian_cwsx.UserAsset
	86, // 25: xian_cwsx.LoadLightweightAssetsRsp.infinite_items:type_name -> xian_cwsx.InfiniteItem
	81, // 26: xian_cwsx.BatchGetUserTeamRsp.ut_map:type_name -> xian_cwsx.BatchGetUserTeamRsp.UtMapEntry
	87, // 27: xian_cwsx.TeamsBatchRsp.TeamsMapEntry.value:type_name -> xian_cwsx.TeamInfo
	19, // 28: xian_cwsx.BatchQueryCwsxPackageRsp.PackagesEntry.value:type_name -> xian_cwsx.Package
	80, // 29: xian_cwsx.BatchGetUserTeamRsp.UtMapEntry.value:type_name -> xian_cwsx.BatchGetUserTeamRsp.TeamInfo
	3,  // 30: xian_cwsx.Notify.Callback:input_type -> xian_cwsx.TimerCallbackRequest
	13, // 31: xian_cwsx.Notify.CreateTeam:input_type -> xian_cwsx.CreateTeamReq
	15, // 32: xian_cwsx.Notify.CheckOrder:input_type -> xian_cwsx.CheckOrderReq
	17, // 33: xian_cwsx.Notify.Delivery:input_type -> xian_cwsx.DeliveryReq
	22, // 34: xian_cwsx.Notify.QueryStage:input_type -> xian_cwsx.QueryStageReq
	20, // 35: xian_cwsx.Notify.BatchQueryCwsxPackage:input_type -> xian_cwsx.BatchQueryCwsxPackageReq
	24, // 36: xian_cwsx.Notify.GameDeliveryPackage:input_type -> xian_cwsx.GameDeliveryPackageReq
	27, // 37: xian_cwsx.Notify.BatchGameDeliveryPackage:input_type -> xian_cwsx.BatchGameDeliveryPackageReq
	29, // 38: xian_cwsx.Notify.BatchGameDeliveryPackageList:input_type -> xian_cwsx.BatchGameDeliveryPackageListReq
	32, // 39: xian_cwsx.Notify.WarOrderRetentionPopup:input_type -> xian_cwsx.WarOrderRetentionPopupReq
	34, // 40: xian_cwsx.Notify.WarOrderCheckOrder:input_type -> xian_cwsx.WarOrderCheckOrderReq
	36, // 41: xian_cwsx.Notify.WarOrderDelivery:input_type -> xian_cwsx.WarOrderDeliveryReq
	42, // 42: xian_cwsx.Notify.SafeCallback:input_type -> xian_cwsx.SafeCheckCallbackV2Req
	42, // 43: xian_cwsx.Notify.SafeCallbackDescribe:input_type -> xian_cwsx.SafeCheckCallbackV2Req
	42, // 44: xian_cwsx.Notify.SafeCallbackPin:input_type -> xian_cwsx.SafeCheckCallbackV2Req
	45, // 45: xian_cwsx.Notify.SafeCallbackOp:input_type -> xian_cwsx.SafeCallbackOpReq
	47, // 46: xian_cwsx.Notify.SafeCallbackQuery:input_type -> xian_cwsx.SafeCallbackQueryReq
	49, // 47: xian_cwsx.Notify.SuperBollRetentionPopup:input_type -> xian_cwsx.SuperBollRetentionPopupReq
	3,  // 48: xian_cwsx.Notify.HeartFullCallback:input_type -> xian_cwsx.TimerCallbackRequest
	88, // 49: xian_cwsx.Notify.ActivityCheck:input_type -> inlet.InletStatusReq
	3,  // 50: xian_cwsx.Notify.TeamWeekCallback:input_type -> xian_cwsx.TimerCallbackRequest
	51, // 51: xian_cwsx.Notify.CallbackStepBuy:input_type -> xian_cwsx.CallbackStepBuyReq
	89, // 52: xian_cwsx.Notify.InletSync:input_type -> inlet.ActivityStateReq
	90, // 53: xian_cwsx.Notify.BatchGetDecorate:input_type -> privilege.BatchGetDecorateReq
	53, // 54: xian_cwsx.Notify.DeliverGoodsCallback:input_type -> xian_cwsx.DeliverGoodsImplementReq
	55, // 55: xian_cwsx.Notify.DragonBoatAchi:input_type -> xian_cwsx.DragonBoatAchiReq
	57, // 56: xian_cwsx.Notify.AchiPlant:input_type -> xian_cwsx.AchiPlantReq
	89, // 57: xian_cwsx.Notify.VIPCheck:input_type -> inlet.ActivityStateReq
	11, // 58: xian_cwsx.Notify.UserGuidesSubmit:input_type -> xian_cwsx.UserGuidesSubmitReq
	9,  // 59: xian_cwsx.Notify.LabaUserInfo:input_type -> xian_cwsx.LabaUserInfoReq
	59, // 60: xian_cwsx.Notify.QueryUserTeam:input_type -> xian_cwsx.QueryUserTeamReq
	61, // 61: xian_cwsx.Notify.InlineDecorateCheck:input_type -> xian_cwsx.InlineDecorateCheckReq
	7,  // 62: xian_cwsx.Notify.CheckGM:input_type -> xian_cwsx.CheckGMReq
	63, // 63: xian_cwsx.Notify.LoadLightweightAssets:input_type -> xian_cwsx.LoadLightweightAssetsReq
	65, // 64: xian_cwsx.Notify.ReportPlantCoin:input_type -> xian_cwsx.ReportPlantCoinReq
	67, // 65: xian_cwsx.Notify.ChangeRegionRank:input_type -> xian_cwsx.ChangeRegionRankReq
	69, // 66: xian_cwsx.Notify.QueryTeamMembers:input_type -> xian_cwsx.QueryTeamMembersReq
	71, // 67: xian_cwsx.Notify.BatchGetUserTeam:input_type -> xian_cwsx.BatchGetUserTeamReq
	5,  // 68: xian_cwsx.Notify.TeamsBatch:input_type -> xian_cwsx.TeamsBatchReq
	4,  // 69: xian_cwsx.Notify.Callback:output_type -> xian_cwsx.TimerCallbackResponse
	14, // 70: xian_cwsx.Notify.CreateTeam:output_type -> xian_cwsx.CreateTeamRsp
	16, // 71: xian_cwsx.Notify.CheckOrder:output_type -> xian_cwsx.CheckOrderRsp
	18, // 72: xian_cwsx.Notify.Delivery:output_type -> xian_cwsx.DeliveryRsp
	23, // 73: xian_cwsx.Notify.QueryStage:output_type -> xian_cwsx.QueryStageRsp
	21, // 74: xian_cwsx.Notify.BatchQueryCwsxPackage:output_type -> xian_cwsx.BatchQueryCwsxPackageRsp
	26, // 75: xian_cwsx.Notify.GameDeliveryPackage:output_type -> xian_cwsx.GameDeliveryPackageRsp
	28, // 76: xian_cwsx.Notify.BatchGameDeliveryPackage:output_type -> xian_cwsx.BatchGameDeliveryPackageRsp
	31, // 77: xian_cwsx.Notify.BatchGameDeliveryPackageList:output_type -> xian_cwsx.BatchGameDeliveryPackageListRsp
	33, // 78: xian_cwsx.Notify.WarOrderRetentionPopup:output_type -> xian_cwsx.WarOrderRetentionPopupRsp
	35, // 79: xian_cwsx.Notify.WarOrderCheckOrder:output_type -> xian_cwsx.WarOrderCheckOrderRsp
	37, // 80: xian_cwsx.Notify.WarOrderDelivery:output_type -> xian_cwsx.WarOrderDeliveryRsp
	43, // 81: xian_cwsx.Notify.SafeCallback:output_type -> xian_cwsx.SafeCheckCallbackV2Rsp
	43, // 82: xian_cwsx.Notify.SafeCallbackDescribe:output_type -> xian_cwsx.SafeCheckCallbackV2Rsp
	43, // 83: xian_cwsx.Notify.SafeCallbackPin:output_type -> xian_cwsx.SafeCheckCallbackV2Rsp
	46, // 84: xian_cwsx.Notify.SafeCallbackOp:output_type -> xian_cwsx.SafeCallbackOpRsp
	48, // 85: xian_cwsx.Notify.SafeCallbackQuery:output_type -> xian_cwsx.SafeCallbackQueryRsp
	50, // 86: xian_cwsx.Notify.SuperBollRetentionPopup:output_type -> xian_cwsx.SuperBollRetentionPopupRsp
	4,  // 87: xian_cwsx.Notify.HeartFullCallback:output_type -> xian_cwsx.TimerCallbackResponse
	91, // 88: xian_cwsx.Notify.ActivityCheck:output_type -> inlet.InletStatusRsp
	4,  // 89: xian_cwsx.Notify.TeamWeekCallback:output_type -> xian_cwsx.TimerCallbackResponse
	52, // 90: xian_cwsx.Notify.CallbackStepBuy:output_type -> xian_cwsx.CallbackStepBuyRsp
	92, // 91: xian_cwsx.Notify.InletSync:output_type -> inlet.ActivityStateRsp
	93, // 92: xian_cwsx.Notify.BatchGetDecorate:output_type -> privilege.BatchGetDecorateRsp
	54, // 93: xian_cwsx.Notify.DeliverGoodsCallback:output_type -> xian_cwsx.DeliverGoodsImplementRsp
	56, // 94: xian_cwsx.Notify.DragonBoatAchi:output_type -> xian_cwsx.DragonBoatAchiRsp
	58, // 95: xian_cwsx.Notify.AchiPlant:output_type -> xian_cwsx.AchiPlantRsp
	92, // 96: xian_cwsx.Notify.VIPCheck:output_type -> inlet.ActivityStateRsp
	12, // 97: xian_cwsx.Notify.UserGuidesSubmit:output_type -> xian_cwsx.UserGuidesSubmitRsp
	10, // 98: xian_cwsx.Notify.LabaUserInfo:output_type -> xian_cwsx.LabaUserInfoRsp
	60, // 99: xian_cwsx.Notify.QueryUserTeam:output_type -> xian_cwsx.QueryUserTeamRsp
	62, // 100: xian_cwsx.Notify.InlineDecorateCheck:output_type -> xian_cwsx.InlineDecorateCheckRsp
	8,  // 101: xian_cwsx.Notify.CheckGM:output_type -> xian_cwsx.CheckGMRsp
	64, // 102: xian_cwsx.Notify.LoadLightweightAssets:output_type -> xian_cwsx.LoadLightweightAssetsRsp
	66, // 103: xian_cwsx.Notify.ReportPlantCoin:output_type -> xian_cwsx.ReportPlantCoinRsp
	68, // 104: xian_cwsx.Notify.ChangeRegionRank:output_type -> xian_cwsx.ChangeRegionRankRsp
	70, // 105: xian_cwsx.Notify.QueryTeamMembers:output_type -> xian_cwsx.QueryTeamMembersRsp
	72, // 106: xian_cwsx.Notify.BatchGetUserTeam:output_type -> xian_cwsx.BatchGetUserTeamRsp
	6,  // 107: xian_cwsx.Notify.TeamsBatch:output_type -> xian_cwsx.TeamsBatchRsp
	69, // [69:108] is the sub-list for method output_type
	30, // [30:69] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_xian_cwsx_notify_notify_proto_init() }
func file_pb_game_cwsx_xian_cwsx_notify_notify_proto_init() {
	if File_pb_game_cwsx_xian_cwsx_notify_notify_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimerCallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamsBatchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamsBatchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckGMReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckGMRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabaUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabaUserInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserGuidesSubmitReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserGuidesSubmitRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Package); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQueryCwsxPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQueryCwsxPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryStageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameDeliveryPackageRewards); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGameDeliveryPackageListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderRetentionPopupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderRetentionPopupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderCheckOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderCheckOrderRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderDeliveryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderDeliveryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCallbackRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCheckCallbackV2Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCheckCallbackV2Rsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamChange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCallbackOpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCallbackOpRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCallbackQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SafeCallbackQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuperBollRetentionPopupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuperBollRetentionPopupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackStepBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackStepBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverGoodsImplementReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverGoodsImplementRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DragonBoatAchiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DragonBoatAchiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AchiPlantReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AchiPlantRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryUserTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryUserTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InlineDecorateCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InlineDecorateCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadLightweightAssetsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadLightweightAssetsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportPlantCoinReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportPlantCoinRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeRegionRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeRegionRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryTeamMembersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryTeamMembersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetUserTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetUserTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryRsp_Product); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WarOrderDeliveryRsp_Product); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliverGoodsImplementReq_Good); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetUserTeamRsp_TeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   79,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_xian_cwsx_notify_notify_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_xian_cwsx_notify_notify_proto_depIdxs,
		EnumInfos:         file_pb_game_cwsx_xian_cwsx_notify_notify_proto_enumTypes,
		MessageInfos:      file_pb_game_cwsx_xian_cwsx_notify_notify_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_xian_cwsx_notify_notify_proto = out.File
	file_pb_game_cwsx_xian_cwsx_notify_notify_proto_rawDesc = nil
	file_pb_game_cwsx_xian_cwsx_notify_notify_proto_goTypes = nil
	file_pb_game_cwsx_xian_cwsx_notify_notify_proto_depIdxs = nil
}
