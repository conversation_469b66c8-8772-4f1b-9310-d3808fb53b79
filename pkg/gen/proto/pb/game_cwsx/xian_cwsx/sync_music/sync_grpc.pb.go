// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/xian_cwsx/sync_music/sync.proto

package sync_music

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	game "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/xian_cwsx/game"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	SyncMusic_SyncFloorMap_FullMethodName = "/xian_cwsx.SyncMusic/SyncFloorMap"
	SyncMusic_GetFloorMap_FullMethodName  = "/xian_cwsx.SyncMusic/GetFloorMap"
	SyncMusic_SyncItem_FullMethodName     = "/xian_cwsx.SyncMusic/SyncItem"
	SyncMusic_GetItem_FullMethodName      = "/xian_cwsx.SyncMusic/GetItem"
)

// SyncMusicClient is the client API for SyncMusic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SyncMusicClient interface {
	// 同步关卡地图
	SyncFloorMap(ctx context.Context, in *game.SyncFloorMapReq, opts ...grpc.CallOption) (*game.SyncFloorMapRsp, error)
	// 获取关卡地图
	GetFloorMap(ctx context.Context, in *game.GetFloorMapReq, opts ...grpc.CallOption) (*game.GetFloorMapRsp, error)
	// 同步道具数据
	SyncItem(ctx context.Context, in *game.SyncItemReq, opts ...grpc.CallOption) (*game.SyncItemRsp, error)
	// 获取道具数据
	GetItem(ctx context.Context, in *game.GetItemReq, opts ...grpc.CallOption) (*game.GetItemRsp, error)
}

type syncMusicClient struct {
	cc grpc.ClientConnInterface
}

func NewSyncMusicClient(cc grpc.ClientConnInterface) SyncMusicClient {
	return &syncMusicClient{cc}
}

func (c *syncMusicClient) SyncFloorMap(ctx context.Context, in *game.SyncFloorMapReq, opts ...grpc.CallOption) (*game.SyncFloorMapRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.SyncFloorMapRsp)
	err := c.cc.Invoke(ctx, SyncMusic_SyncFloorMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncMusicClient) GetFloorMap(ctx context.Context, in *game.GetFloorMapReq, opts ...grpc.CallOption) (*game.GetFloorMapRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.GetFloorMapRsp)
	err := c.cc.Invoke(ctx, SyncMusic_GetFloorMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncMusicClient) SyncItem(ctx context.Context, in *game.SyncItemReq, opts ...grpc.CallOption) (*game.SyncItemRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.SyncItemRsp)
	err := c.cc.Invoke(ctx, SyncMusic_SyncItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncMusicClient) GetItem(ctx context.Context, in *game.GetItemReq, opts ...grpc.CallOption) (*game.GetItemRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.GetItemRsp)
	err := c.cc.Invoke(ctx, SyncMusic_GetItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SyncMusicServer is the server API for SyncMusic service.
// All implementations should embed UnimplementedSyncMusicServer
// for forward compatibility
type SyncMusicServer interface {
	// 同步关卡地图
	SyncFloorMap(context.Context, *game.SyncFloorMapReq) (*game.SyncFloorMapRsp, error)
	// 获取关卡地图
	GetFloorMap(context.Context, *game.GetFloorMapReq) (*game.GetFloorMapRsp, error)
	// 同步道具数据
	SyncItem(context.Context, *game.SyncItemReq) (*game.SyncItemRsp, error)
	// 获取道具数据
	GetItem(context.Context, *game.GetItemReq) (*game.GetItemRsp, error)
}

// UnimplementedSyncMusicServer should be embedded to have forward compatible implementations.
type UnimplementedSyncMusicServer struct {
}

func (UnimplementedSyncMusicServer) SyncFloorMap(context.Context, *game.SyncFloorMapReq) (*game.SyncFloorMapRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFloorMap not implemented")
}
func (UnimplementedSyncMusicServer) GetFloorMap(context.Context, *game.GetFloorMapReq) (*game.GetFloorMapRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFloorMap not implemented")
}
func (UnimplementedSyncMusicServer) SyncItem(context.Context, *game.SyncItemReq) (*game.SyncItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncItem not implemented")
}
func (UnimplementedSyncMusicServer) GetItem(context.Context, *game.GetItemReq) (*game.GetItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetItem not implemented")
}

// UnsafeSyncMusicServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SyncMusicServer will
// result in compilation errors.
type UnsafeSyncMusicServer interface {
	mustEmbedUnimplementedSyncMusicServer()
}

func RegisterSyncMusicServer(s grpc.ServiceRegistrar, srv SyncMusicServer) {
	s.RegisterService(&SyncMusic_ServiceDesc, srv)
}

func _SyncMusic_SyncFloorMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.SyncFloorMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncMusicServer).SyncFloorMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncMusic_SyncFloorMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncMusicServer).SyncFloorMap(ctx, req.(*game.SyncFloorMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncMusic_GetFloorMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.GetFloorMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncMusicServer).GetFloorMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncMusic_GetFloorMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncMusicServer).GetFloorMap(ctx, req.(*game.GetFloorMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncMusic_SyncItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.SyncItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncMusicServer).SyncItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncMusic_SyncItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncMusicServer).SyncItem(ctx, req.(*game.SyncItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncMusic_GetItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.GetItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncMusicServer).GetItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncMusic_GetItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncMusicServer).GetItem(ctx, req.(*game.GetItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SyncMusic_ServiceDesc is the grpc.ServiceDesc for SyncMusic service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SyncMusic_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "xian_cwsx.SyncMusic",
	HandlerType: (*SyncMusicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncFloorMap",
			Handler:    _SyncMusic_SyncFloorMap_Handler,
		},
		{
			MethodName: "GetFloorMap",
			Handler:    _SyncMusic_GetFloorMap_Handler,
		},
		{
			MethodName: "SyncItem",
			Handler:    _SyncMusic_SyncItem_Handler,
		},
		{
			MethodName: "GetItem",
			Handler:    _SyncMusic_GetItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/xian_cwsx/sync_music/sync.proto",
}
