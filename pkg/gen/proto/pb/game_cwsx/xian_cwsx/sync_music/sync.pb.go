// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/xian_cwsx/sync_music/sync.proto

package sync_music

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	game "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/xian_cwsx/game"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_pb_game_cwsx_xian_cwsx_sync_music_sync_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6d, 0x75,
	0x73, 0x69, 0x63, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x1a, 0x2d, 0x70, 0x62, 0x2f, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x8d, 0x02, 0x0a, 0x09, 0x53, 0x79, 0x6e,
	0x63, 0x4d, 0x75, 0x73, 0x69, 0x63, 0x12, 0x46, 0x0a, 0x0c, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x6c,
	0x6f, 0x6f, 0x72, 0x4d, 0x61, 0x70, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x4d, 0x61, 0x70, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53,
	0x79, 0x6e, 0x63, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x4d, 0x61, 0x70, 0x52, 0x73, 0x70, 0x12, 0x43,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x4d, 0x61, 0x70, 0x12, 0x19, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f,
	0x6f, 0x72, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x4d, 0x61, 0x70,
	0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x79, 0x6e, 0x63,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12,
	0x37, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x15, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x47, 0x65,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x42, 0x5f, 0x5a, 0x5d, 0x67, 0x69, 0x74, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x73,
	0x79, 0x6e, 0x63, 0x5f, 0x6d, 0x75, 0x73, 0x69, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_goTypes = []interface{}{
	(*game.SyncFloorMapReq)(nil), // 0: xian_cwsx.SyncFloorMapReq
	(*game.GetFloorMapReq)(nil),  // 1: xian_cwsx.GetFloorMapReq
	(*game.SyncItemReq)(nil),     // 2: xian_cwsx.SyncItemReq
	(*game.GetItemReq)(nil),      // 3: xian_cwsx.GetItemReq
	(*game.SyncFloorMapRsp)(nil), // 4: xian_cwsx.SyncFloorMapRsp
	(*game.GetFloorMapRsp)(nil),  // 5: xian_cwsx.GetFloorMapRsp
	(*game.SyncItemRsp)(nil),     // 6: xian_cwsx.SyncItemRsp
	(*game.GetItemRsp)(nil),      // 7: xian_cwsx.GetItemRsp
}
var file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_depIdxs = []int32{
	0, // 0: xian_cwsx.SyncMusic.SyncFloorMap:input_type -> xian_cwsx.SyncFloorMapReq
	1, // 1: xian_cwsx.SyncMusic.GetFloorMap:input_type -> xian_cwsx.GetFloorMapReq
	2, // 2: xian_cwsx.SyncMusic.SyncItem:input_type -> xian_cwsx.SyncItemReq
	3, // 3: xian_cwsx.SyncMusic.GetItem:input_type -> xian_cwsx.GetItemReq
	4, // 4: xian_cwsx.SyncMusic.SyncFloorMap:output_type -> xian_cwsx.SyncFloorMapRsp
	5, // 5: xian_cwsx.SyncMusic.GetFloorMap:output_type -> xian_cwsx.GetFloorMapRsp
	6, // 6: xian_cwsx.SyncMusic.SyncItem:output_type -> xian_cwsx.SyncItemRsp
	7, // 7: xian_cwsx.SyncMusic.GetItem:output_type -> xian_cwsx.GetItemRsp
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_init() }
func file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_init() {
	if File_pb_game_cwsx_xian_cwsx_sync_music_sync_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_depIdxs,
	}.Build()
	File_pb_game_cwsx_xian_cwsx_sync_music_sync_proto = out.File
	file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_rawDesc = nil
	file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_goTypes = nil
	file_pb_game_cwsx_xian_cwsx_sync_music_sync_proto_depIdxs = nil
}
