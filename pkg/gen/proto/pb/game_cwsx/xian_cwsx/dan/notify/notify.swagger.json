{"swagger": "2.0", "info": {"title": "pb/game_cwsx/xian_cwsx/dan/notify/notify.proto", "version": "version not set"}, "tags": [{"name": "Notify"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xian_cwsx_dan.Notify/ClearDFRedPoint": {"post": {"operationId": "Notify_ClearDFRedPoint", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsx_danClearDFRedPointRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsx_danClearDFRedPointReq"}}], "tags": ["Notify"]}}, "/xian_cwsx_dan.Notify/DanFrames": {"post": {"summary": "获取段位框的id列表", "operationId": "Notify_DanFrames", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsx_danDanFramesRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsx_danDanFramesReq"}}], "tags": ["Notify"]}}, "/xian_cwsx_dan.Notify/DanState": {"post": {"summary": "段位活动的状态信息", "operationId": "Notify_DanState", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/inletActivityStateRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/inletActivityStateReq"}}], "tags": ["Notify"]}}, "/xian_cwsx_dan.Notify/UserDanBatchGet": {"post": {"summary": "批量获取用户的段位信息", "operationId": "Notify_UserDanBatchGet", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanBatchGetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanBatchGetReq"}}], "tags": ["Notify"]}}, "/xian_cwsx_dan.Notify/UserDanFrames": {"post": {"summary": "用户的段位框", "operationId": "Notify_UserDanFrames", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanFramesRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanFramesReq"}}], "tags": ["Notify"]}}, "/xian_cwsx_dan.Notify/UserDanFramesBatch": {"post": {"operationId": "Notify_UserDanFramesBatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanFramesBatchRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanFramesBatchReq"}}], "tags": ["Notify"]}}, "/xian_cwsx_dan.Notify/UserDanGet": {"post": {"summary": "获取个人的段位信息", "operationId": "Notify_UserDanGet", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanGetRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsx_danUserDanGetReq"}}], "tags": ["Notify"]}}}, "definitions": {"UserDanFramesBatchRspUDFrames": {"type": "object", "properties": {"fMap": {"type": "object", "additionalProperties": {"type": "boolean"}}}}, "inletActivityStateReq": {"type": "object", "properties": {"number": {"$ref": "#/definitions/inletInletNumber", "title": "入口活动编号=>InletNumber"}, "openId": {"type": "string", "title": "用户的open_id"}, "os": {"$ref": "#/definitions/inletPlatformType", "title": "设备类型"}, "sceneId": {"type": "integer", "format": "int32", "title": "场景ID"}, "userAgent": {"type": "string", "title": "ua"}, "appId": {"type": "string", "title": "appid"}, "needData": {"$ref": "#/definitions/inletNeedDataType", "title": "需要具体数据"}, "floor": {"type": "string", "format": "int64", "title": "玩家当前关卡"}, "cacheBindKey": {"type": "string", "title": "卢学彦 【荣耀奖牌赛】"}, "teamId": {"type": "string", "title": "战队ID"}}, "title": "ActivityStateReq 查询活动状态请求"}, "inletActivityStateRsp": {"type": "object", "properties": {"inletStatus": {"$ref": "#/definitions/inletInletStatusType", "title": "入口状态 【1.0版本弃用】"}, "nextReqTime": {"type": "string", "format": "int64", "title": "下次请求时间  -1永久保持历史状态"}, "startTime": {"type": "string", "format": "int64", "title": "活动开始时间"}, "endTime": {"type": "string", "format": "int64", "title": "活动结束时间"}, "data": {"type": "string", "format": "byte", "title": "当need_data=1时，返回具体活动的数据。"}, "round": {"type": "string", "title": "轮次 刷新记录使用"}, "buyStatus": {"$ref": "#/definitions/inletBuyType", "title": "购买状态"}, "showType": {"$ref": "#/definitions/inletManageShowType", "description": "入口展示状态【决定是否入口展示】:在nextReqTime过期后重新加载", "title": "-- 入口展示条件，下面的值拥有后，忽略上面的【 InletStatusType inlet_status = 1; // 入口状态】的状态"}, "popupType": {"$ref": "#/definitions/inletPopupType", "title": "弹窗的状态【决定弹窗的类型，在showType开启的时候才会有效】:在nextReqTime过期后重新加载"}}, "title": "ActivityStateRsp 查询活动状态响应 下次请求仅在 在need_data=1的时候不判断next_req_time。入口服务也不记载data"}, "inletBuyType": {"type": "string", "enum": ["BuyType_None", "BuyType_Yes"], "default": "BuyType_None", "description": "- BuyType_None: 未购买\n - BuyType_Yes: 已购买", "title": "BuyType 需要具体数据"}, "inletInletNumber": {"type": "string", "enum": ["InletNumber_None", "InletNumber_HonorMedalCompetition", "InletNumber_AirplaneRace", "InletNumber_SuperColorfulLights", "InletNumber_TeamRudder", "InletNumber_TeamCompetition", "InletNumber_BattlePass", "InletNumber_Fishing", "InletNumber_CuteRabbitParadise", "InletNumber_EndlessTreasures", "InletNumber_CheckIn", "InletNumber_LightingRush", "InletNumber_SpecialDiscountPackage", "InletNumber_NoviceChallengeEvent", "InletNumber_DragonsTreasure", "InletNumber_InviteFriends", "InletNumber_ShareFriends", "InletNumber_Favorite", "InletNumber_Recharge", "InletNumber_EveryDayReceive", "InletNumber_DragonBoat", "InletNumber_DailyTask", "InletNumber_Laba", "InletNumber_Vip", "InletNumber_CwsxShop", "InletNumber_GameHub", "InletNumber_Announces", "InletNumber_RescuePlants", "InletNumber_Dan", "InletNumber_Block", "InletNumber_DecPop", "InletNumber_WeekRank", "InletNumber_StarActivity", "InletNumber_PeakRaceBigRound", "InletNumber_CollectTasks", "InletNumber_PeakRaceSmallRound", "InletNumber_Announce"], "default": "InletNumber_None", "description": "- InletNumber_None: 无\n - InletNumber_HonorMedalCompetition: 荣耀奖牌赛 @卢学彦\n - InletNumber_AirplaneRace: 飞机竞赛 @作废\n - InletNumber_SuperColorfulLights: 超级彩灯 @上官冲\n - InletNumber_TeamRudder: 战队淘金 @裴晓晨\n - InletNumber_TeamCompetition: 战队竞赛  @作废\n - InletNumber_BattlePass: 战令 @上官冲\n - InletNumber_Fishing: 小猫钓鱼 @裴晓晨\n - InletNumber_CuteRabbitParadise: 萌兔乐园 @卢学彦\n - InletNumber_EndlessTreasures: 无尽宝藏 @陈航\n - InletNumber_CheckIn: 签到 @上官冲\n - InletNumber_LightingRush: 彩虹竞速 @裴晓晨\n - InletNumber_SpecialDiscountPackage: 特惠礼包 @陈航\n - InletNumber_NoviceChallengeEvent: 新手闯关活动 @陈航\n - InletNumber_DragonsTreasure: 巨龙宝藏 @陈航\n - InletNumber_InviteFriends: 邀请好友 @白龙斐\n - InletNumber_ShareFriends: 分享好友 @上官冲\n - InletNumber_Favorite: 收藏 @白龙斐\n - InletNumber_Recharge: 宠物三消充值活动 @王国栋\n - InletNumber_EveryDayReceive: 分天领取礼包 @王国栋\n - InletNumber_DragonBoat: 龙舟竞赛 @曾润良\n - InletNumber_DailyTask: 任务中心每日消除金币任务 @卢学彦\n - InletNumber_Laba: 拉霸活动@车照\n - InletNumber_Vip: VIP专有客服 @曾润良\n - InletNumber_CwsxShop: 宠物三消商城入口 @裴晓晨\n - InletNumber_GameHub: 微信游戏圈 @裴晓晨\n - InletNumber_Announces: 公告 @白龙斐\n - InletNumber_RescuePlants: 营救植物 @上官冲\n - InletNumber_Dan: 段位赛 @车照\n - InletNumber_Block: 俄罗斯方块 @裴晓晨\n - InletNumber_DecPop: ------ 游戏中的单机小游戏 从200开始-------\n\n解密消除 @白龙斐\n - InletNumber_WeekRank: -------------\n下面是仅有弹窗无的枚举 Begin\n-------------\n非入口的活动 增加枚举值， 1000 开头\n\n周排行榜 @上官冲\n - InletNumber_StarActivity: 明星活动 @裴晓晨\n - InletNumber_PeakRaceBigRound: 巅峰赛大轮次 @上官冲\n - InletNumber_CollectTasks: 收集任务 @上官冲\n - InletNumber_PeakRaceSmallRound: 巅峰赛小轮次 @上官冲\n - InletNumber_Announce: @废弃 公告由 InletNumber_Announces(26号)代替", "title": "InletNumber 增加枚举值"}, "inletInletStatusType": {"type": "string", "enum": ["InletStatusType_None", "InletStatusType_Open", "InletStatusType_OpenAndJoin", "InletStatusType_EndHaveAward", "InletStatusType_EndReceivedAward", "InletStatusType_EndNoAward", "InletStatusType_EndNoJoin", "InletStatusType_AbClose", "InletStatusType_OpenAndJoin_HasAward", "InletStatusType_Notice"], "default": "InletStatusType_None", "description": "- InletStatusType_None: 无定义，继续保持老状态\n - InletStatusType_Open: 活动开启中但未参与\n - InletStatusType_OpenAndJoin: 活动开启，玩家参与中\n - InletStatusType_EndHaveAward: 活动结束，但有奖可领：有待领取的奖品\n - InletStatusType_EndReceivedAward: 活动结束，但有奖可领：全部奖品领取完成\n - InletStatusType_EndNoAward: 活动结束，但无奖可领：参与后未达到领奖条件\n - InletStatusType_EndNoJoin: 活动结束，且一直未参与\n - InletStatusType_AbClose: Ab无法参与\n - InletStatusType_OpenAndJoin_HasAward: 活动开启，玩家参与中：有待领取的奖品[特殊活动使用]\n - InletStatusType_Notice: 预告期", "title": "InletStatusType 入口状态枚举 （活动入口加载的状态值、弹窗所用的状态值）"}, "inletManageShowType": {"type": "string", "enum": ["ManageShowType_None", "ManageShowType_Open", "ManageShowType_Close"], "default": "ManageShowType_None", "description": "- ManageShowType_None: 无定义，继续保持老状态\n - ManageShowType_Open: 入口展示\n - ManageShowType_Close: 入口关闭", "title": "ManageShowType 查询活动状态响应"}, "inletNeedDataType": {"type": "string", "enum": ["NeedDataType_None", "NeedDataType_Yes"], "default": "NeedDataType_None", "description": "- NeedDataType_None: 不需要活动具体数据，仅返回状态即可\n - NeedDataType_Yes: 需要具体数据", "title": "NeedDataType 需要具体数据"}, "inletPlatformType": {"type": "string", "enum": ["PlatformUnknown", "PlatformAndroid", "PlatformIOS"], "default": "PlatformUnknown", "title": "PlatformType 设备类型"}, "inletPopupType": {"type": "string", "enum": ["PopupType_None", "PopupType_EndNoAward", "PopupType_EndHaveAward", "PopupType_Begin", "PopupType_Buy"], "default": "PopupType_None", "description": "- PopupType_None: 无\n - PopupType_EndNoAward: 结算期间：无领奖状态\n - PopupType_EndHaveAward: 结算期间：有领奖状态\n - PopupType_Begin: 活动开启弹窗\n - PopupType_Buy: 购买类型:展示时间", "title": "弹窗的类型 弹窗的类型状态 弹窗顺序由编号升序决策"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "xian_cwsx_danClearDFRedPointReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}, "frameIds": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "xian_cwsx_danClearDFRedPointRsp": {"type": "object"}, "xian_cwsx_danDanFrame": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "isNew": {"type": "boolean"}, "ts": {"type": "string", "format": "int64", "title": "获取时间"}}}, "xian_cwsx_danDanFramesReq": {"type": "object", "properties": {"appId": {"type": "string"}, "needSelf": {"type": "boolean", "title": "是否需要自己的可用段位框map, 为ture时需要传openId"}, "openId": {"type": "string"}}}, "xian_cwsx_danDanFramesRsp": {"type": "object", "properties": {"dfMap": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "所有的段位框的map"}, "userDfMap": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "用户可用的段位框的map"}}}, "xian_cwsx_danDanInfo": {"type": "object", "properties": {"dan": {"type": "integer", "format": "int32"}, "cd": {"type": "integer", "format": "int32"}}}, "xian_cwsx_danUserDanBatchGetReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openIds": {"type": "array", "items": {"type": "string"}}}}, "xian_cwsx_danUserDanBatchGetRsp": {"type": "object", "properties": {"udMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/xian_cwsx_danDanInfo"}}}}, "xian_cwsx_danUserDanFramesBatchReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openIds": {"type": "array", "items": {"type": "string"}}}}, "xian_cwsx_danUserDanFramesBatchRsp": {"type": "object", "properties": {"uMap": {"type": "object", "additionalProperties": {"$ref": "#/definitions/UserDanFramesBatchRspUDFrames"}}}}, "xian_cwsx_danUserDanFramesReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsx_danUserDanFramesRsp": {"type": "object", "properties": {"dfs": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsx_danDanFrame"}}}}, "xian_cwsx_danUserDanGetReq": {"type": "object", "properties": {"appId": {"type": "string"}, "openId": {"type": "string"}}}, "xian_cwsx_danUserDanGetRsp": {"type": "object", "properties": {"danInfo": {"$ref": "#/definitions/xian_cwsx_danDanInfo"}}}}}