// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/xian_cwsx/dan/api/api.proto

package api

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundId int64 `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"` // 周期id, 配置是跟周期绑定的, 所以需要传这个
}

func (x *ConfigReq) Reset() {
	*x = ConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigReq) ProtoMessage() {}

func (x *ConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigReq.ProtoReflect.Descriptor instead.
func (*ConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigReq) GetRoundId() int64 {
	if x != nil {
		return x.RoundId
	}
	return 0
}

type DanCfg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dan   int32  `protobuf:"varint,1,opt,name=dan,proto3" json:"dan,omitempty"`     // 后端段位id
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`    // 段位名称
	Cd    int32  `protobuf:"varint,3,opt,name=cd,proto3" json:"cd,omitempty"`       // 前端段位id, client_dan
	Frame int32  `protobuf:"varint,4,opt,name=frame,proto3" json:"frame,omitempty"` // 头像框id
}

func (x *DanCfg) Reset() {
	*x = DanCfg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DanCfg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DanCfg) ProtoMessage() {}

func (x *DanCfg) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DanCfg.ProtoReflect.Descriptor instead.
func (*DanCfg) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{1}
}

func (x *DanCfg) GetDan() int32 {
	if x != nil {
		return x.Dan
	}
	return 0
}

func (x *DanCfg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DanCfg) GetCd() int32 {
	if x != nil {
		return x.Cd
	}
	return 0
}

func (x *DanCfg) GetFrame() int32 {
	if x != nil {
		return x.Frame
	}
	return 0
}

type ConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DanMap map[int32]*DanCfg `protobuf:"bytes,1,rep,name=dan_map,json=danMap,proto3" json:"dan_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ConfigRsp) Reset() {
	*x = ConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigRsp) ProtoMessage() {}

func (x *ConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigRsp.ProtoReflect.Descriptor instead.
func (*ConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{2}
}

func (x *ConfigRsp) GetDanMap() map[int32]*DanCfg {
	if x != nil {
		return x.DanMap
	}
	return nil
}

type DanRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundId  int64 `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`    // 周期id, 必传
	Passback int32 `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"`                 // 透传字段, 初始为0, 之后透传rsp中返回的passback
	OnlyRank bool  `protobuf:"varint,3,opt,name=only_rank,json=onlyRank,proto3" json:"only_rank,omitempty"` // 仅排行榜信息, 为true时返回rsp中的所有信息, 为false时不返回 lr_map 和 up_down 等重复信息 (考虑到自己的排名会变, 所以self信息一直返回)
}

func (x *DanRankReq) Reset() {
	*x = DanRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DanRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DanRankReq) ProtoMessage() {}

func (x *DanRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DanRankReq.ProtoReflect.Descriptor instead.
func (*DanRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{3}
}

func (x *DanRankReq) GetRoundId() int64 {
	if x != nil {
		return x.RoundId
	}
	return 0
}

func (x *DanRankReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *DanRankReq) GetOnlyRank() bool {
	if x != nil {
		return x.OnlyRank
	}
	return false
}

type DanRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RankList []*DanRankItem     `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`                                                                                 // 排行榜的榜单
	Self     *DanRankItem       `protobuf:"bytes,2,opt,name=self,proto3" json:"self,omitempty"`                                                                                                         // 自己的信息
	HasNext  bool               `protobuf:"varint,3,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`                                                                                   // 是否还有下一页
	Passback int32              `protobuf:"varint,4,opt,name=passback,proto3" json:"passback,omitempty"`                                                                                                // 继续翻页的透传参数
	LrMap    map[int32]*Rewards `protobuf:"bytes,5,rep,name=lr_map,json=lrMap,proto3" json:"lr_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // level和rewards的映射
	RankDans []*RankDan         `protobuf:"bytes,6,rep,name=rank_dans,json=rankDans,proto3" json:"rank_dans,omitempty"`                                                                                 // 排行榜升降情况
}

func (x *DanRankRsp) Reset() {
	*x = DanRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DanRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DanRankRsp) ProtoMessage() {}

func (x *DanRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DanRankRsp.ProtoReflect.Descriptor instead.
func (*DanRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{4}
}

func (x *DanRankRsp) GetRankList() []*DanRankItem {
	if x != nil {
		return x.RankList
	}
	return nil
}

func (x *DanRankRsp) GetSelf() *DanRankItem {
	if x != nil {
		return x.Self
	}
	return nil
}

func (x *DanRankRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *DanRankRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *DanRankRsp) GetLrMap() map[int32]*Rewards {
	if x != nil {
		return x.LrMap
	}
	return nil
}

func (x *DanRankRsp) GetRankDans() []*RankDan {
	if x != nil {
		return x.RankDans
	}
	return nil
}

type RankDan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank int32 `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"` //
	Dan  int32 `protobuf:"varint,2,opt,name=Dan,proto3" json:"Dan,omitempty"`   // 结算目标段位
}

func (x *RankDan) Reset() {
	*x = RankDan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankDan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankDan) ProtoMessage() {}

func (x *RankDan) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankDan.ProtoReflect.Descriptor instead.
func (*RankDan) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{5}
}

func (x *RankDan) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RankDan) GetDan() int32 {
	if x != nil {
		return x.Dan
	}
	return 0
}

type Rewards struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards []*Reward `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"`
}

func (x *Rewards) Reset() {
	*x = Rewards{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rewards) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rewards) ProtoMessage() {}

func (x *Rewards) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rewards.ProtoReflect.Descriptor instead.
func (*Rewards) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{6}
}

func (x *Rewards) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                    // 奖励id
	Qua    int64  `protobuf:"varint,2,opt,name=qua,proto3" json:"qua,omitempty"`                  // 奖励数量
	Img    string `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`                   // 图片
	Name   string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                 // 昵称
	RType  int32  `protobuf:"varint,5,opt,name=r_type,json=rType,proto3" json:"r_type,omitempty"` // 奖励类型
	Price  int32  `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"`              // 价值
	Remark string `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark,omitempty"`             //备注
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{7}
}

func (x *Reward) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Reward) GetQua() int64 {
	if x != nil {
		return x.Qua
	}
	return 0
}

func (x *Reward) GetImg() string {
	if x != nil {
		return x.Img
	}
	return ""
}

func (x *Reward) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Reward) GetRType() int32 {
	if x != nil {
		return x.RType
	}
	return 0
}

func (x *Reward) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Reward) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type DanRankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank        int32  `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`                                  // 排名
	OpenId      string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`                 // 玩家openId
	Name        string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                   // 玩家昵称
	Avatar      string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`                               // 玩家头像
	Gender      int32  `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`                              // 玩家性别
	Score       int64  `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`                                // 玩家积分, 一级排序参数
	RefreshTime int64  `protobuf:"varint,7,opt,name=refresh_time,json=refreshTime,proto3" json:"refresh_time,omitempty"` // 刷新时间, 二级排序参数
	Level       int32  `protobuf:"varint,8,opt,name=level,proto3" json:"level,omitempty"`                                // 奖励级别, 根据这个从 lr_map中获取到奖励内容, 也根据这个展示前端的宝箱类型
	Frame       *Frame `protobuf:"bytes,9,opt,name=frame,proto3" json:"frame,omitempty"`                                 // 头像框
	TeamName    string `protobuf:"bytes,10,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`          // 战队昵称
}

func (x *DanRankItem) Reset() {
	*x = DanRankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DanRankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DanRankItem) ProtoMessage() {}

func (x *DanRankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DanRankItem.ProtoReflect.Descriptor instead.
func (*DanRankItem) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{8}
}

func (x *DanRankItem) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *DanRankItem) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *DanRankItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DanRankItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *DanRankItem) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *DanRankItem) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *DanRankItem) GetRefreshTime() int64 {
	if x != nil {
		return x.RefreshTime
	}
	return 0
}

func (x *DanRankItem) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *DanRankItem) GetFrame() *Frame {
	if x != nil {
		return x.Frame
	}
	return nil
}

func (x *DanRankItem) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

type SettleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundId int64 `protobuf:"varint,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
}

func (x *SettleReq) Reset() {
	*x = SettleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleReq) ProtoMessage() {}

func (x *SettleReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleReq.ProtoReflect.Descriptor instead.
func (*SettleReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{9}
}

func (x *SettleReq) GetRoundId() int64 {
	if x != nil {
		return x.RoundId
	}
	return 0
}

type SettleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rank     int32           `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`                        // 小组排名
	NewDan   int32           `protobuf:"varint,2,opt,name=new_dan,json=newDan,proto3" json:"new_dan,omitempty"`      // 新的段位
	Rewards  []*Reward       `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`                   // 获得的奖励, 可能没有
	CardList []*TreasureCard `protobuf:"bytes,4,rep,name=card_list,json=cardList,proto3" json:"card_list,omitempty"` // 下发道具的宝藏奖励
}

func (x *SettleRsp) Reset() {
	*x = SettleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SettleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SettleRsp) ProtoMessage() {}

func (x *SettleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SettleRsp.ProtoReflect.Descriptor instead.
func (*SettleRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{10}
}

func (x *SettleRsp) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *SettleRsp) GetNewDan() int32 {
	if x != nil {
		return x.NewDan
	}
	return 0
}

func (x *SettleRsp) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *SettleRsp) GetCardList() []*TreasureCard {
	if x != nil {
		return x.CardList
	}
	return nil
}

type Frame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CwsxPlg_1 string `protobuf:"bytes,1,opt,name=cwsx_plg_1,proto3" json:"cwsx_plg_1,omitempty"`
	CwsxPlg_2 string `protobuf:"bytes,2,opt,name=cwsx_plg_2,proto3" json:"cwsx_plg_2,omitempty"`
}

func (x *Frame) Reset() {
	*x = Frame{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Frame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Frame) ProtoMessage() {}

func (x *Frame) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Frame.ProtoReflect.Descriptor instead.
func (*Frame) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{11}
}

func (x *Frame) GetCwsxPlg_1() string {
	if x != nil {
		return x.CwsxPlg_1
	}
	return ""
}

func (x *Frame) GetCwsxPlg_2() string {
	if x != nil {
		return x.CwsxPlg_2
	}
	return ""
}

type TreasureCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TreasureCardId uint32 `protobuf:"varint,1,opt,name=treasureCardId,proto3" json:"treasureCardId,omitempty"` // 宝藏卡id
	IsDecompose    bool   `protobuf:"varint,2,opt,name=isDecompose,proto3" json:"isDecompose,omitempty"`       // 是否被分
	DecomposeNum   uint32 `protobuf:"varint,3,opt,name=decomposeNum,proto3" json:"decomposeNum,omitempty"`     // 分解出多少碎片
}

func (x *TreasureCard) Reset() {
	*x = TreasureCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreasureCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreasureCard) ProtoMessage() {}

func (x *TreasureCard) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreasureCard.ProtoReflect.Descriptor instead.
func (*TreasureCard) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP(), []int{12}
}

func (x *TreasureCard) GetTreasureCardId() uint32 {
	if x != nil {
		return x.TreasureCardId
	}
	return 0
}

func (x *TreasureCard) GetIsDecompose() bool {
	if x != nil {
		return x.IsDecompose
	}
	return false
}

func (x *TreasureCard) GetDecomposeNum() uint32 {
	if x != nil {
		return x.DecomposeNum
	}
	return 0
}

var File_pb_game_cwsx_xian_cwsx_dan_api_api_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDesc = []byte{
	0x0a, 0x28, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x64, 0x61, 0x6e, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x22, 0x26, 0x0a, 0x09, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x22, 0x54, 0x0a, 0x06, 0x44, 0x61, 0x6e, 0x43, 0x66, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x64,
	0x61, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x61, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x63,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x07, 0x64, 0x61, 0x6e, 0x5f, 0x6d, 0x61, 0x70,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70,
	0x2e, 0x44, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x64, 0x61,
	0x6e, 0x4d, 0x61, 0x70, 0x1a, 0x50, 0x0a, 0x0b, 0x44, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x44, 0x61, 0x6e, 0x43, 0x66, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x60, 0x0a, 0x0a, 0x44, 0x61, 0x6e, 0x52, 0x61, 0x6e,
	0x6b, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x6f,
	0x6e, 0x6c, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x6f, 0x6e, 0x6c, 0x79, 0x52, 0x61, 0x6e, 0x6b, 0x22, 0xf0, 0x02, 0x0a, 0x0a, 0x44, 0x61, 0x6e,
	0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x44, 0x61, 0x6e, 0x52, 0x61,
	0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x2e, 0x0a, 0x04, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x44,
	0x61, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x73, 0x65, 0x6c, 0x66,
	0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3b, 0x0a, 0x06, 0x6c, 0x72, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x44, 0x61, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x73, 0x70, 0x2e, 0x4c, 0x72, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x6c,
	0x72, 0x4d, 0x61, 0x70, 0x12, 0x33, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x64, 0x61, 0x6e,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x44, 0x61, 0x6e, 0x52,
	0x08, 0x72, 0x61, 0x6e, 0x6b, 0x44, 0x61, 0x6e, 0x73, 0x1a, 0x50, 0x0a, 0x0a, 0x4c, 0x72, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2f, 0x0a, 0x07, 0x52,
	0x61, 0x6e, 0x6b, 0x44, 0x61, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x44, 0x61,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x44, 0x61, 0x6e, 0x22, 0x3a, 0x0a, 0x07,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x71, 0x75, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x71, 0x75, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x6d, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x69, 0x6d, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x22, 0x96, 0x02, 0x0a, 0x0b, 0x44, 0x61, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x72, 0x61, 0x6e, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x2a, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x26, 0x0a, 0x09, 0x53, 0x65, 0x74,
	0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x64, 0x22, 0xa3, 0x01, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72,
	0x61, 0x6e, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x65, 0x77, 0x5f, 0x64, 0x61, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x65, 0x77, 0x44, 0x61, 0x6e, 0x12, 0x2f, 0x0a, 0x07,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x38, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e,
	0x2e, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x08, 0x63,
	0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x47, 0x0a, 0x05, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x70, 0x6c, 0x67, 0x5f, 0x31, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x70, 0x6c, 0x67, 0x5f, 0x31,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x70, 0x6c, 0x67, 0x5f, 0x32, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x70, 0x6c, 0x67, 0x5f, 0x32,
	0x22, 0x7c, 0x0a, 0x0c, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64,
	0x12, 0x26, 0x0a, 0x0e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x72, 0x65, 0x61, 0x73, 0x75,
	0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x44, 0x65,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69,
	0x73, 0x44, 0x65, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x64, 0x65,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x64, 0x65, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x65, 0x4e, 0x75, 0x6d, 0x32, 0xc2,
	0x01, 0x0a, 0x03, 0x41, 0x70, 0x69, 0x12, 0x3c, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x07, 0x44, 0x61, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x12,
	0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e,
	0x44, 0x61, 0x6e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x44, 0x61, 0x6e, 0x52, 0x61,
	0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x06, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x12,
	0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e,
	0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x5f, 0x64, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65,
	0x52, 0x73, 0x70, 0x42, 0x5c, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x64, 0x61, 0x6e, 0x2f, 0x61, 0x70,
	0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescData = file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDesc
)

func file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescData)
	})
	return file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDescData
}

var file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_goTypes = []interface{}{
	(*ConfigReq)(nil),    // 0: xian_cwsx_dan.ConfigReq
	(*DanCfg)(nil),       // 1: xian_cwsx_dan.DanCfg
	(*ConfigRsp)(nil),    // 2: xian_cwsx_dan.ConfigRsp
	(*DanRankReq)(nil),   // 3: xian_cwsx_dan.DanRankReq
	(*DanRankRsp)(nil),   // 4: xian_cwsx_dan.DanRankRsp
	(*RankDan)(nil),      // 5: xian_cwsx_dan.RankDan
	(*Rewards)(nil),      // 6: xian_cwsx_dan.Rewards
	(*Reward)(nil),       // 7: xian_cwsx_dan.Reward
	(*DanRankItem)(nil),  // 8: xian_cwsx_dan.DanRankItem
	(*SettleReq)(nil),    // 9: xian_cwsx_dan.SettleReq
	(*SettleRsp)(nil),    // 10: xian_cwsx_dan.SettleRsp
	(*Frame)(nil),        // 11: xian_cwsx_dan.Frame
	(*TreasureCard)(nil), // 12: xian_cwsx_dan.TreasureCard
	nil,                  // 13: xian_cwsx_dan.ConfigRsp.DanMapEntry
	nil,                  // 14: xian_cwsx_dan.DanRankRsp.LrMapEntry
}
var file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_depIdxs = []int32{
	13, // 0: xian_cwsx_dan.ConfigRsp.dan_map:type_name -> xian_cwsx_dan.ConfigRsp.DanMapEntry
	8,  // 1: xian_cwsx_dan.DanRankRsp.rank_list:type_name -> xian_cwsx_dan.DanRankItem
	8,  // 2: xian_cwsx_dan.DanRankRsp.self:type_name -> xian_cwsx_dan.DanRankItem
	14, // 3: xian_cwsx_dan.DanRankRsp.lr_map:type_name -> xian_cwsx_dan.DanRankRsp.LrMapEntry
	5,  // 4: xian_cwsx_dan.DanRankRsp.rank_dans:type_name -> xian_cwsx_dan.RankDan
	7,  // 5: xian_cwsx_dan.Rewards.rewards:type_name -> xian_cwsx_dan.Reward
	11, // 6: xian_cwsx_dan.DanRankItem.frame:type_name -> xian_cwsx_dan.Frame
	7,  // 7: xian_cwsx_dan.SettleRsp.rewards:type_name -> xian_cwsx_dan.Reward
	12, // 8: xian_cwsx_dan.SettleRsp.card_list:type_name -> xian_cwsx_dan.TreasureCard
	1,  // 9: xian_cwsx_dan.ConfigRsp.DanMapEntry.value:type_name -> xian_cwsx_dan.DanCfg
	6,  // 10: xian_cwsx_dan.DanRankRsp.LrMapEntry.value:type_name -> xian_cwsx_dan.Rewards
	0,  // 11: xian_cwsx_dan.Api.Config:input_type -> xian_cwsx_dan.ConfigReq
	3,  // 12: xian_cwsx_dan.Api.DanRank:input_type -> xian_cwsx_dan.DanRankReq
	9,  // 13: xian_cwsx_dan.Api.Settle:input_type -> xian_cwsx_dan.SettleReq
	2,  // 14: xian_cwsx_dan.Api.Config:output_type -> xian_cwsx_dan.ConfigRsp
	4,  // 15: xian_cwsx_dan.Api.DanRank:output_type -> xian_cwsx_dan.DanRankRsp
	10, // 16: xian_cwsx_dan.Api.Settle:output_type -> xian_cwsx_dan.SettleRsp
	14, // [14:17] is the sub-list for method output_type
	11, // [11:14] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_init() }
func file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_init() {
	if File_pb_game_cwsx_xian_cwsx_dan_api_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DanCfg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DanRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DanRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankDan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Rewards); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DanRankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SettleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Frame); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreasureCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_depIdxs,
		MessageInfos:      file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_xian_cwsx_dan_api_api_proto = out.File
	file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_rawDesc = nil
	file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_goTypes = nil
	file_pb_game_cwsx_xian_cwsx_dan_api_api_proto_depIdxs = nil
}
