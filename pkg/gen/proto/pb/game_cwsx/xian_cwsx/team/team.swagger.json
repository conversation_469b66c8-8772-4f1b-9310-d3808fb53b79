{"swagger": "2.0", "info": {"title": "pb/game_cwsx/xian_cwsx/team/team.proto", "version": "version not set"}, "tags": [{"name": "Team"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xian_cwsx.Team/ApplyJoinTeam": {"post": {"summary": "申请加入战队 --添加到战队审核", "operationId": "Team_ApplyJoinTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxApplyJoinTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxApplyJoinTeamReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/ApplyList": {"post": {"summary": "战队申请列表", "operationId": "Team_ApplyList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxApplyListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxApplyListReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/ApprovalToJoin": {"post": {"summary": "审批加入", "operationId": "Team_ApprovalToJoin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxApprovalToJoinRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxApprovalToJoinReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/AutoJoinTeam": {"post": {"summary": "通过邀请页加入\n rpc TeamJoinByInvite(TeamJoinByInviteReq) returns (TeamJoinByInviteRsp);\ntodo 特殊白单自动踢玩家出战队功能 测完在上\n rpc AutomaticKick(AutomaticKickReq) returns (AutomaticKickRsp);\n自动加入推荐列表第一个(如果是无需审核的战队)", "operationId": "Team_AutoJoinTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxAutoJoinTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxAutoJoinTeamReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/BegHeart": {"post": {"summary": "战队聊天发送\n求体力", "operationId": "Team_<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxBegHeartRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxBegHeartReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/CreateTeamToAudit": {"post": {"summary": "--------战队相关--------\n创建战队-发到中台审核 -- 是否有等级限制", "operationId": "Team_CreateTeamToAudit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxCreateTeamToAuditRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxCreateTeamToAuditReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/HelpHeart": {"post": {"summary": "助力体力", "operationId": "Team_HelpHeart", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxHelpHeartRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxHelpHeartReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/SearchTeam": {"post": {"summary": "搜索战队 --模糊查询", "operationId": "Team_SearchTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxSearchTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxSearchTeamReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamAvatarReviewStatus": {"post": {"summary": "查询头像审核状态", "operationId": "Team_TeamAvatarReviewStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamAvatarReviewStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamAvatarReviewStatusReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamCanReciveNum": {"post": {"summary": "领取战队赠送体力\n rpc DrawTeamHeart(DrawTeamHeartReq) returns (DrawTeamHeartRsp);\n获取可领取体力战友列表\n rpc TeamCanReciveList(TeamCanReciveListReq) returns (TeamCanReciveListRsp);\n获取战队求体力次数/可送次数", "operationId": "Team_TeamCanReciveNum", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamCanReciveNumRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamCanReciveNumReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamConfig": {"post": {"summary": "战队配置", "operationId": "Team_TeamConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamConfigRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamConfigReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamEditInfo": {"post": {"summary": "升降队员权限\n rpc TeamChangePermissions(TeamChangePermissionsReq) returns (TeamChangePermissionsRsp);\n编辑战队信息", "operationId": "Team_TeamEditInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamEditInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamEditInfoReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamInvite": {"post": {"summary": "发起邀请", "operationId": "Team_TeamInvite", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamInviteRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamInviteReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamInviteInfo": {"post": {"summary": "查询邀请信息", "operationId": "Team_TeamInviteInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamInviteInfoRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamInviteInfoReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamLeave": {"post": {"summary": "离开战队 --自己主动离开", "operationId": "Team_TeamLeave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamLeaveRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamLeaveReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamList": {"post": {"summary": "战队一览", "operationId": "Team_TeamList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamListReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamMessageList": {"post": {"summary": "抢n\n rpc TeamNRank(TeamNRankReq) returns (TeamNRankRsp);\n   战队消息", "operationId": "Team_TeamMessageList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamMessageListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamMessageListReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamMessageListInitial": {"post": {"summary": "战队消息初始调用", "operationId": "Team_TeamMessageListInitial", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamMessageListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamMessageListReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamMessageListOptimize": {"post": {"summary": "战队消息新接口", "operationId": "Team_TeamMessageListOptimize", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamMessageListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamMessageListReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamOut": {"post": {"summary": "踢出工会 --队长或管理员踢人", "operationId": "Team_TeamOut", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamOutRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamOutReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamRank": {"post": {"summary": "购买战队福利\n rpc TeamBuyWelfare(TeamBuyWelfareReq) returns (TeamBuyWelfareRsp);\n 战队排行榜", "operationId": "Team_TeamRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamRankReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamUserRank": {"post": {"summary": "战队成员排行榜", "operationId": "Team_TeamUserRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamUserRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamUserRankReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamWeekAwardCheck": {"post": {"summary": "战队周榜奖励状态检查 --检查是否可以弹奖励了", "operationId": "Team_TeamWeekAwardCheck", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamWeekAwardCheckRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamWeekAwardCheckReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamWeekRank": {"post": {"summary": "战队周榜查看(当前周)", "operationId": "Team_TeamWeekRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamWeekRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamWeekRankReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/TeamWeekUserRank": {"post": {"summary": "战队成员周贡献榜查看(当前周)", "operationId": "Team_TeamWeekUserRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxTeamWeekUserRankRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxTeamWeekUserRankReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/ViewTeam": {"post": {"summary": "查看战队信息", "operationId": "Team_ViewTeam", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxViewTeamRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxViewTeamReq"}}], "tags": ["Team"]}}, "/xian_cwsx.Team/ViewTeamBase": {"post": {"summary": "查看战队基础信息", "operationId": "Team_ViewTeamBase", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxViewTeamBaseRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxViewTeamBaseReq"}}], "tags": ["Team"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "xian_cwsxApplyJoinTeamReq": {"type": "object", "properties": {"teamId": {"type": "string", "title": "战队唯一id"}, "memberId": {"type": "string", "title": "申请加入者id"}}}, "xian_cwsxApplyJoinTeamRsp": {"type": "object", "title": "如果申请的战队有审核开关,这个成功只表示进入审核列表"}, "xian_cwsxApplyListReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32"}}}, "xian_cwsxApplyListRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxApplyMember"}}, "passback": {"type": "integer", "format": "int32"}, "hasNext": {"type": "boolean"}}}, "xian_cwsxApplyMember": {"type": "object", "properties": {"openId": {"type": "string"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "maxFloor": {"type": "integer", "format": "int64", "title": "当前关卡"}, "score": {"type": "integer", "format": "int64", "title": "巅峰赛积分"}, "applyTime": {"type": "string", "format": "int64", "title": "申请的时间"}, "encryptUid": {"type": "string", "title": "加密uin"}, "friendPetId": {"type": "string", "format": "int64", "title": "此人宠物id"}}, "title": "申请成员信息"}, "xian_cwsxApprovalToJoinReq": {"type": "object", "properties": {"openId": {"type": "string", "title": "审批加入的玩家openid"}, "agree": {"type": "boolean", "title": "true 同意"}}}, "xian_cwsxApprovalToJoinRsp": {"type": "object", "properties": {"player": {"$ref": "#/definitions/xian_cwsxTeamPlayer"}, "weekPlayer": {"$ref": "#/definitions/xian_cwsxTeamWeekPlayer"}}, "title": "加入成功，客户端自动塞入战队普通玩家列表"}, "xian_cwsxAutoJoinTeamReq": {"type": "object"}, "xian_cwsxAutoJoinTeamRsp": {"type": "object", "properties": {"teamId": {"type": "string", "title": "如果加入成功，这里返回加入的id"}, "setAvatar": {"type": "string", "title": "战队设置的头像"}, "name": {"type": "string", "title": "战队名字"}, "describe": {"type": "string", "title": "战队描述"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "unlockFloor": {"type": "integer", "format": "int32", "title": "加入关卡必须打过的关卡"}, "infiniteItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxInfiniteItem"}, "title": "无限道具及过期时间"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamPlayer"}}, "passback": {"type": "integer", "format": "int32"}, "hasNext": {"type": "boolean"}, "score": {"type": "string", "format": "int64", "title": "战队总分"}, "activeLevel": {"type": "integer", "format": "int64", "title": "战队活跃等级"}, "memberNum": {"type": "integer", "format": "int64", "title": "战队成员数量"}, "isLeader": {"type": "boolean"}, "state": {"$ref": "#/definitions/xian_cwsxMemberState"}, "editCount": {"type": "integer", "format": "int64", "title": "改名剩余次数"}, "contributeScore": {"type": "integer", "format": "int64", "title": "战队贡献分"}}}, "xian_cwsxBegHeartReq": {"type": "object"}, "xian_cwsxBegHeartRsp": {"type": "object"}, "xian_cwsxCreateTeamToAuditReq": {"type": "object", "properties": {"setAvatar": {"type": "string", "title": "玩家自己设置图片的url 预制也是url"}, "name": {"type": "string", "title": "战队名字"}, "describe": {"type": "string", "title": "战队描述"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "unlockFloor": {"type": "integer", "format": "int64", "title": "加入关卡必须打过的关卡"}, "device": {"$ref": "#/definitions/xian_cwsxDeviceInfo", "title": "设备信息, 有的尽量填"}}}, "xian_cwsxCreateTeamToAuditRsp": {"type": "object", "properties": {"teamId": {"type": "string", "title": "唯一id"}}}, "xian_cwsxDeviceInfo": {"type": "object", "properties": {"ip": {"type": "string"}, "mac": {"type": "string"}, "imei": {"type": "string"}, "idfa": {"type": "string"}, "idfv": {"type": "string"}, "mobileFlag": {"type": "integer", "format": "int64", "title": "是否来自手机"}, "mobleQUA": {"type": "string", "title": "qua"}, "uuid": {"type": "string"}, "udid": {"type": "string"}, "qimei36": {"type": "string"}, "deviceInfo": {"type": "string"}}}, "xian_cwsxHelpHeartReq": {"type": "object", "properties": {"memberId": {"type": "string"}}}, "xian_cwsxHelpHeartRsp": {"type": "object"}, "xian_cwsxInfiniteItem": {"type": "object", "properties": {"assetId": {"type": "integer", "format": "int64"}, "expireTime": {"type": "integer", "format": "int64", "title": "无限道具到期时间"}}}, "xian_cwsxMemberState": {"type": "string", "enum": ["MemberStateUnknown", "MemberStateMember", "MemberStateApply"], "default": "MemberStateUnknown", "title": "- MemberStateUnknown: 未加入\n - MemberStateMember: 成员/队长\n - MemberStateApply: 在审批列表"}, "xian_cwsxMessageType": {"type": "string", "enum": ["MessageTypeUnknown", "MessageTypeHeart", "MessageTypeEntry", "MessageTypeLeave", "MessageTypeKick", "MessageTypeRefuse", "MessageTypeAgree", "MessageTypeLeader", "MessageTypeHelp"], "default": "MessageTypeUnknown", "title": "- MessageTypeHeart: 体力\n - MessageTypeEntry: 加入消息\n - MessageTypeLeave: 退出消息\n - MessageTypeKick: 踢人消息\n - MessageTypeRefuse: 拒绝人消息\n - MessageTypeAgree: 同意人消息\n - MessageTypeLeader: 成为队长\n - MessageTypeHelp: 助力消息"}, "xian_cwsxReward": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "title": "奖励ID"}, "qua": {"type": "string", "format": "int64", "title": "奖励数量"}}}, "xian_cwsxSearchTeamReq": {"type": "object", "properties": {"name": {"type": "string"}}}, "xian_cwsxSearchTeamRsp": {"type": "object", "properties": {"teamList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamInfo"}}}}, "xian_cwsxTeamAvatarReviewStatusReq": {"type": "object"}, "xian_cwsxTeamAvatarReviewStatusRsp": {"type": "object", "properties": {"isAudit": {"type": "boolean", "title": "是否处于审核中"}, "avatar": {"type": "string", "title": "需要显示的头像"}}}, "xian_cwsxTeamCanReciveNumReq": {"type": "object"}, "xian_cwsxTeamCanReciveNumRsp": {"type": "object", "properties": {"num": {"type": "integer", "format": "int64", "title": "战队求的次数"}, "canHelpNum": {"type": "integer", "format": "int64", "title": "可助力次数"}}}, "xian_cwsxTeamConfigReq": {"type": "object", "properties": {"version": {"type": "integer", "format": "int64"}}}, "xian_cwsxTeamConfigRsp": {"type": "object", "properties": {"version": {"type": "integer", "format": "int64"}, "data": {"type": "string"}}}, "xian_cwsxTeamEditInfoReq": {"type": "object", "properties": {"setAvatar": {"type": "string", "title": "头像"}, "describe": {"type": "string", "title": "描述"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "unlockFloor": {"type": "integer", "format": "int64", "title": "加入关卡必须打过的关卡"}, "device": {"$ref": "#/definitions/xian_cwsxDeviceInfo", "title": "设备信息, 有的尽量填"}, "name": {"type": "string", "title": "战队名字"}}}, "xian_cwsxTeamEditInfoRsp": {"type": "object", "properties": {"editCount": {"type": "integer", "format": "int64", "title": "改名剩余次数"}}}, "xian_cwsxTeamInfo": {"type": "object", "properties": {"teamId": {"type": "string", "title": "战队唯一id"}, "setAvatar": {"type": "string", "title": "玩家自己设置图片的url 预制id也是url填入"}, "name": {"type": "string", "title": "战队名字"}, "membersNumber": {"type": "integer", "format": "int64", "title": "人员数量"}, "infiniteItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxInfiniteItem"}, "title": "无限道具及过期时间"}, "activeLevel": {"type": "integer", "format": "int64", "title": "活跃等级"}, "score": {"type": "integer", "format": "int64"}, "describe": {"type": "string"}, "isApply": {"type": "boolean", "title": "是否已申请"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "lowestLevel": {"type": "integer", "format": "int64", "title": "进关条件"}}}, "xian_cwsxTeamInviteInfoReq": {"type": "object", "properties": {"uniqueId": {"type": "string", "title": "本次邀请唯一id"}}}, "xian_cwsxTeamInviteInfoRsp": {"type": "object", "properties": {"openId": {"type": "string", "title": "邀请者id"}, "position": {"type": "integer", "format": "int64", "title": "邀请者当时职位 0 队长 其他非队长"}, "avatar": {"type": "string", "title": "邀请者头像"}, "name": {"type": "string", "title": "邀请者呢称"}, "teamId": {"type": "string", "title": "邀请时的战队id"}}}, "xian_cwsxTeamInviteReq": {"type": "object"}, "xian_cwsxTeamInviteRsp": {"type": "object", "properties": {"uniqueId": {"type": "string", "title": "本次邀请唯一id"}}}, "xian_cwsxTeamLeaveReq": {"type": "object"}, "xian_cwsxTeamLeaveRsp": {"type": "object"}, "xian_cwsxTeamListReq": {"type": "object"}, "xian_cwsxTeamListRsp": {"type": "object", "properties": {"teamList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamInfo"}}}}, "xian_cwsxTeamMessage": {"type": "object", "properties": {"isAdmin": {"type": "boolean", "title": "发送者是否系统"}, "openId": {"type": "string", "title": "发送者id"}, "avatar": {"type": "string", "title": "发送者头像"}, "name": {"type": "string", "title": "发送者名字"}, "toId": {"type": "string"}, "toName": {"type": "string"}, "messageType": {"$ref": "#/definitions/xian_cwsxMessageType", "title": "消息类型"}, "progress": {"type": "integer", "format": "int64", "title": "如果是求体力，需要发进度,如果是助力，这里是助力后的的进度"}, "canHelp": {"type": "boolean", "title": "如果是求体力，需要发是否可送"}, "ts": {"type": "string", "format": "int64", "title": "发消息时间戳 毫秒"}}}, "xian_cwsxTeamMessageListReq": {"type": "object", "properties": {"ts": {"type": "string", "format": "int64", "title": "客户端需要的时间"}}}, "xian_cwsxTeamMessageListRsp": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamMessage"}, "title": "消息列表"}, "hasNext": {"type": "boolean", "title": "是否还有下一页"}, "messageTime": {"type": "string", "format": "int64", "title": "最后一条消息的时间戳"}, "begTime": {"type": "string", "format": "int64", "title": "上次求体力时间戳"}}}, "xian_cwsxTeamOutReq": {"type": "object", "properties": {"openId": {"type": "string", "title": "需要踢出的人id"}}}, "xian_cwsxTeamOutRsp": {"type": "object"}, "xian_cwsxTeamPlayer": {"type": "object", "properties": {"position": {"type": "integer", "format": "int64", "title": "职位 0 队长 1 管理员 2普通人"}, "maxFloor": {"type": "integer", "format": "int64", "title": "打过的最大非噩梦关卡"}, "joinTime": {"type": "string", "format": "int64", "title": "加入时间"}, "encryptUid": {"type": "string", "title": "加密uin"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "freshTime": {"type": "string", "format": "int64", "title": "玩家数据刷新时间 --打巅峰赛的时候这个值是巅峰赛分数刷新时间"}, "score": {"type": "integer", "format": "int64", "title": "巅峰赛积分"}, "helpNum": {"type": "integer", "format": "int64", "title": "助力次数"}, "openId": {"type": "string"}, "friendPetId": {"type": "string", "format": "int64", "title": "此人宠物id"}}}, "xian_cwsxTeamRankItem": {"type": "object", "properties": {"teamScore": {"type": "string", "format": "uint64", "title": "战队总分"}, "teamId": {"type": "string", "title": "战队id"}, "nickname": {"type": "string", "title": "战队昵称"}, "avatar": {"type": "string", "title": "战队头像"}, "freshTime": {"type": "string", "format": "int64", "title": "战队数据刷新时间"}, "memberNum": {"type": "integer", "format": "int64", "title": "战队目前人数"}}, "title": "战队榜结构"}, "xian_cwsxTeamRankReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}}}, "xian_cwsxTeamRankRsp": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "hasNext": {"type": "boolean"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamRankItem"}}, "rank": {"type": "integer", "format": "int64", "title": "自己的排行"}}}, "xian_cwsxTeamUserRankReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "teamId": {"type": "string", "title": "啥都不传就查自己所在战队"}}}, "xian_cwsxTeamUserRankRsp": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "hasNext": {"type": "boolean"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamPlayer"}}}, "title": "成员榜"}, "xian_cwsxTeamWeekAwardCheckReq": {"type": "object"}, "xian_cwsxTeamWeekAwardCheckRsp": {"type": "object", "properties": {"canWeekReward": {"type": "boolean", "title": "是否可弹战队周榜奖励弹窗(领的都是上期) --满足条件才可以,即使没领奖,但是玩家不满足领奖条件也不可领奖弹窗"}, "canUserWeekReward": {"type": "boolean", "title": "是否可弹战队成员周贡献榜奖励弹窗(领的都是上期) --满足条件才可以,即使没领奖,但是玩家不满足领奖条件也不可领奖弹窗"}, "weekRank": {"type": "string", "format": "int64", "title": "周榜上周战队排名"}, "userWeekRank": {"type": "string", "format": "int64", "title": "上周个人贡献榜个人排名"}, "st": {"type": "string", "format": "int64", "title": "当前服务器10位时间戳"}, "endTs": {"type": "string", "format": "int64", "title": "本轮周榜结束的10位时间戳"}, "score": {"type": "string", "format": "int64", "title": "战队分"}, "rewards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxReward"}, "title": "如果是可以弹,这个返回奖励列表"}, "rankList": {"$ref": "#/definitions/xian_cwsxWeekRank", "title": "这里只会返回上期的第一页,其他数据请拉TeamWeekRank"}, "avatar": {"type": "string", "title": "战队头像"}, "name": {"type": "string", "title": "战队名称"}}}, "xian_cwsxTeamWeekPlayer": {"type": "object", "properties": {"position": {"type": "integer", "format": "int64", "title": "职位 0 队长 1 管理员 2普通人"}, "level": {"type": "string", "format": "uint64", "title": "关卡"}, "peakScore": {"type": "integer", "format": "int64", "title": "巅峰赛积分"}, "contributeScore": {"type": "string", "format": "int64", "title": "本周贡献分数"}, "encryptUid": {"type": "string", "title": "加密uin"}, "nickname": {"type": "string", "title": "用户昵称"}, "avatar": {"type": "string", "title": "用户头像"}, "freshTime": {"type": "string", "format": "int64", "title": "玩家本周贡献分数刷新时间"}, "openId": {"type": "string"}, "friendPetId": {"type": "string", "format": "int64", "title": "此人宠物id"}}}, "xian_cwsxTeamWeekRankReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "isPre": {"type": "boolean", "title": "是否拉上期，false拉本期"}}}, "xian_cwsxTeamWeekRankRsp": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "hasNext": {"type": "boolean"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamRankItem"}}, "rank": {"type": "integer", "format": "int64", "title": "自己的排行"}, "remainSec": {"type": "string", "format": "int64", "title": "本轮周榜倒计时"}, "score": {"type": "integer", "format": "int64", "title": "自己战队的分数"}, "selfContributeScore": {"type": "integer", "format": "int64", "title": "自己贡献分"}}}, "xian_cwsxTeamWeekUserRankReq": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "teamId": {"type": "string", "title": "啥都不传就查自己所在战队"}}}, "xian_cwsxTeamWeekUserRankRsp": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "hasNext": {"type": "boolean"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamWeekPlayer"}}, "rank": {"type": "integer", "format": "int64", "title": "自己的排行"}}, "title": "成员榜"}, "xian_cwsxViewTeamBaseReq": {"type": "object"}, "xian_cwsxViewTeamBaseRsp": {"type": "object", "properties": {"teamId": {"type": "string", "title": "啥都不传就查自己所在战队"}, "setAvatar": {"type": "string", "title": "玩家自己设置图片的url 预制id也是url填入"}, "name": {"type": "string", "title": "战队名字"}, "position": {"type": "integer", "format": "int64", "title": "职位"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "score": {"type": "string", "format": "int64", "title": "战队总分"}, "memberNum": {"type": "integer", "format": "int64", "title": "战队成员数量"}, "editCount": {"type": "integer", "format": "int64", "title": "改名剩余次数"}, "contributeScore": {"type": "string", "format": "int64", "title": "战队贡献分数"}}}, "xian_cwsxViewTeamReq": {"type": "object", "properties": {"teamId": {"type": "string", "title": "啥都不传就查自己所在战队"}}}, "xian_cwsxViewTeamRsp": {"type": "object", "properties": {"setAvatar": {"type": "string", "title": "玩家自己设置图片的url 预制id也是url填入"}, "name": {"type": "string", "title": "战队名字"}, "describe": {"type": "string", "title": "战队描述"}, "isAudit": {"type": "boolean", "title": "加入是否需要审核"}, "unlockFloor": {"type": "integer", "format": "int32", "title": "加入关卡必须打过的关卡"}, "infiniteItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxInfiniteItem"}, "title": "无限道具及过期时间"}, "players": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamPlayer"}}, "passback": {"type": "integer", "format": "int32"}, "hasNext": {"type": "boolean"}, "score": {"type": "string", "format": "int64", "title": "战队总分"}, "activeLevel": {"type": "integer", "format": "int64", "title": "战队活跃等级"}, "memberNum": {"type": "integer", "format": "int64", "title": "战队成员数量"}, "isLeader": {"type": "boolean"}, "state": {"$ref": "#/definitions/xian_cwsxMemberState"}, "editCount": {"type": "integer", "format": "int64", "title": "改名剩余次数"}, "weekRank": {"type": "integer", "format": "int64", "title": "本战队在当前周的周榜排行"}, "contributeScore": {"type": "string", "format": "int64", "title": "战队贡献分数"}}}, "xian_cwsxWeekRank": {"type": "object", "properties": {"passback": {"type": "integer", "format": "int32", "title": "首次不传, 服务器返回什么, 传什么"}, "hasNext": {"type": "boolean"}, "list": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxTeamRankItem"}}}, "title": "这里是弹窗如果玩家有奖励需要把上期排名信息展示"}}}