// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/xian_cwsx/team/team.proto

package team

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	game "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/xian_cwsx/game"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MemberState int32

const (
	MemberState_MemberStateUnknown MemberState = 0 // 未加入
	MemberState_MemberStateMember  MemberState = 1 // 成员/队长
	MemberState_MemberStateApply   MemberState = 2 // 在审批列表
)

// Enum value maps for MemberState.
var (
	MemberState_name = map[int32]string{
		0: "MemberStateUnknown",
		1: "MemberStateMember",
		2: "MemberStateApply",
	}
	MemberState_value = map[string]int32{
		"MemberStateUnknown": 0,
		"MemberStateMember":  1,
		"MemberStateApply":   2,
	}
)

func (x MemberState) Enum() *MemberState {
	p := new(MemberState)
	*p = x
	return p
}

func (x MemberState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MemberState) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_enumTypes[0].Descriptor()
}

func (MemberState) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_team_team_proto_enumTypes[0]
}

func (x MemberState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MemberState.Descriptor instead.
func (MemberState) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{0}
}

type MessageType int32

const (
	MessageType_MessageTypeUnknown MessageType = 0
	MessageType_MessageTypeHeart   MessageType = 1 // 体力
	MessageType_MessageTypeEntry   MessageType = 2 // 加入消息
	MessageType_MessageTypeLeave   MessageType = 3 // 退出消息
	MessageType_MessageTypeKick    MessageType = 4 // 踢人消息
	MessageType_MessageTypeRefuse  MessageType = 5 // 拒绝人消息
	MessageType_MessageTypeAgree   MessageType = 6 // 同意人消息
	MessageType_MessageTypeLeader  MessageType = 7 // 成为队长
	MessageType_MessageTypeHelp    MessageType = 8 // 助力消息
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MessageTypeUnknown",
		1: "MessageTypeHeart",
		2: "MessageTypeEntry",
		3: "MessageTypeLeave",
		4: "MessageTypeKick",
		5: "MessageTypeRefuse",
		6: "MessageTypeAgree",
		7: "MessageTypeLeader",
		8: "MessageTypeHelp",
	}
	MessageType_value = map[string]int32{
		"MessageTypeUnknown": 0,
		"MessageTypeHeart":   1,
		"MessageTypeEntry":   2,
		"MessageTypeLeave":   3,
		"MessageTypeKick":    4,
		"MessageTypeRefuse":  5,
		"MessageTypeAgree":   6,
		"MessageTypeLeader":  7,
		"MessageTypeHelp":    8,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_enumTypes[1].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_team_team_proto_enumTypes[1]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{1}
}

type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip         string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Mac        string `protobuf:"bytes,2,opt,name=mac,proto3" json:"mac,omitempty"`
	Imei       string `protobuf:"bytes,3,opt,name=imei,proto3" json:"imei,omitempty"`
	Idfa       string `protobuf:"bytes,4,opt,name=idfa,proto3" json:"idfa,omitempty"`
	Idfv       string `protobuf:"bytes,5,opt,name=idfv,proto3" json:"idfv,omitempty"`
	MobileFlag uint32 `protobuf:"varint,6,opt,name=mobileFlag,proto3" json:"mobileFlag,omitempty"` // 是否来自手机
	MobleQUA   string `protobuf:"bytes,7,opt,name=mobleQUA,proto3" json:"mobleQUA,omitempty"`      // qua
	Uuid       string `protobuf:"bytes,8,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Udid       string `protobuf:"bytes,9,opt,name=udid,proto3" json:"udid,omitempty"`
	Qimei36    string `protobuf:"bytes,10,opt,name=qimei36,proto3" json:"qimei36,omitempty"`
	DeviceInfo string `protobuf:"bytes,11,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DeviceInfo) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *DeviceInfo) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *DeviceInfo) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *DeviceInfo) GetIdfv() string {
	if x != nil {
		return x.Idfv
	}
	return ""
}

func (x *DeviceInfo) GetMobileFlag() uint32 {
	if x != nil {
		return x.MobileFlag
	}
	return 0
}

func (x *DeviceInfo) GetMobleQUA() string {
	if x != nil {
		return x.MobleQUA
	}
	return ""
}

func (x *DeviceInfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *DeviceInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *DeviceInfo) GetQimei36() string {
	if x != nil {
		return x.Qimei36
	}
	return ""
}

func (x *DeviceInfo) GetDeviceInfo() string {
	if x != nil {
		return x.DeviceInfo
	}
	return ""
}

type CreateTeamToAuditReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SetAvatar   string      `protobuf:"bytes,1,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`        // 玩家自己设置图片的url 预制也是url
	Name        string      `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                   // 战队名字
	Describe    string      `protobuf:"bytes,3,opt,name=describe,proto3" json:"describe,omitempty"`                           // 战队描述
	IsAudit     bool        `protobuf:"varint,4,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"`             // 加入是否需要审核
	UnlockFloor uint32      `protobuf:"varint,5,opt,name=unlock_floor,json=unlockFloor,proto3" json:"unlock_floor,omitempty"` // 加入关卡必须打过的关卡
	Device      *DeviceInfo `protobuf:"bytes,6,opt,name=device,proto3" json:"device,omitempty"`                               // 设备信息, 有的尽量填
}

func (x *CreateTeamToAuditReq) Reset() {
	*x = CreateTeamToAuditReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTeamToAuditReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTeamToAuditReq) ProtoMessage() {}

func (x *CreateTeamToAuditReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTeamToAuditReq.ProtoReflect.Descriptor instead.
func (*CreateTeamToAuditReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{1}
}

func (x *CreateTeamToAuditReq) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *CreateTeamToAuditReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTeamToAuditReq) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *CreateTeamToAuditReq) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *CreateTeamToAuditReq) GetUnlockFloor() uint32 {
	if x != nil {
		return x.UnlockFloor
	}
	return 0
}

func (x *CreateTeamToAuditReq) GetDevice() *DeviceInfo {
	if x != nil {
		return x.Device
	}
	return nil
}

type CreateTeamToAuditRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 唯一id
}

func (x *CreateTeamToAuditRsp) Reset() {
	*x = CreateTeamToAuditRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTeamToAuditRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTeamToAuditRsp) ProtoMessage() {}

func (x *CreateTeamToAuditRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTeamToAuditRsp.ProtoReflect.Descriptor instead.
func (*CreateTeamToAuditRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{2}
}

func (x *CreateTeamToAuditRsp) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

type ApplyJoinTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId   string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`       // 战队唯一id
	MemberId string `protobuf:"bytes,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"` // 申请加入者id
}

func (x *ApplyJoinTeamReq) Reset() {
	*x = ApplyJoinTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyJoinTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyJoinTeamReq) ProtoMessage() {}

func (x *ApplyJoinTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyJoinTeamReq.ProtoReflect.Descriptor instead.
func (*ApplyJoinTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{3}
}

func (x *ApplyJoinTeamReq) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *ApplyJoinTeamReq) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

// 如果申请的战队有审核开关,这个成功只表示进入审核列表
type ApplyJoinTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ApplyJoinTeamRsp) Reset() {
	*x = ApplyJoinTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyJoinTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyJoinTeamRsp) ProtoMessage() {}

func (x *ApplyJoinTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyJoinTeamRsp.ProtoReflect.Descriptor instead.
func (*ApplyJoinTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{4}
}

type ViewTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 啥都不传就查自己所在战队
}

func (x *ViewTeamReq) Reset() {
	*x = ViewTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViewTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewTeamReq) ProtoMessage() {}

func (x *ViewTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewTeamReq.ProtoReflect.Descriptor instead.
func (*ViewTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{5}
}

func (x *ViewTeamReq) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

type ViewTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SetAvatar       string               `protobuf:"bytes,1,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`             // 玩家自己设置图片的url 预制id也是url填入
	Name            string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                        // 战队名字
	Describe        string               `protobuf:"bytes,3,opt,name=describe,proto3" json:"describe,omitempty"`                                // 战队描述
	IsAudit         bool                 `protobuf:"varint,4,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"`                  // 加入是否需要审核
	UnlockFloor     int32                `protobuf:"varint,5,opt,name=unlock_floor,json=unlockFloor,proto3" json:"unlock_floor,omitempty"`      // 加入关卡必须打过的关卡
	InfiniteItems   []*game.InfiniteItem `protobuf:"bytes,6,rep,name=infinite_items,json=infiniteItems,proto3" json:"infinite_items,omitempty"` // 无限道具及过期时间
	Players         []*TeamPlayer        `protobuf:"bytes,7,rep,name=players,proto3" json:"players,omitempty"`
	Passback        int32                `protobuf:"varint,8,opt,name=passback,proto3" json:"passback,omitempty"`
	HasNext         bool                 `protobuf:"varint,9,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	Score           int64                `protobuf:"varint,10,opt,name=score,proto3" json:"score,omitempty"`                                // 战队总分
	ActiveLevel     uint32               `protobuf:"varint,11,opt,name=active_level,json=activeLevel,proto3" json:"active_level,omitempty"` //  战队活跃等级
	MemberNum       uint32               `protobuf:"varint,12,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`       // 战队成员数量
	IsLeader        bool                 `protobuf:"varint,13,opt,name=is_leader,json=isLeader,proto3" json:"is_leader,omitempty"`
	State           MemberState          `protobuf:"varint,14,opt,name=state,proto3,enum=xian_cwsx.MemberState" json:"state,omitempty"`
	EditCount       uint32               `protobuf:"varint,15,opt,name=edit_count,json=editCount,proto3" json:"edit_count,omitempty"`                   // 改名剩余次数
	WeekRank        uint32               `protobuf:"varint,16,opt,name=week_rank,json=weekRank,proto3" json:"week_rank,omitempty"`                      // 本战队在当前周的周榜排行
	ContributeScore int64                `protobuf:"varint,17,opt,name=contribute_score,json=contributeScore,proto3" json:"contribute_score,omitempty"` // 战队贡献分数
}

func (x *ViewTeamRsp) Reset() {
	*x = ViewTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViewTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewTeamRsp) ProtoMessage() {}

func (x *ViewTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewTeamRsp.ProtoReflect.Descriptor instead.
func (*ViewTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{6}
}

func (x *ViewTeamRsp) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *ViewTeamRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ViewTeamRsp) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *ViewTeamRsp) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *ViewTeamRsp) GetUnlockFloor() int32 {
	if x != nil {
		return x.UnlockFloor
	}
	return 0
}

func (x *ViewTeamRsp) GetInfiniteItems() []*game.InfiniteItem {
	if x != nil {
		return x.InfiniteItems
	}
	return nil
}

func (x *ViewTeamRsp) GetPlayers() []*TeamPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *ViewTeamRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *ViewTeamRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *ViewTeamRsp) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ViewTeamRsp) GetActiveLevel() uint32 {
	if x != nil {
		return x.ActiveLevel
	}
	return 0
}

func (x *ViewTeamRsp) GetMemberNum() uint32 {
	if x != nil {
		return x.MemberNum
	}
	return 0
}

func (x *ViewTeamRsp) GetIsLeader() bool {
	if x != nil {
		return x.IsLeader
	}
	return false
}

func (x *ViewTeamRsp) GetState() MemberState {
	if x != nil {
		return x.State
	}
	return MemberState_MemberStateUnknown
}

func (x *ViewTeamRsp) GetEditCount() uint32 {
	if x != nil {
		return x.EditCount
	}
	return 0
}

func (x *ViewTeamRsp) GetWeekRank() uint32 {
	if x != nil {
		return x.WeekRank
	}
	return 0
}

func (x *ViewTeamRsp) GetContributeScore() int64 {
	if x != nil {
		return x.ContributeScore
	}
	return 0
}

type ViewTeamBaseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ViewTeamBaseReq) Reset() {
	*x = ViewTeamBaseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViewTeamBaseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewTeamBaseReq) ProtoMessage() {}

func (x *ViewTeamBaseReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewTeamBaseReq.ProtoReflect.Descriptor instead.
func (*ViewTeamBaseReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{7}
}

type ViewTeamBaseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId          string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`                             // 啥都不传就查自己所在战队
	SetAvatar       string `protobuf:"bytes,2,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`                    // 玩家自己设置图片的url 预制id也是url填入
	Name            string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                               // 战队名字
	Position        uint32 `protobuf:"varint,4,opt,name=position,proto3" json:"position,omitempty"`                                      // 职位
	IsAudit         bool   `protobuf:"varint,5,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"`                         // 加入是否需要审核
	Score           int64  `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`                                            // 战队总分
	MemberNum       uint32 `protobuf:"varint,7,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`                   // 战队成员数量
	EditCount       uint32 `protobuf:"varint,8,opt,name=edit_count,json=editCount,proto3" json:"edit_count,omitempty"`                   // 改名剩余次数
	ContributeScore int64  `protobuf:"varint,9,opt,name=contribute_score,json=contributeScore,proto3" json:"contribute_score,omitempty"` // 战队贡献分数
}

func (x *ViewTeamBaseRsp) Reset() {
	*x = ViewTeamBaseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViewTeamBaseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViewTeamBaseRsp) ProtoMessage() {}

func (x *ViewTeamBaseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViewTeamBaseRsp.ProtoReflect.Descriptor instead.
func (*ViewTeamBaseRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{8}
}

func (x *ViewTeamBaseRsp) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *ViewTeamBaseRsp) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *ViewTeamBaseRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ViewTeamBaseRsp) GetPosition() uint32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *ViewTeamBaseRsp) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *ViewTeamBaseRsp) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ViewTeamBaseRsp) GetMemberNum() uint32 {
	if x != nil {
		return x.MemberNum
	}
	return 0
}

func (x *ViewTeamBaseRsp) GetEditCount() uint32 {
	if x != nil {
		return x.EditCount
	}
	return 0
}

func (x *ViewTeamBaseRsp) GetContributeScore() int64 {
	if x != nil {
		return x.ContributeScore
	}
	return 0
}

type ApplyListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32 `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"`
}

func (x *ApplyListReq) Reset() {
	*x = ApplyListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyListReq) ProtoMessage() {}

func (x *ApplyListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyListReq.ProtoReflect.Descriptor instead.
func (*ApplyListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{9}
}

func (x *ApplyListReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

// 申请成员信息
type ApplyMember struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId      string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	Nickname    string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`                       // 用户昵称
	Avatar      string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`                           // 用户头像
	MaxFloor    uint32 `protobuf:"varint,4,opt,name=max_floor,json=maxFloor,proto3" json:"max_floor,omitempty"`      // 当前关卡
	Score       uint32 `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`                            // 巅峰赛积分
	ApplyTime   int64  `protobuf:"varint,6,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`   // 申请的时间
	EncryptUid  string `protobuf:"bytes,7,opt,name=encrypt_uid,json=encryptUid,proto3" json:"encrypt_uid,omitempty"` // 加密uin
	FriendPetId int64  `protobuf:"varint,8,opt,name=friendPetId,proto3" json:"friendPetId,omitempty"`                // 此人宠物id
}

func (x *ApplyMember) Reset() {
	*x = ApplyMember{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyMember) ProtoMessage() {}

func (x *ApplyMember) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyMember.ProtoReflect.Descriptor instead.
func (*ApplyMember) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{10}
}

func (x *ApplyMember) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ApplyMember) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *ApplyMember) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *ApplyMember) GetMaxFloor() uint32 {
	if x != nil {
		return x.MaxFloor
	}
	return 0
}

func (x *ApplyMember) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ApplyMember) GetApplyTime() int64 {
	if x != nil {
		return x.ApplyTime
	}
	return 0
}

func (x *ApplyMember) GetEncryptUid() string {
	if x != nil {
		return x.EncryptUid
	}
	return ""
}

func (x *ApplyMember) GetFriendPetId() int64 {
	if x != nil {
		return x.FriendPetId
	}
	return 0
}

type ApplyListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*ApplyMember `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Passback int32          `protobuf:"varint,2,opt,name=passback,proto3" json:"passback,omitempty"`
	HasNext  bool           `protobuf:"varint,3,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`
}

func (x *ApplyListRsp) Reset() {
	*x = ApplyListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyListRsp) ProtoMessage() {}

func (x *ApplyListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyListRsp.ProtoReflect.Descriptor instead.
func (*ApplyListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{11}
}

func (x *ApplyListRsp) GetList() []*ApplyMember {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ApplyListRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *ApplyListRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

type TeamPlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position    uint32 `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`                      // 职位 0 队长 1 管理员 2普通人
	MaxFloor    uint32 `protobuf:"varint,2,opt,name=max_floor,json=maxFloor,proto3" json:"max_floor,omitempty"`      // 打过的最大非噩梦关卡
	JoinTime    int64  `protobuf:"varint,3,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`      // 加入时间
	EncryptUid  string `protobuf:"bytes,4,opt,name=encrypt_uid,json=encryptUid,proto3" json:"encrypt_uid,omitempty"` // 加密uin
	Nickname    string `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`                       // 用户昵称
	Avatar      string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`                           // 用户头像
	FreshTime   int64  `protobuf:"varint,7,opt,name=fresh_time,json=freshTime,proto3" json:"fresh_time,omitempty"`   // 玩家数据刷新时间 --打巅峰赛的时候这个值是巅峰赛分数刷新时间
	Score       uint32 `protobuf:"varint,8,opt,name=score,proto3" json:"score,omitempty"`                            // 巅峰赛积分
	HelpNum     uint32 `protobuf:"varint,9,opt,name=help_num,json=helpNum,proto3" json:"help_num,omitempty"`         // 助力次数
	OpenId      string `protobuf:"bytes,10,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	FriendPetId int64  `protobuf:"varint,11,opt,name=friendPetId,proto3" json:"friendPetId,omitempty"` // 此人宠物id
}

func (x *TeamPlayer) Reset() {
	*x = TeamPlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamPlayer) ProtoMessage() {}

func (x *TeamPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamPlayer.ProtoReflect.Descriptor instead.
func (*TeamPlayer) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{12}
}

func (x *TeamPlayer) GetPosition() uint32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *TeamPlayer) GetMaxFloor() uint32 {
	if x != nil {
		return x.MaxFloor
	}
	return 0
}

func (x *TeamPlayer) GetJoinTime() int64 {
	if x != nil {
		return x.JoinTime
	}
	return 0
}

func (x *TeamPlayer) GetEncryptUid() string {
	if x != nil {
		return x.EncryptUid
	}
	return ""
}

func (x *TeamPlayer) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *TeamPlayer) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamPlayer) GetFreshTime() int64 {
	if x != nil {
		return x.FreshTime
	}
	return 0
}

func (x *TeamPlayer) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TeamPlayer) GetHelpNum() uint32 {
	if x != nil {
		return x.HelpNum
	}
	return 0
}

func (x *TeamPlayer) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TeamPlayer) GetFriendPetId() int64 {
	if x != nil {
		return x.FriendPetId
	}
	return 0
}

type SearchTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SearchTeamReq) Reset() {
	*x = SearchTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTeamReq) ProtoMessage() {}

func (x *SearchTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTeamReq.ProtoReflect.Descriptor instead.
func (*SearchTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{13}
}

func (x *SearchTeamReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TeamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId        string               `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`                       // 战队唯一id
	SetAvatar     string               `protobuf:"bytes,2,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`              // 玩家自己设置图片的url 预制id也是url填入
	Name          string               `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                         // 战队名字
	MembersNumber uint32               `protobuf:"varint,4,opt,name=members_number,json=membersNumber,proto3" json:"members_number,omitempty"` // 人员数量
	InfiniteItems []*game.InfiniteItem `protobuf:"bytes,5,rep,name=infinite_items,json=infiniteItems,proto3" json:"infinite_items,omitempty"`  // 无限道具及过期时间
	ActiveLevel   uint32               `protobuf:"varint,6,opt,name=active_level,json=activeLevel,proto3" json:"active_level,omitempty"`       // 活跃等级
	Score         uint32               `protobuf:"varint,7,opt,name=score,proto3" json:"score,omitempty"`
	Describe      string               `protobuf:"bytes,8,opt,name=describe,proto3" json:"describe,omitempty"`
	IsApply       bool                 `protobuf:"varint,9,opt,name=is_apply,json=isApply,proto3" json:"is_apply,omitempty"`              // 是否已申请
	IsAudit       bool                 `protobuf:"varint,10,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"`             // 加入是否需要审核
	LowestLevel   uint32               `protobuf:"varint,11,opt,name=lowest_level,json=lowestLevel,proto3" json:"lowest_level,omitempty"` // 进关条件
}

func (x *TeamInfo) Reset() {
	*x = TeamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInfo) ProtoMessage() {}

func (x *TeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInfo.ProtoReflect.Descriptor instead.
func (*TeamInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{14}
}

func (x *TeamInfo) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *TeamInfo) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *TeamInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamInfo) GetMembersNumber() uint32 {
	if x != nil {
		return x.MembersNumber
	}
	return 0
}

func (x *TeamInfo) GetInfiniteItems() []*game.InfiniteItem {
	if x != nil {
		return x.InfiniteItems
	}
	return nil
}

func (x *TeamInfo) GetActiveLevel() uint32 {
	if x != nil {
		return x.ActiveLevel
	}
	return 0
}

func (x *TeamInfo) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TeamInfo) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *TeamInfo) GetIsApply() bool {
	if x != nil {
		return x.IsApply
	}
	return false
}

func (x *TeamInfo) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *TeamInfo) GetLowestLevel() uint32 {
	if x != nil {
		return x.LowestLevel
	}
	return 0
}

type SearchTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamList []*TeamInfo `protobuf:"bytes,1,rep,name=team_list,json=teamList,proto3" json:"team_list,omitempty"`
}

func (x *SearchTeamRsp) Reset() {
	*x = SearchTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTeamRsp) ProtoMessage() {}

func (x *SearchTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTeamRsp.ProtoReflect.Descriptor instead.
func (*SearchTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{15}
}

func (x *SearchTeamRsp) GetTeamList() []*TeamInfo {
	if x != nil {
		return x.TeamList
	}
	return nil
}

type TeamListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamListReq) Reset() {
	*x = TeamListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamListReq) ProtoMessage() {}

func (x *TeamListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamListReq.ProtoReflect.Descriptor instead.
func (*TeamListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{16}
}

type TeamListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamList []*TeamInfo `protobuf:"bytes,1,rep,name=team_list,json=teamList,proto3" json:"team_list,omitempty"`
}

func (x *TeamListRsp) Reset() {
	*x = TeamListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamListRsp) ProtoMessage() {}

func (x *TeamListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamListRsp.ProtoReflect.Descriptor instead.
func (*TeamListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{17}
}

func (x *TeamListRsp) GetTeamList() []*TeamInfo {
	if x != nil {
		return x.TeamList
	}
	return nil
}

type ApprovalToJoinReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // 审批加入的玩家openid
	Agree  bool   `protobuf:"varint,2,opt,name=agree,proto3" json:"agree,omitempty"`                // true 同意
}

func (x *ApprovalToJoinReq) Reset() {
	*x = ApprovalToJoinReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovalToJoinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalToJoinReq) ProtoMessage() {}

func (x *ApprovalToJoinReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalToJoinReq.ProtoReflect.Descriptor instead.
func (*ApprovalToJoinReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{18}
}

func (x *ApprovalToJoinReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ApprovalToJoinReq) GetAgree() bool {
	if x != nil {
		return x.Agree
	}
	return false
}

// 加入成功，客户端自动塞入战队普通玩家列表
type ApprovalToJoinRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Player     *TeamPlayer     `protobuf:"bytes,1,opt,name=player,proto3" json:"player,omitempty"`
	WeekPlayer *TeamWeekPlayer `protobuf:"bytes,2,opt,name=week_player,json=weekPlayer,proto3" json:"week_player,omitempty"`
}

func (x *ApprovalToJoinRsp) Reset() {
	*x = ApprovalToJoinRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovalToJoinRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovalToJoinRsp) ProtoMessage() {}

func (x *ApprovalToJoinRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovalToJoinRsp.ProtoReflect.Descriptor instead.
func (*ApprovalToJoinRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{19}
}

func (x *ApprovalToJoinRsp) GetPlayer() *TeamPlayer {
	if x != nil {
		return x.Player
	}
	return nil
}

func (x *ApprovalToJoinRsp) GetWeekPlayer() *TeamWeekPlayer {
	if x != nil {
		return x.WeekPlayer
	}
	return nil
}

type TeamLeaveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamLeaveReq) Reset() {
	*x = TeamLeaveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamLeaveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamLeaveReq) ProtoMessage() {}

func (x *TeamLeaveReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamLeaveReq.ProtoReflect.Descriptor instead.
func (*TeamLeaveReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{20}
}

type TeamLeaveRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamLeaveRsp) Reset() {
	*x = TeamLeaveRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamLeaveRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamLeaveRsp) ProtoMessage() {}

func (x *TeamLeaveRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamLeaveRsp.ProtoReflect.Descriptor instead.
func (*TeamLeaveRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{21}
}

type TeamChangePermissionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"` // 需要操作的成员id
	Op  uint32 `protobuf:"varint,2,opt,name=op,proto3" json:"op,omitempty"`  // 0 升为管理员 1 降为普通成员
}

func (x *TeamChangePermissionsReq) Reset() {
	*x = TeamChangePermissionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamChangePermissionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamChangePermissionsReq) ProtoMessage() {}

func (x *TeamChangePermissionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamChangePermissionsReq.ProtoReflect.Descriptor instead.
func (*TeamChangePermissionsReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{22}
}

func (x *TeamChangePermissionsReq) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *TeamChangePermissionsReq) GetOp() uint32 {
	if x != nil {
		return x.Op
	}
	return 0
}

type TeamChangePermissionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamChangePermissionsRsp) Reset() {
	*x = TeamChangePermissionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamChangePermissionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamChangePermissionsRsp) ProtoMessage() {}

func (x *TeamChangePermissionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamChangePermissionsRsp.ProtoReflect.Descriptor instead.
func (*TeamChangePermissionsRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{23}
}

type TeamEditInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SetAvatar   string      `protobuf:"bytes,1,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`        // 头像
	Describe    string      `protobuf:"bytes,2,opt,name=describe,proto3" json:"describe,omitempty"`                           // 描述
	IsAudit     bool        `protobuf:"varint,3,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"`             // 加入是否需要审核
	UnlockFloor uint32      `protobuf:"varint,4,opt,name=unlock_floor,json=unlockFloor,proto3" json:"unlock_floor,omitempty"` // 加入关卡必须打过的关卡
	Device      *DeviceInfo `protobuf:"bytes,5,opt,name=device,proto3" json:"device,omitempty"`                               // 设备信息, 有的尽量填
	Name        string      `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`                                   // 战队名字
}

func (x *TeamEditInfoReq) Reset() {
	*x = TeamEditInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamEditInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamEditInfoReq) ProtoMessage() {}

func (x *TeamEditInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamEditInfoReq.ProtoReflect.Descriptor instead.
func (*TeamEditInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{24}
}

func (x *TeamEditInfoReq) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *TeamEditInfoReq) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *TeamEditInfoReq) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *TeamEditInfoReq) GetUnlockFloor() uint32 {
	if x != nil {
		return x.UnlockFloor
	}
	return 0
}

func (x *TeamEditInfoReq) GetDevice() *DeviceInfo {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *TeamEditInfoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TeamEditInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EditCount uint32 `protobuf:"varint,1,opt,name=edit_count,json=editCount,proto3" json:"edit_count,omitempty"` // 改名剩余次数
}

func (x *TeamEditInfoRsp) Reset() {
	*x = TeamEditInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamEditInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamEditInfoRsp) ProtoMessage() {}

func (x *TeamEditInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamEditInfoRsp.ProtoReflect.Descriptor instead.
func (*TeamEditInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{25}
}

func (x *TeamEditInfoRsp) GetEditCount() uint32 {
	if x != nil {
		return x.EditCount
	}
	return 0
}

type TeamAvatarReviewStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamAvatarReviewStatusReq) Reset() {
	*x = TeamAvatarReviewStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamAvatarReviewStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamAvatarReviewStatusReq) ProtoMessage() {}

func (x *TeamAvatarReviewStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamAvatarReviewStatusReq.ProtoReflect.Descriptor instead.
func (*TeamAvatarReviewStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{26}
}

type TeamAvatarReviewStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsAudit bool   `protobuf:"varint,1,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"` // 是否处于审核中
	Avatar  string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`                   //需要显示的头像
}

func (x *TeamAvatarReviewStatusRsp) Reset() {
	*x = TeamAvatarReviewStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamAvatarReviewStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamAvatarReviewStatusRsp) ProtoMessage() {}

func (x *TeamAvatarReviewStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamAvatarReviewStatusRsp.ProtoReflect.Descriptor instead.
func (*TeamAvatarReviewStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{27}
}

func (x *TeamAvatarReviewStatusRsp) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *TeamAvatarReviewStatusRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type TeamOutReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // 需要踢出的人id
}

func (x *TeamOutReq) Reset() {
	*x = TeamOutReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamOutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamOutReq) ProtoMessage() {}

func (x *TeamOutReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamOutReq.ProtoReflect.Descriptor instead.
func (*TeamOutReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{28}
}

func (x *TeamOutReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type TeamOutRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamOutRsp) Reset() {
	*x = TeamOutRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamOutRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamOutRsp) ProtoMessage() {}

func (x *TeamOutRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamOutRsp.ProtoReflect.Descriptor instead.
func (*TeamOutRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{29}
}

type TeamBuyWelfareReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要兑换的商品id
	ShopId uint32 `protobuf:"varint,1,opt,name=shop_id,json=shopId,proto3" json:"shop_id,omitempty"`
	BillId string `protobuf:"bytes,2,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"` // 使用唯一id
}

func (x *TeamBuyWelfareReq) Reset() {
	*x = TeamBuyWelfareReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamBuyWelfareReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamBuyWelfareReq) ProtoMessage() {}

func (x *TeamBuyWelfareReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamBuyWelfareReq.ProtoReflect.Descriptor instead.
func (*TeamBuyWelfareReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{30}
}

func (x *TeamBuyWelfareReq) GetShopId() uint32 {
	if x != nil {
		return x.ShopId
	}
	return 0
}

func (x *TeamBuyWelfareReq) GetBillId() string {
	if x != nil {
		return x.BillId
	}
	return ""
}

type TeamBuyWelfareRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cards []*game.TreasureCard `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"` // 如果发奖产生宝藏奖励这个字段不为空
}

func (x *TeamBuyWelfareRsp) Reset() {
	*x = TeamBuyWelfareRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamBuyWelfareRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamBuyWelfareRsp) ProtoMessage() {}

func (x *TeamBuyWelfareRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamBuyWelfareRsp.ProtoReflect.Descriptor instead.
func (*TeamBuyWelfareRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{31}
}

func (x *TeamBuyWelfareRsp) GetCards() []*game.TreasureCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

// 战队榜结构
type TeamRankItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamScore uint64 `protobuf:"varint,1,opt,name=team_score,json=teamScore,proto3" json:"team_score,omitempty"` // 战队总分
	TeamId    string `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`           // 战队id
	Nickname  string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`                     // 战队昵称
	Avatar    string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`                         // 战队头像
	FreshTime int64  `protobuf:"varint,5,opt,name=fresh_time,json=freshTime,proto3" json:"fresh_time,omitempty"` // 战队数据刷新时间
	MemberNum uint32 `protobuf:"varint,6,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"` // 战队目前人数
}

func (x *TeamRankItem) Reset() {
	*x = TeamRankItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamRankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamRankItem) ProtoMessage() {}

func (x *TeamRankItem) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamRankItem.ProtoReflect.Descriptor instead.
func (*TeamRankItem) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{32}
}

func (x *TeamRankItem) GetTeamScore() uint64 {
	if x != nil {
		return x.TeamScore
	}
	return 0
}

func (x *TeamRankItem) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *TeamRankItem) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *TeamRankItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamRankItem) GetFreshTime() int64 {
	if x != nil {
		return x.FreshTime
	}
	return 0
}

func (x *TeamRankItem) GetMemberNum() uint32 {
	if x != nil {
		return x.MemberNum
	}
	return 0
}

type TeamRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32 `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 首次不传, 服务器返回什么, 传什么
}

func (x *TeamRankReq) Reset() {
	*x = TeamRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamRankReq) ProtoMessage() {}

func (x *TeamRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamRankReq.ProtoReflect.Descriptor instead.
func (*TeamRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{33}
}

func (x *TeamRankReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

type TeamRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32           `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 首次不传, 服务器返回什么, 传什么
	HasNext  bool            `protobuf:"varint,2,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	List     []*TeamRankItem `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	Rank     uint32          `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"` // 自己的排行
}

func (x *TeamRankRsp) Reset() {
	*x = TeamRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamRankRsp) ProtoMessage() {}

func (x *TeamRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamRankRsp.ProtoReflect.Descriptor instead.
func (*TeamRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{34}
}

func (x *TeamRankRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamRankRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *TeamRankRsp) GetList() []*TeamRankItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TeamRankRsp) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

type TeamUserRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32  `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"`          // 首次不传, 服务器返回什么, 传什么
	TeamId   string `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 啥都不传就查自己所在战队
}

func (x *TeamUserRankReq) Reset() {
	*x = TeamUserRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamUserRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamUserRankReq) ProtoMessage() {}

func (x *TeamUserRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamUserRankReq.ProtoReflect.Descriptor instead.
func (*TeamUserRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{35}
}

func (x *TeamUserRankReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamUserRankReq) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

// 成员榜
type TeamUserRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32         `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 首次不传, 服务器返回什么, 传什么
	HasNext  bool          `protobuf:"varint,2,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	List     []*TeamPlayer `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *TeamUserRankRsp) Reset() {
	*x = TeamUserRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamUserRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamUserRankRsp) ProtoMessage() {}

func (x *TeamUserRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamUserRankRsp.ProtoReflect.Descriptor instead.
func (*TeamUserRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{36}
}

func (x *TeamUserRankRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamUserRankRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *TeamUserRankRsp) GetList() []*TeamPlayer {
	if x != nil {
		return x.List
	}
	return nil
}

type TeamNRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamNRankReq) Reset() {
	*x = TeamNRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamNRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamNRankReq) ProtoMessage() {}

func (x *TeamNRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamNRankReq.ProtoReflect.Descriptor instead.
func (*TeamNRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{37}
}

type TeamNRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TeamRankItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *TeamNRankRsp) Reset() {
	*x = TeamNRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamNRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamNRankRsp) ProtoMessage() {}

func (x *TeamNRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamNRankRsp.ProtoReflect.Descriptor instead.
func (*TeamNRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{38}
}

func (x *TeamNRankRsp) GetList() []*TeamRankItem {
	if x != nil {
		return x.List
	}
	return nil
}

type BegHeartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BegHeartReq) Reset() {
	*x = BegHeartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BegHeartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BegHeartReq) ProtoMessage() {}

func (x *BegHeartReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BegHeartReq.ProtoReflect.Descriptor instead.
func (*BegHeartReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{39}
}

type BegHeartRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BegHeartRsp) Reset() {
	*x = BegHeartRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BegHeartRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BegHeartRsp) ProtoMessage() {}

func (x *BegHeartRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BegHeartRsp.ProtoReflect.Descriptor instead.
func (*BegHeartRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{40}
}

type HelpHeartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberId string `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
}

func (x *HelpHeartReq) Reset() {
	*x = HelpHeartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpHeartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpHeartReq) ProtoMessage() {}

func (x *HelpHeartReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpHeartReq.ProtoReflect.Descriptor instead.
func (*HelpHeartReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{41}
}

func (x *HelpHeartReq) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

type HelpHeartRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HelpHeartRsp) Reset() {
	*x = HelpHeartRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HelpHeartRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelpHeartRsp) ProtoMessage() {}

func (x *HelpHeartRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelpHeartRsp.ProtoReflect.Descriptor instead.
func (*HelpHeartRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{42}
}

type DrawTeamHeartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MemberId string `protobuf:"bytes,1,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"` //  成员openid
}

func (x *DrawTeamHeartReq) Reset() {
	*x = DrawTeamHeartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrawTeamHeartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrawTeamHeartReq) ProtoMessage() {}

func (x *DrawTeamHeartReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrawTeamHeartReq.ProtoReflect.Descriptor instead.
func (*DrawTeamHeartReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{43}
}

func (x *DrawTeamHeartReq) GetMemberId() string {
	if x != nil {
		return x.MemberId
	}
	return ""
}

type DrawTeamHeartRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hearts uint32 `protobuf:"varint,1,opt,name=hearts,proto3" json:"hearts,omitempty"` // 本次领取的爱心数量
}

func (x *DrawTeamHeartRsp) Reset() {
	*x = DrawTeamHeartRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DrawTeamHeartRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DrawTeamHeartRsp) ProtoMessage() {}

func (x *DrawTeamHeartRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DrawTeamHeartRsp.ProtoReflect.Descriptor instead.
func (*DrawTeamHeartRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{44}
}

func (x *DrawTeamHeartRsp) GetHearts() uint32 {
	if x != nil {
		return x.Hearts
	}
	return 0
}

type TeamCanReciveListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamCanReciveListReq) Reset() {
	*x = TeamCanReciveListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamCanReciveListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamCanReciveListReq) ProtoMessage() {}

func (x *TeamCanReciveListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamCanReciveListReq.ProtoReflect.Descriptor instead.
func (*TeamCanReciveListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{45}
}

type MemberBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid    string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`     // 昵称
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"` // 头像
	Num    int32  `protobuf:"varint,4,opt,name=num,proto3" json:"num,omitempty"`      // 还剩几次可领
}

func (x *MemberBase) Reset() {
	*x = MemberBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberBase) ProtoMessage() {}

func (x *MemberBase) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberBase.ProtoReflect.Descriptor instead.
func (*MemberBase) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{46}
}

func (x *MemberBase) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *MemberBase) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MemberBase) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *MemberBase) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type TeamCanReciveListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Friends []*MemberBase `protobuf:"bytes,1,rep,name=friends,proto3" json:"friends,omitempty"`
}

func (x *TeamCanReciveListRsp) Reset() {
	*x = TeamCanReciveListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamCanReciveListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamCanReciveListRsp) ProtoMessage() {}

func (x *TeamCanReciveListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamCanReciveListRsp.ProtoReflect.Descriptor instead.
func (*TeamCanReciveListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{47}
}

func (x *TeamCanReciveListRsp) GetFriends() []*MemberBase {
	if x != nil {
		return x.Friends
	}
	return nil
}

type TeamCanReciveNumReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamCanReciveNumReq) Reset() {
	*x = TeamCanReciveNumReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamCanReciveNumReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamCanReciveNumReq) ProtoMessage() {}

func (x *TeamCanReciveNumReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamCanReciveNumReq.ProtoReflect.Descriptor instead.
func (*TeamCanReciveNumReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{48}
}

type TeamCanReciveNumRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num        uint32 `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`                                   // 战队求的次数
	CanHelpNum uint32 `protobuf:"varint,2,opt,name=can_help_num,json=canHelpNum,proto3" json:"can_help_num,omitempty"` // 可助力次数
}

func (x *TeamCanReciveNumRsp) Reset() {
	*x = TeamCanReciveNumRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamCanReciveNumRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamCanReciveNumRsp) ProtoMessage() {}

func (x *TeamCanReciveNumRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamCanReciveNumRsp.ProtoReflect.Descriptor instead.
func (*TeamCanReciveNumRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{49}
}

func (x *TeamCanReciveNumRsp) GetNum() uint32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *TeamCanReciveNumRsp) GetCanHelpNum() uint32 {
	if x != nil {
		return x.CanHelpNum
	}
	return 0
}

type TeamMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsAdmin     bool        `protobuf:"varint,1,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin,omitempty"` // 发送者是否系统
	OpenId      string      `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`     // 发送者id
	Avatar      string      `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`                   // 发送者头像
	Name        string      `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                       // 发送者名字
	ToId        string      `protobuf:"bytes,5,opt,name=to_id,json=toId,proto3" json:"to_id,omitempty"`
	ToName      string      `protobuf:"bytes,6,opt,name=to_name,json=toName,proto3" json:"to_name,omitempty"`
	MessageType MessageType `protobuf:"varint,7,opt,name=message_type,json=messageType,proto3,enum=xian_cwsx.MessageType" json:"message_type,omitempty"` // 消息类型
	Progress    uint32      `protobuf:"varint,8,opt,name=progress,proto3" json:"progress,omitempty"`                                                     // 如果是求体力，需要发进度,如果是助力，这里是助力后的的进度
	CanHelp     bool        `protobuf:"varint,9,opt,name=can_help,json=canHelp,proto3" json:"can_help,omitempty"`                                        // 如果是求体力，需要发是否可送
	Ts          int64       `protobuf:"varint,10,opt,name=ts,proto3" json:"ts,omitempty"`                                                                // 发消息时间戳 毫秒
}

func (x *TeamMessage) Reset() {
	*x = TeamMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamMessage) ProtoMessage() {}

func (x *TeamMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamMessage.ProtoReflect.Descriptor instead.
func (*TeamMessage) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{50}
}

func (x *TeamMessage) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

func (x *TeamMessage) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TeamMessage) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamMessage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamMessage) GetToId() string {
	if x != nil {
		return x.ToId
	}
	return ""
}

func (x *TeamMessage) GetToName() string {
	if x != nil {
		return x.ToName
	}
	return ""
}

func (x *TeamMessage) GetMessageType() MessageType {
	if x != nil {
		return x.MessageType
	}
	return MessageType_MessageTypeUnknown
}

func (x *TeamMessage) GetProgress() uint32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *TeamMessage) GetCanHelp() bool {
	if x != nil {
		return x.CanHelp
	}
	return false
}

func (x *TeamMessage) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type TeamMessageListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts int64 `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"` // 客户端需要的时间
}

func (x *TeamMessageListReq) Reset() {
	*x = TeamMessageListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamMessageListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamMessageListReq) ProtoMessage() {}

func (x *TeamMessageListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamMessageListReq.ProtoReflect.Descriptor instead.
func (*TeamMessageListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{51}
}

func (x *TeamMessageListReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

type TeamMessageListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List        []*TeamMessage `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`                                   // 消息列表
	HasNext     bool           `protobuf:"varint,2,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`             // 是否还有下一页
	MessageTime int64          `protobuf:"varint,3,opt,name=message_time,json=messageTime,proto3" json:"message_time,omitempty"` // 最后一条消息的时间戳
	BegTime     int64          `protobuf:"varint,4,opt,name=beg_time,json=begTime,proto3" json:"beg_time,omitempty"`             // 上次求体力时间戳
}

func (x *TeamMessageListRsp) Reset() {
	*x = TeamMessageListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamMessageListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamMessageListRsp) ProtoMessage() {}

func (x *TeamMessageListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamMessageListRsp.ProtoReflect.Descriptor instead.
func (*TeamMessageListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{52}
}

func (x *TeamMessageListRsp) GetList() []*TeamMessage {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TeamMessageListRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *TeamMessageListRsp) GetMessageTime() int64 {
	if x != nil {
		return x.MessageTime
	}
	return 0
}

func (x *TeamMessageListRsp) GetBegTime() int64 {
	if x != nil {
		return x.BegTime
	}
	return 0
}

type TeamConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *TeamConfigReq) Reset() {
	*x = TeamConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamConfigReq) ProtoMessage() {}

func (x *TeamConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamConfigReq.ProtoReflect.Descriptor instead.
func (*TeamConfigReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{53}
}

func (x *TeamConfigReq) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type TeamConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Data    string `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TeamConfigRsp) Reset() {
	*x = TeamConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamConfigRsp) ProtoMessage() {}

func (x *TeamConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamConfigRsp.ProtoReflect.Descriptor instead.
func (*TeamConfigRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{54}
}

func (x *TeamConfigRsp) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *TeamConfigRsp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type TeamInviteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamInviteReq) Reset() {
	*x = TeamInviteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInviteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInviteReq) ProtoMessage() {}

func (x *TeamInviteReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInviteReq.ProtoReflect.Descriptor instead.
func (*TeamInviteReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{55}
}

type TeamInviteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueId string `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId,proto3" json:"unique_id,omitempty"` // 本次邀请唯一id
}

func (x *TeamInviteRsp) Reset() {
	*x = TeamInviteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInviteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInviteRsp) ProtoMessage() {}

func (x *TeamInviteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInviteRsp.ProtoReflect.Descriptor instead.
func (*TeamInviteRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{56}
}

func (x *TeamInviteRsp) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

type TeamInviteInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueId string `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId,proto3" json:"unique_id,omitempty"` // 本次邀请唯一id
}

func (x *TeamInviteInfoReq) Reset() {
	*x = TeamInviteInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInviteInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInviteInfoReq) ProtoMessage() {}

func (x *TeamInviteInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInviteInfoReq.ProtoReflect.Descriptor instead.
func (*TeamInviteInfoReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{57}
}

func (x *TeamInviteInfoReq) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

type TeamInviteInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId   string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // 邀请者id
	Position uint32 `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`          // 邀请者当时职位 0 队长 其他非队长
	Avatar   string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 邀请者头像
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                   // 邀请者呢称
	TeamId   string `protobuf:"bytes,5,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 邀请时的战队id
}

func (x *TeamInviteInfoRsp) Reset() {
	*x = TeamInviteInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamInviteInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamInviteInfoRsp) ProtoMessage() {}

func (x *TeamInviteInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamInviteInfoRsp.ProtoReflect.Descriptor instead.
func (*TeamInviteInfoRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{58}
}

func (x *TeamInviteInfoRsp) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TeamInviteInfoRsp) GetPosition() uint32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *TeamInviteInfoRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamInviteInfoRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeamInviteInfoRsp) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

type TeamJoinByInviteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueId string `protobuf:"bytes,1,opt,name=unique_id,json=uniqueId,proto3" json:"unique_id,omitempty"` // 本次邀请唯一id
	OpenId   string `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`       // 被邀请者open_id
}

func (x *TeamJoinByInviteReq) Reset() {
	*x = TeamJoinByInviteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamJoinByInviteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamJoinByInviteReq) ProtoMessage() {}

func (x *TeamJoinByInviteReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamJoinByInviteReq.ProtoReflect.Descriptor instead.
func (*TeamJoinByInviteReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{59}
}

func (x *TeamJoinByInviteReq) GetUniqueId() string {
	if x != nil {
		return x.UniqueId
	}
	return ""
}

func (x *TeamJoinByInviteReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type TeamJoinByInviteRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamJoinByInviteRsp) Reset() {
	*x = TeamJoinByInviteRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamJoinByInviteRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamJoinByInviteRsp) ProtoMessage() {}

func (x *TeamJoinByInviteRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamJoinByInviteRsp.ProtoReflect.Descriptor instead.
func (*TeamJoinByInviteRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{60}
}

type AutomaticKickReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // 需要处理的玩家id
}

func (x *AutomaticKickReq) Reset() {
	*x = AutomaticKickReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutomaticKickReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutomaticKickReq) ProtoMessage() {}

func (x *AutomaticKickReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutomaticKickReq.ProtoReflect.Descriptor instead.
func (*AutomaticKickReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{61}
}

func (x *AutomaticKickReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

type AutomaticKickRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AutomaticKickRsp) Reset() {
	*x = AutomaticKickRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutomaticKickRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutomaticKickRsp) ProtoMessage() {}

func (x *AutomaticKickRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutomaticKickRsp.ProtoReflect.Descriptor instead.
func (*AutomaticKickRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{62}
}

type AutoJoinTeamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AutoJoinTeamReq) Reset() {
	*x = AutoJoinTeamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoJoinTeamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoJoinTeamReq) ProtoMessage() {}

func (x *AutoJoinTeamReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoJoinTeamReq.ProtoReflect.Descriptor instead.
func (*AutoJoinTeamReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{63}
}

type AutoJoinTeamRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TeamId          string               `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`                      // 如果加入成功，这里返回加入的id
	SetAvatar       string               `protobuf:"bytes,2,opt,name=set_avatar,json=setAvatar,proto3" json:"set_avatar,omitempty"`             // 战队设置的头像
	Name            string               `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                        // 战队名字
	Describe        string               `protobuf:"bytes,4,opt,name=describe,proto3" json:"describe,omitempty"`                                // 战队描述
	IsAudit         bool                 `protobuf:"varint,5,opt,name=is_audit,json=isAudit,proto3" json:"is_audit,omitempty"`                  // 加入是否需要审核
	UnlockFloor     int32                `protobuf:"varint,6,opt,name=unlock_floor,json=unlockFloor,proto3" json:"unlock_floor,omitempty"`      // 加入关卡必须打过的关卡
	InfiniteItems   []*game.InfiniteItem `protobuf:"bytes,7,rep,name=infinite_items,json=infiniteItems,proto3" json:"infinite_items,omitempty"` // 无限道具及过期时间
	Players         []*TeamPlayer        `protobuf:"bytes,8,rep,name=players,proto3" json:"players,omitempty"`
	Passback        int32                `protobuf:"varint,9,opt,name=passback,proto3" json:"passback,omitempty"`
	HasNext         bool                 `protobuf:"varint,10,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	Score           int64                `protobuf:"varint,11,opt,name=score,proto3" json:"score,omitempty"`                                // 战队总分
	ActiveLevel     uint32               `protobuf:"varint,12,opt,name=active_level,json=activeLevel,proto3" json:"active_level,omitempty"` //  战队活跃等级
	MemberNum       uint32               `protobuf:"varint,13,opt,name=member_num,json=memberNum,proto3" json:"member_num,omitempty"`       // 战队成员数量
	IsLeader        bool                 `protobuf:"varint,14,opt,name=is_leader,json=isLeader,proto3" json:"is_leader,omitempty"`
	State           MemberState          `protobuf:"varint,15,opt,name=state,proto3,enum=xian_cwsx.MemberState" json:"state,omitempty"`
	EditCount       uint32               `protobuf:"varint,16,opt,name=edit_count,json=editCount,proto3" json:"edit_count,omitempty"`                   // 改名剩余次数
	ContributeScore uint32               `protobuf:"varint,17,opt,name=contribute_score,json=contributeScore,proto3" json:"contribute_score,omitempty"` // 战队贡献分
}

func (x *AutoJoinTeamRsp) Reset() {
	*x = AutoJoinTeamRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoJoinTeamRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoJoinTeamRsp) ProtoMessage() {}

func (x *AutoJoinTeamRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoJoinTeamRsp.ProtoReflect.Descriptor instead.
func (*AutoJoinTeamRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{64}
}

func (x *AutoJoinTeamRsp) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *AutoJoinTeamRsp) GetSetAvatar() string {
	if x != nil {
		return x.SetAvatar
	}
	return ""
}

func (x *AutoJoinTeamRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AutoJoinTeamRsp) GetDescribe() string {
	if x != nil {
		return x.Describe
	}
	return ""
}

func (x *AutoJoinTeamRsp) GetIsAudit() bool {
	if x != nil {
		return x.IsAudit
	}
	return false
}

func (x *AutoJoinTeamRsp) GetUnlockFloor() int32 {
	if x != nil {
		return x.UnlockFloor
	}
	return 0
}

func (x *AutoJoinTeamRsp) GetInfiniteItems() []*game.InfiniteItem {
	if x != nil {
		return x.InfiniteItems
	}
	return nil
}

func (x *AutoJoinTeamRsp) GetPlayers() []*TeamPlayer {
	if x != nil {
		return x.Players
	}
	return nil
}

func (x *AutoJoinTeamRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *AutoJoinTeamRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *AutoJoinTeamRsp) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *AutoJoinTeamRsp) GetActiveLevel() uint32 {
	if x != nil {
		return x.ActiveLevel
	}
	return 0
}

func (x *AutoJoinTeamRsp) GetMemberNum() uint32 {
	if x != nil {
		return x.MemberNum
	}
	return 0
}

func (x *AutoJoinTeamRsp) GetIsLeader() bool {
	if x != nil {
		return x.IsLeader
	}
	return false
}

func (x *AutoJoinTeamRsp) GetState() MemberState {
	if x != nil {
		return x.State
	}
	return MemberState_MemberStateUnknown
}

func (x *AutoJoinTeamRsp) GetEditCount() uint32 {
	if x != nil {
		return x.EditCount
	}
	return 0
}

func (x *AutoJoinTeamRsp) GetContributeScore() uint32 {
	if x != nil {
		return x.ContributeScore
	}
	return 0
}

type TeamWeekRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32 `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"`        // 首次不传, 服务器返回什么, 传什么
	IsPre    bool  `protobuf:"varint,2,opt,name=is_pre,json=isPre,proto3" json:"is_pre,omitempty"` // 是否拉上期，false拉本期
}

func (x *TeamWeekRankReq) Reset() {
	*x = TeamWeekRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekRankReq) ProtoMessage() {}

func (x *TeamWeekRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekRankReq.ProtoReflect.Descriptor instead.
func (*TeamWeekRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{65}
}

func (x *TeamWeekRankReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamWeekRankReq) GetIsPre() bool {
	if x != nil {
		return x.IsPre
	}
	return false
}

type TeamWeekRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback            int32           `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 首次不传, 服务器返回什么, 传什么
	HasNext             bool            `protobuf:"varint,2,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	List                []*TeamRankItem `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	Rank                uint32          `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`                                                            // 自己的排行
	RemainSec           int64           `protobuf:"varint,5,opt,name=remain_sec,json=remainSec,proto3" json:"remain_sec,omitempty"`                                 // 本轮周榜倒计时
	Score               uint32          `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`                                                          // 自己战队的分数
	SelfContributeScore uint32          `protobuf:"varint,7,opt,name=self_contribute_score,json=selfContributeScore,proto3" json:"self_contribute_score,omitempty"` // 自己贡献分
}

func (x *TeamWeekRankRsp) Reset() {
	*x = TeamWeekRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekRankRsp) ProtoMessage() {}

func (x *TeamWeekRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekRankRsp.ProtoReflect.Descriptor instead.
func (*TeamWeekRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{66}
}

func (x *TeamWeekRankRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamWeekRankRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *TeamWeekRankRsp) GetList() []*TeamRankItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TeamWeekRankRsp) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *TeamWeekRankRsp) GetRemainSec() int64 {
	if x != nil {
		return x.RemainSec
	}
	return 0
}

func (x *TeamWeekRankRsp) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TeamWeekRankRsp) GetSelfContributeScore() uint32 {
	if x != nil {
		return x.SelfContributeScore
	}
	return 0
}

type TeamWeekPlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Position        uint32 `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`                                      // 职位 0 队长 1 管理员 2普通人
	Level           uint64 `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`                                            // 关卡
	PeakScore       uint32 `protobuf:"varint,3,opt,name=peak_score,json=peakScore,proto3" json:"peak_score,omitempty"`                   // 巅峰赛积分
	ContributeScore int64  `protobuf:"varint,4,opt,name=contribute_score,json=contributeScore,proto3" json:"contribute_score,omitempty"` // 本周贡献分数
	EncryptUid      string `protobuf:"bytes,5,opt,name=encrypt_uid,json=encryptUid,proto3" json:"encrypt_uid,omitempty"`                 // 加密uin
	Nickname        string `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`                                       // 用户昵称
	Avatar          string `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`                                           // 用户头像
	FreshTime       int64  `protobuf:"varint,8,opt,name=fresh_time,json=freshTime,proto3" json:"fresh_time,omitempty"`                   // 玩家本周贡献分数刷新时间
	OpenId          string `protobuf:"bytes,9,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	FriendPetId     int64  `protobuf:"varint,10,opt,name=friendPetId,proto3" json:"friendPetId,omitempty"` // 此人宠物id
}

func (x *TeamWeekPlayer) Reset() {
	*x = TeamWeekPlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekPlayer) ProtoMessage() {}

func (x *TeamWeekPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekPlayer.ProtoReflect.Descriptor instead.
func (*TeamWeekPlayer) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{67}
}

func (x *TeamWeekPlayer) GetPosition() uint32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *TeamWeekPlayer) GetLevel() uint64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *TeamWeekPlayer) GetPeakScore() uint32 {
	if x != nil {
		return x.PeakScore
	}
	return 0
}

func (x *TeamWeekPlayer) GetContributeScore() int64 {
	if x != nil {
		return x.ContributeScore
	}
	return 0
}

func (x *TeamWeekPlayer) GetEncryptUid() string {
	if x != nil {
		return x.EncryptUid
	}
	return ""
}

func (x *TeamWeekPlayer) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *TeamWeekPlayer) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamWeekPlayer) GetFreshTime() int64 {
	if x != nil {
		return x.FreshTime
	}
	return 0
}

func (x *TeamWeekPlayer) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *TeamWeekPlayer) GetFriendPetId() int64 {
	if x != nil {
		return x.FriendPetId
	}
	return 0
}

type TeamWeekUserRankReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32  `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"`          // 首次不传, 服务器返回什么, 传什么
	TeamId   string `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"` // 啥都不传就查自己所在战队
}

func (x *TeamWeekUserRankReq) Reset() {
	*x = TeamWeekUserRankReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekUserRankReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekUserRankReq) ProtoMessage() {}

func (x *TeamWeekUserRankReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekUserRankReq.ProtoReflect.Descriptor instead.
func (*TeamWeekUserRankReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{68}
}

func (x *TeamWeekUserRankReq) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamWeekUserRankReq) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

// 成员榜
type TeamWeekUserRankRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32             `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 首次不传, 服务器返回什么, 传什么
	HasNext  bool              `protobuf:"varint,2,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	List     []*TeamWeekPlayer `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	Rank     uint32            `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"` // 自己的排行
}

func (x *TeamWeekUserRankRsp) Reset() {
	*x = TeamWeekUserRankRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekUserRankRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekUserRankRsp) ProtoMessage() {}

func (x *TeamWeekUserRankRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekUserRankRsp.ProtoReflect.Descriptor instead.
func (*TeamWeekUserRankRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{69}
}

func (x *TeamWeekUserRankRsp) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *TeamWeekUserRankRsp) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *TeamWeekUserRankRsp) GetList() []*TeamWeekPlayer {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TeamWeekUserRankRsp) GetRank() uint32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

type TeamWeekAwardCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TeamWeekAwardCheckReq) Reset() {
	*x = TeamWeekAwardCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekAwardCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekAwardCheckReq) ProtoMessage() {}

func (x *TeamWeekAwardCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekAwardCheckReq.ProtoReflect.Descriptor instead.
func (*TeamWeekAwardCheckReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{70}
}

// 这里是弹窗如果玩家有奖励需要把上期排名信息展示
type WeekRank struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passback int32           `protobuf:"varint,1,opt,name=passback,proto3" json:"passback,omitempty"` // 首次不传, 服务器返回什么, 传什么
	HasNext  bool            `protobuf:"varint,2,opt,name=hasNext,proto3" json:"hasNext,omitempty"`
	List     []*TeamRankItem `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *WeekRank) Reset() {
	*x = WeekRank{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeekRank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeekRank) ProtoMessage() {}

func (x *WeekRank) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeekRank.ProtoReflect.Descriptor instead.
func (*WeekRank) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{71}
}

func (x *WeekRank) GetPassback() int32 {
	if x != nil {
		return x.Passback
	}
	return 0
}

func (x *WeekRank) GetHasNext() bool {
	if x != nil {
		return x.HasNext
	}
	return false
}

func (x *WeekRank) GetList() []*TeamRankItem {
	if x != nil {
		return x.List
	}
	return nil
}

type TeamWeekAwardCheckRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CanWeekReward     bool           `protobuf:"varint,1,opt,name=can_week_reward,json=canWeekReward,proto3" json:"can_week_reward,omitempty"`               // 是否可弹战队周榜奖励弹窗(领的都是上期) --满足条件才可以,即使没领奖,但是玩家不满足领奖条件也不可领奖弹窗
	CanUserWeekReward bool           `protobuf:"varint,2,opt,name=can_user_week_reward,json=canUserWeekReward,proto3" json:"can_user_week_reward,omitempty"` // 是否可弹战队成员周贡献榜奖励弹窗(领的都是上期) --满足条件才可以,即使没领奖,但是玩家不满足领奖条件也不可领奖弹窗
	WeekRank          int64          `protobuf:"varint,3,opt,name=week_rank,json=weekRank,proto3" json:"week_rank,omitempty"`                                // 周榜上周战队排名
	UserWeekRank      int64          `protobuf:"varint,4,opt,name=user_week_rank,json=userWeekRank,proto3" json:"user_week_rank,omitempty"`                  // 上周个人贡献榜个人排名
	St                int64          `protobuf:"varint,5,opt,name=st,proto3" json:"st,omitempty"`                                                            // 当前服务器10位时间戳
	EndTs             int64          `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`                                         // 本轮周榜结束的10位时间戳
	Score             int64          `protobuf:"varint,7,opt,name=score,proto3" json:"score,omitempty"`                                                      // 战队分
	Rewards           []*game.Reward `protobuf:"bytes,8,rep,name=rewards,proto3" json:"rewards,omitempty"`                                                   // 如果是可以弹,这个返回奖励列表
	RankList          *WeekRank      `protobuf:"bytes,9,opt,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`                                 // 这里只会返回上期的第一页,其他数据请拉TeamWeekRank
	Avatar            string         `protobuf:"bytes,10,opt,name=avatar,proto3" json:"avatar,omitempty"`                                                    // 战队头像
	Name              string         `protobuf:"bytes,11,opt,name=name,proto3" json:"name,omitempty"`                                                        // 战队名称
}

func (x *TeamWeekAwardCheckRsp) Reset() {
	*x = TeamWeekAwardCheckRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TeamWeekAwardCheckRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeamWeekAwardCheckRsp) ProtoMessage() {}

func (x *TeamWeekAwardCheckRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeamWeekAwardCheckRsp.ProtoReflect.Descriptor instead.
func (*TeamWeekAwardCheckRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP(), []int{72}
}

func (x *TeamWeekAwardCheckRsp) GetCanWeekReward() bool {
	if x != nil {
		return x.CanWeekReward
	}
	return false
}

func (x *TeamWeekAwardCheckRsp) GetCanUserWeekReward() bool {
	if x != nil {
		return x.CanUserWeekReward
	}
	return false
}

func (x *TeamWeekAwardCheckRsp) GetWeekRank() int64 {
	if x != nil {
		return x.WeekRank
	}
	return 0
}

func (x *TeamWeekAwardCheckRsp) GetUserWeekRank() int64 {
	if x != nil {
		return x.UserWeekRank
	}
	return 0
}

func (x *TeamWeekAwardCheckRsp) GetSt() int64 {
	if x != nil {
		return x.St
	}
	return 0
}

func (x *TeamWeekAwardCheckRsp) GetEndTs() int64 {
	if x != nil {
		return x.EndTs
	}
	return 0
}

func (x *TeamWeekAwardCheckRsp) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TeamWeekAwardCheckRsp) GetRewards() []*game.Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *TeamWeekAwardCheckRsp) GetRankList() *WeekRank {
	if x != nil {
		return x.RankList
	}
	return nil
}

func (x *TeamWeekAwardCheckRsp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *TeamWeekAwardCheckRsp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_pb_game_cwsx_xian_cwsx_team_team_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x74, 0x65,
	0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x1a, 0x26, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x67, 0x61, 0x6d, 0x65,
	0x2f, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x02, 0x0a, 0x0a,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x6d, 0x65, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6d, 0x65, 0x69,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x64, 0x66, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x76, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x76, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x62, 0x6c,
	0x65, 0x51, 0x55, 0x41, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6f, 0x62, 0x6c,
	0x65, 0x51, 0x55, 0x41, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x71, 0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x71,
	0x69, 0x6d, 0x65, 0x69, 0x33, 0x36, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd2, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x2d, 0x0a, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x2f, 0x0a, 0x14, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x10,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x49, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4a,
	0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x22, 0x26, 0x0a, 0x0b, 0x56, 0x69,
	0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x22, 0xcb, 0x04, 0x0a, 0x0b, 0x56, 0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x52,
	0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12,
	0x3e, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x0d, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x2f, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61,
	0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x65, 0x65, 0x6b,
	0x5f, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x77, 0x65, 0x65,
	0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x22, 0x11, 0x0a, 0x0f, 0x56, 0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x22, 0x93, 0x02, 0x0a, 0x0f, 0x56, 0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x2a, 0x0a, 0x0c, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xef, 0x01, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f,
	0x75, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79,
	0x70, 0x74, 0x55, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x50,
	0x65, 0x74, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x66, 0x72, 0x69, 0x65,
	0x6e, 0x64, 0x50, 0x65, 0x74, 0x49, 0x64, 0x22, 0x71, 0x0a, 0x0c, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x22, 0xc2, 0x02, 0x0a, 0x0a, 0x54,
	0x65, 0x61, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x46, 0x6c, 0x6f,
	0x6f, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x55, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x65, 0x6c,
	0x70, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x68, 0x65, 0x6c,
	0x70, 0x4e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x74, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x23, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xeb, 0x02, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65,
	0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x65, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0d, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x77, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6c, 0x6f, 0x77, 0x65, 0x73, 0x74, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x22, 0x41, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d,
	0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x65, 0x61,
	0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x0d, 0x0a, 0x0b, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x22, 0x3f, 0x0a, 0x0b, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x65, 0x61,
	0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x42, 0x0a, 0x11, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x54, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70,
	0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x67, 0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x61, 0x67, 0x72, 0x65, 0x65, 0x22, 0x7e, 0x0a, 0x11, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x2d,
	0x0a, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x0b, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x0a, 0x77,
	0x65, 0x65, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x22, 0x0e, 0x0a, 0x0c, 0x54, 0x65, 0x61,
	0x6d, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x22, 0x0e, 0x0a, 0x0c, 0x54, 0x65, 0x61,
	0x6d, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x73, 0x70, 0x22, 0x3c, 0x0a, 0x18, 0x54, 0x65, 0x61,
	0x6d, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x6f, 0x70, 0x22, 0x1a, 0x0a, 0x18, 0x54, 0x65, 0x61, 0x6d, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x73, 0x70, 0x22, 0xcd, 0x01, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x45, 0x64, 0x69, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x6c, 0x6f, 0x6f, 0x72,
	0x12, 0x2d, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x30, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x1b, 0x0a, 0x19, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x22, 0x4e, 0x0a, 0x19, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x22, 0x25, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x0c, 0x0a, 0x0a, 0x54, 0x65, 0x61,
	0x6d, 0x4f, 0x75, 0x74, 0x52, 0x73, 0x70, 0x22, 0x45, 0x0a, 0x11, 0x54, 0x65, 0x61, 0x6d, 0x42,
	0x75, 0x79, 0x57, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07,
	0x73, 0x68, 0x6f, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x68, 0x6f, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x42,
	0x0a, 0x11, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x75, 0x79, 0x57, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61, 0x72,
	0x64, 0x73, 0x22, 0xb8, 0x01, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x22, 0x29, 0x0a,
	0x0b, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x22, 0x84, 0x01, 0x0a, 0x0b, 0x54, 0x65, 0x61,
	0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x2b,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e,
	0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x61, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22,
	0x46, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74,
	0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x0e, 0x0a, 0x0c, 0x54,
	0x65, 0x61, 0x6d, 0x4e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x22, 0x3b, 0x0a, 0x0c, 0x54,
	0x65, 0x61, 0x6d, 0x4e, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x0d, 0x0a, 0x0b, 0x42, 0x65, 0x67, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x22, 0x0d, 0x0a, 0x0b, 0x42, 0x65, 0x67, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x52, 0x73, 0x70, 0x22, 0x2b, 0x0a, 0x0c, 0x48, 0x65, 0x6c, 0x70, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x48, 0x65, 0x6c, 0x70, 0x48, 0x65, 0x61, 0x72, 0x74,
	0x52, 0x73, 0x70, 0x22, 0x2f, 0x0a, 0x10, 0x44, 0x72, 0x61, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x48,
	0x65, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x10, 0x44, 0x72, 0x61, 0x77, 0x54, 0x65, 0x61, 0x6d,
	0x48, 0x65, 0x61, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x72,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x61, 0x72, 0x74, 0x73,
	0x22, 0x16, 0x0a, 0x14, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x69, 0x76,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x5c, 0x0a, 0x0a, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x47, 0x0a, 0x14, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x61,
	0x6e, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x2f,
	0x0a, 0x07, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x52, 0x07, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x73, 0x22,
	0x15, 0x0a, 0x13, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65,
	0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x22, 0x49, 0x0a, 0x13, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x61,
	0x6e, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65, 0x4e, 0x75, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a,
	0x03, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x12,
	0x20, 0x0a, 0x0c, 0x63, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x6c, 0x70, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x63, 0x61, 0x6e, 0x48, 0x65, 0x6c, 0x70, 0x4e, 0x75,
	0x6d, 0x22, 0x9d, 0x02, 0x0a, 0x0b, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x13, 0x0a, 0x05, 0x74, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x6f, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x61, 0x6e, 0x5f, 0x68, 0x65,
	0x6c, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x63, 0x61, 0x6e, 0x48, 0x65, 0x6c,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x74,
	0x73, 0x22, 0x24, 0x0a, 0x12, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x74, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x12, 0x54, 0x65, 0x61, 0x6d,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x2a,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61,
	0x73, 0x5f, 0x6e, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61,
	0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x65, 0x67, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x65, 0x67, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x29, 0x0a, 0x0d, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3d,
	0x0a, 0x0d, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x0f, 0x0a,
	0x0d, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x22, 0x2c,
	0x0a, 0x0d, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x22, 0x30, 0x0a, 0x11,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x49, 0x64, 0x22, 0x8d,
	0x01, 0x0a, 0x11, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x4b,
	0x0a, 0x13, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x69, 0x6e, 0x42, 0x79, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x54,
	0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x69, 0x6e, 0x42, 0x79, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x52,
	0x73, 0x70, 0x22, 0x2b, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x4b,
	0x69, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22,
	0x12, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x4b, 0x69, 0x63, 0x6b,
	0x52, 0x73, 0x70, 0x22, 0x11, 0x0a, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x54,
	0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x22, 0xcb, 0x04, 0x0a, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x4a,
	0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61,
	0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x74, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x46, 0x6c, 0x6f, 0x6f, 0x72,
	0x12, 0x3e, 0x0a, 0x0e, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0d, 0x69, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x2f, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a,
	0x07, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x64,
	0x69, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x22, 0x44, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b,
	0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62,
	0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x50, 0x72, 0x65, 0x22, 0xf1, 0x01, 0x0a, 0x0f, 0x54,
	0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61,
	0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73,
	0x4e, 0x65, 0x78, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f,
	0x73, 0x65, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x65,
	0x6c, 0x66, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x73, 0x65, 0x6c, 0x66, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xbb,
	0x02, 0x0a, 0x0e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x61, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x65, 0x61, 0x6b, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x55, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x72,
	0x69, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x74, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x50, 0x65, 0x74, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x13,
	0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b,
	0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x13, 0x54, 0x65, 0x61,
	0x6d, 0x57, 0x65, 0x65, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x68, 0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68,
	0x61, 0x73, 0x4e, 0x65, 0x78, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0x17, 0x0a, 0x15, 0x54, 0x65, 0x61,
	0x6d, 0x57, 0x65, 0x65, 0x6b, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x22, 0x6d, 0x0a, 0x08, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x68, 0x61,
	0x73, 0x4e, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73,
	0x4e, 0x65, 0x78, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xfb, 0x02, 0x0a, 0x15, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x41, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x63,
	0x61, 0x6e, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x61, 0x6e, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x77, 0x65, 0x65, 0x6b, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x72, 0x61, 0x6e,
	0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x77, 0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e,
	0x6b, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x72,
	0x61, 0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x57,
	0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x54, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x12, 0x30, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x08, 0x72, 0x61, 0x6e, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x2a,
	0x52, 0x0a, 0x0b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16,
	0x0a, 0x12, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x10, 0x02, 0x2a, 0xd5, 0x01, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x61, 0x72, 0x74, 0x10,
	0x01, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x10, 0x03, 0x12, 0x13, 0x0a,
	0x0f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4b, 0x69, 0x63, 0x6b,
	0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x10, 0x06, 0x12,
	0x15, 0x0a, 0x11, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x6c, 0x70, 0x10, 0x08, 0x32, 0xc2, 0x0f, 0x0a, 0x04,
	0x54, 0x65, 0x61, 0x6d, 0x12, 0x55, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65,
	0x61, 0x6d, 0x54, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x1f, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x54, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61,
	0x6d, 0x54, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x74, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x0d, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x1b, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4a, 0x6f,
	0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4a, 0x6f, 0x69, 0x6e, 0x54,
	0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x56, 0x69, 0x65, 0x77, 0x54, 0x65,
	0x61, 0x6d, 0x12, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x56,
	0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x52,
	0x73, 0x70, 0x12, 0x46, 0x0a, 0x0c, 0x56, 0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x61,
	0x73, 0x65, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x56,
	0x69, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x54,
	0x65, 0x61, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x40, 0x0a, 0x0a, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x54,
	0x65, 0x61, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09, 0x54, 0x65, 0x61, 0x6d, 0x4c,
	0x65, 0x61, 0x76, 0x65, 0x12, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65,
	0x61, 0x76, 0x65, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x45, 0x64,
	0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x64,
	0x0a, 0x16, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x52,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x24,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x54, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x12, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x6f, 0x4a, 0x6f, 0x69,
	0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x54, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x42, 0x65, 0x67, 0x48, 0x65, 0x61, 0x72, 0x74, 0x12, 0x16,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x42, 0x65, 0x67, 0x48, 0x65,
	0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x42, 0x65, 0x67, 0x48, 0x65, 0x61, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3d,
	0x0a, 0x09, 0x48, 0x65, 0x6c, 0x70, 0x48, 0x65, 0x61, 0x72, 0x74, 0x12, 0x17, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x48, 0x65, 0x6c, 0x70, 0x48, 0x65, 0x61, 0x72,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x48, 0x65, 0x6c, 0x70, 0x48, 0x65, 0x61, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x52, 0x0a,
	0x10, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65, 0x4e, 0x75,
	0x6d, 0x12, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x43, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65, 0x4e, 0x75, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x43, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x69, 0x76, 0x65, 0x4e, 0x75, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x37, 0x0a, 0x07, 0x54, 0x65, 0x61, 0x6d, 0x4f, 0x75, 0x74, 0x12, 0x15, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4f, 0x75, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e,
	0x54, 0x65, 0x61, 0x6d, 0x4f, 0x75, 0x74, 0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x08, 0x54, 0x65,
	0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x4f,
	0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61,
	0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x56, 0x0a, 0x16, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x17, 0x54, 0x65, 0x61, 0x6d, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54,
	0x65, 0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x40, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f,
	0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x73, 0x70, 0x12, 0x40, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x12, 0x18, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x4c, 0x0a, 0x0e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x46, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65,
	0x61, 0x6d, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x41,
	0x75, 0x74, 0x6f, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1a,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x4a,
	0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x46, 0x0a, 0x0c, 0x54, 0x65,
	0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1a, 0x2e, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x61, 0x6e, 0x6b, 0x52,
	0x73, 0x70, 0x12, 0x52, 0x0a, 0x10, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x12, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77,
	0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x58, 0x0a, 0x12, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65,
	0x65, 0x6b, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x20, 0x2e, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57, 0x65, 0x65,
	0x6b, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x20,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x57,
	0x65, 0x65, 0x6b, 0x41, 0x77, 0x61, 0x72, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x73, 0x70,
	0x42, 0x59, 0x5a, 0x57, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x74,
	0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x63,
	0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x2f,
	0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78, 0x69, 0x61,
	0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescData = file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDesc
)

func file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescData)
	})
	return file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDescData
}

var file_pb_game_cwsx_xian_cwsx_team_team_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes = make([]protoimpl.MessageInfo, 73)
var file_pb_game_cwsx_xian_cwsx_team_team_proto_goTypes = []interface{}{
	(MemberState)(0),                  // 0: xian_cwsx.MemberState
	(MessageType)(0),                  // 1: xian_cwsx.MessageType
	(*DeviceInfo)(nil),                // 2: xian_cwsx.DeviceInfo
	(*CreateTeamToAuditReq)(nil),      // 3: xian_cwsx.CreateTeamToAuditReq
	(*CreateTeamToAuditRsp)(nil),      // 4: xian_cwsx.CreateTeamToAuditRsp
	(*ApplyJoinTeamReq)(nil),          // 5: xian_cwsx.ApplyJoinTeamReq
	(*ApplyJoinTeamRsp)(nil),          // 6: xian_cwsx.ApplyJoinTeamRsp
	(*ViewTeamReq)(nil),               // 7: xian_cwsx.ViewTeamReq
	(*ViewTeamRsp)(nil),               // 8: xian_cwsx.ViewTeamRsp
	(*ViewTeamBaseReq)(nil),           // 9: xian_cwsx.ViewTeamBaseReq
	(*ViewTeamBaseRsp)(nil),           // 10: xian_cwsx.ViewTeamBaseRsp
	(*ApplyListReq)(nil),              // 11: xian_cwsx.ApplyListReq
	(*ApplyMember)(nil),               // 12: xian_cwsx.ApplyMember
	(*ApplyListRsp)(nil),              // 13: xian_cwsx.ApplyListRsp
	(*TeamPlayer)(nil),                // 14: xian_cwsx.TeamPlayer
	(*SearchTeamReq)(nil),             // 15: xian_cwsx.SearchTeamReq
	(*TeamInfo)(nil),                  // 16: xian_cwsx.TeamInfo
	(*SearchTeamRsp)(nil),             // 17: xian_cwsx.SearchTeamRsp
	(*TeamListReq)(nil),               // 18: xian_cwsx.TeamListReq
	(*TeamListRsp)(nil),               // 19: xian_cwsx.TeamListRsp
	(*ApprovalToJoinReq)(nil),         // 20: xian_cwsx.ApprovalToJoinReq
	(*ApprovalToJoinRsp)(nil),         // 21: xian_cwsx.ApprovalToJoinRsp
	(*TeamLeaveReq)(nil),              // 22: xian_cwsx.TeamLeaveReq
	(*TeamLeaveRsp)(nil),              // 23: xian_cwsx.TeamLeaveRsp
	(*TeamChangePermissionsReq)(nil),  // 24: xian_cwsx.TeamChangePermissionsReq
	(*TeamChangePermissionsRsp)(nil),  // 25: xian_cwsx.TeamChangePermissionsRsp
	(*TeamEditInfoReq)(nil),           // 26: xian_cwsx.TeamEditInfoReq
	(*TeamEditInfoRsp)(nil),           // 27: xian_cwsx.TeamEditInfoRsp
	(*TeamAvatarReviewStatusReq)(nil), // 28: xian_cwsx.TeamAvatarReviewStatusReq
	(*TeamAvatarReviewStatusRsp)(nil), // 29: xian_cwsx.TeamAvatarReviewStatusRsp
	(*TeamOutReq)(nil),                // 30: xian_cwsx.TeamOutReq
	(*TeamOutRsp)(nil),                // 31: xian_cwsx.TeamOutRsp
	(*TeamBuyWelfareReq)(nil),         // 32: xian_cwsx.TeamBuyWelfareReq
	(*TeamBuyWelfareRsp)(nil),         // 33: xian_cwsx.TeamBuyWelfareRsp
	(*TeamRankItem)(nil),              // 34: xian_cwsx.TeamRankItem
	(*TeamRankReq)(nil),               // 35: xian_cwsx.TeamRankReq
	(*TeamRankRsp)(nil),               // 36: xian_cwsx.TeamRankRsp
	(*TeamUserRankReq)(nil),           // 37: xian_cwsx.TeamUserRankReq
	(*TeamUserRankRsp)(nil),           // 38: xian_cwsx.TeamUserRankRsp
	(*TeamNRankReq)(nil),              // 39: xian_cwsx.TeamNRankReq
	(*TeamNRankRsp)(nil),              // 40: xian_cwsx.TeamNRankRsp
	(*BegHeartReq)(nil),               // 41: xian_cwsx.BegHeartReq
	(*BegHeartRsp)(nil),               // 42: xian_cwsx.BegHeartRsp
	(*HelpHeartReq)(nil),              // 43: xian_cwsx.HelpHeartReq
	(*HelpHeartRsp)(nil),              // 44: xian_cwsx.HelpHeartRsp
	(*DrawTeamHeartReq)(nil),          // 45: xian_cwsx.DrawTeamHeartReq
	(*DrawTeamHeartRsp)(nil),          // 46: xian_cwsx.DrawTeamHeartRsp
	(*TeamCanReciveListReq)(nil),      // 47: xian_cwsx.TeamCanReciveListReq
	(*MemberBase)(nil),                // 48: xian_cwsx.MemberBase
	(*TeamCanReciveListRsp)(nil),      // 49: xian_cwsx.TeamCanReciveListRsp
	(*TeamCanReciveNumReq)(nil),       // 50: xian_cwsx.TeamCanReciveNumReq
	(*TeamCanReciveNumRsp)(nil),       // 51: xian_cwsx.TeamCanReciveNumRsp
	(*TeamMessage)(nil),               // 52: xian_cwsx.TeamMessage
	(*TeamMessageListReq)(nil),        // 53: xian_cwsx.TeamMessageListReq
	(*TeamMessageListRsp)(nil),        // 54: xian_cwsx.TeamMessageListRsp
	(*TeamConfigReq)(nil),             // 55: xian_cwsx.TeamConfigReq
	(*TeamConfigRsp)(nil),             // 56: xian_cwsx.TeamConfigRsp
	(*TeamInviteReq)(nil),             // 57: xian_cwsx.TeamInviteReq
	(*TeamInviteRsp)(nil),             // 58: xian_cwsx.TeamInviteRsp
	(*TeamInviteInfoReq)(nil),         // 59: xian_cwsx.TeamInviteInfoReq
	(*TeamInviteInfoRsp)(nil),         // 60: xian_cwsx.TeamInviteInfoRsp
	(*TeamJoinByInviteReq)(nil),       // 61: xian_cwsx.TeamJoinByInviteReq
	(*TeamJoinByInviteRsp)(nil),       // 62: xian_cwsx.TeamJoinByInviteRsp
	(*AutomaticKickReq)(nil),          // 63: xian_cwsx.AutomaticKickReq
	(*AutomaticKickRsp)(nil),          // 64: xian_cwsx.AutomaticKickRsp
	(*AutoJoinTeamReq)(nil),           // 65: xian_cwsx.AutoJoinTeamReq
	(*AutoJoinTeamRsp)(nil),           // 66: xian_cwsx.AutoJoinTeamRsp
	(*TeamWeekRankReq)(nil),           // 67: xian_cwsx.TeamWeekRankReq
	(*TeamWeekRankRsp)(nil),           // 68: xian_cwsx.TeamWeekRankRsp
	(*TeamWeekPlayer)(nil),            // 69: xian_cwsx.TeamWeekPlayer
	(*TeamWeekUserRankReq)(nil),       // 70: xian_cwsx.TeamWeekUserRankReq
	(*TeamWeekUserRankRsp)(nil),       // 71: xian_cwsx.TeamWeekUserRankRsp
	(*TeamWeekAwardCheckReq)(nil),     // 72: xian_cwsx.TeamWeekAwardCheckReq
	(*WeekRank)(nil),                  // 73: xian_cwsx.WeekRank
	(*TeamWeekAwardCheckRsp)(nil),     // 74: xian_cwsx.TeamWeekAwardCheckRsp
	(*game.InfiniteItem)(nil),         // 75: xian_cwsx.InfiniteItem
	(*game.TreasureCard)(nil),         // 76: xian_cwsx.TreasureCard
	(*game.Reward)(nil),               // 77: xian_cwsx.Reward
}
var file_pb_game_cwsx_xian_cwsx_team_team_proto_depIdxs = []int32{
	2,  // 0: xian_cwsx.CreateTeamToAuditReq.device:type_name -> xian_cwsx.DeviceInfo
	75, // 1: xian_cwsx.ViewTeamRsp.infinite_items:type_name -> xian_cwsx.InfiniteItem
	14, // 2: xian_cwsx.ViewTeamRsp.players:type_name -> xian_cwsx.TeamPlayer
	0,  // 3: xian_cwsx.ViewTeamRsp.state:type_name -> xian_cwsx.MemberState
	12, // 4: xian_cwsx.ApplyListRsp.list:type_name -> xian_cwsx.ApplyMember
	75, // 5: xian_cwsx.TeamInfo.infinite_items:type_name -> xian_cwsx.InfiniteItem
	16, // 6: xian_cwsx.SearchTeamRsp.team_list:type_name -> xian_cwsx.TeamInfo
	16, // 7: xian_cwsx.TeamListRsp.team_list:type_name -> xian_cwsx.TeamInfo
	14, // 8: xian_cwsx.ApprovalToJoinRsp.player:type_name -> xian_cwsx.TeamPlayer
	69, // 9: xian_cwsx.ApprovalToJoinRsp.week_player:type_name -> xian_cwsx.TeamWeekPlayer
	2,  // 10: xian_cwsx.TeamEditInfoReq.device:type_name -> xian_cwsx.DeviceInfo
	76, // 11: xian_cwsx.TeamBuyWelfareRsp.cards:type_name -> xian_cwsx.TreasureCard
	34, // 12: xian_cwsx.TeamRankRsp.list:type_name -> xian_cwsx.TeamRankItem
	14, // 13: xian_cwsx.TeamUserRankRsp.list:type_name -> xian_cwsx.TeamPlayer
	34, // 14: xian_cwsx.TeamNRankRsp.list:type_name -> xian_cwsx.TeamRankItem
	48, // 15: xian_cwsx.TeamCanReciveListRsp.friends:type_name -> xian_cwsx.MemberBase
	1,  // 16: xian_cwsx.TeamMessage.message_type:type_name -> xian_cwsx.MessageType
	52, // 17: xian_cwsx.TeamMessageListRsp.list:type_name -> xian_cwsx.TeamMessage
	75, // 18: xian_cwsx.AutoJoinTeamRsp.infinite_items:type_name -> xian_cwsx.InfiniteItem
	14, // 19: xian_cwsx.AutoJoinTeamRsp.players:type_name -> xian_cwsx.TeamPlayer
	0,  // 20: xian_cwsx.AutoJoinTeamRsp.state:type_name -> xian_cwsx.MemberState
	34, // 21: xian_cwsx.TeamWeekRankRsp.list:type_name -> xian_cwsx.TeamRankItem
	69, // 22: xian_cwsx.TeamWeekUserRankRsp.list:type_name -> xian_cwsx.TeamWeekPlayer
	34, // 23: xian_cwsx.WeekRank.list:type_name -> xian_cwsx.TeamRankItem
	77, // 24: xian_cwsx.TeamWeekAwardCheckRsp.rewards:type_name -> xian_cwsx.Reward
	73, // 25: xian_cwsx.TeamWeekAwardCheckRsp.rank_list:type_name -> xian_cwsx.WeekRank
	3,  // 26: xian_cwsx.Team.CreateTeamToAudit:input_type -> xian_cwsx.CreateTeamToAuditReq
	5,  // 27: xian_cwsx.Team.ApplyJoinTeam:input_type -> xian_cwsx.ApplyJoinTeamReq
	7,  // 28: xian_cwsx.Team.ViewTeam:input_type -> xian_cwsx.ViewTeamReq
	9,  // 29: xian_cwsx.Team.ViewTeamBase:input_type -> xian_cwsx.ViewTeamBaseReq
	11, // 30: xian_cwsx.Team.ApplyList:input_type -> xian_cwsx.ApplyListReq
	15, // 31: xian_cwsx.Team.SearchTeam:input_type -> xian_cwsx.SearchTeamReq
	18, // 32: xian_cwsx.Team.TeamList:input_type -> xian_cwsx.TeamListReq
	22, // 33: xian_cwsx.Team.TeamLeave:input_type -> xian_cwsx.TeamLeaveReq
	26, // 34: xian_cwsx.Team.TeamEditInfo:input_type -> xian_cwsx.TeamEditInfoReq
	28, // 35: xian_cwsx.Team.TeamAvatarReviewStatus:input_type -> xian_cwsx.TeamAvatarReviewStatusReq
	20, // 36: xian_cwsx.Team.ApprovalToJoin:input_type -> xian_cwsx.ApprovalToJoinReq
	41, // 37: xian_cwsx.Team.BegHeart:input_type -> xian_cwsx.BegHeartReq
	43, // 38: xian_cwsx.Team.HelpHeart:input_type -> xian_cwsx.HelpHeartReq
	50, // 39: xian_cwsx.Team.TeamCanReciveNum:input_type -> xian_cwsx.TeamCanReciveNumReq
	30, // 40: xian_cwsx.Team.TeamOut:input_type -> xian_cwsx.TeamOutReq
	35, // 41: xian_cwsx.Team.TeamRank:input_type -> xian_cwsx.TeamRankReq
	37, // 42: xian_cwsx.Team.TeamUserRank:input_type -> xian_cwsx.TeamUserRankReq
	53, // 43: xian_cwsx.Team.TeamMessageList:input_type -> xian_cwsx.TeamMessageListReq
	53, // 44: xian_cwsx.Team.TeamMessageListInitial:input_type -> xian_cwsx.TeamMessageListReq
	53, // 45: xian_cwsx.Team.TeamMessageListOptimize:input_type -> xian_cwsx.TeamMessageListReq
	55, // 46: xian_cwsx.Team.TeamConfig:input_type -> xian_cwsx.TeamConfigReq
	57, // 47: xian_cwsx.Team.TeamInvite:input_type -> xian_cwsx.TeamInviteReq
	59, // 48: xian_cwsx.Team.TeamInviteInfo:input_type -> xian_cwsx.TeamInviteInfoReq
	65, // 49: xian_cwsx.Team.AutoJoinTeam:input_type -> xian_cwsx.AutoJoinTeamReq
	67, // 50: xian_cwsx.Team.TeamWeekRank:input_type -> xian_cwsx.TeamWeekRankReq
	70, // 51: xian_cwsx.Team.TeamWeekUserRank:input_type -> xian_cwsx.TeamWeekUserRankReq
	72, // 52: xian_cwsx.Team.TeamWeekAwardCheck:input_type -> xian_cwsx.TeamWeekAwardCheckReq
	4,  // 53: xian_cwsx.Team.CreateTeamToAudit:output_type -> xian_cwsx.CreateTeamToAuditRsp
	6,  // 54: xian_cwsx.Team.ApplyJoinTeam:output_type -> xian_cwsx.ApplyJoinTeamRsp
	8,  // 55: xian_cwsx.Team.ViewTeam:output_type -> xian_cwsx.ViewTeamRsp
	10, // 56: xian_cwsx.Team.ViewTeamBase:output_type -> xian_cwsx.ViewTeamBaseRsp
	13, // 57: xian_cwsx.Team.ApplyList:output_type -> xian_cwsx.ApplyListRsp
	17, // 58: xian_cwsx.Team.SearchTeam:output_type -> xian_cwsx.SearchTeamRsp
	19, // 59: xian_cwsx.Team.TeamList:output_type -> xian_cwsx.TeamListRsp
	23, // 60: xian_cwsx.Team.TeamLeave:output_type -> xian_cwsx.TeamLeaveRsp
	27, // 61: xian_cwsx.Team.TeamEditInfo:output_type -> xian_cwsx.TeamEditInfoRsp
	29, // 62: xian_cwsx.Team.TeamAvatarReviewStatus:output_type -> xian_cwsx.TeamAvatarReviewStatusRsp
	21, // 63: xian_cwsx.Team.ApprovalToJoin:output_type -> xian_cwsx.ApprovalToJoinRsp
	42, // 64: xian_cwsx.Team.BegHeart:output_type -> xian_cwsx.BegHeartRsp
	44, // 65: xian_cwsx.Team.HelpHeart:output_type -> xian_cwsx.HelpHeartRsp
	51, // 66: xian_cwsx.Team.TeamCanReciveNum:output_type -> xian_cwsx.TeamCanReciveNumRsp
	31, // 67: xian_cwsx.Team.TeamOut:output_type -> xian_cwsx.TeamOutRsp
	36, // 68: xian_cwsx.Team.TeamRank:output_type -> xian_cwsx.TeamRankRsp
	38, // 69: xian_cwsx.Team.TeamUserRank:output_type -> xian_cwsx.TeamUserRankRsp
	54, // 70: xian_cwsx.Team.TeamMessageList:output_type -> xian_cwsx.TeamMessageListRsp
	54, // 71: xian_cwsx.Team.TeamMessageListInitial:output_type -> xian_cwsx.TeamMessageListRsp
	54, // 72: xian_cwsx.Team.TeamMessageListOptimize:output_type -> xian_cwsx.TeamMessageListRsp
	56, // 73: xian_cwsx.Team.TeamConfig:output_type -> xian_cwsx.TeamConfigRsp
	58, // 74: xian_cwsx.Team.TeamInvite:output_type -> xian_cwsx.TeamInviteRsp
	60, // 75: xian_cwsx.Team.TeamInviteInfo:output_type -> xian_cwsx.TeamInviteInfoRsp
	66, // 76: xian_cwsx.Team.AutoJoinTeam:output_type -> xian_cwsx.AutoJoinTeamRsp
	68, // 77: xian_cwsx.Team.TeamWeekRank:output_type -> xian_cwsx.TeamWeekRankRsp
	71, // 78: xian_cwsx.Team.TeamWeekUserRank:output_type -> xian_cwsx.TeamWeekUserRankRsp
	74, // 79: xian_cwsx.Team.TeamWeekAwardCheck:output_type -> xian_cwsx.TeamWeekAwardCheckRsp
	53, // [53:80] is the sub-list for method output_type
	26, // [26:53] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_xian_cwsx_team_team_proto_init() }
func file_pb_game_cwsx_xian_cwsx_team_team_proto_init() {
	if File_pb_game_cwsx_xian_cwsx_team_team_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTeamToAuditReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTeamToAuditRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyJoinTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyJoinTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViewTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViewTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViewTeamBaseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViewTeamBaseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyMember); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamPlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovalToJoinReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovalToJoinRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamLeaveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamLeaveRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamChangePermissionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamChangePermissionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamEditInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamEditInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamAvatarReviewStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamAvatarReviewStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamOutReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamOutRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamBuyWelfareReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamBuyWelfareRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamRankItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamUserRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamUserRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamNRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamNRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BegHeartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BegHeartRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpHeartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HelpHeartRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrawTeamHeartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DrawTeamHeartRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamCanReciveListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamCanReciveListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamCanReciveNumReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamCanReciveNumRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamMessageListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamMessageListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInviteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInviteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInviteInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamInviteInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamJoinByInviteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamJoinByInviteRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutomaticKickReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutomaticKickRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoJoinTeamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoJoinTeamRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekPlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekUserRankReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekUserRankRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekAwardCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeekRank); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TeamWeekAwardCheckRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   73,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_game_cwsx_xian_cwsx_team_team_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_xian_cwsx_team_team_proto_depIdxs,
		EnumInfos:         file_pb_game_cwsx_xian_cwsx_team_team_proto_enumTypes,
		MessageInfos:      file_pb_game_cwsx_xian_cwsx_team_team_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_xian_cwsx_team_team_proto = out.File
	file_pb_game_cwsx_xian_cwsx_team_team_proto_rawDesc = nil
	file_pb_game_cwsx_xian_cwsx_team_team_proto_goTypes = nil
	file_pb_game_cwsx_xian_cwsx_team_team_proto_depIdxs = nil
}
