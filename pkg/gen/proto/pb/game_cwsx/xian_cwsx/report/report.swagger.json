{"swagger": "2.0", "info": {"title": "pb/game_cwsx/xian_cwsx/report/report.proto", "version": "version not set"}, "tags": [{"name": "Report"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/xian_cwsx.Report/AnimationList": {"post": {"summary": "动画列表", "operationId": "Report_AnimationList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxAnimationListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxAnimationListReq"}}], "tags": ["Report"]}}, "/xian_cwsx.Report/AnimationUpdateStatus": {"post": {"summary": "动画编辑", "operationId": "Report_AnimationUpdateStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxAnimationUpdateStatusRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxAnimationUpdateStatusReq"}}], "tags": ["Report"]}}, "/xian_cwsx.Report/CosAuth": {"post": {"summary": "Cos上传临时秘钥", "operationId": "Report_CosAuth", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxCosAuthRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxCosAuthReq"}}], "tags": ["Report"]}}, "/xian_cwsx.Report/FloorList": {"post": {"summary": "关卡列表", "operationId": "Report_FloorList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxFloorListRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxFloorListReq"}}], "tags": ["Report"]}}, "/xian_cwsx.Report/FloorReport": {"post": {"summary": "关卡静态数据上报", "operationId": "Report_FloorReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/xian_cwsxFloorReportRsp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/xian_cwsxFloorReportReq"}}], "tags": ["Report"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "xian_cwsxAnimation": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "业务ID"}, "uid": {"type": "string", "title": "用户ID"}, "openId": {"type": "string", "title": "用户ID"}, "floorId": {"type": "integer", "format": "int64", "title": "关卡顺序id"}, "submitTime": {"type": "string", "format": "int64", "title": "结算时间"}, "failCnt": {"type": "string", "format": "int64", "title": "失败次数"}, "noReport": {"type": "string", "format": "int64", "title": "未收到上报次数"}, "animationUrl": {"type": "string", "title": "操作cos的url"}}, "title": "Animation 动画对象"}, "xian_cwsxAnimationListReq": {"type": "object", "properties": {"page": {"type": "string", "format": "uint64", "title": "页码"}, "floorBegin": {"type": "string", "format": "int64", "title": "关卡开始区间"}, "floorEnd": {"type": "string", "format": "int64", "title": "关卡结束区间"}, "usedStepBegin": {"type": "string", "format": "int64", "title": "使用步数开始区间"}, "usedStepEnd": {"type": "string", "format": "int64", "title": "使用步数结束区间"}, "submitBegin": {"type": "string", "format": "int64", "title": "通关开始区间"}, "submitEnd": {"type": "string", "format": "int64", "title": "通关结束区间"}, "uid": {"type": "string", "title": "用户ID"}, "openId": {"type": "string", "title": "用户ID"}, "status": {"type": "string", "format": "int64", "title": "验证结果"}}, "title": "AnimationListReq 关卡数据对象"}, "xian_cwsxAnimationListRsp": {"type": "object", "properties": {"animationList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxAnimation"}, "title": "关卡上报数据列表"}}, "title": "FloorListRsp 关卡列表rsp"}, "xian_cwsxAnimationUpdateStatusReq": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "编辑ID"}, "status": {"type": "string", "format": "int64", "title": "状态码"}, "time": {"type": "string", "format": "int64", "title": "时间戳"}}, "title": "AnimationUpdateStatusReq 动画编辑req"}, "xian_cwsxAnimationUpdateStatusRsp": {"type": "object", "title": "AnimationUpdateStatusRsp 动画编辑rsp"}, "xian_cwsxCosAuthReq": {"type": "object", "title": "CosAuthReq Cos临时秘钥Req"}, "xian_cwsxCosAuthRsp": {"type": "object", "properties": {"expiredTime": {"type": "string", "format": "int64"}, "expiration": {"type": "string"}, "startTime": {"type": "string", "format": "int64"}, "credentials": {"$ref": "#/definitions/xian_cwsxCredentials"}}, "title": "CosAuthRsp Cos临时秘钥Rsp"}, "xian_cwsxCredentials": {"type": "object", "properties": {"tmpSecretId": {"type": "string"}, "tmpSecretKey": {"type": "string"}, "sessionToken": {"type": "string"}}}, "xian_cwsxFloorListReq": {"type": "object", "properties": {"page": {"type": "string", "format": "uint64", "title": "页码"}, "uid": {"type": "string", "title": "用户ID"}, "passBegin": {"type": "string", "format": "int64", "title": "闯关耗时开始区间"}, "passEnd": {"type": "string", "format": "int64", "title": "闯关耗时结束区间"}, "floorBegin": {"type": "string", "format": "int64", "title": "关卡开始区间"}, "floorEnd": {"type": "string", "format": "int64", "title": "关卡结束区间"}, "submitBegin": {"type": "string", "format": "int64", "title": "通关开始区间"}, "submitEnd": {"type": "string", "format": "int64", "title": "通关结束区间"}}, "title": "FloorListReq 关卡列表req"}, "xian_cwsxFloorListRsp": {"type": "object", "properties": {"reportList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/xian_cwsxFloorReport"}, "title": "关卡上报数据列表"}}, "title": "FloorListRsp 关卡列表rsp"}, "xian_cwsxFloorReport": {"type": "object", "properties": {"uid": {"type": "string", "title": "用户ID"}, "floorId": {"type": "integer", "format": "int64", "title": "关卡顺序id"}, "submitTime": {"type": "string", "format": "int64", "title": "结算时间"}, "entryTime": {"type": "string", "format": "int64", "title": "关卡进入时间"}, "playTime": {"type": "string", "format": "int64", "title": "闯关消耗时间"}, "failCnt": {"type": "string", "format": "int64", "title": "失败次数"}, "noReport": {"type": "string", "format": "int64", "title": "未收到上报次数"}, "cosUrl": {"type": "string", "title": "操作cos的url"}}, "title": "FloorReport 关卡数据对象"}, "xian_cwsxFloorReportReq": {"type": "object", "properties": {"floorId": {"type": "integer", "format": "int64", "title": "关卡顺序id"}, "cosUrl": {"type": "string", "title": "操作cos的url"}, "animationUrl": {"type": "string", "title": "操作cos的url"}}, "title": "FloorReportReq 关卡静态数据上报req"}, "xian_cwsxFloorReportRsp": {"type": "object", "title": "FloorReportRsp 关卡静态数据上报rsp"}}}