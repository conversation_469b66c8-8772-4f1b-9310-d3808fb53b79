// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v3.11.0
// source: pb/game_cwsx/xian_cwsx/sync_kg/sync.proto

package sync_kg

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	game "kugou_adapter_service/pkg/gen/proto/pb/game_cwsx/xian_cwsx/game"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	SyncKg_SyncFloorMap_FullMethodName = "/xian_cwsx.SyncKg/SyncFloorMap"
	SyncKg_GetFloorMap_FullMethodName  = "/xian_cwsx.SyncKg/GetFloorMap"
	SyncKg_SyncItem_FullMethodName     = "/xian_cwsx.SyncKg/SyncItem"
	SyncKg_GetItem_FullMethodName      = "/xian_cwsx.SyncKg/GetItem"
)

// SyncKgClient is the client API for SyncKg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SyncKgClient interface {
	// 同步关卡地图
	SyncFloorMap(ctx context.Context, in *game.SyncFloorMapReq, opts ...grpc.CallOption) (*game.SyncFloorMapRsp, error)
	// 获取关卡地图
	GetFloorMap(ctx context.Context, in *game.GetFloorMapReq, opts ...grpc.CallOption) (*game.GetFloorMapRsp, error)
	// 同步道具数据
	SyncItem(ctx context.Context, in *game.SyncItemReq, opts ...grpc.CallOption) (*game.SyncItemRsp, error)
	// 获取道具数据
	GetItem(ctx context.Context, in *game.GetItemReq, opts ...grpc.CallOption) (*game.GetItemRsp, error)
}

type syncKgClient struct {
	cc grpc.ClientConnInterface
}

func NewSyncKgClient(cc grpc.ClientConnInterface) SyncKgClient {
	return &syncKgClient{cc}
}

func (c *syncKgClient) SyncFloorMap(ctx context.Context, in *game.SyncFloorMapReq, opts ...grpc.CallOption) (*game.SyncFloorMapRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.SyncFloorMapRsp)
	err := c.cc.Invoke(ctx, SyncKg_SyncFloorMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncKgClient) GetFloorMap(ctx context.Context, in *game.GetFloorMapReq, opts ...grpc.CallOption) (*game.GetFloorMapRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.GetFloorMapRsp)
	err := c.cc.Invoke(ctx, SyncKg_GetFloorMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncKgClient) SyncItem(ctx context.Context, in *game.SyncItemReq, opts ...grpc.CallOption) (*game.SyncItemRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.SyncItemRsp)
	err := c.cc.Invoke(ctx, SyncKg_SyncItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *syncKgClient) GetItem(ctx context.Context, in *game.GetItemReq, opts ...grpc.CallOption) (*game.GetItemRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(game.GetItemRsp)
	err := c.cc.Invoke(ctx, SyncKg_GetItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SyncKgServer is the server API for SyncKg service.
// All implementations should embed UnimplementedSyncKgServer
// for forward compatibility
type SyncKgServer interface {
	// 同步关卡地图
	SyncFloorMap(context.Context, *game.SyncFloorMapReq) (*game.SyncFloorMapRsp, error)
	// 获取关卡地图
	GetFloorMap(context.Context, *game.GetFloorMapReq) (*game.GetFloorMapRsp, error)
	// 同步道具数据
	SyncItem(context.Context, *game.SyncItemReq) (*game.SyncItemRsp, error)
	// 获取道具数据
	GetItem(context.Context, *game.GetItemReq) (*game.GetItemRsp, error)
}

// UnimplementedSyncKgServer should be embedded to have forward compatible implementations.
type UnimplementedSyncKgServer struct {
}

func (UnimplementedSyncKgServer) SyncFloorMap(context.Context, *game.SyncFloorMapReq) (*game.SyncFloorMapRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFloorMap not implemented")
}
func (UnimplementedSyncKgServer) GetFloorMap(context.Context, *game.GetFloorMapReq) (*game.GetFloorMapRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFloorMap not implemented")
}
func (UnimplementedSyncKgServer) SyncItem(context.Context, *game.SyncItemReq) (*game.SyncItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncItem not implemented")
}
func (UnimplementedSyncKgServer) GetItem(context.Context, *game.GetItemReq) (*game.GetItemRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetItem not implemented")
}

// UnsafeSyncKgServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SyncKgServer will
// result in compilation errors.
type UnsafeSyncKgServer interface {
	mustEmbedUnimplementedSyncKgServer()
}

func RegisterSyncKgServer(s grpc.ServiceRegistrar, srv SyncKgServer) {
	s.RegisterService(&SyncKg_ServiceDesc, srv)
}

func _SyncKg_SyncFloorMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.SyncFloorMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncKgServer).SyncFloorMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncKg_SyncFloorMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncKgServer).SyncFloorMap(ctx, req.(*game.SyncFloorMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncKg_GetFloorMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.GetFloorMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncKgServer).GetFloorMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncKg_GetFloorMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncKgServer).GetFloorMap(ctx, req.(*game.GetFloorMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncKg_SyncItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.SyncItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncKgServer).SyncItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncKg_SyncItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncKgServer).SyncItem(ctx, req.(*game.SyncItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SyncKg_GetItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(game.GetItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SyncKgServer).GetItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SyncKg_GetItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SyncKgServer).GetItem(ctx, req.(*game.GetItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SyncKg_ServiceDesc is the grpc.ServiceDesc for SyncKg service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SyncKg_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "xian_cwsx.SyncKg",
	HandlerType: (*SyncKgServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncFloorMap",
			Handler:    _SyncKg_SyncFloorMap_Handler,
		},
		{
			MethodName: "GetFloorMap",
			Handler:    _SyncKg_GetFloorMap_Handler,
		},
		{
			MethodName: "SyncItem",
			Handler:    _SyncKg_SyncItem_Handler,
		},
		{
			MethodName: "GetItem",
			Handler:    _SyncKg_GetItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/game_cwsx/xian_cwsx/sync_kg/sync.proto",
}
