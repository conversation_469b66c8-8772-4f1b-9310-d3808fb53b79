// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v3.11.0
// source: pb/game_cwsx/xian_cwsx/activity/inlet.proto

package activity

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlatformType int32

const (
	PlatformType_PlatformUnknown PlatformType = 0
	PlatformType_PlatformAndroid PlatformType = 1
	PlatformType_PlatformIOS     PlatformType = 2
)

// Enum value maps for PlatformType.
var (
	PlatformType_name = map[int32]string{
		0: "PlatformUnknown",
		1: "PlatformAndroid",
		2: "PlatformIOS",
	}
	PlatformType_value = map[string]int32{
		"PlatformUnknown": 0,
		"PlatformAndroid": 1,
		"PlatformIOS":     2,
	}
)

func (x PlatformType) Enum() *PlatformType {
	p := new(PlatformType)
	*p = x
	return p
}

func (x PlatformType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlatformType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes[0].Descriptor()
}

func (PlatformType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes[0]
}

func (x PlatformType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlatformType.Descriptor instead.
func (PlatformType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{0}
}

type InletNumber int32

const (
	InletNumber_InletNumber_None                   InletNumber = 0  // 无
	InletNumber_InletNumber_HonorMedalCompetition  InletNumber = 1  // 荣耀奖牌赛
	InletNumber_InletNumber_AirplaneRace           InletNumber = 2  // 飞机竞赛
	InletNumber_InletNumber_SuperColorfulLights    InletNumber = 3  // 超级彩灯
	InletNumber_InletNumber_TeamRudder             InletNumber = 4  // 战队淘金
	InletNumber_InletNumber_TeamCompetition        InletNumber = 5  // 战队竞赛
	InletNumber_InletNumber_BattlePass             InletNumber = 6  // 战令
	InletNumber_InletNumber_Fishing                InletNumber = 7  // 小猫钓鱼
	InletNumber_InletNumber_CuteRabbitParadise     InletNumber = 8  // 萌兔乐园
	InletNumber_InletNumber_EndlessTreasures       InletNumber = 9  // 无尽宝藏
	InletNumber_InletNumber_CheckIn                InletNumber = 10 // 签到
	InletNumber_InletNumber_LightingRush           InletNumber = 11 // 彩虹竞速
	InletNumber_InletNumber_SpecialDiscountPackage InletNumber = 12 // 特惠礼包
	InletNumber_InletNumber_NoviceChallengeEvent   InletNumber = 13 // 新手闯关活动
	InletNumber_InletNumber_DragonsTreasure        InletNumber = 14 // 巨龙宝藏
	InletNumber_InletNumber_InviteFriends          InletNumber = 15 // 邀请好友
	InletNumber_InletNumber_ShareFriends           InletNumber = 16 // 分享好友
	InletNumber_InletNumber_Favorite               InletNumber = 17 // 收藏
)

// Enum value maps for InletNumber.
var (
	InletNumber_name = map[int32]string{
		0:  "InletNumber_None",
		1:  "InletNumber_HonorMedalCompetition",
		2:  "InletNumber_AirplaneRace",
		3:  "InletNumber_SuperColorfulLights",
		4:  "InletNumber_TeamRudder",
		5:  "InletNumber_TeamCompetition",
		6:  "InletNumber_BattlePass",
		7:  "InletNumber_Fishing",
		8:  "InletNumber_CuteRabbitParadise",
		9:  "InletNumber_EndlessTreasures",
		10: "InletNumber_CheckIn",
		11: "InletNumber_LightingRush",
		12: "InletNumber_SpecialDiscountPackage",
		13: "InletNumber_NoviceChallengeEvent",
		14: "InletNumber_DragonsTreasure",
		15: "InletNumber_InviteFriends",
		16: "InletNumber_ShareFriends",
		17: "InletNumber_Favorite",
	}
	InletNumber_value = map[string]int32{
		"InletNumber_None":                   0,
		"InletNumber_HonorMedalCompetition":  1,
		"InletNumber_AirplaneRace":           2,
		"InletNumber_SuperColorfulLights":    3,
		"InletNumber_TeamRudder":             4,
		"InletNumber_TeamCompetition":        5,
		"InletNumber_BattlePass":             6,
		"InletNumber_Fishing":                7,
		"InletNumber_CuteRabbitParadise":     8,
		"InletNumber_EndlessTreasures":       9,
		"InletNumber_CheckIn":                10,
		"InletNumber_LightingRush":           11,
		"InletNumber_SpecialDiscountPackage": 12,
		"InletNumber_NoviceChallengeEvent":   13,
		"InletNumber_DragonsTreasure":        14,
		"InletNumber_InviteFriends":          15,
		"InletNumber_ShareFriends":           16,
		"InletNumber_Favorite":               17,
	}
)

func (x InletNumber) Enum() *InletNumber {
	p := new(InletNumber)
	*p = x
	return p
}

func (x InletNumber) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InletNumber) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes[1].Descriptor()
}

func (InletNumber) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes[1]
}

func (x InletNumber) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InletNumber.Descriptor instead.
func (InletNumber) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{1}
}

type InletDurationType int32

const (
	InletDurationType_InletDurationType_DurationNone     InletDurationType = 0 // 无
	InletDurationType_InletDurationType_DurationAlways   InletDurationType = 1 // 永久
	InletDurationType_InletDurationType_DurationSingle   InletDurationType = 2 // 单次展示
	InletDurationType_InletDurationType_DurationDay      InletDurationType = 3 // 天刷新
	InletDurationType_InletDurationType_DurationWeek     InletDurationType = 4 // 周刷新
	InletDurationType_InletDurationType_DurationWeekDay  InletDurationType = 5 // 周每天刷新
	InletDurationType_InletDurationType_DurationCallback InletDurationType = 6 // 回调刷新
	InletDurationType_InletDurationType_DurationSetSE    InletDurationType = 7 // 特殊设置开始与结束时间
	InletDurationType_InletDurationType_DurationMinute   InletDurationType = 8 // 分钟刷新
)

// Enum value maps for InletDurationType.
var (
	InletDurationType_name = map[int32]string{
		0: "InletDurationType_DurationNone",
		1: "InletDurationType_DurationAlways",
		2: "InletDurationType_DurationSingle",
		3: "InletDurationType_DurationDay",
		4: "InletDurationType_DurationWeek",
		5: "InletDurationType_DurationWeekDay",
		6: "InletDurationType_DurationCallback",
		7: "InletDurationType_DurationSetSE",
		8: "InletDurationType_DurationMinute",
	}
	InletDurationType_value = map[string]int32{
		"InletDurationType_DurationNone":     0,
		"InletDurationType_DurationAlways":   1,
		"InletDurationType_DurationSingle":   2,
		"InletDurationType_DurationDay":      3,
		"InletDurationType_DurationWeek":     4,
		"InletDurationType_DurationWeekDay":  5,
		"InletDurationType_DurationCallback": 6,
		"InletDurationType_DurationSetSE":    7,
		"InletDurationType_DurationMinute":   8,
	}
)

func (x InletDurationType) Enum() *InletDurationType {
	p := new(InletDurationType)
	*p = x
	return p
}

func (x InletDurationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InletDurationType) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes[2].Descriptor()
}

func (InletDurationType) Type() protoreflect.EnumType {
	return &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes[2]
}

func (x InletDurationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InletDurationType.Descriptor instead.
func (InletDurationType) EnumDescriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{2}
}

// InletListReq 入口管理列表
type InletListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pt       PlatformType `protobuf:"varint,1,opt,name=pt,proto3,enum=xian_cwsx.PlatformType" json:"pt,omitempty"` // 设备类型
	SceneId  int32        `protobuf:"varint,2,opt,name=sceneId,proto3" json:"sceneId,omitempty"`                   // 场景ID 前端与中台李剑军对一下
	LeftNum  int64        `protobuf:"varint,3,opt,name=left_num,json=leftNum,proto3" json:"left_num,omitempty"`    // 左边所有ICON [0~5]
	RightNum int64        `protobuf:"varint,4,opt,name=right_num,json=rightNum,proto3" json:"right_num,omitempty"` // 右边所有ICON [0~5]
}

func (x *InletListReq) Reset() {
	*x = InletListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletListReq) ProtoMessage() {}

func (x *InletListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletListReq.ProtoReflect.Descriptor instead.
func (*InletListReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{0}
}

func (x *InletListReq) GetPt() PlatformType {
	if x != nil {
		return x.Pt
	}
	return PlatformType_PlatformUnknown
}

func (x *InletListReq) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

func (x *InletListReq) GetLeftNum() int64 {
	if x != nil {
		return x.LeftNum
	}
	return 0
}

func (x *InletListReq) GetRightNum() int64 {
	if x != nil {
		return x.RightNum
	}
	return 0
}

// InletListRsp 入口管理列表
type InletListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Left  []*InletInfo `protobuf:"bytes,1,rep,name=left,proto3" json:"left,omitempty"`   //  左边入口活动列表
	Right []*InletInfo `protobuf:"bytes,2,rep,name=right,proto3" json:"right,omitempty"` // 右边入口活动列表
}

func (x *InletListRsp) Reset() {
	*x = InletListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletListRsp) ProtoMessage() {}

func (x *InletListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletListRsp.ProtoReflect.Descriptor instead.
func (*InletListRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{1}
}

func (x *InletListRsp) GetLeft() []*InletInfo {
	if x != nil {
		return x.Left
	}
	return nil
}

func (x *InletListRsp) GetRight() []*InletInfo {
	if x != nil {
		return x.Right
	}
	return nil
}

// InletInfo 信息
type InletInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number       int64 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`                                 // 入口活动编号=>InletNumber
	DurationType int64 `protobuf:"varint,2,opt,name=duration_type,json=durationType,proto3" json:"duration_type,omitempty"` // 持续时间类型=>InletDurationType
	StartTime    int64 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`          // 开始时间
	EndTime      int64 `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                // 结束时间
}

func (x *InletInfo) Reset() {
	*x = InletInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletInfo) ProtoMessage() {}

func (x *InletInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletInfo.ProtoReflect.Descriptor instead.
func (*InletInfo) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{2}
}

func (x *InletInfo) GetNumber() int64 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *InletInfo) GetDurationType() int64 {
	if x != nil {
		return x.DurationType
	}
	return 0
}

func (x *InletInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *InletInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// InletOffReq 关闭活动
type InletOffReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number InletNumber `protobuf:"varint,1,opt,name=number,proto3,enum=xian_cwsx.InletNumber" json:"number,omitempty"` // 入口活动编号=>InletNumber
	OpenId string      `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`               // 用户ID
}

func (x *InletOffReq) Reset() {
	*x = InletOffReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletOffReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletOffReq) ProtoMessage() {}

func (x *InletOffReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletOffReq.ProtoReflect.Descriptor instead.
func (*InletOffReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{3}
}

func (x *InletOffReq) GetNumber() InletNumber {
	if x != nil {
		return x.Number
	}
	return InletNumber_InletNumber_None
}

func (x *InletOffReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

// InletOffRsp 关闭活动
type InletOffRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InletOffRsp) Reset() {
	*x = InletOffRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletOffRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletOffRsp) ProtoMessage() {}

func (x *InletOffRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletOffRsp.ProtoReflect.Descriptor instead.
func (*InletOffRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{4}
}

// InletOptSetReq 设置活动开始与结束时间
type InletOptSetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number  InletNumber                    `protobuf:"varint,1,opt,name=number,proto3,enum=xian_cwsx.InletNumber" json:"number,omitempty"` // 入口活动编号=>InletNumber
	OptTime []*InletOptSetReq_InletOptTime `protobuf:"bytes,2,rep,name=opt_time,json=optTime,proto3" json:"opt_time,omitempty"`            // 时间列表
}

func (x *InletOptSetReq) Reset() {
	*x = InletOptSetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletOptSetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletOptSetReq) ProtoMessage() {}

func (x *InletOptSetReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletOptSetReq.ProtoReflect.Descriptor instead.
func (*InletOptSetReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{5}
}

func (x *InletOptSetReq) GetNumber() InletNumber {
	if x != nil {
		return x.Number
	}
	return InletNumber_InletNumber_None
}

func (x *InletOptSetReq) GetOptTime() []*InletOptSetReq_InletOptTime {
	if x != nil {
		return x.OptTime
	}
	return nil
}

// InletSetRsp 设置活动开始与结束时间
type InletOptSetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InletOptSetRsp) Reset() {
	*x = InletOptSetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletOptSetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletOptSetRsp) ProtoMessage() {}

func (x *InletOptSetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletOptSetRsp.ProtoReflect.Descriptor instead.
func (*InletOptSetRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{6}
}

// InletStatusReq 查询活动状态
type InletStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number    []InletNumber `protobuf:"varint,1,rep,packed,name=number,proto3,enum=xian_cwsx.InletNumber" json:"number,omitempty"` // 入口活动编号=>InletNumber
	OpenId    string        `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`                      // 用户的open_id
	Os        PlatformType  `protobuf:"varint,3,opt,name=os,proto3,enum=xian_cwsx.PlatformType" json:"os,omitempty"`               // 设备类型
	SceneId   int32         `protobuf:"varint,4,opt,name=sceneId,proto3" json:"sceneId,omitempty"`                                 // 场景ID
	UserAgent string        `protobuf:"bytes,5,opt,name=userAgent,proto3" json:"userAgent,omitempty"`                              // ua
	AppId     string        `protobuf:"bytes,6,opt,name=appId,proto3" json:"appId,omitempty"`                                      // appid
}

func (x *InletStatusReq) Reset() {
	*x = InletStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletStatusReq) ProtoMessage() {}

func (x *InletStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletStatusReq.ProtoReflect.Descriptor instead.
func (*InletStatusReq) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{7}
}

func (x *InletStatusReq) GetNumber() []InletNumber {
	if x != nil {
		return x.Number
	}
	return nil
}

func (x *InletStatusReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *InletStatusReq) GetOs() PlatformType {
	if x != nil {
		return x.Os
	}
	return PlatformType_PlatformUnknown
}

func (x *InletStatusReq) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

func (x *InletStatusReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *InletStatusReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

// InletSetRsp 查询活动状态
type InletStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InletStatus map[int64]bool `protobuf:"bytes,2,rep,name=inlet_status,json=inletStatus,proto3" json:"inlet_status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 入口活动编号[InletNumber]=>入口状态[true展示 false关闭]
}

func (x *InletStatusRsp) Reset() {
	*x = InletStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletStatusRsp) ProtoMessage() {}

func (x *InletStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletStatusRsp.ProtoReflect.Descriptor instead.
func (*InletStatusRsp) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{8}
}

func (x *InletStatusRsp) GetInletStatus() map[int64]bool {
	if x != nil {
		return x.InletStatus
	}
	return nil
}

type InletOptSetReq_InletOptTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime int64 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 开始时间
	EndTime   int64 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 结束时间
}

func (x *InletOptSetReq_InletOptTime) Reset() {
	*x = InletOptSetReq_InletOptTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InletOptSetReq_InletOptTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InletOptSetReq_InletOptTime) ProtoMessage() {}

func (x *InletOptSetReq_InletOptTime) ProtoReflect() protoreflect.Message {
	mi := &file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InletOptSetReq_InletOptTime.ProtoReflect.Descriptor instead.
func (*InletOptSetReq_InletOptTime) Descriptor() ([]byte, []int) {
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP(), []int{5, 0}
}

func (x *InletOptSetReq_InletOptTime) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *InletOptSetReq_InletOptTime) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

var File_pb_game_cwsx_xian_cwsx_activity_inlet_proto protoreflect.FileDescriptor

var file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2f, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x78,
	0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x22, 0x89, 0x01, 0x0a, 0x0c, 0x49, 0x6e, 0x6c,
	0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x02, 0x70, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02,
	0x70, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x6c, 0x65, 0x66, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x6c, 0x65, 0x66, 0x74, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x4e, 0x75, 0x6d, 0x22, 0x64, 0x0a, 0x0c, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49,
	0x6e, 0x6c, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x65, 0x66, 0x74, 0x12, 0x2a,
	0x0a, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x72, 0x69, 0x67, 0x68, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x09, 0x49,
	0x6e, 0x6c, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x56, 0x0a, 0x0b, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x71, 0x12, 0x2e,
	0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x49, 0x6e, 0x6c, 0x65, 0x74,
	0x4f, 0x66, 0x66, 0x52, 0x73, 0x70, 0x22, 0xcd, 0x01, 0x0a, 0x0e, 0x49, 0x6e, 0x6c, 0x65, 0x74,
	0x4f, 0x70, 0x74, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2e, 0x0a, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x78, 0x69, 0x61, 0x6e,
	0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x08, 0x6f, 0x70, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4f, 0x70, 0x74,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x48, 0x0a, 0x0c,
	0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4f,
	0x70, 0x74, 0x53, 0x65, 0x74, 0x52, 0x73, 0x70, 0x22, 0xd0, 0x01, 0x0a, 0x0e, 0x49, 0x6e, 0x6c,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x2e, 0x0a, 0x06, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x78, 0x69,
	0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6f,
	0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2e, 0x50, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x0e,
	0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x4d,
	0x0a, 0x0c, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x2e,
	0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x69, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0x3e, 0x0a,
	0x10, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x49, 0x0a,
	0x0c, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x41, 0x6e,
	0x64, 0x72, 0x6f, 0x69, 0x64, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x2a, 0xc2, 0x04, 0x0a, 0x0b, 0x49, 0x6e, 0x6c,
	0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x6e, 0x6c, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x25,
	0x0a, 0x21, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x48, 0x6f,
	0x6e, 0x6f, 0x72, 0x4d, 0x65, 0x64, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x41, 0x69, 0x72, 0x70, 0x6c, 0x61, 0x6e, 0x65, 0x52, 0x61, 0x63,
	0x65, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x53, 0x75, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x66, 0x75, 0x6c,
	0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x6e, 0x6c, 0x65,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x75, 0x64, 0x64,
	0x65, 0x72, 0x10, 0x04, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x6f, 0x6d, 0x70, 0x65, 0x74, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x10, 0x05, 0x12, 0x1a, 0x0a, 0x16, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x10,
	0x06, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x5f, 0x46, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x10, 0x07, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x6e,
	0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x43, 0x75, 0x74, 0x65, 0x52, 0x61,
	0x62, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x64, 0x69, 0x73, 0x65, 0x10, 0x08, 0x12, 0x20,
	0x0a, 0x1c, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x45, 0x6e,
	0x64, 0x6c, 0x65, 0x73, 0x73, 0x54, 0x72, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x73, 0x10, 0x09,
	0x12, 0x17, 0x0a, 0x13, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x10, 0x0a, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x6e, 0x6c,
	0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x73, 0x68, 0x10, 0x0b, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x6e, 0x6c, 0x65, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x10, 0x0c, 0x12,
	0x24, 0x0a, 0x20, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x4e,
	0x6f, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x65, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x10, 0x0d, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x44, 0x72, 0x61, 0x67, 0x6f, 0x6e, 0x73, 0x54, 0x72, 0x65, 0x61,
	0x73, 0x75, 0x72, 0x65, 0x10, 0x0e, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x46, 0x72, 0x69, 0x65,
	0x6e, 0x64, 0x73, 0x10, 0x0f, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x5f, 0x53, 0x68, 0x61, 0x72, 0x65, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x73, 0x10, 0x10, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x5f, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x10, 0x11, 0x2a, 0xe4, 0x02,
	0x0a, 0x11, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x6e, 0x6c, 0x65, 0x74,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6c, 0x77, 0x61, 0x79, 0x73, 0x10, 0x01, 0x12, 0x24, 0x0a,
	0x20, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x6e, 0x67, 0x6c,
	0x65, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x79, 0x10, 0x03, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x57, 0x65, 0x65, 0x6b, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x49, 0x6e,
	0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x65, 0x65, 0x6b, 0x44, 0x61, 0x79, 0x10,
	0x05, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x10, 0x06, 0x12, 0x23, 0x0a, 0x1f, 0x49, 0x6e, 0x6c,
	0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x53, 0x45, 0x10, 0x07, 0x12, 0x24,
	0x0a, 0x20, 0x49, 0x6e, 0x6c, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x75,
	0x74, 0x65, 0x10, 0x08, 0x42, 0x5d, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x2e, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x74, 0x6d, 0x65, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x63, 0x6f, 0x72, 0x70, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73,
	0x78, 0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x2f, 0x70, 0x62, 0x2f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x77, 0x73, 0x78,
	0x2f, 0x78, 0x69, 0x61, 0x6e, 0x5f, 0x63, 0x77, 0x73, 0x78, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescOnce sync.Once
	file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescData = file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDesc
)

func file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescGZIP() []byte {
	file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescOnce.Do(func() {
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescData)
	})
	return file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDescData
}

var file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_goTypes = []interface{}{
	(PlatformType)(0),                   // 0: xian_cwsx.PlatformType
	(InletNumber)(0),                    // 1: xian_cwsx.InletNumber
	(InletDurationType)(0),              // 2: xian_cwsx.InletDurationType
	(*InletListReq)(nil),                // 3: xian_cwsx.InletListReq
	(*InletListRsp)(nil),                // 4: xian_cwsx.InletListRsp
	(*InletInfo)(nil),                   // 5: xian_cwsx.InletInfo
	(*InletOffReq)(nil),                 // 6: xian_cwsx.InletOffReq
	(*InletOffRsp)(nil),                 // 7: xian_cwsx.InletOffRsp
	(*InletOptSetReq)(nil),              // 8: xian_cwsx.InletOptSetReq
	(*InletOptSetRsp)(nil),              // 9: xian_cwsx.InletOptSetRsp
	(*InletStatusReq)(nil),              // 10: xian_cwsx.InletStatusReq
	(*InletStatusRsp)(nil),              // 11: xian_cwsx.InletStatusRsp
	(*InletOptSetReq_InletOptTime)(nil), // 12: xian_cwsx.InletOptSetReq.InletOptTime
	nil,                                 // 13: xian_cwsx.InletStatusRsp.InletStatusEntry
}
var file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_depIdxs = []int32{
	0,  // 0: xian_cwsx.InletListReq.pt:type_name -> xian_cwsx.PlatformType
	5,  // 1: xian_cwsx.InletListRsp.left:type_name -> xian_cwsx.InletInfo
	5,  // 2: xian_cwsx.InletListRsp.right:type_name -> xian_cwsx.InletInfo
	1,  // 3: xian_cwsx.InletOffReq.number:type_name -> xian_cwsx.InletNumber
	1,  // 4: xian_cwsx.InletOptSetReq.number:type_name -> xian_cwsx.InletNumber
	12, // 5: xian_cwsx.InletOptSetReq.opt_time:type_name -> xian_cwsx.InletOptSetReq.InletOptTime
	1,  // 6: xian_cwsx.InletStatusReq.number:type_name -> xian_cwsx.InletNumber
	0,  // 7: xian_cwsx.InletStatusReq.os:type_name -> xian_cwsx.PlatformType
	13, // 8: xian_cwsx.InletStatusRsp.inlet_status:type_name -> xian_cwsx.InletStatusRsp.InletStatusEntry
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_init() }
func file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_init() {
	if File_pb_game_cwsx_xian_cwsx_activity_inlet_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletOffReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletOffRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletOptSetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletOptSetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InletOptSetReq_InletOptTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_goTypes,
		DependencyIndexes: file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_depIdxs,
		EnumInfos:         file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_enumTypes,
		MessageInfos:      file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_msgTypes,
	}.Build()
	File_pb_game_cwsx_xian_cwsx_activity_inlet_proto = out.File
	file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_rawDesc = nil
	file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_goTypes = nil
	file_pb_game_cwsx_xian_cwsx_activity_inlet_proto_depIdxs = nil
}
